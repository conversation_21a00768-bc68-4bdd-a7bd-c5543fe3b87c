from datetime import datetime, timezone
from uuid import UUID, uuid4

import pytest

from commission_engine.accessors.settlement_accessor import SettlementRuleAccessor
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.settlement_on_booking_query_provider import (
    SettlementOnBookingQueryProvider,
)
from everstage_ddd.settlement_v3.tests.utils import normalize_query

CLIENT_ID = 6901


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementOnBookingQueryProvider:
    """
    Unit tests for settlement_on_booking_query_provider
    """

    def test_get_query_and_params(self):
        table_name = '"target_table"'

        sync_info = SyncInfo(
            client_id=CLIENT_ID,
            e2e_sync_run_id=UUID("9046450c-2552-4bee-9962-1752475aa329"),
            sync_run_id=uuid4(),
            period_start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
            period_end_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
            knowledge_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
            log_context={},
        )
        criteria_info = CriteriaInfo(
            criteria_id=uuid4(),
            plan_id=uuid4(),
            date_field="co_22_date",
            databook_id=uuid4(),
            datasheet_id=uuid4(),
        )
        rule = SettlementRuleAccessor(CLIENT_ID).get_settlement_rule(
            "1a23291f-191a-41c2-9993-bbb874af01e5"
        )
        expected_query = """
            INSERT INTO "target_table" (
                payee_email_id, 
                period_start_date,
                period_end_date,
                commission_row_key, 
                line_item_id,
                commission_sheet_data, 
                settlement_sheet_data, 
                additional_data, 
                commission,
                id
            )
            SELECT 
               ps.payee_email_id,
               ps.period_start_date,
               ps.period_end_date,
               ps.line_item_id as commission_row_key,
               ps.line_item_id as line_item_id,
               ps.datasheet_data as commission_sheet_data,
               ps.datasheet_data as settlement_sheet_data,
               ps.additional_data,
               ps.commission,
               row_number() over (order by 0) as id
            FROM 
                table(?) ps
            JOIN 
                "plan_payee_info_6901_9046450c-2552-4bee-9962-1752475aa329" ppe on ppe.plan_id = ps.plan_id
                AND ppe.payee_email_id = ps.payee_email_id
                AND ppe.period_end_date::DATETIME = ?
            WHERE 
                ps.plan_id = ? 
                AND ps.criteria_id = ? 
                AND ps.period_start_date::DATETIME = ?::DATETIME
                AND ps.period_end_date::DATETIME = ?::DATETIME
                AND ps.line_item_id IS NOT NULL
                AND ps.tier_id_split[0]:tier_id::string <> '-'
        """
        expected_params = [
            "PAYOUT_SNAPSHOT_DATA_6901",
            sync_info.period_end_date,
            str(criteria_info.plan_id),
            str(criteria_info.criteria_id),
            sync_info.period_start_date,
            sync_info.period_end_date,
        ]

        actual_query, actual_params = SettlementOnBookingQueryProvider(
            sync_info, criteria_info
        ).get_query_and_params(
            settlement_rule=rule,  # type: ignore
            table_name=table_name,
        )
        assert normalize_query(actual_query) == normalize_query(expected_query)
        assert actual_params == expected_params
