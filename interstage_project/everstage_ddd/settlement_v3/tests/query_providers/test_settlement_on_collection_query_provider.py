from datetime import datetime, timezone
from uuid import UUID, uuid4

import pytest

from commission_engine.accessors.settlement_accessor import SettlementRuleAccessor
from everstage_ddd.settlement_v3.common.exceptions import ColumnNotFoundError
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.settlement_on_collection_query_provider import (
    SettlementOnCollectionQueryProvider,
)
from everstage_ddd.settlement_v3.tests.utils import normalize_query
from spm.accessors.commission_plan_accessor import PlanCriteriaAccessor

CLIENT_ID = 6901


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementOnCollectionQueryProvider:
    """
    Unit tests for settlement_on_collection_query_provider
    """

    @pytest.mark.parametrize(
        "plan_id, criteria_id, settlement_rule_id, expected_result",
        [
            (
                UUID("02d0a248-acae-46ef-a011-c0e72202799c"),
                UUID("17e4569b-b340-4c83-b749-2f687f3cfb54"),
                UUID("44e0e4bd-e2e9-4ef0-8338-9c508b289c09"),
                """
                    EQUAL_NULL(ps.datasheet_data:co_1_deal_id::STRING, ds.data:co_1_deal_id::STRING)
                    AND EQUAL_NULL(ps.datasheet_data:co_1_email_id::STRING, ds.data:co_1_email_id::STRING)
                    AND EQUAL_NULL(ps.datasheet_data:co_1_deal_date::DATETIME, ds.data:co_1_deal_date::DATETIME)
                    AND EQUAL_NULL(ps.datasheet_data:co_1_deal_closed::BOOLEAN, ds.data:co_1_deal_closed::BOOLEAN)
                    AND EQUAL_NULL(ps.datasheet_data:co_1_num::DOUBLE, ds.data:co_1_num::DOUBLE)
                """,
            ),
            (
                UUID("02d0a248-acae-46ef-a011-c0e72202799c"),
                UUID("17e4569b-b340-4c83-b749-2f687f3cfb54"),
                UUID("187451e6-a971-428d-8209-5e511a89bdea"),
                "EQUAL_NULL(ps.datasheet_data:co_1_deal_date::DATETIME, ds.data:co_1_deal_date::DATETIME)",
            ),
        ],
    )
    def test_construct_join_condition(
        self, plan_id, criteria_id, settlement_rule_id, expected_result
    ):
        sync_info = SyncInfo(
            client_id=CLIENT_ID,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            period_start_date=datetime(2025, 2, 1, tzinfo=timezone.utc),
            period_end_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            knowledge_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            log_context={},
        )
        criteria = PlanCriteriaAccessor(CLIENT_ID).get_criteria(
            criteria_id,
            fields=["criteria_data__databook_id", "criteria_data__datasheet_id"],
        )
        assert criteria is not None
        criteria_info = CriteriaInfo(
            plan_id=plan_id,
            criteria_id=criteria_id,
            date_field="cf_date_field",
            databook_id=criteria["criteria_data__databook_id"],
            datasheet_id=criteria["criteria_data__datasheet_id"],
        )
        rule = SettlementRuleAccessor(CLIENT_ID).get_settlement_rule(settlement_rule_id)
        assert rule is not None
        query_provider = SettlementOnCollectionQueryProvider(sync_info, criteria_info)
        join_condition = query_provider.construct_join_condition(
            commission_ds_alias="ps",
            settlement_ds_alias="ds",
            settlement_rule=rule,
        )
        assert normalize_query(join_condition) == normalize_query(expected_result)

    @pytest.mark.parametrize(
        "plan_id, criteria_id, settlement_rule_id",
        [
            (
                UUID("02d0a248-acae-46ef-a011-c0e72202799c"),
                UUID("17e4569b-b340-4c83-b749-2f687f3cfb54"),
                UUID("187451e6-a971-428d-8209-5e511a89bdea"),
            ),
        ],
    )
    def test_construct_join_condition_raise_error_if_column_not_found(
        self, plan_id, criteria_id, settlement_rule_id
    ):
        sync_info = SyncInfo(
            client_id=CLIENT_ID,
            e2e_sync_run_id=uuid4(),
            sync_run_id=uuid4(),
            period_start_date=datetime(2025, 2, 1, tzinfo=timezone.utc),
            period_end_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            knowledge_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            log_context={},
        )
        criteria = PlanCriteriaAccessor(CLIENT_ID).get_criteria(
            criteria_id,
            fields=["criteria_data__databook_id", "criteria_data__datasheet_id"],
        )
        assert criteria is not None
        criteria_info = CriteriaInfo(
            plan_id=plan_id,
            criteria_id=criteria_id,
            date_field="cf_date_field",
            databook_id=criteria["criteria_data__databook_id"],
            datasheet_id=criteria["criteria_data__datasheet_id"],
        )
        rule = SettlementRuleAccessor(CLIENT_ID).get_settlement_rule(settlement_rule_id)

        assert rule is not None
        query_provider = SettlementOnCollectionQueryProvider(sync_info, criteria_info)
        with pytest.raises(ColumnNotFoundError):
            rule.commission_join_keys[0] = "invalid_column"
            query_provider.construct_join_condition(
                commission_ds_alias="ps",
                settlement_ds_alias="ds",
                settlement_rule=rule,
            )
        with pytest.raises(ColumnNotFoundError):
            rule.settlement_join_keys[0] = "invalid_column"
            query_provider.construct_join_condition(
                commission_ds_alias="ps",
                settlement_ds_alias="ds",
                settlement_rule=rule,
            )

    def test_get_query_and_params(self):
        plan_id, criteria_id, settlement_rule_id = (
            UUID("02d0a248-acae-46ef-a011-c0e72202799c"),
            UUID("17e4569b-b340-4c83-b749-2f687f3cfb54"),
            UUID("187451e6-a971-428d-8209-5e511a89bdea"),
        )
        sync_info = SyncInfo(
            client_id=CLIENT_ID,
            e2e_sync_run_id=UUID("0782d9d4-c17f-425c-83f1-2f96fc1c6c0f"),
            sync_run_id=uuid4(),
            period_start_date=datetime(2025, 2, 1, tzinfo=timezone.utc),
            period_end_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            knowledge_date=datetime(2025, 2, 28, tzinfo=timezone.utc),
            log_context={},
        )
        criteria = PlanCriteriaAccessor(CLIENT_ID).get_criteria(
            criteria_id,
            fields=["criteria_data__databook_id", "criteria_data__datasheet_id"],
        )

        expected_query = """
                INSERT INTO "target_table" (
                    payee_email_id, 
                    period_start_date,
                    period_end_date,
                    commission_row_key, 
                    line_item_id,
                    commission_sheet_data, 
                    settlement_sheet_data, 
                    additional_data, 
                    commission,
                    id
                )
                SELECT 
                ps.payee_email_id,
                ps.period_start_date,
                ps.period_end_date,
                ps.line_item_id as commission_row_key,
                ds.row_key as line_item_id,
                ps.datasheet_data as commission_sheet_data,
                ds.data as settlement_sheet_data,
                ps.additional_data,
                ps.commission,
                row_number() over (order by 0) as id
                FROM 
                    table(?) ps
                JOIN 
                    "plan_payee_info_6901_0782d9d4-c17f-425c-83f1-2f96fc1c6c0f" ppe on ppe.plan_id = ps.plan_id
                    AND ppe.payee_email_id = ps.payee_email_id
                    AND ppe.period_end_date::DATETIME = ?::DATETIME
                JOIN 
                    "datasheet_data_6901_35330c34-8d5a-4b5d-921a-aa19367deebb" ds 
                    on EQUAL_NULL(ps.datasheet_data:co_1_deal_date::DATETIME, ds.data:co_1_deal_date::DATETIME)
                LEFT JOIN 
                    SETTLEMENT_SNAPSHOT_DATA_6901 ss on 
                        ss.plan_id = ps.plan_id 
                        AND ss.criteria_id = ps.criteria_id
                        AND ss.settlement_rule_id = ?
                        AND ss.payee_email_id = ps.payee_email_id
                        AND ss.line_item_id = ds.data:row_key
                        AND ss.is_locked = TRUE
                        AND ss.period_start_date < ?::DATETIME
                WHERE 
                    ps.plan_id = ? 
                    AND ps.criteria_id = ?
                    AND ps.tier_id_split[0]:tier_id::string <> '-'
                    AND ds.knowledge_begin_date <= ?::DATETIME
                    AND (ds.knowledge_end_date IS NULL OR ds.knowledge_end_date > ?::DATETIME)
                    AND ds.is_deleted = FALSE 
                    AND ds.data:co_1_close_date::DATETIME >= ?::DATETIME
                    AND ds.data:co_1_close_date::DATETIME <= ?::DATETIME
                    AND ds.data is not null
                    AND ss.is_locked is null
            """
        expected_params = [
            "PAYOUT_SNAPSHOT_DATA_6901",
            sync_info.period_end_date,
            str(settlement_rule_id),
            sync_info.period_start_date,
            str(plan_id),
            str(criteria_id),
            sync_info.knowledge_date,
            sync_info.knowledge_date,
            sync_info.period_start_date,
            sync_info.period_end_date,
        ]

        assert criteria is not None
        criteria_info = CriteriaInfo(
            plan_id=plan_id,
            criteria_id=criteria_id,
            date_field="cf_date_field",
            databook_id=criteria["criteria_data__databook_id"],
            datasheet_id=criteria["criteria_data__datasheet_id"],
        )
        rule = SettlementRuleAccessor(CLIENT_ID).get_settlement_rule(settlement_rule_id)
        assert rule is not None
        query_provider = SettlementOnCollectionQueryProvider(sync_info, criteria_info)
        query, params = query_provider.get_query_and_params(rule, '"target_table"')
        assert normalize_query(query) == normalize_query(expected_query)
        assert params == expected_params
