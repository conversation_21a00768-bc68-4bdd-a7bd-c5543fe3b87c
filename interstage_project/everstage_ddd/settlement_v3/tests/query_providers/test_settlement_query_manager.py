from datetime import datetime, timezone
from uuid import UUID, uuid4

import pytest

from commission_engine.accessors.settlement_accessor import SettlementRuleAccessor
from commission_engine.models.settlement_models import SettlementRule
from everstage_ddd.settlement_v3.common.exceptions import InvalidSettlementTypeError
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.settlement_query_manager import (
    SettlementQueryManager,
    get_typecasted_columns_from_datasheet,
    get_variables_used_in_rule_expressions,
)
from everstage_ddd.settlement_v3.tests.utils import normalize_query

CLIENT_ID = 6901
PLAN_ID = UUID("0496f187-f820-4c49-91d7-29320b49d960")
CRITERIA_ID = UUID("0496f187-f820-4c49-91d7-29320b49d960")
SETTLEMENT_RULE_ID_ON_BOOKING = UUID("47837998-0a48-42f7-84ee-4263f197f8ce")
SETTLEMENT_RULE_ID_ON_COLLECTION = UUID("614009ad-011d-4fd8-a628-1f07cc761a80")

SYNC_INFO = SyncInfo(
    client_id=CLIENT_ID,
    e2e_sync_run_id=UUID("0496f187-f820-4c49-91d7-29320b49d960"),
    sync_run_id=uuid4(),
    period_start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
    period_end_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
    knowledge_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
    log_context={},
)
CRITERIA_INFO = CriteriaInfo(
    plan_id=PLAN_ID,
    criteria_id=CRITERIA_ID,
    date_field="co_1_deal_date",
    databook_id=uuid4(),
    datasheet_id=uuid4(),
)


class MockSettlementOnBookingQueryProvider:
    def __init__(self, sync_info: SyncInfo, criteria_info: CriteriaInfo):
        self.sync_info = sync_info
        self.criteria_info = criteria_info

    def get_query_and_params(self, settlement_rule: SettlementRule, table_name: str):
        return "booking_query", ["booking_params"]


class MockSettlementOnCollectionQueryProvider:
    def __init__(self, sync_info: SyncInfo, criteria_info: CriteriaInfo):
        self.sync_info = sync_info
        self.criteria_info = criteria_info

    def get_query_and_params(self, settlement_rule: SettlementRule, table_name: str):
        return "collection_query", ["collection_params"]


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementQueryManager:
    def test_get_insert_query_for_settlement_temp_table_on_booking(self, monkeypatch):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnBookingQueryProvider",
            MockSettlementOnBookingQueryProvider,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnCollectionQueryProvider",
            MockSettlementOnCollectionQueryProvider,
        )

        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        rule = SettlementRuleAccessor(client_id=CLIENT_ID).get_settlement_rule(
            rule_id=SETTLEMENT_RULE_ID_ON_BOOKING
        )
        assert rule is not None
        result = query_manager.get_insert_query_for_settlement_temp_table(
            rule,
            "settlement_temp_table",
        )
        assert result == ("booking_query", ["booking_params"])

    def test_get_insert_query_for_settlement_temp_table_on_collection(
        self, monkeypatch
    ):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnBookingQueryProvider",
            MockSettlementOnBookingQueryProvider,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnCollectionQueryProvider",
            MockSettlementOnCollectionQueryProvider,
        )
        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        rule = SettlementRuleAccessor(client_id=CLIENT_ID).get_settlement_rule(
            rule_id=SETTLEMENT_RULE_ID_ON_COLLECTION
        )
        assert rule is not None
        result = query_manager.get_insert_query_for_settlement_temp_table(
            rule,
            "settlement_temp_table",
        )
        assert result == ("collection_query", ["collection_params"])

    def test_get_query_to_create_settlement_temp_table_exception(self, monkeypatch):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnBookingQueryProvider",
            MockSettlementOnBookingQueryProvider,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.SettlementOnCollectionQueryProvider",
            MockSettlementOnCollectionQueryProvider,
        )
        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        rule = SettlementRuleAccessor(client_id=CLIENT_ID).get_settlement_rule(
            rule_id=SETTLEMENT_RULE_ID_ON_COLLECTION
        )
        assert rule is not None
        rule.settlement_type = None
        with pytest.raises(InvalidSettlementTypeError):
            query_manager.get_insert_query_for_settlement_temp_table(
                rule,
                "settlement_temp_table",
            )

    def test_get_query_to_create_settlement_temp_table(self):
        result = SettlementQueryManager.get_query_to_create_settlement_temp_table(
            "settlement_temp_table"
        )
        expected_query = """
            CREATE OR REPLACE TABLE settlement_temp_table (
                id integer,
                payee_email_id string,
                period_start_date datetime,
                period_end_date datetime,
                commission_row_key string,
                line_item_id string,
                commission_sheet_data variant,
                settlement_sheet_data variant,
                additional_data variant,
                commission double
            )
        """
        assert normalize_query(result) == normalize_query(expected_query)

    def test_get_query_to_drop_settlement_temp_table(self):
        result = SettlementQueryManager.get_query_to_drop_settlement_temp_table(
            "settlement_temp_table"
        )
        expected_query = "DROP TABLE IF EXISTS settlement_temp_table"
        assert normalize_query(result) == normalize_query(expected_query)

    def test_get_payees_without_line_items_query(self):
        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        actual_query, actual_params = (
            query_manager.get_payees_without_line_items_query()
        )
        expected_query = """
            SELECT 
                ps.payee_email_id as payee_email_id, 
                ps.additional_data:conversion_rate::double as conversion_rate,
                ps.additional_data:criteria_name::string as criteria_name,
                ps.additional_data:data_pay_currency::string as payee_currency,
                ps.additional_data:data_full_name::string as payee_name,
                ps.additional_data:payout_frequency::string as payout_freq,
                ps.additional_data:plan_name::string as plan_name,
                ps.additional_data:plan_type::string as plan_type,
                ps.additional_data:data_variable_pay_as_per_period::double as variable_pay,
            FROM 
                table(?) ps
            JOIN 
                "plan_payee_info_6901_0496f187-f820-4c49-91d7-29320b49d960" ppe on ppe.plan_id = ps.plan_id
                AND ppe.payee_email_id = ps.payee_email_id
                AND ppe.period_end_date::DATETIME = ?::DATETIME
            WHERE 
                ps.plan_id = ? 
                AND ps.criteria_id = ? 
                AND ps.period_start_date::DATETIME = ?::DATETIME
                AND ps.period_end_date::DATETIME = ?::DATETIME
                AND ps.line_item_id IS NULL 
                AND ps.tier_id_split[0]:tier_id::string <> '-'
                AND ps.commission = 0
        """
        expected_params = [
            "PAYOUT_SNAPSHOT_DATA_6901",
            SYNC_INFO.period_end_date,
            str(CRITERIA_INFO.plan_id),
            str(CRITERIA_INFO.criteria_id),
            SYNC_INFO.period_start_date,
            SYNC_INFO.period_end_date,
        ]
        assert actual_params == expected_params
        assert normalize_query(actual_query) == normalize_query(expected_query)

    def test_get_select_query_for_settlement_temp_table(self, monkeypatch):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_typecasted_columns_from_datasheet",
            lambda *args, **kwargs: [],
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_variables_used_in_rule_expressions",
            lambda *args, **kwargs: [],
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_snowflake_temp_settlement_table_name",
            lambda *args, **kwargs: "settlement_temp_table",
        )
        rule = SettlementRuleAccessor(client_id=CLIENT_ID).get_settlement_rule(
            rule_id=SETTLEMENT_RULE_ID_ON_BOOKING
        )
        assert rule is not None
        rule.date_field = "co_7_deal_date"
        rule.name = "Settlement Criteria"
        period_label = "January 2024"
        settlement_databook_name = "Settlement Databook"
        settlement_datasheet_name = "Settlement Sheet"
        org_currency = "USD"
        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        actual_query = query_manager.get_select_query_for_settlement_temp_table(
            rule,
            period_label,
            settlement_databook_name,
            settlement_datasheet_name,
            org_currency,
        )
        expected_query = """
            SELECT 
                st.commission_row_key, 
                st.line_item_id,
                st.commission::double as commission, 
                st.commission_sheet_data:co_1_deal_date::datetime as commission_date,
                st.settlement_sheet_data:co_7_deal_date::datetime as settlement_date,
                st.payee_email_id as payee_email_id, 
                '0496f187-f820-4c49-91d7-29320b49d960'::string as commission_plan_id,
                '0496f187-f820-4c49-91d7-29320b49d960'::string as criteria_id, 
                st.period_start_date,
                st.period_end_date,
                st.additional_data:comm_secondary_kd::datetime as secondary_kd,
                st.commission::double as comm_amount,
                st.additional_data:conversion_rate::double as conversion_rate,
                st.additional_data:amount_payee_currency::double as comm_amount_payee_currency,
                st.additional_data:period_label::string as comm_period_label,
                st.additional_data:criteria_name::string as criteria_name,
                st.additional_data:data_pay_currency::string as payee_currency,
                st.additional_data:data_full_name::string as payee_name,
                st.additional_data:payout_frequency::string as payout_freq,
                'January 2024'::string as period_label,
                st.additional_data:plan_name::string as plan_name,
                st.additional_data:plan_type::string as plan_type,
                'Settlement Databook'::string as settlement_databook,
                'Settlement Sheet'::string as settlement_datasheet,
                'Settlement'::string as record_type,
                'USD'::string as org_currency,
                st.additional_data:data_variable_pay_as_per_period::double as variable_pay,
                'Settlement Criteria'::string as settlement_criteria_name
            FROM 
                settlement_temp_table st       
        """
        assert normalize_query(actual_query) == normalize_query(expected_query)

    def test_get_select_query_for_settlement_temp_table_with_single_quoted_string(
        self, monkeypatch
    ):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_typecasted_columns_from_datasheet",
            lambda *args, **kwargs: ["row_key::string", "co_1_deal_date::datetime"],
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_variables_used_in_rule_expressions",
            lambda *args, **kwargs: ["co_1_deal_date"],
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_snowflake_temp_settlement_table_name",
            lambda *args, **kwargs: '"settlement_temp_table"',
        )
        rule = SettlementRuleAccessor(client_id=CLIENT_ID).get_settlement_rule(
            rule_id=SETTLEMENT_RULE_ID_ON_BOOKING
        )
        assert rule is not None
        rule.date_field = "co_7_deal_date"
        rule.name = "Settlement's Criteria"
        period_label = "Jan' 2024"
        settlement_databook_name = "Settlement's Databook"
        settlement_datasheet_name = "Settlement's Sheet"
        org_currency = "US'D"
        query_manager = SettlementQueryManager(SYNC_INFO, CRITERIA_INFO)
        actual_query = query_manager.get_select_query_for_settlement_temp_table(
            rule,
            period_label,
            settlement_databook_name,
            settlement_datasheet_name,
            org_currency,
        )
        expected_query = """
            SELECT 
                st.commission_row_key, 
                st.line_item_id,
                st.commission::double as commission, 
                st.commission_sheet_data:co_1_deal_date::datetime as commission_date,
                st.settlement_sheet_data:co_7_deal_date::datetime as settlement_date,
                st.payee_email_id as payee_email_id, 
                '0496f187-f820-4c49-91d7-29320b49d960'::string as commission_plan_id,
                '0496f187-f820-4c49-91d7-29320b49d960'::string as criteria_id, 
                st.period_start_date,
                st.period_end_date,
                st.additional_data:comm_secondary_kd::datetime as secondary_kd,
                st.commission::double as comm_amount,
                st.additional_data:conversion_rate::double as conversion_rate,
                st.additional_data:amount_payee_currency::double as comm_amount_payee_currency,
                st.additional_data:period_label::string as comm_period_label,
                st.additional_data:criteria_name::string as criteria_name,
                st.additional_data:data_pay_currency::string as payee_currency,
                st.additional_data:data_full_name::string as payee_name,
                st.additional_data:payout_frequency::string as payout_freq,
                'Jan'' 2024'::string as period_label,
                st.additional_data:plan_name::string as plan_name,
                st.additional_data:plan_type::string as plan_type,
                'Settlement''s Databook'::string as settlement_databook,
                'Settlement''s Sheet'::string as settlement_datasheet,
                'Settlement'::string as record_type,
                'US''D'::string as org_currency,
                st.additional_data:data_variable_pay_as_per_period::double as variable_pay,
                'Settlement''s Criteria'::string as settlement_criteria_name,
                row_key::string,
                co_1_deal_date::datetime
            FROM 
                "settlement_temp_table" st       
        """
        assert normalize_query(actual_query) == normalize_query(expected_query)

    def test_get_count_query_for_settlement_temp_table(self):
        result = SettlementQueryManager.get_count_query_for_settlement_temp_table(
            "settlement_temp_table"
        )
        expected_query = "SELECT COUNT(*) FROM settlement_temp_table"
        assert normalize_query(result) == normalize_query(expected_query)

        result = SettlementQueryManager.get_count_query_for_settlement_temp_table(
            '"settlement_temp_table"'
        )
        expected_query = 'SELECT COUNT(*) FROM "settlement_temp_table"'
        assert normalize_query(result) == normalize_query(expected_query)

    @pytest.mark.parametrize(
        "variables, variable_type, table_alias, data_column_name, expected_result",
        [
            ([], dict(), "ds", "data", []),
            (
                [
                    {"system_name": "var1", "dtype": "INT"},
                    {"system_name": "var2", "dtype": "STRING"},
                    {"system_name": "var3", "dtype": "DATETIME"},
                    {"system_name": "var4", "dtype": "BOOLEAN"},
                    {"system_name": "var5", "dtype": "STRING"},
                ],
                {
                    "var1": "DOUBLE",
                    "var2": "VARCHAR",
                    "var3": "DATETIME",
                    "var4": "BOOLEAN",
                    "var5": "STRING",
                },
                "ds",
                "data",
                [
                    "ds.data:var1::DOUBLE as var1",
                    "ds.data:var2::VARCHAR as var2",
                    "ds.data:var3::DATETIME as var3",
                    "ds.data:var4::BOOLEAN as var4",
                    "ds.data:var5::STRING as var5",
                ],
            ),
        ],
    )
    def test_get_typecasted_columns_from_datasheet(  # noqa: PLR0913
        self,
        monkeypatch,
        variables,
        variable_type,
        table_alias,
        data_column_name,
        expected_result,
    ):
        databook_id = UUID("12345678-1234-5678-1234-************")
        datasheet_id = UUID("*************-8765-4321-************")
        table_alias = "ds"

        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.get_datasheet_snowflake_data_type_map",
            lambda *args, **kwargs: variable_type,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.DatasheetVariableAccessor.get_latest_system_name_and_dtype_for_datasheet",
            lambda *args, **kwargs: variables,
        )
        required_columns = [variable["system_name"] for variable in variables]
        columns = get_typecasted_columns_from_datasheet(
            SYNC_INFO.client_id,
            databook_id,
            datasheet_id,
            table_alias,
            data_column_name,
            required_columns,
        )

        assert columns == expected_result

    def test_get_variables_used_in_rule_expressions(self, monkeypatch):
        def mock_extractor(_, ast):
            return ast["columns"]

        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.create_ast",
            lambda expr: {"ast": {"columns": expr["columns"]}},
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.query_providers.settlement_query_manager.VariableExtractor.get_variables_used",
            mock_extractor,
        )

        # This expression is not valid. This is just for mocking and testing the function.
        # We're only checking if the correct function is called and valid result is returned.
        amount_expr = {"ast": {"columns": ["var1", "var2"]}}
        settlement_flag_expr = {"ast": {"columns": ["var3", "var4"]}}

        result = get_variables_used_in_rule_expressions(
            amount_expr,
            settlement_flag_expr,
        )
        assert len(result) == 4
        assert set(result) == {"var1", "var2", "var3", "var4"}
