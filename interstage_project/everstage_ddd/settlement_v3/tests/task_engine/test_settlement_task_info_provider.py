from datetime import datetime, timezone
from uuid import UUID

import pytest

from commission_engine.utils.date_utils import end_of_day, make_aware_wrapper
from everstage_ddd.settlement_v3.common.enums import TaskPeriod
from everstage_ddd.settlement_v3.common.param_types import SettlementTaskInfo
from everstage_ddd.settlement_v3.task_engine.settlement_task_info_provider import (
    SettlementTaskInfoProvider,
)

CLIENT_ID = 6901


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementTaskService:

    @pytest.mark.parametrize(
        "curr_date, payees, task_period, expected_result",
        [
            (
                datetime(2026, 12, 1, tzinfo=timezone.utc),
                None,
                TaskPeriod.CURRENT_AND_PREVIOUS,
                (
                    [
                        SettlementTaskInfo(
                            plan_id=UUID("8fa03452-f6f0-4075-9f33-ba5ceecf2f65"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("1f80ed18-e360-4a4a-9add-09220f006083")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 12, 31, tzinfo=timezone.utc))
                            ),
                        )
                    ],
                    [],
                ),
            ),
            # current and previous period tasks (no valid previous period tasks) with datetime parameter
            (
                datetime(2025, 1, 1, tzinfo=timezone.utc),
                ["<EMAIL>", "<EMAIL>"],
                TaskPeriod.CURRENT_AND_PREVIOUS,
                (
                    [],
                    [
                        SettlementTaskInfo(
                            plan_id=UUID("580c76fe-8ecb-4c06-9b96-66a33aff96b0"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("f23fc55d-d612-44fe-848b-aeac7aab0811")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 1, 31, tzinfo=timezone.utc))
                            ),
                        ),
                        SettlementTaskInfo(
                            plan_id=UUID("b123cf80-392e-49b1-813d-d066ce58d0a5"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("bcce2f01-c394-4508-ad52-a9b640f097b1")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 1, 31, tzinfo=timezone.utc))
                            ),
                        ),
                        SettlementTaskInfo(
                            plan_id=UUID("02d0a248-acae-46ef-a011-c0e72202799c"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("17e4569b-b340-4c83-b749-2f687f3cfb54")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 1, 31, tzinfo=timezone.utc))
                            ),
                        ),
                        SettlementTaskInfo(
                            plan_id=UUID("c50b7fcb-7298-4a90-a4d5-955a094587f7"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("0496f187-f820-4c49-91d7-29320b49d960")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 1, 14, tzinfo=timezone.utc))
                            ),
                        ),
                    ],
                ),
            ),
            # current period tasks with datetime parameter
            (
                datetime(2025, 2, 16, tzinfo=timezone.utc),
                ["<EMAIL>"],
                TaskPeriod.CURRENT_AND_PREVIOUS,
                (
                    [
                        SettlementTaskInfo(
                            plan_id=UUID("c50b7fcb-7298-4a90-a4d5-955a094587f7"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("0496f187-f820-4c49-91d7-29320b49d960")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 1, 29, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 2, 11, tzinfo=timezone.utc))
                            ),
                        )
                    ],
                    [
                        SettlementTaskInfo(
                            plan_id=UUID("c50b7fcb-7298-4a90-a4d5-955a094587f7"),
                            payee_list=["<EMAIL>"],
                            criteria_ids=[UUID("0496f187-f820-4c49-91d7-29320b49d960")],
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 12, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                end_of_day(datetime(2025, 2, 25, tzinfo=timezone.utc))
                            ),
                        )
                    ],
                ),
            ),
            # no valid current period tasks
            (
                datetime(2025, 2, 28, tzinfo=timezone.utc),
                ["<EMAIL>"],
                TaskPeriod.CURRENT,
                ([], []),
            ),
            # no valid current and previous period tasks
            (
                datetime(2025, 2, 28, tzinfo=timezone.utc),
                ["<EMAIL>"],
                TaskPeriod.CURRENT_AND_PREVIOUS,
                ([], []),
            ),
        ],
    )
    def test_get_settlement_task_info_for_task_period(  # noqa: PLR0913
        self,
        monkeypatch,
        curr_date,
        payees,
        task_period,
        expected_result,
    ):

        class MockDateTime:
            @staticmethod
            def now():
                return datetime(2025, 2, 16)  # noqa: DTZ001

        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.datetime",
            MockDateTime,
        )
        task_info_provider = SettlementTaskInfoProvider(
            CLIENT_ID, curr_date, payees, task_period
        )
        actual_prev, actual_curr = task_info_provider.get_settlement_task_info()
        expected_prev, expected_curr = expected_result

        assert len(actual_prev) == len(expected_prev)
        assert len(actual_curr) == len(expected_curr)

        sorted_actual_prev = sorted(actual_prev, key=lambda x: x.plan_id)
        sorted_expected_prev = sorted(expected_prev, key=lambda x: x.plan_id)
        sorted_actual_curr = sorted(actual_curr, key=lambda x: x.plan_id)
        sorted_expected_curr = sorted(expected_curr, key=lambda x: x.plan_id)

        for actual_task, expected_task in zip(sorted_actual_prev, sorted_expected_prev):
            assert actual_task.plan_id == expected_task.plan_id
            assert set(actual_task.payee_list) == set(expected_task.payee_list)
            assert set(actual_task.criteria_ids) == set(expected_task.criteria_ids)
            assert actual_task.period_start_date == expected_task.period_start_date

        for actual_task, expected_task in zip(sorted_actual_curr, sorted_expected_curr):
            assert actual_task.plan_id == expected_task.plan_id
            assert set(actual_task.payee_list) == set(expected_task.payee_list)
            assert set(actual_task.criteria_ids) == set(expected_task.criteria_ids)
            assert actual_task.period_start_date == expected_task.period_start_date
            assert actual_task.period_end_date == expected_task.period_end_date
