from datetime import datetime, timezone
from uuid import UUID

import pytest
from celery import group

from commission_engine.utils.date_utils import (
    end_of_day,
    last_day_of_month,
    make_aware_wrapper,
)
from everstage_ddd.settlement_v3.common.enums import ExecutionMode
from everstage_ddd.settlement_v3.common.param_types import (
    SettlementTaskInfo,
    SettlementTaskParams,
)
from everstage_ddd.settlement_v3.task_engine.settlement_task_service import (
    get_all_valid_frequency_to_payee_map,
    get_previous_payout_frequencies_of_payees,
    get_settlement_task_group,
    get_settlement_task_info_list_for_period,
    get_valid_frequency_to_payee_map_for_given_payees,
)

CLIENT_ID = 6901


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementTaskService:
    @pytest.mark.parametrize(
        "period_start_date, period_end_date, payee_list, payout_frequency, expected_result",
        [
            # Two payees with settlement rules, one payee without settlement rules
            (
                make_aware_wrapper(datetime(2025, 1, 1, tzinfo=timezone.utc)),
                make_aware_wrapper(
                    last_day_of_month(datetime(2025, 1, 1, tzinfo=timezone.utc))
                ),
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "Monthly",
                [
                    SettlementTaskInfo(
                        plan_id=UUID("b123cf80-392e-49b1-813d-d066ce58d0a5"),
                        payee_list=["<EMAIL>", "<EMAIL>"],
                        criteria_ids=[UUID("bcce2f01-c394-4508-ad52-a9b640f097b1")],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 1, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(
                                datetime(2025, 1, 31, tzinfo=timezone.utc)
                            )
                        ),
                    ),
                ],
            ),
            # Payees without settlement rules in the current period
            (
                make_aware_wrapper(datetime(2023, 1, 1, tzinfo=timezone.utc)),
                make_aware_wrapper(
                    last_day_of_month(datetime(2023, 1, 1, tzinfo=timezone.utc))
                ),
                [
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "Monthly",
                [],
            ),
            # payee in custom period
            (
                make_aware_wrapper(datetime(2025, 1, 1, tzinfo=timezone.utc)),
                make_aware_wrapper(
                    end_of_day(datetime(2025, 1, 14, tzinfo=timezone.utc))
                ),
                [
                    "<EMAIL>",
                ],
                "11111111-1111-1111-1111-111111111111",
                [
                    SettlementTaskInfo(
                        plan_id=UUID("c50b7fcb-7298-4a90-a4d5-955a094587f7"),
                        payee_list=["<EMAIL>"],
                        criteria_ids=[UUID("0496f187-f820-4c49-91d7-29320b49d960")],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 1, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            end_of_day(datetime(2025, 1, 14, tzinfo=timezone.utc))
                        ),
                    ),
                ],
            ),
            # payees with multiple criterias with settlement rules
            (
                make_aware_wrapper(datetime(2025, 2, 1, tzinfo=timezone.utc)),
                make_aware_wrapper(
                    last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                ),
                [
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "Monthly",
                [
                    SettlementTaskInfo(
                        plan_id=UUID("b123cf80-392e-49b1-813d-d066ce58d0a5"),
                        payee_list=["<EMAIL>", "<EMAIL>"],
                        criteria_ids=[UUID("bcce2f01-c394-4508-ad52-a9b640f097b1")],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 2, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                    SettlementTaskInfo(
                        plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                        payee_list=["<EMAIL>"],
                        criteria_ids=[
                            UUID("0ee8deec-7ffa-400a-96f6-894101059bed"),
                            UUID("29990d68-2568-4b89-b644-c121d178ddef"),
                            UUID("3fef08ab-8b30-41ef-a077-9799f87b7bb5"),
                            UUID("c1139d47-0eb9-4ee5-a006-31339e875c2b"),
                        ],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 2, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                    SettlementTaskInfo(
                        plan_id=UUID("56ce88ff-3aa2-4c21-8a3f-4581ef6203e5"),
                        payee_list=["<EMAIL>"],
                        criteria_ids=[
                            UUID("53694e0c-f578-4aa8-a1a8-0a34f4082206"),
                            UUID("890d9cab-45a3-4d0c-b45b-3e4a6eac171a"),
                            UUID("8cf75a09-00e4-4e62-aa83-e41bb2c07744"),
                            UUID("d798b4d0-9c87-46f5-be02-d22663c5b605"),
                        ],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 2, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                ],
            ),
            (
                make_aware_wrapper(datetime(2025, 2, 1, tzinfo=timezone.utc)),
                make_aware_wrapper(
                    last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                ),
                [],
                "Monthly",
                [],
            ),
        ],
    )
    def test_get_settlement_criteria_details_for_period(  # noqa: PLR0913
        self,
        period_start_date,
        period_end_date,
        payee_list,
        payout_frequency,
        expected_result,
    ):
        actual_result = get_settlement_task_info_list_for_period(
            CLIENT_ID, period_start_date, period_end_date, payee_list
        )

        assert len(actual_result) == len(expected_result)

        actual_result_by_plan_id = {task.plan_id: task for task in actual_result}
        expected_result_by_plan_id = {task.plan_id: task for task in expected_result}

        for plan_id, task in actual_result_by_plan_id.items():
            assert plan_id in expected_result_by_plan_id
            assert set(task.payee_list) == set(
                expected_result_by_plan_id[plan_id].payee_list
            )
            assert set(task.criteria_ids) == set(
                expected_result_by_plan_id[plan_id].criteria_ids
            )
            assert (
                task.period_start_date
                == expected_result_by_plan_id[plan_id].period_start_date
            )
            assert (
                task.period_end_date
                == expected_result_by_plan_id[plan_id].period_end_date
            )

    @pytest.mark.parametrize(
        "task_info_list, e2e_sync_run_id, wrapper_sync_run_id, expected_result_args",
        [
            (
                [],
                UUID("11111111-1111-1111-1111-111111111111"),
                UUID("*************-2222-2222-************"),
                [],
            ),
            (
                [
                    SettlementTaskInfo(
                        plan_id=UUID("b123cf80-392e-49b1-813d-d066ce58d0a5"),
                        payee_list=["<EMAIL>", "<EMAIL>"],
                        criteria_ids=[UUID("bcce2f01-c394-4508-ad52-a9b640f097b1")],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 2, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                    SettlementTaskInfo(
                        plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                        payee_list=["<EMAIL>"],
                        criteria_ids=[
                            UUID("0ee8deec-7ffa-400a-96f6-894101059bed"),
                            UUID("29990d68-2568-4b89-b644-c121d178ddef"),
                            UUID("3fef08ab-8b30-41ef-a077-9799f87b7bb5"),
                            UUID("c1139d47-0eb9-4ee5-a006-31339e875c2b"),
                        ],
                        period_start_date=make_aware_wrapper(
                            datetime(2025, 2, 1, tzinfo=timezone.utc)
                        ),
                        period_end_date=make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                ],
                UUID("ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"),
                UUID("5e3164d0-b223-4575-a4f8-080dd5b94a70"),
                [
                    (
                        SettlementTaskParams(
                            client_id=6901,
                            e2e_sync_run_id=UUID(
                                "ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"
                            ),
                            wrapper_sync_run_id=UUID(
                                "5e3164d0-b223-4575-a4f8-080dd5b94a70"
                            ),
                            plan_id=UUID("b123cf80-392e-49b1-813d-d066ce58d0a5"),
                            criteria_id=UUID("bcce2f01-c394-4508-ad52-a9b640f097b1"),
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                last_day_of_month(
                                    datetime(2025, 2, 28, tzinfo=timezone.utc)
                                )
                            ),
                            execution_mode=ExecutionMode.ACTUAL_RUN,
                        ),
                    ),
                    (
                        SettlementTaskParams(
                            client_id=6901,
                            e2e_sync_run_id=UUID(
                                "ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"
                            ),
                            wrapper_sync_run_id=UUID(
                                "5e3164d0-b223-4575-a4f8-080dd5b94a70"
                            ),
                            plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                            criteria_id=UUID("0ee8deec-7ffa-400a-96f6-894101059bed"),
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                last_day_of_month(
                                    datetime(2025, 2, 1, tzinfo=timezone.utc)
                                )
                            ),
                            execution_mode=ExecutionMode.ACTUAL_RUN,
                        ),
                    ),
                    (
                        SettlementTaskParams(
                            client_id=6901,
                            e2e_sync_run_id=UUID(
                                "ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"
                            ),
                            wrapper_sync_run_id=UUID(
                                "5e3164d0-b223-4575-a4f8-080dd5b94a70"
                            ),
                            plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                            criteria_id=UUID("29990d68-2568-4b89-b644-c121d178ddef"),
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                last_day_of_month(
                                    datetime(2025, 2, 28, tzinfo=timezone.utc)
                                )
                            ),
                            execution_mode=ExecutionMode.ACTUAL_RUN,
                        ),
                    ),
                    (
                        SettlementTaskParams(
                            client_id=6901,
                            e2e_sync_run_id=UUID(
                                "ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"
                            ),
                            wrapper_sync_run_id=UUID(
                                "5e3164d0-b223-4575-a4f8-080dd5b94a70"
                            ),
                            plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                            criteria_id=UUID("3fef08ab-8b30-41ef-a077-9799f87b7bb5"),
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                last_day_of_month(
                                    datetime(2025, 2, 28, tzinfo=timezone.utc)
                                )
                            ),
                            execution_mode=ExecutionMode.ACTUAL_RUN,
                        ),
                    ),
                    (
                        SettlementTaskParams(
                            client_id=6901,
                            e2e_sync_run_id=UUID(
                                "ec69b28a-7c75-4ae2-bb6f-1d06dab3d934"
                            ),
                            wrapper_sync_run_id=UUID(
                                "5e3164d0-b223-4575-a4f8-080dd5b94a70"
                            ),
                            plan_id=UUID("1cfb911f-8d31-43e8-85de-e8d389d7a8df"),
                            criteria_id=UUID("c1139d47-0eb9-4ee5-a006-31339e875c2b"),
                            period_start_date=make_aware_wrapper(
                                datetime(2025, 2, 1, tzinfo=timezone.utc)
                            ),
                            period_end_date=make_aware_wrapper(
                                last_day_of_month(
                                    datetime(2025, 2, 28, tzinfo=timezone.utc)
                                )
                            ),
                            execution_mode=ExecutionMode.ACTUAL_RUN,
                        ),
                    ),
                ],
            ),
        ],
    )
    def test_get_settlement_task_group(  # noqa: PLR0913
        self,
        monkeypatch,
        task_info_list,
        e2e_sync_run_id,
        wrapper_sync_run_id,
        expected_result_args,
    ):
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.get_settlement_queue_name",
            lambda x: "test_queue",
        )
        actual_result = get_settlement_task_group(
            CLIENT_ID,
            e2e_sync_run_id,
            wrapper_sync_run_id,
            task_info_list,
            ExecutionMode.ACTUAL_RUN,
        )
        assert isinstance(actual_result, group)
        actual_tasks = actual_result.tasks
        assert actual_tasks is not None
        assert len(actual_tasks) == len(expected_result_args)

        for actual_task, expected_task_args in zip(actual_tasks, expected_result_args):
            assert actual_task is not None
            assert actual_task.args == expected_task_args
            assert actual_task.options == {"queue": "test_queue"}

    @pytest.mark.parametrize(
        "current_frequency_to_payees_map, frequency_to_current_period_map, expected_result",
        [
            # Empty maps
            (
                {},
                {},
                {},
            ),
            # Same current and previous frequencies
            (
                {
                    "monthly": ["<EMAIL>", "<EMAIL>"],
                },
                {
                    "monthly": (
                        datetime(2025, 2, 1, tzinfo=timezone.utc),
                        make_aware_wrapper(
                            last_day_of_month(datetime(2025, 2, 1, tzinfo=timezone.utc))
                        ),
                    ),
                },
                {
                    "monthly": ["<EMAIL>", "<EMAIL>"],
                },
            ),
            # Different current and previous frequencies including custom frequency
            (
                {
                    "monthly": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                    "quarterly": ["<EMAIL>"],
                    "b910c62e-8051-4ae1-9e3f-d6e54bfe47e9": ["<EMAIL>"],
                },
                {
                    "monthly": (
                        datetime(2025, 4, 1, tzinfo=timezone.utc),
                        make_aware_wrapper(
                            last_day_of_month(datetime(2025, 4, 1, tzinfo=timezone.utc))
                        ),
                    ),
                    "quarterly": (
                        datetime(2025, 4, 1, tzinfo=timezone.utc),
                        make_aware_wrapper(
                            last_day_of_month(datetime(2025, 6, 1, tzinfo=timezone.utc))
                        ),
                    ),
                    "b910c62e-8051-4ae1-9e3f-d6e54bfe47e9": (
                        datetime(2025, 4, 1, tzinfo=timezone.utc),
                        make_aware_wrapper(
                            end_of_day(datetime(2025, 4, 16, tzinfo=timezone.utc))
                        ),
                    ),
                },
                {
                    "monthly": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                    "00acf264-2cde-4f40-b7c2-edcb7e4be23e": ["<EMAIL>"],
                },
            ),
            # No valid previous periods
            (
                {
                    "monthly": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                },
                {
                    "monthly": (
                        datetime(2023, 4, 1, tzinfo=timezone.utc),
                        make_aware_wrapper(
                            last_day_of_month(datetime(2023, 4, 1, tzinfo=timezone.utc))
                        ),
                    ),
                },
                {},
            ),
        ],
    )
    def test_get_previous_payout_frequencies_of_payees(
        self,
        current_frequency_to_payees_map,
        frequency_to_current_period_map,
        expected_result,
    ):
        actual_result = get_previous_payout_frequencies_of_payees(
            CLIENT_ID, current_frequency_to_payees_map, frequency_to_current_period_map
        )
        assert actual_result == expected_result

    @pytest.mark.parametrize(
        "payroll, locked_payees, expected_result",
        [
            (
                [],
                [],
                {},
            ),
            (
                [],
                ["<EMAIL>", "<EMAIL>"],
                {},
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                ["<EMAIL>"],
                {
                    "monthly": ["<EMAIL>", "<EMAIL>"],
                    "11111111-1111-1111-1111-111111111111": ["<EMAIL>"],
                },
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
                {},
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                [],
                {
                    "monthly": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                    "11111111-1111-1111-1111-111111111111": ["<EMAIL>"],
                },
            ),
        ],
    )
    def test_get_all_valid_frequency_to_payee_map(
        self, monkeypatch, payroll, locked_payees, expected_result
    ):
        dt = datetime(2025, 2, 1, tzinfo=timezone.utc)

        class MockPayroll:
            def __init__(self, employee_email_id, payout_frequency):
                self.employee_email_id = employee_email_id
                self.payout_frequency = payout_frequency

        class MockSettlementLockAccessor:
            def __init__(self, client_id):
                assert client_id == CLIENT_ID

            def get_all_locked_payees_in_date(self, curr_date):
                assert curr_date == dt
                return locked_payees

        def mock_get_valid_employees_for_sync(client_id, curr_date):
            assert client_id == CLIENT_ID
            assert curr_date == dt
            return [
                MockPayroll(rec["employee_email_id"], rec["payout_frequency"])
                for rec in payroll
            ]

        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.get_valid_employees_for_sync",
            mock_get_valid_employees_for_sync,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.SettlementLockAccessor",
            MockSettlementLockAccessor,
        )
        actual_result = get_all_valid_frequency_to_payee_map(CLIENT_ID, dt)
        assert actual_result == expected_result

    @pytest.mark.parametrize(
        "payroll, locked_payees, expected_result",
        [
            (
                [],
                [],
                {},
            ),
            (
                [],
                ["<EMAIL>", "<EMAIL>"],
                {},
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                ["<EMAIL>"],
                {
                    "monthly": ["<EMAIL>", "<EMAIL>"],
                    "11111111-1111-1111-1111-111111111111": ["<EMAIL>"],
                },
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
                {},
            ),
            (
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "11111111-1111-1111-1111-111111111111",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "payout_frequency": "Monthly",
                    },
                ],
                [],
                {
                    "monthly": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                    "11111111-1111-1111-1111-111111111111": ["<EMAIL>"],
                },
            ),
        ],
    )
    def test_get_valid_frequency_to_payee_map_for_given_payees(
        self, monkeypatch, payroll, locked_payees, expected_result
    ):
        dt = datetime(2025, 2, 1, tzinfo=timezone.utc)
        all_payees = [rec["employee_email_id"] for rec in payroll]
        unlocked_payee_payroll = [
            rec for rec in payroll if rec["employee_email_id"] not in locked_payees
        ]

        class MockSettlementLockAccessor:
            def __init__(self, client_id):
                assert client_id == CLIENT_ID

            def get_locked_payees_from_list(self, payee_list, curr_date):
                assert curr_date == dt
                assert set(payee_list) == set(all_payees)
                return locked_payees

        class MockEmployeePayrollAccessor:
            def __init__(self, client_id):
                assert client_id == CLIENT_ID

            def get_employee_payroll(
                self, date, employee_email_ids, as_dicts=True, projection=None
            ):
                assert date == dt
                assert set(employee_email_ids) == set(
                    [rec["employee_email_id"] for rec in unlocked_payee_payroll]
                )
                assert as_dicts is True
                assert projection == ["employee_email_id", "payout_frequency"]
                return [
                    {
                        "employee_email_id": rec["employee_email_id"],
                        "payout_frequency": rec["payout_frequency"],
                    }
                    for rec in unlocked_payee_payroll
                ]

        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.EmployeePayrollAccessor",
            MockEmployeePayrollAccessor,
        )
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.settlement_task_service.SettlementLockAccessor",
            MockSettlementLockAccessor,
        )
        result = get_valid_frequency_to_payee_map_for_given_payees(
            CLIENT_ID, dt, all_payees
        )

        assert result == expected_result
