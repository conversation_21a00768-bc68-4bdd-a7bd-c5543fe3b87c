from datetime import datetime, timezone
from unittest.mock import patch
from uuid import UUID, uuid4

import pandas as pd
import pytest

from everstage_ddd.settlement_v3.common.enums import ExecutionMode
from everstage_ddd.settlement_v3.task_engine.utils import (
    SettlementETLStatusReaderAccessor,
    StrInSingleQuote,
    add_additional_data_to_dataframe,
    add_payee_list_to_plan_payee_period_cache,
    append_batch_clause_to_query,
    cache,
    calculate_batch_ranges,
    get_cache_key_for_payee_list_from_plan_payee_period_table,
    get_databooks_and_datasheets_name,
    get_escaped_single_quoted_string,
    get_payee_list_from_plan_payee_period_table,
    get_settlement_kd,
    get_settlement_kd_cache_key,
    get_settlement_period_label,
    get_settlement_queue_name,
    get_snowflake_plan_payee_period_table_name,
    get_snowflake_temp_settlement_table_name,
    update_job_count_for_settlement_process,
    write_settlement_data_to_s3,
)
from everstage_ddd.settlement_v3.tests.utils import normalize_query
from interstage_project.celery import TaskGroupEnum

CLIENT_ID = 6901


@pytest.fixture
def sample_dataframe_for_additional_data():
    # Create a DataFrame with the derived columns plus an extra column
    data = {
        "record_type": [1, 2],
        "payout_freq": ["monthly", "yearly"],
        "period_label": ["2020", "2021"],
        "comm_period_label": ["Q1", "Q2"],
        "plan_name": ["Plan A", "Plan B"],
        "criteria_name": ["Criteria 1", "Criteria 2"],
        "settlement_criteria_name": ["Sett Crit 1", "Sett Crit 2"],
        "plan_type": ["Type A", "Type B"],
        "settlement_databook": ["Book A", "Book B"],
        "settlement_datasheet": ["Sheet A", "Sheet B"],
        "payee_name": ["Alice", "Bob"],
        "org_currency": ["USD", "EUR"],
        "payee_currency": ["USD", "EUR"],
        "conversion_rate": [1.0, 0.9],
        "variable_pay": [100, 200],
        "amount_payee_currency": [100, 180],
        "comm_amount": [10, 20],
        "comm_amount_payee_currency": [10, 18],
        "locked_kd": [False, True],
        "is_locked": [False, False],
        "other_column": [999, 888],  # extra column that should remain
    }
    return pd.DataFrame(data)


@pytest.mark.django_db
@pytest.mark.settlement_v3
class TestSettlementTaskUtils:
    """
    Unit tests for task_engine.utils
    """

    @pytest.mark.parametrize(
        "string, expected_result",
        [
            ("test", "'test'"),
            ("test's", "'test's'"),
        ],
    )
    def test_str_in_single_quote(self, string, expected_result):
        assert repr(StrInSingleQuote(string)) == expected_result

    @pytest.mark.parametrize(
        "client_id, e2e_sync_run_id, expected_result",
        [
            (
                CLIENT_ID,
                UUID("123e4567-e89b-12d3-a456-************"),
                "6901_123e4567-e89b-12d3-a456-************_settlement_kd",
            ),
        ],
    )
    def test_get_settlement_kd_cache_key(
        self, client_id, e2e_sync_run_id, expected_result
    ):
        assert (
            get_settlement_kd_cache_key(client_id, e2e_sync_run_id) == expected_result
        )

    def test_get_settlement_kd_cache_hit(self, monkeypatch):
        mock_kd = datetime.now(timezone.utc)
        monkeypatch.setattr(cache, "get", lambda x: mock_kd)
        assert get_settlement_kd(CLIENT_ID, uuid4(), uuid4()) == mock_kd

    def test_get_settlement_kd_cache_miss(self, monkeypatch):
        mock_kd = datetime.now(timezone.utc)
        monkeypatch.setattr(cache, "get", lambda x: None)
        monkeypatch.setattr(cache, "set", lambda x, y, timeout: None)
        monkeypatch.setattr(
            SettlementETLStatusReaderAccessor,
            "get_primary_kd",
            lambda x, y, z: mock_kd,
        )

        assert get_settlement_kd(CLIENT_ID, uuid4(), uuid4()) == mock_kd

    def test_update_job_count_for_settlement_process(self):
        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()
        task_count = 10

        with patch(
            "everstage_ddd.settlement_v3.task_engine.utils.SettlementETLStatusAccessor"
        ) as mock_settlement_etl_status_accessor:
            instance = mock_settlement_etl_status_accessor.return_value
            update_job_count_for_settlement_process(
                CLIENT_ID, e2e_sync_run_id, sync_run_id, task_count
            )

            mock_settlement_etl_status_accessor.assert_called_once_with(
                CLIENT_ID, e2e_sync_run_id, sync_run_id
            )
            instance.update_job_count.assert_called_once_with(task_count)

    def test_get_settlement_queue_name(self):
        expected_subscription_plan = "premium"
        expected_queue_name = "settlement_queue_premium"

        with patch(
            "everstage_ddd.settlement_v3.task_engine.utils.get_client_subscription_plan",
            return_value=expected_subscription_plan,
        ) as mock_get_plan, patch(
            "everstage_ddd.settlement_v3.task_engine.utils.get_queue_name_respect_to_task_group",
            return_value=expected_queue_name,
        ) as mock_get_queue:

            result = get_settlement_queue_name(CLIENT_ID)

            mock_get_plan.assert_called_once_with(CLIENT_ID)
            mock_get_queue.assert_called_once_with(
                CLIENT_ID, expected_subscription_plan, TaskGroupEnum.SETTLEMENT.value
            )
            assert result == expected_queue_name

    @pytest.mark.parametrize(
        "payout_frequency, fiscal_start, period_start_date, period_end_date, expected",
        [
            (
                "Monthly",
                4,
                datetime(2025, 4, 1, tzinfo=timezone.utc),
                datetime(2025, 4, 30, 23, 59, 59, tzinfo=timezone.utc),
                "April 2025",
            ),
            (
                "Quarterly",
                4,
                datetime(2025, 7, 1, tzinfo=timezone.utc),
                datetime(2025, 9, 30, 23, 59, 59, tzinfo=timezone.utc),
                "Q2 2026",
            ),
            (
                "Annual",
                4,
                datetime(2025, 4, 1, tzinfo=timezone.utc),
                datetime(2026, 3, 31, 23, 59, 59, tzinfo=timezone.utc),
                "FY 2026",
            ),
            (
                "Halfyearly",
                4,
                datetime(2025, 4, 1, tzinfo=timezone.utc),
                datetime(2025, 9, 30, 23, 59, 59, tzinfo=timezone.utc),
                "H1 2026",
            ),
            (
                "Monthly",
                1,
                datetime(2025, 1, 1, tzinfo=timezone.utc),
                datetime(2025, 1, 31, 23, 59, 59, tzinfo=timezone.utc),
                "January 2025",
            ),
            (
                "Quarterly",
                1,
                datetime(2025, 4, 1, tzinfo=timezone.utc),
                datetime(2025, 6, 30, 23, 59, 59, tzinfo=timezone.utc),
                "Q2 2025",
            ),
            (
                "Annual",
                1,
                datetime(2025, 1, 1, tzinfo=timezone.utc),
                datetime(2025, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
                "FY 2025",
            ),
            (
                "Halfyearly",
                1,
                datetime(2025, 1, 1, tzinfo=timezone.utc),
                datetime(2025, 6, 30, 23, 59, 59, tzinfo=timezone.utc),
                "H1 2025",
            ),
            (
                "123e4567-e89b-12d3-a456-************",
                1,
                datetime(2025, 2, 15, tzinfo=timezone.utc),
                datetime(2025, 2, 28, 23, 59, 59, tzinfo=timezone.utc),
                "Feb 15, 2025 - Feb 28, 2025",
            ),
            (
                None,
                1,
                datetime(2025, 2, 15, tzinfo=timezone.utc),
                datetime(2025, 2, 28, 23, 59, 59, tzinfo=timezone.utc),
                None,
            ),
        ],
    )
    def test_get_settlement_period_label(  # noqa: PLR0913
        self,
        payout_frequency,
        fiscal_start,
        period_start_date,
        period_end_date,
        expected,
    ):
        result = get_settlement_period_label(
            payout_frequency, fiscal_start, period_start_date, period_end_date
        )
        assert result == expected

    @pytest.mark.parametrize(
        "datasheet_ids, databook_ids, expected_datasheet_names, expected_databook_names",
        [
            (
                [
                    UUID("1ab14041-efba-41df-8bfc-a7aba0122c6c"),
                    UUID("9e7fccc4-169b-4169-a3f2-5d2c834cdb32"),
                ],
                [
                    UUID("c06b2e0b-9228-46db-b3cb-f2ee10a577c3"),
                    UUID("386dd43b-1a9d-4fe0-aae3-9c687a6f7041"),
                ],
                {
                    UUID(
                        "1ab14041-efba-41df-8bfc-a7aba0122c6c"
                    ): "Inter commission report",
                    UUID("9e7fccc4-169b-4169-a3f2-5d2c834cdb32"): "Other Deals",
                },
                {
                    UUID("c06b2e0b-9228-46db-b3cb-f2ee10a577c3"): "SalesBook",
                    UUID("386dd43b-1a9d-4fe0-aae3-9c687a6f7041"): "Reports",
                },
            ),
            (
                [],
                [],
                {},
                {},
            ),
            (
                [UUID("123e4567-e89b-12d3-a456-************")],
                [],
                {},
                {},
            ),
            (
                [],
                [UUID("123e4567-e89b-12d3-a456-************")],
                {},
                {},
            ),
        ],
    )
    def test_get_databooks_and_datasheets_name(
        self,
        datasheet_ids,
        databook_ids,
        expected_datasheet_names,
        expected_databook_names,
    ):
        databook_names, datasheet_names = get_databooks_and_datasheets_name(
            CLIENT_ID, databook_ids, datasheet_ids
        )
        assert databook_names == expected_databook_names
        assert datasheet_names == expected_datasheet_names

    @pytest.mark.parametrize(
        "value, expected_result",
        [
            ("test", "'test'"),
            ("test's", "'test''s'"),
            ("", "''"),
            (None, "''"),
        ],
    )
    def test_get_escaped_single_quoted_string(self, value, expected_result):
        assert repr(get_escaped_single_quoted_string(value)) == expected_result

    def test_add_additional_data_to_dataframe(
        self, sample_dataframe_for_additional_data
    ):
        # Make a copy so we can compare later.
        original_df = sample_dataframe_for_additional_data.copy()
        result = add_additional_data_to_dataframe(
            sample_dataframe_for_additional_data.copy()
        )

        # List of derived columns used in the function.
        derived_columns = [
            "record_type",
            "payout_freq",
            "period_label",
            "comm_period_label",
            "plan_name",
            "criteria_name",
            "settlement_criteria_name",
            "plan_type",
            "settlement_databook",
            "settlement_datasheet",
            "payee_name",
            "org_currency",
            "payee_currency",
            "conversion_rate",
            "variable_pay",
            "amount_payee_currency",
            "comm_amount",
            "comm_amount_payee_currency",
            "locked_kd",
            "is_locked",
        ]

        # Check that none of the derived columns exist in the result.
        for col in derived_columns:
            assert col not in result.columns, f"Column {col} should have been dropped."

        # Verify the additional_data column exists.
        assert "additional_data" in result.columns, "additional_data column is missing."

        # Check the content of the additional_data column.
        for idx, row in result.iterrows():
            additional_data = row["additional_data"]
            assert isinstance(
                additional_data, dict
            ), "Expected a dictionary in additional_data."
            for col in derived_columns:
                # Compare the value from additional_data with the original DataFrame.
                original_value = original_df.loc[idx, col]
                assert additional_data[col] == original_value, (
                    f"Value mismatch for column '{col}' at row {idx}: "
                    f"expected {original_value}, got {additional_data[col]}"
                )

        # Verify that non-derived columns are preserved.
        assert "other_column" in result.columns, "other_column should be preserved."
        pd.testing.assert_series_equal(
            result["other_column"], original_df["other_column"]
        )

    def test_missing_derived_column(self):
        # Create a DataFrame missing one or more derived columns.
        df_incomplete = pd.DataFrame(
            {
                "record_type": [1],
                "payout_freq": ["monthly"],
                # Other derived columns are intentionally missing.
                "other_column": [999],
            }
        )
        # Expect a KeyError since the function tries to access missing columns.
        with pytest.raises(KeyError):
            add_additional_data_to_dataframe(df_incomplete)

    def test_write_settlement_data_to_s3(self):
        # Setup test inputs.
        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()
        settlement_data = pd.DataFrame({"col": [1, 2, 3]})
        expected_s3_path = "s3://bucket/path/to/settlement_data"

        # Patch the dependent functions in the module where they are used.
        with patch(
            "everstage_ddd.settlement_v3.task_engine.utils.get_s3_path_to_write_settlement_v3_snowflake"
        ) as mock_get_path, patch(
            "everstage_ddd.settlement_v3.task_engine.utils.write_settlement_data_as_parquet_to_s3"
        ) as mock_write:

            # Configure the get_s3_path function to return our expected S3 path.
            mock_get_path.return_value = expected_s3_path

            # Call the function under test.
            write_settlement_data_to_s3(
                client_id=CLIENT_ID,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                settlement_data=settlement_data,
                execution_mode=ExecutionMode.ACTUAL_RUN,
                batch_number=1,
            )

            # Verify get_s3_path_to_write_settlement_snowflake was called with the expected arguments.
            mock_get_path.assert_called_once_with(
                client_id=CLIENT_ID,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                execution_mode=ExecutionMode.ACTUAL_RUN,
                operation="INSERT",
                batch_number=1,
            )

            # Verify write_settlement_data_as_parquet_to_s3 was called with the expected data and S3 path.
            mock_write.assert_called_once_with(
                data=settlement_data,
                s3_paths=[expected_s3_path],
            )

    def test_get_snowflake_plan_payee_period_table_name(self):
        e2e_sync_run_id = UUID("12345678-1234-5678-1234-************")
        expected_table_name = (
            '"plan_payee_info_6901_12345678-1234-5678-1234-************"'
        )

        result = get_snowflake_plan_payee_period_table_name(6901, e2e_sync_run_id)

        assert result == expected_table_name

    @pytest.mark.parametrize(
        "client_id, e2e_sync_run_id, plan_id, period_end_date, expected",
        [
            (
                6901,
                UUID("12345678-1234-5678-1234-************"),
                UUID("*************-6789-4321-************"),
                datetime(2021, 12, 31, tzinfo=timezone.utc),
                "ppp:6901:12345678-1234-5678-1234-************:*************-6789-4321-************:2021-12-31",
            ),
            (
                42,
                UUID("11111111-**************-************"),
                UUID("*************-7777-6666-************"),
                datetime(2020, 1, 15, tzinfo=timezone.utc),
                "ppp:42:11111111-**************-************:*************-7777-6666-************:2020-01-15",
            ),
        ],
    )
    def test_get_cache_key_for_payee_list_from_plan_payee_period_table(  # noqa: PLR0913
        self, client_id, e2e_sync_run_id, plan_id, period_end_date, expected
    ):
        result = get_cache_key_for_payee_list_from_plan_payee_period_table(
            client_id, e2e_sync_run_id, plan_id, period_end_date
        )
        assert result == expected

    def test_get_payee_list_from_plan_payee_period_table_cache_hit(self, monkeypatch):
        mock_payee_list = ["payee1", "payee2"]
        monkeypatch.setattr(cache, "get", lambda x: mock_payee_list)
        assert (
            get_payee_list_from_plan_payee_period_table(
                6901,
                UUID("12345678-1234-5678-1234-************"),
                UUID("*************-6789-4321-************"),
                datetime(2021, 12, 31, tzinfo=timezone.utc),
            )
            == mock_payee_list
        )

    def test_get_payee_list_from_plan_payee_period_table_cache_miss(self, monkeypatch):
        payees = [
            {"payee_email_id": "<EMAIL>"},
            {"payee_email_id": "<EMAIL>"},
        ]
        period_end_date = datetime(2021, 12, 31, tzinfo=timezone.utc)
        plan_id = UUID("*************-6789-4321-************")
        e2e_sync_run_id = UUID("12345678-1234-5678-1234-************")

        def mock_create_connection_and_execute_query(query, query_params):
            expected_query = "SELECT payee_email_id FROM table(?) WHERE plan_id = ? AND period_end_date = ?"
            assert normalize_query(query) == normalize_query(expected_query)
            assert query_params == [
                '"plan_payee_info_6901_12345678-1234-5678-1234-************"',
                plan_id,
                period_end_date,
            ]

            class MockConnection:
                def fetch_pandas_all(self):
                    return pd.DataFrame(payees)

            return MockConnection()

        expected_payee_list = ["<EMAIL>", "<EMAIL>"]
        monkeypatch.setattr(cache, "get", lambda x: None)
        monkeypatch.setattr(cache, "set", lambda x, y, timeout: None)
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.utils.SnowflakeDataStore.create_connection_and_execute_query",
            mock_create_connection_and_execute_query,
        )

        assert (
            get_payee_list_from_plan_payee_period_table(
                6901,
                e2e_sync_run_id,
                plan_id,
                period_end_date,
            )
            == expected_payee_list
        )

    def test_get_payee_list_from_plan_payee_period_table_empty_result(
        self, monkeypatch
    ):
        payees = []
        period_end_date = datetime(2021, 12, 31, tzinfo=timezone.utc)
        plan_id = UUID("*************-6789-4321-************")
        e2e_sync_run_id = UUID("12345678-1234-5678-1234-************")

        def mock_create_connection_and_execute_query(query, query_params):
            expected_query = "SELECT payee_email_id FROM table(?) WHERE plan_id = ? AND period_end_date = ?"
            assert normalize_query(query) == normalize_query(expected_query)
            assert query_params == [
                '"plan_payee_info_6901_12345678-1234-5678-1234-************"',
                plan_id,
                period_end_date,
            ]

            class MockConnection:
                def fetch_pandas_all(self):
                    return pd.DataFrame(payees)

            return MockConnection()

        expected_payee_list = []
        monkeypatch.setattr(cache, "get", lambda x: None)
        monkeypatch.setattr(cache, "set", lambda x, y, timeout: None)
        monkeypatch.setattr(
            "everstage_ddd.settlement_v3.task_engine.utils.SnowflakeDataStore.create_connection_and_execute_query",
            mock_create_connection_and_execute_query,
        )

        assert (
            get_payee_list_from_plan_payee_period_table(
                6901,
                e2e_sync_run_id,
                plan_id,
                period_end_date,
            )
            == expected_payee_list
        )

    @pytest.mark.parametrize(
        "client_id, e2e_sync_run_id, plan_id, period_end_date, payee_list",
        [
            (
                6901,
                UUID("12345678-1234-5678-1234-************"),
                UUID("*************-6789-4321-************"),
                datetime(2021, 12, 31, tzinfo=timezone.utc),
                ["<EMAIL>", "<EMAIL>"],
            ),
            (
                42,
                UUID("11111111-**************-************"),
                UUID("*************-7777-6666-************"),
                datetime(2020, 1, 15, tzinfo=timezone.utc),
                [],
            ),
        ],
    )
    def test_add_payee_list_to_plan_payee_period_cache(  # noqa: PLR0913
        self,
        monkeypatch,
        client_id,
        e2e_sync_run_id,
        plan_id,
        period_end_date,
        payee_list,
    ):
        cache_key = get_cache_key_for_payee_list_from_plan_payee_period_table(
            client_id, e2e_sync_run_id, plan_id, period_end_date
        )

        def mock_set(key, value, timeout):
            assert key == cache_key
            assert value == payee_list
            assert timeout == 60 * 60

        monkeypatch.setattr(cache, "set", mock_set)

        add_payee_list_to_plan_payee_period_cache(
            client_id, e2e_sync_run_id, plan_id, period_end_date, payee_list
        )

    @pytest.mark.parametrize(
        "record_count, batch_size, expected",
        [
            # Case: record_count not an exact multiple of batch_size.
            (
                37000,
                10000,
                [(1, 10000), (10001, 20000), (20001, 30000), (30001, 37000)],
            ),
            # Case: record_count is exactly divisible by batch_size.
            (10000, 10000, [(1, 10000)]),
            # Case: record_count is less than batch_size.
            (5000, 10000, [(1, 5000)]),
            # Case: record_count not an exact multiple.
            (25000, 10000, [(1, 10000), (10001, 20000), (20001, 25000)]),
            # Edge Case: record_count is 0.
            (0, 10000, []),
            # Edge Case: record_count is negative.
            (-10, 10000, []),
        ],
    )
    def test_calculate_batch_ranges(self, record_count, batch_size, expected):
        result = calculate_batch_ranges(record_count, batch_size)
        assert result == expected

    @pytest.mark.parametrize(
        "query, column_name, expected",
        [
            (
                "SELECT * FROM table",
                "id",
                "SELECT * FROM table WHERE id BETWEEN ? AND ?",
            ),
            (
                "",
                "user_id",
                "WHERE user_id BETWEEN ? AND ?",
            ),
        ],
    )
    def test_append_batch_clause_to_query(self, query, column_name, expected):
        result = append_batch_clause_to_query(query, column_name)
        assert normalize_query(result) == normalize_query(expected)

    def test_get_snowflake_temp_settlement_table_name(self):
        result = get_snowflake_temp_settlement_table_name(
            6901,
            UUID("12345678-1234-5678-1234-************"),
            UUID("12345678-1234-5678-1234-************"),
        )
        assert (
            result
            == '"settlement_6901_12345678-1234-5678-1234-************_12345678-1234-5678-1234-************"'
        )
