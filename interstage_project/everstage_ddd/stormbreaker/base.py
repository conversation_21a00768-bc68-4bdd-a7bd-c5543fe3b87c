import logging
from uuid import uuid4

from commission_engine.services.expression_designer import ui_filter_infix_converter
from spm.services.datasheet_permission_services import (
    does_user_has_permission_to_custom_objects_in_datasheet,
)
from spm.services.rbac_services import does_user_have_databook_manage_permission

from .query_spec_builder.everstage_query_spec_builder import EverstageQuerySpecBuilder
from .types import (
    ExecutorBase,
    ExecutorParamsType,
    KnowledgeDateAwareExecutorParamsType,
    StormBreakerDSInitType,
)

logger = logging.getLogger(__name__)


class StormBreakerDS:
    def __init__(
        self,
        executor: ExecutorBase,
        args: StormBreakerDSInitType,
    ):
        self._client_id = args.client_id
        self._databook_id = args.databook_id
        self._datasheet_id = args.datasheet_id
        self._logged_in_user_email = args.logged_in_user_email
        self._executor = executor
        self._query_spec_builder = EverstageQuerySpecBuilder()
        self._executor_params = (
            KnowledgeDateAwareExecutorParamsType(
                query_id=uuid4(), logged_in_user_email=args.logged_in_user_email
            )
            if executor.supports_knowledge_date(args.knowledge_date)
            else ExecutorParamsType(
                query_id=uuid4(), logged_in_user_email=args.logged_in_user_email
            )
        )
        self._executor_params.user_has_co_permission = (
            self.does_user_has_co_permission()
        )
        self._executor_params.user_has_databook_manage_permission = (
            self._does_user_has_ds_manage_permission()
        )
        self.log_context = {
            "datasheet_id": str(args.datasheet_id),
            "databook_id": str(args.databook_id),
        }

    def get_executor_params(self):
        return self._executor_params

    def get_query_spec(self):
        return self._query_spec_builder.get_query_spec()

    def get_records_count(self, return_query_alone: bool | None = None):
        self._executor_params.return_query_alone = bool(return_query_alone)
        self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
        return self._executor.get_records_count(self._executor_params)

    def fetch_datasheet_data_latest(
        self,
        limit: int | None = 100,
        offset: int | None = 0,
        as_dataframe: bool | None = None,
    ):
        self._executor_params.as_dataframe = bool(as_dataframe)
        self._executor_params.limit = limit
        self._executor_params.offset = offset
        self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
        return self._executor.fetch_datasheet_data(self._executor_params)

    def fetch_datasheet_data_as_of_date(
        self,
        knowledge_date,
        limit: int | None = 100,
        offset: int | None = 0,
        as_dataframe: bool | None = None,
    ):
        if self._executor.supports_knowledge_date(knowledge_date):
            self._executor_params.as_dataframe = bool(as_dataframe)
            self._executor_params.limit = limit
            self._executor_params.offset = offset
            self._executor_params.knowledge_date = knowledge_date
            self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
            return self._executor.fetch_datasheet_data(self._executor_params)
        raise NotImplementedError("Cannot get datasheet data based on knowledge date!")

    def fetch_datasheet_data_invalidated_after(
        self,
        records_invalidated_after,
        limit: int | None = 100,
        offset: int | None = 0,
        as_dataframe: bool | None = None,
    ):
        if self._executor.supports_records_invalidated_after():
            self._executor_params.as_dataframe = bool(as_dataframe)
            self._executor_params.limit = limit
            self._executor_params.offset = offset
            self._executor_params.records_invalidated_after = records_invalidated_after
            self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
            return self._executor.fetch_datasheet_data(self._executor_params)
        raise NotImplementedError("Cannot get datasheet data invalidated after date!")

    def fetch_datasheet_created_data_after(
        self,
        fetch_records_after,
        limit: int | None = 100,
        offset: int | None = 0,
        as_dataframe: bool | None = None,
    ):
        if self._executor.supports_fetch_records_after():
            self._executor_params.as_dataframe = bool(as_dataframe)
            self._executor_params.limit = limit
            self._executor_params.offset = offset
            self._executor_params.fetch_records_after = fetch_records_after
            self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
            return self._executor.fetch_datasheet_data(self._executor_params)
        raise NotImplementedError("Cannot get datasheet data created after!")

    def get_query_string(  # noqa: PLR0913
        self,
        limit: int | None = None,
        offset: int | None = None,
        add_display_name_alias_in_select: bool | None = None,
        add_custom_name_alias_in_select: bool | None = None,
        system_name_custom_name_map: dict[str, str] | None = None,
    ):
        self._executor_params.return_query_alone = True
        self._executor_params.as_dataframe = False
        self._executor_params.limit = limit
        self._executor_params.offset = offset
        self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
        self._executor_params.add_display_name_alias_in_select = bool(
            add_display_name_alias_in_select
        )
        self._executor_params.add_custom_name_alias_in_select = bool(
            add_custom_name_alias_in_select
        )
        self._executor_params.system_name_custom_name_map = system_name_custom_name_map
        return self._executor.get_query_string(self._executor_params)

    def get_expanded_query_string(
        self,
        limit: int | None = 100,
        offset: int | None = 0,
        as_dataframe: bool | None = None,
        should_quote_column_name: bool | None = None,
    ):
        self._executor_params.as_dataframe = bool(as_dataframe)
        self._executor_params.limit = limit
        self._executor_params.offset = offset
        self._executor_params.full_table_expanded_query = True
        self._executor_params.should_quote_column_name = bool(should_quote_column_name)
        logger.info("Fetching Expanded query string", extra=self.log_context)
        self._executor_params.query_spec = self._query_spec_builder.get_query_spec()
        return self._executor.get_expanded_query_string(self._executor_params)

    def set_fetch_data_without_adjustments(self):
        self._executor_params.fetch_data_without_adjustments = True

    def reset_fetch_data_without_adjustments(self):
        self._executor_params.fetch_data_without_adjustments = False

    def set_fetch_only_adjusted_records(self):
        self._executor_params.fetch_only_adjusted_records = True

    def reset_fetch_only_adjusted_records(self):
        self._executor_params.fetch_only_adjusted_records = False

    def set_apply_datasheet_permissions(self):
        self._executor_params.apply_datasheet_permissions = True

    def reset_apply_datasheet_permissions(self):
        self._executor_params.apply_datasheet_permissions = False

    def reset_logged_in_user(self):
        self._logged_in_user_email = None
        return self

    # TODO: @sukanya - Remove these from base
    def set_knowledge_date(self, knowledge_date):
        if isinstance(
            self._executor_params, KnowledgeDateAwareExecutorParamsType
        ) and self._executor.supports_knowledge_date(knowledge_date):
            self._executor_params.knowledge_date = knowledge_date
        else:
            raise NotImplementedError(
                f"Knowledge date {knowledge_date} for is not supported for this executor!"
            )

    def set_fetch_records_after(self, date):
        if self._executor.supports_fetch_records_after():
            self._executor_params.fetch_records_after = date
        else:
            raise NotImplementedError(
                "Fetch records after is not supported for this executor!"
            )
        return self

    def reset_fetch_records_after(self):
        if self._executor.supports_fetch_records_after():
            self._executor_params.fetch_records_after = None
        else:
            raise NotImplementedError(
                "Fetch records after is not supported for this executor!"
            )
        return self

    def reset_knowledge_date(self):
        if self._executor.supports_knowledge_date(None):
            self._executor_params.knowledge_date = None
        else:
            raise NotImplementedError(
                "Knowledge date is not supported for this executor!"
            )

    def set_records_invalidated_after(self, records_invalidated_after):
        if self._executor.supports_records_invalidated_after():
            self._executor_params.records_invalidated_after = records_invalidated_after
        else:
            raise NotImplementedError(
                "Records invalidated after is not supported for this executor!"
            )

    def reset_records_invalidated_after(self):
        if self._executor.supports_records_invalidated_after():
            self._executor_params.records_invalidated_after = None
        else:
            raise NotImplementedError(
                "Records invalidated after is not supported for this executor!"
            )

    def supports_knowledge_date(self, knowledge_date):
        return self._executor.supports_knowledge_date(knowledge_date)

    def is_valid_query(self):
        return True

    def add_log_context(self, context=None):
        if context is None:
            context = {}
        self.log_context.update(context)
        return self

    def set_limit(self, limit: int | None):
        self._executor_params.limit = limit

    def set_offset(self, offset: int | None):
        self._executor_params.offset = offset

    def distinct_values(self):
        self._query_spec_builder.distinct_values()
        return self

    def reset_query_spec(self):
        self._query_spec_builder.reset_query_spec()
        self._executor_params.limit = 100
        self._executor_params.offset = 0
        return self

    def reset_distinct_values(self):
        self._query_spec_builder.reset_distinct_values()
        return self

    def filter(self, column, operator, value):
        self._query_spec_builder.filter(column, operator, value)
        return self

    def apply_infix_filter(self, infix):
        v1_infix = ui_filter_infix_converter(
            client_id=self._client_id,
            ui_filter_infix=infix,
            databook_id=self._databook_id,
            datasheet_id=self._datasheet_id,
        )
        self._query_spec_builder.apply_infix_filter(v1_infix)
        return self

    def reset_infix_filters(self):
        self._query_spec_builder.reset_infix_filters()
        return self

    def set_case_insensitive_infix_filters(self):
        self._query_spec_builder.set_case_insensitive_infix_filters()
        return self

    def reset_case_insensitive_infix_filters(self):
        self._query_spec_builder.reset_case_insensitive_infix_filters()
        return self

    def set_simulation_custom_filters(
        self, filter_id, sorted_col, sorted_col_value, row_key_value
    ):
        self._query_spec_builder.set_simulation_custom_filters(
            filter_id, sorted_col, sorted_col_value, row_key_value
        )
        return self

    def reset_simulation_custom_filters(self):
        self._query_spec_builder.reset_simulation_custom_filters()

    def reset_limit(self):
        self._executor_params.limit = 100

    def reset_offset(self):
        self._executor_params.offset = 0

    def reset_filter(self):
        self._query_spec_builder.reset_filter()
        return self

    def reset_last_filter(self):
        self._query_spec_builder.reset_last_filter()
        return self

    def group_by(self, columns_to_groupby: list):
        self._query_spec_builder.group_by(columns_to_groupby)
        return self

    def reset_group_by(self):
        self._query_spec_builder.reset_group_by()
        return self

    def select(self, columns_to_select: list):
        self._query_spec_builder.select(columns_to_select)
        return self

    def reset_select(self):
        self._query_spec_builder.reset_select()
        return self

    def order_by(self, order_details: list):
        self._query_spec_builder.order_by(order_details)
        return self

    def reset_order_by(self):
        self._query_spec_builder.reset_order_by()
        return self

    def set_add_system_name_alias_in_select(self):
        self._executor_params.add_system_name_alias_in_select = True

    def reset_add_system_name_alias_in_select(self):
        self._executor_params.add_system_name_alias_in_select = False

    def set_fetch_null_as_null(self):
        self._executor_params.fetch_null_as_null = True
        return self

    def reset_fetch_null_as_null(self):
        self._executor_params.fetch_null_as_null = False
        return self

    def does_user_has_co_permission(self):
        knowledge_date = (
            self._executor_params.knowledge_date
            if isinstance(self._executor_params, KnowledgeDateAwareExecutorParamsType)
            else None
        )
        if not self._logged_in_user_email:
            return False

        return does_user_has_permission_to_custom_objects_in_datasheet(
            self._client_id,
            self._databook_id,
            self._datasheet_id,
            self._logged_in_user_email,
            knowledge_date,
        )

    def _does_user_has_ds_manage_permission(self):
        if not self._logged_in_user_email:
            return False
        return does_user_have_databook_manage_permission(
            self._client_id, self._logged_in_user_email
        )
