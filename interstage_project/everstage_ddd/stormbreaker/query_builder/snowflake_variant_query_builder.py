import logging
import time
from uuid import UUID, uuid4

from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_datatype_id_name_map,
)
from commission_engine.snowflake_accessors.utils import (
    SNOWFLAKE_MAX_IN_CLAUSE_PARAMS_LEN,
    temp_table_filter_stormbreaker,
)
from commission_engine.utils.databook_utils import (
    get_datasheet_data_table_name,
    resolve_snowflake_data_type,
)
from common.utils.infix_utils import InfixToSnowflakeQuery
from everstage_ddd.stormbreaker.stormbreaker_exception import (
    InvalidFilterIdError,
    NotBetweenFilterError,
    OperationNotSupportedError,
)
from everstage_ddd.stormbreaker.types import (
    FilterIdMap,
    StrInSingleQuote,
    VariantTableQueryBuilderType,
)
from spm.services.stormbreaker.stormbreaker_utils import (
    escape_special_characters,
    get_filter_expression,
    resolve_all_user_properties_values,
    resolve_ds_filters,
    resolve_user_properties,
    resolve_user_value_constants,
)
from spm.services.user_group_service import UserGroupMemberService
from spm.utils import make_column_name_mixed_case

logger = logging.getLogger(__name__)


# TODO: @sukanya - Make unused methods private
class SnowflakeVariantQueryBuilder:
    def __init__(self, client_id: int, datasheet_id: UUID, databook_id: UUID):
        self.query_id = uuid4()
        self.client_id = client_id
        self.datasheet_id = datasheet_id
        self.databook_id = databook_id
        self.temp_table_name = None
        self.col_prefix = "data:"
        self.log_context = {}

    def _get_datasheet_data_table_name(self, datasheet_id):
        return get_datasheet_data_table_name(self.client_id, datasheet_id)

    def query_for_order_by(self, query_spec, query_string, colname_type_map):
        if query_spec.order_bys:
            logger.debug(
                "BEGIN: query_for_order_by: %s",
                self.query_id,
                extra=query_spec.model_dump(),
            )
            order_by_cols = []
            for order_by in query_spec.order_bys:
                # each order_by is a dict with key col
                col_to_order = f"{self.col_prefix}{order_by['col']}::{colname_type_map[order_by['col']]}"
                order = order_by.get("order", "asc")
                if order.lower() == "desc":
                    order_by_cols.append(f"{col_to_order} DESC")
                else:
                    order_by_cols.append(col_to_order)
            query_string += f" ORDER BY {','.join(order_by_cols)}"
            logger.debug(
                "END: query_for_order_by: %s",
                self.query_id,
                extra=query_spec.model_dump(),
            )
        return query_string

    def query_for_limit_offset(
        self,
        query_string,
        query_builder_params: VariantTableQueryBuilderType,
        colname_type_map,
    ):
        query_spec = query_builder_params.query_spec
        logger.debug(
            "BEGIN: query_for_limit_offset: %s",
            self.query_id,
            extra=query_spec.model_dump(),
        )
        limit = query_builder_params.limit
        offset = query_builder_params.offset
        order_bys = query_spec.order_bys
        group_bys = query_spec.group_bys
        records_count = query_builder_params.records_count
        if not order_bys and not records_count and group_bys:
            group_by_list = [
                f"{self.col_prefix}{each_col}::{colname_type_map[each_col]}"
                for each_col in group_bys
            ]
            group_by_list.sort()
            query_string += f" ORDER BY {','.join(group_by_list)}"
        if limit is None and offset is None:
            return query_string
        if limit is not None and offset is not None:
            query_string += f" LIMIT {limit} OFFSET {offset}"
        elif limit is not None and offset is None:
            query_string += f" LIMIT {limit}"
        elif offset is not None and limit is None:
            query_string += f" LIMIT 100 OFFSET {offset}"
        logger.debug(
            "END: query_for_limit_offset: %s",
            self.query_id,
            extra=query_spec.model_dump(),
        )
        return query_string

    def query_for_group_by(self, query_spec, query_string, colname_type_map):
        if query_spec.group_bys:
            logger.info(
                "BEGIN: query_for_group_by: %s",
                self.query_id,
                extra=query_spec.model_dump(),
            )
            group_by_list = [
                f"{self.col_prefix}{each_col}::{colname_type_map[each_col]}"
                for each_col in query_spec.group_bys
            ]
            group_by_list.sort()
            query_string += f" GROUP BY {','.join(group_by_list)}"
            logger.info(
                "END: query_for_group_by: %s",
                self.query_id,
                extra=query_spec.model_dump(),
            )
        return query_string

    # ruff: noqa: PLR0915
    def query_for_select_predicate(
        self,
        query_builder_params: VariantTableQueryBuilderType,
        datasheet_variables,
        json_col_name,
        colname_type_map,
    ):
        query_spec = query_builder_params.query_spec
        logger.debug(
            "BEGIN: query_for_select_predicate: %s",
            self.query_id,
            extra=query_spec.model_dump(),
        )
        add_display_name_alias_in_select = (
            query_builder_params.add_display_name_alias_in_select
        )
        add_system_name_alias_in_select = (
            query_builder_params.add_system_name_alias_in_select
        )
        add_custom_name_alias_in_select = (
            query_builder_params.add_custom_name_alias_in_select
        )
        system_name_custom_name_map = query_builder_params.system_name_custom_name_map

        def make_select(select_dict, colname_type_map, json_col_name):
            if select_dict.get("is_distinct") and select_dict["agg"].lower() == "count":
                return f'COUNT(DISTINCT {json_col_name}:{select_dict["col"]}) AS "{json_col_name}:distinct_count__{select_dict["col"]}"'
            if select_dict["agg"].lower() in ("min", "max"):
                return f'{select_dict["agg"]}({json_col_name}:{select_dict["col"]}::{colname_type_map[select_dict["col"]]}) AS "{json_col_name}:{select_dict["agg"].lower()}__{select_dict["col"]}"'
            return f'{select_dict["agg"]}({json_col_name}:{select_dict["col"]})::DOUBLE AS "{json_col_name}:{select_dict["agg"].lower()}__{select_dict["col"]}"'

        def get_select_from_datasheet_variables(
            datasheet_variables, colname_type_map, json_col_name, columns_to_be_hidden
        ):
            var_list = [
                f"{json_col_name}:{each_variable['system_name']}::{colname_type_map[each_variable['system_name']]}"
                for each_variable in datasheet_variables
                if each_variable["system_name"] not in columns_to_be_hidden
            ]
            var_list.append(f"{json_col_name}:row_key::STRING")
            var_list.sort()
            return ", ".join(var_list)

        def get_select_from_datasheet_variables_with_alias(
            datasheet_variables,
            colname_type_map,
            json_col_name,
            type_of_name="display_name",
            should_quote_column_name: bool | None = None,
        ):
            var_list = []
            for each_variable in datasheet_variables:
                # if each_variable["system_name"] not in columns_to_be_hidden: # Returning all columns currently
                sys_name = each_variable["system_name"]
                type_map = colname_type_map[each_variable["system_name"]]

                if type_of_name == "custom_name" and system_name_custom_name_map:
                    custom_name = system_name_custom_name_map.get(sys_name)
                    if custom_name:
                        var_list.append(
                            f'{json_col_name}:{sys_name}::{type_map} as "{custom_name}"'
                        )
                elif type_of_name == "display_name":
                    name_as = make_column_name_mixed_case(each_variable["display_name"])
                    var_list.append(
                        f'{json_col_name}:{sys_name}::{type_map} as "{name_as}"'
                    )
                elif type_of_name == "system_name":
                    var_list.append(
                        f"{json_col_name}:{sys_name}::{type_map} as {sys_name}"
                    )
                elif type_of_name == "expanded_with_sys_name":
                    should_quote_column_name = bool(should_quote_column_name)
                    column = (
                        f'"{sys_name}"' if should_quote_column_name else f"{sys_name}"
                    )
                    pure_column = (
                        f'"pure_{sys_name}"'
                        if should_quote_column_name
                        else f"pure_{sys_name}"
                    )
                    var_list.append(
                        f"{json_col_name}:{sys_name}::{type_map} as {column}"
                    )
                    # data_pure stores the original value of data before any adjustment
                    var_list.append(
                        f"data_pure:{sys_name}::{type_map} as {pure_column}"
                    )
            if type_of_name == "expanded_with_sys_name":
                is_adjusted_column = (
                    '"is_adjusted"' if should_quote_column_name else "is_adjusted"
                )
                adjustment_type_column = (
                    '"adjustment_type"'
                    if should_quote_column_name
                    else "adjustment_type"
                )
                pure_row_key_column = (
                    '"pure_row_key"' if should_quote_column_name else "pure_row_key"
                )
                row_key_column = '"row_key"' if should_quote_column_name else "row_key"

                var_list.append(f"is_adjusted as {is_adjusted_column}")
                var_list.append(f"adjustment_type as {adjustment_type_column}")
                var_list.append(f"data_pure:row_key::STRING as {pure_row_key_column}")
                var_list.append(f"data:row_key::STRING as {row_key_column}")
            else:
                var_list.append(f"{json_col_name}:row_key::STRING as row_key")
            var_list.sort()
            return ", ".join(var_list)

        datasheet_id = str(self.datasheet_id)

        table_name = self._get_datasheet_data_table_name(datasheet_id)

        columns_to_be_hidden = set(query_builder_params.columns_to_be_hidden)
        select_clause = ""
        if query_spec.selects:
            if query_spec.distinct_values:
                select_clause = "DISTINCT "
            select_list = []
            select_set = set()
            for select in query_spec.selects:
                if isinstance(select, str) and select not in columns_to_be_hidden:
                    col_name = f"{json_col_name}:{select}::{colname_type_map[select]}"
                    if col_name not in select_set:
                        select_list.append(col_name)
                        select_set.add(col_name)
                elif (
                    isinstance(select, dict)
                    and select["col"] not in columns_to_be_hidden
                ):
                    col_name = make_select(select, colname_type_map, json_col_name)
                    if col_name not in select_set:
                        select_list.append(col_name)
                        select_set.add(col_name)
            select_list.sort()
            select_clause += ", ".join(select_list)
        else:
            logger.info(
                "BEGIN: Selecting all vars from datasheet_variables",
                extra=query_spec.model_dump(),
            )
            if add_custom_name_alias_in_select and system_name_custom_name_map:
                select_clause = get_select_from_datasheet_variables_with_alias(
                    datasheet_variables,
                    colname_type_map,
                    json_col_name,
                    type_of_name="custom_name",
                )
            elif add_display_name_alias_in_select:
                select_clause = get_select_from_datasheet_variables_with_alias(
                    datasheet_variables,
                    colname_type_map,
                    json_col_name,
                )
            elif add_system_name_alias_in_select:
                select_clause = get_select_from_datasheet_variables_with_alias(
                    datasheet_variables,
                    colname_type_map,
                    json_col_name,
                    type_of_name="system_name",
                )
            elif query_builder_params.full_table_expanded_query:
                select_clause = get_select_from_datasheet_variables_with_alias(
                    datasheet_variables,
                    colname_type_map,
                    json_col_name,
                    type_of_name="expanded_with_sys_name",
                    should_quote_column_name=query_builder_params.should_quote_column_name,
                )
            else:
                select_clause = get_select_from_datasheet_variables(
                    datasheet_variables,
                    colname_type_map,
                    json_col_name,
                    columns_to_be_hidden,
                )
            logger.info("END: Selecting all vars from datasheet_variables")
            logger.debug(
                "Select Clause: %s",
                select_clause,
                extra=query_builder_params.model_dump(),
            )

        logger.debug(
            "END: query_for_select_predicate: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )

        query_template = "SELECT {select_clause} FROM {table_name}"

        return (
            select_clause
            if query_builder_params.has_meta_cols
            else query_template.format(
                select_clause=select_clause, table_name=table_name
            )
        )

    def query_for_default_filter(
        self, query_builder_params: VariantTableQueryBuilderType
    ):
        logger.debug(
            "BEGIN: query_for_default_filter: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )

        client_id = self.client_id
        datasheet_id = self.datasheet_id
        databook_id = self.databook_id
        knowledge_date = query_builder_params.knowledge_date
        is_adjusted_condition = query_builder_params.is_adjusted
        data_null_condition = query_builder_params.data_null
        data_pure_null_condition = query_builder_params.data_pure_null
        fetch_records_after = query_builder_params.fetch_records_after
        records_invalidated_after = query_builder_params.records_invalidated_after

        default_filter = f" WHERE client_id = {client_id} AND datasheet_id = '{datasheet_id}' AND databook_id = '{databook_id}' AND is_deleted = FALSE"

        if records_invalidated_after:
            default_filter += (
                f" AND knowledge_end_date >= '{records_invalidated_after}'"
            )
        elif fetch_records_after:
            default_filter += f" AND knowledge_begin_date >= '{fetch_records_after}' AND knowledge_end_date IS NULL"
        elif knowledge_date is None:
            default_filter += " AND knowledge_end_date IS NULL"
        elif knowledge_date is not None:
            default_filter += f" AND knowledge_begin_date <= '{knowledge_date}' AND (knowledge_end_date IS NULL OR knowledge_end_date > '{knowledge_date}')"

        if is_adjusted_condition == 1:
            default_filter += " AND is_adjusted = TRUE"
        elif is_adjusted_condition == -1:
            default_filter += " AND is_adjusted = FALSE"
        if data_pure_null_condition == 1:
            default_filter += " AND (data_pure IS NULL OR IS_NULL_VALUE(data_pure))"
        elif data_pure_null_condition == -1:
            default_filter += (
                " AND data_pure IS NOT NULL AND NOT IS_NULL_VALUE(data_pure)"
            )
        if data_null_condition == 1:
            default_filter += " AND (data IS NULL OR IS_NULL_VALUE(data))"
        elif data_null_condition == -1:
            default_filter += " AND data IS NOT NULL AND NOT IS_NULL_VALUE(data)"

        query_string = str(query_builder_params.query_string) + default_filter
        logger.debug(
            "END: query_for_default_filter: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )
        return query_string

    def query_for_datasheet_permissions(
        self, query_builder_params: VariantTableQueryBuilderType, column_name_type_map
    ):
        from spm.services.datasheet_permission_services import (
            get_permissions_for_users_and_user_group,
        )

        logger.info("BEGIN: query_for_datasheet_permissions")
        if query_builder_params.apply_datasheet_permissions:
            client_id = self.client_id
            databook_id = self.databook_id
            datasheet_id = self.datasheet_id
            logged_in_user_email = query_builder_params.logged_in_user_email
            query_string = ""
            query_builder_params.columns_to_be_hidden = []
            user_properties = resolve_all_user_properties_values(
                client_id, logged_in_user_email
            )

            def query_string_for_a_permission_set(permission_set):
                start_time = time.monotonic()
                filter_list = []
                logger.info("Get permission values")
                for permission in permission_set["filter_list"]:
                    value = permission["value"]
                    if permission["operator"].lower() == "belongs_to":
                        permission["value"] = resolve_user_value_constants(
                            client_id, value, logged_in_user_email
                        )
                    else:
                        permission["value"] = resolve_user_properties(
                            value,
                            permission["operator"],
                            user_properties,
                        )
                    filter_list.append(permission)
                modified_filter_list = resolve_ds_filters(client_id, filter_list)
                spec_filter = query_builder_params.query_spec.filters
                query_builder_params.query_spec.filters = modified_filter_list
                start_time_query = time.monotonic()
                permission_query = self.query_for_filters(
                    query_builder_params=query_builder_params,
                    colname_type_map=column_name_type_map,
                )
                query_builder_params.query_spec.filters = spec_filter
                logger.info(
                    "Time taken to form permission query: %s seconds",
                    time.monotonic() - start_time_query,
                )
                if permission_set["columns_to_be_hidden"]:
                    query_builder_params.columns_to_be_hidden.extend(
                        permission_set["columns_to_be_hidden"]
                    )
                logger.info(
                    "query_string_for_a_permission_set duration: %s seconds",
                    time.monotonic() - start_time,
                )
                return permission_query

            if logged_in_user_email is not None:
                logger.info("Get user groups and permissions for user")
                list_of_user_groups_for_user = UserGroupMemberService(
                    client_id
                ).get_user_groups_of_user(logged_in_user_email)
                logger.info(
                    "List of user groups for user: %s", list_of_user_groups_for_user
                )
                all_permissions = get_permissions_for_users_and_user_group(
                    client_id,
                    databook_id,
                    datasheet_id,
                    [logged_in_user_email] + list_of_user_groups_for_user,
                )
                logger.info("All permissions for user: %s", all_permissions)
                # AND condition inside of permission set, OR condition across permission set
                for permission_set in all_permissions:
                    permission_query = query_string_for_a_permission_set(permission_set)
                    if permission_query:
                        query_string += (
                            f" OR {permission_query}"
                            if query_string
                            else permission_query
                        )
            if query_string:
                query_string = f" AND ({query_string})"
            query_builder_params.query_string = query_string
            logger.info("END: query_for_datasheet_permissions")
            return query_string
        logger.info("END: query_for_datasheet_permissions - Returning None")
        return None

    # ruff: noqa: PLR0912
    def query_for_in_notin_clause(self, col_name: str, value, operator) -> str:

        if operator == "in":
            operator = "IN"
        elif operator in ["notin", "not in"]:
            operator = "NOT IN"
        if isinstance(value, (str, int, float)):
            value = [value]
        elif value is None:
            value = []
        values_set = set(value)
        add_null_condition = False
        none_variants = {None, "None", "'None'"}
        if values_set.intersection(none_variants):
            values_set.difference_update(none_variants)
            add_null_condition = True

        if len(values_set) == 0:
            # Modified the logic to handle empty list since timestamp, boolean and numeric fields are not handled properly by the previous logic using tuple(['-9!!99999999999$$','-9!!9999999999$$'])
            # hack to handle empty list
            qs = "1 != 1" if operator == "IN" else "1 = 1"
        elif len(value) == 1 and add_null_condition is False:
            if operator == "IN":
                qs = f"{self.col_prefix}{col_name} = {value[0]}"
            else:
                qs = f"{self.col_prefix}{col_name} != {value[0]}"
        elif len(values_set) == 1:
            value = values_set.pop()
            if isinstance(value, str):
                value = f"'{value}'"
            if operator == "IN":
                qs = f"{self.col_prefix}{col_name} = {value}"
            else:
                qs = f"{self.col_prefix}{col_name} != {value}"
        elif len(values_set) < (SNOWFLAKE_MAX_IN_CLAUSE_PARAMS_LEN - 1):
            qs = f"{self.col_prefix}{col_name} {operator} {tuple(values_set)}"
        else:
            table_name = "sb_temp_table_" + str(uuid4()).replace("-", "_")
            logger.info("Total %s values in IN clause", len(values_set))
            logger.info("creating temp table with name %s", table_name)
            self.temp_table_name = table_name
            qs = temp_table_filter_stormbreaker(
                self.client_id, table_name, tuple(values_set), col_name
            )

        without_type = col_name.rsplit("::", 1)[0]
        if add_null_condition and operator == "IN":
            qs = f"({qs} OR {self.col_prefix}{col_name} IS NULL OR IS_NULL_VALUE({self.col_prefix}{without_type}))"
        elif add_null_condition and operator == "NOT IN":
            qs += f" AND {self.col_prefix}{col_name} IS NOT NULL AND NOT IS_NULL_VALUE({self.col_prefix}{without_type})"

        return qs

    def query_for_filters(
        self,
        query_builder_params: VariantTableQueryBuilderType,
        colname_type_map,
    ):
        filters = query_builder_params.query_spec.filters
        query_string = query_builder_params.query_string
        if filters:
            logger.debug(
                "BEGIN: query_for_filters: %s",
                self.query_id,
                extra=query_builder_params.model_dump(),
            )
            ever_comparison = query_builder_params.ever_comparison
            logger.debug(
                "Snowflake ever_comparison - %s",
                ever_comparison,
            )
            filter_list = []
            for iter_filter in filters:
                # Each iter_filter is a tuple (col_name, operator, value)
                col_name = f"{iter_filter[0]}::{colname_type_map[iter_filter[0]]}"
                operator = iter_filter[1].lower()
                value = iter_filter[2]
                value_without_enclosing_quotes = iter_filter[2]
                if isinstance(value, str):
                    # TODO: write a function to escape single quotes and reuse it
                    value = escape_special_characters(value)
                    value_without_enclosing_quotes = f"{value}"
                    value = f"'{value}'"
                elif isinstance(value, (list, tuple)) and value is not None:
                    if len(value) == 1:
                        if isinstance(value[0], str):
                            value = escape_special_characters(value[0])
                            value_without_enclosing_quotes = f"{value}"
                            value = f"'{value}'"
                        elif value[0] is not None:
                            value = value[0]
                    elif len(value) > 1:
                        value = list(value)
                        for idx in range(len(value)):
                            if isinstance(value[idx], str) and "'" in value[idx]:
                                value[idx] = value[idx].replace(r"'", r"''")
                                value[idx] = StrInSingleQuote(value[idx])
                column_dtype = colname_type_map[iter_filter[0]]
                filter_expression = get_filter_expression(
                    col_name,
                    value,
                    operator,
                    datatype=column_dtype,
                    col_name_prefix="data:",
                    ever_comparison=ever_comparison,
                )
                if operator in ("eq", "==", "="):
                    if value is None:
                        filter_list.append(
                            f"({self.col_prefix}{col_name} IS NULL OR IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                        )
                    else:
                        filter_list.append(filter_expression)
                elif operator in ("neq", "!="):
                    if value is None:
                        filter_list.append(
                            f"({self.col_prefix}{col_name} IS NOT NULL AND NOT IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                        )
                    else:
                        filter_list.append(filter_expression)
                elif operator in ("gt", ">", "gte", ">=", "lt", "<", "lte", "<="):
                    filter_list.append(
                        f"({self.col_prefix}{col_name} IS NOT NULL AND {self.col_prefix}{iter_filter[0]} != '' AND NOT IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                    )
                    filter_list.append(filter_expression)
                elif operator in ("in", "notin"):
                    in_clause_query = self.query_for_in_notin_clause(
                        col_name, value, operator
                    )
                    filter_list.append(in_clause_query)
                elif operator == "contains":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} LIKE '%{value_without_enclosing_quotes}%'"
                    )
                elif operator in ("notcontains", "does_not_contains"):
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT LIKE '%{value_without_enclosing_quotes}%'"
                    )
                elif operator == "icontains":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} ILIKE '%{value_without_enclosing_quotes}%'"
                    )
                elif operator == "noticontains":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT ILIKE '%{value_without_enclosing_quotes}%'"
                    )
                elif operator in ("startswith", "starts_with"):
                    filter_list.append(
                        f"{self.col_prefix}{col_name} LIKE '{value_without_enclosing_quotes}%'"
                    )
                elif operator == "notstartswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT LIKE '{value_without_enclosing_quotes}%'"
                    )
                elif operator == "istartswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} ILIKE '{value_without_enclosing_quotes}%'"
                    )
                elif operator == "notistartswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT ILIKE '{value_without_enclosing_quotes}%'"
                    )
                elif operator in ("endswith", "ends_with"):
                    filter_list.append(
                        f"{self.col_prefix}{col_name} LIKE '%{value_without_enclosing_quotes}'"
                    )
                elif operator == "notendswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT LIKE '%{value_without_enclosing_quotes}'"
                    )
                elif operator == "iendswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} ILIKE '%{value_without_enclosing_quotes}'"
                    )
                elif operator == "notiendswith":
                    filter_list.append(
                        f"{self.col_prefix}{col_name} NOT ILIKE '%{value_without_enclosing_quotes}'"
                    )
                elif operator == "isempty":
                    filter_list.append(
                        f"({self.col_prefix}{col_name} IS NULL OR {self.col_prefix}{iter_filter[0]} = '' OR IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                    )
                elif operator == "isnotempty":
                    filter_list.append(
                        f"({self.col_prefix}{col_name} IS NOT NULL AND {self.col_prefix}{iter_filter[0]} != '' AND NOT IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                    )
                elif operator == "isnull":
                    filter_list.append(
                        f"({self.col_prefix}{col_name} IS NULL OR IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                    )
                elif operator == "isnotnull":
                    filter_list.append(
                        f"({self.col_prefix}{col_name} IS NOT NULL AND NOT IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                    )
                elif operator == "notbetween":
                    expected_length = 2
                    if (
                        isinstance(value, (list, tuple))
                        and len(value) == expected_length
                    ):
                        filter_list.append(
                            f"({self.col_prefix}{col_name} IS NOT NULL AND {self.col_prefix}{iter_filter[0]} != '' AND NOT IS_NULL_VALUE({self.col_prefix}{iter_filter[0]}))"
                        )
                        if column_dtype.upper() in ("DATE", "DATETIME"):
                            query_col_name = (
                                f"date_trunc('day', {self.col_prefix}{col_name})"
                            )
                        else:
                            query_col_name = f"{self.col_prefix}{col_name}"
                        if isinstance(value[0], str) and isinstance(value[1], str):
                            filter_list.append(
                                f"({query_col_name} NOT BETWEEN '{value[0]}' AND '{value[1]}')"
                            )
                        else:
                            filter_list.append(
                                f"({query_col_name} NOT BETWEEN {value[0]} AND {value[1]})"
                            )
                    else:
                        raise NotBetweenFilterError(value=value)
                else:
                    raise OperationNotSupportedError(
                        operation=f"iter_Filter {iter_filter[1]} "
                    )
            if query_string:
                query_string += " AND " + " AND ".join(filter_list)
            else:
                query_string = "(" + " AND ".join(filter_list) + ")"
            logger.debug(
                "END: query_for_filters: %s",
                self.query_id,
                extra=query_builder_params.model_dump(),
            )
        return query_string

    def apply_simulation_custom_filters(
        self,
        simulation_custom_filters,
        query_string,
        colname_type_map,
        ever_comparison=None,
    ):
        """
        Filter ID       FILTER APPLIED
            1.          sorted_col > 'sorted_col_value'
                        OR (sorted_col = 'sorted_col_value' AND row_key >= 'row_key_value')
                        OR sorted_col IS NULL

            2.          sorted_col < 'sorted_col_value'
                        OR (sorted_col = 'sorted_col_value' AND row_key <= 'row_key_value')

            3.          (sorted_col IS NULL AND row_key <= 'row_key_value')
                        OR sorted_col IS NOT NULL

            4.          sorted_col IS NULL
                        AND row_key >= 'row_key_value'
                        (this one is supported by existing methods; but using it here for maintainence at one place)
        """
        ever_comparison = bool(ever_comparison)

        def get_filter(filter_id, sorted_col, sorted_col_value, row_key_value):
            def get_udf_ever_comparison_expression(colname, operator, value):
                operator_symbol_map = {
                    "=": "==",
                    "!=": "!=",
                    "<>": "!=",
                    "<": "<",
                    "<=": "<=",
                    ">": ">",
                    ">=": ">=",
                }
                return f"udf_ever_comparison({colname}, '{operator_symbol_map[operator]}', {value})"

            if isinstance(sorted_col_value, str):
                sorted_col_value = escape_special_characters(sorted_col_value)
                sorted_col_value = f"'{sorted_col_value}'"

            if isinstance(row_key_value, str):
                row_key_value = escape_special_characters(row_key_value)
                row_key_value = f"'{row_key_value}'"

            use_udf_ever_expresssion = ever_comparison and colname_type_map[
                sorted_col
            ].upper() in ("DOUBLE", "INTEGER")
            sorted_col = (
                f"{self.col_prefix}{sorted_col}::{colname_type_map[sorted_col]}"
            )
            row_key = f"{self.col_prefix}row_key::{colname_type_map['row_key']}"

            if filter_id == FilterIdMap.FILTER_ID_ONE.value:
                return (
                    f" AND ({get_udf_ever_comparison_expression(sorted_col, '>', sorted_col_value)} OR ({get_udf_ever_comparison_expression(sorted_col, '=', sorted_col_value)} AND {row_key} >= {row_key_value}) OR {sorted_col} IS NULL)"
                    if use_udf_ever_expresssion
                    else f" AND ({sorted_col} > {sorted_col_value} OR ({sorted_col} = {sorted_col_value} AND {row_key} >= {row_key_value}) OR {sorted_col} IS NULL)"
                )
            if filter_id == FilterIdMap.FILTER_ID_TWO.value:
                return (
                    f" AND ({get_udf_ever_comparison_expression(sorted_col, '<', sorted_col_value)} OR ({get_udf_ever_comparison_expression(sorted_col, '=', sorted_col_value)} AND {row_key} <= {row_key_value}))"
                    if use_udf_ever_expresssion
                    else f" AND ({sorted_col} < {sorted_col_value} OR ({sorted_col} = {sorted_col_value} AND {row_key} <= {row_key_value}))"
                )
            if filter_id == FilterIdMap.FILTER_ID_THREE.value:
                return f" AND (({sorted_col} IS NULL AND {row_key} <= {row_key_value}) OR {sorted_col} IS NOT NULL)"
            if filter_id == FilterIdMap.FILTER_ID_FOUR.value:
                return f" AND {sorted_col} IS NULL AND {row_key} >= {row_key_value}"
            raise InvalidFilterIdError(filter_id=filter_id)

        return query_string + get_filter(
            simulation_custom_filters.filter_id,
            simulation_custom_filters.sorted_col,
            simulation_custom_filters.sorted_col_value,
            simulation_custom_filters.row_key_value,
        )

    def apply_infix_filters(
        self,
        *,
        infix_filters,
        colname_type_map,
        query_string,
        case_insensitive_infix_filters=False,
    ):
        for infix_filter in infix_filters:
            infix_query_string = InfixToSnowflakeQuery(
                # TODO: @sriramtr - use model dump when infix type is resolved to InfixV1ListModel
                infix_expression=infix_filter,
                col_prefix=self.col_prefix,
                colname_type_map=colname_type_map,
                typecast_operator="::",
                client_id=self.client_id,
                case_insensitive=case_insensitive_infix_filters,
            ).convert()
            has_where_clause = "WHERE" in query_string.upper()
            if has_where_clause:
                query_string += " AND (" + infix_query_string + ")"
            else:
                query_string += " WHERE (" + infix_query_string + ")"

        return query_string

    def query_to_get_records_count(
        self,
        query_builder_params: VariantTableQueryBuilderType,
        query_string,
        records_count,
    ):
        if records_count:
            logger.info(
                "BEGIN: query_to_get_records_count: %s",
                self.query_id,
                extra=query_builder_params.model_dump(),
            )
            query_template = (
                'SELECT COUNT(*) AS "{col_prefix}records_count" FROM ({inner_query})'
            )
            query_string = query_template.format(
                col_prefix=self.col_prefix, inner_query=query_string
            )
            logger.info(
                "END: query_to_get_records_count: %s",
                self.query_id,
                extra=query_builder_params.model_dump(),
            )
        return query_string

    def construct_query_string_for_dd(
        self, query_builder_params: VariantTableQueryBuilderType
    ):
        # hard converting to string
        json_col_name = query_builder_params.json_col_name
        query_spec = query_builder_params.query_spec
        logger.debug(
            "BEGIN: construct_query_string_for_dd: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )

        databook_id = str(self.databook_id)
        datasheet_id = str(self.datasheet_id)
        knowledge_date = query_builder_params.knowledge_date
        records_count = query_builder_params.records_count
        add_display_name_alias_in_select = (
            query_builder_params.add_display_name_alias_in_select
        )

        ds_permission_query = None
        query_string = query_builder_params.query_string
        logger.info(
            "BEGIN: Fetching datasheet variables for databook_id: %s, datasheet_id: %s",
            databook_id,
            datasheet_id,
        )
        start_time = time.monotonic()
        if add_display_name_alias_in_select:
            datasheet_variables = DatasheetVariableAccessor(
                self.client_id
            ).get_ds_var_system_name_and_dtype_superset(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                knowledge_date=knowledge_date,
            )
        else:
            datasheet_variables = DatasheetVariableAccessor(
                self.client_id
            ).get_ds_var_system_name_and_dtype(
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                knowledge_date=knowledge_date,
            )
        logger.debug(
            "Fetching datasheet variables took %s", time.monotonic() - start_time
        )
        datatype_id_name_map = get_datatype_id_name_map()
        colname_type_map = {"row_key": "STRING"}

        for each_variable in datasheet_variables:
            colname_type_map[str(each_variable.get("system_name"))] = (
                resolve_snowflake_data_type(
                    datatype_id_name_map[each_variable.get("data_type_id")],
                    access_store="stormbreaker",
                )
            )
        if (
            query_builder_params.apply_datasheet_permissions
            and not query_builder_params.user_has_databook_manage_permission
        ):
            ds_permission_query = self.query_for_datasheet_permissions(
                query_builder_params, colname_type_map
            )

        query_string = self.query_for_select_predicate(
            query_builder_params,
            datasheet_variables,
            json_col_name,
            colname_type_map,
        )
        table_name = self._get_datasheet_data_table_name(datasheet_id)
        query_builder_params.select_clause = (query_string.split("SELECT "))[1].split(
            f" FROM {table_name}"
        )[0]
        query_builder_params.query_string = query_string
        query_string = self.query_for_default_filter(query_builder_params)
        if ds_permission_query:
            query_string = query_string + ds_permission_query
        query_builder_params.query_string = query_string
        query_string = self.query_for_filters(
            query_builder_params,
            colname_type_map,
        )

        if query_spec.simulation_custom_filters is not None:
            # TODO: These explicit filters must be replaced when stormbreaker supports OR and Nested filters
            query_string = self.apply_simulation_custom_filters(
                simulation_custom_filters=query_spec.simulation_custom_filters,
                query_string=query_builder_params.query_string,
                colname_type_map=colname_type_map,
                ever_comparison=query_builder_params.ever_comparison,
            )

        if query_spec.infix_filters:
            query_string = self.apply_infix_filters(
                infix_filters=query_spec.infix_filters,
                colname_type_map=colname_type_map,
                query_string=query_string,
                case_insensitive_infix_filters=query_spec.case_insensitive_infix_filters,
            )

        query_string = self.query_for_group_by(
            query_spec, query_string, colname_type_map
        )
        query_string = self.query_for_order_by(
            query_spec, query_string, colname_type_map
        )
        # TODO: Ask superset team to use reset method, instead of this hack.
        if not add_display_name_alias_in_select:
            query_string = self.query_for_limit_offset(
                query_string, query_builder_params, colname_type_map
            )
        query_string = self.query_to_get_records_count(
            query_builder_params, query_string, records_count
        )
        logger.debug(
            "END: construct_query_string_for_dd: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )
        return query_string

    def construct_query_string_for_adjustment(
        self, query_builder_params: VariantTableQueryBuilderType
    ):
        logger.info(
            "BEGIN: construct_query_string_for_adjustment: %s",
            self.query_id,
            extra=query_builder_params.model_dump(),
        )
        query_spec = query_builder_params.query_spec
        databook_id = str(self.databook_id)
        datasheet_id = str(self.datasheet_id)
        knowledge_date = query_builder_params.knowledge_date
        json_col_name = query_builder_params.json_col_name
        datasheet_variables = DatasheetVariableAccessor(
            self.client_id
        ).get_ds_var_system_name_and_dtype(databook_id, datasheet_id, knowledge_date)
        datatype_id_name_map = get_datatype_id_name_map()
        colname_type_map = {f"{json_col_name}:row_key": "STRING"}

        for each_variable in datasheet_variables:
            colname_type_map[str(each_variable.get("system_name"))] = (
                resolve_snowflake_data_type(
                    datatype_id_name_map[each_variable.get("data_type_id")],
                    access_store="stormbreaker",
                )
            )

        query_string = self.query_for_select_predicate(
            query_builder_params,
            datasheet_variables,
            json_col_name,
            colname_type_map,
        )
        table_name = self._get_datasheet_data_table_name(datasheet_id)
        query_builder_params.select_clause = (query_string.split("SELECT "))[1].split(
            f" FROM {table_name}"
        )[0]
        query_builder_params.query_string = query_string
        query_string = self.query_for_default_filter(query_builder_params)
        query_string = self.query_to_get_records_count(
            query_builder_params, query_string, query_builder_params.records_count
        )
        logger.info(
            "END: construct_query_string_for_adjustment: %s %s",
            self.query_id,
            query_string,
            extra=query_spec.model_dump(),
        )
        return query_string
