from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, conint

DEFAULT_PURE_COL_PREFIX = "pure_"


class DatasheetReaderTechnique(Enum):
    SNOWFLAKE_VARIANT = {
        "key": "snowflake_variant",
        "description": "Data stored in Snowflake native tables as a variant column - current approach (as of Dec 2023)",
    }
    SNOWFLAKE_FLATTENED = {
        "key": "snowflake_flattened",
        "description": "Data stored in Snowflake native tables as a flattened column - current approach (as of Dec 2023)",
    }
    DUCKDB_FALLBACK_VARIANT_SNOWFLAKE = {
        "key": "duckdb_fallback_variant_snowflake",
        "description": "Data stored in DuckDB tables with variant columns and fallback to Snowflake if the data for that knowledge date isnt available in EFS ",
    }


class FilterIdMap(Enum):
    FILTER_ID_ONE = 1
    FILTER_ID_THREE = 3
    FILTER_ID_TWO = 2
    FILTER_ID_FOUR = 4


class SimulationCustomFilterType(BaseModel):
    filter_id: int
    sorted_col: str
    sorted_col_value: str | int | bool | datetime | None
    row_key_value: str


class QuerySpecType(BaseModel):
    selects: list[str | dict] = []
    filters: list[
        tuple[str, str, None | str | int | float | bool | datetime | list]
    ] = []
    # TODO - @sukanya/@sriramtr - Use InfixV1ListModel instead of dict for infix_filters
    infix_filters: list[list[dict]] = []
    group_bys: list[str] = []
    having_bys: list[str] = []
    order_bys: list[dict[str, str]] = []
    distinct_values: bool = False
    limit: conint(ge=0) | None = (
        None  # Needed for params construction for fetch_user_sheet_data
    )
    offset: conint(ge=0) | None = None
    simulation_custom_filters: None | SimulationCustomFilterType = None
    case_insensitive_infix_filters: bool = False


class QueryBuilderType(BaseModel):
    limit: conint(ge=0) | None = 100
    offset: conint(ge=0) | None = 0
    add_display_name_alias_in_select: bool = False
    records_count: bool = False
    add_system_name_alias_in_select: bool = False
    columns_to_be_hidden: list[str] = []
    has_meta_cols: Optional[bool] = False
    apply_datasheet_permissions: bool = True
    logged_in_user_email: str | None = None
    ever_comparison: bool = False
    user_has_databook_manage_permission: None | bool = None
    select_clause: str = ""
    is_adjusted: int = 0
    fetch_records_after: None | datetime = None
    as_dataframe: bool = False
    full_table_expanded_query: bool = False
    knowledge_date: None | datetime = None
    records_invalidated_after: None | datetime = None
    query_spec: QuerySpecType = QuerySpecType()
    query_string: str = ""
    system_name_custom_name_map: dict[str, str] | None = None
    add_custom_name_alias_in_select: bool = False


class VariantTableQueryBuilderType(QueryBuilderType):
    json_col_name: str = "data"
    data_pure_null: int = 0
    data_null: int = 0
    should_quote_column_name: bool = True


class FlattenedTableQueryBuilderType(QueryBuilderType):
    pure_col_prefix: str = ""
    table_name: str
    pure_rowkey_null: int = 0
    rowkey_null: int = 0


class ExecutorParamsType(BaseModel):
    query_id: UUID = uuid4()
    limit: conint(ge=0) | None = 100
    offset: conint(ge=0) | None = 0
    query_spec: QuerySpecType = QuerySpecType()
    fetch_only_adjusted_records: bool = False
    fetch_data_without_adjustments: bool = False
    as_dataframe: bool = False
    return_query_alone: bool = False
    meta_col_names: list[str] = []
    fetch_null_as_null: bool = False
    user_has_co_permission: None | bool = None
    records_count: bool = False
    apply_datasheet_permissions: bool = True
    user_has_databook_manage_permission: None | bool = None
    add_display_name_alias_in_select: bool = False
    logged_in_user_email: None | str = None
    add_system_name_alias_in_select: bool = False
    full_table_expanded_query: bool = False
    should_quote_column_name: bool = True
    system_name_custom_name_map: dict[str, str] | None = None
    add_custom_name_alias_in_select: bool = False


class KnowledgeDateAwareExecutorParamsType(ExecutorParamsType):
    fetch_records_after: None | datetime = None
    knowledge_date: None | datetime = None
    records_invalidated_after: None | datetime = None


class StormBreakerDSInitType(BaseModel):
    compute_strategy: str
    client_id: int
    databook_id: UUID
    datasheet_id: UUID
    logged_in_user_email: None | str = None
    log_context: dict = {}
    knowledge_date: None | datetime = None
    records_invalidated_after: None | datetime = None


# TODO: @sukanya - Add return types for all interface methods


class ExecutorBase(ABC):
    @abstractmethod
    def get_query_string(self, params: ExecutorParamsType):
        pass

    @abstractmethod
    def get_expanded_query_string(self, params: ExecutorParamsType):
        """
        Returns a query string with additional columns for both raw and pure data.

        Args:
            params (ExecutorParamsType): Parameters for the executor.

        Returns:
            str: A query string with expanded columns.

        This method expands the original query string to include additional columns
        for both raw and pure data. If the original sheet has X columns, the returned
        query will have 2*X columns. The additional columns for pure data will be prefixed
        with "pure_" and will contain data without adjustments.
        """
        pass

    @abstractmethod
    def get_records_count(self, params: ExecutorParamsType):
        pass

    @abstractmethod
    def fetch_datasheet_data(self, params: ExecutorParamsType):
        pass

    @abstractmethod
    def supports_knowledge_date(self, knowledge_date) -> bool:
        return False

    @abstractmethod
    def supports_fetch_records_after(self) -> bool:
        return False

    @abstractmethod
    def supports_records_invalidated_after(self) -> bool:
        return False


class StrInSingleQuote(str):
    def __repr__(self):
        # Allow str.__repr__() to do the hard work, then
        # remove the outer two characters, single quotes,
        # and replace them with double quotes.
        return "".join(("'", super().__repr__()[1:-1], "'"))
