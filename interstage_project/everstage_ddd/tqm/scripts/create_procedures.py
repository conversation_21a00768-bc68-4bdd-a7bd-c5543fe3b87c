"""
Script to create Snowflake stored procedures from SQL files.

Example usage:
    python create_procedures.py
"""

import os
from pathlib import Path
from typing import Dict

import django
import snowflake.connector
import typer
from rich.console import Console

django.setup()

from everstage_ddd.tqm import utils as tqm_utils

# Rich setup
console = Console()
app = typer.Typer()


def get_env(key: str) -> str:
    val = os.getenv(key)
    if not val:
        raise ValueError(f"Environment variable {key} is not set")
    return val


class SnowflakeProcsCreator:
    def __init__(self, conn_params: Dict[str, str]):
        """
        Initialize Snowflake connection with provided credentials.

        Args:
            conn_params: Dictionary containing Snowflake connection parameters
            target_database: Optional target database name
        """
        self.conn_params = conn_params
        self.conn = None
        self.cursor = None

    def connect(self) -> None:
        """Establish connection to Snowflake."""
        try:
            self.conn = snowflake.connector.connect(**self.conn_params)
            self.cursor = self.conn.cursor()
            console.print("Successfully connected to Snowflake")
        except Exception as e:
            console.print(f"[red]Failed to connect to Snowflake: {str(e)}[/red]")
            raise

    def close(self) -> None:
        """Close Snowflake connection."""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            console.print("Snowflake connection closed")

    def read_sql_file(self, file_path: str) -> str:
        """Read SQL file content."""
        try:
            path = Path(file_path)
            return path.open("r").read()
        except Exception as e:
            console.print(f"[red]Failed to read file {file_path}: {str(e)}[/red]")
            raise

    def get_sql_files(self) -> list[str]:
        """Get list of SQL files in current directory."""
        current_dir = Path(__file__).parent
        sql_dir = current_dir.parent / "sql"

        if not sql_dir.exists():
            raise ValueError(f"SQL directory not found at: {sql_dir}")

        return [
            str(sql_dir / f)
            for f in sql_dir.iterdir()
            if f.is_file() and f.name.endswith(".sql")
        ]

    def create_procedures(self) -> None:
        """Create all stored procedures from SQL files."""
        sql_files = self.get_sql_files()
        console.print(f"Found {len(sql_files)} SQL files")

        database = self.conn_params["database"]
        console.print(f"[blue]Using database {database}[/blue]")
        self.cursor.execute(f"USE DATABASE {database}")
        console.print("[green]Done[/green]")

        failed_files = []

        for sql_file_path in sql_files:
            sql_file = Path(sql_file_path).name
            try:
                console.print(f"[blue]Processing {sql_file}[/blue]")
                sql_content = self.read_sql_file(sql_file_path)
                self.cursor.execute(sql_content)
                console.print("[green]Done[/green]")
            except Exception as e:
                console.print(f"[red]Failed: {str(e)}[/red]")
                failed_files.append(sql_file_path)

        if failed_files:
            console.print(
                "[red]Failed to create procedures for the following files:[/red]"
            )
            for file in failed_files:
                console.print(f"[red]{file}[/red]")

    def upload_coordinates_csv(self) -> None:
        """Upload SIGMA_STATES_COORDINATES.csv to Snowflake and create table."""
        try:
            # Get the current directory where the script is running
            current_dir = Path(__file__).parent
            csv_file_path = current_dir / "state_coordinates.csv"

            # Create table
            create_table_sql = """
            CREATE OR REPLACE TABLE SIGMA_STATES_COORDINATES (
                STATE VARCHAR,
                COORDINATES VARIANT
            );
            """
            self.cursor.execute(create_table_sql)
            console.print("Created SIGMA_STATES_COORDINATES table")

            # First, PUT the local file to Snowflake using absolute path
            put_sql = f"PUT file://{csv_file_path.resolve()} @~"
            self.cursor.execute(put_sql)
            console.print("Uploaded coordinates file to Snowflake")

            # Now COPY from the staged file
            copy_into_sql = """
            COPY INTO SIGMA_STATES_COORDINATES
            FROM '@~/state_coordinates.csv'
            FILE_FORMAT = (
                TYPE = 'CSV'
                FIELD_DELIMITER = ','
                FIELD_OPTIONALLY_ENCLOSED_BY = '"'
                ESCAPE = '\\\\'
                NULL_IF = ('NULL', 'null')
                EMPTY_FIELD_AS_NULL = TRUE
                SKIP_HEADER = 1
            )
            ON_ERROR = 'ABORT_STATEMENT'
            """
            self.cursor.execute(copy_into_sql)
            console.print("Successfully loaded coordinates data")

        except Exception as e:
            console.print(f"Failed to setup coordinates data: {str(e)}")
            raise


@app.command()
def create_procedures():
    """
    Creates Snowflake stored procedures from SQL files in the current directory.
    """
    # Snowflake connection parameters
    conn_params = {
        "user": get_env("SNOWFLAKE_USER"),
        "password": get_env("SNOWFLAKE_PASSWORD"),
        "account": get_env("SNOWFLAKE_ACCOUNT"),
        "warehouse": get_env("SNOWFLAKE_WAREHOUSE"),
        "database": tqm_utils.get_snowflake_procedures_db(),
        "schema": "PUBLIC",
        "role": "ACCOUNTADMIN",
    }
    sf_creator = SnowflakeProcsCreator(conn_params)
    try:
        # Create instance and connect
        sf_creator.connect()

        # Create procedures
        sf_creator.create_procedures()

        # Upload coordinates csv
        sf_creator.upload_coordinates_csv()

    except Exception as e:
        console.print(f"[red]An error occurred: {str(e)}[/red]")
        raise

    finally:
        # Ensure connection is closed
        sf_creator.close()


if __name__ == "__main__":
    app()
