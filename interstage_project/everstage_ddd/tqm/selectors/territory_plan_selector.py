from typing import Dict, List, Optional

from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.tqm.models.territory_plan_model import TerritoryPlan


class TerritoryPlanSelector(BiTemporalSelector):
    def __init__(self, client_id: int):
        super().__init__(client_id, TerritoryPlan)

    def get_all_territory_plans(
        self,
        selected_year: Optional[str] = None,
        selected_status: Optional[str] = None,
        projection: Optional[List[str]] = None,
        order_by: Optional[List[str]] = None,
    ) -> Dict:
        qs = self.client_kd_deleted_aware()
        if selected_year:
            qs = qs.filter(effective_start_date__year=selected_year)

        # Calculate counts before applying filters
        draft_count = qs.filter(plan_stage__iexact="draft").count()
        published_count = qs.filter(plan_stage__iexact="published").count()

        # Apply filters only if parameters are provided
        if selected_status and selected_status != "all":
            qs = qs.filter(plan_stage__iexact=selected_status)
        if order_by:
            qs = qs.order_by(*order_by)

        # Convert to values queryset
        qs = qs.values(*projection) if projection else qs.values()

        # Return both the list of territory plans and the counts
        return {
            "territory_plans": list(qs),
            "draft_count": draft_count,
            "published_count": published_count,
        }

    def get_territory_plan(
        self, tplan_id: str, projection: Optional[List[str]] = None
    ) -> Dict:
        qs = self.client_kd_deleted_aware()
        qs = (
            qs.values(*projection)
            if projection
            else qs.values("tplan_id", "name", "sigma_attributes")
        )
        return qs.get(tplan_id=tplan_id)

    def create_territory_plan(self, data: dict):
        return self.bitemporal_create(data)

    def delete_territory_plan(self, tplan_id):
        tplan = self.get_territory_plan(tplan_id, projection=["temporal_id"])
        temporal_id = tplan.get("temporal_id")
        self.bitemporal_delete(record_identifier=temporal_id)

    def update_territory_plan(self, temporal_id, tplan_name, payload_data):
        if payload_data.get("name") != tplan_name and self.check_plan_name_exists(
            payload_data.get("name")
        ):
            raise ValueError(
                "Plan name already exists. Please choose a different name."
            )
        self.bitemporal_update(record_identifier=temporal_id, data=payload_data)

    def check_plan_name_exists(self, name: str) -> bool:
        """
        Check if a plan name already exists for the client

        Args:
            name (str): The plan name to check

        Returns:
            bool: True if name exists, False otherwise
        """
        return self.client_kd_deleted_aware().filter(name__iexact=name).exists()

    def get_territory_plans_using_datasheet(self, datasheet_id):
        """
        Get all territory plans that use the specified datasheet in their data sources.

        Args:
            datasheet_id (str|int): The ID of the datasheet to check

        Returns:
            list: list of territory plans ids where the datasheet exists
        """
        qs = self.client_kd_deleted_aware()
        plans = qs.filter(data_sources__contains=[{"datasheet_id": str(datasheet_id)}])
        return list(plans.values_list("tplan_id", flat=True))

    def get_territory_plans_using_databook(self, databook_id):
        """
        Get all territory plans that use the specified databook in their data sources.

        Args:
            databook_id (str|int): The ID of the databook to check

        Returns:
            list: list of territory plans ids where databook exists
        """

        qs = self.client_kd_deleted_aware()
        plans = qs.filter(data_sources__contains=[{"databook_id": str(databook_id)}])

        return list(plans.values_list("tplan_id", flat=True))

    def get_plans_with_name_pattern(
        self, name_pattern: str, projection: Optional[List[str]] = None
    ) -> List[Dict]:
        """
        Get all territory plans whose names match a specific pattern.

        Args:
            name_pattern (str): The SQL LIKE pattern to match against plan names
            projection (List[str], optional): List of fields to include in the result

        Returns:
            List[Dict]: List of plans matching the name pattern
        """
        qs = self.client_kd_deleted_aware()
        qs = qs.filter(name__startswith=name_pattern)

        if projection:
            qs = qs.values(*projection)
        else:
            qs = qs.values("tplan_id", "name")

        return list(qs)
