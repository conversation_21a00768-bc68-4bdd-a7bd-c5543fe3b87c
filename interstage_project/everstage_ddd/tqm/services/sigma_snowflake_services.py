import logging
import os
import uuid
from datetime import datetime

import pydash
from django.utils.timezone import make_aware

from commission_engine.database.snowflake_connection import create_snowpark_session
from commission_engine.utils.date_utils import end_of_day
from everstage_ddd.datasheet.selectors import (
    DatasheetSelector,
    DatasheetVariableSelector,
)
from everstage_ddd.tqm import utils as tqm_utils
from everstage_ddd.tqm.constants import MAX_HIERARCHY_LEVELS, MAX_METRICS
from everstage_ddd.tqm.selectors.territory_plan_selector import TerritoryPlanSelector
from spm.services.databook_services import databook_names_by_ids

logger = logging.getLogger(__name__)


class SigmaSnowflakeServices:
    def __init__(self, client_id):
        self.client_id = client_id
        self.session = None

    def connect(self):
        self.session = create_snowpark_session(client_id=self.client_id)

    def close(self):
        if self.session is not None:
            self.session.close()

    def execute_sql(self, sql):
        if self.session is None:
            self.connect()
        # At this point, self.session should not be None
        if self.session is None:
            raise ValueError("Failed to establish Snowflake session")
        return self.session.sql(sql).collect()

    def get_sigma_read_db(self):
        return tqm_utils.get_snowflake_read_db(self.client_id)

    def create_sigma_read_db(self):
        logger.info(f"Creating sigma read db for client_id: {self.client_id}")
        sigma_read_db = self.get_sigma_read_db()
        self.execute_sql(f"create database if not existS {sigma_read_db}")

    def use_sigma_read_db(self):
        logger.info(f"Using sigma read db for client_id: {self.client_id}")
        sigma_read_db = self.get_sigma_read_db()
        self.execute_sql(f"use database {sigma_read_db}")

    def grant_access_to_sigma_read_db(self):
        logger.info(f"Granting access to sigma read db for client_id: {self.client_id}")
        sigma_role = tqm_utils.get_snowflake_role()
        sigma_read_db = self.get_sigma_read_db()
        self.execute_sql(
            f"grant usage on database {sigma_read_db} to role {sigma_role}"
        )
        self.execute_sql(
            f"grant usage on all schemas in database {sigma_read_db} to role {sigma_role}"
        )
        self.execute_sql(
            f"grant select on all tables in database {sigma_read_db} to role {sigma_role}"
        )
        self.execute_sql(
            f"grant select on future tables in database {sigma_read_db} to role {sigma_role}"
        )
        self.execute_sql(
            f"grant select on all views in database {sigma_read_db} to role {sigma_role}"
        )
        self.execute_sql(
            f"grant select on future views in database {sigma_read_db} to role {sigma_role}"
        )

    def create_data_sources(self, tplan_id, data_sources):
        logger.info(f"Creating data sources for tplan_id: {tplan_id}")
        datasheet_ids = pydash.map_(data_sources, "datasheet_id")
        datasheets = DatasheetSelector(self.client_id).get_datasheets(
            datasheet_ids,
            projection=[
                "datasheet_id",
                "ordered_columns",
                "name",
                "primary_key",
            ],
        )
        datasheet_map = {
            str(datasheet["datasheet_id"]): datasheet for datasheet in datasheets
        }
        datasheet_vars = DatasheetVariableSelector(
            self.client_id
        ).get_variables_for_datasheets(
            datasheet_ids,
            projection=[
                "system_name",
                "display_name",
                "datasheet_id",
                "data_type__data_type",
            ],
        )
        snowflake_data_type_map = {
            "Integer": "number",
            "Date": "datetime",
            "Boolean": "boolean",
            "String": "string",
            "Email": "string",
            "Percentage": "number",
        }
        datasheet_vars_map = {}
        for var in datasheet_vars:
            datasheet_id = str(var["datasheet_id"])
            if datasheet_id not in datasheet_vars_map:
                datasheet_vars_map[datasheet_id] = {}
            datasheet_vars_map[datasheet_id][var["system_name"]] = var
        hierarchy_counter = 1
        others_counter = 1
        accounts_primary_key_column = None

        # Store hierarchy metadata for v2 table
        hierarchy_metadata = []
        databook_ids = []
        for data_source in data_sources:
            databook_ids.append(data_source["databook_id"])
        databook_names = databook_names_by_ids(
            client_id=self.client_id, databook_ids=databook_ids
        )
        logger.info(f"databook_names: {databook_names}")
        for data_source in data_sources:
            datasheet_id = data_source["datasheet_id"]
            tag = data_source["tag"]
            sync_date = data_source["sync_date"]
            datasheet = datasheet_map[datasheet_id]
            datasheet_name = datasheet["name"]
            databook_id = data_source["databook_id"]
            # Convert string databook_id to UUID object for dictionary lookup
            databook_uuid = uuid.UUID(databook_id)
            databook_name = databook_names.get(databook_uuid)
            datasheet_vars = datasheet_vars_map[datasheet_id]
            new_table_name = f"ds_{tag}"
            if tag == "hierarchy":
                new_table_name += f"{hierarchy_counter}"
                hierarchy_counter += 1
            elif tag == "others":
                new_table_name += f"{others_counter}"
                others_counter += 1
            new_table_name += f"_{self.client_id}_{tplan_id}"
            new_table_name = f'"{new_table_name}"'
            select_cols = []
            for var_name in datasheet["ordered_columns"]:
                var = datasheet_vars_map[datasheet_id][var_name]
                snowflake_data_type = snowflake_data_type_map.get(
                    var["data_type__data_type"], "variant"
                )
                display_name = var["display_name"]
                system_name = var["system_name"]
                new_column_name = f"{pydash.snake_case(display_name)}"
                select_cols.append(
                    f"cast(data:{var['system_name']} as {snowflake_data_type}) as {new_column_name}"
                )
                if tag == "hierarchy":
                    # Store metadata for v2 table
                    hierarchy_metadata.append(
                        {
                            "system_name": f"{new_table_name}.{new_column_name}",
                            "display_name": f"{databook_name} > {datasheet_name} > {display_name}",
                            "databook_id": databook_id,
                            "datasheet_id": datasheet_id,
                            "databook_name": databook_name,
                            "datasheet_name": datasheet_name,
                            "variable_display_name": display_name,
                            "variable_system_name": system_name,
                            "sigma_datasheet_system_name": new_table_name.strip('"'),
                            "sigma_variable_system_name": new_column_name,
                        }
                    )
            if tag == "accounts":
                accounts_primary_key_column = pydash.snake_case(
                    datasheet_vars[datasheet["primary_key"][0]]["display_name"]
                )
            select_cols.append(f"{self.client_id} as client_id")
            source_table_name = f'{os.getenv("SNOWFLAKE_DATABASE")}.{os.getenv("SNOWFLAKE_SCHEMA")}."datasheet_data_{self.client_id}_{datasheet_id}"'
            where_clause = []
            if sync_date:
                sync_date_obj = make_aware(datetime.strptime(sync_date, "%Y-%m-%d"))
                sync_date_end = end_of_day(sync_date_obj)
                sync_date_str = sync_date_end.strftime("%Y-%m-%d %H:%M:%S")
                where_clause.append(f"knowledge_begin_date <= '{sync_date_str}'")
                where_clause.append(
                    f"(knowledge_end_date is null or knowledge_end_date > '{sync_date_str}')"
                )
            sql = f"""
                create table {new_table_name} as
                select {", ".join(select_cols)}
                from {source_table_name}
                """
            if where_clause:
                sql += f" where {' and '.join(where_clause)}"
            self.execute_sql(sql)
            logger.info(f"Created table {new_table_name}")
        return hierarchy_metadata, accounts_primary_key_column

    def create_hierarchy_level_options_table_v2(self, tplan_id):
        logger.info(f"Creating hierarchy level options table for tplan_id: {tplan_id}")
        table_name = f'"hierarchy_level_options_v2_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                system_name varchar,
                display_name varchar,
                databook_id varchar,
                datasheet_id varchar,
                databook_name varchar,
                datasheet_name varchar,
                variable_display_name varchar,
                variable_system_name varchar,
                sigma_datasheet_system_name varchar,
                sigma_variable_system_name varchar
            );
            """
        )

    def insert_hierarchy_level_options_v2(self, tplan_id, hierarchy_metadata):
        logger.info(f"Inserting hierarchy level options for tplan_id: {tplan_id}")
        table_name = f'"hierarchy_level_options_v2_{self.client_id}_{tplan_id}"'

        # Use list comprehension for the transformation
        values = [
            f"('{item['system_name']}', '{item['display_name']}', '{item['databook_id']}', '{item['datasheet_id']}', '{item['databook_name']}', '{item['datasheet_name']}', '{item['variable_display_name']}', '{item['variable_system_name']}', '{item['sigma_datasheet_system_name']}', '{item['sigma_variable_system_name']}')"
            for item in hierarchy_metadata
        ]

        if values:
            sql = f"""
            insert into {table_name} (system_name, display_name, databook_id, datasheet_id, databook_name, datasheet_name, variable_display_name, variable_system_name, sigma_datasheet_system_name, sigma_variable_system_name)
            values {", ".join(values)}
            """
            self.execute_sql(sql)
            logger.info(f"Inserted {len(values)} rows into {table_name}")
        else:
            logger.warning(f"No hierarchy metadata to insert for tplan_id: {tplan_id}")

    def update_hierarchy_level_options_v2(self, tplan_id, updated_data):
        # The updated data is a dict and have one pair. It can have datasheet_name and datasheet_id or databook_name and databook_id or variable_display_name and variable_system_name
        # The datasheet_id, databook_id, variable_system_name is unique and wont change with those help we have to update other values
        # We have to update the display_name and system_name of the updated data
        table_name = f'"hierarchy_level_options_v2_{self.client_id}_{tplan_id}"'

        if "datasheet_name" in updated_data and "datasheet_id" in updated_data:
            sql = f"""
            update {table_name}
            set datasheet_name = '{updated_data["datasheet_name"]}',
                display_name = replace(display_name, split_part(display_name, ' > ', 2), '{updated_data["datasheet_name"]}')
            where datasheet_id = '{updated_data["datasheet_id"]}'
            """
            self.execute_sql(sql)
        elif "databook_name" in updated_data and "databook_id" in updated_data:
            sql = f"""
            update {table_name}
            set databook_name = '{updated_data["databook_name"]}',
                display_name = replace(display_name, split_part(display_name, ' > ', 1), '{updated_data["databook_name"]}')
            where databook_id = '{updated_data["databook_id"]}'
            """
            self.execute_sql(sql)
        elif (
            "variable_display_name" in updated_data
            and "variable_system_name" in updated_data
        ):
            sql = f"""
            update {table_name}
            set variable_display_name = '{updated_data["variable_display_name"]}',
                display_name = replace(display_name, split_part(display_name, ' > ', 3), '{updated_data["variable_display_name"]}')
            where variable_system_name = '{updated_data["variable_system_name"]}'
            """
            self.execute_sql(sql)
        return

    def create_hierarchy_configuration_table(self, tplan_id):
        logger.info(f"Creating hierarchy configuration table for tplan_id: {tplan_id}")
        table_name = f'"hierarchy_configuration_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                level_number int,
                table_name varchar,
                column_name varchar
            );
            """
        )

    def create_hierarchy_table(self, tplan_id):
        logger.info(f"Creating hierarchy table for tplan_id: {tplan_id}")
        table_name = f'"hierarchy_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                {', '.join([f'l_{i + 1} varchar' for i in range(MAX_HIERARCHY_LEVELS)])},
                hierarchy_id varchar
            );
            """
        )

    def create_hierarchy_graph_table(self, tplan_id):
        logger.info(f"Creating hierarchy graph table for tplan_id: {tplan_id}")
        table_name = f'"hierarchy_graph_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                data varchar
            );
            """
        )

    def create_custom_territories_table(self, tplan_id):
        logger.info(f"Creating custom territories table for tplan_id: {tplan_id}")
        table_name = f'"custom_territories_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                hierarchy_id varchar,
                ct_id varchar default uuid_string(),
                ct_name varchar
            );
            """
        )

    def create_hierarchy_view(self, tplan_id):
        logger.info(f"Creating hierarchy view for tplan_id: {tplan_id}")
        view_name = f'"hierarchy_view_{self.client_id}_{tplan_id}"'
        h_table = f'"hierarchy_{self.client_id}_{tplan_id}"'
        ct_table = f'"custom_territories_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace view {view_name} as
            select
                scenario_id,
                {', '.join([f'l_{i + 1}' for i in range(MAX_HIERARCHY_LEVELS)])},
                hierarchy_id,
                null as ct_name,
                hierarchy_id as join_key
            from {h_table}
            union all
            select
                h.scenario_id,
                {', '.join([f'h.l_{i + 1}' for i in range(MAX_HIERARCHY_LEVELS)])},
                h.hierarchy_id,
                ct.ct_name,
                concat(h.hierarchy_id, ct.ct_name) as join_key
            from {h_table} h
            inner join {ct_table} ct
            on h.scenario_id = ct.scenario_id
            and h.hierarchy_id = ct.hierarchy_id
            """
        )

    def create_territory_options_view(self, tplan_id):
        logger.info(f"Creating territory options view for tplan_id: {tplan_id}")
        view_name = f'"territory_options_view_{self.client_id}_{tplan_id}"'
        h_view = f'"hierarchy_view_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace view {view_name} as
            select 
                scenario_id,
                join_key as system_name,
                regexp_replace(
                    regexp_replace(
                        concat_ws(
                            ' > ',
                            {', '.join([f"coalesce(l_{i + 1}, '')" for i in range(MAX_HIERARCHY_LEVELS)])},
                            coalesce(ct_name, '')
                        ),
                        '( > )+',
                        ' > '
                    ),
                    ' > $',
                    ''
                ) as display_name
            from {h_view}
            where ct_name is null or ct_name != 'Unmapped'
        """
        )

    def create_accounts_hierarchy_configuration(self, tplan_id):
        logger.info(
            f"Creating accounts hierarchy configuration for tplan_id: {tplan_id}"
        )
        table_name = f'"accounts_hierarchy_configuration_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                level_number int,
                column_name varchar
            );
            """
        )

    def create_territory_assignments_table(self, tplan_id):
        logger.info(f"Creating territory assignments table for tplan_id: {tplan_id}")
        table_name = f'"territory_assignments_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                territory_key varchar,
                employee_email_id varchar
            );
            """
        )

    def create_scenarios_table(self, tplan_id):
        logger.info(f"Creating scenarios table for tplan_id: {tplan_id}")
        table_name = f'"scenarios_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar primary key default uuid_string(),
                scenario_name varchar,
                is_partially_implemented boolean default false,
                source_scenario_id varchar
            )
            """
        )

    def create_default_scenario(self, tplan_id):
        logger.info(f"Creating default scenario for tplan_id: {tplan_id}")
        table_name = f'"scenarios_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"insert into {table_name} (scenario_id, scenario_name) values ('{uuid.uuid4().hex}', 'Scenario 1')"
        )

    def create_accounts_hierarchy_overrides_table(self, tplan_id):
        logger.info(
            f"Creating accounts hierarchy overrides table for tplan_id: {tplan_id}"
        )
        table_name = f'"accounts_hierarchy_overrides_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                account_id varchar,
                hierarchy_id varchar,
                ct_id varchar
            );
            """
        )

    def create_accounts_hierarchy_view(self, tplan_id, accounts_primary_key_column):
        logger.info(f"Creating accounts hierarchy view for tplan_id: {tplan_id}")
        aho_table = f'"accounts_hierarchy_overrides_{self.client_id}_{tplan_id}"'
        ct_table = f'"custom_territories_{self.client_id}_{tplan_id}"'
        view_name = f'"accounts_hierarchy_view_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace view {view_name} as
            select
                aho.scenario_id,
                aho.account_id,
                aho.hierarchy_id,
                concat(
                    aho.hierarchy_id,
                    coalesce(ct.ct_name, '')
                ) as join_key
            from {aho_table} aho
            left join {ct_table} ct
            on aho.scenario_id = ct.scenario_id
            and aho.ct_id = ct.ct_id
            """
        )

    def create_accounts_metrics_table(self, tplan_id):
        logger.info(f"Creating accounts metrics table for tplan_id: {tplan_id}")
        table_name = f'"accounts_metrics_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                account_id varchar,
                {", ".join([f"metric_{i + 1} decimal(38,10)" for i in range(MAX_METRICS)])}
            );
            """
        )

    def create_sigma_logs_table(self, tplan_id):
        logger.info(f"Creating sigma logs table for tplan_id: {tplan_id}")
        table_name = f'"sigma_logs_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                log_id varchar,
                action_name varchar,
                action_args variant,
                action_status varchar default 'in_progress',
                action_result variant,
                begin_timestamp timestamp default current_timestamp(),
                end_timestamp timestamp
            )
            """
        )

    def create_overrides_table(self, tplan_id):
        logger.info(f"Creating overrides table for tplan_id: {tplan_id}")
        table_name = f'"overrides_{self.client_id}_{tplan_id}"'
        self.execute_sql(
            f"""
            create or replace table {table_name} (
                scenario_id varchar,
                table_prefix varchar,
                column_name varchar,
                join_key varchar,
                absolute_value number,
                delta_value number
            );
            """
        )

    def create_combinations_table(self, tplan_id):
        logger.info(f"Creating combinations table for tplan_id: {tplan_id}")
        table_name = f'"combinations_{self.client_id}_{tplan_id}"'
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            TABLE_NAME VARCHAR,
            COLUMNS VARCHAR,
            {', '.join([f'L_{i+1} VARCHAR' for i in range(MAX_HIERARCHY_LEVELS)])}
        );
        """
        self.execute_sql(create_table_query)

    def setup_sigma_read_db(self):
        logger.info(f"BEGIN: setting up sigma read db for client_id: {self.client_id}")
        self.connect()
        self.create_sigma_read_db()
        self.grant_access_to_sigma_read_db()
        self.close()
        logger.info(f"END: setting up sigma read db for client_id: {self.client_id}")

    def create_sigma_tables_for_new_tplan(self, tplan_id, payload_data):
        logger.info("BEGIN: creating sigma tables for new tplan")
        self.connect()
        self.use_sigma_read_db()
        (hierarchy_metadata, accounts_primary_key_column) = self.create_data_sources(
            tplan_id, payload_data["data_sources"]
        )
        self.create_hierarchy_level_options_table_v2(tplan_id)
        self.insert_hierarchy_level_options_v2(tplan_id, hierarchy_metadata)
        self.create_hierarchy_configuration_table(tplan_id)
        self.create_hierarchy_table(tplan_id)
        self.create_hierarchy_graph_table(tplan_id)
        self.create_custom_territories_table(tplan_id)
        self.create_accounts_hierarchy_configuration(tplan_id)
        self.create_territory_assignments_table(tplan_id)
        self.create_scenarios_table(tplan_id)
        self.create_default_scenario(tplan_id)
        self.create_accounts_hierarchy_overrides_table(tplan_id)
        self.create_accounts_metrics_table(tplan_id)
        self.create_accounts_hierarchy_view(tplan_id, accounts_primary_key_column)
        self.create_hierarchy_view(tplan_id)
        self.create_territory_options_view(tplan_id)
        self.create_sigma_logs_table(tplan_id)
        self.create_overrides_table(tplan_id)
        self.create_combinations_table(tplan_id)
        self.close()
        logger.info("END: creating sigma tables for new tplan")


def update_sigma_table_for_databook(client_id, databook_id, databook_name):
    plan_ids = TerritoryPlanSelector(client_id).get_territory_plans_using_databook(
        databook_id=databook_id
    )
    if plan_ids:
        sigma_update_data = {
            "databook_name": databook_name,
            "databook_id": databook_id,
        }
        sigma_service = SigmaSnowflakeServices(client_id)
        sigma_service.connect()
        sigma_service.use_sigma_read_db()  # Ensure we're using the correct database

        for plan_id in plan_ids:
            logger.info(f"plan_id: {plan_id}")
            logger.info(f"sigma_update_data: {sigma_update_data}")
            sigma_service.update_hierarchy_level_options_v2(
                tplan_id=plan_id, updated_data=sigma_update_data
            )
        sigma_service.close()


def update_sigma_table_for_datasheet(client_id, datasheet_id, datasheet_name):
    plan_ids = TerritoryPlanSelector(client_id).get_territory_plans_using_datasheet(
        datasheet_id=datasheet_id
    )
    if plan_ids:
        sigma_update_data = {
            "datasheet_name": datasheet_name,
            "datasheet_id": datasheet_id,
        }
        sigma_service = SigmaSnowflakeServices(client_id)
        sigma_service.connect()
        sigma_service.use_sigma_read_db()  # Ensure we're using the correct database

        for plan_id in plan_ids:
            logger.info(f"plan_id: {plan_id}")
            logger.info(f"sigma_update_data: {sigma_update_data}")

            sigma_service.update_hierarchy_level_options_v2(
                tplan_id=plan_id, updated_data=sigma_update_data
            )
        sigma_service.close()
