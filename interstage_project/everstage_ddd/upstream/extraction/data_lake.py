import json
from datetime import datetime
from logging import Logger, getLogger
from os import getenv
from time import time
from uuid import UUID, uuid4

from django.utils import timezone
from django.utils.timezone import now
from pandas import DataFrame
from snowflake.snowpark import Session
from snowflake.snowpark.functions import cast, sql_expr
from snowflake.snowpark.types import (
    BooleanType,
    StringType,
    StructField,
    StructType,
    TimestampTimeZone,
    TimestampType,
    VariantType,
)
from typing_extensions import Self

from commission_engine.accessors.etl_config_accessor import ExtractionConfigAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session,
    get_connection,
)
from commission_engine.database.snowflake_query_utils import check_table_if_exists
from commission_engine.utils.general_data import UpstreamSyncModes
from everstage_infra.aws_infra.constants.environments import (
    ENVIRONMENT,
    PRODUCTION_ENVIRONMENT,
)
from interstage_project.global_utils.general_utils import uuid_in_snakecase

from .constants import UpstreamOperation
from .utils import (
    construct_upstream_key_expression,
    get_source_column_prefix,
    get_source_key_fields,
    get_upstream_staging_table_name,
    get_upstream_table_name,
    get_upstream_timestamps,
)

logger: Logger = getLogger(__name__)


class UpstreamChangesWriter:
    def __init__(  # noqa
        self,
        client_id: int,
        integration_id: UUID,
        snowpark_session: Session | None = None,
        data_fields: list[StructField] | None = None,
        is_validation: bool = False,  # noqa
    ) -> None:
        self.is_validation = is_validation
        self.client_id = client_id
        self.table_name: str = self.get_table_name(client_id, integration_id)
        self.temp_data_table_name: str = (
            f"temp_data_{uuid_in_snakecase(integration_id)}"
        )
        self.is_flattened = data_fields is not None
        self.schema: StructType = self.get_schema(data_fields)
        self.integration_id = integration_id
        self.source_primary_keys, self.source_snapshot_keys = get_source_key_fields(
            self.client_id, self.integration_id, is_flattened=self.is_flattened
        )

        self.connection = get_connection(auto_commit=False, client_id=self.client_id)
        self.snowpark_session = snowpark_session or create_snowpark_session(
            client_id=self.client_id, connection=self.connection
        )
        self.create_upstream_table_if_not_exists()

    def get_table_name(self, client_id: int, integration_id: UUID) -> str:
        table_name = f"{'VALIDATION_' if self.is_validation else ''}{get_upstream_table_name(client_id, integration_id)}"
        logger.info(f"Table name: {table_name}")
        return table_name

    def get_schema(self, data_fields: list[StructField] | None = None) -> StructType:
        meta_fields = [
            StructField(
                "knowledge_begin_date",
                TimestampType(timezone=TimestampTimeZone.TZ),
                nullable=False,
            ),
            StructField(
                "knowledge_end_date",
                TimestampType(timezone=TimestampTimeZone.TZ),
                nullable=True,
            ),
            StructField(
                "row_key",
                StringType(),
                nullable=True,
            ),
            StructField(
                "updated_on",
                TimestampType(timezone=TimestampTimeZone.TZ),
                nullable=False,
            ),
            StructField("is_deleted", BooleanType(), nullable=False),
        ]
        if data_fields is None:  # self.is_flattened is False
            data_fields = [StructField("data", VariantType(), nullable=False)]
        schema = StructType([*meta_fields, *data_fields])
        logger.info(f"Field names: {schema.names}")
        return schema

    def create_upstream_table_if_not_exists(self):
        df = self.snowpark_session.create_dataframe(data=[], schema=self.schema)
        df.write.save_as_table(
            self.table_name, mode="append", column_order="name"
        )  # type: ignore

    def get_temp_table_schema(
        self, data_fields: list[StructField] | None = None
    ) -> StructType:
        if data_fields is None:  # self.is_flattened is False
            data_fields = [StructField("data", VariantType(), nullable=False)]
        schema = StructType([*data_fields])
        logger.info(f"Field names: {schema.names}")
        return schema

    def set_meta(
        self,
        updated_on: datetime | None = None,
        is_deleted: bool | None = None,
    ) -> Self:
        self.meta = {
            "knowledge_begin_date": now(),
            "knowledge_end_date": None,
            "updated_on": now() if updated_on is None else updated_on,
            "is_deleted": False if is_deleted is None else is_deleted,
        }
        logger.info(f"Updated on: {self.meta['updated_on']}")
        return self

    def serialize_data(self, data: dict) -> dict:
        return json.loads(json.dumps(data, default=str))

    def save_in_temp_table(
        self, records: list | None = None, operation: str = ""
    ) -> None:
        target_table = (
            f"{self.temp_data_table_name}_{operation}s"
            if operation
            in [UpstreamOperation.DELETE.value, UpstreamOperation.OVERWRITE.value]
            else self.temp_data_table_name
        )
        if self.is_flattened:
            data = [self.serialize_data(record) for record in (records or [])]
        else:
            data = [{"data": self.serialize_data(record)} for record in (records or [])]

        logger.info(f"Inserting {len(data)} records to {target_table}")

        data_frame = self.snowpark_session.create_dataframe(
            data=data, schema=self.get_temp_table_schema()
        )
        data_frame.write.save_as_table(
            table_name=target_table,
            table_type="temporary",
            mode="append",
            column_order="name",
        )  # type: ignore

        self.connection.commit()

    def save(self, data_with_row_key_table_name: str | None = None) -> None:
        current_timestamp = timezone.now()
        timestamps = get_upstream_timestamps(self.client_id, self.integration_id)
        last_updated_on = timestamps["upstream_source_synced_till"]
        requires_row_key_table_creation = data_with_row_key_table_name is None
        if requires_row_key_table_creation:
            data_with_row_key_table_name = f"{self.temp_data_table_name}_with_row_key"

        extraction_record = ExtractionConfigAccessor(
            self.client_id
        ).get_object_for_integration_id(self.integration_id)
        sync_mode = None
        if extraction_record is not None:
            sync_mode = extraction_record.sync_type

        try:
            if requires_row_key_table_creation:
                if not check_table_if_exists(
                    self.temp_data_table_name, snowflake_session=self.snowpark_session
                ):
                    return
                data_with_row_key_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {data_with_row_key_table_name} AS
                (
                    SELECT data, {construct_upstream_key_expression(self.source_primary_keys)} AS row_key
                    FROM {self.temp_data_table_name}
                )
                """  # noqa: S608
                self.snowpark_session.sql(data_with_row_key_query).collect()

            if sync_mode in (
                UpstreamSyncModes.CHANGES.value,
                UpstreamSyncModes.SNAPSHOT.value,
            ):
                invalidate_upstream_src_changes_query = f"""
                UPDATE {self.table_name}
                SET knowledge_end_date = '{current_timestamp}':: TIMESTAMP_TZ
                WHERE CONVERT_TIMEZONE('UTC', updated_on)::datetime > CONVERT_TIMEZONE('UTC', 
                    '{last_updated_on}')::datetime
                AND row_key IN (SELECT row_key FROM {data_with_row_key_table_name})
                """  # noqa: S608
                self.snowpark_session.sql(
                    invalidate_upstream_src_changes_query
                ).collect()

            if sync_mode == UpstreamSyncModes.ALL.value:
                invalidate_upstream_src_changes_query = f"""
                UPDATE {self.table_name}
                SET knowledge_end_date = '{current_timestamp}':: TIMESTAMP_TZ
                WHERE CONVERT_TIMEZONE('UTC', updated_on)::datetime > CONVERT_TIMEZONE('UTC', 
                    '{last_updated_on}')::datetime
                """  # noqa: S608
                self.snowpark_session.sql(
                    invalidate_upstream_src_changes_query
                ).collect()

            insert_to_upstream_table_query = f"""
            INSERT INTO {self.table_name}(
                knowledge_begin_date,
                knowledge_end_date,
                updated_on,
                is_deleted,
                row_key,
                data
            )
            SELECT  
                '{self.meta.get("knowledge_begin_date")}'::TIMESTAMP_TZ as knowledge_begin_date,
                NULL as knowledge_end_date,
                '{self.meta.get("updated_on")}'::TIMESTAMP_TZ as updated_on,
                {self.meta.get("is_deleted")} as is_deleted,
                row_key,
                data
            FROM {data_with_row_key_table_name}
            """  # noqa: S608
            self.snowpark_session.sql(insert_to_upstream_table_query).collect()
        except Exception as e:
            logger.exception(
                f"Rolling back: Error in saving to upstream table: {self.table_name}"
            )
            self.connection.rollback()
            raise e from e
        else:
            self.connection.commit()

    def overwrite(self, data_with_row_key_table_name: str | None = None) -> None:
        overwrites_source_table_name = (
            f"{self.temp_data_table_name}_{UpstreamOperation.OVERWRITE.value}s"
        )
        requires_row_key_table_creation = data_with_row_key_table_name is None
        if requires_row_key_table_creation:
            data_with_row_key_table_name = (
                f"{overwrites_source_table_name}_with_row_key"
            )

        try:
            if requires_row_key_table_creation:
                if not check_table_if_exists(
                    overwrites_source_table_name,
                    snowflake_session=self.snowpark_session,
                ):
                    return

                data_with_row_key_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {data_with_row_key_table_name} AS
                (
                    SELECT data, {construct_upstream_key_expression(self.source_primary_keys)} AS row_key
                    FROM {overwrites_source_table_name}
                )
                """  # noqa: S608
                self.snowpark_session.sql(data_with_row_key_query).collect()

            overwrite_upstream_query = f"""
            UPDATE {self.table_name} t1
            SET t1.data=t2.data,
            t1.updated_on='{self.meta.get("updated_on")}'::TIMESTAMP_TZ
            FROM {data_with_row_key_table_name} t2
            WHERE t1.row_key = t2.row_key
            AND t1.knowledge_end_date IS NULL;
            """  # noqa: S608
            self.snowpark_session.sql(overwrite_upstream_query).collect()
        except Exception as e:
            logger.exception(
                f"Rolling back: Error in overwriting to upstream table: {self.table_name}"
            )
            self.connection.rollback()
            raise e from e
        else:
            self.connection.commit()

    def delete(self, data_with_row_key_table_name: str | None = None) -> None:
        deletes_source_table_name = (
            f"{self.temp_data_table_name}_{UpstreamOperation.DELETE.value}s"
        )
        requires_row_key_table_creation = data_with_row_key_table_name is None
        if requires_row_key_table_creation:
            data_with_row_key_table_name = f"{deletes_source_table_name}_with_row_key"

        try:
            if requires_row_key_table_creation:
                if not check_table_if_exists(
                    deletes_source_table_name, snowflake_session=self.snowpark_session
                ):
                    return
                data_with_row_key_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {data_with_row_key_table_name} AS
                (
                    SELECT data, {construct_upstream_key_expression(self.source_primary_keys)} AS row_key
                    FROM {deletes_source_table_name}
                )
                """  # noqa: S608
                self.snowpark_session.sql(data_with_row_key_query).collect()

            delete_upstream_query = f"""
            UPDATE {self.table_name} t1
            SET t1.updated_on='{self.meta.get("updated_on")}'::TIMESTAMP_TZ, 
            t1.is_deleted=TRUE
            FROM {data_with_row_key_table_name} t2
            WHERE t1.row_key = t2.row_key
            AND t1.knowledge_end_date IS NULL;
            """  # noqa: S608
            self.snowpark_session.sql(delete_upstream_query).collect()
        except Exception as e:
            logger.exception(
                f"Rolling back: Error in deletion in upstream table: {self.table_name}"
            )
            self.connection.rollback()
            raise e from e
        else:
            self.connection.commit()


class UpstreamFileWriter:
    """
    Interface for writing CSV chunks to Snowflake table in batches
    and handling row integrity across chunks from multiple files.
    """

    S3_BACKUP_STORED_PROCEDURE = "export_table_files"

    def __init__(  # noqa
        self,
        client_id: int,
        integration_id: UUID,
        is_validation: bool = False,  # noqa
        **kwargs,
    ) -> None:
        self.is_validation = is_validation
        self.client_id = client_id
        self.integration_id = integration_id
        self.sync_run_id = kwargs.get("sync_run_id", uuid4())
        self.temp_data_table_name: str = (
            f"temp_data_{uuid_in_snakecase(self.sync_run_id)}"
        )
        self.staging_table_name: str = get_upstream_staging_table_name(
            self.client_id, self.sync_run_id
        )
        self.source_primary_keys, self.source_snapshot_keys = get_source_key_fields(
            self.client_id, self.integration_id
        )

        self.upstream_writer = UpstreamChangesWriter(
            self.client_id,
            self.integration_id,
            is_validation=self.is_validation,
        )
        self.connection = self.upstream_writer.connection
        self.snowpark_session = self.upstream_writer.snowpark_session

    def set_meta(
        self,
        file_name: str,
        file_mtime: datetime,
        **kwargs,
    ) -> Self:
        """
        Sets metadata for the records to be inserted in the save() step.
        """
        self.middleware_meta = {}
        self.meta = {
            "file_name": file_name,
            "file_mtime": file_mtime.isoformat(),
            "delete_field": kwargs.get("delete_field"),
            "delete_value": kwargs.get("delete_value", "true"),
        }
        logger.info(
            f"Metadata set - File name: {self.meta['file_name']}, File mtime: {self.meta['file_mtime']}"
        )
        return self

    def set_middleware_meta(
        self,
        mtime_column: str,
        fname_column: str,
        is_deleted: bool | None = None,
    ) -> Self:
        """
        Sets metadata for the records to be inserted in the save() step.
        """
        self.meta = {}
        self.middleware_meta = {
            "mtime_column": mtime_column,
            "fname_column": fname_column,
            "is_deleted": False if is_deleted is None else is_deleted,
        }
        logger.info(
            f"Middleware meta set: File name column: {self.middleware_meta['fname_column']}, File mtime column: {self.middleware_meta['mtime_column']}, is_deleted: {self.middleware_meta['is_deleted']}"
        )
        return self

    def _stage_file_backup(self, stage_backup: bool = False) -> None:  # noqa
        """
        Stage file backup to S3 Bucket.
        """
        if self.is_validation:
            return
        # Backup ingested files to upstream_backup/client_id/sync_run_id/
        try:
            logger.info("[BEGIN] Staging file backup to S3")
            start_time = time()
            if stage_backup and getenv(
                "ENV", ENVIRONMENT["LOCAL"]
            ) in PRODUCTION_ENVIRONMENT + [ENVIRONMENT["QA"]]:
                self.snowpark_session.call(
                    self.S3_BACKUP_STORED_PROCEDURE,
                    str(self.client_id),
                    self.temp_data_table_name,
                    str(self.sync_run_id),
                )
            end_time = time()
            logger.info(
                f"[END] Staging file backup to S3 completed in {end_time - start_time} seconds"
            )
        except Exception:
            logger.exception("Error staging file backup to S3")

    def _get_changed_records(self, staging_table_name: str) -> str:
        """
        Find records to be updated based on the delete column value.
        """
        if not self.meta["delete_field"]:
            return staging_table_name
        changed_records_table_name = f"{staging_table_name}_changes"
        changed_records_query = f"""
        CREATE OR REPLACE TEMPORARY TABLE {changed_records_table_name} AS
        (
            SELECT data,row_key FROM {staging_table_name}
            WHERE COALESCE({get_source_column_prefix(self.meta["delete_field"])}, '') not ilike '{self.meta["delete_value"]}'
        )
        """  # noqa: S608
        self.snowpark_session.sql(changed_records_query).collect()
        return changed_records_table_name

    def _get_deleted_records(self, staging_table_name: str) -> str | None:
        """
        Find records to be deleted based on the delete column value.
        """
        if not self.meta["delete_field"]:
            return None
        deleted_records_table_name = f"{staging_table_name}_deletes"
        deleted_records_query = f"""
        CREATE OR REPLACE TEMPORARY TABLE {deleted_records_table_name} AS
        (
            SELECT data,row_key FROM {staging_table_name}
            WHERE COALESCE({get_source_column_prefix(self.meta["delete_field"])}, '') ilike '{self.meta["delete_value"]}'
        )
        """  # noqa: S608
        self.snowpark_session.sql(deleted_records_query).collect()
        return deleted_records_table_name

    def _write_to_upstream_table(self, staging_table_name: str) -> None:
        """
        Write changed and deleted records to the upstream table.
        """
        # --- Middleware File Integration ---
        if self.middleware_meta:
            if not self.middleware_meta["is_deleted"]:
                self.upstream_writer.set_meta(is_deleted=False)
                self.upstream_writer.save(staging_table_name)
            else:
                self.upstream_writer.set_meta(is_deleted=True)
                self.upstream_writer.save(staging_table_name)
        # --- Native File Integration ---
        else:
            # Find changed and deleted records based on delete column value
            changed_records_table_name = self._get_changed_records(staging_table_name)
            deleted_records_table_name = self._get_deleted_records(staging_table_name)
            # Save changed records
            self.upstream_writer.set_meta(is_deleted=False)
            self.upstream_writer.save(changed_records_table_name)
            # Save deleted records
            if deleted_records_table_name:
                self.upstream_writer.set_meta(is_deleted=True)
                self.upstream_writer.save(deleted_records_table_name)

    def get_temp_table_schema(self) -> StructType:
        return StructType(
            [
                StructField("data", VariantType(), nullable=False),
                StructField(
                    "file_mtime",
                    TimestampType(timezone=TimestampTimeZone.TZ),
                    nullable=False,
                ),
                StructField("file_name", StringType()),
            ]
        )

    def save_in_temp_table(self, records: DataFrame | list[dict]) -> None:
        """
        Save file record chunks to a temporary table in "data" column (VARIANT)
        with file modification time and file name metadata for each record.
        """
        # --- Handle native integrations ---
        # CSV File records fetched in chunks with pandas read_csv()
        # Requires set_meta() to be called before save_in_temp_table()
        if isinstance(records, DataFrame):
            # Snowflake will automatically try to infer data type from pandas dataframe and typecast
            # Explicitly set schema as string to prevent typecasting the raw data as it will be handled in transformation step
            chunk_schema = [StructField(col, StringType()) for col in records.columns]
            meta_schema = [
                StructField("file_mtime", TimestampType(timezone=TimestampTimeZone.TZ)),
                StructField("file_name", StringType()),
            ]
            flat_schema = StructType(chunk_schema + meta_schema)

            # Using uppercase to store as case invariant column
            records["FILE_MTIME"] = self.meta["file_mtime"]
            records["FILE_NAME"] = self.meta["file_name"]

            logger.info(
                f"Inserting {len(records)} records to {self.temp_data_table_name}"
            )
            flat_df = self.snowpark_session.create_dataframe(
                records, schema=flat_schema
            )
            variant_df = flat_df.select(
                sql_expr("OBJECT_CONSTRUCT_KEEP_NULL(*)")
                .astype(VariantType())
                .alias("data"),
                cast("file_mtime", TimestampType(timezone=TimestampTimeZone.TZ)).alias(
                    "file_mtime"
                ),
                "file_name",
            )
            variant_df.write.save_as_table(
                table_name=self.temp_data_table_name,
                table_type="temporary",
                mode="append",
                column_order="name",
            )  # type: ignore

        # --- Handle middleware integrations ---
        # JSON records fetched in batches from SQL integration with middleware
        # Requires set_middleware_meta() to be called before save_in_temp_table()
        else:
            data = [
                {
                    "data": self.upstream_writer.serialize_data(record),
                    "file_mtime": datetime.fromtimestamp(
                        int(record[self.middleware_meta["mtime_column"]]) / 1000,
                        tz=timezone.utc,
                    ),
                    "file_name": record[self.middleware_meta["fname_column"]],
                }
                for record in (records or [])
            ]
            logger.info(f"Inserting {len(data)} records to {self.temp_data_table_name}")
            variant_df = self.snowpark_session.create_dataframe(
                data, schema=self.get_temp_table_schema()
            )
            variant_df.write.save_as_table(
                table_name=self.temp_data_table_name,
                table_type="temporary",
                mode="append",
                column_order="name",
            )  # type: ignore

        self.connection.commit()

    def save(self, **kwargs) -> None:
        """
        Process the file records and deduplicate records from multiple files
        based on the sync mode and write to the upstream table.
        """
        extraction_record = ExtractionConfigAccessor(
            self.client_id
        ).get_object_for_integration_id(self.integration_id)
        sync_mode = None
        if extraction_record is not None:
            sync_mode = extraction_record.sync_type

        try:
            if not check_table_if_exists(
                self.temp_data_table_name, snowflake_session=self.snowpark_session
            ):
                logger.warning(
                    f"Temporary data table {self.temp_data_table_name} not found. Skipping save."
                )
                return

            self._stage_file_backup(kwargs.get("stage_backup", False))

            # --- Handle Snapshot Sync Mode ---
            # Step 1: Find latest file based on file_mtime for each snapshot value.
            # Step 2: Select all records from the latest file found in step 1.
            # This ensures that we ingest only the latest snapshot uploaded for each snapshot value.
            if sync_mode == UpstreamSyncModes.SNAPSHOT.value:
                table_name_with_snapshot_value = (
                    f"{self.temp_data_table_name}_with_snapshot_value"
                )

                # Create a temporary table extracting row_key and snapshot_value from source data.
                data_with_snapshot_value_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {table_name_with_snapshot_value} AS
                SELECT
                    {construct_upstream_key_expression(self.source_primary_keys)} AS row_key,
                    {construct_upstream_key_expression(self.source_snapshot_keys)} AS snapshot_value,
                    data,
                    file_mtime
                FROM
                    {self.temp_data_table_name};
                """  # noqa: S608
                logger.info(
                    f"Creating temporary table with snapshot values: {table_name_with_snapshot_value}"
                )
                self.snowpark_session.sql(data_with_snapshot_value_query).collect()

                # Deduplicate into the staging table based on the latest file_mtime for each snapshot_value.
                deduplicate_snapshot_records_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {self.staging_table_name} AS
                (
                WITH latest_snapshot_times AS (
                    SELECT
                        snapshot_value,
                        MAX(file_mtime) as max_file_mtime
                    FROM
                        {table_name_with_snapshot_value}
                    GROUP BY
                        snapshot_value
                )
                SELECT
                    t1.row_key,
                    t1.data
                FROM
                    {table_name_with_snapshot_value} t1
                INNER JOIN
                    latest_snapshot_times t2 ON t1.snapshot_value = t2.snapshot_value AND t1.file_mtime = t2.max_file_mtime
                )
                """  # noqa: S608
                logger.info(
                    f"Staging deduplicated table with latest snapshot file data to {self.staging_table_name}"
                )
                self.snowpark_session.sql(deduplicate_snapshot_records_query).collect()

            # --- Handle Changes/All Sync Modes ---
            # Select the latest version of a row_key based on the file modification time.
            elif sync_mode in (
                UpstreamSyncModes.CHANGES.value,
                UpstreamSyncModes.ALL.value,
            ):
                table_name_with_row_key = f"{self.temp_data_table_name}_with_row_key"

                # Create a temporary table with extracted row_key and file_mtime.
                data_with_row_key_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {table_name_with_row_key} AS
                (
                    SELECT
                        data,
                        {construct_upstream_key_expression(self.source_primary_keys)} AS row_key,
                        file_mtime
                    FROM {self.temp_data_table_name}
                )
                """  # noqa: S608
                logger.info(
                    f"Creating temporary table with row keys: {table_name_with_row_key}"
                )
                self.snowpark_session.sql(data_with_row_key_query).collect()

                # Deduplicate into the staging table based on the latest file_mtime for each row_key.
                deduplicate_file_records_query = f"""
                CREATE OR REPLACE TEMPORARY TABLE {self.staging_table_name} AS
                (
                WITH latest_row_key_times AS (
                    SELECT
                        row_key,
                        MAX(file_mtime) as max_file_mtime
                    FROM
                        {table_name_with_row_key}
                    GROUP BY
                        row_key
                )
                SELECT 
                    t1.row_key,
                    t1.data
                FROM {table_name_with_row_key} t1
                INNER JOIN latest_row_key_times t2 ON t1.row_key = t2.row_key AND t1.file_mtime = t2.max_file_mtime
                )
                """  # noqa: S608
                logger.info(
                    f"Staging deduplicated file records with latest row_key data by file_mtime to {self.staging_table_name}"
                )
                self.snowpark_session.sql(deduplicate_file_records_query).collect()

        except Exception as e:
            logger.exception(
                f"Rolling back: Error in saving to upstream staging table: {self.staging_table_name}"
            )
            self.connection.rollback()
            raise e from e
        else:
            logger.info(
                f"Successfully prepared staging table: {self.staging_table_name}"
            )
            self.connection.commit()

            logger.info("[BEGIN] Insert data into upstream table")
            self._write_to_upstream_table(self.staging_table_name)
            logger.info("[END] Insert data into upstream table")
