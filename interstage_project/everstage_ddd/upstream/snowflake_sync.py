import logging
from datetime import datetime
from typing import List, Optional

from django.utils import timezone
from snowflake.snowpark import Session

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import TransformationConfigAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.database.snowflake_query_utils import snowflake_writer_handler
from commission_engine.utils.databook_utils import resolve_snowflake_data_type
from commission_engine.utils.general_data import (
    CustomObjectDataSource,
    UpstreamSyncModes,
)
from commission_engine.utils.general_utils import get_custom_object_data_table_name
from interstage_project.global_utils.general_utils import uuid_in_snakecase
from interstage_project.threadlocal_log_context import set_threadlocal_context

from .exceptions import TableNotExistError
from .extraction.utils import set_upstream_timestamps
from .schema import UpstreamSyncParams

logger = logging.getLogger(__name__)


class UpstreamSnowflakeSync:
    """
    Upstream sync to handle the transform and load through Snowflake
    """

    def __init__(self, args: UpstreamSyncParams):
        self.client_id = args.client_id
        self.e2e_sync_run_id = args.e2e_sync_run_id
        self.sync_run_id = args.sync_run_id
        self.source_data_table = args.source_data_table
        self.custom_object_id = args.custom_object_id
        self.integration_id = args.integration_id
        self.sync_mode = args.sync_mode
        self.changes_sync_time = args.changes_sync_time
        self.is_source_data_as_variant = args.is_source_data_as_variant
        self.transformation_logic = args.transformation_logic
        self.custom_object_data_table = get_custom_object_data_table_name(
            client_id=self.client_id, custom_object_id=self.custom_object_id
        )
        self.validate_source_table()
        self.current_time = timezone.now()
        self.primary_kd = (
            args.primary_kd if args.primary_kd is not None else self.current_time
        )
        self.current_extracted_data_table = f"current_extracted_data_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}"
        self.transformed_data_table = (
            f"transformed_data_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}"
        )
        self.deleted_data_table = (
            f"deleted_data_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}"
        )
        custom_object_variables = CustomObjectVariableAccessor(
            client_id=self.client_id
        ).get_custom_variables_by_id(
            custom_object_id=self.custom_object_id,
            projections=["system_name", "data_type__data_type"],
        )
        self.data_type_for_co_variable = {
            variable["system_name"]: variable["data_type__data_type"]
            for variable in custom_object_variables
        }
        custom_object: List[dict] = CustomObjectAccessor(
            self.client_id
        ).get_object_fields_by_id(
            custom_object_id=self.custom_object_id,
            projections=["primary_key", "snapshot_key"],
        )
        self.co_primary_keys = custom_object[0]["primary_key"] if custom_object else []
        self.co_snapshot_keys = (
            custom_object[0]["snapshot_key"] if custom_object else []
        )
        self.query_tag = {
            "client_id": self.client_id,
            "sync_run_id": str(self.sync_run_id),
        }
        set_threadlocal_context(
            {
                "deleted_data_table": self.deleted_data_table,
                "changes_data_table": self.transformed_data_table,
                "is_source_data_as_variant": self.is_source_data_as_variant,
            }
        )

    def _prefix(self, column):
        if self.is_source_data_as_variant:
            return "data:" + column
        return column

    def validate_source_table(self):
        table_exists = True
        if "." in self.source_data_table:
            schema_name, table_name = self.source_data_table.split(".")
            schema_name = schema_name.strip('"')
            table_name = table_name.strip('"')
        else:
            schema_name = "PUBLIC"
            table_name = self.source_data_table.strip('"')

        self.source_data_table = f"{schema_name}.{table_name}"

        with create_snowpark_session_wrapper(
            client_id=self.client_id
        ) as snowpark_session:
            check_table_query = f"""
            SELECT EXISTS (
                SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = '{schema_name.upper()}' AND 
                TABLE_NAME = '{table_name.upper()}'
            ) AS table_exists
            """  # noqa: S608
            logger.info(f"Validate table query {check_table_query}")
            result: list = snowpark_session.sql(check_table_query).collect()
            table_exists = result[0]["TABLE_EXISTS"]

        if not table_exists:
            raise TableNotExistError(self.source_data_table)

    def _get_custom_date_format_for_destination_field(
        self, source_field: str, date_format: Optional[str] = None
    ):
        if date_format:
            return f"""TO_VARCHAR(TO_TIMESTAMP(CAST({source_field} AS STRING), '{date_format}'), 'YYYY-MM-DD"T"HH24:MI:SS"Z"')"""
        return f"""TO_VARCHAR(TO_TIMESTAMP(CAST({source_field} AS STRING)), 'YYYY-MM-DD"T"HH24:MI:SS"Z"')"""

    def _get_custom_date_format_for_source_field(
        self, source_field: str, date_formats: List[str]
    ):
        """to handle date formats not implicitly handled by snowflake"""

        to_datetime_default = f"""
        WHEN TRY_TO_TIMESTAMP(CAST({self._prefix(source_field)} AS STRING)) IS NOT NULL THEN
            {self._get_custom_date_format_for_destination_field(self._prefix(source_field))}
        """
        to_datetime_with_formats = "\n".join(
            [
                f"""
                WHEN TRY_TO_TIMESTAMP(CAST({self._prefix(source_field)} AS STRING), '{date_format}') IS NOT NULL THEN
                    {self._get_custom_date_format_for_destination_field(self._prefix(source_field),date_format)}
                """
                for date_format in date_formats
            ]
        )
        custom_type_cast = f"""
                            CASE
                                {to_datetime_with_formats}
                                {to_datetime_default}
                                ELSE NULL
                            END
                    """
        return custom_type_cast

    def rename_and_type_cast_source_data(
        self,
        snowpark_session: Session,
        extracted_data_table: str,
        transformation_configs: List[dict],
    ):
        """
        Renames and type casts the fields in extracted_data table to custom_object_variables
        Note: the audit fields should be present in source_table as is_deleted, updated_on
        """
        logger.info("RENAME_AND_TYPE_CAST_SOURCE_DATA: Begin..")

        changes_filters = ""
        renamed_and_type_casted_fields = []

        pre_transform_temp_table = (
            f"pre_transform_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}"
        )
        co_variable_for_source_variable = {
            config["destination_field"]: config["source_field"]
            for config in transformation_configs
        }
        source_var_for_co_var = {
            config["destination_field"]: config["source_field"]
            for config in transformation_configs
        }
        date_format_config = {
            config["source_field"]: (config.get("additional_config") or {}).get(
                "date_format", []
            )
            for config in transformation_configs
        }
        for target_col, source_col in co_variable_for_source_variable.items():
            data_type = self.data_type_for_co_variable[target_col].upper()
            source_field_date_formats = date_format_config.get(source_col, [])

            if data_type == "DATE" and len(source_field_date_formats) > 0:
                type_casted_field = self._get_custom_date_format_for_source_field(
                    source_col, source_field_date_formats
                )
            elif data_type == "DATE":
                type_casted_field = self._get_custom_date_format_for_destination_field(
                    self._prefix(source_col)
                )
            elif data_type == "EMAIL":
                type_casted_field = f"""
                        LOWER(CAST({self._prefix(source_col)} AS STRING))
                    """
            else:
                type_casted_field = f"""
                        CAST({self._prefix(source_col)} AS {
                            resolve_snowflake_data_type(data_type)
                            })
                        """
            renamed_and_type_casted_fields.append(
                f"""
                {type_casted_field} AS {target_col}
                """
            )

        source_fields_select = ",".join(renamed_and_type_casted_fields)
        row_key_expr = " || '#:::#' || ".join(
            [
                f"COALESCE(TO_VARCHAR({self._prefix(source_var_for_co_var[key])}), 'none')"
                for key in self.co_primary_keys
            ]
        )
        snapshot_val_expr = " || '#:::#' || ".join(
            [
                f"COALESCE(TO_VARCHAR({self._prefix(source_var_for_co_var[key])}), 'none')"
                for key in self.co_snapshot_keys
            ]
        )

        query = f"""
        CREATE OR REPLACE TABLE {pre_transform_temp_table} AS
        (
            SELECT {source_fields_select}, 
            LOWER(CONCAT({row_key_expr})) AS row_key,
            LOWER(CONCAT({snapshot_val_expr})) AS snapshot_value,
            is_deleted,
            FROM {extracted_data_table}
            {changes_filters}
        )
        """  # noqa: S608
        snowpark_session.sql(query).collect()
        self.print_count(
            table_name=pre_transform_temp_table,
            snowpark_session=snowpark_session,
            keyword="EXTRACTED_RECORD_COUNT",
        )

        logger.info("RENAME_AND_TYPE_CAST_SOURCE_DATA: End.")
        return pre_transform_temp_table

    def apply_transformation_logic(
        self,
        extracted_data_table: str,
        data_column: str,
        snowpark_session: Session,
    ):
        """Applies the UDTF defined in snowflake to the source data in self.source_data_table"""
        logger.info("APPLY_TRANSFORMATION_LOGIC: Begin..")

        pre_transform_with_logic_table = f"pre_transform_with_logic_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}"
        result_select_fields = [
            "is_deleted",
            "converted.data",
            "updated_on",
        ]  # make sure the udtf yields the 'data' field
        result_selection_expr = ",".join(result_select_fields)
        transformation_logic_query = f"""
        CREATE OR REPLACE TABLE {pre_transform_with_logic_table} AS
        SELECT 
            {result_selection_expr}
        FROM {extracted_data_table},
        TABLE({self.transformation_logic}({data_column})) as converted
        """  # noqa: S608
        snowpark_session.sql(transformation_logic_query).collect()
        logger.info("APPLY_TRANSFORMATION_LOGIC: End.")
        return pre_transform_with_logic_table

    def print_count(self, table_name, snowpark_session, keyword="RECORDS_COUNT"):
        count_query = f""" 
        SELECT COUNT(*) AS row_count FROM {table_name} """  # noqa: S608
        result = snowpark_session.sql(count_query).collect()
        row = result[0]
        row_as_dict = row.as_dict()
        count = row_as_dict.get("ROW_COUNT", -1)
        logger.info(f"{keyword}: {count}")

    def merge_active_data(
        self,
        pre_transform_temp_table: str,
        transformation_configs: List[dict],
        snowpark_session: Session,
    ):
        """
            Merges the extracted data with existing custom object data.

        This function creates a new table by merging the preprocessed extracted data
        with the existing custom object data. The fields are divided into two categories:
        - Integrated fields: These are fields that are directly mapped from the source data
          to the custom object variables.
        - Manually managed fields: These are fields that are not part of the integration
          and are managed manually.

        The function performs the following steps for a record:
        1. Selects the integrated fields from the preprocessed extracted data.
        2. Selects the manually managed fields from the existing custom object data.
        3. Joins the preprocessed extracted data with the existing custom object data
           on the row key.
        4. Creates a new table with the merged data.
        """
        logger.info("MERGE_ACTIVE_DATA: Begin merging with existing records..")
        integration_fields = [
            config["destination_field"] for config in transformation_configs
        ]

        all_custom_variables = self.data_type_for_co_variable.keys()
        manually_managed_fields = [
            var for var in all_custom_variables if var not in integration_fields
        ]

        manually_managed_fields_select_list = []
        for field in manually_managed_fields:
            data_type = self.data_type_for_co_variable[field].upper()
            cast_and_alias_field = f"""
                CAST(co.data:{field} AS {resolve_snowflake_data_type(
                    data_type
                    )}) AS {field}
            """
            manually_managed_fields_select_list.append(cast_and_alias_field)

        manually_managed_fields_select = ", ".join(manually_managed_fields_select_list)

        integration_fields_select = ", ".join(
            [f"pre.{field}" for field in integration_fields]
        )

        custom_object_temp_table = f"co_temp_table_{self.client_id}_{uuid_in_snakecase(self.sync_run_id)}_{self.custom_object_id}"

        existing_custom_objects_query = f"""
        CREATE OR REPLACE TABLE {custom_object_temp_table} AS
        (
            SELECT row_key, data FROM {self.custom_object_data_table}
            WHERE client_id={self.client_id} and 
                custom_object_id={self.custom_object_id} and 
                not is_deleted and 
                knowledge_end_date is null and
                row_key IN (
                    SELECT row_key FROM {pre_transform_temp_table} as pre
                    WHERE pre.is_deleted = FALSE
                )
        )
        """  # noqa: S608

        snowpark_session.sql(existing_custom_objects_query).collect()
        self.print_count(
            custom_object_temp_table,
            snowpark_session,
            keyword="MATCHING_EXISTING_RECORD_COUNT",
        )

        create_result_table_clause = f"""
        CREATE OR REPLACE TABLE {self.transformed_data_table} AS
        (
            SELECT pre.row_key, pre.snapshot_value, 
                {integration_fields_select}, 
                {manually_managed_fields_select}
            FROM {pre_transform_temp_table} pre
            LEFT JOIN {custom_object_temp_table} co
            ON pre.row_key = co.row_key
            WHERE pre.is_deleted = FALSE
        )
        """  # noqa: S608
        snowpark_session.sql(create_result_table_clause).collect()
        self.print_count(
            self.transformed_data_table,
            snowpark_session,
            keyword="TRANSFORMED_RECORD_COUNT",
        )

        logger.info("MERGE_ACTIVE_DATA: End.")

    def invalidate_and_mark_delete(
        self, ked: datetime, key_col: str, snowpark_session: Session
    ):
        logger.info("INVALIDATE_AND_MARK_DELETE: Begin..")
        snowflake_date_type = resolve_snowflake_data_type("DATE")
        invalidate_query = f"""
            UPDATE {self.custom_object_data_table} SET 
                knowledge_end_date = '{ked}'::{snowflake_date_type}
            WHERE client_id={self.client_id} and 
                custom_object_id={self.custom_object_id} and 
                not is_deleted and 
                knowledge_end_date is null and 
                {key_col} IN ( 
                    SELECT {key_col} FROM {self.transformed_data_table} 
                )
        """  # noqa: S608
        snowpark_session.sql(invalidate_query).collect()

        mark_delete_query = f"""
            UPDATE {self.custom_object_data_table} 
            SET knowledge_end_date = '{ked}'::{snowflake_date_type},
                is_deleted = TRUE
            WHERE client_id={self.client_id} and 
                custom_object_id={self.custom_object_id} and 
                not is_deleted and 
                knowledge_end_date is null and 
                row_key IN ( 
                    SELECT row_key FROM {self.deleted_data_table} 
                )
        """  # noqa: S608
        snowpark_session.sql(mark_delete_query).collect()
        logger.info("INVALIDATE_AND_MARK_DELETE: End.")

    def insert_data(self, knowledge_date: datetime, snowpark_session: Session):
        """inserts the transformed data into custom_object_data"""

        logger.info("INSERT_DATA: Begin..")
        all_custom_object_variables = self.data_type_for_co_variable.keys()
        snowflake_date_type = resolve_snowflake_data_type("DATE")
        insert_query = f"""
        INSERT INTO {self.custom_object_data_table} (
            KNOWLEDGE_BEGIN_DATE,
            KNOWLEDGE_END_DATE,
            IS_DELETED,
            ADDITIONAL_DETAILS,
            CUSTOM_OBJECT_ID,
            ROW_KEY,
            DATA,
            SNAPSHOT_VALUE,
            CLIENT_ID,
            SOURCE
        )
        SELECT 
            '{knowledge_date}'::{snowflake_date_type} AS KNOWLEDGE_BEGIN_DATE,
            NULL AS KNOWLEDGE_END_DATE,
            FALSE AS IS_DELETED,
            NULL AS ADDITIONAL_DETAILS,
            {self.custom_object_id} AS CUSTOM_OBJECT_ID,
            pre.row_key AS ROW_KEY,
            OBJECT_CONSTRUCT_KEEP_NULL(
                {', '.join([f"'{var}', pre.{var}" for var in all_custom_object_variables])}

            ) AS DATA,
            pre.snapshot_value AS SNAPSHOT_VALUE,
            {self.client_id} AS CLIENT_ID,
            '{CustomObjectDataSource.UPSTREAM.value}' AS SOURCE
        FROM {self.transformed_data_table} pre
        """  # noqa: S608
        snowpark_session.sql(insert_query).collect()
        logger.info("INSERT_DATA: End.")

    def collect_deleted_data(
        self, snowpark_session: Session, preprocess_temp_table: str
    ):
        """capture deletes in deleted_data_table"""
        logger.info("COLLECT_DELETED_DATA: Begin..")
        deleted_data_query = f"""
        CREATE OR REPLACE TABLE {self.deleted_data_table} AS
        (
            SELECT pre.row_key, pre.snapshot_value
            FROM {preprocess_temp_table} pre
            WHERE pre.is_deleted = TRUE 
        )
        """  # noqa: S608
        snowpark_session.sql(deleted_data_query).collect()
        self.print_count(
            table_name=self.deleted_data_table,
            snowpark_session=snowpark_session,
            keyword="SOURCE_DELETE_RECORD_COUNT",
        )
        logger.info(
            f"COLLECT_DELETED_DATA: End -  Deleted data inserted in {self.deleted_data_table}"
        )

    def transform(self):
        logger.info(f"TRANSFORM: Begin.. mode: {self.sync_mode}")
        transformation_configs = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_transformation_config_by_integration_id(
            integration_id=self.integration_id,
            projections=[
                "source_field",
                "destination_field",
                "transformation_logic_id",
                "additional_config",
            ],
        )
        with create_snowpark_session_wrapper(
            client_id=self.client_id, tag=self.query_tag
        ) as snowpark_session:
            # have to filter upstream_source_changes_()_() table for any sync mode (all, changes, snapshot)
            # as the table will have all the data synced so far, so always filter only the records
            # extracted in the current sync
            logger.info(
                f"FILTERS for sync: '{self.changes_sync_time}' < updated_on < '{self.current_time}'"
            )
            select_fields = ["updated_on", "is_deleted"]

            if self.is_source_data_as_variant:
                data_column = "data"
                select_fields.append("data")
            else:
                source_fields = [
                    config["source_field"] for config in transformation_configs
                ]
                select_fields.extend(source_fields)
                select_fields = list(set(select_fields))
                object_construct_fields = [
                    f"'{field}', {field}" for field in source_fields
                ]
                object_construct_expr = f"TO_VARIANT(OBJECT_CONSTRUCT_KEEP_NULL({', '.join(object_construct_fields)}))"
                data_column = object_construct_expr

            changes_filters = f"""
                WHERE CONVERT_TIMEZONE('UTC',updated_on)::datetime > CONVERT_TIMEZONE('UTC', '{self.changes_sync_time}')::datetime
                AND CONVERT_TIMEZONE('UTC',updated_on)::datetime < CONVERT_TIMEZONE('UTC', '{self.current_time}')::datetime
                AND knowledge_end_date is null
            """
            changes_data_query = f"""
                CREATE OR REPLACE TABLE {self.current_extracted_data_table} AS
                select {', '.join(select_fields)} from {self.source_data_table}
                {changes_filters}
            """  # noqa: S608
            logger.info(f"Changes data query: {changes_data_query}")
            snowpark_session.sql(changes_data_query).collect()
            extracted_data_table = self.current_extracted_data_table

            logger.info(
                f"TRANSFORM: current extracted data inserted in {self.current_extracted_data_table}"
            )
            if self.transformation_logic is not None:
                extracted_data_table = self.apply_transformation_logic(
                    snowpark_session=snowpark_session,
                    extracted_data_table=extracted_data_table,
                    data_column=data_column,
                )
                # setting it as variant as after applying transformation logic all the fields
                # will be in data variant col
                self.is_source_data_as_variant = True
            pre_transform_temp_table = self.rename_and_type_cast_source_data(
                snowpark_session=snowpark_session,
                extracted_data_table=extracted_data_table,
                transformation_configs=transformation_configs,
            )

            if self.sync_mode in (
                UpstreamSyncModes.CHANGES.value,
                UpstreamSyncModes.SNAPSHOT.value,
            ):
                self.collect_deleted_data(
                    snowpark_session=snowpark_session,
                    preprocess_temp_table=pre_transform_temp_table,
                )
            self.merge_active_data(
                pre_transform_temp_table=pre_transform_temp_table,
                transformation_configs=transformation_configs,
                snowpark_session=snowpark_session,
            )
        logger.info(
            f"TRANSFORM: End - Transformed data inserted in {self.transformed_data_table}"
        )

    def load(self):
        """
        Loads data to custom_object_data table
            if sync_mode is all
                invalidate all records in the custom object
            if sync_mode is changes
                invalidate by row key
            if sync_mode is override
                invalidate by snapshot_value
            insert new record from transformed_table
        """
        from everstage_etl.tasks.loading import (
            _invalidate_all_valid_custom_object_data_rows,
        )

        logger.info(f"LOAD: Begin loading to {self.custom_object_data_table}..")
        knowledge_date = self.primary_kd
        with snowflake_writer_handler(
            client_id=self.client_id, tag=self.query_tag
        ) as snowpark_session:
            if self.sync_mode == UpstreamSyncModes.ALL.value:
                logger.info(
                    f"Mode: {self.sync_mode} - Invalidating all existing records"
                )
                _invalidate_all_valid_custom_object_data_rows(
                    client_id=self.client_id,
                    object_id=self.custom_object_id,
                    table_name=self.custom_object_data_table,
                    snowpark_session=snowpark_session,
                    ked=knowledge_date,
                )
            if self.sync_mode == UpstreamSyncModes.CHANGES.value:
                logger.info(
                    f"Mode: {self.sync_mode} - Invalidating existing data by row_key"
                )
                self.invalidate_and_mark_delete(
                    ked=knowledge_date,
                    key_col="row_key",
                    snowpark_session=snowpark_session,
                )

            if self.sync_mode == UpstreamSyncModes.SNAPSHOT.value:
                logger.info(
                    f"Mode: {self.sync_mode} - Invalidating existing data by snapshot_value"
                )
                self.invalidate_and_mark_delete(
                    ked=knowledge_date,
                    key_col="snapshot_value",
                    snowpark_session=snowpark_session,
                )

            self.insert_data(
                knowledge_date=knowledge_date, snowpark_session=snowpark_session
            )
        logger.info("LOAD: End.")

        set_upstream_timestamps(
            self.client_id,
            self.integration_id,
            {"upstream_source_synced_till": self.current_time},
        )
