"""
This module contains hierarchy of classes that represents the data sources for
Full and incremental ETL.
"""

# pylint: disable-msg=abstract-method,logging-fstring-interpolation

import logging
import re
from abc import abstractmethod
from datetime import datetime
from typing import Union

from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    get_datasheet_meta_data,
)
from commission_engine.utils.databook_utils import (
    get_datasheet_data_table_name,
    get_datasheet_transformation_result_table_name,
    resolve_snowflake_data_type,
)
from commission_engine.utils.general_data import DATASHEET_SOURCE
from commission_engine.utils.general_utils import get_custom_object_data_table_name
from commission_engine.utils.report_utils import (
    get_primary_keys as get_report_primary_keys,
)
from commission_engine.utils.report_utils import (
    get_report_variables,
    get_table_for_object_id,
    resolve_report_object_table_name,
)
from everstage_etl.databook_etl.datasheet_etl_utils import (
    DataRowType,
    DataSouceType,
    convert_fetch_query_to_df,
    get_report_object_types,
)
from everstage_etl.databook_etl.lite_etl.exceptions import LiteEtlException
from everstage_etl.databook_etl.lite_etl.utils import get_report_object_flat_view_name
from everstage_etl.snapshot_service.co_snapshot_service import _get_co_latest_table_name
from spm.services.custom_object_services.custom_object_service import (
    get_custom_object_meta_data,
    get_primary_key,
)
from spm.services.databook_services import get_databook_id_by_datasheet_id

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class DataSource:
    """
    Abstract class that contains common methods
    """

    def __init__(
        self,
        client_id,
        source_type: Union[str, None],
        source_id: Union[str, int, None],
        databook_id: str,
        datasheet_id: str,
        last_generated_time: datetime,
        knowledge_date: datetime,
        source_databook_id: str,
    ):
        self._client_id = client_id
        self._source_type = source_type
        self._source_id = source_id
        # datasheet details for the which current object is a source
        self._databook_id = databook_id
        self._datasheet_id = datasheet_id
        self._last_generated_time = last_generated_time
        self._knowledge_date = knowledge_date
        self._source_databook_id = source_databook_id

        self._source_table = self.get_source_table_name()
        # stores the data type of each variable in the source
        self._var_dtype_map = self.construct_source_var_dtype_map()

    def get_variables_with_dtype(self) -> list[str]:
        """
        from the list of source variables, type cast each variable to its snowflake type
        and how it'd be used in a select clause
        example response :- ["col1::int as col1, col2::datetime as col2"]
        """
        columns_with_types = []
        for variable_name, data_type in self._var_dtype_map.items():
            snowflake_data_type = resolve_snowflake_data_type(data_type.upper())
            columns_with_types.append(
                f"{variable_name}::{snowflake_data_type} as {variable_name}"
            )

        return columns_with_types

    def construct_row_key_from_primary_keys(self, primary_key_variables) -> str:
        """
        given a list of primary key variables, typecast date type
        variables to snowflake timestamp and construct the snowflake clause
        for forming the row_key
        """
        row_key_cols = []
        for variable in primary_key_variables:  # type: ignore
            col_data_type = self._var_dtype_map.get(variable, "String").lower()
            date_type = re.match(".*date.*|.*duration.*", col_data_type) is not None
            if date_type:
                # if a primary key is of type date convert it to timestamp
                row_key_cols.append(
                    f"IFNULL(TO_TIMESTAMP(data:{variable})::STRING, 'none')"
                )
            else:
                row_key_cols.append(f"IFNULL(data:{variable}, 'none')")

        row_key_cols = ", ".join(row_key_cols)
        row_key_query = f"""
        LOWER(TO_VARCHAR(CONCAT_WS($$##::##$$, {row_key_cols}))) AS row_key
        """
        return row_key_query

    def construct_row_key_from_primary_keys_spark(self, primary_key_variables) -> str:
        """
        given a list of primary key variables, typecast date type
        variables to spark timestamp and construct the spark clause
        for forming the row_key
        """
        row_key_cols = []
        col_data_types = []
        for variable in primary_key_variables:
            row_key_cols.append(f"IFNULL({variable}, 'none')")
            data_type = self._var_dtype_map.get(variable, "String").lower()
            col_data_types.append(data_type)

        col_data_types_string = ",".join(col_data_types)

        row_key_cols = ", ".join(row_key_cols)
        row_key_query = f"""
        LOWER(PROCESS_AND_CONCAT_ROW_KEY('##::##', '{col_data_types_string}', {row_key_cols})) AS row_key
        """
        return row_key_query

    @abstractmethod
    def construct_source_var_dtype_map(self) -> dict:
        """
        fetches list of source variables and their everstage data types as kd aware
        """

    @abstractmethod
    def get_row_key_for_source(self) -> str:
        """
        returns the list of variables present in the row_key of the
        source converted to corresponding snowflake data type
        """
        return

    @abstractmethod
    def get_row_key_for_source_spark(self) -> str:
        """
        returns the list of variables present in the row_key of the
        source converted to corresponding snowflake data type
        """
        return

    def get_filter_condition_for_invalidate_rows(self) -> str:
        """
        returns the filter clause to be used for fetching rows from source that
        are invalidated in the time range [T_last_generated_time,...., T_knowledge_date]
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} <= '{self._last_generated_time}'::{snowflake_datetime}
            AND knowledge_end_date::{snowflake_datetime} IS NOT NULL AND ( 
                knowledge_end_date::{snowflake_datetime} > '{self._last_generated_time}'::{snowflake_datetime}
                AND knowledge_end_date::{snowflake_datetime} <= '{self._knowledge_date}'::{snowflake_datetime}
            )
        )"""
        return condition

    def get_common_select_query_for_source(self, table_name) -> str:
        # in the intermediate transformation result table, the variables are not stored
        # in json data column, but are stored as separate table columns, hence
        # since in custom object data, the columns are present inside the data json
        source_variable_select_clause = ", ".join(
            list(map(lambda x: x, self._var_dtype_map))
        )
        row_key_clause = self.get_row_key_for_source_spark()

        select_clause = f"{source_variable_select_clause}, {row_key_clause}"

        fetch_query = f"""
            SELECT {select_clause}
            FROM {table_name}
        """
        return fetch_query

    def get_filter_condition_for_insert_rows(self) -> str:
        """
        returns the filter clause to be used for fetching the insert rows from source
        that were inserted to source in the range [T_last_generated_time,...., T_knowledge_date]
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} > '{self._last_generated_time}'::{snowflake_datetime}
            AND knowledge_begin_date::{snowflake_datetime} <= '{self._knowledge_date}'::{snowflake_datetime}
            AND ( 
                knowledge_end_date::{snowflake_datetime} IS NULL 
                OR knowledge_end_date::{snowflake_datetime} > '{self._knowledge_date}'::{snowflake_datetime} 
            )
        )"""
        return condition

    def get_filter_condition_for_existing_rows(self) -> str:
        """
        returns the filter clause to be used for fetching the existing rows from source
        that did not undergo any change during [T_last_generated_time,...., T_knowledge_date]
        (BEGIN_DATE <= T_last AND (END_DATE IS NULL OR END_DATE IS > T_start))
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} <= '{self._last_generated_time}'::{snowflake_datetime}
            AND (
                knowledge_end_date::{snowflake_datetime} IS NULL
                OR knowledge_end_date::{snowflake_datetime} > '{self._knowledge_date}'::{snowflake_datetime}
            )
        )
        """
        return condition

    def get_filter_condition_for_kd_aware_rows(self) -> str:
        """
        returns filter condition for rows that were valid in the source at knowledge_date
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} <= '{self._knowledge_date}'::{snowflake_datetime}
            AND (
                knowledge_end_date::{snowflake_datetime} IS NULL
                OR knowledge_end_date::{snowflake_datetime} > '{self._knowledge_date}'::{snowflake_datetime}
            )
        )
        """
        return condition

    def get_filter_condition_for_row_type(
        self,
        row_type: DataRowType,
    ) -> str:
        """
        Given a row type, returns a filter condition to fetch this row_type
        """
        type_to_method = {
            DataRowType.INSERT: self.get_filter_condition_for_insert_rows,
            DataRowType.INVALIDATE: self.get_filter_condition_for_invalidate_rows,
            DataRowType.EXISTING: self.get_filter_condition_for_existing_rows,
            DataRowType.KD_AWARE: self.get_filter_condition_for_kd_aware_rows,
        }

        return type_to_method[row_type]()

    @abstractmethod
    def get_source_identifer_clause(self) -> str:
        """
        returns the clause used to identify the source
        """
        return ""

    @abstractmethod
    def get_common_fetch_query_for_source(
        self,
        filter_conditions: str,
        fetch_entire_row=True,
        fetch_deleted_records=False,
    ) -> str:
        """
        data from source can be fetched in multiple scenarios, where the only difference
        bw each of those are some filter conditions. All other clauses used in the fetch
        query are common, ex :- source table, variables, row_key etc
        """
        return ""

    def get_invalidate_rows_query(
        self,
        fetch_entire_row: bool = True,
    ) -> str:
        """
        Returns a query to fetch the invalidate rows from source.
        When fetch_entire_row is set to true, returns entire row that needs to be invalidated
        else just the row_keys that need to be invalidated
        """
        invalidate_rows_condition = self.get_filter_condition_for_invalidate_rows()
        filter_expressions = invalidate_rows_condition

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expressions,
            fetch_entire_row=fetch_entire_row,
        )

    def get_insert_rows_query(self) -> str:
        """
        Returns a query to fetch the insert rows from source
        """
        insert_rows_condition = self.get_filter_condition_for_insert_rows()
        filter_expressions = insert_rows_condition

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expressions,
        )

    def get_insert_rows_query_compressed(self) -> str:
        """
        Returns a query to fetch the insert rows from source with
        the source variables compressed into the 'data' column
        """
        insert_rows_from_source = self.get_insert_rows_query()

        return convert_fetch_query_to_df(
            var_dtype_map=self._var_dtype_map,
            cache_table=insert_rows_from_source,
        )

    def get_existing_rows_query(self) -> str:
        """
        Returns a query to fetch the existing rows from source (rows that did not change during
        the time window)
        """
        existing_rows_condition = self.get_filter_condition_for_existing_rows()
        filter_expressions = existing_rows_condition

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expressions
        )

    def get_kd_aware_rows_query(self) -> str:
        """
        returns rows that were valid in the source at knowledge_date
        """
        kd_aware_condition = self.get_filter_condition_for_kd_aware_rows()
        filter_expression = kd_aware_condition

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expression
        )

    def get_arbritrary_rows_query(
        self,
        row_types: list[DataRowType],
    ) -> str:
        """
        Given a list of row types (insert, existing etc), returns query to
        fetch rows from source whose type is in row_types
        """

        filter_expressions = list()
        for row_type in row_types:
            filter_expressions.append(
                self.get_filter_condition_for_row_type(
                    row_type=row_type,
                )
            )

        # combine the filters with OR
        filter_expressions = f"""(
            {" OR ".join(filter_expressions)}
        )
        """

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expressions
        )

    @abstractmethod
    def get_source_table_name(self) -> str:
        """
        Gets the name of the source table for the source
        """
        return

    def get_source_var_dtype(self) -> dict:
        """
        getter for source variable list and their datatype
        """
        return self._var_dtype_map

    def set_source_var_type(self, var_dtype_map: dict):
        """
        setter for source variable list and their datatype
        """
        self._var_dtype_map = var_dtype_map

    def get_source_meta_details(self) -> dict:
        """
        returns the meta details of the data source
        """
        return {
            "client_id": self._client_id,
            "type": self._source_type,
            "source_id": self._source_id,
        }

    def get_source_databook_id(self) -> str:
        """
        returns the source databook id of the data source
        """
        return self._source_databook_id

    def __str__(self) -> str:
        return f"client_id: {self._client_id}, datasheet: {self._datasheet_id}, source_id: {self._source_id}"


class DatasheetSource(DataSource):
    """
    Concrete class for datasheet as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        fetch datasheet metadata as kd aware
        """
        variable_dtype_map = dict()

        source_variables = get_datasheet_meta_data(
            client_id=self._client_id,
            databook_id=self._source_databook_id,
            datasheet_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )
        for variable in source_variables["variables"]:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        # Retain the row key of the source datasheet
        row_key = "LOWER(row_key) AS row_key"
        return row_key

    def get_row_key_for_source_spark(self) -> str:
        """
        returns the list of variables present in the row_key of the
        source converted to corresponding snowflake data type
        """
        return self.get_row_key_for_source()

    def get_source_identifer_clause(self) -> str:
        return f"""(
            client_id = {self._client_id} AND databook_id = '{self._source_databook_id}' AND datasheet_id = '{self._source_id}'
        )"""

    def get_common_fetch_query_for_source(
        self,
        filter_conditions: str,
        fetch_entire_row=True,
        fetch_deleted_records=False,
    ) -> str:
        source_variable_list = self.get_variables_with_dtype()
        # since in datasheet data, the columns are present inside the data json
        # and in data_pure column
        data_source_variable_select_clause = ", ".join(
            list(map(lambda x: f"data:{x}", source_variable_list))
        )
        data_pure_source_variable_select_clause = ", ".join(
            list(map(lambda x: f"data_pure:{x}", source_variable_list))
        )
        row_key_clause = self.get_row_key_for_source()
        datasheet_identifier = f"""(
            client_id = {self._client_id} AND databook_id = '{self._source_databook_id}' AND datasheet_id = '{self._source_id}'
        )
        """

        if fetch_entire_row:
            select_clause_data = (
                f"{data_source_variable_select_clause}, {row_key_clause}"
            )
            select_clause_data_pure = (
                f"{data_pure_source_variable_select_clause}, {row_key_clause}"
            )
        else:
            select_clause_data = f"{row_key_clause}"
            select_clause_data_pure = select_clause_data

        fetch_query = f"""
            SELECT {select_clause_data}
            FROM {self._source_table}
            WHERE {datasheet_identifier}
            AND {filter_conditions} AND is_adjusted = false
            UNION
            SELECT {select_clause_data}
            FROM {self._source_table}
            WHERE {datasheet_identifier} AND {filter_conditions} 
            AND is_adjusted = true AND adjustment_type = 'global' AND data IS NOT NULL
            UNION
            SELECT {select_clause_data_pure}
            FROM {self._source_table}
            WHERE {datasheet_identifier} AND {filter_conditions}
            AND is_adjusted = true AND adjustment_type = 'local' AND data_pure IS NOT NULL
        """
        return fetch_query

    def get_source_table_name(self) -> str:
        """
        fetch name of the shard in which the datasheet's data is located
        """
        return get_datasheet_data_table_name(
            client_id=self._client_id, datasheet_id=self._source_id
        )


class ObjectSource(DataSource):
    """
    Concrete class for custom object as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        construct custom object metadata as kd aware
        """
        variable_dtype_map = dict()
        source_variables = get_custom_object_meta_data(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )
        for variable in source_variables["variables"]:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        primary_key_variables = get_primary_key(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )["primary_key"]

        return self.construct_row_key_from_primary_keys(
            primary_key_variables=primary_key_variables
        )

    def get_row_key_for_source_spark(self) -> str:
        primary_key_variables = get_primary_key(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )["primary_key"]

        return self.construct_row_key_from_primary_keys_spark(
            primary_key_variables=primary_key_variables
        )

    # for custom object as a source, we need to fetch deleted records as well as part of the invalidate
    # records from the source, hence overriding the implementation from super class
    def get_invalidate_rows_query(
        self,
        fetch_entire_row: bool = True,
    ) -> str:
        """
        Returns a query to fetch the invalidate rows from source.
        When fetch_entire_row is set to true, returns entire row that needs to be invalidated
        else just the row_keys that need to be invalidated
        """
        invalidate_rows_condition = self.get_filter_condition_for_invalidate_rows()
        filter_expressions = invalidate_rows_condition

        return self.get_common_fetch_query_for_source(
            filter_conditions=filter_expressions,
            fetch_entire_row=fetch_entire_row,
            fetch_deleted_records=True,
        )

    def get_source_identifer_clause(self) -> str:
        return f"""(
            client_id = {self._client_id} AND custom_object_id = {self._source_id} and is_deleted = false
        )"""

    def get_common_fetch_query_for_source(
        self,
        filter_conditions: str,
        fetch_entire_row=True,
        fetch_deleted_records=False,
    ) -> str:
        source_variable_list = self.get_variables_with_dtype()
        # since in custom object data, the columns are present inside the data json
        source_variable_select_clause = ", ".join(
            list(map(lambda x: f"data:{x}", source_variable_list))
        )
        row_key_clause = self.get_row_key_for_source()

        if fetch_entire_row:
            select_clause = f"{source_variable_select_clause}, {row_key_clause}"
        else:
            select_clause = f"{row_key_clause}"

        is_deleted_clause = ""
        if not fetch_deleted_records:
            is_deleted_clause = " AND is_deleted = false"

        fetch_query = f"""
            SELECT {select_clause}
            FROM {self._source_table}
            WHERE client_id = {self._client_id} AND custom_object_id = {self._source_id} {is_deleted_clause}
            AND {filter_conditions}
        """
        return fetch_query

    def get_source_table_name(self) -> str:
        return get_custom_object_data_table_name(self._client_id, self._source_id)


class ReportSource(DataSource):
    """
    Concrete class for report object as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        construct report object metadata as kd aware
        """
        variable_dtype_map = dict()
        source_variables = get_report_variables(
            report_object_id=self._source_id, client_id=self._client_id
        )
        source_variables = get_report_object_types(source_variables)
        for variable in source_variables:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        primary_key_variables = get_report_primary_keys(
            report_object_id=self._source_id
        )
        return self.construct_row_key_from_primary_keys(
            primary_key_variables=primary_key_variables
        )

    def get_row_key_for_source_spark(self) -> str:
        primary_key_variables = get_report_primary_keys(
            report_object_id=self._source_id
        )
        return self.construct_row_key_from_primary_keys_spark(
            primary_key_variables=primary_key_variables
        )

    def get_source_identifer_clause(self) -> str:
        return f"""(
            client_id = {self._client_id} AND object_id = '{self._source_id}' and is_deleted = false
        )"""

    def get_common_fetch_query_for_source(
        self,
        filter_conditions: str,
        fetch_entire_row=True,
        fetch_deleted_records=False,
    ) -> str:
        source_variable_list = self.get_variables_with_dtype()
        # since in custom object data, the columns are present inside the data json
        source_variable_select_clause = ", ".join(
            list(map(lambda x: f"data:{x}", source_variable_list))
        )
        row_key_clause = self.get_row_key_for_source()

        if fetch_entire_row:
            select_clause = f"{source_variable_select_clause}, {row_key_clause}"
        else:
            select_clause = f"{row_key_clause}"

        fetch_query = f"""
            SELECT {select_clause}
            FROM {self._source_table}
            WHERE client_id = {self._client_id} AND object_id = '{self._source_id}' AND is_deleted = false
            AND {filter_conditions}
        """
        return fetch_query

    def get_source_table_name(self) -> str:
        table_name = get_table_for_object_id(report_object_id=self._source_id)
        table_name = resolve_report_object_table_name(
            self._client_id, self._source_id, table_name
        )
        return table_name


class TransformationSource(DataSource):
    """
    Concrete class for when  transformation is the source
    (source_id in this case is the transformation key)
    """

    def get_row_key_for_source(self) -> str:
        # row_key is fetched directly from the intermediate result table
        row_key = "lower(row_key) as row_key"
        return row_key

    def get_row_key_for_source_spark(self) -> str:
        # row_key is fetched directly from the intermediate result table
        return self.get_row_key_for_source()

    # if intermediate transformation is the source, we use the following conditions
    # for fetching insert and invalidate rows
    def get_filter_condition_for_invalidate_rows(self) -> str:
        """
        returns rows with kbd < last_generated_time and ked >= knowledge_date
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        # when invalidating rows in intermediate table, we put ked = knowledge_date, so using that here
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} <= '{self._last_generated_time}'::{snowflake_datetime}
            AND (
                knowledge_end_date::{snowflake_datetime} IS NOT NULL
                AND knowledge_end_date::{snowflake_datetime} = '{self._knowledge_date}'::{snowflake_datetime}
            )
        )"""
        return condition

    def get_filter_condition_for_insert_rows(self) -> str:
        """
        returns the filter clause to be used for fetching the insert rows from source
        that were inserted to source in the range [T_last_generated_time,...., T_knowledge_date]
        """
        snowflake_datetime = resolve_snowflake_data_type("DATE")
        # when inserting rows in intermediate table, we put kbd = knowledge_date, so using that here
        condition = f"""(
            knowledge_begin_date::{snowflake_datetime} = '{self._knowledge_date}'::{snowflake_datetime}
            AND knowledge_end_date::{snowflake_datetime} IS NULL
            )
        """
        return condition

    def get_common_fetch_query_for_source(
        self,
        filter_conditions: str,
        fetch_entire_row=True,
        fetch_deleted_records=False,
    ) -> str:
        # in the intermediate transformation result table, the variables are not stored
        # in json data column, but are stored as separate table columns, hence
        source_variable_list = self.get_variables_with_dtype()
        # since in custom object data, the columns are present inside the data json
        source_variable_select_clause = ", ".join(
            list(map(lambda x: f"data:{x}", source_variable_list))
        )
        row_key_clause = self.get_row_key_for_source()

        if fetch_entire_row:
            select_clause = f"{source_variable_select_clause}, {row_key_clause}"
        else:
            select_clause = f"{row_key_clause}"

        fetch_query = f"""
            SELECT {select_clause}
            FROM {self._source_table}
            WHERE {filter_conditions}
        """
        return fetch_query

    def get_source_table_name(self) -> str:
        # source_id in this case is the transformation_key
        return get_datasheet_transformation_result_table_name(
            client_id=self._client_id,
            datasheet_id=self._datasheet_id,
            transformation_key=self._source_id,
        )

    def construct_source_var_dtype_map(self) -> dict:
        """
        This super-class method is not relevant for transformation source
        """
        return {}


class FlatDataSource(DataSource):
    def construct_row_key_from_primary_keys(self, primary_key_variables) -> str:
        """
        given a list of primary key variables, typecast date type
        variables to snowflake timestamp and construct the snowflake clause
        for forming the row_key
        """
        row_key_cols = []
        for variable in primary_key_variables:  # type: ignore
            col_data_type = self._var_dtype_map.get(variable, "String").lower()
            date_type = re.match(".*date.*|.*duration.*", col_data_type) is not None
            if date_type:
                # if a primary key is of type date convert it to timestamp
                row_key_cols.append(f"IFNULL(TO_TIMESTAMP({variable})::STRING, 'none')")
            else:
                row_key_cols.append(f"IFNULL({variable}, 'none')")

        row_key_cols = ", ".join(row_key_cols)
        row_key_query = f"""
        LOWER(TO_VARCHAR(CONCAT_WS($$##::##$$, {row_key_cols}))) AS row_key
        """
        return row_key_query

    def get_all_rows_query(self) -> str:
        source_variable_list = self.get_variables_with_dtype()
        source_variable_select_clause = ", ".join(source_variable_list)
        row_key_clause = self.get_row_key_for_source()

        select_clause = f"{source_variable_select_clause}, {row_key_clause}"

        fetch_query = f"""
            SELECT {select_clause}
            FROM {self._source_table}
        """
        return fetch_query


class CteSource(FlatDataSource):
    def get_row_key_for_source(self) -> str:
        # row_key is fetched directly from the CTE
        row_key = "LOWER(row_key) AS row_key"
        return row_key

    def get_row_key_for_source_spark(self) -> str:
        # row_key is fetched directly from the CTE
        return self.get_row_key_for_source()

    def get_source_table_name(self) -> str:
        return self._source_id


class FlatReportSource(FlatDataSource):
    """
    Concrete class for report object as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        construct report object metadata as kd aware
        """
        variable_dtype_map = dict()
        source_variables = get_report_variables(
            report_object_id=self._source_id, client_id=self._client_id
        )
        source_variables = get_report_object_types(source_variables)
        for variable in source_variables:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        """
        row_key column is already present in the flat view
        """
        row_key = "LOWER(row_key) AS row_key"
        return row_key

    def get_row_key_for_source_spark(self) -> str:
        return self.get_row_key_for_source()

    def get_source_identifer_clause(self) -> str:
        """
        Not implemented
        """
        return ""

    def get_source_table_name(self) -> str:
        return get_report_object_flat_view_name(self._client_id, self._source_id)


class FlatDatasheetSource(FlatDataSource):
    """
    Concrete class for datasheet as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        fetch datasheet metadata as kd aware
        """
        variable_dtype_map = dict()

        source_variables = get_datasheet_meta_data(
            client_id=self._client_id,
            databook_id=self._source_databook_id,
            datasheet_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )
        for variable in source_variables["variables"]:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        # Retain the row key of the source datasheet
        row_key = "LOWER(row_key) AS row_key"
        return row_key

    def get_row_key_for_source_spark(self) -> str:
        """
        returns the list of variables present in the row_key of the
        source converted to corresponding snowflake data type
        """
        return self.get_row_key_for_source()

    def get_source_identifer_clause(self) -> str:
        """
        Not implemented
        """
        return ""

    def get_all_rows_query(
        self,
    ) -> str:
        source_variable_list = self.get_variables_with_dtype()
        # since in datasheet data, the columns are present inside the data json
        # and in data_pure column
        data_source_variable_select_clause = ", ".join(source_variable_list)
        data_pure_source_variable_select_clause = ", ".join(
            list(map(lambda x: f"pure_{x}", source_variable_list))
        )
        row_key_clause = self.get_row_key_for_source()
        pure_row_key_clause = "LOWER(pure_row_key) AS row_key"

        select_clause_data = f"{data_source_variable_select_clause}, {row_key_clause}"
        select_clause_data_pure = (
            f"{data_pure_source_variable_select_clause}, {pure_row_key_clause}"
        )

        fetch_query = f"""
            SELECT {select_clause_data}
            FROM {self._source_table}
            WHERE is_adjusted = false
            UNION
            SELECT {select_clause_data}
            FROM {self._source_table}
            WHERE is_adjusted = true AND adjustment_type = 'global' AND row_key IS NOT NULL
            UNION
            SELECT {select_clause_data_pure}
            FROM {self._source_table}
            WHERE is_adjusted = true AND adjustment_type = 'local' AND pure_row_key IS NOT NULL
        """
        return fetch_query

    def get_source_table_name(self) -> str:
        """
        fetch name of the shard in which the datasheet's data is located
        """
        return get_datasheet_data_table_name(
            client_id=self._client_id, datasheet_id=self._source_id, latest=True
        )


class FlatObjectSource(FlatDataSource):
    """
    Concrete class for custom object as a source
    """

    def construct_source_var_dtype_map(self) -> dict:
        """
        construct custom object metadata as kd aware
        """
        variable_dtype_map = dict()
        source_variables = get_custom_object_meta_data(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )
        for variable in source_variables["variables"]:  # type: ignore
            variable_dtype_map[variable["system_name"]] = variable["data_type"]

        return variable_dtype_map

    def get_row_key_for_source(self) -> str:
        primary_key_variables = get_primary_key(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )["primary_key"]

        return self.construct_row_key_from_primary_keys(
            primary_key_variables=primary_key_variables
        )

    def get_row_key_for_source_spark(self) -> str:
        primary_key_variables = get_primary_key(
            client_id=self._client_id,
            custom_object_id=self._source_id,
            knowledge_date=self._knowledge_date,
        )["primary_key"]

        return self.construct_row_key_from_primary_keys_spark(
            primary_key_variables=primary_key_variables
        )

    # for custom object as a source, we need to fetch deleted records as well as part of the invalidate
    # records from the source, hence overriding the implementation from super class
    def get_invalidate_rows_query(
        self,
        fetch_entire_row: bool = True,  # pylint: disable=unused-argument
    ) -> str:
        """
        Not implemented
        """
        return ""

    def get_source_identifer_clause(self) -> str:
        """
        Not implemented
        """
        return ""

    def get_source_table_name(self) -> str:
        return _get_co_latest_table_name(self._client_id, self._source_id)


def get_source_based_on_type(
    client_id,
    source_type: Union[str, None],
    source_id: Union[str, int, None],
    databook_id: str,
    datasheet_id: str,
    last_generated_time: datetime,
    knowledge_date: datetime,
) -> Union[DataSource, None]:
    """
    Depending upon the source type, return the DataSource object
    knowledge_date is used for fetching date aware metadata for each source
    """
    if source_type in source_type_to_class_registry:
        source_databook_id = databook_id
        if source_type == DATASHEET_SOURCE.DATASHEET.value:
            source_databook_id = get_databook_id_by_datasheet_id(
                client_id=client_id, datasheet_id=source_id
            )
            source_databook_id = str(source_databook_id)

        source_obj = source_type_to_class_registry[source_type](
            client_id=client_id,
            source_type=source_type,
            source_id=source_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            last_generated_time=last_generated_time,
            knowledge_date=knowledge_date,
            source_databook_id=source_databook_id,
        )

    else:
        logger.exception(
            f"Datasheet source type is invalid for client - {client_id}, source type {source_type}, source id {source_id}"
        )
        source_obj = None

    return source_obj


def get_flat_source_based_on_type(
    client_id,
    source_type: Union[str, None],
    source_id: Union[str, int, None],
    databook_id: str,
    datasheet_id: str,
    last_generated_time: datetime,
    knowledge_date: datetime,
) -> Union[DataSource, None]:
    """
    Depending upon the source type, return the DataSource object
    knowledge_date is used for fetching date aware metadata for each source
    """

    if source_type not in source_type_to_class_registry_flat:
        raise LiteEtlException(
            f"Flat datasheet source type is invalid for client - {client_id}, source type {source_type}, source id {source_id}"
        )

    source_databook_id = databook_id
    if source_type == DATASHEET_SOURCE.DATASHEET.value:
        source_databook_id = get_databook_id_by_datasheet_id(
            client_id=client_id, datasheet_id=source_id
        )
        source_databook_id = str(source_databook_id)

    source_obj = source_type_to_class_registry_flat[source_type](
        client_id=client_id,
        source_type=source_type,
        source_id=source_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        last_generated_time=last_generated_time,
        knowledge_date=knowledge_date,
        source_databook_id=source_databook_id,
    )

    return source_obj


# mapping of data source type to their class implementation
source_type_to_class_registry = {
    DataSouceType.CUSTOM_OBJECT.value: ObjectSource,
    DataSouceType.DATASHEET.value: DatasheetSource,
    DataSouceType.REPORT_OBJECT.value: ReportSource,
    DataSouceType.TRANSFORMATION.value: TransformationSource,
}

source_type_to_class_registry_flat = {
    DataSouceType.CUSTOM_OBJECT.value: FlatObjectSource,
    DataSouceType.DATASHEET.value: FlatDatasheetSource,
    DataSouceType.REPORT_OBJECT.value: FlatReportSource,
    DataSouceType.CTE.value: CteSource,
}
