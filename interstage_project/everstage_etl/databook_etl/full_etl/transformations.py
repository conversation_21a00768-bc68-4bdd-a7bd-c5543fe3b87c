"""
Snowflake transformations.
"""

# pylint: disable-msg=logging-fstring-interpolation unnecessary-lambda
import json
import logging
import re
from datetime import datetime
from typing import Union

from snowflake.snowpark import Session

from commission_engine.accessors.client_accessor import (
    get_client_datasheet_row_limit,
    get_client_fiscal_start_month,
)
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_datatype_id_name_map,
)
from commission_engine.services.datasheet_data_services.datasheet_etl import (
    InvalidDataTypeError,
    InvalidOperatorError,
)
from commission_engine.services.expression_designer import convert_filter_transform_spec
from commission_engine.utils.databook_utils import (  # get_datasheet_data_table_name,
    get_datasheet_transformation_result_table_name,
    resolve_snowflake_data_type,
)
from commission_engine.utils.date_utils import get_period_start_and_end_date
from commission_engine.utils.general_utils import log_time_taken
from commission_engine.utils.report_utils import get_system_object_data_table_name
from commission_engine.utils.transformation_models import (
    GetUserPropertiesTransformationMeta,
)
from common.utils.infix_utils import InfixToSnowflakeQuery
from everstage_etl.databook_etl.data_sources import DataSource, get_source_based_on_type
from everstage_etl.databook_etl.datasheet_etl_circuit_breaker import (
    check_if_sf_query_will_exceed_row_limit,
    circuit_breaker,
)
from everstage_etl.databook_etl.datasheet_etl_utils import (
    DataSouceType,
    TransformationType,
    cache_transformed_result,
    compress_variant_attributes,
    convert_fetch_query_to_df,
    convert_to_python_datatype,
    create_or_replace_temp_table,
    create_or_replace_temp_view,
    data_explode_transformations_registry,
    format_snowflake_sql,
    get_data_type_col,
    is_datasheet_config_changed,
    multiple_source_transformation_registry,
)
from snowflake_udf_modules.hierarchy.udf_code.data_structure import (
    HierarchyDataStructure,
)
from spm.custom_exceptions.datasheet_exceptions import DatasheetRowLimitExceeded
from spm.services.stormbreaker.stormbreaker_utils import get_filter_expression
from spm.utils import GetUserPropertiesTransformationUtils

logger = logging.getLogger(__name__)


@log_time_taken()
def apply_transforms_snowflake(
    snowpark_session: Session,
    client_id: int,
    datasheet_details: dict,
    databook_id: str,
    knowledge_date: datetime,
):
    """
    Apply all the transformations of the datasheet using manually
    constructed snowflake queries
    """
    datasheet_id: str = datasheet_details["datasheet_id"]
    source_type: str = datasheet_details["source_type"]
    source_id: Union[int, str] = datasheet_details["source_id"]
    transformation_spec: list = datasheet_details["transformation_spec"]
    datasheet_name: str = datasheet_details.get("datasheet_name", "")

    # When the `databook_expressionbox_version` client_feature flag is set to "v2", it implies that
    # the filter expressions are provided in the V2 infix expression format. The V2 format will have
    # different structures compared to the V1 format.

    # This section of the code serves the purpose of transforming and adapting the filter expression
    # from V2 to V1 format, ensuring compatibility with the rest of the system or downstream processes
    # that expect the filter expressions in the V1 format. If the expression is already in the V1 format,
    # we will ignore the conversion.
    transformation_spec = convert_filter_transform_spec(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        transformation_spec=transformation_spec,
    )

    # construct the DataSource object for the sheet's source
    source_data_obj = get_source_based_on_type(
        client_id=client_id,
        source_type=source_type,
        source_id=source_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        last_generated_time=knowledge_date,
        knowledge_date=knowledge_date,
    )
    logger.debug(
        "Query to fetch data from datasheet source %s",
        source_data_obj.get_kd_aware_rows_query(),
    )

    client_datasheet_row_limit = get_client_datasheet_row_limit(client_id=client_id)
    is_config_changed = is_datasheet_config_changed(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
    )

    # if any transformation is present, the result of the transformation will be cached,
    # else cache it right after constructing its fetch query
    inter_result_table_name: str = ""
    if len(transformation_spec) == 0:
        inter_result_table_name = cache_transformed_result(
            snowpark_session=snowpark_session,
            transformed_data_query=source_data_obj.get_kd_aware_rows_query(),
        )
        # when there is no transformation, the source is the transformation result
        transformation_result_obj = source_data_obj
    else:
        # lis of data sources for the datasheet transformation
        data_sources: list = [source_data_obj]
        for transformation_dict in transformation_spec:
            logger.info(f"Running for transformation dict - {transformation_dict}")
            # if transformation is join/union, construct fetch query for the right datasheet
            if (
                transformation_dict.get("type")
                in multiple_source_transformation_registry
            ):
                if (
                    transformation_dict.get("type")
                    == TransformationType.TEMPORAL_SPLICE.value
                ):
                    for index, source in enumerate(transformation_dict.get("meta")):
                        if index == 0:
                            continue
                        data_sources.append(
                            get_source_based_on_type(
                                client_id=client_id,
                                source_type=source.get("source_type"),
                                source_id=source.get("source_id"),
                                databook_id=databook_id,
                                datasheet_id=datasheet_id,
                                last_generated_time=knowledge_date,
                                knowledge_date=knowledge_date,
                            )
                        )
                else:
                    data_sources.append(
                        get_source_based_on_type(
                            client_id=client_id,
                            source_type=DataSouceType.DATASHEET.value,
                            source_id=transformation_dict.get("with"),
                            databook_id=databook_id,
                            datasheet_id=datasheet_id,
                            last_generated_time=knowledge_date,
                            knowledge_date=knowledge_date,
                        )
                    )

            # query to apply transformations
            transformed_data_query, transformed_var_dtype_map = apply_transform(
                snowpark_session=snowpark_session,
                data_sources=data_sources,
                transformation_dict=transformation_dict,
            )

            # if schema has changed, check count first before executing query
            if (
                transformation_dict.get("type") in data_explode_transformations_registry
                and is_config_changed
            ):
                transformation_row_count_query = f"""
                    {transformed_data_query}
                    limit {client_datasheet_row_limit + 1}
                """
                query_exceeds_soft_limit = check_if_sf_query_will_exceed_row_limit(
                    snowpark_session=snowpark_session,
                    snowflake_object=transformation_row_count_query,
                    soft_limit=client_datasheet_row_limit,
                )
                if query_exceeds_soft_limit:
                    raise DatasheetRowLimitExceeded(
                        client_id=client_id,
                        datasheet_id=datasheet_id,
                        datasheet_name=datasheet_name,
                        client_threshold_limit=client_datasheet_row_limit,
                        datasheet_row_count=client_datasheet_row_limit + 1,
                    )

            # cache the result of intermediate transformations in a temp table
            # the previous transformation is not re evaluated becuase of lazy evaluation
            inter_result_table_name = get_datasheet_transformation_result_table_name(
                client_id=client_id,
                datasheet_id=datasheet_id,
                transformation_key=transformation_dict["key"],
            )

            additional_cols = None
            if transformation_dict.get("type") == "UNION":
                additional_cols = ["cnt"]

            check_transformation_count = False  # to check if the transformation produced more rows than set limit
            transformation_count_limit = None  # set the row count limit
            # if it's not a join transformation or if schema doesn't change in case of join
            if (
                transformation_dict.get("type")
                not in data_explode_transformations_registry
                or not is_config_changed
            ):
                check_transformation_count = True
                transformation_count_limit = client_datasheet_row_limit

            _create_intermediate_result_table(
                snowpark_session=snowpark_session,
                transformed_data_query=transformed_data_query,
                inter_result_table=inter_result_table_name,
                var_dtype_map=transformed_var_dtype_map,
                knowledge_date=knowledge_date,
                additional_cols=additional_cols,
                soft_limit=transformation_count_limit,
            )

            if check_transformation_count:
                query_exceeds_soft_limit = check_if_sf_query_will_exceed_row_limit(
                    snowpark_session=snowpark_session,
                    snowflake_object=inter_result_table_name,
                    soft_limit=transformation_count_limit,
                )
                if query_exceeds_soft_limit:
                    raise DatasheetRowLimitExceeded(
                        client_id=client_id,
                        datasheet_id=datasheet_id,
                        datasheet_name=datasheet_name,
                        client_threshold_limit=transformation_count_limit,
                        datasheet_row_count=transformation_count_limit + 1,
                    )

            # construct the data source for encapsulating the result of the transformation
            # a single transformation spec can have multiple transformation dict
            transformation_result_obj = get_source_based_on_type(
                client_id=client_id,
                source_type=DataSouceType.TRANSFORMATION.value,
                source_id=transformation_dict.get("key"),
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                last_generated_time=knowledge_date,
                knowledge_date=knowledge_date,
            )
            transformation_result_obj.set_source_var_type(
                var_dtype_map=transformed_var_dtype_map
            )

            data_sources = [transformation_result_obj]

    # if there are any transformations in sheet, variables are stored in a
    # # json column in intermediate result table
    source_vars_expanded = True if len(transformation_spec) == 0 else False

    # query to unpivot sheet variable columns into a JSON
    transformed_var_dtype_map = transformation_result_obj.get_source_var_dtype()
    result_dataframe_query = convert_fetch_query_to_df(
        var_dtype_map=transformed_var_dtype_map,
        cache_table=inter_result_table_name,
        source_vars_expanded=source_vars_expanded,
    )

    circuit_breaker(
        client_id=client_id,
        datasheet_id=datasheet_id,
        query=result_dataframe_query,
        snowpark_session=snowpark_session,
    )

    result_dataframe = snowpark_session.sql(result_dataframe_query)
    return result_dataframe


# ds_list :- queries to fetch source data of transformations
def apply_transform(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison: bool = False,
) -> tuple[str, dict]:
    """
    Returns the transformed_df
    """
    transformation_handler = choose_transformation_handler(transformation_dict["type"])
    transformed_data, var_dtype_map = transformation_handler(
        snowpark_session,
        data_sources,
        transformation_dict,
        ever_comparison=ever_comparison,
    )
    return transformed_data, var_dtype_map


def choose_transformation_handler(transformation: str):
    """
    Given a transformation type, the method returns the
    function that applies the transformation
    """
    transformations = {
        "SORT": apply_sort_transform_snowflake,
        "GROUP_BY": apply_group_by_transform_snowflake,
        "JOIN": apply_join_transform_snowflake,
        "UNION": apply_union_transform_snowflake,
        "FILTER": apply_filter_transform_snowflake,
        "ADVANCED_FILTER": apply_advanced_filter_transform_snowflake,
        "ADVANCED_FILTER_V2": apply_advanced_filter_v2_transform_snowflake,
        "FLATTEN": apply_flatten_transform_snowflake,
        "TEMPORAL_SPLICE": apply_temporal_splice_transform_snowflake,
        "GET_USER_PROPERTIES": apply_get_user_properties_transform_snowflake,
    }

    return transformations[transformation]


def apply_sort_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Given query to fetch source data this method
    constructs query to sort it
    """
    logger.info("BEGIN: Applying Sort transform")

    sort_type = transformation_dict["sort_type"]
    sort_on_cols = transformation_dict["on"]

    sort_condition = []
    for sort_col in sort_on_cols:
        sort_condition.append(f"{sort_col} {sort_type}")
    sort_condition = ", ".join(sort_condition)

    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    source_var_dtype = data_source.get_source_var_dtype()

    cur_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query
    )

    sort_query = f"""select * from {cur_view_id}
    order by {sort_condition}
    """
    logger.info(f"Sort query - {sort_query}")
    logger.info("END: Applied Sort transform")
    return sort_query, source_var_dtype


def apply_group_by_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Given the query to fetch the source data returns query
    to apply group by transformation on it.
    Transformation spec:
    {
        type: "GROUP_BY",
        by: ["col_1", "col_2", "col_3"],
        aggregations: [{function: "SUM", of: "col_1", col_name: "sum_col_1"},
                        {function: "MIN", of: "col_1", col_name: "min_col_1"},
                        {function: "COUNT_DISTINCT", of: "col_1", col_name: "count_dist_col_1"}]
    }
    """
    logger.info("BEGIN: Applying group by transform")

    group_by_cols = ", ".join(transformation_dict["by"])
    # row key is the concatenation of columns to be grouped by. if any col is null, return as empty string
    row_key_cols = ", ".join(
        list(map(lambda x: f"ifnull({x}::string, 'none')", transformation_dict["by"]))
    )

    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    source_var_dtype = data_source.get_source_var_dtype()

    cur_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query
    )

    grouped_df_var_type = {}
    for variable in transformation_dict["by"]:
        grouped_df_var_type[variable] = source_var_dtype.get(variable, "String")  # type: ignore

    select_cols = list(transformation_dict["by"])
    if "aggregations" in transformation_dict:
        for agg_details in transformation_dict["aggregations"]:
            if agg_details["function"] != "COUNT_DISTINCT":
                # if the aggregation is "sum" or count, the default value is 0 instead of null
                if agg_details["function"] not in ["MIN", "MAX"]:
                    # if value of column is null, change it to 0 in case of numeric summary functions
                    select_cols.append(
                        f"""
                        ifnull( {agg_details["function"]} ({agg_details["of"]}) ,0)
                        as {agg_details["col_name"]}
                    """
                    )
                    grouped_df_var_type[agg_details["col_name"]] = "Integer"
                else:
                    select_cols.append(
                        f"""
                        {agg_details["function"]} ({agg_details["of"]})
                        as {agg_details["col_name"]}
                    """
                    )
                    grouped_df_var_type[agg_details["col_name"]] = source_var_dtype.get(
                        agg_details["of"], "String"
                    )  # type: ignore
            else:
                select_cols.append(
                    f"""
                    COUNT( distinct( {agg_details["of"]} ) )
                    as {agg_details["col_name"]}
                """
                )
                grouped_df_var_type[agg_details["col_name"]] = "Integer"

    select_cols = ", ".join(select_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"
    group_by_query = f"""select {select_cols}, {row_key} as row_key
    from {cur_view_id}
    group by {group_by_cols}
    """

    logger.info(f"Group by transformation - {group_by_query}")
    return group_by_query, grouped_df_var_type


def apply_join_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    """
    Given queries to fetch the left and right datasheet,
    returns query to join the two datasheets
    Transformation spec:
    {
        type: "JOIN",
        join_type: "INNER/LEFT/RIGHT/FULL",
        with: "rhs_worksheet_id",
        on: {lhs:["lhs_col_1", "lhs_col_4"], rhs:["rhs_col_2", "rhs_col_5"]},
        columns: [lhs_col_1, rhs_col_2, lhs_col_3]
    }
    """
    logger.info("BEGIN: Applying join transform.")

    lhs_data_source = data_sources[0]
    lhs_data_query = lhs_data_source.get_kd_aware_rows_query()
    lhs_var_type = lhs_data_source.get_source_var_dtype()

    rhs_data_source = data_sources[1]
    rhs_data_query = rhs_data_source.get_kd_aware_rows_query()
    rhs_var_type = rhs_data_source.get_source_var_dtype()

    # iterate through lhs var type and rhs var type
    joined_df_var_type = {}
    for column in transformation_dict["columns"]:
        if column[0:3] == "rhs":
            joined_df_var_type[column] = rhs_var_type.get(column[4:], "String")  # type: ignore
        else:
            joined_df_var_type[column] = lhs_var_type.get(column[4:], "String")  # type: ignore

    join_type_map = {
        "INNER": "inner",
        "LEFT": "left",
        "RIGHT": "right",
        "FULL": "full outer",
    }

    spec_join_type = transformation_dict["join_type"]
    join_type = join_type_map[spec_join_type]

    # column from left df to join on
    left_on_cols = list(map(lambda x: x[4:], transformation_dict["on"]["lhs"]))
    # column from right df to join on
    right_on_cols = list(map(lambda x: x[4:], transformation_dict["on"]["rhs"]))

    join_condition = []
    for i, _ in enumerate(left_on_cols):
        # data types of left and right columns
        left_col_dtype, right_col_dtype = "String", "String"
        if lhs_var_type:
            left_col_dtype = lhs_var_type[left_on_cols[i]]
        if rhs_var_type:
            right_col_dtype = rhs_var_type[right_on_cols[i]]

        if (
            ever_comparison
            and left_col_dtype == "Integer"
            and right_col_dtype == "Integer"
        ):
            join_condition.append(
                f"udf_ever_comparison(ifnull(lhs.{left_on_cols[i]}, parse_json('NULL')), '==',  ifnull(rhs.{right_on_cols[i]}, parse_json('NULL')))"
            )
        elif left_col_dtype.lower() == "date" and right_col_dtype.lower() == "date":
            join_condition.append(
                f"""
                    ifnull(date_trunc('second', lhs.{left_on_cols[i]}::datetime)::variant, parse_json('NULL')) = ifnull(date_trunc('second', rhs.{right_on_cols[i]}::datetime)::variant, parse_json('NULL'))
                """
            )
        else:
            join_condition.append(
                f"ifnull(lhs.{left_on_cols[i]}::variant, parse_json('NULL')) = ifnull(rhs.{right_on_cols[i]}::variant, parse_json('NULL'))"
            )

    join_condition = " and ".join(join_condition)

    # store the dataframe in a temp view to query the semi-structured "data" column
    # adding a limit clause here for a snowflake sql bug -
    lhs_count = snowpark_session.sql(
        f"""select count(*) as count from ( {lhs_data_query} )"""
    ).collect()[0]["COUNT"]
    ds_lhs = f"""select * from ({lhs_data_query}) limit {lhs_count}"""
    lhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=ds_lhs
    )

    rhs_count = snowpark_session.sql(
        f"""select count(*) as count from ( {rhs_data_query} )"""
    ).collect()[0]["COUNT"]
    ds_rhs = f"""select * from ({rhs_data_query}) limit {rhs_count}"""
    rhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=ds_rhs
    )

    # columns to select from join result
    lhs_cols = []
    rhs_cols = []

    # constructing the columns to select from both the datasheets
    for column in transformation_dict["columns"]:
        if column[0:3] == "rhs":
            rhs_cols.append(f"rhs.{column[4:]} as {column}")
        else:
            lhs_cols.append(f"lhs.{column[4:]} as {column}")

    # if no column is selected of the other table, change it to empty string
    lhs_cols = ", ".join(lhs_cols)
    if len(lhs_cols) > 0:
        lhs_cols += ", "
    rhs_cols = ", ".join(rhs_cols)
    if len(rhs_cols) > 0:
        rhs_cols += ", "

    # row_key to be added as an attribute inside the data col and as a separate column
    row_key = "lower(TO_VARCHAR(concat(ifnull(lhs.row_key::string,'none'), $$##::##$$, ifnull(rhs.row_key::string,'none'))))"
    join_query = f"""select {lhs_cols} {rhs_cols} {row_key} as row_key
    from {lhs_view_id} as lhs {join_type} join {rhs_view_id} as rhs
    on {join_condition}"""

    logger.info(f"Join query - {join_query}")
    return join_query, joined_df_var_type


def apply_union_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Given queries to fetch the left and right datasheet,
    returns query to join the two datasheets
    Transformation spec:
    {
        type: "UNION",
        union_type: "ALL/DISTINCT",
        with: "rhs_worksheet_id",
        on: [{lhs: "col_1", rhs: "col_2", col_name: "col_1"}, {lhs: "col_6", rhs: "col_12", col_name: "col_6"}]
    }
    To keep track of count of rows from both sources that have a value
    for the union columns, union is implemented with group by and full outer join.

    1. First apply group by on left and right source rows on union columns and take the count.
    2. Then do an outer join on both these results on the union columns.
    3. The add the count of rows from left and right source

    Example union query formed for union on col1 and col2 :-
        select  ifnull(lhs.col1, rhs.col1) as c1,
                ifnull(lhs.col2, rhs.col2) as c2,
                ifnull(lhs.cnt, 0) + ifnull(rhs.cnt, 0) as cnt,
                lower(concat_ws($$##::##$$, ifnull(c1::string, 'none'), ifnull(c2::string, 'none'))) as row_key
        from
        (
            select col1 as col1, col2 as col2, count(*) as cnt from left_source
            group by col1, col2
        ) as lhs
        full outer join
        (
            select col1 as col1, col2 as col2, count(*) as cnt from right_source
            group by col1, col2
        ) as rhs
        on
        (
            ifnull(lhs.col1::variant, parse_json('NULL')) = ifnull(rhs.col1::variant, parse_json('NULL')) and
            ifnull(lhs.col2::variant, parse_json('NULL')) = ifnull(rhs.col2::variant, parse_json('NULL'))
        )
    The ifnull(col, parse_json) clause is used, since null = null is true in union when comparing two rows and
    same is not true in join, so this clause converts a column value to string null so that
    'null' = 'null' becomes true in join to replicate union's behaviour
    """
    logger.info("BEGIN: Applying Union transform.")

    lhs_data_source = data_sources[0]
    lhs_data_query = lhs_data_source.get_kd_aware_rows_query()
    lhs_var_type = lhs_data_source.get_source_var_dtype()

    rhs_data_source = data_sources[1]
    rhs_data_query = rhs_data_source.get_kd_aware_rows_query()
    # rhs_var_type = rhs_data_source.get_source_var_dtype()

    lhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=lhs_data_query
    )
    rhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=rhs_data_query
    )

    # columns to be selected in inner join query and columns on which the result
    # is grouped on (union columns)
    lhs_inner_select_cols, lhs_grp_by_cols = [], []
    rhs_inner_select_cols, rhs_grp_by_cols = [], []

    # columns used in inner join condition and construct to form row key
    outer_select_cols, join_condition_cols, row_key_cols = [], [], []
    union_df_var_type = {}

    # in the inner join query, columns from sources are named as col_{index},
    # which are then renamed to the names given in transformation
    for index, col_details in enumerate(transformation_dict["on"]):
        lhs_inner_select_cols.append(f"{col_details['lhs']} as col_{index}")
        lhs_grp_by_cols.append(f"col_{index}")

        rhs_inner_select_cols.append(f"{col_details['rhs']} as col_{index}")
        rhs_grp_by_cols.append(f"col_{index}")

        join_condition_cols.append(
            f"ifnull(lhs.col_{index}::variant, parse_json('NULL')) = ifnull(rhs.col_{index}::variant, parse_json('NULL'))"
        )
        outer_select_cols.append(
            f"ifnull(lhs.col_{index}, rhs.col_{index}) as {col_details['col_name']}"
        )
        row_key_cols.append(f"ifnull({col_details['col_name']}::string, 'none')")
        union_df_var_type[col_details["col_name"]] = lhs_var_type.get(col_details["lhs"], "String")  # type: ignore

    # to get count of rows with column values from both sources
    lhs_inner_select_cols.append("count(*) as cnt")
    rhs_inner_select_cols.append("count(*) as cnt")

    lhs_inner_select_cols = ", ".join(lhs_inner_select_cols)
    rhs_inner_select_cols = ", ".join(rhs_inner_select_cols)

    lhs_grp_by_cols = ", ".join(lhs_grp_by_cols)
    rhs_grp_by_cols = ", ".join(rhs_grp_by_cols)

    row_key_cols = ", ".join(row_key_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"

    sum_count_clause = "ifnull(lhs.cnt, 0) + ifnull(rhs.cnt, 0)"

    join_condition_cols = " and ".join(join_condition_cols)
    outer_select_cols = ", ".join(outer_select_cols)

    union_query = f"""
    select  {outer_select_cols}, {sum_count_clause} as cnt, {row_key} as row_key
    from
    (
        select {lhs_inner_select_cols}
        from {lhs_view_id}
        group by {lhs_grp_by_cols}
    ) as lhs
    full outer join
    (
        select {rhs_inner_select_cols}
        from {rhs_view_id}
        group by {rhs_grp_by_cols}
    ) as rhs
    on
    (
       {join_condition_cols}
    )
    """
    logger.info(f"Executing Union query -  {union_query}")
    return union_query, union_df_var_type


def apply_filter_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
    client_fiscal_start_month=None,
) -> tuple[str, dict]:
    """
    Given query to fetch source data, this constructs
    a query to filter it
    Transformation spec:
    {type:"FILTER", col_name: 'creation_date', operator: '>=', value: '2021/01/01'},
    {type:"FILTER", col_name: 'status', operator: '=', value: 'Open'},
    """
    # pylint: disable=unnecessary-lambda

    logger.info("Applying filter transform.")

    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    var_type = data_source.get_source_var_dtype()
    client_id = data_source.get_source_meta_details()["client_id"]

    cur_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query
    )

    # if var type is none select all columns from source
    if var_type:
        filter_select_columns = list(var_type.keys())
        filter_select_columns.append("row_key")
        filter_select_clause = f"{', '.join(filter_select_columns)}"
    else:
        filter_select_clause = "*"

    filter_query = f"""select {filter_select_clause}
    from {cur_view_id}
    where
    """

    where_clause = construct_filter_condition(
        client_id=client_id,
        transformation_dict=transformation_dict,
        var_type=var_type,
        client_fiscal_start_month=client_fiscal_start_month,
        ever_comparison=ever_comparison,
    )

    filter_query = f"""
        {filter_query}
        {where_clause}
    """
    logger.info(f"Filter query is {filter_query}")
    return filter_query, var_type


def apply_flatten_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,  # pylint: disable=unused-argument
) -> tuple[str, dict]:
    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    var_type = data_source.get_source_var_dtype()

    cur_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query
    )

    col_name = transformation_dict["col_name"]
    output_data_type = transformation_dict["output_data_type"]
    flattened_col = transformation_dict["flattened_col_name"]

    flatten_key = HierarchyDataStructure.node_key

    # The SEQ, PATH, VALUE are created by FLATTEN snowflake function
    # This is included in the row_key to ensure that the row_key is unique
    row_key_cols = [
        "row_key",
        # have used REGEXP_SUBSTR to extract the number from the path
        # path looks something like this "levels[\'1203\'].node"
        # and COALESCE to handle null values
        "COALESCE(REGEXP_SUBSTR(PATH, '[0-9]+', 1)::number, 0)",
        "IFNULL(VALUE, 'none')",
    ]
    row_key_cols = ", ".join(row_key_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"

    # if var type is none select all columns from source
    if var_type:
        flatten_select_columns = list(var_type.keys())
        flatten_select_columns.append(f"value::string as {flattened_col}")
        flatten_select_clause = f"{', '.join(flatten_select_columns)}"
    else:
        flatten_select_clause = "*"

    flatten_query = f"""
        SELECT {flatten_select_clause}, {row_key} as row_key
        FROM {cur_view_id} as ds_table,
        LATERAL FLATTEN(input => ds_table.{col_name}::variant::object, recursive=> true, outer=> true)
        where key = '{flatten_key}' or ds_table.{col_name} is NULL
    """
    var_type_copy = var_type.copy()
    var_type_copy[flattened_col] = output_data_type

    logger.info(f"Flatten query is {flatten_query}")
    return flatten_query, var_type_copy


def apply_advanced_filter_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
    v2_filter=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Complex Expressions can be used to filter the datasheet,
    transformation_spec: {type: ADVANCED_FILTER , infix: "{INFIX}"}

    Sample Infix expression:
            [
                {
                    "meta": {
                        "category": None,
                        "model_name": "",
                        "system_name": "period_start_date",
                        "data_type_id": 2,
                    },
                    "name": "Period Start Date",
                    "tags": None,
                    "type": "VARIABLE",
                    "data_type": "Date",
                },
                {
                    "name": "<=",
                    "type": "OPERATOR",
                    "alt_name": "LESSERTHANEQUALTO",
                    "category": "LOGICAL",
                    "__typename": "",
                    "multi_valued": False,
                    "output_types": ["Boolean"],
                    "needs_operand": True,
                    "operand_types": [
                        "Integer",
                        "Date",
                        "Percentage",
                        "DayDuration",
                        "MinuteDuration",
                        "SecondDuration",
                    ],
                    "output_type_ids": [3],
                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                },
                {
                    "meta": {
                        "category": None,
                        "model_name": "",
                        "system_name": "period_end_date",
                        "data_type_id": 2,
                    },
                    "name": "Period End Date",
                    "tags": None,
                    "type": "VARIABLE",
                    "data_type": "Date",
                },
            ]

    Equivalent Expression:
        period_start_date <= period_end_date

    Refer test case file test_advanced_filter.py for more samples.
    """
    infix_expression = transformation_dict["infix"]

    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    var_type = data_source.get_source_var_dtype()
    client_id = data_source.get_source_meta_details()["client_id"]

    cur_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query, key="advanced_filter"
    )
    logger.info(
        f"Invoking advanced filter with view {cur_view_id} - {client_id} -- {len(infix_expression)}"
    )

    # if var type is none select all columns from source
    if var_type:
        filter_select_columns = list(var_type.keys())
        filter_select_columns.append("row_key")
        filter_select_clause = f"{', '.join(filter_select_columns)}"
    else:
        filter_select_clause = "*"

    conditional_expression = InfixToSnowflakeQuery(
        infix_expression=infix_expression,
        typecast_operator="::",
        client_id=client_id,
        # If type is v2 all the comparison is case insensitive
        case_insensitive=v2_filter,
    ).convert()

    filter_query = f"""select {filter_select_clause}
        from {cur_view_id}
        where {conditional_expression}
        """

    logger.info(f"Filter Query for advanced filter is {filter_query}")
    return filter_query, var_type


def apply_advanced_filter_v2_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,
):
    return apply_advanced_filter_transform_snowflake(
        snowpark_session=snowpark_session,
        data_sources=data_sources,
        transformation_dict=transformation_dict,
        ever_comparison=ever_comparison,
        v2_filter=True,
    )


def apply_get_user_properties_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,  # pylint: disable=unused-argument
) -> tuple[str, dict]:
    """
    This transformation is used to get user properties from user report.

    Sample meta data for this transformation:
    {
        "key": "c1b454d9-5e9d-408c-9dda-f1b3401ef77a",
        "type": "GET_USER_PROPERTIES",
        "email_column": "co_6_deal_owner_email",
        "as_of_date_column": "co_6_deal_close_date",
        "user_properties": [
            {
                "data_type_id": 4,
                "user_property_system_name": "designation",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_designation",
            },
            {
                "data_type_id": 4,
                "user_property_system_name": "payout_frequency",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency",
            },
            {
                "data_type_id": 1,
                "user_property_system_name": "payee_variable_pay",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay",
            },
            {
                "data_type_id": 4,
                "user_property_system_name": "user_role",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_user_role",
            },
        ],
    }

    The join query looks something like this:
        select current_sheet.co_6_deal_id as co_6_deal_id,
        current_sheet.co_6_lead_id as co_6_lead_id,
        current_sheet.co_6_deal_name as co_6_deal_name,
        current_sheet.co_6_deal_amount as co_6_deal_amount,
        current_sheet.co_6_deal_status as co_6_deal_status,
        current_sheet.co_6_deal_creation_date as co_6_deal_creation_date,
        current_sheet.co_6_deal_close_date as co_6_deal_close_date,
        current_sheet.co_6_deal_owner_email as co_6_deal_owner_email,
        current_sheet.co_6_deal_priority as co_6_deal_priority,
        current_sheet.co_6_deal_state as co_6_deal_state,
        current_sheet.co_6_deal_probability as co_6_deal_probability,
        current_sheet.co_6_deal_region as co_6_deal_region,
        current_sheet.co_6_customer_satisfaction as co_6_customer_satisfaction,
        current_sheet.row_key as row_key,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_designation
        as up_co_6_deal_owner_email_co_6_deal_close_date_designation,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency
        as up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay
        as up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_user_role
        as up_co_6_deal_owner_email_co_6_deal_close_date_user_role
        from "EVERSTAGE_LOCAL_INDIA"."PUBLIC"."b53edb47-2cc7-4f2b-815d-fe12ad47017c" as current_sheet
        left join "EVERSTAGE_LOCAL_INDIA"."PUBLIC"."c2e09a0d-23b5-4369-bf12-a2f22c285698" as user_report
        on
        current_sheet.co_6_deal_owner_email = user_report.employee_email_id::string
        and current_sheet.co_6_deal_close_date >= user_report.effective_start_date::datetime
        and (
            current_sheet.co_6_deal_close_date <= user_report.effective_end_date::datetime
            or user_report.effective_end_date::datetime is null
        )
    """
    logger.info("Applying Get user property transform.")

    data_source = data_sources[0]
    data_query = data_source.get_kd_aware_rows_query()
    var_type = data_source.get_source_var_dtype()
    client_id = data_source.get_source_meta_details()["client_id"]

    lhs_alias = "current_sheet"
    rhs_alias = "user_report"

    lhs_columns = [f"{lhs_alias}.{col} as {col}" for col in var_type.keys()]
    lhs_columns.append(f"{lhs_alias}.row_key as row_key")

    transformation_meta = GetUserPropertiesTransformationMeta(**transformation_dict)
    email_column = transformation_meta.email_column
    as_of_date_column = transformation_meta.as_of_date_column
    user_properties = transformation_meta.user_properties

    # get the data type id to name map to resolve the snowflake data type
    data_type_id_name_map = get_datatype_id_name_map()

    # Default columns to be selected from user report
    rhs_raw_columns = [
        "data:employee_email_id::string as employee_email_id",
        "data:effective_start_date::datetime as effective_start_date",
        "data:effective_end_date::datetime as effective_end_date",
    ]
    rhs_columns = []
    resultant_var_type = var_type.copy()

    # iterate through user properties and construct the query
    for user_property in user_properties:
        user_property_name = user_property.user_property_system_name
        data_type_id = user_property.data_type_id
        output_column_name = user_property.output_variable_system_name

        data_type = data_type_id_name_map[data_type_id]
        snowflake_data_type = resolve_snowflake_data_type(data_type)
        rhs_raw_columns.append(
            f"data:{user_property_name}::{snowflake_data_type} as {output_column_name}"
        )

        rhs_columns.append(f"{rhs_alias}.{output_column_name} as {output_column_name}")
        resultant_var_type[output_column_name] = data_type

    rhs_raw_columns = ", ".join(rhs_raw_columns)
    table_name = get_system_object_data_table_name(
        client_id=client_id, object_id="user"
    )
    rhs_fetch_query = f"""
        select {rhs_raw_columns}
        from {table_name}
        where client_id = {client_id}
        and object_id = 'user'
        and knowledge_end_date is null
        and not is_deleted
    """

    lhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=data_query
    )

    rhs_view_id = create_or_replace_temp_view(
        snowpark_session=snowpark_session, data_query=rhs_fetch_query
    )

    lhs_as_of_date_column = f"{lhs_alias}.{as_of_date_column}::datetime"

    if as_of_date_column == GetUserPropertiesTransformationUtils.CURRENT_DATE_KEY.value:
        lhs_as_of_date_column = "current_timestamp()::datetime"

    join_condition = f"""
        {lhs_alias}.{email_column} = {rhs_alias}.employee_email_id::string
        and {lhs_as_of_date_column} >= {rhs_alias}.effective_start_date::datetime
        and (
            {lhs_as_of_date_column} <= {rhs_alias}.effective_end_date::datetime
            or {rhs_alias}.effective_end_date::datetime is null
        )
    """

    select_clause = ", ".join(lhs_columns + rhs_columns)

    join_query = f"""
        select {select_clause}
        from {lhs_view_id} as {lhs_alias}
        left join {rhs_view_id} as {rhs_alias}
        on {join_condition}
    """

    logger.info(f"Get user property join query is {join_query}")

    return join_query, resultant_var_type


def apply_temporal_splice_transform_snowflake(
    snowpark_session: Session,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison=False,  # pylint: disable=unused-argument
) -> tuple[str, dict]:
    """
    Temporal splice is a transformation that allows you to splice two time periods together.

    transformation_spec: {type: TEMPORAL_SPLICE, meta: [{
            "source_id": datasheet/report sheet/ custom object,
            "source_type": "",
            "email_id_column": "employee_email_id",
            "has_effective_dates":True/False,
            "start_date_column": "effective_st_date",
            "end_date_column": "effective_ed_date",
        }]}
    There are two types of temporal splice:
        1. Knowledge dated sources
        2. Non knowledge dated sources
        3. Combination of both

    Knowledge dated sources:
        A knowledge dated source is a source that has a start date and end date associated with it.
    Non knowledge dated sources:
        A non knowledge dated source is a source that does not have a start date and end date associated with it.


    case 1: multiple knowledge dated sources
       - if there are multiple knowledge dated sources, then create a temp table and fetch the data from all the sources
        and then apply the temporal splice transformation(udtf) on the temp table.
       - sample query:
            SELECT transformed_data as data from {dated_source_table_id}, table(temporal_splice_transformation
            (object_construct_keep_null('transformation_dict', parse_json('{json.dumps(meta_for_udtf)}'), 'variables',
            {list(knowledge_dated_source_var_type.keys())}),
            source_id, data::variant)
            over (partition by employee_email_id))

    case 2: multiple non knowledge dated sources
        - if there are multiple non knowledge dated sources, then iterate over each source and apply full outer join
        on the source and the transformed data from the previous source.
        - sample query:
            Select {lhs_cols}, {rhs_cols} from source_1 full outer join source_2 on source_1.employee_email_id = source_2.employee_email_id

    case 3: combination of both
        - if there are both knowledge dated sources and non knowledge dated sources, then apply the temporal splice(udtf) to
        the knowledge dated sources and then apply full outer join on the non knowledge dated sources. Finally apply full outer
        join on the transformed data from the knowledge dated sources and the non knowledge dated sources.


    In all the 3 cases 3 new columns are added to the transformed data and they are the row_key for the temporal splice:
        1. ts_employee_email_id
        2. ts_effective_start_date
        3. ts_effective_end_date

    Special handlings:
        - In case 2 and 3 we use coalesce function to get ts_employee_email_id
        - In temporal splice we add a prefix 'ts_{index of the source}_' to the column names of the transformed data
          to avoid column name conflicts.
    """

    logger.info("Applying temporal splice transform...")
    knowledge_dated_sources_query_list = []
    non_knowledge_dated_sources_query_list = []
    knowledge_dated_source_var_type = {}
    non_knowledge_dated_source_var_type = []
    non_dated_source_vars = {}

    kd_select_clause = None
    non_kd_query = None
    kd_query = None
    row_key_columns = {
        "ts_employee_email_id": "Email",
        "ts_effective_start_date": "Date",
        "ts_effective_end_date": "Date",
    }

    # In the result of udtf the data will be not be flatened and hence we construct
    # row_key and data_row_key
    row_key_cols = []
    data_row_key_cols = []
    for col_details in row_key_columns.keys():
        row_key_cols.append(f"""ifnull({col_details}::string, 'none')""")
        data_row_key_cols.append(f"""ifnull(data:"{col_details}"::string, 'none')""")
    row_key_cols = ", ".join(row_key_cols)
    data_row_key_cols = ", ".join(data_row_key_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"
    data_row_key = f"lower(concat_ws($$##::##$$, {data_row_key_cols}))"

    # Fetching data from all the sources and store in temp table
    for index, meta in enumerate(transformation_dict.get("meta", [])):
        source_table_id_field = None
        query = None
        source_type = data_sources[index].get_source_meta_details()["type"]
        # If source is effective dated, then fetch data from source and construct the fetch query and
        # add to knowledge_dated_sources_query_list
        if meta["has_effective_dates"]:
            source_table_id_field = (
                f"'{str(meta['source_id'])}_{index}'::STRING as source_id"
            )
            if source_type == DataSouceType.TRANSFORMATION.value:
                query = f"""
                select {source_table_id_field}, data:"{meta["email_id_column"][5:]}" as employee_email_id, data from
                {data_sources[index].get_source_table_name()} where knowledge_end_date is null
                and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            elif source_type == DataSouceType.DATASHEET.value:
                query = f"""
                select {source_table_id_field}, data:"{meta["email_id_column"][5:]}" as employee_email_id, data from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and data:"{meta["email_id_column"][5:]}"::STRING is not null and is_adjusted = false
                UNION
                select {source_table_id_field}, data:"{meta["email_id_column"][5:]}" as employee_email_id, data from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null AND is_adjusted = true AND adjustment_type = 'global' AND data IS NOT NULL
                and data:"{meta["email_id_column"][5:]}"::STRING is not null
                UNION
                select {source_table_id_field}, data_pure:"{meta["email_id_column"][5:]}" as employee_email_id, data_pure as data from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null AND is_adjusted = true AND adjustment_type = 'local' AND data_pure IS NOT NULL
                and data_pure:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            elif source_type == DataSouceType.CUSTOM_OBJECT.value:
                query = f"""
                select {source_table_id_field}, data:"{meta["email_id_column"][5:]}" as employee_email_id, data from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            else:
                query = f"""
                select {source_table_id_field}, data:"{meta["email_id_column"][5:]}" as employee_email_id, data from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            knowledge_dated_sources_query_list.append(query)
            var_types = data_sources[index].get_source_var_dtype()
            var_types = {
                "ts_" + str(index + 1) + "_" + key: value
                for key, value in var_types.items()
            }
            del var_types[meta["email_id_column"]]
            if meta["start_date_column"]:
                del var_types[meta["start_date_column"]]
            if meta["end_date_column"]:
                del var_types[meta["end_date_column"]]
            knowledge_dated_source_var_type.update(var_types)

        # If source is not effective dated, then fetch data from source and contruct the fetch query
        # and add to non knowledge dated sources
        else:
            select_clause_vars = []
            var_types = data_sources[index].get_source_var_dtype()
            for variable_name, data_type in var_types.items():
                snowflake_data_type = resolve_snowflake_data_type(data_type.upper())
                select_clause_vars.append(
                    f"{variable_name}::{snowflake_data_type} as ts_{str(index+1)}_{variable_name}"
                )

            data_variable_select_clause = ", ".join(
                list(
                    map(
                        lambda x: f"data:{x}",
                        select_clause_vars,
                    )
                )
            )

            data_pure_variable_select_clause = ", ".join(
                list(
                    map(
                        lambda x: f"data_pure:{x}",
                        select_clause_vars,
                    )
                )
            )

            if source_type == DataSouceType.TRANSFORMATION.value:
                query = f"""
                select {data_variable_select_clause}, data:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where knowledge_end_date is null
                and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            elif source_type == DataSouceType.DATASHEET.value:
                query = f"""
                select {data_variable_select_clause}, data:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and is_adjusted = false and data:"{meta["email_id_column"][5:]}"::STRING is not null
                UNION
                select {data_variable_select_clause}, data:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null AND is_adjusted = true AND adjustment_type = 'global' AND data IS NOT NULL
                and data:"{meta["email_id_column"][5:]}"::STRING is not null
                UNION
                select {data_pure_variable_select_clause}, data_pure:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null AND is_adjusted = true AND adjustment_type = 'local' AND data_pure IS NOT NULL
                and data_pure:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            elif source_type == DataSouceType.CUSTOM_OBJECT.value:
                query = f"""
                select {data_variable_select_clause}, data:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """
            else:
                query = f"""
                select {data_variable_select_clause}, data:{meta["email_id_column"][5:]}::STRING as ts_employee_email_id from
                {data_sources[index].get_source_table_name()} where {data_sources[index].get_source_identifer_clause()}
                and knowledge_end_date is null and data:"{meta["email_id_column"][5:]}"::STRING is not null
                """

            non_knowledge_dated_sources_query_list.append(query)
            var_types = data_sources[index].get_source_var_dtype()
            var_types = {
                "ts_" + str(index + 1) + "_" + key: value
                for key, value in var_types.items()
            }
            del var_types[meta["email_id_column"]]
            non_knowledge_dated_source_var_type.append(var_types)

    # If there are knowledge dated sources, then follow case 1
    # copy data from all soures and apply temporal splice udtf
    if len(knowledge_dated_sources_query_list) > 0:
        logger.info("Knowledge dated sources present")
        dated_source_table_id = create_or_replace_temp_table(
            snowpark_session=snowpark_session,
            data_query=knowledge_dated_sources_query_list[0],
        )

        for query in knowledge_dated_sources_query_list[1:]:
            insert_query = f"""
            INSERT INTO {dated_source_table_id} (SOURCE_ID, EMPLOYEE_EMAIL_ID, DATA) {query};
            """
            snowpark_session.sql(insert_query).collect()

        kd_select_clause = []

        for index, (variable, dtype) in enumerate(
            knowledge_dated_source_var_type.items()
        ):
            kd_select_clause.append(
                f"data:{variable}::{resolve_snowflake_data_type(dtype.upper())} as {variable}"
            )

        kd_select_clause = ", ".join(kd_select_clause)

        meta_for_udtf = []
        for index, meta in enumerate(transformation_dict.get("meta", [])):
            meta_for_udtf.append(
                {
                    "source_id": meta["source_id"],
                    "source_type": meta["source_type"],
                    "email_id_column": meta["email_id_column"],
                    "has_effective_dates": meta["has_effective_dates"],
                    "start_date_column": meta["start_date_column"],
                    "end_date_column": meta["end_date_column"],
                }
            )

        kd_query = f"""
        SELECT transformed_data as data from {dated_source_table_id}, table(temporal_splice_transformation
        (object_construct_keep_null('transformation_dict', parse_json('{json.dumps(meta_for_udtf)}'), 'variables', {list(knowledge_dated_source_var_type.keys())}),
        source_id, data::variant)
        over (partition by employee_email_id))
        """

        logger.info(f"Constructed knowledge dated sources query: {kd_query}")

    # If there are non knowledge dated sources, then follow case 2
    # copy data from all sources and apply full outer join on email id for each source iteratively
    if len(non_knowledge_dated_sources_query_list) > 0:
        logger.info("Non knowledge dated sources present")
        non_dated_join_table_id = create_or_replace_temp_table(
            snowpark_session=snowpark_session,
            data_query=f"""select * from ({non_knowledge_dated_sources_query_list[0]})""",
        )
        non_kd_query = non_knowledge_dated_sources_query_list[0]

        non_dated_source_vars.update(non_knowledge_dated_source_var_type[0])

        for index, _ in enumerate(non_knowledge_dated_sources_query_list[1:]):
            rhs_vars = {}
            non_dated_rhs_join_table_id = create_or_replace_temp_table(
                snowpark_session=snowpark_session,
                data_query=non_knowledge_dated_sources_query_list[index + 1],
            )
            rhs_vars = non_knowledge_dated_source_var_type[index + 1]
            rhs_cols = []
            lhs_cols = []
            for column in non_dated_source_vars.keys():
                lhs_cols.append(f"lhs.{column} as {column}")

            for column in rhs_vars.keys():
                rhs_cols.append(f"rhs.{column} as {column}")

            # if no column is selected of the other table, change it to empty string
            lhs_cols = ", ".join(lhs_cols)
            rhs_cols = ", ".join(rhs_cols)

            non_kd_query = """
            select COALESCE(lhs.ts_employee_email_id, rhs.ts_employee_email_id) AS ts_employee_email_id 
            """
            if lhs_cols != "":
                non_kd_query += f" , {lhs_cols} "

            if rhs_cols != "":
                non_kd_query += f" , {rhs_cols} "

            non_kd_query += f"""
            from {non_dated_join_table_id} as lhs full outer join {non_dated_rhs_join_table_id} as rhs on
            lhs.ts_employee_email_id = rhs.ts_employee_email_id 
            """

            non_dated_source_vars.update(rhs_vars)
            if (index + 1) < (len(non_knowledge_dated_sources_query_list) - 1):
                non_dated_join_table_id = create_or_replace_temp_table(
                    snowpark_session=snowpark_session,
                    data_query=non_kd_query,
                )
        logger.info(f"Constructed non knowledge dated sources query: {non_kd_query}")

    # If there are only knowledge dated sources, return the query and var types
    if (
        len(knowledge_dated_sources_query_list) > 0
        and len(non_knowledge_dated_sources_query_list) == 0
    ):
        splice_transformation_query = """
        SELECT data:ts_employee_email_id::STRING as ts_employee_email_id, data:ts_effective_start_date::DATETIME as ts_effective_start_date, 
        data:ts_effective_end_date::DATETIME as ts_effective_end_date 
        """
        if kd_select_clause != "":
            splice_transformation_query += f", {kd_select_clause} "
        splice_transformation_query += f", {data_row_key} as row_key from ({kd_query})"

        logger.info(f"Constructed splice query: {splice_transformation_query}")
        return splice_transformation_query, {
            **row_key_columns,
            **knowledge_dated_source_var_type,
        }

    # If there are only non knowledge dated sources, return the query and var types
    elif (
        len(non_knowledge_dated_sources_query_list) > 0
        and len(knowledge_dated_sources_query_list) == 0
    ):
        non_kd_select_clause = []
        for column, dtype in non_dated_source_vars.items():
            if column != "ts_employee_email_id":
                non_kd_select_clause.append(
                    f"{column}::{resolve_snowflake_data_type(dtype.upper())} as {column}"
                )
        non_kd_select_clause = ", ".join(non_kd_select_clause)
        splice_transformation_query = """
        SELECT ts_employee_email_id::STRING as ts_employee_email_id, null as ts_effective_start_date, null as ts_effective_end_date 
        """
        if non_kd_select_clause != "":
            splice_transformation_query += f", {non_kd_select_clause}"
        splice_transformation_query += f", {row_key} as row_key from ({non_kd_query})"
        logger.info(f"Constructed splice query: {splice_transformation_query}")
        return splice_transformation_query, {**row_key_columns, **non_dated_source_vars}

    # If there are both knowledge dated sources and non knowledge dated sources, then follow case 3
    else:
        kd_query = f"""
        SELECT data:ts_employee_email_id::STRING as ts_employee_email_id, data:ts_effective_start_date::DATETIME as ts_effective_start_date,
        data:ts_effective_end_date::DATETIME as ts_effective_end_date, {kd_select_clause} from ({kd_query})
        """
        kd_temp_table_id = create_or_replace_temp_table(
            snowpark_session=snowpark_session, data_query=kd_query
        )
        non_kd_temp_table_id = create_or_replace_temp_table(
            snowpark_session=snowpark_session, data_query=non_kd_query
        )

        lhs_cols = []
        rhs_cols = []

        for column, dtype in knowledge_dated_source_var_type.items():
            if column != "ts_employee_email_id":
                lhs_cols.append(
                    f"lhs.{column}::{resolve_snowflake_data_type(dtype.upper())} as {column}"
                )

        for column, dtype in non_dated_source_vars.items():
            if column != "ts_employee_email_id":
                rhs_cols.append(
                    f"rhs.{column}::{resolve_snowflake_data_type(dtype.upper())} as {column}"
                )

        lhs_cols = ", ".join(lhs_cols)
        rhs_cols = ", ".join(rhs_cols)

        splice_transformation_query = f"""
        select *, {row_key} as row_key from (
        select COALESCE(lhs.ts_employee_email_id, rhs.ts_employee_email_id) AS ts_employee_email_id,
        lhs.ts_effective_start_date as ts_effective_start_date, lhs.ts_effective_end_date as ts_effective_end_date
        """
        if lhs_cols != "":
            splice_transformation_query += f"""
            , {lhs_cols}
            """
        if rhs_cols != "":
            splice_transformation_query += f"""
            , {rhs_cols} 
            """
        splice_transformation_query += f"""
        from {kd_temp_table_id} as lhs 
        FULL OUTER JOIN {non_kd_temp_table_id} as rhs 
        on lhs.ts_employee_email_id = rhs.ts_employee_email_id)
        """
        logger.info(f"Constructed splice query: {splice_transformation_query}")

        return splice_transformation_query, {
            **row_key_columns,
            **knowledge_dated_source_var_type,
            **non_dated_source_vars,
        }


def _construct_data_fetch_query_from_source(
    source_table,
    var_dtype_map,
):
    """
    from the compressed variant 'data' column, construct query
    to read as expanded individual columns
    """

    variant_attributes = []
    for variable, data_type in var_dtype_map.items():
        snowflake_data_type = resolve_snowflake_data_type(data_type.upper())
        variant_attributes.append(
            f"data:{variable}::{snowflake_data_type} as {variable}"
        )

    variant_attributes = ", ".join(variant_attributes)
    fetch_query = f"""
        select {variant_attributes}, row_key
        from {source_table}
    """

    return fetch_query


def _create_intermediate_result_table(
    snowpark_session,
    transformed_data_query,
    inter_result_table,
    var_dtype_map,
    knowledge_date=None,
    additional_cols=None,
    soft_limit=None,
):
    """
    Store the result of intermediate transformations in a table of the form :-
    "inter_result_client_datasheet_transformation-key"
    """
    logger.info(f"Caching intermediate result in {inter_result_table}")

    if not knowledge_date:
        knowledge_date = datetime.now()

    additional_cols_caluse = ""
    if additional_cols:
        additional_cols_caluse = f", {', '.join(additional_cols)}"

    variant_attributes = compress_variant_attributes(var_dtype_map=var_dtype_map)
    snowflake_datetime = resolve_snowflake_data_type("DATE")
    client_softlimit_query = f"limit {soft_limit + 1}" if soft_limit is not None else ""

    create_query = f"""
    create or replace table {inter_result_table} as
    (
        select '{knowledge_date}'::{snowflake_datetime} as knowledge_begin_date,
                NULL::{snowflake_datetime} as knowledge_end_date,
                row_key,
                object_construct_keep_null(
                    {variant_attributes}
                ) as data
                {additional_cols_caluse}
        from
        (
            {transformed_data_query}
        )
        {client_softlimit_query}
    );
    """
    snowpark_session.sql(create_query).collect()


def construct_filter_condition(
    client_id,
    transformation_dict,
    var_type,
    client_fiscal_start_month=None,
    ever_comparison=False,
):
    """
    given a transformation dictionary, constructs the filter condition.
    used in both complete and incremental ETL
    """

    def _hierarchy_contains(text, pattern):
        return (
            f"CONTAINS(LOWER({text}::variant::object:repr::string), LOWER({pattern}))"
        )

    def _hierarchy_not_contains(text, pattern):
        return f"({text} is null OR NOT CONTAINS(LOWER({text}::variant::object:repr::string), LOWER({pattern})))"

    col_name = transformation_dict["col_name"]
    operator = transformation_dict["operator"]
    value = transformation_dict["value"] if "value" in transformation_dict else None
    # a value of 'False' gets treated as True (as it is a non-empty string)

    col_data_type = get_data_type_col(
        column=transformation_dict["col_name"],
        df_type=var_type,
    )

    if col_data_type == "boolean":
        if value == "True":
            value = True
        elif value == "False":
            value = False

    if isinstance(value, list):
        corrected_value = []
        for val in value:
            val_to_append = convert_to_python_datatype(str(val), col_data_type)
            if isinstance(val_to_append, str):
                val_to_append = val_to_append.lower()  # pylint: disable=no-member
                val_to_append = format_snowflake_sql(val_to_append)
            corrected_value.append(val_to_append)
        value = corrected_value
    else:
        value = convert_to_python_datatype(str(value), col_data_type)

    if isinstance(value, str):
        value = value.lower()
        value = format_snowflake_sql(value)

    if client_fiscal_start_month is None:
        client_fiscal_start_month = (
            get_client_fiscal_start_month(client_id) if client_id else None
        )

    # true if col is string
    string_type = (
        re.match(".*char.*|text|object|email|string", col_data_type) is not None
    )
    # true if col is of date type
    date_type = re.match(".*date.*|.*duration.*", col_data_type) is not None

    logger.info(
        f"""Filter col - {col_name},
        Type - {col_data_type},
        is_string type - {string_type},
        is_date type - {date_type}
        """
    )
    logger.info(f"Converted value - {value}")

    filter_expression = get_filter_expression(
        col_name,
        value,
        operator,
        datatype=col_data_type,
        ever_comparison=ever_comparison,
    )

    truncated_date_column = f"to_timestamp({col_name})"
    if date_type:
        truncated_date_column = f"date_trunc('second', to_timestamp({col_name}))"

    if operator == ">=":
        if string_type:
            where_clause = f" {col_name} is null or lower({col_name}) >= '{value}' "
        elif date_type:
            where_clause = f"{truncated_date_column} >= '{value}' "
        else:
            where_clause = f" {col_name} is null or {filter_expression} "

    elif operator == "<=":
        if string_type:
            where_clause = f" lower({col_name}) <= '{value}' "
        elif date_type:
            where_clause = f"{truncated_date_column} <= '{value}' "
        else:
            where_clause = f" {filter_expression}"

    elif operator == ">":
        if string_type:
            where_clause = f" {col_name} is null or lower({col_name}) > '{value}' "
        elif date_type:
            where_clause = f"{truncated_date_column} > '{value}' "
        else:
            where_clause = f" {col_name} is null or {filter_expression} "

    elif operator == "<":
        if string_type:
            where_clause = f" lower({col_name}) < '{value}' "
        elif date_type:
            where_clause = f"{truncated_date_column} < '{value}' "
        else:
            where_clause = f" {filter_expression}"

    elif operator == "!=":
        where_clause = f" {col_name} is null or "
        if string_type:
            where_clause += f"lower({col_name})!= '{value}' "
        elif date_type:
            where_clause += f"{truncated_date_column} != '{value}' "
        else:
            where_clause += f"{filter_expression}"

    elif operator == "==":
        if string_type:
            where_clause = f"lower({col_name}) = '{value}' "
        elif date_type:
            where_clause = f"{truncated_date_column} = '{value}' "
        else:
            where_clause = f"{filter_expression}"

    elif operator == "STARTS_WITH":
        where_clause = f"startswith(lower({col_name}), '{value}')"

    elif operator == "ENDS_WITH":
        where_clause = f"endswith(lower({col_name}), '{value}')"

    elif operator == "CONTAINS":
        if col_data_type.lower() == "hierarchy":
            where_clause = _hierarchy_contains(col_name, f"'{value}'")
        else:
            where_clause = f"contains(lower({col_name}), '{value}')"

    elif operator == "DOES_NOT_CONTAINS":
        if col_data_type.lower() == "hierarchy":
            where_clause = _hierarchy_not_contains(col_name, f"'{value}'")
        else:
            where_clause = (
                f"{col_name} is null or lower({col_name}) not like '%{value}%'"
            )

    elif operator == "IS_NOT_EMPTY":
        where_clause = f"{col_name} is not null and {col_name}::STRING not in ('None', 'nan', '', 'none')"

    elif operator == "IS_EMPTY":
        where_clause = (
            f"{col_name} is null or {col_name}::STRING in ('None', 'nan', '', 'none')"
        )

    elif operator == "IN":
        if type(value) not in (list, tuple):
            raise InvalidDataTypeError(
                "Data type {} of {} is not supported!".format(type(value), value)
            )

        if string_type:
            value = ", ".join(list(map(lambda x: f"'{x}'", value)))
            where_clause = f"""lower({col_name}) in ({value})"""
        else:
            # pylint: disable-msg=unnecessary-lambda
            value = ", ".join(list(map(lambda x: str(x), value)))
            # pylint: enable-msg=unnecessary-lambda
            where_clause = f"""{col_name} in ({value})"""

    elif operator == "NOTIN":
        where_clause = f" {col_name} is null or "
        if type(value) not in (list, tuple):
            raise InvalidDataTypeError(
                "Data type {} of {} is not supported!".format(type(value), value)
            )

        if string_type:
            value = ", ".join(list(map(lambda x: f"'{x}'", value)))
            where_clause += f"""lower({col_name}) not in ({value})"""
        else:
            # pylint: disable-msg=unnecessary-lambda
            value = ",".join(list(map(lambda x: str(x), value)))
            # pylint: enable-msg=unnecessary-lambda
            where_clause += f"""{col_name} not in ({value})"""

    elif operator == "IS_IN_CURRENT_MONTH":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "monthly", False
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict['start_date']}'
        and {truncated_date_column} <= '{period_dict["end_date"]}'
        """

    elif operator == "IS_IN_LAST_MONTH":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "monthly", True
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["prev_start_date"]}'
        and {truncated_date_column} <= '{period_dict["prev_end_date"]}'
        """

    elif operator == "IS_IN_CURRENT_FISCAL_QUARTER":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "quarterly", False
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["start_date"]}'
        and {truncated_date_column} <= '{period_dict["end_date"]}'
        """

    elif operator == "IS_IN_LAST_FISCAL_QUARTER":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "quarterly", True
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["prev_start_date"]}'
        and {truncated_date_column} <= '{period_dict["prev_end_date"]}'
        """

    elif operator == "IS_IN_CURRENT_FISCAL_YEAR":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "annual", False
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["start_date"]}'
        and {truncated_date_column} <= '{period_dict["end_date"]}'
        """

    elif operator == "IS_IN_LAST_FISCAL_YEAR":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "annual", True
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["prev_start_date"]}'
        and {truncated_date_column} <= '{period_dict["prev_end_date"]}'
        """

    elif operator == "IS_IN_CURRENT_FISCAL_HALFYEAR":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "halfyearly", False
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["start_date"]}'
        and {truncated_date_column} <= '{period_dict["end_date"]}'
        """

    elif operator == "IS_IN_LAST_FISCAL_HALFYEAR":
        curr_time = datetime.now()
        period_dict = get_period_start_and_end_date(
            curr_time, client_fiscal_start_month, "halfyearly", True
        )

        where_clause = f"""{truncated_date_column} >= '{period_dict["prev_start_date"]}'
        and {truncated_date_column} <= '{period_dict["prev_end_date"]}'
        """

    else:
        where_clause = ""
        raise InvalidOperatorError(f"Operator {operator} is not supported!")

    return where_clause
