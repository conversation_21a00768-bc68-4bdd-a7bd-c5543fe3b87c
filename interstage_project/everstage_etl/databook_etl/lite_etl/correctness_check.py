import json
import logging
import time

from django.db import connection

from commission_engine.accessors.etl_housekeeping_accessor import (
    DatabookETLStatusReaderAccessor,
)
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.database.snowflake_dry_run_connection import (
    get_dry_run_connection,
)
from commission_engine.utils.databook_utils import (
    get_datasheet_transformation_result_table_name,
)
from commission_engine.utils.general_data import (
    ETL_STATUS,
    varDatatypeSnowflakeDatatypeMap,
)
from everstage_etl.databook_etl.datasheet_etl_utils import convert_fetch_query_to_df
from everstage_etl.databook_etl.lite_etl.utils import get_lite_etl_result_table_name

logger = logging.getLogger(__name__)

sf_dtype_to_es_dtype_map = {
    "FLOAT": "INTEGER",
    "NUMBER": "INTEGER",
    "TIMESTAMP_NTZ": "DATE",
    "BOOLEAN": "BOOLEAN",
    "VARCHAR": "STRING",
}


def get_transformation_spec(client_id: int, datasheet_id: str) -> list[dict]:
    query = """
    SELECT transformation_spec FROM datasheet
    WHERE client_id = %s
    AND datasheet_id = %s
    AND not is_deleted
    AND knowledge_end_date is null
    """
    cursor = connection.cursor()
    cursor.execute(query, (client_id, datasheet_id))
    res = cursor.fetchall()
    return json.loads(res[0][0])


def check_row_count(session, query1, query2) -> bool:
    row_count_q1 = session.sql(f"SELECT COUNT(*) FROM ({query1})").collect()[0][0]
    row_count_q2 = session.sql(f"SELECT COUNT(*) FROM ({query2})").collect()[0][0]

    row_key_count_q1 = session.sql(
        f"SELECT COUNT(DISTINCT row_key) FROM ({query1})"
    ).collect()[0][0]
    row_key_count_q2 = session.sql(
        f"SELECT COUNT(DISTINCT row_key) FROM ({query2})"
    ).collect()[0][0]

    if row_count_q1 != row_count_q2:
        logger.info("Row counts are not equal")
        return False

    if not (row_count_q1 == row_count_q2 == row_key_count_q1 == row_key_count_q2):
        logger.info(
            "Mismatch found: row_count_q1=%s, row_count_q2=%s, row_key_count_q1=%s, row_key_count_q2=%s",
            row_count_q1,
            row_count_q2,
            row_key_count_q1,
            row_key_count_q2,
        )
        return False

    return True


def check_row_keys(session, query1, query2) -> bool:
    diff_query = f"""
        (SELECT row_key FROM ({query1}) EXCEPT SELECT row_key FROM ({query2}))
        UNION ALL
        (SELECT row_key FROM ({query2}) EXCEPT SELECT row_key FROM ({query1}))
    """
    diff_df = session.sql(diff_query)
    if diff_df.count() > 0:
        diff_df.show(n=5)
        return False
    return True


def check_tables_data(session, query1, query2) -> bool:
    diff_query = f"""
        (SELECT * FROM ({query1}) EXCEPT SELECT * FROM ({query2}))
        UNION ALL
        (SELECT * FROM ({query2}) EXCEPT SELECT * FROM ({query1}))
    """

    diff_df = session.sql(diff_query)

    if diff_df.count() > 0:
        diff_df.show(n=5)
        return False

    return True


def es_dtype_from_sf_dtype(sf_dtype: str) -> str:
    for key, value in sf_dtype_to_es_dtype_map.items():
        if key in sf_dtype:
            return value

    logger.info("[LITE-ETL-CORRECTNESS-CHECK] Unknown SF data type: %s", sf_dtype)
    return sf_dtype


def get_lite_etl_table_var_dtype_map(session, table_name) -> dict:
    query = f"DESCRIBE TABLE {table_name}"
    df = session.sql(query, [table_name]).collect()
    var_dtype_map_sf = {row[0]: row[1] for row in df}
    var_dtype_map_es = {
        col_name.lower(): es_dtype_from_sf_dtype(sf_data_type)
        for col_name, sf_data_type in var_dtype_map_sf.items()
    }
    var_dtype_map_es.pop("row_key", None)
    var_dtype_map_es.pop("cnt", None)
    return var_dtype_map_es


def check_tables_truncated(
    session, lite_etl_table, normal_etl_table, transformed_var_dtype_map
) -> bool:
    # Build select clauses for both queries
    lite_cols = ["row_key"]
    normal_cols = ["row_key"]

    for col, dtype in transformed_var_dtype_map.items():
        sf_dtype = varDatatypeSnowflakeDatatypeMap[dtype.upper()]
        if dtype.upper() == "INTEGER":
            lite_cols.append(f"TO_DECIMAL({col}, 38, 6) AS {col}")
            normal_cols.append(f"TO_DECIMAL(data:{col}, 38, 6)::{sf_dtype} AS {col}")
        elif dtype.upper() == "HIERARCHY":
            lite_cols.append(f"PARSE_JSON({col}) AS {col}")
            normal_cols.append(f"PARSE_JSON(data:{col}) AS {col}")
        else:
            lite_cols.append(col)
            normal_cols.append(f"data:{col}::{sf_dtype} AS {col}")

    lite_query = f"SELECT {', '.join(lite_cols)} FROM {lite_etl_table}"
    normal_query = f"SELECT {', '.join(normal_cols)} FROM {normal_etl_table}"

    logger.info("Lite table query (truncated) %s", lite_query)
    logger.info("Normal table query (truncated) %s", normal_query)

    return check_tables_data(session, normal_query, lite_query)


def check_datasheet_for_lite_etl_correctness(
    client_id: int,
    datasheet_id: str,
    var_dtype_map: dict | None,
) -> bool | None:
    """
    Returns:
    True if correctness check succeeded
    None if there are no transformations
    False if correctness check failed
    """
    spec = get_transformation_spec(client_id, datasheet_id)

    if len(spec) == 0:
        return None

    last_key = spec[-1]["key"]
    normal_etl_table = get_datasheet_transformation_result_table_name(
        client_id, datasheet_id, last_key
    )
    lite_etl_table = get_lite_etl_result_table_name(client_id, datasheet_id)

    dry_run_connection = get_dry_run_connection(client_id=client_id)
    with create_snowpark_session_wrapper(
        client_id=client_id, connection=dry_run_connection
    ) as session:
        if var_dtype_map is None:
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK] Fetching column information from Snowflake"
            )
            var_dtype_map = get_lite_etl_table_var_dtype_map(session, lite_etl_table)

        normal_table_query = convert_fetch_query_to_df(
            {}, normal_etl_table, source_vars_expanded=False
        )

        lite_table_query = convert_fetch_query_to_df(
            var_dtype_map, lite_etl_table, source_vars_expanded=True
        )

        logger.info("Lite table query %s", lite_table_query)
        logger.info("Normal table query %s", normal_table_query)

        if not check_row_count(session, normal_table_query, lite_table_query):
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK %s] Row count check failed", datasheet_id
            )
            return False

        if not check_row_keys(session, normal_table_query, lite_table_query):
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK %s] Row keys check failed", datasheet_id
            )
            return False

        if not check_tables_data(session, normal_table_query, lite_table_query):
            # fallback to approximate check
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK] Data check failed, trying a truncated check"
            )
            if not check_tables_truncated(
                session, lite_etl_table, normal_etl_table, var_dtype_map
            ):
                logger.info(
                    "[LITE-ETL-CORRECTNESS-CHECK %s] Truncated data check failed",
                    datasheet_id,
                )
                return False

            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK %s] Truncated data check passed",
                datasheet_id,
            )

        return True


def get_latest_datasheet_sync_status(
    client_id: int, databook_id: str, datasheet_id: str
) -> str:
    databook_etl_status_reader_accessor = DatabookETLStatusReaderAccessor(client_id)

    latest_sync = databook_etl_status_reader_accessor.get_last_n_sync_for_datasheet(
        databook_id=databook_id, datasheet_id=datasheet_id, n=1
    )[0]
    return latest_sync["sync_status"]


def wait_for_sync_to_complete_and_get_status(
    client_id: int, databook_id: str, datasheet_id: str
) -> bool:
    # Wait in intervals of 30 seconds up to 20 minutes
    wait_count = 0
    wait_limit = 40
    wait_duration = 30
    while True:
        latest_sync_status = get_latest_datasheet_sync_status(
            client_id, databook_id, datasheet_id
        )
        if latest_sync_status == ETL_STATUS.COMPLETE.value:
            return True
        if latest_sync_status in (
            ETL_STATUS.STARTED.value,
            ETL_STATUS.LOCKED.value,
            ETL_STATUS.IN_PROGRESS.value,
        ):
            if wait_count == wait_limit:
                logger.info(
                    "[LITE-ETL-CORRECTNESS-CHECK-RESULT %s %s] CHECK_SKIPPED_TIMEOUT",
                    client_id,
                    datasheet_id,
                )
                return False
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK %s] Waiting for sync to complete, Count: %s",
                datasheet_id,
                wait_count,
            )
            time.sleep(wait_duration)
            wait_count += 1
        elif latest_sync_status == ETL_STATUS.FAILED.value:
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK-RESULT %s %s] CHECK_SKIPPED_SYNC_FAILED",
                client_id,
                datasheet_id,
            )
            return False
        else:
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK %s] Skipping since latest sync has status %s",
                datasheet_id,
                latest_sync_status,
            )
            return False


def wait_and_check_correctness(
    client_id: int,
    databook_id: str,
    datasheet_id: str,
    transformed_var_dtype_map: dict | None,
):
    # Wait for datasheet_etl_status to complete
    should_check_correctness = wait_for_sync_to_complete_and_get_status(
        client_id, databook_id, datasheet_id
    )

    if should_check_correctness:
        result = check_datasheet_for_lite_etl_correctness(
            client_id, datasheet_id, transformed_var_dtype_map
        )

        if result is True:
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK-RESULT %s %s] CHECK_PASSED",
                client_id,
                datasheet_id,
            )
        elif result is False:
            logger.exception(
                "[LITE-ETL-CORRECTNESS-CHECK-RESULT %s %s] CHECK_FAILED",
                client_id,
                datasheet_id,
            )
        elif result is None:
            logger.info(
                "[LITE-ETL-CORRECTNESS-CHECK-RESULT %s %s] CHECK_SKIPPED_NO_TRANSFORMATIONS",
                client_id,
                datasheet_id,
            )
