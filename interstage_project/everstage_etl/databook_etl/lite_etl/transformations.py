import logging
from datetime import datetime

from snowflake.snowpark import Session

from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_datatype_id_name_map,
)
from commission_engine.services.expression_designer.expression_converter import (
    convert_filter_transform_spec,
)
from commission_engine.utils.databook_utils import resolve_snowflake_data_type
from commission_engine.utils.general_utils import log_time_taken
from commission_engine.utils.report_utils import get_system_object_data_table_name
from commission_engine.utils.transformation_models import (
    GetUserPropertiesTransformationMeta,
)
from common.utils.infix_utils import InfixToSnowflakeQuery
from everstage_etl.databook_etl.data_sources import (
    DataSource,
    FlatDataSource,
    get_flat_source_based_on_type,
    get_source_based_on_type,
)
from everstage_etl.databook_etl.datasheet_etl_circuit_breaker import circuit_breaker
from everstage_etl.databook_etl.datasheet_etl_utils import (
    DataSouceType,
    TransformationType,
    cache_transformed_result,
    convert_fetch_query_to_df,
    multiple_source_transformation_registry,
)
from everstage_etl.databook_etl.lite_etl.hierarchy import process_hierarchy_columns
from everstage_etl.databook_etl.lite_etl.query_builder import (
    SnowflakeLiteEtlQueryBuilder,
)
from everstage_etl.databook_etl.lite_etl.utils import (
    flatten_data_sources,
    persist_transformed_result_lite_etl,
)
from spm.utils import GetUserPropertiesTransformationUtils

logger = logging.getLogger(__name__)


@log_time_taken()
def apply_lite_transforms_snowflake(
    snowpark_session: Session,
    client_id: int,
    datasheet_details: dict,
    databook_id: str,
    knowledge_date: datetime,
    *,
    correctness_check_mode: bool = False,
):
    """
    Apply all the transformations of the datasheet using manually
    constructed snowflake queries
    Construct a single combined query instead of using views

    Returns snowflake.snowpark.DataFrame when correctness_check is False
    Returns tuple[snowflake.snowpark.DataFrame, str] when correctness_check is True
    """
    datasheet_id: str = datasheet_details["datasheet_id"]
    source_type: str = datasheet_details["source_type"]
    source_id: int | str = datasheet_details["source_id"]
    transformation_spec: list = datasheet_details["transformation_spec"]
    sf_query_builder = SnowflakeLiteEtlQueryBuilder(client_id, datasheet_id)

    # When the `databook_expressionbox_version` client_feature flag is set to "v2", it implies that
    # the filter expressions are provided in the V2 infix expression format. The V2 format will have
    # different structures compared to the V1 format.

    # This section of the code serves the purpose of transforming and adapting the filter expression
    # from V2 to V1 format, ensuring compatibility with the rest of the system or downstream processes
    # that expect the filter expressions in the V1 format. If the expression is already in the V1 format,
    # we will ignore the conversion.
    transformation_spec = convert_filter_transform_spec(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        transformation_spec=transformation_spec,
    )

    # construct the FlatDataSource object for the sheet's source

    if len(transformation_spec) == 0:
        # TODO: change this to use a flat source when variant is deprecated
        source_data_obj = get_source_based_on_type(
            client_id=client_id,
            source_type=source_type,
            source_id=source_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            last_generated_time=knowledge_date,
            knowledge_date=knowledge_date,
        )
        logger.debug(
            "Query to fetch data from datasheet source %s",
            source_data_obj.get_kd_aware_rows_query(),
        )

        inter_result_table_name = cache_transformed_result(
            snowpark_session=snowpark_session,
            transformed_data_query=source_data_obj.get_kd_aware_rows_query(),
            prefix="inter_transformation_lite",
        )
        # when there is no transformation, the source is the transformation result
        transformation_result_obj = source_data_obj
    else:
        source_data_obj = get_flat_source_based_on_type(
            client_id=client_id,
            source_type=source_type,
            source_id=source_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            last_generated_time=knowledge_date,
            knowledge_date=knowledge_date,
        )

        # Temporary workaround
        # Report enrichment columns are missing from the flat versions of commission and inter-commission reports
        sources_to_flatten = (
            {
                get_source_based_on_type(
                    client_id=client_id,
                    source_type=source_type,
                    source_id=source_id,
                    databook_id=databook_id,
                    datasheet_id=datasheet_id,
                    last_generated_time=knowledge_date,
                    knowledge_date=knowledge_date,
                )
            }
            if source_type == DataSouceType.REPORT_OBJECT.value
            else set()
        )
        flatten_data_sources(snowpark_session, client_id, sources_to_flatten)

        data_sources = [source_data_obj]
        transformation_cte_name = ""

        for transformation_dict in transformation_spec:
            logger.info(f"Running for transformation dict - {transformation_dict}")
            # if transformation is join/union, construct fetch query for the right datasheet
            if (
                transformation_dict.get("type")
                in multiple_source_transformation_registry
            ):
                if (
                    transformation_dict.get("type")
                    == TransformationType.TEMPORAL_SPLICE.value
                ):
                    for index, source in enumerate(transformation_dict.get("meta")):
                        if index == 0:
                            continue
                        data_sources.append(
                            get_flat_source_based_on_type(
                                client_id=client_id,
                                source_type=source.get("source_type"),
                                source_id=source.get("source_id"),
                                databook_id=databook_id,
                                datasheet_id=datasheet_id,
                                last_generated_time=knowledge_date,
                                knowledge_date=knowledge_date,
                            )
                        )
                else:
                    data_sources.append(
                        get_flat_source_based_on_type(
                            client_id=client_id,
                            source_type=DataSouceType.DATASHEET.value,
                            source_id=transformation_dict.get("with"),
                            databook_id=databook_id,
                            datasheet_id=datasheet_id,
                            last_generated_time=knowledge_date,
                            knowledge_date=knowledge_date,
                        )
                    )

            # query to apply transformations
            transformed_data_query, transformed_var_dtype_map = apply_lite_transform(
                sf_query_builder=sf_query_builder,
                data_sources=data_sources,
                transformation_dict=transformation_dict,
            )

            transformation_cte_name = sf_query_builder.add_query_as_cte(
                transformed_data_query
            )

            # cache the result of intermediate transformations in a temp table
            # the previous transformation is not re evaluated becuase of lazy evaluation

            # construct the data source for encapsulating the result of the transformation
            # a single transformation spec can have multiple transformation dict
            transformation_result_obj = get_flat_source_based_on_type(
                client_id=client_id,
                source_type=DataSouceType.CTE.value,
                source_id=transformation_cte_name,
                databook_id=databook_id,
                datasheet_id=datasheet_id,
                last_generated_time=knowledge_date,
                knowledge_date=knowledge_date,
            )
            transformation_result_obj.set_source_var_type(
                var_dtype_map=transformed_var_dtype_map
            )

            data_sources = [transformation_result_obj]

        # query to unpivot sheet variable columns into a JSON
        final_query = sf_query_builder.generate_query(
            final_query=f"SELECT * FROM {transformation_cte_name}"
        )
        # TODO change to debug
        logger.info("LITE_ETL final query %s", final_query)

        inter_result_table_name = persist_transformed_result_lite_etl(
            snowpark_session=snowpark_session,
            transformed_data_query=final_query,
            client_id=client_id,
            datasheet_id=datasheet_id,
            temporary=False,  # remove after correctness check is complete
        )

        process_hierarchy_columns(
            snowpark_session=snowpark_session,
            transformed_var_dtype_map=transformed_var_dtype_map,
            table_name=inter_result_table_name,
        )

    transformed_var_dtype_map = transformation_result_obj.get_source_var_dtype()

    # always expand source vars
    # the flow with transformations will also create a flat table in Lite ETL
    result_dataframe_query = convert_fetch_query_to_df(
        var_dtype_map=transformed_var_dtype_map,
        cache_table=inter_result_table_name,
        source_vars_expanded=True,
    )

    circuit_breaker(
        client_id=client_id,
        datasheet_id=datasheet_id,
        query=result_dataframe_query,
        snowpark_session=snowpark_session,
    )

    result_dataframe = snowpark_session.sql(result_dataframe_query)

    if correctness_check_mode:
        return result_dataframe, transformed_var_dtype_map

    return result_dataframe


def apply_lite_transform(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[DataSource],
    transformation_dict: dict,
    ever_comparison: bool = False,
) -> tuple[str, dict]:
    """
    Returns the transformed_df
    """
    transformation_handler = choose_transformation_handler(transformation_dict["type"])
    transformed_data, var_dtype_map = transformation_handler(
        sf_query_builder,
        data_sources,
        transformation_dict,
        ever_comparison=ever_comparison,
    )
    return transformed_data, var_dtype_map


def choose_transformation_handler(transformation: str):
    """
    Given a transformation type, the method returns the
    function that applies the transformation
    """
    transformations = {
        # "SORT": apply_sort_transform_snowflake,
        "GROUP_BY": apply_group_by_transform_snowflake_lite,
        "JOIN": apply_join_transform_snowflake_lite,
        "UNION": apply_union_transform_snowflake_lite,
        # "FILTER": apply_filter_transform_snowflake,
        "ADVANCED_FILTER": apply_advanced_filter_transform_snowflake_lite,
        "ADVANCED_FILTER_V2": apply_advanced_filter_v2_transform_snowflake_lite,
        # "FLATTEN": apply_flatten_transform_snowflake,
        # "TEMPORAL_SPLICE": apply_temporal_splice_transform_snowflake,
        "GET_USER_PROPERTIES": apply_get_user_properties_transform_snowflake_lite,
    }

    return transformations[transformation]


def apply_group_by_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Given the query to fetch the source data returns query
    to apply group by transformation on it.
    Transformation spec:
    {
        type: "GROUP_BY",
        by: ["col_1", "col_2", "col_3"],
        aggregations: [{function: "SUM", of: "col_1", col_name: "sum_col_1"},
                        {function: "MIN", of: "col_1", col_name: "min_col_1"},
                        {function: "COUNT_DISTINCT", of: "col_1", col_name: "count_dist_col_1"}]
    }
    """
    logger.info("BEGIN: Applying group by transform")

    group_by_cols = ", ".join(transformation_dict["by"])
    # row key is the concatenation of columns to be grouped by. if any col is null, return as empty string
    row_key_cols = ", ".join(
        list(map(lambda x: f"ifnull({x}::string, 'none')", transformation_dict["by"]))
    )

    data_source = data_sources[0]
    data_query = data_source.get_all_rows_query()
    source_var_dtype = data_source.get_source_var_dtype()

    cur_view_id = sf_query_builder.add_query_as_cte(data_query)

    grouped_df_var_type = {}
    for variable in transformation_dict["by"]:
        grouped_df_var_type[variable] = source_var_dtype.get(variable, "String")  # type: ignore

    select_cols = list(transformation_dict["by"])
    if "aggregations" in transformation_dict:
        for agg_details in transformation_dict["aggregations"]:
            if agg_details["function"] != "COUNT_DISTINCT":
                # if the aggregation is "sum" or count, the default value is 0 instead of null
                if agg_details["function"] not in ["MIN", "MAX"]:
                    # if value of column is null, change it to 0 in case of numeric summary functions
                    select_cols.append(
                        f"""
                        ifnull( {agg_details["function"]} ({agg_details["of"]}) ,0)
                        as {agg_details["col_name"]}
                    """
                    )
                    grouped_df_var_type[agg_details["col_name"]] = "Integer"
                else:
                    select_cols.append(
                        f"""
                        {agg_details["function"]} ({agg_details["of"]})
                        as {agg_details["col_name"]}
                    """
                    )
                    grouped_df_var_type[agg_details["col_name"]] = source_var_dtype.get(
                        agg_details["of"], "String"
                    )  # type: ignore
            else:
                select_cols.append(
                    f"""
                    COUNT( distinct( {agg_details["of"]} ) )
                    as {agg_details["col_name"]}
                """
                )
                grouped_df_var_type[agg_details["col_name"]] = "Integer"

    select_cols = ", ".join(select_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"
    group_by_query = f"""select {select_cols}, {row_key} as row_key
    from {cur_view_id}
    group by {group_by_cols}
    """

    logger.info(f"Group by transformation - {group_by_query}")
    return group_by_query, grouped_df_var_type


def apply_join_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    """
    Given queries to fetch the left and right datasheet,
    returns query to join the two datasheets
    Transformation spec:
    {
        type: "JOIN",
        join_type: "INNER/LEFT/RIGHT/FULL",
        with: "rhs_worksheet_id",
        on: {lhs:["lhs_col_1", "lhs_col_4"], rhs:["rhs_col_2", "rhs_col_5"]},
        columns: [lhs_col_1, rhs_col_2, lhs_col_3]
    }
    """
    logger.info("BEGIN: Applying join transform.")

    lhs_data_source = data_sources[0]
    lhs_data_query = lhs_data_source.get_all_rows_query()
    lhs_var_type = lhs_data_source.get_source_var_dtype()

    rhs_data_source = data_sources[1]
    rhs_data_query = rhs_data_source.get_all_rows_query()
    rhs_var_type = rhs_data_source.get_source_var_dtype()

    # iterate through lhs var type and rhs var type
    joined_df_var_type = {}
    for column in transformation_dict["columns"]:
        if column[0:3] == "rhs":
            joined_df_var_type[column] = rhs_var_type.get(column[4:], "String")  # type: ignore
        else:
            joined_df_var_type[column] = lhs_var_type.get(column[4:], "String")  # type: ignore

    join_type_map = {
        "INNER": "inner",
        "LEFT": "left",
        "RIGHT": "right",
        "FULL": "full outer",
    }

    spec_join_type = transformation_dict["join_type"]
    join_type = join_type_map[spec_join_type]

    # column from left df to join on
    left_on_cols = list(map(lambda x: x[4:], transformation_dict["on"]["lhs"]))
    # column from right df to join on
    right_on_cols = list(map(lambda x: x[4:], transformation_dict["on"]["rhs"]))

    join_condition = []
    for i, _ in enumerate(left_on_cols):
        # data types of left and right columns
        left_col_dtype, right_col_dtype = "String", "String"
        if lhs_var_type:
            left_col_dtype = lhs_var_type[left_on_cols[i]]
        if rhs_var_type:
            right_col_dtype = rhs_var_type[right_on_cols[i]]

        if (
            ever_comparison
            and left_col_dtype == "Integer"
            and right_col_dtype == "Integer"
        ):
            join_condition.append(
                f"udf_ever_comparison(ifnull(lhs.{left_on_cols[i]}, parse_json('NULL')), '==',  ifnull(rhs.{right_on_cols[i]}, parse_json('NULL')))"
            )
        elif left_col_dtype.lower() == "date" and right_col_dtype.lower() == "date":
            join_condition.append(
                f"""
                    ifnull(date_trunc('second', lhs.{left_on_cols[i]}::datetime)::variant, parse_json('NULL')) = ifnull(date_trunc('second', rhs.{right_on_cols[i]}::datetime)::variant, parse_json('NULL'))
                """
            )
        else:
            join_condition.append(
                f"ifnull(lhs.{left_on_cols[i]}::variant, parse_json('NULL')) = ifnull(rhs.{right_on_cols[i]}::variant, parse_json('NULL'))"
            )

    join_condition = " and ".join(join_condition)

    # store the dataframe in a temp view to query the semi-structured "data" column
    # adding a limit clause here for a snowflake sql bug -

    # TODO: why was count used here
    ds_lhs = f"""select * from ({lhs_data_query})"""
    lhs_view_id = sf_query_builder.add_query_as_cte(ds_lhs)

    ds_rhs = f"""select * from ({rhs_data_query})"""
    rhs_view_id = sf_query_builder.add_query_as_cte(ds_rhs)

    # columns to select from join result
    lhs_cols = []
    rhs_cols = []

    # constructing the columns to select from both the datasheets
    for column in transformation_dict["columns"]:
        if column[0:3] == "rhs":
            rhs_cols.append(f"rhs.{column[4:]} as {column}")
        else:
            lhs_cols.append(f"lhs.{column[4:]} as {column}")

    # if no column is selected of the other table, change it to empty string
    lhs_cols = ", ".join(lhs_cols)
    if len(lhs_cols) > 0:
        lhs_cols += ", "
    rhs_cols = ", ".join(rhs_cols)
    if len(rhs_cols) > 0:
        rhs_cols += ", "

    # row_key to be added as an attribute inside the data col and as a separate column
    row_key = "lower(TO_VARCHAR(concat(ifnull(lhs.row_key::string,'none'), $$##::##$$, ifnull(rhs.row_key::string,'none'))))"
    join_query = f"""select {lhs_cols} {rhs_cols} {row_key} as row_key
    from {lhs_view_id} as lhs {join_type} join {rhs_view_id} as rhs
    on {join_condition}"""

    logger.info(f"Join query - {join_query}")

    return join_query, joined_df_var_type


def apply_union_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Given queries to fetch the left and right datasheet,
    returns query to join the two datasheets
    Transformation spec:
    {
        type: "UNION",
        union_type: "ALL/DISTINCT",
        with: "rhs_worksheet_id",
        on: [{lhs: "col_1", rhs: "col_2", col_name: "col_1"}, {lhs: "col_6", rhs: "col_12", col_name: "col_6"}]
    }
    To keep track of count of rows from both sources that have a value
    for the union columns, union is implemented with group by and full outer join.

    1. First apply group by on left and right source rows on union columns and take the count.
    2. Then do an outer join on both these results on the union columns.
    3. The add the count of rows from left and right source

    Example union query formed for union on col1 and col2 :-
        select  ifnull(lhs.col1, rhs.col1) as c1,
                ifnull(lhs.col2, rhs.col2) as c2,
                ifnull(lhs.cnt, 0) + ifnull(rhs.cnt, 0) as cnt,
                lower(concat_ws($$##::##$$, ifnull(c1::string, 'none'), ifnull(c2::string, 'none'))) as row_key
        from
        (
            select col1 as col1, col2 as col2, count(*) as cnt from left_source
            group by col1, col2
        ) as lhs
        full outer join
        (
            select col1 as col1, col2 as col2, count(*) as cnt from right_source
            group by col1, col2
        ) as rhs
        on
        (
            ifnull(lhs.col1::variant, parse_json('NULL')) = ifnull(rhs.col1::variant, parse_json('NULL')) and
            ifnull(lhs.col2::variant, parse_json('NULL')) = ifnull(rhs.col2::variant, parse_json('NULL'))
        )
    The ifnull(col, parse_json) clause is used, since null = null is true in union when comparing two rows and
    same is not true in join, so this clause converts a column value to string null so that
    'null' = 'null' becomes true in join to replicate union's behaviour
    """
    logger.info("BEGIN: Applying Union transform.")

    lhs_data_source = data_sources[0]
    lhs_data_query = lhs_data_source.get_all_rows_query()
    lhs_var_type = lhs_data_source.get_source_var_dtype()

    rhs_data_source = data_sources[1]
    rhs_data_query = rhs_data_source.get_all_rows_query()
    # rhs_var_type = rhs_data_source.get_source_var_dtype()

    lhs_view_id = sf_query_builder.add_query_as_cte(lhs_data_query)

    rhs_view_id = sf_query_builder.add_query_as_cte(rhs_data_query)

    # columns to be selected in inner join query and columns on which the result
    # is grouped on (union columns)
    lhs_inner_select_cols, lhs_grp_by_cols = [], []
    rhs_inner_select_cols, rhs_grp_by_cols = [], []

    # columns used in inner join condition and construct to form row key
    outer_select_cols, join_condition_cols, row_key_cols = [], [], []
    union_df_var_type = {}

    # in the inner join query, columns from sources are named as col_{index},
    # which are then renamed to the names given in transformation
    for index, col_details in enumerate(transformation_dict["on"]):
        lhs_inner_select_cols.append(f"{col_details['lhs']} as col_{index}")
        lhs_grp_by_cols.append(f"col_{index}")

        rhs_inner_select_cols.append(f"{col_details['rhs']} as col_{index}")
        rhs_grp_by_cols.append(f"col_{index}")

        join_condition_cols.append(
            f"ifnull(lhs.col_{index}::variant, parse_json('NULL')) = ifnull(rhs.col_{index}::variant, parse_json('NULL'))"
        )
        outer_select_cols.append(
            f"ifnull(lhs.col_{index}, rhs.col_{index}) as {col_details['col_name']}"
        )
        row_key_cols.append(f"ifnull({col_details['col_name']}::string, 'none')")
        union_df_var_type[col_details["col_name"]] = lhs_var_type.get(col_details["lhs"], "String")  # type: ignore

    # to get count of rows with column values from both sources
    lhs_inner_select_cols.append("count(*) as cnt")
    rhs_inner_select_cols.append("count(*) as cnt")

    lhs_inner_select_cols = ", ".join(lhs_inner_select_cols)
    rhs_inner_select_cols = ", ".join(rhs_inner_select_cols)

    lhs_grp_by_cols = ", ".join(lhs_grp_by_cols)
    rhs_grp_by_cols = ", ".join(rhs_grp_by_cols)

    row_key_cols = ", ".join(row_key_cols)
    row_key = f"lower(concat_ws($$##::##$$, {row_key_cols}))"

    sum_count_clause = "ifnull(lhs.cnt, 0) + ifnull(rhs.cnt, 0)"

    join_condition_cols = " and ".join(join_condition_cols)
    outer_select_cols = ", ".join(outer_select_cols)

    union_query = f"""
    select  {outer_select_cols}, {sum_count_clause} as cnt, {row_key} as row_key
    from
    (
        select {lhs_inner_select_cols}
        from {lhs_view_id}
        group by {lhs_grp_by_cols}
    ) as lhs
    full outer join
    (
        select {rhs_inner_select_cols}
        from {rhs_view_id}
        group by {rhs_grp_by_cols}
    ) as rhs
    on
    (
       {join_condition_cols}
    )
    """
    logger.info(f"Executing Union query -  {union_query}")
    return union_query, union_df_var_type


def apply_advanced_filter_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,
    v2_filter=False,
) -> tuple[str, dict]:
    # pylint: disable=unused-argument
    """
    Complex Expressions can be used to filter the datasheet,
    transformation_spec: {type: ADVANCED_FILTER , infix: "{INFIX}"}

    Sample Infix expression:
            [
                {
                    "meta": {
                        "category": None,
                        "model_name": "",
                        "system_name": "period_start_date",
                        "data_type_id": 2,
                    },
                    "name": "Period Start Date",
                    "tags": None,
                    "type": "VARIABLE",
                    "data_type": "Date",
                },
                {
                    "name": "<=",
                    "type": "OPERATOR",
                    "alt_name": "LESSERTHANEQUALTO",
                    "category": "LOGICAL",
                    "__typename": "",
                    "multi_valued": False,
                    "output_types": ["Boolean"],
                    "needs_operand": True,
                    "operand_types": [
                        "Integer",
                        "Date",
                        "Percentage",
                        "DayDuration",
                        "MinuteDuration",
                        "SecondDuration",
                    ],
                    "output_type_ids": [3],
                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                },
                {
                    "meta": {
                        "category": None,
                        "model_name": "",
                        "system_name": "period_end_date",
                        "data_type_id": 2,
                    },
                    "name": "Period End Date",
                    "tags": None,
                    "type": "VARIABLE",
                    "data_type": "Date",
                },
            ]

    Equivalent Expression:
        period_start_date <= period_end_date

    Refer test case file test_advanced_filter.py for more samples.
    """
    infix_expression = transformation_dict["infix"]

    data_source = data_sources[0]
    data_query = data_source.get_all_rows_query()
    var_type = data_source.get_source_var_dtype()
    client_id = data_source.get_source_meta_details()["client_id"]

    cur_view_id = sf_query_builder.add_query_as_cte(data_query)
    logger.info(
        f"Invoking advanced filter with view {cur_view_id} - {client_id} -- {len(infix_expression)}"
    )

    # if var type is none select all columns from source
    if var_type:
        filter_select_columns = list(var_type.keys())
        filter_select_columns.append("row_key")
        filter_select_clause = f"{', '.join(filter_select_columns)}"
    else:
        filter_select_clause = "*"

    conditional_expression = InfixToSnowflakeQuery(
        infix_expression=infix_expression,
        typecast_operator="::",
        client_id=client_id,
        # If type is v2 all the comparison is case insensitive
        case_insensitive=v2_filter,
    ).convert()

    filter_query = f"""select {filter_select_clause}
        from {cur_view_id}
        where {conditional_expression}
        """

    logger.info(f"Filter Query for advanced filter is {filter_query}")
    return filter_query, var_type


def apply_advanced_filter_v2_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,
):
    return apply_advanced_filter_transform_snowflake_lite(
        sf_query_builder=sf_query_builder,
        data_sources=data_sources,
        transformation_dict=transformation_dict,
        ever_comparison=ever_comparison,
        v2_filter=True,
    )


def apply_get_user_properties_transform_snowflake_lite(
    sf_query_builder: SnowflakeLiteEtlQueryBuilder,
    data_sources: list[FlatDataSource],
    transformation_dict: dict,
    ever_comparison=False,  # pylint: disable=unused-argument
) -> tuple[str, dict]:
    """
    This transformation is used to get user properties from user report.

    Sample meta data for this transformation:
    {
        "key": "c1b454d9-5e9d-408c-9dda-f1b3401ef77a",
        "type": "GET_USER_PROPERTIES",
        "email_column": "co_6_deal_owner_email",
        "as_of_date_column": "co_6_deal_close_date",
        "user_properties": [
            {
                "data_type_id": 4,
                "user_property_system_name": "designation",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_designation",
            },
            {
                "data_type_id": 4,
                "user_property_system_name": "payout_frequency",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency",
            },
            {
                "data_type_id": 1,
                "user_property_system_name": "payee_variable_pay",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay",
            },
            {
                "data_type_id": 4,
                "user_property_system_name": "user_role",
                "output_variable_system_name": "up_co_6_deal_owner_email_co_6_deal_close_date_user_role",
            },
        ],
    }

    The join query looks something like this:
        select current_sheet.co_6_deal_id as co_6_deal_id,
        current_sheet.co_6_lead_id as co_6_lead_id,
        current_sheet.co_6_deal_name as co_6_deal_name,
        current_sheet.co_6_deal_amount as co_6_deal_amount,
        current_sheet.co_6_deal_status as co_6_deal_status,
        current_sheet.co_6_deal_creation_date as co_6_deal_creation_date,
        current_sheet.co_6_deal_close_date as co_6_deal_close_date,
        current_sheet.co_6_deal_owner_email as co_6_deal_owner_email,
        current_sheet.co_6_deal_priority as co_6_deal_priority,
        current_sheet.co_6_deal_state as co_6_deal_state,
        current_sheet.co_6_deal_probability as co_6_deal_probability,
        current_sheet.co_6_deal_region as co_6_deal_region,
        current_sheet.co_6_customer_satisfaction as co_6_customer_satisfaction,
        current_sheet.row_key as row_key,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_designation
        as up_co_6_deal_owner_email_co_6_deal_close_date_designation,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency
        as up_co_6_deal_owner_email_co_6_deal_close_date_payout_frequency,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay
        as up_co_6_deal_owner_email_co_6_deal_close_date_payee_variable_pay,
        user_report.up_co_6_deal_owner_email_co_6_deal_close_date_user_role
        as up_co_6_deal_owner_email_co_6_deal_close_date_user_role
        from "EVERSTAGE_LOCAL_INDIA"."PUBLIC"."b53edb47-2cc7-4f2b-815d-fe12ad47017c" as current_sheet
        left join "EVERSTAGE_LOCAL_INDIA"."PUBLIC"."c2e09a0d-23b5-4369-bf12-a2f22c285698" as user_report
        on
        current_sheet.co_6_deal_owner_email = user_report.employee_email_id::string
        and current_sheet.co_6_deal_close_date >= user_report.effective_start_date::datetime
        and (
            current_sheet.co_6_deal_close_date <= user_report.effective_end_date::datetime
            or user_report.effective_end_date::datetime is null
        )
    """
    logger.info("Applying Get user property transform - lite")

    data_source = data_sources[0]
    data_query = data_source.get_all_rows_query()
    var_type = data_source.get_source_var_dtype()
    client_id = data_source.get_source_meta_details()["client_id"]

    lhs_alias = "current_sheet"
    rhs_alias = "user_report"

    lhs_columns = [f"{lhs_alias}.{col} as {col}" for col in var_type.keys()]
    lhs_columns.append(f"{lhs_alias}.row_key as row_key")

    transformation_meta = GetUserPropertiesTransformationMeta(**transformation_dict)
    email_column = transformation_meta.email_column
    as_of_date_column = transformation_meta.as_of_date_column
    user_properties = transformation_meta.user_properties

    # get the data type id to name map to resolve the snowflake data type
    data_type_id_name_map = get_datatype_id_name_map()

    # Default columns to be selected from user report
    rhs_raw_columns = [
        "data:employee_email_id::string as employee_email_id",
        "data:effective_start_date::datetime as effective_start_date",
        "data:effective_end_date::datetime as effective_end_date",
    ]
    rhs_columns = []
    resultant_var_type = var_type.copy()

    # iterate through user properties and construct the query
    for user_property in user_properties:
        user_property_name = user_property.user_property_system_name
        data_type_id = user_property.data_type_id
        output_column_name = user_property.output_variable_system_name

        data_type = data_type_id_name_map[data_type_id]
        snowflake_data_type = resolve_snowflake_data_type(data_type)
        rhs_raw_columns.append(
            f"data:{user_property_name}::{snowflake_data_type} as {output_column_name}"
        )

        rhs_columns.append(f"{rhs_alias}.{output_column_name} as {output_column_name}")
        resultant_var_type[output_column_name] = data_type

    rhs_raw_columns = ", ".join(rhs_raw_columns)

    # TODO: read from flat version of user report after migrating reports to flat data sources
    table_name = get_system_object_data_table_name(
        client_id=client_id, object_id="user"
    )
    rhs_fetch_query = f"""
        select {rhs_raw_columns}
        from {table_name}
        where client_id = {client_id}
        and object_id = 'user'
        and knowledge_end_date is null
        and not is_deleted
    """

    lhs_view_id = sf_query_builder.add_query_as_cte(data_query)

    rhs_view_id = sf_query_builder.add_query_as_cte(rhs_fetch_query)

    lhs_as_of_date_column = f"{lhs_alias}.{as_of_date_column}::datetime"

    if as_of_date_column == GetUserPropertiesTransformationUtils.CURRENT_DATE_KEY.value:
        lhs_as_of_date_column = "current_timestamp()::datetime"

    join_condition = f"""
        {lhs_alias}.{email_column} = {rhs_alias}.employee_email_id::string
        and {lhs_as_of_date_column} >= {rhs_alias}.effective_start_date::datetime
        and (
            {lhs_as_of_date_column} <= {rhs_alias}.effective_end_date::datetime
            or {rhs_alias}.effective_end_date::datetime is null
        )
    """

    select_clause = ", ".join(lhs_columns + rhs_columns)

    join_query = f"""
        select {select_clause}
        from {lhs_view_id} as {lhs_alias}
        left join {rhs_view_id} as {rhs_alias}
        on {join_condition}
    """

    logger.info(f"Get user property join query is {join_query}")

    return join_query, resultant_var_type
