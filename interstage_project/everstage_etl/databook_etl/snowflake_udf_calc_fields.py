"""
Implementing calculated fields using snowflake udf
"""

import itertools
import logging

from django.conf import settings
from django.core.cache import cache

from commission_engine.accessors.client_accessor import (
    get_calc_fields_strategy,
    get_client,
    get_ever_comparison,
)
from commission_engine.database.snowflake_query_utils import generate_temp_view_name
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    get_ast_or_str,
    get_datasheet_variables_for_the_given_kd_as_dicts,
)
from commission_engine.utils.cache_utils import (
    get_ever_comparison_cache,
    set_ever_comparison_cache,
)
from everstage_etl.databook_etl.calc_fields_lambda_utils import (
    lambda_bin_pack_executor_calc_field,
)
from spm.services.custom_calendar_services import get_all_periods

from .datasheet_etl_utils import snowflake_temp_table_name

logger = logging.getLogger(__name__)


def add_row_calculated_fields_based_on_strategy(
    snowpark_session,
    client_id,
    datasheet_id,
    df_table,
    knowledge_date,
    fields_to_compute=None,
    disable_fallback=False,
    force_lambda=False,  # this is used in checker script to force lambda evaluation
    sync_run_id=None,
):
    """
    Evaluates the calculated fields in datasheet
    Parameters:
        client_id: string
        datasheet_id: string
        datasheet_data_df: pandas.DataFrame
    """
    logger.info("BEGIN - evaluate calculated fields")
    # code to remove nan_nat goes here

    data_type_map = get_data_types()
    datasheet_variables = get_datasheet_variables_for_the_given_kd_as_dicts(
        client_id, datasheet_id, knowledge_date
    )
    ever_comparison = get_ever_comparison_cache(client_id)
    if ever_comparison is None:
        logger.info("ever_comparison flag not found in cache. Fetching from DB")
        ever_comparison = get_ever_comparison(client_id)
        set_ever_comparison_cache(client_id, ever_comparison)

    all_vars_datatype = dict()
    for variable in datasheet_variables:
        all_vars_datatype[variable["system_name"]] = variable["data_type_id"]

    if fields_to_compute is not None:
        non_zero_field_order_variables = fields_to_compute.copy()
    else:
        # We need to consider only variables with a non zero field order
        non_zero_field_order_variables = filter(
            lambda v: v["field_order"] != 0, datasheet_variables
        )

    grouped_datasheet_variables = itertools.groupby(
        non_zero_field_order_variables, key=lambda variable: variable["field_order"]
    )
    fiscal_start_month = get_client(client_id).fiscal_start_month
    variable_ast_map = {}
    variables = []
    for _, order_variables in grouped_datasheet_variables:
        for variable in order_variables:
            # Create a variable to ast map and pass that on
            variable_ast_map[variable["system_name"]] = get_ast_or_str(variable)
            # create a copy of variable and remove attributes not needed for
            # udf calculated fields evaluation
            variable_copy = variable.copy()
            ## convert variable_id to string since UUID is not json serializable
            if variable_copy.get("variable_id"):
                variable_copy["variable_id"] = str(variable["variable_id"])
            variable_copy.pop("databook_id")
            variable_copy.pop("datasheet_id")
            variables.append(variable_copy)

    default_timezone = getattr(settings, "TIME_ZONE", "UTC")

    # TODO: @sriram Set the output format for all the clients in the future
    # after observing the impact for enabled clients
    if has_feature(client_id, "set_timestamp_output_format_for_cf_udf"):
        # set the timestamp output format to match the format in calculated fields
        snowpark_session.sql(
            "alter session set TIMESTAMP_NTZ_OUTPUT_FORMAT = 'YYYY-MM-DD HH24:MI:SS.FF6'"
        ).show()

    # store the data_type_map, variables, ast_map and fiscal month in a temp table
    # so that they can be passed as arguments to udf in snowflake
    data_table = snowflake_temp_table_name(prefix="constants_table")
    custom_periods = get_all_periods(client_id)
    temp_data_df = snowpark_session.create_dataframe(
        [
            [{"type": "ast", "data": dict(variable_ast_map)}],
            [{"type": "variables", "data": list(variables)}],
            [{"type": "dmap", "data": dict(data_type_map)}],
            [{"type": "fiscal", "data": fiscal_start_month}],
            [{"type": "var_type_map", "data": dict(all_vars_datatype)}],
            [{"type": "timezone", "data": str(default_timezone)}],
            [{"type": "ever_comparison", "data": ever_comparison}],
            [{"type": "custom_periods", "data": dict(custom_periods)}],
        ],
        schema=["constant"],
    )
    temp_data_df.write.save_as_table(data_table, table_type="temporary")

    calc_fields_strategy = get_calc_fields_strategy(client_id)
    if force_lambda:
        calc_fields_strategy = "lambda"

    logger.info("Calc fields strategy: %s", calc_fields_strategy)

    fallback_to_udf = False

    if calc_fields_strategy == "lambda":
        logger.info("Evaluate calculated fields using lambda")

        # Exception handling to fallback to udf in case of any error while evaluating calculated fields using lambda
        try:
            for variable in variables:
                # remove attributes not needed for calculated fields evaluation
                # and to avoid "TypeError: Object of type datetime is not JSON serializable" when passing to lambda
                variable.pop("knowledge_begin_date")
                variable.pop("knowledge_end_date")

            cf_meta_data = {
                "ast": dict(variable_ast_map),
                "variables": list(variables),
                "dmap": dict(data_type_map),
                "fiscal": fiscal_start_month,
                "var_type_map": dict(all_vars_datatype),
                "timezone": str(default_timezone),
                "ever_comparison": ever_comparison,
                "custom_periods": dict(custom_periods),
            }

            dest_table = generate_temp_view_name(
                key=f"calc_field_result_{datasheet_id}"
            )

            # calculate the fields using lambda
            is_success, lambda_results = lambda_bin_pack_executor_calc_field(
                client_id=client_id,
                variables=datasheet_variables,
                src_table=df_table,
                dest_table=dest_table,
                cf_meta_data=cf_meta_data,
                snowpark_session=snowpark_session,
            )

            # read the calculated fields result after lambda execution
            calc_fields_sql = (
                f"""select "row_key" as row_key, "data" as data from {dest_table}"""
            )

            if not is_success:
                # if lambda call fails, raise exception so that it can fallback to udf
                failed_results = [
                    result for result in lambda_results if result["status_code"] != 200
                ]
                raise Exception(
                    f"Some of the lambda calls failed. Failed results: {failed_results}"
                )

        except Exception as err:
            # if there is any error while evaluating calculated fields using lambda
            # or if the lambda call fails, fallback to udf
            logger.exception(
                "Error while evaluating calculated fields using lambda. Falling back to udf. Error: %s",
                err,
            )
            if not disable_fallback:
                fallback_to_udf = True

    if calc_fields_strategy == "udf" or fallback_to_udf:
        logger.info("Evaluate calculated fields using snowflake udf")
        calc_fields_sql = f"""
        select row_key, snowflake_calc_fields(
            data, 
            (select constant:data from {data_table} where constant:type='dmap'),
            (select constant:data from {data_table} where constant:type='variables'),
            (select constant:data from {data_table} where constant:type='ast'),
            (select constant:data from {data_table} where constant:type='fiscal'),
            (select constant:data from {data_table} where constant:type='var_type_map'),
            (select constant:data from {data_table} where constant:type='timezone'),
            (select constant:data from {data_table} where constant:type='ever_comparison'),
            (select constant:data from {data_table} where constant:type='custom_periods')
        ) as data
        from {df_table}
        """

    if fallback_to_udf and sync_run_id:
        cache_key = f"{client_id}#{sync_run_id}#fallback_to_udf"
        cache.set(
            cache_key, True, timeout=7200  # noqa: FBT003
        )  # 120 minutes in seconds

    logger.info("Calc fields sql: %s", calc_fields_sql)
    calc_field_df = snowpark_session.sql(calc_fields_sql)
    return calc_field_df
