import logging

from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import get_clients_by_exclusion_status
from interstage_project.utils import handle_ever_exception

from .services.client_monitoring_service import ClientMonitoringService

logger = logging.getLogger(__name__)


class ETLInfraLambdaMonitoredClientsView(APIView):
    """
    API view to fetch currently monitored clients.
    """

    @handle_ever_exception
    def get(self, request: Request) -> Response:
        """Handles GET requests to fetch currently monitored clients."""

        window_name: str | None = request.query_params.get("window_name")
        check_window_param = request.query_params.get("check_window")

        window_name = window_name or "full_window"
        check_window = (
            check_window_param is None or check_window_param.lower() != "false"
        )

        monitored_clients: dict[str, str] = (
            ClientMonitoringService.get_current_window_clients_who_have_a_cron_schedule(
                window_name, check_window=check_window
            )
        )

        return Response(monitored_clients, status=status.HTTP_200_OK)


class ETLInfraLambdaClientsDailySyncDetails(APIView):
    """
    API view to fetch excluded clients, non-excluded clients, and clients with and without a cron schedule.
    """

    @handle_ever_exception
    def get(self, request: Request) -> Response:  # pylint: disable=unused-argument
        """Handles GET requests to fetch client statuses."""

        excluded_clients = get_clients_by_exclusion_status(is_excluded=True)
        not_excluded_clients = get_clients_by_exclusion_status(is_excluded=False)
        live_clients_with_active_cron = (
            ClientMonitoringService.get_current_window_clients_who_have_a_cron_schedule(
                "full_window", check_window=False
            )
        )
        live_clients_without_active_cron = (
            ClientMonitoringService.get_clients_who_does_not_have_a_cron_schedule()
        )

        response_data = {
            "excluded_clients": excluded_clients,
            "not_excluded_clients": not_excluded_clients,
            "live_clients_with_active_cron": live_clients_with_active_cron,
            "live_clients_without_active_cron": live_clients_without_active_cron,
        }

        return Response(response_data, status=status.HTTP_200_OK)
