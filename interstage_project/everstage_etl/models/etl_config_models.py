from django.db import models

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import EsCharField, EsDateTimeField, EsJSONField


class ETLConfig(models.Model):
    """Configuration model for ETL settings"""

    id = EsCharField(max_length=100, primary_key=True)
    config = EsJSONField(default=dict)
    created_at = EsDateTimeField(auto_now_add=True)
    updated_at = EsDateTimeField(auto_now=True)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "etl_config"
