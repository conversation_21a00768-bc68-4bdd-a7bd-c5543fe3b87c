"""Utility functions for managing batch service resources and configuration."""

import logging
import os
import socket
import sys

import requests
from celery import current_app
from celery.app.control import Inspect

from commission_engine.accessors.client_accessor import (
    is_mem_regulated_batching_enabled,
)
from commission_engine.database.snowflake_connection import get_connection
from everstage_etl.accessors.etl_config_accessor import ETLConfigAccessor

logger = logging.getLogger(__name__)


def _get_current_process_max_memory_bytes():
    """Get the max memory allocated to the current ECS process"""
    env = os.environ["ENV"]
    if env == "LOCALDEV":
        return 3 * 1024 * 1024 * 1024
    else:
        metadata_uri = os.getenv("ECS_CONTAINER_METADATA_URI_V4")
        if metadata_uri:
            response = requests.get(
                f"{metadata_uri}/task", timeout=5
            )  # 5 second timeout
            if response.status_code == 200:
                task_metadata = response.json()
                return task_metadata["Limits"]["Memory"] * 1024 * 1024
        raise ValueError("Failed to retrieve metadata")


def _get_celery_concurrency():
    """Get the concurrency value from app config"""
    env = os.environ["ENV"]
    if env == "LOCALDEV":
        return 10
    # Get the current machine's hostname
    current_hostname = socket.gethostname()

    # Create an Inspect instance

    inspect = Inspect(app=current_app)

    # Fetch worker stats
    stats = inspect.stats()

    if stats:
        # Look for the worker running on the current machine
        for worker_name, worker_stats in stats.items():
            if current_hostname in worker_name:
                concurrency = worker_stats.get("pool", {}).get(
                    "max-concurrency", "Unknown"
                )
                return concurrency
    raise ValueError("No workers found for the current machine.")


def get_sf_query_result_samplesize_bytes(client_id, sf_query: str) -> float:
    """Get the size of the SF query result in MB by sampling first 100 rows"""
    # Add LIMIT 100 to the query
    SAMPLE_ROWS = 100
    sample_query = f"SELECT * FROM ({sf_query}) SAMPLE ROW ({SAMPLE_ROWS} ROWS)"
    # Execute query and fetch results
    with get_connection(client_id) as conn:
        cursor = conn.cursor()
        cursor.execute(sample_query)
        results = cursor.fetchall()

        if not results:
            return 0
        sample_row_size_bytes = _get_sample_rows_size(results)
        return sample_row_size_bytes


# TODO: fix sql injection error
def _get_sample_rows_size_from_query(query: str, db_connector) -> float:
    SAMPLE_ROWS = 100
    sample_query = f"SELECT * FROM ({query}) SAMPLE ROW ({SAMPLE_ROWS} ROWS)"
    results = db_connector.query_data(query=sample_query)
    return _get_sample_rows_size(results)


def _get_sample_rows_size(results: list) -> float:
    sample_rows_size = 0
    sample_rows_count = len(results)
    # Calculate approximate size of all row in bytes
    for result in results:
        sample_rows_size += sum(sys.getsizeof(field) for field in result)
    logger.info("Sample %s rows size: %s", sample_rows_count, sample_rows_size)
    sample_row_size_bytes = sample_rows_size / sample_rows_count

    return sample_row_size_bytes


def _calculate_batch_size(
    max_memory_bytes: int,
    sample_row_size_bytes: float,
    celery_concurrency: int,
    batch_discount_percentage: float,
) -> int:
    """Calculate the batch size that will not exceed the memory allocated to the current ECS process"""
    return int(
        (max_memory_bytes / (sample_row_size_bytes * celery_concurrency))
        * (1 - batch_discount_percentage / 100)
    )


def get_memory_regulated_batch_size_sf(
    client_id, sf_query: str, default_batch_size: int, config_id: str
) -> int:
    """Get the batch size that will not exceed the memory allocated to the current ECS process"""
    is_mem_reg_batching = is_mem_regulated_batching_enabled(client_id)
    if not is_mem_reg_batching:
        return default_batch_size
    try:
        max_memory_bytes = _get_current_process_max_memory_bytes()
        celery_concurrency = _get_celery_concurrency()
        sample_row_size_bytes = get_sf_query_result_samplesize_bytes(
            client_id, sf_query
        )
        etl_config = ETLConfigAccessor().get(id=config_id)
        batch_discount_percentage = etl_config.config.get("batch_discount_percentage")

        # Log all parameters
        logger.info("Max memory bytes: %s", max_memory_bytes)
        logger.info("Celery concurrency: %s", celery_concurrency)
        logger.info("Sample row size bytes: %s", sample_row_size_bytes)
        logger.info("Batch discount: %s", batch_discount_percentage)

        if sample_row_size_bytes > 0:
            # 3GB*(1024*1024*1024) / (500 bytes sample row size * 100 concurrency) = 65000
            batch_size = _calculate_batch_size(
                max_memory_bytes,
                sample_row_size_bytes,
                celery_concurrency,
                batch_discount_percentage,
            )
            logger.info("Batch size: %s", batch_size)
            return batch_size
    except Exception as e:
        logger.error(
            "Error getting memory regulated batch size for query: %s", sf_query
        )
        logger.error("Error: %s", e)
        logger.error("Using default batch size: %s", default_batch_size)
    return default_batch_size


def get_memory_regulated_batch_size_upstream_sql(
    client_id: int, query: str, db_connector, default_batch_size: int, config_id: str
) -> int:
    """Get the batch size that will not exceed the memory allocated to the current ECS process"""
    is_mem_reg_batching = is_mem_regulated_batching_enabled(client_id)
    if not is_mem_reg_batching:
        return default_batch_size
    try:
        max_memory_bytes = _get_current_process_max_memory_bytes()
        celery_concurrency = _get_celery_concurrency()
        sample_row_size_bytes = _get_sample_rows_size_from_query(query, db_connector)
        etl_config = ETLConfigAccessor().get(id=config_id)
        batch_discount_percentage = etl_config.config.get("batch_discount_percentage")

        # Log all parameters
        logger.info("Max memory bytes: %s", max_memory_bytes)
        logger.info("Celery concurrency: %s", celery_concurrency)
        logger.info("Sample row size bytes: %s", sample_row_size_bytes)
        logger.info("Batch discount percentage: %s", batch_discount_percentage)

        # 3GB*(1024*1024*1024) / (500 bytes sample row size * 100 concurrency) = 65000
        batch_size = _calculate_batch_size(
            max_memory_bytes,
            sample_row_size_bytes,
            celery_concurrency,
            batch_discount_percentage,
        )
        logger.info("Batch size: %s", batch_size)
        return batch_size
    except Exception as e:
        logger.error("Error getting memory regulated batch size for query: %s", query)
        logger.error("Error: %s", e)
        logger.error("Using default batch size: %s", default_batch_size)
    return default_batch_size
