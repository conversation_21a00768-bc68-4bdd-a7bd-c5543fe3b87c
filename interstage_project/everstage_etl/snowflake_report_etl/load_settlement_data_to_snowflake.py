# pylint: disable=anomalous-backslash-in-string

import logging
import os
import uuid

from celery import shared_task

from commission_engine.accessors.client_accessor import (
    is_settlement_v3_enabled,
    is_write_settlement_to_snowflake,
)
from commission_engine.database.snowflake_connection import get_pooled_connection
from commission_engine.utils.snowflake_commission_utils import (
    get_settlement_table_name_sf,
)
from common.celery.celery_base_task import EverC<PERSON>ryBaseTask
from everstage_ddd.settlement_v3.common import ExecutionMode

from .load_commission_to_snowflake import (
    _create_stage_query,
    _drop_stage_query,
    _drop_temp_table_query,
)

logger = logging.getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def load_settlement_data_to_snowflake(
    client_id,
    e2e_sync_run_id,
    sync_type,
    level=None,
    team_or_payee_sync=None,
    s3_batch_id=1,
):
    """
    load settlement data written in parquet files to s3.
    This task takes all the parquet files from
    s3://{pvt_assets_bucket_name}/{object_type}/{client_id}/{e2e_sync_run_id}/{sync_type}/{operation}/ (opertion = "INSERT" / "INVALIDATE") for settlement sync
    s3://{pvt_assets_bucket_name}/{object_type}/{client_id}/{e2e_sync_run_id}/{sync_type}/{s3_batch_id}/{operation}/ (opertion = "INSERT" / "INVALIDATE") for commission sync
    s3_batch_id is used to support multiple period data in case of multi period sync
    loads INSERT files and INVALIDATE files into two separate stages and moves them to two temporary tables.
    First, it invalidates data in settlement table by joining it with invalidate temp table on
        - payee_email_id, period_start_date, period_end_date,  criteria_id, settlement_rule_id, line_item_id, commission_row_key
        and sets the knowledge_end_date for the matching records to knowledge_end_date of the invalidate temp table.....
    Finally, it inserts the data in the insert temp table to settlement table

    sync_type - COMMISSION/DATASHEET
    """

    # settlement v3 uses a different flow, so we skip the task if settlement v3 is enabled
    if not is_write_settlement_to_snowflake(
        client_id=client_id
    ) or is_settlement_v3_enabled(client_id):
        return

    logger.info("BEGIN: load settlement data to snowflake")

    # get the name of the client's sharded settlement table
    settlement_table_name = get_settlement_table_name_sf(client_id=client_id)

    ctx = get_pooled_connection(client_id=client_id)
    cur = ctx.cursor()
    object_type = "settlement"
    e2e_sync_run_id = str(e2e_sync_run_id).replace("-", "_")
    temp_uuid = str(uuid.uuid4()).replace("-", "_")

    # stage and temp table names for invalidate files
    invalidate_stage_name = f"{object_type}_stage_{client_id}_{e2e_sync_run_id}_{sync_type}_invalidate_{temp_uuid}"
    invalidate_temp_name = f"temp_{object_type}_{client_id}_{e2e_sync_run_id}_{sync_type}_invalidate_{temp_uuid}"
    invalidate_s3_location = get_settlement_s3_url(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        operation="INVALIDATE",
        level=level,
        team_or_payee_sync=team_or_payee_sync,
        s3_batch_id=s3_batch_id,
    )

    # snowflake queries to invalidate records in settlement table
    sf_invalidate_queries_to_execute = [
        _create_stage_query(
            s3_url=invalidate_s3_location, stage_name=invalidate_stage_name
        ),
        create_invalidate_temp_table_query(temp_table_name=invalidate_temp_name),
        copy_data_from_stage_to_temp_table(
            stage_name=invalidate_stage_name, table_name=invalidate_temp_name
        ),
        invalidate_deleted_entries_in_settlement(
            client_id=client_id,
            temp_table_name=invalidate_temp_name,
            table_name=settlement_table_name,
        ),
        _drop_stage_query(stage_name=invalidate_stage_name),
        _drop_temp_table_query(temp_table_name=invalidate_temp_name),
    ]
    for query in sf_invalidate_queries_to_execute:
        cur.execute(query)

    # stage and temp table names for insert files
    insert_stage_name = f"{object_type}_stage_{client_id}_{e2e_sync_run_id}_{sync_type}_insert_{temp_uuid}"
    insert_s3_location = get_settlement_s3_url(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        operation="INSERT",
        level=level,
        team_or_payee_sync=team_or_payee_sync,
        s3_batch_id=s3_batch_id,
    )
    # snowflake queries to insert new/updated settlement records in table
    sf_insert_queries_to_execute = [
        _create_stage_query(s3_url=insert_s3_location, stage_name=insert_stage_name),
        copy_data_from_stage_to_temp_table(
            stage_name=insert_stage_name, table_name=settlement_table_name
        ),
        _drop_stage_query(stage_name=insert_stage_name),
    ]
    for query in sf_insert_queries_to_execute:
        cur.execute(query)

    ctx.commit()

    logger.info("END: load settlement data to snowflake")


# This method is only for settlement v3
@shared_task(base=EverCeleryBaseTask)
def load_settlement_v3_data_to_snowflake(client_id, e2e_sync_run_id, execution_mode):
    """
    load settlement v3 data written in parquet files to s3.
    This task takes all the parquet files from
    s3://{pvt_assets_bucket_name}/{object_type}/{client_id}/{e2e_sync_run_id}/{sync_type}/{operation}/ (opertion = "INSERT" / "INVALIDATE")
    loads INSERT files and INVALIDATE files into two separate stages and moves them to two temporary tables.
    First, it invalidates data in settlement table by joining it with invalidate temp table on
        - payee_email_id, period_start_date, period_end_date,  criteria_id, settlement_rule_id, line_item_id, commission_row_key
        and sets the knowledge_end_date for the matching records to knowledge_end_date of the invalidate temp table.....
    Finally, it inserts the data in the insert temp table to settlement table

    sync_type - SETTLEMENT_V3
    """
    sync_type = "settlement_v3"
    logger.info("BEGIN: load settlement v3 data to snowflake")

    # get the name of the client's sharded settlement table
    if execution_mode == ExecutionMode.ACTUAL_RUN:
        settlement_table_name = get_settlement_table_name_sf(client_id=client_id)
    elif execution_mode == ExecutionMode.DRY_RUN:
        settlement_table_name = (
            get_settlement_table_name_sf(client_id=client_id) + "_dry_run"
        )
    else:
        return

    ctx = get_pooled_connection(client_id=client_id)
    cur = ctx.cursor()
    object_type = "settlement"
    e2e_sync_run_id = str(e2e_sync_run_id).replace("-", "_")
    temp_uuid = str(uuid.uuid4()).replace("-", "_")

    # stage and temp table names for invalidate files
    invalidate_stage_name = f"{object_type}_stage_{client_id}_{e2e_sync_run_id}_{sync_type}_invalidate_{temp_uuid}"
    invalidate_temp_name = f"temp_{object_type}_{client_id}_{e2e_sync_run_id}_{sync_type}_invalidate_{temp_uuid}"
    invalidate_s3_location = get_settlement_v3_s3_url(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        operation="INVALIDATE",
        execution_mode=execution_mode,
    )

    # snowflake queries to invalidate records in settlement table
    sf_invalidate_queries_to_execute = [
        _create_stage_query(
            s3_url=invalidate_s3_location, stage_name=invalidate_stage_name
        ),
        create_settlement_v3_invalidate_temp_table_query(
            temp_table_name=invalidate_temp_name
        ),
        copy_data_from_stage_to_temp_table(
            stage_name=invalidate_stage_name, table_name=invalidate_temp_name
        ),
        invalidate_deleted_entries_in_settlement_v3(
            client_id=client_id,
            temp_table_name=invalidate_temp_name,
            table_name=settlement_table_name,
        ),
        _drop_stage_query(stage_name=invalidate_stage_name),
        _drop_temp_table_query(temp_table_name=invalidate_temp_name),
    ]
    for query in sf_invalidate_queries_to_execute:
        cur.execute(query)

    # stage and temp table names for insert files
    insert_stage_name = f"{object_type}_stage_{client_id}_{e2e_sync_run_id}_{sync_type}_insert_{temp_uuid}"
    insert_s3_location = get_settlement_v3_s3_url(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        operation="INSERT",
        execution_mode=execution_mode,
    )
    # snowflake queries to insert new/updated settlement records in table
    sf_insert_queries_to_execute = [
        _create_stage_query(s3_url=insert_s3_location, stage_name=insert_stage_name),
        copy_data_from_stage_to_temp_table(
            stage_name=insert_stage_name, table_name=settlement_table_name
        ),
        _drop_stage_query(stage_name=insert_stage_name),
    ]
    for query in sf_insert_queries_to_execute:
        cur.execute(query)

    ctx.commit()

    logger.info("END: load settlement v3 data to snowflake")


def get_settlement_s3_url(
    client_id,
    e2e_sync_run_id,
    sync_type,
    operation,
    level=None,
    team_or_payee_sync=None,
    s3_batch_id=1,
):
    """
    get the location of the s3 bucket where the insert/invalidate files are stored
    added s3_batch_id to support multiple period data in case of multi period sync
    """
    pvt_assets_bucket_name = os.getenv("S3_PVT_ASSETS_BUCKET")

    if level and team_or_payee_sync:
        return f"s3://{pvt_assets_bucket_name}/settlement/{client_id}/{e2e_sync_run_id}/{sync_type}/{level}/{team_or_payee_sync}/{s3_batch_id}/{operation}/"

    return f"s3://{pvt_assets_bucket_name}/settlement/{client_id}/{e2e_sync_run_id}/{sync_type}/{operation}/"


def get_settlement_v3_s3_url(  # noqa: PLR0913
    client_id,
    e2e_sync_run_id,
    sync_type,
    operation,
    execution_mode,
    s3_batch_id=1,
):
    """
    get the location of the s3 bucket where the insert/invalidate files are stored
    """
    pvt_assets_bucket_name = os.getenv("S3_PVT_ASSETS_BUCKET")
    execution_mode_str = execution_mode.value.lower()
    base_path = f"s3://{pvt_assets_bucket_name}/settlement/{client_id}/{e2e_sync_run_id}/{sync_type}/{execution_mode_str}"
    level_path = f"/{s3_batch_id}" if s3_batch_id else ""
    return f"{base_path}{level_path}/{operation}/"


def create_invalidate_temp_table_query(temp_table_name):
    """
    snowflake query to create the temporary table for settlement records invalidation
    """

    return f"""CREATE OR REPLACE TEMPORARY TABLE {temp_table_name} (
            payee_email_id STRING,
            period_start_date TIMESTAMP,
            period_end_date TIMESTAMP,
            criteria_id STRING,
            settlement_rule_id STRING,
            line_item_id STRING,
            commission_row_key STRING,
            knowledge_end_date TIMESTAMP
        );
    """


def create_settlement_v3_invalidate_temp_table_query(temp_table_name):
    return f"""CREATE OR REPLACE TEMPORARY TABLE {temp_table_name} (
            payee_email_id STRING,
            period_start_date TIMESTAMP,
            period_end_date TIMESTAMP,
            criteria_id STRING,
            knowledge_end_date TIMESTAMP
        );"""


def copy_data_from_stage_to_temp_table(stage_name, table_name):
    """
    copy from {stage_name} to {table_name}
    """

    return f"""COPY INTO {table_name}
        FROM @{stage_name}
        FILE_FORMAT = (TYPE = PARQUET)
        PATTERN = '.*\.parquet'
        MATCH_BY_COLUMN_NAME = CASE_INSENSITIVE
        ON_ERROR = 'CONTINUE';"""


def invalidate_deleted_entries_in_settlement(client_id, temp_table_name, table_name):
    """
    update table_name by joining with temp_table_name
    """

    return f"""UPDATE {table_name} AS settlement
            SET settlement.knowledge_end_date = temp_table.knowledge_end_date
            FROM {temp_table_name} AS temp_table
            WHERE settlement.client_id = {client_id}
            AND settlement.knowledge_end_date IS NULL
            AND settlement.payee_email_id::STRING = temp_table.payee_email_id::STRING
            AND settlement.period_start_date::datetime = temp_table.period_start_date::datetime
            AND settlement.period_end_date::datetime = temp_table.period_end_date::datetime
            AND settlement.criteria_id::STRING = temp_table.criteria_id::STRING
            AND IFNULL(settlement.settlement_rule_id::VARIANT, PARSE_JSON('NULL')) = IFNULL(temp_table.settlement_rule_id::VARIANT, PARSE_JSON('NULL'))
            AND IFNULL(settlement.line_item_id::VARIANT, PARSE_JSON('NULL')) = IFNULL(temp_table.line_item_id::VARIANT, PARSE_JSON('NULL'))
            AND IFNULL(settlement.commission_row_key::VARIANT, PARSE_JSON('NULL')) = IFNULL(temp_table.commission_row_key::VARIANT, PARSE_JSON('NULL'));
    """


def invalidate_deleted_entries_in_settlement_v3(client_id, temp_table_name, table_name):
    return f"""UPDATE {table_name} AS settlement
            SET settlement.knowledge_end_date = temp_table.knowledge_end_date
            FROM {temp_table_name} AS temp_table
            WHERE settlement.client_id = {client_id}
            AND settlement.knowledge_end_date IS NULL
            AND settlement.payee_email_id::STRING = temp_table.payee_email_id::STRING
            AND settlement.period_start_date::datetime = temp_table.period_start_date::datetime
            AND settlement.period_end_date::datetime = temp_table.period_end_date::datetime
            AND settlement.criteria_id::STRING = temp_table.criteria_id::STRING;
    """
