"""
Responsible for datasheet generation - either full or incremental
"""

import logging
from datetime import datetime
from time import time
from uuid import UUID

from celery import shared_task
from django.core.cache import cache

from commission_engine.accessors.client_accessor import (
    get_client_subscription_plan,
    is_spark_dry_run,
    should_insert_meta_data_to_vec_db,
    should_take_ds_snapshot,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.utils.general_data import ETL_STATUS
from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.datasheet import ETLSpecTransformer, TransformationType
from everstage_ddd.global_search.meta_data_extractor.datasheet_meta_data import (
    upsert_ds_meta_data_in_vector_db,
)
from everstage_ddd.workflow_builder import DatasheetDataTrigger
from everstage_etl.databook_etl import (
    DatasheetETLTechnique,
    DatasheetExecutionContext,
    TranformationInputDetails,
    apply_transform_and_write_datasheet_data,
    datasheet_etl_technique,
)
from everstage_etl.databook_etl.spark_etl import transform_datasheet_with_spark
from everstage_etl.snapshot_service import save_ds_snapshot_to_sf_expanded_and_efs
from everstage_etl.tasks.spark_utils import trigger_spark_dry_run_etl
from interstage_project.celery import TaskGroupEnum
from interstage_project.threadlocal_log_context import (
    ExceptionTags,
    set_threadlocal_context,
)
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.custom_exceptions.datasheet_exceptions import DatasheetRowLimitExceeded

from .utils import (
    DatabookETLStatusAccessor,
    DatabookEtlStatusWrapper,
    DatasheetLockedException,
    ETLLockWrapper,
    drop_calc_fields_temp_tables,
    get_primary_kd,
)

logger = logging.getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def _generate_datasheet_single_task(
    client_id: int,
    e2e_sync_run_id: UUID,
    sync_type: str,
    datasheet_id: str,
    all_datasheet_details: DatasheetExecutionContext = None,
    is_force_invalidate=False,
) -> None:
    """
    This function is responsible for generating a single datasheet.
    """
    datasheet_details = all_datasheet_details.datasheet_details[datasheet_id]
    sync_run_id = datasheet_details.sync_run_id

    log_context = {
        "client_id": client_id,
        "e2e_sync_run_id": str(e2e_sync_run_id),
        "datasheet_id": datasheet_id,
        "is_force_invalidate": is_force_invalidate,
        "sync_type": sync_type,
        "sync_run_id": str(sync_run_id),
    }
    set_threadlocal_context(log_context)
    logger.info(
        "BEGIN: Generating datasheet - client_id %s and datasheet_id %s",
        client_id,
        datasheet_id,
    )
    databook_id = datasheet_details.databook_id
    datasheet_information = datasheet_details.details_for_snowflake_transformation

    etl_lock = ETLLockWrapper(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_run_id=sync_run_id
    )
    databook_etl_status_lock = DatabookEtlStatusWrapper(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
    )

    sync_status = databook_etl_status_lock.get_record_sync_status()
    knowledge_date = get_primary_kd(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_type=sync_type
    )
    databook_etl_status_lock.set_primary_kd(knowledge_date=knowledge_date)
    if sync_status != ETL_STATUS.FAILED.value:
        databook_etl_status_lock.change_status_to_locked_state()
        datasheet_start_time = time()
        release_datasheet_lock_on_exception = True
        try:
            etl_lock.acquire_lock(datasheet_id=datasheet_id)
            with create_snowpark_session_wrapper(
                client_id=client_id,
                tag={
                    "client_id": client_id,
                    "e2e_sync_run_id": e2e_sync_run_id,
                    "sync_run_id": sync_run_id,
                    "datasheet_id": datasheet_id,
                    "databook_id": databook_id,
                },
            ) as snowpark_session:
                _generate_datasheet(
                    client_id=client_id,
                    databook_id=databook_id,
                    datasheet_details_model=datasheet_information,
                    knowledge_date=knowledge_date,
                    snowpark_session=snowpark_session,
                    is_force_invalidate=is_force_invalidate,
                    sync_run_id=sync_run_id,
                    e2e_sync_run_id=e2e_sync_run_id,
                )
                databook_etl_status_lock.change_status_to_complete_state()
                sync_status = ETL_STATUS.COMPLETE.value
        except DatasheetLockedException as exception:
            # Intentionally catching and ignoring this exception to avoid race conditions.
            # This function is called by multiple celery processes simultaneously for the same datasheet.
            # First process succeeds, others receive this exception.
            logger.info(exception.message)
            release_datasheet_lock_on_exception = False

        except DatasheetRowLimitExceeded as ds_row_limit_exception:
            # If the datasheet row limit is exceeded, we mark this datasheet and
            # all children datasheets as failed
            # and passing the error_info as well
            error_info = ds_row_limit_exception.error_details()["message"]
            logger.exception(
                f"DS_ETL_EXCEPTION: {error_info}",
                extra={"exception_tag": ExceptionTags.DATASHEET_ETL.name},
            )

            _bulk_fail_datasheets(
                databook_etl_status_lock=databook_etl_status_lock,
                datasheet_id=datasheet_id,
                all_datasheet_details=all_datasheet_details,
                error_info=error_info,
                error_details=ds_row_limit_exception.error_details(),
            )
            sync_status = ETL_STATUS.FAILED.value
        except Exception:
            # We dont throw here since we want subsequent tasks to run
            logger.exception(
                f"DS_ETL_EXCEPTION: Failed to generate datasheet - {datasheet_id}",
                extra={"exception_tag": ExceptionTags.DATASHEET_ETL.name},
            )
            _bulk_fail_datasheets(
                databook_etl_status_lock=databook_etl_status_lock,
                datasheet_id=datasheet_id,
                all_datasheet_details=all_datasheet_details,
                error_info=None,
            )
            sync_status = ETL_STATUS.FAILED.value
        finally:
            # Handle cache and update sync run log if needed
            cache_key = f"{client_id}#{sync_run_id}#fallback_to_udf"
            if cache.get(cache_key, False):
                _update_sync_run_log(client_id, e2e_sync_run_id, sync_run_id)
                cache.delete(cache_key)

            if release_datasheet_lock_on_exception:
                # if the if_exception_release_lock is true, then we release the lock
                etl_lock.release_lock()
                datasheet_end_time = time()
                time_taken_seconds = datasheet_end_time - datasheet_start_time
                time_taken_minutes = round(time_taken_seconds / 60, 2)
                logger.info(
                    f"Datasheet {datasheet_id} took {time_taken_minutes} minutes for generation and sync_status - {sync_status}"
                )
                # Drop the temporary tables created for calc fields evaluation
                drop_calc_fields_temp_tables(
                    datasheet_id=datasheet_id, client_id=client_id
                )

    logger.info(
        "END: Generating datasheet - client_id %s and datasheet_id %s",
        client_id,
        datasheet_id,
    )


def _update_sync_run_log(
    client_id: int, e2e_sync_run_id: UUID, sync_run_id: UUID
) -> None:
    """Update the sync run log to indicate UDF fallback was used."""
    DatabookETLStatusAccessor(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_run_id=sync_run_id
    ).update_object({"sync_run_log": {"fallback_to_udf": True}})


def _bulk_fail_datasheets(
    databook_etl_status_lock,
    datasheet_id,
    all_datasheet_details,
    error_info=None,
    error_details=None,
) -> None:
    """
    Helper function to bulk release lock for datasheets
    """
    datasheet_details = all_datasheet_details.datasheet_details[datasheet_id]
    sync_run_ids = [
        all_datasheet_details.datasheet_details[child_id].sync_run_id
        for child_id in datasheet_details.childrens
        if child_id in all_datasheet_details.stale_datasheet_list
    ]
    databook_etl_status_lock.bulk_fail_datasheet_lock(
        sync_run_ids=sync_run_ids,
        parent_datasheet_detail=datasheet_details,
        error_info=error_info,
        error_details=error_details,
    )


def _generate_datasheet(
    client_id: int,
    databook_id: str,
    datasheet_details_model: TranformationInputDetails,
    knowledge_date: datetime,
    snowpark_session,
    is_force_invalidate=False,
    sync_run_id=None,
    e2e_sync_run_id=None,
) -> None:
    """
    Generates and writes datasheet data into Snowflake for the specified datasheet.
    It runs either the full etl or incremental etl based on configuration
    """
    datasheet_details = datasheet_details_model.model_dump()
    datasheet_id = datasheet_details["datasheet_id"]
    etl_technique = datasheet_etl_technique(client_id)
    if has_feature(client_id, "show_data_sources_v2"):
        spec_transform = ETLSpecTransformer(datasheet_details["transformation_spec"])
        spec_transform.transform()
        datasheet_details["transformation_spec"] = spec_transform.transformations

    if etl_technique == DatasheetETLTechnique.OPTIMAL:
        if _is_spark_etl_supported_for_datasheet(client_id, datasheet_details_model):
            logger.info("Optimal DatasheetETLTechnique - Spark")
            etl_technique = DatasheetETLTechnique.SPARK
        else:
            logger.info("Optimal DatasheetETLTechnique - Snowflake")
            etl_technique = DatasheetETLTechnique.SNOWFLAKE

    logger.info(f"Choosing etl technique for sheet {datasheet_id} {etl_technique}")
    if etl_technique == DatasheetETLTechnique.SNOWFLAKE:
        if is_spark_dry_run(client_id) and _is_spark_etl_supported_for_datasheet(
            client_id, datasheet_details_model
        ):
            logger.info(f"Running spark dry run ETL for sheet {datasheet_id}")
            subscription_plan = get_client_subscription_plan(client_id)
            queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.SPARK_DRY_RUN.value
            )
            trigger_spark_dry_run_etl.si(
                client_id=client_id,
                databook_id=databook_id,
                datasheet_details=datasheet_details,
                knowledge_date=knowledge_date,
                sync_run_id=sync_run_id,
                e2e_sync_run_id=e2e_sync_run_id,
            ).set(queue=queue_name).apply_async(compression="lzma", serializer="pickle")
        logger.info(f"Running full etl with snowflake for sheet {datasheet_id}")
        apply_transform_and_write_datasheet_data(
            client_id=client_id,
            snowpark_session=snowpark_session,
            databook_id=databook_id,
            datasheet_details=datasheet_details,
            knowledge_date=knowledge_date,
            is_force_invalidate=is_force_invalidate,
            sync_run_id=str(sync_run_id),
        )
        if should_take_ds_snapshot(client_id):
            start_time = time()
            logger.info(f"Taking snapshot for datasheet {datasheet_id}")
            save_ds_snapshot_to_sf_expanded_and_efs(
                str(client_id), databook_id, datasheet_id, str(knowledge_date)
            )
            end = time() - start_time
            logger.info(
                f"Snapshot in Snowflake flat and efs for client_id {client_id}, DB_ID {databook_id}, DS_ID {datasheet_id} and knowledge_date {knowledge_date} took - {end} seconds"
            )
            start_time = time()
            logger.info(f"Writing metadata for datasheet in vector db {datasheet_id}")
            if should_insert_meta_data_to_vec_db(client_id):
                upsert_ds_meta_data_in_vector_db(client_id, databook_id, datasheet_id)
            end = time() - start_time
            logger.info(
                f"Metadata in vector db for client_id {client_id}, DB_ID {databook_id}, DS_ID {datasheet_id} and knowledge_date {knowledge_date} took - {end} seconds"
            )
    elif etl_technique == DatasheetETLTechnique.SPARK:
        logger.info(f"Running full etl with spark for sheet {datasheet_id}")
        transform_datasheet_with_spark(
            client_id,
            databook_id=databook_id,
            datasheet_details=datasheet_details,
            knowledge_date=knowledge_date,
            sync_run_id=sync_run_id,
            e2e_sync_run_id=e2e_sync_run_id,
        )
    elif etl_technique == DatasheetETLTechnique.DUCKDB:
        logger.info(f"Running full etl with duckdb for sheet {datasheet_id}")
    else:
        raise NotImplementedError(
            "The etl technique {etl_technique} is not implemented yet"
        )
    if has_feature(client_id, "enable_custom_workflows"):
        trigger_params = {
            "client_id": client_id,
            "databook_id": databook_id,
            "datasheet_id": datasheet_id,
        }
        datasheet_data_trigger = DatasheetDataTrigger(args=trigger_params)
        datasheet_data_trigger.trigger_event_based_workflows()


# not used anymore
def create_or_replace_expanded_datasheet_table(
    select_query_string, table_name, client_id
):
    """
    Creates or replaces the expanded datasheet table in snowflake
    """
    query_string = f"CREATE OR REPLACE TABLE {table_name} AS {select_query_string}"
    with create_snowpark_session_wrapper(client_id=client_id) as snowpark_session:
        snowpark_session.sql(query_string).collect()


def _datasheet_uses_temporal_splice(transformation_list: list[dict]) -> bool:
    for transformation in transformation_list:
        if transformation["type"] == TransformationType.TEMPORAL_SPLICE.value:
            return True

    return False


def _datasheet_uses_hierarchy(
    client_id, datasheet_details_model: TranformationInputDetails
) -> bool:
    # check for flatten hierarchy transformation and hierarchy based calculated fields
    for transformation in datasheet_details_model.transformation_spec:
        if transformation["type"] == TransformationType.FLATTEN.value:
            return True

    datasheet_variables = DatasheetVariableAccessor(
        client_id
    ).get_objects_by_datasheet_id(datasheet_id=datasheet_details_model.datasheet_id)

    hierarchy_cf_functions = ["GetNthLevelNode", "Hierarchy"]

    variable_extractor = VariableExtractor()
    for datasheet_variable in datasheet_variables:
        if any(
            function
            in variable_extractor.get_functions_used(datasheet_variable.meta_data)
            for function in hierarchy_cf_functions
        ):
            return True

    return False


def _is_spark_etl_supported_for_datasheet(
    client_id,
    datasheet_details_model: TranformationInputDetails,
) -> bool:
    return not _datasheet_uses_temporal_splice(
        datasheet_details_model.transformation_spec
    ) and not _datasheet_uses_hierarchy(client_id, datasheet_details_model)
