"""
Orchestrator for datasheet generation.  It is responsible for putting together all the tasks
required for generating datasheets in parallel
"""

import logging
import uuid
from uuid import UUID

from celery import chain, shared_task

from commission_engine.accessors.client_accessor import get_client_subscription_plan
from common.celery.celery_base_task import Ever<PERSON>eleryBaseTask
from everstage_etl.databook_etl.datasheet_etl_types import DatasheetExecutionContext
from interstage_project.celery import TaskGroupEnum
from interstage_project.threadlocal_log_context import set_threadlocal_context
from interstage_project.utils import get_queue_name_respect_to_task_group, is_pytest_env

from ..datasheet_generation.datasheet_dag_executor import datasheet_custom_dag_executor
from .post_generate import datasheet_generation_post_process
from .pre_generate import datasheet_generation_pre_process
from .utils import (
    execution_info_for_multiple_datasheets,
    wait_for_datasheets_sync_to_process,
)

logger = logging.getLogger(__name__)


def get_datasheet_wrapper_sync_task(
    client_id: int,
    e2e_sync_run_id: UUID,
    sync_type: str | None,
    databook_ids=None,
    datasheet_id=None,
    notification_email_id=None,
    skip_archived_books=True,
    is_force_invalidate=False,
    is_flow_eligible_for_trigger_only_associated_ds=False,
):
    """
    Sets up code to run one or more datasheet syncs.  Everything is modelled as a 'DATASHEET_WRAPPER_SYNC'.
    Both databook_ids and datasheet_id cannot be specified at the same time.

    If datasheet_id is specified, then only that datasheet and ancestor stale datasheet will be generated.
    If databook_ids is specified, then all stale datasheets in the databooks will be generated.

    Within each databook, we run the weakly connected stale datasheets order wise parallelly.
    """

    logger.info(
        f"BEGIN: Datasheet wrapper sync for client_id {client_id} e2e_sync_run_id {e2e_sync_run_id}"
    )
    if datasheet_id and databook_ids:
        raise ValueError(
            "Both databook_ids and datasheet_id cannot be specified at the same time."
        )

    e2e_sync_run_id = uuid.uuid4() if e2e_sync_run_id is None else e2e_sync_run_id

    subscription_plan = get_client_subscription_plan(client_id=client_id)
    databook_queue_name = get_queue_name_respect_to_task_group(
        client_id=client_id,
        subscription_plan=subscription_plan,
        task_group=TaskGroupEnum.DATABOOK.value,
    )

    task = run_datasheets_sync.si(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        databook_ids=databook_ids,
        datasheet_id=datasheet_id,
        notification_email_id=notification_email_id,
        skip_archived_books=skip_archived_books,
        is_force_invalidate=is_force_invalidate,
        databook_queue_name=databook_queue_name,
        is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
    ).set(queue=databook_queue_name)
    logger.info(
        f"END: Datasheet wrapper sync for client_id {client_id} e2e_sync_run_id {e2e_sync_run_id}"
    )
    return chain(task)


@shared_task(base=EverCeleryBaseTask)
def run_datasheets_sync(
    client_id: int,
    e2e_sync_run_id: UUID,
    sync_type: str | None = None,
    databook_ids=None,
    datasheet_id=None,
    notification_email_id=None,
    skip_archived_books=True,
    is_force_invalidate=False,
    databook_queue_name=None,
    is_flow_eligible_for_trigger_only_associated_ds=False,
):
    """
    This function orchestrates the generation of datasheets.  An execution plan is created and
    then datasheets are generated per the execution plan.  Bookkeeping before and after datasheet
    generation is also taken care in this method
    """
    # It is important this function is called inside this task because when datasheet_wrapper_sync
    # is part of a wider orchestration (end to end sync - starting with upstream), the upstream sync
    # may change the staleness of a few datasheets.  Calling this function here ensures that we
    # use the latest staleness information
    wrapper_sync_run_id = uuid.uuid4()

    set_threadlocal_context(
        {
            "client_id": client_id,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": wrapper_sync_run_id,
            "sync_type": sync_type,
            "skip_archived_books": skip_archived_books,
            "is_force_invalidate": is_force_invalidate,
            "databook_queue_name": databook_queue_name,
            "datasheet_id": datasheet_id,
            "databook_ids": databook_ids,
            "is_flow_eligible_for_trigger_only_associated_ds": is_flow_eligible_for_trigger_only_associated_ds,
        }
    )
    all_datasheet_details: DatasheetExecutionContext = (
        execution_info_for_multiple_datasheets(
            client_id=client_id,
            e2e_sync_run_id=str(e2e_sync_run_id),
            databook_ids=databook_ids,
            datasheet_id=datasheet_id,
            sync_type=sync_type,
            skip_archived_books=skip_archived_books,
            is_flow_eligible_for_trigger_only_associated_ds=is_flow_eligible_for_trigger_only_associated_ds,
        )
    )
    logger.info(
        "The execution plan for the e2e_sync_run_id %s is %s",
        e2e_sync_run_id,
        all_datasheet_details.datasheet_execution_plan,
    )

    # If datasheet generation is not possible, this function will raise an exception
    # and this task will fail
    datasheet_generation_pre_process(
        all_datasheet_details=all_datasheet_details,
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=sync_type,
        wrapper_sync_run_id=wrapper_sync_run_id,
        notification_email_id=notification_email_id,
    )

    datasheet_custom_dag_executor(
        client_id=client_id,
        e2e_sync_run_id=str(e2e_sync_run_id),
        sync_type=sync_type,
        is_force_invalidate=is_force_invalidate,
    )

    if not is_pytest_env():
        # Wait for all the datasheets to be processed
        # This is a blocking call
        wait_for_datasheets_sync_to_process(
            client_id=client_id,
            e2e_sync_run_id=str(e2e_sync_run_id),
            sync_type=sync_type,
            datasheet_execution_context=all_datasheet_details,
        )

    datasheet_generation_post_process(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        wrapper_sync_run_id=wrapper_sync_run_id,
        sync_type=sync_type,
        notification_email_id=notification_email_id,
    )
