import datetime
import logging
import os
import time
import traceback
import uuid
from uuid import UUID

from django.core.cache import cache
from django.db import IntegrityError
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.accessors.client_accessor import (
    get_client_subscription_plan,
    get_datasheet_sync_check_interval,
    get_datasheet_sync_check_threshold,
    is_generate_associated_datasheets_only,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    DatabookETLStatusAccessor,
    ETLLockAccessor,
    ReportETLStatusReaderAccessor,
)
from commission_engine.database.snowflake_query_utils import (
    drop_tables_matching_pattern,
)
from commission_engine.services import etl_sync_status_service
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.databook_etl_sync_status_service import set_primary_kd
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    get_datasheets_for_databooks_by_sync_type,
)
from commission_engine.services.etl_sync_status_service import get_etl_params
from commission_engine.utils.general_data import ETL_STATUS, SYNC_OBJECT
from commission_engine.utils.general_utils import log_time_taken
from everstage_etl.databook_etl.datasheet_etl_types import DatasheetExecutionContext
from everstage_etl.services.datasheet_execution_context_service import (
    is_sync_status_polling_threshold_breached,
    store_datasheet_execution_context,
    update_datasheet_execution_context,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.custom_exceptions.custom_exceptions import FailedToAcquireLock
from spm.services.databook_services import (
    get_all_databook_ids,
    remove_archived_databooks,
)
from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph
from spm.services.datasheet_services import remove_archived_datasheets

logger = logging.getLogger(__name__)


class DatasheetLockedException(Exception):
    def __init__(self, client_id, e2e_sync_run_id, sync_run_id, datasheet_id):
        self.message = f"Lock already exists for the client_id: {client_id}, e2e_sync_run_id: {e2e_sync_run_id}, sync_run_id: {sync_run_id}, datasheet_id: {datasheet_id}, So skipping the lock acquisition and datasheet generation"
        super().__init__(self.message)


class DatabookEtlStatusWrapper:
    def __init__(
        self,
        client_id,
        e2e_sync_run_id,
        sync_run_id=uuid.uuid4(),
        sync_type=None,
        databook_id=None,
        datasheet_ids=None,
    ):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id
        self.sync_type = sync_type
        self.databook_id = databook_id
        self.datasheet_ids = datasheet_ids
        self.databook_etl_accessor = DatabookETLStatusAccessor(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=self.sync_run_id,
        )

    def change_status_to_started_state(self) -> None:
        logger.info("Marking wrapper task as started")
        wrapper_primary_kd = make_aware(datetime.datetime.now())
        wrapper_task = SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value
        try:
            params = {
                "task": wrapper_task,
                "primary_kd": wrapper_primary_kd,
                "sync_status": ETL_STATUS.STARTED.value,
                "sync_start_time": make_aware(datetime.datetime.now()),
                "sync_type": self.sync_type,
            }
            self.databook_etl_accessor.insert_sync_status(params)
            logger.info("marked wrapper task as started")
        except Exception as exc:
            failed_timestamp = make_aware(datetime.datetime.now())
            self.databook_etl_accessor.change_status_failed_to_acquire_lock(
                timestamp=failed_timestamp, sync_run_log=None
            )
            etl_sync_status_service.change_status_to_failed(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                timestamp=failed_timestamp,
                should_update_completion_time=True,
                email_id=None,
            )
            raise FailedToAcquireLock(
                client_id=self.client_id,
                task=wrapper_task,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
            ) from exc

    def change_status_to_locked_state(self) -> None:
        """
        This method is used to change the status of the task to locked state
        """
        self.databook_etl_accessor.change_status_to_lock_acquired()

    def change_status_to_failed_state(self, error_code: str = "wrapper task failed"):
        start_time = self.databook_etl_accessor.get_task_start_time()
        sync_run_log = {
            "start_time": str(start_time),
            "task": SYNC_OBJECT.DATASHEET_WRAPPER_SYNC.value,
            "end_time": str(make_aware(datetime.datetime.now())),
            "status": ETL_STATUS.FAILED.value,
            "error_info": error_code,
        }
        self.databook_etl_accessor.change_status_to_failed(
            sync_run_log=sync_run_log,
            timestamp=make_aware(datetime.datetime.now()),
        )

    def change_status_to_complete_state(self, sync_run_log=None) -> None:
        self.databook_etl_accessor.change_status_to_complete(
            timestamp=make_aware(datetime.datetime.now()),
            sync_run_log=sync_run_log,
        )

    def bulk_acquire_datasheet_lock(self, all_datasheet_details):
        """
        This method is used to acquire lock on datasheets in bulk
        """
        databook_etl_status_bulk_lock_dict = {
            "client_id": self.client_id,
            "e2e_sync_run_id": self.e2e_sync_run_id,
            "task": SYNC_OBJECT.DATASHEET_SYNC.value,
            "sync_start_time": make_aware(datetime.datetime.now()),
            "sync_status": ETL_STATUS.STARTED.value,
            "sync_type": self.sync_type,
            "audit": os.environ.get("EVERSTAGE_HOST_INSTANCE_ID"),
        }

        datasheet_lock_records = [
            {
                **databook_etl_status_bulk_lock_dict,
                "datasheet_id": datasheet_id,
                "sync_run_id": all_datasheet_details.datasheet_details[
                    datasheet_id
                ].sync_run_id,
                "databook_id": all_datasheet_details.datasheet_details[
                    datasheet_id
                ].databook_id,
            }
            for datasheet_id in all_datasheet_details.stale_datasheet_list
        ]
        temp_sync_run_id = uuid.uuid4()
        DatabookETLStatusAccessor(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=temp_sync_run_id,
        ).bulk_datasheet_lock(datasheet_records=datasheet_lock_records)

    def bulk_fail_datasheet_lock(
        self, sync_run_ids, parent_datasheet_detail, error_info=None, error_details=None
    ) -> None:
        """
        This method is used to fail datasheets in bulk
        """
        self.databook_etl_accessor.make_bulk_fail_datasheets(
            sync_run_ids=sync_run_ids,
            parent_datasheet_detail=parent_datasheet_detail,
            error_info=error_info,
            error_details=error_details,
        )

    def set_primary_kd(self, knowledge_date) -> None:
        """
        This method is used to set primary kd for the single datasheet
        """
        set_primary_kd(
            client_id=self.client_id,
            e2e_sync_run_id=self.e2e_sync_run_id,
            sync_run_id=self.sync_run_id,
            knowledge_date=knowledge_date,
        )

    def get_record_sync_status(self) -> str:
        """
        Returns the sync_status of the datasheet for the given client_id ,e2e_sync_run_id
        and sync_run_id
        """
        sync_status = self.databook_etl_accessor.get_record_sync_status()
        return sync_status

    def fetch_records_for_specified_columns(self, columns=None) -> list:
        """
        This function is used to get the records for the given columns
        based on e2e_sync_run_id

        If the columns are not specified, then it will return records for all the columns

        columns = ["datasheet_id" , "sync_run_id" , "databook_id"]

        Returns:

        [
            {
                "datasheet_id" :
                "sync_run_id" :
                "databook_id" :
            },
            {
                "datasheet_id" :
                "sync_run_id" :
                "databook_id" :
            },
            ...
        ]

        """
        return self.databook_etl_accessor.fetch_records_for_specified_columns(
            columns=columns
        )

    def has_pending_datasheet_generation_wrapper(self, datasheet_ids: list) -> bool:
        """
        Checks if any of the given datasheet_ids has pending datasheet generation
        """
        has_pending_ds_generation = (
            self.databook_etl_accessor.has_pending_datasheet_generation_accessor(
                datasheet_ids=datasheet_ids
            )
        )
        logger.info(
            f"For e2e_sync_run_id - {self.e2e_sync_run_id} and datasheet_ids - {datasheet_ids} has_pending_datasheet_generation - {has_pending_ds_generation}"
        )
        return has_pending_ds_generation

    def change_status(self, status: str) -> None:
        """
        Changes the status of the datasheet to the given input status.
        """
        self.databook_etl_accessor.change_status(status=status)

    def change_all_pending_datasheets_to_failed(
        self, sync_type: str | None = None
    ) -> None:
        """
        Changes the status of all the pending datasheets to failed for the given e2e_sync_run_id.
        If sync_type is provided, it will filter the datasheets by the given sync_type.
        """
        self.databook_etl_accessor.change_all_pending_datasheets_to_failed(
            sync_type=sync_type
        )


class ETLLockWrapper:
    def __init__(
        self, client_id: int, e2e_sync_run_id: UUID, sync_run_id: UUID
    ) -> None:
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id
        self.etl_lock_accessor = ETLLockAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

    def acquire_lock(self, datasheet_id) -> None:
        """
        This method is used to acquire etl_lock for a datasheet
        """
        try:
            lock_name_delimiter = "#"
            task = SYNC_OBJECT.DATASHEET_SYNC.value
            lock_name = str(self.client_id) + lock_name_delimiter + datasheet_id
            params = {
                "object_id": task,
                "lock_name": lock_name,
                "is_active": True,
            }
            self.etl_lock_accessor.insert_lock(params)
        except IntegrityError as exc:
            raise DatasheetLockedException(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                datasheet_id=datasheet_id,
            ) from exc

    def release_lock(self) -> None:
        """
        This method is used to release etl_lock for a datasheet
        """
        self.etl_lock_accessor.release_lock()

    def fetch_records_for_specified_columns(self, columns=None) -> list:
        """
        This function is used to get the records for the given columns
        based on e2e_sync_run_id

        If the columns are not specified, then it will return records for all the columns

        columns = ["datasheet_id" , "is_lock_active" , "databook_id"]

        Returns:

        [
            {
                "datasheet_id" :
                "is_lock_active" :
                "databook_id" :
            },
            {
                "datasheet_id" :
                "is_lock_active" :
                "databook_id" :
            },
            ...
        ]

        """
        return self.etl_lock_accessor.fetch_records_for_specified_columns(
            columns=columns
        )


def get_primary_kd(client_id: int, e2e_sync_run_id: UUID, sync_type: str):
    """
    This function is used to get the primary kd for the given sync_type
    """
    sync_type_to_task = {
        SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value: SYNC_OBJECT.SYSTEM_REPORT_WRAPPER_SYNC.value,
        SYNC_OBJECT.DATABOOK_COMMISSION_OBJECT.value: SYNC_OBJECT.COMMISSION_REPORT_WRAPPER_SYNC.value,
        SYNC_OBJECT.DATABOOK_SETTLEMENT_OBJECT.value: SYNC_OBJECT.SETTLEMENT_REPORT_WRAPPER_SYNC.value,
        SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value: SYNC_OBJECT.INTER_OBJECT_WRAPPER_SYNC.value,
        SYNC_OBJECT.DATABOOK_FORECAST_OBJECT.value: SYNC_OBJECT.FORECAST_REPORT_WRAPPER_SYNC.value,
        SYNC_OBJECT.DATABOOK_INTER_FORECAST_OBJECT_SYNC.value: SYNC_OBJECT.INTER_FORECAST_REPORT_WRAPPER_SYNC.value,
    }
    task = sync_type_to_task.get(sync_type, SYNC_OBJECT.REPORT_WRAPPER_SYNC.value)

    cache_key = f"{client_id}_{e2e_sync_run_id}_{task}"
    primary_kd = cache.get(cache_key)

    if not primary_kd:
        logger.info("CACHE MISS. CACHE KEY - %s", cache_key)
        record = ReportETLStatusReaderAccessor(client_id).get_sync_time_for_e2e(
            e2e_sync_run_id=e2e_sync_run_id, task=task
        )
        if record:
            primary_kd = record.sync_completion_time
        if not primary_kd:
            primary_kd = timezone.now()
        cache.set(cache_key, primary_kd)

    logger.info(
        "primary_kd for client_id %s, e2e_sync_run_id %s is %s",
        client_id,
        e2e_sync_run_id,
        primary_kd,
    )
    return primary_kd


@log_time_taken(level=logging.INFO)
def pre_process_datasheets_with_additional_source_variables(
    client_id: int,
    datasheet_details: dict,
):
    """
    This function is used to pre-process datasheets with additional source primary variables

    Note: This function should be triggered before the datasheet generation starts.
    """
    if not datasheet_details:
        return

    if not has_feature(client_id, "show_data_sources_v2"):
        return

    from everstage_ddd.datasheet.services.datasheet_service import (
        update_datasheets_with_additional_source_variables,
    )

    try:
        # Sort the datasheets by the datasheet order
        ordered_datasheet_ids = sorted(
            datasheet_details.keys(), key=lambda x: datasheet_details[x].datasheet_order
        )

        logger.info(
            "BEGIN: Pre-processing datasheets with additional source primary variables for client_id %s and datasheet_ids %s",
            client_id,
            ordered_datasheet_ids,
        )
        update_datasheets_with_additional_source_variables(
            client_id=client_id, datasheet_ids=ordered_datasheet_ids
        )
        logger.info(
            "END: Pre-processing datasheets with additional source primary variables for client_id %s",
            client_id,
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(
            "Error in pre-processing datasheets with additional source primary variables for client_id %s",
            client_id,
            exc_info=e,
        )


def execution_info_for_multiple_datasheets(
    client_id: int,
    e2e_sync_run_id: str,
    skip_archived_books: bool = True,
    sync_type: str | None = None,
    databook_ids: list | None = None,
    datasheet_id: str | None = None,
    is_flow_eligible_for_trigger_only_associated_ds: bool = False,
) -> DatasheetExecutionContext:
    """
    Returns the execution plan for one or more datasheets that is being generated in parallel
    """
    logger.info(
        "For client_id %s, the skip_archived_books is: %s",
        client_id,
        skip_archived_books,
    )

    # Trigger only datasheet that are on datasheet_needs_to_be_refreshed list and all of its stale parent datasheets
    etl_params = get_etl_params(client_id=client_id, e2e_sync_run_id=e2e_sync_run_id)
    is_generate_associated_ds_only = is_generate_associated_datasheets_only(
        client_id=client_id
    )
    if (
        etl_params is not None
        and "datasheet_needs_to_be_refreshed" in etl_params
        and is_generate_associated_ds_only is True
        and is_flow_eligible_for_trigger_only_associated_ds is True
    ):
        datasheet_ids = etl_params.get("datasheet_needs_to_be_refreshed")
        logger.info(f"Fetched datasheet_ids from etl_params {datasheet_ids}")
        if skip_archived_books is True:
            datasheet_ids = remove_archived_datasheets(
                client_id=client_id, datasheet_ids=list(datasheet_ids)
            )
            logger.info(f"After removing archived datasheets {datasheet_ids}")
    elif datasheet_id:
        datasheet_ids = [datasheet_id]
    else:
        # If databook_ids is None then it means it is client level sync
        # so we need to fetch all databook_ids.
        if databook_ids is None:
            databook_ids: list[UUID] = get_all_databook_ids(
                client_id=client_id, skip_archived_books=skip_archived_books
            )

        elif skip_archived_books is True:
            databook_ids: list[UUID] = remove_archived_databooks(
                client_id=client_id, databook_ids=databook_ids
            )

        datasheet_objects: list = get_datasheets_for_databooks_by_sync_type(
            client_id=client_id, databook_ids=databook_ids, sync_type=sync_type
        )
        datasheet_ids: list = [
            str(datasheet_object.datasheet_id) for datasheet_object in datasheet_objects
        ]

    datasheet_graph = DataSheetGraph(
        client_id=client_id, include_stale_information_query=True
    )
    stale_details = datasheet_graph.get_stale_datasheets_to_generate(
        datasheet_ids=datasheet_ids, e2e_sync_run_id=e2e_sync_run_id
    )

    datasheet_details = datasheet_graph.datasheet_details(
        datasheet_ids=stale_details.stale_datasheet_list
    )
    root_stale_datasheet_ids: set[str] = datasheet_graph.root_stale_datasheet_ids(
        stale_datasheet_list=stale_details.stale_datasheet_list
    )

    subscription_plan = get_client_subscription_plan(client_id=client_id)
    datasheet_queue_name = get_queue_name_respect_to_task_group(
        client_id=client_id,
        subscription_plan=subscription_plan,
        task_group=TaskGroupEnum.DATABOOK.value,
    )
    # TODO: Remove this commented logic once we move this to `validate and save` datasheet_v2 api call.
    # pre_process_datasheets_with_additional_source_variables(
    #     client_id=client_id, datasheet_details=datasheet_details
    # )
    result = {
        "client_id": client_id,
        "datasheet_execution_plan": stale_details.stale_datasheet_execution_plan,
        "datasheet_details": datasheet_details,
        "stale_datasheet_list": stale_details.stale_datasheet_list,
        "total_datasheets_count": stale_details.total_datasheet_count,
        "root_stale_datasheet_ids": root_stale_datasheet_ids,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_type": sync_type,
        "datasheet_queue_name": datasheet_queue_name,
        "skipped_stale_datasheets": stale_details.skipped_stale_datasheets,
    }
    datasheet_dict_class = DatasheetExecutionContext(**result)
    # Store the datasheet execution context in the postgres table
    store_datasheet_execution_context(client_id=client_id, context=datasheet_dict_class)
    return datasheet_dict_class


def drop_calc_fields_temp_tables(datasheet_id, client_id):
    """
    This function is used to drop the tables created for calc fields computation
    """
    # Drop the tables created for calc fields computation
    logger.info("Removing tables created for calc fields computation")
    calc_fields_temp_table_pattern = f"%_calc_field_{datasheet_id}"
    calc_field_result_temp_table_pattern = f"%_calc_field_result_{datasheet_id}"

    logger.info(f"Drop tables matching pattern - {calc_fields_temp_table_pattern}")

    calc_fields_temp_tables = drop_tables_matching_pattern(
        calc_fields_temp_table_pattern, client_id=client_id
    )
    calc_field_result_temp_tables = drop_tables_matching_pattern(
        calc_field_result_temp_table_pattern, client_id=client_id
    )

    calc_field_tables = calc_fields_temp_tables + calc_field_result_temp_tables

    logger.info(f"Tables removed for calc fields computation - {calc_field_tables}")


def get_datasheet_sync_check_interval_and_threshold(client_id: int) -> tuple[int, int]:
    """
    Returns the interval and threshold for checking datasheet sync status.
    """
    datasheet_sync_status_check_interval = get_datasheet_sync_check_interval(
        client_id=client_id
    )
    datasheet_sync_status_check_threshold = get_datasheet_sync_check_threshold(
        client_id=client_id
    )

    return datasheet_sync_status_check_interval, datasheet_sync_status_check_threshold


def wait_for_datasheets_sync_to_process(
    client_id: int,
    e2e_sync_run_id: str,
    sync_type: str | None,
    datasheet_execution_context: DatasheetExecutionContext,
):
    """
    Check if all individual datasheets are processed.
    If not, wait until they are processed or the threshold limit is exceeded.
    """
    stale_datasheet_ids = datasheet_execution_context.stale_datasheet_list

    datasheet_sync_status_check_interval, datasheet_sync_status_check_threshold = (
        get_datasheet_sync_check_interval_and_threshold(client_id=client_id)
    )

    databook_etl_status_wrapper = DatabookEtlStatusWrapper(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id
    )
    while (
        databook_etl_status_wrapper.has_pending_datasheet_generation_wrapper(
            datasheet_ids=list(stale_datasheet_ids)
        )
        is True
        and datasheet_sync_status_check_threshold > 0
        and is_sync_status_polling_threshold_breached(
            client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_type=sync_type
        )
        is False
    ):
        logger.info(
            f"Datasheets are not processed yet. Waiting for {datasheet_sync_status_check_interval} seconds before checking again."
        )
        datasheet_sync_status_check_threshold -= datasheet_sync_status_check_interval
        time.sleep(datasheet_sync_status_check_interval)

    if datasheet_sync_status_check_threshold <= 0:
        # Mark is_polling_threshold_breached to True in the datasheet_execution_context table
        update_datasheet_execution_context(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_type=sync_type,
            update_data={"is_polling_threshold_breached": True},
        )
    return datasheet_sync_status_check_threshold


def has_pending_datasheet_generation(
    datasheet_execution_context: DatasheetExecutionContext, datasheet_ids: list
) -> bool:
    """
    Checks if any of the given datasheet_ids has pending datasheet generation
    """
    client_id = datasheet_execution_context.client_id
    e2e_sync_run_id = datasheet_execution_context.e2e_sync_run_id
    logger.info(
        f"BEGIN: has_pending_datasheet_generation for e2e_sync_run_id - {e2e_sync_run_id} and datasheet_ids - {datasheet_ids}"
    )
    databook_etl_status_wrapper = DatabookEtlStatusWrapper(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
    )
    pending_datasheet_generation = (
        databook_etl_status_wrapper.has_pending_datasheet_generation_wrapper(
            datasheet_ids=datasheet_ids
        )
    )
    logger.info(
        f"END: has_pending_datasheet_generation for e2e_sync_run_id - {e2e_sync_run_id} and datasheet_ids - {datasheet_ids} pending_datasheet_generation - {pending_datasheet_generation}"
    )
    return pending_datasheet_generation


def has_datasheet_been_picked(
    datasheet_execution_context: DatasheetExecutionContext,
    e2e_sync_run_id: str | None,
    datasheet_id: str,
) -> bool:
    """
    Checks if the given datasheet has been picked for processing.
    """
    sync_run_id = datasheet_execution_context.datasheet_details[
        datasheet_id
    ].sync_run_id
    client_id = datasheet_execution_context.client_id
    logger.info(
        f"BEGIN: has_datasheet_been_picked for client_id - {client_id} ,datasheet_id - {datasheet_id} and e2e_sync_run_id - {e2e_sync_run_id} sync_run_id - {sync_run_id}"
    )
    databook_etl_status_wrapper = DatabookEtlStatusWrapper(
        client_id=client_id, e2e_sync_run_id=e2e_sync_run_id, sync_run_id=sync_run_id
    )
    current_datasheet_status = databook_etl_status_wrapper.get_record_sync_status()
    if current_datasheet_status in [
        ETL_STATUS.COMPLETE.value,
        ETL_STATUS.FAILED.value,
        ETL_STATUS.IN_PROGRESS.value,
        ETL_STATUS.LOCKED.value,
    ]:
        result = True
    else:
        result = False
    logger.info(
        f"END: has_datasheet_been_picked for client_id - {client_id} ,datasheet_id - {datasheet_id} and e2e_sync_run_id - {e2e_sync_run_id} result - {result}"
    )
    return result


def change_datasheet_status(
    datasheet_execution_context: DatasheetExecutionContext,
    datasheet_id: str,
    status: str,
):
    """
    Marks the current datasheet as in progress.
    """
    datasheet_details = datasheet_execution_context.datasheet_details[datasheet_id]
    e2e_sync_run_id = datasheet_execution_context.e2e_sync_run_id
    sync_run_id = datasheet_details.sync_run_id
    client_id = datasheet_execution_context.client_id
    logger.info(
        f"BEGIN: Marking datasheet as in progress for datasheet_id - {datasheet_id} and e2e_sync_run_id - {e2e_sync_run_id} sync_run_id - {sync_run_id}"
    )
    databook_etl_status_wrapper = DatabookEtlStatusWrapper(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_run_id=sync_run_id,
    )
    databook_etl_status_wrapper.change_status(status=status)
    logger.info(
        f"END: Marking datasheet as in progress for datasheet_id - {datasheet_id} and e2e_sync_run_id - {e2e_sync_run_id} sync_run_id - {sync_run_id}"
    )
