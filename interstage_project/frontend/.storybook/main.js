export default {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|ts|tsx)"],

  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    // "@storybook/addon-interactions",
    // "storybook-dark-mode",
    // "@storybook/addon-docs",
    "@storybook/addon-designs",
    // "@storybook/addon-mdx-gfm",
    // for themes switcher
    "@storybook/addon-themes",
    "storybook-addon-pseudo-states",
    "@chromatic-com/storybook",
  ],

  framework: {
    name: "@storybook/react-vite",
    options: {
      builder: {
        viteConfigPath: ".storybook/storybook.vite.config.js",
      },
    },
  },

  docs: {},

  core: { builder: "@storybook/builder-vite" },

  staticDirs: ["../public"],

  typescript: {
    reactDocgen: "react-docgen",
  },
};
