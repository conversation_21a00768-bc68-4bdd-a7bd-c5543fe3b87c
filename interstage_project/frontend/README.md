# Everstage Frontend

Welcome to the Everstage Frontend - a CRA app with some minimal configuration overrides in `craco.config.js`.

## Code Organization

Code in the frontend will be organized as below - highlighting the purpose of important folders -

```
- src
    - Api (code to make REST calls)
    - Enums (constants used across multiple features/components)
    - everstage-supabase (our supabase integration)
    - GlobalStores (state management - new code should co-locate state management close to the screen itself - for eg. inside `src/features`)
    - i18n.js (i18n support)
    - Utils (utilities)
    - tailwind.config.js (tailwind configuration)
    - craco.config.js (create react app configuration)
    - package.json

    - v2 (the components introduced in v2 - these will be moved out to the top level `src` folder soon)
      - components (all the global components that are re-usable across multiple features)
      - features (all the features in the app - each feature will have its own folder)
      - stories (storybook stories for the components)
      - legacy (short-lived folder that will be removed after the new commission plan builder UI)
```

## Guidelines

- `v2/components` should be pure React components (driven by props) that are re-usable in multiple pages across the app
- Co-locate features (or route or page components) and state management for them in the same folder (makes it easy for navigation and will localize all code changes in case of bug fixes). You will see API calls being tracked in `src/Api` - but it is better to move them directly to your feature folder - for eg. `src/v2/features/crystal/api.js`
- Keep client side, component specific state thin and prefer to get this state from the backend as much as possible with `react-query`
- Use mobx for global state management or when there is clear 'props-drilling' (passing the same state through a tree of multiple components). Otherwise prefer to use `react-query` + React / React Context for state management
- Use tailwindcss for styling. All design tokens must be defined in `src/tailwind.config.js`
- Eventually when all components are migrated, legacy code will be deleted and the v2 folder will be collapsed into `src` directly
- Prefer flat structures and lowercaps for file and folder names - eg. use `features/crystal/CrystalDashboard.js` instead of `Features/Crystal/CrystalDashboard.js`
- One feature folder should not import from another (_eslint-enforced_)
- icons must come [@everstage/evericons](https://github.com/Everstage/evericons/pkgs/npm/evericons). To be able to use this you can create your own github token or you can use the one from [this](https://interstage.atlassian.net/wiki/spaces/TECH/pages/917579/Dev+Onboarding#C.-Credentials) confluence page. Then run the following command:

  ```bash
  echo "//npm.pkg.github.com/:_authToken=<your-classic-github-access-token>" >> ~/.npmrc
  ```

  This is a one time exercise, unless the token gets revoked or becomes invalid for some reason.

- For adding new image assets, follow the procedure [here](https://everstage.slack.com/archives/C01FJERETRV/p1692894531916629)
- All new files are run against a stricter ESLint check (`eslint.ideal.js`)

## Development

While building a new feature, make sure to come up with the component design first - what components are needed, what are their props, what should go into the store etc. Only after gettting this reviewed, proceed for development

## Documentation

- If you want to contribute common components (`src/v2/components`), the quality standards are much higher
- A description of what the component's responsibilities are is required ([example](https://github.com/Everstage/everstage-spm/blob/1e740167203b45ee388e2cd7e6f03103fc95b6fe/interstage_project/frontend/src/everstage-supabase/custom-hook.js#L84))
- You can document props with jsdoc so that VSCode Intellisense can pick it up
- In case of places where props are passed across multiple components, document wherever it makes the most sense - and refer to this from the other places
- In case of new components we should also create a Storybook story for it. You can check the current stories [here](https://master--6426e13dda8fee4cd24d46a1.chromatic.com/).

## Testing

- As of 2023-08-01, the expectation is for each new component to have one or more of the following -

1.  playwright based test
2.  storybook example
3.  unit test

- We will gradually enforce this rule for existing components as well

## Component Driven Development

- Use Storybook's extensively for development - it will help you clearly visualize component state and props
- You can start storybook locally by running `npm run storybook`
- Our storybook is deployed [here](https://master--6426e13dda8fee4cd24d46a1.chromatic.com/).
- If you are adding new styles, make sure to add it to Storybook and get it reviewed by designers

## Chrome extension

- Everstage app is built using `esbuild-web.config.mjs`, The chrome extension uses the same css plugins just changing entry points and other variables required to build. This uses `esbuild-chrome-extension.config.mjs`
- To use the chrome extension locally. Follow these steps:

1. Build the extension using this command `npm run build:chrome-extension` for local and non-dev environments
2. Build the extension using 'VARIANT_TYPE', else the extension wont be built.

- `VARIANT_TYPE=PROD npm run build:chrome-extension` for production environments
- `VARIANT_TYPE=DEV npm run build:chrome-extension` for non-production environments

3. To build the extension with any attribute changes from manifest, add `--` followed by the key flag such as `--version='1.0.1'` to change version as postfix in the build command.
   Example: `VARIANT_TYPE=DEV npm run build:chrome-extension -- --version=1.0.0 --name='Everstage Extension for Non-prod'`
4. Go to `chrome://extensions` in any chromium browser and enable developer mode.
5. Upload the `chrome` folder present in root dir to chrome by selecting `Load unpacked`
6. Refresh Gmail to use the extension.
