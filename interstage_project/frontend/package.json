{"name": "interstage-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.2.2", "@apollo/client": "3.11.8", "@auth0/auth0-react": "^1.10.0", "@ckeditor/ckeditor5-react": "^9.3.0", "@emotion/react": "^11.8.1", "@emotion/styled": "^11.8.1", "@everstage/ev-pdf-lib": "^0.0.55", "@everstage/evericons": "^0.0.542", "@everstage/superset-ui-embedded-sdk": "^0.1.0-alpha.10", "@floating-ui/dom": "^1.6.3", "@floating-ui/react": "^0.26.16", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-popover": "^1.0.5", "@radix-ui/react-progress": "^1.0.2", "@radix-ui/react-tooltip": "^1.1.8", "@sentry/react": "^7.119.2", "@sentry/tracing": "^7.20.0", "@supabase/supabase-js": "^2.8.0", "@tanstack/react-virtual": "^3.10.8", "@tsparticles/react": "^3.0.0", "@vitejs/plugin-react": "^4.3.4", "ag-grid-community": "^32.0.2", "ag-grid-enterprise": "^32.0.2", "ag-grid-react": "^32.0.2", "antd": "4.10.3", "antd-img-crop": "^4.12.2", "base-64": "^1.0.0", "buffer": "^6.0.3", "canvas-confetti": "^1.9.3", "change-case": "^4.1.1", "ckeditor5": "^43.2.0", "class-variance-authority": "^0.7.0", "classix": "^2.1.30", "classnames": "^2.3.2", "core-js": "^3.37.1", "d3-hierarchy": "^3.1.2", "d3-timer": "^3.0.1", "dagre": "^0.8.5", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "detect-file-encoding-and-language": "^2.4.0", "dotenv": "^16.3.1", "eslint-import-resolver-custom-alias": "^1.3.2", "file-saver": "^2.0.5", "framer-motion": "^6.2.8", "fusioncharts": "^3.15.3", "graphql": "^16.8.1", "handlebars": "^4.7.8", "highcharts": "^10.0.0", "highcharts-react-official": "^3.1.0", "html-to-image": "^1.11.11", "http-proxy": "^1.18.1", "i18next": "^22.4.14", "i18next-http-backend": "^2.2.0", "idb-keyval": "^6.2.0", "immer": "^10.0.2", "immutability-helper": "^3.1.1", "json-to-graphql-query": "^2.2.3", "lodash": "^4.17.15", "lottie-react": "^2.4.0", "lottie-web": "^5.12.2", "lz-string": "^1.5.0", "mobx": "^6.10.2", "mobx-react": "^9.0.1", "mobx-utils": "^5.5.7", "moment": "^2.30.1", "moment-timezone": "^0.5.35", "pako": "^2.1.0", "postcss-mixins": "^10.0.0", "postcss-scss": "^4.0.9", "prop-types": "^15.8.1", "rc-virtual-list": "^3.4.10", "react": "^17.0.0", "react-avatar": "^3.9.7", "react-beautiful-dnd": "^13.1.0", "react-copy-to-clipboard": "^5.1.0", "react-csv": "2.0.3", "react-dnd": "14.0.2", "react-dnd-html5-backend": "14.0.0", "react-dom": "^17.0.0", "react-fusioncharts": "^3.1.2", "react-grid-layout": "1.3.4", "react-helmet": "^6.1.0", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-html-parser": "^2.0.2", "react-i18next": "^12.2.0", "react-idle-timer": "^4.6.4", "react-markdown": "^8.0.6", "react-otp-input": "^3.1.1", "react-pdf": "9.1.0", "react-portal": "^4.2.2", "react-query": "^3.38.0", "react-router-dom": "^6.23.0", "react-router-prompt": "^0.7.0", "react-spring": "^9.4.5", "react-svg": "^16.1.8", "react-transition-group": "^4.4.2", "react-virtuoso": "^4.6.2", "reactflow": "11.10.3", "recharts": "^2.7.2", "recoil": "0.0.10", "remark-gfm": "^3.0.1", "source-map-explorer": "^2.4.2", "store2": "^2.14.4", "storybook-addon-pseudo-states": "^4.0.3", "tsparticles": "^3.3.0", "use-file-picker": "^2.1.2", "use-immer": "^0.9.0", "use-sync-external-store": "^1.2.0", "utf8": "^3.0.0", "uuid": "^8.2.0", "valibot": "^0.31.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "worker-timers": "^7.1.7", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "scripts": {"start": "vite --open", "start-in-docker": "vite", "build": "npm run prep-before-esbuild && node ./esbuild-web.config.mjs && npm run _gzip-build-assets", "build:chrome-extension": "npm run prep-before-chrome-extension-esbuild && node ./esbuild-chrome-extension.config.mjs", "postbuild": "node ./postbuild.mjs", "prep-before-esbuild": "rm -rf build && mkdir build", "prep-before-chrome-extension-esbuild": "cd chrome && rm -rf chrome-extension-build && mkdir chrome-extension-build", "full:start": "npm ci --//npm.pkg.github.com/:_authToken=$NPM_REGISTRY_TOKEN && npm start", "burn-them-all": "rm -rf node_modules package-lock.json && npm install", "test": "jest", "analyze": "source-map-explorer 'build/static/js/*.js'", "_lint": "eslint --cache --cache-location node_modules --ext js,jsx", "lint": "npm run _lint -- --no-fix src", "lint:v2": "npm run _lint -- --no-fix src/v2", "lint:fix": "npm run _lint -- --fix --max-warnings 0 src/", "lint:error": "npm run _lint -- --no-fix --max-warnings 0 src/", "lint:ideal": "npm run _lint -- --no-eslintrc --config eslint.ideal.js --no-fix", "storybook": "DISABLE_ESLINT_PLUGIN=true storybook dev -p 6006", "build-storybook": "DISABLE_ESLINT_PLUGIN=true storybook build", "pretty": "prettier --write --cache \"./{public,src}/**/*.+(js|jsx|json|css|scss|md)\"", "pretty:ci": "cross-var prettier --check --cache $prettier_files", "pretty:check": "prettier --ignore-path .prettierignore --check --cache \"./{public,src}/**/*.+(js|jsx|json|css|scss|md)\"", "test-ct": "playwright install chromium --with-deps && playwright test -c playwright-ct.config.js", "_gzip-build-assets": "find ./build -type f -name '*.js' -exec gzip -k {} \\; && find ./build -type f -name '*.css' -exec gzip -k {} \\;", "tsc": "tsc", "chromatic": "npx chromatic --project-token=29baafa5b76a"}, "eslintConfig": {"extends": "react-app", "overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off", "unicorn/expiring-todo-comments": "off"}}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/eslint-parser": "^7.27.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@chromatic-com/storybook": "^3.2.6", "@craco/craco": "^7.0.0", "@emotion/babel-plugin": "^11.13.5", "@playwright/experimental-ct-react17": "^1.51.1", "@sentry/esbuild-plugin": "^2.23.0", "@storybook/addon-actions": "^8.6.12", "@storybook/addon-designs": "^8.2.1", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-mdx-gfm": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/manager-api": "^8.6.12", "@storybook/node-logger": "^8.6.12", "@storybook/preset-create-react-app": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/theming": "^8.6.12", "@tailwindcss/container-queries": "^0.1.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.2.0", "autoprefixer": "^10.4.1", "babel-jest": "^29.7.0", "babel-plugin-named-exports-order": "^0.0.2", "chromatic": "^10.1.0", "cross-var": "^1.1.0", "esbuild": "^0.25.0", "esbuild-plugin-svgr": "^2.1.0", "esbuild-sass-plugin": "^2.16.0", "esbuild-server": "^0.3.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-check-file": "^2.2.0", "eslint-plugin-custom-rules": "file:eslint-custom-rules", "eslint-plugin-import-x": "^4.3.1", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-unicorn": "^47.0.0", "events": "^3.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.4.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.7.5", "msw-storybook-addon": "^2.0.4", "postcss": "^8.4.32", "postcss-import": "^15.1.0", "postcss-modules": "^6.0.0", "postcss-nesting": "^12.0.2", "postcss-preset-env": "^9.2.0", "prettier": "^2.6.2", "sass": "^1.50.1", "sass-embedded": "^1.87.0", "source-map-explorer": "^2.4.2", "storybook": "^8.6.12", "storybook-addon-pseudo-states": "^4.0.3", "storybook-dark-mode": "^4.0.2", "storybook-multilevel-sort": "^2.0.1", "tailwind-merge": "^2.2.1", "tailwindcss": "3.4.1", "tailwindcss-themer": "^4.0.0", "typescript": "^5.2.2", "vite-plugin-eslint": "^1.8.1"}, "overrides": {"moment": "$moment", "cross-spawn": "^7.0.5", "cookie": "^0.7.0", "micromatch": "^4.0.8", "json5": "^2.2.3", "canvg": "3.0.11", "dompurify": "3.2.4", "@babel/helpers": "7.26.10", "@babel/runtime": "7.26.10", "jspdf": "3.0.1"}, "engines": {"node": ">=16.16.0", "npm": ">=8.11.0"}, "proxy": "http://localhost:8000/", "msw": {"workerDirectory": "public"}}