import { COMMISSION_TYPE } from "~/Enums";

export async function getAutoSuggestions(body, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };
  const response = await fetch(
    "/commission_engine/autocomplete_context",
    requestOptions
  );
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "");
  }

  return data;
}

export async function validateExpressionStack(
  expressionBody,
  accessToken,
  signal,
  planType = null
) {
  // signal is used to abort the previous request
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    signal,
    body: JSON.stringify(expressionBody),
  };

  const response = await fetch(
    planType === null
      ? "/commission_engine/expression_validate"
      : planType === COMMISSION_TYPE.COMMISSION_PLAN
      ? "/commission_engine/plan-v2/expression_validate"
      : `/commission_engine/plan-v2/${planType}/expression_validate`,
    requestOptions
  );
  const data = await response.json();
  if (response.ok) {
    return data;
  } else {
    return {
      status: "INVALID",
      message: "Internal server error",
    };
  }
}
