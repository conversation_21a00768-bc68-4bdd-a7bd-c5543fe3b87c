export async function validateAndSaveConnectionName(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/commission_engine/validate_save_conn_name", requestOptions);
}

export async function updateConnectionType(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/commission_engine/update_connection_type", requestOptions);
}

export async function changeExtractionTimeForObject(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch(
    "/commission_engine/change_data_extraction_time",
    requestOptions
  );
}

export async function runUpstreamSyncForObject(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/commission_engine/run_wrapper_sync", requestOptions);
}

export async function changeObjectSyncStatus(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/commission_engine/change_object_sync_status", requestOptions);
}

export async function getConnectionConfig(serviceName, accessToken) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  return fetch(
    "/self-service-integration/get-connection-config?service_name=" +
      serviceName,
    requestOptions
  );
}

export async function createEditConnection(data, accessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch(
    "/self-service-integration/validate-save-connection",
    requestOptions
  );
}

export async function getSyncFields(customObjectId, accessToken) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  return fetch(
    `/self-service-integration/get-sync-fields?custom_object_id=${customObjectId}`,
    requestOptions
  );
}
