import { localizeV2 } from "../i18n";

export const QUOTA_MESSAGES = {
  NO_QUOTA_AVAILABLE: "No Quota Available",
  QUOTA: "Quota",
  ADD_QUOTA: "Add Quota",
  QUOTAS: "Quotas",
  HANDLE_CANCEL: `Are you sure you want to cancel the Quota upload process?`,
  TEAM_AND_QUOTA: "Team and Quota",
  QUOTA_DETAILS: "Quota Details",
  RAMP_ADJUSTED_QUOTA: "Ramp Adjusted Quota",
  PRIMARY_QUOTA: `Primary Quota`,
  MANAGER_QUOTA: `Use this as Manager Quota`,
  QUOTA_WITH: `quota with `,
  APPLY_QUOTA: `Apply the above quota for other payees (optional)`,
  QUOTA_CONFIRMATION: `The following payees have existing quota which will be updated on confirmation:`,
  ENTER_NEW_QUOTA: `Enter new quota values`,
  FOR_QUOTA: `for Quota`,
  QUOTA_FREQUENCY: `Quota Frequency`,
  NEW_QUOTA_CATEGORY_CREATED: `New Quota Category Created`,
  QUOTA_SAVE_SUCESS: `Quota saved successfully`,
  ERROR_SAVING_QUOTA: `Error while saving Quota. Please try again later`,
  QUOTA_CATEGORY: `Quota category`,
  QUOTA_PERIOD: `Quota Period`,
  QUOTA_AS_MANAGER: `Quota as Manager`,
  INSTRUCTION_LINE_2: `Ensure that the Quota Category names in the CSV file are consistent.`,
  INSTRUCTION_LINE_4: `Suppose you want to assign individual quotas for a manager, then set the 'Quota as manager' column as No. Else, leave it blank or mark it as Yes`,
  INSTRUCTION_LINE_5: `The ‘Quota as Manager’ column will not be considered for payees and can be left blank.`,
  QUOTAATTAINTMENT: `QuotaAttainment`,
  QUOTAEROSION: `QuotaErosion`,
  QUOTA_ATTAINMENT: `Quota Attainment`,
  SELECT_QUOTA: `Select Quota`,
  QUOTA_NOT_SET: `Quota hasn't been set for any category in the selected year`,
  QUOTA_SCHEDULE: `Quota Schedule`,
  MONITOR_YOUR_QUOTA: `Monitor your quota attainment % for any commission period.`,
  QUOTA_UNAVAILABLE: `Quota Unavailable`,
  QUOTA_ATTAINMENT_DISTRIBUTION: `Quota Attainment Distribution`,
  VISUALIZE_QUOTA_ATTAINMENT: `Visualize quota attainment % for teams in your
  organization for any commission period.`,
  SELECT_QUOTA_SCHEDULE: `Select quota schedule`,
  QUOTA_ATTAINMENT_TRACKER: `Quota Attainment Tracker`,
  TRACK_YOUR_QUOTA_ATTAINMENT: `Track your quota attainment % along with your
  position on the leaderboard for each month in a
  commission period.`,
  QUOTA_ACTION_INSIGHTS: `Get actionable insights on how you can meet your quota.`,
  VISUALIZE_DAILY_BREAKDOWN_QUOTA: `Visualize a daily breakdown of quota % left for you to achieve in
  a commission period.`,
  QUOTA_STRUCTURE: `Quota Structure`,
  QUOTA_CRITERIA_DESCTIPTION: `Use this to create a quota or target based criteria.`,
  QUOTA_EROSION: "Quota Erosion",
  QUOTA_SUBMIT_QUERIES: `quota attainment % and submit queries here`,
  ADD_NEW_QUOTA: `+ Add Quota`,
  ADD_NEW_QUOTA_v2: `Add Quota`,
  IMPORT_QUOTAS: `Import Quotas`,
  QUOTA_AS_PLAYER: `(Quota as Player)`,
  GET_ACTIONABLE_INSIGHT: `Get actionable insights on how you can meet your quota.`,
  DUPLICATE_QUOTA: `Duplicate quota from another payee`,
  QUOTA_ATTAINMENT_TIER: `Tier based on quota attainment %`,
  QUOTA_EROSION_TIER: `Tier based on quota erosion`,
  CUMMILATIVE_QA: `Cumulative quota attainment`,
  CUMMILATIVE_QE: `Cumulative quota erosion`,
  RANGE_QE: `Range based on quota erosion`,
  RANGE_QA: `Range based on quota attainment %`,
  TEAM_QA: `Team-QuotaAttainment`,
  TEAM_QE: `Team-QuotaErosion`,
  TEAM_QUOTA: `Team-Quota`,
  DIFF_QUOTA_SCHEDULE_REPORTS: `Reports are on different Quota schedule`,
  QUOTA_FREQUENCY_TOOLTIP:
    "The time period or regularity at which sales quotas are assigned.",
  SCHEDULE_FREQUENCY_TOOLTIP:
    "Quota schedule determines how the quota for a given period is further broken down to track attainment and payout.",
};

export const COMM_TRACE_MESSAGES = {
  QUOTA_LOWERCASE: `quota`,
  QUOTA_EROSION_LOWERCASE: `quota erosion`,
  QUOTA: `Quota`,
  QUOTA_EROSION: `Quota erosion`,
  QUOTAEROSION: `QuotaErosion`,
  MULTIPLE_TIER_COMM: `Your commission calculated for this deal falls under multiple tiers. Trace respective commissions below.`,
};

export const CRTSTAL_MESSAGES = {
  SELECT_OPPOURTINITIES: `Select the most promising opportunities to simulate potential
  commissions and quota attainment.`,
  ANIMATIONS_CALC_COMM: `Calculating commissions... hang tight, your riches are coming`,
  ANIMATIONS_TIMEOUT_DATA: `We’re doing a little extra math to make sure your commission projections are spot on.. hang tight, it’ll be worth it in the end`,
};

export const COMMISSION_MESSAGES = {
  COMMISSION: "Commission",
  COMMISSIONS: "Commissions",
  NO_COMMISSION_PLAN: `No commission plan & reporting manager for this user`,
  CALCULATE_COMMISSIONS: `Calculate Commissions`,
  CALCULATING_COMMISSIONS: `Calculating Commissions...`,
  COMMISSION_CALCULATION_COMPLETED: `Commission Calculations Completed`,
  COMMISSION_CALCULATION_PARTIALLY_FAILED: `Commission Calculations Failed Partially`,
  COMMISSION_CALCULATION_FAILED: `Commission Calculations Failed`,
  RUN_COMMISSION_CALCULATION: `Run commission calculation on-demand for all or selected payees.`,
  SYNC_LATEST_AND_CALCULATE_COMMISSIONS: `Sync latest information from all your connectors and calculate commissions.`,
  COPY_COMMISSION_TO_INTER_COMMISSION: `Copy commission/quota erosion data to inter commission, inter quota erosion`,
  PLEASE_SELECT_COMMISSIONS: `Please select commissions for bulk action`,
  COMMISSION_LOCKED_ON: `Commission locked on`,
  PRIMARY_COMMISSION_PLAN: `Primary Commission Plan`,
  COMMISSION_LOCKED: `Commission Locked`,
  COMMISSION_UNLOCKED: `Commission Unlocked`,
  COMMISSION_PLAN: `Commission Plan`,
  COMMISSION_PLANS: `Commission Plans`,
  ERROR_FETCHING_COMMISSION_SUMMARY: `Error in fetching commission summary for initiate exit user`,
  ENTER_LAST_COMMISSION_PERIOD: `Enter last commission period`,
  LAST_PROCESSED_COMMISSIONS: `Last processed commissions`,
  COMMISSION_PLAN_REMOVAL: `Commission plan removal`,
  COMMISSION_PLAN_END_DATE: `Commission plan end date`,
  COMMISSION_PLAN_SETTLEMENT_END_DATE: `Commission plan settlement end date`,
  THE_COMMISSION_PLAN_END_DATE: `The commission plan end date`,
  THE_COMMISSION_PLAN_SETTLEMENT_END_DATE: `The commission plan settlement end date`,
  PICK_EXIT_DATE_FOR_EXIT_USER: `Pick the exit date and the commissions end period to offboard your users
  seamlessly. You can even adjust commission if needed.`,
  COMMISSION_END_PERIOD: `Commission end period`,
  SELECT_COMMISSION_PERIOD: `Select commission period`,
  USERS_WITHOUT_COMMISSION_PLAN: `Users without a commission plan & reporting manager`,
  USERS_HAVE_COMMISSION_PLAN: `Users who have a commission plan & reporting manager`,
  SETUP_COMMISSION_PLAN: `Setup commission plans & reporting manager`,
  REPORTING_AND_COMMISSION_PLAN: `Reporting and Commission Plan`,
  COMMISSION_PLAN_DETAILS: `Commission Plan Details`,
  PLEASE_SELECT_COMMISSION_PLAN: `Please select Commission Plan!`,
  SELECT_COMMISSION_PLAN: `Select Commission Plan`,
  COMMISSION_SCHEDULE: `Commission Schedule`,
  LINK_COMMISSION_PLAN: `Link to a Commission Plan`,
  TRACK_COMMISSION_PAYOUT: `Track your Commission payouts for each month in a Commission
  period.`,
  ALL_COMMISSION_PLANS: `All Commission Plans`,
  VIEW_COMMISSION_PAYOUT: `View your Commission payouts for any commission period.`,
  COMMISSION_FEED: `Commission Feed`,
  APPLY_CHANGE_TO_PAYEES: `Do you want to apply this change to payees whose plan
  period was the same as the commission plan period?`,
  TOTAL_COMMISSIONS: `Total Commissions`,
  TOTAL_COMMISSION: `Total Commission`,
  COMMISSION_PAYOUT_LOGIC: `Commission Payout Logic`,
  REVIEW_YOUR_PAYOUT: `Once you're mapped to a commission plan, you can review your payouts,`,
  ADMIN_BUSY_ON_COMMISSION_PLAN: `Guess your admin is busy working out your commission plan`,
  EARNED_COMMISSIONS: `Earned Commissions`,
  DEFERRED_COMMISSIONS: `Deferred Commissions`,
  PREVIOUSLY_DEFERRED_COMMISSIONS: `Payout from previously deferred commissions`,
  COMMISSION_SUMMARY: `Commission Summary`,
  CALCULATING_COMMISSIONS_CRYSTAL: `Calculating Z... hang tight, your riches are coming!`,
  NOT_MAPPED_TO_COMMISSION_PLAN: `is not mapped to a commission plan yet`,
  COMMISSION_PAYOUT: `Commission Payout`,
  REVIEW_PAYOUTS_AFTER_MAPPED: `Once you're mapped to a commission plan, you can review your payouts,`,
  COMM_AND_DATA_SYNC: `Commission & Data Sync`,
  COMM_DATA_GEN_FAILED: `Commission data generation task submission failed`,
  PAYEES_IN_COMM_PLAN: `Payees in Commission Plan`,
  CHOOSE_COMM_PLANS: `Choose Commission Plans`,
  ADD_COMM_FEED: `Add Commission Feed`,
  NO_COMM_FEED_ADD_PERMISSION: `You don't have permission to add commission feed`,
  COMM_LOCK_DATE: `Commission lock date`,
  ACTIVE_COMM_PLAN: `Active Commission Plan`,
  COMM_SUMMATION: `Commission (Summation)`,
  PLAN_LEVEL_COMM: `Plan level commission`,
  COMMS_GET_READY: `Commissions en route - get ready!`,
  L1_COMM_CALC: `L1 Commission Calculation`,
  L2_COMM_CALC: `L2 Commission Calculation`,
  COMM_CALC: `Commission Calculation`,
  REPORT_SYNC_COMM_OBJ: `Report Sync (commission object)`,
  COMM_CRITERIA: `Commission Criteria`,
  NEW_COMM_PLAN: `New Commission Plan`,
  REFRESH_DATA_CALC_COMM: `Refresh databooks & then calculate commissions`,
  DASHBOARD_EMPTY: `Your dashboard is empty because your commissions haven’t run
  yet. Check out the widgets to know what insights you can get
  when there’s data.`,
  SHOW_COMMISSION_TRACE: `Show commission trace`,
  UPLOAD_PERSONALIZED_COMM_PLAN: `Use this section to upload personalized commission plan documents
  for each payee`,
  UPLOAD_COMM_PLAN_ALL_PAYEES: `Use this section to upload standard commission plan documents for
  all payees`,
  COMMISSIONS_SETTINGS_CARD: `Commission & Data Sync`,
  TRACK_COMM_EACH_MONTH: `Track your commission payouts for each month in a commission period.`,
  COMM_REPORT: `Commission Report`,
  INTER_COMM_REPORT: `Inter Commission Report`,
  TAG_DEAL_INSTRUCTION: `Tag the deal id variable in datasheet that is used in commission plan`,
  COMM_BUDDY: `Commission Buddy`,
  MAKE_MORE_COMM: `How do I make more commission?`,
  COMM_STRUCTURE: `Commission Structure`,
  CLONING_COMMISSION_PLAN: `Cloning commission plan`,
};

export const PAYOUT_MESSAGES = {
  PAYOUTS: `Payouts`,
  PAYOUT: `Payout`,
  PAYOUT_APPROVALS: `payout approvals`,
  APPROVED_ALL_PAYOUTS: `You've approved all the payouts.`,
  REJECTED_ALL_PAYOUTS: `You've rejected all the payouts.`,
  WITHDRAWN_SINCE_PAYOUT_UNLOCKED: `Withdrawn since payout was unlocked`,
  WITHDRAWN_SINCE_PAYOUT_REJECTED: `Withdrawn since payout got rejected`,
  NO_LONGER_APPROVE_OR_REJECT_PAYOUT: `Users will no longer be able to approve or reject this payout.`,
  PAYOUT_CURRENCY: `Payout Currency`,
  PAYOUT_AMOUNT: `Payout Amount`,
  PAYOUT_FREQUENCY: `Payout Frequency`,
  PAYOUT_LOCKED: `Payout Locked`,
  PAYOUT_UNLOCKED: `Payout Unlocked`,
  PAYOUT_STATUS: `Payout Status`,
  NO_PERMISSION_TO_OTHERS_PAYOUT: `You don't have permission to view and manage payout of other users.`,
  TOTAL_PAYOUT: `Total Payout`,
  CURRENT_PAYOUT_MESSAGE: `Total payout from current period`,
  PLEASE_SELECT_PAYOUT_FREQUENCY: `Please select payout frequency!`,
  SELECT_PAYOUT_FREQUENCY: `Select Payout Frequency`,
  NO_PAYOUT_FREQUENCY: `Note: Payee does not have a payout frequency`,
  YOUR_PAYOUTS: `Your Payouts`,
  PAYOUTS_TRACKER: `Payouts Tracker`,
  CANNOT_SIMULATE_SINCE_NO_PERMISSION: `You can't simulate since you don't have permission to view payouts data`,
  PAYOUT_PERIOD: `Payout Period`,
  COMMISSION_PAYOUT_SCHEDULE: `Commission Payout Schedule`,
  PAYOUT_DETAILS: `Payout Details`,
  PAYOUT_SUMMARY: `Payout Summary`,
  PAYOUT_LOCKED_SUCESSFULLY: `Payout locked successfully`,
  ERROR_LOCKING_PAYOUT: `Error while Locking payout. Please try again later`,
  PAYOUT_UNLOCKED_SUCESSFULLY: `Payout unlocked successfully`,
  ERROR_UNLOCKING_PAYOUT: `Error while unlocking payout. Please try again later`,
  PAYOUT_FROM_CURRENT_PERIOD: `Payout from current period`,
  PAYOUT_ARREARS: `Payout Arrears`,
  PAYOUTS_VIEW: `Payouts View`,
  ON_TARGET_VARIABLE_PAY: `On-Target variable pay`,
  PAYOUT_CYCLE: `Payout Cycle`,
  "100_VARIABLE_PAYOUT_LABEL": `100% Variable Payout`,
  VARIABLE_PAYOUT_LABEL: `Variable Payout`,
  PROJECTED_PAYOUT_LABEL: `Projected Payout`,
  CURRENT_PAYOUT_LABEL: `Current Payout`,
  TO_TRACK_PAYOUTS: `to track to 100 % payouts`,
  YTD_PAYOUTS: `YTD payouts:`,
  BUDGETED_PAYOUTS: `of the budgeted payouts`,
};

export const ADJUSTMENT_MESSAGES = {
  ADJUSTMENTS: `Adjustments`,
  ADJUSTMENT: `Adjustment`,
  MAKE_AN_ADJJUSTMENT: `Make an adjustment`,
  ADJUSTMENT_TYPE: `Adjustment Type`,
  ADJUSTMENT_SCOPE: `Adjustment Scope`,
  VIEW_ADJUSTMENT: `View Adjustment`,
  REVERT_ADJUSTMENT: `Revert Adjustment`,
  PRE_ADJUSTMENT: `Pre Adjustment`,
  POST_ADJUSTMENT: `Post Adjustment`,
  ADD_ADJUSTMENT: `Add Adjustment`,
  DELETE_ADJUSTMENT_CONFIRM: `Are you sure you want to delete this adjustment?`,
  DELETE_BULK_ADJUSTMENT_CONFIRM: `Are you sure you want to delete the selected adjustments?`,
  ADJUSTMENT_DELETED: `Adjustment deleted successfully`,
  ADJUSTMENTS_DELETED: `Adjustments deleted successfully`,
  EDIT_ADJUSTMENT: `Edit Adjustment`,
  ADJUSTMENT_SAVED_SUCESSFULLY: `Adjustment saved successfully`,
  ADJUSTMENT_MODIFIED_SUCESSFULLY: `Adjustment modified successfully`,
  OTHER_ADJUSTMENTS: `Other Adjustments`,
  ADJUSTMENT_UPDATED_SUCESSFULLY: `Adjustment updated successfully`,
  DRAW_ADJUSTMENTS: `Draw Adjustments`,
  COMMISSION_ADJUSTMENTS: `Commission Adjustments`,
  APPROVAL_MANDATORY_FOR_COMMISSION_ADJUSTMENTS: `Makes approval mandatory to add commission adjustments.`,
  ADJUSTMENT_ID: `Adjustment ID`,
  ADJUSTMENT_DATE: `Adjustment Date`,
};

export const ARREAR_MESSAGES = {
  ARREARS: `Arrears`,
  ARREAR: `Arrear`,
  ARREAR_PROCESS_SUCESSFUL: `Arrear processed successfully`,
  ARREAR_IGNORED_SUCESSFULLY: `Arrear ignored successfully`,
  ARREARS_VIEW: `Arrears View`,
  PROCESS_ARREAR: `Process Arrear`,
  DUE_ARREAR: `Due/Arrear`,
};

export const DEFERRED_MESSAGES = {
  DEFERRED: `Deferred`,
  NO_PREVIOUS_DEFERRED_COMMISSIONS: `No previous deferred commissions`,
  PAYOUTS_FROM_DEFERRED_COMMISSIONS: `Payouts from deferred commissions for`,
};

// No need for localization for these messages as of now. If need in future,
// add them to the updateLocalizationMessages fn.
export const LOGIN_MESSAGES = {
  NOT_INVITED_TITLE: `Stay tuned for the magic! ✨`,
  NOT_INVITED_SUBTITLE_1: `We're delighted by your eagerness to explore Everstage, but we're not quite ready to raise the curtains yet.`,
  NOT_INVITED_SUBTITLE_2: `Your admin will invite you once your account is fully set up.`,
};

export const DEPLOYMENT_FINISH_MESSAGE = {
  DEPLOYMENT_FINISH_MESSAGE:
    "We’ve made some updates to Everstage to improve your experience. Please save your work and refresh the app to see the latest changes.",
};

function updateLocalizationMessages() {
  QUOTA_MESSAGES.NO_QUOTA_AVAILABLE =
    "No " + localizeV2?.t("QUOTA") + " Available";
  QUOTA_MESSAGES.QUOTA = localizeV2?.t("QUOTA");
  QUOTA_MESSAGES.ADD_QUOTA = "Add " + localizeV2?.t("QUOTA");
  QUOTA_MESSAGES.QUOTAS = localizeV2?.t("QUOTAS");
  QUOTA_MESSAGES.HANDLE_CANCEL = `Are you sure you want to cancel the ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} upload process?`;
  QUOTA_MESSAGES.TEAM_AND_QUOTA = "Team and " + localizeV2?.t("QUOTA");
  QUOTA_MESSAGES.QUOTA_DETAILS = localizeV2?.t("QUOTA") + " Details";
  QUOTA_MESSAGES.RAMP_ADJUSTED_QUOTA =
    "Ramp Adjusted " + localizeV2?.t("QUOTA");
  QUOTA_MESSAGES.PRIMARY_QUOTA = `Primary ${localizeV2?.t("QUOTA")}`;
  QUOTA_MESSAGES.MANAGER_QUOTA = `Use this as Manager ${localizeV2?.t(
    "QUOTA"
  )}`;
  QUOTA_MESSAGES.QUOTA_WITH = `${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} with `;
  QUOTA_MESSAGES.APPLY_QUOTA = `Apply the above ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} for other payees (optional):`;
  QUOTA_MESSAGES.QUOTA_CONFIRMATION = `The following payees have existing ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} which will be updated on confirmation:`;
  QUOTA_MESSAGES.ENTER_NEW_QUOTA = `Enter new ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} values`;
  QUOTA_MESSAGES.QUOTA_FREQUENCY = `${localizeV2?.t("QUOTA")} Frequency`;
  QUOTA_MESSAGES.FOR_QUOTA = `for ${localizeV2?.t("QUOTA")}`;
  QUOTA_MESSAGES.NEW_QUOTA_CATEGORY_CREATED = `New ${localizeV2?.t(
    "QUOTA"
  )} Category Created`;
  QUOTA_MESSAGES.QUOTA_SAVE_SUCESS = `${localizeV2?.t(
    "QUOTA"
  )} saved successfully`;
  QUOTA_MESSAGES.ERROR_SAVING_QUOTA = `Error while saving ${localizeV2?.t(
    "QUOTA"
  )}. Please try again later`;
  QUOTA_MESSAGES.QUOTA_CATEGORY = `${localizeV2?.t("QUOTA")} category`;
  QUOTA_MESSAGES.QUOTA_PERIOD = `${localizeV2?.t("QUOTA")} Period`;
  QUOTA_MESSAGES.QUOTA_AS_MANAGER = `${localizeV2?.t("QUOTA")} as Manager`;
  QUOTA_MESSAGES.INSTRUCTION_LINE_2 = `Ensure that the ${localizeV2?.t(
    "QUOTA"
  )} Category names in the CSV file are consistent.`;
  QUOTA_MESSAGES.INSTRUCTION_LINE_4 = `Suppose you want to assign individual ${String(
    localizeV2?.t("QUOTAS")
  ).toLowerCase()} for a manager, then set the '${localizeV2?.t(
    "QUOTA"
  )} as manager' column as No. Else, leave it blank or mark it as Yes`;
  QUOTA_MESSAGES.INSTRUCTION_LINE_5 = `The ‘${localizeV2?.t(
    "QUOTA"
  )} as Manager’ column will not be considered for payees and can be left blank.`;
  QUOTA_MESSAGES.QUOTAATTAINTMENT = `${localizeV2?.t("QUOTA")}Attainment`;
  QUOTA_MESSAGES.QUOTAEROSION = `${String(
    localizeV2?.t("QUOTA_EROSION")
  ).replace(" ", "")}`;
  QUOTA_MESSAGES.QUOTA_ATTAINMENT = `${localizeV2?.t("QUOTA")} Attainment`;
  QUOTA_MESSAGES.SELECT_QUOTA = `Select ${localizeV2?.t("QUOTA")}`;
  QUOTA_MESSAGES.QUOTA_NOT_SET = `${localizeV2?.t(
    "QUOTA"
  )} hasn't been set for any category in the selected year`;
  QUOTA_MESSAGES.QUOTA_SCHEDULE = `${localizeV2?.t("QUOTA")} Schedule`;
  QUOTA_MESSAGES.MONITOR_YOUR_QUOTA = `Monitor your ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment % for any ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period.`;
  QUOTA_MESSAGES.QUOTA_UNAVAILABLE = `${localizeV2?.t("QUOTA")} Unavailable`;
  QUOTA_MESSAGES.QUOTA_ATTAINMENT_DISTRIBUTION = `${localizeV2?.t(
    "QUOTA"
  )} Attainment Distribution`;
  QUOTA_MESSAGES.VISUALIZE_QUOTA_ATTAINMENT = `Visualize ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment % for teams in your
  organization for any ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period.`;
  QUOTA_MESSAGES.SELECT_QUOTA_SCHEDULE = `Select ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} schedule`;
  QUOTA_MESSAGES.QUOTA_ATTAINMENT_TRACKER = `${localizeV2?.t(
    "QUOTA"
  )} Attainment Tracker`;
  QUOTA_MESSAGES.TRACK_YOUR_QUOTA_ATTAINMENT = `Track your ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment % along with your
  position on the leaderboard for each month in a
  ${String(localizeV2?.t("COMMISSION")).toLowerCase()} period.`;
  QUOTA_MESSAGES.QUOTA_ACTION_INSIGHTS = `Get actionable insights on how you can meet your ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()}.`;
  QUOTA_MESSAGES.VISUALIZE_DAILY_BREAKDOWN_QUOTA = `Visualize a daily breakdown of ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} % left for you to achieve in
  a ${String(localizeV2?.t("COMMISSION")).toLowerCase()} period.`;
  QUOTA_MESSAGES.QUOTA_STRUCTURE = `${localizeV2?.t("QUOTA")} Structure`;
  QUOTA_MESSAGES.QUOTA_CRITERIA_DESCTIPTION = `Use this to create a ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} or target based criteria.`;
  QUOTA_MESSAGES.QUOTA_EROSION = localizeV2?.t("QUOTA_EROSION");
  QUOTA_MESSAGES.QUOTA_SUBMIT_QUERIES = `${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment % and submit queries here`;
  QUOTA_MESSAGES.IMPORT_QUOTAS = `Import ${localizeV2?.t("QUOTAS")}`;
  QUOTA_MESSAGES.QUOTA_AS_PLAYER = `(${localizeV2?.t("QUOTA")} as Player)`;
  COMMISSION_MESSAGES.COMMISSION = localizeV2?.t("COMMISSION");
  COMMISSION_MESSAGES.COMMISSIONS = localizeV2?.t("COMMISSIONS");
  COMMISSION_MESSAGES.NO_COMMISSION_PLAN = `No ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan & reporting manager for this user`;
  COMMISSION_MESSAGES.CALCULATE_COMMISSIONS = `Calculate ${localizeV2?.t(
    "COMMISSIONS"
  )}`;
  COMMISSION_MESSAGES.CALCULATING_COMMISSIONS = `Calculating ${localizeV2?.t(
    "COMMISSIONS"
  )}...`;
  COMMISSION_MESSAGES.COMMISSION_CALCULATION_COMPLETED = `${localizeV2?.t(
    "COMMISSION"
  )} Calculations Completed`;
  COMMISSION_MESSAGES.COMMISSION_CALCULATION_PARTIALLY_FAILED = `${localizeV2?.t(
    "COMMISSION"
  )} Calculations Failed Partially`;
  COMMISSION_MESSAGES.COMMISSION_CALCULATION_FAILED = `${localizeV2?.t(
    "COMMISSION"
  )} Calculations Failed`;
  COMMISSION_MESSAGES.RUN_COMMISSION_CALCULATION = `Run ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} calculation on-demand for all or selected payees.`;
  COMMISSION_MESSAGES.SYNC_LATEST_AND_CALCULATE_COMMISSIONS = `Sync latest information from all your connectors and calculate ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()}.`;
  COMMISSION_MESSAGES.COPY_COMMISSION_TO_INTER_COMMISSION = `Copy ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()}/${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()} data to inter ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()}, inter ${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()}`;
  COMMISSION_MESSAGES.PLEASE_SELECT_COMMISSIONS = `Please select ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()} for bulk action`;
  COMMISSION_MESSAGES.COMMISSION_LOCKED_ON = `${localizeV2?.t(
    "COMMISSION"
  )} locked on`;
  COMMISSION_MESSAGES.PRIMARY_COMMISSION_PLAN = `Primary ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.COMMISSION_LOCKED = `${localizeV2?.t(
    "COMMISSION"
  )} Locked`;
  COMMISSION_MESSAGES.COMMISSION_UNLOCKED = `${localizeV2?.t(
    "COMMISSION"
  )} Unlocked`;
  COMMISSION_MESSAGES.COMMISSION_PLAN = `${localizeV2?.t("COMMISSION")} Plan`;
  COMMISSION_MESSAGES.COMMISSION_PLANS = `${localizeV2?.t("COMMISSION")} Plans`;
  COMMISSION_MESSAGES.ERROR_FETCHING_COMMISSION_SUMMARY = `Error in fetching ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} summary for initiate exit user`;
  COMMISSION_MESSAGES.ENTER_LAST_COMMISSION_PERIOD = `Enter last ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period`;
  COMMISSION_MESSAGES.LAST_PROCESSED_COMMISSIONS = `Last processed ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()}`;
  COMMISSION_MESSAGES.COMMISSION_PLAN_REMOVAL = `${localizeV2?.t(
    "COMMISSION"
  )} plan removal`;
  COMMISSION_MESSAGES.COMMISSION_PLAN_END_DATE = `${localizeV2?.t(
    "COMMISSION"
  )} plan end date`;
  COMMISSION_MESSAGES.COMMISSION_PLAN_SETTLEMENT_END_DATE = `${localizeV2?.t(
    "COMMISSION"
  )} plan settlement end date`;
  COMMISSION_MESSAGES.THE_COMMISSION_PLAN_END_DATE = `The ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan end date`;
  COMMISSION_MESSAGES.THE_COMMISSION_PLAN_SETTLEMENT_END_DATE = `The ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan settlement end date`;
  COMMISSION_MESSAGES.PICK_EXIT_DATE_FOR_EXIT_USER = `Pick the exit date and the ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()} end period to offboard your users
  seamlessly. You can even adjust ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} if needed.`;
  COMMISSION_MESSAGES.COMMISSION_END_PERIOD = `${localizeV2?.t(
    "COMMISSION"
  )} end period`;
  COMMISSION_MESSAGES.SELECT_COMMISSION_PERIOD = `Select ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period`;
  COMMISSION_MESSAGES.USERS_WITHOUT_COMMISSION_PLAN = `Users without a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan & reporting manager`;
  COMMISSION_MESSAGES.USERS_HAVE_COMMISSION_PLAN = `Users who have a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan & reporting manager`;
  COMMISSION_MESSAGES.SETUP_COMMISSION_PLAN = `Setup ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plans & reporting manager`;
  COMMISSION_MESSAGES.REPORTING_AND_COMMISSION_PLAN = `Reporting and ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.COMMISSION_PLAN_DETAILS = `${localizeV2?.t(
    "COMMISSION"
  )} Plan Details`;
  COMMISSION_MESSAGES.PLEASE_SELECT_COMMISSION_PLAN = `Please select ${localizeV2?.t(
    "COMMISSION"
  )} Plan!`;
  COMMISSION_MESSAGES.SELECT_COMMISSION_PLAN = `Select ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.COMMISSION_SCHEDULE = `${localizeV2?.t(
    "COMMISSION"
  )} Schedule`;
  COMMISSION_MESSAGES.LINK_COMMISSION_PLAN = `Link to a ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.TRACK_COMMISSION_PAYOUT = `Track your ${localizeV2?.t(
    "COMMISSION"
  )} ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()} for each month in a ${localizeV2?.t("COMMISSION")}
  period.`;
  COMMISSION_MESSAGES.ALL_COMMISSION_PLANS = `All ${localizeV2?.t(
    "COMMISSION"
  )} Plans`;
  COMMISSION_MESSAGES.VIEW_COMMISSION_PAYOUT = `View your ${localizeV2?.t(
    "COMMISSION"
  )} ${String(localizeV2?.t("PAYOUTS")).toLowerCase()} for any ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period.`;
  COMMISSION_MESSAGES.COMMISSION_FEED = `${localizeV2?.t("COMMISSION")} Feed`;
  COMMISSION_MESSAGES.APPLY_CHANGE_TO_PAYEES = `Do you want to apply this change to payees whose plan
  period was the same as the ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan period?`;
  COMMISSION_MESSAGES.TOTAL_COMMISSIONS = `Total ${localizeV2?.t(
    "COMMISSIONS"
  )}`;
  COMMISSION_MESSAGES.TOTAL_COMMISSION = `Total ${localizeV2?.t("COMMISSION")}`;
  COMMISSION_MESSAGES.COMMISSION_PAYOUT_LOGIC = `${localizeV2?.t(
    "COMMISSION"
  )} Payout Logic`;
  COMMISSION_MESSAGES.REVIEW_YOUR_PAYOUT = `Once you're mapped to a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan, you can review your ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()},`;
  COMMISSION_MESSAGES.ADMIN_BUSY_ON_COMMISSION_PLAN = `Guess your admin is busy working out your ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan`;
  COMMISSION_MESSAGES.EARNED_COMMISSIONS = `${localizeV2?.t(
    "EARNED"
  )} ${localizeV2?.t("COMMISSIONS")}`;
  COMMISSION_MESSAGES.DEFERRED_COMMISSIONS = `${localizeV2?.t(
    "DEFERRED"
  )} ${localizeV2?.t("COMMISSIONS")}`;
  COMMISSION_MESSAGES.PREVIOUSLY_DEFERRED_COMMISSIONS = `${localizeV2?.t(
    "PAYOUT"
  )} from previously ${String(
    localizeV2?.t("DEFERRED")
  ).toLowerCase()} ${String(localizeV2?.t("COMMISSIONS")).toLowerCase()}`;
  COMMISSION_MESSAGES.COMMISSION_SUMMARY = `${localizeV2?.t(
    "COMMISSION"
  )} Summary`;
  COMMISSION_MESSAGES.CALCULATING_COMMISSIONS_CRYSTAL = `Calculating ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()}... hang tight, your riches are coming!`;
  COMMISSION_MESSAGES.NOT_MAPPED_TO_COMMISSION_PLAN = `is not mapped to a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan yet`;
  COMMISSION_MESSAGES.COMMISSION_PAYOUT = `${localizeV2?.t(
    "COMMISSION"
  )} ${localizeV2?.t("PAYOUT")}`;
  COMMISSION_MESSAGES.COMM_DATA_GEN_FAILED = `${localizeV2?.t(
    "COMMISSION"
  )} data generation task submission failed`;
  COMMISSION_MESSAGES.REVIEW_PAYOUTS_AFTER_MAPPED = `Once you're mapped to a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan, you can review your ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()},`;
  COMMISSION_MESSAGES.COMM_AND_DATA_SYNC = `${localizeV2?.t(
    "COMMISSION"
  )} & Data Sync`;
  COMMISSION_MESSAGES.PAYEES_IN_COMM_PLAN = `Payees in ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.CHOOSE_COMM_PLANS = `Choose ${localizeV2?.t(
    "COMMISSION"
  )} Plans`;
  COMMISSION_MESSAGES.ADD_COMM_FEED = `Add ${localizeV2?.t("COMMISSION")} Feed`;
  COMMISSION_MESSAGES.COMM_LOCK_DATE = `${localizeV2?.t(
    "COMMISSION"
  )} lock date`;
  COMMISSION_MESSAGES.ACTIVE_COMM_PLAN = `Active ${localizeV2?.t(
    "COMMISSION"
  )} Plan`;
  COMMISSION_MESSAGES.PLAN_LEVEL_COMM = `Plan level ${localizeV2?.t(
    "COMMISSION"
  )}`;
  COMMISSION_MESSAGES.NO_COMM_FEED_ADD_PERMISSION = `You don't have permission to add ${localizeV2?.t(
    "COMMISSION"
  )} feed`;
  COMMISSION_MESSAGES.COMM_SUMMATION = `${localizeV2?.t(
    "COMMISSION"
  )} (Summation)`;
  COMMISSION_MESSAGES.COMMS_GET_READY = `${localizeV2?.t(
    "COMMISSIONS"
  )} en route - get ready!`;
  COMMISSION_MESSAGES.L1_COMM_CALC = `L1 ${localizeV2?.t(
    "COMMISSION"
  )} Calculation`;
  COMMISSION_MESSAGES.L2_COMM_CALC = `L2 ${localizeV2?.t(
    "COMMISSION"
  )} Calculation`;
  COMMISSION_MESSAGES.COMM_CALC = `${localizeV2?.t("COMMISSION")} Calculation`;
  COMMISSION_MESSAGES.REPORT_SYNC_COMM_OBJ = `Report Sync (${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} object)`;
  COMMISSION_MESSAGES.COMM_CRITERIA = `${localizeV2?.t("COMMISSION")} Criteria`;
  COMMISSION_MESSAGES.NEW_COMM_PLAN = `New ${localizeV2?.t("COMMISSION")} Plan`;
  COMMISSION_MESSAGES.REFRESH_DATA_CALC_COMM = `Refresh databooks & then calculate ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()}`;
  COMMISSION_MESSAGES.DASHBOARD_EMPTY = `Your dashboard is empty because your ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()} haven’t run
  yet. Check out the widgets to know what insights you can get
  when there’s data.`;
  COMMISSION_MESSAGES.SHOW_COMMISSION_TRACE = `Show ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} trace`;
  COMMISSION_MESSAGES.UPLOAD_PERSONALIZED_COMM_PLAN = `Use this section to upload personalized ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan documents
  for each payee`;
  COMMISSION_MESSAGES.UPLOAD_COMM_PLAN_ALL_PAYEES = `Use this section to upload standard ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan documents for
  all payees`;
  COMMISSION_MESSAGES.COMMISSIONS_SETTINGS_CARD = `${localizeV2?.t(
    "COMMISSIONS"
  )} & Data Sync`;
  COMMISSION_MESSAGES.CLONING_COMMISSION_PLAN = `Cloning ${localizeV2
    ?.t("COMMISSION")
    .toLowerCase()} plan`;
  PAYOUT_MESSAGES.PAYOUTS = `${localizeV2?.t("PAYOUTS")}`;
  PAYOUT_MESSAGES.PAYOUT = `${localizeV2?.t("PAYOUT")}`;
  PAYOUT_MESSAGES.PAYOUT_APPROVALS = `${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} approvals`;
  PAYOUT_MESSAGES.APPROVED_ALL_PAYOUTS = `You've approved all the ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()}.`;
  PAYOUT_MESSAGES.REJECTED_ALL_PAYOUTS = `You've rejected all the ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()}.`;
  PAYOUT_MESSAGES.WITHDRAWN_SINCE_PAYOUT_UNLOCKED = `Withdrawn since ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} was unlocked`;
  PAYOUT_MESSAGES.WITHDRAWN_SINCE_PAYOUT_REJECTED = `Withdrawn since ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} got rejected`;
  PAYOUT_MESSAGES.NO_LONGER_APPROVE_OR_REJECT_PAYOUT = `Users will no longer be able to approve or reject this ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()}.`;
  PAYOUT_MESSAGES.PAYOUT_CURRENCY = `${localizeV2?.t("PAYOUT")} Currency`;
  PAYOUT_MESSAGES.PAYOUT_AMOUNT = `${localizeV2?.t("PAYOUT")} Amount`;
  PAYOUT_MESSAGES.PAYOUT_FREQUENCY = `${localizeV2?.t("PAYOUT")} Frequency`;
  PAYOUT_MESSAGES.PAYOUT_LOCKED = `${localizeV2?.t("PAYOUT")} Locked`;
  PAYOUT_MESSAGES.PAYOUT_UNLOCKED = `${localizeV2?.t("PAYOUT")} Unlocked`;
  PAYOUT_MESSAGES.PAYOUT_STATUS = `${localizeV2?.t("PAYOUT")} Status`;
  PAYOUT_MESSAGES.NO_PERMISSION_TO_OTHERS_PAYOUT = `You don't have permission to view and manage ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} of other users.`;
  PAYOUT_MESSAGES.TOTAL_PAYOUT = `Total ${localizeV2?.t("PAYOUT")}`;
  PAYOUT_MESSAGES.CURRENT_PAYOUT_MESSAGE = `Total ${localizeV2
    ?.t("PAYOUT")
    .toLowerCase()} from current period`;
  PAYOUT_MESSAGES.PLEASE_SELECT_PAYOUT_FREQUENCY = `Please select ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} frequency!`;
  PAYOUT_MESSAGES.SELECT_PAYOUT_FREQUENCY = `Select ${localizeV2?.t(
    "PAYOUT"
  )} Frequency`;
  PAYOUT_MESSAGES.NO_PAYOUT_FREQUENCY = `Payee does not have a ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()} frequency`;
  PAYOUT_MESSAGES.YOUR_PAYOUTS = `Your ${localizeV2?.t("PAYOUTS")}`;
  PAYOUT_MESSAGES.PAYOUTS_TRACKER = `${localizeV2?.t("PAYOUTS")} Tracker`;
  PAYOUT_MESSAGES.CANNOT_SIMULATE_SINCE_NO_PERMISSION = `You can't simulate since you don't have permission to view ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()} data`;
  PAYOUT_MESSAGES.PAYOUT_PERIOD = `${localizeV2?.t("PAYOUT")} Period`;
  PAYOUT_MESSAGES.COMMISSION_PAYOUT_SCHEDULE = `${localizeV2?.t(
    "COMMISSION"
  )} ${localizeV2?.t("PAYOUT")} Schedule`;
  PAYOUT_MESSAGES.PAYOUT_DETAILS = `${localizeV2?.t("PAYOUT")} Details`;
  PAYOUT_MESSAGES.PAYOUT_SUMMARY = `${localizeV2?.t("PAYOUT")} Summary`;
  PAYOUT_MESSAGES.PAYOUT_LOCKED_SUCESSFULLY = `${localizeV2?.t(
    "PAYOUT"
  )} locked successfully`;
  PAYOUT_MESSAGES.ERROR_LOCKING_PAYOUT = `Error while Locking ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()}. Please try again later`;
  PAYOUT_MESSAGES.PAYOUT_UNLOCKED_SUCESSFULLY = `${localizeV2?.t(
    "PAYOUT"
  )} unlocked successfully`;
  PAYOUT_MESSAGES.ERROR_UNLOCKING_PAYOUT = `Error while unlocking ${String(
    localizeV2?.t("PAYOUT")
  ).toLowerCase()}. Please try again later`;
  PAYOUT_MESSAGES.PAYOUT_FROM_CURRENT_PERIOD = `${localizeV2?.t(
    "PAYOUT"
  )} from current period`;
  PAYOUT_MESSAGES.PAYOUT_ARREARS = `${localizeV2?.t("PAYOUT")} ${localizeV2?.t(
    "ARREARS"
  )}`;
  PAYOUT_MESSAGES.PAYOUTS_VIEW = `${localizeV2?.t("PAYOUTS")} View`;
  PAYOUT_MESSAGES["100_VARIABLE_PAYOUT_LABEL"] = `100% Variable ${localizeV2?.t(
    "PAYOUT"
  )}`;
  PAYOUT_MESSAGES.VARIABLE_PAYOUT_LABEL = `Variable ${localizeV2?.t("PAYOUT")}`;
  PAYOUT_MESSAGES.PROJECTED_PAYOUT_LABEL = `Projected ${localizeV2?.t(
    "PAYOUT"
  )}`;
  PAYOUT_MESSAGES.CURRENT_PAYOUT_LABEL = `Current ${localizeV2?.t("PAYOUT")}`;
  PAYOUT_MESSAGES.ON_TARGET_VARIABLE_PAY = `${localizeV2?.t(
    "ON_TARGET_VARIABLE_PAY"
  )}`;
  QUOTA_MESSAGES.ADD_NEW_QUOTA = `+ Add ${localizeV2?.t("QUOTA")}`;
  PAYOUT_MESSAGES.PAYOUT_CYCLE = `${localizeV2?.t("PAYOUT")} Cycle`;
  ADJUSTMENT_MESSAGES.ADJUSTMENTS = `${localizeV2?.t("ADJUSTMENTS")}`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT = `${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.MAKE_AN_ADJJUSTMENT = `Make an ${String(
    localizeV2?.t("ADJUSTMENT")
  ).toLowerCase()}`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_TYPE = `${localizeV2?.t("ADJUSTMENT")} Type`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_SCOPE = `${localizeV2?.t("ADJUSTMENT")} Scope`;
  ADJUSTMENT_MESSAGES.VIEW_ADJUSTMENT = `View ${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.REVERT_ADJUSTMENT = `Revert ${localizeV2?.t(
    "ADJUSTMENT"
  )}`;
  ADJUSTMENT_MESSAGES.PRE_ADJUSTMENT = `Pre ${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.POST_ADJUSTMENT = `Post ${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.ADD_ADJUSTMENT = `Add ${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.DELETE_ADJUSTMENT_CONFIRM = `Are you sure you want to delete this ${String(
    localizeV2?.t("ADJUSTMENT")
  ).toLowerCase()}?`;
  ADJUSTMENT_MESSAGES.DELETE_BULK_ADJUSTMENT_CONFIRM = `Are you sure you want to delete the selected ${String(
    localizeV2?.t("ADJUSTMENTS")
  ).toLowerCase()}?`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_DELETED = `${localizeV2?.t(
    "ADJUSTMENT"
  )} deleted successfully`;
  ADJUSTMENT_MESSAGES.EDIT_ADJUSTMENT = `Edit ${localizeV2?.t("ADJUSTMENT")}`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_SAVED_SUCESSFULLY = `${localizeV2?.t(
    "ADJUSTMENT"
  )} saved successfully`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_MODIFIED_SUCESSFULLY = `${localizeV2?.t(
    "ADJUSTMENT"
  )} modified successfully`;
  ADJUSTMENT_MESSAGES.OTHER_ADJUSTMENTS = `Other ${localizeV2?.t(
    "ADJUSTMENTS"
  )}`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_UPDATED_SUCESSFULLY = `${localizeV2?.t(
    "ADJUSTMENT"
  )} updated successfully`;
  ADJUSTMENT_MESSAGES.DRAW_ADJUSTMENTS = `Draw ${localizeV2?.t("ADJUSTMENTS")}`;
  ADJUSTMENT_MESSAGES.COMMISSION_ADJUSTMENTS = `${localizeV2?.t(
    "COMMISSION"
  )} ${localizeV2?.t("ADJUSTMENTS")}`;
  ADJUSTMENT_MESSAGES.APPROVAL_MANDATORY_FOR_COMMISSION_ADJUSTMENTS = `Makes approval mandatory to add commission ${String(
    localizeV2?.t("ADJUSTMENTS")
  ).toLowerCase()}.`;
  ARREAR_MESSAGES.ARREARS = `${localizeV2?.t("ARREARS")}`;
  ARREAR_MESSAGES.ARREAR = `${localizeV2?.t("ARREAR")}`;
  ARREAR_MESSAGES.ARREAR_PROCESS_SUCESSFUL = `${localizeV2?.t(
    "ARREAR"
  )} processed successfully`;
  ARREAR_MESSAGES.ARREAR_IGNORED_SUCESSFULLY = `${localizeV2?.t(
    "ARREAR"
  )} ignored successfully`;
  ARREAR_MESSAGES.ARREARS_VIEW = `${localizeV2?.t("ARREARS")} View`;
  ARREAR_MESSAGES.PROCESS_ARREAR = `Process ${localizeV2?.t("ARREAR")}`;
  ARREAR_MESSAGES.DUE_ARREAR = `Due/${localizeV2?.t("ARREAR")}`;
  DEFERRED_MESSAGES.DEFERRED = `${localizeV2?.t("DEFERRED")}`;
  DEFERRED_MESSAGES.NO_PREVIOUS_DEFERRED_COMMISSIONS = `No previous ${String(
    localizeV2?.t("DEFERRED")
  ).toLowerCase()} ${String(localizeV2?.t("COMMISSIONS")).toLowerCase()}`;
  DEFERRED_MESSAGES.PAYOUTS_FROM_DEFERRED_COMMISSIONS = `Payouts from ${String(
    localizeV2?.t("DEFERRED")
  ).toLowerCase()} ${String(localizeV2?.t("COMMISSIONS")).toLowerCase()} for`;
  CRTSTAL_MESSAGES.SELECT_OPPOURTINITIES = `Select the most promising opportunities to simulate potential
    ${String(localizeV2?.t("COMMISSIONS")).toLowerCase()} and ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment.`;
  CRTSTAL_MESSAGES.ANIMATIONS_CALC_COMM = `Calculating ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()}... hang tight, your riches are coming`;
  CRTSTAL_MESSAGES.ANIMATIONS_TIMEOUT_DATA = `We’re doing a little extra math to make sure your ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} projections are spot on.. hang tight, it’ll be worth it in the end`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_ID = `${localizeV2?.t("ADJUSTMENT")} ID`;
  ADJUSTMENT_MESSAGES.ADJUSTMENT_DATE = `${localizeV2?.t("ADJUSTMENT")} Date`;
  COMMISSION_MESSAGES.TRACK_COMM_EACH_MONTH = `Track your ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()} for each month in a ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} period.`;
  COMMISSION_MESSAGES.COMM_REPORT = `${localizeV2?.t("COMMISSION")} Report`;
  COMMISSION_MESSAGES.INTER_COMM_REPORT = `Inter ${localizeV2?.t(
    "COMMISSION"
  )} Report`;
  COMMISSION_MESSAGES.TAG_DEAL_INSTRUCTION = `Tag the deal id variable in datasheet that is used in ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} plan`;
  COMMISSION_MESSAGES.COMM_BUDDY = `${localizeV2?.t("COMMISSION")} Buddy`;
  QUOTA_MESSAGES.GET_ACTIONABLE_INSIGHT = `Get actionable insights on how you can meet your ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()}.`;
  PAYOUT_MESSAGES.TO_TRACK_PAYOUTS = `to track to 100 % ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()}`;
  COMMISSION_MESSAGES.MAKE_MORE_COMM = `How do I make more ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()}?`;
  COMMISSION_MESSAGES.COMM_STRUCTURE = `${localizeV2?.t(
    "COMMISSION"
  )} Structure`;
  QUOTA_MESSAGES.DUPLICATE_QUOTA = `Duplicate ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} from another payee`;
  PAYOUT_MESSAGES.YTD_PAYOUTS = `YTD ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()}:`;
  PAYOUT_MESSAGES.BUDGETED_PAYOUTS = `of the budgeted ${String(
    localizeV2?.t("PAYOUTS")
  ).toLowerCase()}`;
  QUOTA_MESSAGES.QUOTA_ATTAINMENT_TIER = `Tier based on ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment %`;
  QUOTA_MESSAGES.QUOTA_EROSION_TIER = `Tier based on ${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()}`;
  QUOTA_MESSAGES.CUMMILATIVE_QA = `Cumulative ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment`;
  QUOTA_MESSAGES.CUMMILATIVE_QE = `Cumulative ${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()}`;
  QUOTA_MESSAGES.RANGE_QE = `Range based on ${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()}`;
  QUOTA_MESSAGES.RANGE_QA = `Range based on ${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()} attainment %`;

  QUOTA_MESSAGES.MANAGE_QUOTA_CATEGORY = `Manage ${String(
    localizeV2?.t("QUOTA")
  )} Category`;
  QUOTA_MESSAGES.TEAM_QA = `Team-${localizeV2?.t("QUOTA")}Attainment`;
  QUOTA_MESSAGES.TEAM_QE = `Team-${String(
    localizeV2?.t("QUOTA_EROSION")
  ).replace(" ", "")}`;
  QUOTA_MESSAGES.TEAM_QUOTA = `Team-${localizeV2?.t("QUOTA")}`;

  COMM_TRACE_MESSAGES.QUOTA_LOWERCASE = `${String(
    localizeV2?.t("QUOTA")
  ).toLowerCase()}`;
  COMM_TRACE_MESSAGES.QUOTA = `${localizeV2?.t("QUOTA")}`;
  COMM_TRACE_MESSAGES.QUOTA_EROSION = `${String(
    localizeV2?.t("QUOTA_EROSION")
  ).charAt(0)}${String(localizeV2?.t("QUOTA_EROSION")).slice(1).toLowerCase()}`;
  COMM_TRACE_MESSAGES.QUOTA_EROSION_LOWERCASE = `${String(
    localizeV2?.t("QUOTA_EROSION")
  ).toLowerCase()}`;
  COMM_TRACE_MESSAGES.QUOTAEROSION = `${String(
    localizeV2?.t("QUOTA_EROSION")
  ).replace(" ", "")}`;
  COMM_TRACE_MESSAGES.MULTIPLE_TIER_COMM = `Your ${String(
    localizeV2?.t("COMMISSION")
  ).toLowerCase()} calculated for this deal falls under multiple tiers.
  Trace respective ${String(
    localizeV2?.t("COMMISSIONS")
  ).toLowerCase()} below.`;
  QUOTA_MESSAGES.DIFF_QUOTA_SCHEDULE_REPORTS = `Reports are on different ${localizeV2?.t(
    "QUOTA"
  )} schedule`;
}

/* eslint-disable no-unused-vars */
localizeV2.on("loaded", function (lng) {
  updateLocalizationMessages();
});
