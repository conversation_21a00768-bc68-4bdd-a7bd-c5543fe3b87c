import { EXPRESSION_FUNCTION_TYPES } from "~/Enums";
export function autoSuggestionByot(data, t) {
  return data?.map((item) => {
    switch (item.label) {
      case EXPRESSION_FUNCTION_TYPES.Quota: {
        item.label = t("QUOTA");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaErosion: {
        item.label = t("QUOTA_EROSION");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaAttainment: {
        item.label = t("QUOTAATTAINTMENT");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-Quota"]: {
        item.label = `TEAM-${t("QUOTA")}`;
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"]: {
        item.label = `TEAM-${t("QUOTA_EROSION")}`;
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"]: {
        item.label = `TEAM-${t("QUOTAATTAINTMENT")}`;
        break;
      }
      default: {
        break;
      }
    }
    return item;
  });
}
