import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import translationEN from "~/locales/en";

export const localizeV2 = i18n.createInstance();

localizeV2
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    interpolation: {
      escapeValue: false,
      format: function (value, format) {
        if (format === "removespaces") return value.replaceAll(" ", "");
        if (format === "uppercase") return value.toUpperCase();
        if (format === "lowercase") return value.toLowerCase();
        if (format === "titlecase")
          return value
            .toLowerCase()
            .replaceAll(/(^\s*\w|[!.?]\s*\w)/g, function (c) {
              return c.toUpperCase();
            });
        return value;
      },
    },
    resources: {
      en: {
        translation: translationEN,
      },
    },
    lng: "en",
    fallbackLng: "en",
    fallbackNS: "glossary",
  });

export const fetchGlossaryData = async (accessToken) => {
  try {
    const response = await fetch("/spm/localization?lng=en", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    });
    const data = await response.json();
    localizeV2.addResourceBundle("en", "glossary", data);
  } catch (error) {
    console.error("Error fetching glossary data:", error);
  }
};
