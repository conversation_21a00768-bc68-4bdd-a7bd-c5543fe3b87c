{"MAKE_AN_ADJJUSTMENT": "Make an $t(ADJUSTMENT, lowercase)", "ADJUSTMENT_TYPE": "$t(ADJUSTMENT) Type", "ADJUSTMENT_SCOPE": "$t(ADJUSTMENT) Scope", "VIEW_ADJUSTMENT": "View $t(ADJUSTMENT)", "REVERT_ADJUSTMENT": "Revert $t(ADJUSTMENT)", "PRE_ADJUSTMENT": "Pre $t(ADJUSTMENT)", "POST_ADJUSTMENT": "Post $t(ADJUSTMENT)", "ADD_ADJUSTMENT": "Add $t(ADJUSTMENT)", "DELETE_ADJUSTMENT_CONFIRM": "Are you sure you want to delete this $t(ADJUSTMENT, lowercase)?", "DELETE_BULK_ADJUSTMENT_CONFIRM": "Are you sure you want to delete the selected $t(ADJUSTMENTS, lowercase)?", "ADJUSTMENT_DELETED": "$t(ADJUSTMENT) deleted successfully", "ADJUSTMENTS_DELETED": "$t(ADJUSTMENTS) deleted successfully", "EDIT_ADJUSTMENT": "Edit $t(ADJUSTMENT)", "ADJUSTMENT_SAVED_SUCESSFULLY": "$t(ADJUSTMENT) saved successfully", "ADJUSTMENT_MODIFIED_SUCESSFULLY": "$t(ADJUSTMENT) modified successfully", "OTHER_ADJUSTMENTS": "Other $t(ADJUSTMENTS)", "ADJUSTMENT_UPDATED_SUCESSFULLY": "$t(ADJUSTMENT) updated successfully", "DRAW_ADJUSTMENTS": "Draw $t(ADJUSTMENTS)", "COMMISSION_ADJUSTMENTS": "$t(COMMISSION) $t(ADJUSTMENTS)", "APPROVAL_MANDATORY_FOR_COMMISSION_ADJUSTMENTS": "Makes approval mandatory to add $t(COMMISSION, lowercase) $t(ADJUSTMENTS, lowercase).", "ADJUSTMENT_ID": "$t(ADJUSTMENT) ID", "ADJUSTMENT_DATE": "$t(ADJUSTMENT) Date"}