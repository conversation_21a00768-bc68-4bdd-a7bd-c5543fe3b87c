import { FUNCTION_NAMES as FN } from "~/Enums";

// text inside backtick(``) will fall under <Label /> component
const commissionTrace = {
  COMMISSION_TRACE: {
    MULTIPLE_TIER_COMM:
      "Your $t(COMMISSION,lowercase) calculated for this deal falls under multiple tiers. Trace respective $t(COMMISSIONS,lowercase) below.",
    TIER_SPLIT_UP_HEADER: {
      TIER_VALUE: "Tier value",
      CUMULATIVE_QUOTA_ATTAINMENT: "Cumulative $t(QUOTA, lowercase) attainment",
      CUMULATIVE_QUOTA_EROSION: "Cumulative $t(QUOTA_EROSION, lowercase)",
      QUOTA_EROSION_RANGE: "Range based on $t(QUOTA_EROSION, lowercase)",
      QUOTA_ATTAINMENT_RANGE:
        "Range based on $t(QUOTA, lowercase) attainment %",
      TIER_VALUE_RANGE: "Range based on tier value",
    },
    TRACE_INFO_LABEL: {
      date: "Date",
      quotaRetirement: "$t(QUOTA_EROSION, titlecase)",
      tierBasedOnQuotaAttainmentPercentage:
        "Tier based on $t(QUOTA, lowercase) attainment %",
      tierBasedOnQuotaErosion: "Tier based on $t(QUOTA_EROSION, lowercase)",
      quota: "$t(QUOTA)",
      overriddenTier: "Overridden tier",
      team: "Team",
      tier: "Tier",
      tierValue: "Tier Value",
    },
    IF: "IF",
    THEN: "THEN",
    ELSE: "ELSE",
    DAY: "Days",
    MONTH: "Month",
    QUARTER: "Quarter",
    HALFYEAR: "Half-year",
    YEAR: "Year",
    "HH:MM": "hours:minutes",
    SHOW_FULL_TRACE: "Show full trace",
    SHOW_ALL: "Show all",
    CONDITION_TITLE: "Condition(s)",
    FORMULA_TITLE: "Formula",
    DO_NOTHING: "Do Nothing",
    TOTAL: "Total",
    TO: "to",
    UPTO: "Upto",
    ABOVE: "Above",
    SINCE: "Since",
    VIEW_FORMULA: "To view the formula, click",
    HERE: "here",
    CONDITIONS_MET: "`Since` these condition(s) `were met`",
    NO_CONDITIONS_MET: "`Since` none of these condition(s) `were met`",
    SOME_CONDITIONS_MET:
      "`Since` these records matched some of these condition(s)",
    EMPTY_OUTPUT_FUNCTION: "The output of this function has no value",
    EMPTY_FIELD: "This field is empty",
    MULTIPLE_TIER_INFO:
      "Your $t(COMMISSION,lowercase) calculated for this deal falls under multiple tiers. Trace respective $t(COMMISSIONS,lowercase) below.",
    ERROR_MESSAGE:
      "Whoops! Something didn't quite go as planned. Please try again later.",
    VERBAL_OPERATORS: {
      "<=": "is less than or equal to",
      ">=": "is greater than or equal to",
      "!=": "is not equal to",
      "==": "is equal to",
      "<": "is less than",
      ">": "is greater than",
      AND: "AND",
      OR: "OR",
      IN: "is IN",
      NOTIN: "is NOT IN",
    },
    FUNCTION_LABEL_MAP: {
      [FN.QUOTA_ATTAINMENT]: "$t(QUOTA) attainment",
      [FN.QUOTA_EROSION]: "$t(QUOTA_EROSION, titlecase)",
      [FN.QUOTA]: "$t(QUOTA)",
      [FN.TEAM_QUOTA_ATTAINMENT]: "Team-$t(QUOTA) attainment",
      [FN.TEAM_QUOTA_EROSION]: "Team-$t(QUOTA_EROSION, titlecase)",
      [FN.TEAM_QUOTA]: "Team-$t(QUOTA)",
      [FN.TIERED_VALUE]: "Tier value",
      [FN.TIERED_PERCENTAGE]: "Tier percentage",
    },
    FUNCTION_TOOLTIP_TEXT: {
      [FN.TIERED_VALUE]:
        "Tiered value {{roundType}} by {{decimalValue}} decimal places",
      [FN.TIERED_PERCENTAGE]:
        "Tiered percentage {{roundType}} by {{decimalValue}} decimal places",
      [FN.SUM]: "Sum of {{fieldName}}",
      [FN.MIN]: "Minimum of {{fieldName}}",
      [FN.MAX]: "Maximum of {{fieldName}}",
      [FN.AVG]: "Average of {{fieldName}}",
      [FN.COUNT_NOT_NULL]: "Count of {{fieldName}} not null",
      [FN.DISTINCT_COUNT]: "Distinct count of {{fieldName}}",
      [FN.IS_EMPTY]: "{{fieldName}} is empty",
      [FN.IS_NOT_EMPTY]: "{{fieldName}} is not empty",
      [FN.LOWER]: "Lowercase of {{fieldName}}",
      [FN.CURRENT_PAYOUT_PERIOD]: "Current payout period is {{unit}}",
      [FN.CONTAINS]: "{{fieldName}} contains {{value}}",
      [FN.NOT_CONTAINS]: "{{fieldName}} does not contain {{value}}",
      [FN.CONCAT]: 'Concatenation ("{{delimiterValue}}", [{{fieldNameList}}])',
      [FN.FIND]: "Position of {{value}} in {{fieldName}}",
      [FN.GETDATE]: {
        DAY: "Day of the month in {{fieldName}}",
        YEAR: "{{calendarType}} year in {{fieldName}}",
        DEFAULT: "{{unit}} of the {{calendarType}} year in {{fieldName}}",
      },
      [FN.DATE_DIFF]: {
        WITH_CALENDAR_TYPE:
          "Date difference between {{fieldName1}} and {{fieldName2}} in {{calendarType}} {{unit}}",
        DEFAULT:
          "Date difference between {{fieldName1}} and {{fieldName2}} in {{unit}}",
      },
      [FN.QUOTA_ATTAINMENT]: "{{quotaName}} $t(QUOTA) for {{period}}",
      [FN.QUOTA_EROSION]: "{{quotaName}} $t(QUOTA) for {{period}}",
      [FN.QUOTA]: "{{quotaName}} $t(QUOTA) for {{period}}",
      [FN.SUM_IF]: {
        MULTIPLE: "Sum of {{fieldName}} since these condition(s) were met",
        DEFAULT: "Sum of {{fieldName}} since {{condition}}",
      },
      [FN.COUNT_IF]: {
        MULTIPLE: "Count of {{fieldName}} since these condition(s) were met",
        DEFAULT: "Count of {{fieldName}} since {{condition}}",
      },
      // TEAM
      [FN.TEAM_SUM]: "Sum of {{fieldName}} for the team",
      [FN.TEAM_MIN]: "Minimum of {{fieldName}} for the team",
      [FN.TEAM_MAX]: "Maximum of {{fieldName}} for the team",
      [FN.TEAM_AVG]: "Average of {{fieldName}} for the team",
      [FN.TEAM_COUNT_NOT_NULL]: "Count of {{fieldName}} not null for the team",
      [FN.TEAM_DISTINCT_COUNT]: "Distinct count of {{fieldName}} for the team",
      [FN.TEAM_SUM_IF]: {
        MULTIPLE:
          "Team based Sum of {{fieldName}} since these condition(s) were met",
        DEFAULT: "Team based Sum of {{fieldName}} since {{condition}}",
      },
      [FN.TEAM_COUNT_IF]: {
        MULTIPLE:
          "Team based Count of {{fieldName}} since these condition(s) were met",
        DEFAULT: "Team based Count of {{fieldName}} since {{condition}}",
      },
      [FN.TEAM_QUOTA_ATTAINMENT]:
        "{{quotaName}} $t(QUOTA) for {{period}} for the team",
      [FN.TEAM_QUOTA_EROSION]:
        "{{quotaName}} $t(QUOTA) for {{period}} for the team",
      [FN.TEAM_QUOTA]: "{{quotaName}} $t(QUOTA) for {{period}} for the team",
      // Calculated Fields
      [FN.START_DATE]: {
        WITH_CALENDAR_TYPE:
          "Start date of {{fieldName}} in {{calendarType}} {{unit}}",
        DEFAULT: "Start date of {{fieldName}} in {{unit}}",
      },
      [FN.LAST_DATE]: {
        WITH_CALENDAR_TYPE:
          "Last date of {{fieldName}} in {{calendarType}} {{unit}}",
        DEFAULT: "Start date of {{fieldName}} in {{unit}}",
      },
      [FN.DATE_ADD]: "Date addition of {{fieldName}} and {{value}} in {{unit}}",
      [FN.TIMEZONE]:
        "Time zone for {{fieldName}} is converted from {{fromTimezone}} to {{toTimezone}}",
      [FN.ROUND]:
        "Value of {{fieldName}} rounded by {{decimalValue}} decimal places",
      [FN.ROUND_UP]:
        "Value of {{fieldName}} rounded up by {{decimalValue}} decimal places",
      [FN.ROUND_DOWN]:
        "Value of {{fieldName}} rounded down by {{decimalValue}} decimal places",
      [FN.RANK]: {
        WITH_PARTITION:
          "Rank of {{fieldName}} grouped by {{partitionFields}} in {{order}} order",
        DEFAULT: "Rank of {{fieldName}} in {{order}} order",
      },
      [FN.ROLLING_SUM]: {
        WITH_PARTITION:
          "Running total of {{fieldName}} sorted based on {{orderFields}} grouped by {{partitionFields}}",
        DEFAULT:
          "Running total of {{fieldName}} sorted based on {{orderFields}}",
      },
      [FN.TRIM]: "Trimmed output of {{fieldName}}",
      [FN.LEN]: "Length of {{fieldName}}",
      [FN.LEFT]:
        "Substring of {{fieldName}} which is {{value}} characters from the left side",
      [FN.RIGHT]:
        "Substring of {{fieldName}} which is {{value}} characters from the right side",
      [FN.MID]:
        "Substring starting at {{startingIndex}} and consisting of {{value}} characters in {{fieldName}}",
      [FN.GET_USER_PROPERTY]:
        "GetUserProperty ({{property}}) as of {{dateColumn}} for the payee determined by {{emailColumn}}",
    },
    FUNCTION_MAP: {
      [FN.SUM]: "SUM ({{fieldName}})",
      [FN.MIN]: "MIN ({{fieldName}})",
      [FN.MAX]: "MAX ({{fieldName}})",
      [FN.AVG]: "AVG ({{fieldName}})",
      [FN.COUNT_NOT_NULL]: "CountNotNull ({{fieldName}})",
      [FN.DISTINCT_COUNT]: "DistinctCount ({{fieldName}})",
      [FN.IS_EMPTY]: "IsEmpty ({{fieldName}})",
      [FN.IS_NOT_EMPTY]: "IsNotEmpty ({{fieldName}})",
      [FN.LOWER]: "Lowercase ({{fieldName}})",
      [FN.CURRENT_PAYOUT_PERIOD]: "CurrentPayoutPeriod ({{unit}})",
      [FN.CONTAINS]: "{{fieldName}} contains {{value}}",
      [FN.NOT_CONTAINS]: "{{fieldName}} does not contain {{value}}",
      [FN.CONCAT]: 'Concat ("{{delimiterValue}}", [{{fieldNameList}}])',
      [FN.FIND]: "PositionOf ({{value}} `in` {{fieldName}})",
      [FN.GETDATE]: {
        DEFAULT: "GetDate ({{fieldName}}, {{unit}})",
        WITH_CALENDAR_TYPE:
          "GetDate ({{fieldName}}, {{unit}}, {{calendarType}})",
      },
      [FN.DATE_DIFF]: {
        WITH_CALENDAR_TYPE:
          "DateDifference `between` {{fieldName1}} `and` {{fieldName2}} `in` {{calendarType}} {{unit}}",
        DEFAULT:
          "DateDifference `between` {{fieldName1}} `and` {{fieldName2}} `in` {{unit}}",
      },
      [FN.QUOTA_ATTAINMENT]:
        "$t(QUOTA)Attainment ({{quotaName}}) `for` {{period}}",
      [FN.QUOTA_EROSION]:
        "$t(QUOTA_EROSION, removespaces) ({{quotaName}}) `for` {{period}}",
      [FN.QUOTA]: "$t(QUOTA) ({{quotaName}}) `for` {{period}}",
      [FN.SUM_IF]: "SUM",
      [FN.COUNT_IF]: "COUNT",
      // TEAM
      [FN.TEAM_SUM]: "Team-SUM ({{fieldName}})",
      [FN.TEAM_MIN]: "Team-MIN ({{fieldName}})",
      [FN.TEAM_MAX]: "Team-MAX ({{fieldName}})",
      [FN.TEAM_AVG]: "Team-AVG ({{fieldName}})",
      [FN.TEAM_COUNT_NOT_NULL]: "Team-CountNotNull ({{fieldName}})",
      [FN.TEAM_DISTINCT_COUNT]: "Team-DistinctCount ({{fieldName}})",
      [FN.TEAM_SUM_IF]: "Team-SUM",
      [FN.TEAM_COUNT_IF]: "Team-COUNT",
      [FN.TEAM_QUOTA_ATTAINMENT]:
        "Team-$t(QUOTA)Attainment ({{quotaName}}) `for` {{period}}",
      [FN.TEAM_QUOTA_EROSION]:
        "Team-$t(QUOTA_EROSION, removespaces) ({{quotaName}}) `for` {{period}}",
      [FN.TEAM_QUOTA]: "Team-$t(QUOTA) ({{quotaName}}) `for` {{period}}",
      // Calculated Fields
      [FN.START_DATE]: {
        WITH_CALENDAR_TYPE:
          "StartDate ({{fieldName}}) `in` {{calendarType}} {{unit}}",
        DEFAULT: "StartDate ({{fieldName}}) `in` {{unit}}",
      },
      [FN.LAST_DATE]: {
        WITH_CALENDAR_TYPE:
          "LastDate ({{fieldName}}) `in` {{calendarType}} {{unit}}",
        DEFAULT: "LastDate ({{fieldName}}) `in` {{unit}}",
      },
      [FN.DATE_ADD]:
        "Date addition `of` {{fieldName}} `and` {{value}} `in` {{unit}}",
      [FN.TIMEZONE]:
        "Timezone converted `for` {{fieldName}} `from` {{fromTimezone}} `to` {{toTimezone}}",
      [FN.ROUND]:
        "({{fieldName}}) rounded `by` {{decimalValue}} `decimal places`",
      [FN.ROUND_UP]:
        "({{fieldName}}) rounded up `by` {{decimalValue}} `decimal places`",
      [FN.ROUND_DOWN]:
        "({{fieldName}}) rounded down `by` {{decimalValue}} `decimal places`",
      [FN.TRIM]: "Trimmed output ({{fieldName}})",
      [FN.LEN]: "Length ({{fieldName}})",
      [FN.LEFT]:
        "Substring `of` {{fieldName}} `which is` {{value}} `characters from the` left side",
      [FN.RIGHT]:
        "Substring `of` {{fieldName}} `which is` {{value}} `characters from the` right side",
      [FN.MID]:
        "Substring starting at {{startingIndex}} `and consisting of` {{value}} `characters in` {{fieldName}}",
      [FN.GET_USER_PROPERTY]:
        "GetUserProperty ({{property}}) as of {{dateColumn}} for the payee determined by {{emailColumn}}",
    },
  },
};

export default commissionTrace;
