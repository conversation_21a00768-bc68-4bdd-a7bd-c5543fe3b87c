{"APPROVAL_REQUEST_WARNINGS": {"TITLE": "You cannot request approval when,", "UNLOCK_WARNING": "$t(PAYOUT) is unlocked (or)", "COMMISSION_WARNING": "Earned $t(COMMISSION) is 0 (or)", "ALREADY_APPROVED": "$t(PAYOUT) is already approved or submitted for approval"}, "APPROVAL_STATUS": {"APPROVED": "Approved", "PENDING": "Pending approval", "REJECTED": "Rejected", "WITHDRAWN": "Withdrawn", "ABORTED": "Aborted", "REVOKED": "Revoked", "NEEDS_ATTENTION": "Needs attention", "REQUESTED": "Requested", "NOT_REQUESTED": "Not requested"}, "APPROVING": "Approving...", "APPROVING_REQUEST": "Approving request...", "BREAKDOWN": "Breakdown", "COMISSION_SYNC": "$t(COMMISSION) Sync", "COMMISSION_PERCENT": "$t(COMMISSION) %", "DENOTES_COMPONENTS_THAT_ARE_HIDDEN_FROM_PAYEES": "Denotes components that are hidden from payees", "DISPLAY_CURRENCY": "<PERSON><PERSON>lay Currency", "DOWNLOADING_FILE": "Downloading {{fileName}}", "DOWNLOAD_SUCCESS": "Downloaded Successfully!!", "DRAW_AVAILED": "Draw Availed", "DRAW_RECOVERED": "Draw Recovered", "DUE_BY": "Due by", "EMAILING_STATEMENTS": "Emailing statements to {{emailId}}...", "EMAIL_STATEMENTS": "Email Statements", "ENTER_EMAIL_ID_OF_THE_PERSON_TO_SEND_THE_STATEMENTS_TO": "Enter Email ID of the person to send the statements to", "ENTER_THE_VALID_EMAIL": "Enter the valid email!", "ERROR_WHILE_FETCHING_APPROVAL_DATA": "Error while fetching approval data", "EXPORTING_EXCEL": "Exporting statements as Excel...", "EXPORTING_PDF": "Exporting your statement as PDF...", "EXPORT_EXCEL_ERROR": "Error while exporting statements as Excel", "EXPORT_FORMATS": {"EXCEL": "Microsoft Excel (.xlsx)", "PDF": "PDF (.pdf)"}, "EXPORT_PDF_ERROR": "Error while exporting statements as PDF", "EXPORT_STATEMENT": "Export Statement", "FULLY_PAID": "Fully paid", "GLOBAL": "Global", "GLOBAL_CURRENCY": "Global Currency", "HIDDEN_COMPONENT": "Hidden component", "LAST_CALCULATED_INFO": "Last calculated", "LAST_UPDATED_INFO": "Last updated", "LINE_ITEM": "Line Item", "LOCK_RESTRICTED": {"TITLE": "Locking Restricted", "MESSAGE": "This payee has unsynced $t(COMMISSION,lowercase)-impacting changes, hence their statement could not be locked. Run $t(COMMISSION) Sync for them in this payout period, then return to complete locking their statement"}, "MAKE_PAYMENT": {"TOTAL_PAYOUT": "Total $t(PAYOUT)", "TO_BE_PAID": "To be paid", "AMOUNT": "Amount", "COMMENTS": "Comments", "OPTIONAL": "Optional", "ADD_YOUR_COMMENTS": "Add your comments", "PAYMENT_ALREADY_DONE": "Payment is already done for selected payee(s)", "PAYMENT_ALREADY_DONE_WARNING": "You can register payment only for locked $t(PAYOUTS,lowercase). Unlocked $t(PAYOUT,lowercase) will be skipped.", "REGISTER_PAYMENT": "Register Payment", "PAYMENT_FOR_PAYEES": "Payment for {{count}} payee(s)", "FULL_PAYMENT": "Full payment", "PARTIAL_PAYMENT": "Partial payment"}, "MY_STATEMENT": "My Statement", "NEW_CURRENCY": "New Currency", "NOT_PAID": "Not paid", "OF_YOUR_TARGET": "of your target", "OVER_PAID": "Over paid", "PARTIALLY_PAID": "Partially paid", "PAYEE_CURRENCY": "<PERSON>ee <PERSON>", "PAYMENT_REGISTERED_SUCCESS": "Payment registered successfully", "PAYMENT_REGISTER_ERROR": "Error while registering payment. Please try again later", "PAYMENT_RESTRICTED": {"TITLE": "Payment registration restricted", "MESSAGE": "This payee has unsynced $t(COMMISSION,lowercase)-impacting changes, hence their payment could not be registered. Run $t(COMMISSION) Sync for them in this $t(PAYOUT,lowercase) period, then return to complete registering their payment"}, "PAYOUTS_AND_ARREARS_NOT_PROCESSED_BY_ADMIN": "$t(PAYOUTS) and $t(ARREARS) for the current period have not been processed by the admin yet.", "PAYOUT_VALUES_CHANGE": {"TITLE": "$t(PAYOUT) values may change", "SUBTITLE": "The $t(PAYOUT,lowercase) values may have been updated. Unlocking this section will modify the locked $t(COMMISSION,lowercase) values.", "NOTE_MESSAGE_1": "To ensure accurate data, we recommend running the ", "NOTE_MESSAGE_2": " after unlocking. This will calculate $t(COMMISSIONS,lowercase) using the latest data and ensure accuracy. If you need the current $t(COMMISSION,lowercase) data before unlocking, go back and export the statement.", "PROCEED_UNLOCK": "Proceed to unlock", "CONFIRM": "Confirm", "DISCARD": "Discard"}, "PDF_PROGRESS_MESSAGES": {"GATHERING_DATA": "Gathering necessary data...", "SETTING_LAYOUT": "Setting up the layout...", "BUILDING_DOC": "Building your PDF document...", "COMPILING": "Compiling the PDF...", "WRAPPING_UP": "Wrapping up the document creation...", "TAKING_LONGER": "Hang tight, Taking longer than usual..."}, "PREVIOUS_PAYMENT_IN_PROGRESS": "Previous payment is still in progress", "REGISTER_PAYMENT": "Register Payment", "REJECTING": "Rejecting...", "RESTRICTED_USER": "Restricted User", "RUN_COMMISSION_SYNC": "Run $t(COMMISSION) Sync", "SEARCH_PERIOD": "Search Period", "SEARCH_USER": "Search User", "SEND_TO_OTHER_USER": "Send to other user", "SEND_TO_SELF": "Send to self", "STATEMENT": "Statement", "STATEMENTS": "Statements", "STATEMENTS_EMAIL_ERROR": "Error while emailing statements", "STATEMENT_HAS_COMMISSION_ADJUSTMENTS": {"TITLE": "The selected statement has $t(COMMISSION_ADJUSTMENTS,lowercase) that need to be approved.", "DESCRIPTION_1": "If the statement is locked, all ‘requested’ $t(ADJUSTMENTS,lowercase) will be ‘canceled’.", "DESCRIPTION_2": "Review $t(COMMISSION_ADJUSTMENTS,lowercase) requests", "AGREE_SKIP_LOCKING": "<PERSON><PERSON><PERSON>, skip locking the statement.", "NO_LOCK_STATEMENT": "No, lock the statement."}, "STATEMENT_LOCK_STATUS": {"SUCCESS": "Statement {{status}} successfully", "ERROR": "Error while {{action}} statement. Please try again later", "LOCKED": "locked", "UNLOCKED": "unlocked", "LOCKING": "locking", "UNLOCKING": "unlocking"}, "SYNC_IN_PROGRESS": {"TITLE": "Locking Temporarily Disabled", "MESSAGE": "Locking or unlocking statements is disabled while $t(COMMISSION) Sync is in progress. Please try again after the sync is complete.", "DISMISS": "<PERSON><PERSON><PERSON>"}, "THIS_FIELD_IS_REQUIRED": "This field is required!", "VIEW_PROFILE": "View Profile", "WAITING_FOR_YOUR_APPROVAL": "Waiting for your approval", "YOU_RE_ON_FIRE": "You're on fire!", "YOU_VE_ACHIEVED": "You've achieved", "YOU_VE_APPROVED_THE_REQUEST": "You've approved the request!", "YOU_VE_REJECTED_THE_REQUEST": "You've rejected the request!", "ZERO_PAYOUT": "Zero $t(PAYOUT,lowercase)"}