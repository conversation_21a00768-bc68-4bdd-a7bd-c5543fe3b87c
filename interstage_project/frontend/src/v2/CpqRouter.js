import { animated, useTransition } from "@react-spring/web";
import React, { useState, useCallback, lazy, useEffect } from "react";
import {
  Routes,
  Route,
  Navigate,
  useLocation,
  createBrowserRouter,
  createRoutesFromElements,
  RouterProvider,
} from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { CPQ_URLS, RBAC_ROLES } from "~/Enums";
import { useSupabaseBroadcast } from "~/everstage-supabase";
import {
  myClientAtom,
  navPortalAtom,
  currentTheme,
} from "~/GlobalStores/atoms";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import EverNavPortal from "~/v2/components/EverNavPortal";
import { tvOnFire404 } from "~/v2/images";

import {
  LoggedInAsBanner,
  DeploymentFinishMessage,
  NonChromeBanner,
  RedirectTo,
  PageNotFound,
  EverLoader,
  SupportMembershipTimeLeftBanner,
} from "./components";
import { HeaderBar } from "./components/new-sidebar/HeaderBar";
import { SidebarV3 } from "./components/new-sidebar/SideMenu";
import {
  AuthRoute,
  EverErrorBoundary,
  EverBody,
  EverContent,
  EverHeader,
  EverNavBar,
  EverRightContent,
} from "./components/router";
import RouteSwitchAnalyticsTrack from "./components/RouteSwitchAnalyticsTrack";
import { Sidebar } from "./components/sidebar";
import { themerOptions } from "./themes";

const QuotesList = React.lazy(() =>
  import("~/v2/features/cpq/quotes/quotes-list")
);
const BuildQuote = React.lazy(() =>
  import("~/v2/features/cpq/quotes/build-quote")
);
const ListProducts = React.lazy(() =>
  import("~/v2/features/cpq/catalog/list-products")
);

const ViewProduct = React.lazy(() =>
  import("~/v2/features/cpq/catalog/view-product")
);

const EmployeeConfig = React.lazy(() => import("./features/users"));

const Groups = React.lazy(() => import("~/v2/features/groups"));

const Databook = React.lazy(() => import("~/v2/features/databook-summary"));
const Datasheet = React.lazy(() => import("~/v2/features/datasheet"));
const DatabookDetails = lazy(() =>
  import("~/v2/features/databook-summary/databook")
);

const DataSources = React.lazy(() => import("~/v2/features/data-sources"));

const PriceBook = React.lazy(() => import("~/v2/features/cpq/price-book"));
const PriceBookDetails = React.lazy(() =>
  import("~/v2/features/cpq/price-book/Pricebook")
);

const CpqSettings = lazy(() =>
  import("~/v2/features/admin-settings/CpqSettings")
);
const ActivityLogs = React.lazy(() => import("./features/activity-logs"));
const ManageData = React.lazy(() => import("./features/manage-data"));
const BulkUploadData = React.lazy(() =>
  import("~/v2/features/admin-settings/bulk-upload-data-v1")
);
const IntegrationsAndVariables = React.lazy(() =>
  import("~/v2/features/admin-settings/integrations-and-variables")
);
const Connectors = lazy(() =>
  import(
    "~/v2/features/admin-settings/integrations-and-variables/connection-hub/Connectors"
  )
);
const DataSync = React.lazy(() =>
  import("~/v2/features/admin-settings/data-sync-settings")
);
// const FormBuilderList = React.lazy(() =>
//   import("~/v2/features/cpq/form-builders/form-builder-list")
// );
// const FormBuilder = React.lazy(() =>
//   import("~/v2/features/cpq/form-builders/form-builder")
// );
// const QuoteRuleList = React.lazy(() =>
//   import("~/v2/features/cpq/quote-rules/quote-rule-list")
// );
const QuoteRule = React.lazy(() =>
  import("~/v2/features/cpq/quote-rules/quote-rule")
);

const AgentExecutor = React.lazy(() => import("~/v2/features/agent-executor"));

const router = createBrowserRouter(
  // createRoutesFromElements creates the v6 data-api routes from jsx. more: https://reactrouter.com/en/main/utils/create-routes-from-elements
  createRoutesFromElements(<Route path="*" element={<Layout />} />)
);

export function CpqRouter() {
  return <RouterProvider router={router} />;
}

const broadcastConfig = { self: true };
const event = ["databook-version-change"];

/**
 * This is the main layout component for the application.
 * This has breadcrumbs, sidebar, header, content and footer.
 */
export function Layout() {
  const location = useLocation();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const setNavPortalTarget = useSetRecoilState(navPortalAtom);
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const { canUserManageAdmins } = useEmployeeStore();
  const [animationStarted, setAnimationStarted] = useState(false);
  const [currentDataBookVersion, setDatabookCurrentVersion] = useState(null);

  const [message, makeBroadcast] = useSupabaseBroadcast(event, {
    channelPrefix: "databook-version-change-broadcast",
    broadcastConfig,
  });

  function sendMessage(newVersion) {
    makeBroadcast({
      type: "broadcast",
      event: "databook-version-change",
      payload: { data: "version-changed", newVersion: newVersion },
    });
  }

  const myTheme = useRecoilValue(currentTheme);
  const setTheme = useSetRecoilState(currentTheme);

  const transitions = useTransition(location, {
    from: { opacity: 0, display: "none", height: "100%" },
    enter: { opacity: 1, display: "block", width: "100%", height: "100%" },
    leave: { opacity: 0, display: "none", width: "0%", height: "100%" },
    config: {
      duration: 500,
    },
    onStart: () => {
      setAnimationStarted(true);
    },
    onRest: () => {
      setAnimationStarted(false);
    },
  });

  useEffect(() => {
    if (message?.payload?.newVersion) {
      setDatabookCurrentVersion(message.payload.newVersion);
    }
  }, [message]);

  useEffect(() => {
    // get theme from localstorage and set in recoil store
    setTheme(localStorage.getItem("theme") || myTheme || "");

    // set theme in body, along with default bg and text colors
    const classNames = `${localStorage.getItem(
      "theme"
    )} bg-ever-base text-base text-ever-base-content`.split(" ");

    // remove all themes from body
    document.body.classList.remove(...themerOptions.themes.map((t) => t.name));

    // add selected theme to body
    document.body.classList.add(...classNames.filter((c) => c !== ""));

    // no need to clean anything here as unmounting this component means closing the app
  }, [myTheme, setTheme]);

  useEffect(() => {
    setAnimationStarted(true);
  }, [location.pathname]);

  const navPortal = useCallback(
    (node) => {
      if (node !== null) {
        setNavPortalTarget(node);
      }
    },
    [setNavPortalTarget]
  );

  // Retrieves the value associated with the key "show_datasheet_v2_ui" from the browser's localStorage and client features
  const showDatasheetNewUi = clientFeatures?.showDataSourcesV2 ? true : false;

  const isNewSidebar =
    clientFeatures?.enableSidebarV3 ||
    localStorage.getItem("new-sidebar") === "true";

  useEffect(() => {
    if (isNewSidebar) {
      document.body.classList.add("new-sidebar");
    } else {
      document.body.classList.remove("new-sidebar");
    }
  }, [isNewSidebar]);

  return (
    // added className="ever-container" for playwright test to verify login
    <div
      className={twMerge(
        "ever-container h-full flex flex-col",
        isNewSidebar ? "bg-ever-accent" : ""
      )}
      data-testid="login-indicator"
    >
      <RedirectTo />
      {animationStarted ? (
        <EverNavPortal target={navPortalLocation}>
          {/* Emptying the portal children to prevent animated route issues */}
          <div></div>
        </EverNavPortal>
      ) : null}

      <div className="flex flex-col h-screen">
        <LoggedInAsBanner />
        <NonChromeBanner />
        <DeploymentFinishMessage />
        <SupportMembershipTimeLeftBanner />
        <RouteSwitchAnalyticsTrack />
        {isNewSidebar ? <HeaderBar /> : null}
        <div
          className={twMerge(
            "flex flex-auto grow overflow-y-auto",
            isNewSidebar && "pr-1.5 max-h-[calc(100vh-44px)]"
          )}
        >
          {isNewSidebar ? <SidebarV3 /> : <Sidebar />}
          <div className="flex rounded-t-xl bg-ever-base grow relative z-0">
            <EverContent>
              <EverNavBar>
                <EverHeader animationStarted={animationStarted} />
                {animationStarted ? null : <div ref={navPortal}></div>}
              </EverNavBar>
              <EverBody
                pathName={location.pathname}
                isNewSidebar={isNewSidebar}
              >
                <React.Suspense
                  fallback={
                    <div className="fixed top-0 left-0 w-full h-full">
                      <EverLoader
                        wrapperClassName="backdrop-blur-none"
                        tip="Loading..."
                        indicatorType="logo"
                        spinning
                      />
                    </div>
                  }
                >
                  {transitions((styles, item) => (
                    <animated.div style={styles} key={item.key}>
                      <RouteList
                        location={item}
                        showDatasheetNewUi={showDatasheetNewUi}
                        canUserManageAdmins={canUserManageAdmins}
                        sendMessage={sendMessage}
                        currentDataBookVersion={currentDataBookVersion}
                      />
                    </animated.div>
                  ))}
                </React.Suspense>
              </EverBody>
            </EverContent>
            <EverRightContent
              animationStarted={animationStarted}
              isNewSidebar={isNewSidebar}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * This is the main route list component for the application.
 * This is the router v5 structure but it is converted to v6 with the help of createRoutesFromElements.
 * @property {object} location - The location object from react-router-dom.
 * @property {object} clientFeatures - The object that dictates the features available to the client. Properties can be changed using admin-ui
 * @property {boolean} canUserManageAdmins - The boolean value that determines if the user can manage admins.
 * @returns
 */
function RouteList({ location, clientFeatures }) {
  return (
    <Routes location={location}>
      {/* Authentication Routes */}
      <Route path="login" element={<Navigate to="/" />} />

      {/* CPQ */}
      <Route path="/" element={<Navigate to="/cpq/quotes" replace />} />
      <Route path="cpq" element={<Navigate to="/cpq/quotes" replace />} />
      <Route
        path="/cpq/quotes"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq">
              <QuotesList />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/quotes/:quoteId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq">
              <BuildQuote />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Quote Forms */}
      {/* <Route
        path="/cpq/settings/quote-forms"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="quote-forms">
              <FormBuilderList />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/settings/quote-forms/:quoteFormId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="quote-form">
              <FormBuilder />
            </EverErrorBoundary>
          </AuthRoute>
        }
      /> */}
      {/* Quote Rules */}
      {/* <Route
        path="/cpq/settings/quote-rules"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="quote-rules">
              <QuoteRuleList />
            </EverErrorBoundary>
          </AuthRoute>
        }
      /> */}
      <Route
        path="/cpq/settings/quote-rules/:quoteRuleId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="quote-rule">
              <QuoteRule />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/pricebook"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq">
              <PriceBook />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/product-catalog"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq/product-catalog">
              <ListProducts />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/product-catalog/:productId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq/product-catalog-view">
              <ViewProduct />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/pricebook/:priceBookId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="cpq">
              <PriceBookDetails />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Databook Routes */}
      <Route
        path="/cpq/datasheet/*"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="datasheet">
              <Datasheet />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/cpq/databook"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="databook">
              <Databook />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/cpq/databook/:databookId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="databook/:databookId">
              <DatabookDetails />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Objects Routes */}
      <Route
        path="/cpq/objects"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="objects">
              <DataSources />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Admin Settings Routes */}
      <Route
        path="/cpq/settings"
        element={
          <AuthRoute
            permissionId={[
              RBAC_ROLES.MANAGE_DATASETTINGS,
              RBAC_ROLES.MANAGE_ALL_ADMINS,
            ]}
          >
            <EverErrorBoundary key="settings">
              <CpqSettings />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Activity Logs Routes */}
      <Route
        path="/cpq/settings/activity-logs"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="activity-logs">
              <ActivityLogs />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Legacy upload */}
      <Route
        path="/cpq/settings/manage-data"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="manage-data">
              <ManageData />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/cpq/settings/manage-data/v1"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="manage-data/v1">
              <BulkUploadData />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Data Connectors Routes */}
      <Route
        path="/cpq/settings/connectors"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="settings/connectors">
              <IntegrationsAndVariables />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Data Sync Routes */}
      <Route
        path="/cpq/settings/commissions-and-data-sync"
        element={
          <AuthRoute permissionId={[RBAC_ROLES.MANAGE_DATASETTINGS]}>
            <EverErrorBoundary key="settings/commissions-and-data-sync">
              <DataSync />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Connectors */}
      {/* settings/connectors */}
      <Route
        path={"/cpq/settings/connectors/new"}
        element={
          <EverErrorBoundary key="settings/connectors">
            <Connectors />
          </EverErrorBoundary>
        }
      />

      {/* Users Routes */}
      <Route
        path="/cpq/users"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_USERS}>
            <EverErrorBoundary key="users">
              <EmployeeConfig />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* User Groups Routes */}
      <Route
        path="/cpq/groups"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_USERGROUPS}>
            <EverErrorBoundary key="groups">
              <Groups />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/cpq/everAI"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_AGENT_WORKBENCH}
            extraPermission={clientFeatures?.enableEverai}
          >
            <AgentExecutor />
          </AuthRoute>
        }
      />
      {/* DEFAULT ROUTE : NOT FOUND
        this should always be at the end */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export const NotFound = () => {
  return (
    <PageNotFound
      imgSrc={tvOnFire404}
      title="Page not found"
      subTitle="Sorry, the page you visited does not exist."
      showButton
      buttonLabel={"Back Home"}
      redirectUrl={CPQ_URLS.QUOTES}
    />
  );
};
