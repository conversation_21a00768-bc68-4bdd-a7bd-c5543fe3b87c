import { animated, useTransition } from "@react-spring/web";
import React, { useState, useCallback, lazy, useEffect } from "react";
import {
  Routes,
  Route,
  Navigate,
  useLocation,
  createBrowserRouter,
  createRoutesFromElements,
  RouterProvider,
} from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { COMMISSION_TYPE, DASHBOARD_URLS, RBAC_ROLES } from "~/Enums";
import { useSupabaseBroadcast } from "~/everstage-supabase";
import {
  myClientAtom,
  navPortalAtom,
  currentTheme,
} from "~/GlobalStores/atoms";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import EverNavPortal from "~/v2/components/EverNavPortal";
import DashboardRedirect from "~/v2/features/dashboard23/DashboardRedirect";
import HomePage from "~/v2/HomePage";
import { emptyNoDashboardState, tvOnFire404 } from "~/v2/images";

import {
  LoggedInAsBanner,
  DeploymentFinishMessage,
  NonChromeBanner,
  RedirectTo,
  PageNotFound,
  EverLoader,
  SupportMembershipTimeLeftBanner,
} from "./components";
import { HeaderBar } from "./components/new-sidebar/HeaderBar";
import { SidebarV3 } from "./components/new-sidebar/SideMenu";
import {
  AuthRoute,
  EverErrorBoundary,
  EverBody,
  EverContent,
  EverHeader,
  EverNavBar,
  EverRightContent,
} from "./components/router";
import RouteSwitchAnalyticsTrack from "./components/RouteSwitchAnalyticsTrack";
import { Sidebar } from "./components/sidebar";
import HrisNotification from "./features/admin-settings/hris-integration/HrisNotification";
import { AuthApprovalsRoute } from "./features/approvals";
import GlobalSearch from "./features/global-search";
import { themerOptions } from "./themes";

const CommissionAdjustmentApprovals = React.lazy(() =>
  import("./features/approvals/commission-adjustments")
);
const CrystalAdminAuthRoute = React.lazy(() =>
  import("./features/crystal").then((module) => ({
    default: module.CrystalAdminAuthRoute,
  }))
);
const CrystalPayeePage = React.lazy(() =>
  import("./features/crystal/crystal-components/CrystalPayeePage").then(
    (module) => ({
      default: module.CrystalPayeePage,
    })
  )
);
const CrystalAdminViewSetup = React.lazy(() =>
  import("./features/crystal/crystal-components/admin").then((module) => ({
    default: module.CrystalAdminViewSetup,
  }))
);
const ErrorPage = React.lazy(() =>
  import("./features/crystal").then((module) => ({
    default: module.ErrorPage,
  }))
);
const QuotaSummary = React.lazy(() => import("./features/quota/quota-summary"));
const ManageData = React.lazy(() => import("./features/manage-data"));
const ActivityLogs = React.lazy(() => import("./features/activity-logs"));
const AuthCrystalRoute = lazy(() => import("~/v2/features/crystal"));
const AdminSettings = lazy(() => import("~/v2/features/admin-settings"));
const Approvals = lazy(() =>
  import("~/v2/features/approvals/approval-requests")
);
const Workflows = React.lazy(() => import("~/v2/features/workflows"));
const CustomCalendar = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-calendar")
);

const DashboardDetailView = React.lazy(() =>
  import("~/v2/features/dashboard23/dashboard-detail-view")
);
const DashboardListView = React.lazy(() =>
  import("~/v2/features/dashboard23/dashboard-list-view")
);

const IncentivePlanSummary = React.lazy(() =>
  import("~/v2/features/incentive-plan-summary")
);

const DataSources = React.lazy(() => import("~/v2/features/data-sources"));

const IncentivePlanSummaryV2 = React.lazy(() =>
  import("~/v2/features/incentive-plan-summary-v2-modified")
);

const Draws = lazy(() => import("~/v2/features/draws"));
const EmployeeConfig = React.lazy(() => import("./features/users"));

const Teams = lazy(() => import("~/v2/features/teams"));
const Queries = lazy(() => import("~/v2/features/queries"));
const Statements = lazy(() => import("~/v2/features/statements"));
const CommissionsView = lazy(() => import("~/v2/features/commissions"));
const NotificationPreference = React.lazy(() =>
  import("~/v2/features/notification-preference")
);
const AccountSettings = React.lazy(() =>
  import("~/v2/features/admin-settings/basic-settings")
);
const ManualAdjustments = React.lazy(() =>
  import("~/v2/features/admin-settings/manual-adjustment")
);
const ManualAdjustmentsV2 = React.lazy(() =>
  import("~/v2/features/admin-settings/new-manual-adjustment")
);
const DataSync = React.lazy(() =>
  import("~/v2/features/admin-settings/data-sync-settings")
);
const AuditTrail = React.lazy(() =>
  import("~/v2/features/admin-settings/audit-trail")
);
const HRISIntegration = React.lazy(() =>
  import("~/v2/features/admin-settings/hris-integration")
);
const HRISReviewUpdates = React.lazy(() =>
  import("~/v2/features/admin-settings/hris-integration/ReviewUpdate")
);

const HRISProcessedRecords = React.lazy(() =>
  import(
    "~/v2/features/admin-settings/hris-integration/ProcesssedIgnoredRecords"
  )
);

const ContractManagement = React.lazy(() =>
  import("~/v2/features/contract-management")
);

const ContractDetails = React.lazy(() =>
  import("~/v2/features/contract-management/ContractDetails")
);

const Notifications = React.lazy(() =>
  import("~/v2/features/admin-settings/notifications")
);
const IntegrationsAndVariables = React.lazy(() =>
  import("~/v2/features/admin-settings/integrations-and-variables")
);
const ContractSettings = React.lazy(() =>
  import("~/v2/features/admin-settings/ContractSettings")
);
const UserRoles = React.lazy(() =>
  import("~/v2/features/admin-settings/user-roles")
);
const CustomizeStatements = React.lazy(() =>
  import("~/v2/features/admin-settings/customize-statements")
);
const CustomFields = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-fields")
);
const QuerySetting = React.lazy(() =>
  import("~/v2/features/admin-settings/queries-setting")
);
const Metrics = React.lazy(() => import("~/v2/features/metrics"));
const MetricsExplorer = React.lazy(() =>
  import("~/v2/features/metrics/metric-explorer")
);
const MetricsDefinition = React.lazy(() =>
  import("~/v2/features/metrics/metric-definition")
);
const RealTimeTransformation = React.lazy(() =>
  import("~/v2/features/realtime-transformation")
);
const Groups = React.lazy(() => import("~/v2/features/groups"));

const Databook = React.lazy(() => import("~/v2/features/databook-summary"));
const Datasheet = React.lazy(() => import("~/v2/features/datasheet"));
const DatabookDetails = lazy(() =>
  import("~/v2/features/databook-summary/databook")
);

const ProfileSettings = lazy(() => import("~/v2/features/profile-settings"));
const Connectors = lazy(() =>
  import(
    "~/v2/features/admin-settings/integrations-and-variables/connection-hub/Connectors"
  )
);
const ReportEnrichment = React.lazy(() =>
  import("~/v2/features/admin-settings/report-enrichment")
);
const CustomTerminology = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-terminology")
);
const CustomTheme = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-theme")
);
const BulkUploadData = React.lazy(() =>
  import("~/v2/features/admin-settings/bulk-upload-data-v1")
);
const CustomTriggers = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-triggers")
);
const CustomTrigger = React.lazy(() =>
  import("~/v2/features/admin-settings/custom-triggers/custom-trigger")
);
const WorkflowBuilderSummary = React.lazy(() =>
  import("~/v2/features/workflow-builder-summary")
);
const WorkflowBuilder = React.lazy(() =>
  import("~/v2/features/workflow-builder")
);
const WorkflowHistory = React.lazy(() =>
  import("~/v2/features/workflow-history")
);
const AgentExecutor = React.lazy(() => import("~/v2/features/agent-executor"));

const TerritoryPlanListing = React.lazy(() =>
  import("~/v2/features/tqm/territory-plans-listing")
);
const TerritoryPlanDetails = React.lazy(() =>
  import("~/v2/features/tqm/territory-plan-details")
);

const EmptyDashboardState = () => (
  <div className="w-full h-full mb-[100px] flex items-center justify-center">
    <img src={emptyNoDashboardState} alt="empty-quota-attainment-tracker" />
  </div>
);

const router = createBrowserRouter(
  // createRoutesFromElements creates the v6 data-api routes from jsx. more: https://reactrouter.com/en/main/utils/create-routes-from-elements
  createRoutesFromElements(<Route path="*" element={<Layout />} />)
);

export function Router() {
  return <RouterProvider router={router} />;
}

const broadcastConfig = { self: true };
const event = ["databook-version-change"];

/**
 * This is the main layout component for the application.
 * This has breadcrumbs, sidebar, header, content and footer.
 */
export function Layout() {
  const location = useLocation();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const setNavPortalTarget = useSetRecoilState(navPortalAtom);
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const { canUserManageAdmins } = useEmployeeStore();
  const [animationStarted, setAnimationStarted] = useState(false);
  const [currentDataBookVersion, setDatabookCurrentVersion] = useState(null);

  const [message, makeBroadcast] = useSupabaseBroadcast(event, {
    channelPrefix: "databook-version-change-broadcast",
    broadcastConfig,
  });

  function sendMessage(newVersion) {
    makeBroadcast({
      type: "broadcast",
      event: "databook-version-change",
      payload: { data: "version-changed", newVersion: newVersion },
    });
  }
  const myTheme = useRecoilValue(currentTheme);
  const setTheme = useSetRecoilState(currentTheme);

  const transitions = useTransition(location, {
    from: { opacity: 0, display: "none", height: "100%" },
    enter: { opacity: 1, display: "block", width: "100%", height: "100%" },
    leave: { opacity: 0, display: "none", width: "0%", height: "100%" },
    config: {
      duration: 500,
    },
    onStart: () => {
      setAnimationStarted(true);
    },
    onRest: () => {
      setAnimationStarted(false);
    },
  });

  useEffect(() => {
    if (
      clientFeatures &&
      clientFeatures.showDataSourcesV2 !== undefined &&
      currentDataBookVersion === null
    ) {
      setDatabookCurrentVersion(clientFeatures.showDataSourcesV2 ? "v2" : "v1");
    }
  }, [clientFeatures]);

  useEffect(() => {
    if (message?.payload?.newVersion) {
      setDatabookCurrentVersion(message.payload.newVersion);
    }
  }, [message]);

  useEffect(() => {
    // get theme from localstorage and set in recoil store
    setTheme(localStorage.getItem("theme") || myTheme || "");

    // set theme in body, along with default bg and text colors
    const classNames = `${localStorage.getItem(
      "theme"
    )} bg-ever-base text-base text-ever-base-content`.split(" ");

    // remove all themes from body
    document.body.classList.remove(...themerOptions.themes.map((t) => t.name));

    // add selected theme to body
    document.body.classList.add(...classNames.filter((c) => c !== ""));

    // no need to clean anything here as unmounting this component means closing the app
  }, [myTheme, setTheme]);

  useEffect(() => {
    setAnimationStarted(true);
  }, [location.pathname]);

  const navPortal = useCallback(
    (node) => {
      if (node !== null) {
        setNavPortalTarget(node);
      }
    },
    [setNavPortalTarget]
  );

  // const localStorageValue = localStorage.getItem("commission_plan_version");
  // const isCommissionCanvas =
  //   localStorageValue === "v2"
  //     ? true
  //     : localStorageValue === "v1"
  //     ? false
  //     : (clientFeatures?.commissionPlanVersion ?? "v1") === "v2";
  const isCommissionCanvas =
    (clientFeatures?.commissionPlanVersion ?? "v2") === "v2";

  const isCommissionAdjustmentV2 =
    clientFeatures?.isCommissionAdjustmentV2Enabled ?? false;

  // Retrieves the value associated with the key "show_datasheet_v2_ui" from the browser's localStorage and client features
  const showDatasheetNewUi = clientFeatures?.showDataSourcesV2 ? true : false;

  const isNewSidebar =
    clientFeatures?.enableSidebarV3 ||
    localStorage.getItem("new-sidebar") === "true";

  useEffect(() => {
    if (isNewSidebar) {
      document.body.classList.add("new-sidebar");
    } else {
      document.body.classList.remove("new-sidebar");
    }
  }, [isNewSidebar]);

  return (
    // added className="ever-container" for playwright test to verify login
    <div
      className={twMerge(
        "ever-container h-full flex flex-col",
        isNewSidebar ? "bg-ever-accent" : ""
      )}
      data-testid="login-indicator"
    >
      <GlobalSearch />
      <RedirectTo />
      {animationStarted ? (
        <EverNavPortal target={navPortalLocation}>
          {/* Emptying the portal children to prevent animated route issues */}
          <div></div>
        </EverNavPortal>
      ) : null}
      <div className="flex flex-col h-screen">
        <LoggedInAsBanner />
        <NonChromeBanner />
        <DeploymentFinishMessage />
        <SupportMembershipTimeLeftBanner />
        <HrisNotification />
        <RouteSwitchAnalyticsTrack />
        {isNewSidebar ? <HeaderBar /> : null}
        <div
          className={twMerge(
            "flex flex-auto grow overflow-y-auto",
            isNewSidebar && "pr-1.5 max-h-[calc(100vh-44px)]"
          )}
        >
          {isNewSidebar ? <SidebarV3 /> : <Sidebar />}
          <div className="flex rounded-t-xl bg-ever-base grow relative z-0">
            <EverContent>
              <EverNavBar>
                <EverHeader animationStarted={animationStarted} />
                {!animationStarted ? <div ref={navPortal}></div> : null}
              </EverNavBar>
              <EverBody
                pathName={location.pathname}
                isCommissionCanvas={isCommissionCanvas}
                isNewSidebar={isNewSidebar}
              >
                <React.Suspense
                  fallback={
                    <div className="fixed top-0 left-0 w-full h-full">
                      <EverLoader
                        wrapperClassName="backdrop-blur-none"
                        tip="Loading..."
                        indicatorType="logo"
                        spinning
                      />
                    </div>
                  }
                >
                  {transitions((styles, item) => (
                    <animated.div style={styles} key={item.key}>
                      <RouteList
                        location={item}
                        clientFeatures={clientFeatures}
                        isCommissionCanvas={isCommissionCanvas}
                        isCommissionAdjustmentV2={isCommissionAdjustmentV2}
                        showDatasheetNewUi={showDatasheetNewUi}
                        canUserManageAdmins={canUserManageAdmins}
                        sendMessage={sendMessage}
                        currentDataBookVersion={currentDataBookVersion}
                      />
                    </animated.div>
                  ))}
                </React.Suspense>
              </EverBody>
            </EverContent>
            <EverRightContent
              animationStarted={animationStarted}
              isNewSidebar={isNewSidebar}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * This is the main route list component for the application.
 * This is the router v5 structure but it is converted to v6 with the help of createRoutesFromElements.
 * @property {object} location - The location object from react-router-dom.
 * @property {object} clientFeatures - The object that dictates the features available to the client. Properties can be changed using admin-ui
 * @property {boolean} isCommissionCanvas - The boolean value that determines if the commission canvas is v1 or v2. Likely to be removed once v1 is deprecated.
 *  @property {boolean} isCommissionAdjustmentV2 - The boolean value that determines if the commission adjustment is v1 or v2.
 * @property {boolean} canUserManageAdmins - The boolean value that determines if the user can manage admins.
 * @returns
 */
function RouteList({
  location,
  clientFeatures,
  isCommissionCanvas,
  isCommissionAdjustmentV2,
  canUserManageAdmins,
  sendMessage,
  currentDataBookVersion,
}) {
  return (
    <Routes location={location}>
      {/* Authentication Routes */}
      <Route path="login" element={<Navigate to="/" />} />
      <Route
        path="/"
        element={
          <DashboardRedirect>
            <HomePage />
          </DashboardRedirect>
        }
      />
      {/* Dashboard Routes */}
      <Route
        path={DASHBOARD_URLS.DASHBOARDS}
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_DASHBOARD}
            placeholderComponent={<EmptyDashboardState />}
          >
            <EverErrorBoundary key="dashboard">
              <DashboardRedirect>
                <DashboardListView />
              </DashboardRedirect>
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path={DASHBOARD_URLS.DASHBOARD}
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_DASHBOARD}
            placeholderComponent={<EmptyDashboardState />}
          >
            <EverErrorBoundary key="dashboard">
              <DashboardRedirect>
                <DashboardDetailView />
              </DashboardRedirect>
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Databook Routes */}
      <Route
        path="/datasheet/*"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="datasheet">
              <GetDatabookRoutes
                clientFeatures={clientFeatures}
                path="/datasheet"
                sendMessage={sendMessage}
                currentDataBookVersion={currentDataBookVersion}
              />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/databook"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="databook">
              <GetDatabookRoutes
                clientFeatures={clientFeatures}
                path="/databook"
                sendMessage={sendMessage}
                currentDataBookVersion={currentDataBookVersion}
              />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/databook/:databookId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DATABOOK}>
            <EverErrorBoundary key="databook/:databookId">
              <GetDatabookRoutes
                clientFeatures={clientFeatures}
                path="/databook/:databookId"
                sendMessage={sendMessage}
                currentDataBookVersion={currentDataBookVersion}
              />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      <Route
        path="/objects"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="objects">
              <DataSources />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Commissions Routes */}
      <Route
        path="/commissions"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_PAYOUTS}>
            <EverErrorBoundary key="commissions">
              <CommissionsView />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/plans"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_COMMISSIONPLAN}>
            <EverErrorBoundary key="plans">
              {isCommissionCanvas ? (
                <IncentivePlanSummaryV2
                  planType={COMMISSION_TYPE.COMMISSION_PLAN}
                />
              ) : (
                <IncentivePlanSummary />
              )}
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/forecasts"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_COMMISSIONPLAN}
            extraPermission={clientFeatures?.showForecast}
          >
            <EverErrorBoundary key="forecasts">
              <IncentivePlanSummaryV2
                planType={COMMISSION_TYPE.FORECAST_PLAN}
              />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Statements Routes */}
      {["/statements", "/statements/:profileData"].map((path) => (
        <Route
          key={path}
          path={path}
          element={
            <AuthRoute
              permissionId={[
                RBAC_ROLES.VIEW_PAYOUTS,
                RBAC_ROLES.VIEW_STATEMENTS,
              ]}
            >
              <EverErrorBoundary key="statements">
                <Statements />
              </EverErrorBoundary>
            </AuthRoute>
          }
        />
      ))}
      {/* Queries Routes */}
      <Route
        path="/queries/:ticketFilter/:drsId?"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_QUERIES}>
            <EverErrorBoundary key="queries">
              <Queries />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Crystal Routes */}
      <Route path="/crystal">
        <Route
          index
          element={
            <AuthCrystalRoute permissionId={RBAC_ROLES.MANAGE_CRYSTAL} />
          }
        />
        <Route path="page-not-found" element={<ErrorPage />} />
        <Route
          path="preview/:simulatorId/:forUser"
          element={
            <CrystalAdminAuthRoute permissionId={RBAC_ROLES.MANAGE_CRYSTAL}>
              <CrystalPayeePage />
            </CrystalAdminAuthRoute>
          }
        />
        <Route
          path=":simulatorId"
          element={
            <CrystalAdminAuthRoute permissionId={RBAC_ROLES.MANAGE_CRYSTAL}>
              <CrystalAdminViewSetup />
            </CrystalAdminAuthRoute>
          }
        />
      </Route>
      {/* Quotas Routes */}
      <Route
        path="/quotas"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_QUOTAS}
            extraPermission={clientFeatures?.showQuota}
          >
            <EverErrorBoundary key="quotas">
              <QuotaSummary />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Draws Routes */}
      <Route
        path="/draws"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_DRAWS}>
            <EverErrorBoundary key="draws">
              <Draws />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Approvals Routes */}
      <Route
        path="/approvals/payouts"
        element={
          <AuthApprovalsRoute
            clientFeatures={clientFeatures}
            approvalType="payout"
          >
            <EverErrorBoundary key="approvals">
              <Approvals />
            </EverErrorBoundary>
          </AuthApprovalsRoute>
        }
      />
      <Route
        path="/approvals/commission-adjustments"
        element={
          <AuthApprovalsRoute
            clientFeatures={clientFeatures}
            approvalType="commissionAdjustment"
          >
            <EverErrorBoundary key="approvals">
              <CommissionAdjustmentApprovals />
            </EverErrorBoundary>
          </AuthApprovalsRoute>
        }
      />
      {/* Users Routes */}
      <Route
        path="/users"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_USERS}>
            <EverErrorBoundary key="users">
              <EmployeeConfig />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* User Groups Routes */}
      <Route
        path="/groups"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_USERGROUPS}>
            <EverErrorBoundary key="groups">
              <Groups />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Teams Routes */}
      <Route
        path="/teams"
        element={
          <AuthRoute permissionId={RBAC_ROLES.VIEW_TEAMS}>
            <EverErrorBoundary key="teams">
              <Teams />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Contracts Routes */}
      <Route
        path="/contracts"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONTRACTS}>
            <EverErrorBoundary key="contracts">
              <ContractManagement />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/contracts/:contractId"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONTRACTS}>
            <EverErrorBoundary key="contracts/contractId">
              <ContractDetails />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Notification Preferences Routes */}
      <Route
        path="/notification-preference"
        element={
          <EverErrorBoundary key="notification-preference">
            <NotificationPreference />
          </EverErrorBoundary>
        }
      />
      {/* Admin Settings Routes */}
      <Route
        path="/settings"
        element={
          <AuthRoute
            permissionId={[
              RBAC_ROLES.MANAGE_CONFIG,
              RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
              RBAC_ROLES.MANAGE_CONTRACTS,
              RBAC_ROLES.MANAGE_REPORTENRICH,
              RBAC_ROLES.MANAGE_ROLES,
              RBAC_ROLES.MANAGE_USERCUSTOMFIELD,
              RBAC_ROLES.MANAGE_DATASETTINGS,
              RBAC_ROLES.MANAGE_DRAWS,
            ]}
          >
            <EverErrorBoundary key="settings">
              <AdminSettings />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Manage Data Routes */}
      <Route
        path="/settings/manage-data"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="manage-data">
              <ManageData />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />

      {/* Theme maker */}
      <Route
        path="/settings/custom-theme"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}
            extraPermission={clientFeatures?.enableCustomTheme}
          >
            <EverErrorBoundary key="custom-theme">
              <CustomTheme />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/manage-data/v1"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="manage-data/v1">
              <BulkUploadData />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Report Enrichment Routes */}
      <Route
        path="/settings/report-enrichment"
        element={
          <AuthRoute permissionId={[RBAC_ROLES.MANAGE_REPORTENRICH]}>
            <EverErrorBoundary key="settings/report-enrichment">
              <ReportEnrichment />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Activity Logs Routes */}
      <Route
        path="/settings/activity-logs"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="activity-logs">
              <ActivityLogs />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Workflow Settings Routes */}
      <Route
        path="/settings/workflows"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_APPROVAL_WORKFLOWS}
            extraPermission={clientFeatures?.showApprovalFeature}
          >
            <EverErrorBoundary key="settings/workflows">
              <Workflows />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Custom Triggers Routes */}
      <Route
        path="/settings/workflow-builders/custom-triggers"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="custom-triggers">
              <CustomTriggers />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/workflow-builders/custom-triggers/:triggerId"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="custom-trigger">
              <CustomTrigger />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Custom Calendar Routes */}
      <Route
        path="/settings/custom-calendar"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ALL_ADMINS}
            extraPermission={clientFeatures?.customCalendar}
          >
            <EverErrorBoundary key="settings/custom-calendar">
              <CustomCalendar />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Workflow Builders Routes */}
      <Route
        path="/settings/workflow-builders"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="workflow-builder-summary">
              <WorkflowBuilderSummary />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Below two routes are History Routes inside Workflow Builders  */}
      <Route
        path="/settings/workflow-builders/workflow-history"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="workflow-builder-history">
              <WorkflowHistory />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/workflow-builders/workflow-history/:workflowId"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="workflow-builder-history-individual">
              <WorkflowHistory />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/workflow-builders/:workflowId"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.enableCustomWorkflows}
          >
            <EverErrorBoundary key="workflow-builder">
              <WorkflowBuilder />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Account Settings Routes */}
      <Route
        path="/settings/basic"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="/settings/basic">
              <AccountSettings />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Manual Adjustments Routes */}
      <Route
        path="/settings/adjustments"
        element={
          <AuthRoute
            permissionId={[
              RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
              RBAC_ROLES.MANAGE_DRAWS,
            ]}
          >
            <EverErrorBoundary key="settings/adjustments">
              {isCommissionAdjustmentV2 ? (
                <ManualAdjustmentsV2 />
              ) : (
                <ManualAdjustments />
              )}
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Data Sync Routes */}
      <Route
        path="/settings/commissions-and-data-sync"
        element={
          <AuthRoute permissionId={[RBAC_ROLES.MANAGE_DATASETTINGS]}>
            <EverErrorBoundary key="settings/commissions-and-data-sync">
              <DataSync />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Audit Logs Routes */}
      <Route
        path="/settings/audit-logs"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <EverErrorBoundary key="settings/audit-logs">
              <AuditTrail />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Notifications Routes */}
      <Route
        path="/settings/notifications"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_ACCOUNTNOTIFICATIONS}>
            <EverErrorBoundary key="settings/notifications">
              <Notifications />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Data Connectors Routes */}
      <Route
        path="/settings/connectors"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}
            extraPermission={clientFeatures?.showSalesforceIntegration}
          >
            <EverErrorBoundary key="settings/connectors">
              <IntegrationsAndVariables />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Connectors */}
      {/* settings/connectors */}
      <Route
        path={"/settings/connectors/new"}
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}
            extraPermission={clientFeatures?.showSalesforceIntegration}
          >
            <EverErrorBoundary key="settings/connectors">
              <Connectors />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* HRIS Integration Routes */}
      <Route
        path="/settings/hris-integration"
        element={
          <AuthRoute
            permissionId={[RBAC_ROLES.MANAGE_DATASETTINGS]}
            extraPermission={clientFeatures?.enableHrisIntegration}
          >
            <EverErrorBoundary key="settings/hris-integration">
              <HRISIntegration />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/hris-integration/review-updates"
        element={
          <AuthRoute permissionId={[RBAC_ROLES.MANAGE_DATASETTINGS]}>
            <EverErrorBoundary key="/settings/hris-integration/review-updates">
              <HRISReviewUpdates />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/settings/hris-integration/processed-ignored-records"
        element={
          <AuthRoute permissionId={[RBAC_ROLES.MANAGE_DATASETTINGS]}>
            <EverErrorBoundary key="/settings/hris-integration/processed-ignored-records">
              <HRISProcessedRecords />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Contract Settings Routes */}
      <Route
        path="/settings/contracts"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONTRACTS}>
            <EverErrorBoundary key="settings/contracts">
              <ContractSettings />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* User Roles Routes */}
      <Route
        path="/settings/user-roles"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_ROLES}
            extraPermission={clientFeatures?.showRoles}
          >
            <EverErrorBoundary key="settings/user-roles">
              <UserRoles />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Customize Statements Routes */}
      <Route
        path="/settings/statement-settings"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="settings/statement-settings">
              <CustomizeStatements />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Query Setting Routes */}
      <Route
        path="/settings/queries"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="settings/queries">
              <QuerySetting />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Custom Fields Routes */}
      <Route
        path="/settings/custom-fields"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_USERCUSTOMFIELD}>
            <EverErrorBoundary key="settings/custom-fields">
              <CustomFields />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Custom Terminology Routes */}
      <Route
        path="/settings/custom-terminology"
        element={
          <AuthRoute permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <EverErrorBoundary key="settings/custom-terminology">
              <CustomTerminology />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* TQM Routes */}
      <Route
        path="/planning"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_TERRITORY_PLANS}
            extraPermission={clientFeatures.showTerritoryPlan}
          >
            <EverErrorBoundary key="planning">
              <TerritoryPlanListing />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/planning/:planId"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.VIEW_TERRITORY_PLANS}
            extraPermission={clientFeatures.showTerritoryPlan}
          >
            <EverErrorBoundary key="plan-embed">
              <TerritoryPlanDetails />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* Profile Settings Routes */}
      <Route
        path={"/profile-settings"}
        element={
          <EverErrorBoundary key="profile-settings">
            <ProfileSettings />
          </EverErrorBoundary>
        }
      />
      {/* Metrics Routes */}
      <Route
        path="/explorer"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_USERS}
            extraPermission={clientFeatures.showMetrics && canUserManageAdmins}
          >
            <EverErrorBoundary key="metrics-explorer">
              <MetricsExplorer />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/metrics"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_USERS}
            extraPermission={clientFeatures.showMetrics && canUserManageAdmins}
          >
            <EverErrorBoundary key="metrics">
              <Metrics />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/metrics/definition"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_USERS}
            extraPermission={clientFeatures.showMetrics && canUserManageAdmins}
          >
            <EverErrorBoundary key="metrics-definition">
              <MetricsDefinition />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      <Route
        path="/everAI"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_AGENT_WORKBENCH}
            extraPermission={clientFeatures?.enableEverai}
          >
            <AgentExecutor />
          </AuthRoute>
        }
      />
      <Route
        path="/realtime-transformation"
        element={
          <AuthRoute
            permissionId={RBAC_ROLES.MANAGE_USERS}
            extraPermission={clientFeatures.showMetrics && canUserManageAdmins}
          >
            <EverErrorBoundary key="realtime-transformation">
              <RealTimeTransformation />
            </EverErrorBoundary>
          </AuthRoute>
        }
      />
      {/* DEFAULT ROUTE : NOT FOUND
        this should always be at the end */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export const NotFound = () => {
  const [loading, setLoading] = useState(true);

  const conditions = [{ keyword: "everAI", delay: 3000 }];

  useEffect(() => {
    let delay = null;
    conditions.forEach((condition) => {
      if (window.location.href.includes(condition.keyword)) {
        delay = condition.delay;
      }
    });
    if (!delay) {
      setLoading(false);
    } else {
      const timer = setTimeout(() => {
        setLoading(false);
      }, delay);
      return () => clearTimeout(timer);
    }
  }, []);

  if (loading) {
    return <EverLoader />;
  }

  return (
    <PageNotFound
      imgSrc={tvOnFire404}
      title="Page not found"
      subTitle="Sorry, the page you visited does not exist."
      showButton
      buttonLabel={"Back Home"}
      redirectUrl={DASHBOARD_URLS.DASHBOARDS}
    />
  );
};

function GetDatabookRoutes({
  clientFeatures,
  path,
  sendMessage,
  currentDataBookVersion,
}) {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clientFeatures && clientFeatures.showDataSourcesV2 !== undefined) {
      setLoading(false);
    }
  }, [clientFeatures]);

  if (loading) {
    return (
      <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center">
        <EverLoader
          wrapperClassName="backdrop-blur-none"
          tip="Loading..."
          indicatorType="logo"
          spinning
        />
      </div>
    );
  }

  if (path === "/datasheet" && clientFeatures.showDataSourcesV2)
    return (
      <Datasheet
        showVersionChangeBanner={currentDataBookVersion !== "v2"}
        sendMessage={sendMessage}
      />
    );
  if (path === "/databook" && !clientFeatures.showDataSourcesV2)
    return (
      <Databook
        showVersionChangeBanner={currentDataBookVersion !== "v1"}
        sendMessage={sendMessage}
      />
    );
  if (path === "/databook/:databookId" && !clientFeatures.showDataSourcesV2)
    return (
      <DatabookDetails
        showVersionChangeBanner={currentDataBookVersion !== "v1"}
        sendMessage={sendMessage}
      />
    );
  return <NotFound />;
}
