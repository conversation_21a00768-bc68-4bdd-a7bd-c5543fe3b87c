import PropTypes from "prop-types";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";
import { parse } from "valibot";

import { EverPopover } from "./EverPopover";
import { EverTg } from "./EverTypography";
import { AvatarsSchema } from "./schemas";

const availableColors = [
  "bg-ever-avatar-1",
  "bg-ever-avatar-2",
  "bg-ever-avatar-3",
  "bg-ever-avatar-4",
  "bg-ever-avatar-5",
  "bg-ever-avatar-6",
  "bg-ever-avatar-7",
  "bg-ever-avatar-8",
  "bg-ever-avatar-9",
  "bg-ever-avatar-10",
];

// Define size values directly in the component
const sizeValues = {
  large: "h-8 w-8 p-3 text-xs leading-8",
  medium: "h-7 w-7 p-2 text-[11px] leading-7", // 28px (size-7 = 1.75rem = 28px)
  small: "h-6 w-6 p-1 text-[11px] leading-6",
};

// Custom Avatar component to replace antd's Avatar
const CustomAvatar = ({
  className,
  src,
  shape = "round",
  children,
  style,
  ...rest
}) => {
  return (
    <div
      className={twMerge(
        "flex items-center justify-center text-xs font-bold text-ever-base-content overflow-hidden leading-none border border-solid border-ever-base",
        shape === "round" ? "rounded-full" : "rounded-md",
        className
      )}
      style={{
        ...style,
      }}
      {...rest}
    >
      {src ? (
        <img src={src} alt="avatar" className="w-full h-full object-cover" />
      ) : (
        children
      )}
    </div>
  );
};

const RenderAvatar = ({
  index,
  user,
  showInPopover,
  size = "small",
  maxStyle,
  ...rest
}) => {
  const firstName = user.name
    ? user.name.split(" ")[0] || ""
    : user.firstName || "";

  const lastName = user.name
    ? user.name.split(" ")[1] || ""
    : user.lastName || "";

  const firstNameLetter = (firstName[0] ?? "").toLocaleUpperCase();
  const lastNameLetter = (lastName[0] ?? "").toLocaleUpperCase();

  const letters = `${firstNameLetter}${lastNameLetter}`;

  if (showInPopover) {
    return (
      <div className="flex items-center gap-2">
        <CustomAvatar
          key={`avatar-${index}`}
          className={twMerge(
            !user?.image
              ? user?.backgroundColor
                ? user?.backgroundColor
                : firstName
                ? availableColors[generateIndex(firstName)]
                : availableColors[0]
              : null,
            sizeValues[size],
            user?.className
          )}
          src={user?.image}
          style={{ ...maxStyle, ...user?.style }}
          shape={user?.shape || "round"}
          {...rest}
        >
          {user?.fallBack || letters}
        </CustomAvatar>
        <EverTg.Caption className="text-ever-base-content">{`${firstName} ${lastName}`}</EverTg.Caption>
      </div>
    );
  }

  return (
    <CustomAvatar
      key={`avatar-${index}`}
      className={twMerge(
        !user?.image
          ? user?.backgroundColor
            ? user?.backgroundColor
            : firstName
            ? availableColors[generateIndex(firstName)]
            : availableColors[0]
          : null,
        sizeValues[size],
        user?.className
      )}
      src={user?.image}
      style={{ ...maxStyle, ...user?.style }}
      shape={user?.shape || "round"}
      {...rest}
    >
      {user?.fallBack || letters}
    </CustomAvatar>
  );
};

/**
 * @typedef {Object} EverGroupAvatarProps
 * @property {string} [containerClassName] - Class name to be set on the container component
 * @property {import("./types/ever-group-avatar-types").AvatarsType} avatars - An array of objects representing each avatar.
 * @property {number} [groupMaxCount=3] - The maximum number of avatars to show before collapsing into a group.
 * @property {number} [limitInPopover] - The maximum number of avatars to show in the popover (kept for backward compatibility).
 * @property {string} [size="small"] - The size of the avatar group ("small", "medium", or "large").
 * @property {Object} [maxStyle={}] - Additional styles to apply to the avatar group.
 * @property {boolean} [forceShowPopover=false] - Whether to force showing the popover even when there's only one avatar.
 */

// Custom AvatarGroup component to replace antd's Avatar.Group
const CustomAvatarGroup = ({
  children,
  maxCount,
  className,
  maxStyle,
  size = "small",
  forceShowPopover = false,
  totalCount = 0,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const timeoutRef = React.useRef(null);
  const childrenArray = React.Children.toArray(children);
  const visibleAvatars = childrenArray.slice(0, maxCount);
  const hiddenCount = totalCount - visibleAvatars.length;
  const showPopover = forceShowPopover || childrenArray.length > 1; // Show popover when forced or when there's more than 1 avatar

  // Clean up timeout on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    // Add a small delay before closing the popover
    // This gives time for the mouse to enter the popover content
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false);
    }, 100);
  };

  // Create a new array of all users for the popover
  // We use both visible avatars and hidden avatars to show all users in the popover
  const allUsersForPopover = React.Children.map(children, (child) => {
    // Skip null or invalid elements
    if (!React.isValidElement(child)) {
      return null;
    }
    // Clone the child but force showInPopover to true
    return React.cloneElement(child, { showInPopover: true });
  });

  const popoverContent = (
    <div
      className="flex flex-col gap-2 max-h-[200px] overflow-y-auto w-full min-w-[200px] px-4 py-2"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="pb-2 border-b border-ever-base-300 mb-2">
        <EverTg.Caption className="text-ever-base-content font-medium">
          Total members ({totalCount})
        </EverTg.Caption>
      </div>
      <div className="flex flex-col gap-2">{allUsersForPopover}</div>
    </div>
  );

  // Render the avatar group with or without popover
  const renderAvatarGroup = () => (
    <div
      className={twMerge(
        "flex",
        size === "large" && "-space-x-2",
        size === "medium" && "-space-x-1.5",
        size === "small" && "-space-x-1"
      )}
    >
      {visibleAvatars}
      {hiddenCount > 0 && (
        <div className="relative z-10">
          <div
            className={twMerge(
              "flex items-center justify-center rounded-full font-bold bg-ever-base border border-solid border-ever-200",
              sizeValues[size],
              size === "large" && "min-w-8 w-auto",
              size === "medium" && "min-w-7 w-auto",
              size === "small" && "min-w-6 w-auto"
            )}
            style={{
              ...maxStyle,
            }}
          >
            <div className="w-full text-center truncate text-ever-primary">
              +{hiddenCount}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className={twMerge("flex items-center", className)}>
      {showPopover ? (
        <EverPopover
          content={popoverContent}
          showCloseIcon={false}
          className="max-w-[250px] px-0 py-0"
          side="bottom"
          open={isHovered}
          onOpenChange={setIsHovered}
          alignOffset={0}
          noTrigger={false}
          defaultOpen={false}
          arrowClass=""
          portalContainer={undefined}
          onClose={() => setIsHovered(false)}
          closeOnScroll={true}
        >
          <div
            className="cursor-pointer"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {renderAvatarGroup()}
          </div>
        </EverPopover>
      ) : (
        renderAvatarGroup()
      )}
    </div>
  );
};

/**
 * Renders a list of avatars, collapsing them into a group when the number
 * of avatars exceeds the maximum count specified by `groupMaxCount`.
 * @param {EverGroupAvatarProps} options
 * @returns {React.ReactNode} A React component representing the avatar group.
 */
export function EverGroupAvatar({
  containerClassName,
  avatars,
  groupMaxCount = 3,
  limitInPopover, // Keep this for backward compatibility but don't use it
  maxStyle = {},
  size = "small",
  forceShowPopover = false,
  ...rest
}) {
  const parsedAvatars = parse(AvatarsSchema, avatars);

  // If no avatars, don't render anything
  if (!parsedAvatars || parsedAvatars.length === 0) {
    return null;
  }

  const filteredAvatars = parsedAvatars.filter(
    (user) => user?.firstName || user?.lastName || user?.name || user?.image
  );

  return (
    <CustomAvatarGroup
      maxCount={groupMaxCount}
      className={containerClassName}
      maxStyle={maxStyle}
      size={size}
      forceShowPopover={forceShowPopover}
      totalCount={avatars.length}
    >
      {filteredAvatars?.map((user, index) => {
        return (
          <RenderAvatar
            key={index}
            index={index}
            user={user}
            showInPopover={index + 1 > groupMaxCount}
            size={size}
            maxStyle={maxStyle}
            {...rest}
          />
        );
      })}
      {/* if empty values exist show more count */}
      {parsedAvatars.length > filteredAvatars.length && (
        <div className="flex items-center rounded-full">
          <EverTg.Caption className="text-ever-base-content">
            and more...
          </EverTg.Caption>
        </div>
      )}
    </CustomAvatarGroup>
  );
}

/**
 * Generates a consistent index for a name to use for color selection
 * @param {string} name - The name to generate an index for
 * @returns {number} - The generated index
 */
function generateIndex(name) {
  let index = 0;
  if (typeof name === "string") {
    for (let i = 0; i < name.length; i++) {
      index = name.charCodeAt(i) + ((index << 5) - index);
    }
  }
  return Math.abs(index) % availableColors.length;
}

EverGroupAvatar.propTypes = {
  avatars: PropTypes.arrayOf(
    PropTypes.shape({
      firstName: PropTypes.string,
      lastName: PropTypes.string,
      image: PropTypes.string,
      style: PropTypes.string,
      className: PropTypes.string,
    })
  ),
  groupMaxCount: PropTypes.number,
  limitInPopover: PropTypes.number, // Kept for backward compatibility
  containerClassName: PropTypes.string,
  maxStyle: PropTypes.object,
  size: PropTypes.oneOf(["small", "medium", "large"]),
  forceShowPopover: PropTypes.bool,
};
