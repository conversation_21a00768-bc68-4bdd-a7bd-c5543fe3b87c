import { SearchLgIcon } from "@everstage/evericons/duotone";
import { Input, InputNumber } from "antd";
import classnames from "classnames";
import propTypes from "prop-types";
import React, { useState, useCallback, forwardRef } from "react";
import { twMerge } from "tailwind-merge";

const { TextArea, Search, Password, Group } = Input;

/**
 * Renders a Select component.
 *
 * @param {boolean} disabled - Whether to disable select
 * @param {string} placeholder - Placeholder of select
 * @returns {React.ReactNode} The JSX for the Ant-Design's Select component.
 */

const EverInput = forwardRef((props, ref) => {
  const { className, size = "middle", ...rest } = props;
  const sizeMap = {
    middle: "",
    small: "!h-8 !rounded !py-1",
  };
  return (
    <Input
      ref={ref}
      {...rest}
      className={twMerge("shrink-0 grow-0", className, sizeMap[size])}
    />
  );
});

export const EverInputGroup = forwardRef((props, ref) => {
  const { controls, ...inputProps } = props;
  const { children = null, className } = inputProps;
  const [width, setWidth] = useState(0);

  const controlsWrapper = useCallback((controlNode) => {
    setWidth(controlNode ? controlNode.offsetWidth : 0);
  }, []);

  return controls ? (
    <Group
      {...props}
      className={classnames("relative shrink-0 grow-0", className)}
    >
      <Input {...inputProps} ref={ref} style={{ paddingRight: width + 5 }} />
      {controls && controls.length > 0 && (
        <div
          ref={controlsWrapper}
          className="absolute right-0 inset-y-0 z-10 !flex flex-nowrap items-center"
        >
          {controls?.map((Control, index) => (
            <div
              key={index}
              className="h-full border-0 border-l border-solid border-ever-base-400 flex items-center"
            >
              {React.cloneElement(Control, {
                type: "link",
                size: "small",
              })}
            </div>
          ))}
        </div>
      )}
    </Group>
  ) : (
    <Group {...inputProps}>{children}</Group>
  );
});

const EverSearchInput = forwardRef((props, ref) => {
  const { showPrefixIcon } = props;
  return (
    <Search
      ref={ref}
      prefix={
        showPrefixIcon ? (
          <SearchLgIcon className="!size-4 text-ever-base-content-low mr-2" />
        ) : null
      }
      enterButton={
        !showPrefixIcon ? (
          <SearchLgIcon className="!size-4 text-ever-base-content-mid" />
        ) : null
      }
      {...props}
      className={twMerge(
        "[&:hover>.ant-input-wrapper>.ant-input-affix-wrapper]:shadow shrink-0 grow-0",
        props.className,
        props.size
      )}
    />
  );
});

const EverTextArea = forwardRef((props) => {
  const { autoSize, className, ...rest } = props;

  return (
    <TextArea
      {...rest}
      autoSize={autoSize != undefined ? autoSize : true}
      className={twMerge(
        typeof autoSize == "object" ? "" : "!min-h-[72px]",
        className
      )}
    />
  );
});

EverInput.propTypes = {
  disabled: propTypes.bool,
  placeholder: propTypes.string,
  size: propTypes.oneOf(["middle", "small"]),
};

EverInput.defaultProps = {
  size: "middle",
};

EverInputGroup.propTypes = {
  children: propTypes.element,
  controls: propTypes.array,
};

EverSearchInput.propTypes = {
  children: propTypes.element,
  disabled: propTypes.bool,
  showPrefixIcon: propTypes.bool,
};

EverTextArea.propTypes = {
  placeholder: propTypes.string,
  rows: propTypes.string,
  className: propTypes.string,
};

EverInput.Search = EverSearchInput;
EverInput.TextArea = EverTextArea;
EverInput.Password = Password;
EverInput.Number = InputNumber;
EverInput.Group = EverInputGroup;

EverInput.Search.displayName = "EverInput.Search";
EverInput.TextArea.displayName = "EverInput.TextArea";
EverInput.Password.displayName = "EverInput.Password";
EverInput.Number.displayName = "EverInput.Number";
EverInput.Group.displayName = "EverInput.Group";

export { EverInput };
