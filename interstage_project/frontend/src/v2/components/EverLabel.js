import PropTypes from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

/**
 * Renders a Label
 *
 * @param {boolean} showEllipsis - A boolean whether to show elllipsis if text will overflow outside boundry.
 * @returns {React.ReactNode} The JSX for the checkbox component.
 */

export function EverLabel({
  children,
  required = false,
  append,
  prepend,
  className,
  showEllipsis,
  ...rest
}) {
  return (
    <label
      {...rest}
      className={twMerge(
        "text-base !font-[IBM Plex Sans] text-ever-base-content-mid mr-2 flex items-center",
        showEllipsis && "w-full",
        className
      )}
    >
      {prepend && <span className="mr-2 flex items-center">{prepend}</span>}
      <span
        className={twMerge(
          showEllipsis && "w-full truncate",
          "!font-[IBM Plex Sans]"
        )}
      >
        {children}
        {required && (
          <sup className="text-ever-error ml-0.5 text-sm leading-none -top-0.5">
            *
          </sup>
        )}
      </span>
      {append && <span className="ml-2 flex items-center">{append}</span>}
    </label>
  );
}

EverLabel.propTypes = {
  children: PropTypes.node,
  required: PropTypes.bool,
  append: PropTypes.node,
  prepend: PropTypes.node,
  showEllipsis: PropTypes.bool,
};

EverLabel.defaultProps = {
  children: null,
  required: false,
  append: null,
  prepend: null,
  showEllipsis: false,
};
