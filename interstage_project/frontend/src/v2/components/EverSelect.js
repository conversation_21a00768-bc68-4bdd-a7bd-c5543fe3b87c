import { CheckIcon, ChevronDownIcon } from "@everstage/evericons/outlined";
import { Select } from "antd";
import PropTypes from "prop-types";
import React, { useEffect, useRef, useState, forwardRef } from "react";
import { twMerge } from "tailwind-merge";

import { EverButton } from "./ever-button/EverButton";
import { EverNumberBadge } from "./ever-labels";
import { EverLabel } from "./EverLabel";
import { EverListItem } from "./EverListItem";
import { TagRender, findAllStringsInChildren } from "./SelectTagRender";

const everListItemStyleReset = "gap-0 py-0 px-0 rounded-none"; //Resetting these classNames for tags styling to work via scss overrides.

/**
 *
 * @param {string} maxTagTextLength - provide Tailwind's width class value. Minimum value is 16.
 * @param {string} multiSelectOverflow - Choose the behaviour when the tags become longer than EverSelect. (scroll -> Tags become scrollable horizontally, default -> EverSelect stays responsive and changes its height)
 * @param {boolean} showLabel - boolean to show the element above Select box which will have the label, count of tags and clear button
 * @param {string} labelText - text to show in EverSelect's label
 * @param {number} multiSelectCount - number to show in the Number badge
 * @param {function} onClear - function called when clear button is clicked
 *
 * @returns ReactNode
 */
export const EverSelect = forwardRef(
  (
    {
      children,
      options,
      className,
      prependIcon,
      placeholder,
      tagClassName = "",
      filterOption = true,
      maxTagTextLength,
      mode,
      // Props for multi-select
      maxTagCount = 1, //Pass bool 'false' if want this to be defaulted to infinite.
      multiSelectOverflow = "scroll",
      showLabel = false,
      labelText = "",
      multiSelectCount = 0,
      onClear = () => {},
      dropdownMatchSelectWidth = false,
      ...rest
    },
    ref
  ) => {
    const { size = "middle" } = rest;
    // State to monitor if the tags are overflowing inside select box
    const [isOverflowing, setIsOverflowing] = useState(false);
    const overflowRef = useRef(null);

    const borderRadiusMap = {
      middle: "[&>.ant-select-selector]:!rounded-lg",
      small: "[&>.ant-select-selector]:!rounded",
    };

    function checkOverflow(container) {
      if (!container || !(container instanceof HTMLElement)) {
        return;
      }

      const overflowElement = container?.querySelector(
        ".ant-select-selection-overflow"
      );
      if (overflowElement) {
        const isOverflow =
          overflowElement.scrollWidth > overflowElement.clientWidth;
        setIsOverflowing(isOverflow);
      }
    }

    // Observes and checks for overflow in the select container when in multiple mode
    // Uses a MutationObserver to monitor changes in the DOM tree for the select container
    // Calls checkOverflow function to determine if the tags are overflowing inside the select box
    // Disconnects the observer when the component unmounts
    useEffect(() => {
      if (mode !== "multiple") return;
      const selectContainer = overflowRef.current;
      if (!selectContainer) return;

      // observer that changes evertime any element inside the DOM tree changes
      const observer = new MutationObserver(checkOverflow);
      observer.observe(selectContainer, {
        childList: true,
        subtree: true,
        attributes: true,
      });
      checkOverflow(selectContainer);
      return () => observer.disconnect();
    }, [children, options]);

    return (
      <div
        ref={overflowRef}
        className={twMerge(
          "relative w-full flex flex-col",
          size === "small" ? "h-8" : "h-10",
          className
        )}
      >
        {showLabel && mode === "multiple" && (
          <div
            data-testid="ever-select-header"
            className="flex justify-between items-center pl-1 pr-2"
          >
            <EverLabel>{labelText}</EverLabel>
            <div className="flex items-center gap-2">
              <EverNumberBadge
                count={multiSelectCount}
                className="text-ever-chartColors-20 bg-ever-chartColors-2 border-ever-chart-colors-20/20 border border-solid"
              />
              <EverButton
                type="link"
                color="primary"
                className="!p-0"
                onClick={onClear}
              >
                Clear
              </EverButton>
            </div>
          </div>
        )}
        <>
          {prependIcon && (
            <div
              className={twMerge(
                "absolute z-[1] flex items-center justify-center pl-3",
                size === "small" ? "h-8" : "h-10"
              )}
            >
              {prependIcon}
            </div>
          )}
          <Select
            data-testid="ever-select"
            mode={mode}
            ref={ref}
            onClear={onClear}
            suffixIcon={
              <div className="h-full w-10 flex items-center justify-center">
                <ChevronDownIcon
                  className="w-4 h-4 text-ever-base-content-mid"
                  name="suffix"
                />
              </div>
            }
            className={twMerge(
              "w-full",
              prependIcon
                ? "[&>.ant-select-selector]:!pl-10 [&>.ant-select-selector_.ant-select-selection-search]:!left-9"
                : "",
              rest.optionLabelProp === ("iconJSX" || "customSelectedIcon") &&
                "[&>div>.ant-select-selection-item]:flex [&>div>.ant-select-selection-item]:items-center [&>div>.ant-select-selection-placeholder]:flex",
              multiSelectOverflow === "scroll" &&
                "multiselect-overflow-scroll [&>.ant-select-selector>.ant-select-selection-overflow]:flex-nowrap [&>.ant-select-selector>.ant-select-selection-overflow]:overflow-x-auto",
              multiSelectOverflow === "scroll" &&
                isOverflowing &&
                "overflowing-content [&_.ant-select-arrow]:!border-l-0",
              borderRadiusMap[size]
            )}
            showArrow={true}
            maxTagCount={multiSelectOverflow === "scroll" ? false : maxTagCount}
            tagRender={(props) => (
              <TagRender
                {...props}
                tagClassName={tagClassName}
                maxTagTextLength={maxTagTextLength}
                disabled={rest.disabled}
                onMouseDown={rest.onMouseDown}
              />
            )}
            menuItemSelectedIcon={<CheckIcon className="w-5 h-5" />}
            {...(filterOption
              ? {
                  filterOption: (input, option) => {
                    const { options } = option || {};
                    if (options) {
                      return options.includes((child) =>
                        findAllStringsInChildren(child.title)
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      );
                    }
                    return findAllStringsInChildren(option.title)
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  },
                }
              : { filterOption: filterOption })}
            dropdownAlign={{ offset: [0, 6] }} //Position the select dropdown
            optionLabelProp="title"
            dropdownMatchSelectWidth={dropdownMatchSelectWidth}
            placeholder={
              prependIcon ? (
                React.isValidElement(placeholder) ? (
                  placeholder
                ) : (
                  <span className={rest.mode === "multiple" ? "ml-6" : ""}>
                    {placeholder}
                  </span>
                )
              ) : (
                placeholder
              )
            }
            maxTagPlaceholder={(omittedValues) => {
              const ommittedChildren = [];
              omittedValues.forEach((value) => {
                ommittedChildren.push(findAllStringsInChildren(value.label));
              });
              return (
                <span
                  className="cursor-pointer"
                  title={ommittedChildren.join(", ")}
                >
                  +{ommittedChildren.length}
                </span>
              );
            }}
            {...rest}
          >
            {options
              ? options.map((option) => {
                  const { value, label, icon, className, ...restOptions } =
                    option;
                  return (
                    <Select.Option
                      key={value}
                      value={value}
                      title={label ?? value}
                      label={label ?? value}
                      {...restOptions}
                    >
                      <EverListItem
                        prepend={icon}
                        title={label ?? value}
                        className={twMerge(everListItemStyleReset, className)}
                      />
                    </Select.Option>
                  );
                })
              : decorateChildren(children)}
          </Select>
        </>
      </div>
    );
  }
);

EverSelect.Option = Select.Option;
EverSelect.OptGroup = Select.OptGroup;

EverSelect.propTypes = {
  children: PropTypes.node,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.bool,
      ]),
      label: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.bool,
        PropTypes.node,
      ]),
      icon: PropTypes.node,
    })
  ),
  className: PropTypes.string,
  prependIcon: PropTypes.node,
  placeholder: PropTypes.node,
  tagClassName: PropTypes.string,
  filterOption: PropTypes.func,
  maxTagTextLength: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  mode: PropTypes.oneOf(["multiple", "single"]),
  maxTagCount: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.number,
    PropTypes.string,
  ]),
  multiSelectOverflow: PropTypes.oneOf(["scroll", "default"]),
  showLabel: PropTypes.bool,
  labelText: PropTypes.string,
  multiSelectCount: PropTypes.number,
  onClear: PropTypes.func,
};

EverSelect.defaultProps = {
  maxTagCount: false,
  multiSelectOverflow: "scroll",
  showLabel: false,
  multiSelectCount: 0,
  onClear: () => {},
};

function decorateChildren(children) {
  if (!children) return null;
  return React.Children.map(children, (child) => {
    if (!child) return null;
    if (child?.type?.isSelectOptGroup) {
      return renderSelectGroup({ ...child.props });
    }
    return renderSelectOption({ ...child.props });
  });
}

function renderSelectOption({ value, title, children = null, key, ...props }) {
  return typeof children === "string" || typeof children === "number" ? (
    <Select.Option
      key={value ?? title ?? key ?? children}
      value={value ?? title ?? key ?? children}
      title={title || children || value}
      {...props}
    >
      <EverListItem title={children} className={everListItemStyleReset} />
    </Select.Option>
  ) : (
    <Select.Option
      key={value ?? key}
      value={value ?? key}
      title={title || (children?.length > 1 && children[1]) || value}
      className={twMerge(
        !Array.isArray(children) && "[&>.ant-select-item-option-content]:!p-0"
      )}
      {...props}
    >
      {key ? (
        <EverListItem
          title={children?.length > 1 && children[1]}
          className={everListItemStyleReset}
        />
      ) : (
        <span
          title={findAllStringsInChildren(
            title || (children?.length > 1 && children[1]) || value
          )}
          className="w-full py-2.5 px-4"
        >
          {children}
        </span>
      )}
    </Select.Option>
  );
}

function renderSelectGroup({ label, children = null }) {
  return (
    <Select.OptGroup key={label} label={label}>
      {React.Children.map(children, (child) => {
        return renderSelectOption({ ...child.props, key: child.key });
      })}
    </Select.OptGroup>
  );
}
