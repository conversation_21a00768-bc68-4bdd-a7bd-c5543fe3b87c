import { debounce } from "lodash";
import PropTypes from "prop-types";
import React, { useState, useEffect } from "react";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";

import { AgGridIconsMap } from "./AgGridIconsMap";
import { pageSizeList } from "./ever-ag-grid-options";
import { EverButton } from "../ever-button/EverButton";
import { EverInput } from "../EverInput";
import { EverSelect } from "../EverSelect";
import { EverTg } from "../EverTypography";

const { Option } = EverSelect;

/**
 * setCurrentPage triggers re-render in parent and results in new data from GraphQL
 * @param  {} props
 */
export function DynamicPagination(props) {
  const {
    rowPerPageOption,
    pageCount,
    pageSize,
    totalRows,
    setPageSize,
    currentPage,
    setCurrentPage,
    lastUpdated,
    gridRef = {},
    saveTableState = () => {},
    hidePageSize,
    randomPageJumpDisabled = false,
    hideGoFirstGoLastButtons = false,
    pageSwitchDisabled = false,
    wrapperClassName,
    customPageSizeComponent = <></>,
  } = props;

  const rowPerPageOpt = rowPerPageOption || pageSizeList;

  //! current page starts from 0 in aggrid
  const from = currentPage * pageSize + 1;
  let to = from + pageSize - 1;
  to = to < totalRows ? to : totalRows;
  const [tempCurrentPage, setTempCurrentPage] = useState(currentPage + 1);
  const [selectedPageOption, setSelectedPageOption] = useState(pageSize);
  const { accessToken } = useAuthStore();
  function paginationSendAnalytics(page) {
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.PAGINATION, {
      [ANALYTICS_PROPERTIES.PAGINATED_VALUE]: page,
    });
  }
  function onPageSizeChanged(value) {
    saveTableState(gridRef?.current?.api);
    setCurrentPage(1);
    setPageSize(Number(value));
    setSelectedPageOption(Number(value));
  }

  useEffect(() => {
    if (currentPage !== tempCurrentPage - 1) {
      setTempCurrentPage(currentPage + 1);
    }
  }, [currentPage]);

  return (
    <div
      className={twMerge(
        "px-4 py-1 text-sm flex flex-row justify-between items-center",
        wrapperClassName
      )}
    >
      <div className="flex justify-start items-center gap-4">
        {!hidePageSize ? (
          <>
            <EverSelect
              className="w-24"
              size="small"
              value={selectedPageOption}
              onChange={onPageSizeChanged}
            >
              {rowPerPageOpt.map((option) => (
                <Option value={option} key={option}>
                  {option}
                </Option>
              ))}
            </EverSelect>
            <EverTg.Text className="text-ever-base-content whitespace-nowrap">
              per page
            </EverTg.Text>
          </>
        ) : (
          customPageSizeComponent
        )}
        <EverTg.Text className="text-ever-base-content">
          {lastUpdated || ""}
        </EverTg.Text>
      </div>

      <div className="flex items-center gap-8">
        <div className="flex items-center gap-1" data-testid="pt-row-count">
          <EverTg.Text className="text-ever-base-content font-medium">
            {from}
          </EverTg.Text>
          <EverTg.Text className="text-ever-base-content"> - </EverTg.Text>
          <EverTg.Text className="text-ever-base-content font-medium">
            {to}
          </EverTg.Text>
          <EverTg.Text className="text-ever-base-content">of</EverTg.Text>
          <EverTg.Text className="text-ever-base-content font-medium">
            {totalRows}
          </EverTg.Text>
          <EverTg.Text className="text-ever-base-content">rows</EverTg.Text>
        </div>

        <div className="flex items-center gap-1">
          <EverButton.Icon
            disabled={
              pageSwitchDisabled ||
              tempCurrentPage === 1 ||
              tempCurrentPage === 0
                ? true
                : false
            }
            className={hideGoFirstGoLastButtons && "hidden"}
            onClick={() => {
              saveTableState(gridRef?.current?.api);
              setCurrentPage(1);
              paginationSendAnalytics(1);
            }}
            type="text"
            icon={<AgGridIconsMap name="goFirst" />}
          />
          <EverButton.Icon
            disabled={
              pageSwitchDisabled ||
              tempCurrentPage === 1 ||
              tempCurrentPage === 0
                ? true
                : false
            }
            onClick={debounce(() => {
              saveTableState(gridRef?.current?.api);
              setCurrentPage(tempCurrentPage - 1);
              paginationSendAnalytics(tempCurrentPage - 1);
            }, 100)}
            type="text"
            icon={<AgGridIconsMap name="goPrev" />}
          />
          <div className="flex gap-2 items-center">
            <EverTg.Text className="text-ever-base-content">Page </EverTg.Text>
            {randomPageJumpDisabled ? (
              <EverTg.SubHeading4 className="text-ever-base-content">
                {tempCurrentPage}
              </EverTg.SubHeading4>
            ) : (
              <EverInput
                className="w-16"
                size="small"
                value={tempCurrentPage}
                onChange={(e) => {
                  if (
                    (!Number.isNaN(Number(e.target.value)) &&
                      Number(e.target.value) <= pageCount) ||
                    e.target.value === ""
                  ) {
                    setTempCurrentPage(Number(e.target.value));
                  }
                }}
                onPressEnter={(e) => {
                  if (tempCurrentPage !== 0) {
                    setCurrentPage(e.target.value);
                    paginationSendAnalytics(e.target.value);
                  }
                }}
                disabled={pageSwitchDisabled}
              />
            )}
            <div className="flex items-center gap-2">
              <EverTg.Text className="text-ever-base-content">of</EverTg.Text>
              <EverTg.Text className="text-ever-base-content">
                {pageCount}
              </EverTg.Text>
            </div>
          </div>
          <EverButton.Icon
            disabled={
              pageSwitchDisabled ||
              tempCurrentPage >= pageCount ||
              tempCurrentPage === 0 ||
              pageCount === 0
                ? true
                : false
            }
            className={hideGoFirstGoLastButtons && "hidden"}
            onClick={debounce(() => {
              saveTableState(gridRef?.current?.api);
              setCurrentPage(tempCurrentPage + 1);
              paginationSendAnalytics(tempCurrentPage + 1);
            }, 100)}
            type="text"
            icon={<AgGridIconsMap name="goNext" />}
          />
          <EverButton.Icon
            disabled={
              tempCurrentPage >= pageCount ||
              tempCurrentPage === 0 ||
              pageCount === 0
                ? true
                : false
            }
            onClick={() => {
              saveTableState(gridRef?.current?.api);
              setCurrentPage(pageCount);
              paginationSendAnalytics(pageCount);
            }}
            type="text"
            icon={<AgGridIconsMap name="goLast" />}
          />
        </div>
      </div>
    </div>
  );
}

DynamicPagination.propTypes = {
  rowPerPageOption: PropTypes.array,
  pageCount: PropTypes.number,
  pageSize: PropTypes.number,
  totalRows: PropTypes.number,
  setPageSize: PropTypes.func,
  currentPage: PropTypes.number,
  setCurrentPage: PropTypes.func,
  lastUpdated: PropTypes.string,
  gridRef: PropTypes.object,
  saveTableState: PropTypes.func,
  hidePageSize: PropTypes.bool,
};
