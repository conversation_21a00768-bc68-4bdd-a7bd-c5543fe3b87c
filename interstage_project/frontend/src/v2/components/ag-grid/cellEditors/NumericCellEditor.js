import React, { memo, useEffect, useRef } from "react";

import { message } from "~/v2/components";

import { EverInput } from "../../EverInput";

// backspace starts the editor on Windows
const KEY_BACKSPACE = "Backspace";
const KEY_F2 = "F2";
const KEY_ENTER = "Enter";
const KEY_TAB = "Tab";

export const NumericCellEditor = memo(
  ({ value, onValueChange, eventKey, stopEditing, useDecimals }) => {
    const updateValue = (val) => {
      if (useDecimals) {
        val = val?.toString();
        if (val?.endsWith("-")) {
          if (val.toString().length === 1) {
            onValueChange("-0");
          } else {
            return;
          }
        } else if (val === "") {
          onValueChange(null);
        } else if (val?.endsWith(".")) {
          onValueChange(val);
        } else {
          onValueChange(parseFloat(val));
        }
      } else {
        if (val?.endsWith("-")) {
          if (val.toString().length === 1) {
            onValueChange("-");
          } else {
            return;
          }
        } else {
          onValueChange(val === "" ? null : parseInt(val));
        }
      }
    };

    useEffect(() => {
      let startValue;
      let highlightAllOnFocus = true;

      if (eventKey === KEY_BACKSPACE) {
        // if backspace or delete pressed, we clear the cell
        startValue = "";
      } else if (eventKey && eventKey.length === 1) {
        // if a letter was pressed, we start with the letter
        startValue = eventKey;
        highlightAllOnFocus = false;
      } else {
        // otherwise we start with the current value
        startValue = value;
        if (eventKey === KEY_F2) {
          highlightAllOnFocus = false;
        }
      }
      if (startValue == null) {
        startValue = "";
      }

      updateValue(startValue);

      // get ref from React component
      const eInput = refInput.current;
      eInput.focus();
      if (highlightAllOnFocus) {
        eInput.select();
      } else {
        // when we started editing, we want the caret at the end, not the start.
        // this comes into play in two scenarios:
        //   a) when user hits F2
        //   b) when user hits a printable character
        const length = eInput.value ? eInput.value.length : 0;
        if (length > 0) {
          eInput.setSelectionRange(length, length);
        }
      }
    }, []);

    const refInput = useRef(null);

    const isLeftOrRight = (event) => {
      return ["ArrowLeft", "ArrowRight"].indexOf(event.key) > -1;
    };

    const isCharNumeric = (charStr) => {
      return !!/^-?\d*\.?\d*$/.test(charStr);
    };

    const isNumericKey = (event) => {
      const charStr = event.key;
      return isCharNumeric(charStr) || charStr === "." || charStr === "-";
    };

    const isBackspace = (event) => {
      return event.key === KEY_BACKSPACE;
    };

    const finishedEditingPressed = (event) => {
      const key = event.key;
      return key === KEY_ENTER || key === KEY_TAB;
    };

    const onKeyDown = (event) => {
      if ((event.metaKey || event.ctrlKey) && event.key === "v") {
        return;
      }
      if (isLeftOrRight(event) || isBackspace(event)) {
        event.stopPropagation();
        return;
      }

      if (!finishedEditingPressed(event) && !isNumericKey(event)) {
        if (event.preventDefault) event.preventDefault();
      }

      if (finishedEditingPressed(event)) {
        stopEditing();
      }
    };

    const onPaste = (event) => {
      let pasteData = event.clipboardData.getData("Text");
      pasteData = pasteData.replace(/[^0-9.-]/g, "");

      if (isCharNumeric(pasteData)) {
        const eInput = refInput.current;
        const start = eInput.selectionStart;
        const end = eInput.selectionEnd;
        const currentValue = eInput.value;

        const prefix = currentValue?.slice(0, start) || "";
        const suffix = currentValue?.slice(end) || "";

        const newValue = prefix + pasteData + suffix;
        updateValue(newValue);
        eInput.setSelectionRange(
          start + pasteData.length,
          start + pasteData.length
        );
      } else {
        message.error("Invalid input");
      }
      event.preventDefault();
    };

    return (
      <EverInput
        ref={refInput}
        value={value == null ? "" : value}
        onChange={(event) => updateValue(event.target.value)}
        onKeyDown={(event) => onKeyDown(event)}
        onPaste={(event) => onPaste(event)}
      />
    );
  }
);
