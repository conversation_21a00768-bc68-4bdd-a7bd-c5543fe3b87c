import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { PropTypes } from "prop-types";
import React, { forwardRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import { buttonStyles } from "./buttonStyles";
import { IconButton } from "./IconButton";

export const EverButton = forwardRef(
  (
    {
      type = "filled",
      color = "primary",
      size = "medium",
      prependIcon,
      appendIcon,
      appendIconSize,
      className,
      children,
      tooltipTitle = "",
      ...props
    },
    ref
  ) => {
    const [isLoading, setLoading] = useState(false);
    const { icon, ...remainingProps } = props;

    if (remainingProps.onClick) {
      const onClick = remainingProps.onClick;
      remainingProps.onClick = async (e) => {
        try {
          setLoading(true);
          const onClickVal = onClick(e);
          if (onClickVal instanceof Promise) {
            try {
              const res = await onClickVal;
              res && console.log(res);
            } catch (error) {
              error && console.log(error);
            }

            setLoading(false);
          } else {
            setLoading(false);
          }
        } catch {
          setLoading(false);
        }
      };
    }

    remainingProps.loading = remainingProps.loading || isLoading;

    const iconSizes = {
      small: "h-4 w-4",
      medium: "h-5 w-5",
      large: "h-6 w-6",
    };
    const prependIconComponent = prependIcon || icon;

    const everButton = (
      <Button
        ref={ref}
        type={type}
        className={twMerge(
          buttonStyles({
            type: type,
            intent: color,
            size: `${size}`,
          }),
          className
        )}
        {...remainingProps}
      >
        <div className="flex gap-2 justify-center items-center">
          {prependIconComponent && (
            <div className={twMerge("flex text-inherit", iconSizes[size])}>
              {React.cloneElement(prependIconComponent, {
                ...(prependIconComponent.props ?? {}),
                className: twMerge(
                  prependIconComponent.props?.className ?? "",
                  "h-full w-full"
                ),
              })}
            </div>
          )}
          {children}
          {appendIcon && (
            <div
              className={twMerge(
                "flex text-inherit",
                iconSizes[appendIconSize ?? size]
              )}
            >
              {React.cloneElement(appendIcon, {
                ...(appendIcon.props ?? {}),
                className: twMerge(
                  appendIcon.props?.className ?? "",
                  "h-full w-full"
                ),
              })}
            </div>
          )}
        </div>
      </Button>
    );
    // In antd version 4, the tooltip does not appear when the button is disabled,
    // therefore this is a workaround. we need to provide a proper solution for this issue.
    if (tooltipTitle !== "") {
      return (
        <Tooltip title={tooltipTitle}>
          <span className="inline-block">{everButton}</span>
        </Tooltip>
      );
    }
    return everButton;
  }
);

EverButton.Icon = IconButton;

// Add prop-types here
EverButton.propTypes = {
  /**
   * Set the type of the button.
   * */
  type: PropTypes.oneOf(["filled", "ghost", "text", "link", "dashed"]),
  /**
   * Set the color of the button.
   * */
  color: PropTypes.oneOf([
    "primary",
    "success",
    "error",
    "info",
    "warning",
    "base",
  ]),
  /**
   * Set the size of the button.
   */
  size: PropTypes.oneOf(["large", "small", "medium"]),
  /**
   * Set the icon to be prepended in front of the button text.
   * Accepts SVG or React elements.
   */
  prependIcon: PropTypes.element,
  /**
   * Set the icon to be appended after the button text.
   * Accepts SVG or React elements.
   */
  appendIcon: PropTypes.element,
  /**
   * Provide custom size for the append icon. If not provided, it will take the size of the button.
   */
  appendIconSize: PropTypes.oneOf(["large", "small", "medium"]),
};

// Add default props here
EverButton.defaultProps = {
  type: "filled",
  color: "primary",
  size: "medium",
  prependIcon: null,
  appendIcon: null,
  appendIconSize: null,
};
