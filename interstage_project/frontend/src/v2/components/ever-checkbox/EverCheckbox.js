import { Checkbox } from "antd";
import PropTypes from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

import { <PERSON><PERSON>abe<PERSON> } from "../EverLabel";

/**
 * Renders a checkbox
 *
 * @param {string} value - The value of the checkbox.
 * @param {string} label - The label of the checkbox.
 * @param {boolean} checked - Whether or not the checkbox is checked.
 * @param {boolean} disabled - Whether the checkbox is disabled or not.
 * @param {function} onChange - The onChange function that runs when the checkbox is clicked.
 * @param {string} className - A class name that can be passed in to add extra styling to the checkbox.
 * @param {boolean} indeterminate - A boolean value which decided if the checkbox is deault or indeterminate.
 * @param {boolean} showEllipsis - A boolean whether to show elllipsis if text will overflow outside boundry.
 * @returns {React.ReactNode} The JSX for the checkbox component.
 */
export const EverCheckbox = React.forwardRef(
  (
    {
      value,
      label,
      checked,
      disabled,
      onChange,
      className = "",
      indeterminate = false,
      onClick,
      children,
      showEllipsis,
      ...remainingProps
    },
    ref
  ) => {
    let customClassName =
      (disabled ? "disabled-" : "") +
      (indeterminate ? "indeterminate" : "tick");

    return (
      <Checkbox
        value={value}
        ref={ref}
        onChange={onChange}
        checked={checked}
        disabled={disabled}
        onClick={(event) => {
          onClick && onClick(event);
        }}
        indeterminate={indeterminate}
        className={twMerge(
          customClassName,
          showEllipsis
            ? "[&>span+span]:min-w-0 [&>span+span>span]:inline-block [&>span+span>span]:w-full [&>span+span>span]:truncate [&>span+span>span]:align-middle"
            : "w-max",
          className
        )}
        {...remainingProps}
      >
        {label ? (
          <EverLabel className="pointer-events-none" showEllipsis>
            {label}
          </EverLabel>
        ) : (
          children
        )}
      </Checkbox>
    );
  }
);

EverCheckbox.Group = Checkbox.Group;

EverCheckbox.propTypes = {
  type: PropTypes.string,
  value: PropTypes.string,
  label: PropTypes.string,
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  className: PropTypes.string,
  indeterminate: PropTypes.bool,
  showEllipsis: PropTypes.bool,
};
