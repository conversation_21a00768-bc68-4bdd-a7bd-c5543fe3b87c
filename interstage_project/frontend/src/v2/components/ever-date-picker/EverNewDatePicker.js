/* eslint-disable react-hooks/exhaustive-deps */
import { CalendarIcon } from "@everstage/evericons/duotone";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockFastForwardIcon,
  ArrowNarrowRightIcon,
} from "@everstage/evericons/outlined";
import {
  TimePicker as AntTimePicker,
  Button,
  DatePicker as MomentDatePicker,
} from "antd";
import generatePicker from "antd/es/date-picker/generatePicker";
import {
  addDays,
  addMonths,
  addQuarters,
  addWeeks,
  addYears,
  subDays,
  subMonths,
  subQuarters,
  subWeeks,
  subYears,
} from "date-fns";
import moment from "moment";
import propTypes from "prop-types";
import dateFnsGenerateConfig from "rc-picker/lib/generate/dateFns";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

import { COMMON_MOMENT_DATE_FORMAT, COMMON_FNS_DATE_FORMAT } from "~/Enums";
import { EverLoader } from "~/v2/components";

import { EverDateInput } from "../ever-date-input/EverDateInput";
import { EverTg } from "../EverTypography";

const DateFnsPicker = generatePicker(dateFnsGenerateConfig);

const { RangePicker } = DateFnsPicker;
const { RangePicker: MomentRangePicker } = MomentDatePicker;

/**
 * Renders a Datepicker component. Note: DatePicker by default uses date-fns. Use DatePicker.Legacy if you moment is required.
 *
 * @param {boolean} type - Select type of Datepicker to display. 'Period' shows controls on left and right for changing dates
 * @param {string} placeholder - Placeholder of Datepicker.
 * @param {string} className - Adds a classname to Datepicker component.
 * @param {string} wrapperClassname - Adds a classname to the EverDatepicker wrapper element.
 * @returns {React.ReactNode} The JSX for the Ant-Design's Datepicker component.
 */

const buttonStyles =
  "!absolute !z-10 !h-10 !border-0 !border-solid !bg-transparent !border-l hover:text-ever-primary hover:border-ever-base-400";

const LeftControl = (props) => {
  return (
    <Button
      className={twMerge(
        buttonStyles,
        "left-0 rounded-l-lg border-r border-r-ever-base-400 hover:border-r-ever-base-400"
      )}
      {...props}
    >
      <ChevronLeftIcon className="w-4 h-4" />
    </Button>
  );
};

const RightControl = (props) => {
  return (
    <Button
      className={twMerge(
        buttonStyles,
        "right-0 rounded-r-lg border-l border-l-ever-base-400 hover:border-l-ever-base-400"
      )}
      {...props}
    >
      <ChevronRightIcon className="w-4 h-4" />
    </Button>
  );
};

const PeriodPickerWrapper = ({
  isPeriodDatePicker,
  handlePrevDate,
  handleNextDate,
  children,
  wrapperClassname,
  prevDisabled,
  nextDisabled,
  calendarRef,
}) => {
  return (
    <div
      ref={calendarRef}
      className={twMerge("relative inline-block", wrapperClassname)}
    >
      {isPeriodDatePicker ? (
        <LeftControl onClick={handlePrevDate} disabled={prevDisabled} />
      ) : null}
      {children}
      {isPeriodDatePicker ? (
        <RightControl onClick={handleNextDate} disabled={nextDisabled} />
      ) : null}
    </div>
  );
};

const EverNewDatePickerLegacy = forwardRef((props, ref) => {
  const {
    picker,
    type,
    value,
    defaultValue,
    className,
    wrapperClassname,
    hideDateInput = false,
    format = COMMON_MOMENT_DATE_FORMAT,
    onChange,
    isPickerLoading = false,
    showToday = true,
  } = props;
  const [selectedDate, setSelectedDate] = useState(null);
  const [open, setOpen] = useState(false);
  const calendarRef = useRef(null);
  const dateInputRef = useRef(null);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    } else {
      setSelectedDate(null);
    }
  }, [value]);

  useEffect(() => {
    if (defaultValue) setSelectedDate(defaultValue);
    if (value) setSelectedDate(value);
    if (!defaultValue && !value) setSelectedDate(null);
  }, []);

  const pickerType = {
    date: "d",
    week: "w",
    month: "M",
    quarter: "Q",
    year: "y",
  };

  const handleNextDate = () => {
    const nextDate = selectedDate
      ? moment(selectedDate).add(1, picker ? pickerType[picker] : "d")
      : moment();
    setSelectedDate(nextDate);
  };

  const handlePrevDate = () => {
    const prevDate = selectedDate
      ? moment(selectedDate).subtract(1, picker ? pickerType[picker] : "d")
      : moment();
    setSelectedDate(prevDate);
  };

  const isPeriodDatePicker = type === "period";

  const handleDateChange = useCallback((date) => {
    const momentDate = moment(date, "MM-DD-YYYY");
    // Get the current local time
    const currentTime = moment();
    // Set the time of the momentDate to the current local time
    momentDate.set({
      hour: currentTime.hour(),
      minute: currentTime.minute(),
      second: currentTime.second(),
      millisecond: currentTime.millisecond(),
    });
    setSelectedDate(momentDate);
    const dateStr = momentDate.format(COMMON_MOMENT_DATE_FORMAT);
    onChange && onChange(momentDate, dateStr);
  }, []);
  const handleCloseDatePicker = () => {
    setOpen(false);
  };

  return (
    <PeriodPickerWrapper
      isPeriodDatePicker={isPeriodDatePicker}
      handlePrevDate={handlePrevDate}
      handleNextDate={handleNextDate}
      wrapperClassname={wrapperClassname}
      calendarRef={calendarRef}
    >
      <MomentDatePicker
        format={COMMON_MOMENT_DATE_FORMAT}
        {...props}
        ref={ref}
        suffixIcon={
          isPeriodDatePicker ? null : (
            <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
          )
        }
        onClick={() => {
          setOpen(true);
        }}
        open={open}
        onChange={(date, dateStr) => {
          setSelectedDate(date);
          onChange && onChange(date, dateStr);
          setOpen(false);
        }}
        value={selectedDate}
        dropdownAlign={{
          offset: [0, 6],
        }}
        className={twMerge(
          isPeriodDatePicker && "!px-20 [&>*>*]:text-center",
          "rounded-lg text-center border-ever-base-400 bg-ever-primary-content hover:shadow-md focus:!ring-2 focus:ring-ever-primary-ring",
          className
        )}
        renderExtraFooter={() =>
          isPickerLoading ? (
            <div className="absolute top-0 left-0 h-full w-full">
              <EverLoader
                wrapperClassName="absolute top-0 left-0 z-20"
                indicatorType="spinner"
              />
            </div>
          ) : showToday ? (
            <div className="w-full h-auto flex justify-between py-3 border-t border-solid border-ever-base-300 gap-x-3">
              <div
                className="cursor-pointer flex items-center justify-center h-10 border border-solid border-ever-primary text-ever-primary text-ever-base-content px-4 p-2.5 rounded-lg w-auto"
                onClick={(event) => {
                  event.preventDefault();
                  const momentDate = moment(new Date());
                  setSelectedDate(momentDate);
                  const dateStr = momentDate.format(format);

                  onChange && onChange(momentDate, dateStr);
                  setOpen(false);
                }}
              >
                Today
              </div>
              {!hideDateInput && (
                <>
                  <div>
                    <EverTg.Text className="text-ever-base-content-mid">
                      Or
                    </EverTg.Text>
                  </div>
                  <div
                    className="h-10 w-auto flex flex-grow"
                    onClick={(event) => {
                      event.preventDefault();
                    }}
                  >
                    {
                      <EverDateInput
                        value={
                          selectedDate
                            ? moment(selectedDate).format("MM/DD/yyyy")
                            : ""
                        }
                        onChange={handleDateChange}
                        ref={dateInputRef}
                        handleCloseDatePicker={handleCloseDatePicker}
                      />
                    }
                  </div>
                </>
              )}
            </div>
          ) : null
        }
        onBlur={() => {
          setTimeout(() => {
            if (dateInputRef.current === null) {
              setOpen(false);
            }
            if (
              dateInputRef.current &&
              !dateInputRef.current.contains(document.activeElement)
            ) {
              setOpen(false);
            }
          }, 100);
        }}
        showToday={false}
        panelRender={(item) => <>{item}</>}
      />
    </PeriodPickerWrapper>
  );
});

const incrementDate = (date, pickerType, amount) => {
  switch (pickerType) {
    case "date": {
      const newDate = addDays(date, amount);
      return newDate;
    }
    case "week": {
      const newDate = addWeeks(date, amount);
      return newDate;
    }
    case "month": {
      const newDate = addMonths(date, amount);
      return newDate;
    }
    case "quarter": {
      const newDate = addQuarters(date, amount);
      return newDate;
    }
    case "year": {
      const newDate = addYears(date, amount);
      return newDate;
    }
    default: {
      return date;
    }
  }
};

const decrementDate = (date, pickerType, amount) => {
  switch (pickerType) {
    case "date": {
      const newDate = subDays(date, amount);
      return newDate;
    }
    case "week": {
      const newDate = subWeeks(date, amount);
      return newDate;
    }
    case "month": {
      const newDate = subMonths(date, amount);
      return newDate;
    }
    case "quarter": {
      const newDate = subQuarters(date, amount);
      return newDate;
    }
    case "year": {
      const newDate = subYears(date, amount);
      return newDate;
    }
    default: {
      return date;
    }
  }
};

const EverTimePicker = forwardRef((props, ref) => {
  const { className, value, onChange, timezone, ...restProps } = props;

  // Only convert if timezone is not UTC
  const displayValue = value
    ? timezone === "UTC"
      ? moment.utc(value)
      : moment.utc(value).tz(timezone)
    : null;

  const handleChange = (selectedTime) => {
    if (!selectedTime) {
      onChange?.(null);
      return;
    }

    // Extract hours and minutes explicitly to avoid any timezone conversion issues
    // that could occur if we tried to use the full selectedTime object directly
    const hours = selectedTime.hours();
    const minutes = selectedTime.minutes();

    // Create a new UTC moment with the same hours and minutes
    const utcTime =
      timezone === "UTC"
        ? moment.utc().hours(hours).minutes(minutes).seconds(0).milliseconds(0)
        : moment
            .tz(timezone)
            .hours(hours)
            .minutes(minutes)
            .seconds(0)
            .milliseconds(0)
            .utc();

    onChange?.(utcTime);
  };

  return (
    <AntTimePicker
      ref={ref}
      {...restProps}
      value={displayValue}
      onChange={handleChange}
      suffixIcon={
        <ClockFastForwardIcon className="w-4 h-4 text-ever-base-content-mid" />
      }
      className={twMerge(
        "rounded-lg text-center border-ever-base-400 bg-ever-primary-content hover:shadow focus:ring-2 focus:ring-ever-primary-ring",
        className
      )}
    />
  );
});

const EverNewDatePicker = forwardRef((props, ref) => {
  const {
    picker = "date",
    type,
    value,
    defaultValue,
    className,
    wrapperClassname,
    disabledDate,
    hideDateInput = false,
    onChange,
    isPickerLoading = false,
    showToday = true,
  } = props;

  const [selectedDate, setSelectedDate] = useState(null);
  const [prevDisabled, setPrevDisabled] = useState(false);
  const [nextDisabled, setNextDisabled] = useState(false);
  const [open, setOpen] = useState(false);
  const calendarRef = useRef(null);
  const dateInputRef = useRef(null);

  const isDateDisabled = (date) => {
    return disabledDate ? disabledDate(date) : false;
  };

  // Find the next enabled date in the given direction (1 for forward, -1 for backward)
  const findEnabledDate = (startDate, direction) => {
    let date = startDate;
    if (!disabledDate) {
      return date;
    }
    while (isDateDisabled(date)) {
      date =
        direction > 0
          ? incrementDate(date, picker, 1)
          : decrementDate(date, picker, 1);
      //Just in case to prevent infinite loop
      if (date < new Date(2000, 0, 1) || date > new Date(2100, 0, 1))
        return null;
    }
    return date;
  };

  const handlePrevDate = () => {
    const newDate = findEnabledDate(
      decrementDate(selectedDate || new Date(), picker, 1),
      -1
    );
    if (newDate) {
      onChange && onChange(newDate);
      setSelectedDate(newDate);
    }
  };

  const handleNextDate = () => {
    const newDate = findEnabledDate(
      incrementDate(selectedDate || new Date(), picker, 1),
      1
    );
    if (newDate) {
      onChange && onChange(newDate);
      setSelectedDate(newDate);
    }
  };

  useEffect(() => {
    const prevDate = findEnabledDate(
      decrementDate(selectedDate || new Date(), picker, 1),
      -1
    );
    const nextDate = findEnabledDate(
      incrementDate(selectedDate || new Date(), picker, 1),
      1
    );

    setPrevDisabled(prevDate === null);
    setNextDisabled(nextDate === null);
  }, [selectedDate, disabledDate, picker]);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    } else {
      setSelectedDate(null);
    }
  }, [value]);

  useEffect(() => {
    if (defaultValue) setSelectedDate(defaultValue);
    if (value) setSelectedDate(value);
    if (!defaultValue && !value) setSelectedDate(null);
  }, []);

  const isPeriodDatePicker = type === "period";

  const handleDateChange = useCallback((date) => {
    const dateObject = new Date(date);
    setSelectedDate(dateObject);
    onChange && onChange(dateObject);
  }, []);

  const handleCloseDatePicker = () => {
    setOpen(false);
  };

  return (
    <PeriodPickerWrapper
      isPeriodDatePicker={isPeriodDatePicker}
      handlePrevDate={handlePrevDate}
      handleNextDate={handleNextDate}
      wrapperClassname={wrapperClassname}
      prevDisabled={prevDisabled}
      nextDisabled={nextDisabled}
      calendarRef={calendarRef}
    >
      <DateFnsPicker
        ref={ref}
        suffixIcon={
          isPeriodDatePicker ? null : (
            <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
          )
        }
        onClick={() => {
          setOpen(true);
        }}
        id="date-picker-input"
        format={COMMON_FNS_DATE_FORMAT}
        dropdownClassName="date-picker-dropdown"
        open={open}
        {...props}
        renderExtraFooter={() =>
          isPickerLoading ? (
            <div className="absolute top-0 left-0 h-full w-full">
              <EverLoader
                wrapperClassName="absolute top-0 left-0 z-20"
                indicatorType="spinner"
              />
            </div>
          ) : showToday ? (
            <div
              className={twMerge(
                "w-full h-auto flex justify-between py-3 border-t border-solid border-ever-base-300 gap-x-3",
                hideDateInput && "justify-center"
              )}
            >
              <div
                className="cursor-pointer flex items-center justify-center h-10 border border-solid border-ever-primary text-ever-primary text-ever-base-content px-4 p-2.5 rounded-lg w-auto"
                onClick={(event) => {
                  event.preventDefault();
                  setSelectedDate(new Date());
                  onChange && onChange(new Date());
                  setOpen(false);
                }}
              >
                Today
              </div>
              {!hideDateInput && open && (
                <>
                  <div>
                    <EverTg.Text className="text-ever-base-content-mid">
                      Or
                    </EverTg.Text>
                  </div>
                  <div
                    className="h-10 w-auto flex flex-grow"
                    onClick={(event) => {
                      event.preventDefault();
                    }}
                  >
                    {
                      <EverDateInput
                        value={
                          selectedDate
                            ? `${(selectedDate.getMonth() + 1)
                                .toString()
                                .padStart(2, "0")}/${selectedDate
                                .getDate()
                                .toString()
                                .padStart(
                                  2,
                                  "0"
                                )}/${selectedDate.getFullYear()}`
                            : ""
                        }
                        onChange={handleDateChange}
                        ref={dateInputRef}
                        handleCloseDatePicker={handleCloseDatePicker}
                      />
                    }
                  </div>
                </>
              )}
            </div>
          ) : null
        }
        onChange={(date) => {
          setSelectedDate(date);
          onChange && onChange(date);
          setOpen(false);
        }}
        onBlur={() => {
          setTimeout(() => {
            if (dateInputRef.current === null) {
              setOpen(false);
            }
            if (
              dateInputRef.current &&
              !dateInputRef.current.contains(document.activeElement)
            ) {
              setOpen(false);
            }
          }, 100);
        }}
        disabledDate={disabledDate}
        value={selectedDate}
        dropdownAlign={{
          offset: [0, 6],
        }}
        className={twMerge(
          isPeriodDatePicker && "!px-20 [&>*>*]:text-center",
          "h-10 rounded-lg text-center border-ever-base-400 bg-ever-primary-content hover:shadow focus:ring-2 focus:ring-ever-primary-ring",
          className
        )}
        showToday={false}
      />
    </PeriodPickerWrapper>
  );
});

const EverRangePickerLegacy = forwardRef((props, ref) => {
  return (
    <MomentRangePicker
      ref={ref}
      format={COMMON_MOMENT_DATE_FORMAT}
      suffixIcon={
        <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
      }
      separator={
        <div>
          <ArrowNarrowRightIcon className="text-ever-primary w-4 h-4" />
        </div>
      }
      {...props}
      className={twMerge("hover:shadow", props.className)}
    />
  );
});

const EverRangePicker = forwardRef((props, ref) => {
  return (
    <RangePicker
      ref={ref}
      format={COMMON_FNS_DATE_FORMAT}
      suffixIcon={
        <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
      }
      separator={
        <div>
          <ArrowNarrowRightIcon className="text-ever-primary w-4 h-4" />
        </div>
      }
      {...props}
      className={twMerge("hover:shadow", props.className)}
    />
  );
});

EverRangePicker.displayName = "EverNewDatePicker.RangePicker";
EverNewDatePicker.propTypes = {
  disabled: propTypes.bool,
  placeholder: propTypes.string,
  type: propTypes.oneOf([null, "period"]),
  format: propTypes.string,
  picker: propTypes.oneOf(["date", "week", "month", "quarter", "year"]),
  wrapperClassname: propTypes.string,
};

EverTimePicker.propTypes = {
  disabled: propTypes.bool,
  placeholder: propTypes.string,
  format: propTypes.string,
  timezone: propTypes.string,
};
EverRangePickerLegacy.displayName = "EverRangePicker.Legacy.RangePicker";
EverNewDatePickerLegacy.displayName = "EverDatepicker.Legacy";
EverNewDatePickerLegacy.propTypes = {
  disabled: propTypes.bool,
  placeholder: propTypes.string,
  type: propTypes.oneOf([null, "period"]),
  format: propTypes.string,
  picker: propTypes.oneOf(["date", "week", "month", "quarter", "year"]),
  wrapperClassname: propTypes.string,
};

EverNewDatePicker.Legacy = EverNewDatePickerLegacy;
EverNewDatePicker.Legacy.RangePicker = EverRangePickerLegacy;
EverNewDatePicker.RangePicker = EverRangePicker;

EverNewDatePicker.Legacy.displayName = "EverDatepicker.Legacy";
EverNewDatePicker.Legacy.RangePicker.displayName =
  "EverDatePicker.Legacy.RangePicker";
EverNewDatePicker.RangePicker.displayName = "EverDatepicker.RangePicker";
EverTimePicker.displayName = "EverTimePicker";

export { EverNewDatePicker, EverTimePicker };
