// TODO: Replace antd spin with v2 spinner.
import { List, Spin, Tree } from "antd";
import {
  intersection,
  chain,
  map,
  difference,
  cloneDeep,
  isEmpty,
  isArray,
  isNull,
} from "lodash";
import { observer } from "mobx-react";
// import VirtualList from "rc-virtual-list";
import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useCallback,
} from "react";
import { twMerge } from "tailwind-merge";

import { LIST_TYPE } from "~/Enums";
import { EverTg, EverVirtualizerDynamic } from "~/v2/components";
import { EverCheckbox } from "~/v2/components/ever-checkbox";
import { useLazyComponent } from "~/v2/components/ever-lazy-component/useLazyComponent";
import { EverInput } from "~/v2/components/EverInput";

import { EverTooltip } from "../../EverTooltip";

function extractKeys(list) {
  return chain(list)
    .filter((item) => Number.isNaN(Number(item)))
    .compact()
    .uniq()
    .value();
}

function handleSearchInputOnKeyDown(event) {
  event.stopPropagation();
}

export const LazyListContent = observer((props) => {
  const {
    minSearchChar = 3,
    limit = 100,
    delay = 500,
    selectedKeys = [],
    updateSelectedKeys,
    type,
    noDataText = "No matches found",
    listHeight = 264,
    showSearch = true,
    fetchOnBottom = false,
    skipSelectAll = false,
    getOptions,
    abort,
    deps = [],
    customProps = {},
    // NOTE: Used to render custom list item/option.
    listItemRenderer,
    // NOTE: When using `listItemRenderer`, send custom `listItemHeight` if the list item/option height is greater than default(39).
    listItemHeight = 39,
    listHeaderClassName,
    listItemClassName = "",
    searchPlaceholder = "Search",
    transformOptions = null,
    listHeightClass,
    enableScrollShadow,
    showEllipsis,
  } = props;
  const newListHeight =
    listHeight - (showSearch ? 48 : 0) - (!skipSelectAll ? 36 : 0);
  const listRef = useRef(null);
  const [loadingStatus, setLoadingStatus] = useState(null);

  const {
    initialLoad,
    searchValue,
    isLoading,
    isSearchInProgress,
    canFetchMore,
    options,
    onScroll,
    onSearch,
    fetchRemainingData,
  } = isEmpty(customProps)
    ? useLazyComponent({
        minSearchChar,
        limit,
        fetchOnBottom,
        getOptions,
        delay,
        abort,
        listItemHeight: listItemHeight,
        deps,
      })
    : customProps;

  useEffect(() => {
    listRef.current?.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  }, deps);

  const transformOptionsCallback = useCallback((options) => {
    if (!isNull(transformOptions) && typeof transformOptions === "function") {
      return transformOptions(options);
    }

    return type === LIST_TYPE.HIERARCHY_TREE
      ? chain(options)
          .map("children")
          .map((data) => map(data, "key"))
          .flattenDeep()
          .compact()
          .uniq()
          .value()
      : options.map((option) => option.value);
  }, []);

  function handleSelect(data) {
    if (type === LIST_TYPE.HIERARCHY_TREE) {
      const uncheckedKeys = difference(optionKeyList, data);
      const keys = chain(selectedKeys)
        .union(extractKeys(data))
        .difference(uncheckedKeys)
        .compact()
        .uniq()
        .value();
      updateSelectedKeys(keys);
    } else {
      const tempSelectedKeys = cloneDeep(selectedKeys);
      const index = selectedKeys.indexOf(data.target.value);
      if (index > -1) {
        tempSelectedKeys.splice(index, 1);
      } else {
        tempSelectedKeys.push(data.target.value);
      }
      updateSelectedKeys(tempSelectedKeys);
    }
  }

  const optionKeyList = useMemo(() => {
    return transformOptionsCallback(options);
  }, [options, transformOptionsCallback]);

  const checkboxState = useMemo(() => {
    let allSelected = false;
    let isIndeterminate = false;
    const selectedList = intersection(selectedKeys, optionKeyList);
    if (selectedList.length > 0) {
      if (selectedList.length === optionKeyList.length) {
        allSelected = true;
      } else {
        isIndeterminate = true;
      }
    }
    return { allSelected, isIndeterminate };
  }, [optionKeyList, selectedKeys]);

  function selectAllCallback(options) {
    const tempSelectedKeys = transformOptionsCallback(options);
    updateSelectedKeys(tempSelectedKeys);
    setLoadingStatus(null);
  }

  function handleSelectAll(event) {
    let tempSelectedPayees = [];
    if (event.target.checked) {
      setLoadingStatus("selectAll");
      if (canFetchMore) {
        fetchRemainingData(selectAllCallback);
      } else {
        tempSelectedPayees = chain(selectedKeys)
          .union(optionKeyList)
          .compact()
          .uniq()
          .value();
        updateSelectedKeys(tempSelectedPayees);
        setLoadingStatus(null);
      }
    } else {
      tempSelectedPayees = chain(selectedKeys)
        .difference(optionKeyList)
        .compact()
        .uniq()
        .value();
      updateSelectedKeys(tempSelectedPayees);
    }
  }

  function getOption(option, index, isParent = false) {
    return (
      <>
        {!initialLoad &&
        isParent &&
        index === options.length - 1 &&
        isLoading ? (
          <List.Item key="loading" className="p-0 !border-0 w-full block">
            <EverTg.Caption className="py-2.5 pl-4 text-ever-base-200">
              Loading more...
            </EverTg.Caption>
          </List.Item>
        ) : (
          <List.Item
            key={`${option.value}_${index}`}
            className={`p-0 !border-0 w-full block ${
              isParent && isArray(option) ? "grid" : "listItem"
            }`}
            style={{
              gridTemplateColumns:
                isParent && isArray(option)
                  ? `repeat(${option.length}, minmax(0, 1fr))`
                  : "auto",
            }}
          >
            {isArray(option) ? (
              option.map((opt, optIndex) =>
                getOption(opt, `${optIndex}_${index}`)
              )
            ) : listItemRenderer ? (
              listItemRenderer(option, index)
            ) : (
              <EverCheckbox
                key={option.value}
                value={option.value}
                checked={selectedKeys.includes(option.value)}
                onClick={handleSelect}
                className={twMerge(
                  listItemClassName,
                  "py-2.5 pl-4 flex items-end"
                )}
                showEllipsis={showEllipsis}
              >
                <EverTooltip title={option.label}>{option.label}</EverTooltip>
              </EverCheckbox>
            )}
            {/* {isParent && index === options.length - 1 && isLoading && (
        <span style={{ fontSize: 11, color: "gray", marginTop: 4 }}>
          Loading more...
        </span>
      )} */}
          </List.Item>
        )}
      </>
    );
  }

  function renderList() {
    const newOption = [...options];
    switch (type) {
      case LIST_TYPE.HIERARCHY_TREE: {
        if (!initialLoad && isLoading) {
          newOption.push({
            title: (
              <EverTg.Caption className="py-2.5 text-ever-base-200">
                Loading more...
              </EverTg.Caption>
            ),
            key: "loading",
            icon: <></>,
            checkable: false,
            disabled: true,
          });
        }

        return (
          <List onScroll={onScroll} style={{ height: newListHeight }}>
            <Tree
              className={twMerge(
                !initialLoad && isLoading ? "treeLoading" : "",
                "mt-3"
              )}
              height={newListHeight}
              checkable
              onCheck={handleSelect}
              checkedKeys={selectedKeys}
              treeData={newOption}
              selectable={false}
            />
          </List>
        );
      }
      default: {
        return (
          <>
            <EverVirtualizerDynamic
              data={newOption}
              columns={1}
              listHeightClass={listHeightClass}
              height={newListHeight}
              itemHeight={listItemHeight}
              getOption={getOption}
              onScroll={onScroll}
              enableScrollShadow={enableScrollShadow}
            />
            {/*<VirtualList
                data={newOption}
                height={newListHeight}
                itemHeight={listItemHeight}
                itemKey="id"
                ref={listRef}
              >
                {(option, index) => getOption(option, index, true)}
              </VirtualList>*/}
          </>
        );
      }
    }
  }

  const isInitialOrSelectAll =
    (initialLoad && isLoading) || loadingStatus === "selectAll";

  return (
    <>
      {showSearch && (
        <div className="px-2">
          <EverInput.Search
            size="small"
            placeholder={searchPlaceholder}
            onChange={(e) =>
              !(initialLoad && isLoading) && onSearch(e.target.value)
            }
            onKeyDown={handleSearchInputOnKeyDown}
            value={searchValue}
            disabled={loadingStatus === "selectAll"}
            className="mb-1"
            allowClear
            autoFocus
          />
        </div>
      )}
      {options?.length !== 0 || isLoading ? (
        <>
          {!skipSelectAll && (
            <EverCheckbox
              checked={checkboxState.allSelected}
              indeterminate={checkboxState.isIndeterminate}
              onChange={(event) => handleSelectAll(event)}
              className={twMerge("px-4 my-2", listHeaderClassName)}
              disabled={isLoading || isSearchInProgress}
              label="Select All"
            />
          )}
          {isSearchInProgress || isInitialOrSelectAll ? (
            <List onScroll={onScroll} style={{ height: newListHeight }}>
              <List.Item
                key={-1}
                disabled
                className="flex justify-center items-center !h-full"
              >
                <Spin className="flex justify-center items-center h-full" />
              </List.Item>
            </List>
          ) : (
            renderList()
          )}
        </>
      ) : (
        <EverTg.Text
          className="flex items-center justify-center text-ever-base-content font-medium w-full"
          style={{ height: `${newListHeight}px` }}
        >
          {noDataText}
        </EverTg.Text>
      )}
    </>
  );
});
