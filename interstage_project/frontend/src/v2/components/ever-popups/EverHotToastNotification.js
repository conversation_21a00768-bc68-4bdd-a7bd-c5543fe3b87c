import { XCloseIcon } from "@everstage/evericons/outlined";
import {
  XCircleIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  InfoCircleIcon,
} from "@everstage/evericons/solid";
import { cva } from "class-variance-authority";
import { PropTypes } from "prop-types";
import React from "react";
import toast from "react-hot-toast";
import { twMerge } from "tailwind-merge";

import { EverTg } from "~/v2/components";

import { EverLoader } from "../ever-loader";

/**
 * This is the EverBanner component that can be used with react-hot-toast.
 *
 *  @param {string} type type of the popup. can be ["success", "warning", "info", "loading", "error"]
 *  @param {string} title Title for the popup
 *  @param {React.ReactNode} description description for the popup
 *  @param {string} toastId ToastId provided by react-hot-toast
 *  @param {boolean} showClose Boolean value to render close button or not. Default is true
 *  @param {React.ReactNode} customIcon custom icon for the notification
 * @returns {React.ReactNode} Banner Component.
 */

EverHotToastNotification.propTypes = {
  type: PropTypes.oneOf(["success", "error", "warning", "loading", "info"]),
  description: PropTypes.element,
  title: PropTypes.string,
  toastId: PropTypes.string,
  onClose: PropTypes.func,
  showClose: PropTypes.bool,
  customIcon: PropTypes.element,
};

const siderCVA = cva(["h-12", "w-1.5", "rounded-r"], {
  variants: {
    type: {
      warning: ["bg-ever-warning-hover"],
      success: ["bg-ever-success"],
      info: ["bg-ever-primary"],
      error: ["bg-ever-error-hover"],
      loading: ["bg-ever-primary-hover"],
    },
  },
});
export function EverHotToastNotification({
  type,
  title,
  description,
  toastId,
  onClose,
  className,
  showClose = true,
  customIcon,
}) {
  let typeIcon;
  switch (type) {
    case "warning": {
      typeIcon = (
        <AlertCircleIcon
          className={`h-6 w-6 self-center text-ever-warning-hover`}
        />
      );
      break;
    }
    case "success": {
      typeIcon = (
        <CheckCircleIcon className={`h-6 w-6 self-center text-ever-success`} />
      );
      break;
    }
    case "info": {
      typeIcon = (
        <InfoCircleIcon className={`h-6 w-6 self-center text-ever-primary`} />
      );
      break;
    }
    case "error": {
      typeIcon = (
        <XCircleIcon className={`h-6 w-6 self-center text-ever-error-hover`} />
      );
      break;
    }
    case "loading": {
      typeIcon = <EverLoader.SpinnerLottie className={`h-8 w-8 self-center`} />;
      break;
    }
    default: {
      typeIcon = (
        <CheckCircleIcon className={`h-6 w-6 self-center text-ever-success`} />
      );
      break;
    }
  }
  if (customIcon) {
    typeIcon = customIcon;
  }

  return (
    <div
      className={twMerge(
        "flex items-center shadow-lg bg-ever-base w-96 min-h-20 rounded-lg justify-between border border-solid border-ever-base-ring py-2",
        className
      )}
    >
      <div className="flex gap-4 items-center">
        <div className="flex gap-4 items-center">
          <div className={siderCVA({ type: type })} />
          {typeIcon}
        </div>
        <div className="self-center py-1 w-72">
          <div className="text-ever-base-content break-words">{title}</div>
          <EverTg.Caption className="text-ever-base-content-mid break-words">
            {description}
          </EverTg.Caption>
        </div>
      </div>
      {showClose && (
        <button className="p-0 w-4 h-4 self-start mt-2 mr-2 border-0 bg-ever-base">
          <XCloseIcon
            onClick={() => {
              onClose && onClose();
              toast.remove(toastId);
            }}
            className="w-4 h-4 cursor-pointer text-ever-base-content-mid"
          />
        </button>
      )}
    </div>
  );
}
