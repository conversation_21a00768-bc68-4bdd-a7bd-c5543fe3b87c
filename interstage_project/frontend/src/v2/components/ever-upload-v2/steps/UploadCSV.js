import CloseOutlined from "@ant-design/icons/CloseOutlined";
import { FilePlusIcon } from "@everstage/evericons/solid";
import { useSpring, animated } from "@react-spring/web";
import { Row, Col, Upload } from "antd";
import React, { useState, useEffect, useRef } from "react";
import { read, utils, writeFile } from "xlsx";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverBadge } from "~/v2/components";

import { EverButton } from "../../ever-button/EverButton";
import { EverHotToastMessage, toast } from "../../ever-popups";
import { EverTooltip } from "../../EverTooltip";
import { EverTg } from "../../EverTypography";
const { Dragger } = Upload;
const convertToJSON = (workbook) => {
  let result = {};
  for (const sheetName of workbook.SheetNames) {
    let roa = utils.sheet_to_json(workbook.Sheets[sheetName], {
      header: 1,
      // raw: false,
      // dateNF:'mmm yyyy',
    });
    if (roa.length > 0) result[sheetName] = roa;
  }

  return JSON.stringify(result, 2, 2);
};

const processExcelSheet = (data) => {
  const workbook = read(data, {
    type: "binary",
    // cellText: true,
    // cellDates: true,
    raw: true,
  });

  const jsdata = convertToJSON(workbook);

  return jsdata;
};

const UploadCSV = ({
  fileData,
  fieldDefs,
  onUploadCSV,
  instructions,
  onResetFile,
  templateData,
  disabled,
  statementPeriod,
}) => {
  const { accessToken } = useAuthStore();

  const [showInstructions, setShowInstructions] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const contentRef = useRef(null);

  const draggerProps = {
    name: "file",
    accept: ".csv",
    multiple: false,
    showUploadList: false,
    onChange(info) {
      const { status } = info.file;
      if (status !== "removed") {
        let reader = new FileReader();
        // eslint-disable-next-line unicorn/prefer-blob-reading-methods
        reader.readAsText(info.file);
        reader.addEventListener("load", function () {
          const processFile = processExcelSheet(reader.result);
          const contents = JSON.parse(processFile);
          const headers = contents[Object.keys(contents)]
            .splice(0, 1)
            .flat()
            .map((head) => head.trim());
          const rowItems = contents[Object.keys(contents)].map((item) => {
            let obj = {};
            item.map((ele, i) => {
              obj[headers[i]] = ele ? ele.trim() : "";
            });
            return obj;
          });

          const records = rowItems.filter(
            (value) => Object.keys(value).length > 0
          );
          const valuesFromCSV = [headers, records];

          onUploadCSV(info.file.name, ...valuesFromCSV);
          toast.custom(
            (t) => (
              <EverHotToastMessage
                type="success"
                description={`${info.file.name} file read successfully.`}
                toastId={t.id}
              />
            ),
            { position: "top-center" } // This can be used to control the position of the popup.
          );
        });
      }
    },
    beforeUpload() {
      return false;
    },
    onRemove() {
      return true;
    },
  };

  // const downloadTemplate = () =>
  //   window.open(
  //     encodeURI(
  //       "data:text/csv;charset=utf-8," +
  //         fieldDefs.map((field) => field.label).join(",") +
  //         "\n" +
  //         templateData.map((row) => row.join(",")).join("\n")
  //     )
  //   );

  const downloadXlsxCSV = () => {
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.DOWNLOAD_TEMPLATE, {
      [ANALYTICS_PROPERTIES.TEMPLATE_DOWNLOAD]: true,
    });
    const fields = fieldDefs.map((def) => def.label);

    const tempData = templateData.map((temp) => {
      let obj = {};
      temp.map((cur, i) => {
        obj[fields[i]] = cur;
      });
      return obj;
    });

    /* make the worksheet */
    const ws = utils.json_to_sheet(tempData);

    /* add to workbook */
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, "template");

    /* generate an XLSX file */
    writeFile(wb, "template.csv");
  };

  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.scrollHeight);
    }
  }, [showInstructions]);

  const animationProps = useSpring({
    height: showInstructions ? `${contentHeight}px` : "0px",
    opacity: showInstructions ? 1 : 0,
    overflow: "hidden",
    config: { tension: 200, friction: 20 },
  });

  return (
    <>
      {instructions ? (
        <div className="flex flex-col gap-6 bg-ever-chartColors-41 rounded-xl py-5 px-8 !my-6">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-1">
              <EverTg.Heading3 className="text-ever-base-content">
                Download Template
              </EverTg.Heading3>
              <EverTg.Description>
                You can download the attached template and use them as a
                starting point for you own file
              </EverTg.Description>
            </div>
            <div className="flex flex-wrap gap-4">
              <EverButton
                type="ghost"
                target="_blank"
                onClick={() => downloadXlsxCSV()}
                size="small"
                color="base"
                className="bg-ever-base"
              >
                Download
              </EverButton>
              <EverButton
                type="link"
                target="_blank"
                onClick={() => setShowInstructions(!showInstructions)}
                size="small"
              >
                {showInstructions ? "Show less details" : "Show more details"}
              </EverButton>
            </div>
          </div>
          <animated.div style={animationProps}>
            <div ref={contentRef}>
              <EverTg.Heading4 className="mb-2">Instruction:</EverTg.Heading4>
              <ul>
                <EverTg.Description>
                  {instructions.map((instruction, idx) => (
                    <li key={idx}>{instruction}</li>
                  ))}
                </EverTg.Description>
              </ul>
            </div>
          </animated.div>
        </div>
      ) : null}
      <div className="flex flex-nowrap mt-4">
        <EverTg.Text className="mt-3 text-sm font-semibold">
          {`Upload file (CSV) `}
        </EverTg.Text>
        {statementPeriod && (
          <div className="flex items-center">
            <EverTg.Text className="mt-3 text-sm ml-1">for period</EverTg.Text>
            <EverBadge
              className="bg-ever-chartColors-2 text-ever-chartColors-20 border-ever-chartColors-2 font-medium mt-3 ml-1"
              outlined={false}
              title={statementPeriod}
              type="base"
            />
          </div>
        )}
      </div>
      <div className="bg-ever-base-400 rounded-xl h-60 !my-4">
        <Dragger {...draggerProps} className="!rounded-xl" disabled={disabled}>
          <EverTooltip
            title={
              disabled
                ? "Unable to upload data because the statement period is not selected. Please choose the 'Statement Period' above"
                : null
            }
          >
            <div className="flex flex-col gap-6 justify-center">
              <div className="w-full flex justify-center">
                <div className="flex items-center justify-center rounded-full h-16 w-16 bg-ever-primary-lite">
                  <FilePlusIcon className="text-ever-primary w-6 h-6 " />
                </div>
              </div>
              <div>
                <div className="flex justify-center align-center">
                  <EverTg.Text className="self-center mr-1.5">
                    Drag and drop file here, or
                  </EverTg.Text>
                  <a
                    className={`self-center text-ever-primary hover:text-ever-primary-hover ${
                      disabled ? "cursor-not-allowed" : "cursor-pointer"
                    }`}
                  >
                    Browse
                  </a>
                </div>
                <EverTg.Description className="text-base text-ever-base-content-mid">
                  Supports CSV files
                </EverTg.Description>
              </div>
            </div>
          </EverTooltip>
        </Dragger>
      </div>
      {fileData && (
        <div>
          <Row className="items-center">
            <Col className="mr-2">
              <EverTg.Text>Uploaded file</EverTg.Text>
            </Col>
            <div className="bg-ever-chartColors-2 flex gap-2 rounded-2xl h-8 items-center py-2 px-5">
              <EverTg.Text>{fileData.name}</EverTg.Text>
              <CloseOutlined
                className="cursor-pointer"
                onClick={() => onResetFile()}
              />
            </div>
          </Row>
        </div>
      )}
    </>
  );
};

export default UploadCSV;

// const getValuesFromCSV = (csv) => {
//   let lines = csv.split("\n");
//   let records = [];
//   let headers = lines
//     .shift()
//     .split(",")
//     .map((header) => header.replace(/^\s+/g, ""));

//   for (let line of lines) {
//     if (line !== "") {
//       let currentLine = line.split(",");
//       let obj = {};
//       let hasValue = false;
//       for (let idx = 0; idx < headers.length; idx++) {
//         obj[headers[idx]] = currentLine[idx].replace(/^\s+/g, "");
//         if (!hasValue && obj[headers[idx]] !== "") hasValue = true;
//       }
//       if (hasValue) records.push(obj);
//     }
//   }
//   return [headers, records];
// };
