import { Row, Col, Space } from "antd";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { formatCurrencyAsNumber } from "~/Utils/CurrencyUtils";
import { EverCheckbox } from "~/v2/components";
import EverUserEmailModal from "~/v2/components/EverUserEmailModal";

import Importcompleted from "./steps/Importcompleted";
import MapColumns from "./steps/MapColumns";
import PreviewData from "./steps/PreviewData";
import UploadCSV from "./steps/UploadCSV";
import Validate from "./steps/Validate";
import { EverButton } from "../ever-button/EverButton";
import { EverHotToastMessage } from "../ever-popups";
import { EverDrawer } from "../EverDrawer";
import { EverModal } from "../EverModal";
import { EverStepper } from "../EverStepper";

/*
fieldDefs structure:
[
  {
    label: "Employee Email",
    key: "employeeEmailId",
    required: true,
  },
  {
    label: "Employee Name",
    key: "name",
    required: true,
  },
  {
    label: "Age",
    key: "age",
    required: false,
  },
]
*/
const EverUpload = ({
  title,
  instructions = null,
  fieldDefs,
  templateData,
  validateDataService,
  importDataService,
  isVisible,
  handleCloseUpload,
}) => {
  const { accessToken, email } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();

  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  /*
  Steps:
    0 - Upload CSV
    1 - Map Columns
    2 - Validate
    3 - Import Completed
  */
  const [fileData, setFileData] = useState(null);
  /*
  Example structure of fileData
  {
      name: 'file.csv',
      headers: ['email', 'name', 'age'],
      values: [{email: '<EMAIL>', name: 'Everstage User', age: 23}],
  }
  */
  const [fieldsColumnMap, setFieldsColumnMap] = useState({});
  /*
    fieldsColumnMap: {
      employeeEmailId: 'Employee Email',
      name: 'name',
      age: 'age'
    }
  */
  const [validatedData, setValidatedData] = useState(null);
  /*
  Example structure of validatedData
  {
      records: [{employeeEmailId: '<EMAIL>', name: 'Everstage User', age: 23}],
  }
  */

  const [isDataValidated, setIsDataValidated] = useState(false);
  const [proceedToImport, setProceedToImport] = useState(false);

  const [successImportCount, setSuccessImportCount] = useState(null);
  const [emailIdModalVisible, setEmailIdModalVisible] = useState(false);

  const { t } = useTranslation();

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleNext = () => {
    setCurrentStep(1);
  };

  const handleCancelProcess = () => {
    handleCloseUpload();
    setCancelModalOpen(false);
    onResetAll();
  };

  const onResetAll = () => {
    setCurrentStep(0);
    setFileData(null);
    setValidatedData(null);
    setProceedToImport(false);
    setSuccessImportCount(null);
  };

  const formatDate = (data, format = "MMM YYYY") => {
    const acceptDateFormats = [
      moment.ISO_8601,
      "MM/D/YYYY",
      "MM-D-YYYY",
      "D/MM/YYYY",
      "D-MM-YYYY",
      "DD-MMM-YYYY",
      "MMM YYYY",
      "YYYY MMM",
      "MMMM YYYY",
      "YYYY MMMM",
    ];

    if (moment(data, acceptDateFormats).isValid()) {
      return moment(data, acceptDateFormats).format(format);
    }

    return data;
  };

  const onUploadCSV = (name, headers, values) => {
    let data = {
      name,
      headers,
      values,
    };
    let columnMap = {};
    for (let field of fieldDefs) {
      for (let header of headers) {
        if (header.toLowerCase() === field.label.toLowerCase())
          columnMap[field.key] = header;
      }
    }

    setFileData(data);
    setFieldsColumnMap(columnMap);
    setCurrentStep(1);
  };

  const mapFieldWithHeader = (field, value) =>
    setFieldsColumnMap({ ...fieldsColumnMap, [field]: value });

  const onEditValue = (rawRecordId, field, newValue) => {
    setValidatedData({
      ...validatedData,
      records: validatedData.records.map((row) => {
        return row.rawRecordId == rawRecordId
          ? { ...row, [field]: newValue }
          : row;
      }),
    });
    setIsDataValidated(false);
  };

  const onPreviewData = () => {
    for (let field of fieldDefs) {
      if (
        (!(field.key in fieldsColumnMap) || !fieldsColumnMap[field.key]) &&
        field.required
      ) {
        toast.custom(
          (t) => (
            <EverHotToastMessage
              type="error"
              description={"Select all required fields."}
              toastId={t.id}
            />
          ),
          { position: "top-center" }
        );
        return;
      }
    }
    setCurrentStep(2);
  };

  const getRecordsFromFileData = () => {
    return fileData.values.map((record) => {
      let obj = {};
      for (let field of fieldDefs) {
        if (field.type === "Currency")
          obj[field.key] = formatCurrencyAsNumber(
            record[fieldsColumnMap[field.key]]
          );
        else if (field.type === "Date")
          obj[field.key] = formatDate(record[fieldsColumnMap[field.key]]);
        else obj[field.key] = record[fieldsColumnMap[field.key]];
      }
      return obj;
    });
  };

  const onValidateData = async () => {
    const response = await validateDataService(
      {
        records: getRecordsFromFileData(),
      },
      accessToken
    );
    if (response.ok) {
      const resData = await response.json();
      setValidatedData(resData);
      setIsDataValidated(true);
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="success"
            description={"Validation successful."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center", duration: 1000 }
      );
    }
  };

  const onRevalidate = async () => {
    const response = await validateDataService(
      { records: validatedData.records },
      accessToken
    );
    if (response.ok) {
      const resData = await response.json();
      setValidatedData(resData);
      setIsDataValidated(true);
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="success"
            description={"Validation successful."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    }
  };

  const getValidRecordsCount = () => {
    return validatedData?.records?.filter(
      (row) => row.errors === null || row.errors === undefined
    ).length;
  };

  const validRecordsCount = getValidRecordsCount();

  const onImportData = async () => {
    if (hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)) {
      setEmailIdModalVisible(true);
    } else {
      submitRequest(email);
    }
  };

  const handleEmailSubmit = (data) => {
    submitRequest(data?.email);
    setProceedToImport(false);
    setEmailIdModalVisible(false);
    toast.custom(
      (t) => (
        <EverHotToastMessage
          type="loading"
          description={"Upload in progress..."}
          toastId={t.id}
        />
      ),
      { position: "top-center" }
    );
  };

  const submitRequest = async (email) => {
    const response = await importDataService(
      {
        records: validatedData.records.filter(
          (row) => row.errors === null || row.errors === undefined
        ),
        file_name: fileData.name,
        error_records: validatedData.records.filter(
          (row) => row.errors !== null && row.errors !== undefined
        ),
        email_to: email,
      },
      accessToken
    );
    if (response.ok) {
      const resData = await response.json();
      setSuccessImportCount(resData.successCount);
      setCurrentStep(3);
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    }
  };

  const onDone = () => {
    handleCloseUpload();
    onResetAll();
  };

  useEffect(() => {
    if (validatedData) {
      validatedData?.records.filter(
        (row) => row.errors === null || row.errors === undefined
      ).length === 0
        ? setProceedToImport(true)
        : null;
    }
  }, [validatedData]);

  return (
    <>
      <EverModal.Confirm
        visible={cancelModalOpen}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => setCancelModalOpen(false)}
            type="ghost"
          >
            No
          </EverButton>,
          <EverButton
            key="accept"
            color="error"
            onClick={() => handleCancelProcess()}
          >
            Yes
          </EverButton>,
        ]}
        title="Your import is incomplete!"
        subtitle={t("CANCEL_QUOTA_IMPORT")}
        type="error"
      />

      <EverDrawer
        title={title}
        visible={isVisible}
        onClose={() => {
          currentStep != 0 && currentStep != 3
            ? setCancelModalOpen(true)
            : handleCloseUpload();
          onResetAll();
        }}
        closable={true}
        placement="top"
        height="95vh"
        bodyStyle={{ padding: "0px" }}
        footer={
          <Row>
            <Col span={12} className="flex text-left text-ever-base-content">
              {currentStep === 2 && validatedData && (
                <>
                  {isDataValidated ? (
                    <>
                      {validRecordsCount > 0 ? (
                        <div className="flex items-center">
                          <EverCheckbox
                            checked={proceedToImport}
                            onChange={(e) =>
                              setProceedToImport(e.target.checked)
                            }
                            label={`Proceed to import ${validRecordsCount} records and
                          email me the import status`}
                          />
                        </div>
                      ) : (
                        <span className="self-center">
                          No valid records. Please revalidate data to import.
                        </span>
                      )}
                    </>
                  ) : (
                    <span>Revalidate data to import.</span>
                  )}
                </>
              )}
            </Col>
            <Col span={12} className="text-right">
              {currentStep === 0 && (
                <Space>
                  <EverButton
                    type="text"
                    color="base"
                    onClick={() => setCancelModalOpen(true)}
                    size="medium"
                  >
                    Cancel
                  </EverButton>
                  <EverButton
                    type="filled"
                    color={fileData === null ? "base" : "primary"}
                    onClick={handleNext}
                    disabled={fileData === null}
                    size="medium"
                  >
                    Next
                  </EverButton>
                </Space>
              )}
              {currentStep === 1 && (
                <Space>
                  <EverButton
                    type="text"
                    color="base"
                    onClick={handleBack}
                    size="medium"
                  >
                    Back
                  </EverButton>
                  <EverButton
                    type="filled"
                    onClick={onPreviewData}
                    size="medium"
                  >
                    Next
                  </EverButton>
                </Space>
              )}
              {currentStep === 2 && (
                <Space>
                  {validatedData == null ? (
                    <>
                      <EverButton
                        type="text"
                        color="base"
                        onClick={handleBack}
                        size="medium"
                      >
                        Back
                      </EverButton>
                      <EverButton type="filled" onClick={onValidateData}>
                        Validate
                      </EverButton>
                    </>
                  ) : (
                    <>
                      <EverButton
                        type="text"
                        color="base"
                        onClick={() => {
                          setValidatedData(null);
                        }}
                        size="medium"
                      >
                        Back
                      </EverButton>
                      <EverButton
                        type="filled"
                        color={
                          proceedToImport === false ||
                          isDataValidated === false ||
                          validRecordsCount === 0
                            ? "base"
                            : "primary"
                        }
                        onClick={onImportData}
                        disabled={
                          proceedToImport === false ||
                          isDataValidated === false ||
                          validRecordsCount === 0
                        }
                      >
                        Import
                      </EverButton>
                      <EverUserEmailModal
                        isVisible={emailIdModalVisible}
                        onCancel={() => setEmailIdModalVisible(false)}
                        onSubmit={handleEmailSubmit}
                        label="Enter the email address of the user who needs to be notified when the job is complete."
                      />
                    </>
                  )}
                </Space>
              )}
              {currentStep === 3 && (
                <div className="flex justify-end w-full">
                  <EverButton type="filled" onClick={onDone}>
                    Done
                  </EverButton>
                </div>
              )}
            </Col>
          </Row>
        }
      >
        <div className="flex flex-nowrap items-center bg-ever-base-50 p-4 justify-center">
          <div className="w-6/12">
            <EverStepper
              steps={["Upload CSV", "Map columns", "Validate", "Import"]}
              size="small"
              current={currentStep}
            />
          </div>
        </div>
        <div className="py-2.5 px-8">
          {currentStep === 0 && (
            <UploadCSV
              fileData={fileData}
              fieldDefs={fieldDefs}
              onUploadCSV={onUploadCSV}
              instructions={instructions}
              onResetFile={onResetAll}
              templateData={templateData}
            />
          )}
          {currentStep === 1 && (
            <MapColumns
              fieldDefs={fieldDefs}
              fileData={fileData}
              fieldsColumnMap={fieldsColumnMap}
              filedOptions={fileData.headers.map((val) => ({
                label: val,
                value: val,
              }))}
              mapFieldWithHeader={mapFieldWithHeader}
              onResetFile={onResetAll}
            />
          )}
          {currentStep === 2 && (
            <>
              {validatedData == null ? (
                <PreviewData
                  fieldDefs={fieldDefs}
                  getRecords={getRecordsFromFileData}
                />
              ) : (
                <Validate
                  validatedRecords={validatedData?.records}
                  messages={validatedData?.messages}
                  fieldDefs={fieldDefs}
                  onEditValue={onEditValue}
                  onRevalidate={onRevalidate}
                />
              )}
            </>
          )}
          {currentStep === 3 && (
            <Importcompleted successImportCount={successImportCount} />
          )}
        </div>
      </EverDrawer>
    </>
  );
};

export default EverUpload;
