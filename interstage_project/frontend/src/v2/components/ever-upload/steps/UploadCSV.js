import CloseOutlined from "@ant-design/icons/CloseOutlined";
import { LayoutAltIcon } from "@everstage/evericons/outlined";
import { FilePlusIcon } from "@everstage/evericons/solid";
import { Row, Col, Upload } from "antd";
import { read, utils, writeFile } from "xlsx";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";

import { EverButton } from "../../ever-button/EverButton";
import { EverHotToastMessage, toast } from "../../ever-popups";
import { EverTg } from "../../EverTypography";

const { Dragger } = Upload;
const convertToJSON = (workbook) => {
  let result = {};
  for (const sheetName of workbook.SheetNames) {
    let roa = utils.sheet_to_json(workbook.Sheets[sheetName], {
      header: 1,
      // raw: false,
      // dateNF:'mmm yyyy',
    });
    if (roa.length > 0) result[sheetName] = roa;
  }

  return JSON.stringify(result, 2, 2);
};

const processExcelSheet = (data) => {
  const workbook = read(data, {
    type: "binary",
    // cellText: true,
    // cellDates: true,
    raw: true,
  });

  const jsdata = convertToJSON(workbook);

  return jsdata;
};

const UploadCSV = ({
  fileData,
  fieldDefs,
  onUploadCSV,
  instructions,
  onResetFile,
  templateData,
}) => {
  const { accessToken } = useAuthStore();

  const draggerProps = {
    name: "file",
    accept: ".csv",
    multiple: false,
    showUploadList: false,
    onChange(info) {
      const { status } = info.file;
      if (status !== "removed") {
        let reader = new FileReader();
        // eslint-disable-next-line unicorn/prefer-blob-reading-methods
        reader.readAsText(info.file);
        reader.addEventListener("load", function () {
          const processFile = processExcelSheet(reader.result);
          const contents = JSON.parse(processFile);
          const headers = contents[Object.keys(contents)]
            .splice(0, 1)
            .flat()
            .map((head) => head.trim());
          const rowItems = contents[Object.keys(contents)].map((item) => {
            let obj = {};
            item.map((ele, i) => {
              obj[headers[i]] = ele ? ele.trim() : "";
            });
            return obj;
          });

          const records = rowItems.filter(
            (value) => Object.keys(value).length > 0
          );
          const valuesFromCSV = [headers, records];

          onUploadCSV(info.file.name, ...valuesFromCSV);
          toast.custom(
            (t) => (
              <EverHotToastMessage
                type="success"
                description={`${info.file.name} file read successfully.`}
                toastId={t.id}
              />
            ),
            { position: "top-center" } // This can be used to control the position of the popup.
          );
        });
      }
    },
    beforeUpload() {
      return false;
    },
    onRemove() {
      return true;
    },
  };

  // const downloadTemplate = () =>
  //   window.open(
  //     encodeURI(
  //       "data:text/csv;charset=utf-8," +
  //         fieldDefs.map((field) => field.label).join(",") +
  //         "\n" +
  //         templateData.map((row) => row.join(",")).join("\n")
  //     )
  //   );

  const downloadXlsxCSV = () => {
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.DOWNLOAD_TEMPLATE, {
      [ANALYTICS_PROPERTIES.TEMPLATE_DOWNLOAD]: true,
    });
    const fields = fieldDefs.map((def) => def.label);

    const tempData = templateData.map((temp) => {
      let obj = {};
      temp.map((cur, i) => {
        obj[fields[i]] = cur;
      });
      return obj;
    });

    /* make the worksheet */
    const ws = utils.json_to_sheet(tempData);

    /* add to workbook */
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, "template");

    /* generate an XLSX file */
    writeFile(wb, "template.csv");
  };

  return (
    <Row justify="center">
      <Col span={16} justify={"center"}>
        <div className="flex flex-nowrap mt-4">
          <EverTg.Heading3 className="mt-3">
            Upload a file (CSV)
          </EverTg.Heading3>
        </div>
        <div className="bg-ever-base-400 rounded-xl h-60 !my-4">
          <Dragger {...draggerProps} className="!rounded-xl">
            <div className="flex flex-col gap-6 justify-center">
              <div className="w-full flex justify-center">
                <div className="flex items-center justify-center rounded-full h-16 w-16 bg-ever-primary-lite">
                  <FilePlusIcon className="text-ever-primary w-6 h-6 " />
                </div>
              </div>
              <div>
                <div className="flex justify-center align-center">
                  <EverTg.Text className="self-center mr-1.5">
                    Drag and drop file here, or
                  </EverTg.Text>
                  <a className="self-center text-ever-primary hover:text-ever-primary-hover">
                    Browse
                  </a>
                </div>
                <EverTg.Description className="text-base text-ever-base-content-mid">
                  Supports CSV files
                </EverTg.Description>
              </div>
            </div>
          </Dragger>
        </div>
        {fileData && (
          <div>
            <Row className="items-center">
              <Col className="mr-2">
                <EverTg.Text>Uploaded file</EverTg.Text>
              </Col>
              <div className="bg-ever-chartColors-2 flex gap-2 rounded-2xl h-8 items-center py-2 px-5">
                <EverTg.Text>{fileData.name}</EverTg.Text>
                <CloseOutlined
                  className="cursor-pointer"
                  onClick={() => onResetFile()}
                />
              </div>
            </Row>
          </div>
        )}

        {instructions && (
          <div className="flex flex-col gap-6 border border-solid border-ever-base-400 rounded-xl py-5 px-8 !my-6">
            <div className="flex justify-between">
              <div className="flex flex-col gap-1">
                <EverTg.Heading3 className="text-ever-base-content">
                  Table template example
                </EverTg.Heading3>
                <EverTg.Description>
                  To get started, download the template that includes the fields
                  in the selected object.
                </EverTg.Description>
              </div>
              <div className="flex gap-4">
                <EverButton
                  type="filled"
                  target="_blank"
                  onClick={() => downloadXlsxCSV()}
                  prependIcon={
                    <LayoutAltIcon className="text-primary-content w-4 h-4" />
                  }
                  size="small"
                >
                  Download template
                </EverButton>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <EverTg.Heading4>Instruction:</EverTg.Heading4>
              <ul>
                <EverTg.Description>
                  {instructions.map((instruction, idx) => (
                    <li key={idx}>{instruction}</li>
                  ))}
                </EverTg.Description>
              </ul>
            </div>
          </div>
        )}
      </Col>
    </Row>
  );
};

export default UploadCSV;

// const getValuesFromCSV = (csv) => {
//   let lines = csv.split("\n");
//   let records = [];
//   let headers = lines
//     .shift()
//     .split(",")
//     .map((header) => header.replace(/^\s+/g, ""));

//   for (let line of lines) {
//     if (line !== "") {
//       let currentLine = line.split(",");
//       let obj = {};
//       let hasValue = false;
//       for (let idx = 0; idx < headers.length; idx++) {
//         obj[headers[idx]] = currentLine[idx].replace(/^\s+/g, "");
//         if (!hasValue && obj[headers[idx]] !== "") hasValue = true;
//       }
//       if (hasValue) records.push(obj);
//     }
//   }
//   return [headers, records];
// };
