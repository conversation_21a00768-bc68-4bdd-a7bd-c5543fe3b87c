import {
  FunctionIcon,
  OpenParenthesisIcon,
  CloseParenthesisIcon,
} from "@everstage/evericons/outlined";
import { isString, cloneDeep } from "lodash";
import React, { useState, useCallback, useRef } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

import {
  DATATYPE,
  EXPRESSION_FUNCTION_TYPES,
  EXPRESSION_TOKEN_TYPES,
} from "~/Enums";

import { NAVIGATION_KEYS } from "./common";
import { DateToken } from "./DateToken";
import {
  handleNoLabelArg,
  prepareFunctionToken,
  //convertDateFormat,
  convertStringArrayFormat,
  convertIntArrayFormat,
  isDataSheetVariable,
  bracketColorPalette,
  isQuoteVariable,
} from "./utils";
import { EverTooltip } from "../EverTooltip";
import { EverTg } from "../EverTypography";

/**
 * This component renders the token in the expression box.
 * @param {Object} props
 * @param {string} props.className - The class name for the component.
 * @param {number} props.index - The index of the token.
 * @param {Array} props.expressionTokenRef - Collection of references to expression tokens.
 * These references are used to manipulate the DOM and control the behavior of expression tokens.
 * @param {function} props.manageAutoSuggestView - The function to manage the auto suggest view.
 * By calling these functions with a boolean argument, you can update the visibility of the auto-suggest view.
 * Signature: (boolean) => void
 * @param {function} props.onExpressionChange - callback function that is called when the expression is changed - which happens whenever a token is
 * added or removed.Signature: `onExpressionChange(type: string, currentPosition: number, selectedValue: any)`
 *   - `type`: The type of the action that triggered the change.
 *   - `currentPosition`: The position of the token in the expression.
 *   - `selectedValue`: The value to be inserted or used in the expression, depending on the action type.`
 * @param {function} props.openFunctionManagerPopover - This function is used to open the function manager popover. It sets the function content.It also sets the position of the function manager popover anchor.
 * @param {function} props.setEditPosition - The function to set the edit position. This way the ExpressionBox component knows which token is being edited.
 * @param {Object} props.currentToken - The token object. This contains all details related to the current token.
 * @param {function} props.handleSuggestionUserInputs - This function is used to handle the user inputs on the suggestions like Click,Enter,Down Arrow,Up Arrow.
 * @param {boolean} props.isReadOnly - If the expression token is read only.
 * @param {Object} props.expressionCardRef - Reference to the expressionbox container.
 * @param {number} props.selectedColorIndex - The index representing the color of the current expression token.
 * @returns {JSX.Element}
 */

export function ExpressionToken({
  className,
  index,
  manageAutoSuggestView,
  onExpressionChange,
  openFunctionManagerPopover,
  setEditPosition,
  currentToken,
  handleSuggestionUserInputs,
  isReadOnly = false,
  expressionTokenRef,
  expressionCardRef,
  selectedColorIndex,
}) {
  const dateTokenRef = useRef(null);
  /**
   * This function enters the edit mode for the token.
   * @param {number} index - The index of the token.
   * @param {string} value - The value of the token.
   * */
  function enterEditMode(index, value) {
    // if the token is a function, open the function manager popover
    if (currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
      openFunctionManagerPopover(currentToken, index);
    } else {
      if (
        currentToken.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS &&
        currentToken.token.args[0] === DATATYPE.DATE
      ) {
        onExpressionChange({
          type: NAVIGATION_KEYS.EDIT,
          currentPosition: index,
          selectedValue: {
            ...currentToken,
            isCalenderVisible: true,
          },
        });
      } else {
        if (currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.BRACKETS) {
          // if the token is not a function, then set the content editable to true
          // and set the cursor to the end of the token
          expressionTokenRef.current[index].contentEditable = true;
          const range = document.createRange();
          const sel = window.getSelection();
          range.selectNodeContents(expressionTokenRef.current[index]);
          range.collapse(false);
          sel?.removeAllRanges();
          sel?.addRange(range);
          onExpressionChange({
            type: NAVIGATION_KEYS.FOCUS,
            currentPosition: index,
            selectedValue: value,
          });
          manageAutoSuggestView(true);
        } // important to set this to true, otherwise the autosuggest dropdown will not show up if closed with escape key
      }
    }
  }

  const [isFocused, setIsFocused] = useState(false);

  const handleDateToken = () => {
    const clonedToken = cloneDeep(currentToken);

    // check if the dateToken has been set
    if (dateTokenRef.current) {
      // delete the isCalenderVisible node from token
      if (Object.getOwnPropertyDescriptor(clonedToken, "isCalenderVisible")) {
        delete clonedToken.isCalenderVisible;
      }
      // delete the mockToken node from token
      if (Object.getOwnPropertyDescriptor(clonedToken, "mockToken")) {
        delete clonedToken.mockToken;
      }
      // update the token with the value of dateTokenRef
      onExpressionChange({
        type: NAVIGATION_KEYS.EDIT,
        currentPosition: index,
        selectedValue: {
          ...clonedToken,
          token: {
            ...clonedToken.token,
            args: ["Date", dateTokenRef.current],
            key: dateTokenRef.current,
            name: (dateTokenRef.current || "")?.replace(/-/g, "/"),
          },
        },
      });

      // reset the dateTokenRef to null
      dateTokenRef.current = null;
    } else {
      // if the token has a mockToken, then delete it
      if (clonedToken.mockToken) {
        onExpressionChange({
          type: NAVIGATION_KEYS.DELETE,
          currentPosition: index + 1,
        });
      } else {
        // if the token has a isCalenderVisible node, then delete it
        if (Object.getOwnPropertyDescriptor(clonedToken, "isCalenderVisible")) {
          delete clonedToken.isCalenderVisible;
        }
        // update the token with the value of clonedToken
        onExpressionChange({
          type: NAVIGATION_KEYS.EDIT,
          currentPosition: index,
          selectedValue: clonedToken,
        });
      }
    }
  };

  /**
   * This function convert and display token name for following tokens.
   * 1. Date
   * 2. StringArray
   * 3. IntArray
   * @returns {string}
   */
  const renderNonFunctionToken = useCallback(() => {
    if (currentToken.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS) {
      if (currentToken.token.args[0] === DATATYPE.DATE) {
        return (
          <DateToken
            onExpressionChange={onExpressionChange}
            index={index}
            currentToken={currentToken}
            expressionCardRef={expressionCardRef}
            manageAutoSuggestView={manageAutoSuggestView}
            dateTokenRef={dateTokenRef}
            expressionTokenRef={expressionTokenRef}
            handleDateToken={handleDateToken}
          />
        );
      }

      if (currentToken.token.args[0] === DATATYPE.STRINGARRAY) {
        // This function converts the given array of strings into a format suitable for rendering in the UI.
        // The token value is passed to maintain compatibility, ensuring consistent behavior even if the token was created in v1.
        return convertStringArrayFormat(currentToken?.token?.args[1] ?? []);
      }
      if (currentToken.token.args[0] === DATATYPE.INTARRAY) {
        return convertIntArrayFormat(currentToken?.token?.name);
      }
    }
    if (currentToken.tokenType === EXPRESSION_TOKEN_TYPES.BRACKETS) {
      const chartColor = selectedColorIndex
        ? `${bracketColorPalette[(selectedColorIndex - 1) % 12]}`
        : "text-ever-error";
      const bracketClassName = selectedColorIndex
        ? `bracket-${selectedColorIndex}`
        : `unclosed-bracket-${uuidv4()}`;
      const handleMouseEnter = () => {
        // Get elements with the specified bracket color index from the DOM
        const elements =
          expressionCardRef.current.getElementsByClassName(bracketClassName);
        // Apply a scaling effect to each matched element
        Array.from(elements).forEach((element) => {
          element.classList.add("scale-150");
        });
      };
      const handleMouseLeave = () => {
        // Get elements with the specified bracket color index from the DOM
        const elements =
          expressionCardRef.current.getElementsByClassName(bracketClassName);
        // Remove the scaling effect from each matched element
        Array.from(elements).forEach((element) => {
          element.classList.remove("scale-150");
        });
      };
      if (currentToken.token.name === "(") {
        return (
          <OpenParenthesisIcon
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className={`${chartColor} transform ransition-transform duration-300 ${bracketClassName} w-2 h-6 `}
          />
        );
      } else {
        return (
          <CloseParenthesisIcon
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className={`transform ransition-transform duration-300 ${chartColor} ${bracketClassName} w-2 h-6`}
          />
        );
      }
    }
    // trim the token while rendering, because we will show the leading and
    // trailing spaces with mock space squares instead of actual spaces

    return isString(currentToken.token.name)
      ? currentToken.token.name.trim()
      : currentToken.token.name;
  }, [currentToken, dateTokenRef, expressionCardRef]);

  /**
   * This function handles the key up event for the token.
   * @param {Object} event - The event object.
   * */
  const handleKeyUp = (event) => {
    // if the token is read only, then no need to trigger the event
    if (currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.BRACKETS) {
      // When adding multiple continuous space characters, nbsp characters are added instead of breaking space
      // hence replace them with normal space character
      const value = event.target.textContent.replace(/\xA0/g, " ");
      if (currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
        // if its a function no need to do any handleKeyUp event
        onExpressionChange({
          type: NAVIGATION_KEYS.FOCUS,
          currentPosition: index,
          selectedValue: value,
        });
        manageAutoSuggestView(true); // important to set this to true, otherwise the autosuggest dropdown will not show up if closed with escape key
      }

      if (
        event.key === "Enter" &&
        (expressionTokenRef.current[index]?.contentEditable === "false" ||
          (expressionTokenRef.current[index]?.contentEditable === "inherit" &&
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS))
      ) {
        enterEditMode(index, value);
      } else if (
        event.key === "Enter" &&
        expressionTokenRef.current[index]?.contentEditable === "true"
      ) {
        expressionTokenRef.current[index].contentEditable = false;
        handleSuggestionUserInputs("ENTER", index);
      }
      if (event.key === "ArrowDown") {
        handleSuggestionUserInputs("DOWN");
      }
      if (event.key === "ArrowUp") {
        handleSuggestionUserInputs("UP");
      }
      if (event.key === "Escape") {
        document?.activeElement?.blur();
        manageAutoSuggestView(false);
        if (currentToken.isCalenderVisible) {
          const nextIndex = currentToken?.mockToken ? index : index + 1;
          setTimeout(() => {
            if (expressionCardRef.current) {
              expressionCardRef.current
                .querySelectorAll("input")
                [nextIndex].focus(); // once token added focus on the conrresponding input
            }
          });
        }
      }
    }
  };

  /**
   * This function handles the blur event for the token.
   * */
  const handleBlur = () => {
    if (currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.BRACKETS) {
      expressionTokenRef.current[index].contentEditable = false;
      if (
        currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.FUNCTIONS &&
        !(
          currentToken.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS &&
          currentToken.token.args[0] === DATATYPE.DATE
        )
      ) {
        expressionTokenRef.current[index].textContent =
          renderNonFunctionToken();
      }
      // if the token is a date token, then handle the date token
      if (currentToken.isCalenderVisible) {
        handleDateToken();
      }
      setIsFocused(false);
    }
  };

  /**
   * This function renders the token.
   * @param {Object} currentToken - The token object. This contains all details related to the token.
   * @returns {JSX.Element | undefined}
   * */
  const renderToken = (currentToken) => {
    if (currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
      return <FunctionToken currentToken={currentToken} />;
    }

    return renderNonFunctionToken();
  };

  /**
   * Calculate the count of leading and trailing spaces
   * this count will be used to render the mock space squares
   */
  let countOfLeadingSpaces = 0;
  let countOfTrailingSpaces = 0;
  if (
    currentToken.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS &&
    currentToken.token.args[0] === DATATYPE.STRING
  ) {
    // if its a string and has trailing or leading spaces
    if (
      currentToken.token.name.startsWith(" ") ||
      currentToken.token.name.endsWith(" ")
    ) {
      countOfLeadingSpaces = currentToken.token.name.match(/^ +/)?.[0].length;
      countOfTrailingSpaces = currentToken.token.name.match(/ +$/)?.[0].length;
    }
  }

  return (
    <EverTooltip
      title={
        currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS &&
        !isReadOnly &&
        `${isFocused ? "Press enter" : "Click"} to edit`
      }
      placement="top"
      trigger={["hover", "focus"]}
    >
      <div
        className={twMerge(
          "relative",
          isReadOnly ? "flex-auto max-w-fit h-7 " : "min-h-7 h-auto max-w-full "
        )}
      >
        {
          // if there are leading spaces, then render the mock space squares
          countOfLeadingSpaces > 0 &&
            !isFocused &&
            [...Array(countOfLeadingSpaces).keys()].map((i) => (
              <span
                key={i}
                className="inline-block w-1.5 h-4 bg-ever-chartColors-55/25 absolute top-1/2 -translate-y-1/2 pointer-events-none"
                style={{ left: `${(i + 1) * 7}px` }}
              ></span>
            ))
        }
        <div
          key={index}
          ref={(ref) => {
            // Check if the `ref` is not null (i.e., the element exists in the DOM)
            // and if the `expressionTokenRef` at the current `index` is not already set to this `ref`.
            if (ref != null && expressionTokenRef?.current[index] !== ref) {
              // If the conditions are met, set the `expressionTokenRef` at the current `index` to this `ref`.
              expressionTokenRef.current[index] = ref;
            }
          }}
          tabIndex={isReadOnly ? -1 : 0}
          className={twMerge(
            "expression-token bg-ever-base-200 rounded-md px-2 py-1 leading-4 break-all flex items-center focus:outline-none focus:border-dashed focus:relative cursor-pointer border border-solid  focus:border-[1.5px] font-medium h-full w-full break-all",
            !isReadOnly && "focus:border-ever-primary ",
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS &&
              "bg-ever-chartColors-53/[0.12] border-ever-chartColors-53/10 py-1  hover:border-ever-chartColors-53",
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS &&
              currentToken.token.functionName === "addDate" &&
              "bg-ever-chartColors-55/20 !border-ever-chartColors-55 py-1  hover:border-ever-chartColors-55",
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.OPERATORS &&
              "text-ever-base-content font-medium border-ever-base-400 bg-ever-base-50 min-w-[28px] justify-center  hover:border-ever-base-content-low hover:bg-ever-base-25 focus:ring focus:ring-ever-base-ring focus:border-ever-base-content-low focus:border-2  focus:border-solid",
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.BRACKETS &&
              "bg-ever-base p-0  border-transparent focus:border-0",
            currentToken.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS &&
              "bg-ever-chartColors-55/[0.12] border-ever-chartColors-55/10  hover:border-solid hover:border-ever-chartColors-55 text-ever-chartColors-55  focus:border-ever-chartColors-55 ",
            (isDataSheetVariable(currentToken.tokenType) ||
              isQuoteVariable(currentToken.tokenType)) &&
              "bg-ever-chartColors-54/[0.12] border-ever-chartColors-54/10  hover:border-solid hover:border-ever-chartColors-54 text-ever-chartColors-54  focus:border-ever-chartColors-54 ",
            currentToken.tokenType ===
              EXPRESSION_TOKEN_TYPES.CLIENT_VARIABLES &&
              "bg-ever-chartColors-43/[0.12] border-ever-chartColors-43/10  hover:border-solid hover:border-ever-chartColors-43 text-ever-chartColors-43   focus:border-ever-chartColors-43 ",
            currentToken.tokenType ===
              EXPRESSION_TOKEN_TYPES.USER_DEFINED_FUNCTIONS &&
              "bg-ever-chartColors-43/[0.12] border-ever-chartColors-43/10  hover:border-solid hover:border-ever-chartColors-43 text-ever-chartColors-43   focus:border-ever-chartColors-43 ",
            (currentToken.editMode || currentToken.mockToken) &&
              "border-[1.5px] border-dashed border-ever-chartColors-53 focus:ring-0",
            (isReadOnly || currentToken?.delimeter) && "pointer-events-none",
            isFocused
              ? "whitespace-pre overflow-scroll h-8"
              : "whitespace-normal",
            className
          )}
          onKeyUp={handleKeyUp}
          onBlur={handleBlur}
          onFocus={(event) => {
            let value = event?.target?.textContent.trim();
            // while in focus we have to add the leading and trailing spaces back to the token
            // as we cannot show the mock space squares while in focus
            if (countOfLeadingSpaces > 0) {
              // add leading spaces back to the token
              value = `${" ".repeat(countOfLeadingSpaces)}${value}`;
              event.target.textContent = value;
            }
            if (countOfTrailingSpaces > 0) {
              // add trailing spaces back to the token
              value = `${value}${" ".repeat(countOfTrailingSpaces)}`;
              event.target.textContent = value;
            }
            setEditPosition(index);
            setIsFocused(true);
            if (currentToken.tokenType !== EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
              // @ts-ignore
              enterEditMode(index, value);
            }
            event.preventDefault();
          }}
          onKeyDownCapture={(event) => {
            if (event.key === "ArrowDown" || event.key === "ArrowUp") {
              event.preventDefault();
            }
          }}
          onClick={(event) => {
            if (currentToken.tokenType == EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
              // @ts-ignore
              const value = event?.target?.textContent;
              enterEditMode(index, value);
            }
            event.preventDefault();
          }}
          style={{
            // if there is leading spaces, then add padding left to the token for better visibility for mock space squares
            paddingLeft:
              countOfLeadingSpaces > 0 && !isFocused
                ? `${countOfLeadingSpaces * 7 + 8}px`
                : "",
            // if there is trailing spaces, then add padding right to the token for better visibility for mock space squares
            paddingRight:
              countOfTrailingSpaces > 0 && !isFocused
                ? `${countOfTrailingSpaces * 7 + 8}px`
                : "",
          }}
        >
          {renderToken(currentToken)}
        </div>
        {
          // if there are trailing spaces, then render the mock space squares
          countOfTrailingSpaces > 0 &&
            !isFocused &&
            [...Array(countOfTrailingSpaces).keys()].map((i) => (
              <span
                key={i}
                className="inline-block w-1.5 h-4 bg-ever-chartColors-55/25 absolute top-1/2 -translate-y-1/2 pointer-events-none"
                style={{ right: `${(i + 1) * 7}px` }}
              ></span>
            ))
        }
      </div>
    </EverTooltip>
  );
}

// This function renders the function token
function FunctionToken({ currentToken }) {
  const { t } = useTranslation();
  const currentFunctionToken = prepareFunctionToken(currentToken);

  function getFunctionDisplayName(functionName) {
    switch (functionName) {
      case EXPRESSION_FUNCTION_TYPES.Quota: {
        return t("QUOTA");
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaErosion: {
        return t("QUOTA_EROSION");
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaAttainment: {
        return t("QUOTAATTAINTMENT");
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-Quota"]: {
        return `TEAM-${t("QUOTA")}`;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"]: {
        return `TEAM-${t("QUOTA_EROSION")}`;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"]: {
        return `TEAM-${t("QUOTAATTAINTMENT")}`;
      }
      case EXPRESSION_FUNCTION_TYPES.Hierarchy: {
        return "GenerateHierarchy";
      }
      case EXPRESSION_FUNCTION_TYPES.Rolling: {
        return "RollingSum";
      }
      case EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy: {
        return "GetValueFromHierarchy";
      }
      case EXPRESSION_FUNCTION_TYPES.Timezone: {
        return "ConvertTimezone";
      }
      default: {
        return functionName;
      }
    }
  }

  return (
    <div className="flex items-center gap-1 truncate">
      <FunctionIcon className="w-4 h-4  text-ever-base-content-mid shrink-0" />
      <EverTg.SubHeading4 className="m-0 text-ever-chartColors-53 leading-3 truncate max-w-3xl ">
        {[
          EXPRESSION_FUNCTION_TYPES.StartDate,
          EXPRESSION_FUNCTION_TYPES.LastDate,
          EXPRESSION_FUNCTION_TYPES.DATEDIFF,
          EXPRESSION_FUNCTION_TYPES.GET_DATE,
          EXPRESSION_FUNCTION_TYPES.DateIsIn,
        ].includes(currentFunctionToken.token.functionName) ? (
          currentFunctionToken.token.name
        ) : (
          <>
            <span>
              {[
                EXPRESSION_FUNCTION_TYPES.Quota,
                EXPRESSION_FUNCTION_TYPES.QuotaErosion,
                EXPRESSION_FUNCTION_TYPES.QuotaAttainment,
                EXPRESSION_FUNCTION_TYPES["TEAM-Quota"],
                EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"],
                EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"],
                EXPRESSION_FUNCTION_TYPES.Hierarchy,
                EXPRESSION_FUNCTION_TYPES.Rolling,
                EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy,
                EXPRESSION_FUNCTION_TYPES.Timezone,
              ].includes(currentFunctionToken.token.functionName)
                ? getFunctionDisplayName(
                    currentFunctionToken.token.functionName
                  )
                : currentFunctionToken.token.functionName}
            </span>
            <span>
              (
              {currentFunctionToken.token.displayArgs
                .filter((x) => (!Array.isArray(x) && x) || x?.length > 0)
                .map((arg, i) => (
                  <span key={i}>
                    {i !== 0 && arg !== undefined && arg !== null && (
                      <span>, </span>
                    )}
                    <EverTg.Description className="text-ever-chartColors-53">
                      {arg?.token?.name
                        ? arg?.token?.name == "Primary"
                          ? arg?.token?.name + " " + t("QUOTA")
                          : arg?.token?.name
                        : handleNoLabelArg(arg, currentFunctionToken)}
                    </EverTg.Description>
                  </span>
                ))}
              )
            </span>
          </>
        )}
      </EverTg.SubHeading4>
    </div>
  );
}
