import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  EXPRESSION_FUNCTION_TYPES,
  LEARN_MORE_FOR_EXPRESSION_BOX_FUNCTIONS,
} from "~/Enums";
import { EverTg } from "~/v2/components";

import { ApplyAndCancelButton } from "./ApplyAndCancelButtons";
import { AvgFunctionManager } from "./function-managers/AvgFunctionManager";
import { CoalesceFunctionManager } from "./function-managers/CoalesceFunctionManager";
import { ConcatFunctionManager } from "./function-managers/ConcatFunctionManager";
import { ConfigFunctionManager } from "./function-managers/ConfigFunctionManager";
import { ContainsFunctionManager } from "./function-managers/ContainsFunctionManager";
import { CountIfFunctionManager } from "./function-managers/CountIfFunctionManager";
import { CountNotNullFunctionManager } from "./function-managers/CountNotNullFunctionManager";
import { CurrentPayoutPeriodFunctionManager } from "./function-managers/CurrentPayoutPeriodFunctionManager";
import { DateAddFunctionManager } from "./function-managers/DateAddFunctionManager";
import { DateDiffFunctionManager } from "./function-managers/DateDiffFunctionManager";
import { DateIsInFunctionManager } from "./function-managers/DateIsInFunctionManager";
import { DistinctCountManager } from "./function-managers/DistinctCountManager";
import { EndsWithFunctionManager } from "./function-managers/EndsWithFunctionManager";
import { FindFunctionManager } from "./function-managers/FindFunctionManager";
import { GenerateHierarchyFunctionManager } from "./function-managers/GenerateHierarchyManager.js";
import { GenerateHierarchyFunctionV2Manager } from "./function-managers/GenerateHierarchyV2Manager.js";
import { GetDateManager } from "./function-managers/GetDateManager";
import { GetUserPropertyManager } from "./function-managers/GetUserPropertyFunctionManager";
import { GetValueFromHierarchyFunctionManager } from "./function-managers/GetValueFromHierarchyFunctionManager";
import { IsEmptyFunctionManager } from "./function-managers/IsEmptyFunctionManager";
import { IsNotEmptyFunctionManager } from "./function-managers/IsNotEmptyFunctionManager";
import { LeftFunctionManager } from "./function-managers/LeftFunctionManager";
import { LenFunctionManager } from "./function-managers/LenFunctionManager";
import { LowerFunctionManager } from "./function-managers/LowerFunctionManager";
import { MaxFunctionManager } from "./function-managers/MaxFunctionManager";
import { MidFunctionManager } from "./function-managers/MidFunctionManager";
import { MinFunctionManager } from "./function-managers/MinFunctionManager";
import { NotContainsFunctionManager } from "./function-managers/NotContainsFunctionManager";
import { QuotaAttainmentFunctionManager } from "./function-managers/QuotaAttainmentFunctionManager";
import { QuotaErosionFunctionManager } from "./function-managers/QuotaErosionFunctionManager";
import { QuotaFunctionManager } from "./function-managers/QuotaFunctionManager";
import { RankFunctionManager } from "./function-managers/RankFunctionManager";
import { RightFunctionManager } from "./function-managers/RightFunctionManager";
import { RollingSumFunctionManager } from "./function-managers/RollingSumFunctionManager";
import { RoundDownFunctionManager } from "./function-managers/RoundDownFunctionManager";
import { RoundFunctionManager } from "./function-managers/RoundFunctionManager";
import { RoundUpFunctionManager } from "./function-managers/RoundUpFunctionManager";
import { StartDateFunctionManager } from "./function-managers/StartDateFunctionManager";
import { StartsWithFunctionManager } from "./function-managers/StartsWithFunctionManager.js";
import { SumFunctionManager } from "./function-managers/SumFunctionManager";
import { SumIfFunctionManager } from "./function-managers/SumIfFunctionManager";
import { TeamCountFunctionManager } from "./function-managers/TeamCountFuncionManager";
import { TierFunctionManager } from "./function-managers/TierFunctionManager.js";
import { TimezoneFunctionManager } from "./function-managers/TimezoneFunctionManager";
import { TrimFunctionManager } from "./function-managers/TrimFunctionManager";
import { convertToToken } from "./utils";

/**
 * This component is used to manage the function manager popover. It takes the following props:
 * @param {Object} props
 * @param {Object} props.functionContent - This holds the meta data for the currently selected function.
 * @param {Array} props.functionArgs - This holds the list of arguments for the currently edited function.It is added to AST.
 * @param {Function} props.setFunctionArgs - The function to set the function args object
 * @param {Function} props.closeFunctionManagerPopover - The function to close the function manager popover,if pass argument as cancel then it will close the function popover.
 * @param {import("../types/expression-box-types").AutoCompleteContextType} props.fullAutoSuggestionList - The complete array of suggestions obtained from the autocomplete response API.
 *   This array includes all available suggestions without any filtering.
 * @param {Function} props.setFunctionContent - The function to set the function content object
 * @param {Object} props.targetedFunctionConfig - Configuration for targeted functions. If specific values need to be sent to a particular function, pass an object like {"generateHierarchy": {datasheetId: "123", databookId: "33"}}.
 *  @param {import("../types/expression-box-types").AutoCompleteContextType} props.autoCompleteResponse - The filtered array of suggestions passed to the expression box state.
 *   This array represents the suggestions that are currently visible or relevant in the expression box.
 * @param {string} props.planType - The type of plan
 * @returns {JSX.Element}
 */
export function ManageFunction({
  functionContent,
  functionArgs,
  setFunctionArgs,
  closeFunctionManagerPopover,
  fullAutoSuggestionList,
  targetedFunctionConfig,
  setFunctionContent,
  autoCompleteResponse,
  planType,
}) {
  const { t } = useTranslation();
  const [disableApply, setDisableApply] = useState(true);

  // this is the list of options that will be passed to the function manager components
  const options = fullAutoSuggestionList.map((o) => ({
    label: o.label,
    value: o.label,
    dataType: o.meta?.dataType,
    token: convertToToken(o),
  }));

  function getFunctionDisplayName() {
    let functionName = functionContent?.value?.token.functionName;

    switch (functionName) {
      case EXPRESSION_FUNCTION_TYPES.Quota: {
        return t("QUOTA");
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaErosion: {
        return t("QUOTA_EROSION");
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaAttainment: {
        return t("QUOTAATTAINTMENT");
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-Quota"]: {
        return `TEAM-${t("QUOTA")}`;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"]: {
        return `TEAM-${t("QUOTA_EROSION")}`;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"]: {
        return `TEAM-${t("QUOTAATTAINTMENT")}`;
      }
      case EXPRESSION_FUNCTION_TYPES.Hierarchy: {
        return "GenerateHierarchy";
      }
      case EXPRESSION_FUNCTION_TYPES.Rolling: {
        return "RollingSum";
      }
      case EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy: {
        return "GetValueFromHierarchy";
      }
      default: {
        return functionName;
      }
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row items-center justify-between">
        <EverTg.Heading4 className="text-ever-base-content">
          {[
            EXPRESSION_FUNCTION_TYPES.Quota,
            EXPRESSION_FUNCTION_TYPES.QuotaErosion,
            EXPRESSION_FUNCTION_TYPES.QuotaAttainment,
            EXPRESSION_FUNCTION_TYPES["TEAM-Quota"],
            EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"],
            EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"],
            EXPRESSION_FUNCTION_TYPES.Hierarchy,
            EXPRESSION_FUNCTION_TYPES.Rolling,
            EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy,
          ].includes(functionContent?.value?.token.functionName)
            ? getFunctionDisplayName()
            : functionContent?.value?.token.functionName}
        </EverTg.Heading4>
        {targetedFunctionConfig[
          LEARN_MORE_FOR_EXPRESSION_BOX_FUNCTIONS
        ]?.showLearnMoreForFunctions?.(
          functionContent?.value?.token.functionName
        ) ?? null}
      </div>
      <div>
        {(EXPRESSION_FUNCTION_TYPES.TieredValue ===
          functionContent.value?.token.functionName ||
          EXPRESSION_FUNCTION_TYPES.TieredPercentage ===
            functionContent.value?.token.functionName) && (
          <TierFunctionManager
            functionContent={functionContent}
            setFunctionArgs={setFunctionArgs}
            functionArgs={functionArgs}
            setDisableApply={setDisableApply}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.SUM,
          EXPRESSION_FUNCTION_TYPES["TEAM-SUM"],
        ].includes(functionContent.value?.token.functionName) && (
          <SumFunctionManager
            options={options}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.DISTINCT_COUNT,
          EXPRESSION_FUNCTION_TYPES["TEAM-DISTINCT-COUNT"],
        ].includes(functionContent.value?.token.functionName) && (
          <DistinctCountManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.GET_DATE ===
          functionContent.value?.token.functionName && (
          <GetDateManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            setFunctionContent={setFunctionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.GetUserProperty ===
          functionContent.value?.token.functionName && (
          <GetUserPropertyManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            setFunctionContent={setFunctionContent}
            options={options}
            setDisableApply={setDisableApply}
            isSummationMode={
              targetedFunctionConfig[EXPRESSION_FUNCTION_TYPES.GetUserProperty]
                ?.isSummationMode ?? false
            }
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.MIN,
          EXPRESSION_FUNCTION_TYPES["TEAM-MIN"],
        ].includes(functionContent.value?.token.functionName) && (
          <MinFunctionManager
            options={options}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.MAX,
          EXPRESSION_FUNCTION_TYPES["TEAM-MAX"],
        ].includes(functionContent.value?.token.functionName) && (
          <MaxFunctionManager
            options={options}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.AVG,
          EXPRESSION_FUNCTION_TYPES["TEAM-AVG"],
        ].includes(functionContent.value?.token.functionName) && (
          <AvgFunctionManager
            options={options}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Config ===
          functionContent.value?.token.functionName && (
          <ConfigFunctionManager
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            commissionPlanId={
              targetedFunctionConfig[EXPRESSION_FUNCTION_TYPES.Config]
                ?.commissionPlanId || ""
            }
            setFunctionContent={setFunctionContent}
            planType={planType}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.QuotaAttainment,
          EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"],
        ].includes(functionContent.value?.token.functionName) && (
          <QuotaAttainmentFunctionManager
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            functionContent={functionContent}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.QuotaErosion,
          EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"],
        ].includes(functionContent.value?.token.functionName) && (
          <QuotaErosionFunctionManager
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            functionContent={functionContent}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.Quota,
          EXPRESSION_FUNCTION_TYPES["TEAM-Quota"],
        ].includes(functionContent.value?.token.functionName) && (
          <QuotaFunctionManager
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionArgs={functionArgs}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.DATEDIFF ===
          functionContent.value?.token.functionName && (
          <DateDiffFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            setFunctionContent={setFunctionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.SUMIF,
          EXPRESSION_FUNCTION_TYPES["TEAM-SUMIF"],
        ].includes(functionContent.value?.token.functionName) && (
          <SumIfFunctionManager
            functionArgs={functionArgs}
            options={options}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
            autoCompleteResponse={autoCompleteResponse}
            targetedFunctionConfig={targetedFunctionConfig}
            fullAutoSuggestionList={fullAutoSuggestionList}
            // if summation mode is enabled, then return only client variables
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.COUNTIF,
          EXPRESSION_FUNCTION_TYPES["TEAM-COUNTIF"],
        ].includes(functionContent.value?.token.functionName) && (
          <CountIfFunctionManager
            functionArgs={functionArgs}
            options={options}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
            //// if summation mode is enabled, then return only client variables
            autoCompleteResponse={autoCompleteResponse}
            fullAutoSuggestionList={fullAutoSuggestionList}
            targetedFunctionConfig={targetedFunctionConfig}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.IsEmpty ===
          functionContent.value?.token.functionName && (
          <IsEmptyFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.IsNotEmpty ===
          functionContent.value?.token.functionName && (
          <IsNotEmptyFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Contains ===
          functionContent.value?.token.functionName && (
          <ContainsFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.NotContains ===
          functionContent.value?.token.functionName && (
          <NotContainsFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Lower ===
          functionContent.value?.token.functionName && (
          <LowerFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Find ===
          functionContent.value?.token.functionName && (
          <FindFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Round ===
          functionContent.value?.token.functionName && (
          <RoundFunctionManager
            functionContent={functionContent}
            setFunctionArgs={setFunctionArgs}
            functionArgs={functionArgs}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.RoundUp ===
          functionContent.value?.token.functionName && (
          <RoundUpFunctionManager
            functionContent={functionContent}
            setFunctionArgs={setFunctionArgs}
            functionArgs={functionArgs}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.RoundDown ===
          functionContent.value?.token.functionName && (
          <RoundDownFunctionManager
            functionContent={functionContent}
            setFunctionArgs={setFunctionArgs}
            functionArgs={functionArgs}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.CurrentPayoutPeriod ===
          functionContent.value?.token.functionName && (
          <CurrentPayoutPeriodFunctionManager
            functionContent={functionContent}
            setFunctionArgs={setFunctionArgs}
            functionArgs={functionArgs}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {[
          EXPRESSION_FUNCTION_TYPES.CountNotNull,
          EXPRESSION_FUNCTION_TYPES["TEAM-COUNT-NOT-NULL"],
        ].includes(functionContent.value?.token.functionName) && (
          <CountNotNullFunctionManager
            options={options}
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            functionContent={functionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Concat ===
          functionContent.value?.token.functionName && (
          <ConcatFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            options={options}
            fullAutoSuggestionList={fullAutoSuggestionList}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Coalesce ===
          functionContent.value?.token.functionName && (
          <CoalesceFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            setDisableApply={setDisableApply}
            fullAutoSuggestionList={fullAutoSuggestionList}
            functionContent={functionContent}
            setFunctionContent={setFunctionContent}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES["TEAM-COUNT"] ==
          functionContent.value?.token.functionName && (
          <TeamCountFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Rank ==
          functionContent.value?.token.functionName && (
          <RankFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Rolling ==
          functionContent.value?.token.functionName && (
          <RollingSumFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.StartDate ==
          functionContent.value?.token.functionName && (
          <StartDateFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setFunctionContent={setFunctionContent}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.LastDate ==
          functionContent.value?.token.functionName && (
          <StartDateFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setFunctionContent={setFunctionContent}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.DateAdd ==
          functionContent.value?.token.functionName && (
          <DateAddFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Trim ==
          functionContent.value?.token.functionName && (
          <TrimFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Len ==
          functionContent.value?.token.functionName && (
          <LenFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Left ==
          functionContent.value?.token.functionName && (
          <LeftFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Right ==
          functionContent.value?.token.functionName && (
          <RightFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Mid ==
          functionContent.value?.token.functionName && (
          <MidFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.Timezone ==
          functionContent.value?.token.functionName && (
          <TimezoneFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            setFunctionContent={setFunctionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy ==
          functionContent.value?.token.functionName && (
          <GetValueFromHierarchyFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}

        {EXPRESSION_FUNCTION_TYPES.Hierarchy ==
          functionContent.value?.token.functionName &&
          targetedFunctionConfig?.Hierarchy?.datasheetVersion === "v2" && (
            <GenerateHierarchyFunctionV2Manager
              functionArgs={functionArgs}
              setFunctionArgs={setFunctionArgs}
              functionContent={functionContent}
              options={options}
              setDisableApply={setDisableApply}
              databooks={targetedFunctionConfig?.Hierarchy?.databooks || []}
              datasheetMap={
                targetedFunctionConfig?.Hierarchy?.datasheetMap || {}
              }
              databookId={targetedFunctionConfig?.Hierarchy?.databookId || ""}
              datasheetId={targetedFunctionConfig?.Hierarchy?.datasheetId || ""}
              dataTypeIdsFromNames={
                targetedFunctionConfig?.Hierarchy?.dataTypeIdsFromNames
              }
              canBeCrosslinked={
                targetedFunctionConfig?.Hierarchy?.canBeCrosslinked
              }
            />
          )}

        {EXPRESSION_FUNCTION_TYPES.Hierarchy ==
          functionContent.value?.token.functionName &&
          targetedFunctionConfig?.Hierarchy?.datasheetVersion === "v1" && (
            <GenerateHierarchyFunctionManager
              functionArgs={functionArgs}
              setFunctionArgs={setFunctionArgs}
              functionContent={functionContent}
              options={options}
              setDisableApply={setDisableApply}
              databooks={targetedFunctionConfig?.Hierarchy?.databooks || []}
              datasheetMap={
                targetedFunctionConfig?.Hierarchy?.datasheetMap || {}
              }
              databookId={targetedFunctionConfig?.Hierarchy?.databookId || ""}
              datasheetId={targetedFunctionConfig?.Hierarchy?.datasheetId || ""}
              dbStore={targetedFunctionConfig?.Hierarchy?.dbStore}
              dsConfigStore={targetedFunctionConfig?.Hierarchy?.dsConfigStore}
              dataTypeIdsFromNames={
                targetedFunctionConfig?.Hierarchy?.dataTypeIdsFromNames
              }
              canBeCrosslinked={
                targetedFunctionConfig?.Hierarchy?.canBeCrosslinked
              }
            />
          )}

        {EXPRESSION_FUNCTION_TYPES.StartsWith ==
          functionContent.value?.token.functionName && (
          <StartsWithFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.EndsWith ==
          functionContent.value?.token.functionName && (
          <EndsWithFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
          />
        )}
        {EXPRESSION_FUNCTION_TYPES.DateIsIn ==
          functionContent.value?.token.functionName && (
          <DateIsInFunctionManager
            functionArgs={functionArgs}
            setFunctionArgs={setFunctionArgs}
            functionContent={functionContent}
            options={options}
            setDisableApply={setDisableApply}
            setFunctionContent={setFunctionContent}
          />
        )}
      </div>
      <ApplyAndCancelButton
        disableApply={disableApply}
        closeFunctionManagerPopover={closeFunctionManagerPopover}
      />
    </div>
  );
}
