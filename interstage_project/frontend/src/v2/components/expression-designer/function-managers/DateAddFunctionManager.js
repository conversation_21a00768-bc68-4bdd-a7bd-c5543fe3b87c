import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { TimePicker } from "antd";
import { cloneDeep } from "lodash";
import moment from "moment";
import React, { useEffect, useState } from "react";

import { DATATYPE } from "~/Enums";
import {
  EverSelect,
  EverInput,
  EverTooltip,
  EverButtonGroup,
  EverButton,
} from "~/v2/components";

import { CommonFunctionHeader } from "./helpers/CommonFunctionHeader";
import { ExpressionBoxSelectbox } from "./helpers/ExpressionBoxSelectbox";
import { isDataSheetVariable } from "../utils";

const Option = EverSelect.Option;

/**
 * DateAddFunctionManager is a React component responsible for managing date-related function arguments.
 * It allows users to configure and select various function arguments, such as date columns, units, and constants.
 *
 * @param {Object} props - The component's props
 * @param {Array} props.functionArgs - The list of function arguments
 * @param {Function} props.setFunctionArgs - A function to set the function arguments
 * @param {Object} props.functionContent - Metadata for the currently selected function
 * @param {Array} props.options - Autocomplete suggestions for function arguments.
 * @param {Function} props.setDisableApply - A function to disable/enabled the apply button.
 * @returns {JSX.Element} - A React JSX element representing the component
 */
export const DateAddFunctionManager = ({
  functionArgs,
  setFunctionArgs,
  functionContent,
  options,
  setDisableApply,
}) => {
  const [isConstantField, setIsConstantField] = useState(true);

  // Use useEffect to update the "disable apply" state based on function argument values
  useEffect(() => {
    if (
      functionArgs[0] &&
      functionArgs[1] &&
      !["+", "-"].includes(functionArgs[1]) &&
      functionArgs[2]
    ) {
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
    if (isDataSheetVariable(functionArgs[1]?.tokenType) && isConstantField) {
      setIsConstantField(false);
    }
  }, [functionArgs, setDisableApply, isConstantField]);

  // Initialize function arguments if empty
  useEffect(() => {
    if (functionArgs.length === 0) {
      setFunctionArgs([null, null, "Day"]);
    }
  }, []);

  // filter all the date columns
  const filteredOptions = options.filter(
    (o) =>
      // filter the date type columns
      o.dataType === DATATYPE.DATE && isDataSheetVariable(o.token?.tokenType)
  );

  // Filter numeric options
  const numberOptions = options.filter(
    (o) =>
      (o.dataType === DATATYPE.INTEGER || o.dataType === DATATYPE.PERCENTAGE) &&
      isDataSheetVariable(o.token?.tokenType)
  );

  return (
    <CommonFunctionHeader
      functionName={functionContent.value?.token.functionName}
    >
      <ExpressionBoxSelectbox
        showSearch
        allowClear
        className="w-48"
        dropdownMatchSelectWidth
        options={filteredOptions}
        onChange={(_, option) => {
          const clonedArgs = cloneDeep(functionArgs);
          clonedArgs[0] = option?.token;
          setFunctionArgs(clonedArgs);
        }}
        value={functionArgs[0]?.token?.name}
        placeholder="Select Date Column"
      />
      {/* Select Unit */}
      <EverSelect
        showSearch
        allowClear
        className="w-48"
        value={functionArgs[2]}
        placeholder="Select Unit"
        onChange={(value) => {
          const clonedArgs = cloneDeep(functionArgs);
          clonedArgs[1] = null;
          clonedArgs[2] = value;

          setFunctionArgs(clonedArgs);
        }}
      >
        <Option value="Day">Days</Option>
        <Option value="Month">MONTH</Option>
        <Option value="Quarter">QUARTER</Option>
        <Option value="Halfyear">HALF-YEAR</Option>
        <Option value="Year">YEAR</Option>
        <Option value="HH:mm">HH:mm</Option>
      </EverSelect>
      {functionArgs[2] === "HH:mm" ? (
        // Handle time-related function arguments
        <>
          <EverButtonGroup
            className="bg-ever-base-200"
            activeBtnType="text"
            activeBtnColor="primary"
            defActiveBtnIndex={
              functionArgs[1] ? (functionArgs[1][0] == "+" ? 0 : 1) : 0
            }
            size="small"
          >
            <EverButton
              onClick={() => {
                const clonedArgs = cloneDeep(functionArgs);
                clonedArgs[1] = clonedArgs[1]
                  ? `+${clonedArgs[1]?.slice(1, clonedArgs[1]?.length)}`
                  : "+";

                setFunctionArgs(clonedArgs);
              }}
            >
              +
            </EverButton>
            <EverButton
              onClick={() => {
                const clonedArgs = cloneDeep(functionArgs);
                clonedArgs[1] = clonedArgs[1]
                  ? `-${clonedArgs[1]?.slice(1, clonedArgs[1]?.length)}`
                  : "-";

                setFunctionArgs(clonedArgs);
              }}
            >
              -
            </EverButton>
          </EverButtonGroup>
          <TimePicker
            format="HH:mm"
            allowClear={false}
            showNow={false}
            value={
              functionArgs[1]?.slice && functionArgs[1]?.length > 1
                ? moment(
                    functionArgs[1]?.slice(1, functionArgs[1]?.length),
                    "HH:mm"
                  )
                : null
            }
            onChange={(val) => {
              const time = moment(val).format("HH:mm:ss");
              const clonedArgs = cloneDeep(functionArgs);
              clonedArgs[1] = clonedArgs[1]
                ? `${clonedArgs[1][0]}${time}`
                : `+${time}`;
              setFunctionArgs(clonedArgs);
            }}
          />
        </>
      ) : (
        // Handle constant or datasheet field selection
        <>
          <EverSelect
            showSearch
            allowClear
            className="w-48"
            value={isConstantField ? "constant" : "DatasheetField"}
            onChange={(value) => {
              value === "constant"
                ? setIsConstantField(true)
                : setIsConstantField(false);
              const clonedArgs = cloneDeep(functionArgs);
              clonedArgs[1] = null;
              setFunctionArgs(clonedArgs);
            }}
          >
            <Option value="constant">Constant</Option>
            <Option value="DatasheetField">Datasheet Field</Option>
          </EverSelect>
          {/* Input for constant value or select numeric column */}
          {isConstantField ? (
            <EverInput
              className="w-32"
              type="number"
              placeholder="Enter Value"
              value={functionArgs[1]}
              onChange={(e) => {
                // Check if the input value does not contain a decimal point
                if (!e?.target?.value?.includes(".")) {
                  const clonedArgs = cloneDeep(functionArgs);
                  clonedArgs[1] = e?.target?.value;
                  setFunctionArgs(clonedArgs);
                }
              }}
            />
          ) : (
            <>
              <ExpressionBoxSelectbox
                showSearch
                allowClear
                className="w-48"
                dropdownMatchSelectWidth
                options={numberOptions}
                onChange={(_, option) => {
                  const clonedArgs = cloneDeep(functionArgs);
                  clonedArgs[1] = option?.token;
                  setFunctionArgs(clonedArgs);
                }}
                value={functionArgs[1]?.token?.name}
                placeholder="Select Numeric Column"
              />
              <EverTooltip
                title={
                  <>
                    <p>
                      1. The Max and Min Date supported by the function is 10
                      Apr 2062 and 22 Sep 1677 respectively.
                    </p>
                    <p>
                      2. If the selected fields have decimal values, they would
                      be rounded down to the nearest integer.
                    </p>
                  </>
                }
              >
                <InfoCircleIcon className="w-5 h-5 text-ever-base-content-mid" />
              </EverTooltip>
            </>
          )}
        </>
      )}
    </CommonFunctionHeader>
  );
};
