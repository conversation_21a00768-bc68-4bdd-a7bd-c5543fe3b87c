import { includes, cloneDeep } from "lodash";
import React, { useEffect } from "react";

import { DATATYPE } from "~/Enums";
import { EverSelect } from "~/v2/components";

import { CommonFunctionHeader } from "./helpers/CommonFunctionHeader";
import { ExpressionBoxSelectbox } from "./helpers/ExpressionBoxSelectbox";
import { isDataSheetVariable } from "../utils";

const Option = EverSelect.Option;

const UNIT_OPTIONS = [
  { value: "Day", label: "DAYS" },
  {
    value: "Month",
    label: "MONTH",
  },
  {
    value: "Quarter",
    label: "QUARTER",
  },
  {
    value: "Halfyear",
    label: "HALF-YEAR",
  },
  {
    value: "Year",
    label: "YEAR",
  },
];

/**
 * returns the date diff function manager
 * @param {Object} props
 * @param {Array} props.options - Autocomplete suggestions for function arguments.
 * @param {Function} props.setDisableApply - A function to disable/enabled the apply button.
 * @param {Object} props.functionContent - This holds the meta data for the currently selected function.
 * @param {Function} props.setFunctionContent - The function to set the function content
 * @param {Array} props.functionArgs - This holds the list of arguments for the currently edited function.It is added to AST.
 * @param {Function} props.setFunctionArgs - The function to set the function arguments
 * @returns {JSX.Element}
 */
export const DateDiffFunctionManager = ({
  functionArgs,
  setFunctionArgs,
  functionContent,
  setFunctionContent,
  options,
  setDisableApply,
}) => {
  useEffect(() => {
    if (
      functionArgs[0] &&
      functionArgs[1] &&
      functionArgs[2] &&
      (["Quarter", "Halfyear", "Year"].includes(functionArgs[2])
        ? functionArgs[3]
        : true)
    ) {
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
  }, [functionArgs, setDisableApply]);

  // Use the useEffect hook to perform side effects in a functional component
  useEffect(() => {
    const payoutPeriods = {};
    // Iterate through each item in the payoutFreqList array
    UNIT_OPTIONS.forEach((payout) => {
      payoutPeriods[payout.value] = payout.label;
    });
    // Update the state using the setFunctionContent function
    setFunctionContent((prev) => {
      const newPrev = cloneDeep(prev);
      newPrev.value.token.payoutPeriods = payoutPeriods;
      return newPrev;
    });
  }, []);

  // filter all the date columns
  const filteredOptions = options.filter(
    (o) =>
      // filter the date type columns
      o.dataType === DATATYPE.DATE && isDataSheetVariable(o.token?.tokenType)
  );

  return (
    <CommonFunctionHeader
      functionName={functionContent.value?.token.functionName}
    >
      <ExpressionBoxSelectbox
        showSearch
        allowClear
        className="w-48"
        dropdownMatchSelectWidth
        options={filteredOptions}
        onChange={(_, option) => {
          setFunctionArgs([
            option?.token,
            functionArgs[1],
            functionArgs[2],
            functionArgs[3],
          ]);
        }}
        value={functionArgs[0]?.token?.name}
        placeholder="Start Date Column"
      />
      <ExpressionBoxSelectbox
        showSearch
        allowClear
        className="w-48"
        dropdownMatchSelectWidth
        options={filteredOptions}
        onChange={(_, option) => {
          setFunctionArgs([
            functionArgs[0],
            option?.token,
            functionArgs[2],
            functionArgs[3],
          ]);
        }}
        value={functionArgs[1]?.token?.name}
        placeholder="End Date Column"
      />
      <EverSelect
        showSearch
        allowClear
        className="w-48"
        value={functionArgs[2]}
        placeholder="Select Unit"
        onChange={(value) => {
          setFunctionArgs([
            functionArgs[0],
            functionArgs[1],
            value,
            functionArgs[3] || "Fiscal",
          ]);
        }}
      >
        <Option value="Day">DAYS</Option>
        <Option value="Month">MONTH</Option>
        <Option value="Quarter">QUARTER</Option>
        <Option value="Halfyear">HALF-YEAR</Option>
        <Option value="Year">YEAR</Option>
      </EverSelect>
      {includes(["Quarter", "Halfyear", "Year"], functionArgs[2]) && (
        <EverSelect
          showSearch
          allowClear
          className="w-48"
          value={functionArgs[3]}
          onChange={(value) => {
            setFunctionArgs([
              functionArgs[0],
              functionArgs[1],
              functionArgs[2],
              value,
            ]);
          }}
          placeholder="Select year type"
        >
          <Option value="Fiscal">FISCAL</Option>
          <Option value="Calendar">CALENDAR</Option>
        </EverSelect>
      )}
    </CommonFunctionHeader>
  );
};
