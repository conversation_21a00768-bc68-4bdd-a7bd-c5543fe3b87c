import { useLazyQuery, gql } from "@apollo/client";
import { InfoCircleIcon } from "@everstage/evericons/outlined";
import React, { useEffect, useState } from "react";

import {
  DATATYPE,
  CURRENT_DATE_KEY,
  HIERARCHY_CURRENT_SHEET_KEY,
  SOURCE_TYPE_CODES,
} from "~/Enums";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import {
  EverSelect,
  EverLabel,
  EverTooltip,
  EverTg,
  EverLoader,
  DatasheetDropDown,
} from "~/v2/components";

import { NAVIGATION_KEYS } from "../common";
import { isDataSheetVariable } from "../utils";

const Option = EverSelect.Option;

/**
 * Generate hierarchy is a React component for managing the arguments of a "Last Date" function.
 * It allows users to configure and select various function arguments, such as date columns, units, and year types.
 *
 * @param {Object} props - The component's props
 * @param {Array} props.functionArgs - The list of function arguments
 * @param {Function} props.setFunctionArgs - A function to set the function arguments
 * @param {Object} props.functionContent - This holds the meta data for the currently selected function.
 * @param {Array} props.options - Autocomplete suggestions for function arguments.
 * @param {Function} props.setDisableApply - A function to set the "disable apply" state
 * @param {String} props.databookId - The selected databook id
 * @param {String} props.datasheetId - The selected datasheet id
 * @param {Object} props.dbStore - Databook Store
 * @param {Object} props.dsConfigStore - Datasheet Store
 * @returns {JSX.Element} - A React JSX element representing the component
 */

/*
  Don't change the order of fields, if need to be changed ,make sure unit tests are passing,

    functionArgs[0] -> hierarchyForColumn,
    functionArgs[1] -> asOfDateCol,
    functionArgs[2] -> referenceBook,
    functionArgs[3] -> referenceSheet,
    functionArgs[4] -> parentColumn,
    functionArgs[5] -> childColumn,
    functionArgs[6] -> startTimeColumn,
    functionArgs[7] -> endTimeColumn,
    functionArgs[8] -> hierarchyForDataTypeId,
    functionArgs[9] -> referenceSheetDataOrigin,
 */

export function GenerateHierarchyFunctionManager({
  //Hierarchy function requires 10 arguments/fields
  functionArgs = Array(10).fill(null),
  setFunctionArgs,
  functionContent,
  options,
  setDisableApply,
  databookId,
  datasheetId,
  dataTypeIdsFromNames,
  dbStore,
  dsConfigStore,
}) {
  const [
    hierarchyForColumn,
    asOfDateCol,
    referenceBook,
    referenceSheet,
    parentColumn,
    childColumn,
    startTimeColumn,
    endTimeColumn,
    hierarchyForDataTypeId,
    referenceSheetDataOrigin,
  ] = functionArgs;
  const [hierarchyDatatype, setHierarchDatatype] = useState("");
  const [sourceDatabookId, setSourceDatabookId] = useState(
    referenceBook?.value || databookId
  );
  const [dataSheetsToVariableMap, setDataSheetsToVariableMap] = useState({});
  const [loading, setLoading] = useState(false);
  const { getDatabookData } = useDatabookStore();

  const [getDsVariables, { variables }] = useLazyQuery(
    GET_DATASHEET_VARIABLES,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: "no-cache",
      onCompleted: (data) => {
        if (data) {
          const dsVariables = data[SOURCE_TYPE_CODES.DATASHEET].variables;
          modifyDataSheetsToVariableMap(dsVariables, variables.datasheetId);
        }
      },
    }
  );

  useEffect(() => {
    // Use useEffect to update the "disable apply" state based on function argument values
    if (
      hierarchyForColumn &&
      asOfDateCol &&
      referenceBook &&
      referenceSheet &&
      parentColumn &&
      childColumn
    ) {
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
  }, [functionArgs, setDisableApply]);

  // Initialize referenceBook
  useEffect(() => {
    let modifiedRefernceBook = referenceBook;

    if (!referenceBook) {
      const dbOption = {
        name: getDatabookData(sourceDatabookId)?.name,
        value: sourceDatabookId,
      };
      modifiedRefernceBook = dbOption;
    }

    setFunctionArgs([
      hierarchyForColumn,
      asOfDateCol,
      modifiedRefernceBook,
      referenceSheet,
      parentColumn,
      childColumn,
      startTimeColumn,
      endTimeColumn,
      hierarchyForDataTypeId,
      referenceSheetDataOrigin,
    ]);
  }, []);

  const modifyDataSheetsToVariableMap = (dsVariables, reqDatasheetId) => {
    // This function modifies the dataSheetsToVariableMap state variable
    const dsIdVarMap = generateDataSheetsToVariableMap(
      dsVariables,
      reqDatasheetId
    );
    setDataSheetsToVariableMap(dsIdVarMap);
    setLoading(false);
  };

  const getCurrentSheetVariables = () => {
    // This function returns the variables of the current sheet
    const dsVariables = reStructureVariablesData(
      options.filter((o) => isDataSheetVariable(o.token?.tokenType))
    );
    modifyDataSheetsToVariableMap(dsVariables, HIERARCHY_CURRENT_SHEET_KEY);
  };

  const currentSheetOption = {
    value: HIERARCHY_CURRENT_SHEET_KEY,
    name: HIERARCHY_CURRENT_SHEET_KEY,
  };

  const handleReferenceSheetChange = (option, sourceData) => {
    // If cross linking is enabled , this will act as callback Function when referenceSheet value is changed
    const modifiedRefBook = {
      name: getDatabookData(sourceDatabookId)?.name,
      value: sourceDatabookId,
    };
    let modifiedParentColumn = parentColumn;
    let modifiedChildColumn = childColumn;
    let modifiedStartTimeColumn = startTimeColumn;
    let modifiedEndTimeColumn = endTimeColumn;

    let modifiedRefOrigin = null;
    const modifiedRefSheet = { name: option?.label, value: option?.value };
    if (functionContent?.type !== NAVIGATION_KEYS.EDIT) {
      modifiedParentColumn = null;
      modifiedChildColumn = null;
      modifiedStartTimeColumn = null;
      modifiedEndTimeColumn = null;
      modifiedRefOrigin = {
        name: "referenceSheetDataOrigin",
        value: sourceData?.dataOrigin,
      };
    }
    setFunctionArgs([
      hierarchyForColumn,
      asOfDateCol,
      modifiedRefBook,
      modifiedRefSheet,
      modifiedParentColumn,
      modifiedChildColumn,
      modifiedStartTimeColumn,
      modifiedEndTimeColumn,
      hierarchyForDataTypeId,
      modifiedRefOrigin,
    ]);
    setLoading(true); //show spinner till variables fetched from api call,
  };

  useEffect(() => {
    if (
      referenceSheet &&
      referenceSheet?.value !== HIERARCHY_CURRENT_SHEET_KEY
    ) {
      getDsVariables({
        variables: {
          databookId: referenceBook?.value,
          datasheetId: referenceSheet?.value,
        },
      });
    } else {
      getCurrentSheetVariables();
    }
  }, [referenceSheet]);

  // Filter the String and Email datasheet variables from the options
  const filteredOptions = options.filter(
    (o) =>
      (o.dataType === DATATYPE.STRING || o.dataType === DATATYPE.EMAIL) &&
      isDataSheetVariable(o.token?.tokenType)
  );

  const dateVariables = options.filter(
    (o) =>
      o.dataType === DATATYPE.DATE && isDataSheetVariable(o.token?.tokenType)
  );

  return (
    <div className="flex flex-col gap-6 max-h-60 overflow-scroll">
      <div className="flex gap-4 ">
        <div className="w-64">
          <SelectTitle
            title="Column to generate hierarchy for"
            info="Column containing values for which hierarchy needs to be generated, ex: User Email. Values in this column would be used to join rows in reference sheet."
            isRequired={true}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              placeholder="Select"
              options={filteredOptions}
              value={hierarchyForColumn?.token?.name}
              disabled={functionContent?.type === NAVIGATION_KEYS.EDIT}
              onChange={(_v, option) => {
                const modifiedHierarchyForColumn = option?.token;
                const modifiedHierarchyDataTypeId = {
                  name: "hierarchyForDataTypeId",
                  value: dataTypeIdsFromNames[option?.dataType],
                };
                const modifiedParentColumn = null;
                const modifiedChildColumn = null;
                setHierarchDatatype(option?.dataType);
                setFunctionArgs([
                  modifiedHierarchyForColumn,
                  asOfDateCol,
                  referenceBook,
                  referenceSheet,
                  modifiedParentColumn,
                  modifiedChildColumn,
                  startTimeColumn,
                  endTimeColumn,
                  modifiedHierarchyDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              showSearch
            />
          </div>
        </div>
        <div className="w-64">
          <SelectTitle
            title="For Period"
            info="Map the column containing the date value for which you want to generate hierarchy. Used in cases where hierarchy changes across periods and you want the hierarchy as of a specific date."
            isRequired={true}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              placeholder="Select"
              value={asOfDateCol?.token?.name || asOfDateCol}
              onChange={(_v, option) => {
                const modifiedAsofDateColumn = option?.token || option?.value;
                setFunctionArgs([
                  hierarchyForColumn,
                  modifiedAsofDateColumn,
                  referenceBook,
                  referenceSheet,
                  parentColumn,
                  childColumn,
                  startTimeColumn,
                  endTimeColumn,
                  hierarchyForDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              showSearch
            >
              <EverSelect.Option
                key={CURRENT_DATE_KEY}
                value={CURRENT_DATE_KEY}
              >
                {CURRENT_DATE_KEY}
              </EverSelect.Option>
              <EverSelect.OptGroup
                label={
                  <EverTg.Caption.Medium>{`Date columns`}</EverTg.Caption.Medium>
                }
              >
                {dateVariables?.map((option) => (
                  <EverSelect.Option
                    key={option.value}
                    value={option.value}
                    {...option}
                  >
                    {option.label}
                  </EverSelect.Option>
                ))}
              </EverSelect.OptGroup>
            </EverSelect>
          </div>
        </div>
      </div>
      <div>
        {/* switch with datasheet dropdown when cross link flag is enabled */}
        <SelectTitle
          title="Join with sheet"
          info="Sheet containing information on parent values. Records in this sheet will be used to form the hierarchy."
          isRequired={true}
        />
        <div className="w-full">
          {
            <DatasheetDropDown
              sourceDatabookId={sourceDatabookId}
              setSourceDatabookId={setSourceDatabookId}
              sourceDatasheetId={referenceSheet?.value}
              postDatasheetChangeActions={handleReferenceSheetChange}
              datasheetId={datasheetId}
              dsConfigStore={dsConfigStore}
              dbStore={dbStore}
              dropdownWrapperClass="w-full"
              disabled={functionContent?.type === NAVIGATION_KEYS.EDIT}
              extraDsSelectOptions={
                databookId === sourceDatabookId ? [currentSheetOption] : []
              }
            />
          }
        </div>
      </div>
      <div className="flex gap-4">
        <div className="w-64">
          <SelectTitle
            title="Join on column"
            info="Column containing entity values you’re generating hierarchy for, ex: User Email"
            isRequired={true}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              disabled={
                !hierarchyForColumn ||
                !referenceSheet ||
                functionContent?.type === NAVIGATION_KEYS.EDIT
              }
              placeholder="Select"
              value={parentColumn?.value}
              onChange={(_v, option) => {
                const modifiedParentColumn = {
                  name: option?.name,
                  value: option?.value,
                };

                setFunctionArgs([
                  hierarchyForColumn,
                  asOfDateCol,
                  referenceBook,
                  referenceSheet,
                  modifiedParentColumn,
                  childColumn,
                  startTimeColumn,
                  endTimeColumn,
                  hierarchyForDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              notFoundContent={
                loading ? (
                  <div className="w-full h-full flex items-center">
                    <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                    Variables
                  </div>
                ) : null
              }
              showSearch
            >
              {getSelectOptions(
                dataSheetsToVariableMap?.[
                  referenceSheet?.value
                ]?.string?.filter((x) => x.value != childColumn?.value),
                hierarchyDatatype
              )}
            </EverSelect>
          </div>
        </div>
        <div className="w-64">
          <SelectTitle
            title="Parent column"
            info="Column containing information on parent entity for a given child entity"
            isRequired={true}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              disabled={
                !hierarchyForColumn ||
                !referenceSheet ||
                functionContent?.type === NAVIGATION_KEYS.EDIT
              }
              placeholder="Select"
              value={childColumn?.value}
              onChange={(_v, option) => {
                const modifiedChildColumn = {
                  name: option.name,
                  value: option.value,
                };
                setFunctionArgs([
                  hierarchyForColumn,
                  asOfDateCol,
                  referenceBook,
                  referenceSheet,
                  parentColumn,
                  modifiedChildColumn,
                  startTimeColumn,
                  endTimeColumn,
                  hierarchyForDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              notFoundContent={
                loading ? (
                  <div className="w-full h-full flex items-center">
                    <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                    Variables
                  </div>
                ) : null
              }
              showSearch
            >
              {getSelectOptions(
                dataSheetsToVariableMap?.[
                  referenceSheet?.value
                ]?.string?.filter((x) => x.value != parentColumn?.value),
                hierarchyDatatype
              )}
            </EverSelect>
          </div>
        </div>
      </div>
      <div className="flex gap-4">
        <div className="w-64">
          <SelectTitle
            title="Period effective start date"
            info="For cases where hierarchy could change for different periods"
            isRequired={false}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              placeholder="Select"
              value={startTimeColumn?.value}
              onChange={(_v, option) => {
                const modifiedStartTimeColumn = {
                  name: option?.name,
                  value: option?.value,
                };
                setFunctionArgs([
                  hierarchyForColumn,
                  asOfDateCol,
                  referenceBook,
                  referenceSheet,
                  parentColumn,
                  childColumn,
                  modifiedStartTimeColumn,
                  endTimeColumn,
                  hierarchyForDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              notFoundContent={
                loading ? (
                  <div className="w-full h-full flex items-center">
                    <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                    Variables
                  </div>
                ) : null
              }
              showSearch
            >
              {getSelectOptions(
                dataSheetsToVariableMap?.[referenceSheet?.value]?.date?.filter(
                  (x) => x.value != endTimeColumn?.value
                )
              )}
            </EverSelect>
          </div>
        </div>
        <div className="w-64">
          <SelectTitle
            title="Period effective end date"
            info="For cases where hierarchy could change for different periods"
            isRequired={false}
          />
          <div className="w-full">
            <EverSelect
              className="w-full"
              showArrow={true}
              //disabled={startTimeColumn}
              placeholder="Select"
              value={endTimeColumn?.value}
              onChange={(_v, option) => {
                const modifiedEndTimeColumn = {
                  name: option?.name,
                  value: option?.value,
                };
                setFunctionArgs([
                  hierarchyForColumn,
                  asOfDateCol,
                  referenceBook,
                  referenceSheet,
                  parentColumn,
                  childColumn,
                  startTimeColumn,
                  modifiedEndTimeColumn,
                  hierarchyForDataTypeId,
                  referenceSheetDataOrigin,
                ]);
              }}
              notFoundContent={
                loading ? (
                  <div className="w-full h-full flex items-center">
                    <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                    Variables
                  </div>
                ) : null
              }
              showSearch
            >
              {getSelectOptions(
                dataSheetsToVariableMap?.[referenceSheet?.value]?.date?.filter(
                  (x) => x.value != startTimeColumn?.value
                )
              )}
            </EverSelect>
          </div>
        </div>
      </div>
    </div>
  );
}

const SelectTitle = ({ title, info, isRequired }) => {
  return (
    <div className="flex items-center mb-2">
      <div className="mr-2">
        <EverLabel className={"m-0"} required={isRequired}>
          {" "}
          {title}{" "}
        </EverLabel>
        {/* Add a red star if isRequired is true */}
      </div>

      <EverTooltip title={info}>
        <InfoCircleIcon className="w-3 h-3" />
      </EverTooltip>
    </div>
  );
};

/*
This function returns a map of datasheetId to variables of that datasheet
only collects string, email and date variables
*/
const generateDataSheetsToVariableMap = (dsVariables, datasheetId) => {
  //Sample Format of resultMap:
  // {
  //   "{datasheet_id}": {
  //       "string": [
  //           {
  //               "value": "employee_email_id",
  //               "name": "Employee Email Id",
  //               "dataType": "Email"
  //           },
  //           {
  //               "value": "reporting_manager_email_id",
  //               "name": "Reporting Manager EmailId",
  //               "dataType": "Email"
  //           }
  //       ],
  //       "date": [
  //           {
  //               "value": "effective_start_date",
  //               "name": "Effective Start Date",
  //               "dataType": "Date"
  //           },
  //           {
  //               "value": "effective_end_date",
  //               "name": "Effective End Date",
  //               "dataType": "Date"
  //           },
  //       ]
  //   }
  // }

  if (!datasheetId) {
    return {};
  }

  const dataTypeMap = {
    [DATATYPE.STRING]: "string",
    [DATATYPE.EMAIL]: "string",
    [DATATYPE.DATE]: "date",
  };

  const groupedVariables = dsVariables.reduce((grouped, variable) => {
    const dataType = variable.dataType.dataType;
    const mapValue = dataTypeMap[dataType];

    if (!mapValue) {
      return grouped;
    }

    if (!grouped[mapValue]) {
      grouped[mapValue] = [];
    }

    grouped[mapValue].push({
      value: variable.systemName,
      name: variable.displayName,
      dataType: dataType,
    });

    return grouped;
  }, {});

  const resultMap = {
    [datasheetId]: groupedVariables,
  };
  return resultMap;
};

const getSelectOptions = (arr, selectedType = null) => {
  if (!selectedType || selectedType === DATATYPE.STRING) {
    return arr?.map((option) => (
      <Option value={option.value} key={option.value} {...option}>
        {option.name}
      </Option>
    ));
  }
  return arr
    ?.filter((option) => option.dataType === selectedType)
    ?.map((option) => (
      <Option value={option.value} key={option.value} {...option}>
        {option.name}
      </Option>
    ));
};

const reStructureVariablesData = (variables) => {
  // This function restructures the variables data to the format required by the getSelectOptions function
  // input format of variables:
  //   {
  //     "label": "Effective Start Date",
  //     "value": "Effective Start Date",
  //     "dataType": "Date",
  //     "token": {
  //         "tokenType": "DATASHEET_VARIABLES",
  //         "token": {
  //             "systemName": "effective_start_date",
  //             "key": "effective_start_date",
  //             "name": "Effective Start Date"
  //         }
  //     }
  // }

  //  output format of variables:
  //{
  //     "systemName": "co_181_name",
  //     "displayName": "name",
  //     "dataType": {
  //         "dataType": "String",
  //     },
  // }

  let outputList = [];
  variables.forEach((variable) => {
    let output = {};
    output.systemName = variable.token.token.systemName;
    output.displayName = variable.token.token.name;
    output.dataType = {};
    output.dataType.dataType = variable.dataType;
    outputList.push(output);
  });
  return outputList;
};

const GET_DATASHEET_VARIABLES = gql`
  query DatasheetDetails($databookId: String!, $datasheetId: String!) {
    datasheet(databookId: $databookId, datasheetId: $datasheetId) {
      variables {
        systemName
        displayName
        dataType {
          id
          dataType
        }
        fieldOrder
        metaData
      }
      variablesWithPermission {
        systemName
        displayName
        dataType {
          id
          dataType
        }
        fieldOrder
        metaData
      }
    }
  }
`;
