import { useQuery } from "@apollo/client";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { QUOTA_CATEGORIES } from "~/Enums";

import { LookbackPeriodInput } from "./helpers/LookbackPeriodInput";
import { GET_QUOTA_NAMES, getQuotaOptions } from "./helpers/QuotaHelper";
import { SimpleFunctionManager } from "./helpers/SimpleFunctionHelper";
import { EverLoader } from "../../ever-loader";

/**
 * returns the quota attainment function manager
 * @param {Object} props
 * @param {Array} props.functionArgs - This holds the list of arguments for the currently edited function.It is added to AST.
 * @param {Function} props.setDisableApply - The function to set the disable apply
 * @param {Function} props.setFunctionArgs - The function to set the function args
 * @param {Object} props.functionContent - This holds the meta data for the currently selected function.
 * @returns {JSX.Element}
 */
export function QuotaAttainmentFunctionManager({
  setFunctionArgs,
  setDisableApply,
  functionArgs,
  functionContent,
}) {
  const quotaParent = useRef();
  const { t } = useTranslation();
  const primaryQuota = {
    displayName: t("PRIMARY_QUOTA"),
    quotaCategoryName: QUOTA_CATEGORIES.PRIMARY_QUOTA,
  };

  const [loading, setLoading] = useState(true);
  const [quotaOptions, setQuotaOptions] = useState(
    getQuotaOptions([primaryQuota])
  );

  const { data: quotaCategoryData } = useQuery(GET_QUOTA_NAMES, {
    fetchPolicy: "no-cache",
  });

  // set the quota options
  useEffect(() => {
    if (quotaCategoryData && quotaCategoryData.allQuotaCategories) {
      setQuotaOptions(
        getQuotaOptions([primaryQuota, ...quotaCategoryData.allQuotaCategories])
      );
      setLoading(false);
      setTimeout(() => {
        // @ts-ignore
        quotaParent.current?.getElementsByTagName("input")[0].focus();
      });
    }
  }, [quotaCategoryData]);

  // set the disable apply
  useEffect(() => {
    if (functionArgs && functionArgs[0] && functionArgs[1] !== undefined) {
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
  }, [functionArgs, setDisableApply]);

  return (
    <>
      <div ref={quotaParent} className="flex flex-col gap-4">
        {loading ? (
          <div className="flex w-full justify-center">
            <EverLoader.SpinnerLottie className="h-10 w-10" />
          </div>
        ) : (
          <>
            <SimpleFunctionManager
              functionDisplayName={t("QUOTAATTAINTMENT")}
              setFunctionArgs={setFunctionArgs}
              setDisableApply={setDisableApply}
              functionContent={functionContent}
              options={quotaOptions}
              functionArgs={functionArgs}
            />
            <LookbackPeriodInput
              setFunctionArgs={setFunctionArgs}
              functionArgs={functionArgs}
            />
          </>
        )}
      </div>
    </>
  );
}
