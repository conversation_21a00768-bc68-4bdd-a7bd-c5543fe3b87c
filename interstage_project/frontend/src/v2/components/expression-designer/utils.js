import {
  HashIcon,
  TypeIcon,
  AtSignIcon,
  PercentAltIcon,
  CalendarIcon,
  ContrastIcon,
  HierarchyIcon,
} from "@everstage/evericons/outlined";
import { format, isValid, parse } from "date-fns";
import { cloneDeep, isObject } from "lodash";

import { validateExpressionStack as apiValidate } from "~/Api/ExpressionBoxService";
import {
  DATATYPE,
  EXPRESSION_FUNCTION_TYPES,
  EXPRESSION_TOKEN_TYPES,
  PAYOUT_PERIOD,
  EXPRESSION_BOX_STATUS,
  FOR_WHOM_THE_COMMISSION_IS_CALCULATED_KEY,
  FOR_WHOM_THE_COMMISSION_IS_CALCULATED_DISPLAY,
  CURRENT_DATE_KEY,
  CURRENT_DATE_DISPLAY,
  DATE_PLACEHOLDER,
} from "~/Enums";

/**
 * Function to convert a given string to the format "True" or "False"
 * @param {String} inputString - input string
 * @returns {String} - converted string
 */
function convertToBoolean(inputString) {
  return (
    inputString.charAt(0).toUpperCase() + inputString.slice(1).toLowerCase()
  );
}

/**
 * Function to convert a given date string to the format "YYYY-MM-DD"
 * @param {String} inputDate - input date string
 * @param {String} separator - separator to be used in the output date string
 * @returns {String} - converted date string
 *
 */
export function convertDateFormat(inputDate, separator = "/") {
  const parts = inputDate.split("/");
  if (parts.length === 3) {
    return `${parts[2]}${separator}${parts[0]}${separator}${parts[1]}`;
  }
  // Return the input date if it doesn't match the expected format
  return inputDate;
}

export function convertDateForSuggestion(inputDate, separator = "/") {
  const parts = inputDate.split("/");
  if (parts.length === 3) {
    return `${parts[1]}${separator}${parts[2]}${separator}${parts[0]}`;
  }
  // Return the input date if it doesn't match the expected format
  return inputDate;
}

/**
 * Converts an array of strings into a formatted string representation suitable for HTML rendering.
 *
 * @param {Array<String>} value - An array of strings to be converted.
 * @returns {String} - A string representation of the array with each element wrapped in double quotes.
 */
export function convertStringArrayFormat(value = []) {
  // Map each item in the array to a quoted string and join them with commas
  const resultString = value.map((item) => `"${item}"`).join(",");
  // Return the formatted string enclosed in square brackets
  return `[${resultString}]`;
}

/**
 * Function to convert a given integer array to the format "[element1, element2, ...]"
 * @param {String} inputString - input string
 * @returns {String} - converted string
 */
export function convertIntArrayFormat(inputString) {
  return `[${inputString
    .split("-")[1] // Split the string by '-' and take the second part
    .split(",") // Split the remaining part by ',' to get individual numbers
    .map((item) => item.trim())}]`;
}

function isArray(inputString) {
  // Check if the input string starts and ends with '[' and ']'
  if (inputString[0] === "[" && inputString[inputString.length - 1] === "]") {
    return true;
  }
  return false;
}

// Function to check if a string is enclosed in double quotes
function isStringArrayValid(inputString) {
  var firstChar = inputString[0];
  // Get the last character
  var lastChar = inputString[inputString.length - 1];
  // Check if the first and last characters are double quotes
  if (firstChar === '"' && lastChar === '"') {
    return true;
  } else {
    return false;
  }
}

const customJsonParseForString = (input) => {
  // Remove the first and last character from the input string
  // ex: input = '["a", "b", "c"]' => arrayString = '"a", "b", "c"'
  let arrayString = input.slice(2, -2);
  // Replace the escaped double quotes with single quotes
  return arrayString.split('","');
};

const customJsonParseForInt = (input) => {
  // Remove the first and last character from the input string
  // ex: input = '[1, 2, 3]' => arrayString = '1, 2, 3'
  let arrayString = input.slice(1, -1);
  // Replace the escaped double quotes with single quotes
  return arrayString.split(",");
};

export const formatDateForRender = (value) => {
  // parse() lets us define the date format, ensuring accurate conversion
  // and avoiding timezone issues that can happen with new Date().
  const date = parse(value, "yyyy-MM-dd", new Date());
  if (!isValid(date)) {
    return value; // or return a default value like "Invalid Date"
  }
  return format(date, "MMM dd, yyyy");
};

export const formatDateForDatePicker = (value) => {
  // parse() lets us define the date format, ensuring accurate conversion
  // and avoiding timezone issues that can happen with new Date().
  const date = parse(value, "yyyy-MM-dd", new Date());
  if (!isValid(date)) {
    return null;
  }
  return date;
};

/**
 * Function to identify the type of the constant
 * @param {String} userInput - user input
 * @returns {Array} - array of matched types
 * The format of the matched types is as follows:
 * {
 * label: <label>,
 * value: <value>,
 * meta: {
 *   dataType: <dataType>,
 *  dataTypeId: <dataTypeId>
 * },
 * group: <group>
 * }
 *
 */
export const constantIdentifier = (userInput) => {
  // if it contains only while space return empty array
  if (userInput.trim() === "") {
    return [];
  }
  if (userInput) {
    // Regular expressions to match boolean values, percentages, numbers, and strings
    const booleanRegex = /^(true|false)$/i;
    const percentageRegex = /^-?\d+(\.\d+)?%$/;
    const numberRegex = /^-?\d+(\.\d+)?$/;

    const isValidDate = (input) => {
      // check if the input is matching the format mm/dd/yyyy
      const dateRegex =
        /^(?:(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/(\d{4}))$/;
      const match = input.match(dateRegex);
      if (!match) return false; // Invalid format
      let [, month, day, year] = match;
      day = parseInt(day, 10);
      month = parseInt(month, 10);
      year = year ? parseInt(year, 10) : new Date().getFullYear(); // Use current year if not provided
      // Calculate the number of days in the given month and year
      const daysInMonth = new Date(year, month, 0).getDate();
      // Return true if the day is valid for the given month, otherwise false
      return day <= daysInMonth; // Check if day is valid for the given month
    };
    const emailRegex = /^[a-zA-Z0-9+._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+$/;
    // Function to check if the given input is a valid integer array
    const isValidNumberArray = (input) => {
      try {
        // Check if the input is an array
        if (!isArray(input)) {
          return false;
        }
        // parse the input string to get the array
        const parsedArray = customJsonParseForInt(input);
        // Check if the parsed array every item is a non-empty string
        if (parsedArray?.some((x) => x?.trim() === "")) {
          return false; // Not an array
        }
        // Check if the parsed array every item is a number
        for (const item of parsedArray) {
          if (isNaN(Number(item))) {
            return false; // Array contains non-number value
          }
        }

        return true; // Valid number array
      } catch {
        return false; // Invalid JSON syntax
      }
    };
    // Function to check if the given input is a valid string array
    const isValidStringArray = (input) => {
      try {
        // Check if the input is an array and if each element is a valid string
        if (!isArray(input) || !isStringArrayValid(input.slice(1, -1))) {
          return false;
        }
        // parse the input string to get the array
        const parsedArray = customJsonParseForString(input);
        // Check if the parsed array every item is a non-empty string
        if (
          parsedArray?.some((x) => x?.trim() === "" && typeof x === "string")
        ) {
          return false; // Not an array
        }

        return true; // Valid number array
      } catch {
        return false; // Invalid JSON syntax
      }
    };

    const matchedTypes = [];
    // Check if the input matches any of the above types
    if (booleanRegex.test(userInput)) {
      matchedTypes.push({
        label: convertToBoolean(userInput),
        value: convertToBoolean(userInput),
        meta: { dataType: DATATYPE.BOOLEAN, dataTypeId: 3 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    if (percentageRegex.test(userInput)) {
      matchedTypes.push({
        label: userInput,
        value: Number(userInput.slice(0, -1)),
        meta: { dataType: DATATYPE.PERCENTAGE, dataTypeId: 6 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    if (numberRegex.test(userInput)) {
      matchedTypes.push({
        label: userInput,
        value: Number(userInput),
        meta: { dataType: DATATYPE.INTEGER, dataTypeId: 1 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    if (emailRegex.test(userInput)) {
      matchedTypes.push({
        label: userInput.toLowerCase(),
        value: userInput.toLowerCase(),
        meta: { dataType: DATATYPE.EMAIL, dataTypeId: 12 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    if (isValidNumberArray(userInput)) {
      matchedTypes.push({
        label: `Array - ${customJsonParseForInt(userInput)?.join(", ")}`,
        value: customJsonParseForInt(userInput).map((x) => Number(x)),
        meta: { dataType: DATATYPE.INTARRAY, dataTypeId: 13 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    // remove the whitespace between the elements of the array
    const removeWhitespaceInArrayString = userInput.replace(
      /"\s*,\s*"/g,
      '","'
    );
    if (isValidStringArray(removeWhitespaceInArrayString)) {
      matchedTypes.push({
        label: `Array - ${customJsonParseForString(
          removeWhitespaceInArrayString
        )?.join(", ")}`,
        value: customJsonParseForString(removeWhitespaceInArrayString),
        meta: { dataType: DATATYPE.STRINGARRAY, dataTypeId: 14 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    if (isValidDate(userInput)) {
      matchedTypes.push({
        label: convertDateFormat(userInput),
        value: convertDateFormat(userInput, "-"),
        meta: { dataType: DATATYPE.DATE, dataTypeId: 2 },
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      });
    }
    matchedTypes.push({
      label: userInput,
      value: userInput,
      group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
      meta: { dataType: DATATYPE.STRING, dataTypeId: 4 },
    });

    // if the input is not a valid date, then add the placeholder
    if (!isValidDate(userInput)) {
      matchedTypes.push({
        label: "Choose date",
        value: DATE_PLACEHOLDER,
        group: EXPRESSION_TOKEN_TYPES.CONSTANTS,
        meta: { dataType: DATATYPE.DATE, dataTypeId: 2 },
      });
    }

    return matchedTypes;
  }
  return [];
};

/**
 * Function to check current token has constant suggestions based on previous token group
 * @param {String} previousIndexGroup - previous token group
 * @returns {Boolean} - true if current token group is not constant or function or datasheet variable
 */
export const hasConstantSuggestions = (previousIndexGroup) => {
  if (
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.FUNCTIONS ||
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES ||
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.CONSTANTS ||
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.CLIENT_VARIABLES ||
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.USER_DEFINED_FUNCTIONS ||
    previousIndexGroup === EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES
  ) {
    return false;
  }
  return true;
};

function setFunctionArgs(item) {
  return item?.args ?? ["TieredPercentage", "TieredValue"].includes(item.label)
    ? []
    : [{ token: { name: "args" } }];
}

function setFunctionName(item) {
  return item.label;
}
/**
 * Function to convert the token to the format required by the expression designer
 * @param {Object} item - token object
 * @returns {Object} token
 * The format is as follows:
 * {
 *  tokenType: <tokenType>,
 *  token: {
 *     name: <tokenName>,
 *     key: <tokenKey>,
 *     args: <tokenArgs>,
 *     dataType: <tokenDataType>,
 *     functionName: <tokenFunctionName>,
 *     type: <tokenType>,
 *     tokenCategory: <tokenCategory>
 *   }

 */
export function convertToToken(item) {
  switch (item.group) {
    case EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES: {
      return {
        tokenType: item.group,
        token: {
          systemName: item.value,
          key: item.value,
          name: item.label,
          ...(item.variableId && { variableId: item.variableId }),
          ...(item.sourceNameHistory && { path: item.sourceNameHistory }),
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.DATASHEET_INTER_VARIABLE: {
      return {
        tokenType: item.group,
        token: {
          systemName: item.value,
          key: item.value,
          name: item.label,
          meta: item.meta,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.CLIENT_VARIABLES: {
      return {
        tokenType: item.group,
        token: {
          systemName: item.value,
          key: item.value,
          name: item.label,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.USER_DEFINED_FUNCTIONS: {
      return {
        tokenType: item.group,
        token: {
          systemName: item.value,
          key: item.value,
          name: item.label,
          dataType: item.meta.dataType,
          dataTypeId: item.meta.dataTypeId,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.OPERATORS: {
      return {
        tokenType: item.group,
        token: {
          name: item.value,
          key: item.value,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.CONSTANTS: {
      return {
        tokenType: EXPRESSION_TOKEN_TYPES.CONSTANTS,
        token: {
          args: [item.meta.dataType, item.value],
          name: item.label,
          key: item.value,
          dataType: item.meta.dataType,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.BRACKETS: {
      return {
        tokenType: item.group,
        token: {
          name: item.value,
          key: item.value,
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.FUNCTIONS: {
      return {
        tokenType: item.group,
        token: {
          name: setFunctionName(item),
          key: item.value,
          dataType: item?.meta?.dataType,
          functionName: item.value,
          args: setFunctionArgs(item),
          type: "VARIABLE",
          tokenCategory: "DYNAMIC",
        },
      };
    }
    case EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES: {
      return {
        tokenType: item.group,
        token: {
          systemName: item.value,
          key: item.value,
          name: item.label,
          dataType: item?.meta?.dataType,
          dataTypeId: item?.meta?.dataTypeId,
        },
      };
    }
  }
  return null;
}

/**
 * No Label arg means the arg is either an expression array or a string
 * So here we will check the datatype:
 * In case of string - return the as it is
 * In case of expression array - return the label each token space separated
 */
export function handleNoLabelArg(arg, currentFunctionToken) {
  if (!arg) return arg;
  if (
    ["Concat", "Coalesce"].includes(currentFunctionToken?.token.functionName)
  ) {
    return Array.isArray(arg)
      ? `[${arg?.map((x) => x.token.name).join(", ")}]`
      : arg;
  }
  if (
    currentFunctionToken?.token.functionName ===
      EXPRESSION_FUNCTION_TYPES.Rolling ||
    currentFunctionToken?.token.functionName === EXPRESSION_FUNCTION_TYPES.Rank
  ) {
    return Array.isArray(arg) && arg?.length > 0
      ? `Partition by ${arg?.map((x) => x.token.name).join(", ")}`
      : arg;
  }

  if (
    currentFunctionToken?.token.functionName ===
    EXPRESSION_FUNCTION_TYPES.GetUserProperty
  ) {
    if (arg === FOR_WHOM_THE_COMMISSION_IS_CALCULATED_KEY) {
      return FOR_WHOM_THE_COMMISSION_IS_CALCULATED_DISPLAY;
    }
    if (arg === CURRENT_DATE_KEY) {
      return CURRENT_DATE_DISPLAY;
    }
  }

  // Check if the function name in the token property of currentFunctionToken
  // is either StartDate or LastDate
  if (
    // [
    //   EXPRESSION_FUNCTION_TYPES.StartDate,
    //   EXPRESSION_FUNCTION_TYPES.LastDate,
    //   EXPRESSION_FUNCTION_TYPES.DATEDIFF,
    //   EXPRESSION_FUNCTION_TYPES.GET_DATE,
    // ].includes(currentFunctionToken?.token.functionName)
    isDateRelatedFunction(currentFunctionToken?.token.functionName)
  ) {
    // Retrieve the payoutPeriods from the token
    const payoutPeriods = currentFunctionToken?.token?.payoutPeriods;

    // Return the value corresponding to the 'arg' index in payoutPeriods,
    // or 'arg' itself if the index is not found
    return payoutPeriods[arg] || arg;
  }

  // if arg is an array
  return Array.isArray(arg)
    ? arg?.map((x) => x?.token?.name).join(" ")
    : isObject(arg)
    ? arg?.name
    : arg;
}

export function prepareFunctionToken(currentToken) {
  const newToken = cloneDeep(currentToken);
  newToken.token.displayArgs = cloneDeep(newToken.token.args);
  if (
    newToken.token.functionName.includes("Quota") &&
    newToken.token.displayArgs[1] &&
    newToken.token.displayArgs[1] != 0
  ) {
    newToken.token.displayArgs[1] = -newToken.token.displayArgs[1];
  }
  // Check if the function name in 'newToken' is 'Rank'
  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.Rank) {
    // If the third argument is "asc" or "desc", modify it to represent an order
    if (["asc", "desc"].includes(newToken.token.displayArgs[2])) {
      const order = newToken.token.displayArgs[2] === "asc" ? "Asc" : "Desc";
      newToken.token.displayArgs[2] = `ORDER by ${order}`;
    }
    // If the fourth argument is false, remove it, else update it to "Skip Rank"
    if (newToken.token.displayArgs[3] === true) {
      newToken.token.displayArgs[3] = "Skip Rank";
    }
    if (newToken.token.displayArgs[3] === false) {
      newToken.token.displayArgs?.pop();
    }
  }
  // Check if the function name is 'Rolling'
  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.Rolling) {
    // Slice 'displayArgs' to keep only the first two elements
    newToken.token.displayArgs = newToken.token.displayArgs.slice(0, 2);
  }
  // Check if the function name is 'DateAdd'
  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.DateAdd) {
    // Swap the second and third arguments in 'displayArgs'
    const clonedDisplayArgs = cloneDeep(newToken.token.displayArgs);
    clonedDisplayArgs[1] = newToken.token.displayArgs[2];
    clonedDisplayArgs[2] = newToken.token.displayArgs[1];
    newToken.token.displayArgs = clonedDisplayArgs;
  }
  // Check if the function name is 'StartDate' or 'LastDate'
  if (
    newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.StartDate ||
    newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.LastDate
  ) {
    // If the second argument is "Month", remove it
    if (newToken.token.displayArgs[1] === PAYOUT_PERIOD.MONTH) {
      newToken.token.displayArgs?.pop();
    }
  }

  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.DateIsIn) {
    // If the third argument is "Month", remove the fiscal unit argument from the display
    if (newToken.token.displayArgs[2] === PAYOUT_PERIOD.MONTH) {
      newToken.token.displayArgs?.pop();
    }
  }

  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.DATEDIFF) {
    if (
      newToken.token.displayArgs[2] === PAYOUT_PERIOD.MONTH ||
      newToken.token.displayArgs[2] === PAYOUT_PERIOD.DAY
    ) {
      newToken.token.displayArgs?.pop();
    }
  }
  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.GET_DATE) {
    if (newToken.token.displayArgs[1]?.token?.name === PAYOUT_PERIOD.DAY) {
      newToken.token.displayArgs?.pop();
    }
  }
  // Check if the function name is 'Timezone' and 'timezoneMap' property exists in 'token'
  if (
    newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.Timezone &&
    newToken.token?.timezoneMap
  ) {
    // Replace the timezones in 'displayArgs' using the 'timezoneMap'
    const clonedDisplayArgs = cloneDeep(newToken.token.displayArgs);
    clonedDisplayArgs[1] =
      newToken.token?.timezoneMap[newToken.token.displayArgs[1]];
    clonedDisplayArgs[2] =
      newToken.token?.timezoneMap[newToken.token.displayArgs[2]];
    newToken.token.displayArgs = clonedDisplayArgs;
  }

  if (newToken.token.functionName === EXPRESSION_FUNCTION_TYPES.Hierarchy) {
    // args[6] -> start date column
    if (newToken.token.displayArgs[6] === null) {
      newToken.token.displayArgs[6] = "null";
    }
    // args[7] -> end date column
    if (newToken.token.displayArgs[7] === null) {
      newToken.token.displayArgs[7] = "null";
    }

    // If Cross Link is disabled(args[10]) , remove the databook details(args[2])
    if (!newToken.token.displayArgs[10]) {
      newToken.token.displayArgs[2] = null;
    }

    // 8 9 and 10 args are additional variables,so removing from expression box display
    newToken.token.displayArgs[8] = null;
    newToken.token.displayArgs[9] = null;
    newToken.token.displayArgs[10] = null;
  }

  if (
    newToken.token.functionName ===
    EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy
  ) {
    if (newToken.token.displayArgs[1] === -1) {
      newToken.token.displayArgs[1] = "Top";
    }
  }
  return newToken;
}

export function autoSuggestionByot(data, t) {
  return data?.map((item) => {
    switch (item.label) {
      case EXPRESSION_FUNCTION_TYPES.Quota: {
        item.label = t("QUOTA");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaErosion: {
        item.label = t("QUOTA_EROSION");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES.QuotaAttainment: {
        item.label = t("QUOTAATTAINTMENT");
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-Quota"]: {
        item.label = `TEAM-${t("QUOTA")}`;
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaErosion"]: {
        item.label = `TEAM-${t("QUOTA_EROSION")}`;
        break;
      }
      case EXPRESSION_FUNCTION_TYPES["TEAM-QuotaAttainment"]: {
        item.label = `TEAM-${t("QUOTAATTAINTMENT")}`;
        break;
      }
      default: {
        break;
      }
    }
    return item;
  });
}

export function convertToDatasheetVariables(fields, dataTypesById) {
  return fields.map((field) => {
    return {
      group: EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES,
      label: field?.displayName,
      meta: {
        dataTypeId: field?.dataTypeId,
        dataType: dataTypesById[field?.dataTypeId],
      },
      value: field?.systemName,
      sourceCfMetaData: field.sourceCfMetaData,
    };
  });
}

// return the status of the api
export function apiStatus(message = "") {
  return {
    status: "LOADING",
    message: message,
  };
}

export const convertConstantToToken = (constant, dataType) => {
  if (!constant) {
    return null;
  }
  let token = {
    tokenType: EXPRESSION_TOKEN_TYPES.CONSTANTS,
    token: {
      args: [dataType, constant],
      name: constant,
      key: constant,
      dataType: dataType,
    },
  };
  return token;
};

export const compareIfIntegerOrPercentage = (dataTypeNameA, dataTypeNameB) => {
  if (!dataTypeNameA || !dataTypeNameB) {
    return false;
  }
  let dataTypeAIsIntegerOrPercentage =
    dataTypeNameA.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameA.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  let dataTypeBIsIntegerOrPercentage =
    dataTypeNameB.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameB.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  return dataTypeAIsIntegerOrPercentage && dataTypeBIsIntegerOrPercentage;
};

export const isDateRelatedFunction = (functionName) => {
  return [
    EXPRESSION_FUNCTION_TYPES.StartDate,
    EXPRESSION_FUNCTION_TYPES.LastDate,
    EXPRESSION_FUNCTION_TYPES.DATEDIFF,
    EXPRESSION_FUNCTION_TYPES.GET_DATE,
    EXPRESSION_FUNCTION_TYPES.DateIsIn,
  ].includes(functionName);
};

export function DataTypeIcon({ name }) {
  const iconComponentMap = {
    Integer: (
      <div className="bg-ever-chartColors-8 w-5 h-5 flex items-center justify-center rounded">
        <HashIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    String: (
      <div className="bg-ever-chartColors-6 w-5 h-5 flex items-center justify-center rounded">
        <TypeIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    Date: (
      <div className="bg-ever-chartColors-24 w-5 h-5 flex items-center justify-center rounded">
        <CalendarIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    Boolean: (
      <div className="bg-ever-chartColors-2 w-5 h-5 flex items-center justify-center rounded">
        <ContrastIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    Percentage: (
      <div className="bg-ever-chartColors-9 w-5 h-5 flex items-center justify-center rounded">
        <PercentAltIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    Email: (
      <div className="bg-ever-chartColors-25 w-5 h-5 flex items-center justify-center rounded">
        <AtSignIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
    Hierarchy: (
      <div className="bg-ever-chartColors-25 w-5 h-5 flex items-center justify-center rounded">
        <HierarchyIcon className="w-3 h-3 text-ever-base-content" />
      </div>
    ),
  };
  return iconComponentMap[name] || <></>;
}
/**
 * Locally validates an expression, and if the local validation result is deemed valid,
 * utilizes an API endpoint for additional validation. If the local validation result is not valid,
 * the same status is returned.
 *
 * @param {Array} expression - An array representing the expression to be validated.
 * @param {Object} options - Options for validation including URL, body, and access token.
 * @param {string} [options.planType] - Specifies the plan type, which determines the URL for the validation API.
 * @param {Object} options.body - The request body to be sent to the validation API.
 * @param {string} options.accessToken - The access token for authentication with the validation API.
 * @param {Object} ongoingApiController - Object( React Ref) to handle aborting ongoing API requests.
 * @returns {Object} - An object containing the validation status and additional information.
 *   - status: One of the EXPRESSION_BOX_STATUS values indicating the validation status.
 *   - message: A string providing additional details about the validation status.
 *   - Other properties may be present based on the API response.
 * @throws {Error} - Throws an error if there is an issue with the API request.
 */
export async function handleValidationWithApi(
  expression,
  { body, accessToken, planType = null },
  ongoingApiController
) {
  // Check if there is an ongoing API request, abort it if so.
  if (ongoingApiController.current) {
    ongoingApiController.current.abort();
    // set the abortControllerObj to null
    ongoingApiController.current = null;
  }
  // Validate the expression locally.
  const validationResult = validateExpression(expression);
  // If local validation is successful and API details are provided, make an API request
  if (
    validationResult.status === EXPRESSION_BOX_STATUS.VALID &&
    body &&
    accessToken
  ) {
    try {
      ongoingApiController.current = new AbortController();
      // Get the signal from the abort controller.
      const { signal } = ongoingApiController.current;
      // Make the API request for validation.
      const result = await apiValidate(body, accessToken, signal, planType);
      // Handle the API response and return the result.
      if (result.dataType === EXPRESSION_BOX_STATUS.INVALID) {
        return {
          ...result,
          message: result?.msg || "Invalid expression",
        };
      }
      return result;
    } catch (error) {
      // Handle API request errors and return an appropriate status.
      return {
        status: EXPRESSION_BOX_STATUS.ABORTED,
        message: error?.msg || "Aborted",
      };
    }
  }
  // If local validation fails or API details are incomplete, return the local validation result.
  return validationResult;
}

/**
 * Validates a given expression locally before sending it to the API for further validation.
 *
 * @param {Array} expression - An array representing the expression to be locally validated.
 * @returns {Object} - An object containing the validation status and additional information.
 *   - status: One of the EXPRESSION_BOX_STATUS values indicating the validation status.
 *   - message: A string providing additional details about the validation status.
 */
export function validateExpression(expression) {
  // if expression is empty or if the expression contains mock tokens, then return initial status
  if (
    expression.length === 0 ||
    expression.findIndex((x) => x.mockToken === true) > -1
  ) {
    const res = {
      status: EXPRESSION_BOX_STATUS.INITIAL,
      message: "",
    };
    // trigger the onChange event
    return res;
  }

  // If the expression contains only bracket tokens, return a warning status
  if (
    expression?.every(
      (exp) => exp?.tokenType === EXPRESSION_TOKEN_TYPES.BRACKETS
    )
  ) {
    const res = {
      status: EXPRESSION_BOX_STATUS.WARNING,
      message: "Please provide a valid expression",
    };
    return res;
  }

  // if the last token is an operator, then return invalid status
  const lastOperatorType = expression.at(-1).tokenType;
  if (lastOperatorType === "OPERATORS") {
    const res = {
      status: EXPRESSION_BOX_STATUS.WARNING,
      message: "Please provide a valid expression",
    };

    return res;
  }

  let leftBracketCount = 0;
  let rightBracketCount = 0;
  // count the number of left and right brackets
  for (let item of expression) {
    if (
      item.tokenType == EXPRESSION_TOKEN_TYPES.BRACKETS &&
      item.token.name == EXPRESSION_TOKEN_TYPES.LEFT_BRACKET
    ) {
      leftBracketCount++;
    }
    if (
      item.tokenType == EXPRESSION_TOKEN_TYPES.BRACKETS &&
      item.token.name == EXPRESSION_TOKEN_TYPES.RIGHT_BRACKET
    ) {
      rightBracketCount++;
    }
  }
  // if the number of left and right brackets are not equal, then return invalid status
  if (leftBracketCount === rightBracketCount) {
    return {
      status: EXPRESSION_BOX_STATUS.VALID,
      message: "valid expression",
    };
  } else {
    return {
      status: EXPRESSION_BOX_STATUS.INVALID,
      message: "Invalid Expression,Mismatching Brackets",
    };
  }
}
export const isDataSheetVariable = (tokenType) => {
  return [
    EXPRESSION_TOKEN_TYPES.DATASHEET_INTER_VARIABLE,
    EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES,
  ].includes(tokenType);
};

export const isQuoteVariable = (tokenType) => {
  return tokenType === EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES;
};

export const initialTokenSuggestions = [
  EXPRESSION_TOKEN_TYPES.FUNCTIONS,
  EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES,
  EXPRESSION_TOKEN_TYPES.DATASHEET_INTER_VARIABLE,
  EXPRESSION_TOKEN_TYPES.CLIENT_VARIABLES,
  EXPRESSION_TOKEN_TYPES.USER_DEFINED_FUNCTIONS,
  EXPRESSION_TOKEN_TYPES.QUOTE_VARIABLES,
];

export const isFollowedByOperator = (token) => {
  return (
    initialTokenSuggestions.includes(token?.tokenType) ||
    token?.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS ||
    (token?.tokenType === EXPRESSION_TOKEN_TYPES.BRACKETS &&
      token?.token.name === ")")
  );
};

export const isFollwedByBracket = (token) => {
  return (
    initialTokenSuggestions.includes(token?.tokenType) ||
    token?.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS ||
    (token?.tokenType === EXPRESSION_TOKEN_TYPES.BRACKETS &&
      token?.token?.name === ")")
  );
};

export const bracketColorPalette = [
  "text-ever-base-content",
  "text-ever-chartColors-5",
  "text-ever-chartColors-10",
  "text-ever-chartColors-12",
  "text-ever-chartColors-13",
  "text-ever-chartColors-15",
  "text-ever-chartColors-16",
  "text-ever-chartColors-17",
  "text-ever-chartColors-21",
  "text-ever-chartColors-50",
  "text-ever-chartColors-48",
  "text-ever-chartColors-26",
];
