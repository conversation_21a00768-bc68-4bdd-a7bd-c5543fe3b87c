import {
  CheckIcon,
  ChevronSelectorVerticalIcon,
  XCloseIcon,
} from "@everstage/evericons/outlined";
import { BuildingIcon } from "@everstage/evericons/solid";
// import { IcmIcon } from "@everstage/evericons/variety";
import { decode as base64_decode } from "base-64";
import { getUnixTime } from "date-fns";
import { isEmpty } from "lodash";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";

import { EverGroupAvatar, EverModal, EverTg, EverTooltip } from "..";

/**
 * @component ClientSwitchModal
 * @description Renders a modal for switching clients.
 * @param {object} props - The component props.
 * @param {string} [props.location=""] - The location of the dropdown, depending upon which the componenet's look different
 * @param {function} props.dontClosePopover - The function to set the dontClosePopover state, if true then interactions made on the modal will not close the user dropdown
 */
export function ClientSwitchModal({ location = "", dontClosePopover }) {
  const myClient = useRecoilValue(myClientAtom);
  const { logoUrl, name, metaInfo } = myClient;
  const authStore = useAuthStore();
  const {
    isLoggedInAsUser,
    isSupportUser,
    clients: clientsData,
    selectedClientId: authStoreSelectedClientId,
  } = authStore;

  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState("");
  const [selectedClientId, setSelectedClientId] = useState(null);

  const modifiedLogoUrl = isEmpty(metaInfo?.updatedAt)
    ? logoUrl
    : `${logoUrl}?ts=${getUnixTime(new Date(metaInfo.updatedAt))}`;

  async function handleSwitchClient(client) {
    const response = await authStore.setClient({
      selectedClientId: client.clientId,
      isSelectionDefault: false,
    });
    if (response.ok) {
      // Clear the recent datasheets section when switching accounts
      window.localStorage.setItem("recent_datasheets", JSON.stringify([]));
      window.localStorage.setItem("selectedClientId", client.clientId);
      window.dispatchEvent(new Event("storage"));
    } else {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  }

  useEffect(() => {
    if (selectedClientId && clients)
      for (const ele of clients) {
        if (ele.clientId == selectedClientId) {
          setSelectedClient(ele);
        }
      }
  }, [selectedClientId, clients]);

  useEffect(() => {
    const getMulticlientInfo = async () => {
      const clientIds = clientsData.map((client) => String(client.clientId));
      setClients(clientsData);
      const clientIdInSearchParams = new URLSearchParams(
        window.location.search
      ).get("client_id");
      let decodedClientId = null;
      if (!isEmpty(clientIdInSearchParams)) {
        try {
          decodedClientId = base64_decode(clientIdInSearchParams);
        } catch {
          decodedClientId = null;
        }
      }
      if (clientIds.includes(decodedClientId)) {
        window.localStorage.setItem("selectedClientId", decodedClientId);
        setSelectedClientId(Number(decodedClientId));
      } else {
        setSelectedClientId(authStoreSelectedClientId);
      }
      window.addEventListener("storage", () => {
        const localStorageSelectedClientId =
          window.localStorage.getItem("selectedClientId");
        if (
          (localStorageSelectedClientId &&
            clientsData &&
            localStorageSelectedClientId !==
              String(authStoreSelectedClientId) &&
            authStoreSelectedClientId !== null) ||
          (decodedClientId &&
            localStorageSelectedClientId !== String(decodedClientId))
        ) {
          window.location.replace("/");
        }
      });
    };
    getMulticlientInfo();
  }, [clientsData, authStoreSelectedClientId]);

  return clients?.length > 1 && !isLoggedInAsUser && !isSupportUser ? (
    <ClientSwitchTrigger
      name={name}
      logoUrl={logoUrl}
      modifiedLogoUrl={modifiedLogoUrl}
      clients={clients}
      selectedClient={selectedClient}
      setSelectedClient={setSelectedClient}
      handleSwitchClient={handleSwitchClient}
      location={location}
      dontClosePopover={dontClosePopover}
    />
  ) : (
    <ClientNameDisplay
      name={name}
      logoUrl={logoUrl}
      modifiedLogoUrl={modifiedLogoUrl}
      location={location}
    />
  );
}

function ClientAvatar({ logoUrl, modifiedLogoUrl, name, className }) {
  return (
    <div
      className={twMerge(
        "flex shadow-lg w-7 h-7 items-center justify-center bg-ever-base-25 rounded-lg",
        className
      )}
    >
      {logoUrl ? (
        <img
          src={modifiedLogoUrl}
          className="w-full h-full object-contain rounded-lg"
        />
      ) : (
        <EverGroupAvatar avatars={[{ firstName: name, lastName: "" }]} />
      )}
    </div>
  );
}

function ClientSwitchTrigger({
  name,
  logoUrl,
  modifiedLogoUrl,
  clients,
  selectedClient,
  setSelectedClient,
  handleSwitchClient,
  location,
  dontClosePopover,
}) {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <>
      <div
        className={twMerge(
          "flex flex-col py-1",
          location === "UserProfileDropdown"
            ? "hover:bg-ever-base-100 rounded cursor-pointer px-4"
            : "px-4"
        )}
        onClick={() => {
          if (location === "UserProfileDropdown") {
            setDropdownOpen(!dropdownOpen);
            dontClosePopover(true);
          }
        }}
      >
        <div
          className={twMerge(
            "flex items-center w-max-full flex-nowrap",
            location === "UserProfileDropdown" && "gap-4 w-full pr-1",
            location === "HeaderBar" && "gap-2.5"
          )}
        >
          <ClientAvatar
            className={twMerge(
              "grow-0 shrink-0",
              location === "HeaderBar" && "translate-y-px"
            )}
            logoUrl={logoUrl}
            modifiedLogoUrl={modifiedLogoUrl}
            name={name}
          />
          <EverTooltip
            title={name}
            mouseEnterDelay={0.8}
            mode="dark"
            placement="right"
          >
            <div
              className={twMerge(
                "flex items-center gap-1.5 cursor-pointer overflow-hidden",
                location === "UserProfileDropdown" && "justify-between grow",
                location === "HeaderBar" &&
                  "h-6 pl-2 pr-1 py-1 translate-y-px hover:bg-ever-accent-500 rounded"
              )}
              onClick={() => {
                if (location === "HeaderBar") {
                  setDropdownOpen(!dropdownOpen);
                }
              }}
            >
              <EverTg.Text
                className={twMerge(
                  "font-medium truncate",
                  location === "UserProfileDropdown"
                    ? "text-ever-base-content"
                    : "text-ever-base text-[13px]"
                )}
              >
                {name}
              </EverTg.Text>
              <ChevronSelectorVerticalIcon
                className={twMerge(
                  "size-4 shrink-0 grow-0",
                  location === "UserProfileDropdown"
                    ? "text-ever-base-content"
                    : "text-ever-accent-100"
                )}
              />
            </div>
          </EverTooltip>
        </div>
      </div>
      <EverModal
        width={348}
        visible={dropdownOpen}
        onCancel={() => {
          setDropdownOpen(false);
          dontClosePopover(false);
        }}
        closeIcon={
          <XCloseIcon className="h-5 w-5 text-ever-base-content-mid" />
        }
      >
        <div>
          <EverTg.Heading3>Switch accounts</EverTg.Heading3>
          <div className="flex flex-col max-h-[400px] overflow-auto -mx-6 gap-4 mt-4">
            {/* <div className="py-2 flex items-center gap-3 px-5 bg-gradient-to-r from-ever-primary/10 to-ever-base border-b border-solid border-ever-primary/5">
              <IcmIcon className="size-8" />
              <EverTg.Caption className="text-ever-base-content-mid">
                Incentive Compensation Managament (ICM)
              </EverTg.Caption>
            </div> */}
            <div className="flex flex-col cursor-pointer gap-2.5 px-4">
              {clients.map((client) => {
                return (
                  <div
                    className={twMerge(
                      "w-full p-1 flex rounded justify-between items-center hover:bg-ever-base-50 h-10 pr-3",
                      selectedClient.clientId === client.clientId
                        ? "bg-ever-primary-lite border border-solid border-ever-primary-ring"
                        : ""
                    )}
                    onClick={() => {
                      setSelectedClient(client);
                      handleSwitchClient(client);
                    }}
                    key={client.clientId}
                  >
                    <div className="flex items-center gap-2">
                      {client.src ? (
                        <EverGroupAvatar
                          avatars={[
                            {
                              image: client.src,
                              firstName: client.clientName,
                              shape: "square",
                              className:
                                "rounded-lg size-8 bg-transparent !border-none text-ever-base-content-low",
                              fallBack: <BuildingIcon className="size-6" />,
                            },
                          ]}
                        />
                      ) : (
                        <BuildingIcon className="size-4" />
                      )}
                      <EverTg.Text className="w-40 truncate">
                        {client.clientName}
                      </EverTg.Text>
                    </div>
                    {selectedClient.clientId === client.clientId && (
                      <CheckIcon className="size-4 text-ever-primary" />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </EverModal>
    </>
  );
}

function ClientNameDisplay({ name, logoUrl, modifiedLogoUrl, location }) {
  return (
    <div
      className={twMerge(
        "flex items-center w-max-full flex-nowrap py-1",
        location === "UserProfileDropdown" && "gap-3 px-4",
        location === "HeaderBar" && "gap-px"
      )}
    >
      <div
        className={twMerge(
          location === "HeaderBar" && "w-16 flex justify-center grow-0 shrink-0"
        )}
      >
        <ClientAvatar
          className={twMerge(
            "grow-0 shrink-0",
            location === "HeaderBar" && "translate-y-0.5"
          )}
          logoUrl={logoUrl}
          modifiedLogoUrl={modifiedLogoUrl}
          name={name}
        />
      </div>
      <EverTooltip
        title={name}
        mouseEnterDelay={0.4}
        mode="dark"
        placement="right"
      >
        <div className="overflow-hidden flex">
          <EverTg.Text
            className={twMerge(
              "font-medium inline-block truncate",
              location === "UserProfileDropdown" && "text-ever-base-content",
              location === "HeaderBar" &&
                "translate-y-px text-ever-base text-[13px]"
            )}
          >
            {name}
          </EverTg.Text>
        </div>
      </EverTooltip>
    </div>
  );
}
