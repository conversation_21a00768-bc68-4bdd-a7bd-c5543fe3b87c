import {
  HelpCircleIcon,
  HeadphonesIcon,
  BookOpenIcon,
} from "@everstage/evericons/outlined";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { freshdeskTicketForm } from "~/Utils/FreshdeskUtils";
import { useModules } from "~/v2/hooks";

import { EverHotToastMessage, toast } from "../ever-popups";
import { EverTooltip } from "../EverTooltip";

export const HelpCenter = observer(() => {
  const showHelpIcon = [
    "DEV",
    "LOCALDEV",
    "STAGING",
    "DEMO",
    "QA",
    "PRODUCTION",
    "PRODUCTION-EU",
  ].includes(process.env.REACT_APP_ACTUAL_ENV);
  const authStore = useAuthStore();
  const { isCPQ } = useModules();
  const { accessToken, email } = authStore;
  const { payeeName, firstName, lastName, userRoleName, userRoleId } =
    useEmployeeStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const [showHelpBubble, setShowHelpBubble] = useState(false);
  const [solutionCornerDisplayed, setSolutionCornerDisplayed] = useState(false);
  const roles = clientFeatures.helpDocUserRole;

  const { hasPermissions } = useUserPermissionStore();
  const hasHelpPermissions = hasPermissions(RBAC_ROLES.ACCESS_HELP_CENTRE);

  useEffect(() => {
    if (userRoleName == "Power Admin") {
      setSolutionCornerDisplayed(true);
    } else if (roles && roles.includes(userRoleId[0])) {
      setSolutionCornerDisplayed(true);
    } else {
      setSolutionCornerDisplayed(false);
    }
  }, [roles, userRoleName, userRoleId]);
  useEffect(() => {
    if (email && payeeName) {
      setShowHelpBubble(true);
    }
  }, [payeeName, email]);
  const navigateToDocumentLink = () => {
    const documentationUrl = clientFeatures.documentationUrl;
    if (documentationUrl == "") {
      console.log("entered else block");
      toast.custom(() => {
        return (
          <EverHotToastMessage
            description="Documentation url is not present."
            type="error"
          />
        );
      });
    } else {
      window.open(documentationUrl, "_blank");
    }
  };

  const triggerFreshdesk = () => {
    freshdeskTicketForm(`${firstName || ""} ${lastName || ""}`.trim(), email);
  };

  const redirectToHelpCenter = async () => {
    toast.custom(() => {
      <EverHotToastMessage title="Opening Help Center" type="loading" />;
    });

    const freshdeskBaseUrl = `https://everstagesupport.myfreshworks.com/sp/OAUTH/${process.env.REACT_APP_FRESHDESK_OAUTH_ID}/login`;
    const loginParams = new URLSearchParams({
      redirect_uri:
        "https://everstage-support.freshdesk.com/freshid/customer_authorize_callback?hd=support.everstage.com",
      client_id: process.env.REACT_APP_FRESHDESK_CLIENT_ID,
      slug: process.env.REACT_APP_FRESHDESK_SLUG,
    }).toString();

    const freshdeskUrl = `${freshdeskBaseUrl}?${loginParams}`;

    window.open(freshdeskUrl);
  };

  if (isCPQ) {
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      {solutionCornerDisplayed && (
        <IconWrapper
          title="Solutions Corner"
          onClick={() => {
            navigateToDocumentLink();
          }}
        >
          <BookOpenIcon className="size-[18px] text-ever-accent-content cursor-pointer" />
        </IconWrapper>
      )}
      <IconWrapper
        title="Contact Everstage support"
        onClick={() => {
          triggerFreshdesk();
          sendAnalyticsEvent(
            accessToken,
            ANALYTICS_EVENTS.VISIT_CONTACT_EVERSTAGE,
            { [ANALYTICS_PROPERTIES.ENTRY_POINT]: "Top Nav" }
          );
        }}
      >
        <HeadphonesIcon className="size-[18px] text-ever-accent-content cursor-pointer" />
      </IconWrapper>
      {showHelpIcon &&
        showHelpBubble &&
        !isCPQ &&
        (hasHelpPermissions ? (
          <IconWrapper
            title="Help center"
            className="cursor-pointer flex items-center justify-center size-8 rounded hover:bg-ever-accent-500"
            onClick={() => {
              redirectToHelpCenter();
              sendAnalyticsEvent(
                accessToken,
                ANALYTICS_EVENTS.VISIT_HELP_CENTER,
                {
                  [ANALYTICS_PROPERTIES.ENTRY_POINT]: "Top Nav",
                }
              );
            }}
          >
            <HelpCircleIcon className="size-[18px] text-ever-accent-content cursor-pointer" />
          </IconWrapper>
        ) : (
          <IconWrapper
            title="Please reach out to your admin or submit a query if you need help."
            className="cursor-pointer flex items-center justify-center size-8 rounded hover:bg-ever-accent-500"
          >
            <HelpCircleIcon className="size-[18px] text-ever-accent-content cursor-pointer" />
          </IconWrapper>
        ))}
    </div>
  );
});

const IconWrapper = ({ children, title, ...props }) => {
  return (
    <EverTooltip title={title}>
      <div
        className="cursor-pointer flex items-center justify-center size-8 rounded hover:bg-ever-accent-500 grow-0 shrink-0"
        {...props}
      >
        {children}
      </div>
    </EverTooltip>
  );
};
