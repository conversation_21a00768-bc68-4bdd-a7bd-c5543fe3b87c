import {
  Dashboard<PERSON>ottie,
  A<PERSON><PERSON><PERSON><PERSON>ottie,
  <PERSON>Lottie,
  ContractsLottie,
  DatabookLottie,
  <PERSON>sLottie,
  PeopleLottie,
  QueriesLottie,
  QuotasLottie,
  <PERSON>tingsLottie,
  StatementsLottie,
  <PERSON>ChartLottie,
  CrystalLottie,
  MagicWandLottie,
  TransformationLottie,
  TerritoryPlansLottie,
} from "@everstage/evericons/lotties";
import {
  PieChartIcon as PieChartIconOL,
  LayoutGridAltIcon as LayoutGridIconOL,
  PercentCircleIcon as PercentIconOL,
  MessageChatSquareIcon as MessageChatSquareIconOL,
  TargetIcon as TargetIconOL,
  EditPencilWavyIcon as EditIconOL,
  UsersFullIcon as UsersIconOL,
  SettingsIcon as SettingsIconOL,
  WalletIcon as WalletIconOL,
  DataflowIcon as DataflowIconOL,
  CrystalIcon as CrystalIconOL,
  FileIcon as FileIconOL,
  SearchMdIcon as SearchIconOL,
  MagicWandIcon as MagicWandIconOL,
  TerritoryPlansIcon as TerritoryPlansIconOL,
} from "@everstage/evericons/outlined";
import {
  <PERSON>etIcon,
  TargetIcon,
  LayoutGridIcon,
  PieChartIcon,
  PercentIcon,
  MessageChatSquareIcon,
  EditIcon,
  UsersIcon,
  SettingsIcon,
  DataflowIcon,
  FileIcon,
  CrystalIcon,
  LineChartIcon,
  CrystalFlareIcon,
  MagicWandIcon,
  TransformationIcon,
  TerritoryPlansIcon,
} from "@everstage/evericons/solid";
export const cpqMenuIcons = {
  quotes: {
    main: <TargetIconOL className="size-[18px]" />,
    active: QuotasLottie,
    selected: <TargetIcon className="size-[18px]" />,
  },
  catalog: {
    main: <FileIconOL className="size-[18px]" />,
    active: StatementsLottie,
    selected: <FileIcon className="size-[18px]" />,
  },
};

export const sidebarMenuIcons = {
  dashboards: {
    main: <PieChartIconOL className="size-[18px]" />,
    active: DashboardLottie,
    selected: <PieChartIcon className="size-[18px] text-ever-accent-content" />,
  },
  agents: {
    main: <MagicWandIconOL className="size-[18px]" />,
    active: MagicWandLottie,
    selected: (
      <MagicWandIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  databook: {
    main: <LayoutGridIconOL className="size-[18px]" />,
    active: DatabookLottie,
    selected: (
      <LayoutGridIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  commissions: {
    main: <PercentIconOL className="size-[18px]" />,
    active: CommissionLottie,
    selected: <PercentIcon className="size-[18px] text-ever-accent-content" />,
  },
  queries: {
    main: <MessageChatSquareIconOL className="size-[18px]" />,
    active: QueriesLottie,
    selected: (
      <MessageChatSquareIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  territoryPlans: {
    main: <TerritoryPlansIconOL className="size-[18px]" />,
    active: TerritoryPlansLottie,
    selected: (
      <TerritoryPlansIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  crystal: {
    main: <CrystalIconOL className="size-[18px]" />,
    active: CrystalLottie,
    selected: (
      <div className="flex relative">
        <CrystalIcon className="size-[18px]" />
        <CrystalFlareIcon className="text-ever-base size-[18px] absolute" />
      </div>
    ),
  },
  quotas: {
    main: <TargetIconOL className="size-[18px]" />,
    active: QuotasLottie,
    selected: <TargetIcon className="size-[18px] text-ever-accent-content" />,
  },
  contracts: {
    main: <EditIconOL className="size-[18px]" />,
    active: ContractsLottie,
    selected: <EditIcon className="size-[18px] text-ever-accent-content" />,
  },
  people: {
    main: <UsersIconOL className="size-[18px]" />,
    active: PeopleLottie,
    selected: <UsersIcon className="size-[18px] text-ever-accent-content" />,
  },
  settings: {
    main: <SettingsIconOL className="size-[18px]" />,
    active: SettingsLottie,
    selected: <SettingsIcon className="size-[18px] text-ever-accent-content" />,
  },
  draws: {
    main: <WalletIconOL className="size-[18px]" />,
    active: DrawsLottie,
    selected: <WalletIcon className="size-[18px] text-ever-accent-content" />,
  },
  approvals: {
    main: <DataflowIconOL className="size-[18px]" />,
    active: ApprovalsLottie,
    selected: <DataflowIcon className="size-[18px] text-ever-accent-content" />,
  },
  statements: {
    main: <FileIconOL className="size-[18px]" />,
    active: StatementsLottie,
    selected: <FileIcon className="size-[18px] text-ever-accent-content" />,
  },
  kpi: {
    main: <LineChartIcon className="size-[18px] scale-150" />,
    active: LineChartLottie,
    selected: (
      <LineChartIcon className="size-[18px] scale-150 text-ever-accent-content" />
    ),
  },
  transformation: {
    main: <TransformationIcon className="size-[18px]" />,
    active: TransformationLottie,
    selected: (
      <TransformationIcon className="size-[18px] text-ever-accent-content" />
    ),
  },
  search: {
    main: <SearchIconOL className="size-[18px]" />,
    active: () => <SearchIconOL className="size-[18px]" />,
    selected: <SearchIconOL className="size-[18px] text-ever-accent-content" />,
  },
  ...cpqMenuIcons,
};

export const getMenuList = (menuList, hasPermissions, extraConditions) => {
  const filteredMenu = [];
  // Helper function to check if an item is accessible based on client features and permissions
  const isItemAccessible = (item) => {
    let baseCondition =
      !item.rbacPermission || hasPermissions(item.rbacPermission);
    // Apply additional condition if it exists for this label
    if (extraConditions[item.label]) {
      baseCondition = baseCondition && extraConditions[item.label]();
    }
    return baseCondition;
  };

  for (const item of menuList) {
    if (isItemAccessible(item)) {
      if (item.type === "submenu" && Array.isArray(item.items)) {
        // Filter accessible submenu items
        // eslint-disable-next-line unicorn/no-array-callback-reference
        const accessibleSubItems = item.items.filter(isItemAccessible);
        // Only add the submenu if it has accessible items
        if (accessibleSubItems.length > 0) {
          filteredMenu.push({
            ...item,
            items: accessibleSubItems,
          });
        }
      } else {
        // Add non-submenu item directly
        filteredMenu.push(item);
      }
    }
  }

  return filteredMenu;
};

export function isRoute(currentPath, buttonPath) {
  if (
    buttonPath.includes("/dashboards") &&
    (currentPath.includes(buttonPath) || buttonPath.includes(currentPath))
  ) {
    return true;
  }
  if (currentPath.startsWith(buttonPath)) return true;
  return false;
}
