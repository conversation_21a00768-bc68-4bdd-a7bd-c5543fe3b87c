import { animated, useSpring } from "@react-spring/web";
// import { debounce } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { everHeaderHeightAtom, myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";

const NON_NAVBAR_ROUTES = ["/datasheet"];

export function EverNavBar({ children = null }) {
  const navbarRef = useRef(null);

  const [style, animate] = useSpring(() => ({ height: "0px" }), []);
  const animationFrameRef = useRef(null);
  const { pathname } = useLocation();
  const basePathName = pathname.split("/")[1];
  const [headerHeight, setHeaderHeight] = useState();
  const setNavbarHeight = useSetRecoilState(everHeaderHeightAtom);
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const isTerritoryPlanPath = pathname.match(/^\/planning\/[^/]+$/);
  useEffect(() => {
    if (!animationFrameRef.current) {
      return null;
    }

    const resizeObserver = new ResizeObserver(() => {
      if (animationFrameRef.current?.offsetHeight !== headerHeight) {
        const animatedHeight = animationFrameRef.current?.offsetHeight;
        setHeaderHeight(animatedHeight);
        setNavbarHeight(animatedHeight);
      }
    });

    resizeObserver.observe(animationFrameRef.current);

    return function cleanup() {
      resizeObserver.disconnect();
    };
  }, [animationFrameRef.current]);

  useEffect(() => {
    if (headerHeight) {
      animate({
        height: headerHeight + "px",
      });
    }
  }, [headerHeight]);

  const isNewSidebar =
    clientFeatures?.enableSidebarV3 ||
    localStorage.getItem("new-sidebar") === "true";

  return (
    <div
      className={twMerge(
        "w-full",
        isNewSidebar && !isTerritoryPlanPath
          ? "bg-ever-base-50 border-b border-solid border-ever-base-200"
          : "",
        `transition duration-200 ease-in-out h-auto`,
        isTerritoryPlanPath && "bg-ever-base-50 h-11",
        (NON_NAVBAR_ROUTES.includes(pathname) ||
          NON_NAVBAR_ROUTES.includes(`/${basePathName}`)) &&
          "!h-0 hidden"
        // scrollingUp ? "translate-y-0" : `-translate-y-full`,
      )}
      ref={navbarRef}
    >
      <animated.div style={style}>
        <div ref={animationFrameRef}>{children}</div>
      </animated.div>
    </div>
  );
}
