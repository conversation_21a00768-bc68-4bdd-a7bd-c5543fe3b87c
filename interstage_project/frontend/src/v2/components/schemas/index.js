/**
 * Contains schemas that can be used for validating component props
 *
 * If this file becomes too big, we can split it into smaller files and re-export them from index.js
 */
import { object, string, nullish, optional, array, number, any } from "valibot";

export const AvatarsSchema = array(
  object({
    firstName: nullish(string("Invalid firstName in EverGroupAvatar")),
    lastName: nullish(string("Invalid lastName in EverGroupAvatar")),
    name: nullish(string("Invalid name in EverGroupAvatar")),
    // Keeping image as any since EverGroupAvatar component accepts string url as well as ReactNode for image.
    image: any(),
    fallBack: nullish(any()),
    style: nullish(
      object({
        backgroundColor: nullish(
          string("Invalid style object in EverGroupAvatar")
        ),
      })
    ),
    className: nullish(string("Invalid className in EverGroupAvatar")),
    shape: nullish(string("Invalid shape for avatar")),
  })
);

export const MetaSchema = object({
  dataTypeId: nullish(string("Invalid dataTypeId in Meta")),
  dataType: nullish(string("Invalid datatype in Meta")),
  operandTypeIds: nullish(array(number("Invalid operandTypeId in Meta"))),
  category: nullish(string("Invalid category in Meta")),
});

export const AutoCompleteContextSchema = array(
  object({
    label: nullish(string("Invalid label in AutoCompleteContext")),
    value: nullish(string("Invalid value in AutoCompleteContext")),
    group: nullish(string("Invalid group in AutoCompleteContext")),
    meta: MetaSchema,
  })
);

export const ExpressionReducerActionScheme = object({
  type: string("Invalid type in expressionReducerActionScheme"),
  position: optional(
    number("Invalid position in expressionReducerActionScheme")
  ),
  value: optional(string("Invalid value in expressionReducerActionScheme")),
});

export const InitialExpressionSchema = array(
  object({
    token: any(),
    type: string("Invalid type in InitialExpressionSchema"),
  })
);

export const ExpressionDesignerStateSchema = object({
  expression: array(
    object({
      token: any(),
      type: string("Invalid type in InitialExpressionSchema"),
    })
  ),
  position: number("Invalid position in ExpressionDesignerStateSchema"),
  autocompleteValues: AutoCompleteContextSchema,
  type: string("Invalid type in ExpressionDesignerStateSchema"),
});

export const ValidationStatusSchema = object({
  status: string("Invalid status in ValidationStatusSchema"),
  message: string("Invalid message in ValidationStatusSchema"),
});

export const CustomTerminologyTableDataSchema = array(
  object({
    label: string("Invalid label type in CustomTerminologyTableDataSchema."),
    key: string("Invalid key type in CustomTerminologyTableDataSchema."),
    value: nullish(
      string("Invalid value type in CustomTerminologyTableDataSchema.")
    ),
    placeholder: string(
      "Invalid placeholder type in CustomTerminologyTableDataSchema."
    ),
  })
);
