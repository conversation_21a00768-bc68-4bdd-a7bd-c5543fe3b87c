import PropTypes from "prop-types";
import React, { useRef } from "react";
import { useLocation, Link } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { NotificationBadge } from "./Common";
import { EverTg } from "../EverTypography";

const buttonStyles = {
  default:
    "relative group w-10 h-10 p-[7.5px] hover:bg-ever-sidebar-icon-hover-background rounded-lg border border-transparent border-solid cursor-pointer",
  current: `relative group w-10 h-10 p-[7.5px] rounded-lg bg-ever-sidebar-icon-selected-background border border-ever-sidebar-icon-selected-border border-solid cursor-pointer`,
};
RouteButton.propTypes = {
  data: PropTypes.object,
  icon: PropTypes.node,
  activeIcon: PropTypes.func,
};

export function RouteButton({ data, icon, activeIcon, selected, badgeList }) {
  const location = useLocation();
  const routeButtonRef = useRef();
  const hasBadge = data.label in badgeList;
  const badgeItem = hasBadge ? badgeList[data.label] : null;
  const Lottie = activeIcon;

  function isRoute(currentPath, buttonPath) {
    if (data.name == "Dashboards") {
      if (currentPath.includes(buttonPath) || buttonPath.includes(currentPath))
        return true;
    }
    if (currentPath.startsWith(buttonPath)) return true;
    return false;
  }

  return (
    <Link to={data.path} className="group relative">
      <div
        id={data.name}
        ref={routeButtonRef}
        className={twMerge(
          isRoute(location.pathname, data.path)
            ? buttonStyles.current
            : buttonStyles.default
        )}
      >
        <div className="z-10 hidden px-4 py-2 group-hover:block left-12 top-0 bg-ever-sidebar-base-content absolute rounded-lg shadow after:content-['_'] after:absolute after:top-1/2 after:right-full after:mt-[-5px] after:border-4 after:border-solid after:border-t-transparent after:border-r-ever-sidebar-base-content after:border-b-transparent after:border-l-transparent">
          <div className="flex justify-center w-full">
            <EverTg.SubHeading4 className="text-ever-base-25">
              {data.name}
            </EverTg.SubHeading4>
          </div>
        </div>
        {hasBadge && badgeItem.count > 0 && (
          <NotificationBadge badgeCount={badgeItem.count} />
        )}
        <div className="hidden group-hover:block">
          <Lottie
            animationTriggerEvent="hover"
            animationTriggerRef={routeButtonRef}
            className="w-6 h-6"
          />
        </div>
        <div className="block group-hover:hidden">
          {isRoute(location.pathname, data.path) ? selected : icon}
        </div>
      </div>
    </Link>
  );
}
