import { gql, useQuery } from "@apollo/client";
import {
  BellIcon,
  LogOutCircleIcon,
  MailFlatIcon,
  UserEditIcon,
  HelpCircleIcon,
  DotsVerticalIcon,
} from "@everstage/evericons/outlined";
import * as Popover from "@radix-ui/react-popover";
import { observer } from "mobx-react";
import React, { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { freshdeskTicketForm } from "~/Utils/FreshdeskUtils";
import { EverTooltip } from "~/v2/components/EverTooltip";
import { useModules } from "~/v2/hooks";

// import { themerOptions } from "~/v2/themes";
import { HelpCenter } from "./HelpCenter";
import { EverButton } from "../ever-button/EverButton";
import { EverHotToastMessage, toast } from "../ever-popups";
import { EverGroupAvatar } from "../EverGroupAvatar";
import { EverModal } from "../EverModal";
import { EverTg } from "../EverTypography";
import { useOnClickOutside } from "../useOnClickOutside";

export const getLogoutRedirectUrl = () => {
  return window.location.origin + "/login";
};

const GET_PROFILE_DETAILS = gql`
  query PayeeProfileDetails {
    payeeProfileDetails
  }
`;

export const UserDetails = observer(() => {
  const authStore = useAuthStore();
  const { isCPQ } = useModules();
  const [isOpen, setIsOpen] = useState(false);
  const [showHelpBubble, setShowHelpBubble] = useState(false);
  const [userName, setUserName] = useState("");
  const { accessToken, email, isLoggedInAsUser, isSupportUser } = authStore;
  const popupRef = useRef(null);
  const {
    payeeName,
    firstName,
    lastName,
    profilePicture,
    userRoleName,
    userRoleId,
  } = useEmployeeStore();
  const [consModalVisible, setConsModalVisible] = useState(false);
  const [solutionCornerDisplayed, setSolutionCornerDisplayed] = useState(false);
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const hasNickname = payeeName ? payeeName : "-";
  const showHelpIcon = [
    "DEV",
    //"LOCALDEV",
    "STAGING",
    "DEMO",
    "QA",
    "PRODUCTION",
    "PRODUCTION-EU",
  ].includes(process.env.REACT_APP_ACTUAL_ENV);

  const { data } = useQuery(GET_PROFILE_DETAILS, {
    fetchPolicy: "no-cache",
  });

  const commonCss = "flex p-3 rounded-md items-center bg-ever-base w-full";

  function Content() {
    // Commenting out the code for theme switcher for now.
    // const themes = [
    //   {
    //     name: "",
    //     extend: themerOptions.defaultTheme.extend,
    //     label: themerOptions.defaultTheme.label,
    //   },
    //   ...themerOptions.themes,
    // ].map((theme) => {
    //   return {
    //     ...theme,
    //     label: theme.label,
    //     color: theme.extend.colors.ever.base.DEFAULT,
    //   };
    // });

    // const setTheme = useSetRecoilState(currentTheme);
    // const myTheme = useRecoilValue(currentTheme);

    // function setThemeData(name) {
    //   localStorage.setItem("theme", name);
    //   setTheme(name);
    // }

    return (
      <div>
        <div
          className={twMerge(
            "w-full mb-2 border border-solid border-ever-base-300 gap-2 flex bg-ever-base-50 py-1 cursor-default justify-start px-2 items-center rounded-lg shadow-sm",
            isLoggedInAsUser ? "h-12" : "h-[54px]"
          )}
        >
          <EverGroupAvatar
            avatars={[{ image: profilePicture, firstName, lastName }]}
          />
          <div className="w-[120px] flex flex-col text-ever-base-content text-ellipsis whitespace-nowrap overflow-hidden">
            <EverTg.Heading4 className="text-ever-base-content text-ellipsis whitespace-nowrap overflow-hidden">
              {userName}
            </EverTg.Heading4>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          {hasNickname !== "-" && !isCPQ && (
            <>
              {/* Showing profile settings when there is no-impoersonation */}
              {!isLoggedInAsUser && !isSupportUser && (
                <Link
                  className="text-ever-base-content hover:!text-ever-base-content"
                  to="/profile-settings"
                >
                  <PopUpButton
                    item={{
                      label: "Profile Settings",
                      icon: UserEditIcon,
                      onClick: () => {},
                    }}
                  />
                </Link>
              )}
              <Link
                className="text-ever-base-content hover:!text-ever-base-content"
                to="/notification-preference"
              >
                <PopUpButton
                  item={{
                    label: "Notifications",
                    icon: BellIcon,
                    onClick: () => {},
                  }}
                />
              </Link>
            </>
          )}
          {showHelpIcon && showHelpBubble && !isCPQ && (
            <>
              <PopUpButton
                item={{
                  label: "Contact us",
                  icon: MailFlatIcon,
                  onClick: () => {
                    triggerFreshdesk();
                    sendAnalyticsEvent(
                      accessToken,
                      ANALYTICS_EVENTS.VISIT_CONTACT_EVERSTAGE,
                      { [ANALYTICS_PROPERTIES.ENTRY_POINT]: "Top Nav" }
                    );
                  },
                }}
              />
              {solutionCornerDisplayed ? (
                <PopUpButton
                  item={{
                    label: "Solutions Corner",
                    icon: HelpCircleIcon,
                    onClick: () => {
                      navigateToDocumentLink();
                      sendAnalyticsEvent(
                        accessToken,
                        ANALYTICS_EVENTS.VISIT_SOLUTIONS_CORNER,
                        { [ANALYTICS_PROPERTIES.ENTRY_POINT]: "Top Nav" }
                      );
                    },
                  }}
                />
              ) : null}
            </>
          )}
          {hasNickname !== "-" && !isLoggedInAsUser && (
            <PopUpButton
              item={{
                label: "Logout",
                icon: LogOutCircleIcon,
                onClick: () => authStore.userLogout(),
              }}
            />
          )}
        </div>
      </div>
    );
  }
  const navigateToDocumentLink = () => {
    const documentationUrl = clientFeatures.documentationUrl;
    if (documentationUrl == "") {
      console.log("entered else block");
      toast.custom(() => {
        return (
          <EverHotToastMessage
            description="Documentation url is not present."
            type="error"
          />
        );
      });
    } else {
      window.open(documentationUrl, "_blank");
    }
  };

  const triggerFreshdesk = () => {
    freshdeskTicketForm(firstName + " " + lastName, email);
    setIsOpen(false);
  };

  const redirectToHelpCenter = async () => {
    toast.custom(() => {
      <EverHotToastMessage title="Opening Help Center" type="loading" />;
    });

    const freshdeskBaseUrl = `https://everstagesupport.myfreshworks.com/sp/OAUTH/${process.env.REACT_APP_FRESHDESK_OAUTH_ID}/login`;
    const loginParams = new URLSearchParams({
      redirect_uri:
        "https://everstage-support.freshdesk.com/freshid/customer_authorize_callback?hd=support.everstage.com",
      client_id: process.env.REACT_APP_FRESHDESK_CLIENT_ID,
      slug: process.env.REACT_APP_FRESHDESK_SLUG,
    }).toString();

    const freshdeskUrl = `${freshdeskBaseUrl}?${loginParams}`;

    console.log("Freshdesk URL", freshdeskUrl);

    window.open(freshdeskUrl);

    setIsOpen(false);
  };

  function PopUpButton({ item }) {
    const [buttonHover, setButtonHover] = useState(false);
    return (
      <div
        onMouseEnter={() => setButtonHover(true)}
        onMouseLeave={() => setButtonHover(false)}
        key={item.label}
        className={twMerge(
          commonCss,
          `cursor-pointer gap-3 py-2.5`,
          item.label == "Logout" ? "text-ever-error" : "",
          buttonHover
            ? item.label == "Logout"
              ? "bg-ever-error-ring"
              : "bg-ever-base-200"
            : ""
        )}
        onClick={() => {
          item.onClick();
          setIsOpen(false);
        }}
      >
        <item.icon
          className={twMerge(
            "w-5 h-5",
            buttonHover && item.label != "Logout"
              ? "text-ever-base-content"
              : "",
            !buttonHover && item.label != "Logout"
              ? "text-ever-base-content-mid"
              : ""
          )}
        />
        <EverTg.Text>{item.label}</EverTg.Text>
      </div>
    );
  }

  useOnClickOutside(popupRef, () => setIsOpen(false));
  const roles = clientFeatures.helpDocUserRole;
  useEffect(() => {
    if (userRoleName == "Power Admin") {
      setSolutionCornerDisplayed(true);
    } else if (roles && roles.includes(userRoleId[0])) {
      setSolutionCornerDisplayed(true);
    } else {
      setSolutionCornerDisplayed(false);
    }
  }, [roles, userRoleName, userRoleId]);
  useEffect(() => {
    if (email && payeeName) {
      setShowHelpBubble(true);
    }
  }, [payeeName, email]);

  useEffect(() => {
    if (data) {
      const profileData = JSON.parse(data.payeeProfileDetails);
      setUserName(`${profileData.first_name} ${profileData.last_name}`);
    }
  }, [data]);

  const { hasPermissions } = useUserPermissionStore();
  const hasHelpcentrepermissions = hasPermissions(
    RBAC_ROLES.ACCESS_HELP_CENTRE
  );

  return (
    <>
      {showHelpIcon &&
        showHelpBubble &&
        !isCPQ &&
        (hasHelpcentrepermissions ? (
          <HelpCenter
            handleClick={() => {
              redirectToHelpCenter();
              sendAnalyticsEvent(
                accessToken,
                ANALYTICS_EVENTS.VISIT_HELP_CENTER,
                {
                  [ANALYTICS_PROPERTIES.ENTRY_POINT]: "Top Nav",
                }
              );
            }}
          />
        ) : (
          <EverTooltip
            placement="right"
            title={
              "Please reach out to your admin or submit a query if you need help."
            }
          >
            <div className="flex items-center justify-center h-12 w-12 ml-[-4px]">
              <HelpCircleIcon className="w-6 h-6 text-ever-sidebar-icon hover:text-ever-sidebar-base-content" />
            </div>
          </EverTooltip>
        ))}

      <div className="flex items-center">
        <EverModal.Confirm
          title="Under Construction"
          subtitle="Reskinning under progress!"
          confirmationButtons={[
            <EverButton key="okay" onClick={() => setConsModalVisible(false)}>
              Okay
            </EverButton>,
          ]}
          type="error"
          visible={consModalVisible}
        />
        <Popover.Root open={isOpen}>
          <Popover.Trigger asChild>
            <div
              // added id="user-avatar-element" for playwright test to verify login
              id="user-avatar-element"
              className={twMerge(
                "flex shadow items-center justify-center rounded-lg border border-solid border-transparent bg-ever-base-25 cursor-pointer hover:border-ever-sidebar-icon-selected-border hover:ring-ever-sidebar-profile-hover-ring ring ring-transparent",
                isOpen
                  ? "ring-ever-sidebar-profile-hover-ring hover:ring-ever-sidebar-profile-hover-ring border border-solid border-ever-sidebar-icon-selected-border"
                  : ""
              )}
              onClick={() => setIsOpen(true)}
            >
              <EverGroupAvatar
                size="lg"
                avatars={[
                  {
                    image: profilePicture,
                    firstName,
                    lastName,
                    className: "w-8 h-8",
                    shape: "square",
                  },
                ]}
              />
            </div>
          </Popover.Trigger>
          <Popover.Portal>
            <Popover.Content
              className="z-[1070] mb-2.5 rounded-lg border border-solid border-ever-base-400 p-2 m-auto w-[212px] bg-ever-base shadow-lg will-change-[transform,opacity] data-[state=open]:data-[side=top]:animate-slideDownAndFade data-[state=open]:data-[side=right]:animate-slideLeftAndFade data-[state=open]:data-[side=bottom]:animate-slideUpAndFade data-[state=open]:data-[side=left]:animate-slideRightAndFade"
              sideOffset={20}
              ref={popupRef}
              side="right"
            >
              <div className="flex flex-col gap-2.5">
                <Content />
              </div>
            </Popover.Content>
          </Popover.Portal>
        </Popover.Root>
        <DotsVerticalIcon className="h-[13px] w-[13px] text-ever-sidebar-icon" />
      </div>
    </>
  );
});
