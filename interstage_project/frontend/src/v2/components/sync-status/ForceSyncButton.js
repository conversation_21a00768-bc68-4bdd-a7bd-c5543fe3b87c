/**
 * ForceSyncButton Component
 *
 * A button component that triggers data synchronization and displays sync status.
 * It shows a notification badge when there are pending updates for report sync
 * and opens a modal with detailed sync status information.
 *
 * Features:
 * - Real-time sync status updates using Supabase Postgres changes
 * - Notification badge for pending updates for each report object
 * - Modal view for detailed sync status
 * - Feature flag controlled visibility
 *
 * @component
 * @returns {JSX.Element|null} A button with sync status indicator and modal, or null if feature is disabled
 */
import {
  RefreshCwNeutralIcon,
  RefreshSuccessCwIcon,
  XCloseIcon,
} from "@everstage/evericons/outlined";
import { useState, useEffect, useCallback } from "react";
import { useRecoilValue } from "recoil";

import { getObservableReportObjectsData } from "~/Api/ObservableReportObjects";
import { useSupabasePostgresChanges } from "~/everstage-supabase/custom-hook";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverModal } from "~/v2/components/EverModal";
import { SyncStatusView } from "~/v2/components/sync-status/SyncStatusView";

export const ForceSyncButton = () => {
  // State management for modal and sync updates
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [updates, setUpdates] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);

  // Authentication and client context
  const { accessToken } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  // Supabase realtime configuration for monitoring sync status
  const realtimePostgresChangesFilter = [
    { event: "INSERT", filter: "task_name=eq.stale_report" },
  ];
  const channelPrefix = "channel-1";

  // Subscribe to realtime updates for sync status
  const data = useSupabasePostgresChanges(realtimePostgresChangesFilter, {
    channelPrefix,
  });

  /**
   * Fetches stale records that need synchronization
   * @returns {Promise<Array>} Array of stale records
   */
  const fetchStaleRecords = useCallback(async () => {
    const response = await getObservableReportObjectsData(accessToken);
    const data = await response.json();
    return data;
  }, [accessToken]);

  // Initial fetch of stale records
  useEffect(() => {
    async function fetchCount() {
      const fetchedUpdates = await fetchStaleRecords();
      setUpdates(fetchedUpdates);
      setNotificationCount(fetchedUpdates.length);
    }

    fetchCount();
  }, [fetchStaleRecords]);

  // Update stale records when realtime data changes or modal state changes
  useEffect(() => {
    if (data) {
      fetchStaleRecords().then((fetchedUpdates) => {
        setUpdates(fetchedUpdates);
        setNotificationCount(fetchedUpdates.length);
      });
    }
  }, [data, isModalOpen, fetchStaleRecords]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // Hide component if feature is disabled
  if (!clientFeatures?.showObservableNotification) {
    return null;
  }

  return (
    <>
      {/* Sync button with notification badge */}
      <button
        onClick={handleOpenModal}
        className="group flex items-center justify-center w-10 h-10 rounded-lg hover:bg-ever-sidebar-hover relative"
        title="Force data sync"
      >
        {notificationCount > 0 ? (
          <>
            <RefreshCwNeutralIcon className="w-5 h-5 text-ever-sidebar-icon group-hover:text-ever-sidebar-icon-hover text-emerald-500" />
            <span className="absolute top-0 right-0 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
              {notificationCount}
            </span>
          </>
        ) : (
          <RefreshSuccessCwIcon className="w-5 h-5 text-ever-sidebar-icon group-hover:text-ever-sidebar-icon-hover text-emerald-500" />
        )}
      </button>

      {/* Sync status modal */}
      <EverModal
        visible={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onCancel={() => setIsModalOpen(false)}
        width={720}
        centered={true}
        closable={true}
        destroyOnClose={true}
        footer={null}
        closeIcon={
          <XCloseIcon className="h-5 w-5 text-ever-base-content-mid" />
        }
      >
        <SyncStatusView
          onClose={() => setIsModalOpen(false)}
          updatesFromButton={updates}
        />
      </EverModal>
    </>
  );
};
