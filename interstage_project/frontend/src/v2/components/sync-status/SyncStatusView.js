/**
 * SyncStatusView Component
 *
 * A view component that displays sync status information and provides controls for force syncing.
 * It shows a list of updates with their sync status and timestamps, along with options to
 * force sync or close the view.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.onClose - Callback function when the view is closed
 * @param {Array} props.updatesFromButton - Array of update objects containing sync information
 * @returns {JSX.Element} A view with sync status information and controls
 */

import { LinkExternalIcon } from "@everstage/evericons/outlined";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { ForceSyncCard } from "./ForceSyncCard";
import { SyncUpdatesList } from "./SyncUpdatesList";

/**
 * Formats an ISO date string into a localized date-time string
 * @param {string} isoString - ISO format date string
 * @returns {string} Formatted date-time string (e.g., "Jan 1, 2024, 2:30 PM")
 */
const formatDateTime = (isoString) => {
  return new Date(isoString).toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });
};

export const SyncStatusView = ({ onClose, updatesFromButton }) => {
  // State to track which sections are expanded
  const [expandedSections, setExpandedSections] = useState({});
  // State to store and manage updates
  const [updates, setUpdates] = useState(updatesFromButton || []);
  const navigate = useNavigate();

  /**
   * Toggles the expanded state of a section
   * @param {string} section - The section identifier to toggle
   */
  const handleToggle = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Update local state when prop changes
  useEffect(() => {
    setUpdates(updatesFromButton);
  }, [updatesFromButton]);

  /**
   * Handles navigation to the sync settings page
   */
  const handleForceSyncClick = () => {
    navigate("/settings/commissions-and-data-sync");
    onClose();
  };

  return updates ? (
    <div className="max-w-3xl mx-auto p-6 space-y-6">
      {/* Information card about force sync */}
      <ForceSyncCard />

      {/* List of sync updates */}
      <div className="space-y-4">
        {updates.map((update) => (
          <SyncUpdatesList
            key={update.type}
            title={update.type}
            updates={update.updates || []}
            lastSync={formatDateTime(update.lastSync?.timestamp)}
            nextSync={formatDateTime(update.nextSync?.timestamp)}
            isExpanded={expandedSections[update.type]}
            onToggle={() => handleToggle(update.type)}
          />
        ))}
      </div>

      {/* Action buttons */}
      <div className="flex justify-between pt-4">
        <button
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          onClick={onClose}
        >
          Close
        </button>

        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
          onClick={handleForceSyncClick}
        >
          <span onClick={handleForceSyncClick}>Report Sync</span>
          <LinkExternalIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  ) : (
    // Empty state when there are no updates
    <div className="flex flex-col items-center justify-center h-full">
      <div>Nothing to update</div>
      <div className="flex justify-between pt-4">
        <button
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};
