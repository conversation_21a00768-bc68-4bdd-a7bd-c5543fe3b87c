import {
  DataflowAltIcon,
  DatabaseIcon,
  DatabaseAltIcon,
  ClockCheckIcon,
} from "@everstage/evericons/duotone";
import {
  ConnectorsLottie,
  ManageDataLottie,
  CommissionsAndDataSyncLottie,
  ActivityLogsLottie,
} from "@everstage/evericons/lotties";
import {
  CalculatorIcon,
  Certificate02Icon,
} from "@everstage/evericons/outlined";
import {
  CalculatorIcon as CalculatorSolidIcon,
  Certificate02Icon as Certificate02SolidIcon,
} from "@everstage/evericons/solid";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { RBAC_ROLES } from "~/Enums";
import { EverCard, EverTg } from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";

const SettingsPageCard = ({ path, title, icon, subTitle }) => {
  const iconMap = {
    connectors: <DataflowAltIcon className="w-6 h-6" />,
    upload: <DatabaseIcon className="w-6 h-6" />,
    activityLogs: <ClockCheckIcon className="w-6 h-6" />,
    commissionSettings: <DatabaseAltIcon className="w-6 h-6" />,
    quoteRules: <CalculatorIcon className="w-6 h-6" />,
    quoteForms: <Certificate02Icon className="w-6 h-6" />,
  };
  const activeIconMap = {
    connectors: <ConnectorsLottie autoplay className="w-12 h-12" />,
    upload: <ManageDataLottie autoplay className="w-12 h-12" />,
    activityLogs: <ActivityLogsLottie autoplay className="w-12 h-12" />,
    commissionSettings: (
      <CommissionsAndDataSyncLottie autoplay className="w-12 h-12" />
    ),
    quoteRules: <CalculatorSolidIcon className="w-6 h-6" />,
    quoteForms: <Certificate02SolidIcon className="w-6 h-6" />,
  };

  const SettingsIconActive = activeIconMap[icon];
  const SettingsIcon = iconMap[icon];

  const [isActive, setActive] = useState("");

  return (
    <Link to={path}>
      <div
        onMouseEnter={() => setActive(title)}
        onMouseLeave={() => setActive("")}
      >
        <EverCard className="w-96 h-44" interactive={true}>
          <div className="h-12 w-12 flex items-center justify-center bg-ever-primary-lite text-ever-primary rounded-lg rounded-tl-none">
            {isActive == title ? SettingsIconActive : SettingsIcon}
          </div>
          <div className="mt-4 flex flex-col gap-2">
            <EverTg.Heading3 className="text-ever-base-content">
              {title}
            </EverTg.Heading3>
            <EverTg.Description>{subTitle}</EverTg.Description>
          </div>
        </EverCard>
      </div>
    </Link>
  );
};

const CpqSettings = () => {
  const { t } = useTranslation();

  return (
    <div className="mb-10">
      <div className="flex flex-col flex-wrap gap-3">
        <div className="flex flex-initial flex-wrap gap-4">
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/activity-logs"
              title="Activity Logs"
              icon="activityLogs"
              subTitle="Stay up-to-date with detailed insights into the import within the app."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/manage-data"
              title="Manage Data"
              icon="upload"
              subTitle="Manage your data by manually creating, updating, and deleting it without hassle."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/connectors"
              title="Connectors"
              icon="connectors"
              subTitle="Manage app integrations and object configurations effortlessly."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/commissions-and-data-sync"
              title={t("COMMISSIONS_SETTINGS_CARD")}
              icon="commissionSettings"
              subTitle={t("DATA_SYNC_SUBTITLE")}
            />
          </RBACProtectedComponent>
          {/* <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/quote-rules"
              title="Quote Rules"
              icon="quoteRules"
              subTitle="Create and manage quote rules to customize your quote process."
            />
          </RBACProtectedComponent> */}
          {/* <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/cpq/settings/quote-forms"
              title="Quote Forms"
              icon="quoteForms"
              subTitle="Create and manage quote forms to customize your quote process."
            />
          </RBACProtectedComponent> */}
        </div>
      </div>
    </div>
  );
};

export default CpqSettings;
