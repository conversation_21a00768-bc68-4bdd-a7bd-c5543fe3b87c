// import { SettingOutlined, ApiOutlined, MoneyCollectOutlined, FastForwardOutlined } from "@ant-design/icons";
import {
  ToolIcon,
  DataflowIcon,
  DataflowAltIcon,
  FilePlusIcon,
  UsersEditIcon,
  DatabaseIcon,
  DatabaseAltIcon,
  BellRingingIcon,
  BezierCurveIcon,
  EditIcon,
  MessageChatSquareIcon,
  FileCheckIcon,
  SettingsAltIcon,
  ClockCheckIcon,
  HrisIntegrationIcon,
  TypeIcon,
  FileGraphIcon,
  CalendarCheckIcon,
  Dataflow02Icon,
} from "@everstage/evericons/duotone";
import {
  ActivityLogsLottie,
  AdjustmentsLottie,
  ApprovalWorkflowsLottie,
  AuditLogsLottie,
  BasicSettingsLottie,
  CommissionsAndDataSyncLottie,
  ConnectorsLottie,
  ContractsSettingsLottie,
  CustomFieldsLottie,
  CustomTerminologyLottie,
  HrisIntegrationLottie,
  ManageDataLottie,
  NotificationsLottie,
  RolesLottie,
  StatementsSettingsLottie,
  ReportEnrichmentLottie,
  CustomCalendarLottie,
} from "@everstage/evericons/lotties";
import { PaletteIcon } from "@everstage/evericons/outlined";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverCard, EverTg } from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";

const SettingsPageCard = ({ path, title, icon, subTitle }) => {
  const iconMap = {
    settings: <ToolIcon className="w-6 h-6" />,
    statementsettings: <FilePlusIcon className="w-6 h-6" />,
    notifications: <BellRingingIcon className="w-6 h-6" />,
    contracts: <EditIcon className="w-6 h-6" />,
    userRoles: <UsersEditIcon className="w-6 h-6" />,
    customfield: <BezierCurveIcon className="w-6 h-6" />,
    reportEnrichment: <FileGraphIcon className="w-6 h-6" />,
    approvals: <DataflowIcon className="w-6 h-6" />,
    queries: <MessageChatSquareIcon className="w-6 h-6" />,
    connectors: <DataflowAltIcon className="w-6 h-6" />,
    upload: <DatabaseIcon className="w-6 h-6" />,
    hrisIntegration: <HrisIntegrationIcon className="w-6 h-6" />,
    auditlogs: <FileCheckIcon className="w-6 h-6" />,
    adjustments: <SettingsAltIcon className="w-6 h-6" />,
    commissionSettings: <DatabaseAltIcon className="w-6 h-6" />,
    customCalendar: <CalendarCheckIcon className="w-6 h-6" />,
    activityLogs: <ClockCheckIcon className="w-6 h-6" />,
    customTerminology: <TypeIcon className="w-6 h-6" />,
    customTheme: <PaletteIcon className="w-6 h-6" />,
    workflowBuilder: <Dataflow02Icon className="w-6 h-6" />,
  };
  const activeIconMap = {
    settings: <BasicSettingsLottie autoplay className="w-12 h-12" />,
    statementsettings: (
      <StatementsSettingsLottie autoplay className="w-12 h-12" />
    ),
    notifications: <NotificationsLottie autoplay className="w-12 h-12" />,
    contracts: <ContractsSettingsLottie autoplay className="w-12 h-12" />,
    userRoles: <RolesLottie autoplay className="h-12 w-12" />,
    customfield: <CustomFieldsLottie autoplay className="w-12 h-12" />,
    reportEnrichment: <ReportEnrichmentLottie autoplay className="w-12 h-12" />,
    approvals: <ApprovalWorkflowsLottie autoplay className="w-12 h-12" />,
    queries: <MessageChatSquareIcon className="h-6 w-6" />, // 2
    connectors: <ConnectorsLottie autoplay className="w-12 h-12" />,
    upload: <ManageDataLottie autoplay className="w-12 h-12" />,
    hrisIntegration: <HrisIntegrationLottie autoplay className="w-12 h-12" />,
    auditlogs: <AuditLogsLottie autoplay className="w-12 h-12" />,
    adjustments: <AdjustmentsLottie autoplay className="w-12 h-12" />,
    commissionSettings: (
      <CommissionsAndDataSyncLottie autoplay className="w-12 h-12" />
    ),
    customCalendar: <CustomCalendarLottie autoplay className="w-12 h-12" />, // 3
    activityLogs: <ActivityLogsLottie autoplay className="w-12 h-12" />,
    customTerminology: (
      <CustomTerminologyLottie autoplay className="h-12 w-12" />
    ),
    customTheme: <PaletteIcon className="w-6 h-6" />,
    workflowBuilder: <Dataflow02Icon className="w-6 h-6" />,
  };

  const SettingsIconActive = activeIconMap[icon];
  const SettingsIcon = iconMap[icon];

  const [isActive, setActive] = useState("");

  return (
    <Link to={path}>
      <div
        onMouseEnter={() => setActive(title)}
        onMouseLeave={() => setActive("")}
      >
        <EverCard className="w-96 h-44" interactive={true}>
          <div className="h-12 w-12 flex items-center justify-center bg-ever-primary-lite text-ever-primary rounded-lg rounded-tl-none">
            {isActive == title ? SettingsIconActive : SettingsIcon}
          </div>
          <div className="mt-4 flex flex-col gap-2">
            <EverTg.Heading3 className="text-ever-base-content">
              {title}
            </EverTg.Heading3>
            <EverTg.Description>{subTitle}</EverTg.Description>
          </div>
        </EverCard>
      </div>
    </Link>
  );
};

const Render = () => {
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const { t } = useTranslation();

  return (
    <div className="mb-10">
      <div className="flex flex-col flex-wrap gap-3">
        <div className="flex flex-initial flex-wrap gap-4">
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <SettingsPageCard
              path="/settings/basic"
              title="Basic Settings"
              icon="settings"
              subTitle="Configure fiscal start month, currency, and exchange rates for your organization."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_ALL_ADMINS}>
            {clientFeatures?.customCalendar ? (
              <SettingsPageCard
                path="/settings/custom-calendar"
                title="Custom Calendar"
                icon="customCalendar"
                subTitle={t("CUSTOM_CALENDAR_SUBTITLE")}
              />
            ) : null}
          </RBACProtectedComponent>
          <RBACProtectedComponent
            permissionId={RBAC_ROLES.MANAGE_ACCOUNTNOTIFICATIONS}
          >
            <SettingsPageCard
              path="/settings/notifications"
              title="Notifications"
              icon="notifications"
              subTitle={t("NOTIFICATION_SUBTITLE")}
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONTRACTS}>
            <SettingsPageCard
              path="/settings/contracts"
              title="Contracts"
              icon="contracts"
              subTitle={t("CONTRACTS_SUBTITLE")}
            />
          </RBACProtectedComponent>
          {/* <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_REPORTENRICH}>
            <SettingsPageCard path="/settings/tags" title="Tags" icon="tags" />
          </RBACProtectedComponent> */}
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <SettingsPageCard
              path="/settings/statement-settings"
              title="Statement Settings"
              icon="statementsettings"
              subTitle={t("STATEMENTS_SUBTITLE")}
            />
          </RBACProtectedComponent>

          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_ROLES}>
            {clientFeatures?.showRoles ? (
              <SettingsPageCard
                path="/settings/user-roles"
                title="Roles"
                icon="userRoles"
                subTitle="Tailor roles and permissions to suit your organization's needs with ease."
              />
            ) : null}
          </RBACProtectedComponent>
          <RBACProtectedComponent
            permissionId={RBAC_ROLES.MANAGE_USERCUSTOMFIELD}
          >
            <SettingsPageCard
              path="/settings/custom-fields"
              title="Custom Fields"
              icon="customfield"
              subTitle="Optimize user data management with the power of custom user fields."
            />
          </RBACProtectedComponent>

          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_REPORTENRICH}>
            {clientFeatures?.isAutoEnrichReport ? null : (
              <SettingsPageCard
                path="/settings/report-enrichment"
                title="Report Enrichment"
                icon="reportEnrichment"
                subTitle="Enrich the Report data with the power of Report Enrichment"
              />
            )}
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            {clientFeatures?.showApprovalFeature ? (
              <SettingsPageCard
                path="/settings/workflows"
                title="Approval Workflows"
                icon="approvals"
                subTitle="Build customized workflows to streamline your organization's approval process."
              />
            ) : null}
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <SettingsPageCard
              path="/settings/queries"
              title="Queries"
              icon="queries"
              subTitle="Tailor your query management process to suit your organization's needs."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/settings/audit-logs"
              title="Audit Logs"
              icon="auditlogs"
              subTitle="Track, review, and analyze system events to ensure data integrity."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent
            permissionId={[
              RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
              RBAC_ROLES.MANAGE_DRAWS,
            ]}
          >
            <SettingsPageCard
              path="/settings/adjustments"
              title={t("ADJUSTMENTS")}
              icon="adjustments"
              subTitle={t("ADJUSTMENTS_SUBTITLE")}
            />
          </RBACProtectedComponent>

          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/settings/connectors"
              title="Connectors"
              icon="connectors"
              subTitle="Manage app integrations and object configurations effortlessly."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            {clientFeatures?.enableHrisIntegration ? (
              <SettingsPageCard
                path="/settings/hris-integration"
                title="HRIS Integration"
                icon="hrisIntegration"
                subTitle="Automate user management by syncing users from your HR system."
              />
            ) : null}
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/settings/manage-data"
              title="Manage Data"
              icon="upload"
              subTitle="Manage your data by manually creating, updating, and deleting it without hassle."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/settings/activity-logs"
              title="Activity Logs"
              icon="activityLogs"
              subTitle="Stay up-to-date with detailed insights into the import within the app."
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DATASETTINGS}>
            <SettingsPageCard
              path="/settings/commissions-and-data-sync"
              title={t("COMMISSIONS_SETTINGS_CARD")}
              icon="commissionSettings"
              subTitle={t("DATA_SYNC_SUBTITLE")}
            />
          </RBACProtectedComponent>
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
            <SettingsPageCard
              path="/settings/custom-terminology"
              title="Custom Terminology"
              icon="customTerminology"
              subTitle="Personalize Everstage's standard terminologies to match your unique business needs."
            />
          </RBACProtectedComponent>

          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_ROLES}>
            {clientFeatures?.enableCustomWorkflows && (
              <SettingsPageCard
                path="/settings/workflow-builders"
                title="Custom Workflows"
                icon="workflowBuilder"
                subTitle="Automate processes based on triggers, conditions, and actions"
              />
            )}
          </RBACProtectedComponent>
        </div>
      </div>

      {/* <div className="flex flex-col flex-wrap gap-3 mt-8">
        <div>
          <RBACProtectedComponent permissionId={[RBAC_ROLES.MANAGE_ROLES]}>
            <EverTg.SubHeading3>Users</EverTg.SubHeading3>
          </RBACProtectedComponent>
        </div>
        <div className="flex flex-initial flex-wrap gap-4">
        </div>
      </div> */}
    </div>
  );
};

export default Render;
