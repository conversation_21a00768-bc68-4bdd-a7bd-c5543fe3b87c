import { AgGridReact } from "ag-grid-react";
import { Row, Col } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useState, Fragment, useMemo, useRef } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { navPortalAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { sortCallbackUtil } from "~/Utils/sortColumnsUtils";
import {
  EverButton,
  EverNewDatePicker,
  EverSelect,
  EverNavPortal,
  EverTg,
  EverModal,
  EverLabel,
  message,
  EverFormatter,
} from "~/v2/components";
import {
  everAgGridOptions,
  DynamicPagination,
  CustomHeader,
} from "~/v2/components/ag-grid";

import {
  exportAuditLogs,
  getAuditFilterOptions,
  getAuditLogs,
} from "./service";
import ViewAuditLogModal from "./ViewAuditLogModal";

const { RangePicker } = EverNewDatePicker.Legacy;

/**
 * @typedef {import("~/Utils/sortColumnsUtils").SortInfoType} SortInfoType
 */

const DateAndRangePicker = ({ dateFilter, setDateFilter }) => {
  const [picker, setPicker] = useState("between");
  return (
    <div className={"flex gap-4"}>
      <EverSelect
        showArrow={true}
        defaultValue={picker}
        value={picker}
        onChange={(value) => {
          setPicker(value);
          setDateFilter([null, null]);
        }}
        className={"w-40"}
        size="small"
      >
        <EverSelect.Option value={"between"}>Between</EverSelect.Option>
        <EverSelect.Option value={"equals"}>Equals</EverSelect.Option>
      </EverSelect>

      {picker === "between" && (
        <RangePicker
          format="MMM DD, YYYY"
          placeholder={["MMM DD,YYYY", "MMM DD,YYYY"]}
          value={dateFilter}
          onChange={(momentArr) => {
            setDateFilter(momentArr);
          }}
          size="small"
        />
      )}
      {picker === "equals" && (
        <EverNewDatePicker.Legacy
          size="small"
          value={dateFilter && dateFilter[0]}
          onChange={(momentDate) => {
            if (momentDate) {
              momentDate = [momentDate, momentDate];
            }
            setDateFilter(momentDate);
          }}
          placeholder="MMM DD, YYYY"
        />
      )}
    </div>
  );
};

const FiltersModal = ({
  visible,
  handleCancel,
  handleApply,
  eventOptions,
  userOptions,
}) => {
  const [filters, setFilters] = useState({ actions: [], users: [] });

  return (
    <EverModal
      title="Audit Log Filters"
      visible={visible}
      onCancel={handleCancel}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <EverLabel className="text-ever-base-content-mid">
            Select Users
          </EverLabel>
          <EverSelect
            searchable
            mode="multiple"
            multiSelectOverflow="scroll"
            value={filters.users}
            options={userOptions}
            placeholder="Select users"
            onChange={(value) => setFilters({ ...filters, users: value })}
          />
        </div>
        <div className="flex flex-col gap-2">
          <EverLabel className="text-ever-base-content-mid">
            Select Actions
          </EverLabel>
          <EverSelect
            searchable
            mode="multiple"
            multiSelectOverflow="scroll"
            value={filters.actions}
            options={eventOptions}
            placeholder="Select actions"
            onChange={(value) => setFilters({ ...filters, actions: value })}
          />
        </div>
      </div>
      <EverModal.Footer>
        <div className="flex justify-end gap-2">
          <EverButton onClick={handleCancel} type="ghost">
            Cancel
          </EverButton>
          <EverButton onClick={() => handleApply(filters)}>Apply</EverButton>
        </div>
      </EverModal.Footer>
    </EverModal>
  );
};

const Render = observer(({ alluserRoles }) => {
  const gridRef = useRef();

  // Pagination props
  const [pageSize, setPageSize] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  // Sort columns
  const [orderbyFields, setOrderbyFields] = useState(
    /** @type {SortInfoType[]}*/ ([])
  );

  const [dateFilter, setDateFilter] = useState([
    moment().subtract(2, "days"),
    moment(),
  ]);
  const [filters, setFilters] = useState({ actions: [], users: [] });
  const [showFiltersModal, setShowFiltersModal] = useState(false);

  const navPortalLocation = useRecoilValue(navPortalAtom);
  const { accessToken } = useAuthStore();

  const { data: auditLogs } = useReactQuery(
    ["getAuditLogs", dateFilter, filters, currentPage, pageSize, orderbyFields],
    () => {
      if (isEmpty(dateFilter[0]) || isEmpty(dateFilter[1])) {
        return Promise.resolve({ data: [], count: 0 });
      }
      return getAuditLogs(accessToken, {
        periodStartDate: dateFilter[0].format("YYYY-MM-DD"),
        periodEndDate: dateFilter[1].format("YYYY-MM-DD"),
        filters,
        pageSize,
        currentPage: currentPage - 1,
        orderbyFields,
      });
    },
    {
      retry: false,
      cacheTime: 0,
    }
  );

  const { data: auditFilterOptions } = useReactQuery(
    ["getAuditFilterOptions", dateFilter],
    () => {
      if (isEmpty(dateFilter[0]) || isEmpty(dateFilter[1])) {
        return Promise.resolve({});
      }
      return getAuditFilterOptions(accessToken, {
        periodStartDate: dateFilter[0].format("YYYY-MM-DD"),
        periodEndDate: dateFilter[1].format("YYYY-MM-DD"),
      });
    },
    {
      retry: false,
      cacheTime: 0,
    }
  );

  const totalRows = auditLogs?.count ?? 0;

  const handleApplyFilters = (filters) => {
    setFilters(filters);
    setShowFiltersModal(false);
    setCurrentPage(1);
  };

  const handleCloseFilters = () => {
    setShowFiltersModal(false);
  };

  const handleOpenFilters = () => {
    setShowFiltersModal(true);
  };

  const sortCallback = (column) => {
    if (isEmpty(auditLogs?.data)) return;
    sortCallbackUtil(column, orderbyFields, setOrderbyFields);
  };

  const onGridReady = (params) => {
    params.api.sizeColumnsToFit();
  };

  const defaultColDef = useMemo(() => {
    return {
      sortable: false,
      flex: 1,
      minWidth: 100,
      wrapText: true,
      autoHeight: true,
      resizable: true,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
    };
  }, []);

  const onExport = () => {
    const payload = {
      periodStartDate: dateFilter[0].format("YYYY-MM-DD"),
      periodEndDate: dateFilter[1].format("YYYY-MM-DD"),
      filters,
    };
    return exportAuditLogs(accessToken, payload)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Error in export audit logs");
        }
        return response.blob();
      })
      .then((blobby) => {
        const objectUrl = window.URL.createObjectURL(blobby);
        const anchor = document.createElement("a");
        anchor.href = objectUrl;
        anchor.download = `Audit_${payload.periodStartDate}_${payload.periodEndDate}.csv`;
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
        message.success("Audit logs exported successfully");
      })
      .catch((error) => {
        message.error("Error in export audit logs");
        console.error(error.message);
      });
  };

  const columns = useMemo(() => {
    const cols = [
      {
        headerName: "Time",
        width: 300,
        minWidth: 300,
        field: "updatedAt",
        colId: "updatedAt",
        cellRenderer: (props, index) => {
          let record = props.data;

          return (
            <Fragment key={`tableDate_${index}`}>
              <EverFormatter.DateTime
                date={record?.updatedAt}
                type="shortWithZone"
                className="font-normal"
              />
            </Fragment>
          );
        },
      },
      {
        headerName: "User",
        field: "updatedByName",
        colId: "updatedByName",
        type: "string",
        minWidth: 300,
        wrapText: false,
        autoHeight: true,
        cellRenderer: (props) => {
          let record = props.data;
          return (
            <div className="flex flex-col justify-center h-full leading-normal">
              <EverTg.Heading4>{record.updatedByName}</EverTg.Heading4>
              <EverTg.Caption.Medium>{record.updatedBy}</EverTg.Caption.Medium>
            </div>
          );
        },
      },
      {
        headerName: "Action",
        type: "string",
        field: "eventTypeName",
        colId: "eventTypeName",
        minWidth: 200,
      },
      {
        headerName: "Description",
        field: "summary",
        colId: "summary",
        minWidth: 250,
      },
      {
        headerName: "View",
        field: "details",
        type: "rightAligned",
        minWidth: 300,
        cellRenderer: (props, index) => {
          let record = props.data;
          if (isEmpty(record.detailsDiff)) return "";
          else {
            const obj = record.detailsDiff;
            if (obj?.userRole) {
              const userRoleId = obj.userRole.after;
              const matchingRole = alluserRoles.find(
                (role) => role.rolePermissionId == userRoleId
              );
              if (matchingRole) {
                const displayName = matchingRole.displayName;
                obj.userRole = { ...obj.userRole, after: displayName };
                record.detailsDiff = obj;
              }
            }
          }
          return (
            <ViewAuditLogModal
              key={`tableModal_${index}`}
              content={record.detailsDiff}
            />
          );
        },
      },
    ];

    return cols.map((col) => {
      const sortInfo = orderbyFields.find((item) => item.column === col.field);
      const headerParams = col.headerComponentParams ?? {};
      return {
        ...col,
        headerComponentParams: {
          ...headerParams,
          serverSideSortable: col.field !== "details",
          sortOrder: sortInfo?.order ?? "",
          sortByField: col.field,
          sortCallback,
        },
      };
    });
  }, [alluserRoles, orderbyFields, auditLogs]);

  return (
    <Fragment>
      <EverNavPortal target={navPortalLocation}>
        <Row className="mb-1 justify-between">
          <Col>
            <DateAndRangePicker
              dateFilter={dateFilter}
              setDateFilter={setDateFilter}
            />
          </Col>
          <Col>
            <Row>
              <EverButton
                onClick={handleOpenFilters}
                size="small"
                type="dashed"
                className="mr-4"
              >
                Filters
              </EverButton>
              <EverButton onClick={onExport} size="small">
                Export as CSV
              </EverButton>
            </Row>
          </Col>
        </Row>
      </EverNavPortal>
      <div className="flex h-full">
        <div className="ag-theme-material zebra-grid w-full flex flex-col flex-auto">
          <AgGridReact
            {...everAgGridOptions.getDefaultOptions({ type: "md" })}
            className="audit-log-table"
            ref={gridRef}
            rowData={auditLogs?.data ?? []}
            columnDefs={columns}
            defaultColDef={defaultColDef}
            components={{ agColumnHeader: CustomHeader }}
            pagination={false}
            suppressCellFocus={true}
            suppressRowClickSelection={true}
            onGridReady={onGridReady}
          />
          {totalRows > 0 && (
            <DynamicPagination
              minHeight="50px"
              rowPerPageOption={[20, 50, 100]}
              pageCount={Math.ceil(totalRows / pageSize)}
              pageSize={pageSize}
              totalRows={totalRows}
              setPageSize={setPageSize}
              currentPage={currentPage - 1}
              setCurrentPage={setCurrentPage}
              gridRef={gridRef}
            />
          )}
        </div>
      </div>
      <FiltersModal
        visible={showFiltersModal}
        handleCancel={handleCloseFilters}
        handleApply={handleApplyFilters}
        eventOptions={auditFilterOptions?.eventCodeOptions ?? []}
        userOptions={auditFilterOptions?.userOptions ?? []}
      />
    </Fragment>
  );
});

export default Render;
