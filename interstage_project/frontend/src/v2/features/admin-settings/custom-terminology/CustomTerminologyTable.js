import { AgGridReact } from "ag-grid-react";
import React, { useState, useEffect } from "react";
import { parse } from "valibot";

import { EverTg } from "~/v2/components";
import {
  cellRenderers,
  everAgGridCallbacks,
  everAgGridOptions,
} from "~/v2/components/ag-grid";
import { CustomTerminologyTableDataSchema } from "~/v2/components/schemas";

/**
 * @typedef {Object} CustomTerminologyTable
 * @property {import("~/v2/components/types/custom-terminology-types").CustomTermsType} customTermData - Array of Row Data for AgGrid Table comprising
 * Cutomizable column key,value, label and placeholder
 */

/**
 * Renders an AGGrid tables with customizable column names and their customized values if any
 * @param {CustomTerminologyTable} customTermData
 * @param {function} setInitialDataMap A setter function to set isTableDirty whenever table value changes from its original value
 * @returns {React.ReactNode[]} An array of React components representing each avatar.
 */

function CustomTerminologyTable({ customTermData, setIsTableDirty }) {
  parse(CustomTerminologyTableDataSchema, customTermData);
  const [initialDataMap, setInitialDataMap] = useState(null);

  useEffect(() => {
    const initialValuesMap = {};
    customTermData.forEach((element) => {
      initialValuesMap[element["key"]] = element["value"];
    });
    setInitialDataMap(initialValuesMap);
  }, []);

  const columnDefs = [
    {
      headerName: "Everstage Terminology",
      field: "label",
      minWidth: 200,
      pinned: "left",
      cellRenderer: (params) => {
        const { value } = params;
        return (
          <EverTg.Text className="text-ever-base-content">{value}</EverTg.Text>
        );
      },
    },
    {
      headerName: "Custom Terminology",
      field: "value",
      editable: true,
      singleClickEdit: true,
      cellRenderer: cellRenderers.EditedCellRenderer,
    },
  ];

  return (
    <div className="ag-theme-material zebra-grid overflow-y-auto mt-4 w-[600px] mb-20">
      <AgGridReact
        {...everAgGridOptions.getDefaultOptions({ type: "md" })}
        columnDefs={columnDefs}
        rowData={customTermData}
        domLayout="autoHeight"
        onCellValueChanged={(props) => {
          everAgGridCallbacks.onCellValueChangedStyled(props);
          const latestDataMap = {};
          customTermData.forEach((element) => {
            latestDataMap[element["key"]] = element["value"];
          });
          if (
            JSON.stringify(initialDataMap) !== JSON.stringify(latestDataMap)
          ) {
            setIsTableDirty(true);
          } else {
            setIsTableDirty(false);
          }
        }}
      />
    </div>
  );
}

export default CustomTerminologyTable;
