import { CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS } from "~/Enums";

export async function getDomainSpecificAttributes(accessToken) {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  const response = await fetch("/spm/localization?lng=en", requestOptions);

  const responseData = await response.json();

  if (!response.ok) {
    console.error("Error while getting custom terminology");
    throw new Error(responseData?.reason || "");
  }

  const customTermTempData = [];
  Object.keys(responseData).map((key) => {
    customTermTempData.push({
      label: CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS[key],
      key: key,
      value:
        responseData[key] == CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS[key]
          ? "" //If default comes from backend it will be treated as empty string
          : responseData[key],
      placeholder: CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS[key],
    });
  });
  return customTermTempData;
}

export async function postDomainSpecificAttributes(accessToken, data) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  const response = await fetch("/spm/localization", requestOptions);

  if (!response.ok) {
    console.error("Error while updating custom Terminology.");
    throw data || {};
  }
  const responseData = await response.json();
  return responseData;
}
