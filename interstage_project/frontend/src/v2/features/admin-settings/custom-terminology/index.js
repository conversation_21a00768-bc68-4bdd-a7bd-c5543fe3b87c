import { isEmpty } from "lodash";
import React, { useState } from "react";
import { useQuery, useMutation } from "react-query";

import { CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverTg, EverButton, message, EverFooter } from "~/v2/components";

import {
  getDomainSpecificAttributes,
  postDomainSpecificAttributes,
} from "./api";
import CustomTerminologyTable from "./CustomTerminologyTable";

export default function CustomTerminology() {
  const { accessToken } = useAuthStore();
  const [isTableDirty, setIsTableDirty] = useState(false);
  const { data, isError, status } = useQuery(
    ["getDomainSpecificAttributes", accessToken],
    () => {
      return getDomainSpecificAttributes(accessToken);
    },
    {
      lazy: true,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  const { mutate: updateTerminology } = useMutation(
    (data) => postDomainSpecificAttributes(accessToken, data),
    {
      // onSuccess is called when the mutation is successfull
      onSuccess: () => {
        message.success("Terminologies updated successfully!");
        window.location.reload();
      },
    }
  );

  function onUpdate() {
    const domainData = {};

    if (status === "success") {
      for (const element of data) {
        // The value can be null when the cell is emptied while the cell already has a non-default value
        element["value"] = element["value"] ? element["value"] : ""; //If value is null then we send value as empty string
        domainData[element["key"]] =
          element["key"] in CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS &&
          element["value"] !=
            CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS[element["key"]]
            ? element["value"]
            : (domainData[element["key"]] = ""); //If value they update is equal to default then we send value as empty string
      }

      const enData = {
        en: domainData,
      };

      updateTerminology(enData);
    } else {
      message.error(
        "Terminologies have not been initialized yet - kindly try after a while"
      );
    }
  }

  if (isError) {
    message.error("Something went wrong please try later");
  }
  return (
    <>
      <div className="flex flex-row items-center justify-between w-2/5">
        <EverTg.Description>
          Personalize Everstage’s standard terminologies to match your unique
          business needs.
        </EverTg.Description>
      </div>
      {!isEmpty(data) && (
        <CustomTerminologyTable
          customTermData={data}
          setIsTableDirty={setIsTableDirty}
        />
      )}
      <EverFooter>
        <div className="flex justify-end">
          <EverButton size="medium" onClick={onUpdate} disabled={!isTableDirty}>
            Update
          </EverButton>
        </div>
      </EverFooter>
    </>
  );
}
