import {
  LinkExternalIcon,
  Trash03Icon,
  MagicWandIcon,
} from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import React, { useRef, useState } from "react";
import { Link } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { CONNECTION_STATUS, INTEGRATION } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { integrations } from "~/Utils/integrations";
import { EverButton, EverTg, EverTooltip } from "~/v2/components";
import {
  everAgGridCallbacks,
  everAgGridOptions,
  Pagination,
} from "~/v2/components/ag-grid";
import { formatDateTime } from "~/v2/components/ever-formatter/EverFormatter";
import { useModules } from "~/v2/hooks";
import { integrationImages } from "~/v2/images";

import { ConnectionStatus } from "../common";

const ConnectionTableView = (props) => {
  const {
    data,
    handleOpenConnection,
    setDeleteConfigId,
    handleOpenSheetBuilder,
  } = props;
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const { getDefaultOptions } = everAgGridOptions;
  const gridRef = useRef();
  const [displayPagination, setDisplayPagination] = useState(false);
  const [pageSize, setPageSize] = useState(everAgGridOptions.defaultPageSize);
  const [pageData, setPageData] = useState({});
  const modules = useModules();

  const setPaginationValues = (params) => {
    setPageData(everAgGridCallbacks.onPaginationChangedDefault(params));
  };

  const doOnFirstRender = (params) => {
    setDisplayPagination(true);
    everAgGridCallbacks.adjustAllColumnSizes(params);
  };

  const basePath = modules.isCPQ ? "/cpq" : "";

  const colDef = React.useMemo(
    () => [
      {
        headerName: "Connection name",
        field: "connectionName",
        cellRenderer: (params) => {
          return (
            <EverButton
              type="link"
              onClick={() => {
                handleOpenConnection(params.data);
              }}
              size={"small"}
              className="!pl-0"
            >
              {params.value}
            </EverButton>
          );
        },
      },
      {
        headerName: "Source",
        field: "serviceName",
        cellRenderer: (params) => {
          const connectionDetails = integrations?.[params.data.serviceName];
          const icon = integrationImages[connectionDetails?.image];
          return (
            <div className="flex items-center gap-2">
              {icon && <img src={icon} className="size-5" />}
              <EverTg.Text>{connectionDetails?.displayName || ""}</EverTg.Text>
            </div>
          );
        },
      },
      {
        headerName: "Connection status",
        field: "connectionStatus",
        cellRenderer: (params) => {
          return (
            <ConnectionStatus
              isConnected={params.value === CONNECTION_STATUS.CONNECTED}
              statusText={
                params.value?.charAt(0).toUpperCase() +
                params.value?.slice(1).toLowerCase()
              }
            />
          );
        },
      },
      {
        headerName: "No. of objects created",
        field: "objectsCreated",
        cellRenderer: (params) => {
          return (
            <div className="flex items-center justify-between w-full">
              <span>{params.value}</span>
              {params.value > 0 && (
                <Link
                  to={`${basePath}/objects?quickFilters=${params.data.serviceName}`}
                >
                  <EverButton type="link" size="small" className="!px-0">
                    <LinkExternalIcon className="size-4" />
                  </EverButton>
                </Link>
              )}
            </div>
          );
        },
      },
      {
        headerName: "Last updated at",
        field: "knowledgeBeginDate",
        valueFormatter: (params) => {
          return formatDateTime({
            date: params.value,
            type: "short",
            targetTimeZone: "local",
          });
        },
      },
      {
        headerName: "Actions",
        field: "actions",
        maxWidth: 100,
        cellRenderer: (params) => {
          const sourceName = params.data.serviceName;
          return (
            <div className="flex justify-center items-center w-full">
              {clientFeatures?.datasheetBuilder &&
                clientFeatures?.enableEverai &&
                sourceName === INTEGRATION.SALESFORCE && (
                  <EverTooltip title="Build Sheets with AI">
                    <EverButton
                      type="link"
                      onClick={() => {
                        handleOpenSheetBuilder(params.data.accessTokenConfigId);
                      }}
                    >
                      <MagicWandIcon className="text-ever-primary w-5 h-5" />
                    </EverButton>
                  </EverTooltip>
                )}
              <EverButton
                type="link"
                onClick={() =>
                  setDeleteConfigId(params.data.accessTokenConfigId)
                }
              >
                <Trash03Icon className="text-ever-error w-5 h-5" />
              </EverButton>
            </div>
          );
        },
      },
    ],
    [handleOpenConnection, integrations, setDeleteConfigId]
  );

  return (
    <div className="ag-theme-material objects-v2-table no-border zebra-grid w-full mb-4">
      <AgGridReact
        {...getDefaultOptions({ type: "sm" })}
        ref={gridRef}
        onFirstDataRendered={doOnFirstRender}
        onPaginationChanged={setPaginationValues}
        rowData={data}
        columnDefs={colDef}
        cacheBlockSize={null}
        domLayout="autoHeight"
        paginationPageSize={pageSize}
      ></AgGridReact>
      {displayPagination && (
        <Pagination
          pageData={pageData}
          control={gridRef}
          setPageSize={setPageSize}
        />
      )}
    </div>
  );
};

export default ConnectionTableView;
