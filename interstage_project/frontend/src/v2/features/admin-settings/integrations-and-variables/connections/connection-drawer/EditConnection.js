import React, { useState } from "react";

import { CONNECTION_STATUS, INTEGRATION } from "~/Enums";
import { integrations } from "~/Utils/integrations";
import { EverButton, EverDivider, EverForm } from "~/v2/components";
import { integrationImages } from "~/v2/images";

import { IntegrationDetails } from "./IntegrationDetails";
import {
  <PERSON><PERSON>,
  <PERSON>er,
  HeaderCard,
  ConnectionStatus,
  CreatedUpdatedInfo,
} from "../../common";

const EditConnection = ({
  loading,
  errorField,
  handleCloseDrawer,
  setShowConfirmPopup,
  setShowLoading,
  onSubmit,
  selectedAccessTokenConfigs,
}) => {
  const isConnected =
    selectedAccessTokenConfigs?.connectionStatus ===
    CONNECTION_STATUS.CONNECTED;
  const [connectionName, setConnectionName] = useState(
    selectedAccessTokenConfigs?.connectionName
  );
  const [accessRequestBody, setAccessRequestBody] = useState({});
  const [apiAccessKey, setApiAccessKey] = useState(
    selectedAccessTokenConfigs?.apiAccessKey || ""
  );
  const [isEditCredentials, setIsEditCredentials] = useState(!isConnected);
  const [showEditCredentials, setShowEditCredentials] = useState(true);
  const [authType, setAuthType] = useState(
    JSON.parse(selectedAccessTokenConfigs?.accessRequestBody)?.grant_type ===
      "password"
      ? "CREDENTIALS"
      : "REFRESH"
  );
  const [isFieldEdited, setIsFieldEdited] = useState(false);
  const [form] = EverForm.useForm();
  const showAbstractFooter = ![
    INTEGRATION.HUBSPOT,
    INTEGRATION.SALESFORCE,
  ].includes(selectedAccessTokenConfigs?.serviceName);

  const cardDetails = {
    key: "integration",
    title: "Integration",
    icon:
      integrationImages[
        integrations[selectedAccessTokenConfigs?.serviceName]?.image
      ] || null,
    name:
      integrations[selectedAccessTokenConfigs?.serviceName]?.displayName || "",
    editable: false,
  };

  const handleClose = () => {
    if (!loading) {
      if (isFieldEdited) {
        setShowConfirmPopup(true);
      } else {
        handleCloseDrawer();
      }
    }
  };

  const handleCancel = () => {
    if (selectedAccessTokenConfigs?.serviceName == INTEGRATION.SALESFORCE) {
      const tempAccessRequestBody = {};
      const parsedAccessRequestBody = JSON.parse(
        selectedAccessTokenConfigs?.accessRequestBody
      );
      Object.values(accessRequestBody).forEach((field) => {
        tempAccessRequestBody[field.key] = {
          key: field.key,
          value: parsedAccessRequestBody[field.key],
          error: false,
        };
      });
      setAccessRequestBody(tempAccessRequestBody);
    } else if (selectedAccessTokenConfigs?.serviceName == INTEGRATION.HUBSPOT) {
      setApiAccessKey(selectedAccessTokenConfigs?.apiAccessKey);
    }
    setIsEditCredentials(false);
  };

  const handleSave = () => {
    const tempServiceName = selectedAccessTokenConfigs?.serviceName;
    if (tempServiceName === INTEGRATION.HUBSPOT) {
      const data = {
        connectionName,
        serviceName: tempServiceName,
        apiAccessKey,
        accessTokenConfigId: selectedAccessTokenConfigs.accessTokenConfigId,
      };
      onSubmit(data, setApiAccessKey);
    } else if (tempServiceName === INTEGRATION.SALESFORCE) {
      const data = {
        connectionName,
        accessRequestBody,
        serviceName: tempServiceName,
        accessTokenConfigId: selectedAccessTokenConfigs.accessTokenConfigId,
      };
      onSubmit(data, setAccessRequestBody);
    }
  };

  const integrationRender = (serviceName) => {
    if (serviceName == INTEGRATION.SALESFORCE) {
      return (
        <IntegrationDetails
          isEdit={true}
          errorField={errorField}
          serviceName={selectedAccessTokenConfigs?.serviceName}
          selectedAccessRequestBody={JSON.parse(
            selectedAccessTokenConfigs?.accessRequestBody
          )}
          selectedAdditionalData={JSON.parse(
            JSON.parse(selectedAccessTokenConfigs?.additionalData)
          )}
          selectedAccessTokenConfigId={
            selectedAccessTokenConfigs?.accessTokenConfigId
          }
          accessRequestBody={accessRequestBody}
          setAccessRequestBody={setAccessRequestBody}
          disableFields={!isEditCredentials}
          setShowEditCredentials={setShowEditCredentials}
          onAuthTypeChange={(_authType) => {
            setAuthType(_authType);
          }}
          handleCloseDrawer={handleCloseDrawer}
          isFieldEdited={isFieldEdited}
          setIsFieldEdited={setIsFieldEdited}
          underlyingServiceName={serviceName}
        />
      );
    } else if (serviceName == INTEGRATION.HUBSPOT) {
      return (
        <IntegrationDetails
          isEdit={true}
          errorField={errorField}
          serviceName={selectedAccessTokenConfigs?.serviceName}
          apiAccessKey={apiAccessKey}
          setApiAccessKey={setApiAccessKey}
          disableFields={!isEditCredentials}
          isFieldEdited={isFieldEdited}
          setIsFieldEdited={setIsFieldEdited}
          underlyingServiceName={serviceName}
        />
      );
    } else if (selectedAccessTokenConfigs?.serviceName == INTEGRATION.ZOHO) {
      return (
        <IntegrationDetails
          form={form}
          errorField={errorField}
          serviceName={INTEGRATION.ZOHO}
          isFieldEdited={isFieldEdited}
          setIsFieldEdited={setIsFieldEdited}
          disableFields={!isEditCredentials}
          selectedAccessTokenConfigs={selectedAccessTokenConfigs}
          underlyingServiceName={serviceName}
        />
      );
    } else if (
      serviceName === INTEGRATION.MSSQL ||
      serviceName === INTEGRATION.POSTGRESQL ||
      serviceName === INTEGRATION.SNOWFLAKE ||
      serviceName === INTEGRATION.SUITEANALYTICS
    ) {
      return (
        <IntegrationDetails
          form={form}
          errorField={errorField}
          serviceName={INTEGRATION.SQL}
          isFieldEdited={isFieldEdited}
          setIsFieldEdited={setIsFieldEdited}
          disableFields={!isEditCredentials}
          selectedAccessTokenConfigs={selectedAccessTokenConfigs}
          underlyingServiceName={serviceName}
        />
      );
    }
    return null;
  };

  const serviceName = selectedAccessTokenConfigs?.serviceName;
  const showFooter =
    isEditCredentials &&
    (serviceName === INTEGRATION.HUBSPOT
      ? true
      : serviceName === INTEGRATION.SALESFORCE
      ? authType !== "REFRESH"
      : false);

  return (
    <div className="flex flex-col justify-between h-full">
      <Header
        label={"Edit Connection"}
        name={connectionName}
        displayName={
          <div className="flex gap-2 items-center">
            <span>{connectionName}</span>
            <span>
              <ConnectionStatus
                isConnected={isConnected}
                statusText={selectedAccessTokenConfigs?.connectionStatus}
              />
            </span>
          </div>
        }
        onChange={setConnectionName}
        handleCancel={handleClose}
        editable={isEditCredentials}
      />
      <div className="w-full flex flex-col overflow-auto my-0 mx-auto">
        <div
          className={`pt-10 px-6 pb-6 flex flex-col max-w-7xl w-full my-0 mx-auto gap-10 h-[73vh]`}
        >
          <HeaderCard {...cardDetails} />
          {integrationRender(selectedAccessTokenConfigs?.serviceName)}
          {!isEditCredentials && showEditCredentials && (
            <EverButton
              color="base"
              className="w-40"
              onClick={() => setIsEditCredentials(true)}
            >
              Edit Connection
            </EverButton>
          )}

          <div className="flex flex-col gap-5">
            <EverDivider />
            <CreatedUpdatedInfo
              createdBy={selectedAccessTokenConfigs?.createdBy}
              createdAt={selectedAccessTokenConfigs?.createdOn}
              updatedAt={selectedAccessTokenConfigs?.knowledgeBeginDate}
            />
          </div>
        </div>
      </div>
      <div className="h-20 flex flex-col justify-end">
        {showFooter && (
          <Footer
            isLoading={loading}
            handleCancel={() => (isConnected ? handleCancel() : handleClose())}
            handleSave={handleSave}
            okText="Validate & Connect"
          />
        )}
        {showAbstractFooter && isEditCredentials && (
          <IntegrationDetails.Footer
            form={form}
            isLoading={loading}
            handleCancel={() => (isConnected ? handleCancel() : handleClose())}
            okText="Validate & Connect"
            isEdit={true}
            connectionName={connectionName}
            setShowLoading={setShowLoading}
            handleCloseDrawer={handleCloseDrawer}
            serviceName={selectedAccessTokenConfigs?.serviceName}
            accessTokenConfigId={
              selectedAccessTokenConfigs?.accessTokenConfigId
            }
          />
        )}
      </div>
    </div>
  );
};

export default EditConnection;
