import { Row } from "antd";
import { camelCase, isEmpty } from "lodash";
import React, { useEffect, useState } from "react";

import { createEditConnection } from "~/Api/IntegrationSettingsService";
import { INTEGRATION } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverForm } from "~/v2/components";

import RenderSingleComponent from "./RenderSingleComponent";
import { Footer, showError } from "../../../common";

/**
 * Returns an abstract connection component.
 *
 * @param {Object} props - The props for the abstract connection component.
 * @param {Object} props.form - The form object.
 * @param {Array} props.connectionConfig - The configuration for the connection.
 * @param {Object} props.selectedAccessTokenConfigs - The selected access token configurations.
 * @param {Array} props.disableFields - The fields to disable.
 * @param {string} props.serviceName - The name of the service.
 * @returns {React.ReactNode} The rendered abstract connection component.
 */
const AbstractConnection = ({
  form,
  connectionConfig,
  selectedAccessTokenConfigs,
  disableFields,
  serviceName,
}) => {
  const [allfieldValues, setAllFieldValues] = useState({});
  const [disabledFields, setDisabledFields] = useState(new Set());

  useEffect(() => {
    if (
      serviceName === INTEGRATION.MSSQL ||
      serviceName === INTEGRATION.POSTGRESQL ||
      serviceName === INTEGRATION.SNOWFLAKE ||
      serviceName === INTEGRATION.SUITEANALYTICS
    ) {
      setDisabledFields(new Set([...disabledFields, "dbType"]));
    }
    if (serviceName === INTEGRATION.ZOHO) {
      setDisabledFields(new Set([...disabledFields, "connectionType"]));
    }
  }, [disableFields, serviceName]);

  let formData = null;
  if (selectedAccessTokenConfigs?.apiAccessKey) {
    formData = { apiAccessKey: selectedAccessTokenConfigs.apiAccessKey };
  } else if (selectedAccessTokenConfigs?.accessRequestBody) {
    formData = JSON.parse(selectedAccessTokenConfigs.accessRequestBody);
  }
  if (selectedAccessTokenConfigs?.additionalData) {
    formData = {
      ...formData,
      ...JSON.parse(selectedAccessTokenConfigs?.additionalData),
    };
  }
  const firstNonMandatoryConfigId = connectionConfig.find(
    (config) => config?.displayConditions === undefined
  )?.key;

  if (formData && !form.getFieldValue(firstNonMandatoryConfigId)) {
    const tempValues = {};
    for (const [key, value] of Object.entries(formData)) {
      tempValues[camelCase(String(key))] = value;
    }
    // Set default values of the form
    const defaultFormFields = connectionConfig.filter(
      (config) => !!config.defaultValue
    );
    for (const field of defaultFormFields) {
      const key = camelCase(String(field.key));
      if (!tempValues[key]) {
        // Handle boolean default values
        tempValues[key] = ["true", "false"].includes(field.defaultValue)
          ? field.defaultValue === "true"
          : field.defaultValue;
      }
    }
    // Assign values to the form
    form.setFieldsValue(tempValues);
  }

  const onChange = (_, allValues) => {
    setAllFieldValues(allValues);
  };

  return (
    <EverForm
      layout="vertical"
      form={form}
      name="abstract-connection-wrapper"
      onFieldsChange={onChange}
    >
      <Row gutter={16} className="mb-2">
        {connectionConfig.map((config) => {
          return (
            <RenderSingleComponent
              key={config.key}
              config={config}
              form={form}
              allfieldValues={allfieldValues}
              disableFields={disableFields}
              disabledFields={disabledFields}
            />
          );
        })}
      </Row>
    </EverForm>
  );
};

/**
 * renders the footer component for an abstract connection.
 *
 * @param {Object} props - The props for the abstract connection footer component.
 * @param {boolean} props.isLoading - Indicates whether the component is in a loading state.
 * @param {function} props.handleCancel - The function to handle the cancel action.
 * @param {string} props.okText - The text for the OK button.
 * @param {Object} props.form - The form object.
 * @param {boolean} props.isEdit - Indicates whether the component is in Edit mode.
 * @param {string} props.connectionName - The name of the connection.
 * @param {string} props.serviceName - The name of the service.
 * @param {function} props.setShowLoading - The function to set the loading state.
 * @param {number} props.accessTokenConfigId - The access token configuration ID.
 * @param {function} props.handleCloseDrawer - The function to close the drawer after save.
 * @returns {React.ReactNode} The rendered abstract connection footer component.
 */
const AbstractConnectionFooter = ({
  isLoading,
  handleCancel,
  okText,
  form,
  connectionName,
  accessTokenConfigId,
  serviceName,
  setShowLoading,
  isEdit,
  handleCloseDrawer = () => {},
}) => {
  /**
   * Handles form submission
   */

  const { accessToken } = useAuthStore();

  const onSubmit = async () => {
    let res = null;
    form
      .validateFields()
      .then(async (values) => {
        if (isEmpty(connectionName)) {
          showError("Missing connection name");
        }
        setShowLoading(true);
        if (isEdit) {
          const data = {
            connectionName,
            serviceName:
              serviceName === INTEGRATION.MSSQL ||
              serviceName === INTEGRATION.POSTGRESQL ||
              serviceName === INTEGRATION.SNOWFLAKE ||
              serviceName === INTEGRATION.SUITEANALYTICS
                ? INTEGRATION.SQL
                : serviceName,
            isEdit,
            connectionData: values,
            accessTokenConfigId,
          };
          res = await createEditConnection(data, accessToken);
        } else {
          const data = {
            connectionName,
            serviceName:
              serviceName === INTEGRATION.MSSQL ||
              serviceName === INTEGRATION.POSTGRESQL ||
              serviceName === INTEGRATION.SNOWFLAKE ||
              serviceName === INTEGRATION.SUITEANALYTICS
                ? INTEGRATION.SQL
                : serviceName,
            isEdit,
            connectionData: values,
          };
          res = await createEditConnection(data, accessToken);
        }
        if (res.ok) {
          setShowLoading(false);
          handleCloseDrawer(
            true,
            { connectionName, serviceName },
            isEdit ? undefined : "success"
          );
        } else {
          setShowLoading(false);
          const errMsg = await res.json();
          showError(errMsg.message);
        }
      })
      .catch((error) => console.log(error));
  };
  return (
    <Footer
      isLoading={isLoading}
      handleCancel={handleCancel}
      handleSave={onSubmit}
      okText={okText}
    />
  );
};

AbstractConnection.Footer = AbstractConnectionFooter;

export { AbstractConnection };
