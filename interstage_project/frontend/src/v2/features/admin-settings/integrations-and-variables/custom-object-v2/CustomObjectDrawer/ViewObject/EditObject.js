import { isEmpty, isEqual } from "lodash";
import React, { useMemo, useState } from "react";

import EditObjectHeader from "./EditObjectHeader";
import {
  Footer,
  SearchInput,
  CreatedUpdatedInfo,
  showError,
} from "../../../common";
import { OBJECT_TYPES } from "../../../constants";
import { isValidHyperlinkUrl } from "../../../utils";
import VariableList from "../../VariableList";

const EditObject = (props) => {
  const {
    objectType,
    objData,
    integrationId,
    handleCancel,
    addedVariables,
    setAddedVariables,
    addedPrimaryKey,
    addedSnapshotKey,
    tempObjectOrder,
    systemNameMapping,
    objectName,
    isObjectNameChanged,
    isVariableChanged,
    sourceDestMapping,
    isConnection,
    updateCO,
    store,
    hyperlinkedField,
    selectedConnection,
    setHyperlinkedField,
    newHyperlinkedField,
    setNewHyperlinkedField,
    hyperlinkedUrl,
    setHyperlinkedUrl,
    newHyperlinkedUrl,
    setNewHyperlinkedUrl,
    selectedSourceObj,
    mappedFields,
    isHyperlinksSelfServeIntegration,
    objectRefetch,
    resetTransformationConfig,
    syncFields,
    ...remainingProps
  } = props;
  const {
    errorField,
    connectionLoading,
    setLoading,
    setErrorField,
    isLoading,
    setConnectionLoading,
  } = store;
  const [searchValue, setSearchValue] = useState("");

  const variables = useMemo(() => {
    const value = searchValue.toLowerCase();
    const checkInSource = (key) =>
      isConnection
        ? sourceDestMapping[key]?.label?.toLowerCase().includes(value) ||
          sourceDestMapping[key]?.name?.toLowerCase().includes(value)
        : false;
    return searchValue
      ? addedVariables.filter(
          (data) =>
            data.variableName.toLowerCase().includes(value) ||
            checkInSource(data.variableKey)
        )
      : addedVariables;
  }, [searchValue, addedVariables]);

  const handleSave = () => {
    setLoading(true);
    let error = false;
    const data = {
      objectId: objData.customObjectId,
    };
    const newAddedVariables = [];
    const finalHyperlinkedField = newHyperlinkedField ?? hyperlinkedField;
    const finalHyperlinkedUrl = newHyperlinkedUrl ?? hyperlinkedUrl;
    const objectOrder = addedVariables.map((variable) => variable.variableKey);
    if (isObjectNameChanged) {
      data["name"] = objectName;
    }
    if (isVariableChanged) {
      let variableNames = new Set();
      let variableNameCount = 0;
      const variables = [];
      const totalFields = addedVariables.length;
      let validFields = 0;
      addedVariables.forEach((variable) => {
        const { variableName, variableType, variableKey } = variable;
        if (!isEmpty(variableName)) {
          variableNameCount++;
          variableNames.add(variableName.toLowerCase().trim());
          if (!isEmpty(variableType)) {
            validFields++;
          }
        }
        newAddedVariables.push({
          ...variable,
          error: isEmpty(variableName) || isEmpty(variableType),
        });
        if (
          systemNameMapping[variableKey]?.toLowerCase() !==
          variableName?.toLowerCase()
        ) {
          variables.push({
            displayName: variableName,
            systemName: variableKey,
          });
        }
      });
      if (variableNames.size !== variableNameCount) {
        error = true;
        showError("Object field names should be unique");
      } else if (validFields !== totalFields) {
        error = true;
        showError("Missing data-type or name for the added fields");
      } else if (variables.length > 0) {
        data["variables"] = variables;
      }
    }
    // Check for Hyperlink URL errors
    if (
      !isEmpty(finalHyperlinkedUrl) &&
      (finalHyperlinkedUrl.match(/{/g) || []).length !==
        (finalHyperlinkedUrl.match(/}/g) || []).length
    ) {
      error = true;
      showError("Missing braces in Hyperlink URL");
      setLoading(false);
      return;
    }
    if (error) {
      setAddedVariables(newAddedVariables);
      const firstErrorField = newAddedVariables.find(
        (variable) => variable.error
      );
      if (!isEmpty(firstErrorField)) {
        setErrorField(firstErrorField.variableKey);
        setTimeout(() => {
          setErrorField("");
        }, 2000);
      }
      setLoading(false);
    } else {
      if (!isEqual(objectOrder, tempObjectOrder)) {
        data["isOrderChanged"] = true;
        data["order"] = objectOrder;
      }

      if (
        isHyperlinksSelfServeIntegration &&
        newHyperlinkedField !== null &&
        !isEqual(newHyperlinkedField, hyperlinkedField)
      ) {
        data["hyperlinked_field"] = newHyperlinkedField;
        data["is_new_hyperlink_config"] = hyperlinkedField === null;
        data["service_name"] = selectedConnection?.serviceName;
      }

      // Add hyperlinked URL & hyperlinked Field in data
      if (
        !isHyperlinksSelfServeIntegration &&
        (newHyperlinkedField !== null || newHyperlinkedUrl !== null) &&
        (!isEqual(newHyperlinkedField, hyperlinkedField) ||
          !isEqual(newHyperlinkedUrl, hyperlinkedUrl))
      ) {
        data["hyperlinked_field"] = systemNameMapping[finalHyperlinkedField];
        data["hyperlinked_url"] = finalHyperlinkedUrl;
        data["is_new_hyperlink_config"] =
          hyperlinkedField === null || hyperlinkedUrl === null;
      }

      updateCO(data);
    }
  };

  /**
   * Determines whether the save action should be disabled based on various conditions.
   *
   * @returns {boolean} True if the save action should be disabled, otherwise false.
   *
   * @description
   * - If `isConnection` is true (for self-service):
   *   - Returns true if neither `isVariableChanged` nor `isObjectNameChanged` are true,
   *     and either `newHyperlinkedField` is null or equal to `hyperlinkedField`.
   *
   * - If `isConnection` is false:
   *   - Calculates conditions for hyperlink modification and validity.
   *   - Returns true if:
   *     - Neither `isVariableChanged` nor `isObjectNameChanged` are true,
   *     - Hyperlink modification conditions are met, and
   *     - Hyperlink validity and emptiness conditions are satisfied.
   *   - If the hyperlink already exists (both `hyperlinkedField` and `hyperlinkedUrl` are truthy):
   *     - Returns true if the conditions for hyperlink modification and validity are met.
   *   - If the hyperlink is new:
   *     - Returns true if the conditions for invalid hyperlink are met.
   */
  const isSaveDisabled = () => {
    if (isHyperlinksSelfServeIntegration) {
      // For Self-service
      return (
        !isVariableChanged &&
        !isObjectNameChanged &&
        (newHyperlinkedField === null ||
          isEqual(newHyperlinkedField, hyperlinkedField))
      );
    }
    const isHyperlinkFieldEmpty = isEmpty(
      newHyperlinkedField ?? hyperlinkedField
    );
    const isHyperlinkUrlEmpty = isEmpty(newHyperlinkedUrl ?? hyperlinkedUrl);
    const isHyperlinkUrlValid = isValidHyperlinkUrl(
      newHyperlinkedUrl ?? hyperlinkedUrl
    );
    const isHyperlinkFieldModified = newHyperlinkedField
      ? !isEqual(newHyperlinkedField, hyperlinkedField)
      : false;
    const isHyperlinkUrlModified = newHyperlinkedUrl
      ? !isEqual(newHyperlinkedUrl, hyperlinkedUrl)
      : false;
    const isHyperlinkModified =
      isHyperlinkFieldModified || isHyperlinkUrlModified;

    const hyperlinkUrlContainsMappedFields = isHyperlinkUrlEmpty
      ? false
      : !isEmpty((newHyperlinkedUrl ?? hyperlinkedUrl).match(/{{(.*?)}}/g));

    // Check if newHyperlinkedField is empty or newHyperlinkedUrl is empty or not valid
    const isInvalidHyperlink =
      isHyperlinkFieldEmpty ||
      isHyperlinkUrlEmpty ||
      !isHyperlinkUrlValid ||
      !hyperlinkUrlContainsMappedFields;

    const isHyperlinkModifiedAndValid =
      !isHyperlinkModified ||
      (!isHyperlinkUrlEmpty &&
        (!isHyperlinkUrlValid || isHyperlinkFieldEmpty)) ||
      (!isHyperlinkFieldEmpty &&
        (isHyperlinkUrlEmpty ||
          !isHyperlinkUrlValid ||
          !hyperlinkUrlContainsMappedFields));

    // Hyperlink already exists
    if (hyperlinkedField && hyperlinkedUrl) {
      return (
        !isVariableChanged &&
        !isObjectNameChanged &&
        isHyperlinkModifiedAndValid
      );
    }

    // Hyperlink is new
    return !isVariableChanged && !isObjectNameChanged && isInvalidHyperlink;
  };

  return (
    <div className="flex flex-col justify-between h-[calc(100%-65px)]">
      <div
        className={"w-full flex flex-col overflow-auto my-0 mx-auto h-[74vh]"}
      >
        <div className="pt-8 px-6 pb-6 flex flex-col overflow-auto">
          <EditObjectHeader
            isConnection={isConnection}
            addedVariables={addedVariables}
            objectType={objectType}
            objData={objData}
            store={store}
            selectedConnection={selectedConnection}
            selectedSourceObj={selectedSourceObj}
            {...remainingProps}
          />
          <div
            className={`
              ${
                isConnection && "w-[calc(100%-62px)]"
              }  flex flex-col m-auto max-w-6xl w-full overflow-auto
            `}
          >
            <SearchInput
              placeholder={`Search by field name${
                isConnection ? " or label" : ""
              }`}
              value={searchValue}
              handleSearch={setSearchValue}
              className="max-w-xl w-full mb-4 mx-0"
            />
            <VariableList
              isEdit
              integrationId={integrationId}
              objData={objData}
              objectType={objectType}
              errorField={errorField}
              variables={variables}
              setVariables={setAddedVariables}
              primaryKey={addedPrimaryKey}
              snapshotKey={addedSnapshotKey}
              sourceDestMapping={sourceDestMapping}
              isConnection={isConnection}
              connectionLoading={connectionLoading}
              setConnectionLoading={setConnectionLoading}
              hyperlinkedField={hyperlinkedField}
              selectedConnection={selectedConnection}
              addedVariables={addedVariables}
              isEditMode={true}
              setHyperlinkedField={setHyperlinkedField}
              newHyperlinkedField={newHyperlinkedField}
              setNewHyperlinkedField={setNewHyperlinkedField}
              hyperlinkedUrl={hyperlinkedUrl}
              setHyperlinkedUrl={setHyperlinkedUrl}
              newHyperlinkedUrl={newHyperlinkedUrl}
              setNewHyperlinkedUrl={setNewHyperlinkedUrl}
              selectedSourceObj={selectedSourceObj}
              mappedFields={mappedFields}
              objectId={objData.customObjectId}
              isHyperlinksSelfServeIntegration={
                isHyperlinksSelfServeIntegration
              }
              objectRefetch={objectRefetch}
              resetTransformationConfig={resetTransformationConfig}
              syncFields={syncFields}
            />
            <div className="mt-9">
              <CreatedUpdatedInfo
                objectType={objectType}
                createdBy={objData.createdBy}
                createdAt={objData.createdAt}
                updatedAt={objData?.customObjectVariableLastUpdatedAt}
              />
            </div>
          </div>
        </div>
      </div>
      <Footer
        handleCancel={() => handleCancel(false)}
        handleSave={handleSave}
        isLoading={isLoading}
        isSaveDisabled={isSaveDisabled()}
        showOkButton={objectType === OBJECT_TYPES.CUSTOM_OBJECT}
        okText="Update"
      />
    </div>
  );
};

export default EditObject;
