import {
  AtSignIcon,
  CalendarIcon,
  ContrastIcon,
  HashIcon,
  HierarchyIcon,
  KeyIcon,
  PercentAltIcon,
  TypeIcon,
} from "@everstage/evericons/outlined";
import { LayoutGrid01Icon, NodeZeroIcon } from "@everstage/evericons/solid";
import dagre from "dagre";
import { motion } from "framer-motion";
import "reactflow/dist/style.css";
import {
  Handle,
  Position,
  useStore,
  getBezierPath,
  EdgeLabelRenderer,
} from "reactflow";
import { twMerge } from "tailwind-merge";

import { INTEGRATION } from "~/Enums";
import { EverTg, useCurrentTheme } from "~/v2/components";
import { integrationImages } from "~/v2/images";

const SourceNode = ({ id }) => {
  const icon = integrationImages[INTEGRATION.SALESFORCE];
  const connections = useStore((store) => {
    const edges = store.edges || [];
    const nodeIds = new Set(
      store.nodeInternals ? Array.from(store.nodeInternals.keys()) : []
    );
    return {
      hasOutgoing: edges.some(
        (edge) => edge.source === id && nodeIds.has(edge.target)
      ),
    };
  });
  return (
    <div className="flex items-center justify-center rounded-lg border shadow-md border-ever-base-400 w-30 h-30 p-2 bg-ever-base-100 relative">
      <Handle
        type="source"
        position={Position.Bottom}
        className={twMerge(
          "transition-opacity !bg-ever-base-content-low !border-ever-base-content-low",
          connections.hasOutgoing ? "opacity-100" : "opacity-0 hover:opacity-50"
        )}
        isConnectable={false}
      />
      <div className="flex flex-col items-center">
        <img src={icon} className="w-20 h-20" />
      </div>
    </div>
  );
};

function DataTypeIcon({ name }) {
  const icons = {
    Integer: HashIcon,
    String: TypeIcon,
    Date: CalendarIcon,
    Boolean: ContrastIcon,
    Percentage: PercentAltIcon,
    Email: AtSignIcon,
    Hierarchy: HierarchyIcon,
  };

  const colors = {
    Integer: "bg-ever-chartColors-8",
    String: "bg-ever-chartColors-6",
    Date: "bg-ever-chartColors-24",
    Boolean: "bg-ever-chartColors-2",
    Percentage: "bg-ever-chartColors-9",
    Email: "bg-ever-chartColors-25",
    Hierarchy: "bg-ever-chartColors-25",
  };

  const Icon = icons[name];
  if (!Icon) return null;

  return (
    <div
      className={`${colors[name]} w-3.5 h-3.5 flex items-center justify-center rounded-sm`}
    >
      <Icon className="w-2.5 h-2.5 text-ever-base-content" />
    </div>
  );
}

const CustomNode = ({ data, id }) => {
  const connections = useStore((store) => {
    const edges = store.edges || [];
    const nodeIds = new Set(
      store.nodeInternals ? Array.from(store.nodeInternals.keys()) : []
    );
    return {
      hasIncoming: edges.some(
        (edge) => edge.target === id && nodeIds.has(edge.source)
      ),
      hasOutgoing: edges.some(
        (edge) => edge.source === id && nodeIds.has(edge.target)
      ),
    };
  });

  return (
    <div className="items-center gap-3 rounded-md border bg-white border-ever-base-400 shadow-sm cursor-pointer relative min-w-60">
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className={twMerge(
          "transition-opacity !bg-ever-base-content-low !border-ever-base-content-low",
          connections.hasIncoming ? "opacity-100" : "opacity-0 hover:opacity-50"
        )}
        isConnectable={false}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className={twMerge(
          "transition-opacity !bg-ever-base-content-low !border-ever-base-content-low",
          connections.hasOutgoing ? "opacity-100" : "opacity-0 hover:opacity-50"
        )}
        isConnectable={false}
      />
      <div className="p-3 pl-0 flex items-center gap-2">
        <div
          className={twMerge(
            "w-[3px] h-8 rounded-r-lg",
            data.nodeOrder == 0
              ? "bg-ever-info-content-lite"
              : "bg-ever-success-content-lite"
          )}
        />
        <div
          className={twMerge(
            "size-9 p-2 rounded-sm flex justify-center items-center",
            data.nodeOrder == 0 ? "bg-ever-info-lite" : "bg-ever-success-lite"
          )}
        >
          {data.nodeOrder == 0 ? (
            <NodeZeroIcon className="size-4 text-ever-info-content-lite" />
          ) : (
            <LayoutGrid01Icon className="size-4 text-ever-success-content-lite" />
          )}
        </div>
        <div className="min-w-[150px]">
          <div className="flex items-center">
            <EverTg.Heading4>{data.label}</EverTg.Heading4>
          </div>
          <div className="text-ever-base-content-mid text-xs font-medium">
            ({data.aliasName})
          </div>
        </div>
        <div className="ml-auto">
          {data.nodeOrder == 0 ? (
            <div className="flex justify-center items-center py-0.5 px-2 bg-ever-info-lite rounded-full">
              <EverTg.Caption className="text-[10px] text-ever-info">
                Object
              </EverTg.Caption>
            </div>
          ) : (
            <div className="flex justify-center items-center py-0.5 px-2 bg-ever-success-lite rounded-full">
              <EverTg.Caption className="text-[10px] text-ever-success-hover">
                Datasheet
              </EverTg.Caption>
            </div>
          )}
          <div className="h-4" />
        </div>
      </div>
      <div className="bg-ever-base-50 border-t border-ever-base-400 rounded-b-lg p-3 flex flex-col gap-1">
        {data?.fields?.map(
          (field, index) =>
            field?.isPrimaryKey && (
              <div
                key={`${field.name}-${index}`}
                className="flex justify-center items-center gap-4"
              >
                <div className="flex justify-center items-center gap-2">
                  <DataTypeIcon name={field.type} />
                  <EverTg.Caption className="text-ever-base-content">
                    {field.name}
                  </EverTg.Caption>
                </div>
                <KeyIcon className="ml-auto size-3.5 text-ever-base-content-mid" />
              </div>
            )
        )}
      </div>
    </div>
  );
};

const CustomEdge = ({ id, sourceX, sourceY, targetX, targetY, label }) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });
  const colors = useCurrentTheme();
  const pathVariant = {
    hidden: {
      opacity: 0.1,
      pathLength: 0,
    },
    visible: {
      opacity: 1,
      pathLength: 1,
      transition: {
        duration: 3,
        ease: "easeInOut",
      },
    },
  };
  return (
    <>
      <path
        d={edgePath}
        id={id}
        stroke={colors.base[100]}
        strokeWidth={1.5}
        fill="none"
      />
      <motion.path
        d={edgePath}
        stroke={colors.base.content.low}
        strokeWidth={1.5}
        fill="none"
        variants={pathVariant}
        initial="hidden"
        animate="visible"
      />
      {label && (
        <EdgeLabelRenderer>
          <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 fade-in"
            style={{ left: `${labelX}px`, top: `${labelY}px` }}
          >
            <div className="bg-white w-[150px] text-center rounded-md border border-ever-base-400 p-1">
              <EverTg.Caption className="text-balance text-ever-base-content-mid text-center">
                {label}
              </EverTg.Caption>
            </div>
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

export const getLayoutedElements = (nodes, edges) => {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));

  dagreGraph.setGraph({
    rankdir: "TB",
    nodesep: 120,
    ranksep: 100,
  });

  for (const node of nodes) {
    if (node.id === "salesforce") {
      dagreGraph.setNode(node.id, { width: 75, height: 170, rank: node.rank });
    } else {
      dagreGraph.setNode(node.id, { width: 250, height: 220, rank: node.rank });
    }
  }

  for (const edge of edges) {
    dagreGraph.setEdge(edge.source, edge.target);
  }

  dagre.layout(dagreGraph);

  for (const node of nodes) {
    const nodeWithPosition = dagreGraph.node(node.id);
    node.position = {
      x: nodeWithPosition.x - nodeWithPosition.width / 2,
      y: nodeWithPosition.y - nodeWithPosition.height / 2,
    };
  }

  return { nodes, edges };
};

export const nodeTypes = {
  tableNode: CustomNode,
  sourceNode: SourceNode,
};

export const edgeTypes = {
  customEdge: CustomEdge,
};
