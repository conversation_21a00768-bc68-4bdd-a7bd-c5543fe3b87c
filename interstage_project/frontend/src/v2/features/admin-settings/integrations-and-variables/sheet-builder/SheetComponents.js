import "reactflow/dist/style.css";
import { useQuery } from "@apollo/client";
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckIcon,
  PlusSquareIcon,
  XIcon,
  ChevronLeftIcon,
  XCloseIcon,
  Save03Icon,
} from "@everstage/evericons/outlined";
import { Col, Row } from "antd";
import { useEffect, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import {
  EverInput,
  EverCheckbox,
  EverBadge,
  EverTg,
  EverSelect,
  EverModal,
  EverLabel,
  EverRadio,
  EverButton,
  EverTooltip,
  EverLoader,
  EverCard,
} from "~/v2/components";
import { DataTypeIcon } from "~/v2/components/expression-designer/utils";

import { GET_FIELDS } from "../graphql";

const getMatchScore = (label, query) => {
  let score = 0;
  for (let i = 0; i < Math.min(label.length, query.length); i++) {
    if (
      label[i].replaceAll(" ", "").toLowerCase() ===
      query[i].replaceAll(" ", "").toLowerCase()
    ) {
      score += 1;
    } else {
      break;
    }
  }
  return score;
};

const getType = (fieldType) => {
  return (
    {
      integer: "Integer",
      currency: "Integer",
      double: "Integer",
      float: "Integer",
      boolean: "Boolean",
      date: "Date",
      datetime: "Date",
      percentage: "Percentage",
      email: "Email",
      hierarchy: "Hierarchy",
    }[fieldType] || "String"
  );
};

export const FieldPreview = ({ name, accessTokenConfigId, setFieldCount }) => {
  const [fields, setFields] = useState([]);

  const { loading } = useQuery(GET_FIELDS, {
    variables: {
      serviceName: "salesforce",
      objectId: name?.name,
      accessTokenConfigId: accessTokenConfigId,
    },
    onError: (error) => {
      console.error(error);
    },
    onCompleted: (data) => {
      setFieldCount(data.fields.length);
      setFields(data.fields);
    },
    skip: !name,
  });

  return (
    <div className="h-full pr-1 overflow-hidden">
      {loading ? (
        <EverLoader size={2} />
      ) : (
        <div className="mt-2 gap-y-1 overflow-y-auto max-h-full pb-4">
          {fields.map((field, index) => (
            <div
              key={index}
              className="flex items-center gap-3 border border-ever-base-400 min-w-[20px] m-1 px-3 py-2 rounded-md"
            >
              <DataTypeIcon name={getType(field.type)} />
              <div className="flex flex-col">
                <EverTg.Text className="break-all">{field.label}</EverTg.Text>
                <EverTg.Caption className="break-all">
                  {field.name}
                </EverTg.Caption>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export const SheetColumns = ({
  isEdit,
  setIsEdit,
  field,
  selectedNode,
  setSelectedNode,
  reloadComponent,
  setReloadComponent,
}) => {
  const { name, type, isPrimaryKey } = field;
  const handleSelectField = () => {
    if (isPrimaryKey) {
      return;
    }
    const selected = selectedNode;
    selected.data.fields
      .filter((fields) => fields.name == name)
      .map((field) => {
        const { isSelected } = field;
        field.isSelected = !isSelected;
        return field;
      });
    setSelectedNode(selected);
    setReloadComponent(reloadComponent + 1);
  };
  return (
    <div className={"flex items-center w-full pb-1 pr-1"}>
      {isEdit ? (
        <EverInput value={name} onBlur={() => setIsEdit(false)} autoFocus />
      ) : (
        <div
          className={` ${
            name.length < 254 ? "border-ever-base-400" : "border-ever-error"
          } flex border border-solid w-full min-h-[40px] items-center gap-3 px-3 py-2 rounded-lg
            `}
        >
          <EverCheckbox
            disabled={isPrimaryKey}
            className="flex flex-col"
            checked={
              selectedNode.data.fields.find((fields) => fields.name == name)
                .isSelected
            }
            onChange={handleSelectField}
          />
          <div
            className={twMerge(
              "flex items-center justify-between gap-3 w-full",
              isPrimaryKey ? "cursor-not-allowed" : "cursor-text"
            )}
            aria-disabled={isPrimaryKey}
          >
            <div className="flex items-center gap-3">
              <div>
                <DataTypeIcon name={type} />
              </div>
              <EverTg.Text className="break-all">{name}</EverTg.Text>
            </div>
            <div className="flex items-center gap-3">
              {isPrimaryKey && <EverBadge type={"error"} title="Primary key" />}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const Fields = ({
  selectedNode,
  setSelectedNode,
  isEdit,
  setIsEdit,
}) => {
  const [fieldCount, setFieldCount] = useState(0);
  const [reloadComponent, setReloadComponent] = useState(0);
  const [allSelected, setAllSelected] = useState(false);
  const [selectedFieldCount, setSelectedFieldCount] = useState(0);
  const [searchedField, setSearchedField] = useState("");
  const scrollContainerRef = useRef(null);

  const toggleSelectAll = () => {
    const updatedNode = { ...selectedNode };
    updatedNode.data.fields = updatedNode.data.fields.map((field) => {
      if (field.isPrimaryKey) {
        return field;
      }
      return {
        ...field,
        isSelected: !allSelected,
      };
    });
    setSelectedNode(updatedNode);
    setAllSelected(!allSelected);
    setReloadComponent((prev) => prev + 1);
  };

  useEffect(() => {
    const fields = selectedNode.data.fields || [];
    setFieldCount(fields.length);
    setAllSelected(fields.every((field) => field.isSelected));
    setSelectedFieldCount(fields.filter((field) => field.isSelected).length);
  }, [selectedNode.data.fields, reloadComponent]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  }, [searchedField]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
    if (selectedNode) {
      selectedNode.data.fields.sort((a, b) => {
        if (a.isSelected && !b.isSelected) return -1;
        if (!a.isSelected && b.isSelected) return 1;
        return 0;
      });
    }
  }, [selectedNode]);

  return (
    <div
      ref={scrollContainerRef}
      className="flex-1 p-4 pt-0 border border-gray-300 rounded-lg overflow-y-auto relative"
    >
      <div className="sticky py-3 top-0 z-10 bg-white">
        <EverInput.Search
          placeholder="Search by field name"
          value={searchedField}
          onChange={(e) => setSearchedField(e.target.value)}
          enterButton={
            <XIcon
              className="w-5 h-5 text-ever-base-content-low cursor-pointer"
              onClick={() => setSearchedField("")}
            />
          }
          className="mb-3"
        />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <EverCheckbox checked={allSelected} onChange={toggleSelectAll} />
            <EverTg.Text>select all</EverTg.Text>
          </div>
          {fieldCount > 0 && (
            <EverBadge
              type={"base"}
              title={`${selectedFieldCount.toString()}/${fieldCount.toString()}`}
            />
          )}
        </div>
      </div>
      <ul className="mt-2 space-y-1">
        {selectedNode.data.fields
          .filter((x) =>
            x.name
              .toLowerCase()
              .includes(searchedField.replaceAll(" ", "").toLowerCase())
          )
          .sort((a, b) => {
            if (a.isPrimaryKey && !b.isPrimaryKey) return -1;
            if (!a.isPrimaryKey && b.isPrimaryKey) return 1;
            const scoreA = getMatchScore(a.name, searchedField);
            const scoreB = getMatchScore(b.name, searchedField);
            return scoreB - scoreA;
          })
          .map((field, index) => (
            <SheetColumns
              key={index}
              isEdit={isEdit}
              setIsEdit={setIsEdit}
              field={field}
              selectedNode={selectedNode}
              setSelectedNode={setSelectedNode}
              reloadComponent={reloadComponent}
              setReloadComponent={setReloadComponent}
            />
          ))}
      </ul>
    </div>
  );
};

export const ObjectItem = ({
  name,
  onSelect,
  textareaRef,
  handleTextareaInput,
}) => {
  const [isCopied, setIsCopied] = useState(false);

  const copy = (name) => {
    if (textareaRef.current) {
      const updatedPrompt = textareaRef.current.value.trimEnd() + " " + name;
      textareaRef.current.value = updatedPrompt;
      handleTextareaInput({ target: textareaRef.current });
    }
    navigator.clipboard.writeText(name);
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 1000);
  };

  return (
    <div className="flex items-center w-full pr-2 relative">
      <div
        className="flex w-full min-h-[40px] items-center gap-3 px-3 py-2 cursor-pointer"
        onClick={onSelect}
      >
        <EverTg.Text className="break-all">{name.label}</EverTg.Text>
      </div>
      <div
        className="gap-1 ml-3 mr-1 flex cursor-pointer relative"
        onClick={() => copy(name.label)}
      >
        <EverTooltip
          title={isCopied ? "Copied to clipboard" : "Copy to clipboard"}
        >
          {isCopied ? (
            <CheckIcon className="w-4 h-4 text-ever-base-content-mid text-ever-primary" />
          ) : (
            <PlusSquareIcon className="w-4 h-4 text-ever-base-content-mid" />
          )}
        </EverTooltip>
      </div>
      <div
        className="gap-1 ml-3 mr-1 flex cursor-pointer relative"
        onClick={onSelect}
      >
        <ChevronRightIcon className="w-4 h-4 text-ever-base-content-mid" />
      </div>
    </div>
  );
};

export const SheetDrawerFooter = ({
  onClose,
  onGenerateObjectAndSheet,
  nodes,
  databookName,
  setDatabookName,
  isFinalLoadVisible,
}) => {
  const [showDatabookNameModel, setShowDatabookNameModel] = useState(false);

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && databookName) {
      e.preventDefault();
      onGenerateObjectAndSheet(nodes);
      setShowDatabookNameModel(false);
    }
  };

  return (
    <div className="flex justify-end gap-3">
      <EverModal
        visible={showDatabookNameModel}
        destroyOnClose={true}
        centered={true}
        onClose={() => {
          setDatabookName("");
          setShowDatabookNameModel(false);
        }}
        onCancel={() => {
          setDatabookName("");
          setShowDatabookNameModel(false);
        }}
        footer={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => {
              setDatabookName("");
              setShowDatabookNameModel(false);
            }}
            type="ghost"
          >
            Cancel
          </EverButton>,
          <EverButton
            key="create"
            onClick={() => {
              setShowDatabookNameModel(false);
              onGenerateObjectAndSheet(nodes);
            }}
            disabled={!databookName}
          >
            Create
          </EverButton>,
        ]}
        title="Enter Databook Name"
      >
        <Row>
          <Col span={24} className="mb-2">
            <EverLabel required>Name</EverLabel>
          </Col>
          <Col span={24}>
            <EverInput
              value={databookName}
              onChange={(e) => setDatabookName(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter databook name"
              autoFocus
            />
          </Col>
        </Row>
      </EverModal>
      <EverButton
        key="close"
        color="base"
        type="ghost"
        onClick={onClose}
        disabled={isFinalLoadVisible}
      >
        Close
      </EverButton>
      <EverButton
        key="save"
        onClick={() => {
          setShowDatabookNameModel(true);
        }}
        disabled={isFinalLoadVisible}
      >
        Save
      </EverButton>
    </div>
  );
};

export const FilterTransformation = ({
  transformationSpec,
  setUpdateUserPrompt,
  selectedNode,
}) => {
  const { conditionLabel } = transformationSpec;
  const aliasName = selectedNode.data.aliasName;
  const [conditionLabelEdited, setConditionLabelEdited] =
    useState(conditionLabel);

  function modifyTransformation(value) {
    if (value === conditionLabel) {
      setUpdateUserPrompt(null);
    } else {
      const updatedTransformationText = `Modify node ${aliasName} filter transformation condition to ${value}`;
      setUpdateUserPrompt(updatedTransformationText);
    }
  }

  useEffect(() => {
    setConditionLabelEdited(conditionLabel);
  }, [transformationSpec]);

  return (
    <div className="flex flex-col space-y-6">
      <EverLabel className="mb-2 font-semibold text-gray-800">
        Filter Transformation
      </EverLabel>
      <div className="flex flex-col">
        <EverLabel className="mb-2">Filter</EverLabel>
        <EverInput
          value={conditionLabelEdited}
          className="w-full h-auto"
          onChange={(e) => {
            setConditionLabelEdited(e.target.value);
            modifyTransformation(e.target.value);
          }}
        />
      </div>
    </div>
  );
};

export const JoinTransformation = ({
  parentDatasheets,
  transformationSpec,
  setUpdateUserPrompt,
  selectedNode,
}) => {
  const { joinType, joinedWith, lhsField, rhsField } = transformationSpec;
  const aliasName = selectedNode.data.aliasName;

  const [joinTypeEdited, setJoinTypeEdited] = useState(joinType);
  const [lhsFieldEdited, setLhsFieldEdited] = useState(lhsField);
  const [rhsFieldEdited, setRhsFieldEdited] = useState(rhsField);

  function modifyTransformation(
    newJoinType = null,
    newLhsField = null,
    newRhsField = null
  ) {
    const currentJoinType = newJoinType !== null ? newJoinType : joinTypeEdited;
    const currentLhsField = newLhsField !== null ? newLhsField : lhsFieldEdited;
    const currentRhsField = newRhsField !== null ? newRhsField : rhsFieldEdited;

    if (
      currentJoinType === joinType &&
      currentLhsField === lhsField &&
      currentRhsField === rhsField
    ) {
      setUpdateUserPrompt(null);
    } else {
      const updatedTransformationText = `Modify node ${aliasName} join transformation condition as ${currentJoinType} join on ${currentLhsField} = ${currentRhsField}`;
      setUpdateUserPrompt(updatedTransformationText);
    }
  }

  useEffect(() => {
    setJoinTypeEdited(joinType);
    setLhsFieldEdited(lhsField);
    setRhsFieldEdited(rhsField);
  }, [transformationSpec]);

  return (
    <div className="flex flex-col space-y-6">
      <EverLabel className="mb-2 font-semibold text-gray-800">
        Join Transformation
      </EverLabel>
      <div className="flex flex-col">
        <EverLabel className="mb-2">Join Type</EverLabel>
        <EverRadio.Group
          value={joinTypeEdited}
          onChange={(e) => {
            const newValue = e.target.value;
            setJoinTypeEdited(newValue);
            modifyTransformation(newValue, null, null);
          }}
        >
          <EverRadio value="LEFT" label="Left" />
          <EverRadio value="RIGHT" label="Right" />
          <EverRadio value="INNER" label="Inner" />
          <EverRadio value="FULL" label="Full" />
        </EverRadio.Group>
      </div>

      <div className="flex flex-col">
        <EverLabel className="mb-2">Join with</EverLabel>
        <EverSelect
          mode="single"
          disabled={true}
          maxTagTextLength={16}
          showSearch
          value={joinedWith}
          suffixIcon={<ChevronDownIcon />}
          tagClassName="w-auto max-w-[320px]"
          className="w-full h-auto"
          maxTagCount={10_000}
          placeholder="Select Datasheet"
        />
      </div>

      <div className="flex flex-col">
        <EverLabel className="mb-2">
          {parentDatasheets[0]} lookup columns
        </EverLabel>
        <EverInput
          value={lhsFieldEdited}
          className="w-full h-auto"
          onChange={(e) => {
            const newValue = e.target.value;
            setLhsFieldEdited(newValue);
            modifyTransformation(null, newValue, null);
          }}
        />
      </div>

      <div className="flex flex-col">
        <EverLabel className="mb-2">
          {parentDatasheets[1]} lookup columns
        </EverLabel>
        <EverInput
          value={rhsFieldEdited}
          className="w-full h-auto"
          onChange={(e) => {
            const newValue = e.target.value;
            setRhsFieldEdited(newValue);
            modifyTransformation(null, null, newValue);
          }}
        />
      </div>
    </div>
  );
};

export const Transformations = ({
  selectedNode,
  onCallGenerateNodesLLM,
  setSelectedNode,
  isNodeGenerating,
}) => {
  const allTransformations = selectedNode.data.transformations;

  const [updateUserPrompt, setUpdateUserPrompt] = useState(null);

  const transformationMap = {
    JOIN: (spec) => (
      <JoinTransformation
        parentDatasheets={selectedNode.data.parentDatasheets}
        transformationSpec={spec}
        setUpdateUserPrompt={setUpdateUserPrompt}
        selectedNode={selectedNode}
      />
    ),
    FILTER: (spec) => (
      <FilterTransformation
        transformationSpec={spec}
        setUpdateUserPrompt={setUpdateUserPrompt}
        selectedNode={selectedNode}
      />
    ),
  };

  return (
    <div className="flex-1 p-4 border border-gray-300 rounded-lg overflow-y-auto space-y-6">
      {allTransformations.map((transformation, index) => {
        const Component = transformationMap[transformation.type];
        return (
          <div key={index} className="space-y-4">
            <div>{Component ? Component(transformation) : null}</div>
            {index < allTransformations.length - 1 && (
              <div className="h-px bg-gray-200 w-full" />
            )}
          </div>
        );
      })}
      <EverButton
        className="mt-4 w-full"
        size="large"
        onClick={() => {
          if (!isNodeGenerating) {
            onCallGenerateNodesLLM({ updateUserPrompt });
            setSelectedNode(null);
          }
        }}
        disabled={!updateUserPrompt}
        loading={isNodeGenerating}
      >
        Update Transformation
      </EverButton>
    </div>
  );
};

export const FieldsDrawer = ({
  selectedNode,
  setSelectedNode,
  onCallGenerateNodesLLM,
  nodes,
  setNodes,
  isNodeGenerating,
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [editName, setEditName] = useState(false);
  const [datasheetName, setDatasheetName] = useState(selectedNode.data.label);

  const closeSidebar = () => setSelectedNode(null);

  const handleFocusOut = () => {
    const updatedNode = { ...selectedNode };
    updatedNode.data.label = datasheetName;
    setSelectedNode(updatedNode);
    const updatedNodes = nodes.map((node) => {
      if (node.id === selectedNode.id) {
        return {
          ...node,
          data: {
            ...node.data,
            label: datasheetName,
          },
        };
      }
      return node;
    });
    setNodes(updatedNodes);
    setEditName(false);
  };

  useEffect(() => {
    setDatasheetName(selectedNode?.data?.label || "");
  }, [selectedNode]);

  return (
    selectedNode && (
      <div className="border border-solid border-ever-base-400 border-r-0 border-b-0 border-t-0 pl-3 relative h-full flex flex-col w-[650px]">
        <div className="absolute left-[-5px] top-0 h-full w-1 border-ever-base-400" />
        <div className="flex items-center gap-2 mb-1 p-1">
          {editName ? (
            <EverInput
              placeholder="Enter datasheet name"
              value={datasheetName}
              className="w-64"
              onChange={(e) => setDatasheetName(e.target.value)}
              onBlur={handleFocusOut}
              onPressEnter={handleFocusOut}
              suffix={
                <Save03Icon
                  className="w-4 h-4 text-ever-base-content-mid cursor-pointer hover:text-ever-primary"
                  onClick={handleFocusOut}
                />
              }
              autoFocus
            />
          ) : (
            <div className="flex items-center">
              <div>
                <EverTg.SubHeading2
                  title={datasheetName}
                  className="inline-block align-middle w-[300px] mr-2 overflow-hidden whitespace-nowrap overflow-ellipsis truncate cursor-pointer"
                >
                  {datasheetName}
                </EverTg.SubHeading2>
              </div>
              {/* <EverBadge
                className="cursor-pointer"
                type="warning"
                title="Edit"
                onClick={() => setEditName(true)}
                data-test-id="connection-name-edit-icon"
                icon={<EditPencilIcon className="w-3 h-3" />}
              /> */}
            </div>
          )}
          <div
            className="hover:bg-ever-base-100 rounded w-9 h-9 flex items-center justify-center cursor-pointer ml-auto"
            onClick={closeSidebar}
          >
            <XCloseIcon className="w-6 h-6 text-ever-base-content-mid" />
          </div>
        </div>

        <div className="flex-1 flex flex-col space-y-2 overflow-hidden">
          <Fields
            selectedNode={selectedNode}
            setSelectedNode={setSelectedNode}
            isEdit={isEdit}
            setIsEdit={setIsEdit}
          />
          {selectedNode.data.transformations && (
            <Transformations
              selectedNode={selectedNode}
              onCallGenerateNodesLLM={onCallGenerateNodesLLM}
              setSelectedNode={setSelectedNode}
              isNodeGenerating={isNodeGenerating}
            />
          )}
        </div>
      </div>
    )
  );
};

export const PreviewDrawer = ({
  viewSobjects,
  setViewSobjects,
  loading,
  objects,
  selectedObject,
  setSelectedObject,
  textareaRef,
  handleTextareaInput,
  accessTokenConfigId,
}) => {
  const [fieldCount, setFieldCount] = useState(0);
  const [isCopied, setIsCopied] = useState(false);
  const [searchedData, setSearchedData] = useState("");
  const scrollContainerRef = useRef(null);

  const copy = (name) => {
    if (textareaRef.current) {
      const el = textareaRef.current;
      el.value = el.value.trimEnd() + " " + name;
      handleTextareaInput({ target: el });
    }
    navigator.clipboard.writeText(name);
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 1000);
  };

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  }, [searchedData]);

  const SectionHeader = ({ title }) => (
    <div className="flex items-center gap-3 px-3 py-2">
      <EverTg.Text className="text-ever-base-content-low font-medium whitespace-nowrap">
        {title}
      </EverTg.Text>
      <div className="h-[1px] bg-ever-base-400 w-full" />
    </div>
  );

  const renderGroupedObjects = (objects, searchedData) => {
    const sortedObjects = objects
      .filter((x) => x.label.toLowerCase().includes(searchedData.toLowerCase()))
      .sort((a, b) => {
        if (b.priority !== a.priority) {
          return b.priority - a.priority;
        }
        const scoreA = getMatchScore(a.label, searchedData);
        const scoreB = getMatchScore(b.label, searchedData);
        return scoreB - scoreA;
      });

    const priorityObjects = sortedObjects.filter((obj) => obj.priority);
    const nonPriorityObjects = sortedObjects.filter((obj) => !obj.priority);

    return (
      <>
        {priorityObjects.length > 0 && (
          <div>
            <SectionHeader title="QUICK PICKS" />
            {priorityObjects.map((name, index) => (
              <ObjectItem
                key={index}
                name={name}
                onSelect={() => setSelectedObject(name)}
                textareaRef={textareaRef}
                handleTextareaInput={handleTextareaInput}
              />
            ))}
          </div>
        )}
        {nonPriorityObjects.length > 0 && (
          <div>
            <SectionHeader title="OTHER SALESFORCE OBJECTS" />
            {nonPriorityObjects.map((name, index) => (
              <ObjectItem
                key={index}
                name={name}
                onSelect={() => setSelectedObject(name)}
                textareaRef={textareaRef}
                handleTextareaInput={handleTextareaInput}
              />
            ))}
          </div>
        )}
      </>
    );
  };

  return (
    <div
      className={twMerge(
        "flex transition-all duration-300 ease-in-out",
        viewSobjects ? "translate-x-0" : "ml-5 -translate-x-full"
      )}
    >
      <div
        className={twMerge(
          "relative gap-2 h-full flex flex-col overflow-x-hidden shrink-0 grow-0 transition-all duration-300 ease-in-out",
          viewSobjects ? "w-72" : "w-0 opacity-0"
        )}
      >
        <EverCard className="h-full p-0 overflow-hidden">
          {loading ? (
            <EverLoader size={2} />
          ) : (
            <div className="flex max-h-full overflow-hidden">
              <div
                ref={scrollContainerRef}
                className={twMerge(
                  "flex flex-col w-72 max-h-full shrink-0 grow-0",
                  selectedObject ? "overflow-hidden" : "overflow-y-auto"
                )}
              >
                <div className="p-2 sticky top-0 bg-ever-base z-10">
                  <EverInput.Search
                    placeholder="Search by object name"
                    value={searchedData}
                    onChange={(e) => setSearchedData(e.target.value)}
                    enterButton={
                      <XIcon
                        className="w-5 h-5 text-ever-base-content-low cursor-pointer"
                        onClick={() => setSearchedData("")}
                      />
                    }
                  />
                </div>
                {renderGroupedObjects(objects, searchedData)}
              </div>
              <div
                className={twMerge(
                  "flex flex-col w-72 shrink-0 grow-0 max-h-full transition-all bg-ever-base relative z-10",
                  selectedObject ? "-translate-x-full" : "translate-x-0"
                )}
              >
                <div className="flex py-2 px-3 gap-3 border-b border-ever-base-200 bg-ever-base-50 border-solid items-center cursor-default justify-between">
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={() => {
                      setFieldCount(0);
                      setSelectedObject(null);
                    }}
                  >
                    <ChevronLeftIcon className="w-5 h-5 text-ever-base-content-mid" />
                  </div>
                  <EverTg.Text className="flex-1 text-center">
                    {selectedObject?.label} Fields
                  </EverTg.Text>
                  <div className="flex items-center">
                    <EverBadge type={"base"} title={fieldCount.toString()} />
                  </div>
                  <div
                    className="flex cursor-pointer relative"
                    onClick={() => copy(selectedObject.label)}
                  >
                    <EverTooltip
                      title={
                        isCopied ? "Copied to clipboard" : "Copy to clipboard"
                      }
                    >
                      {isCopied ? (
                        <CheckIcon className="w-4 h-4 text-ever-primary" />
                      ) : (
                        <PlusSquareIcon className="w-4 h-4 text-ever-base-content-mid" />
                      )}
                    </EverTooltip>
                  </div>
                </div>

                <FieldPreview
                  name={selectedObject}
                  accessTokenConfigId={accessTokenConfigId}
                  setFieldCount={setFieldCount}
                />
              </div>
            </div>
          )}
        </EverCard>
      </div>
      <div
        className="ml-2 cursor-pointer border border-solid border-ever-base-400 bg-ever-base hover:bg-ever-base-100 rounded-full flex items-center justify-center w-5 h-5 shrink-0 grow-0"
        onClick={() => setViewSobjects(!viewSobjects)}
      >
        {viewSobjects ? (
          <ChevronLeftIcon className="w-4 h-4 flex-shrink-0" />
        ) : (
          <EverTooltip title={"View salesforce objects"} placement="right">
            <ChevronRightIcon className="w-4 h-4 flex-shrink-0" />
          </EverTooltip>
        )}
      </div>
    </div>
  );
};
