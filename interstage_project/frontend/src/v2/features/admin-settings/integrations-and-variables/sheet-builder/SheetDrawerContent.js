import { useQuery } from "@apollo/client";
import { XIcon } from "@everstage/evericons/outlined";
import { MagicWandIcon } from "@everstage/evericons/solid";
import { useState, useRef, useEffect, useCallback } from "react";
import "reactflow/dist/style.css";
import ReactFlow, {
  ReactFlowProvider,
  Background,
  Controls,
  BackgroundVariant,
} from "reactflow";
import { twMerge } from "tailwind-merge";

import { INTEGRATION } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverButton, EverHotToastMessage, toast } from "~/v2/components";
import { generateNodeOperations } from "~/v2/features/admin-settings/integrations-and-variables/sheet-builder/api";
import useInvokeLLMWithTag from "~/v2/features/llm-invocations";

import { nodeTypes, edgeTypes, getLayoutedElements } from "./FlowComponents";
import { PreviewDrawer, FieldsDrawer } from "./SheetComponents";
import { showError } from "../common";
import { SKILL_TAGS, PRIORITY_SALESFORCE_OBJECTS } from "../constants";
import { GET_ALL_OBJECTS } from "../graphql";

export const SheetDrawerContent = ({
  nodes,
  edges,
  setNodes,
  setEdges,
  accessTokenConfigId,
  loadingToastId,
  setLoadingToastId,
}) => {
  const [isNodeGenerating, setIsNodeGenerating] = useState(false);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedObject, setSelectedObject] = useState(null);
  const [viewSobjects, setViewSobjects] = useState(true);
  const [currentEdges, setCurrentEdges] = useState([]);
  const [currentNodes, setCurrentNodes] = useState([]);
  const [rerenderNodes, setRerenderNodes] = useState(true);
  const [objects, setObjects] = useState([]);
  const [isUpdateNode, setIsUpdateNode] = useState(false);
  const [frequentObjects, setFrequentObjects] = useState([]);
  const [updatingNodeLabel, setUpdatingNodeLabel] = useState("");
  const { accessToken } = useAuthStore();
  const textareaRef = useRef(null);
  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useRef(null);
  const llmInvoker = useInvokeLLMWithTag();

  const onNodeClick = useCallback((_event, node) => {
    setSelectedNode((prevNode) =>
      prevNode?.id == node?.id || node?.id === "salesforce" ? null : node
    );
  }, []);

  const { loading } = useQuery(GET_ALL_OBJECTS, {
    variables: {
      serviceName: "salesforce",
      accessTokenConfigId: accessTokenConfigId,
    },
    onError: (error) => {
      showError(error.message);
    },
    onCompleted: (data) => {
      if (data?.objects) {
        const sortedObjects = data.objects
          .map((item) => ({
            ...item,
            priority: PRIORITY_SALESFORCE_OBJECTS.has(item.name),
          }))
          .sort((a, b) => b.priority - a.priority);
        const filteredObjects = data.objects.filter((item) =>
          PRIORITY_SALESFORCE_OBJECTS.has(item.name)
        );
        setObjects(sortedObjects);
        setFrequentObjects(filteredObjects);
      }
    },
  });

  async function onSuccessGenerateNode(responseData) {
    try {
      const operations = responseData;
      let tempNodes = [...nodes];
      let tempEdges = [...edges];
      for (const operation of operations) {
        switch (operation.operationType) {
          case "ADD_NODE": {
            const { node } = operation;
            if (tempNodes.length === 0) {
              tempNodes.push({
                id: "salesforce",
                type: "sourceNode",
                position: { x: 0, y: 0 },
                data: { nodeOrder: -1 },
              });
            }
            if (node.data.nodeOrder === 0) {
              tempEdges.push({
                id: `salesforce_to_${node.id}`,
                source: "salesforce",
                target: node.id,
                type: "customEdge",
              });
            }
            tempNodes.push(node);
            break;
          }
          case "ADD_EDGE": {
            const { edge } = operation;
            tempEdges.push(edge);
            break;
          }
          case "REMOVE_NODE": {
            const { nodeId } = operation;
            tempNodes = tempNodes.filter((node) => node.id !== nodeId);

            if (tempNodes.length === 1 && tempNodes[0].id === "salesforce") {
              tempNodes = [];
              tempEdges = [];
            }
            break;
          }
          case "REMOVE_EDGE": {
            const { edgeId } = operation;
            tempEdges = tempEdges.filter((edge) => edge.id !== edgeId);
            break;
          }
          case "UPDATE_NODE": {
            const nodeId = operation.node.id;
            tempNodes = tempNodes.map((node) => {
              if (node.id === nodeId) {
                return {
                  ...node,
                  data: operation.node.data,
                };
              }
              return node;
            });
            break;
          }
          case "UPDATE_EDGE": {
            const updatedEdge = operation.edge;
            tempEdges = tempEdges.map((edge) => {
              if (
                edge.source === updatedEdge.source &&
                edge.target === updatedEdge.target
              ) {
                return updatedEdge;
              }
              return edge;
            });
            break;
          }
          default: {
            break;
          }
        }
      }

      if (tempNodes && tempEdges) {
        setCurrentEdges(tempEdges);
        setCurrentNodes(tempNodes);
        setRerenderNodes(true);
        setIsNodeGenerating(false);
        setSelectedNode(null);
      } else {
        setIsNodeGenerating(false);
        showError("Error generating nodes. Try again!");
      }
      setIsUpdateNode(false);
    } catch (error) {
      onErrorGenerateNode(error);
    }
  }

  function onErrorGenerateNode(error) {
    setIsNodeGenerating(false);
    setIsUpdateNode(false);
    showError(error?.message || "Error generating nodes. Try again!");
  }

  const onCallGenerateTemplateLLM = () => {
    setIsNodeGenerating(true);
    function onSuccessGenerateTemplateLLM(responseData) {
      const { nodes, edges } = responseData?.aiGeneratedContent || {};
      setCurrentEdges(edges);
      setCurrentNodes(nodes);
      setRerenderNodes(true);
      setIsNodeGenerating(false);
    }
    const agentTag = SKILL_TAGS.SHEET_TEMPLATE_SKILL;
    llmInvoker.invoke({
      prompt: "",
      tag: agentTag,
      onSuccess: onSuccessGenerateTemplateLLM,
      onError: onErrorGenerateNode,
      entityType: "skill",
    });
  };

  const onCallGenerateNodesLLM = async ({ updateUserPrompt = null } = {}) => {
    let generationPrompt = textareaRef.current.value;
    if (updateUserPrompt) {
      setIsUpdateNode(true);
      setUpdatingNodeLabel(selectedNode?.data?.label || "");
      generationPrompt = updateUserPrompt;
    }
    setIsNodeGenerating(true);
    const promptNodes = nodes.filter((node) => node.id !== "salesforce");
    const promptEdges = edges.filter((edge) => edge.source !== "salesforce");
    const data = {
      nodes: JSON.stringify(promptNodes),
      edges: JSON.stringify(promptEdges),
      accessTokenConfigId: accessTokenConfigId,
      userPrompt: generationPrompt,
      serviceName: INTEGRATION.SALESFORCE,
      frequentObjects: frequentObjects,
    };
    try {
      const response = await generateNodeOperations(accessToken, data);
      if (response?.status == "success") {
        onSuccessGenerateNode(response.data);
      } else {
        onErrorGenerateNode(response?.data);
      }
    } catch (error) {
      onErrorGenerateNode(error);
    }
    setIsNodeGenerating(false);
  };

  const handleTextareaKeyDown = (e) => {
    if (
      e.key === "Enter" &&
      !e.shiftKey &&
      !isNodeGenerating &&
      textareaRef.current &&
      textareaRef.current.value.trim()
    ) {
      e.preventDefault();
      onCallGenerateNodesLLM();
    }
  };

  const handleTextareaInput = (e) => {
    e.target.style.height = "40px";
    e.target.style.height = `${Math.min(e.target.scrollHeight, 100)}px`;
    e.target.scrollTop = e.target.scrollHeight;
  };

  useEffect(() => {
    if (isNodeGenerating && !isUpdateNode) {
      const executionText = textareaRef.current.value;
      const id = toast.custom(
        () => (
          <EverHotToastMessage
            type="loading"
            description={`Executing: ${executionText}`}
          />
        ),
        { position: "top-center", duration: Number.POSITIVE_INFINITY }
      );
      setLoadingToastId(id);
      textareaRef.current.value = "";
      textareaRef.current.style.height = "40px";
    } else if (loadingToastId) {
      toast.dismiss(loadingToastId);
      setLoadingToastId(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNodeGenerating]);

  useEffect(() => {
    if (isUpdateNode) {
      const id = toast.custom(
        () => (
          <EverHotToastMessage
            type="loading"
            description={`Updating node ${updatingNodeLabel}`}
          />
        ),
        { position: "top-center", duration: Number.POSITIVE_INFINITY }
      );
      setLoadingToastId(id);
    } else if (loadingToastId) {
      toast.dismiss(loadingToastId);
      setLoadingToastId(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isUpdateNode, updatingNodeLabel]);

  useEffect(() => {
    if (rerenderNodes) {
      const layoutedElements = getLayoutedElements(currentNodes, currentEdges);
      setNodes(layoutedElements.nodes || []);
      setEdges(layoutedElements.edges || []);
      setRerenderNodes(false);

      setTimeout(() => {
        reactFlowInstance.current?.fitView();
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rerenderNodes]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  return (
    <div
      className="reactflow-wrapper flex w-full h-full"
      ref={reactFlowWrapper}
    >
      <PreviewDrawer
        viewSobjects={viewSobjects}
        setViewSobjects={setViewSobjects}
        loading={loading}
        objects={objects}
        selectedObject={selectedObject}
        setSelectedObject={setSelectedObject}
        textareaRef={textareaRef}
        handleTextareaInput={handleTextareaInput}
        accessTokenConfigId={accessTokenConfigId}
      />
      <div className="reactflow-wrapper flex flex-col flex-grow w-full h-full">
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            className={twMerge("h-full", selectedNode ? "w-2/3" : "w-full")}
            proOptions={{ account: "paid-pro", hideAttribution: true }}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            onNodeClick={onNodeClick}
            snapToGrid={true}
            fitView
            nodesConnectable={true}
            nodesDraggable={true}
            elementsSelectable={false}
            minZoom={0.1}
            onInit={(instance) => {
              reactFlowInstance.current = instance;
            }}
          >
            <Controls showInteractive={false} />
            <Background variant={BackgroundVariant.Dots} gap={12} />
          </ReactFlow>
        </ReactFlowProvider>

        <div className="flex justify-center items-center gap-2 z-10 py-3">
          {nodes.length === -1 && (
            <MagicWandIcon
              className="h-6 w-6 text-ever-chartColors-12 animate-wiggle cursor-pointer flex-shrink-0"
              onClick={onCallGenerateTemplateLLM}
            />
          )}
          <div className="relative flex items-center">
            <textarea
              ref={textareaRef}
              className="w-[500px] h-[38px] border border-solid border-ever-base-400 rounded p-2 pr-10 resize-none select-none"
              placeholder="Enter your prompt here"
              onInput={handleTextareaInput}
              onKeyDown={handleTextareaKeyDown}
            />
            <XIcon
              className="w-5 h-5 text-ever-base-content-low absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              onClick={() => {
                textareaRef.current.value = "";
                textareaRef.current.style.height = "40px";
              }}
            />
          </div>
          <EverButton
            key="generate"
            onClick={() => {
              if (textareaRef.current && textareaRef.current.value.trim()) {
                onCallGenerateNodesLLM();
              }
            }}
            loading={isNodeGenerating}
            className="h-10 flex-shrink-0"
          >
            Generate
          </EverButton>
        </div>
      </div>

      {selectedNode && (
        <FieldsDrawer
          selectedNode={selectedNode}
          setSelectedNode={setSelectedNode}
          onCallGenerateNodesLLM={onCallGenerateNodesLLM}
          nodes={nodes}
          setNodes={setNodes}
          isNodeGenerating={isNodeGenerating}
        />
      )}
    </div>
  );
};
