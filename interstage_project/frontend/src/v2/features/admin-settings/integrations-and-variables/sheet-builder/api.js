export const generateNodeOperations = async (accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  const response = await fetch(
    "/datasheet_builder/generate_nodes",
    requestOptions
  );
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data?.message || "Try again!");
  }
  return data;
};

export const sheetBuilder = async (accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  const response = await fetch(
    "/datasheet_builder/sheet_builder",
    requestOptions
  );
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "");
  }
  return data;
};
