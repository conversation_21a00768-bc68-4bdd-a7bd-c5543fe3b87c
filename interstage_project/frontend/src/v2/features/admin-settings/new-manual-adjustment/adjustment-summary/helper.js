import { endOfMonth, format, startOfMonth } from "date-fns";

export const ADJUSTMENT_FILTERS = {
  PAYEES: "payees",
  STATEMENT_PERIOD: "statementPeriod",
  COMMISSION_ADJUSTMENT_AMOUNT: "adjustmentAmount",
  COMMISSION_ADJUSTMENT_CURRENCY: "adjustmentCurrency",
  REASON_CATGORY: "reasonCategory",
  ADJUSTMENT_ADDED_BY: "adjustmentAddedBy",
  COMMISSION_PLAN: "commissionPlan",
  COMPONENT: "component",
  LINE_ITEM: "lineItem",
};

export function getUserFilterDefaultFields(t) {
  return [
    {
      displayName: "Payees",
      systemName: ADJUSTMENT_FILTERS.PAYEES,
      fieldType: "Dropdown",
      options: [],
    },
    {
      displayName: "Statement Period",
      systemName: ADJUSTMENT_FILTERS.STATEMENT_PERIOD,
      fieldType: "Dropdown",
      options: [],
    },
    {
      displayName: `${t("COMMISSION")} ${t("ADJUSTMENT")} Amount`,
      systemName: ADJUSTMENT_FILTERS.COMMISSION_ADJUSTMENT_AMOUNT,
      fieldType: "Number",
    },
    {
      displayName: `${t("COMMISSION")} Amount Currency`,
      systemName: ADJUSTMENT_FILTERS.COMMISSION_ADJUSTMENT_CURRENCY,
      fieldType: "Dropdown",
    },
    {
      displayName: "Reason Category",
      systemName: ADJUSTMENT_FILTERS.REASON_CATGORY,
      fieldType: "PickList",
      options: [],
    },
    {
      displayName: `${t("ADJUSTMENT")} Added By`,
      systemName: ADJUSTMENT_FILTERS.ADJUSTMENT_ADDED_BY,
      fieldType: "Dropdown",
      options: [],
    },
    {
      displayName: t("COMMISSION_PLANS"),
      systemName: ADJUSTMENT_FILTERS.COMMISSION_PLAN,
      fieldType: "PickList",
      options: [],
    },
    {
      displayName: "Line Item",
      systemName: ADJUSTMENT_FILTERS.LINE_ITEM,
      fieldType: "Text",
    },
  ];
}

export function getFiltersObject(filters, clientFeatures) {
  if (!filters) {
    return [];
  }

  return [
    {
      ...filters, // Spread the existing filters
      ...(filters.statementPeriod
        ? {
            statementPeriod: {
              type: filters.statementPeriod.type, // Preserve the original type
              value: filters.statementPeriod.value?.map((filterPeriod) => {
                if (filterPeriod && filterPeriod !== "all") {
                  const periodDates = filterPeriod.split(" <-> ");
                  if (clientFeatures?.customCalendar) {
                    const psd = format(new Date(periodDates[0]), "yyyy-MM-dd");
                    const ped = format(new Date(periodDates[1]), "yyyy-MM-dd");
                    return `${psd} <-> ${ped}`;
                  } else {
                    const psd = format(
                      startOfMonth(new Date(periodDates[0])),
                      "yyyy-MM-dd"
                    );
                    const ped = format(
                      endOfMonth(new Date(periodDates[1])),
                      "yyyy-MM-dd"
                    );
                    return `${psd} <-> ${ped}`;
                  }
                }
                return "all";
              }),
            },
          }
        : {}),
    },
  ];
}
