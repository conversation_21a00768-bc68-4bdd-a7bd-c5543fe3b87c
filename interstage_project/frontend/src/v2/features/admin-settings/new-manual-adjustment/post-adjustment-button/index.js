import { EraserIcon, FilterFunnelIcon } from "@everstage/evericons/duotone";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { endOfMonth, format } from "date-fns";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import React, { useState, useEffect, useRef, useMemo } from "react";
import { useForm, Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import {
  adjustmentsUploadValidate,
  adjustmentsUploadImport,
} from "~/Api/ManualAdjustmentService";
import { RBAC_ROLES, SEARCH_BOX } from "~/Enums";
import { myClient<PERSON>tom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverForm,
  EverInput,
  EverSelect,
  EverTg,
  message,
  EverUploadV2,
  useOnClickOutside,
  EverTooltip,
  EverDivider,
} from "~/v2/components";

import AdjustmentModal from "./AdjustmentModal";
import postAdjustmentStore from "./store";
import { getFiltersObject } from "../adjustment-summary/helper";
import {
  getAdjustmentsFieldDefs,
  SAMPLE_TEMPLATE_DATA,
  useGetAdjustmentsInstructions,
} from "../constants";

const adjustmentItems = [{ value: "commission", label: "Commission" }];

const AdjustmentForm = ({
  onFormValidityChange,
  statementPeriods,
  setStatementPeriod,
  statementPeriod,
  bulkMode,
}) => {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      statementPeriod: statementPeriod || "",
    },
  });

  const selectedOption = watch("adjustmentType");
  const selectedDate = watch("statementPeriod");
  const isDateValid = !!selectedDate;
  const isFormValid = selectedOption && isDateValid;

  const { t } = useTranslation();
  const selectedPeriod = watch("statementPeriod");
  useEffect(() => {
    // Update the parent component's state when the selected value changes
    if (selectedPeriod !== statementPeriod) {
      setStatementPeriod(selectedPeriod);
    }
  }, [selectedPeriod, setStatementPeriod, statementPeriod]);

  useEffect(() => {
    if (!bulkMode) {
      setStatementPeriod(null);
      setValue("statementPeriod", ""); // Reset the form's statementPeriod field
    }
  }, [bulkMode, setStatementPeriod, setValue]);

  useEffect(() => {
    onFormValidityChange(isFormValid);
  }, [isFormValid, onFormValidityChange]);

  return (
    <EverForm name="adjustment-info" layout="vertical">
      <EverForm.Item
        label={`${t("ADJUSTMENT")} Type`}
        rules={[{ required: true, message: "This value is required" }]}
        help={errors.option?.message}
        required
      >
        <Controller
          name="adjustmentType"
          control={control}
          defaultValue={`Add ${t("COMMISSION")} ${t("ADJUSTMENT")}`}
          rules={{
            required: "This value is required",
            validate: (value) => value !== null || "This value is required",
          }}
          render={({ field }) => {
            return (
              <EverSelect
                disabled={true}
                value="commission"
                className="w-60"
                options={adjustmentItems}
                placeholder="Select Commission Adjustment"
                {...field}
              />
            );
          }}
        />
      </EverForm.Item>

      <EverForm.Item
        label="Statement Period"
        rules={[{ required: true, message: "This value is required" }]}
        help={errors.date?.message}
        required
      >
        <Controller
          name="statementPeriod"
          control={control}
          rules={{ required: "Please select a period" }}
          defaultValue={statementPeriod}
          render={({ field }) => (
            <EverSelect
              className="w-60"
              onChange={(value) => setValue("statementPeriod", value)}
              options={statementPeriods}
              placeholder="Select Statement Period"
              {...field}
            />
          )}
        />
      </EverForm.Item>
    </EverForm>
  );
};

const PostAdjustmentButton = observer((props) => {
  const {
    onFinish,
    label,
    searchTerm,
    setSearchTerm,
    setDrawSearchTerm,
    activeKey,
    statementPeriods,
    approvalConfig,
    tableFilter,
    reasonCategoryOptions,
    filterPeriod,
    filters,
    parseDate,
    handleOpenFilterBar,
  } = props;
  const store = postAdjustmentStore;
  const [showModal, setShowModal] = useState(false);
  const [adjustmentType, setAdjustmentType] = useState(false);
  const [exportActive, setExportActive] = useState(false);
  const [bulkMode, setBulkMode] = useState(false);
  const [statementPeriod, setStatementPeriod] = useState(null);
  const [exportDropdownOpen, setExportDropdownOpen] = useState(false);
  const { accessToken } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const searchInputRef = useRef(null);
  const dropdownRef = useRef(null);

  const { t } = useTranslation();

  const filtersObj = getFiltersObject(filters, clientFeatures);
  const instructions = useGetAdjustmentsInstructions(reasonCategoryOptions);
  const fieldDefs = getAdjustmentsFieldDefs(reasonCategoryOptions);

  const statementPeriodLabel = statementPeriods?.find(
    (period) => period.value === statementPeriod
  )?.label;

  const { data } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const debounceFn = useMemo(
    () =>
      debounce((value) => {
        setSearchTerm(value);
      }, SEARCH_BOX.DEBOUNCE_TIME),
    [setSearchTerm]
  );

  const debounceWrapper = (value) => {
    const searchVal = value.target.value;
    if (
      searchVal.length === 0 ||
      searchVal.length >= SEARCH_BOX.MINIMUM_CHARS
    ) {
      debounceFn(searchVal);
    }
  };

  useEffect(() => {
    if (accessToken) {
      store.setAccessToken(accessToken);
    }
  }, [accessToken]);

  const menu = (
    <Menu
      onClick={(e) => {
        setAdjustmentType(e.key);
        setShowModal(true);
      }}
    >
      {hasPermissions(RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT) && (
        <Menu.Item key="commission">
          {" "}
          <EverTg.Text className="text-ever-base-content">
            {t("COMMISSION")}
          </EverTg.Text>
        </Menu.Item>
      )}
      {hasPermissions(RBAC_ROLES.MANAGE_DRAWS) && (
        <Menu.Item key="drawRecover">
          <EverTg.Text className="text-ever-base-content">
            Recover Draw
          </EverTg.Text>
        </Menu.Item>
      )}
    </Menu>
  );
  useOnClickOutside(dropdownRef, () => setExportDropdownOpen(false));
  const handleClickExport = () => {
    const loadingToastId = message.loading("Exporting Adjustments...");
    setExportActive(true);

    const requestObj = {
      searchTerm: searchTerm,
      approvalStatus: tableFilter,
      ped:
        filterPeriod && filterPeriod !== "all"
          ? clientFeatures?.customCalendar
            ? format(parseDate(filterPeriod), "yyyy-MM-dd")
            : format(
                endOfMonth(new Date(parseDate(filterPeriod))),
                "yyyy-MM-dd"
              )
          : "all",
      planId: store.planFilter,
      filters: filtersObj,
    };

    store.exportAdjustments(requestObj).then((response) => {
      message.destroy(loadingToastId);
      if (response.success) {
        message.success(response.message);
      } else {
        message.error("Error while exporting adjustments");
      }
      setExportActive(false);
    });
  };

  const handleCloseInput = () => {
    setSearchTerm("");
  };

  return (
    <>
      {activeKey == 0 ? (
        <div className="flex items-center justify-end relative gap-3">
          <EverInput.Search
            ref={searchInputRef}
            placeholder="Search"
            className="w-40"
            onChange={(e) => debounceWrapper(e)}
            size="small"
            onKeyDown={(e) => {
              if (e.key === "Escape") {
                handleCloseInput();
              }
            }}
          />
          {handleOpenFilterBar && (
            <EverTooltip title="Filters">
              <EverButton.Icon
                onClick={handleOpenFilterBar}
                type="ghost"
                color="base"
                size="small"
                icon={
                  <FilterFunnelIcon className="size-4 text-ever-base-content-mid" />
                }
              />
            </EverTooltip>
          )}

          <EverDivider type="vertical" />

          <Dropdown
            className="h-max"
            trigger={["click"]}
            overlay={
              <Menu className="p-1.5">
                <div className="users-menu-div" ref={dropdownRef}>
                  <Menu.Item
                    className="!h-max"
                    onClick={() => {
                      setExportDropdownOpen(!exportDropdownOpen);
                      setBulkMode(true);
                    }}
                  >
                    <div className="py-2 my-1 flex flex-col w-full whitespace-pre-line gap-1">
                      <EverTg.SubHeading4 className="text-ever-base-content">
                        {`Import ${t("ADJUSTMENTS")}`}
                      </EverTg.SubHeading4>
                      <EverTg.Description>
                        {`Add ${t(
                          "ADJUSTMENTS_LOWERCASE"
                        )} to Everstage in bulk using a CSV file.`}
                      </EverTg.Description>
                    </div>
                  </Menu.Item>
                  <Menu.Item className="!h-max !px-0">
                    <EverButton
                      type="text"
                      color="base"
                      disabled={exportActive ? true : false}
                      onClick={() => {
                        handleClickExport();
                        setExportDropdownOpen(!exportDropdownOpen);
                      }}
                      className={twMerge(
                        "py-2 my-1 justify-start !h-auto [&>div]:items-start hover:bg-ever-base-100 [&>div]:text-left [&>div]:gap-1 [&>div]:flex-col w-full whitespace-pre-line"
                      )}
                    >
                      <EverTg.SubHeading4 className="text-ever-base-content">
                        {`Export ${t("ADJUSTMENTS")}`}
                      </EverTg.SubHeading4>
                      <EverTg.Description className="font-normal">
                        {`You can export ${t(
                          "ADJUSTMENTS_LOWERCASE"
                        )} in a CSV file.`}
                      </EverTg.Description>
                    </EverButton>
                  </Menu.Item>
                </div>
              </Menu>
            }
            overlayStyle={{ width: "300px" }}
            placement="bottomLeft"
            visible={exportDropdownOpen}
          >
            <EverButton
              className="flex justify-between items-center"
              color="base"
              type="ghost"
              appendIcon={<ChevronDownIcon />}
              size="small"
              onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
            >
              Import / Export
            </EverButton>
          </Dropdown>
          <EverUploadV2
            title="Bulk Import"
            instructions={instructions}
            fieldDefs={fieldDefs}
            templateData={SAMPLE_TEMPLATE_DATA}
            validateDataService={adjustmentsUploadValidate}
            importDataService={adjustmentsUploadImport}
            isVisible={bulkMode}
            statementPeriodLabel={statementPeriodLabel}
            statementPeriod={statementPeriod}
            handleCloseUpload={() => setBulkMode(false)}
            approvalConfig={approvalConfig}
            ExtraContentStep1={(props) => (
              <AdjustmentForm
                {...props}
                bulkMode={bulkMode}
                statementPeriods={statementPeriods}
                setStatementPeriod={setStatementPeriod}
                statementPeriod={statementPeriod}
              />
            )}
          />
          <Dropdown trigger="click" overlay={menu}>
            <EverButton
              type="filled"
              color="primary"
              size="small"
              prependIcon={<EraserIcon />}
            >
              {label}
            </EverButton>
          </Dropdown>
        </div>
      ) : null}
      {activeKey == 1 && (
        <div className="flex grow justify-end gap-3">
          <EverInput.Search
            placeholder="Search"
            className="w-44"
            onChange={(event) => setDrawSearchTerm(event.target.value)}
            size="small"
          />
          <Dropdown trigger="click" overlay={menu}>
            <EverButton
              type="ghost"
              color="base"
              size="small"
              prependIcon={
                <EraserIcon className="text-ever-base-content-mid" />
              }
            >
              {label}
            </EverButton>
          </Dropdown>
        </div>
      )}

      <AdjustmentModal
        showModal={showModal}
        setShowModal={setShowModal}
        adjustmentType={adjustmentType}
        store={store}
        onFinish={onFinish}
        approvalConfig={data?.data}
      />
    </>
  );
});

export default PostAdjustmentButton;
