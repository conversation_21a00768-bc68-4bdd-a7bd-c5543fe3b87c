export const getUserRole = async (userRoleId, accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await fetch(
      `/spm/permission/role_details?role_permission_id=${userRoleId}`,
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getAllUserRoles = async (accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await fetch(
      "/spm/permission/all_role_details",
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
    /*
                            userRoles => [{ displayName : String, rolePermissionId: String  }]
                          */
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getUserRolePermissions = async (
  userRoleId,
  accessToken,
  abortControllerSignal = null
) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;

  try {
    const response = await fetch(
      `/spm/permission/role_permission_details?role_permission_id=${userRoleId}`,
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const createUserRole = async (displayName, description, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({ displayName, description }),
  };

  try {
    const response = await fetch("/spm/permission/create", requestOptions);
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateUserRole = async (
  userRoleId,
  userRoleRequest,
  accessToken
) => {
  const requestOptions = {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      rolePermissionId: userRoleId,
      ...userRoleRequest,
    }),
  };

  try {
    const response = await fetch("/spm/permission/edit", requestOptions);
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const cloneUserRole = async (userRoleId, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({ rolePermissionId: userRoleId }),
  };

  try {
    const response = await fetch("/spm/permission/clone", requestOptions);
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const deleteUserRole = async (
  { userRoleId, newUserRoleId },
  accessToken
) => {
  const urlEndPath = newUserRoleId
    ? "move_and_invalidate_role"
    : "invalidate_role";
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(
      newUserRoleId
        ? { rolePermissionId: userRoleId, newRolePermissionId: newUserRoleId }
        : { rolePermissionId: userRoleId }
    ),
  };

  try {
    const response = await fetch(
      `/spm/permission/${urlEndPath}`,
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
