import { useEffect, useState, useCallback } from "react";
import { useQueryClient } from "react-query";
import { v4 as uuidv4 } from "uuid";

import { EverButton, toast, EverHotToastMessage } from "~/v2/components";

import { useAgentExecutorApi } from "./restapi";

export function Approval({
  lastMessage,
  sessionId,
  handleShowApproval,
  setApprovalHide,
}) {
  const queryClient = useQueryClient();
  const agentExecutorApi = useAgentExecutorApi();
  const [option, setOption] = useState(null);

  useEffect(() => {
    handleShowApproval(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSelect = useCallback(async () => {
    handleShowApproval(false);
    setApprovalHide(true);
    const newMessage = {
      ...lastMessage,
      approvalStatus: option,
      approvalMessageId: lastMessage.messageId,
      messageId: uuidv4(),
      messageContent: {
        text: option === "APPROVED" ? "Approved" : "Rejected",
      },
      internalTrackingMessage: true,
    };

    queryClient.fetchQuery({
      queryKey: ["approveMessage"],
      queryFn: () =>
        agentExecutorApi.sendMessage(newMessage, sessionId, false, lastMessage),
    });

    toast.custom(() => (
      <EverHotToastMessage
        type="success"
        description={"Approval request sent successfully"}
      />
    ));
  }, [
    handleShowApproval,
    setApprovalHide,
    lastMessage,
    option,
    queryClient,
    agentExecutorApi,
    sessionId,
  ]);

  useEffect(() => {
    if (!option) return;
    onSelect();
  }, [option, onSelect]);

  return (
    <div className="flex gap-3 w-full justify-center items-center h-15 py-10">
      <EverButton
        size="medium"
        key="cancel"
        color="base"
        onClick={() => setOption("REJECTED")}
      >
        Reject
      </EverButton>
      <EverButton
        size="medium"
        key="approve"
        onClick={() => setOption("APPROVED")}
      >
        Approve
      </EverButton>
    </div>
  );
}
