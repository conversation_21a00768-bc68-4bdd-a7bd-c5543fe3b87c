// Importing necessary libraries and components

import { debounce } from "lodash";
import { useEffect } from "react";

import { ChatInput } from "./ChatBotAccessories/ChatInput";
import { MessageList } from "./ChatBotAccessories/MessageList";
import { MessagesHeader } from "./ChatBotAccessories/MessagesHeader";

// Main ChatBot component
export const ChatBot = ({
  chatProperties,
  agentDetails,
  handleSend,
  handleSetDraftInput,
  handleActiveSession,
  handleSetOffsetValue,
  handleSetLoadingOffset,
  handleDeleteSessionModal,
  handleSetActiveSelectedSession,
  handleShowApproval,
  handleActiveMessageId,
  handleActiveSessionId,
  fetchChainOfThought,
  refetchAllMessages,
  handleChainOfThoughtLoading,
}) => {
  // Effect to fetch more messages using offset when scrolling up
  useEffect(() => {
    const debouncedFetch = debounce(() => {
      if (
        chatProperties.offsetValue !== 0 &&
        chatProperties.loadingOffset === true
      ) {
        refetchAllMessages();
      }
    }, 2000);

    debouncedFetch();

    return () => {
      // Cleanup the debounce function if necessary
      debouncedFetch.cancel && debouncedFetch.cancel();
    };
  }, [chatProperties.offsetValue]);

  return (
    <div className="h-full w-full flex flex-col justify-between">
      {/* Chat Messages Section */}
      <div className="flex relative flex-col w-full px-2 gap-2 overflow-auto h-full">
        {/* Rendered Chat Messages */}
        {agentDetails.length > 0 && (
          <MessagesHeader agentDetails={agentDetails} />
        )}
        <MessageList
          chatProperties={chatProperties}
          handleSetLoadingOffset={handleSetLoadingOffset}
          handleSetOffsetValue={handleSetOffsetValue}
          handleActiveSession={handleActiveSession}
          handleDeleteSessionModal={handleDeleteSessionModal}
          handleSetActiveSelectedSession={handleSetActiveSelectedSession}
          threadView={false}
          handleShowApproval={handleShowApproval}
          handleActiveMessageId={handleActiveMessageId}
          handleActiveSessionId={handleActiveSessionId}
          fetchChainOfThought={fetchChainOfThought}
          handleChainOfThoughtLoading={handleChainOfThoughtLoading}
        />
      </div>
      {/* User Input Section */}
      {chatProperties?.agents !== null && chatProperties?.skills !== null && (
        <ChatInput
          handleSend={handleSend}
          setDraft={handleSetDraftInput}
          draft={chatProperties.draftInput}
          sessionId={null}
          chatProperties={chatProperties}
        />
      )}
    </div>
  );
};
