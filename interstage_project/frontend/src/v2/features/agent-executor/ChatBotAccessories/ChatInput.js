import { FlashIcon } from "@everstage/evericons/duotone";
import {
  SendIcon,
  UploadIcon,
  UsersIcon,
  XCloseIcon,
} from "@everstage/evericons/outlined";
import { useEffect, useRef, useState } from "react";
import { useFilePicker } from "use-file-picker";

import { EverButton, EverTg } from "~/v2/components";

import styles from "../styles.module.scss";

const clearMention = (text) => text.replace(/@\S*\s?/, "").trim();

export const ChatInput = ({
  draft,
  setDraft,
  handleSend,
  sessionId,
  chatProperties,
}) => {
  const textAreaRef = useRef(null);
  const MAX_HEIGHT = 200; // Maximum height in pixels

  // State for dropdown and keyboard navigation
  const [showAgents, setShowAgents] = useState(false);
  const [showSkills, setShowSkills] = useState(false);
  // Use this state to track which dropdown option is highlighted via arrow keys.
  const [highlightedIndex, setHighlightedIndex] = useState(0);

  const [dragActive, setDragActive] = useState(false);
  const handleResize = () => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = "auto";
      const newHeight = Math.min(textAreaRef.current.scrollHeight, MAX_HEIGHT);
      textAreaRef.current.style.height = `${newHeight}px`;
      textAreaRef.current.style.overflowY =
        textAreaRef.current.scrollHeight > MAX_HEIGHT ? "auto" : "hidden";
    }
  };

  const acceptedFileTypes = [
    ".doc",
    ".docx",
    ".json",
    ".md",
    ".txt",
    ".csv",
    ".jpg",
    ".png",
  ];

  const { openFilePicker } = useFilePicker({
    readAs: "DataURL",
    accept: acceptedFileTypes.join(","),
    multiple: true,
    onFilesRejected: ({ errors }) => {
      console.log("onFilesRejected", errors);
    },
    onFilesSuccessfullySelected: ({ filesContent }) => {
      const updatedFiles = filesContent.map(
        ({ name, content, lastModified }) => ({
          name,
          content,
          lastModified,
        })
      );
      setDraft({
        ...draft,
        fileUrls: [...draft.fileUrls, ...updatedFiles],
      });
    },
  });

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const filesArray = [...e.dataTransfer.files];
      const acceptedFiles = filesArray.filter((file) => {
        const fileExtension = `.${file.name.split(".").pop().toLowerCase()}`;
        return acceptedFileTypes.includes(fileExtension);
      });

      // Read files using FileReader
      const readerPromises = acceptedFiles.map(
        (file) =>
          new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.addEventListener("load", () => {
              resolve({
                name: file.name,
                content: reader.result,
                lastModified: file.lastModified,
              });
            });
            reader.addEventListener("error", () => {
              reject(reader.error);
            });
            reader.readAsDataURL(file);
          })
      );

      Promise.all(readerPromises)
        .then((filesContent) => {
          setDraft({
            ...draft,
            fileUrls: [...draft.fileUrls, ...filesContent],
          });
        })
        .catch((error) => {
          console.error("Error reading files:", error);
        });
    }
  };

  useEffect(() => {
    handleResize();
  }, [draft]);

  const removeFile = (index) => {
    setDraft({
      ...draft,
      fileUrls: draft.fileUrls.filter((_, i) => i !== index),
    });
  };

  // Determine the list of dropdown options based on which trigger is active.
  const dropdownOptions = showAgents
    ? chatProperties?.agents?.entity || []
    : showSkills
    ? chatProperties?.skills?.entity || []
    : [];

  return (
    <div
      className="group relative flex w-full items-center px-5 pb-5"
      onDragEnter={handleDrag}
      onDragOver={handleDrag}
      onDragLeave={handleDrag}
      onDrop={handleDrop}
    >
      <div
        className={`flex w-full cursor-text flex-col rounded-lg border border-solid border-ever-base-500 ${
          dragActive ? "bg-ever-base-300" : "bg-ever-base"
        }`}
      >
        <div className="flex flex-col">
          {(showAgents || showSkills) && dropdownOptions.length > 0 && (
            <div className="absolute">
              <div className="relative ml-[20%]">
                <div className="absolute bottom-full left-0 w-[170px] bg-ever-base border border-solid border-ever-base-300 rounded-md shadow-xl z-10 p-2 mb-2">
                  <div className="pt-[7px]">
                    {dropdownOptions
                      .filter((option) => !option.technical)
                      .filter((option) => {
                        const textWithName = draft.text;

                        if (textWithName.startsWith("@@")) {
                          return option.name
                            .toLowerCase()
                            .includes(
                              textWithName.split(" ")[0].slice(2).toLowerCase()
                            );
                        } else if (textWithName.startsWith("@")) {
                          return option.name
                            .toLowerCase()
                            .includes(
                              textWithName.split(" ")[0].slice(1).toLowerCase()
                            );
                        } else {
                          return option.name
                            .toLowerCase()
                            .includes(textWithName.toLowerCase());
                        }
                      })
                      .map((option, index) => (
                        <div
                          key={index}
                          onClick={(e) => {
                            e.preventDefault();
                            if (showAgents) {
                              setDraft({
                                ...draft,
                                text: clearMention(draft.text),
                                mention: {
                                  ...option,
                                  entity: "agent",
                                  mentionStr: `@${option.name}`,
                                },
                              });
                              setShowAgents(false);
                            } else if (showSkills) {
                              setDraft({
                                ...draft,
                                text: clearMention(draft.text),
                                mention: {
                                  ...option,
                                  entity: "skill",
                                  mentionStr: `@@${option.name}`,
                                },
                              });
                              setShowSkills(false);
                            }
                          }}
                          onMouseEnter={() => setHighlightedIndex(index)}
                          className={`cursor-pointer hover:bg-ever-base-300 py-2.5 pl-4 pr-3 ${
                            highlightedIndex === index
                              ? "bg-ever-primary-ring"
                              : ""
                          }`}
                        >
                          <EverTg.Text className="text-sm font-normal text-ever-base-content">
                            {option.name}
                          </EverTg.Text>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}
          <div
            className={`flex w-full overflow-x-auto ${styles.scrollBarThin}`}
          >
            {draft.fileUrls?.length > 0 && (
              <div className="flex gap-2 p-2">
                {draft.fileUrls.map((file, index) => (
                  <div
                    key={index}
                    className="px-1 relative flex items-center justify-between border rounded bg-ever-base-100 gap-1.5"
                  >
                    <div className="flex truncate max-w-[150px]">
                      <EverTg.Caption>{file.name}</EverTg.Caption>
                    </div>
                    <div className="flex border rounded-xl bg-ever-base-300 hover:bg-ever-base-400">
                      <XCloseIcon
                        className="w-3 h-3 cursor-pointer"
                        onClick={() => removeFile(index)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="flex w-full items-center px-2">
            <EverTg.SubHeading3 className="flex flex-col w-full">
              {draft.mention ? (
                <div
                  className={`flex border rounded-lg mx-1 my-2 p-0.5 px-1 items-center justify-center w-fit whitespace-nowrap h-fit ${
                    draft.mention.entity == "agent"
                      ? "bg-ever-chartColors-10 border-ever-chartColors-10"
                      : "bg-ever-chartColors-17 border-ever-chartColors-17"
                  }`}
                >
                  <EverTg.Text className="text-ever-primary-content flex gap-1 justify-center items-center">
                    {draft.mention.mentionStr}
                    {draft.mention.mentionStr.startsWith("@@") ? (
                      <FlashIcon className="w-4 h-4 text-ever-base" />
                    ) : (
                      <UsersIcon className="w-4 h-4 text-ever-base" />
                    )}
                  </EverTg.Text>
                </div>
              ) : (
                <></>
              )}
              <textarea
                ref={textAreaRef}
                value={draft.text}
                onChange={(e) => {
                  const value = e.target.value;
                  // Trigger dropdown when the input is exactly "@" or "@@"
                  if (value.startsWith("@@")) {
                    setShowAgents(false);
                    setShowSkills(true);
                    setDraft({ ...draft, mention: null });
                    setHighlightedIndex(0);
                  } else if (value.startsWith("@")) {
                    setShowAgents(true);
                    setShowSkills(false);
                    setDraft({ ...draft, mention: null });
                    setHighlightedIndex(0);
                  } else {
                    setShowAgents(false);
                    setShowSkills(false);
                  }
                  setDraft({ ...draft, text: value });
                  handleResize();
                }}
                onKeyDown={(e) => {
                  // If a dropdown is open, intercept arrow keys and Enter.
                  if (
                    (showAgents || showSkills) &&
                    dropdownOptions.length > 0
                  ) {
                    if (e.key === "ArrowDown") {
                      e.preventDefault();
                      setHighlightedIndex(
                        (prev) => (prev + 1) % dropdownOptions.length
                      );
                      return;
                    } else if (e.key === "ArrowUp") {
                      e.preventDefault();
                      setHighlightedIndex(
                        (prev) =>
                          (prev - 1 + dropdownOptions.length) %
                          dropdownOptions.length
                      );
                      return;
                    } else if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      const filteredOptions = dropdownOptions
                        .filter((option) => !option.technical)
                        .filter((option) => {
                          const textWithName = draft.text;

                          if (textWithName.startsWith("@@")) {
                            return option.name
                              .toLowerCase()
                              .includes(
                                textWithName
                                  .split(" ")[0]
                                  .slice(2)
                                  .toLowerCase()
                              );
                          } else if (textWithName.startsWith("@")) {
                            return option.name
                              .toLowerCase()
                              .includes(
                                textWithName
                                  .split(" ")[0]
                                  .slice(1)
                                  .toLowerCase()
                              );
                          } else {
                            return option.name
                              .toLowerCase()
                              .includes(textWithName.toLowerCase());
                          }
                        });
                      const option = filteredOptions[highlightedIndex];
                      const newOptions = {
                        ...option,
                        entity: showAgents ? "agent" : "skill",
                      };
                      if (showAgents) {
                        setDraft({
                          ...draft,
                          text: clearMention(draft.text),
                          mention: {
                            ...newOptions,
                            mentionStr: `@${option.name}`,
                          },
                        });
                        setShowAgents(false);
                      } else if (showSkills) {
                        setDraft({
                          ...draft,
                          text: clearMention(draft.text),
                          mention: {
                            ...newOptions,
                            mentionStr: `@@${option.name}`,
                          },
                        });
                        setShowSkills(false);
                      }
                      return;
                    }
                  }
                  if (
                    e.target.selectionStart === 0 &&
                    e.key === "Backspace" &&
                    draft.mention !== null
                  ) {
                    e.preventDefault();
                    setDraft({
                      ...draft,
                      mention: null,
                    });
                  }
                  // If no dropdown is active, Enter sends the message.
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    sessionId ? handleSend(sessionId) : handleSend();
                  }
                }}
                placeholder={
                  dragActive ? "Drop files here" : "Message team-agents"
                }
                className="block w-full outline-none resize-none border-0 bg-transparent px-1 py-2 max-h-[200px] min-h-[40px] overflow-y-auto"
                rows={1}
              />
            </EverTg.SubHeading3>
          </div>
        </div>
        <div className="flex justify-between w-full">
          <EverButton
            type="text"
            icon={<UploadIcon className="w-5 h-5" />}
            onClick={() => openFilePicker()}
            color="base"
          />
          <EverButton
            type="text"
            icon={<SendIcon className="w-5 h-5" />}
            onClick={(e) => {
              e.preventDefault();
              sessionId ? handleSend(sessionId) : handleSend();
            }}
            color="base"
          />
        </div>
      </div>
    </div>
  );
};
