import { FlashIcon } from "@everstage/evericons/duotone";
import { FileIcon, UsersIcon } from "@everstage/evericons/outlined";
import { Image } from "antd";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

import { EverTg } from "~/v2/components";
import { ExpressionBoxController } from "~/v2/components/expression-designer";

import { SYSTEM } from "../constants";
import styles from "../styles.module.scss";

const extractUrl = (text) => {
  if (text && typeof text === "string") {
    const urlRegex = /(https?:\/\/\S+)/g;
    const urls = text.match(urlRegex);
    return urls ? urls[0] : null;
  }
  return null;
};

const IframeLink = ({ url }) => {
  return (
    <div>
      <p>Here is the chart:</p>
      <iframe
        width="400"
        height="400"
        seamless
        src={url}
        className="max-h-[400px]"
      ></iframe>
    </div>
  );
};

const formatText = (text) => {
  if (typeof text === "string") {
    try {
      text = JSON.parse(text);
    } catch {
      return (
        <ReactMarkdown
          className={`whitespace-pre-line w-full text-wrap !text-base ${styles.markdown}`}
          remarkPlugins={[remarkGfm]}
        >
          {text}
        </ReactMarkdown>
      );
    }
  }

  // Intermediate steps where the json text being a string is converted to an object
  // console.log(text, typeof text);

  if (typeof text === "object") {
    if (Object.prototype.hasOwnProperty.call(text, "GENERAL_EXPRESSION")) {
      return (
        <ExpressionBoxController
          context="criteria"
          criteriaContext={null}
          databookId="SOME ID"
          datasheetId="SOME ID"
          initialExpression={text.GENERAL_EXPRESSION}
          onChange={() => {}}
          resultType="Integer"
          validateExpression={() => {}}
          className="w-[600px]"
        />
      );
    } else if (Object.prototype.hasOwnProperty.call(text, "IF_STATEMENT")) {
      // return (
      //   <IFStatement
      //     onASTChange={() => {}}
      //     data={text.IF_STATEMENT}
      //     isSummationMode={false}
      //     resetExpression={() => {}}
      //     datasheetId={null}
      //     databookId={null}
      //     commissionPlanId={null}
      //     criteriaContext={null}
      //     showDoNothing={false}
      //     showLearnMoreForFunctions={false}
      //   />
      // );
      return (
        <ReactMarkdown
          className={`whitespace-pre-line w-full text-wrap ${styles.markdown}`}
          remarkPlugins={[remarkGfm]}
        >
          {JSON.stringify(text, null, 4)}
        </ReactMarkdown>
      );
    }
    return (
      <ReactMarkdown
        className={`whitespace-pre-line w-full text-wrap ${styles.markdown}`}
        remarkPlugins={[remarkGfm]}
      >
        {JSON.stringify(text, null, 4)}
      </ReactMarkdown>
    );
  }
  return (
    <ReactMarkdown
      className={`whitespace-pre-line w-full text-wrap ${styles.markdown}`}
      remarkPlugins={[remarkGfm]}
    >
      <pre className="text-wrap overflow-hidden">{text}</pre>
    </ReactMarkdown>
  );
};

export const ChatMessageFormatter = ({ text, files, mention, senderType }) => {
  const url = extractUrl(text);

  return url ? (
    <div className="flex flex-col gap-1 w-full w-max-none">
      {/* <ReactMarkdown
            className={`whitespace-pre-line w-full text-wrap ${styles.markdown}`}
            remarkPlugins={[remarkGfm]}
          > */}
      <div>
        {mention && (
          <div
            className={`float-left flex justify-center items-center border rounded-lg p-0.5 px-1 whitespace-nowrap h-fit gap-1 mr-2 ${
              mention.mentionStr.startsWith("@@")
                ? "bg-ever-chartColors-17 border-ever-chartColors-17"
                : "bg-ever-chartColors-10 border-ever-chartColors-10"
            }`}
          >
            <EverTg.Text className="text-ever-primary-content">
              {mention.mentionStr}
            </EverTg.Text>
            {mention.mentionStr.startsWith("@@") ? (
              <FlashIcon className="w-4 h-4 text-ever-base" />
            ) : (
              <UsersIcon className="w-4 h-4 text-ever-base" />
            )}
          </div>
        )}
        <div className="break-words">{formatText(text)}</div>
        <div className="clear-both"></div>
      </div>
      <div className="flex flex-col gap-4 w-full w-max-none items-center justify-center">
        <IframeLink url={url} />
        {files &&
          files.map((file, index) => {
            const fileurl = file.url;
            const filetype = file.type;
            const fullUrl = fileurl.split("?")[0];
            const fileNameWithId = fullUrl.split("/").pop();
            const fileName = fileNameWithId.includes("----")
              ? fileNameWithId.split("----")[1]
              : fileNameWithId;
            return filetype === "image" && senderType === SYSTEM ? (
              <Image
                className="w-full"
                src={fileurl}
                key={index}
                preview={false}
              />
            ) : (
              <FileDownloader
                fileName={fileName}
                fileUrl={fileurl}
                key={index}
                senderType={senderType}
              />
            );
          })}
      </div>
    </div>
  ) : (
    <>
      <div className="flex flex-col gap-1 w-full w-max-none">
        <div className="flex flex-col gap-4 w-full w-max-none justify-center">
          <div>
            {mention && (
              <div
                className={`float-left flex justify-center items-center border rounded-lg p-0.5 px-1 whitespace-nowrap h-fit gap-1 mr-2 ${
                  mention.mentionStr.startsWith("@@")
                    ? "bg-ever-chartColors-17 border-ever-chartColors-17"
                    : "bg-ever-chartColors-10 border-ever-chartColors-10"
                }`}
              >
                <EverTg.Text className="text-ever-primary-content">
                  {mention.mentionStr}
                </EverTg.Text>
                {mention.mentionStr.startsWith("@@") ? (
                  <FlashIcon className="w-4 h-4 text-ever-base" />
                ) : (
                  <UsersIcon className="w-4 h-4 text-ever-base" />
                )}
              </div>
            )}
            <div className="break-words">{formatText(text)}</div>
            <div className="clear-both"></div>
          </div>
          {files &&
            files.map((file, index) => {
              const fileurl = file.url;
              const filetype = file.type;
              const fullUrl = fileurl.split("?")[0];
              const fileNameWithId = fullUrl.split("/").pop();
              const fileName = fileNameWithId.includes("----")
                ? fileNameWithId.split("----")[1]
                : fileNameWithId;
              return filetype === "image" && senderType === SYSTEM ? (
                <Image
                  className="w-full"
                  src={fileurl}
                  key={index}
                  preview={false}
                />
              ) : (
                <FileDownloader
                  fileName={fileName}
                  fileUrl={fileurl}
                  key={index}
                  senderType={senderType}
                />
              );
            })}
        </div>
      </div>
    </>
    // </ReactMarkdown>
  );
};

const FileDownloader = ({ fileName, fileUrl, senderType }) => {
  return (
    <div className="flex p-0 items-center border border-ever-base-400 rounded-lg shadow-sm w-1/2 bg-ever-base-50 text-nowrap text-ellipsis whitespace-nowrap overflow-hidden">
      <div className="bg-ever-base-300 p-3">
        <FileIcon className="w-6 h-6" />
      </div>

      <div className="ml-4">
        <EverTg.Caption.Medium>{fileName}</EverTg.Caption.Medium>
        <br />
        {senderType === SYSTEM && (
          <a href={fileUrl} download={fileName}>
            <EverTg.Caption>Download</EverTg.Caption>
          </a>
        )}
      </div>
    </div>
  );
};
