import { format } from "date-fns";
import { useMemo, useState, useRef } from "react";

import { EverLoader, EverTg } from "~/v2/components";

import { Message } from "./Messages";
import { formatDate } from "./Sessions";
import { Approval } from "../Approval";
import { HUMAN, SYSTEM } from "../constants";
import styles from "../styles.module.scss";

export const MessageList = ({
  chatProperties,
  handleSetOffsetValue,
  handleSetLoadingOffset,
  handleActiveSession,
  handleDeleteSessionModal,
  handleSetActiveSelectedSession,
  threadView,
  handleShowApproval,
  handleActiveMessageId,
  handleActiveSessionId,
  fetchChainOfThought,
  handleChainOfThoughtLoading,
}) => {
  const observerRef = useRef(null);
  const [approvalHide, setApprovalHide] = useState(false);
  const reversedMessages = useMemo(() => {
    if (threadView) {
      return chatProperties.activeThreadMessages
        ? [...chatProperties.activeThreadMessages].reverse()
        : [];
    } else {
      return Object.keys(chatProperties.sessionData).map(
        (key) => chatProperties.sessionData[key].messageContent
      );
    }
  }, [
    threadView,
    chatProperties.activeThreadMessages,
    chatProperties.sessionData,
  ]);

  const groupedMessages = useMemo(() => {
    const groups = {};
    for (const message of reversedMessages) {
      const date = new Date(message?.sentTime || Date.now());
      const dateKey = format(date, "yyyy-MM-dd");
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      // Both conditions currently perform the same action; adjust as needed.
      if (threadView) {
        groups[dateKey].unshift(message);
      } else {
        groups[dateKey].unshift(message);
      }
    }

    // Sort the keys of groups in descending order and create the final object
    const sortedKeys = Object.keys(groups).sort((a, b) => b.localeCompare(a));
    const sortedGroups = {};
    for (const key of sortedKeys) {
      sortedGroups[key] = groups[key];
    }
    return sortedGroups;
  }, [reversedMessages, threadView]);

  const mapSenderToAvatar = (sessionId, sender, senderType) => {
    const senderObj = chatProperties.sessionData[
      sessionId
    ]?.usersInThread?.users
      ?.filter((user) => user.senderId === sender)
      .at(0);

    const systemUser = chatProperties.sessionData[
      sessionId
    ]?.usersInThread?.users
      ?.filter((user) => user.type === SYSTEM)
      .at(0);

    return (
      senderObj ||
      (senderType === HUMAN && chatProperties.currentUser) || {
        ...systemUser,
        name: "Agent",
      }
    );
  };

  const latestMessageId = useMemo(() => {
    if (Object.keys(groupedMessages)?.length === 0) {
      return null;
    } else {
      const dates = Object.keys(groupedMessages);
      dates.sort();
      const lastDate = dates.at(-1);
      const messages = groupedMessages[lastDate];
      return messages.at(-1).messageId;
    }
  }, [groupedMessages]);

  const firstMessageId = useMemo(() => {
    if (Object.keys(groupedMessages)?.length === 0) {
      return null;
    } else {
      const dates = Object.keys(groupedMessages);
      dates.sort();
      const firstDate = dates[0];
      const messages = groupedMessages[firstDate];
      return messages[0].messageId;
    }
  }, [groupedMessages]);

  return Object.keys(chatProperties.sessionData)?.length > 0 ? (
    <div
      className={`flex ${
        threadView ? "flex-col-reverse" : "flex-col-reverse"
      } pb-4 overflow-y-auto h-full ${styles.messagesScrollbar} ${
        styles.hideScrollBar
      }`}
    >
      {Object.entries(groupedMessages).map(([dateKey, messages]) => (
        <div key={dateKey}>
          {!threadView && (
            <div className="text-center mb-2 mt-1 relative">
              <hr className="absolute top-1/2 left-0 right-0 border-t border-solid border-ever-base-400 shadow-sm" />
              <span className="relative inline-block px-4 py-1 rounded-full text-xs border border-solid border-ever-base-400 border-opacity-40 shadow-md backdrop-blur-md bg-ever-base-25 bg-opacity-50">
                <EverTg.Text className="font-medium">
                  {formatDate(dateKey)}
                </EverTg.Text>
              </span>
            </div>
          )}
          {messages?.map((message) =>
            !message?.internalTrackingMessage &&
            message?.approvalStatus !== "True" ? (
              <div key={message.messageId}>
                <Message
                  key={`message-${message.messageId}`}
                  messageId={message.messageId}
                  firstMessage={message.messageId === firstMessageId}
                  sessionId={message.sessionId}
                  body={message.messageContent.text}
                  files={message.messageContent.file}
                  mention={message.messageContent.mention}
                  sentTime={message?.sentTime}
                  sentUser={
                    mapSenderToAvatar(
                      message.sessionId,
                      message.sender,
                      message.senderType
                    )?.name
                  }
                  senderType={message.senderType}
                  senderAvatar={
                    mapSenderToAvatar(
                      message.sessionId,
                      message.sender,
                      message.senderType
                    )?.avatar
                  }
                  activeSession={chatProperties.activeSession}
                  handleActiveSession={handleActiveSession}
                  threadView={threadView}
                  threadCount={Math.trunc(
                    chatProperties.sessionData[message["sessionId"]]
                      ?.repliesCount || 0
                  )}
                  usersInThread={
                    threadView
                      ? []
                      : chatProperties.sessionData[message["sessionId"]]
                          ?.usersInThread || []
                  }
                  threadTimeStamp={
                    threadView
                      ? ""
                      : chatProperties.sessionData[message["sessionId"]]
                          ?.lastInteractedTime || ""
                  }
                  handleDeleteSessionModal={handleDeleteSessionModal}
                  handleSetActiveSelectedSession={
                    handleSetActiveSelectedSession
                  }
                  handleActiveMessageId={handleActiveMessageId}
                  handleActiveSessionId={handleActiveSessionId}
                  fetchChainOfThought={fetchChainOfThought}
                  handleChainOfThoughtLoading={handleChainOfThoughtLoading}
                />
                {message?.approvalRequired &&
                latestMessageId === message.messageId &&
                !approvalHide ? (
                  <Approval
                    key={`approval-${message.messageId}`}
                    lastMessage={message}
                    sessionId={message.sessionId}
                    handleShowApproval={handleShowApproval}
                    setApprovalHide={setApprovalHide}
                  />
                ) : (
                  <></>
                )}
              </div>
            ) : (
              <></>
            )
          )}
        </div>
      ))}
      {!threadView && (
        <div
          className="flex p-1 w-full display-none"
          ref={(el) => {
            if (el) {
              // Disconnect previous observer if it exists
              if (observerRef.current) {
                observerRef.current.disconnect();
              }

              const observer = new IntersectionObserver(
                (entries) => {
                  // Access the latest chatProperties from the function parameters
                  if (
                    entries.some((entry) => entry.isIntersecting) &&
                    chatProperties?.hasMore &&
                    !chatProperties?.loadingOffset &&
                    Object.keys(chatProperties?.sessionData).length >=
                      (chatProperties?.limitValue || 12)
                  ) {
                    handleSetLoadingOffset(true);
                    handleSetOffsetValue(
                      (chatProperties?.offsetValue || 0) +
                        (chatProperties?.limitValue || 12)
                    );
                  }
                },
                { threshold: 0.5 }
              );

              observerRef.current = observer; // Store the observer in the ref
              observer.observe(el);

              return () => {
                if (observerRef.current) {
                  observerRef.current.disconnect();
                }
              };
            }
          }}
        ></div>
      )}
      {chatProperties.loadingOffset && (
        <EverLoader className="pt-16" size={1} />
      )}
    </div>
  ) : (
    <></>
  );
};
