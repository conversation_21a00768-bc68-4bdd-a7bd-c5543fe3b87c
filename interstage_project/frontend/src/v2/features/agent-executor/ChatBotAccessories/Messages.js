import { StarsAltIcon } from "@everstage/evericons/outlined";
import { format } from "date-fns";

import { EverTg, EverTooltip } from "~/v2/components";
import EverAvatar from "~/v2/legacy/components/EverAvatar";

import { ChatMessageFormatter } from "./ChatMessageFormatter";
import { DropdownInMessage } from "./DropdownInMessage";
import { formatDate } from "./Sessions";
import { ThreadBar } from "./ThreadBar";
import { ThinkBulbAnimation } from "../../llm-invocations/lotties/Lotties";
import { HUMAN } from "../constants";

export const Message = ({
  messageId,
  sessionId,
  firstMessage,
  body,
  files,
  mention,
  sentTime,
  sentUser,
  senderAvatar,
  senderType,
  activeSession,
  handleActiveSession,
  threadView,
  threadCount,
  usersInThread,
  threadTimeStamp,
  handleDeleteSessionModal,
  handleSetActiveSelectedSession,
  handleActiveMessageId,
  handleActiveSessionId,
  fetchChainOfThought,
  handleChainOfThoughtLoading,
}) => {
  return (
    <div
      className={`flex flex-col p-1.5 px-2.5 relative ${"hover:rounded-2xl hover:backdrop-blur-md hover:bg-ever-base-25 hover:bg-opacity-20"}`}
    >
      <div className="flex flex-col items-start">
        <div className="flex gap-2.5 w-full group/parent">
          {senderAvatar || senderType === HUMAN ? (
            <EverAvatar
              src={senderAvatar}
              className="rounded-lg !w-9 !h-9"
              name={sentUser}
            />
          ) : (
            <div className="rounded-md w-7 h-7 flex items-center justify-center border border-solid border-ever-base-300 bg-ever-base-300">
              <StarsAltIcon className="w-4 h-4 text-ever-chartColors-7" />
            </div>
          )}
          <div className="flex w-full justify-between">
            <div className="flex flex-col w-full overflow-hidden">
              <div className="flex gap-2.5">
                <EverTg.Heading3 className="flex items-center justify-center !text-base">
                  {sentUser.at(0).toUpperCase() + sentUser.slice(1)}
                </EverTg.Heading3>
                <EverTooltip title={formatDate(sentTime, true)}>
                  <div className="flex items-center justify-center opacity-60 group-hover/parent:opacity-100 hover:underline hover:cursor-pointer">
                    <EverTg.Caption>
                      {threadView
                        ? formatDate(sentTime, false, true)
                        : format(new Date(sentTime), "hh:mm")}
                    </EverTg.Caption>
                  </div>
                </EverTooltip>
                {senderType === HUMAN && (
                  <div
                    onClick={() => {
                      handleChainOfThoughtLoading(true);
                      handleActiveMessageId(messageId);
                      handleActiveSessionId(sessionId);
                      setTimeout(() => {
                        fetchChainOfThought();
                        setTimeout(() => {
                          handleChainOfThoughtLoading(false);
                        }, 500);
                      }, 500);
                    }}
                    className="flex items-center justify-center opacity-0 group-hover/parent:opacity-100 hover:underline hover:cursor-pointer"
                  >
                    <ThinkBulbAnimation />
                  </div>
                )}
              </div>
              <div className="flex text-wrap w-full">
                {body.toString().trim() ? (
                  <ChatMessageFormatter
                    text={body}
                    files={files}
                    mention={mention}
                    senderType={senderType}
                  />
                ) : (
                  "Sorry, there was an error processing your message. Please try again."
                )}
              </div>
            </div>
            {!threadView && (
              <div className="flex items-center opacity-0 group-hover/parent:opacity-100">
                <DropdownInMessage
                  sessionId={sessionId}
                  handleDeleteSessionModal={handleDeleteSessionModal}
                  handleSetActiveSelectedSession={
                    handleSetActiveSelectedSession
                  }
                />
              </div>
            )}
          </div>
        </div>
        {threadCount > 0 && !threadView && (
          <ThreadBar
            count={threadCount}
            images={usersInThread}
            threadTimeStamp={threadTimeStamp}
            activeSession={activeSession}
            handleActiveSession={handleActiveSession}
            sessionId={sessionId}
          />
        )}
      </div>
      <div>
        {firstMessage && threadView && (
          <div className="text-left mb-1 mt-1 relative">
            <hr className="absolute top-1/2 left-0 right-0 border-t border-solid border-ever-base-400 shadow-sm" />
            <span className={`relative inline-block pr-2 py-1 bg-ever-base`}>
              <EverTg.Caption.Thick>
                {threadCount} {threadCount === 1 ? "reply" : "replies"}
              </EverTg.Caption.Thick>
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
