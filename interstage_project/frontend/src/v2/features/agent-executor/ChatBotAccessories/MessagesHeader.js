import { Popover } from "antd";
import { useState } from "react";

// import { EverInteractiveChip, EverTg } from "~/v2/components";
import { EverInteractiveChip, EverTg } from "~/v2/components";
import EverAvatar from "~/v2/legacy/components/EverAvatar";

export const MessagesHeader = (agentDetails) => {
  return (
    <div className="flex w-full justify-between shadow-sm px-3 py-1 border-b border-solid border-ever-base-400 shadow-sm">
      <div className="flex flex-row items-center">
        <EverTg.SubHeading1 className="text-ever-content font-medium text-lg">
          # team-agents
        </EverTg.SubHeading1>
      </div>
      <div className="flex items-center">
        <EverTg.SubHeading1 className="text-ever-content font-medium text-lg">
          <AvatarGroup agents={agentDetails} />
        </EverTg.SubHeading1>
      </div>
    </div>
  );
};

const AvatarGroup = ({ agents }) => {
  const [visiblePopoverIndex, setVisiblePopoverIndex] = useState(null);

  const togglePopover = (index) => {
    setVisiblePopoverIndex(visiblePopoverIndex === index ? null : index);
  };

  const renderPopoverContent = (agent) => (
    <div className="flex gap-2 flex-col w-64 !p-0">
      {agent.avatarPath ? (
        <div className="w-full">
          <EverAvatar
            isImage
            src={agent.avatarPath}
            className="w-full h-64 rounded-sm"
          />
        </div>
      ) : (
        <></>
      )}
      <div className="flex flex-col gap-1 w-full items-center justify-center">
        <EverTg.Heading2>{agent.agentName}</EverTg.Heading2>
        <EverTg.Heading4>{agent.name}</EverTg.Heading4>
        <EverTg.Description className="text-center">
          {agent.description}
        </EverTg.Description>
      </div>
      <div className="flex flex-col gap-1.5 w-full items-center justify-center">
        <EverTg.Heading4>Skills</EverTg.Heading4>
        <div className="flex w-full gap-1.5 pt-0.25 flex-wrap items-center justify-center">
          {agent.skillIds?.map((skill, index) => (
            <EverInteractiveChip
              key={index}
              title={skill.name}
              className={
                "font-medium rounded-xl text-ever-chartColors-20 border-ever-chartColors-20/30 bg-ever-chartColors-41"
              }
            />
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex -space-x-2">
      {agents.agentDetails.map((agent, index) => (
        <Popover
          key={index}
          placement="bottom"
          content={renderPopoverContent(agent)}
          trigger="click"
          visible={visiblePopoverIndex === index}
          onVisibleChange={() => togglePopover(index)}
        >
          <div className="cursor-pointer">
            <span onClick={() => togglePopover(index)}>
              <EverAvatar
                src={agent.avatarPath}
                name={agent.agentName}
                className="rounded-full w-9 h-9"
                width={36}
                height={36}
              />
            </span>
          </div>
        </Popover>
      ))}
    </div>
  );
};
