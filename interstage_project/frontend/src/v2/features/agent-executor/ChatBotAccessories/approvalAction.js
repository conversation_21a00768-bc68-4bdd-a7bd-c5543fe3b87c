import { CheckIcon, XCloseIcon } from "@everstage/evericons/outlined";
import { twMerge } from "tailwind-merge";

import { EverTg } from "~/v2/components";

export const ApproveAction = ({
  message,
  chatProperties,
  sendPrompt,
  handleSetSessionMessages,
}) => {
  function onApproveOrRejectMessage(messsage, approvalStatus) {
    const message = {
      ...chatProperties.sessionMessages?.find(
        (message) => message.messageId === messsage.messageId
      ),
    };
    message.approvalStatus = approvalStatus;
    message.approvalMessageId = messsage.messageId;

    handleSetSessionMessages([
      ...chatProperties.sessionMessages.slice(0, -1),
      message,
    ]);

    sendPrompt(message, [
      ...chatProperties.sessionMessages.slice(0, -1),
      message,
    ]);
  }
  return (
    <div className="flex divide-x divide-ever-base-200 mt-6 border-0 border-t border-solid border-ever-base-200">
      {message?.approvalStatus !== "unresolved" && (
        <div className="w-0 flex-1 flex">
          <div
            className={twMerge(
              "cursor-not-allowed relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm font-medium border-0 rounded-bl-lg",
              message?.approvalStatus === "approved"
                ? "text-ever-primary"
                : "text-ever-error"
            )}
          >
            {message?.approvalStatus === "approved" ? (
              <EverTg.Text>
                <CheckIcon className="w-4 h-4 -mb-1" /> Approved
              </EverTg.Text>
            ) : (
              <EverTg.Text>
                <XCloseIcon className="w-4 h-4 -mb-1" /> Rejected
              </EverTg.Text>
            )}
          </div>
        </div>
      )}
      {message?.approvalStatus === "unresolved" && (
        <>
          <div className="w-0 flex-1 flex">
            <div
              className={twMerge(
                "cursor-pointer relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-ever-primary text-sm font-medium border-0 rounded-bl-lg hover:scale-105 transition-transform duration-200",
                chatProperties.sessionMessages?.slice(-1)[0]?.messageId !==
                  message?.messageId
                  ? "text-ever-base-content-mid text-ever-base-400 cursor-not-allowed"
                  : ""
              )}
              onClick={() => {
                if (
                  chatProperties.sessionMessages.at(-1)?.messageId ===
                  message?.messageId
                ) {
                  onApproveOrRejectMessage(message, "approved");
                }
              }}
            >
              <CheckIcon className="w-4 h-4" />{" "}
              <EverTg.Text>Approve</EverTg.Text>
            </div>
          </div>
          <div className="w-0 flex-1 flex">
            <div
              className={twMerge(
                "relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-ever-error text-sm font-medium border-0 border-l border-solid border-ever-base-200 rounded-bl-lg hover:scale-105 transition-transform duration-200",
                chatProperties.sessionMessages?.slice(-1)[0]?.messageId !==
                  message?.messageId
                  ? "text-ever-base-content-mid text-ever-base-400 cursor-not-allowed"
                  : "cursor-pointer"
              )}
              onClick={() => {
                if (
                  chatProperties.sessionMessages?.slice(-1)[0]?.messageId ===
                  message?.messageId
                ) {
                  onApproveOrRejectMessage(message, "rejected");
                }
              }}
            >
              <XCloseIcon className="w-4 h-4" />{" "}
              <EverTg.Text>Reject</EverTg.Text>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
