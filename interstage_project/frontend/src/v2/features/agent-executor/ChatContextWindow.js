import { EverTg, EverLoader } from "~/v2/components";
import { addNewField } from "~/v2/images";

import { ChatBot } from "./ChatBot";
import { Kanban } from "./Kanban";
import { ChainOfThought } from "./ThoughtChain";
import Threads from "./Threads";

export const ChatContextWindow = (props) => {
  if (props.allMessagesLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <EverLoader />
      </div>
    );
  }

  return props?.agentDetails.length === 0 ? (
    <div className="w-full h-full transform -translate-y-10 flex flex-col items-center justify-center text-center min-h-full gap-10">
      <img src={addNewField} />

      <div className="w-[1400px] flex flex-col gap-3 text-ever-base-content">
        <EverTg.Heading3> No Support Agents Available.</EverTg.Heading3>
        <EverTg.Description>
          Admins are setting up agents for assistance.
        </EverTg.Description>
      </div>
    </div>
  ) : (
    <div className={`flex h-full w-full bg-ever-base`}>
      <div className="w-[50%]">
        {props.allMessagesLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <EverLoader />
          </div>
        ) : props.chatProperties.activeSession &&
          props.chatProperties?.activeThreadMessages?.length > 1 ? (
          <Threads {...props} />
        ) : (
          <ChatBot {...props} />
        )}
      </div>

      <div className="flex flex-col w-[50%] h-full border-0 border-l border-solid border-ever-base-400">
        {props.chatProperties.chainOfThoughtLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <EverLoader />
          </div>
        ) : props.chatProperties.thoughtActive ? (
          <ChainOfThought {...props} />
        ) : (
          <Kanban {...props} />
        )}
      </div>
    </div>
  );
};
