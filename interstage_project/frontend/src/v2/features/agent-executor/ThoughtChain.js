import { CheckCircleIcon } from "@everstage/evericons/duotone";
import { XCloseIcon } from "@everstage/evericons/outlined";
import { useState } from "react";

import { EverCard, EverLabel, EverTg } from "~/v2/components";

import { OnlyWaitBubble } from "./ChatBotAccessories/UIElements";
import styles from "./styles.module.scss";

// Simple text formatter: if not a string, JSON.stringify it.
const formatText = (text) => {
  if (typeof text === "string") return text;
  return JSON.stringify(text, null, 4)?.trim();
};

const ThoughtCard = ({ index, thought, isCalling, isLastElement }) => {
  const [isOutputExpanded, setIsOutputExpanded] = useState(false);
  const displayThought = formatText(thought.thought);
  const displayResponse = formatText(thought.strResponse);
  const headerLabel = thought.signature ? thought.signature : "EverAI";

  // Determine tool content based on toolCalling flag and available data.
  let toolContent = null;
  if (thought.tool && Object.keys(thought.tool).length > 0) {
    if (thought.tool.toolCalling === true && thought.tool.toolArg) {
      toolContent = (
        <div className="gap-2 py-4 flex rounded-xl flex-col">
          <EverTg.Heading4 className="text-ever-base-content">
            Input
          </EverTg.Heading4>
          <EverTg.SubHeading4 className="bg-ever-base-100 text-ever-base-content-mid font-medium text-wrap overflow-hidden p-2 border rounded">
            <pre className="whitespace-pre-wrap">
              {formatText(thought.tool.toolArg)}
            </pre>
          </EverTg.SubHeading4>
        </div>
      );
    } else if (thought.tool.toolCalling === false && thought.tool.toolOutput) {
      toolContent = (
        <div
          className="gap-2 py-4 flex rounded-xl flex-col"
          onClick={() => setIsOutputExpanded(!isOutputExpanded)}
        >
          <EverTg.Heading4 className="text-ever-base-content">
            Output
          </EverTg.Heading4>
          <EverTg.SubHeading4 className="bg-ever-base-100 text-ever-base-content-mid font-medium text-wrap overflow-hidden p-2 border rounded cursor-pointer">
            <pre className="whitespace-pre-wrap">
              {formatText(thought.tool.toolOutput).length > 400 &&
              !isOutputExpanded
                ? formatText(thought.tool.toolOutput).slice(0, 400) + "..."
                : formatText(thought.tool.toolOutput)}
            </pre>
          </EverTg.SubHeading4>
        </div>
      );
    }
  }

  return (
    <EverCard
      key={index}
      className="!p-0 w-full h-full my-3 bg-ever-base-25 transition-all duration-1000"
    >
      <div className="flex flex-col">
        {/* Header: shows the signature (or Final Answer) */}
        <div className="px-6 pt-4 pb-2 flex bg-ever-base-100 rounded-xl">
          <EverTg.Heading3>
            <div className="flex items-center gap-2">
              {headerLabel}
              {isLastElement && isCalling ? (
                <OnlyWaitBubble />
              ) : (
                <CheckCircleIcon className="h-4 w-4 text-ever-success" />
              )}
            </div>
          </EverTg.Heading3>
        </div>

        {/* Divider */}
        <div className="flex items-center w-full">
          <div className="h-1 w-1 rotate-45 border border-solid border-ever-base-300 bg-ever-base-400"></div>
          <div className="h-0 w-full border border-solid border-ever-base-300"></div>
          <div className="h-1 w-1 rotate-45 border border-solid border-ever-base-300 bg-ever-base-400"></div>
        </div>

        <div
          className={`${
            displayThought?.length > 0 && "py-4"
          } flex flex-col px-6`}
        >
          {/* Internal Thought */}
          {displayThought?.length > 0 && (
            <div className="flex flex-col gap-2">
              <EverTg.Heading4 className="text-ever-base-content">
                Thought
              </EverTg.Heading4>
              <EverTg.SubHeading4 className="text-ever-base-content-mid font-medium text-wrap overflow-hidden p-2 border rounded">
                {displayThought}
              </EverTg.SubHeading4>
            </div>
          )}

          {/* Tool Details */}
          {toolContent ? (
            toolContent
          ) : (
            <div
              className={`flex flex-col ${
                displayResponse?.length > 0 && "gap-2 py-4"
              }`}
            >
              {/* Response: display both string and JSON if available */}
              {displayResponse?.length > 0 && (
                <>
                  <EverTg.Heading4 className="text-ever-base-content">
                    Response
                  </EverTg.Heading4>
                  {displayResponse && (
                    <EverTg.SubHeading4 className="text-ever-base-content-mid font-medium text-wrap overflow-hidden p-2 border rounded">
                      {displayResponse}
                    </EverTg.SubHeading4>
                  )}
                </>
              )}
              {thought.jsonResponse &&
                Object.keys(thought.jsonResponse).length > 0 && (
                  <EverTg.SubHeading4 className="bg-ever-base-100 text-ever-base-content-mid font-medium text-wrap overflow-hidden p-2 border rounded">
                    <pre className="whitespace-pre-wrap">
                      {formatText(thought.jsonResponse)}
                    </pre>
                  </EverTg.SubHeading4>
                )}
            </div>
          )}
        </div>
      </div>
    </EverCard>
  );
};

export const ChainOfThought = ({ chatProperties, handleThoughtActive }) => {
  // Filter out any chain-of-thought items that have both an empty "thought" and "strResponse"
  const chainArray = chatProperties.thoughtActive?.chainOfThought || [];

  // Find if the last thought's signature is having Calling in substring
  const lastThought = chainArray[chainArray.length - 1];
  const isCalling = lastThought?.signature?.includes("Calling");

  return (
    <div className="flex flex-col px-4 py-4 gap-4 bg-ever-base-100 w-full h-full overflow-hidden">
      {/* Header */}
      <div className="w-full flex justify-between items-center">
        <EverLabel className="pl-1 font-bold text-lg text-ever-base-content">
          Chain of thought
        </EverLabel>
        <XCloseIcon
          className="w-5 h-5 cursor-pointer"
          onClick={() => handleThoughtActive(null)}
        />
      </div>

      <div className={"overflow-auto " + styles.hideScrollBar}>
        <div className="flex flex-col">
          {chainArray.map((thought, index) => (
            <div
              className="flex flex-col gap-4"
              key={`${chatProperties.sessionId}-${index}`}
            >
              <ThoughtCard
                index={index}
                thought={thought}
                isCalling={isCalling}
                isLastElement={index === chainArray.length - 1}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
