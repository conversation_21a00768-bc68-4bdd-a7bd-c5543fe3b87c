import { XIcon } from "@everstage/evericons/outlined";

import { EverLoader, EverTg } from "~/v2/components";

import { ChatInput } from "./ChatBotAccessories/ChatInput";
import { MessageList } from "./ChatBotAccessories/MessageList";

const Threads = ({
  chatProperties,
  handleSetLoadingOffset,
  handleSetOffsetValue,
  handleActiveSession,
  handleSetThreadDraftInput,
  handleDeleteSessionModal,
  handleSetActiveSelectedSession,
  handleSend,
  threadsIsLoading,
  handleShowApproval,
  handleActiveMessageId,
  handleActiveSessionId,
  fetchChainOfThought,
  handleChainOfThoughtLoading,
}) => {
  return (
    <div className="h-full w-full px-2 gap-2 flex flex-col justify-between border-0 border-solid border-ever-base-400">
      <div className="w-full justify-between flex flex-row items-center pl-2">
        <EverTg.SubHeading1 className="text-ever-content font-medium text-lg">
          Replies
        </EverTg.SubHeading1>
        <XIcon
          className="w-5 h-5 cursor-pointer"
          onClick={() => {
            handleActiveSession(null);
          }}
        />
      </div>
      {/* Chat Messages Section */}
      {threadsIsLoading ? (
        <EverLoader />
      ) : (
        <div className="relative flex flex-col mb-auto overflow-y-auto">
          <div className="relative flex-col w-full p-2 overflow-auto h-full">
            {/* Rendered Chat Messages */}
            <MessageList
              chatProperties={chatProperties}
              handleSetLoadingOffset={handleSetLoadingOffset}
              handleSetOffsetValue={handleSetOffsetValue}
              handleActiveSession={handleActiveSession}
              handleDeleteSessionModal={handleDeleteSessionModal}
              handleSetActiveSelectedSession={handleSetActiveSelectedSession}
              threadView={true}
              handleShowApproval={handleShowApproval}
              handleActiveMessageId={handleActiveMessageId}
              handleActiveSessionId={handleActiveSessionId}
              fetchChainOfThought={fetchChainOfThought}
              handleChainOfThoughtLoading={handleChainOfThoughtLoading}
            />
          </div>
          {/* User Input Section */}
          {!chatProperties?.showApproval && (
            <ChatInput
              handleSend={handleSend}
              draft={chatProperties.threadDraftInput}
              setDraft={handleSetThreadDraftInput}
              sessionId={chatProperties.activeSession}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default Threads;
