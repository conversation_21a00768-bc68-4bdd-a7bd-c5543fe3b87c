import { useState } from "react";
import { useQuery } from "react-query";
import { useImmer } from "use-immer";
import { v4 as uuidv4 } from "uuid";

import { HUMAN } from "./constants";
import { useAgentExecutorApi } from "./restapi";

export const useChatInstance = () => {
  const [chatProperties, setChatProperties] = useImmer({
    draftInput: {
      text: "",
      imageUrls: [],
      fileUrls: [],
      mention: null,
    }, // Draft input for the chat
    threadDraftInput: {
      text: "",
      imageUrls: [],
      fileUrls: [],
      mention: null,
    },
    activeSession: null, // if the thread is selected, this will hold the sessionID alone
    offsetValue: 0, // Offset value for fetching all messages
    limitValue: 12,
    hasMore: true, // Flag to check if there are more messages to fetch
    loadingOffset: false, // Flag to check if messages are being fetched
    feedbackInput: "",
    feedbackModal: false,
    deleteSessionModal: false,
    activeSelectedSession: null,
    sessionData: {}, // All session data
    thoughtActive: null, // alternate bw kanban and thought chain
    errorPage: false,
    activeThreadMessages: [],
    showApproval: false,
    currentUser: {},
    skills: null,
    agents: null,
    activeMessageIdForThought: null,
    activeSessionIdForThought: null,
    chainOfThoughtLoading: false,
  });
  const [agentDetails, setAgentDetails] = useState([]);
  const [sessionIdFromSupabase, setSessionIdFromSupabase] = useState(null);
  const [loadInInterval, setLoadedInitially] = useState(false);

  const agentExecutorApi = useAgentExecutorApi();

  const ENTITY_TYPE = {
    SKILL: "skill",
    AGENT: "agent",
  };

  const { refetch: fetchAgentDetails } = useQuery(
    ["getAgentDetails"],
    () => agentExecutorApi.getPublishAgentDetails(),
    {
      enabled: true,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        setAgentDetails(response?.agents);
      },
    }
  );

  // Query to fetch messages for the active session - Threads here.
  const { refetch: fetchThreadMessages, isLoading: threadsIsLoading } =
    useQuery(
      ["getMessages", chatProperties.activeSession],
      () => agentExecutorApi.getMessages(chatProperties.activeSession),
      {
        enabled: Boolean(chatProperties.activeSession),
        retry: false,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        onSuccess: (response) => {
          const messageData = response;
          if (messageData) {
            const messages = messageData.allMessagesSession;
            setChatProperties((draft) => {
              draft.activeThreadMessages = messages;
            });
            handleSetLoadingOffset(false);
          }
        },
      }
    );

  // Query to fetch all active sessions/stories
  const { refetch: refetchAllMessages, isLoading: allMessagesLoading } =
    useQuery(
      ["getActiveSessions"],
      () =>
        agentExecutorApi.getAllSessions(
          sessionIdFromSupabase,
          chatProperties.offsetValue || 0,
          chatProperties.limitValue || 10
        ),
      {
        enabled:
          Object.keys(chatProperties.sessionData).length === 0 ||
          loadInInterval,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        retry: false,
        onSuccess: (data) => {
          if (Object.keys(data).length > 0) {
            handleSessionUpdate(data.allSessionsData);
            setChatProperties((draft) => {
              draft.currentUser = data.currentUser;
            });
            setLoadedInitially(true);
          }
        },
        onError: (error) => {
          console.error("Error fetching active sessions", error);
          handleSetErrorPage(true);
        },
      }
    );

  //Query to retrieve the chainOfThought
  const { refetch: fetchChainOfThought } = useQuery(
    ["getChainOfThought"],
    () =>
      agentExecutorApi.getChainOfThought(
        chatProperties?.activeSessionIdForThought,
        chatProperties?.activeMessageIdForThought
      ),
    {
      enabled: chatProperties?.thoughtActive !== null,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        const thoughtData = response;
        if (thoughtData) {
          const thought = thoughtData?.chainOfThought;
          setChatProperties((draft) => {
            draft.thoughtActive = thought;
          });
        }
      },
      refetchInterval: 5000,
    }
  );

  //Query to fetch skills
  const { refetch: fetchSkills } = useQuery(
    ["getSkills"],
    () => agentExecutorApi.getEntities(ENTITY_TYPE.SKILL),
    {
      enabled: true,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        setChatProperties((draft) => {
          draft.skills = response;
        });
      },
    }
  );

  //Query to fetch agents
  const { refetch: fetchAgents } = useQuery(
    ["getAgents"],
    () => agentExecutorApi.getEntities(ENTITY_TYPE.AGENT),
    {
      enabled: true,
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        setChatProperties((draft) => {
          draft.agents = response;
        });
      },
    }
  );

  // Function to handle sending a message
  const handleSend = async (sessionId = null) => {
    if (
      sessionId
        ? chatProperties.threadDraftInput.text.trim().length === 0
        : chatProperties.draftInput.text.trim().length === 0
    )
      return;

    const newMessage = {
      messageId: uuidv4(),
      sessionId: sessionId,
      messageContent: sessionId
        ? chatProperties.threadDraftInput
        : chatProperties.draftInput,
      senderType: HUMAN,
      sender: chatProperties.currentUser.senderID, // email
      chainOfThought: {},
      threadId: sessionId,
      systemMessageId: "",
      humanRequired: false,
      approvalRequired: false,
      approvalStatus: null,
      sentTime: new Date().toISOString(),
      like: null,
    };

    if (sessionId) {
      handleSetThreadDraftInput({
        text: "",
        imageUrls: [],
        fileUrls: [],
        mention: null,
      });
      setChatProperties((draft) => {
        draft.activeThreadMessages.push(newMessage);
      });
      await agentExecutorApi.sendMessage(
        newMessage,
        sessionId,
        chatProperties.activeThreadMessages[0]
      );
      handleSessionUpdate({
        ...chatProperties.sessionData,
        [sessionId]: {
          ...chatProperties.sessionData[sessionId],
          repliesCount: chatProperties.sessionData[sessionId].repliesCount + 1,
          lastInteractedTime: new Date().toISOString(),
          usersInThread: {
            users:
              chatProperties.sessionData[
                sessionId
              ]?.usersInThread?.users?.filter(
                (user) => user.senderID === chatProperties.currentUser.senderID
              ).length > 0
                ? chatProperties.sessionData[sessionId]?.usersInThread?.users
                : chatProperties.sessionData[sessionId].usersInThread?.users
                ? [
                    ...chatProperties.sessionData[sessionId].usersInThread
                      .users,
                    {
                      name: chatProperties.currentUser.name,
                      avatar: chatProperties.currentUser.avatar,
                      senderId: chatProperties.currentUser.senderID,
                      type: HUMAN,
                    },
                  ]
                : [
                    {
                      name: chatProperties.currentUser.name,
                      avatar: chatProperties.currentUser.avatar,
                      senderId: chatProperties.currentUser.senderID,
                      type: HUMAN,
                    },
                  ],
          },
        },
      });
    } else {
      const newSessionId = uuidv4();
      newMessage.sessionId = newSessionId;
      setChatProperties((draft) => {
        draft.sessionData = {
          [newSessionId]: {
            sessionId: newSessionId,
            messageContent: newMessage,
            usersInThread: {},
            repliesCount: 0,
            lastInteractedTime: new Date().toISOString(),
          },
          ...draft.sessionData,
        };
      });
      handleSetDraftInput({
        text: "",
        imageUrls: [],
        fileUrls: [],
        mention: null,
      });
      await agentExecutorApi.sendMessage(newMessage, newSessionId, true);
    }
  };

  function handleActiveSession(value) {
    setChatProperties((draft) => {
      draft.activeSession = value;
    });
  }

  function handleActiveMessageId(value) {
    setChatProperties((draft) => {
      draft.activeMessageIdForThought = value;
    });
  }

  function handleActiveSessionId(value) {
    setChatProperties((draft) => {
      draft.activeSessionIdForThought = value;
    });
  }

  function handleSetHasMore(value) {
    setChatProperties((draft) => {
      draft.hasMore = value;
    });
  }

  function handleSetFeedbackModal(value) {
    setChatProperties((draft) => {
      draft.feedbackModal = value;
    });
  }

  function handleSetDraftInput(value) {
    setChatProperties((draft) => {
      draft.draftInput = value;
    });
  }

  function handleSetLoadingOffset(value) {
    setChatProperties((draft) => {
      draft.loadingOffset = value;
    });
  }

  function handleSetActiveSelectedSession(value) {
    setChatProperties((draft) => {
      draft.activeSelectedSession = value;
    });
  }

  function handleSetOffsetValue(value) {
    setChatProperties((draft) => {
      draft.offsetValue =
        typeof value === "function" ? value(draft.offsetValue) : value;
    });
  }

  function handleFeedbackInput(value) {
    setChatProperties((draft) => {
      draft.feedbackInput = value;
    });
  }

  function handleThoughtActive(value) {
    setChatProperties((draft) => {
      draft.thoughtActive = value;
    });
  }

  function handleSetErrorPage(value) {
    setChatProperties((draft) => {
      draft.errorPage = value;
    });
  }

  function handleSessionUpdate(data) {
    const normalizedData =
      Object.keys(data).length == 0 && chatProperties.offsetValue === 0
        ? {}
        : { ...chatProperties.sessionData, ...data };

    if (Object.keys(data).length == 0 && chatProperties.offsetValue !== 0) {
      handleSetHasMore(false);
    }

    setChatProperties((draft) => {
      draft.sessionData = normalizedData;
    });
    handleSetLoadingOffset(false);
  }

  function deleteSessionFromSessionData(sessionId) {
    // delete key sessionId from sessionData
    setChatProperties((draft) => {
      delete draft.sessionData[sessionId];
    });
    // reset activesession if it is the same as the deleted session
    if (chatProperties.activeSession === sessionId) {
      handleActiveSession(null);
    }
  }

  function handleDeleteSessionModal(value) {
    setChatProperties((draft) => {
      draft.deleteSessionModal = value;
    });
  }

  function handleSetThreadDraftInput(value) {
    setChatProperties((draft) => {
      draft.threadDraftInput = value;
    });
  }

  function handleSessionIdFromSupabase(value) {
    setSessionIdFromSupabase(value);
  }

  function handleShowApproval(value) {
    setChatProperties((draft) => {
      draft.showApproval = value;
    });
  }

  function handleChainOfThoughtLoading(value) {
    setChatProperties((draft) => {
      draft.chainOfThoughtLoading = value;
    });
  }

  return {
    chatProperties,
    sessionIdFromSupabase,
    fetchThreadMessages,
    fetchChainOfThought,
    refetchAllMessages,
    threadsIsLoading,
    handleSend,
    handleSetDraftInput,
    handleActiveSession,
    handleSetOffsetValue,
    handleSetHasMore,
    handleSetLoadingOffset,
    handleFeedbackInput,
    handleSetFeedbackModal,
    handleDeleteSessionModal,
    handleSetActiveSelectedSession,
    handleSessionUpdate,
    handleThoughtActive,
    handleSetErrorPage,
    deleteSessionFromSessionData,
    handleSetThreadDraftInput,
    handleSessionIdFromSupabase,
    fetchAgentDetails,
    agentDetails,
    handleShowApproval,
    handleActiveMessageId,
    handleActiveSessionId,
    fetchSkills,
    fetchAgents,
    handleChainOfThoughtLoading,
    allMessagesLoading,
  };
};
