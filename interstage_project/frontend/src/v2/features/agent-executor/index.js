import { useSupabasePostgresChanges } from "everstage-supabase";
import React, { useEffect } from "react";

import { SUPABASE_CONSTANTS } from "~/Enums";
import { EverHotToastMessage, toast } from "~/v2/components";
import { NotFound } from "~/v2/Router";

import { ChatContextWindow } from "./ChatContextWindow";
import { useChatInstance } from "./chatHooks";
import { DeleteSessionModal } from "./DeleteSession";

// Main component for AgentExecutor
const AgentExecutor = () => {
  const chatInstanceProps = useChatInstance();

  const {
    errorPage,
    chatProperties,
    refetchAllMessages,
    fetchThreadMessages,
    sessionIdFromSupabase,
    handleDeleteSessionModal,
    handleSessionIdFromSupabase,
    deleteSessionFromSessionData,
  } = chatInstanceProps;

  const realtimePostgresChangesFilter = [
    {
      event: SUPABASE_CONSTANTS.EVERAI_MESSAGE.EVENT_TYPE[0],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.EVERAI_MESSAGE.TASK_NAME}`,
    },
    {
      event: SUPABASE_CONSTANTS.EVERAI_MESSAGE.EVENT_TYPE[1],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.EVERAI_MESSAGE.TASK_NAME}`,
    },
  ];

  const channelPrefix = SUPABASE_CONSTANTS.EVERAI_MESSAGE.CHANNEL_NAME;
  const testStore = null;

  let updates = useSupabasePostgresChanges(realtimePostgresChangesFilter, {
    channelPrefix,
    testStore,
  });

  useEffect(() => {
    if (updates && updates?.new?.data?.sessionId) {
      handleSessionIdFromSupabase(updates?.new?.data?.sessionId);
    } else if (updates && updates?.new?.data?.new_messages_from_spm === true) {
      refetchAllMessages();
    }
  }, [updates]);

  useEffect(() => {
    if (sessionIdFromSupabase) {
      refetchAllMessages();
      handleSessionIdFromSupabase(null);
      if (chatProperties.activeSession) {
        fetchThreadMessages();
      }
    }
  }, [sessionIdFromSupabase]);

  // Render the main component or error page
  return errorPage ? (
    <NotFound />
  ) : (
    <div className="h-full w-full relative">
      <div className="absolute h-full w-full flex flex-row">
        <ChatContextWindow
          {...chatInstanceProps}
          className="flex h-full w-[90%]"
        />
      </div>
      {chatProperties.deleteSessionModal ? (
        <DeleteSessionModal
          showDeletionModal={chatProperties.deleteSessionModal}
          onDeleteView={() => {
            handleDeleteSessionModal(false);
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={"Session deleted successfully"}
                />
              ),
              { position: "top-center", duration: 1500 }
            );
            deleteSessionFromSessionData(chatProperties.activeSelectedSession);
          }}
          onCancelView={() => {
            handleDeleteSessionModal(false);
          }}
          deleteSessionId={chatProperties.activeSelectedSession}
        />
      ) : (
        <></>
      )}
    </div>
  );
};

export default AgentExecutor;
