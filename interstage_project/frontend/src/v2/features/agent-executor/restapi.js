import { useAuthStore } from "~/GlobalStores/AuthStore";

// Class to handle API calls for AgentExecutor
class AgentExecutorApi {
  constructor(accessToken) {
    this.accessToken = accessToken;
  }

  // Method to get authorization headers
  getAuthHeaders() {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.accessToken}`,
    };
  }

  // Method to fetch active sessions
  async getAllSessions(sessionIdToFetch, offsetValue, limitValue) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    try {
      if (sessionIdToFetch) {
        const response = await fetch(
          `/llm_agent/sessions/${sessionIdToFetch}`,
          requestOptions
        );
        const responseData = await response.json();
        if (!response.ok) throw responseData;
        return responseData;
      } else {
        let url = `/llm_agent/sessions/?offsetValue=${offsetValue}&limitValue=${limitValue}`;
        const response = await fetch(url, requestOptions);
        const responseData = await response.json();
        if (!response.ok) throw responseData;
        return responseData;
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to update an active session
  // @param {string} sessionId - ID of the session to update
  // @param {object} data - Data to update the session with
  async updateActiveSession(sessionId, data) {
    try {
      const requestOptions = {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(data),
      };
      const response = await fetch(
        `/llm_agent/sessions/${sessionId}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to delete a session
  // @param {string} sessionId - ID of the session to delete
  // @param {boolean} onlyMessages - Flag to indicate if only messages should be deleted
  async deleteSession(sessionId, onlyMessages = false) {
    const requestOptions = {
      method: "DELETE",
      headers: this.getAuthHeaders(),
    };
    try {
      const response = await fetch(
        `/llm_agent/sessions/${sessionId}?onlyMessages=${onlyMessages}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to fetch messages for a session
  // @param {string} sessionId - ID of the session to fetch messages for
  async getMessages(sessionId) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    try {
      const response = await fetch(
        `/llm_agent/sessions/${sessionId}/messages`,
        requestOptions
      );
      const responseData = await response.json();

      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to send a message to the backend
  // @param {string} sessionId - ID of the session to send the message to
  // @param {object} message - Message content to send
  // @param {object} lastMessage - Last message sent in the session
  async sendMessage(message, sessionId, newSession, lastMessage) {
    const data = {
      messageContent: message.messageContent,
      threadId: message.threadId,
      messageId: message.messageId,
      systemMessageId: null,
      approvalMessageId: null,
    };
    const url = `/llm_agent/sessions/${sessionId}/prompt`;
    if (!newSession) {
      if (lastMessage?.humanRequired) {
        data.threadId = lastMessage?.threadId;
        data.systemMessageId = lastMessage?.systemMessageId;
      }
      if (message?.approvalRequired) {
        data.approvalMessageId = message?.approvalMessageId;
        data.approvalStatus = message?.approvalStatus;
        data.threadId = message?.threadId;
        data.systemMessageId = message?.systemMessageId;
      }
    }
    const requestOptions = {
      method: "POST",
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    };
    try {
      const response = await fetch(url, requestOptions);
      if (!response.ok) {
        const responseData = await response.json();
        throw responseData;
      }
      return response.body;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to send feedback for an agent
  // @param {string} sessionId - ID of the session to send feedback for
  // @param {string} feedback - Feedback content
  // @param {string} messageId - ID of the message to send feedback for
  // @param {boolean} likeStatus - Like status of the feedback
  async agentFeedback(sessionId, feedback, messageId, likeStatus) {
    const data = {
      sessionId: sessionId,
      feedback: feedback,
      messageId: messageId,
      likeStatus: likeStatus,
    };
    const requestOptions = {
      method: "PUT",
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    };

    try {
      const response = await fetch(
        `/llm_agent/${sessionId}/feedback`,
        requestOptions
      );
      if (!response.ok) {
        const responseData = await response.json();
        throw responseData;
      }
      const responseData = await response.json();
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getPublishAgentDetails() {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    try {
      const response = await fetch(
        `/llm_agent/get_publish_agent_versions`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getChainOfThought(sessionId, messageId) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    try {
      if (!sessionId) throw new Error("Session ID is required");
      const url = messageId
        ? `/llm_agent/get_chain_of_thought/${sessionId}/${messageId}`
        : `/llm_agent/get_chain_of_thought/${sessionId}/`;
      const response = await fetch(url, requestOptions);
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getEntities(entityType) {
    const requestOptions = {
      method: "GET",
      headers: this.getAuthHeaders(),
    };
    try {
      if (!entityType) throw new Error("Entity type is required");
      const response = await fetch(
        `/llm_agent/get_all_entities/${entityType}`,
        requestOptions
      );
      const responseData = await response.json();
      if (!response.ok) throw responseData;
      return responseData;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

// Hook to use the AgentExecutorApi
export function useAgentExecutorApi() {
  const authStore = useAuthStore();
  const agentExecutor = new AgentExecutorApi(authStore.accessToken);
  return agentExecutor;
}
