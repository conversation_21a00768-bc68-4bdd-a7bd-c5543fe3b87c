.markdown {
  p {
    margin: 0 !important;
    line-height: calc(100% + 12px);
  }
  tr {
    border-top: 1px solid #c6cbd1;
    background: #fff;
  }
  a {
    color: inherit;
    font-weight: bold;
  }
  a:hover {
    text-decoration: underline;
  }
  th,
  td {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
  }
}

.sendButton {
  background: #2365ff !important;
  border: 1px solid #2365ff !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;
  border-radius: 8px !important;
}

.sendButtonFeedback {
  background: #2365ff !important;
  border: 1px solid #2365ff !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;
  border-radius: 8px !important;
  height: 36px;
  cursor: pointer;
}

.clearButton {
  background: #ffffff !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;
  border-radius: 8px !important;
}

.clearButton:hover {
  background: #edeff2 !important;
}

.messageInput {
  display: flex;
}

.messageInput button {
  margin-left: 10px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.hideScrollBar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.messagesScrollbar {
  scrollbar-width: thin;
}

.kanbanBoard {
  scrollbar-width: thin;
  max-height: "42vh";
}

.scrollBarThin {
  scrollbar-width: thin;
}

.react-resizable-handle {
  background: #ccc; /* Change the background color */
  width: 6px; /* Adjust the width of the handle */
  height: 100%;
  position: absolute;
  right: 0; /* Position it on the right edge */
  top: 0;
  cursor: col-resize; /* Show the appropriate cursor */
}

.react-resizable-handle:hover {
  background: #aaa; /* Change color on hover */
}

// typing chat bubble

.half {
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: flex-start;
  &.light .typing {
    background-color: #ffffff;
  }
}
.typing {
  display: block;
  width: 100px;
  height: 45px;
  border-radius: 0px 12px 12px 12px;
  box-shadow: 0px 1px 3px rgba(0, 23, 128, 0.1),
    0px 1px 2px rgba(0, 23, 128, 0.06);
  margin: 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
}

.circle {
  display: block;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #000000;
  margin: 3px;

  &.scaling {
    animation: typing 1000ms ease-in-out infinite;
    animation-delay: 3600ms;
  }
}
.circle:nth-child(1) {
  animation-delay: 0ms;
}
.circle:nth-child(2) {
  animation-delay: 333ms;
}
.circle:nth-child(3) {
  animation-delay: 666ms;
}

@keyframes typing {
  0% {
    transform: scale(1);
  }
  33% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.4);
  }
  100% {
    transform: scale(1);
  }
}
