import { DataflowIcon } from "@everstage/evericons/outlined";
import { cloneDeep } from "lodash";
import React, { useState, useEffect } from "react";
import { useQuery as useReactQuery } from "react-query";

import {
  createApprovalRequest,
  getApprovalConfig,
} from "~/Api/ApprovalWorkflowService";
import {
  RBAC_ROLES,
  PAYOUT_APPROVAL_TYPES,
  LINE_ITEM_TYPE_ENTITY_TYPE,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import {
  EverLoader,
  EverButton,
  EverModal,
  EverLabel,
  EverCard,
  EverHotToastMessage,
  toast,
  EverTg,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { ApprovalWorkFlowTemplateBuilder } from "~/v2/features/approvals/approval-workflow-builder";

export const ApprovalWorkFlowTemplateSelector = (props) => {
  const { accessToken } = useAuthStore();
  const {
    showSelector,
    setShowSelector,
    title,
    templatesRefetch,
    templatesMetaData,
    requestParams,
    refetch,
  } = props;
  const [selectedTemplateId, setSelectedTemplateId] = useState(null);
  const [showTemplateBuilder, setShowTemplateBuilder] = useState(false);
  const { rolesRefetch } = useEmployeeStore();

  const [payoutLevel, setPayoutLevel] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
      setPayoutLevel(approvalType);
    }
  }, [approvalConfigData]);

  const onSendApprovalRequest = () => {
    const params = cloneDeep(requestParams);
    if (payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL) {
      params["entity_type"] = LINE_ITEM_TYPE_ENTITY_TYPE;
    }
    params["template_id"] = selectedTemplateId;

    params.instance_params.instance_details.forEach(
      (ele) => delete ele["name"]
    );

    // Empty out instance details when isSelectedAll is true
    // The instance details will be fetched in backend to process.
    if (params.bulk_mode && params.isSelectedAll) {
      params.instance_params.instance_details = [];
    }

    return new Promise(() => {
      createApprovalRequest(params, accessToken)
        .then((response) => {
          if (response.ok) {
            response.json().then((data) => {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="success"
                    description={data.message}
                  />
                ),
                { position: "top-center" }
              );
            });
          } else {
            response.json().then((data) => {
              toast.custom(
                () => (
                  <EverHotToastMessage
                    type="error"
                    description={
                      data?.message ?? "Approval request creation failed."
                    }
                  />
                ),
                { position: "top-center" }
              );
            });
          }
        })
        .finally(() => {
          setSelectedTemplateId(null);
          setShowSelector(false);
          refetch();
          rolesRefetch();
        });
    });
  };
  return (
    <>
      <EverModal
        closable={false}
        bodyStyle={{ height: 480, overflowY: "auto" }}
        width="950px"
        visible={showSelector}
        onCancel={() => setShowSelector(false)}
        maskClosable={false}
        title={title}
        footer={
          <EverModal.Footer>
            <ApprovalTemplateSelectorFooter
              setShowSelector={setShowSelector}
              setShowTemplateBuilder={setShowTemplateBuilder}
              isSendApprovalDisabled={selectedTemplateId == null}
              onSendApprovalRequest={onSendApprovalRequest}
            />
          </EverModal.Footer>
        }
      >
        <EverLabel>Pick a workflow and send request</EverLabel>
        {templatesMetaData ? (
          templatesMetaData?.approvalTemplatesByEntityType.length > 0 &&
          templatesMetaData.approvalTemplatesByEntityType.map(
            (templateMetaData, index) => (
              <ApprovalTemplateCardView
                key={index}
                templateData={templateMetaData}
                isSelected={selectedTemplateId == templateMetaData.templateId}
                setSelected={setSelectedTemplateId}
              />
            )
          )
        ) : (
          <EverLoader indicatorType="spinner" tip="" />
        )}
      </EverModal>
      {showTemplateBuilder && (
        <ApprovalWorkFlowTemplateBuilder
          showApproval={showTemplateBuilder}
          setShowApproval={setShowTemplateBuilder}
          mode="create"
          save="optional"
          title={title}
          type="instance"
          setEditTemplateId={() => {}}
          existingTemplatesRefetch={templatesRefetch}
          onSaveCloseBuilder={true}
          showBackToWorkflows={true}
          requestParams={requestParams}
          setShowSelector={setShowSelector}
          refetch={refetch}
        />
      )}
    </>
  );
};

const ApprovalTemplateCardView = (props) => {
  const { templateData, isSelected, setSelected } = props;
  const { templateId, templateName, templateDescription } = templateData;

  return (
    <EverCard
      className={`w-full cursor-pointer mt-3.5 ${
        isSelected ? "bg-ever-info-lite" : "bg-ever-base"
      }`}
      interactive
      onClick={() => {
        setSelected(templateId);
      }}
    >
      <div className="flex flex-col gap-2">
        <EverTg.Heading3>{templateName}</EverTg.Heading3>
        <EverTg.Caption className="text-ever-base-content-mid whitespace-nowrap overflow-hidden text-ellipsis">
          {templateDescription ? templateDescription : "-"}
        </EverTg.Caption>
      </div>
    </EverCard>
  );
};

const ApprovalTemplateSelectorFooter = (props) => {
  const {
    setShowSelector,
    isSendApprovalDisabled,
    setShowTemplateBuilder,
    onSendApprovalRequest,
  } = props;

  return (
    <div className="flex items-center text-left">
      <div className="grow">
        <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_CONFIG}>
          <EverButton
            type="link"
            onClick={() => {
              setShowTemplateBuilder(true);
            }}
            prependIcon={<DataflowIcon className="w-4 h-4" />}
          >
            Create Custom Workflow
          </EverButton>
        </RBACProtectedComponent>
      </div>
      <EverButton color="base" onClick={() => setShowSelector(false)}>
        Cancel
      </EverButton>
      <EverButton
        onClick={onSendApprovalRequest}
        disabled={isSendApprovalDisabled}
      >
        Send Approval Request
      </EverButton>
    </div>
  );
};
