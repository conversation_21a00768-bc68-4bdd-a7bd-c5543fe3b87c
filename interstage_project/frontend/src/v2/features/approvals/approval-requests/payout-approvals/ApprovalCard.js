import { CheckIcon, SlashCircleIcon } from "@everstage/evericons/outlined";
import { CheckCircleIcon, XCircleIcon } from "@everstage/evericons/solid";
import moment from "moment-timezone";
import React, { useState, useEffect } from "react";
import { useQuery, useMutation } from "react-query";

import {
  getApprovalConfig,
  approveLineItemRequest,
  rejectLineItemRequest,
} from "~/Api/ApprovalWorkflowService";
import { PAYOUT_APPROVAL_TYPES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { EverButton, EverCard, EverTg, message } from "~/v2/components";

import RejectApprovalModal from "./approval-action-modals/RejectModal";
import { approveConfirm } from "./Common";
import { RejectApprovalModal as RejectApprovalModalCommAdj } from "../../commission-adjustments/RejectModal";
import { ENTITY_TYPES } from "../../Constants";

const ApprovalCard = ({
  approvalData,
  store,
  handleApproveRequest,
  refetchData,
  refetchApprovalStatusData,
  closeDrawer,
  loading,
  customCalendar,
  entityType,
}) => {
  const [payoutLevel, setPayoutLevel] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );
  const [modalVisibility, setModalVisibility] = useState(false);
  const { timeZone, rolesRefetch } = useEmployeeStore();
  const userTimeZone = timeZone ? timeZone : "UTC";
  const { accessToken } = useAuthStore();

  const { data } = useQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const isCommAdj = entityType === ENTITY_TYPES.COMMISSION_ADJUSTMENT_APPROVALS;

  const { mutate } = useMutation(
    (payload) => approveLineItemRequest(payload, accessToken),
    {
      onError: (error) => {
        message.dismiss();
        const msg =
          error?.message ||
          "Could not process the request. Please try again later.";
        message.error(msg);
      },
      onSuccess: (res) => {
        console.log("success", res);
        const msg = res?.message || "You've approved the request.";
        message.dismiss();
        message.success(msg);
        refetchData();
        // refreshTimeLine();
        closeDrawer && closeDrawer();
        refetchApprovalStatusData && refetchApprovalStatusData();
        rolesRefetch();
        console.log("success");
      },
    }
  );

  const { mutate: rejectLineItemReq } = useMutation(
    (payload) => rejectLineItemRequest(payload, accessToken),
    {
      onError: (error) => {
        message.dismiss();
        const msg =
          error?.message ||
          "Could not process the request. Please try again later.";
        message.error(msg);
        console.log("error", error);
      },
      onSuccess: (res) => {
        refetchData();

        rolesRefetch();
        refetchApprovalStatusData && refetchApprovalStatusData();
        closeDrawer && closeDrawer();
        setModalVisibility(false);
        const msg = res?.message || "You've rejected the request.";
        message.dismiss();
        message.success(msg);
      },
    }
  );

  useEffect(() => {
    if (data?.status === "SUCCESS") {
      const config = data?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
      setPayoutLevel(approvalType);
    }
  }, [data]);

  const handleLineItemApproveRequest = (approvalData) => {
    const payload = {
      requestIds: [approvalData?.approvalRequestId],
    };
    message.loading("Approving...");
    mutate(payload);
  };

  const handleApproveRequestByEntityType =
    isCommAdj || payoutLevel !== PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
      ? handleApproveRequest
      : handleLineItemApproveRequest;

  return (
    <>
      <EverCard
        outlined
        className={
          isCommAdj
            ? "bg-ever-info-lite border border-solid border-ever-info"
            : ""
        }
        data-testid="approval-banner"
      >
        <div className="flex items-center gap-3">
          <div className="grow flex flex-col my-2">
            <EverTg.Heading4>
              Waiting for your approval{" "}
              {approvalData?.dueDate &&
                `[ Due by ${moment(approvalData?.dueDate)
                  .tz(userTimeZone)
                  .format("LL")} ]`}
            </EverTg.Heading4>
            <EverTg.Description>
              {approvalData?.notes ? approvalData.notes : ""}
            </EverTg.Description>
          </div>

          <div className="flex gap-3">
            <EverButton
              type="text"
              size="small"
              color={isCommAdj ? "primary" : "success"}
              icon={
                isCommAdj ? (
                  <CheckIcon className="w-5 h-5" />
                ) : (
                  <CheckCircleIcon className="w-4 h-4" />
                )
              }
              onClick={() => {
                if (!loading) {
                  const handleRequest = handleApproveRequestByEntityType;
                  approveConfirm(approvalData, handleRequest);
                }
              }}
            >
              Approve
            </EverButton>
            <EverButton
              type="text"
              size="small"
              color={isCommAdj ? "base" : "error"}
              icon={
                isCommAdj ? (
                  <SlashCircleIcon className="w-4 h-4" />
                ) : (
                  <XCircleIcon className="w-4 h-4" />
                )
              }
              onClick={() => {
                if (!loading) setModalVisibility(true);
              }}
            >
              Reject
            </EverButton>
          </div>
        </div>
      </EverCard>
      {modalVisibility &&
        (isCommAdj ? (
          <RejectApprovalModalCommAdj
            modalVisibility={modalVisibility}
            closeModal={() => setModalVisibility(false)}
            rejectModalData={approvalData}
            refetchData={refetchData}
            customCalendar={customCalendar}
            currencyCodeSymbolMap={store.currencyCodeSymbolMap}
            accessToken={accessToken}
            closeDrawer={closeDrawer}
          />
        ) : (
          <RejectApprovalModal
            modalVisibility={modalVisibility}
            closeModal={() => setModalVisibility(false)}
            rejectModalData={approvalData}
            store={store}
            refetchData={refetchData}
            refetchApprovalStatusData={refetchApprovalStatusData}
            closeDrawer={closeDrawer}
            customCalendar={customCalendar}
            payoutLevel={payoutLevel}
            rejectLineItemReq={rejectLineItemReq}
          />
        ))}
    </>
  );
};
export default ApprovalCard;
