import { useQuery } from "@apollo/client";
import React, { useMemo, useState, useRef } from "react";

import { addApprover } from "~/Api/ApprovalWorkflowService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { EverButton, EverLoader, EverModal, message } from "~/v2/components";
import {
  getRandomColor,
  APPROVAL_STAGES_ACTION,
  dynamicData,
  approversFieldAction,
} from "~/v2/features/approvals/approval-workflow-builder/helper";
import { ChooserDeletableCardView } from "~/v2/features/approvals/approval-workflow-builder/index";
import { MultiTabSelector } from "~/v2/features/approvals/approval-workflow-builder/MultiTabSelector";
import useOnClickOutside from "~/v2/features/approvals/approval-workflow-builder/useOnClickOutside";

import { GET_GROUPS_LIST } from "../../graphql";

const AddApproverModal = (props) => {
  const {
    stageOrder,
    addApproverModalVisible,
    setAddApproverModalVisible,
    stageId,
    entityKey,
    refetchTimelineData,
    refreshRequiredData,
  } = props;
  const { accessToken } = useAuthStore();
  const siderMuliTabSelectorRef = useRef(null);

  const [isChooseApproverSelected, setIsChooseApproverSelected] =
    useState(false);
  const [dynamicApprovers, setDynamicApprovers] = useState([]);
  const [userApprovers, setUserApprovers] = useState([]);
  const [groupApprovers, setGroupApprovers] = useState([]);
  const [loading, setLoading] = useState(false);
  const { rolesRefetch } = useEmployeeStore();

  const { data: fetchedGroupsData } = useQuery(GET_GROUPS_LIST, {
    fetchPolicy: "no-cache",
  });

  const groupsData = useMemo(() => {
    if (fetchedGroupsData?.userGroups) {
      return fetchedGroupsData.userGroups.map((group) => {
        return {
          label: group.userGroupName,
          text: group?.allMembers
            ? group.allMembers.length + " users"
            : "0 user",
          value: group.userGroupId,
          color: getRandomColor(),
        };
      });
    }
    return null;
  }, [fetchedGroupsData]);

  const handleOk = () => {
    let requestBody = {
      employeesObject: {
        dynamic: dynamicApprovers.map((ap) => ap.value),
        groups: groupApprovers.map((ap) => ap.value),
        users: userApprovers.map((ap) => ap.value),
      },
      stageId: stageId,
      entityKey: entityKey,
    };

    setLoading(true);
    message.loading({
      content: `Adding Approver`,
      key: "loading",
      duration: 0,
      icon: <EverLoader.SpinnerLottie className="w-10 h-10" />,
    });
    addApprover(requestBody, accessToken)
      .then((res) => {
        if (res.status == "SUCCESS") {
          message.success("Successfully added approvers!");
        } else {
          console.log("Error", res);
          message.error(res.message);
        }
      })
      .finally(() => {
        message.destroy("loading");
        setLoading(false);
        setAddApproverModalVisible(false);
        refetchTimelineData();
        rolesRefetch();
        if (refreshRequiredData) refreshRequiredData();
      });
  };

  const handleCancel = () => {
    setAddApproverModalVisible(false);
  };

  const disableOKButton = () => {
    if (
      dynamicApprovers.length == 0 &&
      groupApprovers.length == 0 &&
      userApprovers.length == 0
    ) {
      return true;
    }
    return false;
  };

  useOnClickOutside(siderMuliTabSelectorRef, () =>
    setStageData({
      type: APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_APPROVERS_SELECTED,
      payload: false,
    })
  );

  const setStageData = ({ type, payload }) => {
    switch (type) {
      case APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_APPROVERS_SELECTED: {
        return setIsChooseApproverSelected(payload);
      }
      case APPROVAL_STAGES_ACTION.ADD_DYNAMIC_APPROVERS: {
        return setDynamicApprovers((previousState) => [
          ...previousState,
          payload,
        ]);
      }
      case APPROVAL_STAGES_ACTION.ADD_GROUP_APPROVERS: {
        return setGroupApprovers((previousState) => [
          ...previousState,
          payload,
        ]);
      }
      case APPROVAL_STAGES_ACTION.ADD_USER_APPROVERS: {
        return setUserApprovers((previousState) => [...previousState, payload]);
      }
      case APPROVAL_STAGES_ACTION.REMOVE_DYNAMIC_APPROVERS: {
        dynamicApprovers.splice(payload, 1);
        return setDynamicApprovers([...dynamicApprovers]);
      }
      case APPROVAL_STAGES_ACTION.REMOVE_GROUP_APPROVERS: {
        groupApprovers.splice(payload, 1);
        return setGroupApprovers([...groupApprovers]);
      }
      case APPROVAL_STAGES_ACTION.REMOVE_USER_APPROVERS: {
        userApprovers.splice(payload, 1);
        return setUserApprovers([...userApprovers]);
      }
    }
    return null;
  };

  const setApproversData = (approvers, approverType, data) => {
    const index = approvers.map((e) => e.value).indexOf(data.value);
    if (index === -1) {
      setStageData({
        type: approversFieldAction[approverType].add,
        payload: data,
      });
    } else {
      setStageData({
        type: approversFieldAction[approverType].remove,
        payload: index,
      });
    }
  };

  return (
    <>
      <EverModal
        closable={false}
        title="Add approvers"
        visible={addApproverModalVisible}
        width={600}
        destroyOnClose={true}
        closeIcon={<></>}
        footer={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => {
              if (!loading) handleCancel();
            }}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="submit"
            onClick={handleOk}
            loading={loading}
            disabled={disableOKButton()}
          >
            Add Approvers
          </EverButton>,
        ]}
      >
        <div className="h-72 overflow-hidden">
          <MultiTabSelector
            siderMuliTabSelectorRef={siderMuliTabSelectorRef}
            isSelectorSelected={isChooseApproverSelected}
            setIsSelectorSelectedAction={
              APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_APPROVERS_SELECTED
            }
            setTemplateData={setStageData}
            setSelectedData={setApproversData}
            dynamicSourceData={
              stageOrder == 1
                ? dynamicData.filter(
                    (ele) => ele.value !== "previous_approver_manager"
                  )
                : dynamicData
            }
            dynamicSelectedData={dynamicApprovers}
            dynamicType={"dynamicApprovers"}
            groupSourceData={groupsData}
            groupSelectedData={groupApprovers}
            groupType={"groupApprovers"}
            userSelectedData={userApprovers}
            userType={"userApprovers"}
            placeholder="Choose Approvers"
            listHeightClass="h-36"
          />
          <div className="max-h-64 overflow-auto">
            <ChooserDeletableCardView
              placeholder="Choose Approvers"
              dynamicSelected={dynamicApprovers}
              usersSelected={userApprovers}
              groupsSelected={groupApprovers}
              isChoosenType={
                APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_APPROVERS_SELECTED
              }
              setData={setStageData}
              isChoosen={isChooseApproverSelected}
              dynamicType={APPROVAL_STAGES_ACTION.REMOVE_DYNAMIC_APPROVERS}
              usersType={APPROVAL_STAGES_ACTION.REMOVE_USER_APPROVERS}
              groupsType={APPROVAL_STAGES_ACTION.REMOVE_GROUP_APPROVERS}
            />
          </div>
        </div>
      </EverModal>
    </>
  );
};

export default AddApproverModal;
