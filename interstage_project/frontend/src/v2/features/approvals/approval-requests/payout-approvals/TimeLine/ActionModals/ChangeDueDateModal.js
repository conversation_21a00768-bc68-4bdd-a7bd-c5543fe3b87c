import moment from "moment";
import React, { useState } from "react";

import { changeDueDate } from "~/Api/ApprovalWorkflowService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverButton,
  EverDatePicker,
  EverForm,
  EverLoader,
  EverModal,
  EverTg,
  message,
} from "~/v2/components";
const { Item } = EverForm;
const dateFormat = "YYYY-MM-DD";

const ChangeDueDateModal = (props) => {
  const {
    duedateModalVisible,
    setDuedateModalVisible,
    dueDate,
    stageId,
    refetchTimelineData,
    refreshRequiredData,
    instanceCreationTime,
  } = props;
  const { accessToken } = useAuthStore();
  const [date, setDate] = useState(null);
  const [loading, setLoading] = useState(false);

  const disableOKButton = () => {
    if (!date || loading) {
      return true;
    }
    return false;
  };

  const onDateChange = (date) => {
    let dateString = moment(date).format(dateFormat);
    setDate(dateString);
  };

  const handleOk = () => {
    let requestBody = {
      stageId: stageId,
      dueDate: date,
    };
    setLoading(true);
    message.loading({
      content: `Updating due date...`,
      key: "loading",
      duration: 0,
      icon: <EverLoader.SpinnerLottie className="w-10 h-10" />,
    });
    changeDueDate(requestBody, accessToken)
      .then((res) => {
        if (res.status == "SUCCESS") {
          message.success("Due date updated successfully.");
        } else {
          console.log("Error", res);
          message.error(res.message);
        }
      })
      .finally(() => {
        message.destroy("loading");
        setLoading(false);
        setDuedateModalVisible(false);
        refetchTimelineData();
        refreshRequiredData();
      });
  };

  return (
    <div>
      <EverModal
        closable={false}
        visible={duedateModalVisible}
        width="440px"
        destroyOnClose={true}
        title="Change due date"
        footer={[
          <EverButton
            key="cancel"
            onClick={() => {
              if (!loading) setDuedateModalVisible(false);
            }}
            color="base"
          >
            Cancel
          </EverButton>,
          <EverButton
            key="update"
            onClick={handleOk}
            disabled={disableOKButton()}
          >
            Update due date
          </EverButton>,
        ]}
      >
        <div>
          <Item
            label={<EverTg.Text className="mt-1">Current due date</EverTg.Text>}
            name="currentDate"
          >
            <EverTg.SubHeading4 className="text-ever-base-content">
              {dueDate ? dueDate : "No Due Date set"}
            </EverTg.SubHeading4>
          </Item>
          <Item
            label={<EverTg.Text>New due date</EverTg.Text>}
            name="dueDate"
            rules={[
              {
                required: true,
                message: "Select due date!",
              },
            ]}
          >
            <EverDatePicker
              format={dateFormat}
              onChange={onDateChange}
              value={date}
              allowClear={false}
              disabledDate={(current) => {
                let startDate = moment().add(-1, "days");
                let endDate = moment().add(30, "days");
                return (
                  current < startDate ||
                  current > endDate ||
                  moment(current).format("YYYY-MM-DD") == instanceCreationTime
                );
              }}
            />
          </Item>
        </div>
      </EverModal>
    </div>
  );
};
export default ChangeDueDateModal;
