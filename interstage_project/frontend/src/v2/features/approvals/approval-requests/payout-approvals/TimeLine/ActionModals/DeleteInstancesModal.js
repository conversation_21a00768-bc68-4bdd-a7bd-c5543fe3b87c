import { useMutation } from "react-query";

import { deleteInstances } from "~/Api/ApprovalWorkflowService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverButton, EverModal, message } from "~/v2/components";

export function DeleteInstancesModal(props) {
  const { entityKey, setShowDeleteInstanceModal, onDeleteInstancSuccess } =
    props;
  const { accessToken } = useAuthStore();

  const deleteInstance = useMutation(
    (payload) => deleteInstances(payload, accessToken),
    {
      onError: (error) => {
        message.dismiss();
        const msg =
          error?.message ||
          "Could not process the request. Please try again later.";
        message.error(msg);
      },
      onSuccess: async (res) => {
        message.dismiss();
        message.success(res?.message || "You've deleted the instance(s)!");
        setShowDeleteInstanceModal();
        onDeleteInstancSuccess();
      },
    }
  );

  return (
    <EverModal.Confirm
      visible={true}
      type="warning"
      confirmationButtons={[
        <EverButton
          key="cancel"
          color="base"
          onClick={() => {
            setShowDeleteInstanceModal();
          }}
          type="ghost"
        >
          Cancel
        </EverButton>,
        <EverButton
          key="accept"
          onClick={async () => {
            message.loading("Deleting...");
            const payload = {
              entityKeys: [entityKey],
            };
            deleteInstance.mutate(payload);
          }}
        >
          Yes, delete
        </EverButton>,
      ]}
      iconContainerClasses="mb-4"
      icon={null}
      subtitle=""
      title={
        "This will delete all approval requests (ongoing and past) for this user. Are you sure to proceed?"
      }
    />
  );
}
