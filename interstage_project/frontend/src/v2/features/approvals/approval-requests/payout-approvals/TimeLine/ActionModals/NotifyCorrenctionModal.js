import React, { useState } from "react";
import { useMutation } from "react-query";

import { notifyCorrections } from "~/Api/ApprovalWorkflowService";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import {
  EverTg,
  EverButton,
  EverModal,
  EverInput,
  message,
} from "~/v2/components";

export function NotifyCorrectionModal({
  notifyModalVisibility,
  setNotifyModalVisibility,
  refetchTimelineData,
  accessToken,
  arRefetch,
  setShowApprovalBanner,
  entityKey,
  period,
  applyFiltersFun,
}) {
  const { rolesRefetch } = useEmployeeStore();
  const [notifyReason, setNotifyReason] = useState(null);
  const { mutate } = useMutation(
    (payload) => notifyCorrections(payload, accessToken),
    {
      onError: (error) => {
        console.log("error");
        message.dismiss();
        const msg =
          error?.message ||
          "Could not process the request. Please try again later.";
        message.error(msg);
        setNotifyModalVisibility(false);
      },
      onSuccess: (data) => {
        refetchTimelineData();
        rolesRefetch();
        setNotifyModalVisibility(false);
        setShowApprovalBanner && setShowApprovalBanner(true);
        arRefetch && arRefetch();
        applyFiltersFun && applyFiltersFun();
        message.dismiss();
        message.success(data?.message || "You've notified the corrections.");
      },
    }
  );

  return (
    <EverModal
      visible={notifyModalVisibility}
      title="Request approval for changes made"
      onCancel={() => {
        setNotifyModalVisibility(false);
      }}
      footer={
        <>
          <EverButton
            onClick={() => {
              setNotifyModalVisibility(false);
            }}
            color="base"
            type="ghost"
          >
            Cancel
          </EverButton>
          <EverButton
            key="submit"
            onClick={() => {
              message.loading("Requesting...");
              const payload = {
                entityKeys: [entityKey],
                periodEndDate: period,
                reason: notifyReason,
              };
              mutate(payload);
            }}
          >
            Send Request
          </EverButton>
        </>
      }
    >
      <div className="flex flex-col gap-2">
        <EverTg.Text className="text-ever-base-content-mid">
          This includes new line items added to statement and any change made to
          existing line items.
        </EverTg.Text>
        <EverTg.SubHeading4 className="text-ever-base-content">
          Note to approvers
        </EverTg.SubHeading4>
        <EverInput.TextArea
          rows={4}
          onChange={(e) => {
            setNotifyReason(e.target.value);
          }}
          maxLength="255"
        />
      </div>
    </EverModal>
  );
}
