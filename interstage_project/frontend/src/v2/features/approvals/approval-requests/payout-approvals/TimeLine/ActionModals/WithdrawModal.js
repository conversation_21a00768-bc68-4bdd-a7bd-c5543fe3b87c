import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { withdrawStage } from "~/Api/ApprovalWorkflowService";
import { LINE_ITEM_TYPE_ENTITY_TYPE } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { EverButton, EverLoader, EverModal, message } from "~/v2/components";

const WithdrawModal = (props) => {
  const {
    withdrawModalVisible,
    setWithdrawModalVisible,
    stageId,
    refetchTimelineData,
    refreshRequiredData,
    refetchApprovalStatusData,
    refreshData = null, //To refetch overview data of statement
    isLineItemLevel = false,
    handleWithdraw,
    entityType,
  } = props;
  const { accessToken } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const { rolesRefetch } = useEmployeeStore();

  const { t } = useTranslation();

  const handleOnClick = () => {
    let requestBody = {
      stageId: stageId,
    };
    if (isLineItemLevel) {
      requestBody.entityType = LINE_ITEM_TYPE_ENTITY_TYPE;
    }
    if (entityType && !isLineItemLevel) {
      requestBody.entityType = entityType;
    }
    setLoading(true);
    message.loading({
      content: `Withdrawing requests...`,
      key: "loading",
      duration: 0,
      icon: <EverLoader.SpinnerLottie className="w-10 h-10" />,
    });

    withdrawStage(requestBody, accessToken)
      .then((res) => {
        if (res.status == "SUCCESS") {
          message.success("Request withdrawn successfully.");
          handleWithdraw && handleWithdraw();
        } else {
          console.log("Error", res);
          message.error(res.message);
        }
      })
      .finally(() => {
        message.destroy("loading");
        setLoading(false);
        setWithdrawModalVisible(false);
        refetchTimelineData();
        rolesRefetch();
        handleWithdraw && handleWithdraw();
        if (refreshRequiredData) refreshRequiredData({ drawerClose: true });
        if (refetchApprovalStatusData) refetchApprovalStatusData();
        if (refreshData) refreshData();
      });
  };

  return (
    <EverModal.Confirm
      closable={false}
      destroyOnClose={true}
      visible={withdrawModalVisible}
      footer={null}
      title="Withdraw this approval request?"
      subtitle={t("NO_LONGER_APPROVE_OR_REJECT_PAYOUT")}
      type="warning"
      confirmationButtons={[
        <EverButton
          key="cancel"
          color="base"
          onClick={() => {
            if (!loading) setWithdrawModalVisible(false);
          }}
        >
          Cancel
        </EverButton>,
        <EverButton
          key="withdraw"
          color="error"
          onClick={handleOnClick}
          loading={loading}
        >
          Withdraw Request
        </EverButton>,
      ]}
    />
  );
};

export default WithdrawModal;
