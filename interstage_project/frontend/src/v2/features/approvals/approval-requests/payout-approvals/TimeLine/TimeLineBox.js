import {
  BellIcon,
  CalendarIcon,
  UserPlusIcon,
  ReverseLeftIcon,
  DotsVerticalIcon,
  CheckCircleBrokenIcon,
  InfoCircleIcon,
} from "@everstage/evericons/outlined";
import { CircleIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

import { sendReminder } from "~/Api/ApprovalWorkflowService";
import {
  APPROVAL_STAGE_STATUS,
  RBAC_ROLES,
  LINE_ITEM_TYPE_ENTITY_TYPE,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  ClickBoundary,
  EverBadge,
  EverButton,
  <PERSON>Card,
  EverDivider,
  EverHotToastBanner,
  <PERSON>List<PERSON><PERSON>,
  <PERSON>Loader,
  EverTg,
  EverTool<PERSON>,
  message,
  EverPopover,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";

import AddApproverModal from "./ActionModals/AddApprover";
import ChangeDueDateModal from "./ActionModals/ChangeDueDateModal";
import { DeleteInstancesModal } from "./ActionModals/DeleteInstancesModal";
import { NotifyCorrectionModal } from "./ActionModals/NotifyCorrenctionModal";
import WithdrawModal from "./ActionModals/WithdrawModal";
import { ApproverCard } from "./ApproverCard";
import { ENTITY_TYPES } from "../../../Constants";

const statusClasses =
  "w-4 h-4 absolute rounded-full border border-solid border-ever-base-400 -translate-x-1/2 -ml-px";

const renderInProgress = () => {
  return (
    <div className="absolute w-8 h-8 -translate-x-1/2 -translate-y-1/4 -ml-px">
      <div className="animate-ping absolute inline-flex h-full w-full rounded-full bg-ever-primary opacity-75"></div>
      <div
        className={twMerge(
          statusClasses,
          "bg-ever-primary top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 ml-0"
        )}
      ></div>
    </div>
  );
};

const renderRevoked = () => (
  <div className="absolute -translate-x-1/2 -ml-px bg-ever-error-lite rounded-full p-1">
    <ReverseLeftIcon className="w-4 h-4 text-ever-error" />
  </div>
);

const renderCompleted = () => (
  <div className={twMerge(statusClasses, "bg-ever-primary")}></div>
);
const renderNext = () => (
  <div className={twMerge(statusClasses, "bg-ever-base")}></div>
);
const renderFailure = () => (
  <div className={twMerge(statusClasses, "bg-ever-error")}></div>
);

const timeLineStatus = {
  completed: renderCompleted(),
  withdrawn: renderCompleted(),
  requested: renderInProgress(),
  started: renderNext(),
  not_started: renderNext(),
  rejected: renderFailure(),
  aborted: renderFailure(),
  revoked_title: renderRevoked(),
  revoked: renderCompleted(),
};

/**
 * @param {object} props
 * @returns {React.Component}
 * @description This component is used to render the time line box.
 */
export const TimeLineBox = (props) => {
  const {
    timeLineData,
    store,
    entityKey,
    refetchTimelineData,
    refreshRequiredData,
    refetchApprovalStatusData,
    refreshData,
    period,
    isLocked = false,
    handleWithdraw,
    arRefetch,
    setShowApprovalBanner,
    closeDrawer,
    applyFiltersFun,
    showDeleteButton,
  } = props;

  const { t } = useTranslation();
  const renderApprovalAbort = (data) => {
    const { approverData, previousStageStatus } = data;
    const { actionTime } = approverData[0];
    return (
      <>
        {previousStageStatus !== APPROVAL_STAGE_STATUS.REJECTED && (
          <EverHotToastBanner
            className="mx-8 my-6"
            type="warning"
            description={`Approval request was cancelled on ${actionTime}`}
          />
        )}
      </>
    );
  };

  const renderTimeLineEnd = (data, showTimeLineEnd, entityType) => {
    const { title, status, isLastStage } = data;
    return (
      <>
        {showTimeLineEnd ? (
          <div className="absolute -translate-x-1/2 bottom-0 w-1.5 h-1.5 rounded-full bg-ever-base-400"></div>
        ) : null}
        {status === APPROVAL_STAGE_STATUS.REJECTED && (
          <EverHotToastBanner
            className="mx-8 my-6"
            type="error"
            description={`${
              entityType === ENTITY_TYPES.COMMISSION_ADJUSTMENT_APPROVALS
                ? t("ADJUSTMENT")
                : t("PAYOUT").toLowerCase()
            } Rejected @ ${title}. You can address the feedback from reviewers and request approval again.`}
          />
        )}
        {isLastStage &&
          status === APPROVAL_STAGE_STATUS.ABORTED &&
          renderApprovalAbort(data)}
      </>
    );
  };

  const renderTimeLineBox = (data, index) => {
    const {
      status,
      approverData,
      instanceData,
      previousStageStatus,
      entityType,
    } = data;
    let timeLineClassess =
      "h-full relative border-solid border-0 border-l border-ever-base-400";
    if (
      status === APPROVAL_STAGE_STATUS.COMPLETED ||
      status === APPROVAL_STAGE_STATUS.REJECTED ||
      status === APPROVAL_STAGE_STATUS.WITHDRAWN
    ) {
      timeLineClassess = twMerge(timeLineClassess, "border-ever-primary");
    }

    const handleWithDrawForLastStage = () => {
      //reload the commission page when the approver withdraws the request in the last stage
      if (index === timeLineData.length - 1) {
        handleWithdraw && handleWithdraw();
      }
    };

    return (
      <div className={timeLineClassess} key={index}>
        {timeLineStatus[status]}
        <div className="pb-10">
          <RenderTimeLineHeader
            data={data}
            entityKey={entityKey}
            refetchTimelineData={refetchTimelineData}
            refreshRequiredData={refreshRequiredData}
            refetchApprovalStatusData={refetchApprovalStatusData}
            refreshData={refreshData}
            showNotifyCorrections={entityType === LINE_ITEM_TYPE_ENTITY_TYPE}
            entityType={entityType}
            handleWithdraw={handleWithDrawForLastStage}
            arRefetch={arRefetch}
            setShowApprovalBanner={setShowApprovalBanner}
            isLocked={isLocked}
            index={index}
            closeDrawer={closeDrawer}
            applyFiltersFun={applyFiltersFun}
            showDeleteButton={showDeleteButton}
            period={period}
          />
          {/*{renderTimeLineHeader(data)}*/}
          <ApproverCard
            stageId={data.stageId}
            stageStatus={status}
            period={period}
            store={store}
            approverData={approverData}
            instanceData={instanceData}
            previousStageStatus={previousStageStatus}
            refetchTimelineData={refetchTimelineData}
            entityType={entityType}
            applyFiltersFun={applyFiltersFun}
          />
          {renderTimeLineEnd(
            data,
            index === timeLineData.length - 1,
            entityType
          )}
        </div>
      </div>
    );
  };

  return timeLineData?.map(renderTimeLineBox);
};
/**
 * @param {object} props
 * @param {object} props.data - current stage details.
 * @param {string} props.entityKey - entity key of the request
 * @param {function} props.refetchTimelineData - refetch the timeline data
 * @param {function} props.refreshRequiredData - refresh required approval requests data .
 * @param {function} props.refetchApprovalStatusData - function to get approval status.
 * @param {function} props.refreshData - refresh data (GET_OVERVIEW graphql call)
 * @param {boolean} props.enableNotifyCorrections - boolean to enable/disable notify corrections
 * @param {boolean} props.showNotifyCorrections - boolean to show/hide notify corrections
 * @param {string} props.entityType - entity type of the payout approval
 * @param {function} props.handleWithdraw - function to withdraw stage request
 * @param {function} props.arRefetch - function to ar refecth.
 * @param {function} props.setShowApprovalBanner - boolean to show approval banner
 * @param {function} props.handleRequestReview - function to request review again.
 * @param {boolean} props.isLocked - boolean to check if payout is locked.
 * @param {number} props.index - index value of the current data.
 * @param {function} props.closeDrawer - handle to close the drawer in statement level approvals page.
 * @param {function} props.applyFiltersFun - handle to refresh payouts page data.
 * @param {boolean} props.showDeleteButton - boolean to show/hide delete button.
 * @param {boolean} props.period - current period.
 * @returns {React.Component}
 * @description This component is used to render the menu actions
 * available for current stage in the approval requests
 *
 */
const RenderTimeLineHeader = ({
  data,
  entityKey,
  refetchTimelineData,
  refreshRequiredData,
  refetchApprovalStatusData,
  refreshData,
  showNotifyCorrections,
  entityType,
  handleWithdraw,
  arRefetch,
  setShowApprovalBanner,
  isLocked,
  index,
  closeDrawer,
  applyFiltersFun,
  period,
  showDeleteButton = false,
}) => {
  const {
    title,
    updatedBy,
    status,
    actionTime,
    dueDate,
    numberOfDaysForDueDate,
    stageId,
    stageOrder,
    instanceCreationTimeUTC,
    approverData,
    notes,
  } = data;

  const { accessToken } = useAuthStore();
  const { rolesRefetch } = useEmployeeStore();

  const { hasPermissions } = useUserPermissionStore();
  const [openPopup, setOpenPopup] = useState(false);
  const [withdrawModalVisible, setWithdrawModalVisible] = useState(false);
  const [notifyModalVisibility, setNotifyModalVisibility] = useState(false);
  const [duedateModalVisible, setDuedateModalVisible] = useState(false);
  const [addApproverModalVisible, setAddApproverModalVisible] = useState(false);
  const [sendReminderLoading, setSendReminderLoading] = useState(false);
  const [showDeleteInstanceModal, setshowDeleteInstanceModal] = useState(false);

  const handleSendReminder = () => {
    let requestBody = {
      stageId: stageId,
    };

    setSendReminderLoading(true);
    const toastMessage = message.loading("Sending reminders...");
    sendReminder(requestBody, accessToken)
      .then((res) => {
        if (res.status === "SUCCESS") {
          message.dismiss(toastMessage);
          message.success("All reminders sent successfully");
        } else {
          if (res.message === "No approvers to remind") {
            message.dismiss(toastMessage);
            message.info("No pending requests to send a reminder");
          } else {
            console.log("Error", res);
            message.dismiss(toastMessage);
            message.error(res.message);
          }
        }
      })
      .finally(() => {
        message.dismiss(toastMessage);
        setSendReminderLoading(false);
        setOpenPopup(false);
        refetchTimelineData();
      });
  };

  const onDeleteInstancSuccess = () => {
    refetchTimelineData && refetchTimelineData();
    arRefetch && arRefetch();
    refreshRequiredData && refreshRequiredData();
    closeDrawer && closeDrawer();
    applyFiltersFun && applyFiltersFun();
    refetchApprovalStatusData && refetchApprovalStatusData();
    refreshData && refreshData();
    rolesRefetch();
  };

  const ContextMenu = () => {
    return (
      <EverCard
        paddingSize="sm"
        shadowSize="lg"
        className="flex flex-col gap-0.5"
      >
        <Menu className="!border-r-0">
          {hasPermissions(RBAC_ROLES.VIEW_REQUESTAPPROVALS) &&
            entityType !== ENTITY_TYPES.QUOTE_APPROVALS &&
            entityType !== ENTITY_TYPES.COMMISSION_PLAN_APPROVALS && (
              <Menu.Item
                className={`[&>div]:px-4 [&>div]:py-0 !my-0.5 !px-0 ${
                  !isLocked && `pointer-events-none`
                }`}
                // If the payout is unlocked, the user should not be able to add approver in the current stage
                disabled={!isLocked}
              >
                <EverListItem
                  selectable={true}
                  onClick={() => {
                    setAddApproverModalVisible(true);
                    setOpenPopup(false);
                  }}
                  prepend={
                    <UserPlusIcon
                      className={`w-5 h-5 -mt-3 ${!isLocked && `opacity-20`}`}
                    />
                  }
                  title="Add approver"
                  className="hover:text-ever-base-content"
                ></EverListItem>
              </Menu.Item>
            )}
          {hasPermissions([
            RBAC_ROLES.MANAGE_PAYOUTS,
            RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
          ]) && (
            <Menu.Item className="[&>div]:px-4 [&>div]:py-0 !my-0.5 !px-0">
              <EverListItem
                selectable={true}
                onClick={() => {
                  setDuedateModalVisible(true);
                  setOpenPopup(false);
                }}
                prepend={
                  <CalendarIcon
                    className="w-5 h-5 -mt-3"
                    data-testid="change-due-date"
                  />
                }
                title="Change due date"
                className="hover:text-ever-base-content"
              ></EverListItem>
            </Menu.Item>
          )}
          {hasPermissions([
            RBAC_ROLES.MANAGE_PAYOUTS,
            RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
          ]) &&
            entityType !== ENTITY_TYPES.COMMISSION_PLAN_APPROVALS && (
              <Menu.Item className="[&>div]:px-4 [&>div]:py-0 !my-0.5 !px-0">
                <EverListItem
                  selectable={true}
                  onClick={handleSendReminder}
                  prepend={
                    sendReminderLoading ? (
                      <EverLoader.SpinnerLottie className="w-6 h-6 -mt-7" />
                    ) : (
                      <BellIcon className="w-5 h-5 -mt-6" />
                    )
                  }
                  title={
                    <>
                      <EverTg.Text className="mr-2">
                        Send reminder to all
                      </EverTg.Text>
                      <EverTooltip title="Approvers will receive notifications regardless of their notification settings">
                        <InfoCircleIcon className="w-4 h-4" />
                      </EverTooltip>
                    </>
                  }
                  className="hover:text-ever-base-content"
                ></EverListItem>
              </Menu.Item>
            )}
          {showNotifyCorrections &&
            hasPermissions([RBAC_ROLES.VIEW_REQUESTAPPROVALS]) && (
              <Menu.Item
                className={`[&>div]:px-4 [&>div]:py-0 !my-0.5 !px-0 ${
                  !isLocked && `pointer-events-none`
                }`}
                // If none of the subrequests are in rejected status, the user should not be able to notify corrections.
                disabled={!isLocked}
              >
                <EverListItem
                  selectable={true}
                  onClick={() => {
                    setOpenPopup(false);
                    setNotifyModalVisibility(true);
                  }}
                  prepend={
                    <CheckCircleBrokenIcon
                      className={`w-5 h-5 -mt-3 ${!isLocked && `opacity-20`}`}
                    />
                  }
                  title="Request approval again"
                  className="hover:text-ever-base-content"
                ></EverListItem>
              </Menu.Item>
            )}
          {hasPermissions([
            RBAC_ROLES.MANAGE_PAYOUTS,
            RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
          ]) &&
            entityType !== ENTITY_TYPES.QUOTE_APPROVALS && (
              <>
                <EverDivider />

                <Menu.Item className="[&>div]:px-4 [&>div]:py-0 !my-0.5 !px-0">
                  <EverListItem
                    selectable={true}
                    onClick={() => {
                      setWithdrawModalVisible(true);
                      setOpenPopup(false);
                    }}
                    prepend={
                      <ReverseLeftIcon className="w-5 h-5 -mt-3 text-ever-error" />
                    }
                    title={
                      <span className="text-ever-error">
                        Withdraw pending requests
                      </span>
                    }
                  ></EverListItem>
                </Menu.Item>
              </>
            )}
        </Menu>
      </EverCard>
    );
  };

  const titleContainer =
    status === APPROVAL_STAGE_STATUS.REQUESTED
      ? `pl-6 mb-7 h-8 items-center`
      : `pl-6 mb-6 flex flex-col gap-2`;

  return (
    <div className={titleContainer}>
      <div className="flex items-center gap-2">
        <EverTg.Heading4
          className={
            title === "Approval request revoked" ? "text-ever-error" : ""
          }
        >
          {title}
        </EverTg.Heading4>
        {showDeleteButton &&
          hasPermissions([RBAC_ROLES.MANAGE_ALL_ADMINS]) &&
          index == 0 && (
            <div className="flex flex-col gap-1 ml-auto">
              <EverButton
                color="error"
                onClick={() => {
                  setshowDeleteInstanceModal(true);
                }}
              >
                Delete all requests
              </EverButton>
            </div>
          )}
        {notes && (
          <EverPopover
            showArrow={true}
            content={
              <div className="w-[350px] min-h-auto max-h-52 bg-ever-base break-words overflow-hidden overflow-y-auto">
                <EverTg.Caption>{notes}</EverTg.Caption>
              </div>
            }
            showCloseIcon={false}
            arrowClass="stroke-ever-base-400 stroke-2"
            className="border border-solid border-ever-base-400 p-2 shadow-none"
            side="bottom"
            alignOffset={-20}
          >
            <div>
              <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid cursor-pointer" />
            </div>
          </EverPopover>
        )}
        <RBACProtectedComponent
          permissionId={[
            RBAC_ROLES.MANAGE_PAYOUTS,
            RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT,
            RBAC_ROLES.VIEW_REQUESTAPPROVALS,
          ]}
          placeholderComponent={null}
        >
          {status === APPROVAL_STAGE_STATUS.REQUESTED ? (
            <ClickBoundary onClickOutside={() => setOpenPopup(false)}>
              <div className="flex">
                <Dropdown
                  overlay={<ContextMenu />}
                  trigger={["click"]}
                  placement="bottomLeft"
                  visible={openPopup}
                  className="text"
                  getPopupContainer={(trigger) => trigger.parentNode}
                  onClick={() => {
                    //event.stopPropagation();
                    setOpenPopup(!openPopup);
                  }}
                >
                  <DotsVerticalIcon
                    onClick={() => setOpenPopup(true)}
                    className="w-3 h-3 cursor-pointer"
                    data-testid="approval-request-dropdown"
                  />
                </Dropdown>
              </div>
            </ClickBoundary>
          ) : null}
        </RBACProtectedComponent>
      </div>
      {notifyModalVisibility && (
        <NotifyCorrectionModal
          setNotifyModalVisibility={setNotifyModalVisibility}
          notifyModalVisibility={notifyModalVisibility}
          refetchTimelineData={refetchTimelineData}
          accessToken={accessToken}
          arRefetch={arRefetch}
          setShowApprovalBanner={setShowApprovalBanner}
          entityKey={entityKey}
          period={period}
          applyFiltersFun={applyFiltersFun}
        />
      )}
      {withdrawModalVisible && (
        <WithdrawModal
          withdrawModalVisible={withdrawModalVisible}
          setWithdrawModalVisible={setWithdrawModalVisible}
          status={status}
          stageId={stageId}
          refetchTimelineData={refetchTimelineData}
          refreshRequiredData={refreshRequiredData}
          refetchApprovalStatusData={refetchApprovalStatusData}
          refreshData={refreshData}
          isLineItemLevel={entityType === LINE_ITEM_TYPE_ENTITY_TYPE}
          handleWithdraw={handleWithdraw}
          entityType={entityType}
        />
      )}
      {duedateModalVisible && (
        <ChangeDueDateModal
          duedateModalVisible={duedateModalVisible}
          setDuedateModalVisible={setDuedateModalVisible}
          instanceCreationTime={instanceCreationTimeUTC}
          dueDate={dueDate}
          stageId={stageId}
          refetchTimelineData={refetchTimelineData}
          refreshRequiredData={refreshRequiredData}
        />
      )}
      {addApproverModalVisible && (
        <AddApproverModal
          addApproverModalVisible={addApproverModalVisible}
          setAddApproverModalVisible={setAddApproverModalVisible}
          stageId={stageId}
          stageOrder={stageOrder}
          entityKey={entityKey}
          refetchTimelineData={refetchTimelineData}
          refreshRequiredData={refreshRequiredData}
        />
      )}
      {showDeleteInstanceModal && (
        <DeleteInstancesModal
          entityKey={entityKey}
          setShowDeleteInstanceModal={() => setshowDeleteInstanceModal(false)}
          onDeleteInstancSuccess={onDeleteInstancSuccess}
        />
      )}
      <EverTg.Description className={approverData.length ? " !mt-0" : ""}>
        {actionTime && <span>On {actionTime}</span>}
        {updatedBy && <span> by {`${updatedBy}`} </span>}
        {dueDate && status == APPROVAL_STAGE_STATUS.REQUESTED && (
          <div className="flex items-center gap-2 mt-2">
            {numberOfDaysForDueDate >= 0 ? (
              <>
                <EverBadge
                  className="font-medium"
                  title="On track"
                  type="success"
                  icon={<CircleIcon className="w-1.5 h-1.5" />}
                />
                <EverTg.Text className="text-ever-success-lite-content">
                  Due in {numberOfDaysForDueDate} day(s),
                </EverTg.Text>
                <EverTg.Text>{dueDate}.</EverTg.Text>
              </>
            ) : numberOfDaysForDueDate < 0 ? (
              <>
                <EverBadge
                  className="font-medium"
                  title="Overdue"
                  type="error"
                  icon={<CircleIcon className="w-1.5 h-1.5" />}
                />
                <EverTg.Text className="text-ever-error-lite-content">
                  Overdue by {Math.abs(numberOfDaysForDueDate)} day(s),
                </EverTg.Text>
                <EverTg.Text>{dueDate}.</EverTg.Text>
              </>
            ) : null}
          </div>
        )}
      </EverTg.Description>
    </div>
  );
};
