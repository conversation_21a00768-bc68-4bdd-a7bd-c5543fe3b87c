import { useQuery } from "@apollo/client";
import { CheckCircleIcon } from "@everstage/evericons/duotone";
import React, { useState, useEffect } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { parse } from "valibot";

import {
  getApprovalConfig,
  getCommAdjTimelineDetails,
} from "~/Api/ApprovalWorkflowService";
import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverLoader,
  EverButton,
  toast,
  EverHotToastMessage,
  EverTg,
  message,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";

import { TIMELINE_DATA } from "./graphql";
import { TimeLine } from "./TimeLine";
import { ENTITY_TYPES } from "../../Constants";
import { commAdjTimelineSchema } from "../payout-approvals-schema";

export const TimelineWrapper = (props) => {
  const {
    entityKey,
    store,
    refreshRequiredData,
    refetchTimelineDataFromProps,
    onRequestApproval,
    refetchApprovalStatusData,
    refreshData,
    period,
    isLocked = false,
    handleWithdraw,
    arRefetch,
    setShowApprovalBanner,
    entityType,
    closeDrawer,
    applyFiltersFun,
    showDeleteButton,
    stageInstanceId = null,
    workFlowInstanceId = null,
  } = props;
  const { accessToken } = useAuthStore();
  const [timelineData, setTimelineData] = useState([]);
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const [payoutApprovalsEnabled, setPayoutApprovalsEnabled] = useState(false);
  const isCommAdjTimeLine =
    entityType === ENTITY_TYPES.COMMISSION_ADJUSTMENT_APPROVALS ? true : false;

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      setPayoutApprovalsEnabled(config?.payoutApprovals?.enabled || false);
    }
  }, [approvalConfigData]);

  const {
    data: tlData,
    loading: tdLoading,
    refetch: refetchTimelineData,
  } = useQuery(TIMELINE_DATA, {
    variables: {
      entityKey: entityKey,
      refetchTimelineDataFromProps: refetchTimelineDataFromProps,
      stageInstanceId: stageInstanceId,
      workFlowInstanceId: workFlowInstanceId,
    },
    notifyOnNetworkStatusChange: true,
    fetchPolicy: "no-cache",
  });

  function commAdjTimelineDetailsGetter() {
    return new Promise((resolve, reject) => {
      getCommAdjTimelineDetails(entityKey, accessToken).then((response) => {
        if (response.ok) {
          response.json().then((data) => {
            if (data?.data?.message) {
              setTimelineData(data.data);
            } else {
              const parsedData = parse(commAdjTimelineSchema, data.data);
              setTimelineData(parsedData);
            }
          });
          resolve();
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({
                content: "Some error occured while fetching Timeline Data",
              });
            }
          });
          reject();
        }
      });
    });
  }

  useEffect(() => {
    isCommAdjTimeLine && commAdjTimelineDetailsGetter();
    store?.setTimeLineRefetch(
      isCommAdjTimeLine ? commAdjTimelineDetailsGetter : refetchTimelineData
    );
  }, []);

  useEffect(() => {
    if (!isCommAdjTimeLine && tlData && tlData.getTimelineDetails) {
      let tData = JSON.parse(tlData?.getTimelineDetails?.data);
      if (tData && "status" in tData && tData.status === "Error") {
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description="Some error occured while fetching Timeline Data"
            />
          ),
          { position: "top-center" }
        );
      } else {
        setTimelineData(tData);
      }
    }
  }, [tlData]);

  return (
    <div
      className={`spin-sticky-center${
        timelineData && timelineData.length == 0
          ? " absolute inset-0 max-w-xs m-auto h-6"
          : ""
      }`}
    >
      <EverLoader
        indicatorType="spinner"
        spinning={tdLoading}
        wrapperClassName="h-[120px]"
      >
        {timelineData?.status === "Success" ? (
          <AutoApprovedComponent details={timelineData} />
        ) : timelineData && timelineData.length !== 0 ? (
          <TimeLine
            timelineData={timelineData}
            store={store}
            entityKey={entityKey}
            refetchTimelineData={
              isCommAdjTimeLine
                ? commAdjTimelineDetailsGetter
                : refetchTimelineData
            }
            refreshRequiredData={refreshRequiredData}
            refetchApprovalStatusData={refetchApprovalStatusData}
            refreshData={refreshData}
            period={period}
            isLocked={isLocked}
            handleWithdraw={handleWithdraw}
            arRefetch={arRefetch}
            setShowApprovalBanner={setShowApprovalBanner}
            entityType={entityType}
            closeDrawer={closeDrawer}
            applyFiltersFun={applyFiltersFun}
            showDeleteButton={showDeleteButton}
          />
        ) : (
          <center>
            <EverTg.SubHeading3>
              No approval requests for this statement
            </EverTg.SubHeading3>
            {clientFeatures.showApprovalFeature && payoutApprovalsEnabled && (
              <RBACProtectedComponent
                permissionId={RBAC_ROLES.VIEW_REQUESTAPPROVALS}
              >
                <EverButton className="mt-2" onClick={onRequestApproval}>
                  Request Approval
                </EverButton>
              </RBACProtectedComponent>
            )}
          </center>
        )}
      </EverLoader>
    </div>
  );
};

function AutoApprovedComponent({ details }) {
  return (
    <div className="w-full flex justify-center mt-20">
      <div className="flex flex-col items-center gap-4">
        <CheckCircleIcon className="w-16 h-16 text-ever-success" />
        <div className="flex flex-col gap-2 items-center">
          <EverTg.Heading2>Auto Approved</EverTg.Heading2>
          <EverTg.Description>{details.message}</EverTg.Description>
          {details.isApprovalSkipped && (
            <div className="flex flex-col items-center">
              <EverTg.SubHeading3>
                Reason for skipping approval:
              </EverTg.SubHeading3>
              <EverTg.Description>{details.skipReason}</EverTg.Description>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
