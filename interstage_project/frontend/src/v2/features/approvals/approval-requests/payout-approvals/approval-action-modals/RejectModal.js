import { UserIcon } from "@everstage/evericons/outlined";
// eslint-disable-next-line no-restricted-imports
import { Avatar } from "antd";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useState } from "react";

import {
  PAYOUT_APPROVAL_TYPES,
  DATE_FORMATS,
  APPROVAL_ENTITY_TYPES,
} from "~/Enums";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import {
  EverButton,
  EverInput,
  EverLabel,
  EverListItem,
  EverModal,
  EverTg,
  useCurrentTheme,
  message,
  EverTooltip,
} from "~/v2/components";
import { getCurrencyValue } from "~/v2/features/commissions/constants";

import { processErrorMessage } from "../Common";

const { DDMMMYYYY, MMMYYYY } = DATE_FORMATS;

const RejectApprovalModal = observer((props) => {
  const {
    modalVisibility,
    closeModal,
    rejectModalData,
    store,
    refetchData,
    refetchApprovalStatusData = null,
    closeDrawer = null,
    customCalendar,
    payoutLevel = PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL,
    rejectLineItemReq,
    entityType = APPROVAL_ENTITY_TYPES.PAYOUT,
    setApprovalSummaryVisibility = null,
  } = props;

  const [rejectionComment, setRejectionComment] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [rejectLoading, setRejectLoading] = useState(false);
  const [rejectBtnVisibility, setRejectBtnVisibility] = useState(false);

  const { rolesRefetch } = useEmployeeStore();
  const { rejectApprovalRequest, currencyCodeSymbolMap } = store;
  const handleOnOk = async () => {
    if (!rejectionComment || rejectionComment.trim() === "") {
      setErrorMessage("Please add comment.");
    } else {
      if (payoutLevel === PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL) {
        setRejectBtnVisibility(true);
        setRejectLoading(true);
        rejectApprovalRequest({
          requestId: [rejectModalData?.approvalRequestId],
          comments: rejectionComment,
        })
          .then((res) => {
            if (res?.status !== "SUCCESS") {
              console.log("Error", res.message);
              processErrorMessage(res.message);
            } else {
              message.success(`You've rejected the request.`);
            }
          })
          .catch((err) => {
            console.log(err);
            message.error("Some error occured.");
          })
          .finally(() => {
            setRejectLoading(false);
            setErrorMessage("");
            setRejectionComment("");
            refetchData();
            closeModal();
            rolesRefetch();
            setRejectBtnVisibility(false);
            if (refetchApprovalStatusData) refetchApprovalStatusData();
            if (closeDrawer) closeDrawer();
            if (setApprovalSummaryVisibility) {
              setApprovalSummaryVisibility(false);
            }
          });
      } else {
        if (payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL) {
          setRejectLoading(true);
          const payload = {
            requestIds: [rejectModalData?.approvalRequestId],
            rejectionReason: rejectionComment,
          };
          message.loading("Rejecting...");
          await rejectLineItemReq(payload);
          setRejectLoading(false);
          setErrorMessage("");
          setRejectionComment("");
        }
      }
    }
  };

  const { chartColors } = useCurrentTheme();

  return (
    <>
      <EverModal
        closable={false}
        title="Reject approval request"
        visible={modalVisibility}
        confirmLoading={rejectLoading}
        width={600}
        footer={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => {
              if (!rejectLoading) closeModal();
            }}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="submit"
            color="error"
            onClick={handleOnOk}
            disabled={rejectBtnVisibility}
          >
            Reject Request
          </EverButton>,
        ]}
      >
        <div className="flex gap-3 items-center">
          <div className="w-1/2">
            <EverListItem
              prepend={
                <Avatar
                  size="large"
                  style={{ backgroundColor: chartColors[4] + "3A" }}
                  icon={
                    rejectModalData?.profilePicture || (
                      <UserIcon className="w-5 h-5 mt-2 ml-2.5 text-ever-chartColors-4" />
                    )
                  }
                />
              }
              title={rejectModalData?.fullName}
              subtitle={rejectModalData?.employeeEmailId}
            />
          </div>
          <div className="w-1/4">
            <div className="flex flex-col gap-1">
              <EverLabel>
                {entityType == APPROVAL_ENTITY_TYPES.QUOTE
                  ? "Quote Total"
                  : "Amount"}
              </EverLabel>
              <EverTg.SubHeading3 className="text-ever-base-content">
                <div
                  className={
                    rejectModalData?.payoutAmount === "-"
                      ? "blur-sm select-none"
                      : ""
                  }
                >
                  <EverTooltip
                    title={
                      rejectModalData?.payoutAmount === "-"
                        ? "You don't have permission to view this data"
                        : ""
                    }
                  >
                    {getCurrencyValue(
                      currencyCodeSymbolMap[rejectModalData?.currency],
                      rejectModalData?.payoutAmount
                    )}
                  </EverTooltip>
                </div>
              </EverTg.SubHeading3>
            </div>
          </div>
          {entityType !== APPROVAL_ENTITY_TYPES.QUOTE && (
            <div className="w-1/4">
              <div className="flex flex-col gap-1">
                <EverLabel>Period</EverLabel>
                <EverTg.SubHeading3 className="text-ever-base-content">
                  {customCalendar
                    ? moment(rejectModalData?.period).format(DDMMMYYYY)
                    : moment(rejectModalData?.period).format(MMMYYYY)}
                </EverTg.SubHeading3>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-2 mt-3">
          <EverLabel required={true}>Comments</EverLabel>
          <EverInput.TextArea
            className="resize-none"
            value={rejectionComment}
            onChange={(e) => setRejectionComment(e.target.value)}
            rows="4"
            placeholder="Add your comments"
            maxLength="250"
          />
          {errorMessage && (
            <span className="text-ever-error">{errorMessage}</span>
          )}
        </div>
      </EverModal>
    </>
  );
});

export default RejectApprovalModal;
