import { ChevronLeftIcon } from "@everstage/evericons/outlined";
import React, { useState } from "react";

import { EverButton, EverModal, message } from "~/v2/components";

import { constructDataToSend, isWorkflowValid } from "./helper";
import { NameDescription } from "./NameDescription";

export const ApprovalTemplateFooter = (props) => {
  const {
    showBackToWorkflows,
    showSaveAsTemplate,
    showInitiateApproval,
    setApprovalWorkflowData,
    name,
    description,
    handleCancel,
    ValidateAndSave,
    onSendApprovalRequest,
    requestParams,
    onCompleteFunction,
    approvalWorkflowData,
    mode,
    approvalConfigData,
    type,
  } = props;

  const [showNameDescriptionModal, setShowNameDescriptionModal] =
    useState(false);
  return (
    <>
      <div className="flex justify-between w-full">
        <div className="flex">
          {showBackToWorkflows && (
            <EverButton
              type="link"
              onClick={handleCancel}
              prependIcon={<ChevronLeftIcon />}
            >
              back to workflows
            </EverButton>
          )}
          {showSaveAsTemplate && (
            <EverButton
              type="link"
              onClick={() => {
                setShowNameDescriptionModal(true);
              }}
            >
              Save as template
            </EverButton>
          )}
        </div>
        <div className="flex">
          <EverButton type="ghost" color="base" onClick={handleCancel}>
            Cancel
          </EverButton>
          {showInitiateApproval ? (
            <EverButton
              onClick={() =>
                typeof onCompleteFunction == "function"
                  ? isWorkflowValid(approvalWorkflowData, type)
                    ? onCompleteFunction(
                        constructDataToSend(approvalWorkflowData, mode)
                      )
                    : message.error("Please fill all mandatory fields.")
                  : onSendApprovalRequest()
              }
              disabled={showNameDescriptionModal ? true : false}
            >
              {" "}
              Send Approval Request
            </EverButton>
          ) : (
            <EverButton onClick={ValidateAndSave}>Save Workflow</EverButton>
          )}
        </div>
      </div>
      <EverModal
        title="Request Approval"
        visible={showNameDescriptionModal}
        onCancel={() => {
          setShowNameDescriptionModal(false);
        }}
        footer={[
          <EverButton
            key="cancel"
            type="ghost"
            color="base"
            onClick={() => {
              setShowNameDescriptionModal(false);
            }}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="save"
            onClick={() => {
              setShowNameDescriptionModal(false);
              ValidateAndSave();
            }}
          >
            Save as Template
          </EverButton>,
        ]}
      >
        <NameDescription
          setApprovalWorkflowData={setApprovalWorkflowData}
          name={name}
          description={description}
          requestParams={requestParams}
          approvalConfigData={approvalConfigData}
        />
      </EverModal>
    </>
  );
};
