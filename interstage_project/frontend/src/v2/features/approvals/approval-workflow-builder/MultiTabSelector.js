import { gql } from "@apollo/client";
import { UserIcon, CheckIcon } from "@everstage/evericons/outlined";
// eslint-disable-next-line no-restricted-imports
import { Avatar, List } from "antd";
import { isNil, isEmpty } from "lodash";
import React, { useRef } from "react";

import {
  LazyListContent,
  useAbortiveLazyQuery,
  EverTabs,
  useCurrentTheme,
  EverTg,
  EverInput,
  EverListItem,
} from "~/v2/components";
import { emptyFace } from "~/v2/images";
import EverAvatar from "~/v2/legacy/components/EverAvatar";

const EmptyFaceState = (props) => {
  const { description } = props;
  return (
    <span className="flex flex-col items-center mt-10">
      <img src={emptyFace} />
      <EverTg.SubHeading3 className="mt-8">{description}</EverTg.SubHeading3>
    </span>
  );
};

const SelectableCard = ({ data, setData, isSelected, avatar }) => {
  const { chartColors } = useCurrentTheme();
  const prepend = avatar || (
    <Avatar size="medium" style={{ backgroundColor: chartColors[4] + "3A" }}>
      {`${data.label.split(" ")[0].charAt(0).toUpperCase()}${data.label
        .split(" ")[1]
        .charAt(0)
        .toUpperCase()}`}
    </Avatar>
  );
  return (
    <EverListItem
      key={data.value}
      onSelect={setData}
      prepend={prepend}
      title={data.label}
      subtitle={data.text}
      selectable
      append={
        isSelected ? <CheckIcon className="w-5 h-5 text-ever-primary" /> : null
      }
    />
  );
};

const LazyFetchUsers = (props) => {
  const { userSelectedData, setData, userType, listHeightClass } = props;
  const GET_USERS = gql`
    query AllEmployees(
      $userStatus: String
      $searchTerm: String
      $offsetValue: Int
      $limitValue: Int
      $context: String
    ) {
      employeeProfileDetailsPayoutModule(
        userStatus: $userStatus
        searchTerm: $searchTerm
        offsetValue: $offsetValue
        limitValue: $limitValue
        context: $context
      ) {
        employeeEmailId
        fullName
        profilePicture
      }
    }
  `;
  const initialLoadRef = useRef(true);
  const [getUsers, { variables, abort, data }] = useAbortiveLazyQuery(
    GET_USERS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const response = data?.employeeProfileDetailsPayoutModule;
        initialLoadRef.current = false;
        if (isNil(response)) {
          variables.failureCbk();
        } else {
          const formattedResponse = response.map((option) => {
            return {
              label: option.fullName,
              value: option.employeeEmailId,
              text: option.employeeEmailId,
              profilePicture: option.profilePicture,
            };
          });
          variables.successCbk(formattedResponse);
        }
      },
      onError: () => {
        variables.failureCbk();
      },
    }
  );

  const usersLazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { searchTerm, offset, limit } = params;
      await getUsers({
        ...params,
        userStatus: "Active",
        context: "notify",
        searchTerm,
        offsetValue: offset,
        limitValue: limit,
      });
    },
  };

  return (
    <LazyListContent
      listHeight={340}
      skipSelectAll
      showSearch={
        initialLoadRef.current &&
        isEmpty(data?.employeeProfileDetailsPayoutModule)
          ? false
          : true
      }
      noDataText={<EmptyFaceState description="No users were found!" />}
      listItemRenderer={(option) => (
        <SelectableCard
          isSelected={userSelectedData.some((e) => e.value === option.value)}
          data={option}
          setData={() => setData(userSelectedData, userType, option)}
          avatar={
            <EverAvatar
              size={32}
              width={32}
              name={option.label}
              round={true}
              src={option.profilePicture}
            />
          }
        />
      )}
      listItemHeight={72}
      listHeightClass={listHeightClass}
      {...usersLazyLoadProps}
    />
  );
};

export const MultiTabSelector = (props) => {
  const {
    isSelectorSelected,
    setIsSelectorSelectedAction,
    setTemplateData,
    dynamicSourceData,
    dynamicDataOnNotify,
    dynamicDataOnNotifyForRevoked,
    notifyType,
    dynamicSelectedData,
    setSelectedData,
    groupSourceData,
    groupSelectedData,
    userSelectedData,
    userType,
    dynamicType,
    groupType,
    placeholder,
    isSelectorUnSelected,
    siderMuliTabSelectorRef,
    listHeightClass,
  } = props;

  const { chartColors } = useCurrentTheme();

  return (
    <div className="relative" ref={siderMuliTabSelectorRef}>
      <EverInput
        placeholder={placeholder}
        onKeyPress={(e) => (e.preventDefault(), e.stopPropagation())}
        onPaste={(e) => (e.preventDefault(), e.stopPropagation())}
        onClick={() =>
          setTemplateData({
            type: setIsSelectorSelectedAction,
            payload: !isSelectorUnSelected,
          })
        }
      />
      {isSelectorSelected ? (
        <div className="absolute top-11 left-0 bg-ever-base z-20 w-2/3 rounded-lg min-w-fit shadow-lg">
          <EverTabs>
            <EverTabs.TabPane tab="Dynamic" key="dynamic" className="p-2">
              <List
                itemLayout="horizontal"
                dataSource={
                  notifyType === "revoke"
                    ? dynamicDataOnNotifyForRevoked
                    : dynamicDataOnNotify || dynamicSourceData
                }
                renderItem={(data) => (
                  <SelectableCard
                    key={data.value}
                    data={data}
                    isSelected={dynamicSelectedData.some(
                      (e) => e.value === data.value
                    )}
                    setData={() =>
                      setSelectedData(dynamicSelectedData, dynamicType, data)
                    }
                    avatar={
                      <Avatar
                        className="flex items-center justify-center flex-wrap"
                        size="medium"
                        style={{ backgroundColor: chartColors[4] + "3A" }}
                        icon={
                          <UserIcon
                            className="size-2/3"
                            style={{ color: chartColors[4] }}
                          />
                        }
                      />
                    }
                  />
                )}
              />
            </EverTabs.TabPane>
            <EverTabs.TabPane tab="Users" key="users">
              <LazyFetchUsers
                userSelectedData={userSelectedData}
                userType={userType}
                setData={setSelectedData}
                listHeightClass={listHeightClass}
              />
            </EverTabs.TabPane>
            <EverTabs.TabPane tab="Groups" key="groups">
              {groupSourceData ? (
                <List
                  itemLayout="horizontal"
                  dataSource={groupSourceData}
                  renderItem={(data) => (
                    <SelectableCard
                      data={data}
                      isSelected={groupSelectedData.some(
                        (e) => e.value === data.value
                      )}
                      setData={() =>
                        setSelectedData(groupSelectedData, groupType, data)
                      }
                      avatar={
                        <Avatar
                          className="flex items-center justify-center flex-wrap"
                          size="medium"
                          style={{ backgroundColor: chartColors[4] + "3A" }}
                          icon={
                            <UserIcon
                              className="size-2/3"
                              style={{ color: chartColors[4] }}
                            />
                          }
                        />
                      }
                    />
                  )}
                />
              ) : (
                <EmptyFaceState
                  description={"No user groups have been setup!"}
                />
              )}
            </EverTabs.TabPane>
          </EverTabs>
        </div>
      ) : null}
    </div>
  );
};
