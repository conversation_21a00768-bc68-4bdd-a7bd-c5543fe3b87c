import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { EverInput, EverLabe<PERSON>, EverSelect } from "~/v2/components";

import { APPROVAL_STAGES_ACTION } from "./helper";
import { ENTITY_TYPES } from "../Constants";

export const NameDescription = (props) => {
  const {
    setApprovalWorkflowData,
    name,
    description,
    requestParams,
    approvalConfigData,
    entityType,
    approvalWorkflowData,
  } = props;
  const { t } = useTranslation();

  function calculateModuleOptions() {
    // {
    //   value: "datasheet_adjustment",
    //   label: "Datasheet Adjustment",
    // },
    const options = [];
    approvalConfigData?.payoutApprovals?.enabled &&
      options.push({
        value: ENTITY_TYPES.PAYOUT_APPROVALS,
        label: t("PAYOUTS"),
      });
    approvalConfigData?.commissionAdjustmentApprovals?.enabled &&
      options.push({
        value: ENTITY_TYPES.COMMISSION_ADJUSTMENT_APPROVALS,
        label: t("COMMISSION_ADJUSTMENTS"),
      });
    approvalConfigData?.commissionPlanApprovals?.enabled &&
      options.push({
        value: ENTITY_TYPES.COMMISSION_PLAN_APPROVALS,
        label: t("COMMISSION_PLANS"),
      });

    return options;
  }

  useEffect(() => {
    requestParams &&
      requestParams.entity_type &&
      setApprovalWorkflowData({
        type: APPROVAL_STAGES_ACTION.ENTITY_CHANGE,
        payload: requestParams.entity_type,
      });
    entityType &&
      setApprovalWorkflowData({
        type: APPROVAL_STAGES_ACTION.ENTITY_CHANGE,
        payload: entityType,
      });
  }, []);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <EverLabel required={true}>Workflow name</EverLabel>
        <EverInput
          placeholder="Enter workflow name"
          value={name}
          maxLength={80}
          onChange={(e) => {
            setApprovalWorkflowData({
              type: APPROVAL_STAGES_ACTION.SET_WORFLOW_NAME,
              payload: e.target.value,
            });
          }}
        />
      </div>
      <div className="flex flex-col gap-2">
        <EverLabel required={true}>Modules</EverLabel>
        <EverSelect
          placeholder="Enter module name"
          value={approvalWorkflowData?.entityType}
          options={calculateModuleOptions()}
          onChange={(value) => {
            setApprovalWorkflowData({
              type: APPROVAL_STAGES_ACTION.ENTITY_CHANGE,
              payload: value,
            });
          }}
        />
      </div>
      <div className="flex flex-col gap-2">
        <EverLabel>Description</EverLabel>
        <EverInput.TextArea
          placeholder="Enter description"
          maxLength="255"
          autoSize={{ minRows: 4 }}
          value={description}
          onChange={(e) => {
            setApprovalWorkflowData({
              type: APPROVAL_STAGES_ACTION.SET_WORFLOW_DESCRIPTION,
              payload: e.target.value,
            });
          }}
        />
      </div>
    </div>
  );
};
