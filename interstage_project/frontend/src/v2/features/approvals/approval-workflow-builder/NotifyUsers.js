import React, { useRef, useCallback } from "react";

import { EverLabel } from "~/v2/components/EverLabel";
import { ChooserDeletableCardView } from "~/v2/features/approvals/approval-workflow-builder/index";
import { MultiTabSelector } from "~/v2/features/approvals/approval-workflow-builder/MultiTabSelector";

import {
  APPROVAL_STAGES_ACTION,
  dynamicData,
  dynamicDataOnNotify,
  commissionPlanDynamicDataOnNotify,
  otherModuleDynamicDataOnNotify,
  dynamicDataOnNotifyForRevoked,
} from "./helper";
import useOnClickOutside from "./useOnClickOutside.js";
import { ENTITY_TYPES } from "../Constants.js";

export const NotifyUsers = (props) => {
  const {
    title,
    notifyType,
    approvalWorkflowData,
    setApprovalWorkflowData,
    setNotifyWhenRejectedData,
    groupsData,
  } = props;
  const siderMuliTabSelectorRef1 = useRef(null);

  let dynamicSelectedData;
  let groupSelectedData;
  let userSelectedData;
  let dynamicType;
  let userType;
  let groupType;
  let dynamicTypeKey;
  let userTypeKey;
  let groupTypeKey;
  let selectorSelected;
  let selectorSelectedAction;

  if (notifyType === "approve") {
    dynamicSelectedData = approvalWorkflowData.notifyDynamic.onApprove;
    groupSelectedData = approvalWorkflowData.notifyGroups.onApprove;
    userSelectedData = approvalWorkflowData.notifyUsers.onApprove;
    dynamicTypeKey = "notifyWhenApprovedDynamic";
    dynamicType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_APPROVED_DYNAMIC;
    userTypeKey = "notifyWhenApprovedUsers";
    userType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_APPROVED_USERS;
    groupTypeKey = "notifyWhenApprovedGroups";
    groupType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_APPROVED_GROUPS;
    selectorSelected = approvalWorkflowData.isChooseNotifyWhenApproveSelected;
    selectorSelectedAction =
      APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_APPROVE_SELECTED;
  } else if (notifyType === "reject") {
    dynamicSelectedData = approvalWorkflowData.notifyDynamic.onReject;
    groupSelectedData = approvalWorkflowData.notifyGroups.onReject;
    userSelectedData = approvalWorkflowData.notifyUsers.onReject;
    dynamicTypeKey = "notifyWhenRejectedDynamic";
    dynamicType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REJECTED_DYNAMIC;
    userTypeKey = "notifyWhenRejectedUsers";
    userType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REJECTED_USERS;
    groupTypeKey = "notifyWhenRejectedGroups";
    groupType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REJECTED_GROUPS;
    selectorSelected = approvalWorkflowData.isChooseNotifyWhenRejectSelected;
    selectorSelectedAction =
      APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_REJECT_SELECTED;
  } else if (notifyType === "revoke") {
    dynamicSelectedData = approvalWorkflowData.notifyDynamic.onRevoke;
    groupSelectedData = approvalWorkflowData.notifyGroups.onRevoke;
    userSelectedData = approvalWorkflowData.notifyUsers.onRevoke;
    dynamicTypeKey = "notifyWhenRevokedDynamic";
    dynamicType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REVOKED_DYNAMIC;
    userTypeKey = "notifyWhenRevokedUsers";
    userType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REVOKED_USERS;
    groupTypeKey = "notifyWhenRevokedGroups";
    groupType = APPROVAL_STAGES_ACTION.REMOVE_NOTIFY_WHEN_REVOKED_GROUPS;
    selectorSelected = approvalWorkflowData.isChooseNotifyWhenRevokeSelected;
    selectorSelectedAction =
      APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_REVOKE_SELECTED;
  }

  // Create a callback function that updates when notifyType changes
  const handleOutsideClick = useCallback(() => {
    if (notifyType === "approve") {
      setApprovalWorkflowData({
        type: APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_APPROVE_SELECTED,
        payload: false,
      });
    } else if (notifyType === "reject") {
      setApprovalWorkflowData({
        type: APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_REJECT_SELECTED,
        payload: false,
      });
    } else if (notifyType === "revoke") {
      setApprovalWorkflowData({
        type: APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_NOTIFY_WHEN_REVOKE_SELECTED,
        payload: false,
      });
    }
  }, [notifyType, setApprovalWorkflowData]);
  useOnClickOutside(siderMuliTabSelectorRef1, handleOutsideClick);

  let notifyDynamicData = [];
  if (
    approvalWorkflowData?.entityType === ENTITY_TYPES.COMMISSION_PLAN_APPROVALS
  ) {
    notifyDynamicData = dynamicDataOnNotify.filter((ele) =>
      commissionPlanDynamicDataOnNotify.includes(ele.value)
    );
  } else {
    notifyDynamicData = dynamicDataOnNotify.filter((ele) =>
      otherModuleDynamicDataOnNotify.includes(ele.value)
    );
  }

  return (
    <>
      <div>
        <div className="flex flex-col gap-2">
          <EverLabel required={notifyType === "reject"}>{title}</EverLabel>
          <MultiTabSelector
            siderMuliTabSelectorRef={siderMuliTabSelectorRef1}
            placeholder="Choose who needs to be notified"
            isSelectorSelected={selectorSelected}
            isSelectorUnSelected={selectorSelected}
            setIsSelectorSelectedAction={selectorSelectedAction}
            setTemplateData={setApprovalWorkflowData}
            dynamicSourceData={dynamicData.filter(
              (ele) => ele.value !== "previous_approver_manager"
            )}
            dynamicDataOnNotify={notifyDynamicData}
            dynamicDataOnNotifyForRevoked={dynamicDataOnNotifyForRevoked}
            notifyType={notifyType}
            dynamicSelectedData={dynamicSelectedData}
            setSelectedData={setNotifyWhenRejectedData}
            groupSourceData={groupsData}
            groupSelectedData={groupSelectedData}
            dynamicType={dynamicTypeKey}
            groupType={groupTypeKey}
            userSelectedData={userSelectedData}
            userType={userTypeKey}
          />
        </div>
        <div className="w-full">
          <ChooserDeletableCardView
            placeholder="Choose who needs to be notified"
            dynamicSelected={dynamicSelectedData}
            usersSelected={userSelectedData}
            groupsSelected={groupSelectedData}
            setData={setApprovalWorkflowData}
            isChoosenType={selectorSelectedAction}
            isChoosen={selectorSelected}
            dynamicType={dynamicType}
            usersType={userType}
            groupsType={groupType}
          />
        </div>
      </div>
    </>
  );
};
