import { gql, useQuery } from "@apollo/client";
import {
  MinusCircleIcon,
  PlusCircleIcon,
  PlusSquareIcon,
  UserIcon,
} from "@everstage/evericons/outlined";
// eslint-disable-next-line no-restricted-imports
import { Avatar } from "antd";
import { cloneDeep } from "lodash";
import React, { useReducer, useMemo, useEffect, useState } from "react";
import { useQuery as useReactQuery } from "react-query";

import {
  createApprovalTemplate,
  updateApprovalTemplate,
  createApprovalRequest,
  getApprovalConfig,
} from "~/Api/ApprovalWorkflowService";
import { PAYOUT_APPROVAL_TYPES, LINE_ITEM_TYPE_ENTITY_TYPE } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import {
  EverLoader,
  useCurrentTheme,
  EverButton,
  EverTg,
  EverCheckbox,
  EverLabel,
  EverInput,
  EverModal,
  EverRadio,
  EverSwitch,
  message,
} from "~/v2/components";

import { ApprovalTemplateFooter } from "./ApprovalTemplateFooter";
import { ChooseApprovers } from "./ChooseApprovers";
import {
  approvalWorkFlowReducer,
  initialApprovalWorkflowState,
  APPROVAL_STAGES_ACTION,
  approversFieldAction,
  notifiyeeFieldAction,
  approvalCriterias,
  isWorkflowValid,
  isDueDateValid,
  constructDataToSend,
  setFetchedDataToWorkflowState,
} from "./helper";
import { NameDescription } from "./NameDescription";
import { NotifyUsers } from "./NotifyUsers";
import { StageMenu } from "./StageMenu";
import { ENTITY_TYPES } from "../Constants";

const GET_GROUPS_LIST = gql`
  query UserGroupsQuery {
    userGroups {
      userGroupId
      userGroupName
      allMembers {
        firstName
      }
    }
  }
`;

const GET_TEMPLATE_DATA = gql`
  query TemplateDataById($templateId: String!) {
    templateDataById(templateId: $templateId) {
      templateName
      entityType
      templateDescription
      notifyOnReject
      notifyOnApprove
      notifyOnRevoke
    }
    stagesDataByTemplateId(templateId: $templateId) {
      stageTemplateId
      stageName
      approvers
      approvalStrategy
      duePeriod
      approvalTrigger
      coolOffPeriod
      isAutoApprove
      notes
    }
  }
`;

export const ApprovalWorkFlowTemplateBuilder = (props) => {
  const {
    showApproval,
    setShowApproval,
    mode,
    save,
    title,
    type,
    existingTemplatesRefetch,
    editTemplateId,
    setEditTemplateId,
    onSaveCloseBuilder,
    showBackToWorkflows,
    requestParams,
    setShowSelector,
    refetch,
    onCompleteFunction,
  } = props;
  const { accessToken } = useAuthStore();
  const { rolesRefetch } = useEmployeeStore();

  const [payoutLevel, setPayoutLevel] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
      setPayoutLevel(approvalType);
    }
  }, [approvalConfigData]);

  const { data: fetchedGroupsData, loading: groupsLoading } = useQuery(
    GET_GROUPS_LIST,
    { fetchPolicy: "no-cache" }
  );
  const { data: fetchedTemplateData, loading: templateDataLoading } = useQuery(
    GET_TEMPLATE_DATA,
    {
      fetchPolicy: "no-cache",
      variables: {
        templateId: editTemplateId,
      },
      skip: editTemplateId == null,
    }
  );
  const handleCancel = () => {
    setApprovalWorkflowData({
      type: APPROVAL_STAGES_ACTION.SET_TO_INITIAL_STATE,
    });
    setEditTemplateId(null);
    setShowApproval(false);
  };

  const [approvalWorkflowData, setApprovalWorkflowData] = useReducer(
    approvalWorkFlowReducer,
    initialApprovalWorkflowState()
  );
  const currentStageData =
    approvalWorkflowData.stages[approvalWorkflowData.currentStage];

  const isView = mode === "View";

  const showNameAndDescription = save !== "optional";

  const setNotifyWhenRejectedData = (notifiyee, notifiyeeType, data) => {
    const index = notifiyee.map((e) => e.value).indexOf(data.value);

    if (index === -1) {
      setApprovalWorkflowData({
        type: notifiyeeFieldAction[notifiyeeType].add,
        payload: data,
      });
    } else {
      setApprovalWorkflowData({
        type: notifiyeeFieldAction[notifiyeeType].remove,
        payload: index,
      });
    }
  };

  const onSaveWorkflow = () => {
    const dueDateValidateResult = isDueDateValid(approvalWorkflowData);
    if (!dueDateValidateResult.isValid)
      return message.error(dueDateValidateResult.message);
    if (isWorkflowValid(approvalWorkflowData, type)) {
      let templateData = constructDataToSend(approvalWorkflowData, mode);
      if (mode == "create") {
        return new Promise((settled) => {
          createApprovalTemplate(templateData, accessToken)
            .then((response) => {
              if (response.ok) {
                response.json().then((data) => {
                  message.success(data.message);
                });
                if (onSaveCloseBuilder) {
                  setApprovalWorkflowData({
                    type: APPROVAL_STAGES_ACTION.SET_TO_INITIAL_STATE,
                  });
                  setShowApproval(false);
                }
                existingTemplatesRefetch();
              } else {
                response.json().then((data) => {
                  if (data?.message) {
                    message.error(data?.message);
                  } else {
                    message.error("Workflow creation failed.");
                  }
                });
              }
            })
            .finally(() => {
              settled();
            });
        });
      } else {
        const updateTemplateData = {
          templateId: editTemplateId,
          templateData: templateData,
        };
        return new Promise((settled) => {
          updateApprovalTemplate(updateTemplateData, accessToken)
            .then((response) => {
              if (response.ok) {
                response.json().then((data) => {
                  message.success(data.message);
                });
                setShowApproval(false);
                setApprovalWorkflowData({
                  type: APPROVAL_STAGES_ACTION.SET_TO_INITIAL_STATE,
                });
                existingTemplatesRefetch();
              } else {
                response.json().then((data) => {
                  if (data?.message) {
                    message.error(data?.message);
                  } else {
                    message.error("Workflow updation failed.");
                  }
                });
              }
            })
            .finally(() => {
              settled();
            });
        });
      }
    } else {
      message.error("Please fill all mandatory fields.");
    }
    return null;
  };

  const groupsData = useMemo(() => {
    if (fetchedGroupsData?.userGroups) {
      return fetchedGroupsData.userGroups.map((group) => {
        return {
          label: group.userGroupName,
          text: group?.allMembers
            ? group.allMembers.length + " users"
            : "0 user",
          value: group.userGroupId,
        };
      });
    }
    return null;
  }, [fetchedGroupsData]);

  useEffect(() => {
    if (mode == "edit" && fetchedTemplateData) {
      if (fetchedTemplateData?.templateDataById) {
        setFetchedDataToWorkflowState(
          setApprovalWorkflowData,
          fetchedTemplateData
        );
      } else {
        handleCancel();
        message.error("Workflow does not exist to edit, please reload!");
      }
    }
  }, [mode, fetchedTemplateData, fetchedGroupsData]);

  const onSendApprovalRequest = () => {
    const dueDateValidateResult = isDueDateValid(approvalWorkflowData);
    if (!dueDateValidateResult.isValid)
      return message.error(dueDateValidateResult.message);
    if (isWorkflowValid(approvalWorkflowData, type)) {
      requestParams["template_data"] = constructDataToSend(
        approvalWorkflowData,
        mode
      );
      requestParams.instance_params.instance_details.forEach(
        (ele) => delete ele["name"]
      );

      // Empty out instance details when isSelectedAll is true
      // The instance details will be fetched in backend to process.
      const params = cloneDeep(requestParams);
      if (requestParams.bulk_mode && requestParams.isSelectedAll) {
        params.instance_params.instance_details = [];
      }
      if (payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL) {
        params["entity_type"] = LINE_ITEM_TYPE_ENTITY_TYPE;
      }

      return new Promise(() => {
        createApprovalRequest(params, accessToken)
          .then((response) => {
            if (response.ok) {
              response.json().then((data) => {
                message.success(data.message);
              });
            } else {
              response.json().then((data) => {
                if (data?.message) {
                  message.error(data?.message);
                } else {
                  message.error("Approval request creation failed.");
                }
              });
            }
          })
          .finally(() => {
            setShowApproval(false);
            refetch();
            rolesRefetch();
            if (setShowSelector) setShowSelector(false);
          });
      });
    } else {
      message.error("Please fill all mandatory fields.");
    }
    return null;
  };

  return (
    <EverModal
      title={title}
      visible={showApproval}
      onCancel={handleCancel}
      maskClosable={false}
      width={"80%"}
      footer={
        <ApprovalTemplateFooter
          key="footer"
          showBackToWorkflows={showBackToWorkflows}
          showSaveAsTemplate={save === "optional"}
          showInitiateApproval={type === "instance"}
          name={approvalWorkflowData.workflowName}
          description={approvalWorkflowData.workflowDescription}
          setApprovalWorkflowData={setApprovalWorkflowData}
          handleCancel={handleCancel}
          ValidateAndSave={onSaveWorkflow}
          requestParams={requestParams}
          onSendApprovalRequest={onSendApprovalRequest}
          onCompleteFunction={onCompleteFunction}
          approvalWorkflowData={approvalWorkflowData}
          mode={mode}
          approvalConfigData={approvalConfigData?.data}
          type={type}
        />
      }
    >
      {!groupsLoading && (mode != "edit" || !templateDataLoading) ? (
        <div className="-my-6 -mx-6 flex max-h-[70vh]">
          <div className="overflow-auto pl-2 pt-4 pb-8 border border-solid border-ever-base-100">
            <div className="w-96 flex flex-col gap-6 mx-4">
              {showNameAndDescription && (
                <>
                  <NameDescription
                    approvalWorkflowData={approvalWorkflowData}
                    setApprovalWorkflowData={setApprovalWorkflowData}
                    name={approvalWorkflowData.workflowName}
                    description={approvalWorkflowData.workflowDescription}
                    entityType={approvalWorkflowData.entityType}
                    approvalConfigData={approvalConfigData?.data}
                    requestParams={requestParams}
                  />
                </>
              )}
              <div className="flex flex-col gap-2">
                {Object.keys(approvalWorkflowData.stages).map(
                  (stage, index) => {
                    const thisStage = parseInt(stage);
                    const totalApprovers = [
                      ...approvalWorkflowData.stages[stage].dynamicApprovers,
                      ...approvalWorkflowData.stages[stage].userApprovers,
                      ...approvalWorkflowData.stages[stage].groupApprovers,
                    ].length;
                    return (
                      <StageMenu
                        key={stage}
                        stage={"Stage #" + stage}
                        isSelected={
                          approvalWorkflowData.currentStage === thisStage
                        }
                        approversText={
                          totalApprovers > 1
                            ? totalApprovers + " approvers"
                            : totalApprovers === 0
                            ? "No Approver"
                            : totalApprovers + " approver"
                        }
                        setSelectedStage={() =>
                          setApprovalWorkflowData({
                            type: APPROVAL_STAGES_ACTION.SET_CURRENT_STAGE,
                            payload: thisStage,
                          })
                        }
                        isValid={totalApprovers > 0}
                        index={index}
                      />
                    );
                  }
                )}
                <div className="flex justify-start">
                  <EverButton
                    onClick={() => {
                      setApprovalWorkflowData({
                        type: APPROVAL_STAGES_ACTION.ADD_STAGE,
                      });
                      setApprovalWorkflowData({
                        type: APPROVAL_STAGES_ACTION.SET_CURRENT_STAGE,
                        payload:
                          Object.values(approvalWorkflowData.stages).length + 1,
                      });
                    }}
                    disabled={isView}
                    type="link"
                    prependIcon={<PlusCircleIcon className="h-4 w-4" />}
                  >
                    Add Stage
                  </EverButton>
                </div>
              </div>
              <div className="flex flex-col gap-6">
                <div>
                  <NotifyUsers
                    title="Notify when Rejected"
                    notifyType="reject"
                    approvalWorkflowData={approvalWorkflowData}
                    setApprovalWorkflowData={setApprovalWorkflowData}
                    setNotifyWhenRejectedData={setNotifyWhenRejectedData}
                    groupsData={groupsData}
                  />
                </div>
                {approvalWorkflowData.entityType ===
                  ENTITY_TYPES.COMMISSION_PLAN_APPROVALS && (
                  <div>
                    <NotifyUsers
                      title="Notify when Revoked"
                      notifyType="revoke"
                      approvalWorkflowData={approvalWorkflowData}
                      setApprovalWorkflowData={setApprovalWorkflowData}
                      setNotifyWhenRejectedData={setNotifyWhenRejectedData}
                      groupsData={groupsData}
                    />
                  </div>
                )}
                <div>
                  <NotifyUsers
                    title="Notify when Approved"
                    notifyType="approve"
                    approvalWorkflowData={approvalWorkflowData}
                    setApprovalWorkflowData={setApprovalWorkflowData}
                    setNotifyWhenRejectedData={setNotifyWhenRejectedData}
                    groupsData={groupsData}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="grow overflow-auto bg-ever-base-100 px-10 pb-4 pt-6">
            <Stage
              stage={approvalWorkflowData.currentStage}
              workflowModule={approvalWorkflowData.entityType}
              stageData={currentStageData}
              setStageData={setApprovalWorkflowData}
              isView={isView}
              groupsData={groupsData}
            />
          </div>
        </div>
      ) : (
        <div className="h-[70vh]">
          <EverLoader indicatorType="spinner" />
        </div>
      )}
    </EverModal>
  );
};

const DeletableCard = (props) => {
  const { label, setDeleted, avatar } = props;
  const { chartColors } = useCurrentTheme();
  return (
    <div className="flex items-center justify-between bg-ever-base rounded-lg px-2 py-1 mt-1">
      <div className="grow">
        {avatar ? (
          avatar
        ) : (
          <Avatar
            size="medium"
            style={{
              backgroundColor: chartColors[3],
            }}
          >
            {label.charAt(0).toUpperCase()}
          </Avatar>
        )}
        <EverTg.Text className="ml-2">{label}</EverTg.Text>
      </div>
      <EverButton
        type="link"
        onClick={setDeleted}
        title="Remove item"
        color="error"
      >
        <MinusCircleIcon className="h-4 w-4" />
      </EverButton>
    </div>
  );
};

const Stage = (props) => {
  const { stage, stageData, setStageData, isView, groupsData, workflowModule } =
    props;

  const {
    dynamicApprovers,
    groupApprovers,
    userApprovers,
    additionalNotes,
    duePeriod,
    isAutoApprove,
    considerApproved,
    isDueDateSelected,
    isAdditionalNotesSelected,
    isChooseApproversSelected,
  } = stageData;

  const setApproversData = (approvers, approverType, data) => {
    const index = approvers.map((e) => e.value).indexOf(data.value);
    if (index === -1) {
      // If not present already then add else remove
      setStageData({
        type: approversFieldAction[approverType].add,
        payload: data,
      });
    } else {
      setStageData({
        type: approversFieldAction[approverType].remove,
        payload: index,
      });
    }
  };

  const isShowStrategy = () => {
    // if ([...dynamicApprovers, ...groupApprovers, ...userApprovers].length > 1) {
    //   return true;
    // }
    // if (
    //   groupApprovers.length == 1 &&
    //   groupApprovers[0].text.split(" ")[0] > 1
    // ) {
    //   return true;
    // }
    // if (
    //   dynamicApprovers.length == 1 &&
    //   dynamicApprovers[0].value == "previous_approver_manager"
    // ) {
    //   return true;
    // }
    // return false;
    return true;
  };

  return (
    <>
      <div className="w-full flex items-center justify-between gap-3">
        <div className="grow flex flex-col gap-3">
          <EverTg.Heading3 className="font-medium">
            Stage #{stage}
          </EverTg.Heading3>
          <EverTg.Description className="mb-6">
            Configure approvers, due date, etc. for this stage.
          </EverTg.Description>
        </div>
        {stage > 1 && (
          <EverButton
            type="link"
            onClick={() => {
              setStageData({
                type: APPROVAL_STAGES_ACTION.REMOVE_STAGE,
                payload: stage,
              });
            }}
            title="Remove item"
            color="error"
            prependIcon={<MinusCircleIcon className="h-4 w-4" />}
          >
            Remove Stage
          </EverButton>
        )}
      </div>
      <div className="flex flex-col gap-6">
        <div>
          <div>
            <ChooseApprovers
              stageData={stageData}
              setStageData={setStageData}
              setApproversData={setApproversData}
              stage={stage}
              groupsData={groupsData}
              workflowModule={workflowModule}
            />
          </div>
          <div className="w-full">
            <ChooserDeletableCardView
              placeholder="Choose Approvers"
              dynamicSelected={dynamicApprovers}
              usersSelected={userApprovers}
              groupsSelected={groupApprovers}
              setData={setStageData}
              isChoosenType={
                APPROVAL_STAGES_ACTION.SET_IS_CHOOSE_APPROVERS_SELECTED
              }
              isChoosen={isChooseApproversSelected}
              dynamicType={APPROVAL_STAGES_ACTION.REMOVE_DYNAMIC_APPROVERS}
              usersType={APPROVAL_STAGES_ACTION.REMOVE_USER_APPROVERS}
              groupsType={APPROVAL_STAGES_ACTION.REMOVE_GROUP_APPROVERS}
            />
          </div>
        </div>
        <>
          {isShowStrategy() && (
            <div className="flex flex-col gap-4">
              <div>
                <EverTg.Heading4>Consider stage approved when</EverTg.Heading4>
              </div>
              <div>
                <EverRadio.Group
                  onChange={(e) => {
                    setStageData({
                      type: APPROVAL_STAGES_ACTION.SET_CONSIDER_APPROVED,
                      payload: e.target.value,
                    });
                  }}
                  value={considerApproved}
                  disabled={isView}
                >
                  <div className="flex flex-col gap-6">
                    {approvalCriterias.map((criteria) => {
                      return (
                        <div key={criteria.value}>
                          <EverRadio
                            value={criteria.value}
                            className="flex items-start"
                          >
                            <div className="flex items-start flex-col gap-2">
                              <EverTg.Text>{criteria.label}</EverTg.Text>
                              <EverTg.Description>
                                {criteria.text}
                              </EverTg.Description>
                            </div>
                          </EverRadio>
                        </div>
                      );
                    })}
                  </div>
                </EverRadio.Group>
              </div>
            </div>
          )}
          {[...dynamicApprovers, ...groupApprovers, ...userApprovers].length >
          0 ? (
            !isAdditionalNotesSelected ? (
              <div
                className="flex gap-3 items-center cursor-pointer self-start"
                onClick={() => {
                  setStageData({
                    type: APPROVAL_STAGES_ACTION.SET_IS_ADDTIONAL_NOTES_SELECTED,
                    payload: !isAdditionalNotesSelected,
                  });
                }}
              >
                <PlusSquareIcon className="h-5 w-5" />
                <EverTg.Heading4>
                  Include additional notes for the approvers
                </EverTg.Heading4>
              </div>
            ) : (
              <div className="flex flex-col">
                <div className="flex items-center mb-2 gap-1">
                  <EverLabel>Additional message</EverLabel>
                  <MinusCircleIcon
                    title="Remove Message"
                    onClick={() => {
                      setStageData({
                        type: APPROVAL_STAGES_ACTION.SET_IS_ADDTIONAL_NOTES_SELECTED,
                        payload: !isAdditionalNotesSelected,
                      });
                    }}
                    className="w-4 h-4 text-ever-error cursor-pointer"
                  />
                </div>
                <EverInput.TextArea
                  maxLength="500"
                  className="w-96"
                  placeholder="Type here"
                  onChange={(e) => {
                    setStageData({
                      type: APPROVAL_STAGES_ACTION.SET_ADDITIONAL_NOTES,
                      payload: e.target.value,
                    });
                  }}
                  value={additionalNotes}
                />
              </div>
            )
          ) : (
            ""
          )}
          <div className="w-full h-px bg-ever-base-400"></div>
          <div className="flex flex-col gap-4">
            <EverTg.Heading3 className="font-medium">Due date</EverTg.Heading3>
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3">
                <EverSwitch
                  onChange={(checked) =>
                    setStageData({
                      type: APPROVAL_STAGES_ACTION.SET_IS_DUE_DATE_SELECTED,
                      payload: checked,
                    })
                  }
                  checked={isDueDateSelected}
                />
                <EverLabel
                  onClick={() =>
                    setStageData({
                      type: APPROVAL_STAGES_ACTION.SET_IS_DUE_DATE_SELECTED,
                      payload: !isDueDateSelected,
                    })
                  }
                  className="cursor-pointer"
                >
                  Set a due date for this stage
                </EverLabel>
              </div>
              {isDueDateSelected && (
                <>
                  <div className="flex flex-col gap-2 mt-2">
                    <EverLabel required={true}>
                      Select no of days from when this stage is triggered.
                    </EverLabel>
                    <div>
                      <EverInput.Number
                        className="w-32"
                        min={1}
                        max={30}
                        value={duePeriod}
                        onChange={(date) =>
                          setStageData({
                            type: APPROVAL_STAGES_ACTION.SET_DUE_PERIOD,
                            payload: date,
                          })
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <EverCheckbox
                      onChange={(e) =>
                        setStageData({
                          type: APPROVAL_STAGES_ACTION.SET_IS_AUTO_APPROVE,
                          payload: e.target.checked,
                        })
                      }
                      checked={isAutoApprove}
                      label="Automatically approve stage if no response beyond the due date"
                    ></EverCheckbox>
                  </div>
                </>
              )}
            </div>
          </div>
        </>
      </div>
    </>
  );
};

export const ChooserDeletableCardView = (props) => {
  const {
    // placeholder,
    dynamicSelected,
    usersSelected,
    groupsSelected,
    setData,
    // isChoosenType,
    // isChoosen,
    dynamicType,
    usersType,
    groupsType,
  } = props;
  const { chartColors } = useCurrentTheme();
  return (
    <>
      {/*<Select
        style={{ width: "100%" }}
        placeholder={placeholder}
        onClick={() =>
          setData({
            type: isChoosenType,
            payload: !isChoosen,
          })
        }
      />*/}
      {dynamicSelected.map((data, index) => {
        return (
          <DeletableCard
            key={index}
            label={data.label}
            setDeleted={() => {
              setData({
                type: dynamicType,
                payload: index,
              });
            }}
            avatar={
              <Avatar
                size="medium"
                style={{ backgroundColor: chartColors[4] + "3A" }}
                icon={
                  <UserIcon
                    className="w-8 h-5 mt-2"
                    style={{ color: chartColors[4] }}
                  />
                }
              />
            }
          />
        );
      })}
      {usersSelected.map((data, index) => {
        return (
          <DeletableCard
            key={index}
            label={data.label}
            setDeleted={() => {
              setData({
                type: usersType,
                payload: index,
              });
            }}
          />
        );
      })}
      {groupsSelected.map((data, index) => {
        return (
          <DeletableCard
            key={index}
            label={data.label}
            setDeleted={() => {
              setData({
                type: groupsType,
                payload: index,
              });
            }}
            avatar={
              <Avatar
                size="medium"
                style={{ backgroundColor: chartColors[4] + "3A" }}
                icon={
                  <UserIcon
                    className="w-8 h-5 mt-2"
                    style={{ color: chartColors[4] }}
                  />
                }
              />
            }
          />
        );
      })}
    </>
  );
};
