import { UserIcon } from "@everstage/evericons/outlined";
// eslint-disable-next-line no-restricted-imports
import { Avatar } from "antd";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useState } from "react";

import { rejectRequest } from "~/Api/ApprovalWorkflowService";
import { PAYOUT_APPROVAL_TYPES, DATE_FORMATS } from "~/Enums";
import {
  EverButton,
  EverInput,
  EverLabel,
  EverListItem,
  EverModal,
  EverTg,
  useCurrentTheme,
  message,
} from "~/v2/components";
import { getCurrencyValue } from "~/v2/features/commissions/constants";

import { processErrorMessage } from "../approval-requests/payout-approvals/Common";

const { DDMMMYYYY, MMMYYYY } = DATE_FORMATS;

const RejectApprovalModal = observer((props) => {
  const {
    modalVisibility,
    closeModal,
    rejectModalData,
    refetchData,
    customCalendar,
    payoutLevel = PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL,
    currencyCodeSymbolMap,
    accessToken,
    rolesRefetch,
    closeDrawer,
  } = props;

  const [rejectionComment, setRejectionComment] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [rejectLoading, setRejectLoading] = useState(false);

  function rejectApprovalRequest(requestData) {
    return rejectRequest(requestData, accessToken);
  }

  const handleOnOk = async () => {
    if (!rejectionComment || rejectionComment.trim() === "") {
      setErrorMessage("Please add comment.");
    } else {
      if (payoutLevel === PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL) {
        setRejectLoading(true);
        rejectApprovalRequest({
          requestId: [rejectModalData?.approvalRequestId],
          comments: rejectionComment,
        })
          .then((res) => {
            if (res.status == "FAILED") {
              console.log("Error", res.message);
              processErrorMessage(res.message);
            } else {
              message.success(`You've rejected the request.`);
              closeDrawer && closeDrawer();
            }
          })
          .catch((error) => {
            console.log(error);
            message.error("Some error occured.");
          })
          .finally(() => {
            setRejectLoading(false);
            setErrorMessage("");
            setRejectionComment("");
            refetchData();
            closeModal();
            rolesRefetch();
          });
      }
    }
  };

  const { chartColors } = useCurrentTheme();
  return (
    <>
      <EverModal
        closable={false}
        title="Reject approval request"
        visible={modalVisibility}
        confirmLoading={rejectLoading}
        width={600}
        footer={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => {
              if (!rejectLoading) closeModal();
            }}
          >
            Cancel
          </EverButton>,
          <EverButton key="submit" color="error" onClick={handleOnOk}>
            Reject Request
          </EverButton>,
        ]}
      >
        <div className="flex gap-3 items-center">
          <div className="w-1/2">
            <EverListItem
              prepend={
                <Avatar
                  size="large"
                  style={{ backgroundColor: chartColors[4] + "3A" }}
                  icon={
                    rejectModalData?.profilePicture || (
                      <UserIcon className="w-5 h-5 mt-2 text-ever-chartColors-4" />
                    )
                  }
                />
              }
              title={`${rejectModalData?.firstName} ${rejectModalData?.lastName}`}
              subtitle={rejectModalData?.employeeEmailId}
            />
          </div>
          <div className="w-1/4">
            <div className="flex flex-col gap-1">
              <EverLabel>Amount</EverLabel>
              <EverTg.SubHeading3 className="text-ever-base-content">
                {getCurrencyValue(
                  currencyCodeSymbolMap[
                    rejectModalData?.instanceData?.currency
                  ],
                  rejectModalData?.instanceData?.adjustment_value
                )}
              </EverTg.SubHeading3>
            </div>
          </div>
          <div className="w-1/4">
            <div className="flex flex-col gap-1">
              <EverLabel>Period</EverLabel>
              <EverTg.SubHeading3 className="text-ever-base-content">
                {customCalendar
                  ? moment.utc(rejectModalData?.periodEndDate).format(DDMMMYYYY)
                  : moment.utc(rejectModalData?.periodEndDate).format(MMMYYYY)}
              </EverTg.SubHeading3>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2 mt-3">
          <EverLabel required={true}>Comments</EverLabel>
          <EverInput.TextArea
            className="resize-none"
            value={rejectionComment}
            onChange={(e) => setRejectionComment(e.target.value)}
            rows="4"
            placeholder="Add your comments"
            maxLength="250"
          />
          {errorMessage && (
            <span className="text-ever-error">{errorMessage}</span>
          )}
        </div>
      </EverModal>
    </>
  );
});

export { RejectApprovalModal };
