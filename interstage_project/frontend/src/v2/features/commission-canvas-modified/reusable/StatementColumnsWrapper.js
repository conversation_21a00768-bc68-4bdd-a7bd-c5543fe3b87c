import { useQuery } from "@apollo/client";
import {
  DotsVerticalIcon,
  EditPencilAltIcon,
  LinkIcon,
  MinusCircleIcon,
} from "@everstage/evericons/outlined";
import { cloneDeep } from "lodash";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  ANALYTICS_EVENTS,
  COMMISSION_TYPE,
  EVERSTAGE_GENERATED_COLUMNS,
  RBAC_ROLES,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  DataTypeIcon,
  EverBadge,
  EverButton,
  EverCheckbox,
  EverDraggableColumns,
  EverLoader,
  EverModal,
  EverTg,
  EverTooltip,
} from "~/v2/components";
import { CRITERIA_COLUMN_COMPONENT } from "~/v2/features/commission-canvas-modified/constants";
import { HYPERLINK_MAP } from "~/v2/features/commission-canvas-modified/services/graphql";
import { getDataSheetVariables } from "~/v2/features/commission-canvas-modified/services/http";

const _columnsDeformatter = (formattedCards) => {
  return formattedCards.map(({ content, ...otherCardProps }) => {
    return otherCardProps;
  });
};

const ColumnContent = ({ type, name, isPrimaryKey, showHyperLink }) => (
  <div className="ml-2 flex gap-2 items-center min-w-0">
    <div className="w-5 h-5">
      <DataTypeIcon
        type={type}
        className="w-5 h-5 bg-ever-chartColors-8 rounded p-1"
      />
    </div>
    <EverTg.Text className="truncate" title={name}>
      {name}
    </EverTg.Text>
    {isPrimaryKey ? (
      <EverBadge
        className="h-full whitespace-nowrap mx-1"
        type="error"
        title="Primary key"
      />
    ) : null}
    {showHyperLink ? (
      <EverTooltip title="This field contains a hyperlink to the source.">
        <div className="w-4 h-4 mx-2">
          <LinkIcon className="w-4 h-4 text-ever-chartColors-4" />
        </div>
      </EverTooltip>
    ) : null}
  </div>
);

const DragContent = ({ id, isDragDisabled, onRemove, ...props }) => (
  <div className="flex items-center bg-ever-base-50 rounded-lg my-2 px-2 py-2.5 border border-solid border-ever-base-400">
    {!isDragDisabled && (
      <>
        <div className="w-1.5 h-4">
          <DotsVerticalIcon className="w-1.5 h-3 text-ever-base-content-low" />
        </div>
        <div className="w-1.5 h-4">
          <DotsVerticalIcon className="w-1.5 h-3 mr-3 text-ever-base-content-low" />
        </div>
      </>
    )}
    <ColumnContent {...props} />
    {!isDragDisabled && (
      <div className="w-4 h-4 ml-auto">
        <MinusCircleIcon
          className="ml-auto w-4 h-4 text-ever-error-hover cursor-pointer"
          title="Remove column"
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
        />
      </div>
    )}
  </div>
);

const ColumnSelection = ({
  type,
  canEdit,
  sourceVars,
  statementCols,
  className,
  showHyperLink,
  isPrimaryKey,
  handleSelect,
  handleSelectAll,
}) => {
  const selected = statementCols.filter((col) => col.type === type);
  const isIntermed = selected.length > 0 && selected.length < sourceVars.length;
  const isSelectedAll =
    sourceVars.length > 0 && sourceVars.length === selected.length;
  return (
    <>
      {canEdit && (
        <EverCheckbox
          className="pt-1.5 pb-2.5 px-2"
          checked={isSelectedAll}
          indeterminate={isIntermed}
          onChange={() => handleSelectAll(type, selected, sourceVars)}
        >
          <EverTg.Text className="ml-1 text-ever-base-content-mid">
            Select all
          </EverTg.Text>
        </EverCheckbox>
      )}
      <div className={className}>
        {sourceVars.map((variable) => {
          return (
            <div
              key={variable.systemName}
              className={`flex ${
                canEdit ? "px-2" : ""
              } py-2.5 items-center h-max`}
            >
              {canEdit ? (
                <EverCheckbox
                  value={variable.systemName}
                  checked={statementCols.some(
                    (c) => c.id === variable.systemName
                  )}
                  onChange={() => handleSelect(variable, type)}
                >
                  <ColumnContent
                    name={variable.displayName}
                    // datatype won't be there for default column, so default to
                    // Integer datatype.
                    type={variable.dataType_DataType ?? "Integer"}
                    isPrimaryKey={isPrimaryKey(variable.systemName)}
                    showHyperLink={showHyperLink(variable.systemName)}
                  />
                </EverCheckbox>
              ) : (
                <ColumnContent
                  name={variable.displayName}
                  // datatype won't be there for default column, so default to
                  // Integer datatype.
                  type={variable.dataType_DataType ?? "Integer"}
                  isPrimaryKey={isPrimaryKey(variable.systemName)}
                  showHyperLink={showHyperLink(variable.systemName)}
                />
              )}
            </div>
          );
        })}
      </div>
    </>
  );
};

// Additionally used by CustomizeColumnComponent in src/v2/features/statements/payout-details/CustomizeColumnComponent.js
const StatementColumnsWrapper = ({
  planType,
  component,
  componentDisplayName,
  commissionPlanDetails,
  criteriaId,
  criteriaType,
  criteriaW,
  analyticsProperties,
  handleApply,
  handleClose,
  footerSubtitleComponent,
  updateCriteriaColumns,
  isUnderReview,
}) => {
  // Each item has the following fields:
  // {
  //   id:                The system name of the variable for datasheet variables, or the
  //                      system name of the column for everstage generated columns
  //   type:              "datasheet" | "default"
  //   isDragDisabled:    true | false
  //   dragContentProps:  { id, type, name, planScope { canEdit }, isPrimaryKey, showHyperLink }
  // }
  const [statementCols, setStatementCols] = useState([]);

  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  // everstage generated columns
  const defaultColumns = [
    {
      ...EVERSTAGE_GENERATED_COLUMNS.COMMISSION,
      displayName: t("COMMISSIONS"),
    },
  ];
  if (["Tier", "Quota"].includes(criteriaW.data.type)) {
    defaultColumns.push({
      ...EVERSTAGE_GENERATED_COLUMNS.TIERNAME,
      displayName: "Tier / Overridden Tier",
    });
  }
  if (
    criteriaType === "CustomQuota" ||
    (criteriaType === "CustomTeam" && criteriaW.data.criteriaType === "Quota")
  ) {
    defaultColumns.push({
      ...EVERSTAGE_GENERATED_COLUMNS.QUOTAEROSION,
      displayName: t("QUOTA_EROSION"),
    });
  }

  const datasheetId = criteriaW.data.datasheetId;
  const { getDatasheetPrimaryKey } = useDatabookStore();
  const primaryKeys = datasheetId ? getDatasheetPrimaryKey(datasheetId) : [];
  const isPrimaryKey = (variable) => primaryKeys.includes(variable);

  const { accessToken } = useAuthStore();
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const { data: hyperlinkMapData, loading: hyperlinkMapLoading } = useQuery(
    HYPERLINK_MAP,
    {
      variables: {
        criteriaId: criteriaId,
        planId: commissionPlanDetails.planId,
      },
      fetchPolicy: "no-cache",
      skip: planType === COMMISSION_TYPE.FORECAST_PLAN,
    }
  );

  const showHyperLink = (variable) => {
    return (
      clientFeatures.crmHyperlinks &&
      Object.keys(
        JSON.parse(hyperlinkMapData?.hyperlinkMapForStatements ?? "{}")
      ).includes(variable)
    );
  };

  const handleRemoveDragCard = (columnId) => {
    const cols = cloneDeep(statementCols);
    const idx = cols.findIndex((col) => col.id === columnId);
    cols.splice(idx, 1);
    setStatementCols(cols);
    if (updateCriteriaColumns) {
      updateCriteriaColumns(
        criteriaId,
        cols.map((col) => col.id)
      );
    }
  };

  const { data: dsVarData, isLoading: dsVariablesLoading } = useReactQuery(
    ["datasheetVariables", hyperlinkMapLoading],
    () => {
      if (
        !datasheetId ||
        (hyperlinkMapLoading && planType === COMMISSION_TYPE.COMMISSION_PLAN)
      ) {
        return Promise.resolve([]);
      }
      return getDataSheetVariables(accessToken, datasheetId);
    },
    {
      cacheTime: 0,
      retry: false,
      onSuccess: (dsVariables) => {
        const mappedCols = [];
        const criteriaCols =
          (component === CRITERIA_COLUMN_COMPONENT.STATEMENT
            ? criteriaW.criteriaColumns
            : criteriaW.simulateColumns) ?? [];
        for (const col of criteriaCols) {
          const dsVariable = dsVariables.find((v) => v.systemName === col);
          const fixedCol = defaultColumns.find((c) => c.systemName === col);
          if (dsVariable) {
            mappedCols.push({
              id: col,
              type: "datasheet",
              isDragDisabled: !commissionPlanDetails.planScope.canEdit,
              dragContentProps: {
                id: col,
                type: dsVariable.dataType_DataType,
                name: dsVariable.displayName,
                isDragDisabled: !commissionPlanDetails.planScope.canEdit,
                isPrimaryKey: isPrimaryKey(col),
                showHyperLink: showHyperLink(col),
              },
            });
            continue;
          }
          if (fixedCol) {
            mappedCols.push({
              id: col,
              type: "default",
              isDragDisabled:
                col === "commission" ||
                !commissionPlanDetails.planScope.canEdit,
              dragContentProps: {
                id: col,
                type: "Integer",
                name: fixedCol.displayName,
                isDragDisabled:
                  col === "commission" ||
                  !commissionPlanDetails.planScope.canEdit,
                isPrimaryKey: false,
                showHyperLink: false,
              },
            });
          }
        }
        setStatementCols(mappedCols);
      },
    }
  );
  const dsVariables = dsVarData ?? [];

  const handleDragStatementCols = (columns) => {
    // This handler will always put the commission column at end of the list if
    // it exists.
    const commissionCol = columns.find((col) => col.id === "commission");
    let tempCols = [];
    if (commissionCol) {
      tempCols = columns.filter((col) => col.id !== "commission");
      tempCols.push(commissionCol);
    }
    const updateCol = commissionCol ? tempCols : columns;
    setStatementCols(updateCol);
    if (updateCriteriaColumns) {
      updateCriteriaColumns(
        criteriaId,
        updateCol.map((col) => col.id)
      );
    }
  };

  const handleChangeStatementCols = (variable, type) => {
    const cols = cloneDeep(statementCols);
    const idx = cols.findIndex((col) => col.id === variable.systemName);
    // If the given variable doesn't exist in the statement columns, add it
    if (idx < 0) {
      const colId = variable.systemName;
      cols.push({
        id: colId,
        type,
        isDragDisabled: colId === "commission",
        dragContentProps: {
          id: colId,
          type: variable.dataType_DataType ?? "Integer",
          name: variable.displayName,
          isDragDisabled: colId === "commission",
          isPrimaryKey: isPrimaryKey(colId),
          showHyperLink: showHyperLink(colId),
        },
      });

      // Remove the commission column if it exists and add it to the end
      const commissionCol = cols.find((col) => col.id === "commission");
      if (commissionCol) {
        cols.splice(cols.indexOf(commissionCol), 1);
        cols.push(commissionCol);
      }
    } else {
      cols.splice(idx, 1);
    }
    setStatementCols(cols);
    if (updateCriteriaColumns) {
      updateCriteriaColumns(
        criteriaId,
        cols.map((col) => col.id)
      );
    }
  };
  const handleSelectAll = (type, selected, allCols) => {
    // If all the columns all already selected, remove columns of the given type
    if (selected.length === allCols.length) {
      const filterCol = statementCols.filter((col) => col.type !== type);
      setStatementCols(filterCol);
      if (updateCriteriaColumns) {
        updateCriteriaColumns(
          criteriaId,
          filterCol.map((col) => col.id)
        );
      }
    } else {
      // Check if the column is already selected, if not, add it. This is to
      // preserve the order of the columns.
      const cols = cloneDeep(statementCols);
      for (const col of allCols) {
        const colId = col.systemName;
        const idx = cols.findIndex((c) => c.id === colId);
        if (idx < 0) {
          cols.push({
            id: colId,
            type,
            isDragDisabled: colId === "commission",
            dragContentProps: {
              id: colId,
              type: col.dataType_DataType ?? "Integer",
              name: col.displayName,
              isDragDisabled: colId === "commission",
              isPrimaryKey: isPrimaryKey(colId),
              showHyperLink: showHyperLink(colId),
            },
          });
        }
      }
      // Remove the commission column if it exists and add it to the end
      const commissionCol = cols.find((col) => col.id === "commission");
      if (commissionCol) {
        cols.splice(cols.indexOf(commissionCol), 1);
        cols.push(commissionCol);
      }
      setStatementCols(cols);
      if (updateCriteriaColumns) {
        updateCriteriaColumns(
          criteriaId,
          cols.map((col) => col.id)
        );
      }
    }
  };

  const commonProps = {
    statementCols,
    isPrimaryKey,
    showHyperLink,
    handleSelectAll,
    handleSelect: handleChangeStatementCols,
  };

  if (dsVariablesLoading || hyperlinkMapLoading) {
    return (
      <div className="h-full flex justify-center items-center">
        <EverLoader />
      </div>
    );
  }

  const columnsFormatter = (statementCols, isUnderReview) => {
    return statementCols.map((col) => {
      return {
        id: col.id,
        type: col.type,
        isDragDisabled: isUnderReview || col.isDragDisabled,
        dragContentProps: col.dragContentProps,
        content: (
          <DragContent
            {...col.dragContentProps}
            onRemove={() => handleRemoveDragCard(col.id)}
          />
        ),
      };
    });
  };

  const onRenameFieldsClick = () => {
    sendAnalyticsEvent(
      accessToken,
      ANALYTICS_EVENTS.RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED,
      analyticsProperties
    );
    window.open(`/datasheet?id=${datasheetId}&isEditView=true`, "_blank");
  };

  return (
    <div className="h-full w-full flex gap-6">
      <div className="w-1/2 flex flex-col">
        <EverTg.SubHeading4>Available columns</EverTg.SubHeading4>
        <div className="flex flex-col overflow-y-auto mt-2 px-4 py-2 border border-ever-base-400 border-solid rounded-xl h-full">
          <EverTg.Heading4 className="mb-2">Everstage fields</EverTg.Heading4>
          <ColumnSelection
            type="default"
            canEdit={commissionPlanDetails.planScope.canEdit && !isUnderReview}
            sourceVars={defaultColumns}
            {...commonProps}
          />
          {datasheetId && (
            <>
              <div className="mt-4 mb-2 flex items-center justify-between">
                <EverTg.Heading4>Datasheet fields</EverTg.Heading4>
                {clientFeatures.showDataSourcesV2 && datasheetId && (
                  <EverButton
                    className=""
                    type="link"
                    icon={<EditPencilAltIcon />}
                    title={
                      hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)
                        ? ""
                        : "You do not have the required permission to perform this action."
                    }
                    disabled={!hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)}
                    onClick={onRenameFieldsClick}
                  >
                    Rename fields
                  </EverButton>
                )}
              </div>
              <ColumnSelection
                type="datasheet"
                canEdit={
                  commissionPlanDetails.planScope.canEdit && !isUnderReview
                }
                sourceVars={dsVariables}
                className="overflow-y-auto"
                {...commonProps}
              />
            </>
          )}
        </div>
      </div>
      <div className="w-1/2 flex flex-col">
        <EverTg.SubHeading4>{componentDisplayName} columns</EverTg.SubHeading4>
        <div className="h-full mt-2 px-4 py-2 overflow-y-auto border border-ever-base-400 border-solid rounded-xl">
          <EverDraggableColumns
            cards={columnsFormatter(statementCols, isUnderReview)}
            setCards={(formattedCards) =>
              handleDragStatementCols(_columnsDeformatter(formattedCards))
            }
          />
        </div>
      </div>
      <EverModal.Footer>
        <div className="flex justify-between">
          {footerSubtitleComponent}
          <div className="flex justify-end gap-4">
            <EverButton type="ghost" color="base" onClick={() => handleClose()}>
              Cancel
            </EverButton>
            {commissionPlanDetails.planScope.canEdit && (
              <EverButton
                disabled={statementCols.length === 0 || !datasheetId}
                onClick={() => handleApply(statementCols.map((col) => col.id))}
              >
                {commissionPlanDetails.isDraft ? "Apply" : "Save"}
              </EverButton>
            )}
          </div>
        </div>
      </EverModal.Footer>
    </div>
  );
};

export default StatementColumnsWrapper;
