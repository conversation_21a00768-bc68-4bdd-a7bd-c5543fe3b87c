import { COMMISSION_TYPE } from "~/Enums";

export const getPayoutFrequencyList = async (accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const response = await fetch("/spm/payout_frequency/all", requestOptions);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "");
  }
  return data;
};

export const getIndividuals = (
  urlPrefix,
  requestQueryParams,
  requestBody,
  accessToken,
  additionalRequestOptions
) => {
  const url =
    `${urlPrefix}/possible-plan-payees` +
    "?" +
    new URLSearchParams(requestQueryParams).toString();
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(requestBody),
    ...additionalRequestOptions,
  };
  return fetch(url, requestOptions);
};

export const getAutomationImpactCount = async (
  urlPrefix,
  accessToken,
  sessionId,
  options
) => {
  const url = `${urlPrefix}/plan-period-settlement-end-automation-impact-count?session_id=${sessionId}`;
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(options),
  };
  const response = await fetch(url, requestOptions);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "");
  }
  return data;
};

export const getFilterFields = async (urlPrefix, accessToken, component) => {
  const url = `${urlPrefix}/filters-allowed-fields?component=${component}`;
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const response = await fetch(url, requestOptions);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(data?.reason || "");
  }

  return data;
};

export const saveCommissionPlanDetails = (urlPrefix, accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  };
  return fetch(`${urlPrefix}/save`, requestOptions);
};

export const validatePayeesInPlan = (urlPrefix, accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  };
  return fetch(`${urlPrefix}/validate`, requestOptions);
};

export const savePayeesInPlan = async (
  urlPrefix,
  accessToken,
  requestQueryParams,
  requestBody
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  };

  try {
    const response = await fetch(
      `${urlPrefix}/save` +
        "?" +
        new URLSearchParams(requestQueryParams).toString(),
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getPlanPayeesQuickFiltersCount = async (
  urlPrefix,
  accessToken,
  requestQueryParams,
  requestBody,
  abortControllerSignal
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  };

  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;

  try {
    const response = await fetch(
      `${urlPrefix}/plan-payees/quick-filter-counts` +
        "?" +
        new URLSearchParams(requestQueryParams).toString(),
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getPlanPayees = async (
  urlPrefix,
  accessToken,
  requestQueryParams,
  requestBody,
  abortControllerSignal
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  };

  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;

  try {
    const response = await fetch(
      `${urlPrefix}/plan-payees` +
        "?" +
        new URLSearchParams(requestQueryParams).toString(),
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getPlanPayeesSimulate = async (
  urlPrefix,
  accessToken,
  requestQueryParams,
  requestBody,
  abortControllerSignal
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  };

  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;

  try {
    const response = await fetch(
      `${urlPrefix}/plan-payees-simulate` +
        "?" +
        new URLSearchParams(requestQueryParams).toString(),
      requestOptions
    );
    const responseData = await response.json();

    if (!response.ok) throw responseData;

    return responseData;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const exportPlanPayees = async (
  urlPrefix,
  accessToken,
  requestQueryParams,
  requestBody,
  abortControllerSignal
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  };

  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;

  try {
    const response = await fetch(
      `${urlPrefix}/plan-payees/export` +
        "?" +
        new URLSearchParams(requestQueryParams).toString(),
      requestOptions
    );

    if (!response.ok) {
      const responseData = await response.json();
      throw responseData;
    }

    return response;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const downloadPlanDocument = async (downloadUrl, accessToken) => {
  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
  };

  try {
    const response = await fetch(downloadUrl, requestOptions);

    if (!response.ok) {
      const responseData = await response.json();
      throw responseData;
    }

    return response;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const removeTempPlanDocument = async (
  urlPrefix,
  payload,
  accessToken
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };

  const response = await fetch(
    `${urlPrefix}/remove-temp-plan-doc`,
    requestOptions
  );

  if (!response.ok) {
    const responseData = await response.json();
    throw responseData;
  }

  return response;
};

// -------------------------- Criteria Builder --------------------------

export const getDataSheetVariables = async (accessToken, datasheetId) => {
  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  };
  const response = await fetch(
    `/spm/datasheet/get_ds_variables?datasheet_id=${datasheetId}`,
    requestOptions
  );
  const data = await response.json();
  if (!response.ok) {
    return [];
  }
  return data;
};

export const getTeamTypes = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };
  const response = await fetch(
    `${urlPrefix}/criteria/team-types`,
    requestOptions
  );
  const data = await response.json();
  if (!response.ok) {
    throw data?.message?.error ?? "Error fetching team types";
  }
  return data;
};

export const createNewCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(`${urlPrefix}/criteria/add`, requestOptions);
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error creating component";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const importCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/criteria/import`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error importing component";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateCriteriaColumn = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/criteria/update-column`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error updating component/simulate columns";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// Additionally used by CustomizeColumnComponent in src/v2/features/statements/payout-details/CustomizeColumnComponent.js
export const updateAllCriteriaColumns = async (accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      "/spm/plan-v2/commissionplan/criteria/update-all-criteria-column",
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error updating statement columns";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const savePlanCriterias = (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  return fetch(`${urlPrefix}/criteria/save`, requestOptions);
};

export const deleteCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/criteria/delete`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error deleting component";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const cloneCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(`${urlPrefix}/criteria/clone`, requestOptions);
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error cloning component";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const uploadTempPlanDoc = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: payload,
  };
  return fetch(`${urlPrefix}/upload-temp-plan-doc`, requestOptions);
};

export const editCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/criteria/update`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Failed to edit the component";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateCriteriaConfig = async (urlPrefix, accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: ` Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/criteria/criteria-config/update`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data?.message?.error ?? "Error updating component config";
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getPsdPedForFreq = (
  accessToken,
  frequency,
  planStartDate,
  planEndDate
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({ frequency, planStartDate, planEndDate }),
  };
  return fetch(`/commission_engine/period_dates_for_frequency`, requestOptions);
};

export const evaluateExpression = (
  planType,
  data,
  accessToken,
  abortControllerSignal
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  if (abortControllerSignal) requestOptions.signal = abortControllerSignal;
  return fetch(
    planType === COMMISSION_TYPE.COMMISSION_PLAN
      ? "/commission_engine/plan-v2/expression_evaluate"
      : `/commission_engine/plan-v2/${planType}/expression_evaluate`,
    requestOptions
  );
};

export const exportSimulatedRecords = (planType, accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch(
    planType === COMMISSION_TYPE.COMMISSION_PLAN
      ? "/commission_engine/plan-v2/export_simulated_records"
      : `/commission_engine/plan-v2/${planType}/export_simulated_records`,
    requestOptions
  );
};

export const publishCriteria = async (urlPrefix, accessToken, payload) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(`${urlPrefix}/publish`, requestOptions);
    const data = await response.json();
    if (!response.ok) {
      throw data;
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const changeCriteriasDisplayOrder = async (
  urlPrefix,
  accessToken,
  payload
) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(payload),
  };
  try {
    const response = await fetch(
      `${urlPrefix}/update-criteria-order`,
      requestOptions
    );
    const data = await response.json();
    if (!response.ok) {
      throw data;
    }
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
