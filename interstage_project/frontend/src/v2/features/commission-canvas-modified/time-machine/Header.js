import { AlertCircleIcon, CheckIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import React from "react";
import { twMerge } from "tailwind-merge";

import {
  EverButton,
  EverNewDatePicker,
  EverDivider,
  EverSelect,
  EverTg,
  LazySelect,
} from "~/v2/components";

const MAX_SELECTION_COUNT = 5;
const MAX_PAYEES_IN_PLAN = 100;
const MIN_SEARCH_CHAR = 3;

export default function Header({
  selectedCriteriaInTimeMachine,
  handleCriteriaChange,
  selectedDateRange,
  handleSelectedDateRangeChange,
  selectedPayeeList,
  handleSelectedPayeeListChange,
  allPayeesSelected,
  handleAllPayeesSelectedChange,
  lazyLoadProps,
  totalCount,
  initialTotalCount,
  handleTotalCountChange,
  initialUsers,
  handleFetchedUsersChange,
  getSelectCriteriaOptions,
  isAnalyzeButtonDisabled,
  evaluateCriteriaExpression,
}) {
  const { RangePicker } = EverNewDatePicker;

  return (
    <div className="w-full h-16 bg-ever-primary-lite flex px-10 py-4 gap-6 items-center">
      <div className="flex gap-2 items-center">
        <EverTg.Text>Component</EverTg.Text>
        <EverSelect
          value={selectedCriteriaInTimeMachine?.id}
          onChange={handleCriteriaChange}
          className="w-52"
          options={getSelectCriteriaOptions()}
          placeholder="Select Criteria"
        />
      </div>
      <div className="flex gap-2 items-center">
        <EverTg.Text>Period</EverTg.Text>
        <RangePicker
          allowClear={false}
          value={selectedDateRange || null}
          onChange={handleSelectedDateRangeChange}
          className="w-64"
        />
      </div>
      <div className="flex gap-2 items-center">
        <EverTg.Text className="whitespace-nowrap">Simulate for</EverTg.Text>
        <LazySelect
          mode="multiple"
          className={twMerge(
            "min-w-[240px]",
            allPayeesSelected &&
              "[&>.ant-select>.ant-select-selector>.ant-select-selection-overflow]:pl-2"
          )}
          placeholder={
            allPayeesSelected ? (
              <div className="rounded-3xl w-max py-1 px-2 text-ever-primary bg-ever-primary-lite">
                All payees
              </div>
            ) : (
              "Select payee"
            )
          }
          value={selectedPayeeList}
          onChange={(values) => {
            handleAllPayeesSelectedChange(false);
            handleSelectedPayeeListChange(values);
          }}
          deps={[selectedDateRange]}
          {...lazyLoadProps}
          selectionLimit={MAX_SELECTION_COUNT}
          selectAllElement={({ searchValue }) => {
            return (
              <div className="pl-3 pr-2.5 py-1">
                <div
                  className={twMerge(
                    "flex justify-between px-1 items-center rounded",
                    allPayeesSelected ? "bg-ever-base-100 rounded" : "",
                    totalCount > MAX_PAYEES_IN_PLAN || totalCount === 0
                      ? "cursor-not-allowed"
                      : "cursor-pointer hover:bg-ever-base-200"
                  )}
                  onClick={() => {
                    if (totalCount > MAX_PAYEES_IN_PLAN || totalCount === 0)
                      return;
                    if (allPayeesSelected) {
                      handleAllPayeesSelectedChange(false);
                      if (isEmpty(searchValue)) {
                        handleTotalCountChange(initialTotalCount);
                        handleFetchedUsersChange(initialUsers);
                      }
                    } else {
                      handleAllPayeesSelectedChange(true);
                    }
                  }}
                >
                  <div className="flex flex-col pl-1 py-2">
                    <EverTg.Heading4
                      className={twMerge(
                        totalCount > MAX_PAYEES_IN_PLAN || totalCount === 0
                          ? "text-ever-base-content-mid"
                          : allPayeesSelected
                          ? "text-ever-primary"
                          : "text-ever-base-content"
                      )}
                    >
                      Select all payees in period {`(${totalCount} payees)`}
                    </EverTg.Heading4>

                    <div className="flex items-center gap-2 mt-2">
                      {totalCount > MAX_PAYEES_IN_PLAN ? (
                        <>
                          <AlertCircleIcon className="text-ever-warning h-5 w-5" />
                          <EverTg.SubHeading4 className="">
                            {`Can't use this option when there are more than 100
                            payees in the plan`}
                          </EverTg.SubHeading4>
                        </>
                      ) : (
                        <EverTg.Text
                          className={twMerge(
                            totalCount === 0
                              ? "text-ever-base-content-mid"
                              : "text-ever-base-content"
                          )}
                        >
                          This might take a while.
                        </EverTg.Text>
                      )}
                    </div>
                  </div>
                  <div
                    className={twMerge(
                      "mr-1",
                      allPayeesSelected ? "block" : "hidden"
                    )}
                  >
                    <CheckIcon className="w-6 h-6 text-ever-primary" />
                  </div>
                </div>
                <EverDivider className="mt-1 mb-3 ml-1" />
                <EverTg.Heading4 className="ml-1.5">
                  Select individual payees in period (Max 5 payees)
                </EverTg.Heading4>
              </div>
            );
          }}
          minSearchChar={MIN_SEARCH_CHAR}
          onDropdownVisibleChange={(open) => {
            if (!open && !allPayeesSelected) {
              handleTotalCountChange(initialTotalCount);
              handleFetchedUsersChange(initialUsers);
            }
          }}
          searchCbk={(searchValue) => {
            if (searchValue.length < MIN_SEARCH_CHAR) {
              handleTotalCountChange(initialTotalCount);
              handleFetchedUsersChange(initialUsers);
            }
          }}
        />
      </div>
      <div className="ml-auto">
        <EverButton
          disabled={isAnalyzeButtonDisabled}
          size="small"
          color="primary"
          type="filled"
          onClick={evaluateCriteriaExpression}
        >
          Simulate
        </EverButton>
      </div>
    </div>
  );
}
