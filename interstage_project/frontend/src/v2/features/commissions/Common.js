import styled from "@emotion/styled";
import {
  ArrowNarrowUpIcon,
  MessageTextSquareIcon,
  ReverseLeftIcon,
} from "@everstage/evericons/outlined";
import {
  CheckCircleIcon,
  DownloadIcon,
  HourglassIcon,
  MinusCircleIcon,
  XCircleIcon,
} from "@everstage/evericons/solid";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

import {
  PAYMENT_STATUS,
  APPROVAL_STATUS,
  PAYOUT_APPROVAL_TYPES,
} from "~/Enums";
import {
  EverActiveBadge,
  EverBadge,
  EverGroupAvatar,
  EverModal,
  EverTg,
  EverTooltip,
  EverButton,
} from "~/v2/components";

export const Tag = (props) => {
  let type = "";
  let className = "";
  let innerClassName = "";
  const Icon = props.icon;

  switch (props.value) {
    case PAYMENT_STATUS.PAID: {
      type = "success";
      break;
    }
    case PAYMENT_STATUS.UNPAID: {
      type = "error";
      break;
    }
    case PAYMENT_STATUS.PARTIALLY_PAID: {
      type = "warning";
      break;
    }
    case PAYMENT_STATUS.ZERO_PAYOUT: {
      type = "custom";
      className =
        "text-ever-chartColors-20 border-ever-chartColors-20/30 bg-ever-chartColors-41";
      innerClassName = "bg-ever-chartColors-12";
      break;
    }
    case PAYMENT_STATUS.OVER_PAID: {
      type = "custom";
      className =
        "text-ever-chartColors-29 border-ever-chartColors-29/40 bg-ever-chartColors-36";
      innerClassName = "bg-ever-chartColors-29";
      break;
    }
  }
  return (
    <div className={props.className}>
      {Icon ? (
        <EverBadge
          className={"font-medium " + className}
          title={props.value}
          type={type}
          icon={<Icon className="size-3.5" />}
        />
      ) : (
        <EverActiveBadge
          description={props.value}
          type={type}
          className={className}
          innerClassName={innerClassName}
        />
      )}
    </div>
  );
};

export const ApprovalTag = (props) => {
  const { value, handleClick, payoutLevel, record } = props;

  const [showRejectReasonModal, setShowRejectReasonModal] = useState(false);

  const updatedByDetails = [
    APPROVAL_STATUS.WITHDRAWN,
    APPROVAL_STATUS.REJECTED,
    APPROVAL_STATUS.REVOKED,
  ].includes(value)
    ? record.approvals_updated_by
    : null;

  const {
    rejected_details: rejectReason = null,
    payee_name: rejectedPayee = null,
    period: rawPeriod = null,
  } = value === APPROVAL_STATUS.REJECTED ? record : {};

  const rejectedBy =
    value === APPROVAL_STATUS.REJECTED && record.approvals_updated_by
      ? `${record.approvals_updated_by.first_name} ${record.approvals_updated_by.last_name}`
      : null;

  const period = rawPeriod
    ? new Date(rawPeriod.replace(/-/g, " ")).toLocaleString("en-US", {
        month: "long",
        year: "numeric",
      })
    : null;

  const tagType = (val) => {
    switch (val) {
      case APPROVAL_STATUS.APPROVED: {
        return {
          text: "Approved",
          type: "success",
          icon: CheckCircleIcon,
        };
      }
      case APPROVAL_STATUS.REJECTED: {
        return {
          text: "Rejected",
          type: "error",
          icon: XCircleIcon,
        };
      }
      case APPROVAL_STATUS.WITHDRAWN: {
        return {
          text: "Withdrawn",
          type: "custom",
          icon: DownloadIcon,
          icon_class: "-rotate-90",
          color_class:
            "text-ever-chartColors-20 border-ever-chartColors-20/20 bg-ever-chartColors-20/5",
        };
      }
      case APPROVAL_STATUS.ABORTED: {
        return {
          text: "Auto cancelled",
          type: "warning",
          icon: MinusCircleIcon,
        };
      }
      case APPROVAL_STATUS.REQUESTED: {
        return {
          text: "Requested",
          type: "info",
          icon: HourglassIcon,
        };
      }
      case APPROVAL_STATUS.NEEDS_ATTENTION: {
        return {
          text: "Needs Attention",
          type: "warning",
          icon: XCircleIcon,
        };
      }
      case APPROVAL_STATUS.REVOKED: {
        return {
          text: "Revoked",
          type: "error",
          icon: ReverseLeftIcon,
        };
      }
    }
    return null;
  };

  const Icon = tagType(value).icon;

  return (
    <div>
      {value === "not_started" ? null : (
        <div className="flex items-center gap-2">
          <EverBadge
            className={twMerge("font-medium", tagType(value).color_class)}
            title={tagType(value).text}
            type={tagType(value).type}
            icon={
              <Icon
                className={twMerge("w-3.5 h-3.5", tagType(value).icon_class)}
              />
            }
          />
          {updatedByDetails && (
            <>
              <EverTg.Text> by </EverTg.Text>
              <EverTooltip
                title={`${updatedByDetails.first_name} ${updatedByDetails.last_name}`}
              >
                <div className="flex items-center gap-2">
                  <EverGroupAvatar
                    avatars={[
                      {
                        firstName: updatedByDetails.first_name,
                        lastName: updatedByDetails.last_name,
                        image: updatedByDetails.image,
                      },
                    ]}
                    groupMaxCount={1}
                    limitInPopover={1}
                  />
                </div>
              </EverTooltip>
            </>
          )}
          {rejectReason && (
            <>
              <div
                className="text-ever-base-content-mid cursor-pointer hover:text-ever-primary"
                onClick={() => setShowRejectReasonModal(true)}
              >
                <EverTooltip title="View Reject Reason">
                  <MessageTextSquareIcon className="w-5 h-5" />
                </EverTooltip>
              </div>
            </>
          )}
          {payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL && (
            <div
              className={`w-5 h-5 rounded-sm flex items-center justify-center bg-ever-base-100 cursor-pointer`}
              onClick={() => {
                if (payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL) {
                  handleClick();
                }
              }}
            >
              <ArrowNarrowUpIcon
                className={`text-ever-primary w-4 h-4 transform rotate-45`}
              />
            </div>
          )}
          {showRejectReasonModal && (
            <EverModal
              visible={showRejectReasonModal}
              title="Reject reason"
              onCancel={() => setShowRejectReasonModal(false)}
              closable={false}
              footer={[
                <EverButton
                  key="Close"
                  color="primary"
                  onClick={() => setShowRejectReasonModal(false)}
                >
                  Close
                </EverButton>,
              ]}
            >
              <div>
                <EverTg.Text className="text-ever-base-content">
                  <span className="font-semibold capitalize">
                    {rejectedBy || "-"}
                  </span>{" "}
                  rejected{" "}
                  <span className="font-semibold capitalize">
                    {rejectedPayee}
                  </span>
                  <span className="font-semibold">{"’s"}</span> {period} payout.
                </EverTg.Text>
                <br />
                <br />
                <EverTg.Text className="text-ever-base-content">
                  {rejectReason}
                </EverTg.Text>
              </div>
            </EverModal>
          )}
        </div>
      )}
    </div>
  );
};

export const ActionWrapper = styled.div`
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

export const BulkActionWrapperStyled = styled.div((props) => ({
  transition: "all 0.3s ease-out",
  zIndex: 1,
  height: props.isCollapsed ? "0px" : "auto",
  margin: props.isCollapsed ? "0px" : "0px 0px 12px",
  opacity: props.isCollapsed ? 0 : 1,
  transform: props.isCollapsed ? "translateY(-32px)" : "translateY(0)",
}));

export const TransactionText = ({ isPaid, children = null }) => {
  return (
    <div
      className={`${
        isPaid
          ? "text-ever-base-content"
          : "text-ever-base-content-mid line-through"
      }`}
    >
      {children}
    </div>
  );
};
