import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { AlertTriangleIcon } from "@everstage/evericons/solid";
import { format } from "date-fns";
import _, { isEmpty } from "lodash";
import React from "react";
import { Link } from "react-router-dom";

import {
  CALCULATION_STATUS,
  COMMISSION_ACTION_STATUS,
  UNLOCK_STATUS,
} from "~/Enums";
import { EverModal, EverButton, EverTooltip } from "~/v2/components";

let showWarningMessage = true;

const FreezeConfirmationPopup = (props) => {
  const {
    type,
    onClose,
    onConfirm,
    tableData,
    selectedRowKeys,
    isLoading,
    approvalFeatureEnabled,
    showSubTitle = true,
    period,
    t,
    allowAdjustmentsToFrozenCommission,
  } = props;
  if (isEmpty(type)) {
    showWarningMessage = true;
    return null;
  }

  const showCommAdjWarning = selectedRowKeys.some((email) => {
    const user = tableData.find((obj) => obj.payee_email_id === email);
    return user && user.is_comm_adj_requested;
  });

  let text = "lock";
  let infoText = "locked";
  const payees = [];
  const payeesToSkip = [];
  const calculationStatus =
    type === COMMISSION_ACTION_STATUS.FREEZE
      ? CALCULATION_STATUS.NOTFROZEN
      : CALCULATION_STATUS.FROZEN;

  tableData.forEach((data) => {
    if (selectedRowKeys.includes(data.payee_email_id)) {
      if (
        data.comm_calc_status === calculationStatus ||
        data.settlement_calc_status === calculationStatus
      ) {
        payees.push(data.payee_email_id);
      } else {
        payeesToSkip.push(data.payee_email_id);
      }
    }
  });

  if (type === COMMISSION_ACTION_STATUS.UNFREEZE) {
    text = "Unlock";
    infoText = "unlocked";
  }

  if (payees.length === 0) {
    if (showWarningMessage) {
      showWarningMessage = false;
      EverModal.warning({
        title: `Selected payee(s) are already ${infoText}`,
        onOk: onClose,
      });
    }
    return null;
  }

  function getPeriod() {
    return format(new Date(period), "yyyy-MM-dd");
  }

  return (
    <EverModal.Confirm
      visible={true}
      width="560px"
      type="warning"
      title={`Are you sure you want to ${text} Statement(s) for selected payee(s)?`}
      subtitle={
        <div className="mt-1 flex flex-col gap-1">
          <span>{`Statement will be ${infoText} for ${payees.length} payee(s)`}</span>
          {payeesToSkip.length > 0 && (
            <span>{`Statement is already ${infoText} for ${payeesToSkip.length} payee(s)`}</span>
          )}
          {approvalFeatureEnabled &&
            type === COMMISSION_ACTION_STATUS.UNFREEZE &&
            showSubTitle && <span>{UNLOCK_STATUS}</span>}
          {showCommAdjWarning &&
            !allowAdjustmentsToFrozenCommission &&
            type === COMMISSION_ACTION_STATUS.FREEZE && (
              <div className="flex items-center gap-2">
                <AlertTriangleIcon className="w-4 h-4 text-ever-warning" />
                <div>
                  Review pending approvals{" "}
                  <Link
                    to={`/settings/adjustments?status=requested&period=${getPeriod()}`}
                    className="hover:underline text-ever-base-primary"
                  >
                    here
                  </Link>
                </div>
                <EverTooltip
                  title={`Some of the selected statements requires ${t(
                    "COMMISSION_ADJUSTMENTS"
                  ).toLowerCase()} that need to be approved. Locking statements shall cancel requested ${t(
                    "ADJUSTMENTS"
                  ).toLowerCase()}.`}
                >
                  <InfoCircleIcon className="w-4 h-4" />
                </EverTooltip>
              </div>
            )}
        </div>
      }
      confirmationButtons={[
        <EverButton key="cancel" color="base" onClick={onClose} type="ghost">
          Cancel
        </EverButton>,
        <EverButton
          key="accept"
          loading={isLoading}
          onClick={() => onConfirm(payees)}
        >
          {_.capitalize(text)}
        </EverButton>,
      ]}
    />
  );
};

export default FreezeConfirmationPopup;
