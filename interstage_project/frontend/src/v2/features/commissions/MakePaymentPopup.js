import {
  AlertSquareIcon,
  CreditCardMinusIcon,
} from "@everstage/evericons/solid";
import { encode as base64_encode } from "base-64";
import { cloneDeep } from "lodash";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { DATE_FORMATS, CALCULATION_STATUS, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import {
  EverButton,
  EverModal,
  EverGroupAvatar,
  EverInput,
  EverLabel,
  EverDivider,
  EverTg,
} from "~/v2/components";
import { Tag } from "~/v2/features/commissions/Common";
import {
  getCurrencyValue,
  roundToTwoDecimals,
  isSkipPayment,
} from "~/v2/features/commissions/constants";

const { DDMMMYYYY } = DATE_FORMATS;
let showWarningMessage = true;

const PayeePaymentDetails = (props) => {
  const { payee, selectedPayees, constructInitialData, handleChange, index } =
    props;
  let pendingAmount = roundToTwoDecimals(payee.pending_amount);
  let currencySymbolWithPrefix = payee.payee_currency_symbol;
  let prefix = "";
  if (pendingAmount.includes("-")) {
    prefix = "-";
    currencySymbolWithPrefix = `${prefix} ${currencySymbolWithPrefix}`;
    pendingAmount = Number(pendingAmount.replace(/-/i, "")) || "";
  }
  const [value, setValue] = useState(formatCurrency(pendingAmount));
  const [comment, setComment] = useState("");

  const { t } = useTranslation();

  const getAmount = (amount) =>
    amount ? Number.parseFloat(`${prefix}${amount}`) : 0;

  useEffect(() => {
    constructInitialData(payee, getAmount(pendingAmount), comment, index);
  }, []);

  const profileData = {
    payeeEmailId: payee.payee_email_id,
    psd: payee.period_start_date,
    ped: payee.period_end_date,
  };
  const encodedData = base64_encode(JSON.stringify(profileData));

  const onChange = (value) => {
    const splitData = String(value).split(".");
    if (
      splitData?.length > 1 &&
      (splitData?.[1]?.includes(",") || splitData?.[1]?.length > 2)
    ) {
      return;
    }
    const formattedValue = value.replaceAll(",", "");
    const isUpdateValue =
      formattedValue == "" ||
      (Number(formattedValue) >= 0 &&
        Number(formattedValue) <= Number(pendingAmount));
    if (isUpdateValue) {
      setValue(value);
      handleChange(payee, getAmount(formattedValue), comment);
    }
  };

  const onBlur = (value) => {
    const formattedValue = value.replaceAll(",", "");
    if (/^(\d+\.?\d*|\.\d+|\s*)$/.test(formattedValue)) {
      setValue(formatCurrency(formattedValue));
      handleChange(payee, getAmount(formattedValue), comment);
    }
  };

  const handleCommentChange = (event) => {
    const formattedValue = value.replaceAll(",", "");
    setComment(event.target.value);
    handleChange(payee, getAmount(formattedValue), event.target.value);
  };

  return (
    <div
      key={payee.payee_email_id}
      className={
        selectedPayees.length > 1
          ? `flex flex-wrap gap-y-8 gap-x-6 ${
              index > 0 ? "mt-3" : ""
            } bg-ever-base border border-solid border-ever-base-400 rounded-lg p-4`
          : "flex flex-col gap-y-8"
      }
    >
      <div className="flex grow shrink basis-full items-center justify-between">
        <div className="flex">
          <div className="flex items-center gap-x-3">
            <EverGroupAvatar
              avatars={[
                {
                  firstName: payee.payee_name,
                  lastName: "",
                  image: payee.profile_picture,
                  className: "w-12 h-12",
                },
              ]}
              groupMaxCount={1}
              limitInPopover={1}
            />
            <div className="flex flex-col gap-y-1">
              <div className="flex flex-wrap gap-x-2 gap-y-1">
                <Link
                  to={`/statements/${encodedData}`}
                  className="text-lg text-ever-base-content font-semibold hover:underline hover:text-ever-base-content"
                >
                  {payee.payee_name}
                </Link>
                <Tag value={payee.payment_status} icon={CreditCardMinusIcon} />
              </div>
              <EverTg.Caption className="text-ever-base-content-low">
                {payee.payee_email_id}
              </EverTg.Caption>
            </div>
          </div>
        </div>
        <div className="flex shrink-0 gap-4">
          <div className="flex flex-col self-start h-full bg-ever-success-lite rounded-lg py-1.5 px-4 border border-solid border-ever-chartColors-51">
            <EverTg.Caption className="text-ever-base-content">
              {t("TOTAL_PAYOUT")}
            </EverTg.Caption>
            <EverTg.SubHeading3 className="text-ever-base-content">
              {getCurrencyValue(
                payee.payee_currency_symbol,
                payee.total_payout
              )}
            </EverTg.SubHeading3>
          </div>
          <div className="flex flex-col self-start h-full bg-ever-warning-lite rounded-lg py-1.5 px-4 border border-solid border-ever-chartColors-34">
            <EverTg.Caption className="text-ever-base-content">
              To be paid
            </EverTg.Caption>
            <EverTg.SubHeading3 className="text-ever-base-content">
              {getCurrencyValue(
                payee.payee_currency_symbol,
                payee.pending_amount
              )}
            </EverTg.SubHeading3>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <EverLabel className="mb-2">Amount</EverLabel>
        <EverInput
          className="w-40"
          prefix={currencySymbolWithPrefix}
          suffix={payee.payee_currency}
          value={value}
          onChange={(event) => onChange(event.target.value)}
          onBlur={(event) => onBlur(event.target.value)}
        />
      </div>
      <div className="flex flex-col flex-auto">
        <EverLabel className="mb-2">Comments (optional)</EverLabel>
        <EverInput.TextArea
          placeholder="Add your comments"
          value={comment}
          onChange={handleCommentChange}
          {...(selectedPayees.length === 1
            ? {
                autoSize: { minRows: 3 },
              }
            : { autoSize: { minRows: 1 } })}
        />
      </div>
    </div>
  );
};

const MakePaymentPopup = (props) => {
  const {
    keys,
    onClose,
    onConfirm,
    tableData,
    periodLabel,
    period,
    isLoading,
    email,
    viewPayoutValueOfOthers,
  } = props;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const [data, setData] = useState({
    payeeIds: [],
    settlementDetails: {},
  });
  const [isFullPayment, setIsFullPayment] = useState(true);
  const { hasPermissions } = useUserPermissionStore();

  if (keys.length <= 0) {
    showWarningMessage = true;
    return null;
  }

  let tempData = {
    payeeIds: [],
    settlementDetails: {},
  };
  const selectedPayees = [];
  let unfrozenPayeeCount = 0;
  for (const data of tableData) {
    const isFrozen =
      hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS) ||
      data.comm_calc_status === CALCULATION_STATUS.FROZEN ||
      data.settlement_calc_status === CALCULATION_STATUS.FROZEN;

    if (
      keys.includes(data.payee_email_id) &&
      !isSkipPayment(data.payment_status) &&
      (data.payee_email_id === email || viewPayoutValueOfOthers) &&
      isFrozen
    ) {
      selectedPayees.push(data);
    }

    if (keys.includes(data.payee_email_id) && !isFrozen) {
      unfrozenPayeeCount++;
    }
  }
  if (selectedPayees.length === 0) {
    if (
      !hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS) &&
      showWarningMessage &&
      unfrozenPayeeCount > 0
    ) {
      showWarningMessage = false;
      EverModal.warning({
        title:
          "You can register payment only for locked payouts. Unlocked payouts will be skipped.",
        onOk: onClose,
      });
    }
    if (showWarningMessage) {
      showWarningMessage = false;
      EverModal.warning({
        title: `Payment is already done for selected payee(s)`,
        onOk: onClose,
      });
    }

    return null;
  }

  const constructData = (data, payee, value, comment) => {
    const clonedData = cloneDeep(data);
    const { [payee.payee_email_id]: selectedPayee, ...remainingData } =
      clonedData["settlementDetails"];
    if (!clonedData["payeeIds"].includes(payee.payee_email_id)) {
      if (value !== 0) {
        clonedData["payeeIds"].push(payee.payee_email_id);
      }
    } else if (value === 0) {
      const payeeIndex = clonedData["payeeIds"].indexOf(payee.payee_email_id);
      clonedData["payeeIds"].splice(payeeIndex, 1);
    }
    clonedData["settlementDetails"] = {
      ...remainingData,
      ...(value !== 0 && {
        [payee.payee_email_id]: {
          calculated_adjustments: payee.payout_split_up,
          paid_amount: value,
          payee_email_id: payee.payee_email_id,
          comment,
          period_start_date: payee.period_start_date,
          period_end_date: payee.period_end_date,
        },
      }),
    };
    return clonedData;
  };

  const constructInitialData = (payee, value, comment, index) => {
    const newData = constructData(tempData, payee, value, comment);
    tempData = newData;
    if (selectedPayees.length - 1 === index) {
      setData(tempData);
    }
  };

  const handleChange = (payee, value, comment) => {
    const newData = constructData(data, payee, value, comment);
    setIsFullPayment(
      Number.parseFloat(value) ===
        Number(roundToTwoDecimals(payee.pending_amount))
    );
    setData(newData);
  };

  const getDisplayDate = () => {
    if (clientFeatures.customCalendar) {
      return moment(period).format(DDMMMYYYY);
    }
    return periodLabel;
  };

  return (
    <EverModal
      title={
        <div className="flex gap-2">
          <span>Register payment</span>
          <EverDivider type="vertical" />
          <EverLabel className="text-lg font-medium">
            {getDisplayDate()}
          </EverLabel>
        </div>
      }
      centered
      visible={true}
      onCancel={onClose}
      className="min-w-[760px]"
      bodyStyle={{ overflowY: "auto" }}
      footer={
        <div className="w-full">
          <div className="flex items-center">
            <div key="info" className="text-ever-base-content grow text-left">
              <AlertSquareIcon className="w-5 h-5 text-ever-warning align-middle mr-2" />
              <span className="align-middle">
                {selectedPayees.length > 1
                  ? `You are registering payment for ${selectedPayees.length} payees`
                  : isFullPayment
                  ? "You are registering full payment"
                  : "You are registering partial payment"}
              </span>
            </div>
            <EverButton
              key="back"
              color="base"
              type="ghost"
              disabled={isLoading}
              onClick={onClose}
            >
              Cancel
            </EverButton>
            <EverButton
              key="submit"
              disabled={data.payeeIds.length === 0}
              loading={isLoading}
              onClick={() => onConfirm(data)}
            >
              Register
            </EverButton>
          </div>
        </div>
      }
    >
      <div>
        {selectedPayees.map((payee, index) => (
          <PayeePaymentDetails
            key={index}
            index={index}
            payee={payee}
            selectedPayees={selectedPayees}
            constructInitialData={constructInitialData}
            handleChange={handleChange}
          />
        ))}
      </div>
    </EverModal>
  );
};

export default MakePaymentPopup;
