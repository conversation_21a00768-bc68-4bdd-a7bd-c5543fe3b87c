import React from "react";

import { UNLOCK_STATUS } from "~/Enums";
import { EverModal, EverButton } from "~/v2/components";

const UnFreezeConfirmationPopup = (props) => {
  const { onCancel, onConfirm, isVisible } = props;

  return (
    <EverModal.Confirm
      visible={isVisible}
      title="Confirm Unlock"
      type="warning"
      mode="inline"
      subtitle={UNLOCK_STATUS}
      confirmationButtons={[
        <EverButton
          key="cancel"
          size="small"
          color="base"
          onClick={onCancel}
          type="ghost"
        >
          Cancel
        </EverButton>,
        <EverButton key="accept" size="small" onClick={onConfirm}>
          Confirm
        </EverButton>,
      ]}
    />
  );
};

export default UnFreezeConfirmationPopup;
