import { format } from "date-fns";
import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { EverButton, EverModal, EverTg } from "~/v2/components";

export function CommissionAdjustmentModal({
  visible,
  onCancel,
  onLock,
  period,
  payeeEmail,
}) {
  const { t } = useTranslation();

  function getPeriod() {
    return format(new Date(period), "yyyy-MM-dd");
  }

  return (
    <EverModal.Confirm
      visible={visible}
      type="warning"
      title={`The selected statement has ${t(
        "COMMISSION_ADJUSTMENTS"
      ).toLowerCase()} that need to be approved.`}
      subtitle={
        <>
          <EverTg.Description>
            If the statement is locked, all ‘requested’{" "}
            {t("ADJUSTMENTS").toLowerCase()} will be ‘canceled’.
          </EverTg.Description>
          <br />
          <EverTg.Description>
            Review {t("COMMISSION_ADJUSTMENTS").toLowerCase()} requests{" "}
            <Link
              to={`/settings/adjustments?status=requested&period=${getPeriod()}&search=${payeeEmail}`}
              className="hover:underline text-ever-base-primary"
            >
              here
            </Link>
          </EverTg.Description>
        </>
      }
      confirmationButtons={[
        <EverButton type="ghost" color="base" key="confirm" onClick={onCancel}>
          Agree, skip locking the statement.
        </EverButton>,
        <EverButton key="cancel" onClick={onLock}>
          No, lock the statement.
        </EverButton>,
      ]}
    />
  );
}
