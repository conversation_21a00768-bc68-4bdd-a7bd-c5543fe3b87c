import {
  DataflowIcon,
  DotsHorizontalIcon,
  LockIcon,
  MinusCircleIcon,
  XCloseIcon,
  CreditCardIcon,
  ChevronDownIcon,
  XlsxFileIcon,
  PdfFileIcon,
  InfoCircleIcon,
  ReverseLeftIcon,
  AlertCircleIcon,
} from "@everstage/evericons/outlined";
import { CircleIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { Dropdown, Menu } from "antd";
import { encode as base64_encode } from "base-64";
import { isEmpty, isNaN, isNumber, union, isNull } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import moment from "moment";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery as useReactQuery } from "react-query";
import { Link, useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  fetchActiveTasks,
  getApprovalConfig,
} from "~/Api/ApprovalWorkflowService";
import { validateUpdateFreezeStatus } from "~/Api/CommissionActionService";
import { downloadLockedXLSheet } from "~/Api/StatementsService";
import {
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  APPROVAL_STATUS,
  CALCULATION_STATUS,
  COMMISSION_ACTION_STATUS,
  DATATYPE,
  HEADER_STATE,
  LINE_ITEM_TYPE_ENTITY_TYPE,
  PAYMENT_STATUS,
  RBAC_ROLES,
  SUPABASE_CONSTANTS,
  PLATFORM,
  PAYOUT_APPROVAL_TYPES,
  STATEMENT_EXPORT_TYPES,
  COMMON_MOMENT_DATE_FORMAT,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverDivider,
  EverHotToastMessage,
  EverInteractiveChip,
  EverModal,
  EverTooltip,
  EverGroupAvatar,
  PollingBanner,
  toast,
  useCheckboxSelection,
  EverTg,
  message,
  EverBadge,
  EverGroupViewAllAvatars,
  EverHotToastAlert,
} from "~/v2/components";
import {
  CustomHeader,
  everAgGridOptions,
  DynamicPagination as Pagination,
  EverAgCellTooltip,
  everAgGridCallbacks,
} from "~/v2/components/ag-grid";
import {
  CheckboxRenderer,
  GroupHeaderCheckbox,
} from "~/v2/components/ag-grid/cellRenderers";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { UserStatusIndicator } from "~/v2/components/StatusIndicator";
import { ApprovalWorkFlowTemplateSelectorWrapper } from "~/v2/features/approvals/ApprovalWorkFlowSelectorWrapper";
import {
  ignoreArrear,
  invalidatePayout,
  setFreezeStatus,
  processArrear,
  setPaidStatus,
  downloadBulkStatements,
  setFreezeStatusWithPendingChanges,
  setPaidStatusV2,
} from "~/v2/features/commissions/api";
import {
  ActionWrapper,
  ApprovalTag,
  BulkActionWrapperStyled,
  Tag,
  TransactionText,
} from "~/v2/features/commissions/Common";
import {
  bulkActionButtons,
  freezeUnfreezeOption,
  getCurrencyValue,
} from "~/v2/features/commissions/constants";
import FreezeConfirmationPopup from "~/v2/features/commissions/FreezeConfirmationPopup";
import MakePaymentPopup from "~/v2/features/commissions/MakePaymentPopup";
import ProcessArrearPopup from "~/v2/features/commissions/ProcessArrearPopup";
import StatementDownloadPopup from "~/v2/features/commissions/StatementDownloadPopup";
import UnFreezeConfirmationPopup from "~/v2/features/commissions/UnfreezeConfirmationPopup";

import { ApprovalDrawer } from "./ApprovalDrawer";
import "./styles.module.scss";
import { CommissionAdjustmentModal } from "./CommissionAdjustmentModal";
import { SidePanelRenderer } from "./PayoutsSidePanel";
import { isSkipPayment } from "../constants";
import { useCommissionStore } from "../store";

const { adjustColumnWidth } = everAgGridCallbacks;

const WarningModal = ({ isVisible, title, message, handleOk }) => {
  return (
    <EverModal footer={null} closable={false} visible={isVisible} width={600}>
      <div className="flex flex-col items-center mx-4">
        <div className="w-14 px-4 py-4 my-4 bg-ever-warning-lite rounded-full">
          <AlertCircleIcon className="text-ever-warning" />
        </div>
        <EverTg.Heading1>{title}</EverTg.Heading1>
        <EverTg.Description className="py-4 text-center">
          {message}
        </EverTg.Description>
        <EverButton
          onClick={handleOk}
          type="filled"
          color="primary"
          size="small"
          className="mt-8 mb-3"
        >
          Done
        </EverButton>
      </div>
    </EverModal>
  );
};

const CommissionTable = observer((props) => {
  const {
    date,
    gridApi,
    period,
    periodLabel,
    currentDataSource: rowData,
    customFields,
    onGridReady,
    sortCallback,
    orderbyFields,
    refetch,
    getArrear,
    isPayout,
    isLoading,
    setExportableColumns,
    tableData,
    pageCount,
    pageSize,
    totalRows,
    currentPage,
    setPageSize,
    setCurrentPage,
    getBulkActionsData,
    approvalFeatureEnabled,
    handleBulkEmailStatements,
    updates,
    customCalendar,
    showApprovalFeature,
    onClickRequestApproval,
    onClickRevokeApproval,
    approvalParams,
    allowAdjustmentsToFrozenCommission,
    loading,
    applyFiltersFun,
    handleCustomizeColumnsChange,
    isFilterSelected,
  } = props;

  const gridRef = useRef();
  const { dataTypesById } = useVariableStore();
  const { hasPermissions } = useUserPermissionStore();
  const [columnDefs, setColumnDefs] = useState([]);
  const [showColumn, setToggleColumn] = useState(false);
  const [makePayment, setMakePayment] = useState([]);
  const [freezeConfirmation, setFreezeConfirmation] = useState("");
  const [payeesOnAction, setPayeesOnAction] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedPayee, setSelectedPayee] = useState({});
  const [showApprovalTemplateSelector, setShowApprovalTemplateSelector] =
    useState(false);

  const [bulkActionsDataState, setBulkActionsDataState] = useState([]);
  const [bulkActionsPaymentDataState, setBulkActionsPaymentDataState] =
    useState([]);
  const [approvalDrawerVisibility, setApprovalDrawerVisibility] =
    useState(false);
  const [selectedPayeeName, setSelectedPayeeName] = useState("");
  const [selectedPayeeCurrency, setSelectedPayeeCurrency] = useState("");
  const [selectedPayeePayout, setSelectedPayeePayout] = useState(0);
  const [selectedPayeeEntityKey, setSelectedPayeeEntityKey] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("");
  const [payoutLevel, setPayoutLevel] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );
  const { setIsLoading, selectedFilter } = useCommissionStore();
  const [tableState, setTableState] = useState(null);
  const [sidePanelColumns, setSidePanelColumns] = useState([]);
  const { accessToken, email } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const [payoutApprovalsEnabled, setPayoutApprovalsEnabled] = useState(false);
  const [isSelectedRecordLocked, setIsSelectedRecordLocked] = useState(false);
  const [showBulkDownloadStatements, setShowBulkDownloadStatements] =
    useState(true);
  const [downloadStatementType, setDownloadStatementType] = useState("");

  const [pendingChangesModalDetails, setPendingChangesModalDetails] = useState({
    show: false,
    title: "",
    message: "",
  });

  const [registerPaymentWarningDetails, setRegisterPaymentWarningDetails] =
    useState({
      isVisible: false,
      title: "",
      message: "",
    });

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;

      setPayoutLevel(approvalType);
      setPayoutApprovalsEnabled(config?.payoutApprovals?.enabled || false);
    }
  }, [approvalConfigData]);

  const location = useLocation();
  const { pathname } = location;

  const parentRef = useRef();

  const { t } = useTranslation();

  const { rolesRefetch } = useEmployeeStore();

  const comparator = (valueA, valueB) => {
    if (typeof valueA === "string") {
      return valueA.localeCompare(valueB, undefined, {
        numeric: true,
      });
    }
    return valueA - valueB;
  };

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      sortable: false,
      suppressMenu: true,
      filter: true,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
      autoHeight: true,
      minWidth: 150,
      comparator: comparator,
      resizable: true,
      cellStyle: { whiteSpace: "nowrap" },
    };
  }, []);

  const {
    cumulativeSet,
    cumulativeList,
    headerCbxState,
    setHeaderCbxState,
    setSelectedEmailSet,
    clearSelection,
    maxSelectionCount,
    cumulativeSelectionData,
  } = useCheckboxSelection({
    data: rowData,
    currentPage,
    pageSize,
    key: "payee_email_id",
    maxSelectionCount: 100,
  });

  const renderNameColumn = (props) => {
    let profileData = {
      payeeEmailId: props.data.payee_email_id,
      psd: props.data.period_start_date,
      ped: props.data.period_end_date,
    };
    let encodedData = base64_encode(JSON.stringify(profileData));
    return (
      <div
        className="flex items-center gap-x-3"
        data-testid={`pt-${props?.data?.payee_email_id}-name-cell`}
      >
        <EverGroupAvatar
          avatars={[
            {
              name: props.data.payee_name,
              image: props.data.profile_picture,
              className: "w-8 h-8",
            },
          ]}
          groupMaxCount={1}
          limitInPopover={1}
        />
        <div className="flex flex-col min-w-0">
          <EverTooltip mouseEnterDelay={0.5} title={props.value}>
            <Link
              className="inline-block truncate text-sm text-ever-base-content font-medium hover:underline hover:text-ever-base-content"
              to={`/statements/${encodedData}`}
              state={{ backActionRoute: pathname }} //BreadCrumb Back Action
              onClick={() => {
                sendAnalyticsEvent(
                  accessToken,
                  ANALYTICS_EVENTS.PAYEE_STATEMENT,
                  {
                    [ANALYTICS_PROPERTIES.PAYEE_NAME]: props.data.payee_name,
                  }
                );
              }}
            >
              {props.value}
            </Link>
          </EverTooltip>
          <EverTg.Caption className="text-ever-base-content-mid">
            {props.data.payee_email_id}
          </EverTg.Caption>
        </div>
      </div>
    );
  };

  const getExpandSelectParams = (
    cellClassRuleNotExpandable,
    cellClassRuleExpandable,
    isDateColumn = false
  ) => ({
    // TODO: For now Bulk action disabled for arrears
    // headerCheckboxSelection: isPayout,
    // headerCheckboxSelectionFilteredOnly: isPayout,
    cellRenderer: "agGroupCellRenderer",
    cellRendererParams: {
      // checkbox: isPayout,
      suppressDoubleClickExpand: false,
      suppressEnterExpand: true,
      suppressCount: true,
      innerRenderer: (props) => {
        return isPayout ? (
          renderNameColumn(props)
        ) : isDateColumn ? (
          <span>{props.value ? props.value : ""}</span>
        ) : (
          <span>{props.value ? props.value : ""}</span>
        );
      },
    },
    cellClassRules: {
      [`notExpandable ${cellClassRuleNotExpandable}`]: (params) => {
        return !params.data?.payout_transactions?.length > 0;
      },
      [`${cellClassRuleExpandable}`]: (params) => {
        return params.data?.payout_transactions?.length > 0;
      },
    },
  });

  useEffect(() => {
    // THIS IS FOR UPDATING SELECTION UI IN AG-GRID FROM 'cumulative'
    if (!isEmpty(columnDefs)) {
      const stopSelection =
        cumulativeSet.size >= maxSelectionCount &&
        headerCbxState !== HEADER_STATE.EVERYTHING;
      gridApi?.forEachNode((node) => {
        const { payee_email_id } = node.data;
        if (stopSelection) {
          node.setDataValue(
            "checkbox",
            cumulativeSet.has(payee_email_id) ? true : "blocked"
          );
        } else {
          node.setDataValue("checkbox", cumulativeSet.has(payee_email_id));
        }
      });
    }
  }, [cumulativeSet, columnDefs]);

  const onToggleCheckbox = ({ e, data }) => {
    if (e.target.checked) {
      setSelectedEmailSet(
        (previousState) => new Set([...previousState, data?.payee_email_id])
      );
    } else {
      setSelectedEmailSet(
        (previousState) =>
          new Set([...previousState].filter((x) => x !== data?.payee_email_id))
      );
    }
    setHeaderCbxState(HEADER_STATE.PARTIAL);
  };

  const onToggleHeaderCbx = (e) => {
    e.stopPropagation();
    e.preventDefault();
    if (headerCbxState === HEADER_STATE.NONE) {
      setHeaderCbxState(HEADER_STATE.ALL);
    } else {
      setHeaderCbxState(HEADER_STATE.NONE);
    }
  };

  const renderColumns = () => {
    const payoutHiddenColumnProps = {
      hide: true,
      suppressColumnsToolPanel: !isPayout,
      cellRenderer: (props) => (props.value ? String(props.value) : "--"),
    };

    const numberFilter = (field) => ({
      filter: "agNumberColumnFilter",
      filterParams: {
        suppressAndOrCondition: true,
      },
      valueGetter: (params) => {
        const value = params.data[field];
        if (!isEmpty(value)) {
          return Number(value);
        }
        return value;
      },
    });

    const dateFilter = {
      filter: "agDateColumnFilter",
      filterParams: {
        suppressAndOrCondition: true,
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          const dateAsString = moment(cellValue).format("DD/MM/YYYY");
          if (dateAsString == null) return -1;
          const dateParts = dateAsString.split("/");
          const cellDate = new Date(
            Number(dateParts[2]),
            Number(dateParts[1]) - 1,
            Number(dateParts[0])
          );
          if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) {
            return 0;
          }
          if (cellDate < filterLocalDateAtMidnight) {
            return -1;
          }
          if (cellDate > filterLocalDateAtMidnight) {
            return 1;
          }
          return null;
        },
      },
      cellRenderer: (props) => {
        return props.value
          ? moment(props.value).format(COMMON_MOMENT_DATE_FORMAT)
          : "--";
      },
    };

    const formattedCustomFields = Object.entries(customFields);
    const customColumns = formattedCustomFields.map((customField) => ({
      headerName: customField[1].value,
      field: customField[0],
      ...payoutHiddenColumnProps,
      ...(dataTypesById[customField[1].data_type] === DATATYPE.DATE && {
        cellRenderer: (props) =>
          props.value
            ? moment(props.value).format(COMMON_MOMENT_DATE_FORMAT)
            : "--",
      }),
      ...(dataTypesById[customField[1].data_type] === DATATYPE.BOOLEAN && {
        cellRenderer: (props) => {
          if (props.value === true) {
            return "True";
          } else if (props.value === false) {
            return "False";
          } else {
            return "--";
          }
        },
      }),
    }));

    const columns = [
      {
        hide:
          !isPayout ||
          (isPayout &&
            !(
              hasPermissions([
                RBAC_ROLES.MANAGE_PAYOUTS,
                RBAC_ROLES.REGISTER_PAYOUTS,
              ]) || showApprovalFeature
            )),
        field: "checkbox",
        colId: "checkbox",
        headerName: "",
        width: 60,
        maxWidth: 68,
        minWidth: 60,
        sortable: false,
        suppressColumnsToolPanel: true,
        suppressSizeToFit: true,
        pinned: "left",
        lockPosition: "left",
        headerClass: "!pl-7",
        // cellStyle: { paddingLeft: "0px", paddingRight: "0px" },
        suppressMenu: true,
        cellRenderer: CheckboxRenderer,
        cellRendererParams: {
          onToggleCheckbox: onToggleCheckbox,
        },
        headerComponent: GroupHeaderCheckbox,
        headerComponentParams: {
          value: headerCbxState,
          onToggleHeaderCbx: onToggleHeaderCbx,
        },
      },
      {
        headerName: "Period",
        field: "custom_period_label",
        pinned: "left",
        lockPinned: true,
        minWidth: 180,
        headerClass: "!pl-12",
        hide: isPayout,
        suppressColumnsToolPanel: true,

        // TODO: For now Bulk action disabled for arrears
        ...(!isPayout && getExpandSelectParams("!pl-11", "!pl-5", true)),
      },
      {
        headerName: "Name",
        field: "payee_name",
        pinned: "left",
        lockPinned: true,
        minWidth: 240,
        maxWidth: 480,
        headerClass: isPayout ? "!pl-7" : "!pl-5",
        wrapText: true,
        suppressColumnsToolPanel: true,
        cellRenderer: renderNameColumn,
        ...(isPayout && getExpandSelectParams("!pl-3", "!pl-0")),
      },
      {
        headerName: "Status",
        field: "payment_status",
        maxWidth: 140,
        cellStyle: { paddingRight: "5px" },
        cellRenderer: (props) => {
          return <Tag value={props.value} />;
        },
      },
      {
        headerName: "Currency",
        field: "payee_currency",
        minWidth: 100,
      },
      {
        headerName: t("PAYOUT"),
        field: "total_payout",
        type: "rightAligned",
        cellRenderer: (props) => {
          const freezed =
            props.data?.comm_calc_status === CALCULATION_STATUS.FROZEN &&
            props.data?.settlement_calc_status === CALCULATION_STATUS.FROZEN;
          const value = parseFloat(props.value, 10);
          return (
            <div
              className="w-full flex items-center justify-end gap-3"
              data-testid={`${props?.data?.payee_email_id} payout`}
            >
              {freezed && (
                <EverTooltip
                  placement="bottom"
                  title={
                    <>
                      <div>
                        {props.data?.comm_freezed_at &&
                        props.data?.comm_freezed_by
                          ? `Statements locked on ${props.data.comm_freezed_at} by ${props.data.comm_freezed_by}`
                          : ""}
                      </div>
                    </>
                  }
                >
                  <LockIcon className="w-[18px] h-[18px] flex-shrink-0 text-ever-base-content stroke-[1.5px]" />
                </EverTooltip>
              )}
              <div data-testid={`pt-payout-${props.data.payee_email_id}`}>
                <EverTg.Text className="text-ever-base-content font-medium">
                  {!isNaN(value) && isNumber(value)
                    ? getCurrencyValue(props.data.payee_currency_symbol, value)
                    : "--"}
                </EverTg.Text>
              </div>
            </div>
          );
        },
        ...numberFilter("total_payout"),
      },
      {
        headerName: "Stage Name",
        field: "stage_name",
        hide: !isPayout || !showApprovalFeature || !payoutApprovalsEnabled,
        suppressColumnsToolPanel: !isPayout || !showApprovalFeature,
        minWidth: 200,
        cellRenderer: (props) => {
          if (props.data.stage_name) {
            return (
              <EverTg.Text className="text-ever-base-content font-medium">
                {props.data.stage_name}
              </EverTg.Text>
            );
          }
          return "--";
        },
      },
      {
        headerName: "Stage Status",
        field: "due_date",
        hide: !isPayout || !showApprovalFeature || !payoutApprovalsEnabled,
        suppressColumnsToolPanel: !isPayout || !showApprovalFeature,
        minWidth: 200,
        cellRenderer: (props) => {
          const dueDays = props.data.due_date;
          if (isNull(dueDays)) return "--";

          if (dueDays >= 0) {
            return (
              <div className="flex items-center gap-2">
                <EverBadge
                  className="font-medium"
                  title={`On track`}
                  type="success"
                  icon={<CircleIcon className="w-1.5 h-1.5" />}
                />
                <EverTg.Text className="text-ever-base-content font-medium">
                  Due in {dueDays} day{dueDays > 1 ? "s" : ""}
                </EverTg.Text>
              </div>
            );
          } else if (dueDays < 0) {
            return (
              <div className="flex items-center gap-2">
                <EverBadge
                  className="font-medium"
                  title={`Overdue`}
                  type="error"
                  icon={<CircleIcon className="w-1.5 h-1.5" />}
                />
                <EverTg.Text className="text-ever-base-content font-medium">
                  Overdue by {Math.abs(dueDays)} day
                  {Math.abs(dueDays) > 1 ? "s" : ""}
                </EverTg.Text>
              </div>
            );
          }
          return "--";
        },
      },
      {
        headerName: "Awaiting Response From",
        field: "awaiting_response_from",
        hide: !isPayout || !showApprovalFeature || !payoutApprovalsEnabled,
        suppressColumnsToolPanel: !isPayout || !showApprovalFeature,
        minWidth: 200,
        cellRenderer: (props) => {
          if (
            !props.data.awaiting_response_from ||
            props.data.awaiting_response_from.length === 0
          )
            return "--";

          const avatars = (props.data.awaiting_response_from || []).map(
            (approver) => ({
              firstName: approver.first_name,
              lastName: approver.last_name,
              image: approver.profile_picture,
            })
          );

          return (
            <div className="flex items-center gap-2">
              <EverGroupViewAllAvatars
                avatars={avatars}
                groupMaxCount={4}
                limitInPopover={20}
              />
            </div>
          );
        },
      },
      {
        headerName: "Requested On",
        field: "stage_requested_on",
        hide: !isPayout || !showApprovalFeature || !payoutApprovalsEnabled,
        suppressColumnsToolPanel: !isPayout || !showApprovalFeature,
        minWidth: 200,
        cellRenderer: (props) => {
          if (props.data.stage_requested_on) {
            return (
              <EverTg.Text className="text-ever-base-content font-medium">
                {props.data.stage_requested_on}
              </EverTg.Text>
            );
          }
          return "--";
        },
      },
      {
        headerName: "Approval Status",
        field: "approval_status",
        hide: !isPayout || !showApprovalFeature || !payoutApprovalsEnabled,
        suppressColumnsToolPanel: !isPayout || !showApprovalFeature,
        minWidth: 200,
        cellRenderer: (props) => {
          const freezed =
            props.data?.comm_calc_status === CALCULATION_STATUS.FROZEN &&
            props.data?.settlement_calc_status === CALCULATION_STATUS.FROZEN;
          if (!props.data.approval_status) {
            return null;
          }
          if (props.data.approval_status === APPROVAL_STATUS.NOT_REQUESTED)
            return "--";
          return (
            <div className="flex items-center gap-2">
              <ApprovalTag
                value={props.data.approval_status}
                handleClick={() => {
                  setApprovalDrawerVisibility(true);
                  setIsSelectedRecordLocked(freezed);
                  setSelectedPayeeName(props?.data?.payee_name || "");
                  setSelectedPayeeCurrency(
                    props?.data?.payee_currency_symbol || ""
                  );
                  setSelectedPayeePayout(
                    parseFloat(props?.data?.total_payout || 0, 10)
                  );
                  setSelectedPayeeEntityKey(
                    `${props?.data?.payee_email_id}##::##${props?.data?.period_end_date}`
                  );
                  setSelectedPeriod(props?.data?.period_end_date);
                }}
                payoutLevel={payoutLevel}
                record={props.data}
              />
            </div>
          );
        },
      },
      {
        headerName: "Paid",
        field: "paid_amount",
        type: "rightAligned",
        cellRenderer: (props) => {
          const value = parseFloat(props.value, 10);
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {!isNaN(value) && isNumber(value)
                  ? getCurrencyValue(props.data.payee_currency_symbol, value)
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("paid_amount"),
      },
      {
        headerName: "Processed",
        field: "processed_amount",
        type: "rightAligned",
        hide: !isPayout,
        cellRenderer: (props) => {
          const value = parseFloat(props.value, 10);
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {!isNaN(value) && isNumber(value)
                  ? getCurrencyValue(props.data.payee_currency_symbol, value)
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("processed_amount"),
      },
      {
        headerName: "Ignored",
        field: "ignored_amount",
        type: "rightAligned",
        hide: !isPayout,
        cellRenderer: (props) => {
          const value = parseFloat(props.value, 10);
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {!isNaN(value) && isNumber(value)
                  ? getCurrencyValue(props.data.payee_currency_symbol, value)
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("ignored_amount"),
      },
      {
        headerName: "Pending",
        field: "pending_amount",
        type: "rightAligned",
        cellRenderer: (props) => {
          const value = parseFloat(props.value, 10);
          return (
            <div
              data-testid={`pt-${props.data.payee_email_id}-pending-amount`}
              className="w-full flex items-center justify-end"
            >
              <EverTg.Text className="text-ever-base-content font-medium">
                {!isNaN(value) &&
                props.data.payment_status !== PAYMENT_STATUS.UNPAID
                  ? isNumber(value)
                    ? getCurrencyValue(props.data.payee_currency_symbol, value)
                    : "Nil"
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("pending_amount"),
      },
      {
        headerName: `${t("COMMISSION")} %`,
        field: "commission_percentage",
        type: "rightAligned",
        hide: !isPayout,
        suppressColumnsToolPanel: !isPayout,
        cellRenderer: (props) => {
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {Number(props.value)} %
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("commission_percentage"),
      },
      {
        headerName: "Employee ID",
        field: "employee_id",
        type: "rightAligned",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: t("PRIMARY_COMMISSION_PLAN"),
        field: "primary_commission_plans",
        maxWidth: 300,
        ...payoutHiddenColumnProps,
        cellRenderer: (props) => {
          return <span title={props.value}>{props.value}</span>;
        },
      },
      {
        headerName: "SPIFF Plans",
        field: "spiff_plans",
        maxWidth: 300,
        ...payoutHiddenColumnProps,
        cellRenderer: (props) => {
          return <span title={props.value}>{props.value}</span>;
        },
      },
      {
        headerName: "Designation",
        field: "designation",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: "Reporting Manager",
        field: "reporting_manager",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: "Joining Date",
        field: "joining_date",
        ...payoutHiddenColumnProps,
        ...dateFilter,
      },
      {
        headerName: t("COMMISSION"),
        field: "commission",
        ...payoutHiddenColumnProps,
        cellRenderer: (props) => {
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {props.value !== "-"
                  ? getCurrencyValue(
                      props.data.payee_currency_symbol,
                      props.value
                    )
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("commission"),
      },
      {
        headerName: "Employment Country",
        field: "employment_country",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: t("PAYOUT_FREQUENCY"),
        field: "payout_frequency",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: "User Type",
        field: "user_type",
        ...payoutHiddenColumnProps,
      },
      {
        headerName: "Base Pay",
        field: "base_pay",
        type: "rightAligned",
        ...payoutHiddenColumnProps,
        cellRenderer: (props) => {
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {props.value
                  ? getCurrencyValue(
                      props.data.payee_currency_symbol,
                      props.value
                    )
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("base_pay"),
      },
      {
        headerName: "Variable Pay",
        field: "variable_pay_as_per_period",
        type: "rightAligned",
        ...payoutHiddenColumnProps,
        cellRenderer: (props) => {
          return (
            <div className="w-full flex items-center justify-end">
              <EverTg.Text className="text-ever-base-content font-medium">
                {props.value
                  ? getCurrencyValue(
                      props.data.payee_currency_symbol,
                      props.value
                    )
                  : "--"}
              </EverTg.Text>
            </div>
          );
        },
        ...numberFilter("variable_pay_as_per_period"),
      },
      {
        headerName: "User Status",
        field: "user_status",
        ...payoutHiddenColumnProps,
        cellRenderer: (params) => {
          const { user_status, joining_date, deactivation_date, exit_date } =
            params.data;
          const currentStatus = params.value;
          return (
            <UserStatusIndicator
              userStatus={user_status}
              joiningDate={joining_date}
              exitDate={exit_date}
              deactivationDate={deactivation_date}
              currentStatus={currentStatus}
            />
          );
        },
      },
      {
        headerName: "User Source",
        field: "user_source",
        ...payoutHiddenColumnProps,
        cellRenderer: (params) => {
          const data =
            params.value === null || params.value === "manual"
              ? "Manually managed"
              : "Managed via integrations";
          return data;
        },
      },
      {
        headerName: "Exit Date",
        field: "exit_date",
        ...payoutHiddenColumnProps,
        ...dateFilter,
      },
      {
        headerName: "Added Date",
        field: "created_date",
        ...payoutHiddenColumnProps,
        ...dateFilter,
      },
      ...customColumns,
    ];

    if (
      (isPayout &&
        (hasPermissions([
          RBAC_ROLES.MANAGE_PAYOUTS,
          RBAC_ROLES.REGISTER_PAYOUTS,
        ]) ||
          (approvalFeatureEnabled &&
            hasPermissions(RBAC_ROLES.VIEW_REQUESTAPPROVALS)))) ||
      (!isPayout &&
        hasPermissions([
          RBAC_ROLES.REGISTER_PAYOUTS,
          RBAC_ROLES.INVALIDATE_PAYOUTS,
        ]))
    ) {
      columns.push({
        headerName: (
          <div className="flex items-center justify-between">Actions</div>
        ),
        field: "action",
        pinned: "right",
        lockPinned: true,
        sortable: false,
        filter: false,
        // resizable: false,
        minWidth: hasPermissions([RBAC_ROLES.REGISTER_PAYOUTS]) ? 150 : 100,
        headerClass: hasPermissions([RBAC_ROLES.REGISTER_PAYOUTS])
          ? isPayout
            ? "!pl-6"
            : "!pl-16"
          : "",
        suppressColumnsToolPanel: true,
        cellStyle: {
          padding: hasPermissions([RBAC_ROLES.REGISTER_PAYOUTS])
            ? isPayout
              ? "0px 24px"
              : "0px 24px"
            : "",
        },
        cellRenderer: (props) => {
          const record = props.data;
          const isActionOnPayee =
            Array.isArray(payeesOnAction) &&
            payeesOnAction.includes(record.payee_email_id);

          const permissionRestricted = !(
            record.payee_email_id === email ||
            hasPermissions(RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS)
          );

          // Assumption of comm calc status and settlement calc status are equal
          const isFrozen =
            record.comm_calc_status === CALCULATION_STATUS.FROZEN &&
            record.settlement_calc_status === CALCULATION_STATUS.FROZEN;
          const freezeStatus = isFrozen
            ? CALCULATION_STATUS.FROZEN
            : CALCULATION_STATUS.NOTFROZEN;

          const statusToCheck =
            record.entity_type === LINE_ITEM_TYPE_ENTITY_TYPE
              ? [APPROVAL_STATUS.REQUESTED, APPROVAL_STATUS.NEEDS_ATTENTION]
              : [APPROVAL_STATUS.REQUESTED];
          const canRevoke = statusToCheck.includes(record.approval_status);

          // To show lock icon and label for payout table
          const getLockMenuItem = () => {
            const { icon: Icon, label } = freezeUnfreezeOption[freezeStatus];
            return (
              <div className="flex gap-3">
                {Icon}
                <span className="text-ever-base-content">{label}</span>
              </div>
            );
          };

          return (
            <>
              <ActionWrapper isPayout={isPayout}>
                {isPayout ? (
                  <div className="flex flex-row gap-2 items-center">
                    {/*
                      Show menu actions when either "manage payouts" or
                      "request approvals" permission is present.
                    */}
                    <RBACProtectedComponent
                      permissionId={[RBAC_ROLES.REGISTER_PAYOUTS]}
                    >
                      <EverTooltip
                        title={
                          permissionRestricted
                            ? t("NO_PERMISSION_TO_OTHERS_PAYOUT")
                            : "Register Payment"
                        }
                      >
                        <EverButton
                          className="text-ever-primary !px-2"
                          onClick={() =>
                            setMakePayment([record.payee_email_id])
                          }
                          data-testid={`pt-${record?.payee_email_id}-register-payment`}
                          type="link"
                          disabled={
                            isSkipPayment(record?.payment_status) ||
                            Number(record?.processed_amount) > 0 ||
                            Number(record?.ignored_amount) > 0 ||
                            isActionOnPayee ||
                            permissionRestricted ||
                            (!isFrozen &&
                              !hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS))
                          }
                          prependIcon={<CreditCardIcon />}
                        />
                      </EverTooltip>
                    </RBACProtectedComponent>
                    {(hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS) ||
                      (approvalFeatureEnabled &&
                        hasPermissions(RBAC_ROLES.VIEW_REQUESTAPPROVALS))) && (
                      <Dropdown
                        trigger={["click"]}
                        placement="bottomRight"
                        overlay={
                          <Menu>
                            <Menu.Item
                              hidden={
                                !hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS)
                              }
                              onClick={() => {
                                // If clicked for unlocking statements
                                if (
                                  freezeStatus === CALCULATION_STATUS.FROZEN
                                ) {
                                  // If approvals is requested, show confirmation popup
                                  if (
                                    record.entity_type !==
                                      LINE_ITEM_TYPE_ENTITY_TYPE &&
                                    approvalFeatureEnabled &&
                                    [
                                      APPROVAL_STATUS.APPROVED,
                                      APPROVAL_STATUS.REQUESTED,
                                    ].includes(record.approval_status)
                                  ) {
                                    setFreezePayoutData({
                                      email: record.payee_email_id,
                                      isLocked: false,
                                    });
                                    setShowUnFreezeConfirm(true);
                                  } else {
                                    onClickFreezeStatus(
                                      [record.payee_email_id],
                                      false
                                    );
                                  }
                                } else {
                                  if (
                                    record.is_comm_adj_requested &&
                                    !clientFeatures.allowAdjustmentsToFrozenCommission
                                  ) {
                                    setModalPayeeEmail(record.payee_email_id);
                                    setCommissionAdjustmentModalVisisble(true);
                                  } else {
                                    onClickFreezeStatus(
                                      [record.payee_email_id],
                                      true
                                    );
                                  }
                                }
                              }}
                            >
                              {getLockMenuItem()}
                            </Menu.Item>
                            <Menu.Item
                              hidden={
                                !approvalFeatureEnabled ||
                                !payoutApprovalsEnabled ||
                                !hasPermissions(
                                  RBAC_ROLES.VIEW_REQUESTAPPROVALS
                                )
                              }
                              onClick={() => {
                                const statusToCheck =
                                  record.entity_type ===
                                  LINE_ITEM_TYPE_ENTITY_TYPE
                                    ? [
                                        APPROVAL_STATUS.REQUESTED,
                                        APPROVAL_STATUS.NEEDS_ATTENTION,
                                      ]
                                    : [
                                        APPROVAL_STATUS.REQUESTED,
                                        APPROVAL_STATUS.APPROVED,
                                      ];
                                if (
                                  freezeStatus === CALCULATION_STATUS.FROZEN &&
                                  !statusToCheck.includes(
                                    record.approval_status
                                  )
                                ) {
                                  setShowApprovalTemplateSelector(true);
                                  onClickRequestApproval([record], false);
                                } else {
                                  EverModal.warning({
                                    title: (
                                      <div>
                                        <p className="text-left text-base">
                                          You cannot request approval when,
                                        </p>
                                        <p className="text-left text-sm font-normal">
                                          1. {t("PAYOUT")} is unlocked (or)
                                        </p>
                                        <p className="text-left text-sm font-normal">
                                          2. {t("PAYOUT")} is already approved
                                          or submitted for approval
                                        </p>
                                      </div>
                                    ),
                                  });
                                }
                              }}
                            >
                              <div className="flex gap-3">
                                <DataflowIcon className="h-[18px] w-[18px] text-ever-base-content" />
                                <span className="text-ever-base-content">
                                  Request Approval
                                </span>
                              </div>
                            </Menu.Item>
                            <Menu.Item
                              hidden={
                                !approvalFeatureEnabled ||
                                !payoutApprovalsEnabled ||
                                !hasPermissions(
                                  RBAC_ROLES.VIEW_REQUESTAPPROVALS
                                ) ||
                                !canRevoke
                              }
                              onClick={() => {
                                const totalApprovedRequests =
                                  record.approved_requests_count;
                                const stageName = record.stage_name;

                                let subtitle = "";
                                if (totalApprovedRequests > 0) {
                                  subtitle = `This request is currently in ${stageName} and has been approved by ${totalApprovedRequests} user(s) so far. Revoking now will stop the approval process, and no further actions will take place. The request will be marked as Revoked.`;
                                } else {
                                  subtitle =
                                    "Revoking this request will stop the current approval process. No further actions will be taken, and the request will be marked as Revoked. This action cannot be undone.";
                                }
                                setRevokeConfirmationTitle(
                                  "Are you sure you want to revoke this approval request?"
                                );
                                setRevokeConfirmationSubtitle(subtitle);
                                setShowRevokeConfirmModal({
                                  visible: true,
                                  records: [record],
                                  bulkMode: false,
                                });
                              }}
                            >
                              <div className="flex gap-3">
                                <ReverseLeftIcon className="h-[18px] w-[18px] text-ever-base-content" />
                                <span className="text-ever-base-content">
                                  Revoke Approval
                                </span>
                              </div>
                            </Menu.Item>
                          </Menu>
                        }
                      >
                        <EverButton.Icon
                          size="small"
                          type="ghost"
                          color="base"
                          className="bg-transparent"
                          data-testid={`${record?.payee_email_id}-actions-dd`}
                          icon={
                            <DotsHorizontalIcon
                              className="text-ever-base-content-mid"
                              data-testid="commission-dropdown"
                            />
                          }
                        />
                      </Dropdown>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-row gap-2 items-center">
                    <EverTooltip
                      title={
                        permissionRestricted
                          ? t("NO_PERMISSION_TO_OTHERS_PAYOUT")
                          : ""
                      }
                    >
                      <EverButton
                        type="link"
                        hidden={!hasPermissions(RBAC_ROLES.REGISTER_PAYOUTS)}
                        disabled={
                          isActionOnPayee ||
                          record.payout_period_options?.length <= 0 ||
                          permissionRestricted
                        }
                        onClick={() => setSelectedPayee(record)}
                      >
                        <EverTg.Text className="font-medium">
                          Process
                        </EverTg.Text>
                      </EverButton>
                    </EverTooltip>
                    {hasPermissions(RBAC_ROLES.INVALIDATE_PAYOUTS) && (
                      <Dropdown
                        trigger={["click"]}
                        overlay={
                          <Menu>
                            <Menu.Item
                              icon={
                                <MinusCircleIcon className="w-5 h-5 text-ever-error" />
                              }
                              disabled={isActionOnPayee || permissionRestricted}
                              onClick={() => onIgnoreArrear([record])}
                            >
                              <EverTooltip
                                title={
                                  permissionRestricted
                                    ? t("NO_PERMISSION_TO_OTHERS_PAYOUT")
                                    : ""
                                }
                              >
                                <span className="text-ever-error">
                                  {t("IGNORE_ARREAR")}
                                </span>
                              </EverTooltip>
                            </Menu.Item>
                          </Menu>
                        }
                      >
                        <EverButton.Icon
                          size="small"
                          type="ghost"
                          color="base"
                          className="bg-transparent"
                          icon={
                            <DotsHorizontalIcon className="text-ever-base-content-mid" />
                          }
                        />
                      </Dropdown>
                    )}
                  </div>
                )}
              </ActionWrapper>
            </>
          );
        },
      });
    }
    if (isPayout) {
      columns.sort((a, b) => {
        // Get the index of each element in the selectedColumns
        const indexA = selectedFilter?.selectedColumns.indexOf(a.field);
        const indexB = selectedFilter?.selectedColumns.indexOf(b.field);

        // If both elements exist in selectedColumns, compare their positions
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB;
        }
        // If only one of the elements exists in selectedColumns, it comes before the other
        else if (indexA !== -1) {
          return -1;
        } else if (indexB !== -1) {
          return 1;
        }
        // If neither element exists in selectedColumns, maintain their original order
        else {
          return 0;
        }
      });
      const tempSidePanelColumns = [];
      columns
        .filter((col) => col.suppressColumnsToolPanel !== true)
        .forEach((column) => {
          if (selectedFilter?.selectedColumns.includes(column.field)) {
            column.hide = false;
          } else {
            column.hide = true;
          }

          const data = {
            id: column.field,
            name: column.headerName,
            hide: false,
            selected: selectedFilter?.selectedColumns.includes(column.field),
          };
          tempSidePanelColumns.push({
            ...data,
            dragContentProps: {
              ...data,
            },
          });
        });
      setSidePanelColumns([...tempSidePanelColumns]);
    } else {
      setSidePanelColumns([]);
    }

    const defaultColumns = [{ label: "Period", key: "period" }];
    columns.forEach((column) => {
      if (
        column.field !== "action" &&
        column.field !== "period" &&
        column.field !== "rowNumbers" &&
        !column.hide
      ) {
        defaultColumns.push({ label: column.headerName, key: column.field });
      }
    });
    setExportableColumns([...defaultColumns]);

    const sortIgnoreCols = ["action", "checkbox"];
    const sortableColumns = columns.map((col) => {
      const isSortable = !sortIgnoreCols.includes(col.field);
      const sortInfo = orderbyFields.find((item) => item.column === col.field);
      const headerParams = col.headerComponentParams ?? {};
      return {
        ...col,
        headerComponentParams: {
          ...headerParams,
          serverSideSortable: isSortable,
          sortOrder: sortInfo?.order ?? "",
          sortByField: col.field,
          sortCallback,
        },
      };
    });

    return sortableColumns;
  };

  const components = useMemo(() => {
    return {
      agColumnHeader: CustomHeader,
    };
  }, []);

  const detailCellRendererParams = useMemo(() => {
    const columnDefs = [
      {
        headerName: "Date",
        field: "date",
        suppressMenu: true,
        cellRenderer: (props) => {
          return (
            <TransactionText isPaid={props.data.is_paid ?? true}>
              {props.value
                ? moment(props.value).format(COMMON_MOMENT_DATE_FORMAT)
                : ""}
            </TransactionText>
          );
        },
      },
      {
        headerName: "Amount",
        field: "amount",
        suppressMenu: true,
        cellRenderer: (props) => {
          const value = parseFloat(props.value, 10);
          return (
            <TransactionText isPaid={props.data.is_paid ?? true}>
              {!isNaN(value) && isNumber(value)
                ? getCurrencyValue(props.data.payee_currency_symbol, value)
                : "--"}
            </TransactionText>
          );
        },
      },
      {
        headerName: "Comments",
        field: "comment",
        minWidth: 150,
        suppressMenu: true,
        cellClass: "[&>*]:truncate",
        tooltipField: "comment",
        tooltipComponent: EverAgCellTooltip,
        cellRenderer: (props) => {
          return (
            <TransactionText isPaid={props.data.is_paid ?? true}>
              {props.value || "--"}
            </TransactionText>
          );
        },
      },
      {
        headerName: "Paid by",
        field: "paid_by",
        suppressMenu: true,
        cellRenderer: (props) => {
          return (
            <TransactionText isPaid={props.data.is_paid ?? true}>
              {props.value || "--"}
            </TransactionText>
          );
        },
      },
    ];

    if (hasPermissions(RBAC_ROLES.INVALIDATE_PAYOUTS)) {
      columnDefs.push({
        headerName: "Actions",
        field: "action",
        hide: !isPayout,
        minWidth: 150,
        suppressMenu: true,
        headerClass: "!pl-8",
        cellRenderer: (props) => {
          const record = props.data;
          if (
            !record.is_paid ||
            (record.type == "arrear" && !record.ignored_flag)
          ) {
            return null;
          }
          const isActionOnPayee =
            Array.isArray(payeesOnAction) &&
            payeesOnAction.includes(record?.payee_email_id);
          const permissionRestricted = !(
            record.payee_email_id === email ||
            hasPermissions(RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS)
          );

          return (
            <EverTooltip
              title={
                permissionRestricted ? t("NO_PERMISSION_TO_OTHERS_PAYOUT") : ""
              }
            >
              <EverButton
                type="link"
                size="small"
                disabled={isActionOnPayee || permissionRestricted}
                onClick={() => onInvalidate(props.data)}
              >
                Invalidate
              </EverButton>
            </EverTooltip>
          );
        },
      });
    }

    return {
      refreshStrategy: "rows",
      detailGridOptions: {
        columnDefs,
        defaultColDef: {
          flex: 1,
        },
        components: components,
        detailRowAutoHeight: true,
        suppressContextMenu: true,
      },
      getDetailRowData: function (params) {
        params.successCallback(params.data.payout_transactions);
      },
    };
  }, [accessToken, date, isPayout]);

  const onColumnVisibleChange = (event) => {
    const columns = gridApi?.getAllGridColumns();
    const exportableColumns = [{ label: "Period", key: "period" }];
    const keys = [];
    columns.forEach((column) => {
      if (column.visible) {
        keys.push(column.colId);
        if (
          column.userProvidedColDef.field !== "action" &&
          column.userProvidedColDef.field !== "period" &&
          column.userProvidedColDef.field !== "rowNumbers"
        ) {
          const { headerName, field } = column.userProvidedColDef;
          exportableColumns.push({ label: headerName, key: field });
        }
      }
    });
    setTimeout(() => {
      gridApi?.autoSizeColumns([...keys, event?.column?.colId]);
    }, 200);
    adjustColumnWidth(event);
    setExportableColumns(exportableColumns);
  };

  useEffect(() => {
    if (!isLoading) {
      setColumnDefs(renderColumns());
    }
  }, [
    date,
    rowData,
    payeesOnAction,
    isPayout,
    accessToken,
    customFields,
    isLoading,
    headerCbxState,
    approvalFeatureEnabled,
    payoutApprovalsEnabled,
    payoutLevel,
    toJS(selectedFilter?.selectedColumns).join(),
    orderbyFields,
  ]);

  const visibleChangeEventListener = useCallback(() => {
    setTimeout(() => {
      const columns = gridApi?.getAllGridColumns();
      if (columns?.length > 0) {
        columns.forEach((column) => {
          if (!column?.userProvidedColDef?.suppressColumnsToolPanel) {
            const col = gridApi?.getColumn(column);
            col?.addEventListener("visibleChanged", onColumnVisibleChange);
          }
        });
      }
    }, 200);
  }, [parentRef, customFields]);

  useEffect(() => {
    if (!isLoading) {
      visibleChangeEventListener();
    }
  }, [gridApi, parentRef, customFields, isLoading]);

  useEffect(() => {
    if (!isPayout) {
      clearSelection();
    }

    gridApi?.deselectAll?.();
    gridApi?.closeToolPanel();
    let isExpandClosed = false;
    setTimeout(() => {
      gridApi?.forEachNode(function (node) {
        if (node?.expanded && node?.data?.payout_transactions?.length <= 0) {
          isExpandClosed = true;
          node.expanded = false;
        }
      });
      if (isExpandClosed) {
        gridApi?.onGroupExpandedOrCollapsed();
      }
    }, 100);
  }, [rowData, isPayout]);

  useEffect(() => {
    setTimeout(() => {
      gridApi?.resetColumnState();
      gridApi?.forEachNode(function (node) {
        node.expanded = false;
      });
      gridApi?.onGroupExpandedOrCollapsed();
      gridApi?.redrawRows?.();
    }, 100);
  }, [isPayout]);

  // TODO - Need to enable when Filter is implemented
  const onShowColumnChange = useCallback(() => {
    if (gridApi?.isSideBarVisible()) {
      gridApi?.setSideBarVisible(false);
      gridApi?.closeToolPanel();
    } else {
      gridApi?.setSideBarVisible(true);
      gridApi?.openToolPanel("filters");
      gridApi?.openToolPanel("filters");
    }
    setToggleColumn(false);
  }, [gridApi, showColumn]);

  useEffect(() => {
    if (period) {
      clearSelection();
    }
  }, [period]);

  useEffect(() => {
    if (showColumn) {
      onShowColumnChange();
    }
  }, [showColumn]);

  useEffect(() => {
    setShowBulkActions(cumulativeList.length > 0);
  }, [cumulativeList]);

  const onFilterChanged = useCallback(() => {
    if (cumulativeList.length > 0) {
      gridApi?.deselectAll();
      gridApi?.forEachNodeAfterFilterAndSort((node) => {
        node.setSelected(cumulativeList.includes(node.data.payee_email_id));
      });
    }
    gridApi?.refreshCells({ columns: ["rowNumbers"] });
  }, [gridApi, cumulativeList]);

  const onSuccess = (isSuccess, toastMessage, type = "payout") => {
    if (isSuccess) {
      toast.custom(
        () => <EverHotToastMessage type="success" description={toastMessage} />,
        { position: "top-center" }
      );
      type === "payout" ? refetch() : getArrear();
    }
    setIsLoading(false);
    gridApi?.deselectAll();
    setPayeesOnAction([]);
    clearSelection();
    setMakePayment([]);
    setSelectedPayee({});
    setFreezeConfirmation("");
  };

  const onFailure = (toastMessage, type = "payout") => {
    toast.custom(
      () => <EverHotToastMessage type="error" description={toastMessage} />,
      { position: "top-center" }
    );
    type === "payout" ? refetch() : getArrear();
    setIsLoading(false);
    gridApi?.deselectAll();
    setPayeesOnAction([]);
    clearSelection();
    setMakePayment([]);
    setSelectedPayee({});
    setFreezeConfirmation("");
  };
  const onPaymentComplete = () => {
    refetch();
    setIsLoading(false);
    gridApi?.deselectAll();
    setPayeesOnAction([]);
    clearSelection();
    setMakePayment([]);
    setSelectedPayee({});
    setFreezeConfirmation("");
  };

  const showCommissionSyncRunningToastAlert = () => {
    toast.custom(
      (t) => (
        <EverHotToastAlert
          type="error"
          description="Locking or unlocking statements is disabled while Commission Sync is in progress. Please try again after the sync is complete."
          title="Locking Temporarily Disabled"
          toastId={t.id}
          className="w-[519px]"
          truncateDescription={false}
          buttons={[
            {
              buttonText: "Dismiss",
              onClick: () => toast.dismiss(t.id),
              className: "border-l",
            },
          ]}
        />
      ),
      { position: "top-right", duration: Infinity }
    );
  };

  const downloadPendingChangesFile = (pendingChangesFileContent) => {
    const file = new File(
      [pendingChangesFileContent],
      `StatementLockFailures_${periodLabel}.csv`,
      { type: "text/csv" }
    );
    const url = URL.createObjectURL(file);

    // Create anchor element
    const link = document.createElement("a");
    link.href = url;
    link.download = `StatementLockFailures_${periodLabel}.csv`; // Set desired filename
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url); // Free up memory
  };

  const handleFreezeFailureWithPendingChanges = (
    isSuccess,
    responseData,
    type = "payout"
  ) => {
    if (responseData?.messageCode == "CANNOT_LOCK_ON_ONGOING_SYNC") {
      showCommissionSyncRunningToastAlert();
    } else if (
      responseData?.messageCode == "LOCK_RESTRICTED_SINGLE_PAYEE_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Locking Restricted",
        message:
          "This payee has unsynced commission-impacting changes, hence their statement could not be locked. Run Commission Sync for them in this payout period, then return to complete locking their statement",
      });
      downloadPendingChangesFile(responseData?.pendingChangesFileContent);
    } else if (
      responseData?.messageCode == "LOCK_PARTIALLY_COMPLETED_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Locking Partially Completed",
        message: responseData?.displayMessage,
      });
      downloadPendingChangesFile(responseData?.pendingChangesFileContent);
    } else {
      toast.custom(
        () => <EverHotToastMessage type="success" description={responseData} />,
        { position: "top-center" }
      );
    }
    type === "payout" ? refetch() : getArrear();
    setIsLoading(false);
    gridApi?.deselectAll();
    setPayeesOnAction([]);
    clearSelection();
    setMakePayment([]);
    setSelectedPayee({});
    setFreezeConfirmation("");
  };

  const handlePaymentFailureWithPendingChanges = (responseData) => {
    if (responseData?.messageCode == "CANNOT_LOCK_ON_ONGOING_SYNC") {
      showCommissionSyncRunningToastAlert();
    } else if (
      responseData?.messageCode == "PAYMENT_RESTRICTED_SINGLE_PAYEE_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Payment registration restricted",
        message:
          "This payee has unsynced commission-impacting changes, hence their payment could not be registered. Run Commission Sync for them in this payout period, then return to complete registering their payment",
      });
      downloadPendingChangesFile(responseData?.pendingChangesFileContent);
    } else if (
      responseData?.messageCode == "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Payment registration partially completed",
        message: responseData.displayMessage,
      });
      downloadPendingChangesFile(responseData?.pendingChangesFileContent);
    }
  };

  const handleProcessedCompletely = () => {
    toast.custom(
      () => (
        <EverHotToastAlert
          type="success"
          description={
            <span className="text-ever-base-content-black font-extrabold">
              Payment(s) registered successfully
            </span>
          }
        />
      ),
      { position: "top-right" }
    );
    return;
  };

  const handleProcessedPartially = (message) => {
    setRegisterPaymentWarningDetails({
      isVisible: true,
      title: "Payment registration partially failed",
      message: message,
    });
    return;
  };

  const handleProcessFailed = (message) => {
    setRegisterPaymentWarningDetails({
      isVisible: true,
      title: "Payment registration failed",
      message: message,
    });
    return;
  };

  const handleError = (description) => {
    toast.custom(
      () => (
        <EverHotToastMessage
          type="error"
          description={
            <span className="text-ever-base-content-black font-extrabold">
              {description}
            </span>
          }
        />
      ),
      { position: "top-center" }
    );
    return;
  };

  const handleMakePayment = async (paymentDetails) => {
    const data = {
      ...paymentDetails,
      date,
    };
    if (data?.payeeIds?.length > 0) {
      setIsLoading(true);
      setPayeesOnAction(data.payeeIds);
      if (
        clientFeatures.avoidConcurrentRegisterPayment ||
        clientFeatures.canAvoidLockingWithPendingChanges
      ) {
        await setPaidStatusV2(
          data,
          accessToken,
          handleProcessedCompletely,
          handleProcessedPartially,
          handleProcessFailed,
          handleError,
          handlePaymentFailureWithPendingChanges,
          onSuccess
        );
        onPaymentComplete();
        return;
      } else {
        await setPaidStatus(data, accessToken, onSuccess, onFailure);
      }
    }
  };

  const onProcessArrear = (payee, selectedPeriod) => {
    setIsLoading(true);
    const data = [
      {
        payee_id: payee.payee_email_id,
        period_end_date: payee.period_end_date,
        payout_period_date: selectedPeriod,
        comment: "",
      },
    ];
    processArrear(data, accessToken, (isSuccess, toastMessage) =>
      onSuccess(isSuccess, t(toastMessage), "arrear")
    );
  };

  const onIgnoreArrear = (payees) => {
    setIsLoading(true);
    const data = payees.map((payee) => ({
      payee_id: payee.payee_email_id,
      period_end_date: payee.period_end_date,
      comment: "",
    }));
    ignoreArrear(data, accessToken, (isSuccess, toastMessage) =>
      onSuccess(isSuccess, t(toastMessage), "arrear")
    );
  };

  const onInvalidate = (transaction) => {
    if (transaction) {
      setIsLoading(true);
      setPayeesOnAction([transaction.payee_email_id]);
      let invalidateData = {
        payout_id: transaction.payout_id,
        type: transaction.type,
        date,
      };
      if (transaction?.ignored_flag) {
        invalidateData = {
          ...invalidateData,
          arrear_month: transaction.arrear_month
            ? transaction.arrear_month
            : null,
          payeeEmailId: transaction.payee_email_id,
        };
      }
      invalidatePayout(
        invalidateData,
        accessToken,
        (isSuccess, toastMessage) => {
          if (isSuccess) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={toastMessage}
                />
              ),
              { position: "top-center" }
            );
            refetch();
            setIsLoading(false);
            setPayeesOnAction([]);
            clearSelection();
            setMakePayment([]);
            setSelectedPayee({});
            setFreezeConfirmation("");
          }
        }
      );
    }
  };

  const onClickValidateFreezeStatus = async (data, accessToken) => {
    try {
      const validationResponse = await validateUpdateFreezeStatus(
        data,
        accessToken
      );
      if (!validationResponse.ok) {
        throw new Error("Validation API call failed");
      }

      const validationData = await validationResponse.json();

      if (validationData.payoutChanged) {
        const { payeeIds } = validationData;
        return new Promise((resolve) => {
          EverModal.confirm({
            width: 600,
            title: `${t("PAYOUT")} values may change`,
            subtitle: `The ${t(
              "PAYOUT"
            ).toLowerCase()} values may have been updated. Unlocking this section will modify the locked ${t(
              "COMMISSION"
            ).toLowerCase()} values.`,
            noteMessage: (
              <div className="text-left">
                <EverTg.Caption>
                  To ensure accurate data, we recommend running the{" "}
                  <a
                    href="/settings/commissions-and-data-sync"
                    target="_blank"
                    className="text-ever-primary font-medium hover:underline hover:text-ever-primary"
                  >
                    {t("COMMISSION").toLowerCase()} sync
                  </a>{" "}
                  after unlocking. This will calculate{" "}
                  {t("COMMISSIONS").toLowerCase()} using the latest data and
                  ensure accuracy. If you need the current{" "}
                  {t("COMMISSION").toLowerCase()} data before unlocking, go back
                  and export the statement.
                </EverTg.Caption>
                <div className="mt-4">
                  <EverTg.Caption>
                    You may also download the user list for reference before
                    proceeding.
                  </EverTg.Caption>
                  {payeeIds?.length ? (
                    <EverButton
                      type="link"
                      className="!px-0 !text-xs"
                      onClick={() =>
                        downloadLockedXLSheet({ payeeIds }, accessToken)
                      }
                    >
                      Download the users list as .xlsx
                    </EverButton>
                  ) : null}
                </div>
                <p>
                  <EverTg.Caption>Proceed to unlock?</EverTg.Caption>
                </p>
              </div>
            ),
            okText: "Confirm",
            cancelText: "Discard",
            centered: true,
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        });
      }
      return true;
    } catch (error) {
      console.error("Error during validation: ", error);
      return false;
    }
  };

  const onClickFreezeStatus = async (payeeIds, is_locked) => {
    if (payeeIds.length > 0) {
      setIsLoading(true);
      const handleLockUrl = clientFeatures?.canAvoidLockingWithPendingChanges
        ? "/spm/settlements/comm_lock_with_pending_changes"
        : "/spm/settlements/update_settlement_comm_freeze_status";
      const data = {
        payeeIds,
        date,
        isLocked: is_locked,
        url: handleLockUrl,
        abort_instance_check: !is_locked && approvalFeatureEnabled,
      };
      setPayeesOnAction(payeeIds);
      try {
        if (!is_locked && clientFeatures.warnOnUnlock == "SUMMARY") {
          const proceed = await onClickValidateFreezeStatus(data, accessToken);
          if (!proceed) {
            setIsLoading(false);
            return;
          }
        }

        // Proceed with the freeze/unfreeze if warnOnUnlock != "SUMMARY" or when confirm is clicked.
        if (clientFeatures.canAvoidLockingWithPendingChanges) {
          await setFreezeStatusWithPendingChanges(
            data,
            accessToken,
            onSuccess,
            handleFreezeFailureWithPendingChanges
          );
        } else {
          await setFreezeStatus(data, accessToken, onSuccess);
        }
      } catch (error) {
        console.error("Error during freeze/unfreeze: ", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const downloadStatementsMutation = useMutation(
    (payload) => downloadBulkStatements(accessToken, payload),
    {
      onSuccess: (data) => {
        message.success(data.message);
        clearSelection();
      },
      onError: (error) => {
        message.error(error.message);
        clearSelection();
      },
    }
  );
  const onClickDownloadStatements = (email, payeeEmailIds) => {
    const statementDowloadData = {
      emailId: email,
      payees: payeeEmailIds,
      ped: date,
      exportType: downloadStatementType,
    };
    downloadStatementsMutation.mutate(statementDowloadData);
  };

  const revokeApprovalMutation = useMutation(
    (revokeApprovalPayload) => onClickRevokeApproval(revokeApprovalPayload),
    {
      onSuccess: (response) => {
        message.success(
          response.message ||
            (showRevokeConfirmModal.bulkMode
              ? "Bulk approval revoking task submitted."
              : "Request has been revoked successfully")
        );
        refetch();
        rolesRefetch();
      },
      onError: (error) => {
        message.error(error?.message || "Revoke approval failed.");
      },
    }
  );

  const handleRevokeApproval = () => {
    const records = showRevokeConfirmModal.records;
    const isSelectedAll = headerCbxState === HEADER_STATE.EVERYTHING;

    const revokeApprovalPayload = {
      records: records,
      bulkMode: showRevokeConfirmModal.bulkMode,
      isSelectedAll: isSelectedAll,
    };

    if (isSelectedAll || (records && records.length > 0)) {
      revokeApprovalMutation.mutate(revokeApprovalPayload);
    }
  };

  const filterAndRequestApproval = (rowData, isSelectedAll = false) => {
    // To initiate only for locked ones
    const filteredData = rowData.filter((record) => {
      return (
        record.settlement_calc_status === CALCULATION_STATUS.FROZEN &&
        record.comm_calc_status === CALCULATION_STATUS.FROZEN
      );
    });
    if (filteredData.length > 0) {
      setShowApprovalTemplateSelector(true);
      onClickRequestApproval(filteredData, true, isSelectedAll);
    }
  };

  const [showRevokeConfirmModal, setShowRevokeConfirmModal] = useState({
    visible: false,
    records: null,
    bulkMode: false,
  });
  const [revokeConfirmationTitle, setRevokeConfirmationTitle] = useState("");
  const [revokeConfirmationSubtitle, setRevokeConfirmationSubtitle] =
    useState("");

  const RevokeConfirmationModal = ({ visible, onClose, onConfirm }) => {
    return (
      <EverModal.Confirm
        visible={visible}
        type="warning"
        width={600}
        title={revokeConfirmationTitle}
        subtitle={
          showRevokeConfirmModal.records?.length === 0 ? (
            <>
              <EverTg.Text>{revokeConfirmationSubtitle}</EverTg.Text>
              <br />
              <EverTg.Text>
                Please note that only approval requests with a{" "}
                <strong>Requested</strong> status can be revoked.
              </EverTg.Text>
            </>
          ) : (
            <EverTg.Text>{revokeConfirmationSubtitle}</EverTg.Text>
          )
        }
        confirmationButtons={[
          <EverButton key="revoke" color="error" onClick={onConfirm}>
            Revoke
          </EverButton>,
          <EverButton key="cancel" color="base" onClick={onClose}>
            Cancel
          </EverButton>,
        ]}
      />
    );
  };

  const onBulkActionChange = async (action) => {
    if (cumulativeList.length > 0) {
      if (action === COMMISSION_ACTION_STATUS.REQUEST_APPROVAL) {
        const filteredData = rowData.filter((record) => {
          return cumulativeList.includes(record.payee_email_id);
        });
        EverModal.warning({
          title: (
            <div>
              <p className="text-left text-base">
                Payouts matching any of the following conditions will not be
                submitted for approval.
              </p>
              <p className="text-left text-sm font-normal">
                1. {t("PAYOUT")} is unlocked (or)
              </p>
              {payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL ? (
                <>
                  <p className="text-left text-sm font-normal">
                    2. Earned {t("COMMISSION")} is 0 (or)
                  </p>
                  <p className="text-left text-sm font-normal">
                    3. {t("PAYOUT")} is already approved or submitted for
                    approval
                  </p>
                </>
              ) : (
                <p className="text-left text-sm font-normal">
                  2. {t("PAYOUT")} is already approved or submitted for approval
                </p>
              )}
            </div>
          ),
          onOk: () => {
            filterAndRequestApproval(
              filteredData,
              headerCbxState === HEADER_STATE.EVERYTHING
            );
          },
          okText: "Ok, request approval",
        });
      } else if (action === COMMISSION_ACTION_STATUS.REVOKE_APPROVAL) {
        const filteredData = rowData.filter(
          (record) =>
            cumulativeList.includes(record.payee_email_id) &&
            [
              APPROVAL_STATUS.REQUESTED,
              APPROVAL_STATUS.NEEDS_ATTENTION,
            ].includes(record.approval_status)
        );
        if (headerCbxState === HEADER_STATE.EVERYTHING) {
          setRevokeConfirmationSubtitle(
            "Revoking the approval request(s) will stop the current approval process. No further actions will be taken, and the request will be marked as Revoked. This action cannot be undone."
          );
        } else {
          setRevokeConfirmationSubtitle(
            `You're about to revoke ${filteredData.length} approval request(s). This will stop the approval process for all selected payouts. This action cannot be undone.`
          );
        }
        setRevokeConfirmationTitle(
          `Are you sure you want to revoke these approval request(s)?`
        );
        setShowRevokeConfirmModal({
          visible: true,
          records: filteredData,
          bulkMode: true,
        });
      } else if (action === COMMISSION_ACTION_STATUS.MAKE_PAYMENT) {
        if (headerCbxState === HEADER_STATE.EVERYTHING) {
          const toastId = toast.custom(
            () => (
              <EverHotToastMessage
                type="loading"
                description="Fetching details..."
              />
            ),
            { position: "top-center", duration: Infinity }
          );
          const data = await getBulkActionsData("PAYMENT_BULK_ACTION");
          setBulkActionsPaymentDataState(data.data);
          setMakePayment(
            data?.data?.map((item) => item["payee_email_id"]) || []
          );
          toast.remove(toastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description="Fetched Successfully"
              />
            ),
            { position: "top-center" }
          );
        } else {
          setMakePayment(cumulativeList);
        }
      } else if (action === COMMISSION_ACTION_STATUS.EMAIL_STATEMENTS) {
        let filteredEmailIds = null;
        if (headerCbxState !== HEADER_STATE.EVERYTHING) {
          filteredEmailIds = rowData
            .filter((record) => {
              return cumulativeList.includes(record.payee_email_id);
            })
            .map((record) => record.payee_email_id);
        }

        EverModal.warning({
          title: `This will email statements for ${selectedCount} payee(s)`,
          onOk: () => {
            handleBulkEmailStatements(
              headerCbxState === HEADER_STATE.EVERYTHING,
              filteredEmailIds,
              (isSuccess) => {
                // Loading for roughly 5 minutes, if no response is get from supabase,
                // the loader will be closed.
                const toastId = toast.custom(
                  () => (
                    <EverHotToastMessage
                      type="loading"
                      description="Bulk sending statements emails..."
                    />
                  ),
                  { position: "top-center", duration: 5 * 60 * 1000 }
                );
                if (!isSuccess) {
                  toast.remove(toastId);
                  toast.custom(
                    () => (
                      <EverHotToastMessage
                        type="error"
                        description="Error in sending statements emails"
                      />
                    ),
                    { position: "top-center" }
                  );
                }
              }
            );
          },
        });
      } else if (action === COMMISSION_ACTION_STATUS.DOWNLOAD_STATEMENTS) {
        // If everything is selected, fetch the data from the API
        // Else we can get the data from the table itself
        if (headerCbxState === HEADER_STATE.EVERYTHING) {
          const toastId = toast.custom(
            () => (
              <EverHotToastMessage
                type="loading"
                description="Fetching details..."
              />
            ),
            { position: "top-center", duration: Infinity }
          );
          // We use LOCK_UNLOCK_BULK_ACTION because it fetches only the minimal data
          // to check if a payee is locked or not
          const data = await getBulkActionsData("LOCK_UNLOCK_BULK_ACTION");
          setBulkActionsDataState(data.data);

          toast.remove(toastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description="Fetched Successfully"
              />
            ),
            { position: "top-center" }
          );
        }
      } else {
        if (headerCbxState === HEADER_STATE.EVERYTHING) {
          const toastId = toast.custom(
            () => (
              <EverHotToastMessage
                type="loading"
                description="Fetching details..."
              />
            ),
            { position: "top-center", duration: Infinity }
          );
          const data = await getBulkActionsData("LOCK_UNLOCK_BULK_ACTION");
          setBulkActionsDataState(data.data);
          setFreezeConfirmation(action);

          toast.remove(toastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description="Fetched Successfully"
              />
            ),
            { position: "top-center" }
          );
        } else {
          setFreezeConfirmation(action);
        }
      }
    } else {
      toast.custom(
        () => (
          <EverHotToastMessage
            type="error"
            description={t("PLEASE_SELECT_COMMISSIONS")}
          />
        ),
        { position: "top-center" }
      );
    }
  };

  const setUpdatedCustomizedColumns = (selectedColumns) => {
    console.log("selected columns", selectedColumns);
    const columns = selectedColumns
      .filter((column) => column.selected)
      .map((column) => column.id);
    handleCustomizeColumnsChange(columns);
  };

  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          width: 250,
          id: "columns",
          labelKey: "columns",
          labelDefault: "Customize Columns",
          iconKey: "columns",
          // toolPanel: "agColumnsToolPanel",
          toolPanel: !isPayout
            ? "agColumnsToolPanel"
            : () => {
                return (
                  <SidePanelRenderer
                    columns={sidePanelColumns}
                    selectedCustomizedColumns={selectedFilter?.selectedColumns}
                    selectedFilterName={selectedFilter?.filterName}
                    setUpdatedCustomizedColumns={setUpdatedCustomizedColumns}
                  />
                );
              },
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: true,
          },
        },
      ],
      defaultToolPanel: "",
    };
  }, [
    isPayout,
    JSON.stringify(sidePanelColumns),
    toJS(selectedFilter?.selectedColumns.join()),
    toJS(selectedFilter?.filterId),
    isFilterSelected,
  ]);

  const onRowGroupOpened = () => {
    setTimeout(() => {
      adjustColumnWidth({ api: gridApi });
    }, 50);
  };

  const onWindowResize = () => {
    setTimeout(() => {
      gridApi?.autoSizeAllColumns(false);
      adjustColumnWidth({ api: gridApi });
    }, 200);
  };

  useEffect(() => {
    window.addEventListener("resize", onWindowResize);
    return () => {
      window.removeEventListener("resize", onWindowResize);
    };
  }, [gridApi, parentRef]);

  // This useEffect will be executed when the updates are received from the supabase
  // This will be used to show/hide the bulk download statements button
  useEffect(() => {
    if (
      updates?.new?.task_name ===
      SUPABASE_CONSTANTS.BULK_DOWNLOAD_STATEMENTS.TASK_NAME
    ) {
      if (
        updates?.new?.data?.status ==
          SUPABASE_CONSTANTS.BULK_DOWNLOAD_STATEMENTS.PROCESSING ||
        updates?.new?.data?.status ==
          SUPABASE_CONSTANTS.BULK_DOWNLOAD_STATEMENTS.PENDING
      ) {
        setShowBulkDownloadStatements(false);
      } else {
        setShowBulkDownloadStatements(true);
      }
    }
  }, [updates]);

  let showBanner = false;
  let showBannerRevoke = false;

  // This will API returns an array of currently running BULK_CREATE_APPROVAL tasks
  const {
    isLoading: isDataLoading,
    error,
    data,
  } = useReactQuery(
    ["fetchCurrentActiveTasks"],
    async () => {
      const res = await fetchActiveTasks(
        SUPABASE_CONSTANTS.BULK_CREATE_APPROVAL_INSTANCES.TASK_NAME,
        accessToken
      );
      return res;
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  const {
    isLoading: isRevokeDataLoading,
    error: revokeError,
    data: revokeData,
  } = useReactQuery(
    ["fetchCurrentActiveTasksRevoke"],
    async () => {
      const res = await fetchActiveTasks(
        SUPABASE_CONSTANTS.BULK_REVOKE_APPROVAL_INSTANCES.TASK_NAME,
        accessToken
      );
      return res;
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  // This if block wil be executed after the first time the data is fetched successfully from the UseReactQuery API
  if (!isDataLoading && !error && data) {
    // showBanner is set to true if the status of the task is PROCESSING else it is set to false
    if (
      updates?.new?.task_name ===
      SUPABASE_CONSTANTS.BULK_CREATE_APPROVAL_INSTANCES.TASK_NAME
    ) {
      if (updates?.new?.data?.status) {
        showBanner = [
          SUPABASE_CONSTANTS.BULK_CREATE_APPROVAL_INSTANCES.PENDING,
          SUPABASE_CONSTANTS.BULK_CREATE_APPROVAL_INSTANCES.PROCESSING,
        ].includes(updates?.new?.data?.status);
      }
      // showBanner is set to true if the data fetched from useReactQuery contains any active tasks running
      else if (data?.activeTasks && data.activeTasks.length > 0) {
        showBanner = true;
      }
    }
  }

  if (!isRevokeDataLoading && !revokeError && revokeData) {
    if (
      updates?.new?.task_name ===
      SUPABASE_CONSTANTS.BULK_REVOKE_APPROVAL_INSTANCES.TASK_NAME
    ) {
      if (updates?.new?.data?.status) {
        showBannerRevoke = [
          SUPABASE_CONSTANTS.BULK_REVOKE_APPROVAL_INSTANCES.PENDING,
          SUPABASE_CONSTANTS.BULK_REVOKE_APPROVAL_INSTANCES.PROCESSING,
        ].includes(updates?.new?.data?.status);
      } else if (revokeData?.activeTasks && revokeData.activeTasks.length > 0) {
        showBannerRevoke = true;
      }
    }
  }

  const [showUnFreezeConfirm, setShowUnFreezeConfirm] = useState(false);
  const [freezePayoutData, setFreezePayoutData] = useState({});
  const [
    commissionAdjustmentModalVisisble,
    setCommissionAdjustmentModalVisisble,
  ] = useState(false);
  const [modalPayeeEmail, setModalPayeeEmail] = useState("");

  const selectedCount =
    headerCbxState === HEADER_STATE.EVERYTHING
      ? `${totalRows}`
      : cumulativeList.length;

  const freezeData = (
    headerCbxState !== HEADER_STATE.EVERYTHING
      ? union(cumulativeSelectionData, tableData)
      : bulkActionsDataState
  ).map((item) => {
    return {
      comm_calc_status: item.comm_calc_status,
      payee_email_id: item.payee_email_id,
      settlement_calc_status: item.settlement_calc_status,
      is_comm_adj_requested: item.is_comm_adj_requested,
    };
  });

  const paymentData =
    headerCbxState !== HEADER_STATE.EVERYTHING
      ? union(cumulativeSelectionData, tableData)
      : bulkActionsPaymentDataState;

  const selectedRowKeys = useMemo(() => {
    if (headerCbxState !== HEADER_STATE.EVERYTHING) {
      return cumulativeList;
    } else {
      return bulkActionsDataState.map((item) => item["payee_email_id"]);
    }
  }, [bulkActionsDataState, cumulativeList, headerCbxState]);

  const saveTableState = useCallback(() => {
    const allState = parentRef?.current?.api.getColumnState();
    const orderAndVisibilityState = allState.map((state) => ({
      colId: state.colId,
      hide: state.hide,
    }));
    setTableState(orderAndVisibilityState);
  }, [setTableState]);

  const doOnFirstRender = (params) => {
    try {
      if (tableState) {
        params.api.applyColumnState({
          state: tableState,
          applyOrder: true,
        });
      }
    } catch (e) {
      console.log("Error in restoring table state : ", e);
    }
    adjustColumnWidth(params);
  };

  const resetRevokeConfirmation = () => {
    setShowRevokeConfirmModal({
      visible: false,
      records: null,
      bulkMode: false,
    });
    setRevokeConfirmationTitle("");
    setRevokeConfirmationSubtitle("");
  };
  const handleOk = () => {
    setRegisterPaymentWarningDetails({
      isVisible: false,
      title: "",
      message: "",
    });
    onPaymentComplete();
  };
  return (
    <>
      <RBACProtectedComponent permissionId={RBAC_ROLES.VIEW_REQUESTAPPROVALS}>
        {showBanner ? (
          <PollingBanner
            headingText="Processing Approval Instance creation..."
            descriptionText="Process running in background, you can initiate another request once it is done."
          />
        ) : null}
        {showBannerRevoke ? (
          <PollingBanner
            headingText="Processing Approval Requests Revoking..."
            descriptionText="Process running in background, you can initiate another request once it is done."
          />
        ) : null}
      </RBACProtectedComponent>
      <BulkActionWrapperStyled isCollapsed={cumulativeList.length <= 0}>
        {showBulkActions && (
          <div className="flex items-center w-full px-4 py-2 rounded-lg bg-ever-base-100">
            <div className="flex items-center gap-2">
              <EverInteractiveChip
                size="medium"
                title={
                  <span className="text-ever-base-content">
                    <EverTg.Text className="font-semibold">
                      {selectedCount}
                    </EverTg.Text>{" "}
                    selected
                  </span>
                }
                showIndicator={false}
                append={
                  <XCloseIcon
                    className="w-4 h-4 text-ever-base-content cursor-pointer"
                    onClick={clearSelection}
                  />
                }
              />
              {headerCbxState !== HEADER_STATE.EVERYTHING &&
                totalRows > pageSize && (
                  <EverButton
                    type="link"
                    onClick={() => {
                      setHeaderCbxState(HEADER_STATE.EVERYTHING);
                    }}
                  >
                    Select All {totalRows}
                  </EverButton>
                )}
              <EverDivider className="ml-2 mr-4 h-6" type="vertical" />
            </div>
            <div className="flex items-center gap-4">
              {bulkActionButtons.map((actionButton, index) => {
                if (
                  (actionButton.label === "Request Approval" ||
                    actionButton.label === "Revoke Approval") &&
                  !(approvalFeatureEnabled && payoutApprovalsEnabled)
                )
                  return null;

                if (
                  actionButton.label === "Download Statements" &&
                  hasPermissions(actionButton.rbacPermissions, true)
                ) {
                  // Only When the user has Export Payouts permission, the button will be enabled
                  return (
                    <RBACProtectedComponent
                      key={index}
                      permissionId={actionButton.rbacPermissions}
                      strictCheck={true}
                    >
                      <Dropdown
                        trigger={["click"]}
                        disabled={!showBulkDownloadStatements}
                        overlay={
                          <Menu>
                            <Menu.Item
                              onClick={() => {
                                onBulkActionChange(actionButton.value);
                                setDownloadStatementType(
                                  STATEMENT_EXPORT_TYPES.PDF
                                );
                              }}
                            >
                              <div className="flex items-center gap-3">
                                <PdfFileIcon className="text-ever-base-content-mid w-[18px] h-[18px]" />
                                <EverTg.Text>Download as PDF</EverTg.Text>
                              </div>
                            </Menu.Item>
                            <Menu.Item
                              onClick={() => {
                                onBulkActionChange(actionButton.value);
                                setDownloadStatementType(
                                  STATEMENT_EXPORT_TYPES.XLSX
                                );
                              }}
                            >
                              <div className="flex items-center gap-3">
                                <XlsxFileIcon className="text-ever-base-content-mid w-[18px] h-[18px]" />
                                <EverTg.Text>Download as Excel</EverTg.Text>
                              </div>
                            </Menu.Item>
                          </Menu>
                        }
                      >
                        <EverInteractiveChip
                          size="medium"
                          title={actionButton.label}
                          showIndicator={false}
                          onClick={null}
                          append={<ChevronDownIcon className="w-4 h-4" />}
                          disabled={true}
                          prepend={
                            !showBulkDownloadStatements ? (
                              <EverTooltip title="Please wait while the previous task completes.">
                                <InfoCircleIcon className="w-4 h-4 gap-2 text-ever-error" />
                              </EverTooltip>
                            ) : (
                              ""
                            )
                          }
                          className="w-50"
                        />
                      </Dropdown>
                    </RBACProtectedComponent>
                  );
                }

                return (
                  <RBACProtectedComponent
                    key={index}
                    permissionId={actionButton.rbacPermissions}
                  >
                    <EverInteractiveChip
                      size="medium"
                      title={actionButton.label}
                      showIndicator={false}
                      onClick={() => onBulkActionChange(actionButton.value)}
                    />
                  </RBACProtectedComponent>
                );
              })}
            </div>
          </div>
        )}
      </BulkActionWrapperStyled>
      <div
        className={twMerge(
          "ag-theme-material zebra-grid w-full flex flex-col flex-auto",
          loading ? "blur-sm" : "blur-0"
        )}
        ref={parentRef}
      >
        <AgGridReact
          {...everAgGridOptions.getDefaultOptions({ type: "md" })}
          ref={gridRef}
          rowHeight={56}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowData={rowData}
          onGridReady={onGridReady}
          suppressContextMenu={true}
          suppressRowClickSelection={true}
          suppressBrowserResizeObserver={true}
          masterDetail={true}
          detailCellRendererParams={detailCellRendererParams}
          onRowGroupOpened={onRowGroupOpened}
          onToolPanelVisibleChanged={(event) => {
            if (gridApi?.isToolPanelShowing()) {
              if (event.source && event.source === "columns") {
                sendAnalyticsEvent(
                  accessToken,
                  ANALYTICS_EVENTS.CUSTOMIZE_COLUMNS,
                  {
                    [ANALYTICS_PROPERTIES.PLATFORM_USED]: PLATFORM.PAYOUTS,
                  }
                );
              }
              onColumnVisibleChange();
            } else {
              adjustColumnWidth(event);
            }
          }}
          onFilterChanged={onFilterChanged}
          // customprops
          showColumn
          sideBar={hasPermissions([RBAC_ROLES.MANAGE_PAYOUTS]) ? sideBar : null}
          sideBarProps={{ hiddenByDefault: true }}
          sideColumnProps={{
            width: 250,
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              suppressColumnSelectAll: false,
            },
          }}
          paginationPageSize={pageSize}
          cacheBlockSize={pageSize}
          className="commissionTable"
          onColumnVisible={(params) => {
            if (isPayout && params.source === "toolPanelUi") {
              saveTableState();
            }
          }}
          onFirstDataRendered={doOnFirstRender}
        />
        <Pagination
          rowPerPageOption={[2, 10, 20, 50, 100]}
          pageCount={pageCount}
          pageSize={pageSize}
          totalRows={totalRows}
          setPageSize={setPageSize}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />
      </div>
      <FreezeConfirmationPopup
        t={t}
        type={freezeConfirmation}
        onClose={() => {
          if (!isLoading) {
            setFreezeConfirmation("");
          }
        }}
        selectedRowKeys={selectedRowKeys}
        tableData={freezeData}
        showSubTitle={payoutLevel === PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL}
        onConfirm={(keys) => {
          onClickFreezeStatus(
            keys,
            freezeConfirmation === COMMISSION_ACTION_STATUS.FREEZE
          );
          setFreezeConfirmation("");
        }}
        isLoading={isLoading}
        approvalFeatureEnabled={approvalFeatureEnabled}
        period={period}
        allowAdjustmentsToFrozenCommission={allowAdjustmentsToFrozenCommission}
      />
      <MakePaymentPopup
        key={makePayment.join(":")}
        keys={makePayment}
        onClose={() => {
          if (!isLoading) {
            setMakePayment([]);
          }
        }}
        onConfirm={handleMakePayment}
        tableData={paymentData}
        periodLabel={periodLabel}
        period={period}
        isLoading={isLoading}
        viewPayoutValueOfOthers={hasPermissions(
          RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS
        )}
        email={email}
      />
      <ProcessArrearPopup
        onClose={() => {
          if (!isLoading) {
            setSelectedPayee({});
          }
        }}
        onConfirm={onProcessArrear}
        payee={selectedPayee}
        isLoading={isLoading}
        customCalendar={customCalendar}
      />
      <ApprovalDrawer
        drawerVisibility={approvalDrawerVisibility}
        isLocked={isSelectedRecordLocked}
        closeDrawer={() => {
          setApprovalDrawerVisibility(false);
        }}
        refreshRequiredData={refetch}
        payeeName={selectedPayeeName}
        currencySymbol={selectedPayeeCurrency}
        payoutAmount={selectedPayeePayout}
        period={period}
        periodLabel={periodLabel}
        periodEndDate={selectedPeriod}
        selectedPayeeEntityKey={selectedPayeeEntityKey}
        applyFiltersFun={applyFiltersFun}
      />
      {showUnFreezeConfirm && (
        <UnFreezeConfirmationPopup
          isVisible={showUnFreezeConfirm}
          onCancel={() => setShowUnFreezeConfirm(false)}
          onConfirm={() => {
            onClickFreezeStatus(
              [freezePayoutData.email],
              freezePayoutData.isLocked
            );
            setShowUnFreezeConfirm(false);
          }}
        />
      )}
      {showApprovalTemplateSelector && (
        <ApprovalWorkFlowTemplateSelectorWrapper
          requestParams={approvalParams}
          showSelector={showApprovalTemplateSelector}
          setShowSelector={setShowApprovalTemplateSelector}
          refetch={refetch}
        />
      )}
      {commissionAdjustmentModalVisisble && (
        <CommissionAdjustmentModal
          visible={commissionAdjustmentModalVisisble}
          onCancel={() => setCommissionAdjustmentModalVisisble(false)}
          onLock={() => {
            onClickFreezeStatus([modalPayeeEmail], true);
            setCommissionAdjustmentModalVisisble(false);
          }}
          period={period}
          payeeEmail={modalPayeeEmail}
        />
      )}
      <StatementDownloadPopup
        t={t}
        type={downloadStatementType}
        onClose={() => {
          if (!isLoading) {
            setDownloadStatementType("");
          }
        }}
        selectedRowKeys={selectedRowKeys}
        tableData={freezeData}
        onConfirm={(email, keys) => {
          onClickDownloadStatements(email, keys);
          setDownloadStatementType("");
        }}
        isLoading={isLoading}
      />
      <RevokeConfirmationModal
        visible={showRevokeConfirmModal.visible}
        onClose={resetRevokeConfirmation}
        onConfirm={() => {
          handleRevokeApproval();
          resetRevokeConfirmation();
        }}
      />
      {clientFeatures.avoidConcurrentRegisterPayment && (
        <WarningModal
          isVisible={registerPaymentWarningDetails.isVisible}
          title={registerPaymentWarningDetails.title}
          message={registerPaymentWarningDetails.message}
          handleOk={handleOk}
        />
      )}
      {clientFeatures.canAvoidLockingWithPendingChanges && (
        <EverModal.Confirm
          title={pendingChangesModalDetails.title}
          subtitle={pendingChangesModalDetails.message}
          visible={pendingChangesModalDetails.show}
          width={620}
          onClose={() =>
            setPendingChangesModalDetails({
              show: false,
              title: "",
              message: "",
            })
          }
          confirmationButtons={[
            <EverButton
              key="cancel"
              color="base"
              onClick={() => {
                setPendingChangesModalDetails({
                  show: false,
                  title: "",
                  message: "",
                });
              }}
            >
              Dismiss
            </EverButton>,
            <EverButton
              key="run-commission-sync"
              color="primary"
              onClick={() => {
                setPendingChangesModalDetails({
                  show: false,
                  title: "",
                  message: "",
                });
              }}
            >
              <Link
                to={"/settings/commissions-and-data-sync"}
                className="text-ever-primary-content hover:text-ever-primary-content"
              >
                Run Commission Sync
              </Link>
            </EverButton>,
          ]}
          icon={<AlertCircleIcon className="h-6 w-6 text-ever-warning" />}
          iconContainerClasses="bg-ever-warning-lite"
        />
      )}
    </>
  );
});

export default CommissionTable;
