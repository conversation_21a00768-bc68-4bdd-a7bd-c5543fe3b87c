import { InfoSquareIcon } from "@everstage/evericons/outlined";
import { format } from "date-fns";
import { cloneDeep, isNil } from "lodash";
import React, { useEffect, useMemo, useState } from "react";
import { useMutation, useQuery } from "react-query";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ever<PERSON><PERSON><PERSON>,
  EverStepper,
  EverTg,
  message,
} from "~/v2/components";

import PriceFactors from "./steps/PriceFactors";
import PriceSettings from "./steps/PriceSettings";
import ShowEdits from "./steps/ShowEdits";
import { TruncatedText } from "../../../components";
import { useFetchApiWithAuth } from "../../../hooks";
import { DRAWER_STEPS, PRICE_POINT_PRICE_MODELS } from "../../utils";
import { SchedulePricePointModal } from "../SchedulePricePointModal";

const DEFAULT_PRICE_POINT = {
  name: "",
  price_model: null,
  currency: null,
  billing_frequency: null,
  list_price: null,
  flat_price: null,
  can_modify_quote: false,
  modification_treshold: null,
  flat_modification_treshold: null,
  can_apply_discount: false,
  max_discount: null,
  flat_max_discount: null,
  discountPercentage: null,
  flatDiscountPercentage: null,
  startNow: true,
  schedule_start_date: null,
  custom_factors: {
    region: "US",
  },
};

function validateCreatePricePoint(state) {
  const {
    name,
    price_model,
    billing_frequency,
    list_price,
    flat_price,
    can_modify_quote,
    modification_treshold,
    can_apply_discount,
    max_discount,
    flat_max_discount,
    flat_modification_treshold,
    discountPercentage,
    flatDiscountPercentage,
  } = state;
  if (!name || !price_model || !billing_frequency) return true;
  if (
    price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE &&
    (isNil(list_price) || list_price === "")
  )
    return true;
  if (
    price_model !== PRICE_POINT_PRICE_MODELS.PER_UNIT &&
    (isNil(flat_price) || flat_price === "")
  )
    return true;

  if (can_modify_quote) {
    if (price_model === PRICE_POINT_PRICE_MODELS.PER_UNIT) {
      // For per unit, only modification_treshold is used and it can be 0 but not null
      if (modification_treshold === null) return true;
    } else if (price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE) {
      // For flat fee, only flat_modification_treshold is used and it can be 0 but not null
      if (flat_modification_treshold === null) return true;
    } else {
      // For other price models, both values are required and can be 0 but not null
      if (modification_treshold === null || flat_modification_treshold === null)
        return true;
    }
  }
  if (can_apply_discount) {
    if (discountPercentage < 0 || flatDiscountPercentage < 0) return true;
    if (discountPercentage > 100 || flatDiscountPercentage > 100) return true;
    if (price_model === PRICE_POINT_PRICE_MODELS.PER_UNIT) {
      // For per unit, only max_discount is used and it can be 0 but not null
      if (max_discount === null) return true;
    } else if (price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE) {
      // For flat fee, only flat_max_discount is used and it can be 0 but not null
      if (flat_max_discount === null) return true;
    } else {
      // For other price models, both values are required and can be 0 but not null
      if (max_discount === null || flat_max_discount === null) return true;
    }
  }
  return false;
}

function constructPriceFactors(data) {
  const priceFactors = {};

  for (const key of Object.keys(data)) {
    const factor = data[key];
    if (factor.type === "default") {
      priceFactors[key] = Object.entries(factor.values).map(
        ([value, label]) => {
          return {
            label,
            value,
          };
        }
      );
    }
  }

  return priceFactors;
}

export function AddPricePointDrawer({
  visible,
  setVisible,
  isEdit,
  handleClose,
  isViewingEditedPricePoint,
  initialPricePoint,
  refetchPricePoints,
  productId,
  allPricePoints,
}) {
  const { fetchData } = useFetchApiWithAuth();

  const [currentStep, setCurrentStep] = useState(DRAWER_STEPS.BASIC_INFO);
  const [pricePoint, setPricePoint] = useState(DEFAULT_PRICE_POINT);
  const [inActivePricePoint, setInActivePricePoint] = useState({});
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [priceFactors, setPriceFactors] = useState([]);

  const pricepointsNameMap = useMemo(() => {
    const nameMap = {};
    for (const pricePoint of allPricePoints) {
      nameMap[pricePoint.name] = true;
    }
    return nameMap;
  }, [allPricePoints]);

  const createPricePointRequest = useMutation(
    (payload) => fetchData("/ninja/cpq/pricepoint/create", "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: async () => {
        message.success(
          isEdit
            ? "Price point updated successfully"
            : "Price point created successfully"
        );
        refetchPricePoints();
        handleOnClose();
      },
    }
  );

  const deletePricePointUpdateRequest = useMutation(
    () =>
      fetchData(
        `/ninja/cpq/pricepoint/delete_update_schedule/${pricePoint.pricepoint_id}`,
        "POST"
      ),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: async () => {
        message.success("Price point update deleted successfully");
        refetchPricePoints();
        handleOnClose();
      },
    }
  );

  const { isFetching } = useQuery(
    ["getPriceFactors"],
    () => {
      return fetchData(`/ninja/cpq/pricepoint/get_pricefactors`, "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        setPriceFactors(constructPriceFactors(data.price_factors));
      },
    }
  );

  useQuery(
    ["getPricepointDetails"],
    () => {
      return fetchData(
        `/ninja/cpq/pricepoint/get/${pricePoint.pricepoint_id}`,
        "GET"
      );
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: !!pricePoint.pricepoint_id && isViewingEditedPricePoint,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        setInActivePricePoint(data.inactive);
        setPricePoint(data.active);
      },
    }
  );

  function handleOnClose() {
    setShowScheduleModal(false);
    setVisible(false);
    setPricePoint(DEFAULT_PRICE_POINT);
    setInActivePricePoint(DEFAULT_PRICE_POINT);
    setCurrentStep(DRAWER_STEPS.BASIC_INFO);
    handleClose();
  }

  function handleOnCreate() {
    setShowScheduleModal(true);
  }

  function handleCreatePricePoint(date, updatedPricePoint) {
    const payload = {};
    const pricePointClone = cloneDeep(updatedPricePoint || pricePoint);

    payload.product_id = productId;
    payload.price_model = pricePointClone.price_model;
    payload.currency = pricePointClone.currency;
    payload.billing_frequency = pricePointClone.billing_frequency;
    payload.name = pricePointClone.name;
    payload.list_price =
      pricePointClone.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
        ? pricePointClone.flat_price
        : pricePointClone.list_price;
    if (pricePointClone.price_model !== PRICE_POINT_PRICE_MODELS.PER_UNIT) {
      payload.flat_price = pricePointClone.flat_price;
    }
    payload.modification_treshold = pricePointClone.modification_treshold || 0;
    payload.flat_modification_treshold =
      pricePointClone.flat_modification_treshold || 0;
    payload.max_discount =
      Math.round(Number(pricePointClone.max_discount)) || 0;
    payload.flat_max_discount =
      Math.round(Number(pricePointClone.flat_max_discount)) || 0;

    payload.custom_factors = {};

    if (
      pricePointClone.price_model === PRICE_POINT_PRICE_MODELS.VOLUME ||
      pricePointClone.price_model === PRICE_POINT_PRICE_MODELS.TIERED
    ) {
      payload.tier_data = pricePointClone.tier_data;
    }

    if (isEdit) {
      payload.pricepoint_id = pricePointClone.pricepoint_id;
    }

    payload.schedule_start_date = format(date, "yyyy-MM-dd");
    createPricePointRequest.mutate(payload);
  }

  function handleDeleteUpdate() {
    const modal = EverModal.error({
      title: "Delete price point update?",
      subtitle: "This action cannot be undone",
      confirmationButtons: [
        <EverButton
          key="cancel"
          type="filled"
          color="base"
          onClick={() => closeModal()}
        >
          Cancel
        </EverButton>,
        <EverButton
          key="confirm"
          onClick={() => {
            deletePricePointUpdateRequest.mutate();
            closeModal();
            handleOnClose();
          }}
          type="filled"
          color="error"
        >
          Yes, proceed
        </EverButton>,
      ],
    });
    const closeModal = () => {
      modal.destroy();
    };
  }

  function validatePricePointName() {
    const trimmedName = pricePoint.name.trim();

    if (trimmedName === "") {
      message.error("Price point name cannot be empty.");
      setPricePoint((prev) => {
        return { ...prev, name: trimmedName };
      });
      return false;
    }
    if (pricepointsNameMap[trimmedName]) {
      message.error("A price point with this name already exists.");
      return false;
    }
    return true;
  }

  useEffect(() => {
    if ((isEdit || isViewingEditedPricePoint) && initialPricePoint) {
      setPricePoint(initialPricePoint);
    }
    if (isViewingEditedPricePoint) {
      setCurrentStep(DRAWER_STEPS.SHOW_EDITS);
    } else if (isEdit) {
      setCurrentStep(DRAWER_STEPS.PRICE_SETTINGS);
    }
  }, [isEdit, isViewingEditedPricePoint, initialPricePoint]);
  return (
    <>
      <EverDrawer
        title={
          <div className="w-full flex items-center relative">
            <EverTg.Heading4>Add Price Point</EverTg.Heading4>
            {currentStep === DRAWER_STEPS.PRICE_SETTINGS && (
              <div className="absolute w-full flex justify-center items-start">
                <TruncatedText
                  text={pricePoint.name}
                  className="w-full max-w-[30%] w-max"
                >
                  <EverTg.Heading4 className="text-ever-base-content-mid">
                    {pricePoint.name}
                  </EverTg.Heading4>
                </TruncatedText>
              </div>
            )}
          </div>
        }
        placement="right"
        onClose={handleOnClose}
        visible={visible}
        width={800}
        bodyStyle={{ padding: "0px" }}
        footer={
          <div className="flex justify-between w-full items-center">
            {currentStep === DRAWER_STEPS.SHOW_EDITS ? (
              <div className="flex gap-4 items-center">
                <EverButton
                  color="base"
                  size="small"
                  type="ghost"
                  onClick={() => setCurrentStep(DRAWER_STEPS.PRICE_SETTINGS)}
                >
                  Edit Update
                </EverButton>
                <EverButton
                  color="error"
                  size="small"
                  type="ghost"
                  onClick={handleDeleteUpdate}
                >
                  Delete Update
                </EverButton>
              </div>
            ) : (
              <div className="flex gap-2 items-center">
                <InfoSquareIcon className="w-4 h-4 text-ever-base-content-mid" />
                <EverTg.Caption className="text-ever-base-content-mid">
                  Step {currentStep + 1} of 2
                </EverTg.Caption>
              </div>
            )}
            <div className="flex gap-6">
              {currentStep === DRAWER_STEPS.PRICE_SETTINGS && !isEdit && (
                <EverButton
                  type="filled"
                  color="base"
                  onClick={() => setCurrentStep(DRAWER_STEPS.BASIC_INFO)}
                >
                  Back
                </EverButton>
              )}
              {currentStep === DRAWER_STEPS.BASIC_INFO && (
                <EverButton
                  type="filled"
                  color="primary"
                  onClick={() => {
                    if (validatePricePointName()) {
                      setCurrentStep(DRAWER_STEPS.PRICE_SETTINGS);
                    }
                  }}
                  disabled={
                    !pricePoint.price_model ||
                    !pricePoint.currency ||
                    !pricePoint.billing_frequency ||
                    !pricePoint.name
                  }
                >
                  Next
                </EverButton>
              )}
              {currentStep === DRAWER_STEPS.PRICE_SETTINGS && (
                <EverButton
                  type="filled"
                  color="primary"
                  onClick={() => handleOnCreate()}
                  disabled={validateCreatePricePoint(pricePoint)}
                >
                  {isEdit ? "Apply changes" : "Create"}
                </EverButton>
              )}
              {currentStep === DRAWER_STEPS.SHOW_EDITS && (
                <EverButton
                  type="filled"
                  color="primary"
                  onClick={handleOnClose}
                >
                  Close
                </EverButton>
              )}
            </div>
          </div>
        }
        destroyOnClose={true}
      >
        {!isViewingEditedPricePoint && !isEdit && (
          <div className="flex flex-nowrap items-center bg-ever-primary-lite p-4 justify-center shadow-md sticky top-0 z-10">
            <div className="w-6/12">
              <EverStepper
                steps={["Select Price Factors", "Set Price"]}
                size="small"
                current={currentStep}
              />
            </div>
          </div>
        )}
        <div className="w-full py-8 pl-6 pr-4 h-full">
          {currentStep === DRAWER_STEPS.BASIC_INFO && (
            <PriceFactors
              pricePoint={pricePoint}
              setPricePoint={setPricePoint}
              priceFactors={priceFactors}
              isPriceFactorsFetching={isFetching}
            />
          )}
          {currentStep === DRAWER_STEPS.PRICE_SETTINGS && (
            <PriceSettings
              pricePoint={pricePoint}
              setPricePoint={setPricePoint}
              isEdit={isEdit}
            />
          )}
          {currentStep === DRAWER_STEPS.SHOW_EDITS && (
            <ShowEdits
              pricePoint={pricePoint}
              setPricePoint={setPricePoint}
              inActivePricePoint={inActivePricePoint}
              handleCreatePricePoint={handleCreatePricePoint}
              isConfirmEventLoading={createPricePointRequest.isLoading}
            />
          )}
        </div>
      </EverDrawer>
      {showScheduleModal && (
        <SchedulePricePointModal
          visible={showScheduleModal}
          setVisible={setShowScheduleModal}
          pricePoint={pricePoint}
          setPricePoint={setPricePoint}
          handleConfirm={handleCreatePricePoint}
          isConfirmEventLoading={createPricePointRequest.isLoading}
        />
      )}
    </>
  );
}
