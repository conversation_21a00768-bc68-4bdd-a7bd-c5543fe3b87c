import { AgGridReact } from "ag-grid-react";
import React, { useMemo } from "react";
import { twMerge } from "tailwind-merge";

import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import { numberFormatter } from "~/v2/features/cpq/utils";

function UpdatedDataCellRenderer(params) {
  const price = params?.data?.[params?.keyName];
  return (
    <>
      {price?.isValueUpdated ? (
        <div className="flex gap-px">
          <div
            className={twMerge(
              `grid px-2 line-through`,
              price.isValueIncreased ? "text-ever-error" : "text-ever-success"
            )}
          >
            {numberFormatter(price.oldValue, {
              innerPrefix: "$",
              precision: 2,
              locale: "en-US",
            })}
          </div>
          <div
            className={twMerge(
              `grid px-2`,
              price.isValueIncreased ? "text-ever-success" : "text-ever-error"
            )}
          >
            {numberFormatter(price.newValue, {
              innerPrefix: "$",
              precision: 2,
              locale: "en-US",
            })}
          </div>
        </div>
      ) : (
        <div
          className={twMerge(
            "grid px-2",
            params?.data?.isDeleted && "!line-through"
          )}
        >
          {numberFormatter(price, {
            innerPrefix: "$",
            precision: 2,
            locale: "en-US",
          })}
        </div>
      )}
    </>
  );
}

function UpdatedDataCellRendererDiscount(params) {
  const discount = params?.data?.[params?.keyName];
  return (
    <>
      {discount?.isValueUpdated ? (
        <div className="flex gap-px">
          <div
            className={twMerge(
              `grid px-2 line-through`,
              discount.isValueIncreased
                ? "text-ever-error"
                : "text-ever-success"
            )}
          >
            {discount.oldValue}%
          </div>
          <div
            className={twMerge(
              `grid px-2`,
              discount.isValueIncreased
                ? "text-ever-success"
                : "text-ever-error"
            )}
          >
            {discount.newValue}%
          </div>
        </div>
      ) : (
        <div
          className={twMerge(
            "grid px-2",
            params?.data?.isDeleted && "!line-through"
          )}
        >
          {discount}%
        </div>
      )}
    </>
  );
}

export function ComparisonTable({ originalData, updatedData }) {
  const rowData = useMemo(() => {
    const originalMap = {};
    const updatedMap = {};
    const rows = [];
    const addedTiers = {};

    if (originalData)
      for (const tier of originalData) {
        const range = `${tier.lower_bound}-${tier.upper_bound}`;
        const tierData = {
          range,
          list_price: tier.list_price,
          tier_discount: tier.tier_discount,
          flat_fee_discount: tier.flat_fee_discount,
          flat_price: tier.flat_price,
          lower_bound: tier.lower_bound,
          upper_bound: tier.upper_bound,
        };
        originalMap[range] = tierData;
      }

    if (updatedData)
      for (const tier of updatedData) {
        const range = `${tier.lower_bound}-${tier.upper_bound}`;
        const tierData = {
          range,
          list_price: tier.list_price,
          tier_discount: tier.tier_discount,
          flat_fee_discount: tier.flat_fee_discount,
          flat_price: tier.flat_price,
          lower_bound: tier.lower_bound,
          upper_bound: tier.upper_bound,
        };
        updatedMap[range] = tierData;
      }

    for (const range of Object.keys(originalMap)) {
      if (updatedMap[range]) {
        const newRow = {};
        for (const key of Object.keys(originalMap[range])) {
          newRow[key] =
            originalMap[range][key] === updatedMap[range][key]
              ? updatedMap[range][key]
              : {
                  oldValue: originalMap[range][key],
                  newValue: updatedMap[range][key],
                  isValueUpdated: true,
                  isValueIncreased:
                    updatedMap[range][key] > originalMap[range][key],
                };
        }
        rows.push(newRow);
      } else {
        rows.push({
          ...originalMap[range],
          isDeleted: true,
        });
      }
      addedTiers[range] = true;
    }

    for (const range of Object.keys(updatedMap)) {
      if (!addedTiers[range]) {
        rows.push({
          ...updatedMap[range],
          isNewTier: true,
        });
      }
    }

    rows.sort((a, b) => {
      const [aLower, aUpper] = a.range.split("-").map(Number);
      const [bLower, bUpper] = b.range.split("-").map(Number);

      return aUpper === bUpper ? aLower - bLower : aUpper - bUpper;
    });

    return rows;
  }, [originalData, updatedData]);

  const columnDefs = useMemo(() => {
    return [
      {
        field: "range",
        headerName: "TIER RANGE",
        headerClass: "!px-4 !text-ever-base-content",
        cellRenderer: (params) => {
          return (
            <div
              className={twMerge(
                "grid px-2",
                params?.data?.isDeleted && "!line-through"
              )}
            >{`${params?.data?.lower_bound}${
              params?.data?.upper_bound === 1_000_000
                ? " and above"
                : ` - ${params?.data?.upper_bound}`
            }`}</div>
          );
        },
      },
      {
        field: "tier_discount",
        headerName: "TIER DISCOUNT",
        headerClass: "!px-4 !text-ever-base-content",
        cellClass: (params) => {
          const tierDiscount = params?.data?.tier_discount;
          return twMerge(
            "justify-end",
            tierDiscount?.isValueUpdated
              ? tierDiscount?.isValueIncreased
                ? "bg-ever-success-lite"
                : "bg-ever-error-lite"
              : ""
          );
        },
        type: "rightAligned",
        cellRenderer: UpdatedDataCellRendererDiscount,
        cellRendererParams: (params) => ({
          data: params?.data,
          keyName: "tier_discount",
        }),
      },
      {
        field: "list_price",
        headerName: "TIER LIST UNIT PRICE",
        headerClass: "!px-4 !text-ever-base-content",
        cellClass: (params) => {
          const listPrice = params?.data?.list_price;
          return twMerge(
            "justify-end",
            listPrice?.isValueUpdated
              ? listPrice?.isValueIncreased
                ? "bg-ever-success-lite"
                : "bg-ever-error-lite"
              : ""
          );
        },
        type: "rightAligned",
        cellRenderer: UpdatedDataCellRenderer,
        cellRendererParams: (params) => ({
          data: params?.data,
          keyName: "list_price",
        }),
      },
      {
        field: "flat_fee_discount",
        headerName: "TIER FLAT FEE DISCOUNT",
        headerClass: "!px-4 !text-ever-base-content",
        cellClass: (params) => {
          const flatFeeDiscount = params?.data?.flat_fee_discount;
          return twMerge(
            "justify-end",
            flatFeeDiscount?.isValueUpdated
              ? flatFeeDiscount?.isValueIncreased
                ? "bg-ever-success-lite"
                : "bg-ever-error-lite"
              : ""
          );
        },
        type: "rightAligned",
        cellRenderer: UpdatedDataCellRendererDiscount,
        cellRendererParams: (params) => ({
          data: params?.data,
          keyName: "flat_fee_discount",
        }),
      },
      {
        field: "flat_price",
        headerName: "TIER LIST FLAT FEE",
        headerClass: "!px-4 !text-ever-base-content",
        cellClass: (params) => {
          const flatPrice = params?.data?.flat_price;
          return twMerge(
            "justify-end",
            flatPrice?.isValueUpdated
              ? flatPrice?.isValueIncreased
                ? "bg-ever-success-lite"
                : "bg-ever-error-lite"
              : ""
          );
        },
        type: "rightAligned",
        cellRenderer: UpdatedDataCellRenderer,
        cellRendererParams: (params) => ({
          data: params?.data,
          keyName: "flat_price",
        }),
      },
    ];
  }, []);

  return (
    <div className="w-full h-full mt-2 mb-5 overflow-hidden">
      <div className="ag-theme-material w-full h-full overflow-y-auto quote-list-table">
        <AgGridReact
          {...getDefaultOptions({ type: "sm" })}
          rowData={rowData}
          columnDefs={columnDefs}
          rowClassRules={{
            "bg-ever-success-lite !text-ever-success": (params) =>
              params?.data?.isNewTier,
            "bg-ever-error-lite !text-ever-error !line-through": (params) =>
              params?.data?.isDeleted,
          }}
          rowHeight={40}
          headerHeight={40}
          suppressContextMenu={true}
          rowBuffer={50}
          pagination={false}
        />
      </div>
    </div>
  );
}
