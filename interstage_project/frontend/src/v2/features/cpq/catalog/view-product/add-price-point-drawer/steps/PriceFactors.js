import React from "react";

import { EverSelect, EverInput, EverLoader } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

import { SectionHeader } from "./PriceSettings";
import { PRICE_MODEL_TYPES, getDisplayNameforKeys } from "../../../utils";

export default function PriceFactors({
  pricePoint,
  setPricePoint,
  priceFactors,
  isPriceFactorsFetching,
}) {
  function handleFactorChange(value, name) {
    setPricePoint((prev) => {
      const updatedPricePoint = {
        ...prev,
        [name]: value,
      };

      // Automatically update the name field
      if (
        name === "price_model" ||
        name === "currency" ||
        name === "billing_frequency"
      ) {
        updatedPricePoint.name = `${
          getDisplayNameforKeys(updatedPricePoint.price_model) || ""
        } - ${updatedPricePoint.currency || "USD"} - ${
          getDisplayNameforKeys(updatedPricePoint.billing_frequency) || ""
        }`;
      }

      return updatedPricePoint;
    });
  }

  return (
    <div className="flex flex-col gap-6">
      <SectionHeader title="Price Factors" />
      {isPriceFactorsFetching ? (
        <EverLoader spinner />
      ) : (
        <div className="flex flex-col gap-5">
          <LabeledField required label="Price Model" labelPlacement="top">
            <EverSelect
              name="price_model"
              options={PRICE_MODEL_TYPES}
              onChange={(value) => handleFactorChange(value, "price_model")}
              value={pricePoint.price_model}
              placeholder="Select Price Model"
            />
          </LabeledField>
          <LabeledField required label="Currency" labelPlacement="top">
            <EverSelect
              name="currency"
              onChange={(value) => handleFactorChange(value, "currency")}
              value={pricePoint.currency}
              placeholder="Select currency"
              options={priceFactors.currency}
            />
          </LabeledField>
          <LabeledField required label="Billing Frequency" labelPlacement="top">
            <EverSelect
              name="billing_frequency"
              options={priceFactors.billing_frequency}
              onChange={(value) =>
                handleFactorChange(value, "billing_frequency")
              }
              value={pricePoint.billing_frequency}
              placeholder="Select billing frequency"
            />
          </LabeledField>
          <LabeledField required label="Price Point Name" labelPlacement="top">
            <EverInput
              name="name"
              onChange={(e) => handleFactorChange(e.target.value, "name")}
              value={pricePoint.name}
              placeholder="Enter price point name"
              disabled={
                !pricePoint.billing_frequency ||
                !pricePoint.currency ||
                !pricePoint.price_model
              }
            />
          </LabeledField>
        </div>
      )}
    </div>
  );
}
