import {
  InfoCircleIcon,
  // SwitchVerticalIcon,
} from "@everstage/evericons/outlined";
import React, { useEffect, useState, useCallback } from "react";
import { twMerge } from "tailwind-merge";

import {
  EverDivider,
  EverInput,
  EverSwitch,
  EverTg,
  EverTooltip,
} from "~/v2/components";
import {
  AnimatedWrapper,
  ANIMATION_TYPES,
  InputGroup,
  LabeledField,
} from "~/v2/features/cpq/components";

import AddTiersTable from "./AddTiersTable";
import { PRICE_POINT_PRICE_MODELS } from "../../../utils";

export function SectionHeader({ title }) {
  return (
    <div className="flex gap-2 items-center">
      <EverTg.Caption className="whitespace-nowrap font-medium">
        {title.toUpperCase()}
      </EverTg.Caption>
      <EverDivider />
    </div>
  );
}

const TIERED_PRICE_POINTS = new Set([
  PRICE_POINT_PRICE_MODELS.TIERED,
  PRICE_POINT_PRICE_MODELS.VOLUME,
]);

const inputNumberCommonProps = {
  min: 0,
  formatter: (value) => value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ","),
  parser: (value) => value.replace(/,/g, ""),
};

export default function PriceSettings({ pricePoint, setPricePoint, isEdit }) {
  function handleFactorChange(value, name) {
    if (name === "can_modify_quote" && value === false) {
      setPricePoint((prev) => ({
        ...prev,
        can_modify_quote: false,
        modification_treshold: 0,
        flat_modification_treshold: 0,
      }));
    } else if (name === "can_apply_discount" && value === false) {
      setPricePoint((prev) => ({
        ...prev,
        can_apply_discount: false,
        max_discount: 0,
        flat_max_discount: 0,
      }));
    }

    setPricePoint((prev) => ({
      ...prev,
      [name]: value,
    }));
  }

  return (
    <div className="flex flex-col w-full">
      <div className="flex flex-col gap-5 w-full mb-8">
        <SectionHeader title="Price Settings" />
        <div className="flex flex-col gap-4 w-full">
          {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
            <LabeledField
              label={
                TIERED_PRICE_POINTS.has(pricePoint.price_model)
                  ? "Base List Unit Price"
                  : "List Unit Price"
              }
              labelPlacement="top"
            >
              <InputGroup prefix={"$"}>
                <EverInput.Number
                  name="list_price"
                  onChange={(value) => handleFactorChange(value, "list_price")}
                  value={pricePoint.list_price}
                  className="w-full [&>.ant-input-number-handler-wrap]:!hidden"
                  {...inputNumberCommonProps}
                />
              </InputGroup>
            </LabeledField>
          )}
          {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.PER_UNIT && (
            <LabeledField
              label={
                TIERED_PRICE_POINTS.has(pricePoint.price_model)
                  ? "Base List Flat Fee"
                  : "List Flat Fee"
              }
              labelPlacement="top"
            >
              <InputGroup prefix={"$"}>
                <EverInput.Number
                  name="flat_price"
                  onChange={(value) => handleFactorChange(value, "flat_price")}
                  value={
                    pricePoint.flat_price === null &&
                    pricePoint.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
                      ? pricePoint.list_price
                      : pricePoint.flat_price
                  }
                  className="w-full [&>.ant-input-number-handler-wrap]:!hidden"
                  {...inputNumberCommonProps}
                />
              </InputGroup>
            </LabeledField>
          )}
        </div>
      </div>
      {(pricePoint.price_model === PRICE_POINT_PRICE_MODELS.TIERED ||
        pricePoint.price_model === PRICE_POINT_PRICE_MODELS.VOLUME) && (
        <div className="flex flex-col gap-6 mb-6">
          <SectionHeader title="Tier Configuration" />
          <AddTiersTable
            pricePoint={pricePoint}
            setPricePoint={setPricePoint}
          />
        </div>
      )}
      <div className="flex flex-col gap-6">
        <SectionHeader title="Other Settings" />
        <OtherSettings
          pricePoint={pricePoint}
          handleFactorChange={handleFactorChange}
          setPricePoint={setPricePoint}
          isEdit={isEdit}
        />
      </div>
    </div>
  );
}

function OtherSettings({
  pricePoint,
  setPricePoint,
  handleFactorChange,
  isEdit,
}) {
  const [_percentageError, setPercentageError] = useState({
    discountPercentage: false,
    flat_max_discount: false,
  });

  const handleDiscountAmountChange = useCallback(
    (key, value) => {
      switch (key) {
        case "max_discount": {
          const denominator =
            pricePoint.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
              ? pricePoint.flat_price
              : pricePoint.list_price;
          let percentage = "0";
          if (denominator) {
            percentage = ((value / denominator) * 100).toFixed(2);
          }
          const isError = Number(percentage) > 100 || Number(percentage) < 0;
          setPercentageError((prev) => ({
            ...prev,
            max_discount: isError,
          }));
          setPricePoint((prev) => ({
            ...prev,
            discountPercentage: percentage,
            max_discount: value,
          }));

          break;
        }
        case "discountPercentage": {
          const isError = Number(value) > 100 || Number(value) < 0;
          setPercentageError((prev) => ({
            ...prev,
            max_discount: isError,
          }));
          const denominator =
            pricePoint.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
              ? pricePoint.flat_price
              : pricePoint.list_price;
          let amount = "0";
          if (denominator !== 0) {
            amount = ((value / 100) * denominator).toFixed(2);
          }
          setPricePoint((prev) => ({
            ...prev,
            max_discount: amount,
            discountPercentage: value,
          }));

          break;
        }
        case "unitPriceChanged": {
          const percentage = ((pricePoint.max_discount / value) * 100).toFixed(
            2
          );
          setPricePoint((prev) => ({
            ...prev,
            discountPercentage: percentage,
          }));

          break;
        }
        case "flat_max_discount": {
          const denominator = pricePoint.flat_price;
          let percentage = "0";
          if (denominator) {
            percentage = ((value / denominator) * 100).toFixed(2);
          }
          const isError = Number(percentage) > 100 || Number(percentage) < 0;
          setPercentageError((prev) => ({
            ...prev,
            flat_max_discount: isError,
          }));
          setPricePoint((prev) => ({
            ...prev,
            flatDiscountPercentage: percentage,
            flat_max_discount: value,
          }));

          break;
        }
        case "flatFeeChanged": {
          const percentage = (
            (pricePoint.flat_max_discount / value) *
            100
          ).toFixed(2);
          setPricePoint((prev) => ({
            ...prev,
            flatDiscountPercentage: percentage,
          }));

          break;
        }
        case "flatDiscountPercentage": {
          const isError = Number(value) > 100 || Number(value) < 0;
          setPercentageError((prev) => ({
            ...prev,
            flat_max_discount: isError,
          }));
          const denominator = pricePoint.flat_price;
          let amount = "0";
          if (denominator !== 0) {
            amount = ((value / 100) * denominator).toFixed(2);
          }
          setPricePoint((prev) => ({
            ...prev,
            flat_max_discount: amount,
            flatDiscountPercentage: value,
          }));

          break;
        }
      }
    },
    [pricePoint, setPricePoint, setPercentageError]
  );

  useEffect(() => {
    // Only update if the percentage doesn't match the current price
    if (
      (pricePoint.can_apply_discount ||
        pricePoint.max_discount ||
        pricePoint.flat_max_discount) &&
      pricePoint.list_price > 0 &&
      pricePoint.max_discount > 0
    ) {
      const currentPercentage = (
        (pricePoint.max_discount / pricePoint.list_price) *
        100
      ).toFixed(2);
      if (currentPercentage !== pricePoint.discountPercentage) {
        handleDiscountAmountChange("unitPriceChanged", pricePoint.list_price);
      }
    }
  }, [
    pricePoint.list_price,
    pricePoint.can_apply_discount,
    pricePoint.flat_max_discount,
    pricePoint.max_discount,
    pricePoint.discountPercentage,
    handleDiscountAmountChange,
  ]);

  useEffect(() => {
    // Only update if the percentage doesn't match the current price
    if (
      (pricePoint.can_apply_discount ||
        pricePoint.flat_max_discount ||
        pricePoint.flat_discount_percentage) &&
      pricePoint.flat_price > 0 &&
      pricePoint.flat_max_discount > 0
    ) {
      const currentPercentage = (
        (pricePoint.flat_max_discount / pricePoint.flat_price) *
        100
      ).toFixed(2);
      if (currentPercentage !== pricePoint.flatDiscountPercentage) {
        handleDiscountAmountChange("flatFeeChanged", pricePoint.flat_price);
      }
    }
  }, [
    pricePoint.flat_price,
    pricePoint.can_apply_discount,
    pricePoint.flat_discount_percentage,
    pricePoint.flat_max_discount,
    pricePoint.flatDiscountPercentage,
    handleDiscountAmountChange,
  ]);

  return (
    <div
      className={twMerge(
        "flex flex-col",
        pricePoint.can_apply_discount && pricePoint.can_modify_quote
          ? "gap-7"
          : "gap-4"
      )}
    >
      <div className="flex flex-col">
        <div className="flex items-center">
          <EverSwitch
            checked={
              isEdit
                ? pricePoint.modification_treshold ||
                  pricePoint.flat_modification_treshold ||
                  pricePoint.can_modify_quote
                : pricePoint.can_modify_quote
            }
            label="Allow reps to modify the list price on the quote"
            onChange={(checked) =>
              handleFactorChange(checked, "can_modify_quote")
            }
          />
          <EverTooltip
            title="Restrict how low or high list price can be set by reps before
              discounts. This works only when reps are allowed to modify list
              price on quote."
          >
            <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
          </EverTooltip>
        </div>
        <AnimatedWrapper
          isVisible={
            isEdit
              ? pricePoint.modification_treshold ||
                pricePoint.flat_modification_treshold ||
                pricePoint.can_modify_quote
              : pricePoint.can_modify_quote
          }
          motionProps={{
            type: ANIMATION_TYPES.EXPAND_COLLAPSE,
          }}
        >
          <div className="flex flex-col gap-4 mt-4">
            {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
              <LabeledField
                label={`Configure modification range for list unit price${
                  TIERED_PRICE_POINTS.has(pricePoint.price_model)
                    ? " for each tier"
                    : ""
                } (+/-)`}
                labelPlacement="top"
              >
                <InputGroup prefix="$">
                  <EverInput.Number
                    name="modification_treshold"
                    onChange={(value) =>
                      handleFactorChange(value, "modification_treshold")
                    }
                    value={pricePoint.modification_treshold}
                    className={twMerge(
                      "w-full [&>.ant-input-number-handler-wrap]:!hidden "
                    )}
                    max={pricePoint.list_price}
                    onKeyDown={(e) => {
                      if (
                        e.key === "Backspace" ||
                        e.key === "Delete" ||
                        e.key === "ArrowLeft" ||
                        e.key === "ArrowRight" ||
                        e.key === "Enter"
                      ) {
                        return;
                      }
                      if (!"0123456789.".includes(e.key)) {
                        e.preventDefault();
                      }
                    }}
                    {...inputNumberCommonProps}
                  />
                </InputGroup>
              </LabeledField>
            )}
            {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.PER_UNIT && (
              <LabeledField
                label={`Configure modification range for list flat fee ${
                  TIERED_PRICE_POINTS.has(pricePoint.price_model)
                    ? "for each tier"
                    : ""
                } (+/-)`}
                labelPlacement="top"
              >
                <InputGroup prefix="$">
                  <EverInput.Number
                    name="flat_modification_treshold"
                    onChange={(value) =>
                      handleFactorChange(value, "flat_modification_treshold")
                    }
                    value={pricePoint.flat_modification_treshold}
                    className={twMerge(
                      "w-full [&>.ant-input-number-handler-wrap]:!hidden "
                    )}
                    max={pricePoint.flat_price}
                    onKeyDown={(e) => {
                      if (
                        e.key === "Backspace" ||
                        e.key === "Delete" ||
                        e.key === "ArrowLeft" ||
                        e.key === "ArrowRight" ||
                        e.key === "Enter"
                      ) {
                        return;
                      }
                      if (!"0123456789.".includes(e.key)) {
                        e.preventDefault();
                      }
                    }}
                    {...inputNumberCommonProps}
                  />
                </InputGroup>
              </LabeledField>
            )}
          </div>
        </AnimatedWrapper>
      </div>
      <div className="flex flex-col">
        <EverSwitch
          checked={
            isEdit
              ? pricePoint.max_discount ||
                pricePoint.flat_max_discount ||
                pricePoint.can_apply_discount
              : pricePoint.can_apply_discount
          }
          label="Allow reps to apply discount to this price point on the quote"
          onChange={(checked) =>
            handleFactorChange(checked, "can_apply_discount")
          }
        />
        <AnimatedWrapper
          isVisible={
            isEdit
              ? pricePoint.max_discount ||
                pricePoint.flat_max_discount ||
                pricePoint.can_apply_discount
              : pricePoint.can_apply_discount
          }
          motionProps={{
            type: ANIMATION_TYPES.EXPAND_COLLAPSE,
          }}
        >
          <div className="w-full flex flex-col gap-4 mt-4">
            {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
              <div className="w-full flex flex-col gap-2">
                <LabeledField
                  label="Configure max discount reps can apply to list unit price"
                  labelPlacement="top"
                >
                  <InputGroup prefix={"$"} className="w-full">
                    <EverInput.Number
                      name="max_discount"
                      onChange={(value) =>
                        handleDiscountAmountChange("max_discount", value)
                      }
                      value={pricePoint.max_discount}
                      className={twMerge(
                        "w-full [&>.ant-input-number-handler-wrap]:!hidden "
                      )}
                      max={pricePoint.list_price}
                      onKeyDown={(e) => {
                        if (
                          e.key === "Backspace" ||
                          e.key === "Delete" ||
                          e.key === "ArrowLeft" ||
                          e.key === "ArrowRight" ||
                          e.key === "Enter"
                        ) {
                          return;
                        }
                        if (!"0123456789.".includes(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      {...inputNumberCommonProps}
                    />
                  </InputGroup>
                </LabeledField>
                {/* <div className="mt-2.5">
                    <SwitchVerticalIcon className="w-5 h-5 text-ever-info rotate-90" />
                  </div>
                  <div className="flex flex-col gap-2">
                    <InputGroup prefix={"%"} className="w-[295px]">
                      <EverInput.Number
                        name="discountPercentage"
                        onChange={(value) =>
                          handleDiscountAmountChange(
                            "discountPercentage",
                            value
                          )
                        }
                        value={pricePoint.discountPercentage}
                        placeholder="0%"
                        className={twMerge(
                          "w-full [&>.ant-input-number-handler-wrap]:!hidden"
                        )}
                        formatter={(value) => {
                          return value ? `${value}%` : "0%";
                        }}
                        parser={(value) => value.replace("%", "")}
                        onKeyDown={(e) => {
                          if (
                            e.key === "Backspace" ||
                            e.key === "Delete" ||
                            e.key === "ArrowLeft" ||
                            e.key === "ArrowRight" ||
                            e.key === "Enter"
                          ) {
                            return;
                          }
                          if (!"0123456789.".includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </InputGroup>
                    {percentageError?.["max_discount"] && (
                      <EverTg.Caption className="text-ever-error -mt-2">
                        Value must be between 0 and 100
                      </EverTg.Caption>
                    )}
                  </div> */}
              </div>
            )}
            {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.PER_UNIT && (
              <div className="w-full flex flex-col gap-2">
                <LabeledField
                  label="Configure max discount reps can apply to list flat fee"
                  labelPlacement="top"
                >
                  <InputGroup prefix={"$"} className="w-full">
                    <EverInput.Number
                      name="flat_max_discount"
                      onChange={(value) =>
                        handleDiscountAmountChange("flat_max_discount", value)
                      }
                      value={pricePoint.flat_max_discount}
                      className={twMerge(
                        "w-full [&>.ant-input-number-handler-wrap]:!hidden "
                      )}
                      max={pricePoint.flat_price}
                      onKeyDown={(e) => {
                        if (
                          e.key === "Backspace" ||
                          e.key === "Delete" ||
                          e.key === "ArrowLeft" ||
                          e.key === "ArrowRight" ||
                          e.key === "Enter"
                        ) {
                          return;
                        }
                        if (!"0123456789.".includes(e.key)) {
                          e.preventDefault();
                        }
                      }}
                      {...inputNumberCommonProps}
                    />
                  </InputGroup>
                </LabeledField>
                {/* <div className="mt-2.5">
                    <SwitchVerticalIcon className="w-5 h-5 text-ever-info rotate-90" />
                  </div>
                  <div className="flex flex-col gap-2">
                    <InputGroup prefix={"%"} className="w-[295px]">
                      <EverInput.Number
                        name="flatDiscountPercentage"
                        onChange={(value) =>
                          handleDiscountAmountChange(
                            "flatDiscountPercentage",
                            value
                          )
                        }
                        value={pricePoint.flatDiscountPercentage}
                        placeholder="0%"
                        className={twMerge(
                          "w-full [&>.ant-input-number-handler-wrap]:!hidden"
                        )}
                        formatter={(value) => {
                          return value ? `${value}%` : "0%";
                        }}
                        parser={(value) => value.replace("%", "")}
                        onKeyDown={(e) => {
                          if (
                            e.key === "Backspace" ||
                            e.key === "Delete" ||
                            e.key === "ArrowLeft" ||
                            e.key === "ArrowRight" ||
                            e.key === "Enter"
                          ) {
                            return;
                          }
                          if (!"0123456789.".includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                    </InputGroup>
                    {percentageError?.["flat_max_discount"] && (
                      <EverTg.Caption className="text-ever-error -mt-2">
                        Value must be between 0 and 100
                      </EverTg.Caption>
                    )}
                  </div> */}
              </div>
            )}
          </div>
        </AnimatedWrapper>
      </div>
    </div>
  );
}
