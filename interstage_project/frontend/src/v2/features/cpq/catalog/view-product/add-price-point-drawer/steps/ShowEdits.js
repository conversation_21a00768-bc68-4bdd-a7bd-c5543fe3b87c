import { PencilLineIcon } from "@everstage/evericons/duotone";
import { CalendarPlusIcon } from "@everstage/evericons/outlined";
import { format } from "date-fns";
import React, { useState } from "react";

import { EverButton, EverTg } from "~/v2/components";

import { ComparisonTable } from "./ComparisonTable";
import { SectionHeader } from "./PriceSettings";
import { getFormattedAmount, PRICE_POINT_PRICE_MODELS } from "../../../utils";
import { SchedulePricePointModal } from "../../SchedulePricePointModal";

export default function ShowEdits({
  pricePoint,
  inActivePricePoint,
  handleCreatePricePoint,
  isConfirmEventLoading,
}) {
  const [updatePricePoint, setUpdatePricePoint] = useState(null);
  const [showScheduleModal, setShowScheduleModal] = useState(false);

  function handleOpenScheduleModal() {
    setUpdatePricePoint(inActivePricePoint);
    setShowScheduleModal(true);
  }
  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="flex items-center justify-between w-full px-3 bg-ever-info-lite border border-solid border-ever-info rounded-md text-ever-info-lite-content">
        <div className="flex gap-2.5">
          <CalendarPlusIcon className="w-5 h-5" />
          <EverTg.Caption className="text-ever-info-hover font-medium">
            Scheduled to become active from{" "}
            {pricePoint.schedule_end_date
              ? format(new Date(pricePoint.schedule_end_date), "MMM d, yyyy")
              : "-"}
          </EverTg.Caption>
        </div>
        <EverButton
          size="small"
          color="info"
          type="link"
          icon={<PencilLineIcon />}
          onClick={() => handleOpenScheduleModal()}
        >
          Change date
        </EverButton>
      </div>
      <div className="flex flex-col gap-5 w-full">
        <SectionHeader title="Price Settings" />
        {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
          <div className="flex w-full">
            <div className="w-2/5">
              <EverTg.Text className="text-ever-base-content-mid">
                List Unit Price
              </EverTg.Text>
            </div>
            <div className="w-3/5">
              {inActivePricePoint?.list_price === pricePoint?.list_price ? (
                <EverTg.SubHeading4>
                  {getFormattedAmount(pricePoint.list_price)}
                </EverTg.SubHeading4>
              ) : (
                <div className="flex gap-2">
                  <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                    {getFormattedAmount(pricePoint.list_price)}
                  </EverTg.SubHeading4>
                  <EverTg.SubHeading4 className="text-ever-success-hover">
                    {getFormattedAmount(inActivePricePoint.list_price)}
                  </EverTg.SubHeading4>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="flex w-full">
          <div className="w-2/5">
            <EverTg.Text className="text-ever-base-content-mid">
              List Flat Fee
            </EverTg.Text>
          </div>
          <div className="w-3/5">
            {inActivePricePoint?.flat_price === pricePoint?.flat_price ? (
              <EverTg.SubHeading4>
                {getFormattedAmount(pricePoint.flat_price)}
              </EverTg.SubHeading4>
            ) : (
              <div className="flex gap-2">
                <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                  {getFormattedAmount(pricePoint.flat_price)}
                </EverTg.SubHeading4>
                <EverTg.SubHeading4 className="text-ever-success-hover">
                  {getFormattedAmount(inActivePricePoint.flat_price)}
                </EverTg.SubHeading4>
              </div>
            )}
          </div>
        </div>
      </div>
      {(pricePoint.price_model === PRICE_POINT_PRICE_MODELS.TIERED ||
        pricePoint.price_model === PRICE_POINT_PRICE_MODELS.VOLUME) && (
        <div className="flex flex-col gap-5 w-full h-full">
          <SectionHeader title="Tier Configuration" />
          {pricePoint.tier_data && inActivePricePoint.tier_data && (
            <ComparisonTable
              originalData={pricePoint.tier_data}
              updatedData={inActivePricePoint.tier_data}
            />
          )}
        </div>
      )}
      <div className="flex flex-col gap-5 w-full">
        <SectionHeader title="Other Settings" />
        {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
          <div className="flex">
            <div className="w-2/5">
              <EverTg.Text className="text-ever-base-content-mid">
                Modification Range for unit price (+/-)
              </EverTg.Text>
            </div>
            <div className="w-3/5">
              {inActivePricePoint?.modification_treshold ===
              pricePoint?.modification_treshold ? (
                <EverTg.SubHeading4>
                  {getFormattedAmount(pricePoint.modification_treshold)}
                </EverTg.SubHeading4>
              ) : (
                <div className="flex gap-2">
                  <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                    {getFormattedAmount(pricePoint.modification_treshold)}
                  </EverTg.SubHeading4>
                  <EverTg.SubHeading4 className="text-ever-success-hover">
                    {getFormattedAmount(
                      inActivePricePoint.modification_treshold
                    )}
                  </EverTg.SubHeading4>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="flex">
          <div className="w-2/5">
            <EverTg.Text className="text-ever-base-content-mid">
              Modification Range for flat fee (+/-)
            </EverTg.Text>
          </div>
          <div className="w-3/5">
            {inActivePricePoint?.flat_modification_treshold ===
            pricePoint?.flat_modification_treshold ? (
              <EverTg.SubHeading4>
                {getFormattedAmount(pricePoint.flat_modification_treshold)}
              </EverTg.SubHeading4>
            ) : (
              <div className="flex gap-2">
                <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                  {getFormattedAmount(pricePoint.flat_modification_treshold)}
                </EverTg.SubHeading4>
                <EverTg.SubHeading4 className="text-ever-success-hover">
                  {getFormattedAmount(
                    inActivePricePoint.flat_modification_treshold
                  )}
                </EverTg.SubHeading4>
              </div>
            )}
          </div>
        </div>
        {pricePoint.price_model !== PRICE_POINT_PRICE_MODELS.FLAT_FEE && (
          <div className="flex w-full">
            <div className="w-2/5">
              <EverTg.Text className="text-ever-base-content-mid">
                Max Discount for Unit Price
              </EverTg.Text>
            </div>
            <div className="w-3/5">
              {inActivePricePoint?.max_discount === pricePoint?.max_discount ? (
                <EverTg.SubHeading4>
                  {getFormattedAmount(pricePoint.max_discount)}
                </EverTg.SubHeading4>
              ) : (
                <div className="flex gap-2">
                  <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                    {getFormattedAmount(pricePoint.max_discount)}
                  </EverTg.SubHeading4>
                  <EverTg.SubHeading4 className="text-ever-success-hover">
                    {getFormattedAmount(inActivePricePoint.max_discount)}
                  </EverTg.SubHeading4>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="flex w-full">
          <div className="w-2/5">
            <EverTg.Text className="text-ever-base-content-mid">
              Max Discount for Flat Fee
            </EverTg.Text>
          </div>
          <div className="w-3/5">
            {inActivePricePoint?.flat_max_discount ===
            pricePoint?.flat_max_discount ? (
              <EverTg.SubHeading4>
                {getFormattedAmount(pricePoint.flat_max_discount)}
              </EverTg.SubHeading4>
            ) : (
              <div className="flex gap-2">
                <EverTg.SubHeading4 className="text-ever-error-hover line-through">
                  {getFormattedAmount(pricePoint.flat_max_discount)}
                </EverTg.SubHeading4>
                <EverTg.SubHeading4 className="text-ever-success-hover">
                  {getFormattedAmount(inActivePricePoint.flat_max_discount)}
                </EverTg.SubHeading4>
              </div>
            )}
          </div>
        </div>
      </div>
      {updatePricePoint && showScheduleModal && (
        <SchedulePricePointModal
          visible={showScheduleModal}
          setVisible={setShowScheduleModal}
          pricePoint={updatePricePoint}
          handleConfirm={(date) =>
            handleCreatePricePoint(date, inActivePricePoint)
          }
          dateKey="schedule_start_date"
          isDateUpdating={true}
          isConfirmEventLoading={isConfirmEventLoading}
        />
      )}
    </div>
  );
}
