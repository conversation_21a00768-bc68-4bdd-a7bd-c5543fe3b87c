import {
  DotsVerticalIcon,
  EditPencilAltIcon,
  FlipBackwardIcon,
  MinusCircleIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { format } from "date-fns";
import React, { useState } from "react";
import { useMutation } from "react-query";

import { EverButton, EverTg, message } from "~/v2/components";

import { useFetchApiWithAuth } from "../../../hooks";
import { PRICE_POINT_ACTIONS, hasDatePassed } from "../../utils";
import { SchedulePricePointModal } from "../SchedulePricePointModal";

const dropdownMenu = ({
  pricePoint,
  pricePointId,
  setIsHovered,
  onEditPricePoint,
  showScheduleModal,
  setShowScheduleModal,
  deactivatePricePointRequest,
  activatePricePointRequest,
}) => {
  const PRICE_POINTS_MENU_ITEMS = [
    {
      icon: EditPencilAltIcon,
      label: "Edit",
      action: PRICE_POINT_ACTIONS.EDIT,
      disabled: false,
      toBeRendered: !pricePoint.schedule_end_date,
    },
    {
      icon: MinusCircleIcon,
      label: "Deactivate",
      action: PRICE_POINT_ACTIONS.DEACTIVATE,
      disabled: pricePoint.schedule_end_date,
      toBeRendered: pricePoint.status !== "inactive",
      tooltipText: "Cannot disable price points with scheduled updates",
    },
    {
      icon: FlipBackwardIcon,
      label: "Activate",
      action: PRICE_POINT_ACTIONS.ACTIVATE,
      disabled:
        pricePoint.schedule_start_date &&
        !hasDatePassed(pricePoint.schedule_start_date),
      toBeRendered: pricePoint.status !== "active",
      tooltipText: "Activation already scheduled",
    },
  ];

  function handleMenuItemClick(item, pricePointId) {
    switch (item) {
      case PRICE_POINT_ACTIONS.EDIT: {
        onEditPricePoint(pricePointId);
        break;
      }
      default: {
        break;
      }
    }
  }

  function handleOnClose() {
    setShowScheduleModal(false);
  }

  function handleOnDeactivate(date) {
    deactivatePricePointRequest.mutate({
      payload: {
        pricepoint_id: pricePointId,
        schedule_end_date: format(date, "yyyy-MM-dd"),
      },
    });
    handleOnClose();
  }
  function handleOnActivate(date) {
    activatePricePointRequest.mutate({
      payload: {
        pricepoint_id: pricePointId,
        schedule_start_date: format(date, "yyyy-MM-dd"),
      },
    });
    handleOnClose();
  }

  return (
    <Menu className="flex items-start !p-2">
      {PRICE_POINTS_MENU_ITEMS.map((item, key) => {
        if (!item.toBeRendered) return null;
        return (
          <React.Fragment key={key}>
            <Menu.Item className="!px-0 w-full hover:!bg-transparent">
              <EverButton
                type="text"
                color="base"
                size="small"
                className="w-full justify-start"
                prependIcon={
                  <item.icon
                    className={
                      item.disabled
                        ? "text-ever-base-content-low"
                        : "text-ever-base-content-mid"
                    }
                  />
                }
                onClick={() => {
                  setIsHovered(false);
                  item.action === PRICE_POINT_ACTIONS.DEACTIVATE ||
                  item.action === PRICE_POINT_ACTIONS.ACTIVATE
                    ? setShowScheduleModal(true)
                    : handleMenuItemClick(item.action, pricePointId);
                }}
                disabled={item.disabled}
                tooltipTitle={item.disabled ? item.tooltipText : ""}
              >
                <EverTg.Caption className="font-normal">
                  {item.label}
                </EverTg.Caption>
              </EverButton>
            </Menu.Item>
            {(item.action === PRICE_POINT_ACTIONS.DEACTIVATE ||
              item.action === PRICE_POINT_ACTIONS.ACTIVATE) && (
              <SchedulePricePointModal
                visible={showScheduleModal}
                setVisible={setShowScheduleModal}
                pricePoint={pricePoint}
                handleConfirm={
                  item.action === PRICE_POINT_ACTIONS.DEACTIVATE
                    ? handleOnDeactivate
                    : handleOnActivate
                }
                isConfirmEventLoading={
                  item.action === PRICE_POINT_ACTIONS.DEACTIVATE
                    ? deactivatePricePointRequest.isLoading
                    : activatePricePointRequest.isLoading
                }
              />
            )}
          </React.Fragment>
        );
      })}
    </Menu>
  );
};

export function MenuButton({
  pricePoint,
  setIsHovered,
  onEditPricePoint,
  refetchPricePoints,
}) {
  const { fetchData } = useFetchApiWithAuth();

  const [showScheduleModal, setShowScheduleModal] = useState(false);

  const deactivatePricePointRequest = useMutation(
    ({ payload }) =>
      fetchData(`/ninja/cpq/pricepoint/deactivate`, "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (res) => {
        message.success(res?.message || "Product deactivated successfully");
        refetchPricePoints();
      },
    }
  );

  const activatePricePointRequest = useMutation(
    ({ payload }) =>
      fetchData(`/ninja/cpq/pricepoint/activate`, "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (res) => {
        message.success(res?.message || "Product activated successfully");
        refetchPricePoints();
      },
    }
  );

  return (
    <Dropdown
      trigger={["click"]}
      overlay={dropdownMenu({
        pricePoint,
        pricePointId: pricePoint.pricepoint_id,
        setIsHovered,
        onEditPricePoint,
        showScheduleModal,
        setShowScheduleModal,
        deactivatePricePointRequest,
        activatePricePointRequest,
      })}
      onVisibleChange={(visible) => setIsHovered(visible)}
    >
      <EverButton.Icon
        size="small"
        type="text"
        color="base"
        className="!w-8 !h-8 !p-1.5"
        icon={
          <DotsVerticalIcon className="text-ever-base-content-mid !w-4 !h-4 shrink-0" />
        }
      />
    </Dropdown>
  );
}
