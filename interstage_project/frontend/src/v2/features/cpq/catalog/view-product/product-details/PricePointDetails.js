import { PlusSquareIcon } from "@everstage/evericons/outlined";
import { CalendarCheckIcon, MinusCircleIcon } from "@everstage/evericons/solid";
import { format } from "date-fns";
import React, { useEffect, useMemo, useState } from "react";
import { useMutation } from "react-query";
import { twMerge } from "tailwind-merge";

import {
  EverButton,
  // EverButtonGroup,
  EverNumberBadge,
  EverTg,
  EverTooltip,
  message,
} from "~/v2/components";

import { MenuButton } from "./MenuButton";
import { TruncatedText } from "../../../components";
import { useFetchApiWithAuth } from "../../../hooks";
import { ProductTable } from "../../ProductTable";
import {
  // FILTER_PRICE_POINTS_BUTTONS,
  getDisplayNameforKeys,
  getFormattedAmount,
  PRICE_POINT_PRICE_MODELS,
  PRICE_POINT_STATE_MAP,
} from "../../utils";
import { SchedulePricePointModal } from "../SchedulePricePointModal";

// const initialCounts = {
//   [PRICE_POINT_PRICE_MODELS.FLAT_FEE]: 0,
//   [PRICE_POINT_PRICE_MODELS.VOLUME]: 0,
//   [PRICE_POINT_PRICE_MODELS.TIERED]: 0,
//   [PRICE_POINT_PRICE_MODELS.PER_UNIT]: 0,
// };

function getMaxMinValues(tierData) {
  const max = Math.max(...tierData.map((tier) => tier.list_price));
  const min = Math.min(...tierData.map((tier) => tier.list_price));

  return `${getFormattedAmount(min)} to ${getFormattedAmount(max)}`;
}

function getScheduleUpdateText(type, date) {
  switch (type) {
    case PRICE_POINT_STATE_MAP.SCHEDULED_UPDATE: {
      return `Scheduled update to become active from ${
        date ? format(new Date(date), "MMM d, yyyy") : "Invalid Date"
      }`;
    }
    case PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION: {
      return `Scheduled to go active from ${
        date ? format(new Date(date), "MMM d, yyyy") : "Invalid Date"
      }`;
    }
    case PRICE_POINT_STATE_MAP.SCHEDULED_DEACTIVATION: {
      return `Scheduled to go inactive from ${
        date ? format(new Date(date), "MMM d, yyyy") : "Invalid Date"
      }`;
    }
    // No default
  }
  return "";
}

export function PricePointDetails({
  handleAddPricePoint,
  handleEditPricePoint,
  handleViewEditedPricePoint,
  allPricePoints = [],
  refetchPricePoints,
  currFilter,
  // setCurrFilter,
}) {
  const { fetchData } = useFetchApiWithAuth();

  // const [priceModelCounts, setPriceModelCounts] = useState(initialCounts);
  const [totalPricePointsCount, setTotalPricePointsCount] = useState(0);
  const [currentPricePoints, setCurrentPricePoints] = useState([]);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [updatePricePoint, setUpdatePricePoint] = useState(null);

  // const defIndex = FILTER_PRICE_POINTS_BUTTONS.findIndex(
  //   (button) => button.value === currFilter
  // );

  const updateScheduleRequest = useMutation(
    ({ operation, payload }) =>
      fetchData(`/ninja/cpq/pricepoint/${operation}`, "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (res) => {
        message.success(res?.message || "Product deactivated successfully");
        refetchPricePoints();
        setShowScheduleModal(false);
        setUpdatePricePoint(null);
      },
    }
  );

  function handleOpenScheduleModal(pricePoint) {
    setUpdatePricePoint(pricePoint);
    setShowScheduleModal(true);
  }

  function handleUpdateScheduleDate(date) {
    const operation =
      updatePricePoint.pricePointState ===
      PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION
        ? "activate"
        : "deactivate";
    const payload = {
      pricepoint_id: updatePricePoint.pricepoint_id,
    };
    if (operation === "activate") {
      payload.schedule_start_date = format(date, "yyyy-MM-dd");
    } else {
      payload.schedule_end_date = format(date, "yyyy-MM-dd");
    }
    updateScheduleRequest.mutate({
      operation,
      payload,
    });
  }

  async function handleDeleteScheduleDate() {
    await updateScheduleRequest.mutate({
      operation:
        updatePricePoint.pricePointState ===
        PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION
          ? "activate"
          : "deactivate",
      payload: {
        pricepoint_id: updatePricePoint.pricepoint_id,
        delete_schedule: true,
      },
    });
  }

  const rowClassRules = useMemo(
    () => ({
      "more-menu-button-active": (params) => params.node.data.isHovered,
      "bg-ever-base-50 !text-ever-base-content-low": (params) =>
        params.node.data.status === "inactive",
    }),
    []
  );

  const columnDefs = [
    {
      field: "name",
      headerName: "Price Point Name",
      minWidth: 250,
      cellRenderer: (params) => {
        const date =
          params?.data?.pricePointState ===
          PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION
            ? params?.data?.schedule_start_date
            : params?.data?.schedule_end_date;
        const pricePointState = params?.data?.pricePointState;
        return (
          <div className="flex gap-2 items-center w-full">
            <TruncatedText text={params?.data?.name} className="max-w-[80%]">
              <EverTg.SubHeading4
                className={twMerge(
                  "text-ever-base-content",
                  params?.data?.status === "inactive" &&
                    "!text-ever-base-content-low"
                )}
              >
                {params?.data?.name}
              </EverTg.SubHeading4>
            </TruncatedText>
            {(pricePointState === PRICE_POINT_STATE_MAP.SCHEDULED_UPDATE ||
              pricePointState === PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION ||
              pricePointState ===
                PRICE_POINT_STATE_MAP.SCHEDULED_DEACTIVATION) && (
              <EverTooltip title={getScheduleUpdateText(pricePointState, date)}>
                <div
                  onClick={() => {
                    pricePointState === PRICE_POINT_STATE_MAP.SCHEDULED_UPDATE
                      ? handleViewEditedPricePoint(params?.data?.pricepoint_id)
                      : handleOpenScheduleModal(params?.data);
                  }}
                  className="flex gap-2 w-6 h-6 items-center justify-center rounded-sm bg-ever-info-lite border border-solid border-ever-primary-200 cursor-pointer"
                >
                  <CalendarCheckIcon className="text-ever-primary w-4 h-4" />
                </div>
              </EverTooltip>
            )}
            {params?.data?.status === "inactive" &&
              pricePointState === PRICE_POINT_STATE_MAP.INACTIVE && (
                <EverTooltip title="Deactivated">
                  <MinusCircleIcon className="text-ever-base-content-low w-4 h-4" />
                </EverTooltip>
              )}
          </div>
        );
      },
    },
    {
      field: "price_model",
      headerName: "Price Model",
      width: 100,
      minWidth: 100,
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {getDisplayNameforKeys(params?.data?.price_model)}
          </EverTg.Text>
        );
      },
    },
    {
      field: "billing_frequency",
      headerName: "Billing Frequency",
      minWidth: 150,
      width: 150,
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {getDisplayNameforKeys(params?.data?.billing_frequency)}
          </EverTg.Text>
        );
      },
    },
    {
      field: "currency",
      headerName: "Currency",
      width: 80,
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {params?.data?.currency
              ? params?.data?.currency?.toUpperCase()
              : "USD"}
          </EverTg.Text>
        );
      },
    },
    {
      field: "list_price",
      headerName: "List unit price",
      cellRenderer: (params) => {
        const modelType = params?.data?.price_model;
        const text =
          modelType === PRICE_POINT_PRICE_MODELS.PER_UNIT
            ? getFormattedAmount(params?.data?.list_price)
            : modelType === PRICE_POINT_PRICE_MODELS.VOLUME ||
              modelType === PRICE_POINT_PRICE_MODELS.TIERED
            ? `${getMaxMinValues(params?.data?.tier_data)} + flat fee`
            : getFormattedAmount(params?.data?.list_price);

        return (
          <TruncatedText text={text}>
            <EverTg.SubHeading4
              className={twMerge(
                "text-ever-base-content",
                params?.data?.status === "inactive" &&
                  "!text-ever-base-content-low"
              )}
            >
              {text}
            </EverTg.SubHeading4>
          </TruncatedText>
        );
      },
    },
    {
      field: "modification_treshold",
      headerName: "List Price Override",
      maxWidth: 180,
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {params?.data?.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
              ? params?.data?.flat_modification_treshold === 0
                ? "Not allowed"
                : "Allowed"
              : params?.data?.modification_treshold === 0
              ? "Not allowed"
              : "Allowed"}
          </EverTg.Text>
        );
      },
    },
    {
      field: "otherSettings",
      headerName: "Max discount",
      cellRenderer: (params) => {
        const numerator =
          params?.data?.price_model === PRICE_POINT_PRICE_MODELS.FLAT_FEE
            ? params?.data?.flat_max_discount
            : params?.data?.max_discount;
        const percentage = (numerator / params?.data?.list_price) * 100;
        return (
          <EverTg.Text
            className={twMerge(
              "text-ever-base-content",
              params?.data?.status === "inactive" &&
                "!text-ever-base-content-low"
            )}
          >
            {params.data.price_model === "flatfee"
              ? params?.data?.flat_max_discount
                ? `${getFormattedAmount(params?.data?.flat_max_discount)} (${
                    percentage % 1 === 0 ? percentage : percentage.toFixed(2)
                  }%)`
                : "No restriction"
              : params?.data?.max_discount
              ? `${getFormattedAmount(params?.data?.max_discount)} (${
                  percentage % 1 === 0 ? percentage : percentage.toFixed(2)
                }%)`
              : "No restriction"}
          </EverTg.Text>
        );
      },
    },
    {
      field: "moreButton",
      headerName: "ACTIONS",
      cellClass: "flex justify-end",
      headerClass: "text-ever-base-content-mid",
      type: "rightAligned",
      maxWidth: 120,
      pinned: "right",
      lockPinned: true,
      cellRenderer: MenuButton,
      cellRendererParams: (params) => ({
        pricePoint: params?.data,
        onEditPricePoint: handleEditPricePoint,
        setIsHovered: (visible) => {
          params.api.applyTransaction({
            update: [{ ...params.data, isHovered: visible }],
          });
        },
        refetchPricePoints: refetchPricePoints,
      }),
    },
  ];

  // function handleFilterChange(value) {
  //   setCurrFilter(value);
  // }

  useEffect(() => {
    // setCurrentPricePoints(
    //   allPricePoints.filter(
    //     (pricePoint) => pricePoint.price_model === currFilter
    //   )
    // );
    setCurrentPricePoints(allPricePoints);
  }, [currFilter, allPricePoints]);

  // useEffect(() => {
  //   const counts = allPricePoints?.reduce((acc, pricePoint) => {
  //     acc[pricePoint.price_model] = (acc[pricePoint.price_model] || 0) + 1;
  //     return acc;
  //   }, cloneDeep(initialCounts));

  //   setPriceModelCounts(counts);
  // }, [allPricePoints]);

  useEffect(() => {
    setTotalPricePointsCount(allPricePoints.length);
  }, [allPricePoints]);

  return (
    <div className="flex flex-col gap-2 h-full">
      <div className="flex justify-between pr-1">
        <div className="flex gap-2 items-center">
          <EverTg.Heading2>Price Points</EverTg.Heading2>
          <EverNumberBadge count={totalPricePointsCount} />
        </div>
        {/* <EverButtonGroup
          className="bg-ever-base-200"
          activeBtnType="text"
          activeBtnColor="primary"
          defActiveBtnIndex={defIndex}
          size="small"
        >
          {FILTER_PRICE_POINTS_BUTTONS.map((button) => (
            <EverButton
              key={button.value}
              className="!pr-2 !h-7"
              onClick={() => {
                handleFilterChange(button.value);
              }}
            >
              {button.label}
              <EverNumberBadge
                addPrecedingZero={false}
                count={priceModelCounts[button.value]}
              />
            </EverButton>
          ))}
        </EverButtonGroup> */}
        <EverButton
          type="ghost"
          size="small"
          color="primary"
          prependIcon={<PlusSquareIcon />}
          onClick={handleAddPricePoint}
        >
          Add Price Point
        </EverButton>
      </div>
      <div className="w-full h-full">
        <ProductTable
          rowData={currentPricePoints}
          columnDefs={columnDefs}
          getRowId={(params) => params.data.pricepoint_id}
          rowClassRules={rowClassRules}
          autoSizeStrategy="autoSizeAll"
        />
      </div>
      {updatePricePoint && showScheduleModal && (
        <SchedulePricePointModal
          visible={showScheduleModal}
          setVisible={setShowScheduleModal}
          pricePoint={updatePricePoint}
          handleConfirm={handleUpdateScheduleDate}
          dateKey={
            updatePricePoint?.pricePointState ===
            PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION
              ? "schedule_start_date"
              : "schedule_end_date"
          }
          isDateUpdating={true}
          allowDelete={true}
          handleDelete={handleDeleteScheduleDate}
          isConfirmEventLoading={updateScheduleRequest.isLoading}
        />
      )}
    </div>
  );
}
