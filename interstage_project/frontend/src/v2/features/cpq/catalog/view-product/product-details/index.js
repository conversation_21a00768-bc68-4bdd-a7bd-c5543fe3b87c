import { PlusSquareIcon } from "@everstage/evericons/outlined";
import React, { useState } from "react";
import { useQuery } from "react-query";

import { EverButton, EverTg, message } from "~/v2/components";
import { emptyBg, droppingCoins } from "~/v2/images";

import { PricePointDetails } from "./PricePointDetails";
import { AgGridLoaderTable } from "../../../components/ag-grid/AgGridLoaderTable";
import { useFetchApiWithAuth } from "../../../hooks";
import {
  FILTER_PRICE_POINTS_BUTTONS,
  hasDatePassed,
  PRICE_POINT_STATE_MAP,
  PRICE_POINT_TABLE_SKELETON_COLUMN_DEFS,
} from "../../utils";
import { AddPricePointDrawer } from "../add-price-point-drawer";

function getPPState(startDate, endDate, hasScheduledUpdate, status) {
  if (hasScheduledUpdate) {
    return PRICE_POINT_STATE_MAP.SCHEDULED_UPDATE;
  }
  if (hasDatePassed(startDate) && !endDate) {
    return PRICE_POINT_STATE_MAP.ACTIVE;
  }
  if (hasDatePassed(startDate) && hasDatePassed(endDate)) {
    return PRICE_POINT_STATE_MAP.INACTIVE;
  }
  if (
    status === "inactive" &&
    startDate &&
    !hasDatePassed(startDate) &&
    (!endDate || hasDatePassed(endDate))
  ) {
    return PRICE_POINT_STATE_MAP.SCHEDULED_ACTIVATION;
  }
  if (hasDatePassed(startDate) && endDate && !hasDatePassed(endDate)) {
    return PRICE_POINT_STATE_MAP.SCHEDULED_DEACTIVATION;
  }
  if (status === "inactive" && !startDate && !endDate) {
    return PRICE_POINT_STATE_MAP.INACTIVE;
  }
}

export function PricePointsView({ productId, setProductHasPricepoints }) {
  const { fetchData } = useFetchApiWithAuth();

  const [addPricePointDrawerVisible, setAddPricePointDrawerVisible] =
    useState(false);
  const [showEditedPricePoint, setShowEditedPricePoint] = useState(false);
  const [editPricePointId, setEditPricePointId] = useState(null);
  const [allPricePoints, setAllPricePoints] = useState(null);
  const [currFilter, setCurrFilter] = useState(
    FILTER_PRICE_POINTS_BUTTONS[0].value
  );

  const { refetch, isFetching } = useQuery(
    ["getPricePoints"],
    () => {
      return fetchData(`/ninja/cpq/pricepoint/pricepoints/${productId}`, "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      enabled: !!productId,
      onError: (error) => {
        allPricePoints === null && setAllPricePoints([]);
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        const pricePoints = data?.pricepoints?.map((pricePoint) => ({
          ...pricePoint,
          pricePointState: getPPState(
            pricePoint?.schedule_start_date,
            pricePoint?.schedule_end_date,
            pricePoint?.has_scheduled_update,
            pricePoint?.status
          ),
        }));
        setAllPricePoints(pricePoints);
        setProductHasPricepoints(pricePoints?.length > 0);
      },
    }
  );

  function handleAddPricePoint() {
    setAddPricePointDrawerVisible(true);
  }

  function handleViewEditedPricePoint(pricePointId) {
    setAddPricePointDrawerVisible(true);
    setEditPricePointId(pricePointId);
    setShowEditedPricePoint(true);
  }

  function handleEditPricePoint(pricePointId) {
    setEditPricePointId(pricePointId);
    handleAddPricePoint();
  }

  function handleCloseAddPricePointDrawer() {
    setAddPricePointDrawerVisible(false);
    setEditPricePointId(null);
    setShowEditedPricePoint(false);
  }

  return (
    <>
      {isFetching || allPricePoints === null ? (
        <AgGridLoaderTable
          columnDefs={PRICE_POINT_TABLE_SKELETON_COLUMN_DEFS}
          rowCount={5}
          agGridProps={{
            rowHeight: 40,
            headerHeight: 40,
          }}
        />
      ) : allPricePoints?.length === 0 ? (
        <NoProductPresent handleAddPricePoint={handleAddPricePoint} />
      ) : (
        <PricePointDetails
          handleAddPricePoint={handleAddPricePoint}
          handleEditPricePoint={handleEditPricePoint}
          handleViewEditedPricePoint={handleViewEditedPricePoint}
          allPricePoints={allPricePoints}
          refetchPricePoints={refetch}
          currFilter={currFilter}
          setCurrFilter={setCurrFilter}
        />
      )}
      {addPricePointDrawerVisible && (
        <AddPricePointDrawer
          visible={addPricePointDrawerVisible}
          setVisible={setAddPricePointDrawerVisible}
          isEdit={!!editPricePointId}
          handleClose={handleCloseAddPricePointDrawer}
          isViewingEditedPricePoint={showEditedPricePoint}
          refetchPricePoints={refetch}
          productId={productId}
          initialPricePoint={allPricePoints.find(
            (pricePoint) => pricePoint.pricepoint_id === editPricePointId
          )}
          allPricePoints={allPricePoints}
        />
      )}
    </>
  );
}

function NoProductPresent({ handleAddPricePoint }) {
  return (
    <div
      className="flex flex-col rounded-2xl items-center h-full bg-cover bg-top w-full"
      style={{ backgroundImage: `url(${emptyBg})` }}
    >
      <div className="flex flex-col items-center justify-center mt-32">
        <img src={droppingCoins} className="mb-8" />
        <div className="flex flex-col items-center gap-1.5 mb-6">
          <EverTg.Heading3>
            Next step: Set a price for this product
          </EverTg.Heading3>
          <EverTg.Description className="text-center">
            Add this product to a price book, set price and manage price
            variations.
          </EverTg.Description>
        </div>
        <div className="flex gap-6">
          <EverButton
            size="small"
            color="primary"
            prependIcon={<PlusSquareIcon />}
            onClick={handleAddPricePoint}
          >
            Add Price Point
          </EverButton>
        </div>
      </div>
    </div>
  );
}
