import { PlusCircleIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";

import { EverTg, EverButton } from "~/v2/components";
import { emptyBg } from "~/v2/images";

export function EmptyListScreen({
  title = "",
  image = null,
  description = "",
  buttonText = "",
  buttonOnClick = () => {},
  isLoading = false,
}) {
  return (
    <div className="w-full h-full pb-6">
      <div className="relative h-full flex flex-col justify-center items-center">
        <img
          src={emptyBg}
          className="w-full h-full object-top object-cover absolute top-0 left-0 rounded-xl"
        />
        <div className="flex flex-col items-center p-6 z-2">
          {!isEmpty(image) && <img src={image} className="mb-8" />}
          <div className="flex flex-col gap-2 text-center">
            {!isEmpty(title) && (
              <EverTg.Heading2 className="text-center">{title}</EverTg.Heading2>
            )}
            {!isEmpty(description) && (
              <EverTg.Text className="text-ever-base-content-mid">
                {description}
              </EverTg.Text>
            )}
          </div>
          {!isEmpty(buttonText) && (
            <EverButton
              size="small"
              prependIcon={<PlusCircleIcon className="w-4 h-4" />}
              className="mt-6"
              onClick={buttonOnClick}
              loading={isLoading}
            >
              {buttonText}
            </EverButton>
          )}
        </div>
      </div>
    </div>
  );
}
