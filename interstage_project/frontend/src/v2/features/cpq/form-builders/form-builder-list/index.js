/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import { PlusCircleIcon, SearchIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useEffect, useState, useCallback, useRef } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { EverButton, EverInput, EverLoader, EverTg } from "~/v2/components";
import { EmptyListScreen } from "~/v2/features/cpq/components";
import { useDebouncedSearch, useFetchApi } from "~/v2/features/cpq/hooks";
import { createQuotes } from "~/v2/images";

import { API_ENDPOINTS, API } from "./constants";
import CreateFormBuilderModal from "./CreateFormBuilderModal";
import FormBuilderCard from "./FormBuilderCard";

const FormBuilderList = () => {
  const LIST_PAGE_SIZE = useRef(15);
  const initialLoading = useRef(true);
  const canFetchMore = useRef(true);
  const pageNumber = useRef(1);
  const isScrollLoading = useRef(false);
  const scrollContainerRef = useRef(null);

  const [formBuilderList, setFormBuilderList] = useState([]);
  const [isCreateFormModalOpen, setIsCreateFormModalOpen] = useState(false);
  const [isContentScrolled, setIsContentScrolled] = useState(false);

  const navigate = useNavigate();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const { mutate: getFormBuildersMutate, isLoading: getFormBuildersLoading } =
    useMutation((body) => fetchEndpoint(API.GET_ALL_FORM_BUILDERS, { body }), {
      onSuccess: (data, variables) => {
        const formBuilders = data?.form_builders ?? [];
        initialLoading.current = false;
        canFetchMore.current = formBuilders.length === variables.limit_value;
        pageNumber.current = variables.page_number;
        isScrollLoading.current = false;
        setFormBuilderList((prev) => {
          if (variables.page_number === 1) {
            return formBuilders;
          }
          return [...prev, ...formBuilders];
        });
      },
    });

  const createFormBuilder = useMutation(
    (body) => fetchEndpoint(API.CREATE_FORM_BUILDER, { body }),
    {
      onSuccess: (data) => {
        setIsCreateFormModalOpen(false);
        navigate(`/cpq/settings/quote-forms/${data.form_builder_id}`);
      },
    }
  );

  const updateFormBuilder = useMutation(
    (body) => fetchEndpoint(API.UPDATE_FORM_BUILDER, { body }),
    {
      onSuccess: (_, variables) => {
        setFormBuilderList((prev) => {
          return prev.map((formBuilder) => {
            if (formBuilder.form_builder_id === variables.form_builder_id) {
              return {
                ...formBuilder,
                status: variables.updated_fields.status,
              };
            }
            return formBuilder;
          });
        });
      },
    }
  );

  const deleteFormBuilder = useMutation(
    (body) => fetchEndpoint(API.DELETE_FORM_BUILDER, { body }),
    {
      onSuccess: () => {
        getFormBuilderList({ searchTerm: searchQuery });
      },
    }
  );

  const getFormBuilderList = useCallback(
    ({ searchTerm, pageNumber = 1 }) => {
      getFormBuildersMutate({
        search_term: searchTerm,
        limit_value: LIST_PAGE_SIZE.current,
        page_number: pageNumber,
      });
    },
    [getFormBuildersMutate]
  );

  const handleScroll = (event) => {
    const scrollPosition = event.target.scrollTop;
    console.log(scrollPosition);
    const reachedFetchMorePos =
      event.target.scrollHeight - scrollPosition === event.target.clientHeight;
    const fetchMoreEnabled =
      !getFormBuildersLoading && canFetchMore.current && reachedFetchMorePos;

    setIsContentScrolled(scrollPosition > 8);
    if (scrollPosition > 0 && fetchMoreEnabled) {
      isScrollLoading.current = true;
      getFormBuilderList({
        searchTerm: searchQuery,
        pageNumber: pageNumber.current + 1,
      });
      setTimeout(() => {
        scrollContainerRef.current.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: "smooth",
        });
      }, 10);
    }
  };

  const {
    searchState: { userInput, searchQuery },
    onSearch,
  } = useDebouncedSearch(getFormBuilderList);

  useEffect(() => {
    getFormBuilderList({ searchTerm: null });
  }, [getFormBuilderList]);

  const isLoading =
    getFormBuildersLoading ||
    updateFormBuilder.isLoading ||
    createFormBuilder.isLoading ||
    deleteFormBuilder.isLoading;
  const showEmptyScreen =
    !isLoading && isEmpty(searchQuery) && formBuilderList.length === 0;
  const disableControls = isLoading || isScrollLoading.current;
  const anyLoaderActive = isLoading && !isScrollLoading.current;

  const renderContent = () => {
    if (initialLoading.current) {
      return (
        <EverLoader
          indicatorType="spinner"
          className="flex h-full"
          wrapperClassName="z-20"
          spinning
        />
      );
    }

    if (showEmptyScreen) {
      return (
        <div className="h-full px-6">
          <EmptyListScreen
            title="Forms helps you quote.."
            description="Create and configure new form with customised sections and fields for seamless quoting."
            image={createQuotes}
            buttonText="Create New Form"
            buttonOnClick={() => setIsCreateFormModalOpen(true)}
          />
        </div>
      );
    }

    return (
      <>
        <div
          className={twMerge(
            "flex items-center justify-between px-6 pb-4 z-10",
            isContentScrolled && "shadow-[0_16px_10px_-16px_#a5a5a5]"
          )}
        >
          <EverInput.Search
            allowClear
            className="w-80 [&_.ant-input-affix-wrapper]:!px-3"
            placeholder="Search"
            value={userInput}
            onChange={onSearch}
            size="small"
            disabled={disableControls}
            {...(isEmpty(userInput)
              ? {
                  enterButton: (
                    <SearchIcon className="w-4 h-4 text-ever-base-content-low" />
                  ),
                }
              : {
                  enterButton: null,
                })}
          />
          <EverButton
            size="small"
            prependIcon={<PlusCircleIcon className="w-4 h-4" />}
            onClick={() => setIsCreateFormModalOpen(true)}
            disabled={disableControls}
          >
            Create New Form
          </EverButton>
        </div>
        <EverLoader
          indicatorType="spinner"
          className="flex flex-col overflow-auto"
          wrapperClassName="z-20"
          spinning={anyLoaderActive}
        >
          {!isEmpty(searchQuery) && formBuilderList.length === 0 ? (
            <div className="flex items-center justify-center h-full p-6">
              <EverTg.Heading2 className="text-ever-base-content-low">
                No quote forms found
              </EverTg.Heading2>
            </div>
          ) : (
            <div
              ref={scrollContainerRef}
              className="flex flex-col gap-4 h-full px-6 py-2 mb-4 overflow-auto"
              onScroll={handleScroll}
            >
              {formBuilderList.map((formBuilder) => (
                <FormBuilderCard
                  key={formBuilder.form_builder_id}
                  formBuilder={formBuilder}
                  onClone={createFormBuilder.mutate}
                  onUpdate={updateFormBuilder.mutate}
                  onDelete={deleteFormBuilder.mutate}
                  isLoading={
                    deleteFormBuilder.isLoading || getFormBuildersLoading
                  }
                />
              ))}
              {isScrollLoading.current && getFormBuildersLoading && (
                <div className="flex items-center justify-center h-full">
                  <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-10 [&>svg]:!h-10" />
                </div>
              )}
            </div>
          )}
        </EverLoader>
      </>
    );
  };

  return (
    <div className="flex flex-col h-full py-1">
      <CreateFormBuilderModal
        visible={isCreateFormModalOpen}
        onClose={() => setIsCreateFormModalOpen(false)}
        onSubmit={createFormBuilder.mutate}
        loading={createFormBuilder.isLoading}
      />
      <div className="flex flex-col h-full">{renderContent()}</div>
    </div>
  );
};

export default FormBuilderList;
