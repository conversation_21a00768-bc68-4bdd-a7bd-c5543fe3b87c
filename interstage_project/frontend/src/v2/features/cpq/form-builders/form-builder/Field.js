import {
  DotsVerticalIcon,
  Trash03Icon,
  EyeOffIcon,
  AlertCircleIcon,
} from "@everstage/evericons/outlined";
import { FlashIcon } from "@everstage/evericons/solid";
import { intersection, isEmpty } from "lodash";
import React, { useState, useEffect, useMemo } from "react";
import { Draggable } from "react-beautiful-dnd";
import { twMerge } from "tailwind-merge";

import {
  EverTg,
  IconButton,
  EverButton,
  EverDrawer,
  EverTooltip,
  message,
} from "~/v2/components";
import { RemoveConfirmationModal } from "~/v2/features/cpq/components";
import { FIELD_TYPE, FIELD_TYPE_LABELS } from "~/v2/features/cpq/constants";
import {
  FIELD_PROPERTIES_BY_TYPE,
  FIELD_SECTION_TYPES,
  FIELD_LABELS,
} from "~/v2/features/cpq/form-builders/form-builder/constants";
import { FIELD_COMPONENTS } from "~/v2/features/cpq/form-builders/form-builder/FieldComponents";

export default function Field({
  section,
  fieldId,
  index,
  updateFields,
  sourceFields,
  defaultFields,
  duplicateFieldIds,
}) {
  const [isHovering, setIsHovering] = useState(false);
  const [isPropertiesDrawerOpen, setIsPropertiesDrawerOpen] = useState(false);
  const [fieldToRemove, setFieldToRemove] = useState(null);

  const field = useMemo(() => {
    return section.fields[fieldId];
  }, [section.fields, fieldId]);

  const isDefault = useMemo(() => {
    return defaultFields.includes(field.id);
  }, [defaultFields, field.id]);

  const hasRules = useMemo(() => {
    return sourceFields.includes(field.id);
  }, [sourceFields, field.id]);

  return (
    <React.Fragment key={field.id}>
      <Draggable draggableId={field.id} index={index} isDragDisabled={false}>
        {(provided) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={twMerge(
              "flex items-center w-full h-8 border border-ever-base-300 rounded-md py-1 px-2 mt-2",
              index === 0 && "mt-0",
              isDefault
                ? "bg-ever-base-100"
                : isHovering
                ? "bg-ever-base-50"
                : "bg-ever-base"
            )}
            onMouseEnter={() => {
              setIsHovering(true);
            }}
            onMouseLeave={() => {
              setIsHovering(false);
            }}
            onClick={() => setIsPropertiesDrawerOpen(true)}
          >
            <div className="flex items-center gap-3 w-full">
              <div className="flex items-center justify-center max-w-[10px] max-h-[10px] overflow-hidden">
                <DotsVerticalIcon className="w-1 h-3 mt-1.5 group-hover:block text-ever-base-content-mid" />
                <DotsVerticalIcon className="w-1 h-3 mt-1.5 group-hover:block text-ever-base-content-mid ml-0.5" />
              </div>
              <div className="flex items-center gap-1.5 w-48">
                <EverTg.Text className="font-medium">{field.label}</EverTg.Text>
                {duplicateFieldIds.includes(fieldId) && (
                  <EverTooltip title="Field name already exists">
                    <AlertCircleIcon className="w-4 h-4 text-ever-error" />
                  </EverTooltip>
                )}
                {hasRules && (
                  <FlashIcon className="w-4 h-4 text-ever-chartColors-1" />
                )}
                {field.properties.isHidden && (
                  <EyeOffIcon className="w-4 h-4 text-ever-base-content-low" />
                )}
              </div>
              <div className="flex items-center gap-2 text-ever-base-content-low">
                <EverTg.Caption.Medium>
                  {FIELD_TYPE_LABELS[field.field_type]}
                </EverTg.Caption.Medium>
                {isDefault && (
                  <>
                    <div className="text-ever-base-400 !text-lg">|</div>
                    <EverTg.Caption.Medium>Default</EverTg.Caption.Medium>
                  </>
                )}
                {field.properties.isHidden && (
                  <>
                    <div className="text-ever-base-400 !text-lg">|</div>
                    <EverTg.Caption.Medium>Hidden</EverTg.Caption.Medium>
                  </>
                )}
              </div>
              {isHovering && (
                <EverTooltip
                  title={
                    isDefault
                      ? "This is a default field. You can't delete this field"
                      : ""
                  }
                  placement="top"
                  overlayClassName="!text-sm"
                >
                  <IconButton
                    type="ghost"
                    color="base"
                    className={twMerge(
                      "!w-6 !h-6 !p-1 ml-auto rounded-sm !bg-ever-base",
                      isDefault && "!opacity-50 cursor-not-allowed"
                    )}
                    icon={
                      <Trash03Icon className="ml-auto text-ever-error-hover" />
                    }
                    onClick={(event) => {
                      if (isDefault) {
                        return;
                      }
                      event.stopPropagation();
                      setFieldToRemove(field);
                    }}
                  />
                </EverTooltip>
              )}
            </div>
            {provided.placeholder}
          </div>
        )}
      </Draggable>
      <FieldPropertiesDrawer
        visible={isPropertiesDrawerOpen}
        field={field}
        updateField={(field) => {
          updateFields({
            ...section.fields,
            [fieldId]: field,
          });
        }}
        onClose={() => setIsPropertiesDrawerOpen(false)}
      />
      <RemoveConfirmationModal
        visible={!!fieldToRemove}
        title={`Remove ${fieldToRemove?.label}`}
        subtitle="This action cannot be undone"
        onConfirm={() => {
          const newFields = { ...section.fields };
          delete newFields[fieldToRemove.id];
          const newFieldOrder = [...section.field_order];
          newFieldOrder.splice(index, 1);
          updateFields(newFields, newFieldOrder);
        }}
        onCancel={() => setFieldToRemove(null)}
      />
    </React.Fragment>
  );
}

const FieldPropertiesDrawer = ({ field, visible, onClose, updateField }) => {
  const [fieldData, setFieldData] = useState(field);

  useEffect(() => {
    if (visible) {
      setFieldData(field);
    }
  }, [visible, field]);

  const validators = useMemo(
    () => [
      {
        test: () => isEmpty(fieldData.label),
        message: "Field name is required.",
      },
      {
        test: () => isEmpty(fieldData.data_type),
        message: "Data type is required.",
      },
      {
        test: () =>
          fieldData.properties.min != null &&
          fieldData.properties.max != null &&
          fieldData.properties.max < fieldData.properties.min,
        message: "Max must be ≥ Min.",
      },
      {
        test: () =>
          fieldData.properties.min_length != null &&
          fieldData.properties.max_length != null &&
          fieldData.properties.max_length < fieldData.properties.min_length,
        message: "Max length must be ≥ Min length.",
      },
      {
        test: () =>
          [FIELD_TYPE.MULTI_SELECT, FIELD_TYPE.SINGLE_SELECT].includes(
            fieldData.field_type
          ) &&
          (fieldData.options ?? []).length > 1 &&
          new Set(fieldData.options.map((o) => o.value)).size !==
            fieldData.options.length,
        message: "Select values must be unique.",
      },
      {
        test: () =>
          fieldData.field_type === FIELD_TYPE.LOOKUP &&
          (isEmpty(fieldData.options_lookup_spec?.databook_id) ||
            isEmpty(fieldData.options_lookup_spec?.datasheet_id) ||
            isEmpty(fieldData.options_lookup_spec?.column)),
        message: "Lookup spec (databook, datasheet, column) is required.",
      },
    ],
    [fieldData]
  );

  const handleSave = () => {
    const errorMessages = validators
      .filter((v) => v.test())
      .map((v) => v.message);

    if (errorMessages.length) {
      message.error(errorMessages[0]);
      return;
    }

    if (
      [FIELD_TYPE.SINGLE_SELECT, FIELD_TYPE.MULTI_SELECT].includes(
        fieldData.field_type
      )
    ) {
      fieldData.options = fieldData.options
        .map((opt) => ({
          label: opt.value.trim(),
          value: opt.value.trim(),
        }))
        .filter((opt) => opt.value !== "");
    }

    updateField(fieldData);
    onClose();
  };

  return (
    <EverDrawer
      placement="right"
      visible={visible}
      height="100%"
      width={440}
      onClose={onClose}
      destroyOnClose
      title={
        <div className="max-w-xl">
          <EverTg.Heading3>
            {FIELD_TYPE_LABELS[field.field_type]} Properties
          </EverTg.Heading3>
        </div>
      }
      footer={
        <div className="flex items-center justify-end">
          <EverButton onClick={handleSave}>Save</EverButton>
        </div>
      }
      bodyStyle={{ padding: 0 }}
      footerStyle={{
        padding: "12px 16px",
        boxShadow: "0px -4px 14px 0px rgba(0, 0, 0, 0.08)",
      }}
    >
      {visible && (
        <div className="flex flex-col gap-6 py-6">
          {FIELD_SECTION_TYPES.map((section) => {
            const propertiesByType =
              FIELD_PROPERTIES_BY_TYPE[fieldData.field_type] ?? [];
            const properties = intersection(propertiesByType, section.fields);

            if (isEmpty(properties)) {
              return null;
            }

            return (
              <div key={section.id} className="flex flex-col gap-6 px-6">
                <div className="flex items-center gap-2">
                  <EverTg.Text className="uppercase text-xs font-medium whitespace-nowrap">
                    {section.label}
                  </EverTg.Text>
                  <div className="h-px w-full bg-ever-base-300" />
                </div>
                <div className="flex flex-col gap-4">
                  {properties.map((propertyType) => {
                    const Component = FIELD_COMPONENTS[propertyType];
                    return (
                      <Component
                        key={propertyType}
                        propertyType={propertyType}
                        label={FIELD_LABELS[propertyType]}
                        field={fieldData}
                        onChange={setFieldData}
                      />
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </EverDrawer>
  );
};
