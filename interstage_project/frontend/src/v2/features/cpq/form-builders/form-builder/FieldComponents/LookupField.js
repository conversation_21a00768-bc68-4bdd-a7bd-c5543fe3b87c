/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import {
  PlusIcon,
  ChevronDownIcon,
  EditPencilAltIcon,
} from "@everstage/evericons/outlined";
import { CheckCircleIcon } from "@everstage/evericons/solid";
import { cloneDeep, debounce, isEmpty, get } from "lodash";
import React, { useRef, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useParams } from "react-router-dom";

import { DATATYPE, EXPRESSION_BOX_STATUS } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useDSVariableStore } from "~/GlobalStores/DSVariableStore";
import { autoSuggestionByot } from "~/Utils/expressionBoxUtils";
import {
  EverSelect,
  EverTg,
  <PERSON><PERSON>utton,
  EverModal,
  EverLabel,
} from "~/v2/components";
import {
  ExpressionBoxController,
  handleValidationWithApi as handleValidation,
} from "~/v2/components/expression-designer";
import { DATA_TYPE_MAP } from "~/v2/features/cpq/constants";
import { useFetchApi } from "~/v2/features/cpq/hooks";

import { API_ENDPOINTS, API } from "../constants";

const compareIfIntegerOrPercentage = (dataTypeNameA, dataTypeNameB) => {
  if (!dataTypeNameA || !dataTypeNameB) {
    return false;
  }
  let dataTypeAIsIntegerOrPercentage =
    dataTypeNameA.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameA.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  let dataTypeBIsIntegerOrPercentage =
    dataTypeNameB.toLowerCase() === DATATYPE.INTEGER.toLowerCase() ||
    dataTypeNameB.toLowerCase() === DATATYPE.PERCENTAGE.toLowerCase();
  return dataTypeAIsIntegerOrPercentage && dataTypeBIsIntegerOrPercentage;
};

const criteriaContext = {
  datasheet: "ui_filter",
  criteriaType: "custom_simple",
};
const resultType = DATATYPE.BOOLEAN;

const LookupField = ({ field, onChange }) => {
  const { data_type, options_lookup_spec = {} } = field || {};

  const { quoteFormId } = useParams();
  const { t } = useTranslation();
  const { accessToken } = useAuthStore();
  const { activeDatabooks, getDatasheets, getDatasheetOrderedColumns } =
    useDatabookStore();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const expressionBoxRef = useRef(null);
  const abortControllerObj = useRef(null); // Use useRef to store an abort controller for handling asynchronous requests.

  const [databookId, setDatabookId] = useState(
    get(field, "options_lookup_spec.databook_id", null)
  );
  const [datasheetId, setDatasheetId] = useState(
    get(field, "options_lookup_spec.datasheet_id", null)
  );
  const [columnId, setColumnId] = useState(
    get(field, "options_lookup_spec.column", null)
  );
  const [columnOptions, setColumnOptions] = useState([]);
  const [currentExpression, setCurrentExpression] = useState(
    get(field, "options_lookup_spec.filters", [])
  );
  const [expressionBoxStatus, setExpressionBoxStatus] = useState({
    status: EXPRESSION_BOX_STATUS.INITIAL,
    message: "",
  });
  const [validationApiLoading, setValidationApiLoading] = useState(false);
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [fullAutoSuggestionList, setFullAutoSuggestionList] = useState([]);
  const [variableTokens, setVariableTokens] = useState([]);
  const [isExpressionValid, setIsExpressionValid] = useState(false);

  const { optionsMapByDS, loading } = useDSVariableStore(datasheetId);

  // get the auto suggestions
  const {
    mutate: autoCompleteSuggestionsMutate,
    isLoading: autoCompleteSuggestionsLoading,
  } = useMutation((body) => fetchEndpoint(API.AUTOCOMPLETE_CONTEXT, { body }), {
    onSuccess: (data) => {
      const fullAutoSuggestionList = autoSuggestionByot(data.data || [], t);
      const autoSuggestionList = cloneDeep(fullAutoSuggestionList)?.filter(
        (x) => x.meta.dataType !== DATATYPE.HIERARCHY
      );
      setFullAutoSuggestionList(fullAutoSuggestionList);
      setVariableTokens(autoSuggestionList);
    },
  });

  useEffect(() => {
    if (quoteFormId && databookId && datasheetId) {
      autoCompleteSuggestionsMutate({
        form_builder_id: quoteFormId,
        variables: [
          "datasheet_variables",
          "quote_fields",
          "line_item_fields",
          "functions",
          "operators",
        ],
        databook_id: databookId,
        datasheet_id: datasheetId,
      });
    }
  }, [quoteFormId, databookId, datasheetId, autoCompleteSuggestionsMutate]);

  const variablesMap = JSON.stringify(
    optionsMapByDS[datasheetId]?.variablesMap ?? {}
  );

  useEffect(() => {
    if (!loading) {
      const columns = [];
      const datasheetColumns = getDatasheetOrderedColumns(datasheetId);
      const currentVariablesMap = JSON.parse(variablesMap);
      if (!isEmpty(currentVariablesMap)) {
        for (const variable of Object.values(currentVariablesMap)) {
          if (
            datasheetColumns.includes(variable.meta.systemName) &&
            variable.dataType === DATA_TYPE_MAP[data_type]
          ) {
            columns.push({
              value: variable.meta.systemName,
              label: variable.name,
            });
          }
        }
      }
      setColumnOptions(columns);
    }
  }, [
    loading,
    datasheetId,
    data_type,
    variablesMap,
    getDatasheetOrderedColumns,
  ]);

  const updateExpression = useCallback((expression, result) => {
    setCurrentExpression(expression);
    setIsExpressionValid(
      result.status === EXPRESSION_BOX_STATUS.VALID &&
        result.dataType?.toLowerCase() === resultType?.toLowerCase()
    );
  }, []);

  // Handles expression change and performs validation
  const handleExpressionChange = useCallback(
    async (expression) => {
      updateExpression(expression, {});
      setValidationApiLoading(true);
      let expressionBody = {
        expression: expression,
        meta: {
          databook_id: databookId,
          context: "datasheet",
          contextMeta: criteriaContext,
          isLineItemLevel: true,
          datasheetId: datasheetId,
        },

        is_ds_variables_required: true,
        has_intermediate_variables: false,
        expressionVersion: "v2",
      };

      const result = await handleValidation(
        expression,
        {
          body: expressionBody,
          accessToken,
        },
        abortControllerObj
      );

      setValidationApiLoading(false);

      if (result?.status !== EXPRESSION_BOX_STATUS.ABORTED) {
        if (result.status === EXPRESSION_BOX_STATUS.VALID) {
          updateExpression(expression, result);
          if (
            resultType === null ||
            result.dataType?.toLowerCase() === resultType?.toLowerCase() ||
            compareIfIntegerOrPercentage(result.dataType, resultType)
          ) {
            setExpressionBoxStatus({
              status: result.status,
              message: "valid expression",
            });
          } else {
            setExpressionBoxStatus({
              status: EXPRESSION_BOX_STATUS.INVALID,
              message: `This is a(n) ${result.dataType} expression. Please provide a(n) ${resultType} expression`,
            });
          }
        } else {
          updateExpression(expression, {});
          setExpressionBoxStatus(result);
        }
      }
    },
    [updateExpression, accessToken, databookId, datasheetId]
  );

  useEffect(() => {
    if (currentExpression.length > 0) {
      handleExpressionChange(currentExpression);
    }
  }, [
    currentExpression,
    handleExpressionChange,
    accessToken,
    databookId,
    datasheetId,
  ]);

  const handleDatabookChange = (value) => {
    setDatabookId(value);
    setDatasheetId(null);
    setColumnId(null);
    onChange({
      ...field,
      options_lookup_spec: {
        ...options_lookup_spec,
        source: "datasheet_data",
        databook_id: value,
        datasheet_id: null,
        column: null,
        filters: [],
      },
    });
    setCurrentExpression([]);
  };

  const handleDatasheetChange = (value) => {
    setDatasheetId(value);
    setColumnId(null);
    onChange({
      ...field,
      options_lookup_spec: {
        ...options_lookup_spec,
        source: "datasheet_data",
        datasheet_id: value,
        column: null,
        filters: [],
      },
    });
    setCurrentExpression([]);
  };

  const handleColumnChange = (value) => {
    setColumnId(value);
    onChange({
      ...field,
      options_lookup_spec: {
        ...options_lookup_spec,
        source: "datasheet_data",
        column: value,
      },
    });
  };

  return (
    <div className="flex flex-col gap-3">
      <EverLabel className="font-medium text-ever-base-content" required>
        Look-up:
      </EverLabel>
      <div className="flex flex-col gap-2">
        <EverSelect
          placeholder="Select Databook"
          value={databookId}
          onChange={handleDatabookChange}
          options={activeDatabooks.map((databook) => ({
            value: databook.databookId,
            label: databook.name,
          }))}
          showSearch
        />
        <EverSelect
          placeholder="Select Datasheet"
          value={datasheetId}
          onChange={handleDatasheetChange}
          options={getDatasheets(databookId).map((datasheet) => ({
            value: datasheet.datasheetId,
            label: datasheet.name,
          }))}
          disabled={!databookId}
          showSearch
        />
        <EverSelect
          placeholder="Select Column"
          value={columnId}
          onChange={handleColumnChange}
          options={columnOptions}
          disabled={!datasheetId || loading}
          showSearch
          suffixIcon={
            loading ? (
              <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
            ) : (
              <div className="h-full w-10 flex items-center justify-center">
                <ChevronDownIcon className="w-4 h-4" name="suffix" />
              </div>
            )
          }
        />
      </div>
      <div className="flex flex-col gap-2">
        <EverTg.Text className="font-medium">
          Filter rows in datasheet
        </EverTg.Text>
        {get(field, "options_lookup_spec.filters", []).length > 0 ? (
          <div className="flex border border-solid border-ever-base-400 rounded-lg px-3 py-2 justify-between font-medium">
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="w-4 h-4 text-ever-success" />
              <div>Filter configured</div>
            </div>
            <div
              className="flex items-center gap-2 text-ever-primary"
              onClick={() => setIsFilterModalVisible(true)}
            >
              <EditPencilAltIcon className="w-4 h-4 text-ever-primary" />
              <div>Edit filter</div>
            </div>
          </div>
        ) : (
          <EverButton
            className="w-full"
            type="dashed"
            prependIcon={<PlusIcon />}
            onClick={() => setIsFilterModalVisible(true)}
            disabled={!columnId || loading}
          >
            Add filter
          </EverButton>
        )}
      </div>
      <EverModal
        width={830}
        title="Add filter"
        visible={isFilterModalVisible}
        onCancel={() => setIsFilterModalVisible(false)}
        footer={
          <EverButton
            color="primary"
            onClick={() => {
              onChange({
                ...field,
                options_lookup_spec: {
                  ...options_lookup_spec,
                  filters: currentExpression,
                },
              });
              setIsFilterModalVisible(false);
            }}
            disabled={!isExpressionValid}
          >
            Add
          </EverButton>
        }
        bodyStyle={{ overflow: "visible" }}
      >
        {isFilterModalVisible && (
          <div className="flex flex-col py-1 px-3 bg-ever-base-100 rounded-lg">
            <ExpressionBoxController
              autoCompleteResponse={variableTokens}
              fullAutoSuggestionList={fullAutoSuggestionList || []}
              initialExpression={
                currentExpression?.length > 0 ? currentExpression : []
              }
              className="mt-2"
              onChange={debounce(handleExpressionChange, 500)}
              status={
                autoCompleteSuggestionsLoading || validationApiLoading
                  ? { status: "LOADING", message: "" }
                  : expressionBoxStatus
              }
              expressionBoxRef={expressionBoxRef}
            />
          </div>
        )}
      </EverModal>
    </div>
  );
};

export default LookupField;
