import { EverSwitch, EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { FIELD_PROPERTIES } from "~/v2/features/cpq/form-builders/form-builder/constants";

import DataTypeField from "./DataTypeField";
import HelpTextField from "./HelpTextField";
import LookupField from "./LookupField";
import Min<PERSON>ax<PERSON>ield from "./MinMaxField";
import MinMax<PERSON>engthField from "./MinMaxLengthField";
import OptionsField from "./OptionsField";
import PrecisionField from "./PrecisionField";
import PrefixSuffixField from "./PrefixSuffixField";
import <PERSON>Field from "./StepField";

// For BooleanField, the propertyType should match with the field.properties key
const BooleanField = ({ propertyType, label, field, onChange }) => {
  return (
    <EverSwitch
      label={label}
      checked={field.properties[propertyType]}
      onChange={(checked) => {
        onChange({
          ...field,
          properties: {
            ...field.properties,
            [propertyType]: checked,
          },
        });
      }}
    />
  );
};

const FieldNameField = ({ propertyType, label, field, onChange }) => {
  return (
    <LabeledField
      label={label}
      error={
        field[propertyType] === ""
          ? { message: "This field is required" }
          : null
      }
      required
    >
      <EverInput
        value={field[propertyType]}
        onChange={(event) =>
          onChange({
            ...field,
            [propertyType]: event.target.value,
          })
        }
      />
    </LabeledField>
  );
};

export const FIELD_COMPONENTS = {
  [FIELD_PROPERTIES.FIELD_NAME]: FieldNameField,
  [FIELD_PROPERTIES.DATA_TYPE]: DataTypeField,
  [FIELD_PROPERTIES.HELP_TEXT]: HelpTextField,
  [FIELD_PROPERTIES.MIN_MAX]: MinMaxField,
  [FIELD_PROPERTIES.MIN_MAX_LENGTH]: MinMaxLengthField,
  [FIELD_PROPERTIES.OPTIONS]: OptionsField,
  [FIELD_PROPERTIES.PRECISION]: PrecisionField,
  [FIELD_PROPERTIES.PREFIX_SUFFIX]: PrefixSuffixField,
  [FIELD_PROPERTIES.STEP]: StepField,
  [FIELD_PROPERTIES.IS_MANDATORY]: BooleanField,
  [FIELD_PROPERTIES.IS_READ_ONLY]: BooleanField,
  [FIELD_PROPERTIES.IS_HIDDEN]: BooleanField,
  [FIELD_PROPERTIES.IS_PERCENTAGE]: BooleanField,
  [FIELD_PROPERTIES.LOOKUP]: LookupField,
};
