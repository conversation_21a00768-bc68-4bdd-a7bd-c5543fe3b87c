import {
  PlusCircleIcon,
  EditPencilAltIcon,
} from "@everstage/evericons/outlined";
import { get, startsWith, isEmpty } from "lodash";
import { useState, useRef } from "react";
import { twMerge } from "tailwind-merge";

import { EverTg, EverButton } from "~/v2/components";

const Header = ({ formBuilder, updateFormBuilder }) => {
  const nameRef = useRef(null);

  const [nameWidth, setNameWidth] = useState(0);
  const [isEditQuoteName, setIsEditQuoteName] = useState(false);

  const handleSectionNameChange = (e) => {
    if (e.target.value.length <= 50) {
      updateFormBuilder({
        ...formBuilder,
        form_builder_name: e.target.value,
      });
      setTimeout(() => {
        setNameWidth(nameRef.current?.offsetWidth ?? 0);
      }, 10);
    }
  };

  const handleSectionNameBlur = () => {
    if (formBuilder.form_builder_name.length > 0) {
      setIsEditQuoteName(false);
    }
  };

  return (
    <div className="flex justify-between items-center gap-2 px-8 py-4 bg-ever-base-50 border-0 border-b border-ever-base-300">
      <div className="flex flex-col gap-1.5">
        {isEditQuoteName && (
          <input
            autoFocus
            className={twMerge(
              "min-w-20 h-[26px] text-xl font-semibold border-0 border-b border-ever-primary bg-transparent outline-0 transition-all",
              formBuilder.form_builder_name.length === 0
                ? "border-ever-error"
                : ""
            )}
            style={{
              width: nameWidth - 8 + "px",
            }}
            value={formBuilder.form_builder_name}
            onChange={handleSectionNameChange}
            onBlur={handleSectionNameBlur}
            onKeyUp={(e) => {
              if (e.key === "Enter") {
                handleSectionNameBlur();
              }
            }}
          />
        )}
        <div
          ref={nameRef}
          className={twMerge(
            "flex items-center gap-1.5 min-w-24",
            isEditQuoteName ? "absolute invisible" : ""
          )}
        >
          <div
            className="flex items-center gap-1 cursor-pointer"
            onClick={() => {
              setIsEditQuoteName(true);
              setNameWidth(nameRef.current?.offsetWidth ?? 0);
            }}
          >
            <EverTg.Text className="text-xl font-semibold">
              {formBuilder.form_builder_name}
            </EverTg.Text>
            <EditPencilAltIcon className="w-5 h-5 shrink-0 text-ever-base-content-low" />
          </div>
        </div>
        {formBuilder.form_builder_description && (
          <EverTg.Text>{formBuilder.form_builder_description}</EverTg.Text>
        )}
      </div>
      <div className="flex gap-3 items-center ml-auto">
        <EverButton
          type="outlined"
          color="base"
          className="!h-8"
          prependIcon={
            <PlusCircleIcon className="text-ever-base-content-mid" />
          }
          onClick={() => {
            const sectionOrder = get(
              formBuilder,
              "form_spec.section_order",
              []
            );
            const highestSectionNumber =
              sectionOrder
                .map((section) => {
                  const sectionNumber = section?.split("section")?.[1] ?? 0;
                  return Number(sectionNumber);
                })
                .sort((a, b) => b - a)[0] ?? 0;
            const newSectionId = `section${highestSectionNumber + 1}`;

            const untitledLabels = [];
            Object.values(formBuilder.form_spec.sections).forEach((section) => {
              if (
                startsWith(section.label, "New Section") &&
                !isEmpty(section.label.split("New Section ")[1]) &&
                !Number.isNaN(Number(section.label.split("New Section ")[1]))
              ) {
                untitledLabels.push(
                  Number(section.label.split("New Section ")[1])
                );
              }
            });
            const highestUntitledNumber =
              untitledLabels.sort((a, b) => b - a)[0] ?? 0;

            updateFormBuilder({
              ...formBuilder,
              form_spec: {
                ...formBuilder.form_spec,
                section_order: [
                  ...formBuilder.form_spec.section_order,
                  newSectionId,
                ],
                sections: {
                  ...formBuilder.form_spec.sections,
                  [newSectionId]: {
                    id: newSectionId,
                    description: "",
                    field_order: [],
                    fields: {},
                    help_text: "",
                    is_hidden: false,
                    label: `New Section ${highestUntitledNumber + 1}`,
                  },
                },
              },
            });
          }}
        >
          New section
        </EverButton>
        {/* <EverButton
          type="outlined"
          color="base"
          className="!h-8"
          prependIcon={<EyeIcon className="text-ever-base-content-mid" />}
          onClick={() => console.log("Previewing form")}
        >
          Preview
        </EverButton> */}
      </div>
    </div>
  );
};

export default Header;
