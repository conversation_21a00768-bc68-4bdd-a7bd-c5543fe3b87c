import { get } from "lodash";
import { useMemo, useLayoutEffect, useRef, useState } from "react";
import { DragDropContext } from "react-beautiful-dnd";

import Section from "./Section";

const Sections = ({
  formBuilder,
  updateFormBuilder,
  duplicateSectionIds,
  duplicateFieldIdsBySection,
}) => {
  const initialLoad = useRef(true);
  const containerRef = useRef(null);
  const previousSectionOrderLength = useRef(0);

  const [isNewSectionAdded, setIsNewSectionAdded] = useState(false);

  const { sections, sectionOrder, defaultFields, sourceFields } =
    useMemo(() => {
      return {
        sections: get(formBuilder, "form_spec.sections", {}),
        sectionOrder: get(formBuilder, "form_spec.section_order", []),
        defaultFields: get(formBuilder, "default_fields", []),
        sourceFields: get(formBuilder, "source_fields", []),
      };
    }, [formBuilder]);

  useLayoutEffect(() => {
    if (initialLoad.current) {
      initialLoad.current = false;
      previousSectionOrderLength.current = sectionOrder.length;
    } else if (sectionOrder.length > previousSectionOrderLength.current) {
      setIsNewSectionAdded(true);
      containerRef.current.scrollTo({
        top: containerRef.current.scrollHeight,
        behavior: "smooth",
      });
      setTimeout(() => {
        setIsNewSectionAdded(false);
      }, 1000);
    }
  }, [sectionOrder.length]);

  const onDragEnd = (result) => {
    const { source, destination } = result;

    if (!destination) return; // Dropped outside of a valid droppable area

    const sourceSection = source.droppableId;
    const destinationSection = destination.droppableId;

    const draggedItem = sections[sourceSection].field_order[source.index];

    if (sourceSection === destinationSection) {
      const sourceFieldOrder = [...sections[sourceSection].field_order];
      sourceFieldOrder.splice(source.index, 1);
      sourceFieldOrder.splice(destination.index, 0, draggedItem);
      updateFormBuilder({
        ...formBuilder,
        form_spec: {
          ...formBuilder.form_spec,
          sections: {
            ...sections,
            [sourceSection]: {
              ...sections[sourceSection],
              field_order: sourceFieldOrder,
            },
          },
        },
      });
    } else {
      // Remove item from source section
      const sourceFieldOrder = [...sections[sourceSection].field_order];
      sourceFieldOrder.splice(source.index, 1);
      const sourceField = sections[sourceSection].fields[draggedItem];
      delete sections[sourceSection].fields[draggedItem];

      // Add item to destination section
      const destinationFieldOrder = [
        ...sections[destinationSection].field_order,
      ];
      destinationFieldOrder.splice(destination.index, 0, draggedItem);
      sections[destinationSection].fields[draggedItem] = sourceField;

      updateFormBuilder({
        ...formBuilder,
        form_spec: {
          ...formBuilder.form_spec,
          sections: {
            ...sections,
            [sourceSection]: {
              ...sections[sourceSection],
              field_order: sourceFieldOrder,
            },
            [destinationSection]: {
              ...sections[destinationSection],
              field_order: destinationFieldOrder,
            },
          },
        },
      });
    }
  };

  return (
    <div
      ref={containerRef}
      className="max-w-[960px] w-full py-4 m-auto overflow-y-auto overflow-x-hidden"
    >
      <div className="flex flex-col">
        <DragDropContext onDragEnd={onDragEnd}>
          <div className="flex flex-col gap-1 px-2">
            {sectionOrder.map((sectionId, index) => (
              <Section
                key={`${sectionId}##::##${index}`}
                index={index}
                sectionId={sectionId}
                sectionOrder={sectionOrder}
                sections={sections}
                isNewSection={
                  isNewSectionAdded && index === sectionOrder.length - 1
                }
                section={sections[sectionId]}
                sourceFields={sourceFields}
                defaultFields={defaultFields}
                duplicateSectionIds={duplicateSectionIds}
                duplicateFieldIds={duplicateFieldIdsBySection[sectionId]}
                updateSections={(sections, sectionOrder) => {
                  updateFormBuilder({
                    ...formBuilder,
                    form_spec: {
                      ...formBuilder.form_spec,
                      sections: sections ?? formBuilder.form_spec.sections,
                      section_order:
                        sectionOrder ?? formBuilder.form_spec.section_order,
                    },
                  });
                }}
              />
            ))}
          </div>
        </DragDropContext>
      </div>
    </div>
  );
};

export default Sections;
