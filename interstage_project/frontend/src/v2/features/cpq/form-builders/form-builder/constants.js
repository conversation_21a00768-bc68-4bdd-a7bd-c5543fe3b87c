import { Trash03Icon } from "@everstage/evericons/outlined";

import { FIELD_TYPE } from "~/v2/features/cpq/constants";

const API = {
  GET_FORM_BUILDER: "getFormBuilder",
  UPDATE_FORM_BUILDER: "updateFormBuilder",
  AUTOCOMPLETE_CONTEXT: "autocompleteContext",
};

const API_ENDPOINTS = {
  [API.GET_FORM_BUILDER]: ({ quoteFormId }) => ({
    url: `/ninja/cpq/forms/form_builder_id/${quoteFormId}`,
    method: "GET",
  }),
  [API.UPDATE_FORM_BUILDER]: {
    url: "/ninja/cpq/forms/update_form_builder",
    method: "POST",
  },
  [API.AUTOCOMPLETE_CONTEXT]: {
    url: `/ninja/cpq/forms/autocomplete_context`,
    method: "POST",
  },
};

const FIELD_MORE_OPTIONS = {
  DELETE: "delete",
};

const FIELD_MORE_MENU_ITEMS = [
  {
    icon: <Trash03Icon className="w-4 h-4 text-ever-error shrink-0" />,
    type: FIELD_MORE_OPTIONS.DELETE,
    label: "Delete",
  },
];

const FIELD_PROPERTIES = {
  FIELD_NAME: "label",
  DATA_TYPE: "data_type",
  IS_MANDATORY: "is_mandatory",
  PREFIX_SUFFIX: "prefix_suffix",
  MIN_MAX: "min_max",
  MIN_MAX_LENGTH: "min_max_length",
  HELP_TEXT: "help_text",
  IS_READ_ONLY: "is_read_only",
  IS_HIDDEN: "is_hidden",
  OPTIONS: "options",
  PRECISION: "precision",
  IS_PERCENTAGE: "is_percentage",
  STEP: "step",
  LOOKUP: "lookup",
};

const FIELD_LABELS = {
  [FIELD_PROPERTIES.FIELD_NAME]: "Field Name",
  [FIELD_PROPERTIES.DATA_TYPE]: "Data type for value",
  [FIELD_PROPERTIES.IS_MANDATORY]: "Set as mandatory field",
  [FIELD_PROPERTIES.PREFIX_SUFFIX]: "Set prefix / suffix",
  [FIELD_PROPERTIES.MIN_MAX]: "Min & Max",
  [FIELD_PROPERTIES.MIN_MAX_LENGTH]: "Set Min & Max character length",
  [FIELD_PROPERTIES.HELP_TEXT]: "Show tooltip",
  [FIELD_PROPERTIES.IS_READ_ONLY]: "Set as read-only field",
  [FIELD_PROPERTIES.IS_HIDDEN]: "Set as hidden field",
  [FIELD_PROPERTIES.OPTIONS]: "Configure Options",
  [FIELD_PROPERTIES.PRECISION]: "Enable decimal type",
  [FIELD_PROPERTIES.IS_PERCENTAGE]: "Show as percentage (%)",
  [FIELD_PROPERTIES.STEP]: "Add step-interval",
};

// Shared property arrays for similar field types

const SINGLE_LINE_TEXT_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.PREFIX_SUFFIX,
  FIELD_PROPERTIES.MIN_MAX_LENGTH,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
];

const MULTI_LINE_TEXT_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.MIN_MAX_LENGTH,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
];

// Both single-select and multi-select fields share these properties.
const SELECT_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.PREFIX_SUFFIX,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
  FIELD_PROPERTIES.OPTIONS,
];

// Number fields include additional constraints and options.
const NUMBER_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.PREFIX_SUFFIX,
  FIELD_PROPERTIES.MIN_MAX_LENGTH,
  FIELD_PROPERTIES.MIN_MAX,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
  FIELD_PROPERTIES.PRECISION,
  FIELD_PROPERTIES.IS_PERCENTAGE,
  FIELD_PROPERTIES.STEP,
];

const LOOKUP_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.DATA_TYPE,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
  FIELD_PROPERTIES.LOOKUP,
];

// A basic set for field types that don't require extra properties.
const SIMPLE_PROPERTIES = [
  FIELD_PROPERTIES.FIELD_NAME,
  FIELD_PROPERTIES.IS_MANDATORY,
  FIELD_PROPERTIES.HELP_TEXT,
  FIELD_PROPERTIES.IS_READ_ONLY,
  FIELD_PROPERTIES.IS_HIDDEN,
];

const FIELD_SECTION_TYPES = [
  {
    id: "general",
    label: "General",
    fields: [
      FIELD_PROPERTIES.FIELD_NAME,
      FIELD_PROPERTIES.DATA_TYPE,
      FIELD_PROPERTIES.IS_MANDATORY,
      FIELD_PROPERTIES.PREFIX_SUFFIX,
      FIELD_PROPERTIES.MIN_MAX,
      FIELD_PROPERTIES.MIN_MAX_LENGTH,
      FIELD_PROPERTIES.HELP_TEXT,
      FIELD_PROPERTIES.IS_READ_ONLY,
      FIELD_PROPERTIES.IS_HIDDEN,
    ],
  },
  {
    id: "data_settings",
    label: "Data Settings",
    fields: [
      FIELD_PROPERTIES.OPTIONS,
      FIELD_PROPERTIES.PRECISION,
      FIELD_PROPERTIES.IS_PERCENTAGE,
      FIELD_PROPERTIES.STEP,
      FIELD_PROPERTIES.LOOKUP,
    ],
  },
];

const FIELD_PROPERTIES_BY_TYPE = {
  [FIELD_TYPE.SINGLE_LINE]: SINGLE_LINE_TEXT_PROPERTIES,
  [FIELD_TYPE.MULTI_LINE]: MULTI_LINE_TEXT_PROPERTIES,
  [FIELD_TYPE.SINGLE_SELECT]: SELECT_PROPERTIES,
  [FIELD_TYPE.MULTI_SELECT]: SELECT_PROPERTIES,
  [FIELD_TYPE.NUMBER]: NUMBER_PROPERTIES,
  [FIELD_TYPE.CURRENCY]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.CHECKBOX]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.EMAIL]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.DATE]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.DATE_TIME]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.URL]: SIMPLE_PROPERTIES,
  [FIELD_TYPE.LOOKUP]: LOOKUP_PROPERTIES,
};

export {
  API,
  API_ENDPOINTS,
  FIELD_PROPERTIES,
  FIELD_LABELS,
  FIELD_PROPERTIES_BY_TYPE,
  FIELD_SECTION_TYPES,
  FIELD_MORE_MENU_ITEMS,
  FIELD_MORE_OPTIONS,
};
