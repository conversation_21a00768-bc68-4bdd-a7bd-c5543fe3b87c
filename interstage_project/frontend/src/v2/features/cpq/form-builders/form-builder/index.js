import { ArrowNarrowLeftIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useState, useMemo } from "react";
import { useQuery, useMutation } from "react-query";
import { useParams, useNavigate, useLocation } from "react-router-dom";

import { EverButton, EverLoader, message } from "~/v2/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";

import { API, API_ENDPOINTS } from "./constants";
import Header from "./Header";
import Sections from "./Sections";

const FormBuilder = () => {
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);
  const { quoteFormId } = useParams();
  const [formBuilder, setFormBuilder] = useState(null);
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const { data, isLoading } = useQuery(
    [API.GET_FORM_BUILDER, quoteFormId],
    () => fetchEndpoint(API.GET_FORM_BUILDER, {}, { quoteFormId }),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setFormBuilder(data.form_builder);
      },
      onError: () => {
        message.error("Something went wrong! Please try again later.");
      },
    }
  );

  const updateFormBuilder = useMutation(
    (body) => fetchEndpoint(API.UPDATE_FORM_BUILDER, { body }),
    {
      onSuccess: () => {
        message.success("Quote form updated successfully!");
      },
      onError: () => {
        message.error("Failed to update quote form!");
      },
    }
  );

  const navigateBack = () => {
    const pathnameArr = pathname.split("/");
    pathnameArr.pop(); // remove the last element
    navigate(pathnameArr.join("/") || "/");
  };

  const {
    emptyFields,
    duplicateSectionIds,
    duplicateFieldIdsBySection,
    emptySectionLabels,
  } = useMemo(() => {
    if (!isEmpty(formBuilder?.form_spec?.sections)) {
      const sectionLabelMap = {};
      const duplicateFieldIds = {};
      const emptySectionLabels = [];
      const emptyFields = [];

      Object.values(formBuilder.form_spec.sections).forEach(
        ({ label, id, fields = {} }) => {
          const sectionKey = label.trim().toLowerCase();
          sectionLabelMap[sectionKey] = sectionLabelMap[sectionKey] || [];
          sectionLabelMap[sectionKey].push(id);
          duplicateFieldIds[id] = {};

          // Getting empty section name
          if (sectionKey === "") {
            emptySectionLabels.push(id);
          } else if (isEmpty(fields)) {
            emptyFields.push(label);
          } else {
            // Getting duplicate section names
            Object.values(fields).forEach(({ label: fLabel, id: fieldId }) => {
              const fieldKey = fLabel.trim().toLowerCase();
              duplicateFieldIds[id][fieldKey] =
                duplicateFieldIds[id][fieldKey] || [];
              duplicateFieldIds[id][fieldKey].push(fieldId);
            });
          }

          // Getting duplicate section wise field name
          duplicateFieldIds[id] = Object.values(duplicateFieldIds[id])
            .filter((ids) => ids.length > 1)
            .flat();
        }
      );

      // flatten only those arrays whose length > 1
      const duplicateSectionIds = Object.values(sectionLabelMap)
        .filter((ids) => ids.length > 1)
        .flat();

      return {
        emptySectionLabels,
        emptyFields,
        duplicateSectionIds,
        duplicateFieldIdsBySection: duplicateFieldIds,
      };
    }
    return {
      duplicateSectionIds: [],
      duplicateFieldIdsBySection: {},
      emptySectionLabels: [],
      emptyFields: [],
    };
  }, [formBuilder?.form_spec?.sections]);

  const handleSave = () => {
    if (isEmpty(formBuilder.form_builder_name)) {
      message.error("Quote form name cannot be empty");
    } else if (emptySectionLabels.length !== 0) {
      message.error("One or more section names are empty");
    } else if (emptyFields.length !== 0) {
      message.error(`${emptyFields.join(", ")} has no fields`);
    } else if (duplicateSectionIds.length !== 0) {
      message.error("Section names contains one or more duplicate value");
    } else if (
      Object.values(duplicateFieldIdsBySection).some(
        (duplicateFieldIds) => duplicateFieldIds.length !== 0
      )
    ) {
      message.error("Field names contains one or more duplicate value");
    } else {
      message.loading("Quote form is being saved...");
      updateFormBuilder.mutate({
        form_builder_id: formBuilder.form_builder_id,
        updated_fields: { ...formBuilder },
      });
    }
  };

  if (isLoading) {
    return (
      <EverLoader
        indicatorType="spinner"
        className="flex h-full"
        wrapperClassName="z-20"
        spinning
      />
    );
  } else if (data.status !== "success") {
    return <div>Error</div>;
  } else if (isEmpty(formBuilder)) {
    return null;
  }

  return (
    <EverLoader
      indicatorType="spinner"
      className="flex flex-col overflow-auto"
      wrapperClassName="z-20"
      spinning={updateFormBuilder.isLoading}
    >
      <div className="flex flex-col overflow-y-auto h-full">
        <Header formBuilder={formBuilder} updateFormBuilder={setFormBuilder} />
        <Sections
          formBuilder={formBuilder}
          updateFormBuilder={setFormBuilder}
          duplicateSectionIds={duplicateSectionIds}
          duplicateFieldIdsBySection={duplicateFieldIdsBySection}
        />
        <div className="flex justify-between px-6 py-3 border-t border-solid border-ever-base-300 shadow-[0px_0px_2px_0px_rgba(0,23,128,0.06),0px_-1px_3px_0px_rgba(0,23,128,0.10)]">
          <EverButton
            type="text"
            color="base"
            prependIcon={<ArrowNarrowLeftIcon />}
            onClick={navigateBack}
          >
            Back
          </EverButton>
          <EverButton onClick={handleSave}>Save Changes</EverButton>
        </div>
      </div>
    </EverLoader>
  );
};

export default FormBuilder;
