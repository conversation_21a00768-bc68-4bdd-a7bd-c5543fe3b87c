import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useRef } from "react";
import { useForm, useWatch, Controller } from "react-hook-form";

import {
  EverModal,
  EverButton,
  EverTg,
  EverInput,
  LazySelect,
} from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { useFetchApi } from "~/v2/features/cpq/hooks";

import { API, API_ENDPOINTS } from "./constants";

const CreateQuoteRuleModal = ({
  visible,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      name: "",
      description: "",
      quoteForm: "",
    },
  });
  const abortController = useRef();
  const formValues = useWatch({ control, name: ["name", "quoteForm"] });
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleCreateQuoteRule = ({ name, description, quoteForm }) => {
    onSubmit({
      form_rule_name: name,
      form_rule_description: description,
      form_builder_id: quoteForm,
    });
  };

  const fetchQuoteForms = async (params) => {
    const { searchTerm = null, page, limit, successCbk, failureCbk } = params;

    abortController.current = new AbortController();
    const { signal } = abortController.current;
    const body = {
      limit_value: limit,
      search_term: searchTerm || "",
      page_number: page + 1,
    };

    try {
      const res = await fetchEndpoint(API.GET_ALL_FORM_BUILDERS, {
        body,
        signal,
      });
      const data = res.form_builders.map((item) => ({
        label: item.form_builder_name,
        value: item.form_builder_id,
        key: `${item.form_builder_name}##::##${item.form_builder_id}`,
      }));
      successCbk(data);
    } catch {
      failureCbk();
    }
  };

  const lazyLoadProps = {
    abort: () => {
      abortController?.current?.abort();
    },
    getOptions: async (params) => {
      await fetchQuoteForms(params);
    },
  };

  return (
    <EverModal
      visible={visible}
      onCancel={!loading && handleClose}
      destroyOnClose
      width={640}
      bodyStyle={{ height: "554px", maxHeight: "80%" }}
    >
      <div className="flex flex-col items-center justify-between py-9 px-24 gap-12 w-full">
        <div className="flex flex-col w-full gap-3 text-center">
          <EverTg.Heading1>Create New Rule </EverTg.Heading1>
          <EverTg.Text className="text-ever-base-content-mid">
            Create and configure new rule.
          </EverTg.Text>
        </div>
        <div className="flex flex-col gap-8 justify-between w-full">
          <div className="flex flex-col gap-4">
            <LabeledField
              label="Name"
              className="gap-2"
              labelClassName="text-xs"
              error={errors.name}
              required
            >
              <Controller
                control={control}
                name="name"
                rules={{
                  required: "Rule name is required",
                }}
                render={({ field }) => (
                  <EverInput {...field} placeholder="Enter rule name" />
                )}
              />
            </LabeledField>
            <LabeledField
              label="Description"
              className="gap-2"
              labelClassName="text-xs"
            >
              <Controller
                control={control}
                name="description"
                render={({ field }) => (
                  <EverInput.TextArea
                    {...field}
                    placeholder="Enter description"
                    autoSize={{ minRows: 2, maxRows: 2 }}
                  />
                )}
              />
            </LabeledField>
            <LabeledField
              label="Which quote form is this rule for?"
              className="gap-2"
              labelClassName="text-xs"
              required
            >
              <Controller
                control={control}
                name="quoteForm"
                render={({ field }) => (
                  <LazySelect
                    {...field}
                    showSearch
                    limit={25}
                    size="medium"
                    className="w-full"
                    placeholder="Select a quote form"
                    {...lazyLoadProps}
                  />
                )}
              />
            </LabeledField>
          </div>
          <EverButton
            appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
            onClick={handleSubmit(handleCreateQuoteRule)}
            disabled={isEmpty(formValues[0]) || isEmpty(formValues[1])}
            loading={loading}
          >
            Add Rule
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
};

export default CreateQuoteRuleModal;
