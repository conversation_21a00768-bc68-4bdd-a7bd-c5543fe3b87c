import {
  DotsVerticalIcon,
  ChevronDownIcon,
  CheckIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import {
  EverCard,
  EverTg,
  IconButton,
  EverTooltip,
  EverBadge,
  EverButton,
} from "~/v2/components";
import { RemoveConfirmationModal } from "~/v2/features/cpq/components";

import {
  STATUS_OPTIONS,
  STATUS_MENU_ITEMS,
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
  REMOVE_CONFIRMATION_MESSAGES,
} from "./constants";

const QuoteRuleCard = ({
  quoteRule,
  onClone,
  onUpdate,
  onDelete,
  isLoading,
}) => {
  const navigate = useNavigate();
  const [showRemoveConfirmModal, setShowRemoveConfirmModal] = useState(false);

  const getBadgeProps = () => {
    const props = STATUS_MENU_ITEMS[quoteRule.status];

    switch (quoteRule.status) {
      case STATUS_OPTIONS.ACTIVE: {
        return {
          type: props.type,
          icon: props.icon,
          title: (
            <div className="flex items-center gap-1">
              <span>{props.label}</span>
              <ChevronDownIcon className="w-4 h-4 text-ever-success-lite-content" />
            </div>
          ),
        };
      }
      case STATUS_OPTIONS.INACTIVE: {
        return {
          type: props.type,
          icon: props.icon,
          title: (
            <div className="flex items-center gap-1.5">
              <span>{props.label}</span>
              <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
            </div>
          ),
        };
      }
      default: {
        return {};
      }
    }
  };

  const handleStatusChange = (event) => {
    event.domEvent.stopPropagation();
    if (event.key === quoteRule.status) {
      return;
    }

    onUpdate({
      form_rule_id: quoteRule.form_rule_id,
      updated_fields: { status: event.key },
    });
  };

  const handleMoreOptionsClick = (item) => {
    if (item === MORE_OPTIONS.CLONE) {
      onClone({ form_rule_id: quoteRule.form_rule_id });
    } else if (item === MORE_OPTIONS.DELETE) {
      setShowRemoveConfirmModal(true);
    }
  };

  const statusMenu = (
    <Menu
      className="flex items-start !p-2"
      selectedKeys={[quoteRule.status]}
      onClick={handleStatusChange}
    >
      {Object.values(STATUS_MENU_ITEMS).map((item) => (
        <Menu.Item key={item.value} className="rounded-lg w-full !h-9">
          <div className="flex gap-2 items-center">
            <EverTg.Caption className="font-normal">
              {item.label}
            </EverTg.Caption>
            {quoteRule.status === item.value && (
              <CheckIcon className="h-4 w-4" />
            )}
          </div>
        </Menu.Item>
      ))}
    </Menu>
  );

  const moreOptionsMenu = (
    <Menu
      className="flex items-start !p-2"
      onClick={(event) => {
        event.domEvent.stopPropagation();
      }}
    >
      {MORE_MENU_ITEMS.map((item, key) => (
        <Menu.Item
          className="!px-0 w-full hover:!bg-transparent !h-9"
          key={key}
        >
          <EverButton
            type="text"
            color={item.type === MORE_OPTIONS.DELETE ? "error" : "base"}
            size="small"
            className="w-full justify-start"
            prependIcon={item.icon}
            onClick={() => handleMoreOptionsClick(item.type)}
          >
            <EverTg.Caption
              className={twMerge(
                "font-normal",
                item.type === MORE_OPTIONS.DELETE
                  ? "text-ever-error-lite-content"
                  : ""
              )}
            >
              {item.label}
            </EverTg.Caption>
          </EverButton>
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <React.Fragment key={quoteRule.form_rule_id}>
      <EverCard
        className="flex gap-6 items-center justify-between py-3"
        onClick={() => {
          navigate(`/cpq/settings/quote-forms/${quoteRule.form_rule_id}`);
        }}
        interactive
      >
        <div className="w-full">
          <div className="flex flex-col gap-1.5 w-11/12">
            <EverTg.Text className="font-medium">
              {quoteRule.form_rule_name}
            </EverTg.Text>
            {quoteRule.form_rule_description && (
              <EverTooltip title={quoteRule.form_rule_description}>
                <div className="w-fit text-sm text-ever-base-content-mid line-clamp-2">
                  {quoteRule.form_rule_description}
                </div>
              </EverTooltip>
            )}
          </div>
        </div>
        <Dropdown
          overlay={statusMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <EverBadge
            className="!h-8 gap-1.5 text-sm font-medium"
            {...getBadgeProps()}
          />
        </Dropdown>
        <Dropdown
          overlay={moreOptionsMenu}
          trigger={["click"]}
          onClick={(event) => {
            event.stopPropagation();
          }}
        >
          <IconButton
            type="text"
            color="base"
            size="small"
            className="!px-2"
            icon={
              <DotsVerticalIcon className="w-4 h-4 text-ever-base-content-mid" />
            }
          />
        </Dropdown>
      </EverCard>
      <RemoveConfirmationModal
        visible={showRemoveConfirmModal}
        title={REMOVE_CONFIRMATION_MESSAGES.title}
        subtitle={REMOVE_CONFIRMATION_MESSAGES.subtitle}
        onConfirm={() => onDelete({ form_rule_id: quoteRule.form_rule_id })}
        onCancel={() => setShowRemoveConfirmModal(false)}
        loading={isLoading}
      />
    </React.Fragment>
  );
};

export default QuoteRuleCard;
