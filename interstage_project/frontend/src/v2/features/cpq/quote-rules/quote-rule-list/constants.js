import { CopyIcon, Trash03Icon } from "@everstage/evericons/outlined";
import { CheckCircleIcon, PauseCircleIcon } from "@everstage/evericons/solid";

const STATUS_OPTIONS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
};

const STATUS_MENU_ITEMS = {
  [STATUS_OPTIONS.ACTIVE]: {
    type: "success",
    icon: <CheckCircleIcon className="h-4 w-4 text-ever-success" />,
    value: STATUS_OPTIONS.ACTIVE,
    label: "Active",
  },
  [STATUS_OPTIONS.INACTIVE]: {
    type: "base",
    icon: <PauseCircleIcon className="h-4 w-4 text-ever-base-content-mid" />,
    value: STATUS_OPTIONS.INACTIVE,
    label: "Inactive",
  },
};

const MORE_OPTIONS = {
  CLONE: "clone",
  DELETE: "delete",
};

const MORE_MENU_ITEMS = [
  {
    icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid shrink-0" />,
    type: MORE_OPTIONS.CLONE,
    label: "Clone",
  },
  {
    icon: <Trash03Icon className="w-4 h-4 text-ever-error shrink-0" />,
    type: MORE_OPTIONS.DELETE,
    label: "Delete",
  },
];

const REMOVE_CONFIRMATION_MESSAGES = {
  title: "Are you sure to remove this quote form?",
  subtitle: "This action cannot be undone",
};

const API = {
  GET_ALL_QUOTE_RULES: "getAllQuoteRules",
  CREATE_QUOTE_RULE: "createQuoteRule",
  UPDATE_QUOTE_RULE: "updateQuoteRule",
  DELETE_QUOTE_RULE: "deleteQuoteRule",
  GET_ALL_FORM_BUILDERS: "getAllFormBuilders",
};

const API_ENDPOINTS = {
  [API.GET_ALL_QUOTE_RULES]: {
    url: "/ninja/cpq/forms/form_rules",
    method: "POST",
  },
  [API.CREATE_QUOTE_RULE]: {
    url: "/ninja/cpq/forms/create_form_rule",
    method: "POST",
  },
  [API.UPDATE_QUOTE_RULE]: {
    url: "/ninja/cpq/forms/update_form_rule",
    method: "POST",
  },
  [API.DELETE_QUOTE_RULE]: {
    url: "/ninja/cpq/forms/delete_form_rule",
    method: "POST",
  },
  [API.GET_ALL_FORM_BUILDERS]: {
    url: "/ninja/cpq/forms/all_form_builders",
    method: "POSt",
  },
};

export {
  STATUS_OPTIONS,
  STATUS_MENU_ITEMS,
  MORE_OPTIONS,
  MORE_MENU_ITEMS,
  REMOVE_CONFIRMATION_MESSAGES,
  API,
  API_ENDPOINTS,
};
