/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import { PlusCircleIcon, SearchIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useEffect, useState, useCallback, useRef } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { EverButton, EverInput, EverLoader, EverTg } from "~/v2/components";
import { EmptyListScreen } from "~/v2/features/cpq/components";
import { useDebouncedSearch, useFetchApi } from "~/v2/features/cpq/hooks";
import { createQuoteRule } from "~/v2/images";

import { API_ENDPOINTS, API } from "./constants";
import CreateQuoteRuleModal from "./CreateQuoteRuleModal";
import QuoteRuleCard from "./QuoteRuleCard";

const QuoteRuleList = () => {
  const LIST_PAGE_SIZE = useRef(15);
  const initialLoading = useRef(false);
  const canFetchMore = useRef(true);
  const pageNumber = useRef(1);
  const isScrollLoading = useRef(false);
  const scrollContainerRef = useRef(null);

  const [quoteRuleList, setQuoteRuleListList] = useState([]);
  const [isCreateRuleModalOpen, setIsCreateRuleModalOpen] = useState(false);
  const [isContentScrolled, setIsContentScrolled] = useState(false);

  const navigate = useNavigate();
  const { fetchEndpoint } = useFetchApi(API_ENDPOINTS);

  const { mutate: getQuoteRulesMutate, isLoading: getQuoteRulesLoading } =
    useMutation((body) => fetchEndpoint(API.GET_ALL_QUOTE_RULES, { body }), {
      onSuccess: (data, variables) => {
        const quoteRules = data?.form_rules ?? [];
        initialLoading.current = false;
        canFetchMore.current = quoteRules.length === variables.limit_value;
        pageNumber.current = variables.page_number;
        isScrollLoading.current = false;
        setQuoteRuleListList((prev) => {
          if (variables.page_number === 1) {
            return quoteRules;
          }
          return [...prev, ...quoteRules];
        });
      },
    });

  const createQuoteRuleMutation = useMutation(
    (body) => fetchEndpoint(API.CREATE_QUOTE_RULE, { body }),
    {
      onSuccess: (data) => {
        setIsCreateRuleModalOpen(false);
        navigate(`/cpq/settings/quote-rules/${data.form_rule_id}`);
      },
    }
  );

  const updateQuoteRule = useMutation(
    (body) => fetchEndpoint(API.UPDATE_QUOTE_RULE, { body }),
    {
      onSuccess: (_, variables) => {
        setQuoteRuleListList((prev) => {
          return prev.map((quoteRule) => {
            if (quoteRule.form_rule_id === variables.form_rule_id) {
              return {
                ...quoteRule,
                status: variables.updated_fields.status,
              };
            }
            return quoteRule;
          });
        });
      },
    }
  );

  const deleteQuoteRule = useMutation(
    (body) => fetchEndpoint(API.DELETE_QUOTE_RULE, { body }),
    {
      onSuccess: () => {
        getQuoteRulesList({ searchTerm: searchQuery });
      },
    }
  );

  const getQuoteRulesList = useCallback(
    ({ searchTerm, pageNumber = 1 }) => {
      getQuoteRulesMutate({
        search_term: searchTerm,
        limit_value: LIST_PAGE_SIZE.current,
        page_number: pageNumber,
      });
    },
    [getQuoteRulesMutate]
  );

  const handleScroll = (event) => {
    const scrollPosition = event.target.scrollTop;
    console.log(scrollPosition);
    const reachedFetchMorePos =
      event.target.scrollHeight - scrollPosition === event.target.clientHeight;
    const fetchMoreEnabled =
      !getQuoteRulesLoading && canFetchMore.current && reachedFetchMorePos;

    setIsContentScrolled(scrollPosition > 8);
    if (scrollPosition > 0 && fetchMoreEnabled) {
      isScrollLoading.current = true;
      getQuoteRulesList({
        searchTerm: searchQuery,
        pageNumber: pageNumber.current + 1,
      });
      setTimeout(() => {
        scrollContainerRef.current.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: "smooth",
        });
      }, 10);
    }
  };

  const {
    searchState: { userInput, searchQuery },
    onSearch,
  } = useDebouncedSearch(getQuoteRulesList);

  useEffect(() => {
    getQuoteRulesList({ searchTerm: null });
  }, [getQuoteRulesList]);

  const isLoading =
    getQuoteRulesLoading ||
    updateQuoteRule.isLoading ||
    createQuoteRuleMutation.isLoading ||
    deleteQuoteRule.isLoading;
  const showEmptyScreen =
    !isLoading && isEmpty(searchQuery) && quoteRuleList.length === 0;
  const disableControls = isLoading || isScrollLoading.current;
  const anyLoaderActive = isLoading && !isScrollLoading.current;

  const renderContent = () => {
    if (initialLoading.current) {
      return (
        <EverLoader
          indicatorType="spinner"
          className="flex h-full"
          wrapperClassName="z-20"
          spinning
        />
      );
    }

    if (showEmptyScreen) {
      return (
        <div className="h-full px-6">
          <EmptyListScreen
            title="No rules have been set up yet"
            description="Create and configure new quote rule."
            image={createQuoteRule}
            buttonText="Create New Rule"
            buttonOnClick={() => setIsCreateRuleModalOpen(true)}
          />
        </div>
      );
    }

    return (
      <>
        <div
          className={twMerge(
            "flex items-center justify-between px-6 pb-4 z-10",
            isContentScrolled && "shadow-[0_16px_10px_-16px_#a5a5a5]"
          )}
        >
          <EverInput.Search
            allowClear
            className="w-80 [&_.ant-input-affix-wrapper]:!px-3"
            placeholder="Search"
            value={userInput}
            onChange={onSearch}
            size="small"
            disabled={disableControls}
            {...(isEmpty(userInput)
              ? {
                  enterButton: (
                    <SearchIcon className="w-4 h-4 text-ever-base-content-low" />
                  ),
                }
              : {
                  enterButton: null,
                })}
          />
          <EverButton
            size="small"
            prependIcon={<PlusCircleIcon className="w-4 h-4" />}
            onClick={() => setIsCreateRuleModalOpen(true)}
            disabled={disableControls}
          >
            Create New Rule
          </EverButton>
        </div>
        <EverLoader
          indicatorType="spinner"
          className="flex flex-col overflow-auto"
          wrapperClassName="z-20"
          spinning={anyLoaderActive}
        >
          {!isEmpty(searchQuery) && quoteRuleList.length === 0 ? (
            <div className="flex items-center justify-center h-full p-6">
              <EverTg.Heading2 className="text-ever-base-content-low">
                No quote forms found
              </EverTg.Heading2>
            </div>
          ) : (
            <div
              ref={scrollContainerRef}
              className="flex flex-col gap-4 h-full px-6 py-2 mb-4 overflow-auto"
              onScroll={handleScroll}
            >
              {quoteRuleList.map((quoteRule) => (
                <QuoteRuleCard
                  key={quoteRule.form_rule_id}
                  quoteRule={quoteRule}
                  onClone={createQuoteRuleMutation.mutate}
                  onUpdate={updateQuoteRule.mutate}
                  onDelete={deleteQuoteRule.mutate}
                  isLoading={deleteQuoteRule.isLoading || getQuoteRulesLoading}
                />
              ))}
              {isScrollLoading.current && getQuoteRulesLoading && (
                <div className="flex items-center justify-center h-full">
                  <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-10 [&>svg]:!h-10" />
                </div>
              )}
            </div>
          )}
        </EverLoader>
      </>
    );
  };

  return (
    <div className="flex flex-col h-full py-1">
      <CreateQuoteRuleModal
        visible={isCreateRuleModalOpen}
        onClose={() => setIsCreateRuleModalOpen(false)}
        onSubmit={createQuoteRuleMutation.mutate}
        loading={createQuoteRuleMutation.isLoading}
      />
      <div className="flex flex-col h-full">{renderContent()}</div>
    </div>
  );
};

export default QuoteRuleList;
