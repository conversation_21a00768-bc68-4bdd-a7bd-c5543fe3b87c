/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import { ChevronDownArrowIcon } from "@everstage/evericons/solid";
import { isNil } from "lodash";
import { twMerge } from "tailwind-merge";

import { EverInput } from "~/v2/components";
import { InputGroup } from "~/v2/features/cpq/components";
import { useFormattedNumberField } from "~/v2/features/cpq/hooks";

const NumberField = ({ field, fieldData = {}, properties, loading }) => {
  const { placeholder } = fieldData;
  const {
    prefix = null,
    suffix = null,
    min = null,
    max = null,
    step = null,
    is_read_only = false,
    is_percentage = false,
    hide_min_max = false,
  } = properties;
  const isGroupedInput = !!prefix || !!suffix || !isNil(min) || !isNil(max);
  const hasStep = !isNil(step);

  const {
    inputRef,
    formatter,
    parser,
    onStepChange,
    onChange,
    onClick,
    onKeyUp,
    onBlur,
  } = useFormattedNumberField({
    value: field.value,
    onChange: field.onChange,
    properties: {
      ...properties,
      innerSuffix: is_percentage ? "%" : "",
    },
  });

  const inputProps = {
    ...field,
    ...(isGroupedInput ? { size: "small" } : {}),
    ...(!isNil(min) && { min }),
    ...(!isNil(max) && { max }),
    placeholder,
    step: step || 0,
    className: twMerge(
      "w-full [&>.ant-input-number-handler-wrap]:!hidden",
      prefix && "[&_input]:rounded-l-none",
      suffix && "[&_input]:rounded-r-none",
      hasStep && "!pr-8"
    ),
    // ...((format || is_percentage) && {
    //   formatter: (value) => formatter(value, properties),
    //   parser: (newValue) => parser(newValue, field.value, properties),
    // }),
    formatter,
    parser,
    onChange,
    onClick,
    onKeyUp,
    onBlur: () => {
      onBlur();
      field?.onBlur?.();
    },
  };

  const numberInput = (
    <div className="relative w-full">
      <EverInput.Number ref={inputRef} {...inputProps} />
      {loading && (
        <LoadingOutlined
          className={twMerge(
            "flex flex-col items-center justify-center absolute top-0 bottom-0 m-auto text-ever-base-content-low text-[14px]",
            hasStep ? "right-8" : "right-3"
          )}
        />
      )}
      {hasStep && (
        <Stepper onChange={onStepChange} hasBorder={!suffix && isNil(max)} />
      )}
    </div>
  );

  return isGroupedInput ? (
    <InputGroup prefix={prefix} suffix={suffix} disabled={is_read_only}>
      {hide_min_max ? <></> : <MinMaxLabel value={min} isMin />}
      {numberInput}
      {hide_min_max ? <></> : <MinMaxLabel value={max} />}
    </InputGroup>
  ) : (
    numberInput
  );
};

export const withCurrencyPrefix = (Component) => {
  const WithCurrencyPrefix = (props) => {
    return (
      <Component {...props} properties={{ ...props.properties, prefix: "$" }} />
    );
  };

  WithCurrencyPrefix.displayName = `withCurrencyPrefix(${Component.name})`;

  return WithCurrencyPrefix;
};

function Stepper({ onChange, hasBorder = false }) {
  return (
    <div
      className={twMerge(
        "flex flex-col items-center justify-center absolute top-0 bottom-0 right-0 m-auto w-8 h-full cursor-pointer",
        hasBorder && "border-0 border-l border-solid border-ever-base-400"
      )}
    >
      <div
        className={twMerge(
          "w-full flex items-center justify-center h-1/3",
          hasBorder && "border-0 border-b border-solid border-ever-base-400"
        )}
        onClick={() => onChange(true)}
      >
        <ChevronDownArrowIcon className="w-5 h-5 rotate-180 text-ever-base-content-mid" />
      </div>
      <div
        className="w-full flex items-center justify-center h-1/3"
        onClick={() => onChange(false)}
      >
        <ChevronDownArrowIcon className="w-5 h-5 text-ever-base-content-mid" />
      </div>
    </div>
  );
}

function MinMaxLabel({ value, isMin = false }) {
  if (isNil(value)) {
    return <></>;
  }

  return (
    <div
      className={twMerge(
        "!flex items-center gap-2 text-ever-base-content-mid leading-none",
        isMin ? "flex-row-reverse !ml-2" : "!mr-2"
      )}
    >
      <span>&lt;</span>
      <span className="border border-solid border-ever-base-200 bg-ever-base-100 shadow-sm rounded py-1 px-2">
        {value}
      </span>
    </div>
  );
}

export default NumberField;
