/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { useRef } from "react";

import { EverSelect, EverCheckbox } from "~/v2/components";
import { InputGroup } from "~/v2/features/cpq/components";
import { FIELD_TYPE } from "~/v2/features/cpq/constants";

const SelectField = ({ field, fieldData, properties, loading }) => {
  const selectFieldRef = useRef(null);

  const { field_type, options = [], placeholder } = fieldData;
  const { prefix = null, suffix = null, is_read_only = false } = properties;
  const hasPrefixOrSuffix = prefix || suffix;
  const isMultiSelect = field_type === FIELD_TYPE.MULTI_SELECT;

  const handleChange = (value) => {
    selectFieldRef.current?.blur?.();
    field.onChange(value ?? (isMultiSelect ? [] : null));
  };

  const renderSelectComponent = (width) => (
    <EverSelect
      {...field}
      {...(hasPrefixOrSuffix && { size: "small" })}
      {...(isMultiSelect && { mode: "multiple", menuItemSelectedIcon: false })}
      ref={selectFieldRef}
      value={field.value === "" ? null : field.value}
      placeholder={placeholder || "Select"}
      optionLabelProp="title"
      onChange={handleChange}
      dropdownMatchSelectWidth={width}
      dropdownStyle={{ maxWidth: "max-content" }}
      getPopupContainer={() =>
        document.getElementById(`field_${field_type}_${fieldData.id}`)
      }
      showSearch
      allowClear
      suffixIcon={
        loading ? (
          <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
        ) : (
          <div className="h-full w-10 flex items-center justify-center">
            <ChevronDownIcon className="w-4 h-4" name="suffix" />
          </div>
        )
      }
    >
      {options.map((option) => (
        <EverSelect.Option
          key={option.value}
          value={option.value}
          title={option.label}
        >
          <div className="flex items-center gap-3 leading-5">
            {isMultiSelect && (
              <EverCheckbox
                className="h-max"
                checked={field.value.includes(option.value)}
              />
            )}
            {option.label}
          </div>
        </EverSelect.Option>
      ))}
    </EverSelect>
  );

  return hasPrefixOrSuffix ? (
    <InputGroup prefix={prefix} suffix={suffix} disabled={is_read_only}>
      {renderSelectComponent}
    </InputGroup>
  ) : (
    renderSelectComponent(true)
  );
};

export default SelectField;
