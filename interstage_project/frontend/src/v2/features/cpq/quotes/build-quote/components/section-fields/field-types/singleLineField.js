/* eslint-disable no-restricted-imports */
import { LoadingOutlined } from "@ant-design/icons";

import { EverInput } from "~/v2/components";
import { InputGroup } from "~/v2/features/cpq/components";

const SingleLineField = ({ field, fieldData, properties, loading }) => {
  const { placeholder } = fieldData;
  const { prefix = null, suffix = null, is_read_only = false } = properties;
  const hasPrefixOrSuffix = prefix || suffix;

  const inputProps = {
    ...field,
    ...(hasPrefixOrSuffix && { size: "small" }),
    placeholder,
    suffix: loading && (
      <LoadingOutlined className="text-ever-base-content-low [&>svg]:!w-[16px] [&>svg]:!h-[16px]" />
    ),
  };

  return hasPrefixOrSuffix ? (
    <InputGroup prefix={prefix} suffix={suffix} disabled={is_read_only}>
      <EverInput {...inputProps} className="shrink" />
    </InputGroup>
  ) : (
    <EverInput {...inputProps} />
  );
};

export default SingleLineField;
