import {
  addDays,
  subDays,
  addMonths,
  differenceInDays,
  differenceInMonths,
  isValid,
  isAfter,
} from "date-fns";
import { cloneDeep, isEqual, isNil, isEmpty } from "lodash";
import { useState, useCallback, useRef, useMemo } from "react";
import { useQuery, useMutation } from "react-query";
import { v4 as uuidv4 } from "uuid";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { DURATION_TYPES } from "~/v2/features/cpq/constants";
import { useBuildQuote } from "~/v2/features/cpq/quotes/build-quote/hooks";
import {
  showQuoteLineItems,
  updateSubscriptionPhases,
  updateSubscriptionDetails,
} from "~/v2/features/cpq/quotes/build-quote/services/restApi";
import {
  getIsoDateString,
  formatDateToIsoDate,
  createDateObject,
} from "~/v2/features/cpq/quotes/build-quote/utils";

import { getSubscriptionEndDate } from "../Header";

const getDurationLabel = (phaseDates, prevPhase) => {
  let durationLabel = "";
  if (
    !isEmpty(prevPhase?.duration) &&
    prevPhase.start_date === phaseDates.start_date &&
    prevPhase.end_date === phaseDates.end_date
  ) {
    durationLabel = prevPhase.duration;
  } else if (isNil(phaseDates.end_date)) {
    durationLabel = "Forever";
  } else {
    const startDate = createDateObject(phaseDates.start_date);
    const endDate = addDays(createDateObject(phaseDates.end_date), 1);
    const months = differenceInMonths(endDate, startDate);
    const adjustedStartDate = addMonths(startDate, months); // Add the calculated months to the start date
    const days = differenceInDays(endDate, adjustedStartDate); // Calculate the remaining days after accounting for full months

    durationLabel = `${months > 0 ? `${months}m` : ""}${
      days > 0 ? ` ${days}d` : ""
    }`;
  }
  return durationLabel;
};

const initialSubscriptionData = () => {
  const duration = {
    duration_type: DURATION_TYPES.YEARS,
    duration_value: 1,
  };
  const startDate = formatDateToIsoDate(new Date());
  return {
    start_date: startDate,
    end_date: getSubscriptionEndDate(startDate, duration),
    ...duration,
  };
};

const normalizePhaseDates = (phase, prevPhase) => {
  const phaseDates = {
    start_date:
      getIsoDateString(phase.start_date) || formatDateToIsoDate(new Date()),
    end_date: getIsoDateString(phase.end_date),
  };
  const duration = getDurationLabel(phaseDates, prevPhase);
  return {
    ...phase,
    ...phaseDates,
    row_data: phase.row_data.map((row) => ({
      ...row,
      ...phaseDates,
      duration,
    })),
  };
};

const normalizePhases = (phases, phasesMap) => {
  return phases.map((phase) =>
    normalizePhaseDates(phase, phasesMap.get(phase.phase_id))
  );
};

const useSelectProducts = () => {
  const [phases, setPhases] = useState([]);
  const [skuPricePoint, setSkuPricePoint] = useState({});
  const [summary, setSummary] = useState({});
  const [qliLoading, setQliLoading] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(() =>
    initialSubscriptionData()
  );

  const isInitialLoad = useRef(true);
  const prevStartDate = useRef(subscriptionData.start_date);
  const prevEndDate = useRef(subscriptionData.end_date);
  const prevDurationType = useRef(subscriptionData.duration_type);
  const {
    quoteId,
    setIsAutoSaveLoading,
    updateLastSavedTime,
    updateTableField,
    refetchApprovals,
  } = useBuildQuote();
  const { accessToken } = useAuthStore();

  const phasesMap = useMemo(() => {
    return new Map(phases.map((phase) => [phase.phase_id, phase]));
  }, [phases]);

  const setInitialData = (data) => {
    const newSubscriptionData =
      data.duration_type === null
        ? {
            ...subscriptionData,
          }
        : {
            start_date:
              getIsoDateString(data.start_date) ||
              formatDateToIsoDate(new Date()),
            end_date: getIsoDateString(data.end_date),
            duration_type: data.duration_type,
            duration_value: data.duration_value,
          };
    const phases =
      data.phases.length > 0
        ? normalizePhases(data.phases, phasesMap)
        : [
            {
              phase_id: uuidv4(),
              phase_name: "Phase",
              start_date: newSubscriptionData.start_date,
              end_date: newSubscriptionData.end_date,
              row_data: [],
            },
          ];

    setPhases(phases);
    setSkuPricePoint(data.sku_pricepoint_dict);
    setSummary(data.summary);
    setSubscriptionData(newSubscriptionData);

    setTimeout(() => {
      isInitialLoad.current = false;
    }, 1000);
    prevStartDate.current = newSubscriptionData.start_date;
    prevEndDate.current = newSubscriptionData.end_date;
    prevDurationType.current = data.duration_type;
  };

  const quoteLineItems = useQuery(
    ["showQuoteLineItems"],
    () =>
      showQuoteLineItems(
        {
          quote_id: quoteId,
          pricebook_id: "65863636-24a1-4d52-9a40-dcc206de7018",
          known_price_factors: {},
        },
        accessToken
      ),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: setInitialData,
    }
  );

  const updateSubscription = useMutation(
    (body) => updateSubscriptionDetails(body, accessToken),
    {
      onMutate: () => {
        setIsAutoSaveLoading(true);
      },
      onSuccess: (data, variables) => {
        updateLastSavedTime();
        setSummary(data.summary);
        if (data?.phases?.length > 0) {
          setPhases(normalizePhases(data.phases, phasesMap));
        } else {
          setPhases(
            phases.map((phase) => ({
              ...phase,
              start_date: variables?.start_date,
              end_date: variables?.end_date,
            }))
          );
        }
        refetchApprovals();
      },
    }
  );

  // TODO: Show loading overlay
  const updatePhases = useMutation(
    (body) => updateSubscriptionPhases(body, accessToken),
    {
      onMutate: () => {
        setIsAutoSaveLoading(true);
      },
      onSuccess: (data) => {
        const { phases, summary } = data;
        updateTableField(phases?.length > 0);

        // If there are no phases left, add a new phase with empty data
        if (phases.length === 0) {
          const newPhase = createNewPhase(
            {},
            subscriptionData.start_date,
            subscriptionData.end_date
          );
          phases.push(newPhase);
        }
        updateLastSavedTime();
        setPhases(normalizePhases(phases, phasesMap));
        setSummary(summary);
      },
    }
  );

  const createNewPhase = useCallback((phaseToClone, startDate, endDate) => {
    const phaseDetails = {
      phase_id: uuidv4(),
      phase_name: phaseToClone.phase_name
        ? `Copy of ${phaseToClone.phase_name}`
        : "Phase",
      start_date: startDate,
      end_date: endDate,
    };

    return {
      ...phaseDetails,
      sub_total: phaseToClone.sub_total ?? 0,
      row_data: (phaseToClone.row_data ?? []).map((row) => ({
        ...row,
        ...phaseDetails,
      })),
    };
  }, []);

  /**
   * Handles the addition of a new subscription phase by adjusting the dates of existing phases.
   * - If the duration_type is "forever":
   *   - Sets the end date of the last phase to match its start date.
   *   - Creates a new phase that starts the day after the last phase's end date.
   * - For other durations:
   *   - Creates a new phase using the last phase's end date.
   *   - Adjusts the end dates of the existing phases, moving backward from the last phase.
   */
  const addPhase = useCallback(() => {
    let newPhase;
    const modifiedPhases = [];
    const existingPhases = [...phases]; // Create a copy of the existing phases
    const lastPhase = existingPhases.at(-1); // Get the last phase in the list

    // Check if the subscription duration_type is "forever"
    if (subscriptionData.duration_type === DURATION_TYPES.FOREVER) {
      // Handle "forever" duration_type

      // Update the last phase
      const lastPhaseEndDate = lastPhase.start_date; // Set the end date of the last phase to its start date
      const updatedLastPhase = {
        ...lastPhase,
        end_date: lastPhaseEndDate,
        row_data: lastPhase.row_data.map((row) => ({
          ...row,
          end_date: lastPhaseEndDate,
        })),
      };
      existingPhases[existingPhases.length - 1] = updatedLastPhase;
      modifiedPhases.push(updatedLastPhase); // Store the modified phase

      // Create a new phase using the last phase
      const newPhaseStartDate = addDays(createDateObject(lastPhaseEndDate), 1);
      newPhase = createNewPhase(
        updatedLastPhase,
        formatDateToIsoDate(newPhaseStartDate), // Start date is the day after the last phase's end date
        null // No end date for the new phase since it's "forever"
      );
    } else {
      // Handle other durations

      // Create a new phase using the last phase
      newPhase = createNewPhase(
        lastPhase,
        lastPhase.end_date, // Start date is the same as the last phase's end date
        lastPhase.end_date // End date is initially the same as the start date
      );

      // Adjust the dates of the existing phases starting from the last phase and moving backward
      for (let i = existingPhases.length - 1; i >= 0; i--) {
        const phase = existingPhases[i];
        const newEndDate = subDays(createDateObject(phase.end_date), 1); // Subtract one day from the end date

        // Check if the start date is after the new end date.
        const isStartDateAfterEndDate = isAfter(
          createDateObject(phase.start_date),
          newEndDate
        );
        const modifiedDates = {
          end_date: formatDateToIsoDate(newEndDate),
          start_date: isStartDateAfterEndDate
            ? formatDateToIsoDate(newEndDate)
            : phase.start_date,
        };

        // Update the phase with the new dates
        const updatedPhase = {
          ...phase,
          ...modifiedDates,
          row_data: phase.row_data.map((row) => ({
            ...row,
            ...modifiedDates,
          })),
        };
        existingPhases[i] = updatedPhase;
        modifiedPhases.push(updatedPhase); // Store the modified phase

        // If the start date is valid and does not need further adjustments, break the loop.
        if (!isStartDateAfterEndDate) {
          break;
        }
      }
    }

    updatePhases.mutate({
      type: "add",
      quote_id: quoteId,
      existing_phases: modifiedPhases,
      new_phase: newPhase,
    });
  }, [
    quoteId,
    subscriptionData.duration_type,
    phases,
    createNewPhase,
    updatePhases,
  ]);

  // Function to remove a phase
  const removePhase = useCallback(
    (index) => {
      const removedPhaseId = phases[index].phase_id;
      const modifiedPhases = [];

      // Update adjacent phases to handle the removed phase
      if (phases.length > 1) {
        if (index === 0) {
          // Update the next phase's start date
          const nextPhase = phases[index + 1];
          const newStartDate = phases[index].start_date;
          nextPhase.start_date = newStartDate;
          nextPhase.row_data = nextPhase.row_data.map((row) => ({
            ...row,
            start_date: newStartDate,
          }));
          modifiedPhases.push(nextPhase);
        } else {
          // Update the previous phase's end date
          const previousPhase = phases[index - 1];
          const newEndDate = phases[index].end_date;
          previousPhase.end_date = newEndDate;
          previousPhase.row_data = previousPhase.row_data.map((row) => ({
            ...row,
            end_date: newEndDate,
          }));
          modifiedPhases.push(previousPhase);
        }
      }

      return updatePhases.mutateAsync({
        type: "remove",
        quote_id: quoteId,
        removed_phase_id: removedPhaseId,
        existing_phases: modifiedPhases,
      });
    },
    [quoteId, phases, updatePhases]
  );

  // Function to update an existing phase
  const updatePhase = useCallback((phases) => {
    if (Array.isArray(phases)) {
      setPhases((prevPhases) =>
        prevPhases.map((prevPhase) => {
          const phase = phases.find(
            (phase) => phase.phase_id === prevPhase.phase_id
          );
          return phase
            ? normalizePhaseDates(phase, phasesMap.get(phase.phase_id))
            : prevPhase;
        })
      );
    } else {
      setPhases((prevPhases) =>
        prevPhases.map((prevPhase) =>
          prevPhase.phase_id === phases.phase_id
            ? normalizePhaseDates(phases, phasesMap.get(phases.phase_id))
            : prevPhase
        )
      );
    }
  }, []);

  const setNewPhases = useCallback((phases) => {
    setPhases(
      phases.map((phase) =>
        normalizePhaseDates(phase, phasesMap.get(phase.phase_id))
      )
    );
  }, []);

  const updatePricePointMap = useCallback((pricePoint) => {
    setSkuPricePoint((prevPricePoint) => ({
      ...prevPricePoint,
      ...pricePoint,
    }));
  }, []);

  const cloneProductsToPhases = useCallback((phaseIds, products) => {
    setPhases((prevPhases) =>
      prevPhases.map((phase) => {
        if (phaseIds.includes(phase.phase_id)) {
          const existingProductIds = new Set(
            phase.row_data.map((row) => row.sku)
          );
          const newProducts = [];
          for (const product of products) {
            if (!existingProductIds.has(product.sku)) {
              newProducts.push({
                ...product,
                phase_id: phase.phase_id,
              });
            }
          }

          return {
            ...phase,
            row_data: [...phase.row_data, ...newProducts],
          };
        }
        return phase;
      })
    );
  }, []);

  const endDateChange = useCallback(
    (index, date) => {
      const updatedPhases = cloneDeep(phases);

      const currentPhaseEndDate = formatDateToIsoDate(date);
      updatedPhases[index] = {
        ...updatedPhases[index],
        end_date: currentPhaseEndDate,
        row_data: updatedPhases[index].row_data.map((row) => ({
          ...row,
          end_date: currentPhaseEndDate,
        })),
      };

      const nextPhaseStartDate = formatDateToIsoDate(addDays(date, 1));
      updatedPhases[index + 1] = {
        ...updatedPhases[index + 1],
        start_date: nextPhaseStartDate,
        row_data: updatedPhases[index + 1].row_data.map((row) => ({
          ...row,
          start_date: nextPhaseStartDate,
        })),
      };

      updatePhases.mutate({
        type: "update",
        quote_id: quoteId,
        existing_phases: [updatedPhases[index], updatedPhases[index + 1]],
      });
    },
    [quoteId, phases, updatePhases]
  );

  const onUpdateSubscription = (subscriptionData) => {
    const newPhases = computeNewPhases(subscriptionData);
    updateSubscription.mutate({
      ...subscriptionData,
      quote_id: quoteId,
      phases: newPhases,
    });
  };

  const onSubscriptionDataChange = (subscriptionDetails = {}) => {
    const newSubscriptionData = {
      ...subscriptionData,
      ...subscriptionDetails,
    };
    setSubscriptionData(newSubscriptionData);
    onUpdateSubscription(newSubscriptionData);
  };

  const adjustEndDate = (newPhases, subscriptionData) => {
    for (let i = newPhases.length - 1; i >= 0; i--) {
      let phase = newPhases[i];
      const newEndDate =
        i === newPhases.length - 1
          ? createDateObject(subscriptionData.end_date)
          : subDays(createDateObject(newPhases[i + 1].start_date), 1);

      // Check if the start date is after the new end date.
      const isStartDateAfterEndDate = isAfter(
        createDateObject(phase.start_date),
        newEndDate
      );
      const modifiedDates = {
        end_date: formatDateToIsoDate(newEndDate),
        start_date: isStartDateAfterEndDate
          ? formatDateToIsoDate(newEndDate)
          : phase.start_date,
      };

      // Update the phase with the new dates
      phase = {
        ...phase,
        ...modifiedDates,
        row_data: phase.row_data.map((row) => ({
          ...row,
          ...modifiedDates,
        })),
      };
      newPhases[i] = phase;

      // If the start date is valid and does not need further adjustments, break the loop.
      if (!isStartDateAfterEndDate) {
        break;
      }
    }
    return newPhases;
  };

  const computeNewPhases = (subscriptionData) => {
    let newPhases = cloneDeep(phases);

    if (
      /**
       * If the subscriptionStartDate changes, it adjusts the start and end dates of each phase.
       *
       * 1. Calculate the difference in days between the previous start date and the current start date.
       * 2. If the difference is positive, shift the phase dates forward by that number of days.
       * 3. If the difference is negative, shift the phase dates backward by that number of days.
       * 4. Adjust the row_data for each phase to reflect the new start and end dates.
       */
      !isEqual(prevStartDate.current, subscriptionData.start_date)
    ) {
      const diffInDays = differenceInDays(
        createDateObject(subscriptionData.start_date),
        createDateObject(prevStartDate.current)
      );

      // addDays => Negative diffInDays will perform subtraction
      newPhases = phases.map((phase) => {
        const startDate = addDays(
          createDateObject(phase.start_date),
          diffInDays
        );
        const endDateObj = createDateObject(phase.end_date);
        const endDate =
          phase.end_date === null || !isValid(endDateObj)
            ? null
            : addDays(endDateObj, diffInDays);
        const modifiedDates = {
          start_date: formatDateToIsoDate(startDate),
          end_date: formatDateToIsoDate(endDate),
        };
        return {
          ...phase,
          ...modifiedDates,
          row_data: phase.row_data.map((row) => ({
            ...row,
            ...modifiedDates,
          })),
        };
      });

      // Adjust end date if the modified end date mis-match with new end date
      if (
        !isEqual(
          subscriptionData.end_date,
          newPhases[newPhases.length - 1].end_date
        )
      ) {
        newPhases = adjustEndDate(newPhases, subscriptionData);
      }
    } else if (
      /**
       * If the subscriptionEndDate changes, it updates the end dates for each phase and adjusts the start dates accordingly.
       *
       * 1. If it's the last phase, set the end date to subscriptionEndDate.
       * 2. For other phases, set the end date to the day before the next phase's start date.
       * 3. If the start date exceeds the end date for a phase, adjust the start date to match the end date.
       * 4. Adjust the row_data for each phase to reflect the new start and end dates.
       */
      !isEqual(prevEndDate.current, subscriptionData.end_date) &&
      (prevEndDate.current !== null || subscriptionData.end_date !== null)
    ) {
      // If start date hasn't changed, check if end date has changed
      newPhases = adjustEndDate(newPhases, subscriptionData);
    }
    prevStartDate.current = subscriptionData.start_date;
    prevEndDate.current = subscriptionData.end_date;

    return newPhases;
  };

  return {
    isLoading: quoteLineItems.isLoading,
    phases,
    isInitialLoad: isInitialLoad.current,
    skuPricePoint,
    summary,
    subscriptionData,
    updateSubscription,
    updatePhases,
    qliLoading,
    setQliLoading,
    setSummary,
    onSubscriptionDataChange,
    onAddPhase: addPhase,
    onUpdatePhase: updatePhase,
    onRemovePhase: removePhase,
    onUpdatePricePointMap: updatePricePointMap,
    onCloneProductsToPhases: cloneProductsToPhases,
    onEndDateChange: endDateChange,
    setNewPhases,
  };
};

export default useSelectProducts;
