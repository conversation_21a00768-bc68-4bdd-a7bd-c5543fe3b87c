import { Trash03Icon } from "@everstage/evericons/outlined";

import theme from "~/v2/themes/blueBias23";

const TAG_TYPE = {
  SELECT_PRODUCT: "select_product",
};

const DISCOUNT_TYPES = {
  PERCENTAGE: "percentage",
  PRICE: "price",
};

const DISCOUNT_IN_OPTIONS = [
  { value: DISCOUNT_TYPES.PERCENTAGE, label: "Percentage" },
  // { value: DISCOUNT_TYPES.PRICE, label: "Price" },
];

const DISCOUNT_RANGE = {
  MIN: 0,
  MAX: 100,
};

const PHASE_ACTIONS = {
  DELETE: "delete",
};

const PHASE_MENU_OPTIONS = [
  {
    key: PHASE_ACTIONS.DELETE,
    icon: <Trash03Icon className="w-4 h-4 text-ever-error-hover" />,
    label: "Remove",
  },
];

// TODO: Change the colors which is not available in design system
const PROGRESS_BG = {
  "0%": "#" + "A3D0FF",
  "100%": theme.colors.ever.chartColors[1],
};

const SECTION_PROGRESS_BG = {
  selected: {
    trailColor: "#" + "E1E8FD",
    strokeColor: {
      "0%": theme.colors.ever.chartColors[1],
      "100%": theme.colors.ever.chartColors[19],
    },
  },
  unselected: {
    trailColor: theme.colors.ever.base[400],
    strokeColor: {
      "0%": "#" + "414449",
      "100%": theme.colors.ever.base.content.low,
    },
  },
};

export {
  PROGRESS_BG,
  SECTION_PROGRESS_BG,
  DISCOUNT_TYPES,
  DISCOUNT_IN_OPTIONS,
  DISCOUNT_RANGE,
  PHASE_ACTIONS,
  PHASE_MENU_OPTIONS,
  TAG_TYPE,
};
