import { useQuery, gql } from "@apollo/client";
import { get, isEmpty, intersection } from "lodash";
import {
  useRef,
  createContext,
  useContext,
  useMemo,
  useState,
  useEffect,
} from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { useMutation, useQuery as useReactQuery } from "react-query";
import { useNavigate, useSearchParams, useLocation } from "react-router-dom";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useAbortiveLazyRESTQuery, message } from "~/v2/components";
import { TAG_TYPE } from "~/v2/features/cpq/quotes/build-quote/constants";
import {
  executeRuleEvaluation,
  executeSubmitForm,
  executeValidateAndPublishForm,
  getApprovalsForQuote,
  getQuoteLineItemsForPdf,
  executeExitBuildQuote,
  changeQuoteStatus,
} from "~/v2/features/cpq/quotes/build-quote/services/restApi";
import { getFormValues } from "~/v2/features/cpq/quotes/build-quote/utils";
import { STATUS_TYPES } from "~/v2/features/cpq/quotes/quotes-list/utils";

const GET_All_COUNTRIES = gql`
  {
    allActiveCountries {
      currencyCode
      currencySymbol
    }
  }
`;

const useQuoteForm = ({
  quoteId,
  initialData,
  refreshQuoteDetails,
  closeDrawer,
}) => {
  const navigate = useNavigate();
  const lastInputChangeTime = useRef(null);
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { accessToken } = useAuthStore();
  const { getValues, reset, setValue, setError, clearErrors, control } =
    useFormContext();
  // Field 2 is a quote name field
  const watchedQuoteName = useWatch({ control, name: "field2" });

  const formId = initialData.form_id;
  const formBuilderId = initialData.form_builder_id;
  const [isEditMode, setIsEditMode] = useState(
    initialData.quote_status === STATUS_TYPES.DRAFT ||
      location.state?.isEditMode ||
      false
  );
  const [formSpec, setFormSpec] = useState(initialData.form_spec);
  const [activeSectionKey, setActiveSectionKey] = useState(
    searchParams.get("section") ||
      formSpec.section_order.find((order) => isSectionVisible(order))
  );
  const [basicDetails, setBasicDetails] = useState({});
  const [sourceRuleFields, setSourceRuleFields] = useState(
    initialData.source_rule_fields
  );
  const [dependentFieldMap, setDependentFieldMap] = useState(
    initialData.dependent_fields
  );
  const [ruleEvaluatingFields, setRuleEvaluatingFields] = useState(new Set());
  const [isPolling, setIsPolling] = useState(false);
  const [currencyCodeSymbolMap, setCurrencyCodeSymbolMap] = useState({});
  const [approvalCycles, setApprovalCycles] = useState([]);
  const [lastSavedTime, setLastSavedTime] = useState(
    initialData.last_updated_at
  );
  const [isAutoSaveLoading, setIsAutoSaveLoading] = useState(false);
  const [showApprovalsModal, setShowApprovalsModal] = useState(false);

  const [pdfData, setPdfData] = useState({});

  const selectProductFieldId = useMemo(() => {
    const selectProductSection = Object.values(formSpec.sections).find(
      (section) => section.tag === TAG_TYPE.SELECT_PRODUCT
    );
    const selectProductFields = Object.values(
      get(selectProductSection, "fields", {})
    ).find((field) => field.tag === TAG_TYPE.SELECT_PRODUCT);

    return get(selectProductFields, "id", "");
  }, [formSpec]);

  useQuery(GET_All_COUNTRIES, {
    onCompleted: (data) => {
      if (data) {
        let currencySymbolMap = {};
        for (const country of data.allActiveCountries) {
          currencySymbolMap[country.currencyCode] = country.currencySymbol;
        }
        setCurrencyCodeSymbolMap(currencySymbolMap);
      }
    },
  });

  const evaluateRule = useMutation(
    (payload) => executeRuleEvaluation(payload, accessToken),
    {
      onMutate: () => {
        setIsAutoSaveLoading(true);
      },
      onSuccess: (data, variables) => {
        const evaluatedField = variables.changed_field;
        const formValues = getFormValues(data);
        const fieldsToUpdate = {};

        for (const fieldId in dependentFieldMap) {
          if (dependentFieldMap[fieldId].includes(evaluatedField)) {
            fieldsToUpdate[fieldId] = formValues[fieldId];
          }
        }

        setFormSpec(data);
        if (data?.approval_cycles && data?.approval_cycles.length > 0) {
          setApprovalCycles(data.approval_cycles);
        } else {
          setApprovalCycles([]);
        }

        reset(
          { ...getValues(), ...fieldsToUpdate },
          {
            keepErrors: true,
            keepDirty: true,
            keepDefaultValues: true,
            keepIsSubmitted: true,
            keepTouched: true,
            keepIsValid: true,
            keepSubmitCount: true,
          }
        );
        clearErrors(Object.keys(fieldsToUpdate));
        setBasicDetails({
          quoteName: formValues.field2 || initialData.quote_name,
          opportunity: formValues.field3,
          account: formValues.field5,
          ownerId: initialData.quote_owner_id,
          ownerName: initialData.quote_owner,
          quoteTotal: initialData.quote_total,
        });
      },
      onError: () => {
        console.log("error");
      },
      onSettled: (_, __, variables) => {
        setRuleEvaluatingFields((prev) => {
          prev.delete(variables.changed_field);
          return prev;
        });
      },
    }
  );

  const publishForm = useMutation(
    (payload) => executeSubmitForm(payload, accessToken),
    {
      onMutate: () => {
        message.loading("Publishing quote...");
      },
      onSuccess: (data) => {
        if (!isEmpty(data?.errors)) {
          setShowApprovalsModal(false);
          message.error("Unable to publish quote");
        } else {
          message.success("Quote was published successfully");
          refreshQuoteDetails();
          setIsEditMode(false);
        }
      },
    }
  );

  const validateAndPublishForm = useMutation(
    (payload) => executeValidateAndPublishForm(payload, accessToken),
    {
      onMutate: () => {
        message.loading("Validating and publishing quote...");
      },
      onSuccess: (data) => {
        if (data?.approval_cycles && data?.approval_cycles.length > 0) {
          setApprovalCycles(data.approval_cycles);
          setShowApprovalsModal(true);
          message.dismiss();
        } else if (!isEmpty(data?.errors)) {
          Object.keys(data.errors).forEach((error) => {
            setError(error, {
              type: "validate",
              message: data.errors[error],
            });
          });
          message.error(
            "Please fill missing information before publishing the quote!"
          );
        } else if (data?.last_updated_time) {
          refreshQuoteDetails();
          setIsEditMode(false);
          message.success("Quote was published successfully");
        } else {
          message.dismiss();
        }
      },
    }
  );

  const { refetch: refetchApprovals, isFetching: isApprovalsLoading } =
    useReactQuery(
      ["getApprovalCyclesForQuote"],
      () =>
        getApprovalsForQuote(
          {
            form_builder_id: formBuilderId,
            quote_id: quoteId,
            form_data: getValues(),
            form_spec: formSpec,
          },
          accessToken
        ),
      {
        retry: false,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        enabled: isEditMode,
        onSuccess: (data) => {
          if (data?.approval_cycles && data?.approval_cycles.length > 0) {
            setApprovalCycles(data.approval_cycles);
          } else {
            setApprovalCycles([]);
          }
        },
        onError: () => {
          console.log("error while fetching approval cycles for quote");
        },
      }
    );

  const exitBuildQuote = useMutation(
    (payload) => executeExitBuildQuote(payload, accessToken),
    {
      onSuccess: onCloseBuildQuoteSuccess,
      onError: () => {
        console.error("error while exiting build quote");
      },
    }
  );

  const changeQuoteStatusHandler = useMutation(
    (payload) => changeQuoteStatus(payload, accessToken),
    {
      onSuccess: () => {
        refreshQuoteDetails();
      },
    }
  );

  const updateTableField = (hasQuoteLineItems = false) => {
    if (isEmpty(selectProductFieldId)) {
      return;
    }
    setValue(selectProductFieldId, hasQuoteLineItems || null);
    clearErrors(selectProductFieldId);
  };

  const {
    data: tableData,
    refetch: refetchTableData,
    isLoading: isTableDataLoading,
  } = useReactQuery(
    ["getQuoteLineItemsForPdf"],
    () =>
      getQuoteLineItemsForPdf(
        {
          quote_id: quoteId,
          is_edit: isEditMode,
        },
        accessToken
      ),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        updateTableField(get(data, "phases.length", 0) > 0);
      },
    }
  );

  /** Auto Save Start */
  const [
    autoSaveForm,
    { abort: autoSaveFormAbort, loading: autoSaveFormLoading },
  ] = useAbortiveLazyRESTQuery("ninja/cpq/forms/auto_save_form", "POST", {
    onCompleted: updateLastSavedTime,
  });

  const startPolling = () => {
    lastInputChangeTime.current = Date.now();
    setIsPolling(true);
    setIsAutoSaveLoading(true);
  };

  const stopPolling = () => {
    lastInputChangeTime.current = null;
    setIsPolling(false);
  };

  useEffect(() => {
    let autoSaveInterval;
    if (isPolling) {
      autoSaveInterval = setInterval(() => {
        const now = Date.now();
        // Only autosave if 3 seconds have passed since the last change
        if (
          now - lastInputChangeTime.current >= 3000 &&
          ruleEvaluatingFields.size === 0
        ) {
          if (autoSaveFormLoading) {
            autoSaveFormAbort();
          }
          autoSaveForm({
            bodyParams: {
              form_id: formId,
              form_builder_id: formBuilderId,
              form_data: getValues(),
            },
          });

          stopPolling();
          clearInterval(autoSaveInterval);
        }
      }, 3000);
    }

    // Cleanup interval on component unmount
    return () => {
      if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
      }
    };
  }, [
    formId,
    formBuilderId,
    isPolling,
    autoSaveForm,
    autoSaveFormLoading,
    autoSaveFormAbort,
    ruleEvaluatingFields,
    getValues,
  ]);
  /** Auto Save End */

  useEffect(() => {
    if (isEmpty(searchParams.get("section"))) {
      const url = `${location.pathname}?section=${activeSectionKey}`;
      window.history.replaceState(null, "", url);
    }
  }, [searchParams, location.pathname, activeSectionKey]);

  useEffect(() => {
    const formValues = getValues();
    if (formValues.field2 === "") {
      setValue("field2", initialData.quote_name);
    }
  }, [initialData.quote_name, getValues, setValue]);

  useEffect(() => {
    const formValues = getValues();
    setBasicDetails({
      quoteName: watchedQuoteName || initialData.quote_name,
      opportunity: formValues.field3,
      account: formValues.field5,
      ownerId: initialData.quote_owner_id,
      ownerName: initialData.quote_owner,
      quoteTotal: initialData.quote_total,
    });
  }, [
    initialData.quote_name,
    initialData.quote_total,
    watchedQuoteName,
    getValues,
  ]);

  const {
    visibleSectionOrder,
    activeSectionStep,
    totalSteps,
    activeSection,
    activeSectionIndex,
  } = useMemo(() => {
    const visibleSectionIds = [];
    for (const s of Object.values(formSpec.sections)) {
      if (!s.is_hidden) {
        visibleSectionIds.push(s.id);
      }
    }
    const visibleSectionOrder = intersection(
      formSpec.section_order,
      visibleSectionIds
    );
    const activeIndex = visibleSectionOrder.indexOf(activeSectionKey);

    return {
      visibleSectionOrder,
      activeSectionStep: activeIndex + 1,
      totalSteps: visibleSectionOrder.length,
      activeSection: get(formSpec, `sections[${activeSectionKey}]`) || {},
      activeSectionIndex: formSpec.section_order.indexOf(activeSectionKey),
    };
  }, [formSpec.sections, formSpec.section_order, activeSectionKey]);

  function updateLastSavedTime() {
    if (lastInputChangeTime.current === null) {
      setLastSavedTime(new Date());
      setIsAutoSaveLoading(false);
    }
  }

  function isSectionVisible(sectionId) {
    return !get(formSpec, `sections[${sectionId}].is_hidden`, true);
  }

  function onSectionChange(sectionKey) {
    const url = `${location.pathname}?section=${sectionKey}`;
    window.history.pushState(null, "", url);
    setActiveSectionKey((prev) => {
      if (formSpec.sections[prev].tag === TAG_TYPE.SELECT_PRODUCT) {
        refetchTableData();
      }
      return sectionKey;
    });
  }

  function onCloseBuildQuote() {
    const formData = getValues();
    if (formData.field2 === "") {
      formData.field2 = initialData.quote_name;
    }
    exitBuildQuote.mutate({
      quote_id: quoteId,
      form_id: formId,
      form_builder_id: formBuilderId,
      form_data: formData,
    });
  }

  function onCloseBuildQuoteSuccess() {
    closeDrawer();
    navigate("/cpq/quotes");
  }

  return {
    quoteId,
    formId,
    formBuilderId,
    quoteStatus: initialData.quote_status,
    basicDetails,
    isEditMode,
    setIsEditMode,
    formSpec,
    visibleSectionOrder,
    activeSectionKey,
    activeSection,
    activeSectionIndex,
    activeSectionStep,
    totalSteps,
    isAutoSaveLoading,
    setIsAutoSaveLoading,
    lastSavedTime,
    updateLastSavedTime,
    currencyCodeSymbolMap,
    isSelectProductSection: activeSection.tag === TAG_TYPE.SELECT_PRODUCT,
    sourceRuleFields,
    setSourceRuleFields,
    ruleEvaluatingFields,
    setRuleEvaluatingFields,
    dependentFieldMap,
    setDependentFieldMap,
    showApprovalsModal,
    setShowApprovalsModal,
    approvalCycles,
    setApprovalCycles,
    tableData,
    isTableDataLoading,
    refetchTableData,
    evaluateRule,
    publishForm,
    validateAndPublishForm,
    refetchApprovals,
    isApprovalsLoading,
    exitBuildQuote,
    changeQuoteStatusHandler,
    pdfData,
    setPdfData,
    startPolling,
    updateTableField,
    isSectionVisible,
    onSectionChange,
    onCloseBuildQuote,
    onCloseBuildQuoteSuccess,
  };
};

const BuildQuoteContext = createContext(null);

export const BuildQuoteProvider = ({
  children,
  quoteId,
  initialData,
  refreshQuoteDetails,
  closeDrawer,
}) => {
  const value = useQuoteForm({
    quoteId,
    initialData,
    refreshQuoteDetails,
    closeDrawer,
  });
  const memoizedValue = useMemo(() => value, [value]);

  return (
    <BuildQuoteContext.Provider value={memoizedValue}>
      {children}
    </BuildQuoteContext.Provider>
  );
};

export const useBuildQuote = () => {
  return useContext(BuildQuoteContext);
};
