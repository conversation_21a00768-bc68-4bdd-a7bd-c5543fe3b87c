import { isArray, isObject } from "lodash";

const getErrorMessage = (errorMessage) => {
  if (isArray(errorMessage)) {
    return errorMessage.join(", ");
  } else if (isObject(errorMessage)) {
    return Object.values(errorMessage).flat().join(", ");
  } else {
    return errorMessage;
  }
};

const fetchAndParseResponse = async (url, options) => {
  const response = await fetch(url, options);
  const data = await response.json();
  if (!response.ok) {
    if (data?.reason) {
      console.error(data.reason);
    }
    throw new Error(getErrorMessage(data?.reason || ""));
  }
  return data;
};

export const fetchQuoteDetails = async ({ accessToken, quoteId }) => {
  const requestOptions = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  //no status check
  return fetchAndParseResponse(
    `/ninja/cpq/quotes/details/${quoteId}`,
    requestOptions
  );
};

export const executeRuleEvaluation = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    "/ninja/cpq/forms/evaluate_rule",
    requestOptions
  );
};

export const executeSaveLookupValue = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    "/ninja/cpq/forms/save_lookup_value",
    requestOptions
  );
};

export const executeSubmitForm = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse("/ninja/cpq/quotes/publish", requestOptions);
};

export const executeValidateAndPublishForm = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse(
    "/ninja/cpq/quotes/validate-and-publish",
    requestOptions
  );
};

export const getApprovalsForQuote = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  return fetchAndParseResponse(
    "/ninja/cpq/approvals/approval_instances",
    requestOptions
  );
};

export const fetchPriceBookData = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/get_pricebooks_records`,
    requestOptions
  );
};

export const getPdfTemplate = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/get_pdf_template`,
    requestOptions
  );
};

export const getQuoteLineItemsForPdf = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/get_quote_lineitems_for_pdf`,
    requestOptions
  );
};

export const downloadPdf = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return await fetch(
    `ninja/cpq/quote_line_items/download_quote_pdf`,
    requestOptions
  );
};

export const showQuoteLineItems = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/show`,
    requestOptions
  );
};

export const updateSubscriptionDetails = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/subscription_details`,
    requestOptions
  );
};

export const addProductToQuote = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //no status check
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/add_product`,
    requestOptions
  );
};

export const updateSubscriptionPhases = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/update_phases`,
    requestOptions
  );
};

export const updateQuoteLineItems = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse(
    `ninja/cpq/quote_line_items/edit`,
    requestOptions
  );
};

export const fetchApprovalBannerData = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  //has status
  return fetchAndParseResponse(
    `/ninja/cpq/approvals/get_approval_details`,
    requestOptions
  );
};

export const refreshFormObjects = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  return fetchAndParseResponse(
    "/ninja/cpq/forms/refresh_objects",
    requestOptions
  );
};

export const executeExitBuildQuote = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  return fetchAndParseResponse("/ninja/cpq/quotes/exit", requestOptions);
};

export const changeQuoteStatus = async (body, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(body),
  };

  return fetchAndParseResponse(
    "/ninja/cpq/quotes/status/change-status",
    requestOptions
  );
};
