import { format, parse } from "date-fns";
import { isEmpty, get } from "lodash";

import { EverHotToastMessage, toast } from "~/v2/components";
import { FIELD_TYPE } from "~/v2/features/cpq/constants";
import { downloadPdf } from "~/v2/features/cpq/quotes/build-quote/services/restApi";

function getPercent(completed, total) {
  return Math.round((completed / total) * 100);
}

function isValidEmail(string) {
  /**
   * This regex is the optimized version of regex(/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/)
   * which is used in admin-ui for email validation
   */
  const emailRegex = /^[\w.-]+@[\d.A-Za-z-]+\.[A-Za-z]{2,4}$/;
  return emailRegex.test(string);
}

function urlValidation(string) {
  let isValid = false;
  try {
    const newUrl = new URL(string);
    isValid = newUrl.protocol === "http:" || newUrl.protocol === "https:";
  } catch (error) {
    console.error(error);
    isValid = false;
  }

  return isValid || "Invalid URL";
}

function emailValidation(string) {
  return isValidEmail(string) || "Invalid email";
}

function numberValidation(string) {
  return !Number.isNaN(Number(string)) || "Invalid number";
}

const validationByType = {
  [FIELD_TYPE.URL]: urlValidation,
  [FIELD_TYPE.EMAIL]: emailValidation,
  [FIELD_TYPE.NUMBER]: numberValidation,
  [FIELD_TYPE.CURRENCY]: numberValidation,
};

const getRules = (type, { is_mandatory, min, max, min_length, max_length }) => {
  const rules = {
    required: is_mandatory ? "This field is required" : false,
    validate: (value) => {
      if (!is_mandatory && !value) {
        return true;
      }

      return validationByType[type] ? validationByType[type](value) : true;
    },
    ...(min && {
      min: {
        value: min,
        message: `Minimum value is ${min}`,
      },
    }),
    ...(max && {
      max: {
        value: max,
        message: `Maximum value is ${max}`,
      },
    }),
    ...(min_length && {
      minLength: {
        value: min_length,
        message: `Minimum length is ${min_length}`,
      },
    }),
    ...(max_length && {
      maxLength: {
        value: max_length,
        message: `Maximum length is ${max_length}`,
      },
    }),
  };

  return rules;
};

const getFormValues = (formSpec) => {
  /**
  const SAMPLE = {
    section1: {
      fields: {
        field1: data.name,
        field2: data.age,
      },
      sub_sections: {
        sub_section1: {
          fields: {
            field3: data.name,
            field4: data.age,
          },
        },
      },
    },
  };'

  const OUTPUT = {
    field1: data.name,
    field2: data.age,
    field3: data.name,
    field4: data.age,
  }
  */

  const formValues = {};

  const constructData = (order, data) => {
    for (const key of order) {
      for (const field of get(data[key], "field_order", [])) {
        formValues[field] = get(data[key], `fields[${field}].value`, "");
      }
      if (!isEmpty(get(data[key], "sub_section_order", []))) {
        constructData(
          get(data[key], "sub_section_order", []),
          get(data[key], "sub_sections", {})
        );
      }
    }
  };

  constructData(
    get(formSpec, "section_order", []),
    get(formSpec, "sections", {})
  );

  return formValues;
};

function getIsoDateString(dateString) {
  return typeof dateString === "string" ? dateString.split("T")[0] : null;
}

function formatDateToIsoDate(date) {
  return date instanceof Date ? format(date, "yyyy-MM-dd") : null;
}

function createDateObject(date) {
  if (date == null) return null;

  const formattedDate = parse(date, "yyyy-MM-dd", new Date());
  return Number.isNaN(formattedDate.getTime()) ? null : formattedDate;
}

function onExportAsPdf(pdfData, accessToken, quoteName) {
  let toastId = toast.custom(
    () => (
      <EverHotToastMessage
        type="loading"
        description="Preparing the file to download..."
      />
    ),
    { position: "top-center", duration: Number.POSITIVE_INFINITY }
  );

  downloadPdf(pdfData, accessToken, quoteName)
    .then((response) => {
      if (response.ok) {
        return response.blob();
      } else {
        throw new Error("Request failed");
      }
    })
    .then((blobby) => {
      let objectUrl = window.URL.createObjectURL(blobby);
      let anchor = document.createElement("a");
      anchor.href = objectUrl;
      anchor.download = quoteName ? `${quoteName}.pdf` : "quote.pdf";
      anchor.click();

      window.URL.revokeObjectURL(objectUrl);
      anchor.remove();
      toast.remove(toastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="success"
            description="Downloaded Successfully!!"
          />
        ),
        { position: "top-center" }
      );
    })
    .catch(() => {
      toast.remove(toastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="error"
            description="Error while exporting quote as PDF"
          />
        ),
        { position: "top-center" }
      );
    });
}

export {
  getPercent,
  isValidEmail,
  getRules,
  getFormValues,
  getIsoDateString,
  formatDateToIsoDate,
  createDateObject,
  onExportAsPdf,
};
