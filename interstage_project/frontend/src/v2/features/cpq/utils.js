import {
  CheckCircleIcon,
  UsersCheckIcon,
  MinusCircleIcon,
  EditIcon,
  EditPencilIcon as EditPencilSolidIcon,
  XCircleIcon,
  HourglassIcon,
  CalendarMinus02Icon,
  ThumbsDownIcon,
  Edit04Icon,
  PauseCircleIcon,
} from "@everstage/evericons/solid";
import { isNil, isNumber } from "lodash";

import {
  DEFAULT_DECIMAL_SEPARATOR,
  DURATION_TYPES,
  DURATION_LABELS,
} from "./constants";

/**
 * Extracts the group and decimal separators for a given locale by formatting a sample number.
 *
 * @param {string} locale - The locale identifier.
 * @returns {Object} An object containing group and decimal separators.
 */
const getSeparators = (locale = "en-US") => {
  const formattedNumber = new Intl.NumberFormat([locale, "en-US"], {
    style: "decimal",
    minimumFractionDigits: 1,
  }).format(12_345.6);
  const separators = formattedNumber.match(/\D+/g); // Matches non-digit characters

  return { groupSeparator: separators[0], decimalSeparator: separators[1] };
};

const numberFormatter = (
  value,
  {
    locale = "en-US",
    skipSeparatorFormat = false,
    innerPrefix = "",
    innerSuffix = "",
    precision = null,
  }
) => {
  const { decimalSeparator } = getSeparators(locale);
  // Check if the value is invalid or empty and return as is
  if (Number.isNaN(Number(value)) || value === "") {
    return `${innerPrefix}${value || ""}${innerSuffix}`;
  }

  const precisedValue = isNil(precision)
    ? value
    : formatNumberByPrecision(value, precision);
  // Split the value into integer and decimal parts
  const [integerPart, decimalPart] = String(precisedValue).split(
    DEFAULT_DECIMAL_SEPARATOR
  );
  // Format the integer part using the specified locale
  let formattedValue = skipSeparatorFormat
    ? Number(integerPart)
    : new Intl.NumberFormat([locale, "en-US"]).format(Number(integerPart));
  // Append the decimal part if it exists
  if (decimalPart !== undefined) {
    formattedValue += `${decimalSeparator}${decimalPart}`;
  }

  // Return the formatted value with the percentage sign if the field is a percentage
  return `${innerPrefix}${formattedValue}${innerSuffix}`;
};

function roundTo(num, precision) {
  const factor = 10 ** precision;
  return Math.round(num * factor + Number.EPSILON) / factor;
}

export const formatNumberByPrecision = (value, precision) => {
  const numericValue = Number.parseFloat(value);
  if (!(Number.isNaN(numericValue) || isNil(value) || value === "")) {
    // Format value to specified precision and remove trailing zeros
    const formattedValue = String(
      roundTo(numericValue, precision || 0)
    ).replace(/(\.\d*[1-9])0+|\.0*$/, "$1");
    const isNumberType = isNumber(Number.parseFloat(formattedValue));
    const finalValue = isNumberType
      ? Number.parseFloat(formattedValue)
      : formattedValue;

    return finalValue;
  }

  return value;
};

function getDurationLabel({ duration_type, duration_value }) {
  if (duration_type === DURATION_TYPES.FOREVER) {
    return DURATION_LABELS[duration_type].default;
  }
  const label = Number(duration_value) === 1 ? "singular" : "plural";
  return `${duration_value} ${DURATION_LABELS[duration_type][label]}`;
}

const getStatusBadgeProps = (status) => {
  switch (status) {
    case "Published": {
      return {
        type: "success",
        icon: <CheckCircleIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Active": {
      return {
        type: "success",
        icon: <CheckCircleIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Inactive": {
      return {
        type: "info",
        icon: <PauseCircleIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Draft": {
      return {
        type: "warning",
        icon: (
          <EditPencilSolidIcon className="h-3.5 w-3.5 text-ever-chartColors-22" />
        ),
      };
    }

    case "Pending Approval": {
      return {
        type: "info",
        icon: <HourglassIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Needs Attention": {
      return {
        type: "warning",
        icon: <HourglassIcon className="h-3.5 w-3.5" />,
      };
    }

    case "Approved": {
      return {
        type: "success",
        icon: <UsersCheckIcon className="h-3.5 w-3.5 text-ever-success" />,
      };
    }

    case "Rejected": {
      return {
        type: "error",
        icon: <MinusCircleIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Pending Signature": {
      return {
        type: "info",
        icon: <EditIcon className="h-3.5 w-3.5 text-ever-info" />,
      };
    }

    case "Lost": {
      return {
        type: "error",
        icon: <ThumbsDownIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Cancelled": {
      return {
        type: "error",
        icon: <XCircleIcon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Expired": {
      return {
        type: "error",
        icon: <CalendarMinus02Icon className="h-3.5 w-3.5 text-ever-error" />,
      };
    }

    case "Signed Offline": {
      return {
        type: "warning",
        icon: <Edit04Icon className="h-3.5 w-3.5 text-ever-chartColors-22" />,
      };
    }

    case "Won": {
      return {
        type: "custom",
        render: (
          <div className="status-badge-wrapper">
            <div className="status-badge">
              <span className="status-badge-text">{status}</span>
              <span>🎉</span>
            </div>
          </div>
        ),
      };
    }

    default: {
      return null;
    }
  }
};

export {
  getSeparators,
  numberFormatter,
  getDurationLabel,
  getStatusBadgeProps,
};
