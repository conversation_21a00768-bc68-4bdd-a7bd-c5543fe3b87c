import {
  CheckCircleTwoTone,
  WarningTwoTone,
  LoadingOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import {
  Row,
  Space,
  Col,
  Popover,
  Button,
  Typography,
  Divider,
  Tooltip,
} from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  SPLDATATYPE,
  FUNCTIONTYPES,
  TOKENTYPE,
  DATATYPE,
  TOKENCATEGORY,
  EXPR_STATUS,
} from "~/Enums";
import { getSelectOptionObject } from "~/Utils/ExpressionDesignerUtils";

import styles from "./styles.module.scss";
import ConcatFunctionButton from "../concat-function-button";
import ConditionalFunctionButton from "../conditional-function-button";
import ConstantFunctionButton from "../constant-function-button";
import { ContainsFunctionButton } from "../contains-function-button";
import CountFunctionButton from "../count-function-button";
import CurrentPeriodFunctionButton from "../current-period-function-button";
import DateDiffFunctionButton from "../date-diff-function-button";
import { EmptyConditionFunction } from "../empty-condition-function-button";
import ExpressionBox from "../expression-box";
import FindStringFunctionButton from "../find-string-function-button";
import GetDateFunctionButton from "../get-date-function-button";
import RoundFunctionButton from "../round-function-button";
import SimpleFunctionButton from "../simple-function-button";
import SimpleStringFunctionButton from "../simple-string-function-button";
import TokenFunctionButton from "../token-function-button";

const { Text } = Typography;

const Render = observer((props) => {
  const {
    onExpressionStackChange,
    onExpressionOutputTypeChange,
    expressionStackProp,
    expectedType,
    extraFunctions,
    skipFunctions = [],
    isSummationMode,
    resetCount,
    datasheetId,
    commissionPlanId,
    skipVariableTokens,
    dsVariableStore,
  } = props;

  const [expression, setExpression] = useState(null);
  const [expressionOutputType, setExpressionOutputType] = useState(null);
  const [newFunctionToken, setNewToken] = useState([null, null]);
  const [allowFunctions, setAllowFunctions] = useState(true);
  const [wrapCounter, wrap] = useState(0);

  const [popVisible, setPopVisible] = useState(false);

  const { t } = useTranslation();

  const doWhenExpressionStackChanges = (newStack) => {
    onExpressionStackChange && onExpressionStackChange(newStack);
  };

  const doWhenExpressionOutputTypeChanges = (outputType) => {
    setExpressionOutputType(outputType);
    onExpressionOutputTypeChange && onExpressionOutputTypeChange(outputType);
  };

  const onNewFunctionSelection = ({ functionName, name, args, dataType }) => {
    let token = {
      functionName,
      name,
      args,
      dataType,
      type: TOKENTYPE.VARIABLE,
      tokenCategory: TOKENCATEGORY.DYNAMIC,
    };

    setNewToken([token, getSelectOptionObject(token)]);
  };

  const getFunctionButtons = () => {
    const buttonStyleProps = {
      backgroundColor: "#E8EEFD",
      border: "1px solid #DBE3F7",
      borderRadius: 6,
      fontWeight: 400,
      fontSize: 12,
    };

    let extraFunctionsRender = null;
    if (extraFunctions) {
      let functionList = [],
        NUM_FUNC_BUTTONS_PER_COL = 5;

      extraFunctions.forEach((x, index) => {
        if (index % NUM_FUNC_BUTTONS_PER_COL === 0) {
          functionList.push([]);
        }

        let functionsPerCol = functionList[functionList.length - 1];
        let func = null;

        if (x.functionType === FUNCTIONTYPES.TOKEN) {
          func = (
            <TokenFunctionButton
              name={x.name}
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              dataType={x.dataType}
              key={x.name}
              onFocusChange={setPopVisible}
              buttonStyleProps={buttonStyleProps}
            />
          );
        } else if (x.functionType === FUNCTIONTYPES.SIMPLE) {
          func = (
            <SimpleFunctionButton
              name={x.name}
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={x.dataType}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
              displayText={x.displayText}
            />
          );
        } else if (x.functionType === FUNCTIONTYPES.COUNT) {
          func = (
            <CountFunctionButton
              name={x.name}
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={x.dataType}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
          );
        } else if (x.functionType === FUNCTIONTYPES.CONDITIONAL) {
          func = (
            <ConditionalFunctionButton
              name={x.name}
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              dataType={x.dataType}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              excludePayrollVariables={x.excludePayrollVariables}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
          );
        }
        functionsPerCol.push(func);
      });

      extraFunctionsRender = functionList.map((functionsPerCol, index) => (
        <Col key={index}>
          <Space direction="vertical">{functionsPerCol}</Space>
        </Col>
      ));
    }

    return (
      <Row gutter={[24, 0]}>
        <Col>
          <Space direction="vertical">
            {!skipFunctions.includes("QuotaAttainment") && (
              <SimpleFunctionButton
                name="SUM"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText="SUM"
              />
            )}
            {!skipFunctions.includes("SUM") && (
              <ConditionalFunctionButton
                name="SUM"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                dataType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
              />
            )}
            {!skipFunctions.includes("MIN") && (
              <SimpleFunctionButton
                name="MIN"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText="MIN"
              />
            )}
            {!skipFunctions.includes("MAX") && (
              <SimpleFunctionButton
                name="MAX"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText="MAX"
              />
            )}
            {!skipFunctions.includes("AVG") && (
              <SimpleFunctionButton
                name="AVG"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText="AVG"
              />
            )}
          </Space>
        </Col>
        <Col>
          <Space direction="vertical">
            {!skipFunctions.includes("COUNT") && (
              <ConditionalFunctionButton
                name="COUNT"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                dataType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
              />
            )}
            <CurrentPeriodFunctionButton
              name="CurrentPayoutPeriod"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.INTEGER}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            {!skipFunctions.includes("CountNotNull") && (
              <CountFunctionButton
                name="CountNotNull"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
              />
            )}
            <GetDateFunctionButton
              name="GetDate"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.INTEGER}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            {!skipFunctions.includes("DistinctCount") && (
              <CountFunctionButton
                name="DistinctCount"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={DATATYPE.INTEGER}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
              />
            )}
          </Space>
        </Col>
        <Col>
          <Space direction="vertical">
            <EmptyConditionFunction
              name="IsEmpty"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.BOOLEAN}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <EmptyConditionFunction
              name="IsNotEmpty"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.BOOLEAN}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <ContainsFunctionButton
              name="Contains"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.STRING}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <ContainsFunctionButton
              name="NotContains"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.STRING}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <DateDiffFunctionButton
              name={FUNCTIONTYPES.DATEDIFF}
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
          </Space>
        </Col>
        <Col>
          <Space direction="vertical">
            <SimpleStringFunctionButton
              name="Lower"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <ConcatFunctionButton
              name="Concat"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <FindStringFunctionButton
              name="Find"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={DATATYPE.STRING}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
          </Space>
        </Col>
        <Col>
          <Space direction="vertical">
            <RoundFunctionButton
              name="Round"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <RoundFunctionButton
              name="RoundUp"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
            <RoundFunctionButton
              name="RoundDown"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
            />
          </Space>
        </Col>

        <Divider type="vertical" style={{ height: "auto" }} />
        <Col>
          <Space direction="vertical">
            <SimpleFunctionButton
              name="Config"
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              optionType={SPLDATATYPE.ACONFIG}
              onFocusChange={setPopVisible}
              datasheetId={datasheetId}
              commissionPlanId={commissionPlanId}
              key={"AC-" + commissionPlanId}
              buttonStyleProps={buttonStyleProps}
              dsVariableStore={dsVariableStore}
              displayText="Config"
            />
            {!skipFunctions.includes("QuotaAttainment") && (
              <SimpleFunctionButton
                name="QuotaAttainment"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={SPLDATATYPE.QUOTA}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText={t("QUOTAATTAINTMENT")}
              />
            )}
            {!skipFunctions.includes("QuotaErosion") && (
              <SimpleFunctionButton
                name="QuotaErosion"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={SPLDATATYPE.QUOTA}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText={t("QUOTAEROSION")}
              />
            )}
            {!skipFunctions.includes("Quota") && (
              <SimpleFunctionButton
                name="Quota"
                onSelection={onNewFunctionSelection}
                enabled={allowFunctions}
                optionType={SPLDATATYPE.QUOTA}
                onFocusChange={setPopVisible}
                datasheetId={datasheetId}
                buttonStyleProps={buttonStyleProps}
                dsVariableStore={dsVariableStore}
                displayText={t("QUOTA")}
              />
            )}
          </Space>
        </Col>
        {!isEmpty(extraFunctionsRender) && (
          <>
            <Divider type="vertical" style={{ height: "auto" }} />
            {extraFunctionsRender}
          </>
        )}
      </Row>
    );
  };

  let [status, msg] = getExprStatus(
    expressionOutputType,
    expectedType,
    expression
  );

  return (
    <div className={styles.expressionContainer}>
      <Row justify="end">
        <Col>
          <Space size={0}>
            <Popover
              content={getFunctionButtons()}
              title={<Text style={{ fontSize: 14 }}>Choose Function</Text>}
              trigger="click"
              placement="bottomRight"
              visible={popVisible}
              onVisibleChange={setPopVisible}
              style={{ width: 200 }}
            >
              <Button
                disabled={!allowFunctions}
                size="small"
                style={{
                  borderRadius: 0,
                  borderTopLeftRadius: 7,
                }}
                type="default fw-normal fz-12"
              >
                <PlusCircleOutlined />
                Functions
              </Button>
            </Popover>

            <ConstantFunctionButton
              onSelection={onNewFunctionSelection}
              enabled={allowFunctions}
              style={{
                borderRadius: 0,
              }}
            />
            <Tooltip placement="rightTop" title="Wrap formula with bracket">
              <Button
                size={"small"}
                disabled={!!allowFunctions}
                onClick={() => wrap(wrapCounter + 1)}
                style={{
                  fontSize: 12,
                  borderRadius: 0,
                  borderTopRightRadius: 7,
                }}
              >
                {"(..)"}
              </Button>
            </Tooltip>
          </Space>
        </Col>
      </Row>
      <Row align="middle">
        <Col span={24}>
          <ExpressionBox
            onExpressionStackChange={doWhenExpressionStackChanges}
            onExpressionChange={setExpression}
            onExpressionOutputTypeChange={doWhenExpressionOutputTypeChanges}
            externalToken={newFunctionToken}
            setAllowFunctions={setAllowFunctions}
            wrapCounter={wrapCounter}
            expressionStackProp={expressionStackProp}
            isSummationMode={isSummationMode}
            resetCount={resetCount}
            datasheetId={datasheetId}
            expressionBoxMsg={msg}
            expressionBoxStatus={status}
            skipVariableTokens={skipVariableTokens}
            dsVariableStore={dsVariableStore}
          />
        </Col>
      </Row>
    </div>
  );
});

export const getExprStatus = (
  expressionOutputType,
  expectedType,
  expression
) => {
  let status = null;
  let msg = { text: "", color: "cyan" };
  if (expressionOutputType) {
    let exprStatus = expressionOutputType[0];
    let msgOrType = expressionOutputType[1];
    if (exprStatus === EXPR_STATUS.VALID) {
      if (expectedType) {
        if (expectedType === msgOrType) {
          status = (
            <CheckCircleTwoTone
              twoToneColor="#52c41a"
              style={{ fontSize: 18, paddingLeft: 5 }}
            />
          );
          msg = { text: `${expression.join(" ")}`, color: "cyan" };
        } else {
          status = (
            <WarningTwoTone
              twoToneColor="#ff7875"
              style={{ fontSize: 18, paddingLeft: 5 }}
            />
          );
          msg = {
            text: `This is a(n) ${msgOrType} expression. Please provide a(n) ${expectedType} expression.`,
            color: "volcano",
          };
        }
      } else {
        status = (
          <CheckCircleTwoTone
            twoToneColor="#52c41a"
            style={{ fontSize: 18, paddingLeft: 5 }}
          />
        );
        msg = { text: `: ${expression.join(" ")}`, color: "cyan" };
      }
    } else if (exprStatus === EXPR_STATUS.INVALID) {
      status = (
        <WarningTwoTone
          twoToneColor="#ff7875"
          style={{ fontSize: 18, paddingLeft: 5 }}
        />
      );
      msg = {
        text: msgOrType,
        color: "volcano",
      };
    } else if (exprStatus === EXPR_STATUS.CHECKING) {
      status = <LoadingOutlined style={{ fontSize: 18, paddingLeft: 5 }} />;
      msg = {
        text: msgOrType,
        color: "yellow",
      };
    }
  }
  return [status, msg];
};

export default Render;
