import { observer } from "mobx-react";
import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import { DASHBOARD_URLS, RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDashboardsStore } from "~/GlobalStores/DashboardsStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverLoader } from "~/v2/components";
import { dashboardsUrl } from "~/v2/features/dashboard23/utils.js";
import { useQueryParams } from "~/v2/hooks";

const DashboardRedirect = ({ children }) => {
  const navigate = useNavigate();
  const query = useQueryParams();
  const { pathname } = useLocation();
  const { accessToken } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const { loading, dashboards, fetchDashboards, setAccessToken, isApiCalled } =
    useDashboardsStore();
  const IS_DASHBOARD_PATHS = [
    "/",
    DASHBOARD_URLS.DASHBOARDS,
    DASHBOARD_URLS.DASHBOARD,
  ].includes(pathname);
  const DASHBOARDS_BUT_NOT_ALL =
    pathname === DASHBOARD_URLS.DASHBOARDS ? query.get("type") !== "all" : true;
  const HAS_PERMISSIONS = hasPermissions(RBAC_ROLES.VIEW_DASHBOARD);
  const HAS_PAYOUTS_PERMISSIONS = hasPermissions(RBAC_ROLES.VIEW_PAYOUTS);
  const HAS_STATEMENTS_PERMISSIONS = hasPermissions(RBAC_ROLES.VIEW_STATEMENTS);
  const HAS_REDIRECT_TO = query.get("redirect_to");
  let message = "Loading Dashboard...";
  if (pathname === DASHBOARD_URLS.DASHBOARDS && query.get("type") === "all") {
    message = "Loading Dashboards...";
  }

  useEffect(() => {
    setAccessToken(accessToken);
  }, [accessToken]);

  useEffect(() => {
    // Will pre-load the dashboards if required
    if (
      !IS_DASHBOARD_PATHS ||
      !HAS_PERMISSIONS ||
      HAS_REDIRECT_TO ||
      isApiCalled
    ) {
      return;
    }
    fetchDashboards();
  }, [pathname, query, dashboards, isApiCalled]);

  useEffect(() => {
    // Will redirect to specific dashboard if required

    // If the User doesn't have permissions to view dashboard,
    // we redirect to Payouts page if permission is available
    // if not redirect to Statements page if permission is available.
    if (!HAS_PERMISSIONS) {
      if (HAS_PAYOUTS_PERMISSIONS) {
        navigate("/commissions");
      } else if (HAS_STATEMENTS_PERMISSIONS) {
        navigate("/statements");
      }
      return;
    }

    if (
      !IS_DASHBOARD_PATHS ||
      !DASHBOARDS_BUT_NOT_ALL ||
      HAS_REDIRECT_TO ||
      !isApiCalled ||
      (pathname === DASHBOARD_URLS.DASHBOARD &&
        query.get("id") &&
        query.get("type"))
    ) {
      return;
    }

    // If dashboard is empty we simply redirect to DashboardsListView AuthRoute which
    // takes care of showing empty state.
    if (dashboards.length === 0) {
      navigate(`${DASHBOARD_URLS.DASHBOARDS}?type=all`);
      return;
    }

    navigate(
      dashboardsUrl(dashboards[0].dashboardId, dashboards[0].dashboardType)
    );
  }, [pathname, query, dashboards, isApiCalled]);

  if (loading) {
    return (
      <div className="fixed top-0 left-0 w-full h-full">
        <EverLoader
          wrapperClassName="backdrop-blur-none"
          tip={message}
          indicatorType="logo"
          spinning
        />
      </div>
    );
  }

  return children;
};

export default observer(DashboardRedirect);
