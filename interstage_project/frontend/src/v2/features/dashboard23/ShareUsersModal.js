import { useQuery } from "@apollo/client";
import { Col, Row, message } from "antd";
import { isEqual } from "lodash";
import PropTypes from "prop-types";
import React, { useState, useEffect } from "react";

import { useAuthStore } from "~/GlobalStores/AuthStore";
// import EverCheckbox from "Components/EverCheckbox";
import {
  EverButton,
  EverCheckbox,
  EverModal,
  EverSelect,
  EverTg,
} from "~/v2/components";

import { GET_USER_DATA, GET_USER_GROUPS_DATA } from "./graphql";
import { shareDashboard } from "./restApi";
/**
 
 * @param {Object} selectedDashboard - contains the selected dashboard data
 * @param {boolean} isVisible - boolean describing whether the modal is visible or not
 * @param {function} handleClose - function to close the modal
 * @param {function} refetch - function to refetch the dashboard data
 */

export function ShareUsersModal({
  selectedDashboard,
  isVisible,
  handleClose: handleBaseClose,
  refetch,
}) {
  const { accessToken } = useAuthStore();
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedUserGroups, setSelectedUserGroups] = useState([]);

  const [isLoading, setIsLoading] = useState(false);

  // get the user data
  const { data: userData } = useQuery(GET_USER_DATA, {
    variables: {
      dashboardType: selectedDashboard?.dashboardType,
    },
    fetchPolicy: "network-only",
  });
  // get the userGroup data
  const { data: userGroupData } = useQuery(GET_USER_GROUPS_DATA, {
    fetchPolicy: "network-only",
  });

  const users = userData?.validUsers || [];
  const userGroups = userGroupData?.userGroups || [];

  useEffect(() => {
    setSelectedUsers(selectedDashboard?.sharedUsers || []);
    setSelectedUserGroups(selectedDashboard?.sharedUserGroups || []);
  }, [selectedDashboard]);

  const handleClose = () => {
    setSelectedUsers([]);
    setSelectedUserGroups([]);
    handleBaseClose();
  };

  const handleShareDashboard = async () => {
    setIsLoading(true);
    try {
      /**
       * Share a dashboard with other users.
       * @param {string} accessToken The access token.
       * @param {string} dashboardId The dashboard ID.
       * @param {Object} options The options.
       * @param {Array<string>} options.sharedUsers The users to share the dashboard with.
       * @param {Array<string>} options.sharedUserGroups The user groups to share the dashboard with.
       * @returns {Promise<ShareDashboardResponse>} The promise.
       */

      const data = await shareDashboard(
        accessToken,
        selectedDashboard?.dashboardId,
        selectedDashboard?.dashboardType,
        { sharedUsers: selectedUsers, sharedUserGroups: selectedUserGroups }
      );
      if (data === "SUCCESS") {
        message.success("Dashboard shared successfully");
        refetch && refetch();
        handleClose();
      } else {
        message.error("Unable to share dashboard. Please try again later.");
      }
    } catch (error) {
      console.log("Error:", error);
      message.error("Unable to share dashboard. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectAll = (type, checked) => {
    /**
     * @function
     * @name handleSelectAll
     * @description Handle select all functionality for users and user groups table
     *
     * @param {string} type Type of the table
     * @param {boolean} checked Check value

     */

    if (type === "users") {
      if (checked) {
        const selectedRecords = users.map((user) => user.employeeEmailId);
        setSelectedUsers(selectedRecords);
      } else {
        setSelectedUsers([]);
      }
    } else if (type === "user-groups") {
      if (checked) {
        const selectedRecords = userGroups.map((group) => group.userGroupId);
        setSelectedUserGroups(selectedRecords);
      } else {
        setSelectedUserGroups([]);
      }
    }
  };

  const isShareDisabled = () => {
    /**
     * Returns true if the dashboard has not been modified, false otherwise.
     * @returns {boolean}
     */

    const isShareChanged =
      !isEqual(
        (selectedDashboard?.sharedUsers || []).sort(),
        (selectedUsers || []).sort()
      ) ||
      !isEqual(
        (selectedDashboard?.sharedUserGroups || []).sort(),
        (selectedUserGroups || []).sort()
      );
    return isLoading || !isShareChanged;
  };

  return (
    <EverModal
      visible={isVisible}
      centered={false}
      className="share-dashboard-modal"
      width="750px"
      title={
        <EverTg.Heading3 className="text-ever-base-content">{`Share Dashboard - ${
          selectedDashboard?.name || ""
        }`}</EverTg.Heading3>
      }
      footer={[
        <EverButton
          type="filled"
          key={"share-button"}
          onClick={handleShareDashboard}
          disabled={isShareDisabled()}
          loading={isLoading}
          className={"w-32"}
        >
          Share
        </EverButton>,
      ]}
      onCancel={() => {
        if (!isLoading) {
          handleClose();
        }
      }}
    >
      <Row>
        <Col span={12} className="px-2">
          <div>
            <EverTg.Text className="text-ever-base-content">
              Share to Users
            </EverTg.Text>
            <EverSelect
              mode="multiple"
              allowClear
              className={"w-full mt-1 h-auto"}
              placeholder="Select Users"
              maxTagCount={10}
              onChange={setSelectedUsers}
              value={selectedUsers}
              dropdownRender={(menu) => (
                <div className="flex-nowrap">
                  <EverCheckbox
                    className="py-2 !px-4"
                    value="user-select-all"
                    type="es"
                    label="Select All"
                    checked={users.length === selectedUsers.length}
                    onClick={(event) => {
                      handleSelectAll("users", !event.target.checked);
                    }}
                    onChange={(event) =>
                      handleSelectAll("users", event.target.checked)
                    }
                  />
                  <div className="!px-1">{menu}</div>
                </div>
              )}
              options={users.map((user) => {
                return {
                  value: user.employeeEmailId,
                  label: user.firstName + " " + user.lastName,
                };
              })}
              filterOption={(input, option) => {
                return (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase());
              }}
              data-testid="share-users-select"
            />
          </div>
        </Col>
        <Col span={12} className="px-2">
          <div>
            <EverTg.Text className="text-ever-base-content">
              Share to User Groups
            </EverTg.Text>
            <EverSelect
              mode="multiple"
              allowClear
              className={"w-full mt-1 h-auto"}
              placeholder="Select User Groups"
              maxTagCount={10}
              onChange={setSelectedUserGroups}
              value={selectedUserGroups}
              tagClassName="max-w-[96px] truncate"
              dropdownRender={(menu) => (
                <div className="flex-nowrap">
                  <EverCheckbox
                    className="py-2 !px-4"
                    value="user-group-select-all"
                    type="es"
                    label="Select All"
                    checked={userGroups.length === selectedUserGroups.length}
                    onClick={(event) => {
                      handleSelectAll("user-groups", !event.target.checked);
                    }}
                    onChange={(event) =>
                      handleSelectAll("user-groups", event.target.checked)
                    }
                  />
                  <div className="!px-1">{menu}</div>
                </div>
              )}
              options={userGroups.map((group) => {
                return {
                  value: group.userGroupId,
                  label: group.userGroupName,
                };
              })}
              filterOption={(input, option) => {
                return (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase());
              }}
            />
          </div>
        </Col>
      </Row>
    </EverModal>
  );
}

ShareUsersModal.propTypes = {
  selectedDashboard: PropTypes.shape({
    dashboardId: PropTypes.string,
    name: PropTypes.string,
    sharedUsers: PropTypes.arrayOf(PropTypes.string),
    sharedUserGroups: PropTypes.arrayOf(PropTypes.string),
  }),
  isVisible: PropTypes.bool,
  handleClose: PropTypes.func,
  refetch: PropTypes.func,
};
