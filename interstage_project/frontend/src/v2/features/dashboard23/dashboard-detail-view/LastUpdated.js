import { ClockRewindIcon } from "@everstage/evericons/outlined";
import React from "react";
import { useSpring, animated } from "react-spring";

import { EverTg } from "~/v2/components";

export function LastUpdated({ lastUpdated }) {
  const [isHovered, setIsHovered] = React.useState(false);
  const textAnimation = useSpring({
    opacity: isHovered ? 1 : 0,
    transform: `translateX(${isHovered ? "-8px" : "0px"})`,
  });
  return (
    <>
      <div
        className={`text-ever-base-content-mid flex items-center rounded-md pl-4 pr-2 py-1 ${
          isHovered && "shadow-md"
        }`}
      >
        <animated.div style={textAnimation}>
          <EverTg.Text>Last updated: {lastUpdated}</EverTg.Text>
        </animated.div>
        <div
          className={`flex items-center ${isHovered && "text-ever-primary"}`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <ClockRewindIcon className="w-5 h-5" />
        </div>
      </div>
    </>
  );
}
