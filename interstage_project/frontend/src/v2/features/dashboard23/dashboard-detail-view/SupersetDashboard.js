import React from "react";

import { DASHBOARD_URLS } from "~/Enums";
import { PageNotFound, EverLoader } from "~/v2/components";

import "./common.module.scss";

export function SupersetDashboard({ isValidUrl }) {
  if (isValidUrl) {
    //if it is validurl then show the detail page
    return (
      <>
        <div className="dashboard-detail-view w-full h-full bg-white pt-1.5 pb-8 overflow-hidden flex flex-col">
          <div id="superset-container" className="w-full h-full">
            <div className="w-full h-full justify-center items-center flex">
              <EverLoader.SpinnerLottie className="w-14 h-14" />
            </div>
          </div>
        </div>
      </>
    );
  }
  //When an invalid url is used, an error page is displayed.
  return (
    <PageNotFound
      status="404"
      title="404"
      subTitle="Sorry, the dashboard you visited does not exist."
      showButton
      redirectUrl={DASHBOARD_URLS.DASHBOARDS}
      buttonLabel="Back Home"
    />
  );
}
