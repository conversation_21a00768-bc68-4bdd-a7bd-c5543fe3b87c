import {
  RefreshIcon,
  ShareIcon,
  TrashIcon,
} from "@everstage/evericons/outlined";
import { Divider, message } from "antd";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

import { DASHBOARD_URLS, RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverLink, EverTooltip, EverButton, EverModal } from "~/v2/components";
import Lottie from "~/v2/components/Lottie";

import DeleteAnimationData from "../dashboard-lotties/delete-animation.json";
import { deleteDashboard } from "../restApi";
import { ShareUsersModal } from "../ShareUsersModal";

export function SupersetDashboardActions({
  dashboardData,
  supersetHostUrl,
  getDashboardDetail,
  handleEmbedDashboard,
  selectedDashboardData,
  setSelectedDashboardData,
}) {
  const { isLoggedInAsUser } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const { accessToken } = useAuthStore();
  const navigate = useNavigate();

  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const [isSharedUserModalVisible, setIsSharedUserModalVisible] =
    useState(false);

  /* This function fetches the dashboard data from the Superset API. It takes in the
   * accessToken and the dashboardId as arguments. It checks whether the response is
   * okay and then returns the data and the supersetHostUrl. If the response is not
   * okay, then it sets the isValidUrl to false.
   */

  const handleDelete = async () => {
    //delete the dashboard and redirect to dashboard list view page
    const result = await deleteDashboard(dashboardData, accessToken);

    // Check if result is an object with status property
    if (result?.status === "SUCCESS") {
      message.success("Dashboard successfully deleted");
      navigate(DASHBOARD_URLS.DASHBOARDS);
    } else {
      // Show specific error message if available, otherwise fallback to generic message
      message.error(result?.message || "Dashboard deletion failed");
    }
  };

  // When the user clicks the Share button, the modal opens and the dashboard data is passed to the modal.
  const handleShareModalClick = () => {
    setIsSharedUserModalVisible(true);
    setSelectedDashboardData(dashboardData);
  };

  // This function will be called when the user closes the shared user modal.

  const handleShareModalClose = () => {
    setIsSharedUserModalVisible(false);
    setSelectedDashboardData({});
  };

  const handleShowConfirmationPopup = (value) => {
    setShowConfirmationPopup(value);
  };
  const lottieDefaultOptions = {
    loop: true,
    autoplay: true,
    animationData: DeleteAnimationData,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  return (
    <>
      <EverModal.Confirm
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={(event) => {
              event.stopPropagation();
              handleShowConfirmationPopup(false);
            }}
            type="ghost"
          >
            Cancel
          </EverButton>,
          <EverButton
            type="filled"
            color="error"
            key="accept"
            onClick={(event) => {
              event.stopPropagation();
              handleShowConfirmationPopup(false);
              handleDelete();
            }}
          >
            Delete
          </EverButton>,
        ]}
        subtitle="You can't undo this action"
        title="Are you sure you want to permanently delete this dashboard?"
        visible={showConfirmationPopup}
        icon={<Lottie options={lottieDefaultOptions} width={64} height={64} />}
      />
      {dashboardData?.supersetDashboardId && (
        <div className="flex items-center justify-end width-full">
          {hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS) && !isLoggedInAsUser && (
            <>
              <div className="w-40">
                <EverLink
                  className={"mr-2"}
                  href={`${supersetHostUrl}/superset/dashboard/${dashboardData?.supersetDashboardId}`}
                  target="_blank"
                  label={"Manage dashboard"}
                  tooltipTitle={"You can add or modify charts in analytics."}
                />
              </div>
              <Divider type="vertical" className="ml-4" />

              {/* <div className="mr-[25px] cursor-pointer text-[18px] text-everstageGray-1600">
            <img src={ExportIcon} alt="export-icon" />
          </div> */}
            </>
          )}
          {hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS) && (
            <>
              <div
                className="hover:bg-ever-base-200 mr-[5px] w-10 h-10 flex items-center justify-center rounded-lg cursor-pointer text-lg"
                onClick={() => {
                  handleEmbedDashboard();
                  document
                    .querySelector("#superset-container")
                    ?.contentWindow?.location?.reload();
                }}
              >
                <EverTooltip title="Refresh">
                  <RefreshIcon className="h-4 w-4 " />
                </EverTooltip>
              </div>
              <div
                className="hover:bg-ever-base-200 mr-[5px] w-10 h-10 flex items-center justify-center rounded-lg cursor-pointer text-lg "
                onClick={() => {
                  handleShareModalClick();
                }}
              >
                <EverTooltip title="Share">
                  <ShareIcon className="h-4 w-4" />
                </EverTooltip>
              </div>
              <div
                className="hover:bg-ever-base-200 w-10 h-10 flex items-center justify-center rounded-lg cursor-pointer text-lg"
                onClick={() => {
                  handleShowConfirmationPopup(true);
                }}
              >
                <EverTooltip title="Delete">
                  <TrashIcon className="w-4 h-4" />
                </EverTooltip>
              </div>
            </>
          )}
        </div>
      )}
      {isSharedUserModalVisible && (
        <ShareUsersModal
          isVisible={isSharedUserModalVisible}
          selectedDashboard={selectedDashboardData}
          handleClose={handleShareModalClose}
          refetch={getDashboardDetail}
        />
      )}
    </>
  );
}
