import { useQuery, gql } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Row, Col, Divider } from "antd";
import { sortBy } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useState, useEffect, Fragment } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import {
  formatCurrencyWithoutDecimal,
  getLocalizedCurrencyValue,
} from "~/Utils/CurrencyUtils";
import { getClientFiscalYear } from "~/Utils/DateUtils";
import { percentageOneDecimalPlace } from "~/Utils/QuotaUtils";
import {
  useCurrentTheme,
  EverLoader,
  EverSelect,
  EverDatePicker,
  <PERSON><PERSON>uttonGroup,
  EverButton,
  EverTg,
  EverBadge,
} from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyCommissionPayoutsTracker } from "~/v2/images";

import { LastUpdated } from "../LastUpdated";

const ALL_COMM_PLAN_VALUE = "All";

const COMMISSION_SUMMARY_PAYOUTS_TRACKER = gql`
  query AllPublishedPlansAndCommissionSummaryPayoutsCount(
    $planIds: [String]!
    $year: Int!
  ) {
    allPublishedPlans {
      planId
      planName
    }
    commissionSummaryPayoutsCount(planIds: $planIds, year: $year)
  }
`;

const EmptyCommissionPayoutsTrackerComp = ({ emptyText }) => {
  return (
    <div className="mt-5 w-[80%] mx-auto">
      <img
        src={emptyCommissionPayoutsTracker}
        alt="empty-commission-payouts-tracker"
        className="w-full"
      />
      <div className="text-center !mt-5 w-[70%] m-auto">
        <EverTg.Text className="text-center text-ever-base-content-mid mt-[10px] mb-5">
          {emptyText}
        </EverTg.Text>
      </div>
    </div>
  );
};

const CommissionPayoutsTracker = observer((props) => {
  const { store } = props;
  const { chartColors, base } = useCurrentTheme();
  const themeColor = Object.values(chartColors || {});
  const baseFontColor = base.content.mid || "";
  const xAxisLineColor = base[300];
  const valueColor = base.content.DEFAULT || "";
  const { baseCurrencySymbol } = store;
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [commissionPlans, setCommissionPlans] = useState();
  const [selectedPlans, setSelectedPlans] = useState([ALL_COMM_PLAN_VALUE]);
  const [planIds, setPlanIds] = useState([]);
  const [open, setOpen] = useState(false);
  const [commPayouts, setCommPayouts] = useState();
  const [ytd, setYtd] = useState();
  const [ytdPerc, setYtdPerc] = useState();
  const [year, setYear] = useState();
  const [isDataConfigured, setIsDataConfigured] = useState(false);
  const [totalVarPay, setTotalVarPay] = useState(0);
  const [isQtd, setIsQtd] = useState(false);
  const myClient = useRecoilValue(myClientAtom);
  const { fiscalStartMonthZero: fiscalYearMonth, baseCurrency } = myClient;

  const { loading, data } = useQuery(COMMISSION_SUMMARY_PAYOUTS_TRACKER, {
    variables: {
      planIds:
        planIds.length === 0 || planIds.includes(ALL_COMM_PLAN_VALUE)
          ? []
          : planIds,
      year: year && year.year(),
    },
    fetchPolicy: year ? "network-only" : "cache-only",
    // skip: !year,
    onCompleted: () => setIsFirstLoad(false),
  });

  const { t } = useTranslation();

  useEffect(() => {
    let dataConfigured = false;
    if (data && fiscalYearMonth !== undefined) {
      if (data.allPublishedPlans) {
        let options = [
          {
            label: t("ALL_COMMISSION_PLANS"),
            value: ALL_COMM_PLAN_VALUE,
          },
        ];
        data.allPublishedPlans.forEach((plan) => {
          options.push({ label: plan.planName, value: plan.planId });
        });
        setCommissionPlans(sortBy(options, ["label"]));
      }

      let resData = JSON.parse(data.commissionSummaryPayoutsCount);
      if (resData && !("error" in resData)) {
        let ptData = isQtd
          ? resData?.commission_payouts_qtd
          : resData?.commission_payouts;
        let fiscalPeriods = [];
        if (Object.keys(ptData).length === 4) {
          fiscalPeriods = ["Q1", "Q2", "Q3", "Q4"];
        } else if (Object.keys(ptData).length === 12) {
          let monthArr = moment.monthsShort();
          fiscalPeriods = monthArr
            .slice(fiscalYearMonth, monthArr.length)
            .concat(monthArr.slice(0, fiscalYearMonth));
        } else {
          fiscalPeriods = null;
        }

        let chartData = [];
        let totalYtd = 0;
        let totalVarPay = 0;

        //const currentMonth = moment().endOf("month").format("MMM");
        fiscalPeriods &&
          fiscalPeriods.forEach((fm) => {
            let monthData = ptData[fiscalPeriods.indexOf(fm) + 1]
              ? ptData[fiscalPeriods.indexOf(fm) + 1]
              : {};
            chartData.push({
              label: fm,
              value: Math.floor(monthData?.commission_amount),
              toolText: getLocalizedCurrencyValue(
                monthData?.commission_amount,
                baseCurrencySymbol
              ),
              variablePay: parseFloat(monthData?.variable_pay),
            });
            totalYtd = totalYtd + parseFloat(monthData?.commission_amount);
            totalVarPay = totalVarPay + parseFloat(monthData?.variable_pay);
          });
        setTotalVarPay(totalVarPay);
        // if (getClientFiscalYear(fiscalYearMonth).year() == year.year()) {
        //   const monthToSlice = chartData.findIndex(
        //     (item) => item.label == currentMonth
        //   );
        //   let sData = chartData.slice(0, monthToSlice + 1);
        //   setCommPayouts(sData);
        //   let mYtd = 0;
        //   let mVP = 0;
        //   sData.forEach((x) => {
        //     mYtd = mYtd + parseFloat(x.value);
        //     mVP = mVP + x.variablePay;
        //   });
        //   totalYtd = mYtd;
        //   totalVarPay = mVP;
        // } else {
        setCommPayouts(chartData);
        // }
        setYtd(totalYtd);
        let ytdPerc = ((totalYtd / totalVarPay) * 100).toFixed(2);
        setYtdPerc(isNaN(ytdPerc) ? 0.0 : ytdPerc);
        dataConfigured = true;
      }
    }
    setIsDataConfigured(dataConfigured);
  }, [data, fiscalYearMonth, isQtd]);

  useEffect(() => {
    if (fiscalYearMonth != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonth));
    }
  }, [fiscalYearMonth]);

  const onHandleChange = (val) => {
    console.log("CPT On handle change", val, selectedPlans);
    if (val.length > 1 && val.includes(ALL_COMM_PLAN_VALUE)) {
      let index = val.indexOf(ALL_COMM_PLAN_VALUE);
      if (index > -1) {
        val.splice(index, 1);
      }
    }
    setSelectedPlans(val);
  };

  const onHandleBlur = () => {
    console.log("CPT On handle blur", selectedPlans);
    if (selectedPlans.length === 0) {
      setSelectedPlans([ALL_COMM_PLAN_VALUE]);
      setPlanIds([ALL_COMM_PLAN_VALUE]);
    } else {
      setPlanIds(selectedPlans);
    }
  };

  const handleOnDrowdownEvent = (open) => {
    console.log("On handle Dropdown event", selectedPlans, planIds);
    setOpen(open);
    if (!open) {
      if (selectedPlans.length === 0) {
        if (planIds.length === 0) {
          setSelectedPlans([ALL_COMM_PLAN_VALUE]);
          setPlanIds([ALL_COMM_PLAN_VALUE]);
        } else {
          setSelectedPlans(planIds);
        }
      }
    }
  };

  const isIndianCurrency = baseCurrency === "INR";

  const dataSource = {
    chart: {
      paletteColors: themeColor[0],
      // usePlotGradientColor: 1,
      // plotGradientColor: [themeColor[0], themeColor[18]],
      valueFontColor: valueColor,
      baseFont: "Inter",
      baseFontColor: baseFontColor,
      valueFont: "Inter",
      baseFontSize: 12,
      valueFontSize: 12,
      showShadow: false,
      showValues: 1,
      maxColWidth: 35,
      placeValuesInside: "0",
      showXAxisLine: 1,
      xAxisLineColor: xAxisLineColor,
      canvasTopPadding: 20,
      formatNumberScale: 1,
      divLineAlpha: 0,
      numberPrefix: `${baseCurrencySymbol}`, //Adding space between value and currency
      showHoverEffect: 0,
      theme: "fusion",
      // chartLeftMargin: 20,
      // chartRightMargin: 20,
      bgColor: base.DEFAULT,
      toolTipBgColor: base?.DEFAULT,
      toolTipColor: base?.content?.DEFAULT,
      toolTipBorderColor: base?.[300],
      numberScaleValue: isIndianCurrency ? "1000,100,100" : "1000,1000,1000",
      numberScaleUnit: isIndianCurrency ? "K,L,Cr" : "K,M,B",
    },

    data: commPayouts,
  };

  const lastUpdated = data?.commissionSummaryPayoutsCount
    ? JSON.parse(data?.commissionSummaryPayoutsCount)?.last_updated?.split(",")
    : [];

  if (loading && isFirstLoad) {
    return <WidgetLoading />;
  }

  return (
    <Fragment>
      <div>
        <div className="flex w-full mb-4 justify-between">
          <EverTg.Heading3
            //style={{ fontSize: 16, fontWeight: 500, margin: "12px 0" }}
            className="text-ever-base-content"
          >
            {t("PAYOUTS_TRACKER")}
          </EverTg.Heading3>
          {lastUpdated?.length === 2 && (
            <div className="flex">
              {/* <div className="text-ever-base-content-mid flex items-center">
                      <ClockRewindIcon className="w-5 h-5 mr-2" />
                      <div className="!my-3">
                        <EverTg.Text> Last updated:</EverTg.Text>
                      </div>
                      <div className="!my-3 !font-normal !text-sm">
                        <EverTg.Text>
                          {" "}
                          {lastUpdated[0] + ","}
                          {" " + lastUpdated[1]} UTC{" "}
                        </EverTg.Text>
                      </div>
                    </div> */}
              <LastUpdated
                lastUpdated={`${lastUpdated[0]}, ${lastUpdated[1]} UTC`}
              />
            </div>
          )}
        </div>
        <div className="flex mb-8 items-center">
          <div className="flex mr-2 w-60">
            <EverSelect
              open={open}
              mode="multiple"
              placeholder="Choose Plan"
              options={commissionPlans}
              value={selectedPlans}
              onDropdownVisibleChange={handleOnDrowdownEvent}
              onChange={(value) => {
                onHandleChange(value);
                setOpen(true);
              }}
              onBlur={() => onHandleBlur()}
              showArrow
              suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
              maxTagTextLength={6}
              maxTagCount={1}
              className="[&>div]:w-full w-full h-full flex items-center justify-end"
              filterOption={(input, option) => {
                return (
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                );
              }}
              disabled={loading}
              size="small"
              dropdownMatchSelectWidth={false}
              getPopupContainer={({ parentNode }) => parentNode}
              dropdownAlign={{ offset: [0, 6] }}
            ></EverSelect>
          </div>
          {isDataConfigured && (
            <div className="flex mr-2">
              <EverButtonGroup
                className="bg-ever-base-200"
                activeBtnType="text"
                activeBtnColor="primary"
                defActiveBtnIndex={isQtd ? 1 : 0}
                size="small"
                activeButtonClassname="shadow-sm"
              >
                <EverButton
                  size=""
                  onClick={() => {
                    setIsQtd(false);
                  }}
                >
                  Monthly
                </EverButton>
                <EverButton
                  onClick={() => {
                    setIsQtd(true);
                  }}
                >
                  Quarterly
                </EverButton>
              </EverButtonGroup>
            </div>
          )}
          <div className="flex w-28 mt-1">
            <EverDatePicker.Legacy
              onChange={(date) => setYear(date)}
              picker="year"
              bordered={true}
              value={year}
              allowClear={false}
              size="small"
              className="w-full h-full rounded-lg"
              disabled={loading}
              placeholder="Year"
              getPopupContainer={({ parentNode }) => parentNode}
            />
          </div>
        </div>
      </div>

      {loading ? (
        <EverLoader
          indicatorType="spinner"
          className="!static"
          wrapperClassName="rounded-xl"
        >
          <EmptyCommissionPayoutsTrackerComp
            emptyText={t("TRACK_COMM_EACH_MONTH")}
          />
        </EverLoader>
      ) : isDataConfigured ? (
        <>
          <Row>
            <Col span={16} className="flex items-center">
              <>
                <EverBadge
                  type={`success`}
                  icon={
                    <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                  }
                  title={`${t(
                    "YTD_PAYOUTS"
                  )} ${baseCurrencySymbol}${formatCurrencyWithoutDecimal(
                    ytd
                  )} `}
                  className={"text-ever-success-lite-content"}
                />
                {/* <EverTg.Text className="text-ever-base-content mr-1">
                    {t("YTD_PAYOUTS")}
                  </EverTg.Text>
                  <EverTg.Text className="font-semibold text-ever-success-content-lite">
                    {``}
                  </EverTg.Text> */}
              </>
              {totalVarPay != 0 && (
                <>
                  <Divider className="border-ever-base-400" type="vertical" />
                  <EverBadge
                    type={`info`}
                    title={`${percentageOneDecimalPlace(ytdPerc)}% ${t(
                      "BUDGETED_PAYOUTS"
                    )} `}
                    icon={
                      <div className="w-1.5 h-1.5 rounded-full bg-ever-chartColors-12"></div>
                    }
                    className={
                      "bg-ever-chartColors-41 text-ever-chartColors-20"
                    }
                  />
                  {/* <EverTg.Text
                      className="font-semibold text-ever-success-content-lite"
                      title={`${ytdPerc}%`}
                    >
                      {percentageOneDecimalPlace(ytdPerc)}
                    </EverTg.Text>
                    <EverTg.Text className="text-ever-base-content ml-1">
                      {t("BUDGETED_PAYOUTS")}
                    </EverTg.Text> */}
                </>
              )}
            </Col>
          </Row>
          <Row className="mt-2">
            <Col span={24}>
              <ReactFusioncharts
                type="column2d"
                width="100%"
                height="350"
                dataFormat="JSON"
                dataSource={dataSource}
                dataUpdated={base.DEFAULT}
              />
            </Col>
            <Col span={24}></Col>
          </Row>
        </>
      ) : (
        <EmptyCommissionPayoutsTrackerComp
          emptyText={t("TRACK_COMM_EACH_MONTH")}
        />
      )}
    </Fragment>
  );
});

export default CommissionPayoutsTracker;
