import { gql, useQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Col, Divider, Row, Space } from "antd";
import { cloneDeep, isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { Fragment, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverTabs,
  filterAndFormatOptions,
  LazyListContent,
  EverLoader,
  EverButton,
  EverSelect,
  EverTg,
  EverModal,
  EverTooltip,
} from "~/v2/components";
import { useAbortiveLazyQuery } from "~/v2/components/CustomQueryHooks";

const QUOTA_CATEGORIES = gql`
  query AllQuotaCategoryTypes($year: Int!) {
    allQuotaCategoryTypes(year: $year) {
      label
      value
    }
  }
`;

const GET_PAYEE_FOR_QUOTA = gql`
  query PayeesForQuota(
    $quotaYear: String!
    $quotaCategory: String!
    $quotaSchedule: String!
    $limitValue: Int!
    $offsetValue: Int!
    $searchText: String
  ) {
    payeesForQuota(
      quotaYear: $quotaYear
      quotaCategory: $quotaCategory
      quotaSchedule: $quotaSchedule
      limitValue: $limitValue
      offsetValue: $offsetValue
      searchText: $searchText
    ) {
      headers
      data
    }
  }
`;

// const ModalTransition = {
//   transitionName: "",
//   maskTransitionName: "",
//   maskClosable: false,
//   closeIcon: <i className="esi-fw esi-close fz-20" />,
// };

const CompareIndividuals = observer((props) => {
  const {
    compareIndividuals,
    year,
    setQuota,
    quota,
    setIndividualPayees,
    setIndividualTeams,
  } = props;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tabKey, setTabKey] = useState("Monthly");
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [empDetails, setEmpDetails] = useState({});
  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  const hideQuotaCategories = (list) => {
    if (hasPermissions(RBAC_ROLES.VIEW_HIDDENQUOTAS)) {
      const quotas = [...list];
      return quotas;
    }

    const hideCategories = clientFeatures?.hideCategories ?? [];
    const quotas = [];
    list.forEach((quota) => {
      if (!hideCategories.includes(quota)) {
        quotas.push(quota);
      }
    });
    return quotas;
  };

  const [getPayeesForQuota, { variables, abort }] = useAbortiveLazyQuery(
    GET_PAYEE_FOR_QUOTA,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const headers = data?.payeesForQuota?.headers || [];
        let response = data?.payeesForQuota?.data || [];
        const fullNameIndex = headers.indexOf("fullName") ?? null;
        const emailIndex = headers.indexOf("employeeEmailId") ?? null;
        const newEmpDetails = cloneDeep(empDetails);
        if (!isNil(response)) {
          response = response.reduce((result, value, index, array) => {
            if (!(value[emailIndex] in newEmpDetails)) {
              newEmpDetails[value[emailIndex]] = value[fullNameIndex];
            }
            if (index % 2 === 0) {
              const pairArray = array.slice(index, index + 2);
              result.push(
                filterAndFormatOptions(pairArray, {
                  label: fullNameIndex,
                  value: emailIndex,
                })
              );
            }
            return result;
          }, []);
          setEmpDetails(newEmpDetails);
          variables.successCbk(response);
        } else {
          variables.failureCbk();
        }
      },
      onError: () => {
        variables.failureCbk();
      },
    }
  );

  const { loading } = useQuery(QUOTA_CATEGORIES, {
    variables: {
      year: year,
    },
    fetchPolicy: year ? "network-only" : "cache-only",
    onCompleted: (data) => {
      if (data && data.allQuotaCategoryTypes) {
        const allQuotas = data.allQuotaCategoryTypes;
        const quotas = hideQuotaCategories(allQuotas.map((x) => x.value));
        let quotaOptions = [];
        if (quotas.length > 0) {
          allQuotas.map((quota) => {
            if (quotas.includes(quota.value)) {
              quotaOptions.push({
                label: quota.label,
                value: quota.value,
              });
            }
          });
          setQuotaOptions(quotaOptions);
          setQuota(quotas[0]);
        } else {
          setQuotaOptions([]);
          setQuota(null);
        }
      }
    },
  });

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { limit, offset, searchTerm, successCbk, failureCbk } = params;
      await getPayeesForQuota({
        limitValue: limit,
        offsetValue: offset,
        searchText: searchTerm,
        quotaYear: String(year),
        quotaCategory: quota,
        quotaSchedule: tabKey,
        successCbk,
        failureCbk,
      });
    },
    deps: [quota, tabKey, year],
  };

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedMembers([]);
    setTabKey("Monthly");
  };

  const tabChange = (key) => {
    abort();
    setTabKey(key);
    setSelectedMembers([]);
  };

  return (
    <Fragment>
      <Divider className="!my-0 !mb-2">or</Divider>
      <EverButton
        block
        size="small"
        type="filled"
        color="primary"
        onClick={showModal}
      >
        Compare Individuals
      </EverButton>
      <EverModal
        title={
          <EverTg.Heading3 className=" text-ever-base-content">
            Compare Individual Payees
          </EverTg.Heading3>
        }
        visible={isModalVisible}
        onCancel={handleCancel}
        bodyStyle={{ padding: "0 24px" }}
        cancelButtonProps={{ className: "btn-cancel" }}
        footer={[
          <EverButton
            type="filled"
            color="error"
            key="cancel"
            size="small"
            onClick={handleCancel}
          >
            Cancel
          </EverButton>,
          <EverTooltip
            key="compare"
            title={selectedMembers
              .map((member) => empDetails[member])
              .join(", ")}
          >
            <EverButton
              type="filled"
              color="primary"
              size="small"
              disabled={selectedMembers.length === 0}
              onClick={(e) => {
                compareIndividuals(e, selectedMembers);
                handleCancel();
              }}
            >
              {selectedMembers.length
                ? `Compare (${selectedMembers.length})`
                : "Compare"}
            </EverButton>
          </EverTooltip>,
        ]}
      >
        <Row className="py-2">
          <Col span={24}>
            <Space align="center">
              <div className="!mr-2 fz-12  ">
                <EverTg.Caption className="text-ever-base-content">
                  {t("QUOTA_CATEGORY")}
                </EverTg.Caption>
              </div>
              <EverSelect
                placeholder={t("SELECT_QUOTA")}
                defaultValue={quota}
                bordered={true}
                onChange={(value) => {
                  abort();
                  setIndividualPayees([]);
                  setIndividualTeams([]);
                  setQuota(value);
                  setSelectedMembers([]);
                }}
                value={quota}
                className="rounded h-full w-40 ml-3"
                options={quotaOptions}
                suffixIcon={<ChevronDownIcon />}
                disabled={quotaOptions.length === 0}
                dropdownAlign={{ offset: [0, 6] }}
              ></EverSelect>
            </Space>
          </Col>
        </Row>
        <EverLoader
          indicatorType="spinner"
          spinning={loading}
          className="!static"
        >
          {isEmpty(quota) ? (
            <div className="flex justify-center py-5 text-ever-base-content">
              {t("QUOTA_NOT_SET")}
            </div>
          ) : (
            <>
              <Row gutter={16} className="border-top">
                <Col span={6} className="border-on-right !py-4">
                  <div className="mb-2">
                    <EverTg.Caption className="text-ever-base-content">
                      {t("QUOTA_SCHEDULE")}
                    </EverTg.Caption>
                  </div>
                  <EverTabs
                    activeKey={tabKey}
                    onChange={tabChange}
                    defaultActiveKey="1"
                    tabPosition="left"
                    className="block-style m-auto"
                  >
                    <EverTabs.TabPane
                      tab="Monthly"
                      key="Monthly"
                    ></EverTabs.TabPane>
                    <EverTabs.TabPane
                      tab="Quarterly"
                      key="Quarterly"
                    ></EverTabs.TabPane>
                    <EverTabs.TabPane
                      tab="Half-Yearly"
                      key="Halfyearly"
                    ></EverTabs.TabPane>
                    <EverTabs.TabPane
                      tab="Annual"
                      key="Annual"
                    ></EverTabs.TabPane>
                  </EverTabs>
                </Col>
                <Col span={18} className="!pt-4">
                  <div className="!mb-2">
                    <EverTg.Caption className="text-ever-base-content">
                      {tabKey} Payees
                    </EverTg.Caption>
                  </div>
                  <div className={`payeeList`}>
                    <LazyListContent
                      selectedKeys={selectedMembers}
                      updateSelectedKeys={setSelectedMembers}
                      listHeight={200}
                      skipSelectAll
                      showEllipsis
                      {...lazyLoadProps}
                    />
                  </div>
                </Col>
              </Row>
            </>
          )}
        </EverLoader>
      </EverModal>
    </Fragment>
  );
});

export default CompareIndividuals;
