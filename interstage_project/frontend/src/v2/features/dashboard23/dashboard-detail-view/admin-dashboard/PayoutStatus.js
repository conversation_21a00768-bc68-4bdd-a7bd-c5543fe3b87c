import { useQuery, gql } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import {
  UsersXIcon,
  FileLockIcon,
  CoinsHandIcon,
} from "@everstage/evericons/solid";
import { Row } from "antd";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState, Fragment } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { EverTg, EverLoader, EverSelect, EverLabel } from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";

const PAYOUT_STATUS = gql`
  query PayeeStatusCount {
    payeeStatusCount
  }
`;

const PayoutStatus = observer(() => {
  const [payoutStatusData, setPayoutStatusData] = useState();
  const [selectedMonth, setSelectedMonth] = useState("lastMonth");

  const { loading: loading, data: data } = useQuery(PAYOUT_STATUS, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (data && data.payeeStatusCount) {
      console.log("AdminD payout status card", data.payeeStatusCount);
      let res = {};
      let psData = JSON.parse(data.payeeStatusCount);
      res["unmappedPayees"] = psData.unmapped_payees_count;
      res["lastMonth"] = {
        activePayees: psData.active_payees_count.previous_month,
        lockedPayees: psData.locked_payees_count.previous_month,
        payrollSent: psData.payroll_sent_count.previous_month,
      };
      res["currentMonth"] = {
        activePayees: psData.active_payees_count.current_month,
        lockedPayees: psData.locked_payees_count.current_month,
        payrollSent: psData.payroll_sent_count.current_month,
      };
      setPayoutStatusData(res);
    }
  }, [data]);

  const todaysDate = moment();
  const lastMonthDate = moment().subtract(1, "month");

  const { t } = useTranslation();

  const formatDate = (date) => {
    return moment(date).format("MMM YYYY");
  };

  if (loading) {
    return <WidgetLoading />;
  }

  return (
    <Fragment>
      <div className="flex h-full flex-col">
        <div className="flex w-full items-center mb-6">
          <div className="flex-auto w-60 ">
            <div>
              <EverTg.Heading3 className="text-ever-base-content">
                {t("PAYOUT_STATUS")}
              </EverTg.Heading3>
            </div>
          </div>
          <div className="flex-auto w-40 ">
            <EverSelect
              className="w-full h-full"
              value={selectedMonth}
              onChange={(val) => {
                setSelectedMonth(val);
              }}
              suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
              disabled={loading}
              everListOptions
              size="small"
              getPopupContainer={({ parentNode }) => parentNode}
              dropdownAlign={{ offset: [0, 6] }}
            >
              <EverSelect.Option
                value="lastMonth"
                label={formatDate(lastMonthDate)}
              >
                {formatDate(lastMonthDate)}
              </EverSelect.Option>
              <EverSelect.Option
                value="currentMonth"
                label={formatDate(todaysDate)}
              >
                {formatDate(todaysDate)}
              </EverSelect.Option>
            </EverSelect>
          </div>
        </div>
        <div>
          <EverLoader
            indicatorType="spinner"
            spinning={loading}
            className="!static"
            wrapperClassName="rounded-xl"
          >
            <Row>
              <div className="w-full">
                <div className="flex  items-center justify-center mb-4">
                  <div className="w-52 pr-1 text-right flex flex-row">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-10 bg-opacity-10 mr-2">
                      <CoinsHandIcon className="w-5 h-5 text-ever-chartColors-10" />
                    </div>

                    <EverLabel className="justify-end text-ever-base-content ">
                      Payroll sent
                    </EverLabel>
                  </div>
                  <div className="w-24 text-left">
                    <Link
                      className={
                        "text-sm font-medium text-ever-primary hover:text-ever-primary"
                      }
                      to="/commissions"
                      state={{ statusPeriod: selectedMonth }}
                    >
                      <span className="tracking-[-1px]">
                        {payoutStatusData
                          ? `${payoutStatusData[selectedMonth].payrollSent} / ${payoutStatusData[selectedMonth].activePayees}`
                          : "0 / 0"}
                      </span>
                    </Link>
                  </div>
                </div>
                <div className="flex justify-center items-center mb-4">
                  <div className=" w-52 pr-1 text-right flex flex-row">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-20 bg-opacity-10 mr-2">
                      <FileLockIcon className="w-5 h-5 text-ever-chartColors-20" />
                    </div>
                    <EverLabel className="justify-end text-ever-base-content ">
                      Locked Statements
                    </EverLabel>
                  </div>
                  <div className="w-24 text-left">
                    <Link
                      className={
                        "text-sm font-medium text-ever-primary hover:text-ever-primary"
                      }
                      to="/commissions"
                      state={{ statusPeriod: selectedMonth }}
                    >
                      <span className="tracking-[-1px]">
                        {payoutStatusData
                          ? `${payoutStatusData[selectedMonth].lockedPayees} / ${payoutStatusData[selectedMonth].activePayees}`
                          : "0 / 0"}
                      </span>
                    </Link>
                  </div>
                </div>
                <div className="flex items-center justify-center">
                  <div className=" w-52 text-right pr-1 flex flex-row">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-29 bg-opacity-10 mr-2">
                      <UsersXIcon className="w-5 h-5 text-ever-chartColors-29" />
                    </div>
                    <EverLabel className="justify-end text-ever-base-content">
                      Unmapped payees
                    </EverLabel>
                  </div>
                  <div className="w-24 text-left">
                    <Link
                      className={
                        "text-sm font-medium text-ever-primary hover:text-ever-primary"
                      }
                      to="/users"
                      state={{ defaultTab: "4", fromDashboard: true }}
                    >
                      <span className="tracking-[-1px]">
                        {payoutStatusData ? payoutStatusData.unmappedPayees : 0}
                      </span>
                    </Link>
                  </div>
                </div>
              </div>
            </Row>
          </EverLoader>
        </div>
      </div>
    </Fragment>
  );
});
export default PayoutStatus;
