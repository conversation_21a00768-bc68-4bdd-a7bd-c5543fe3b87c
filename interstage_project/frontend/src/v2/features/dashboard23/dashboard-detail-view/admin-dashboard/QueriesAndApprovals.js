import { useQuery, gql } from "@apollo/client";
import {
  BookmarkXIcon,
  UsersCheckIcon,
  MarkIcon,
} from "@everstage/evericons/solid";
import { Row, Col } from "antd";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverTg,
  EverButton,
  EverLoader,
  EverBadge,
  EverLabel,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";

const QUERIESANDAPPROVALS = gql`
  query DrsCounts($payee_email: String!) {
    drsCounts(email: $payee_email)
  }
`;

const QueriesAndApprovals = observer(() => {
  const { email } = useAuthStore();

  const [drsData, setDrsData] = useState();

  const { loading, data } = useQuery(QUERIESANDAPPROVALS, {
    variables: {
      payee_email: email,
    },
    fetchPolicy: "no-cache",
  });
  const navigate = useNavigate();

  useEffect(() => {
    if (data) {
      console.log("AdminD drs", data.drsCounts);
      setDrsData(JSON.parse(data.drsCounts));
    }
  }, [data]);

  if (loading) {
    return <WidgetLoading />;
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center w-full mb-6">
        <div className="flex-auto w-full">
          <div>
            <EverTg.Heading3 className="text-ever-base-content">
              Queries & Approvals
            </EverTg.Heading3>
          </div>
        </div>
      </div>

      <EverLoader
        indicatorType="spinner"
        spinning={loading}
        className="!static"
        wrapperClassName="rounded-xl"
      >
        <div className="flex flex-col justify-between">
          <Row>
            <Col span={24}>
              <div>
                <DisplayComponent
                  text="Assigned to me"
                  value={drsData ? drsData.assigned_to_me : 0}
                  linkTo="/queries/assignedToMe"
                  type="success"
                  icon={
                    <MarkIcon className="w-5 h-5 text-ever-chartColors-26" />
                  }
                  iconContainerClass={
                    "w-10 h-10 rounded-full flex items-center justify-center mr-2 bg-ever-chartColors-26 bg-opacity-10"
                  }
                />
                <DisplayComponent
                  text="Unassigned"
                  value={drsData ? drsData.unassigned : 0}
                  linkTo="/queries/open"
                  type="error"
                  icon={
                    <BookmarkXIcon className="w-5 h-5 text-ever-chartColors-4" />
                  }
                  iconContainerClass={
                    "w-10 h-10 rounded-full flex items-center justify-center mr-2 bg-ever-chartColors-4 bg-opacity-10"
                  }
                />
                <DisplayComponent
                  text="Assigned to others"
                  value={drsData ? drsData.assigned_to_others : 0}
                  linkTo="/queries/allTickets"
                  type="info"
                  icon={
                    <UsersCheckIcon className="w-5 h-5 text-ever-chartColors-1" />
                  }
                  iconContainerClass={
                    " w-10 h-10 rounded-full flex items-center justify-center mr-2 bg-ever-chartColors-1 bg-opacity-10"
                  }
                />
              </div>
            </Col>
          </Row>
          <RBACProtectedComponent permissionId={RBAC_ROLES.CREATE_QUERIES}>
            <Row className="flex w-full justify-center">
              <div className="w-full">
                <EverButton
                  type="filled"
                  color="base"
                  size="medium"
                  className={"w-full"}
                  onClick={() => {
                    navigate("/queries/allTickets");
                  }}
                >
                  Raise Query
                </EverButton>
              </div>
            </Row>
          </RBACProtectedComponent>
        </div>
      </EverLoader>
    </div>
  );
});
export default QueriesAndApprovals;

const DisplayComponent = observer((props) => {
  const { text, value, linkTo, type, icon, iconContainerClass } = props;
  const navigate = useNavigate();

  return (
    <div className="flex justify-center mb-4">
      <div className="w-52 flex flex-row pr-1 text-right">
        <div className={iconContainerClass}>{icon}</div>
        <EverLabel className="justify-end text-ever-base-content">
          {text}
        </EverLabel>
      </div>
      <div className="w-24  flex items-center ">
        <EverBadge
          type={type}
          outline={false}
          onClick={() => {
            navigate(linkTo);
          }}
          title={value}
          className={"!cursor-pointer"}
        />
      </div>
    </div>
  );

  // return (
  //   <tbody>
  //     <tr>
  //       <td className="text-ever-base-content text-sm w-[50%] !pr-4 !py-2.5">
  //         {text} :
  //       </td>
  //       <td className="w-[50%] !pl-4 !py-2.5">
  //         <EverBadge
  //           type={type}
  //           outline={false}
  //           onClick={() => {
  //             navigate(linkTo);
  //           }}
  //           title={value}
  //         />
  //       </td>
  //     </tr>
  //   </tbody>
  // );
});
