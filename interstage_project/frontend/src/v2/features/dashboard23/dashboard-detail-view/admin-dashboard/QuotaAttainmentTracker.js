import { gql, useLazyQuery, useQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Col, Empty, Layout, Row } from "antd";
import cx from "classix";
import { isEmpty, isNil, sortBy } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { QUOTA_CATEGORIES, RBAC_ROLES } from "~/Enums";
import { QUOTA_MESSAGES } from "~/Enums/LocalizationMessages";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { getClientFiscalYear, getUpToPeriodForYear } from "~/Utils/DateUtils";
import { sortPrimaryQuota } from "~/Utils/QuotaUtils";
import {
  useCurrentTheme,
  useAbortiveLazyQuery,
  filterAndFormatOptions,
  LazySelect,
  EverLoader,
  EverSelect,
  EverDatePicker,
  EverButtonGroup,
  EverButton,
  EverTg,
  EverBadge,
} from "~/v2/components";
import {
  ALL_MANAGERS,
  getTeamOption,
  GET_EMPLOYEE,
} from "~/v2/features/dashboard23/dashboard-detail-view/admin-dashboard";
import CompareIndividuals from "~/v2/features/dashboard23/dashboard-detail-view/admin-dashboard/CompareIndividuals";
import FusionTooltip from "~/v2/features/dashboard23/dashboard-detail-view/FusionTooltip";
import { useFusionTooltip } from "~/v2/features/dashboard23/dashboard-detail-view/useFusionTooltip";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyQuotaAttainmentTracker } from "~/v2/images";

const { Content } = Layout;

const QA_TRACKER = gql`
  query QuotaAttainmentsCategoryForTeam(
    $teamOwnerId: String!
    $year: Int!
    $category: String
  ) {
    quotaAttainmentsCategoryForTeam(
      teamOwnerId: $teamOwnerId
      year: $year
      category: $category
    ) {
      quotas
      payeesInfo
      quotaCategoryNames
      displayNames {
        label
        value
      }
    }
  }
`;

const QA_TRACKER_INDV = gql`
  query QuotaAttainmentForPayeeAndTeams(
    $teamOwnerIds: [String]!
    $payeeIds: [String]!
    $year: String!
    $quotaCategory: String!
  ) {
    quotaAttainmentForPayeeAndTeams(
      teamOwnerIds: $teamOwnerIds
      payeeIds: $payeeIds
      year: $year
      quotaCategory: $quotaCategory
    ) {
      quotas
      payeesInfo
      displayNames {
        label
        value
      }
    }
  }
`;

/*const cols = [
  {
    title: "Payee",
    dataIndex: "payee",
    key: "payee",
  },
  {
    title: "QA",
    dataIndex: "qa",
    key: "qa",
    render: (text) => {
      return parseFloat(text).toFixed(2) + "%";
    },
  },
];*/

// const toolTipDom = (tableData, isPercent, base, t) => {
//   return `<table width="296"  style="text-align: center;">
//       <thead >
//         <tr">
//           <td colSpan="2" style="color: ${
//             base.content.DEFAULT
//           }; font-size: 14px; font-weight-600; text-align:left; padding-left: 24px;padding-right: 24px; height:60px">
//             <b>${`${t("QUOTA_ATTAINMENT")} for $label${
//               tableData?.length > 10 ? " (Top 10)" : ""
//             }`}</b>
//           </td>
//         </tr>
//       </thead>
//       <tbody style="padding-bottom:8px">
//         ${
//           tableData !== undefined &&
//           tableData.slice(0, 10).map(
//             (data) =>
//               `<tr ">
//             <td className="text-left" style="text-align:left;font-size: 14px;padding-left: 24px;padding-right: 24px;height:40px">${
//               data.payee
//             }</td>
//             <td className="text-left"  style="text-align:left; font-size: 14px;padding-left: 24px;padding-right: 24px;height:40px">${
//               isPercent ? data.qa : data.cum_qe
//             }${isPercent ? "%" : ""}</td>
//           </tr>`
//           )
//         }
//       </tbody>
//     </table>`;
// };

const ToolTipComponent = ({ tableData, isPercent, t, tooltipLabel }) => {
  const parseDate = moment(tooltipLabel, "MMM");
  const formattedDate = parseDate.format("MMMM");
  const isValidDate = parseDate.isValid();

  return (
    <table width="296" className="text-center">
      <thead>
        <tr>
          <td
            colSpan="2"
            className="text-ever-base-content text-md font-semibold text-left px-6 py-4 h-16"
          >
            <b>
              {t("QUOTA_ATTAINMENT")} for{" "}
              {isValidDate ? formattedDate : tooltipLabel}{" "}
              {tableData?.length > 10 ? " (Top 10)" : ""}
            </b>
          </td>
        </tr>
      </thead>
      <tbody className="pb-2">
        {tableData !== undefined
          ? tableData.slice(0, 10).map((data) => (
              <tr key={data.id}>
                <td className="text-left text-md px-6 py-4 h-10">
                  {data.payee}
                </td>
                <td className="text-left text-md px-6 py-4 h-10">
                  {isPercent ? data.qa : data.cum_qe}
                  {isPercent ? "%" : ""}
                </td>
              </tr>
            ))
          : null}
      </tbody>
    </table>
  );
};

const QuotaAttainmentTracker = observer((props) => {
  const { chartColors, base, primary } = useCurrentTheme();
  const { store } = props;
  const themeColor = Object.values(chartColors || {});
  const baseFontColor = base.content.mid || "";
  const valueColor = base.content.DEFAULT || "";
  const xAxisLineColor = base[300];
  const { revLeader } = store;
  const [fiscalPeriods, setFiscalPeriods] = useState();
  const [selectedTeam, setSelectedTeam] = useState(revLeader);
  const [qaTrackerData, setQaTrackerData] = useState();
  const [selectedChartMonth, setSelectedChartMonth] = useState(null);
  const [tableDS, setTableDS] = useState();
  const [switchToIndv, setSwitchToIndv] = useState(false);
  //const [treeSelectVal, setTreeSelectVal] = useState();
  //const [tempSelectedTeams, setTempSelectedTeams] = useState();
  //const [tempSelectedPayees, setTempSelectedPayees] = useState();
  const [indvSelectedTeams, setIndvSelectedTeams] = useState();
  const [indvSelectedPayees, setIndvSelectedPayees] = useState();
  const [indvCategory, setIndvCategory] = useState();
  const [indvDataSet, setIndvDataSet] = useState([]);
  const [periodLength, setPeriodLength] = useState();
  const [year, setYear] = useState();
  const [quota, setQuota] = useState(null);
  const [qaQtd, setQaQtd] = useState(false);
  const [individualQuota, setIndividualQuota] = useState(null);
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [chartMessage, setChartMessage] = useState("No data to display");
  const [selectOpen, setSelectOpen] = useState(false);
  const [ytd, setYtd] = useState(0);
  const [isQtd, setIsQtd] = useState(false);
  const [isPercent, setIsPercent] = useState(true);
  const [selectedOption, setSelectedOption] = useState({});
  const [initialOptions, setInitialOptions] = useState([]);
  const [managersLoaded, setManagersLoaded] = useState(false);
  const [quotaAttainmentLoaded, setQuotaAttainmentLoaded] = useState(false);
  const [showChart, setShowChart] = useState(true);

  const fiscalYearMonth = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const { hasPermissions } = useUserPermissionStore();

  const initialLoad = useRef(true);
  const tempQuota = useRef(null);

  const { t } = useTranslation();
  const location = useLocation();

  const {
    tooltipRef,
    showTooltip,
    tooltipContent,
    tooltipLabel,
    show,
    hide,
    updateTooltipPosition,
  } = useFusionTooltip();

  const [getEmployee, { loading: employeeLoading, variables: empVariables }] =
    useLazyQuery(GET_EMPLOYEE, {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        setSelectedTeam(empVariables.emailId);
        setSelectedOption(getTeamOption(data.employeeNameDetail));
      },
    });

  const [getManager, { variables, abort }] = useAbortiveLazyQuery(
    ALL_MANAGERS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const headers = data?.allManagersWithLimit?.headers || [];
        const fullNameIndex =
          headers.indexOf("reportingManagerFullName") ?? null;
        const firstNameIndex = headers.indexOf("firstName") ?? null;
        const emailIndex = headers.indexOf("employeeEmailId") ?? null;
        let response = data?.allManagersWithLimit?.data || [];
        if (!isNil(response)) {
          response = filterAndFormatOptions(response, {
            label: (option) => `${option[fullNameIndex]} & Team`,
            value: emailIndex,
            key: firstNameIndex,
          });
          if (initialLoad.current) {
            setInitialOptions(response);
            initialLoad.current = false;
          }
          variables.successCbk(response);
        } else {
          variables.failureCbk();
        }
        setManagersLoaded(true);
      },
      onError: () => {
        variables.failureCbk();
        setManagersLoaded(true);
      },
    }
  );

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { page, searchTerm, options, limit, successCbk, failureCbk } =
        params;
      await getManager({
        limitValue: limit,
        ...(page > 0 &&
          options.length > 0 && {
            firstName: options[options.length - 1].key.split("##::##")[0],
            email: options[options.length - 1].value,
          }),
        ...(searchTerm && { searchTerm }),
        successCbk,
        failureCbk,
      });
    },
  };

  const [getQATracker, { data: trackerData, loading }] = useLazyQuery(
    QA_TRACKER,
    {
      fetchPolicy: "network-only",
    }
  );

  const { loading: iLoading, data: iData } = useQuery(QA_TRACKER_INDV, {
    variables: {
      teamOwnerIds: indvSelectedTeams,
      payeeIds: indvSelectedPayees,
      year: year && String(year.year()),
      quotaCategory: individualQuota,
    },
    fetchPolicy:
      (!isEmpty(indvSelectedTeams) || !isEmpty(indvSelectedPayees)) &&
      year &&
      individualQuota
        ? "network-only"
        : "cache-only",
  });

  const getQATrackerData = (selectedTeam, year, quota = null) => {
    getQATracker({
      variables: {
        teamOwnerId: selectedTeam,
        year: year,
        ...(quota && { category: quota }),
      },
    });
  };

  const refreshChart = () => {
    /* A workaround for fusioncharts to re-render */
    setTimeout(() => setShowChart(true), 1000); //1s for same route change
  };

  useEffect(() => {
    if (location.key) {
      setShowChart(false);
    }
  }, [location.key]);

  useEffect(() => {
    setShowChart(false);
  }, [isQtd]);

  useEffect(() => {
    if (!showChart) {
      refreshChart();
    }
  }, [showChart]);

  useEffect(() => {
    if (selectedTeam && year?.year()) {
      getQATrackerData(selectedTeam, year.year());
    }
  }, [selectedTeam, year]);

  useEffect(() => {
    if (selectedTeam && year?.year() && quota && tempQuota.current !== quota) {
      getQATrackerData(selectedTeam, year.year(), quota);
    }
  }, [quota]);

  useEffect(() => {
    if (!switchToIndv && isEmpty(selectedTeam) && revLeader !== null) {
      if (revLeader) {
        getEmployee({
          variables: {
            emailId: revLeader,
          },
        });
      } else if (initialOptions.length) {
        setSelectedTeam(initialOptions[0].value);
        setSelectedOption(initialOptions[0]);
      } else {
        // Quota Attainment will not be obtained since there is no team to select.
        // So, updating this will stop the loading skeleton.
        setQuotaAttainmentLoaded(true);
      }
    }
  }, [selectedTeam, revLeader, initialOptions]);

  const updateChartData = (props = {}) => {
    const { data = [], msg = "No data to display", ytd = 0 } = props;
    setQaTrackerData(data);
    setChartMessage(msg);
    setYtd(ytd);
  };

  useEffect(() => {
    let allData = {};
    if (trackerData?.quotaAttainmentsCategoryForTeam?.quotas && !switchToIndv) {
      allData = JSON.parse(trackerData.quotaAttainmentsCategoryForTeam.quotas);
    }
    if (!("error" in allData)) {
      if (!isEmpty(allData)) {
        if (
          !isEmpty(
            trackerData.quotaAttainmentsCategoryForTeam.quotaCategoryNames
          )
        ) {
          const quotaOpts =
            trackerData.quotaAttainmentsCategoryForTeam.displayNames.map(
              (category) => ({
                label:
                  category.label === QUOTA_CATEGORIES.PRIMARY_QUOTA
                    ? QUOTA_MESSAGES.PRIMARY_QUOTA
                    : category.label,
                value: category.value,
              })
            );
          setQuotaOptions(quotaOpts);
        }
        tempQuota.current = Object.keys(allData)[0];
        const currentQuota = quota || tempQuota.current;
        if (isEmpty(quota)) {
          setQuota(tempQuota.current);
        }
        setQaQtd(!isEmpty(allData[currentQuota]?.team_qa_qtd));
        updateChartDetails(tempQuota.current);
      }
    } else {
      setQuotaOptions([]);
      setQuota(null);
      updateChartData();
    }
    if (!isEmpty(trackerData)) {
      setQuotaAttainmentLoaded(true);
    }
  }, [trackerData]);

  useEffect(() => {
    let allData = {};
    if (switchToIndv && iData?.quotaAttainmentForPayeeAndTeams?.quotas) {
      allData = hideCategoryOptions(
        JSON.parse(iData.quotaAttainmentForPayeeAndTeams.quotas)
      );
    }
    let systemNameToDisplayName = {};
    if (iData?.quotaAttainmentForPayeeAndTeams?.displayNames) {
      iData.quotaAttainmentForPayeeAndTeams.displayNames.forEach((item) => {
        systemNameToDisplayName[item.value] = item.label;
      });
    }

    if (!("error" in allData)) {
      if (!isEmpty(allData)) {
        let quotaOpts = [];
        Object.keys(allData).map((cat) => {
          quotaOpts.push({
            label:
              cat === QUOTA_CATEGORIES.PRIMARY_QUOTA
                ? t("PRIMARY_QUOTA")
                : systemNameToDisplayName[cat],
            value: cat,
          });
        });
        setQuotaOptions(sortPrimaryQuota(quotaOpts));
        Object.keys(allData).includes(QUOTA_CATEGORIES.PRIMARY_QUOTA)
          ? setQuota(QUOTA_CATEGORIES.PRIMARY_QUOTA)
          : setQuota(Object.keys(allData)[0]);
      }
    } else {
      setQuotaOptions([]);
      setQuota(null);
    }
  }, [iData]);

  const getFiscalPeriods = (fiscalYearMonth, periodLength, year) => {
    let fiscalPeriods = [];
    if (periodLength === 12) {
      let monthArr = moment.monthsShort();
      fiscalPeriods = monthArr
        .slice(fiscalYearMonth, monthArr.length)
        .concat(monthArr.slice(0, fiscalYearMonth));
    } else if (periodLength === 4) {
      fiscalPeriods = ["Q1", "Q2", "Q3", "Q4"];
    } else if (periodLength === 2) {
      fiscalPeriods = ["H1", "H2"];
    } else if (periodLength === 1) {
      fiscalPeriods = ["Annual"];
      setFiscalPeriods(["Annual"]);
    } else {
      fiscalPeriods = null;
    }

    if (year && year.year() == getClientFiscalYear(fiscalYearMonth).year()) {
      let upToMonth = getUpToPeriodForYear(fiscalYearMonth, periodLength);
      fiscalPeriods = fiscalPeriods && fiscalPeriods.slice(0, upToMonth);
    }

    return fiscalPeriods;
  };

  const updateChartDetails = (quota) => {
    if (
      trackerData?.quotaAttainmentsCategoryForTeam?.quotas &&
      !switchToIndv &&
      quota
    ) {
      let allData = JSON.parse(
        trackerData.quotaAttainmentsCategoryForTeam.quotas
      );
      if (!("error" in allData) && quota in allData) {
        setChartMessage(null);
        let qaData = isQtd
          ? allData[quota].team_qa_qtd
          : allData[quota].team_qa;
        let qaCurrentPeriod = allData[quota].curr_period;
        const periodLength = Object.keys(qaData).length;
        setPeriodLength(periodLength);
        const fiscalPeriods = getFiscalPeriods(
          fiscalYearMonth,
          periodLength,
          year
        );
        let chartObj = {
          categories: [
            {
              category: [],
            },
          ],
          dataset: [
            {
              color: themeColor[0],
              data: [],
            },
            {
              color: themeColor[51],
              data: [],
            },
          ],
        };
        fiscalPeriods &&
          fiscalPeriods.forEach((fm) => {
            let fiscalPeriodIndex = fiscalPeriods.indexOf(fm) + 1;
            let qa = qaData[fiscalPeriodIndex]
              ? isPercent
                ? qaData[fiscalPeriodIndex].team_qa
                : qaData[fiscalPeriodIndex].cum_qe
              : 0;

            chartObj.categories[0].category.push({ label: fm });
            const val = Number(qa);
            const threshold = isPercent ? 100 : qaData[fiscalPeriodIndex]?.qv;
            chartObj.dataset[0].data.push({
              value: String(val < threshold ? val : threshold),
            });
            chartObj.dataset[1].data.push({
              value: String(val < threshold ? 0 : val - threshold),
              showValue: val < threshold ? false : true,
            });

            // chartData.push({
            //   label: fm,
            //   value: qa,
            //   toolText: "$dataValue",
            // });
          });
        updateChartData({
          data: chartObj,
          msg: qaCurrentPeriod.index + 1,
          ytd: isPercent ? allData[quota].ytd : allData[quota].ytd_cum_qe,
        });
      } else {
        updateChartData({ msg: allData["error"] });
      }
    } else {
      updateChartData();
    }
  };

  useEffect(() => {
    updateChartDetails(quota);
  }, [switchToIndv, isQtd, isPercent]);

  useEffect(() => {
    if (switchToIndv) {
      if (iData?.quotaAttainmentForPayeeAndTeams?.quotas && quota) {
        let periodLength;
        let iApiData = JSON.parse(iData.quotaAttainmentForPayeeAndTeams.quotas);
        let ds = [];
        const payeesInfo = JSON.parse(
          iData.quotaAttainmentForPayeeAndTeams.payeesInfo
        );
        setQaQtd(
          !isEmpty(iApiData[individualQuota]?.team_qa_qtd) ||
            !isEmpty(iApiData[individualQuota]?.payee_qa_qtd)
        );
        if (
          !("error" in iApiData) &&
          quota in iApiData &&
          !isEmpty(iApiData[quota].team_qa)
        ) {
          let firstTeamData = isQtd
            ? Object.values(iApiData[quota].team_qa_qtd)[0]
            : Object.values(iApiData[quota].team_qa)[0];
          periodLength = Object.keys(firstTeamData).length;
          Object.entries(
            isQtd ? iApiData[quota].team_qa_qtd : iApiData[quota].team_qa
          ).forEach(([key, value]) => {
            ds.push({
              seriesname: `${payeesInfo[key]} & Team`,
              data: Object.values(value).map((x) => {
                return { value: x };
              }),
            });
          });
        }
        if (
          !("error" in iApiData) &&
          quota in iApiData &&
          !isEmpty(iApiData[quota].payee_qa)
        ) {
          let firstPayeeData = isQtd
            ? Object.values(iApiData[quota].payee_qa_qtd)[0]
            : Object.values(iApiData[quota].payee_qa)[0];
          periodLength = Object.keys(firstPayeeData).length;
          let quotaData = isQtd
            ? isPercent
              ? iApiData[quota].payee_qa_qtd
              : iApiData[quota].payee_cum_qe_qtd
            : isPercent
            ? iApiData[quota].payee_qa
            : iApiData[quota].payee_cum_qe;
          Object.entries(quotaData).forEach(([key, value]) => {
            ds.push({
              seriesname: payeesInfo[key],
              data: Object.values(value).map((x) => {
                return { value: x };
              }),
            });
          });
        }
        setIndvDataSet(ds);
        setPeriodLength(periodLength);
        // setYtd(iApiData[quota]?.ytd);
        setYtd(isPercent ? iApiData[quota]?.ytd : iApiData[quota]?.ytd_cum_qe);
      } else {
        setIndvDataSet([]);
        setYtd(0);
      }
    }
  }, [iData, switchToIndv, quota, isQtd, isPercent]);

  useEffect(() => {
    const fiscalPeriods = getFiscalPeriods(fiscalYearMonth, periodLength, year);
    setFiscalPeriods(fiscalPeriods);
  }, [fiscalYearMonth, periodLength, year]);

  useEffect(() => {
    let cat =
      fiscalPeriods &&
      switchToIndv &&
      fiscalPeriods.map((fm) => {
        return { label: fm };
      });
    setIndvCategory(cat);
  }, [fiscalPeriods, switchToIndv]);

  useEffect(() => {
    if (fiscalYearMonth != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonth));
    }
  }, [fiscalYearMonth]);

  useEffect(() => {
    if (
      selectedChartMonth &&
      trackerData?.quotaAttainmentsCategoryForTeam?.quotas
    ) {
      let allData = JSON.parse(
        trackerData.quotaAttainmentsCategoryForTeam.quotas
      );
      if (!("error" in allData) && quota in allData) {
        const payeesInfo = JSON.parse(
          trackerData.quotaAttainmentsCategoryForTeam.payeesInfo
        );
        let qaData = isQtd
          ? allData[quota].team_qa_qtd
          : allData[quota].team_qa;
        let reporteeList =
          qaData &&
          selectedChartMonth in qaData &&
          qaData[selectedChartMonth].reportee_list;
        if (!isEmpty(reporteeList)) {
          let ds = [];
          reporteeList.forEach((r) => {
            ds.push({
              key: r.employee_email_id,
              payee: payeesInfo[r.employee_email_id],
              qa: Number(r.qa).toFixed(2),
              cum_qe: Number(r.cum_qe).toFixed(2),
            });
          });
          setTableDS(
            sortBy(ds, [
              (o) => {
                return isPercent ? parseFloat(o.qa) : parseFloat(o.cum_qe);
              },
            ]).reverse()
          );
        } else {
          setTableDS([]);
        }
      } else {
        setTableDS([]);
      }
    } else {
      setTableDS([]);
    }
  }, [selectedChartMonth, trackerData, isQtd]);

  const hideCategoryOptions = (quotaOptions) => {
    if (!("error" in quotaOptions)) {
      const hideCategories = hasPermissions(RBAC_ROLES.VIEW_HIDDENQUOTAS)
        ? []
        : clientFeatures?.hideCategories || [];
      const options = { ...quotaOptions };
      hideCategories.forEach((category) => {
        delete options[category];
      });
      return options;
    } else {
      return quotaOptions;
    }
  };

  const onHandleChange = (value, option) => {
    setQuota(null);
    setSwitchToIndv(false);
    setSelectedTeam(value);
    setSelectedChartMonth(null);
    setIsQtd(false);
    setIsPercent(true);
    setSelectedOption(option);
  };

  const compareIndividuals = (event, members) => {
    setSwitchToIndv(true);
    setSelectedTeam(null);
    setIndvSelectedTeams([]);
    setIndvSelectedPayees(members);
    setQuota(individualQuota);
  };

  const dataSource = {
    chart: {
      paletteColors: themeColor,
      baseFont: "Inter",
      valueFont: "Inter",
      baseFontSize: 12,
      valueFontSize: 12,
      showShadow: 0,
      showValues: 1,
      showSum: 1,
      showHoverEffect: 0,
      showXAxisLine: 1,
      xAxisLineColor: xAxisLineColor,
      canvasTopPadding: 20,
      formatNumberScale: 1,
      numberSuffix: isPercent ? " %" : "",
      divLineAlpha: 0,
      theme: "fusion",
      baseFontColor: baseFontColor,
      valueFontColor: valueColor,
      // plottooltext: qaTrackerData
      //   ? toolTipDom(tableDS, isPercent, base, t).replace(/,/g, "")
      //   : "",
      toolTipBgColor: base.DEFAULT,
      toolTipColor: base.content.DEFAULT,
      toolTipBorderColor: base?.[400],
      chartLeftMargin: 20,
      chartRightMargin: 20,
      bgColor: base.DEFAULT,
      showToolTip: qaTrackerData ? 0 : 1,
    },
    ...qaTrackerData,
    ...(isPercent && {
      trendlines: [
        {
          line: [{ startValue: 100, showOnTop: "1", dashed: "1" }],
        },
      ],
    }),
  };

  const indvDataSource = {
    chart: {
      showhovereffect: "1",
      baseFontColor: baseFontColor,
      valueFontColor: valueColor,
      formatNumberScale: 0,
      numbersuffix: isPercent ? " %" : "",
      drawcrossline: "1",
      plottooltext: "<b>$dataValue</b> - $seriesName",
      divLineColor: base.DEFAULT,
      baseFontSize: 12,

      valueFontSize: 12,
      showXAxisLine: "1",
      showYAxisLine: "1",
      showLegend: "0",
      theme: "fusion",
      chartLeftMargin: 20,
      chartRightMargin: 20,
      toolTipBgColor: base?.content?.DEFAULT,
      toolTipColor: primary?.ring,
      toolTipBorderColor: base?.content?.DEFAULT,

      bgColor: base.DEFAULT,
    },
    categories: [
      {
        category: indvCategory,
      },
    ],
    dataset: indvDataSet,
  };

  const handleMouseMove = (evt) => {
    const { clientX, clientY } = evt;
    updateTooltipPosition(clientX, clientY);
  };

  const selectedTeamLoading =
    (revLeader || !isEmpty(initialOptions)) && isEmpty(selectedTeam);
  const isLoading =
    loading ||
    iLoading ||
    employeeLoading ||
    (!switchToIndv && selectedTeamLoading) ||
    (switchToIndv && isEmpty(indvSelectedPayees));
  const showSkeleton = !managersLoaded || !quotaAttainmentLoaded;

  const renderCategory = () => {
    return (
      <>
        {switchToIndv ? (
          indvDataSet && indvCategory ? (
            <>
              {((!isEmpty(indvDataSource) && periodLength === 12 && qaQtd) ||
                isQtd) && (
                <>
                  <div>
                    <EverButtonGroup
                      className="bg-ever-base-200 mr-2"
                      activeBtnType="text"
                      activeBtnColor="primary"
                      defActiveBtnIndex={isQtd ? 1 : 0}
                      size="small"
                      activeButtonClassname="shadow-sm"
                    >
                      <EverButton
                        onClick={() => {
                          setIsQtd(false);
                        }}
                      >
                        Monthly
                      </EverButton>
                      <EverButton
                        onClick={() => {
                          setIsQtd(true);
                        }}
                      >
                        Quarterly
                      </EverButton>
                    </EverButtonGroup>
                  </div>
                </>
              )}
              {!isEmpty(indvDataSource) &&
                indvSelectedTeams &&
                indvSelectedTeams.length === 0 && (
                  <div className="mr-2">
                    <EverButtonGroup
                      className="bg-ever-base-200"
                      activeBtnType="text"
                      activeBtnColor="primary"
                      defActiveBtnIndex={isPercent ? 0 : 1}
                      size="small"
                      activeButtonClassname="shadow-sm"
                    >
                      <EverButton
                        onClick={() => {
                          setIsPercent(true);
                        }}
                      >
                        %
                      </EverButton>
                      <EverButton
                        onClick={() => {
                          setIsPercent(false);
                        }}
                      >
                        #
                      </EverButton>
                    </EverButtonGroup>
                  </div>
                )}
            </>
          ) : null
        ) : (
          <>
            {((!isEmpty(qaTrackerData) && periodLength === 12 && qaQtd) ||
              isQtd) && (
              <>
                <div>
                  <EverButtonGroup
                    className="bg-ever-base-200"
                    activeBtnType="text"
                    activeBtnColor="primary"
                    defActiveBtnIndex={isQtd ? 1 : 0}
                    size="small"
                    activeButtonClassname="shadow-sm"
                  >
                    <EverButton
                      onClick={() => {
                        setIsQtd(false);
                      }}
                    >
                      Monthly
                    </EverButton>
                    <EverButton
                      onClick={() => {
                        setIsQtd(true);
                      }}
                    >
                      Quarterly
                    </EverButton>
                  </EverButtonGroup>
                </div>
              </>
            )}
            {!isEmpty(qaTrackerData) && (
              <div className="mr-2">
                <EverButtonGroup
                  className="bg-ever-base-200"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={isPercent ? 0 : 1}
                  size="small"
                  activeButtonClassname="shadow-sm"
                >
                  <EverButton
                    onClick={() => {
                      setIsPercent(true);
                    }}
                  >
                    %
                  </EverButton>
                  <EverButton
                    onClick={() => {
                      setIsPercent(false);
                    }}
                  >
                    #
                  </EverButton>
                </EverButtonGroup>
              </div>
            )}
          </>
        )}
      </>
    );
  };

  return (
    <>
      {showSkeleton && <WidgetLoading />}
      <div className={cx("qa-tracker-card", showSkeleton && "hidden")}>
        <>
          <div className="flex mb-4">
            <div>
              <EverTg.Heading3 className=" text-ever-base-content">
                {t("QUOTA_ATTAINMENT_TRACKER")}
              </EverTg.Heading3>
            </div>
          </div>
          <div className="flex mb-8">
            <div className="flex w-60 mr-2">
              <LazySelect
                placeholder="Select Team"
                className="dashboard-select  w-full h-full flex items-center"
                value={selectedTeam || null}
                onChange={onHandleChange}
                loading={
                  !switchToIndv && (selectedTeamLoading || employeeLoading)
                }
                showArrow
                suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
                open={selectOpen}
                onDropdownVisibleChange={(openState) => {
                  setSelectOpen(openState);
                }}
                dropdownRender={(menu) => (
                  <div
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {menu}
                    <div
                      className="!px-2 !py-1 h-auto flex-nowrap text-center"
                      onClick={() => {
                        setSelectOpen(false);
                        setIsQtd(false);
                      }}
                    >
                      <CompareIndividuals
                        compareIndividuals={compareIndividuals}
                        setIndividualPayees={setIndvSelectedPayees}
                        setIndividualTeams={setIndvSelectedTeams}
                        year={year && year.year()}
                        quota={individualQuota}
                        setQuota={setIndividualQuota}
                      />
                    </div>
                  </div>
                )}
                // custom props
                selectedOption={selectedOption}
                {...lazyLoadProps}
                size="small"
                dropdownMatchSelectWidth={false}
                getPopupContainer={({ parentNode }) => parentNode}
              />
            </div>
            <div className="flex w-40 mr-2">
              <EverSelect
                defaultValue={quota}
                showArrow
                suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
                onChange={(value) => {
                  setQuota(value);
                  setIsQtd(false);
                }}
                {...(isLoading
                  ? { loading: true, disabled: true }
                  : { suffixIcon: <ChevronDownIcon className="h-5 w-5" /> })}
                value={quota}
                className="dashboard-select  w-full h-full flex items-center"
                placeholder={
                  quotaOptions.length !== 0 || isLoading
                    ? t("SELECT_QUOTA")
                    : t("QUOTA_UNAVAILABLE")
                }
                options={quotaOptions}
                size="small"
                dropdownMatchSelectWidth={false}
                getPopupContainer={({ parentNode }) => parentNode}
                dropdownAlign={{ offset: [0, 6] }}
              />
            </div>
            {renderCategory()}
            <div className="flex w-28  items-center">
              <EverDatePicker.Legacy
                onChange={(date) => {
                  setQuota(null);
                  setYear(date);
                  setIsQtd(false);
                }}
                picker="year"
                value={year}
                allowClear={false}
                size="small"
                className="w-full h-full rounded-lg"
                placeholder="Year"
                getPopupContainer={({ parentNode }) => parentNode}
              />
            </div>
          </div>
        </>

        <EverLoader
          indicatorType="spinner"
          spinning={isLoading}
          className="!static"
          wrapperClassName="rounded-xl"
        >
          {switchToIndv ? (
            indvDataSet && indvCategory ? (
              <>
                <Row>
                  <div className="flex items-center">
                    {/* <EverTg.Text className="text-ever-base-content mr-1"></EverTg.Text> */}
                    <EverBadge
                      type={`success`}
                      icon={
                        <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                      }
                      title={`YTD: ${isPercent ? ytd : formatCurrency(ytd)}${
                        isPercent ? "%" : ""
                      }`}
                      className={"text-ever-success-lite-content"}
                    />
                    {/* <EverTg.Text className="font-semibold text-ever-success-content-lite">
                      {isPercent ? ytd : formatCurrency(ytd)}
                      {isPercent ? "%" : ""}
                    </EverTg.Text> */}
                  </div>
                </Row>
                <Row className="!mt-2">
                  <Col span={24}>
                    <ReactFusioncharts
                      type="msline"
                      width="100%"
                      height="400"
                      dataFormat="JSON"
                      dataSource={indvDataSource}
                      dataUpdated={base.DEFAULT}
                    />
                  </Col>
                </Row>
              </>
            ) : (
              <Empty
                className="block mx-auto mt-[10%]"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )
          ) : (
            <>
              <Row>
                <div className="mr-auto flex items-center">
                  {/* <div className="mr-1">
                    <EverTg.Text className="text-ever-base-content">
                    
                    </EverTg.Text>
                  </div> */}
                  <div>
                    <EverBadge
                      type={`success`}
                      icon={
                        <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                      }
                      title={` YTD: ${isPercent ? ytd : formatCurrency(ytd)}${
                        isPercent ? "%" : ""
                      }`}
                      className={"text-ever-success-lite-content"}
                    />
                    {/* <EverTg.Text className="font-semibold text-ever-success-content-lite">
                      {isPercent ? ytd : formatCurrency(ytd)}
                      {isPercent ? "%" : ""}
                    </EverTg.Text> */}
                  </div>
                </div>
              </Row>
              <Row className="!mt-2">
                <Col span={24}>
                  <Layout className="bg-ever-base-25">
                    <Layout>
                      {showChart ? (
                        <Content className="bg-ever-base-25">
                          {!isEmpty(qaTrackerData) ? (
                            <ReactFusioncharts
                              type="stackedcolumn2d"
                              width="100%"
                              height="400"
                              dataFormat="json"
                              dataSource={dataSource}
                              dataUpdated={base.DEFAULT}
                              events={{
                                // dataPlotClick: (eventObj, dataObj) => {
                                //   // setSelectedChartMonth(dataObj.categoryLabel);
                                //   setSelectedChartMonth(dataObj.dataIndex + 1);
                                // },
                                dataplotRollOver: (eventObj, dataObj) => {
                                  show(eventObj, dataObj);
                                  document.addEventListener(
                                    "mousemove",
                                    handleMouseMove
                                  );
                                  setSelectedChartMonth(dataObj.dataIndex + 1);
                                },
                                dataplotRollOut: () => {
                                  hide();
                                  document.removeEventListener(
                                    "mousemove",
                                    handleMouseMove
                                  );
                                },
                              }}
                              dataEmptyMessage={chartMessage}
                            />
                          ) : (
                            <div className="mt-5 w-[80%] mx-auto">
                              <img
                                src={emptyQuotaAttainmentTracker}
                                alt="empty-quota-attainment-tracker"
                                className="w-full"
                              />
                              <div className="w-[60%] m-auto">
                                <p className="mt-5 mb-5 text-center">
                                  <EverTg.Text className="text-ever-base-content-mid">
                                    {t("TRACK_YOUR_QUOTA_ATTAINMENT")}
                                  </EverTg.Text>
                                </p>
                              </div>
                            </div>
                          )}
                          {qaTrackerData ? (
                            <FusionTooltip
                              ref={tooltipRef}
                              showTooltip={showTooltip}
                              tooltipContent={tooltipContent}
                            >
                              <ToolTipComponent
                                tableData={tableDS}
                                tooltipLabel={tooltipLabel}
                                isPercent={isPercent}
                                base={base}
                                t={t}
                              />
                            </FusionTooltip>
                          ) : null}
                        </Content>
                      ) : null}
                    </Layout>
                    {/*<Sider
                  width="250"
                  style={{
                    backgroundColor: "white",
                    // alignSelf: "center",
                  }}
                >
                  <Row style={{ marginTop: 15, marginBottom: 10 }}>
                    <Col span={24} style={{ textAlign: "center" }}>
                      {selectedChartMonth && fiscalPeriods && (
                        <Text
                          className={styles.numberFont}
                          style={{ fontSize: 12 }}
                        >
                          {fiscalPeriods[selectedChartMonth - 1]} Quota Attainment
                        </Text>
                      )}
                    </Col>
                  </Row>
                  <Table
                    size="small"
                    showHeader={false}
                    pagination={false}
                    scroll={{ y: 300 }}
                    style={{ fontSize: 12 }}
                    dataSource={tableDS}
                    columns={cols}
                  ></Table>
                </Sider>*/}
                  </Layout>
                </Col>
              </Row>
            </>
          )}
        </EverLoader>
      </div>
    </>
  );
});
export default QuotaAttainmentTracker;
