import { Row, Col } from "antd";
import { observer } from "mobx-react";
import React from "react";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverCard, EverTg } from "~/v2/components";
import { wavingHand } from "~/v2/images";

import CommissionPayouts from "./CommissionPayouts";
import CommissionPayoutsTracker from "./CommissionPayoutsTracker";
import PayoutStatus from "./PayoutStatus";
import QueriesAndApprovals from "./QueriesAndApprovals";
import QuotaAttainment from "./quota-attainment";
import QuotaAttainmentDistribution from "./quota-attainment-distribution";
import QuotaAttainmentTracker from "./QuotaAttainmentTracker";

const Render = observer((props) => {
  const { store, loggedInUserName } = props;
  // const { chartColors } = useCurrentTheme();

  // const themeColor = Object.values(chartColors || {});

  const { hasPermissions } = useUserPermissionStore();

  const cardClasses =
    "relative h-full !bg-ever-base border border-solid border-ever-base-400 p-5 shadow-sm duration-300 ease-in-out rounded-xl";

  //"relative h-full !bg-ever-base border border-solid border-ever-base-400 p-4 duration-300 ease-in-out drop-shadow-sm hover:drop-shadow-md hover:z-50 hover:scale-101 transform-gpu rounded-xl";

  return (
    <div className="admin-dashboard">
      {loggedInUserName && (
        <div className="mb-5">
          <EverTg.Heading1 className="flex">
            {`Hi`}
            {`, ${loggedInUserName}!`}
            <img
              className="p-0 !my-0 !mr-0 !ml-1 "
              src={wavingHand}
              alt="waving hand"
            />
          </EverTg.Heading1>
        </div>
      )}
      <Row gutter={[20, 20]}>
        <Col span={8}>
          <EverCard
            bordered={false}
            className={`${cardClasses} min-h-[350px]`}
            bodyStyle={{ height: "100%", padding: 0 }}
          >
            <CommissionPayouts store={store} />
          </EverCard>
        </Col>
        <Col span={16}>
          <EverCard
            bordered={false}
            className={`${cardClasses} min-h-[350px]`}
            bodyStyle={{ height: "100%", padding: 0 }}
            shadowSize="none"
          >
            <CommissionPayoutsTracker store={store} />
          </EverCard>
        </Col>
      </Row>
      {hasPermissions(RBAC_ROLES.VIEW_QUOTAS) && (
        <Row gutter={[20, 20]}>
          <Col span={8}>
            <EverCard
              bordered={false}
              className={`${cardClasses} min-h-[350px]`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <QuotaAttainment store={store} />
            </EverCard>
          </Col>
          <Col span={16}>
            <EverCard
              bordered={false}
              className={`${cardClasses} min-h-[350px]`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <QuotaAttainmentTracker store={store} />
            </EverCard>
          </Col>
        </Row>
      )}
      <Row gutter={[20, 20]}>
        {/*
        
        OPPORTUNITIES TRACKER IS GOING TO BE DEPRECATED
        
        <Col span={12}>
            <Card
              bordered={false}
              className="h-full"
              style={{ borderRadius: 6, minHeight: 200 }}
            >
              <OpportunitiesTracker store={store} />
            </Card>
          </Col> */}
        {/*<Col span={12}>
          <Card bordered={false} className="h-full" style={{borderRadius: 6, minHeight: 200}}>
            <OpportunitiesTracker store={store} />
          </Card>
        </Col>*/}
        <Col span={8} className="flex flex-col">
          <EverCard
            bordered={false}
            className={`${cardClasses} mb-4 min-h-[200px] ${
              hasPermissions(RBAC_ROLES.VIEW_QUERIES) ? "h-[85%]" : "h-auto"
            }`}
            bodyStyle={{ height: "100%", padding: 0 }}
            shadowSize="none"
          >
            <PayoutStatus store={store} />
          </EverCard>
          {hasPermissions(RBAC_ROLES.VIEW_QUERIES) && (
            <EverCard
              bordered={false}
              className={`${cardClasses} min-h-[200px]`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <QueriesAndApprovals store={store} />
            </EverCard>
          )}
        </Col>

        {hasPermissions(RBAC_ROLES.VIEW_QUOTAS) && (
          <Col span={16}>
            <EverCard
              bordered={false}
              className={`${cardClasses} min-h-[200px]`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <QuotaAttainmentDistribution store={store} />
            </EverCard>
          </Col>
        )}
      </Row>
    </div>
  );
});

export default Render;
