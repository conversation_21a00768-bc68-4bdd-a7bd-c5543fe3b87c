import { useQuery, gql } from "@apollo/client";
import { useLocalS<PERSON>, observer } from "mobx-react";
import moment from "moment";
import React, { useEffect } from "react";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";

import Render from "./Render";
import AdminDashboardStore from "./store";

const REVENUE_LEADER = gql`
  query RevenueLeader {
    revenueLeader {
      effectiveStartDate
      effectiveEndDate
      value
    }
  }
`;

export const ALL_MANAGERS = gql`
  query AllManagersWithLimit(
    $limitValue: Int!
    $firstName: String
    $email: String
    $searchTerm: String
  ) {
    allManagersWithLimit(
      limitValue: $limitValue
      firstName: $firstName
      email: $email
      searchTerm: $searchTerm
    ) {
      headers
      data
    }
  }
`;

export const GET_EMPLOYEE = gql`
  query Employee($emailId: String!) {
    employeeNameDetail(emailId: $emailId) {
      employeeEmailId
      firstName
      lastName
    }
  }
`;

export const getTeamOption = (option) => {
  const label = option?.fullName || `${option?.firstName} ${option?.lastName}`;

  return {
    value: option.employeeEmailId,
    label: `${label} & Team`,
    key: option.employeeEmailId,
  };
};

const AdminDashboard = observer(() => {
  const { email, name, loading } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const baseCurrencySymbol = myClient.baseCurrencySymbol;
  const adminDashboardStore = useLocalStore(() => new AdminDashboardStore());

  const {
    setLoggedInUser,
    // setEmpDetails,
    setYear,
    setLoadingRevLeader,
    setRevLeader,
    setBaseCurrencySymbol,
  } = adminDashboardStore;

  const { loading: loadingRevLeader } = useQuery(REVENUE_LEADER, {
    fetchPolicy: "no-cache",
    onCompleted: (data) => {
      let revLeader;
      data?.revenueLeader?.forEach((rl) => {
        if (!rl.effectiveEndDate) {
          revLeader = JSON.parse(rl.value);
        }
      });
      setRevLeader(revLeader);
    },
  });

  useEffect(() => {
    setLoadingRevLeader(loadingRevLeader);
  }, [loadingRevLeader]);

  useEffect(() => {
    if (baseCurrencySymbol) {
      setBaseCurrencySymbol(baseCurrencySymbol);
    }
  }, [baseCurrencySymbol]);

  useEffect(() => {
    setLoggedInUser(email);
    setYear(moment());
  }, [email]);

  return (
    <Render
      store={adminDashboardStore}
      loggedInUserName={name}
      loading={loading}
    />
  );
});

export default AdminDashboard;
