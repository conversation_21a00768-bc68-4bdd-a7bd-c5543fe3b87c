import { gql } from "@apollo/client";
import { isEmpty, uniqBy, cloneDeep, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { useRef } from "react";

import { LIST_TYPE } from "~/Enums";
import { LazyListContent, useAbortiveLazyQuery } from "~/v2/components";

export const TEAMS = gql`
  query DynamicTeamDirectMembers(
    $quotaYear: String
    $quotaCategory: String
    $quotaSchedule: String
    $managerEmails: [String]
    $searchText: String
    $limitValue: Int
    $offsetValue: Int
  ) {
    dynamicTeamDirectMembers(
      quotaYear: $quotaYear
      quotaCategory: $quotaCategory
      quotaSchedule: $quotaSchedule
      managerEmails: $managerEmails
      searchText: $searchText
      limitValue: $limitValue
      offsetValue: $offsetValue
    )
  }
`;

export const TeamTab = observer((props) => {
  const teamCount = useRef(0);
  const {
    empDetails,
    year,
    quotaSchedule,
    quota,
    setEmpDetails,
    selectedKeys,
    updateSelectedKeys,
  } = props;

  const getTransformedList = (data, isOptionReset) => {
    if (isOptionReset) {
      teamCount.current = 0;
    }
    const newEmpDetails = cloneDeep(empDetails);
    const records =
      data?.map((team, index) => {
        const getChildren = (reportees) => {
          const reporteeList = [];
          if (!isEmpty(reportees)) {
            uniqBy(reportees, "employee_email_id").forEach((reportee) => {
              if (isEmpty(newEmpDetails[reportee.employee_email_id])) {
                newEmpDetails[reportee.employee_email_id] = {
                  label: reportee.full_name,
                  value: reportee.employee_email_id,
                  key: reportee.employee_email_id,
                };
              }
              reporteeList.push({
                key: reportee.employee_email_id,
                value: reportee.employee_email_id,
                title: reportee.full_name,
                isLeaf: true,
              });
            });
          }

          return reporteeList;
        };

        return {
          title: `${team.full_name}'s Reports`,
          key: teamCount.current + index,
          // value: `${managerKey}_${teamCount.current + index}`,
          children: getChildren(team?.reportees),
        };
      }) || [];
    teamCount.current += records.length;
    setEmpDetails(newEmpDetails);

    return records;
  };

  const [getManagerWithReportees, { variables, abort }] = useAbortiveLazyQuery(
    TEAMS,
    {
      fetchPolicy: year && quota ? "network-only" : "cache-only",
      onCompleted: (data) => {
        let response = JSON.parse(data?.dynamicTeamDirectMembers);
        if (!isNil(response)) {
          response = getTransformedList(response, variables.offset === 0);
          variables.successCbk(response);
        } else {
          variables.failureCbk();
        }
      },
      onError: () => {
        variables.failureCbk();
      },
    }
  );

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { offset, searchTerm, limit, successCbk, failureCbk } = params;
      await getManagerWithReportees({
        quotaYear: String(year),
        quotaCategory: quota,
        quotaSchedule,
        searchText: searchTerm,
        limitValue: limit,
        offsetValue: offset,
        successCbk,
        failureCbk,
      });
    },
    deps: [year, quota, quotaSchedule],
  };

  return (
    <LazyListContent
      selectedKeys={selectedKeys}
      updateSelectedKeys={updateSelectedKeys}
      type={LIST_TYPE.HIERARCHY_TREE}
      skipSelectAll
      showEllipsis
      {...lazyLoadProps}
    />
  );
});
