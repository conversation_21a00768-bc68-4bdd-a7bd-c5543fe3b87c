import { gql, useLazyQuery, useQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Col, Row } from "antd";
import { cloneDeep, groupBy, isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import {
  QUOTA_CATEGORIES as QUOTA_PRIMARY,
  QUOTA_SCHEDULE,
  RBAC_ROLES,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { getClientFiscalYear, getFiscalYearForMonth } from "~/Utils/DateUtils";
import { sortPrimaryQuota } from "~/Utils/QuotaUtils";
import {
  ClickBoundary,
  MultiTabDropdownSelect,
  EverLoader,
  EverSelect,
  EverDatePicker,
  EverTg,
  useCurrentTheme,
} from "~/v2/components";
import FusionTooltip from "~/v2/features/dashboard23/dashboard-detail-view/FusionTooltip";
import { useFusionTooltip } from "~/v2/features/dashboard23/dashboard-detail-view/useFusionTooltip";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyQuotaAttainmentDistribution } from "~/v2/images";

import { IndividualTab } from "./IndividualTab";
import { TEAMS, TeamTab } from "./TeamTab";

const quotaScheduleOptions = Object.keys(QUOTA_SCHEDULE).map(
  (quotaScheduleKey) => ({
    value: quotaScheduleKey,
    label: QUOTA_SCHEDULE[quotaScheduleKey],
  })
);

const QUOTA_ATTAINMENT = gql`
  query QuotaAttainmentDistribution(
    $payeeIds: [String!]
    $year: String!
    $quotaCategory: String!
  ) {
    quotaAttainmentDistribution(
      payeeIds: $payeeIds
      year: $year
      quotaCategory: $quotaCategory
    )
  }
`;

const QUOTA_CATEGORIES = gql`
  query AllQuotaCategoryTypes($year: Int!) {
    allQuotaCategoryTypes(year: $year) {
      label
      value
    }
  }
`;

const ToolTipComponent = ({ tableData, t, tooltipLabel }) => {
  return (
    <table width="320" className="text-center">
      <thead>
        <tr>
          <td
            colSpan="2"
            className="text-ever-base-content text-md font-semibold text-left px-6 py-4 h-16"
          >
            <b>
              {`${tooltipLabel} ${t("QUOTA_ATTAINMENT_DISTRIBUTION")}${
                tableData?.length > 10 ? " (Top 10)" : ""
              }`}
            </b>
          </td>
        </tr>
      </thead>
      <tbody className="pb-2">
        {tableData !== undefined
          ? tableData.slice(0, 10).map((data) => (
              <tr key={data.key}>
                <td className="text-left text-md px-6 py-4 h-10">
                  {data.name}
                </td>
                <td className="text-left text-md px-6 py-4 h-10">{data.qa}</td>
              </tr>
            ))
          : null}
      </tbody>
    </table>
  );
};

// const toolTipDom = (tableData, themeBase, t) => {
//   const rowData =
//     tableData !== undefined &&
//     tableData.slice(0, 10).map(
//       (data) =>
//         `<tr>
//             <td style="padding-left: 24px;padding-right:24px; height:40px;  font-size: 14px;">${data.name}</td>
//             <td style="padding-left: 24px;padding-right:24px; height:40px;  font-size: 14px;">${data.qa}%</td>
//           </tr>`
//     );

//   return `<table width="180" style="text-align: left;">
//       <thead>
//         <tr >
//           <td colSpan="2" style="padding-left: 24px;padding-right:24px; height:60px;  color: ${
//             themeBase.content.DEFAULT
//           }; font-size: 14px;">
//             <b>${`$label ${t("QUOTA_ATTAINMENT_DISTRIBUTION")}${
//               tableData?.length > 10 ? " (Top 10)" : ""
//             }`}</b>
//           </td>
//         </tr>
//       </thead>
//       <tbody>
//         ${rowData}
//       </tbody>
//     </table>`;
// };

const QuotaAttainmentDistribution = observer((props) => {
  const { store } = props;
  const { chartColors, base } = useCurrentTheme();
  const themeColor = Object.values(chartColors || {});
  const baseFontColor = base.content.mid || "";
  const valueFontColor = base.content.DEFAULT;
  const xAxisLineColor = base[300];
  const { loadingRevLeader, revLeader } = store;
  const initialLoad = useRef(true);
  const [selectedReportees, setSelectedReportees] = useState([]);
  const [emailIds, setEmailIds] = useState([]);
  const [selectedPeriod, setSelectedPeriod] = useState();
  const [periodOptions, setPeriodOptions] = useState([]);
  const [chartData, setChartData] = useState();
  const [qaData, setQaData] = useState();
  const [currentQaData, setCurrentQaData] = useState([]);
  const [year, setYear] = useState();
  const [quota, setQuota] = useState();
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [quotaSchedule, setQuotaSchedule] = useState(
    quotaScheduleOptions[0].value
  );
  const [empDetails, setEmpDetails] = useState({});
  // const [showDropdown, setShowDropdown] = useState(false);
  // const [searchValue, setSearchValue] = useState("");
  const [quotaCategoriesLoaded, setQuotaCategoriesLoaded] = useState(false);
  const [reporteesLoaded, setReporteesLoaded] = useState(false);
  const [periodsLoaded, setPeriodsLoaded] = useState(false);

  const fiscalYearMonthZero = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const { t } = useTranslation();

  const [getManagerWithReportees, { loading: reporteesLoading }] = useLazyQuery(
    TEAMS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        setReporteesLoaded(true);
        const response = JSON.parse(data?.dynamicTeamDirectMembers);
        const selectedReportees = [];
        const emailIds = [];
        const newEmpDetails = cloneDeep(empDetails);
        if (response?.length && response[0]?.reportees?.length) {
          const data = response[0];
          const reporteesBySchedule = groupBy(data.reportees, "quota_schedule");
          let reportees = [];
          if (quotaSchedule in reporteesBySchedule) {
            reportees = reporteesBySchedule[quotaSchedule];
          } else {
            const scheduleKey = Object.keys(reporteesBySchedule).find(
              (scheduleKey) => scheduleKey !== "null"
            );
            if (scheduleKey) {
              reportees = reporteesBySchedule[scheduleKey];
              setQuotaSchedule(scheduleKey);
            }
          }
          reportees.forEach((reportee) => {
            newEmpDetails[reportee.employee_email_id] = {
              label: reportee.full_name,
              value: reportee.employee_email_id,
              key: reportee.employee_email_id,
            };
            selectedReportees.push({
              label: reportee.full_name,
              value: reportee.employee_email_id,
              key: reportee.employee_email_id,
            });
            emailIds.push(reportee.employee_email_id);
          });
        }

        setSelectedReportees(selectedReportees);
        setEmailIds(emailIds);
        setEmpDetails(newEmpDetails);
        if (isEmpty(emailIds)) {
          setPeriodsLoaded(true);
        }
      },
      onError: () => {
        setReporteesLoaded(true);
        setSelectedReportees([]);
        setEmailIds([]);
      },
    }
  );

  useEffect(() => {
    if (
      initialLoad.current &&
      isEmpty(selectedReportees) &&
      year &&
      year.year() &&
      quota
    ) {
      if (loadingRevLeader) {
        return;
      }
      if (revLeader) {
        initialLoad.current = false;
        getManagerWithReportees({
          variables: {
            managerEmails: [revLeader],
            quotaYear: String(year.year()),
            quotaCategory: quota,
          },
        });
      } else {
        setReporteesLoaded(true);
        setPeriodsLoaded(true);
      }
    }
  }, [selectedReportees, loadingRevLeader, revLeader, year, quota]);

  const { loading, data } = useQuery(QUOTA_ATTAINMENT, {
    variables: {
      payeeIds: emailIds,
      year: year && String(year.year()),
      quotaCategory: quota,
    },
    fetchPolicy:
      !isEmpty(emailIds) && year && quota ? "network-only" : "cache-only",
    onCompleted: () => setPeriodsLoaded(true),
    onError: () => setPeriodsLoaded(true),
  });

  // (1) gettng quota categories
  const { data: qdata, loading: qLoading } = useQuery(QUOTA_CATEGORIES, {
    variables: {
      year: year && Number(year.year()),
    },
    fetchPolicy: year ? "network-only" : "cache-only",
    onCompleted: () => setQuotaCategoriesLoaded(true),
    onError: () => setQuotaCategoriesLoaded(true),
  });

  const { hasPermissions } = useUserPermissionStore();

  useEffect(() => {
    if (qdata && qdata.allQuotaCategoryTypes) {
      let allQuotas = qdata.allQuotaCategoryTypes;
      let quotas = hideCategories(allQuotas.map((x) => x.value));
      let quotaOptions = [];
      if (quotas.length > 0) {
        allQuotas.map((quota) => {
          if (quotas.includes(quota.value)) {
            quotaOptions.push({
              label: quota.label,
              value: quota.value,
            });
          }
        });

        setQuotaOptions(sortPrimaryQuota(quotaOptions));
        quotas.includes(QUOTA_PRIMARY.PRIMARY_QUOTA)
          ? setQuota(QUOTA_PRIMARY.PRIMARY_QUOTA)
          : setQuota(quotas[0]);
      } else {
        setQuotaOptions([]);
        setQuota();
        setChartData([]);
        setCurrentQaData([]);
        setPeriodOptions([]);
        setSelectedPeriod();
        setReporteesLoaded(true);
        setPeriodsLoaded(true);
      }
    }
  }, [qdata]);

  useEffect(() => {
    console.log("### data", data);
    if (data && data.quotaAttainmentDistribution && quota) {
      let qaData = JSON.parse(data.quotaAttainmentDistribution);
      console.log("## qaData", qaData);
      if (qaData["error"] !== undefined) {
        setChartData([]);
        setCurrentQaData([]);
        setQaData();
        setPeriodOptions([]);
        setSelectedPeriod();
      } else {
        let periodOptions = [];
        let qdData = quota in qaData ? qaData[quota].payee_qa : null;
        if (qdData) {
          setQaData(qdData);
          Object.keys(qdData).map((key) => {
            let size = Object.keys(qdData).length;
            periodOptions.push({
              label: `${key} ${
                year && size === 12
                  ? getFiscalYearForMonth(
                      fiscalYearMonthZero,
                      moment().month(key).month(),
                      year.year()
                    )
                  : year.year()
              }`,
              value: key,
            });
          });
          if (periodOptions.length > 0) {
            setPeriodOptions(periodOptions);
            let currentPeriod = qaData[quota].curr_period;
            let selectedperiod = periodOptions[currentPeriod.index]["value"];
            setSelectedPeriod(selectedperiod);
            let qa = qdData[selectedperiod];
            let getData = getChartData(qa);
            setChartData(getData[0]);
            setCurrentQaData(getData[1]);
          }
        } else {
          setQaData();
          setChartData([]);
          setCurrentQaData([]);
          setPeriodOptions([]);
          setSelectedPeriod();
        }
      }
    }
  }, [data, quota]);

  useEffect(() => {
    if (fiscalYearMonthZero != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonthZero));
    }
  }, [fiscalYearMonthZero]);

  const hideCategories = (quotaOptions) => {
    if (hasPermissions(RBAC_ROLES.VIEW_HIDDENQUOTAS)) {
      const remainingCategories = [...quotaOptions];
      return remainingCategories;
    }

    const hideCategories = clientFeatures?.hideCategories || [];
    const remainingCategories = [];
    quotaOptions.forEach((quota) => {
      if (!hideCategories.includes(quota)) {
        remainingCategories.push(quota);
      }
    });
    return remainingCategories;
  };

  const onHandleChange = (val) => {
    const result = Object.values(empDetails).filter((emp) =>
      val.includes(emp.value)
    );

    setSelectedReportees(result);
  };

  const onRemoveChange = (reportees) => {
    setSelectedReportees(reportees);
  };

  const onQuotaScheduleChange = (value) => {
    setQuotaSchedule(value);
    setSelectedReportees([]);
    setEmailIds([]);
    setChartData([]);
    setCurrentQaData([]);
    setQaData();
  };

  const tabList = [
    {
      key: "individual",
      name: "Individual",
      isLazy: true,
      selectedValues: selectedReportees,
      renderLazyList: () => (
        <IndividualTab
          empDetails={empDetails}
          setEmpDetails={setEmpDetails}
          year={year?.year()}
          quota={quota}
          quotaSchedule={quotaSchedule}
          selectedKeys={selectedReportees.map((reportee) => reportee.value)}
          updateSelectedKeys={onHandleChange}
        />
      ),
    },
    {
      key: "teams",
      name: "Teams",
      isLazy: true,
      selectedValues: selectedReportees,
      renderLazyList: () => (
        <TeamTab
          empDetails={empDetails}
          setEmpDetails={setEmpDetails}
          year={year?.year()}
          quota={quota}
          quotaSchedule={quotaSchedule}
          selectedKeys={selectedReportees.map((reportee) => reportee.value)}
          updateSelectedKeys={onHandleChange}
        />
      ),
    },
  ];

  const onHandleBlur = () => {
    setEmailIds(selectedReportees.map((reportee) => reportee.value));
    if (selectedReportees.length === 0) {
      setChartData([]);
      setCurrentQaData([]);
      setQaData();
    }
  };

  const onHandlePeriodChange = (val) => {
    setSelectedPeriod(val);
    let qa = qaData && val in qaData ? qaData[val] : null;
    if (qa) {
      let getData = getChartData(qa);
      setChartData(getData[0]);
      setCurrentQaData(getData[1]);
    }
  };

  // const reporteesOptions = useMemo(
  //   () => Object.values(empDetails),
  //   [empDetails]
  // );

  const showSkeleton =
    !quotaCategoriesLoaded || !reporteesLoaded || !periodsLoaded;
  if (showSkeleton) {
    return <WidgetLoading />;
  }
  return (
    <div className="h-full qa-distribution-card">
      <div className="flex mb-4">
        <EverTg.Heading3 className="text-ever-base-content">
          {t("QUOTA_ATTAINMENT_DISTRIBUTION")}
        </EverTg.Heading3>
      </div>

      <div className="flex w-full mt-2 mb-4">
        <div className="w-40 mr-2">
          <EverSelect
            placeholder={t("SELECT_QUOTA")}
            defaultValue={quota}
            onChange={(value) => {
              setQuota(value);
            }}
            value={quota}
            className="h-full w-full"
            options={quotaOptions}
            showArrow
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            disabled={revLeader === null || qLoading}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
            dropdownAlign={{ offset: [0, 6] }}
          />
        </div>
        <div className="w-40 mr-2">
          <EverSelect
            placeholder={t("SELECT_QUOTA_SCHEDULE")}
            defaultValue={quotaSchedule}
            onChange={onQuotaScheduleChange}
            value={quotaSchedule}
            className="h-full w-full"
            options={quotaScheduleOptions}
            showArrow
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            disabled={revLeader === null || qLoading}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
            dropdownAlign={{ offset: [0, 6] }}
          />
        </div>
        <div className="w-60 relative mr-2">
          <ClickBoundary onClickOutside={onHandleBlur}>
            <MultiTabDropdownSelect
              tabs={tabList}
              placeholder="Select Reports"
              onRemove={onRemoveChange}
              dropdownMatchSelectWidth={false}
              maxTagCount={1}
              maxTagTextLength={3}
              size="small"
              // dropdownAlign={{ offset: [-10, 0] }}
              // getPopupContainer={({ parentNode }) => parentNode}
            />
          </ClickBoundary>
        </div>
        <div className="w-40 mr-2">
          <EverSelect
            options={periodOptions}
            value={selectedPeriod}
            placeholder="Select Period"
            onChange={(value) => onHandlePeriodChange(value)}
            className="w-full h-full"
            showArrow={false}
            showSearch
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
            dropdownAlign={{ offset: [0, 6] }}
          />
        </div>
        <div className="flex w-28">
          <EverDatePicker.Legacy
            onChange={(date) => setYear(date)}
            picker="year"
            value={year}
            allowClear={false}
            size="small"
            className="w-full h-full w-28 rounded-lg"
            placeholder="Year"
          />
        </div>
      </div>

      <EverLoader
        indicatorType="spinner"
        spinning={qLoading || loading || reporteesLoading}
        className="!static !h-100"
        wrapperClassName="rounded-xl"
      >
        {!isEmpty(chartData) ? (
          <Row>
            <Col span={24}>
              <DistributionSection
                chartData={chartData}
                currentQaData={currentQaData}
                empDetails={empDetails}
                accentColor={themeColor}
                fontType={"Inter"}
                baseFontColor={baseFontColor}
                valueFontColor={valueFontColor}
                xAxisLineColor={xAxisLineColor}
                baseColor={base.DEFAULT}
                themeBase={base}
              />
            </Col>
          </Row>
        ) : (
          <div className="mt-5 w-[80%] mx-auto">
            <img
              src={emptyQuotaAttainmentDistribution}
              alt="empty-quota-attainment-distribution"
              className="w-full"
            />
            <div className="w-[60%] m-auto">
              <p className="mt-5 mb-5 text-center">
                <EverTg.Text className="text-ever-base-content-mid">
                  {t("VISUALIZE_QUOTA_ATTAINMENT")}
                </EverTg.Text>
              </p>
            </div>
          </div>
        )}
      </EverLoader>
    </div>
  );
});
export default QuotaAttainmentDistribution;

function getChartData(qa) {
  let count0to20 = 0;
  let count20to40 = 0;
  let count40to60 = 0;
  let count60to80 = 0;
  let count80to100 = 0;
  let count100to120 = 0;
  let count120to140 = 0;
  let count140to160 = 0;
  let count160plus = 0;
  let chartData = {};
  let currentQaData = {
    "0-20%": [],
    "20-40%": [],
    "40-60%": [],
    "60-80%": [],
    "80-100%": [],
    "100-120%": [],
    "120-140%": [],
    "140-160%": [],
    "160%+": [],
  };

  qa &&
    qa.length > 0 &&
    qa.map((x) => {
      let percent = x["qa"];
      if (percent >= 0 && percent < 20) {
        count0to20++;
        currentQaData["0-20%"].push(x);
      } else if (percent >= 20 && percent < 40) {
        count20to40++;
        currentQaData["20-40%"].push(x);
      } else if (percent >= 40 && percent < 60) {
        count40to60++;
        currentQaData["40-60%"].push(x);
      } else if (percent >= 60 && percent < 80) {
        count60to80++;
        currentQaData["60-80%"].push(x);
      } else if (percent >= 80 && percent < 100) {
        count80to100++;
        currentQaData["80-100%"].push(x);
      } else if (percent >= 100 && percent < 120) {
        count100to120++;
        currentQaData["100-120%"].push(x);
      } else if (percent >= 120 && percent < 140) {
        count120to140++;
        currentQaData["120-140%"].push(x);
      } else if (percent >= 140 && percent < 160) {
        count140to160++;
        currentQaData["140-160%"].push(x);
      } else if (percent >= 160) {
        count160plus++;
        currentQaData["160%+"].push(x);
      }

      chartData = {
        categories: [
          {
            category: [
              {
                label: "0-20%",
              },
              {
                label: "20-40%",
              },
              {
                label: "40-60%",
              },
              {
                label: "60-80%",
              },
              {
                label: "80-100%",
              },
              {
                label: "100-120%",
              },
              {
                label: "120-140%",
              },
              {
                label: "140-160%",
              },
              {
                label: "160%+",
              },
            ],
          },
        ],
        dataset: [
          {
            data: [
              {
                value: count0to20,
              },
              {
                value: count20to40,
              },
              {
                value: count40to60,
              },
              {
                value: count60to80,
              },
              {
                value: count80to100,
              },
              {
                value: count100to120,
              },
              {
                value: count120to140,
              },
              {
                value: count140to160,
              },
              {
                value: count160plus,
              },
            ],
          },
        ],
      };

      // chartData = [
      //   {
      //     label: "0-20%",
      //     value: count0to20,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "20-40%",
      //     value: count20to40,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "40-60%",
      //     value: count40to60,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "60-80%",
      //     value: count60to80,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "80-100%",
      //     value: count80to100,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "100-120%",
      //     value: count100to120,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "120-140%",
      //     value: count120to140,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "140-160%",
      //     value: count140to160,
      //     toolText: "$dataValue",
      //   },
      //   {
      //     label: "160%+",
      //     value: count160plus,
      //     toolText: "$dataValue",
      //   },
      // ];
    });

  return [chartData, currentQaData];
}

const DistributionSection = observer((props) => {
  const {
    chartData,
    currentQaData,
    empDetails,
    accentColor,
    baseFontColor,
    valueFontColor,
    xAxisLineColor,
    fontType,
    baseColor,
    themeBase,
  } = props;
  const [selectedPeriod, setSelectedPeriod] = useState("0-20%");
  const [dataSource, setDataSource] = useState([]);

  const { t } = useTranslation();

  const {
    tooltipRef,
    showTooltip,
    tooltipContent,
    tooltipLabel,
    show,
    hide,
    updateTooltipPosition,
  } = useFusionTooltip();

  const handleMouseMove = (evt) => {
    const { clientX, clientY } = evt;
    updateTooltipPosition(clientX, clientY);
  };

  // const columns = [
  //   {
  //     dataIndex: "name",
  //     key: "name",
  //   },
  //   {
  //     dataIndex: "qa",
  //     key: "qa",
  //   },
  // ];

  useEffect(() => {
    if (empDetails && selectedPeriod && currentQaData) {
      const dataSource = [];
      let allQa = currentQaData[selectedPeriod];
      allQa &&
        allQa.map((data) => {
          dataSource.push({
            key: data["employee_email_id"],
            name: empDetails[data["employee_email_id"]].label,
            qa: Number(data["qa"]).toFixed(2),
          });
        });

      setDataSource(dataSource);
    }
  }, [selectedPeriod, currentQaData, empDetails]);

  return (
    <Row>
      <Col span={24}>
        <ReactFusioncharts
          type="stackedcolumn2d"
          width="100%"
          height="400"
          dataFormat="JSON"
          dataSource={getDataSource(
            chartData,
            dataSource,
            accentColor,
            fontType,
            baseFontColor,
            valueFontColor,
            xAxisLineColor,
            baseColor,
            themeBase,
            t
          )}
          dataUpdated={baseColor.DEFAULT}
          events={{
            // dataPlotClick: (eventObj, dataObj) => {
            //   setSelectedPeriod(dataObj.categoryLabel);
            // },
            dataplotRollOver: (eventObj, dataObj) => {
              show(eventObj, dataObj);
              document.addEventListener("mousemove", handleMouseMove);
              setSelectedPeriod(dataObj.categoryLabel);
            },
            dataplotRollOut: () => {
              hide();
              document.removeEventListener("mousemove", handleMouseMove);
            },
          }}
        />
        {dataSource ? (
          <FusionTooltip
            ref={tooltipRef}
            showTooltip={showTooltip}
            tooltipContent={tooltipContent}
          >
            <ToolTipComponent
              tableData={dataSource}
              tooltipLabel={tooltipLabel}
              base={themeBase}
              t={t}
            />
          </FusionTooltip>
        ) : null}
      </Col>
      {/*</Layout> */}
      {/*<Sider
        width="200"
        style={{
          backgroundColor: "white",
          alignSelf: "center",
        }}
      >
        <Content style={{ backgroundColor: "white" }}>
          {dataSource.length > 0 && (
            <Table
              size="small"
              pagination={false}
              scroll={{ y: 103 }}
              style={{ fontSize: 10 }}
              dataSource={dataSource}
              columns={columns}
              title={() => (
                <b style={{ color: "#003A8C" }}>
                  {selectedPeriod + " quota attainment"}
                </b>
              )}
              showHeader={false}
            ></Table>
          )}
        </Content>
      </Sider>*/}
    </Row>
  );
});

const getDataSource = (
  val,
  tableData,
  color,
  font,
  baseFontColor,
  valueFontColor,
  xAxisLineColor,
  baseColor,
  themeBase
) => {
  let dataSource = {
    chart: {
      paletteColors: color,
      valueFontColor: valueFontColor,
      baseFontColor: baseFontColor,
      baseFont: font,
      valueFont: font,
      plotFillRatio: 0,
      baseFontSize: 12,
      valueFontSize: 12,
      showShadow: false,
      showValues: 1,
      showHoverEffect: 0,
      showSum: 0,
      canvasTopPadding: 20,
      theme: "fusion",
      scaleRecursively: "0",
      xAxisLineColor: xAxisLineColor,
      showXAxisLine: 1,
      // plottooltext: tableData
      //   ? toolTipDom(tableData, themeBase, t).replace(/,/g, "")
      //   : "",
      showToolTip: 0,
      toolTipBgColor: themeBase.DEFAULT,
      toolTipColor: themeBase.content.DEFAULT,
      toolTipBorderColor: themeBase?.[400],
      divLineAlpha: 0,
      chartLeftMargin: 20,
      chartRightMargin: 20,
      bgColor: baseColor,
    },
    ...val,
  };
  return dataSource;
};
