import { TargetIcon } from "@everstage/evericons/solid";
import { Row, Col } from "antd";
import { observer } from "mobx-react";
import React from "react";
import ReactFusioncharts from "react-fusioncharts";

import { percentageOneDecimalPlace } from "~/Utils/QuotaUtils";
import { useCurrentTheme } from "~/v2/components";

import { AnimatedNumber } from "../../AnimatedNumber";

const getDataSource = (val, chartColors, primary, base) => {
  const dataSource = {
    chart: {
      lowerlimit: "0",
      upperlimit: "160",
      showvalue: "1",
      numbersuffix: " %",
      theme: "fusion",
      showtooltip: "0",
      baseFontSize: 12,
      showShadow: "0",
      baseFont: "Inter",
      baseFontColor: base.content.mid,
      valueFont: "Inter",
      chartLeftMargin: 20,
      chartRightMargin: 20,
      bgColor: base.DEFAULT,
    },
    colorrange: {
      color: [
        {
          minvalue: "0",
          maxvalue: val,
          code: chartColors[0],
        },
        {
          minvalue: val,
          maxvalue: "160",
          code: primary?.lite?.DEFAULT,
        },
      ],
    },
    dials: {
      dial: [
        {
          value: val,
          bgColor: `${base?.content?.DEFAULT}`,
        },
      ],
    },
  };
  return dataSource;
};

const QuotaAttainmentSection = observer((props) => {
  const { quotaAttainment } = props;
  const { base, primary, chartColors } = useCurrentTheme();
  const themeColor = Object.values(chartColors || {});
  return (
    <Row align="center" className="w-full h-auto">
      <Col span={24}>
        <div className="gauge-chart-value mb-1 w-2/4">
          <AnimatedNumber
            handleRoundOff={(value) => {
              if (value === null) return null;
              if (value === 0) return 0;
              return percentageOneDecimalPlace(value);
            }}
            key={quotaAttainment}
            number={percentageOneDecimalPlace(quotaAttainment)}
            description={"Attainment"}
            iconClassName={`bg-ever-chartColors-2`}
            append={"%"}
            icon={<TargetIcon className="text-ever-chartColors-12 w-6 h-6" />}
          />
        </div>
        <div className="flex justify-center w-full">
          <ReactFusioncharts
            key={quotaAttainment}
            type="angulargauge"
            width="100%"
            height="300"
            dataFormat="JSON"
            dataSource={getDataSource(
              quotaAttainment,
              themeColor,
              primary,
              base
            )}
          />
        </div>
      </Col>
    </Row>
  );
});

export default QuotaAttainmentSection;
