import { gql, useLazyQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Row } from "antd";
import { cx } from "classix";
import { isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { Fragment, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { QUOTA_CATEGORIES } from "~/Enums";
import { QUOTA_MESSAGES } from "~/Enums/LocalizationMessages";
import {
  useAbortiveLazyQuery,
  EverSelect,
  EverLoader,
  EverButtonGroup,
  EverButton,
  EverTg,
  filterAndFormatOptions,
  LazySelect,
} from "~/v2/components";
import {
  ALL_MANAGERS,
  GET_EMPLOYEE,
  getTeamOption,
} from "~/v2/features/dashboard23/dashboard-detail-view/admin-dashboard";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyQuotaAttainment } from "~/v2/images";

import QuotaAttainmentSection from "./QuotaAttainmentSection";

const QUOTA_ATTAINMENT = gql`
  query CurrentCategoryQuotaAttainment(
    $teamOwnerId: String!
    $category: String
  ) {
    currentCategoryQuotaAttainment(
      teamOwnerId: $teamOwnerId
      category: $category
    ) {
      quotas
      quotaCategoryNames
      displayNames {
        label
        value
      }
    }
  }
`;

const QuotaAttainment = observer((props) => {
  const { store } = props;
  const { revLeader, baseFontColor } = store;
  const initialLoad = useRef(true);
  const [managersLoaded, setManagersLoaded] = useState(false);
  const [quotaAttainmentLoaded, setQuotaAttainmentLoaded] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState(revLeader);
  const [quotaAttainment, setQuotaAttainment] = useState(0);
  const [quota, setQuota] = useState();
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [periodOptions, setPeriodOptions] = useState([]);
  const [period, setPeriod] = useState("");
  const [isDataConfigured, setIsDataConfigured] = useState(false);
  const [selectedOption, setSelectedOption] = useState({});
  const [initialOptions, setInitialOptions] = useState([]);
  const [quotaData, setQuotaData] = useState({});

  const { t } = useTranslation();

  const [getEmployee, { loading: employeeLoading, variables: empVariables }] =
    useLazyQuery(GET_EMPLOYEE, {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        setSelectedTeamId(empVariables.emailId);
        setSelectedOption(getTeamOption(data.employeeBasicDetail));
      },
    });

  const [getManager, { variables, abort }] = useAbortiveLazyQuery(
    ALL_MANAGERS,
    {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        const headers = data?.allManagersWithLimit?.headers || [];
        const fullNameIndex =
          headers.indexOf("reportingManagerFullName") ?? null;
        const firstNameIndex = headers.indexOf("firstName") ?? null;
        const emailIndex = headers.indexOf("employeeEmailId") ?? null;
        let response = data?.allManagersWithLimit?.data || [];
        if (!isNil(response)) {
          response = filterAndFormatOptions(response, {
            label: (option) => `${option[fullNameIndex]} & Team`,
            value: emailIndex,
            key: firstNameIndex,
          });
          if (initialLoad.current) {
            setInitialOptions(response);
            initialLoad.current = false;
          }
          variables.successCbk(response);
        } else {
          variables.failureCbk();
        }
        setManagersLoaded(true);
      },
      onError: () => {
        variables.failureCbk();
        setManagersLoaded(true);
      },
    }
  );

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { page, searchTerm, options, limit, successCbk, failureCbk } =
        params;
      await getManager({
        limitValue: limit,
        ...(page > 0 &&
          options.length > 0 && {
            firstName: options[options.length - 1].key.split("##::##")[0],
            email: options[options.length - 1].value,
          }),
        ...(searchTerm && { searchTerm }),
        successCbk,
        failureCbk,
      });
    },
  };

  const resetStates = () => {
    setQuotaOptions([]);
    setQuota(null);
    setQuotaAttainment(0);
    setPeriodOptions([]);
    setPeriod("");
    setQuotaAttainmentLoaded(true);
  };

  const onQuotaAttainmentComplete = (data) => {
    let dataConfigure = false;
    if (data) {
      const qaData = JSON.parse(
        data?.currentCategoryQuotaAttainment?.quotas ?? "{}"
      );
      setQuotaData(qaData);
      if (qaData && !("error" in qaData) && Object.keys(qaData).length > 0) {
        if (!isEmpty(data.currentCategoryQuotaAttainment.quotaCategoryNames)) {
          const quotaOpts =
            data.currentCategoryQuotaAttainment.displayNames.map(
              (category) => ({
                label:
                  category.label === QUOTA_CATEGORIES.PRIMARY_QUOTA
                    ? QUOTA_MESSAGES.PRIMARY_QUOTA
                    : category.label,
                value: category.value,
              })
            );
          setQuotaOptions(quotaOpts);
        }
        const newQuota = Object.keys(qaData)[0];
        if (isEmpty(quota)) {
          setQuota(newQuota);
        }
        const quotaAttainments = qaData[newQuota];
        const periodOptionList = Object.getOwnPropertyNames(quotaAttainments);
        setQuotaAttainment(Number(quotaAttainments[periodOptionList[1]]));
        setPeriodOptions(periodOptionList);
        setPeriod(periodOptionList[1]);
        dataConfigure = true;
      } else {
        resetStates();
      }
    }
    setIsDataConfigured(dataConfigure);
    setQuotaAttainmentLoaded(true);
  };

  const [getQuotaAttainment, { loading }] = useLazyQuery(QUOTA_ATTAINMENT, {
    fetchPolicy: "network-only",
    onCompleted: onQuotaAttainmentComplete,
    onError: resetStates,
  });

  useEffect(() => {
    if (selectedTeamId) {
      getQuotaAttainment({
        variables: {
          teamOwnerId: selectedTeamId,
        },
      });
    }
  }, [selectedTeamId]);

  useEffect(() => {
    if (isEmpty(selectedTeamId) && revLeader !== null) {
      if (revLeader) {
        getEmployee({
          variables: {
            emailId: revLeader,
          },
        });
      } else if (initialOptions.length) {
        setSelectedTeamId(initialOptions[0].value);
        setSelectedOption(initialOptions[0]);
      } else {
        // Quota Attainment will not be obtained since there is no team to select.
        // So, updating this will stop the loading skeleton.
        setQuotaAttainmentLoaded(true);
      }
    }
  }, [selectedTeamId, revLeader, initialOptions]);

  const onHandleChange = (value, option) => {
    setQuota(null);
    setSelectedOption(option);
    setSelectedTeamId(value);
  };

  // const onPeriodChange = (event) => {
  //   const period = event.target.value;
  //   setPeriod(period);
  //   setQuotaAttainment(Number(quotaData[quota][period]));
  // };

  const onQuotaChange = (value) => {
    setQuota(value);
    getQuotaAttainment({
      variables: {
        teamOwnerId: selectedTeamId,
        category: value,
      },
    });
  };

  const selectedTeamLoading =
    (revLeader || !isEmpty(initialOptions)) && isEmpty(selectedTeamId);
  const isLoading = loading || selectedTeamLoading || employeeLoading;
  const showSkeleton = !managersLoaded || !quotaAttainmentLoaded;

  return (
    <Fragment>
      {showSkeleton && <WidgetLoading />}
      <div className={cx(showSkeleton && "hidden")}>
        <div className="flex w-full justify-between">
          <div className="flex mb-4">
            <EverTg.Heading3 className="text-ever-base-content">
              {t("QUOTA_ATTAINMENT")}
            </EverTg.Heading3>
          </div>
        </div>
        <Row className="flex">
          <div className="flex w-2/4 pr-1">
            <LazySelect
              className=" h-full"
              placeholder="Select Team"
              value={selectedTeamId || null}
              onChange={onHandleChange}
              loading={selectedTeamLoading || employeeLoading}
              // custom props
              suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
              selectedOption={selectedOption}
              {...lazyLoadProps}
              size="small"
              dropdownMatchSelectWidth={false}
              getPopupContainer={({ parentNode }) => parentNode}
            />
          </div>

          <div className="flex w-2/4 pl-1">
            <EverSelect
              placeholder={
                quotaOptions.length !== 0 || isLoading
                  ? t("SELECT_QUOTA")
                  : t("QUOTA_UNAVAILABLE")
              }
              onChange={onQuotaChange}
              value={quota}
              className="!h-full"
              options={quotaOptions}
              suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
              size="small"
              {...(isLoading
                ? { loading: true, disabled: true }
                : { suffixIcon: <ChevronDownIcon className="h-5 w-5" /> })}
              dropdownMatchSelectWidth={false}
              getPopupContainer={({ parentNode }) => parentNode}
              dropdownAlign={{ offset: [0, 6] }}
            ></EverSelect>
          </div>
        </Row>
        <EverLoader
          indicatorType="spinner"
          spinning={isLoading}
          className="!static"
          wrapperClassName="rounded-xl"
        >
          <div className="pt-8">
            {isDataConfigured ? (
              <div className="flex flex-col">
                <QuotaAttainmentSection
                  quotaAttainment={quotaAttainment}
                  baseFontColor={baseFontColor}
                />
              </div>
            ) : (
              <div className="mt-6 w-[80%] mx-auto">
                <img
                  src={emptyQuotaAttainment}
                  alt="empty-quota-attainment"
                  className="w-full"
                />
                <div className="w-[80%] m-auto">
                  <p className="mt-5 text-center">
                    <EverTg.Text className="text-ever-base-content-mid">
                      {t("MONITOR_YOUR_QUOTA")}
                    </EverTg.Text>
                  </p>
                </div>
              </div>
            )}
            {!isEmpty(periodOptions) && (
              <div className="flex justify-center">
                <EverButtonGroup
                  className="bg-ever-base-200"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={
                    (period && periodOptions.indexOf(period)) || 0
                  }
                  size="small"
                  activeButtonClassname="shadow-sm"
                >
                  {periodOptions.map((period, index) => (
                    <EverButton
                      key={`period-button-${index}`}
                      onClick={() => {
                        setPeriod(period);
                        setQuotaAttainment(Number(quotaData[quota][period]));
                      }}
                    >
                      {period}
                    </EverButton>
                  ))}
                </EverButtonGroup>
              </div>
            )}
          </div>
        </EverLoader>
      </div>
    </Fragment>
  );
});

export default QuotaAttainment;
