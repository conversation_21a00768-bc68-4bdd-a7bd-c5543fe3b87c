import { embedDashboard } from "@everstage/superset-ui-embedded-sdk";
import { message } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { DASHBOARD_URLS, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDashboardsStore } from "~/GlobalStores/DashboardsStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  PageNotFound,
  EverBreadcrumbPortal,
  EverSelect,
  EverTg,
  EverLink,
  EverHotToastAlert,
} from "~/v2/components";
import { useQueryParams } from "~/v2/hooks";
import { notAuthorised } from "~/v2/images";

import AdminDashboard from "./admin-dashboard";
import PayeeDashboard from "./payee-dashboard";
import { SupersetDashboard } from "./SupersetDashboard";
import { SupersetDashboardActions } from "./SupersetDashboardActions";
import { getDashboardData, getGuestToken } from "../restApi";

export const DefaultDashboard = ({ dashboardType }) => {
  if (dashboardType === "admin_dashboard") {
    return (
      <div className="w-full">
        <AdminDashboard />
      </div>
    );
  }

  return (
    <div className=" w-full">
      <PayeeDashboard />
    </div>
  );
};

function RenderDashboard(props) {
  const { dashboardId, dashboardType } = props;

  //validation for dashboard by checking if the dashboard id and dashboard type is present in the list of dashboards
  const isValidDashboard = () => {
    return (props?.loggedInUserDashboards || []).some((dashboard) => {
      if (dashboard.value === dashboardId && dashboard.type === dashboardType) {
        return true;
      }
      return false;
    });
  };

  if (isValidDashboard()) {
    if (
      dashboardType === "admin_dashboard" ||
      dashboardType === "payee_dashboard"
    ) {
      return <DefaultDashboard {...props} dashboardType={dashboardType} />;
    }
    if (dashboardType === "superset_dashboard") {
      return (
        <SupersetDashboard
          dashboardId={dashboardId}
          isValidUrl={props.isValidUrl}
        />
      );
    }
  }
  return (
    <PageNotFound
      title="404"
      imgSrc={notAuthorised}
      subTitle="Sorry, the dashboard you visited does not exist."
      showButton
      redirectUrl={DASHBOARD_URLS.DASHBOARDS}
      buttonLabel="Back Home"
    />
  );
}

function DashboardDetailView() {
  const { fetchDashboards: getDashboards, loggedInUserDashboards } =
    useDashboardsStore();
  const { email, accessToken } = useAuthStore();
  const query = useQueryParams();
  const navigate = useNavigate();
  let dashboardId = 0;
  let dashboardType = "";
  const [token, setToken] = useState("");
  const [dashboardData, setDashboardData] = useState({});
  const [supersetHostUrl, setSupersetHostUrl] = useState("");
  const [fiscalOffset, setfiscalOffset] = useState(1);
  const [selectedDashboardData, setSelectedDashboardData] = useState({});

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  if (location.search) {
    //getting the dashboard id and dashboard type from url
    const queryParams = new URLSearchParams(location.search);
    dashboardId = queryParams.get("id");
    dashboardType = queryParams.get("type");
  }
  const [isValidUrl, setIsValidUrl] = useState(true);

  const {
    isLoading: isG2ReviewDataLoading,
    data: g2ReviewData,
    error: g2ReviewDataError,
  } = useReactQuery(
    "g2ReviewData",
    () =>
      fetch("/common/get-g2-review-credentials", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      })
        .then((res) => {
          if (res.ok) return res.json();

          throw new Error("Failed to get G2 review access token");
        })
        .catch((error) => {
          console.log(
            "Error while retrieving data from /get-g2-review-credentials",
            error
          );
          throw error;
        }),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const getDashboardDetail = async () => {
    getDashboardData(accessToken, dashboardId).then(async (response) => {
      if (response.ok) {
        const result = await response.json();
        setDashboardData(result.data);
        setSelectedDashboardData(result.data);
        setSupersetHostUrl(result.supersetHostUrl);
        setfiscalOffset(result.fiscalOffset);
      } else {
        setIsValidUrl(false);
      }
    });
  };

  useEffect(() => {
    if (dashboardId && accessToken && dashboardType === "superset_dashboard") {
      // this function is used to get the guest token
      getGuestToken(accessToken, {
        dashid: dashboardId,
      }).then(async (response) => {
        if (response.ok) {
          const parameters_data = await response.json();
          setToken(parameters_data["token"]);
          await getDashboardDetail();
        } else {
          message.error("Token Generation Failed.");
        }
      });
    }
  }, [dashboardId, accessToken, dashboardType]);

  const handleEmbedDashboard = () => {
    if (supersetHostUrl !== "" && dashboardId !== "" && token !== "") {
      // embed the dashboard on the div that has superset-container as an id.
      // This function generates a dashboard with a given ID and token.
      // It is used to embed a dashboard.
      // The "dashboardId" parameter is the ID of the dashboard to be embedded.
      // The "token" parameter is the token used to access the dashboard.
      embedDashboard({
        id: dashboardId,
        supersetDomain: supersetHostUrl,
        mountPoint: document.querySelector("#superset-container"),
        fetchGuestToken: () => token,
        dashboardUiConfig: {
          hideTitle: true,
          hideTab: true,
          filters: {
            expanded: false,
            visible: true,
            fiscalOffset: fiscalOffset,
          },
        },
      });
    }
  };

  const handleDashboardChange = (value, option) => {
    navigate(`${DASHBOARD_URLS.DASHBOARD}?id=${value}&type=${option.type}`);
  };

  const { hasPermissions } = useUserPermissionStore();
  useEffect(() => {
    handleEmbedDashboard();
  }, [token, supersetHostUrl, dashboardId, fiscalOffset]);

  const showG2ReviewRedirection =
    !hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS) &&
    clientFeatures.showG2ReviewForm !== "Off" &&
    !isG2ReviewDataLoading &&
    !isEmpty(g2ReviewData) &&
    isEmpty(g2ReviewDataError);

  const messageType = clientFeatures.showG2ReviewForm;

  return (
    <>
      <EverBreadcrumbPortal
        dividerIcon={
          <EverTg.Heading3 className="ml-2 text-ever-base-content-low">
            /
          </EverTg.Heading3>
        }
      >
        <div className="flex flex-row items-center w-full">
          <EverSelect
            bordered={false}
            onChange={(value, option) => {
              handleDashboardChange(value, option);
            }}
            value={loggedInUserDashboards.length > 0 ? query.get("id") : null}
            className="rounded h-full !w-max [&>.ant-select-single>.ant-select-selector>.ant-select-selection-item]:!font-[IBM Plex Sans] [&>.ant-select-single>.ant-select-selector>.ant-select-selection-item]:text-ever-base-content [&>.ant-select-single>.ant-select-selector>.ant-select-selection-item]:font-medium [&>.ant-select-single>.ant-select-selector>.ant-select-selection-item]:text-lg [&>.ant-select-single>.ant-select-selector>.ant-select-selection-item]:mr-2 [&>.ant-select]:!w-max"
            options={loggedInUserDashboards}
            dropdownStyle={{ zIndex: 900 }}
          />
          {dashboardType === "superset_dashboard" && (
            <div>
              <SupersetDashboardActions
                dashboardData={dashboardData}
                supersetHostUrl={supersetHostUrl}
                getDashboardDetail={getDashboardDetail}
                handleEmbedDashboard={handleEmbedDashboard}
                selectedDashboardData={selectedDashboardData}
                setSelectedDashboardData={setSelectedDashboardData}
              />
            </div>
          )}
          {showG2ReviewRedirection && (
            <G2ReviewMessage
              messageType={messageType}
              g2ReviewData={g2ReviewData}
              email={email}
            />
          )}
        </div>
      </EverBreadcrumbPortal>
      <RenderDashboard
        loggedInUserDashboards={loggedInUserDashboards}
        getDashboards={getDashboards}
        dashboardId={dashboardId}
        dashboardType={dashboardType}
        isValidUrl={isValidUrl}
      />
    </>
  );
}

export default observer(DashboardDetailView);

function G2ReviewMessage({ messageType, g2ReviewData, email }) {
  const [animationClass, setAnimationClass] = useState("translate-x-[1000px]");
  useEffect(() => {
    const timeout = setTimeout(() => {
      setAnimationClass("translate-x-0");
    }, 1000);
    return () => {
      localStorage.setItem("hideG2DynamicMessage", "true");
      clearTimeout(timeout);
    };
  }, []);

  const reviewUrl = `https://www.g2.com/partnerships/${g2ReviewData.partnerName}/users/login.embed?state=${g2ReviewData.accessToken}&email=${email}`;

  if (messageType === "Static") {
    return (
      <>
        <div className="flex-grow" />
        <EverLink
          href={reviewUrl}
          target="_blank"
          label="Leave your review about Everstage on G2"
        />
      </>
    );
  } else if (
    messageType === "Dynamic" &&
    localStorage.getItem("hideG2DynamicMessage") !== "true"
  ) {
    return (
      <div className="relative grow z-50">
        <EverHotToastAlert
          className={`absolute top-0 right-0 transition-transform duration-300 ${animationClass}`}
          type="info"
          title={"Loving Everstage?"}
          description={"Share your experience with others"}
          buttons={[
            {
              buttonText: "Rate us on G2",
              onClick: () => {
                localStorage.setItem("hideG2DynamicMessage", "true");
                window.open(reviewUrl, "_blank");
                setAnimationClass("translate-x-[1000px]");
              },
            },
            {
              buttonText: "Maybe later",
              onClick: () => {
                setAnimationClass("translate-x-[1000px]");
                setTimeout(() => {
                  localStorage.setItem("hideG2DynamicMessage", "true");
                }, 1000);
              },
            },
          ]}
        />
      </div>
    );
  }
  return null;
}
