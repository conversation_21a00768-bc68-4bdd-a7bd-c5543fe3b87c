import { useQuery, gql } from "@apollo/client";
import {
  CurrencyDollarCircleIcon,
  HourglassPentagonIcon,
} from "@everstage/evericons/solid";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { EverButton, EverLoader, EverTg } from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyCommissionBuddy } from "~/v2/images";

const COMMISSION_BUDDY = gql`
  query CommissionBuddy($payeeId: String!) {
    commissionBuddy(payeeId: $payeeId)
  }
`;

const CommissionBuddy = observer((props) => {
  const { email: loggedInUser } = useAuthStore();

  const { store } = props;
  const { currencySymbol } = store;
  const { isRevenueRole } = useEmployeeStore();
  const [commBuddy, setCommBuddy] = useState(null);

  const navigate = useNavigate();
  const { loading, data } = useQuery(COMMISSION_BUDDY, {
    variables: {
      payeeId: loggedInUser,
    },
    fetchPolicy: loggedInUser ? "network-only" : "cache-only",
  });

  const { t } = useTranslation();

  useEffect(() => {
    let commissionBuddyData = null;
    if (data) {
      let pcData = JSON.parse(data.commissionBuddy);
      if (pcData && !("error" in pcData)) commissionBuddyData = pcData;
    }
    setCommBuddy(commissionBuddyData);
  }, [data]);

  if (loading) {
    return <WidgetLoading />;
  }

  return (
    <div className="h-full">
      <div className="flex w-full mb-8">
        <EverTg.Heading3 className="text-ever-base-content">
          {t("COMM_BUDDY")}
        </EverTg.Heading3>
      </div>

      <div className="card-body">
        <EverLoader
          indicatorType="spinner"
          spinning={loading}
          className="!static flex w-full"
          wrapperClassName="rounded-xl"
        >
          {commBuddy ? (
            <div className="w-full h-full">
              {commBuddy.variable_pay != 0 && (
                <>
                  <div className="flex flex-row mb-6">
                    <div className="w-10 h-10 rounded-full bg-ever-chartColors-40 mr-3 flex items-center justify-center">
                      <CurrencyDollarCircleIcon className="w-5 h-5 text-ever-chartColors-10" />
                    </div>
                    <div className="flex flex-col text-left">
                      <EverTg.Heading1 className="tracking-tighter">
                        {currencySymbol}
                        {formatCurrency(commBuddy?.target)}
                      </EverTg.Heading1>
                      <EverTg.Text>{t("NEED_TO_TRACK_PAYOUT")}</EverTg.Text>
                    </div>
                  </div>
                </>
              )}
              <div className="flex flex-row mb-6">
                <div className="w-10 h-10 rounded-full bg-ever-chartColors-37 mr-3 flex items-center justify-center">
                  <HourglassPentagonIcon className="w-5 h-5 text-ever-chartColors-5" />
                </div>
                <div className="flex flex-col text-left">
                  <EverTg.Heading1>{commBuddy.days_away} days</EverTg.Heading1>
                  <EverTg.Text>left for period close</EverTg.Text>
                </div>
              </div>
              {/* <Row className="w-full">
                <Col span={24} className="!text-center">
                  <EverTg.Text className="text-ever-base-content">
                    We are
                  </EverTg.Text>
                  <EverTg.Text className={"text-ever-primary"}>
                    {" "}
                    {commBuddy.days_away}{" "}
                  </EverTg.Text>
                  <EverTg.Text className="text-ever-base-content">
                    days away from period close
                  </EverTg.Text>
                </Col> 
              </Row>*/}
              {isRevenueRole && (
                <EverButton
                  type="filled"
                  color="base"
                  size="medium"
                  className={"w-full mt-3"}
                  onClick={() => {
                    navigate("/crystal");
                  }}
                >
                  {t("MAKE_MORE_COMM")}
                </EverButton>
              )}
            </div>
          ) : (
            <div className="mt-4 w-[80%] mx-auto">
              <img
                src={emptyCommissionBuddy}
                alt="empty-commission-buddy"
                className="w-full"
              />
              <div className="!mt-5 text-center w-[80%] m-auto">
                <EverTg.Text className=" text-center text-ever-base-content-mid">
                  {t("GET_ACTIONABLE_INSIGHT")}
                </EverTg.Text>
              </div>
            </div>
          )}
        </EverLoader>
      </div>
    </div>
  );
});

export default CommissionBuddy;
