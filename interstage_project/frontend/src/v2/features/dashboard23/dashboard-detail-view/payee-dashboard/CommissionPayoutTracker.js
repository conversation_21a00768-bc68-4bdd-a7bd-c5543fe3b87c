import { useQuery, gql } from "@apollo/client";
import { Row, Col, Divider } from "antd";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useState, useEffect } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  formatCurrencyWithoutDecimal,
  getLocalizedCurrencyValue,
} from "~/Utils/CurrencyUtils";
import { getClientFiscalYear } from "~/Utils/DateUtils";
import { percentageOneDecimalPlace } from "~/Utils/QuotaUtils";
import {
  useCurrentTheme,
  EverButton,
  EverButtonGroup,
  EverTg,
  EverDatePicker,
  EverLoader,
  EverBadge,
} from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyCommissionPayoutsTracker } from "~/v2/images";

import { LastUpdated } from "../LastUpdated";

const COMMISSION_SUMMARY_PAYOUT_TRACKER = gql`
  query PayeeCommissionSummaryTracker($payeeId: String!, $year: String!) {
    payeeCommissionSummaryTracker(year: $year, payeeId: $payeeId)
  }
`;

const CommissionPayoutsTracker = observer((props) => {
  const { email: loggedInUser } = useAuthStore();
  const { store, showErrorBanner } = props;
  const { fontFamily, currencySymbol } = store;
  const { chartColors, base, primary } = useCurrentTheme();
  const themeColor = Object.values(chartColors || {});
  const baseFontColor = base.content.mid || "";
  const valueColor = primary.DEFAULT || "";
  //   const [commPayoutsTracker, setCommPayoutsTracker] = useState();
  const [ytd, setYtd] = useState();
  const [ytdPerc, setYtdPerc] = useState();
  const [chartData, setChartData] = useState();
  const [year, setYear] = useState();
  const [isDataConfigured, setIsDataConfigured] = useState(false);
  const [totalVarPay, setTotalVarPay] = useState(0);
  const [isQtd, setIsQtd] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const myClient = useRecoilValue(myClientAtom);
  const { fiscalStartMonthZero: fiscalYearMonth, baseCurrency } = myClient;

  const { loading, data } = useQuery(COMMISSION_SUMMARY_PAYOUT_TRACKER, {
    variables: {
      payeeId: loggedInUser,
      year: year && String(year.year()),
    },
    fetchPolicy: loggedInUser && year ? "network-only" : "cache-only",
    onCompleted: () => setIsFirstLoad(false),
    onError: () => setIsFirstLoad(false),
  });

  const { t } = useTranslation();

  useEffect(() => {
    let dataConfigured = false;
    if (data && fiscalYearMonth != undefined) {
      let resData = JSON.parse(data.payeeCommissionSummaryTracker);
      if (resData && !("error" in resData)) {
        let pctData = isQtd
          ? resData?.commission_payouts_qtd
          : resData?.commission_payouts;
        let fiscalPeriods = [];
        if (Object.keys(pctData).length === 4) {
          fiscalPeriods = ["Q1", "Q2", "Q3", "Q4"];
        } else if (Object.keys(pctData).length === 12) {
          let monthArr = moment.monthsShort();
          fiscalPeriods = monthArr
            .slice(fiscalYearMonth, monthArr.length)
            .concat(monthArr.slice(0, fiscalYearMonth));
        } else {
          fiscalPeriods = null;
        }
        let chartData = [];
        let ytd = 0;
        let totalVarPay = 0;

        //const currentMonth = moment().endOf("month").format("MMM");
        fiscalPeriods &&
          fiscalPeriods.forEach((fp) => {
            let comm = pctData[fiscalPeriods.indexOf(fp) + 1].commission_amount;
            chartData.push({
              label: fp,
              value: Math.floor(comm),
              toolText: getLocalizedCurrencyValue(comm, currencySymbol),
              variablePay: parseFloat(
                pctData[fiscalPeriods.indexOf(fp) + 1].variable_pay
              ),
            });
            ytd = ytd + parseFloat(comm);
            totalVarPay =
              totalVarPay +
              parseFloat(pctData[fiscalPeriods.indexOf(fp) + 1].variable_pay);
          });
        setTotalVarPay(totalVarPay);
        // if (getClientFiscalYear(fiscalYearMonth).year() == year.year()) {
        //   const monthToSlice = chartData.findIndex(
        //     (item) => item.label == currentMonth
        //   );
        //   let sData = chartData.slice(0, monthToSlice + 1);
        //   setChartData(sData);
        //   let mYtd = 0;
        //   let mVP = 0;
        //   sData.forEach((x) => {
        //     mYtd = mYtd + parseFloat(x.value);
        //     mVP = mVP + x.variablePay;
        //   });
        //   ytd = mYtd;
        //   totalVarPay = mVP;
        // } else {
        setChartData(chartData);
        // }
        setYtd(ytd);
        let ytdPerc = ((ytd / totalVarPay) * 100).toFixed(2);
        setYtdPerc(isNaN(ytdPerc) ? 0.0 : ytdPerc);
        dataConfigured = true;
      } else {
        showErrorBanner();
      }
    }
    setIsDataConfigured(dataConfigured);
  }, [data, fiscalYearMonth, isQtd]);

  useEffect(() => {
    if (fiscalYearMonth != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonth));
    }
  }, [fiscalYearMonth]);

  const isIndianCurrency = baseCurrency === "INR";

  const dataSource = {
    chart: {
      paletteColors: themeColor[0],
      usePlotGradientColor: 1,
      plotGradientColor: [themeColor[0], themeColor[18]],
      valueFontColor: valueColor,
      baseFont: fontFamily,
      baseFontColor: baseFontColor,
      valueFont: fontFamily,
      baseFontSize: 12,
      valueFontSize: 12,
      showShadow: 0,
      showValues: 1,
      maxColWidth: 35,
      placeValuesInside: "0",
      numvisibleplot: 5,
      canvasTopPadding: 20,
      formatNumberScale: 1,
      numberPrefix: `${currencySymbol}`,
      scrollPadding: 10,
      divLineAlpha: 0,
      showXAxisLine: 1,
      xAxisLineColor: base[100],
      showHoverEffect: 0,
      theme: "fusion",
      chartLeftMargin: 20,
      chartRightMargin: 20,
      bgColor: base?.DEFAULT,
      toolTipBgColor: base?.DEFAULT,
      toolTipColor: base?.content?.DEFAULT,
      toolTipBorderColor: base?.[300],
      numberScaleValue: isIndianCurrency ? "1000,100,100" : "1000,1000,1000",
      numberScaleUnit: isIndianCurrency ? "K,L,Cr" : "K,M,B",
    },
    data: chartData,
  };

  if (loading && isFirstLoad) {
    return <WidgetLoading />;
  }

  return (
    <>
      <div>
        <div className="flex w-full mb-4 justify-between">
          <EverTg.Heading3 className="text-ever-base-content">
            {t("PAYOUTS_TRACKER")}
          </EverTg.Heading3>
          {data &&
            data?.payeeCommissionSummaryTracker &&
            JSON.parse(data.payeeCommissionSummaryTracker)?.last_updated && (
              <div className="flex">
                <LastUpdated
                  lastUpdated={`${
                    JSON.parse(data.payeeCommissionSummaryTracker).last_updated
                  } UTC`}
                />
              </div>
            )}
        </div>

        <div className="flex mb-8">
          {isDataConfigured && (
            <EverButtonGroup
              className="bg-ever-base-200 mr-2"
              activeBtnType="text"
              activeBtnColor="primary"
              defActiveBtnIndex={0}
              size="small"
              activeButtonClassname="shadow-sm"
            >
              <EverButton
                onClick={() => {
                  setIsQtd(false);
                }}
              >
                Monthly
              </EverButton>
              <EverButton
                onClick={() => {
                  setIsQtd(true);
                }}
              >
                Quarterly
              </EverButton>
            </EverButtonGroup>
          )}
          <EverDatePicker.Legacy
            onChange={(date) => setYear(date)}
            picker="year"
            bordered={true}
            value={year}
            allowClear={false}
            size="small"
            className="w-28 h-full mt-1"
            disabled={loading}
            getPopupContainer={({ parentNode }) => parentNode}
          />
        </div>
      </div>

      <EverLoader
        indicatorType="spinner"
        spinning={loading}
        className="!static"
        wrapperClassName="rounded-xl"
      >
        <>
          {isDataConfigured ? (
            <>
              <Row align="middle">
                <Col span={16} className="flex items-center">
                  <>
                    <EverBadge
                      type={`success`}
                      icon={
                        <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                      }
                      title={`${t("YTD_PAYOUTS")} ${
                        currencySymbol ? currencySymbol : ""
                      }${formatCurrencyWithoutDecimal(ytd)} `}
                      className={"text-ever-success-lite-content"}
                    />
                    {/* <EverTg.Text className="text-ever-base-content mr-1">
                      {t("YTD_PAYOUTS")}
                    </EverTg.Text>
                    <EverTg.Text className="font-semibold text-ever-success-content-lite">
                      {`${
                        currencySymbol ? currencySymbol : ""
                      } ${formatCurrencyWithoutDecimal(ytd)}`}
                    </EverTg.Text> */}
                  </>
                  {totalVarPay != 0 && (
                    <>
                      <Divider
                        className="border-ever-base-400"
                        type="vertical"
                      />
                      <EverBadge
                        type={`info`}
                        title={`${percentageOneDecimalPlace(
                          ytdPerc
                        )}% of the YTD variable pay `}
                        icon={
                          <div className="w-1.5 h-1.5 rounded-full bg-ever-chartColors-12"></div>
                        }
                        className={
                          "bg-ever-chartColors-41 text-ever-chartColors-20"
                        }
                      />
                      {/* <EverTg.Text
                        className="font-semibold text-ever-success-content-lite "
                        title={`${ytdPerc} %`}
                      >
                        {percentageOneDecimalPlace(ytdPerc)} %
                      </EverTg.Text>
                      <EverTg.Text className="text-ever-base-content ml-1">
                        {`of the YTD variable pay`}
                      </EverTg.Text> */}
                    </>
                  )}
                </Col>
              </Row>
              <Row className="!mt-2">
                <Col span={24}>
                  <ReactFusioncharts
                    type="column2d"
                    width="100%"
                    height="350"
                    dataFormat="JSON"
                    dataUpdated={base?.DEFAULT}
                    dataSource={dataSource}
                  />
                </Col>
              </Row>
            </>
          ) : (
            <div className="mt-5 w-[80%] mx-auto">
              <img
                src={emptyCommissionPayoutsTracker}
                alt="empty-commission-payouts-tracker"
                className="w-full"
              />
              <div className="text-center !mt-5 w-[70%] m-auto">
                <EverTg.Text className="text-center text-ever-base-content-mid">
                  {t("TRACK_COMMISSION_PAYOUT")}
                </EverTg.Text>
              </div>
            </div>
          )}
        </>
      </EverLoader>
    </>
  );
});

export default CommissionPayoutsTracker;
