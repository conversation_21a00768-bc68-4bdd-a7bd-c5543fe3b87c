import { useQuery, gql } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { CoinsStackedIcon } from "@everstage/evericons/solid";
import { Row, Col, Empty } from "antd";
import { encode as base64_encode } from "base-64";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useState, Fragment } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { formatCurrencyWithoutDecimal } from "~/Utils/CurrencyUtils";
import {
  EverButton,
  useCurrentTheme,
  EverLoader,
  EverSelect,
  EverTg,
  EverCard,
} from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyCommissionPayout } from "~/v2/images";

import { AnimatedNumber } from "../AnimatedNumber";

const COMMISSION_PAYOUTS = gql`
  query PayeeCommissionCurrent($payeeId: String!) {
    payeeCommissionCurrent(payeeId: $payeeId)
  }
`;

const cardClasses =
  "relative !bg-ever-base border border-solid border-ever-base-400 p-5 duration-300 ease-in-outshadow-sm rounded-xl";

const CommissionPayouts = observer((props) => {
  const { chartColors, base, warning, error, success } = useCurrentTheme();

  const { email: loggedInUser } = useAuthStore();
  const { store } = props;
  const { fontFamily, currencySymbol } = store;
  const themeColor = Object.values(chartColors || {});
  const [commPayouts, setCommPayouts] = useState();
  const [tabPaneText, setTabPaneText] = useState([]);
  const [period, setPeriod] = useState("");
  const [isDataConfigured, setIsDataConfigured] = useState(false);
  const navigate = useNavigate();
  const { loading, data } = useQuery(COMMISSION_PAYOUTS, {
    variables: {
      payeeId: loggedInUser,
    },
    fetchPolicy: loggedInUser ? "network-only" : "cache-only",
  });

  const { t } = useTranslation();

  useEffect(() => {
    let dataConfigured = false;
    if (data) {
      let pcData = JSON.parse(data.payeeCommissionCurrent);
      if (pcData && !("error" in pcData)) {
        setCommPayouts(pcData);
        setTabPaneText(Object.keys(pcData));
        dataConfigured = true;
      }
    }
    setIsDataConfigured(dataConfigured);
  }, [data]);

  useEffect(() => {
    if (tabPaneText.length !== 0) {
      setPeriod(tabPaneText[0]);
    }
  }, [tabPaneText]);

  const getStatementsLink = () => {
    let link = "/statements";
    if (!isEmpty(period) && !isEmpty(commPayouts)) {
      const psd = commPayouts[period].psd;
      const ped = commPayouts[period].ped;
      const payeeEmailId = loggedInUser;
      let encodedData = base64_encode(
        JSON.stringify({ psd, ped, payeeEmailId })
      );
      link = `/statements/${encodedData}`;
    }

    return link;
  };

  if (loading) {
    return <WidgetLoading />;
  }

  const variablePay =
    isDataConfigured &&
    tabPaneText.length !== 0 &&
    period &&
    commPayouts &&
    commPayouts[period]?.variable_pay;

  const isVariablePayZero = variablePay == 0;

  return (
    <EverCard
      outlined={false}
      className={twMerge("flex flex-col", cardClasses, "h-full min-h-[350px]")}
      shadowSize="none"
    >
      <div className="mb-8">
        <div className="flex w-full mb-4">
          <EverTg.Heading3 className="flex-auto  mx-0 text-ever-base-content">
            {t("YOUR_PAYOUTS")}
          </EverTg.Heading3>
        </div>
        <div className="flex w-40">
          <EverSelect
            className="dashboard-select  w-full h-full flex items-center"
            defaultValue={period}
            onChange={(value) => setPeriod(value)}
            value={period}
            showArrow={true}
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            disabled={loading}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
            dropdownAlign={{ offset: [0, 6] }}
            data-testid="pt-dashboard-payouts-period-select"
          >
            {tabPaneText.map((text) => (
              <EverSelect.Option key={text} value={text}>
                {text}
              </EverSelect.Option>
            ))}
          </EverSelect>
        </div>
      </div>
      <EverLoader
        indicatorType="spinner"
        spinning={loading}
        className="!static"
        wrapperClassName="rounded-xl"
      >
        <>
          {isDataConfigured ? (
            tabPaneText.length !== 0 ? (
              <div className="flex flex-col justify-between h-full">
                {period && (
                  <Row className="h-72">
                    <Col
                      span={24}
                      className={twMerge(
                        isVariablePayZero
                          ? "justify-center"
                          : "justify-between",
                        "flex flex-col h-full"
                      )}
                    >
                      {commPayouts &&
                      commPayouts[period].variable_pay === "0" ? (
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                      ) : (
                        <CommPayoutSection
                          commission={
                            commPayouts && commPayouts[period].commission_amount
                          }
                          variablePay={variablePay}
                          currencySymbol={currencySymbol}
                          fontType={fontFamily}
                          accentColor={themeColor[0]}
                          chartColors={themeColor}
                          base={base}
                          warning={warning}
                          error={error}
                          success={success}
                          isVariablePayZero={isVariablePayZero}
                        />
                      )}
                    </Col>
                  </Row>
                )}
                <Row className="!mt-2">
                  <Col span={24} className="text-center">
                    <EverButton
                      type="filled"
                      color="base"
                      size="medium"
                      className={"w-full"}
                      onClick={() => {
                        const link = getStatementsLink();
                        navigate(link);
                      }}
                    >
                      View Statement
                    </EverButton>
                  </Col>
                </Row>
              </div>
            ) : (
              <Empty
                className="block mx-auto mt-[20%]"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )
          ) : (
            <div className="mt-[10%] w-[80%] mx-auto">
              <img
                src={emptyCommissionPayout}
                alt="empty-commission-payout"
                className="w-full"
              />
              <div className="w-[70%] m-auto mt-[20px] text-center">
                <EverTg.Text className=" text-ever-base-content-mid">
                  {t("VIEW_COMMISSION_PAYOUT")}
                </EverTg.Text>
              </div>
            </div>
          )}
        </>
      </EverLoader>
    </EverCard>
  );
});
export default CommissionPayouts;

const CommPayoutSection = observer((props) => {
  const {
    commission,
    variablePay,
    currencySymbol,
    fontType,
    accentColor,
    base,
    warning,
    error,
    success,
    chartColors,
    isVariablePayZero,
  } = props;

  return (
    <Fragment>
      <Row>
        <Col span={24} className="text-center p-0 text-sm">
          {/* <div
            className={"text-ever-info text-xl"}
            //style={{ fontSize: commissionSize }}
          >
            {commission &&
              `${
                currencySymbol ? currencySymbol : ""
              } ${formatCurrencyWithoutDecimal(commission)}`}
          </div> */}
          <div
            className={twMerge(isVariablePayZero ? "m-auto w-full" : "w-2/4")}
          >
            <AnimatedNumber
              handleRoundOff={(value) => {
                if (value === null) return null;
                if (value === 0) return 0;
                return formatCurrencyWithoutDecimal(value);
              }}
              key={commission}
              number={Math.floor(commission)}
              description="Payout"
              layout={!isVariablePayZero ? "landscape" : "portrait"}
              noStyle={isVariablePayZero}
              iconClassName={`bg-ever-chartColors-12 bg-opacity-10`}
              prepend={currencySymbol ? currencySymbol : ""}
              icon={
                <CoinsStackedIcon className="w-6 h-6 text-ever-chartColors-12" />
              }
              className={twMerge(
                isVariablePayZero
                  ? "[&>.text-lg>.flex-col>.w-full>*]:text-4xl [&>.text-lg>.flex-col>*]:text-4xl [&>.text-lg>.justify-center]:w-24 [&>.text-lg>.justify-center]:h-24 [&>.text-lg>.justify-center>*]:w-12 [&>.text-lg>.justify-center>*]:h-12"
                  : "[&>.text-lg>.flex-col]:w-[calc(100%-60px)]"
              )}
            />
          </div>
        </Col>
      </Row>
      {!isVariablePayZero && (
        <ReactFusioncharts
          type="hlineargauge"
          width="100%"
          height="140"
          dataFormat="JSON"
          dataUpdated={base?.DEFAULT}
          dataSource={getDataSource(
            commission,
            variablePay,
            fontType,
            accentColor,
            currencySymbol,
            base,
            warning,
            error,
            success,
            chartColors
          )}
        />
      )}
    </Fragment>
  );
});

const getDataSource = (
  commission,
  variablePay,
  font,
  color,
  currencySymbol,
  base,
  warning,
  error,
  success,
  chartColors
) => {
  let fCommission = parseFloat(commission);
  let fVarPay = parseFloat(variablePay);
  let upperLimit = fVarPay + fVarPay / 2;

  let dataSource = {
    chart: {
      theme: "fusion",
      baseFont: "Inter",
      valueFont: "Inter",
      pointerbghovercolor: base?.DEFAULT,
      pointerbghoveralpha: "80",
      pointerhoverradius: "12",
      showborderonhover: "1",
      pointerborderhovercolor: base?.content?.mid,
      pointerborderhoverthickness: "2",
      showtickmarks: "0",
      numberprefix: currencySymbol ? `${currencySymbol} ` : "$ ",
      showShadow: 0,
      baseFontColor: base?.DEFAULT,
      toolTipBgColor: base?.DEFAULT,
      toolTipColor: base?.content?.DEFAULT,
      toolTipBorderColor: base?.[300],
      lowerLimit: 0,
      upperLimit: upperLimit,
      //chartLeftMargin: 40,
      chartRightMargin: 40,
      //gaugefillmix: "{dark-20},{light+70},{dark-10}",
      valueFontColor: color,
      valueFontBold: "1",
      origH: "430",
      baseFontSize: 12,
      valueFontSize: 12,
      // autoScale: 1,
      // manageResize: 1
      bgColor: base?.DEFAULT,
    },
    colorrange: {
      color: [
        {
          minvalue: "0",
          maxvalue: fCommission,
          code:
            fCommission < fVarPay / 2
              ? chartColors[45]
              : fCommission < fVarPay
              ? chartColors[44]
              : fCommission < upperLimit
              ? chartColors[46]
              : chartColors[46],
        },
        {
          minvalue: fCommission,
          maxvalue: upperLimit,
          code: base[100],
        },
      ],
    },
    pointers: {
      pointer: [
        {
          value: fCommission > upperLimit ? upperLimit + "+" : fCommission,
          tooltext: `${
            Math.round(((fCommission / fVarPay) * 100 + Number.EPSILON) * 100) /
            100
          } %`,
          showValue: 0, //fCommission > upperLimit ? "0" : "1",
          sides: 3,
        },
      ],
    },
    trendPoints: {
      point: [
        {
          startValue: fVarPay,
          color: base?.content?.DEFAULT,
          thickness: "2",
          displayValue: "100 %",
          showOnTop: "1",
          useMarker: "1",
        },
        {
          startValue: fCommission > upperLimit ? upperLimit : fCommission,
          // startValue: fCommission > fVarPay ? fVarPay : fCommission,
          color:
            fCommission < fVarPay / 2
              ? error.hover
              : fCommission < fVarPay
              ? warning.hover
              : fCommission < upperLimit
              ? success.hover
              : success.hover,
          showOnTop: "0",
          alpha: 0,
          displayValue: `\n\n
            ${
              Math.round(
                ((fCommission / fVarPay) * 100 + Number.EPSILON) * 100
              ) / 100
            } %\n
            ${
              ""
              // fCommission < 500
              //   ? ""
              //   : fCommission < fVarPay / 2
              //   ? "Lots of Work There"
              //   : fCommission < fVarPay
              //   ? "Almost There"
              //   : fCommission < upperLimit
              //   ? "Woohoo! Time to maximize"
              //   : "Woohoo! Time to maximize"
            }
          `,
        },
        // {
        //   startValue: upperLimit,
        //   color: color,
        //   showOnTop: "0",
        //   alpha: 0,
        // },
        // {
        //   startValue: fVarPay / 2,
        //   color: color,
        //   showOnTop: "0",
        //   alpha: 0,
        // },
      ],
    },
  };
  return dataSource;
};
