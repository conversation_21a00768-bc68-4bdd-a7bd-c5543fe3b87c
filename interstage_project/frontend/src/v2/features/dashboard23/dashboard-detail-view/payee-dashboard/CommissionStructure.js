import { useQuery, gql } from "@apollo/client";
import {
  CoinsSwapIcon,
  WalletIcon,
  UserCircleIcon,
} from "@everstage/evericons/solid";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { EverTg, EverLoader, EverLabel } from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";

const COMMISSION_STRUCTURE = gql`
  query CommissionStructureAndEmployeePayForAll {
    commissionStructure
    employeeBasicHierarchyDetails {
      employeeHierarchy {
        reportingManagerEmailId
        effectiveStartDate
        effectiveEndDate
        managerDetails {
          firstName
          lastName
        }
      }
    }
  }
`;

const CommissionStructure = observer((props) => {
  const { email: loggedInUser } = useAuthStore();
  const { store } = props;
  const { currencySymbol } = store;
  const [commStructure, setCommStructure] = useState();
  const [reportingManagerName, setReportingManagerName] = useState();

  const { t } = useTranslation();

  const { loading, data } = useQuery(COMMISSION_STRUCTURE, {
    fetchPolicy: loggedInUser ? "network-only" : "cache-only",
  });
  useEffect(() => {
    let commissionStructureData = null;
    if (data) {
      let pcsData = JSON.parse(data.commissionStructure);
      if (pcsData && !("error" in pcsData)) commissionStructureData = pcsData;
      // get the current manager
      if (
        data.employeeBasicHierarchyDetails &&
        data.employeeBasicHierarchyDetails.employeeHierarchy
      ) {
        let currentDate = moment();
        let mgr;
        data.employeeBasicHierarchyDetails.employeeHierarchy.forEach(
          (mgrRec) => {
            let effEndDate = mgrRec.effectiveEndDate
              ? mgrRec.effectiveEndDate.split("T")[0]
              : null;
            let effStartDate = mgrRec.effectiveStartDate
              ? mgrRec.effectiveStartDate.split("T")[0]
              : null;
            if (
              (currentDate.isSameOrBefore(effEndDate, "day") || !effEndDate) &&
              currentDate.isSameOrAfter(effStartDate, "day")
            ) {
              mgr =
                mgrRec.managerDetails.firstName +
                " " +
                mgrRec.managerDetails.lastName;
            }
          }
        );
        setReportingManagerName(mgr);
      }
    }
    setCommStructure(commissionStructureData);
  }, [data]);

  if (loading) {
    return <WidgetLoading />;
  }

  return (
    <div>
      <div className="flex w-full mb-8">
        <div className="flex">
          <EverTg.Heading3 className="flex  mx-0 text-ever-base-content">
            {t("COMM_STRUCTURE")}
          </EverTg.Heading3>
        </div>
      </div>
      <div className="card-body">
        <EverLoader
          indicatorType="spinner"
          spinning={loading}
          className="!static"
          wrapperClassName="rounded-xl"
        >
          <>
            <div className="flex mb-6 w-full justify-start">
              <div className="w-52 text-right flex items-center  ">
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-10 bg-opacity-10 mr-2">
                  <CoinsSwapIcon className="w-5 h-5 text-ever-chartColors-10" />
                </div>
                <EverLabel
                  className={"justify-end text-ever-base-content text-lg"}
                >
                  {t("PAYOUT_FREQUENCY")}
                </EverLabel>
              </div>
              <div className="w-32 flex items-center ">
                {commStructure ? (
                  <EverTg.SubHeading3>
                    {commStructure.payout_frequency}
                  </EverTg.SubHeading3>
                ) : (
                  <EverTg.SubHeading3>NA</EverTg.SubHeading3>
                )}
              </div>
            </div>
            {commStructure &&
              commStructure.variable_pay &&
              parseFloat(commStructure.variable_pay) != 0 && (
                <div className="flex mb-6 w-full justify-start">
                  <div className=" text-right flex items-center  w-52">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-20 bg-opacity-10 mr-2">
                      <WalletIcon className="w-5 h-5 text-ever-chartColors-20" />
                    </div>
                    <EverLabel
                      className={"justify-end text-ever-base-content text-lg"}
                    >
                      Variable Pay
                    </EverLabel>
                  </div>
                  <div className=" flex items-center w-32">
                    <EverTg.SubHeading3>
                      {`${currencySymbol}${formatCurrency(
                        commStructure.variable_pay
                      )}`}
                    </EverTg.SubHeading3>
                  </div>
                </div>
              )}
            <div className="flex w-full justify-start">
              <div className="text-right flex items-center w-52">
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-29 bg-opacity-10 mr-2">
                  <UserCircleIcon className="w-5 h-5 text-ever-chartColors-29" />
                </div>
                <EverLabel
                  className={"justify-end text-ever-base-content text-lg"}
                >
                  Manager
                </EverLabel>
              </div>
              <div className=" flex items-center w-32">
                {!isEmpty(reportingManagerName) ? (
                  <EverTg.SubHeading3>
                    {reportingManagerName}
                  </EverTg.SubHeading3>
                ) : (
                  <EverTg.SubHeading3>NA</EverTg.SubHeading3>
                )}
              </div>
            </div>
          </>
        </EverLoader>
      </div>
    </div>
  );
});
export default CommissionStructure;
