import { useQuery, gql } from "@apollo/client";
import { TicketSolidIcon } from "@everstage/evericons/solid";
import { Row, Col } from "antd";
import cx from "classix";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverCard,
  EverLoader,
  EverBadge,
  EverTg,
  EverButton,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";

const QUERIES_APPROVALS = gql`
  query DrsPayoutStatus($payeeId: String!) {
    drsPayoutStatus(payeeId: $payeeId)
  }
`;

const QueriesAndApprovals = observer(() => {
  const { email: loggedInUser } = useAuthStore();
  const [drsData, setDrsData] = useState();

  const { loading, data } = useQuery(QUERIES_APPROVALS, {
    variables: {
      payeeId: loggedInUser,
    },
    fetchPolicy: loggedInUser ? "network-only" : "cache-only",
  });

  const cardClasses =
    "flex flex-col relative h-full !bg-ever-base border border-solid border-ever-base-400 p-5 duration-300 ease-in-out hover:z-50 hover:scale-101 transform-gpu rounded-xl";

  const navigate = useNavigate();

  const { t } = useTranslation();

  useEffect(() => {
    if (data) {
      setDrsData(JSON.parse(data.drsPayoutStatus));
    }
  }, [data]);

  return (
    <Row gutter={[20, 20]}>
      <RBACProtectedComponent permissionId={RBAC_ROLES.VIEW_QUERIES}>
        <Col span={12}>
          <EverCard
            outlined={false}
            className={`${cardClasses} min-h-[200px]`}
            bodyStyle={{ height: "100%", padding: 0 }}
            shadowSize="none"
          >
            {loading && <WidgetLoading />}
            <div className={cx(loading && "hidden")}>
              <div className="flex w-full items-center">
                <div className="flex-auto w-full">
                  <EverTg.Heading3 className="text-ever-base-content">
                    Queries
                  </EverTg.Heading3>
                </div>
              </div>
              <div className="card-body">
                <EverLoader
                  indicatorType="spinner"
                  spinning={loading}
                  className="!static "
                  wrapperClassName="rounded-xl"
                >
                  <>
                    <Row className="mt-8" justify="center">
                      <Col span={24}>
                        <DisplayComponent
                          text="My tickets"
                          value={drsData ? drsData.tickets : 0}
                          linkTo="/queries/open"
                          type="success"
                          icon={
                            <TicketSolidIcon className="w-6 h-6 text-ever-chartColors-1" />
                          }
                          iconContainerClass={
                            " w-12 h-12 rounded-full flex items-center justify-center mr-2 bg-ever-chartColors-1 bg-opacity-10"
                          }
                        />
                      </Col>
                    </Row>
                    <RBACProtectedComponent
                      permissionId={RBAC_ROLES.CREATE_QUERIES}
                    >
                      <Row className="mt-6">
                        <EverButton
                          type="filled"
                          color="base"
                          size="medium"
                          className={"w-full"}
                          onClick={() => {
                            navigate("/queries/allTickets");
                          }}
                        >
                          Raise Query
                        </EverButton>
                      </Row>
                    </RBACProtectedComponent>
                  </>
                </EverLoader>
              </div>
            </div>
          </EverCard>
        </Col>
      </RBACProtectedComponent>
      <Col span={12}>
        <EverCard
          outlined={false}
          className={`${cardClasses} min-h-[200px]`}
          bodyStyle={{ height: "100%", padding: 0 }}
          shadowSize="none"
        >
          {loading && <WidgetLoading />}
          <div className={cx("flex flex-col h-full", loading && "hidden")}>
            <div className="flex w-full items-center">
              <div className="flex-auto w-full">
                <EverTg.Heading3 className="text-ever-base-content">
                  {t("PAYOUT_STATUS")}
                </EverTg.Heading3>
              </div>
            </div>
            <div className="card-body">
              <EverLoader
                indicatorType="spinner"
                spinning={loading}
                className="!static flex justify-center items-center"
                wrapperClassName="rounded-xl"
              >
                <Row className="!p-6 h-full" justify="center" align="middle">
                  <Col className="text-center" span={24}>
                    <div className="flex items-center mb-2">
                      {/* <LockIcon className="w-5 h-5 mr-2" /> */}
                      <EverTg.Heading1>
                        {drsData && drsData.payout_status.toUpperCase()}
                      </EverTg.Heading1>
                    </div>
                    <EverTg.Text>{`${drsData && drsData.period}`}</EverTg.Text>
                  </Col>
                </Row>
              </EverLoader>
            </div>
          </div>
        </EverCard>
      </Col>
    </Row>
  );
});
export default QueriesAndApprovals;

const DisplayComponent = observer((props) => {
  const { text, value, linkTo, type, iconContainerClass, icon } = props;
  const navigate = useNavigate();
  return (
    <div className="flex ">
      <div className=" flex items-center w-52">
        <div className={iconContainerClass}>{icon}</div>
        <EverTg.Text className="justify-end">{text}</EverTg.Text>
      </div>
      <div className="w-24 flex items-center text-left">
        <EverBadge
          type={type}
          outline={false}
          onClick={() => {
            navigate(linkTo);
          }}
          title={value}
          className={"!cursor-pointer"}
        />
      </div>
    </div>
  );
});
