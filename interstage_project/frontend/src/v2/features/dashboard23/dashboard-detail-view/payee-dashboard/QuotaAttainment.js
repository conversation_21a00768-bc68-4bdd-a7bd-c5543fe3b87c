import { useQuery, gql, useLazyQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { TargetIcon } from "@everstage/evericons/solid";
import { Row, Col, Layout, Empty } from "antd";
import { isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useState, Fragment, useRef } from "react";
// import { isEmpty } from "lodash";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { QUOTA_CATEGORIES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  sortPrimaryQuota,
  percentageOneDecimalPlace,
} from "~/Utils/QuotaUtils";
import {
  useCurrentTheme,
  EverButton,
  EverButtonGroup,
  EverTg,
  EverSelect,
  EverLoader,
  LazySelect,
  filterAndFormatOptions,
  useAbortiveLazyQuery,
} from "~/v2/components";
import {
  MANAGERS,
  GET_EMPLOYEE,
  getTeamOption,
} from "~/v2/features/dashboard23/dashboard-detail-view/payee-dashboard";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyQuotaAttainment } from "~/v2/images";

import { AnimatedNumber } from "../AnimatedNumber";

const { Content } = Layout;

const QUOTA_ATTAINMENT = gql`
  query PayeeCurrentQuotaAttainment($payeeId: String!) {
    payeeCurrentQuotaAttainment(payeeId: $payeeId)
    allQuotaCategories {
      quotaCategoryName
      displayName
    }
  }
`;

const QuotaAttainment = observer((props) => {
  const { primary, base, chartColors } = useCurrentTheme();
  const currentChartColors = Object.values(chartColors || {});
  const { store } = props;
  const { email: loggedInUser } = useAuthStore();
  const { hasReportees } = store;
  const [selectedTeamId, setSelectedTeamId] = useState(loggedInUser);
  const [quotaAttainment, setQuotaAttainment] = useState();
  const [tabPaneText, setTabPaneText] = useState([]);
  const [period, setPeriod] = useState("");
  const [quota, setQuota] = useState();
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [isDataConfigured, setIsDataConfigured] = useState(false);
  const initialLoad = useRef(true);
  const [selectedOption, setSelectedOption] = useState({});
  const [initialOptions, setInitialOptions] = useState([]);

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { loading, data } = useQuery(QUOTA_ATTAINMENT, {
    variables: {
      payeeId: selectedTeamId,
    },
    fetchPolicy: selectedTeamId ? "network-only" : "cache-only",
  });

  const { t } = useTranslation();

  useEffect(() => {
    let dataConfigured = false;
    if (data && data.payeeCurrentQuotaAttainment) {
      let qa = hideCategoryOptions(
        JSON.parse(data.payeeCurrentQuotaAttainment)
      );
      const quotaMap = {};
      data?.allQuotaCategories?.forEach((cat) => {
        quotaMap[cat.quotaCategoryName] = cat.displayName;
      });
      if (!("error" in qa)) {
        if (Object.keys(qa).length > 0) {
          let quotaOpts = [];
          Object.keys(qa).map((cat) => {
            quotaOpts.push({
              label:
                cat === QUOTA_CATEGORIES.PRIMARY_QUOTA
                  ? t("PRIMARY_QUOTA")
                  : quotaMap[cat],
              value: cat,
            });
          });
          const modifiedOptions = sortPrimaryQuota(quotaOpts);
          setQuotaOptions(modifiedOptions);
          setQuota(modifiedOptions?.[0]?.value);
        }
        dataConfigured = true;
      }
    }
    setIsDataConfigured(dataConfigured);
  }, [data]);

  useEffect(() => {
    if (data && data.payeeCurrentQuotaAttainment && quota) {
      let qa = JSON.parse(data.payeeCurrentQuotaAttainment);
      if (!("error" in qa) && quota && quota in qa) {
        setQuotaAttainment(qa[quota]);
        setTabPaneText(Object.keys(qa[quota]));
      } else {
        setQuotaAttainment();
        setTabPaneText([]);
      }
    }
  }, [data, quota]);

  useEffect(() => {
    if (tabPaneText.length != 0) {
      setPeriod(tabPaneText[1]);
    }
  }, [tabPaneText]);

  const hideCategoryOptions = (quotaOptions) => {
    if (!("error" in quotaOptions)) {
      const hideCategories = clientFeatures?.hideCategories || [];
      const options = { ...quotaOptions };
      hideCategories.forEach((category) => {
        delete options[category];
      });
      return options;
    } else {
      return quotaOptions;
    }
  };

  const [getEmployee, { loading: employeeLoading, variables: empVariables }] =
    useLazyQuery(GET_EMPLOYEE, {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        setSelectedTeamId(empVariables.emailId);
        setSelectedOption(getTeamOption(data.employeeNameDetail, loggedInUser));
      },
    });

  const [getManager, { variables, abort }] = useAbortiveLazyQuery(MANAGERS, {
    fetchPolicy: "network-only",
    onCompleted: (data) => {
      const headers = data?.managersWithLimit?.headers || [];
      const fullNameIndex = headers.indexOf("reportingManagerFullName") ?? null;
      const emailIndex = headers.indexOf("employeeEmailId") ?? null;
      let response = data?.managersWithLimit?.data || [];
      if (!isNil(response)) {
        response = filterAndFormatOptions(response, {
          label: (option) =>
            option[emailIndex] === loggedInUser
              ? "Mine"
              : `${option[fullNameIndex]} & Team`,
          value: emailIndex,
          key: fullNameIndex,
        });
        if (initialLoad.current) {
          setInitialOptions(response);
          initialLoad.current = false;
        }
        variables.successCbk(response);
      } else {
        variables.failureCbk();
      }
    },
    onError: () => {
      variables.failureCbk();
    },
  });

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { page, searchTerm, options, limit, successCbk, failureCbk } =
        params;
      await getManager({
        limitValue: limit,
        ...(page > 0 &&
          options.length > 0 && {
            offsetFullName: options[options.length - 1].key.split("##::##")[0],
            offsetEmail: options[options.length - 1].value,
          }),
        ...(searchTerm && { searchTerm }),
        successCbk,
        failureCbk,
      });
    },
  };

  useEffect(() => {
    if (isEmpty(selectedTeamId) && loggedInUser !== null) {
      if (loggedInUser) {
        getEmployee({
          variables: {
            emailId: loggedInUser,
          },
        });
      } else if (initialOptions.length) {
        setSelectedTeamId(initialOptions[0].value);
        setSelectedOption(initialOptions[0]);
      }
    }
  }, [selectedTeamId, loggedInUser, initialOptions]);

  const onHandleChange = (value, option) => {
    setSelectedOption(option);
    setSelectedTeamId(value);
  };

  const showSkeleton = loading || isEmpty(selectedTeamId) || employeeLoading;

  if (showSkeleton) {
    return <WidgetLoading />;
  }

  return (
    <Fragment>
      <div className="flex w-full mb-4">
        <div className="flex w-70 items-center">
          <EverTg.Heading3 className=" text-ever-base-content">
            {t("QUOTA_ATTAINMENT")}
          </EverTg.Heading3>
        </div>
      </div>
      <Row className="mb-8">
        {hasReportees && (
          <LazySelect
            className="w-2/4 pr-1 h-full"
            placeholder="Select Team"
            bordered={true}
            value={selectedTeamId || null}
            onChange={onHandleChange}
            loading={isEmpty(selectedTeamId) || employeeLoading}
            // custom props
            selectedOption={selectedOption}
            {...lazyLoadProps}
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            disabled={loading}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
          />
        )}

        <EverSelect
          placeholder={
            quotaOptions.length !== 0 || showSkeleton
              ? t("SELECT_QUOTA")
              : t("QUOTA_UNAVAILABLE")
          }
          bordered={true}
          onChange={(value) => setQuota(value)}
          value={quota}
          className={`dashboard-select h-full w-2/4 ${hasReportees && "pl-1"}`}
          options={quotaOptions}
          {...(showSkeleton
            ? { loading: true, disabled: true }
            : { suffixIcon: <ChevronDownIcon className="h-5 w-5" /> })}
          size="small"
          dropdownMatchSelectWidth={false}
          getPopupContainer={({ parentNode }) => parentNode}
          dropdownAlign={{ offset: [0, 6] }}
        ></EverSelect>
      </Row>

      <EverLoader
        indicatorType="spinner"
        spinning={showSkeleton}
        className="!static"
        wrapperClassName="rounded-xl"
      >
        <>
          {isDataConfigured ? (
            <Row>
              <Col span={24}>
                {tabPaneText.length > 1 ? (
                  <QuotaAttainmentSection
                    quotaAttainment={quotaAttainment && quotaAttainment[period]}
                    currentChartColors={currentChartColors}
                    base={base}
                    primary={primary}
                  />
                ) : (
                  <Empty
                    className="block mx-auto mt-[20%]"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Col>
            </Row>
          ) : (
            <div className="mt-5 w-[80%] mx-auto">
              <img
                src={emptyQuotaAttainment}
                alt="empty-quota-attainment"
                className="w-full"
              />
              <div className="!mt-5 text-center w-[80%] m-auto">
                <EverTg.Text className=" text-ever-base-content-mid">
                  {t("MONITOR_YOUR_QUOTA")}
                </EverTg.Text>
              </div>
            </div>
          )}
          {tabPaneText.length > 1 && (
            <div className="flex justify-center">
              <div className="flex">
                <EverButtonGroup
                  className="bg-ever-base-200"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={
                    (period && tabPaneText.indexOf(period)) || 0
                  }
                  size="small"
                  activeButtonClassname="shadow-sm"
                >
                  {tabPaneText.map((period, index) => (
                    <EverButton
                      key={`tabPan-button-${index}`}
                      onClick={() => {
                        setPeriod(period);
                      }}
                    >
                      {period}
                    </EverButton>
                  ))}
                </EverButtonGroup>
              </div>
            </div>
          )}
        </>
      </EverLoader>
    </Fragment>
  );
});
export default QuotaAttainment;

const QuotaAttainmentSection = observer((props) => {
  const { quotaAttainment, currentChartColors, base, primary } = props;
  return (
    <Layout className="bg-ever-base">
      <Content className="bg-ever-base">
        <div className="items-center !my-2">
          <div className="gauge-chart-value  w-2/4 mb-2">
            <AnimatedNumber
              handleRoundOff={(value) => {
                if (value === null) return null;
                if (value === 0) return 0;
                return percentageOneDecimalPlace(value);
              }}
              key={quotaAttainment}
              number={percentageOneDecimalPlace(quotaAttainment)}
              description={"Attainment"}
              iconClassName={`bg-ever-chartColors-2`}
              isPercentage
              append={"%"}
              icon={<TargetIcon className="text-ever-chartColors-12 w-6 h-6" />}
            />
            {/* <EverTg.Heading2 className="text-ever-primary">
              {!quotaAttainment
                ? 0
                : percentageOneDecimalPlace(quotaAttainment)}{" "}
              %
            </EverTg.Heading2> */}
          </div>
          <ReactFusioncharts
            key={quotaAttainment}
            type="angulargauge"
            width="100%"
            height="300"
            dataFormat="JSON"
            dataUpdated={base?.DEFAULT}
            dataSource={getDataSource(
              quotaAttainment,
              currentChartColors,
              base,
              primary
            )}
          />
        </div>
      </Content>
    </Layout>
  );
});

const getDataSource = (val, currentChartColors, base, primary) => {
  let dataSource = {
    chart: {
      lowerlimit: "0",
      upperlimit: "160",
      showvalue: "1",
      numbersuffix: " %",
      theme: "fusion",
      showtooltip: "0",
      baseFontSize: 12,
      showShadow: "0",
      baseFont: "Inter",
      baseFontColor: base.content.mid,
      valueFont: "Inter",
      bgColor: base?.DEFAULT,
    },
    colorrange: {
      color: [
        {
          minvalue: "0",
          maxvalue: val,
          code: currentChartColors[0],
        },
        {
          minvalue: val,
          maxvalue: "160",
          code: primary?.lite?.DEFAULT,
        },
      ],
    },
    dials: {
      dial: [
        {
          value: val,
          bgColor: `${base?.content?.DEFAULT}`,
        },
      ],
    },
  };
  return dataSource;
};
