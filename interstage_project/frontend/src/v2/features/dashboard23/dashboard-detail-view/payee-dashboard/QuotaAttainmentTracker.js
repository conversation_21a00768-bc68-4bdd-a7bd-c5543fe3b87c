import { useQuery, gql, useLazyQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Col, Empty, Layout, Row } from "antd";
import { cx } from "classix";
import { isEmpty, isNil, sortBy } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import ReactFusioncharts from "react-fusioncharts";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { QUOTA_CATEGORIES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { getClientFiscalYear, getUpToPeriodForYear } from "~/Utils/DateUtils";
import { sortPrimaryQuota } from "~/Utils/QuotaUtils";
import {
  useCurrentTheme,
  EverButton,
  EverButtonGroup,
  EverTg,
  EverDatePicker,
  EverSelect,
  LazySelect,
  filterAndFormatOptions,
  useAbortiveLazyQuery,
  EverLoader,
  EverBadge,
} from "~/v2/components";
import FusionTooltip from "~/v2/features/dashboard23/dashboard-detail-view/FusionTooltip";
import {
  MANAGERS,
  GET_EMPLOYEE,
  getTeamOption,
} from "~/v2/features/dashboard23/dashboard-detail-view/payee-dashboard";
import { useFusionTooltip } from "~/v2/features/dashboard23/dashboard-detail-view/useFusionTooltip";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";
import { emptyQuotaAttainmentTracker } from "~/v2/images";

import CompareIndividuals from "./CompareIndividuals";

const { Content } = Layout;

const PAYEE_QA_TRACKER = gql`
  query PayeeQuotaAttainmentTracker($payeeId: String!, $year: String!) {
    payeeQuotaAttainmentTracker(payeeId: $payeeId, year: $year)
    allQuotaCategories {
      quotaCategoryName
      displayName
    }
  }
`;

const QA_TRACKER = gql`
  query QuotaAttainmentsForTeam($teamOwnerId: String!, $year: String!) {
    quotaAttainmentsForTeam(teamOwnerId: $teamOwnerId, year: $year) {
      quotas
      payeesInfo
      displayNames {
        label
        value
      }
    }
  }
`;

const QA_TRACKER_INDV = gql`
  query QuotaAttainmentForPayeeAndTeams(
    $teamOwnerIds: [String]!
    $payeeIds: [String]!
    $year: String!
    $quotaCategory: String!
  ) {
    quotaAttainmentForPayeeAndTeams(
      teamOwnerIds: $teamOwnerIds
      payeeIds: $payeeIds
      year: $year
      quotaCategory: $quotaCategory
    ) {
      quotas
      payeesInfo
      displayNames {
        label
        value
      }
    }
  }
`;

// const toolTipDom = (tableData, hasReportees, selectedChartMonth, isPercent) => {
//   if (hasReportees) {
//     return `<table width="240" style="text-align: center;">
//         <thead>
//           <tr>
//             <td colSpan="2" className="text-ever-base-content" style="padding: 10px;font-size: 14px;">
//               <b>${`Quota Attainment for $label${
//                 tableData?.length > 10 ? " (Top 10)" : ""
//               }`}</b>
//             </td>
//           </tr>
//         </thead>
//         <tbody>
//           ${
//             tableData !== undefined &&
//             tableData.slice(0, 10).map(
//               (data) => `<tr>
//               <td style="padding: 10px; font-size: 14px;">${data.payee}</td>
//               <td style="padding: 10px; font-size: 14px;">${
//                 isPercent ? data.qa : data.cum_qe
//               }${isPercent ? "%" : ""}</td>
//             </tr>`
//             )
//           }
//         </tbody>
//       </table>`;
//   } else {
//     if (selectedChartMonth) {
//       return ``;
//     } else {
//       return "$label";
//     }
//   }
// };

const ToolTipComponent = ({ tableData, isPercent, t, tooltipLabel }) => {
  const parseDate = moment(tooltipLabel, "MMM");
  const formattedDate = parseDate.format("MMMM");
  const isValidDate = parseDate.isValid();
  return (
    <table width="296" className="text-center">
      <thead>
        <tr>
          <td
            colSpan="2"
            className="text-ever-base-content text-md font-semibold text-left px-6 py-4 h-16"
          >
            <b>
              {t("QUOTA_ATTAINMENT")} for{" "}
              {isValidDate ? formattedDate : tooltipLabel}{" "}
              {tableData?.length > 10 ? " (Top 10)" : ""}
            </b>
          </td>
        </tr>
      </thead>
      <tbody className="pb-2">
        {tableData !== undefined
          ? tableData.slice(0, 10).map((data) => (
              <tr key={data.id}>
                <td className="text-left text-md px-6 py-4 h-10">
                  {data.payee}
                </td>
                <td className="text-left text-md px-6 py-4 h-10">
                  {isPercent ? data.qa : data.cum_qe}
                  {isPercent ? "%" : ""}
                </td>
              </tr>
            ))
          : null}
      </tbody>
    </table>
  );
};

const QuotaAttainmentTracker = observer((props) => {
  const { chartColors, base, primary } = useCurrentTheme();

  const { store, onLoading } = props;
  const { email: loggedInUser } = useAuthStore();
  const themeColor = Object.values(chartColors || {});
  const baseFontColor = base?.content?.mid;
  const { hasReportees, fontFamily } = store;

  const [fiscalPeriods, setFiscalPeriods] = useState();
  const [switchToIndv, setSwitchToIndv] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState(loggedInUser);
  const [selectedChartMonth, setSelectedChartMonth] = useState(null);
  const [qaTrackerData, setQaTrackerData] = useState({});
  const [teamTrData, setTeamTrData] = useState({});
  const [tableDS, setTableDS] = useState();

  const [indvSelectedTeams, setIndvSelectedTeams] = useState();
  const [indvSelectedPayees, setIndvSelectedPayees] = useState();
  const [indvCategory, setIndvCategory] = useState();
  const [indvDataSet, setIndvDataSet] = useState([]);
  const [periodLength, setPeriodLength] = useState();
  const [quota, setQuota] = useState();
  const [year, setYear] = useState();
  const [quotaOptions, setQuotaOptions] = useState([]);
  const [chartMessage, setChartMessage] = useState("No data to display");
  const [individualQuota, setIndividualQuota] = useState();
  const [selectOpen, setSelectOpen] = useState(false);
  const [ytd, setYtd] = useState(0);
  const [isQtd, setIsQtd] = useState(false);
  const [qaQtd, setQaQtd] = useState(false);
  const [isPercent, setIsPercent] = useState(true);
  const [selectedOption, setSelectedOption] = useState({});
  const [initialOptions, setInitialOptions] = useState([]);
  const [reporteesLoaded, setReporteesLoaded] = useState(false);
  const [quotaLoaded, setQuotaLoaded] = useState(false);
  const [showChart, setShowChart] = useState(true);

  const fiscalYearMonth = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const initialLoad = useRef(true);
  const { t } = useTranslation();
  const location = useLocation();

  const {
    tooltipRef,
    showTooltip,
    tooltipContent,
    tooltipLabel,
    show,
    hide,
    updateTooltipPosition,
  } = useFusionTooltip();

  const handleMouseMove = (evt) => {
    const { clientX, clientY } = evt;
    updateTooltipPosition(clientX, clientY);
  };

  const [getEmployee, { loading: employeeLoading, variables: empVariables }] =
    useLazyQuery(GET_EMPLOYEE, {
      fetchPolicy: "network-only",
      onCompleted: (data) => {
        setSelectedTeam(empVariables.emailId);
        setSelectedOption(getTeamOption(data.employeeNameDetail, loggedInUser));
      },
    });

  const [getManager, { variables, abort }] = useAbortiveLazyQuery(MANAGERS, {
    fetchPolicy: "network-only",
    onCompleted: (data) => {
      setReporteesLoaded(true);
      const headers = data?.managersWithLimit?.headers || [];
      const fullNameIndex = headers.indexOf("reportingManagerFullName") ?? null;
      const emailIndex = headers.indexOf("employeeEmailId") ?? null;
      let response = data?.managersWithLimit?.data || [];
      if (!isNil(response)) {
        response = filterAndFormatOptions(response, {
          label: (option) =>
            option[emailIndex] === loggedInUser
              ? "Mine"
              : `${option[fullNameIndex]} & Team`,
          value: emailIndex,
          key: fullNameIndex,
        });
        if (initialLoad.current) {
          setInitialOptions(response);
          initialLoad.current = false;
        }
        variables.successCbk(response);
      } else {
        variables.failureCbk();
      }
    },
    onError: () => {
      setReporteesLoaded(true);
      variables.failureCbk();
    },
  });

  const lazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { page, searchTerm, options, limit, successCbk, failureCbk } =
        params;
      await getManager({
        limitValue: limit,
        ...(page > 0 &&
          options.length > 0 && {
            offsetFullName: options[options.length - 1].key.split("##::##")[0],
            offsetEmail: options[options.length - 1].value,
          }),
        ...(searchTerm && { searchTerm }),
        successCbk,
        failureCbk,
      });
    },
  };

  const refreshChart = () => {
    /* A workaround for fusioncharts to re-render */
    setTimeout(() => setShowChart(true), 1000); //1s for same route change
  };

  useEffect(() => {
    if (location.key) {
      setShowChart(false);
    }
  }, [location.key]);

  useEffect(() => {
    setShowChart(false);
  }, [isQtd]);

  useEffect(() => {
    if (!showChart) {
      refreshChart();
    }
  }, [showChart]);

  useEffect(() => {
    if (!switchToIndv && isEmpty(selectedTeam) && loggedInUser !== null) {
      if (loggedInUser) {
        getEmployee({
          variables: {
            emailId: loggedInUser,
          },
        });
      } else if (initialOptions.length) {
        setSelectedTeam(initialOptions[0].value);
        setSelectedOption(initialOptions[0]);
      }
    }
  }, [selectedTeam, loggedInUser, initialOptions]);

  const { loading, data: trackerData } = useQuery(PAYEE_QA_TRACKER, {
    variables: {
      payeeId: selectedTeam,
      year: year && String(year.year()),
    },
    fetchPolicy:
      hasReportees === false && selectedTeam && year
        ? "network-only"
        : "cache-only",
  });

  useQuery(QA_TRACKER, {
    variables: {
      teamOwnerId: selectedTeam,
      year: year && String(year.year()),
    },
    fetchPolicy:
      hasReportees === true && selectedTeam && year
        ? "network-only"
        : "cache-only",
    onCompleted: (data) => {
      setTeamTrData(data);
    },
    onError: (error) => {
      console.error(error);
      setTeamTrData({});
    },
  });

  const { loading: iLoading, data: iData } = useQuery(QA_TRACKER_INDV, {
    variables: {
      teamOwnerIds: indvSelectedTeams,
      payeeIds: indvSelectedPayees,
      year: year && String(year.year()),
      quotaCategory: individualQuota,
    },
    fetchPolicy:
      (!isEmpty(indvSelectedTeams) || !isEmpty(indvSelectedPayees)) &&
      year &&
      individualQuota
        ? "network-only"
        : "cache-only",
  });

  useEffect(() => {
    let fiscalPeriods = [];
    if (periodLength === 12) {
      let monthArr = moment.monthsShort();
      fiscalPeriods = monthArr
        .slice(fiscalYearMonth, monthArr.length)
        .concat(monthArr.slice(0, fiscalYearMonth));
    } else if (periodLength === 4) {
      fiscalPeriods = ["Q1", "Q2", "Q3", "Q4"];
    } else if (periodLength === 2) {
      fiscalPeriods = ["H1", "H2"];
    } else if (periodLength === 1) {
      fiscalPeriods = ["Annual"];
      setFiscalPeriods(["Annual"]);
    } else {
      fiscalPeriods = null;
    }
    if (year && year.year() == getClientFiscalYear(fiscalYearMonth).year()) {
      let upToMonth = getUpToPeriodForYear(fiscalYearMonth, periodLength);
      fiscalPeriods = fiscalPeriods && fiscalPeriods.slice(0, upToMonth);
    }
    setFiscalPeriods(fiscalPeriods);
  }, [fiscalYearMonth, periodLength, year]);

  useEffect(() => {
    if (fiscalYearMonth != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonth));
    }
  }, [fiscalYearMonth]);

  useEffect(() => {
    setSelectedTeam(loggedInUser);
  }, [loggedInUser]);

  useEffect(() => {
    let allData = {};
    let systemNameToDisplayName = {};
    if (switchToIndv && iData?.quotaAttainmentForPayeeAndTeams?.quotas) {
      allData = JSON.parse(iData.quotaAttainmentForPayeeAndTeams.quotas);

      iData.quotaAttainmentForPayeeAndTeams.displayNames.forEach((item) => {
        systemNameToDisplayName[item.value] = item.label;
      });
    } else if (
      !hasReportees &&
      trackerData &&
      trackerData.payeeQuotaAttainmentTracker
    ) {
      allData = hideCategoryOptions(
        JSON.parse(trackerData.payeeQuotaAttainmentTracker)
      );
      trackerData.allQuotaCategories.forEach((item) => {
        systemNameToDisplayName[item.quotaCategoryName] = item.displayName;
      });
    } else if (teamTrData?.quotaAttainmentsForTeam?.quotas) {
      allData = hideCategoryOptions(
        JSON.parse(teamTrData.quotaAttainmentsForTeam.quotas)
      );

      teamTrData.quotaAttainmentsForTeam.displayNames.forEach((item) => {
        systemNameToDisplayName[item.value] = item.label;
      });
    }
    if (!("error" in allData)) {
      if (Object.keys(allData).length > 0) {
        let quotaOpts = [];
        Object.keys(allData).map((cat) => {
          quotaOpts.push({
            label:
              cat === QUOTA_CATEGORIES.PRIMARY_QUOTA
                ? t("PRIMARY_QUOTA")
                : systemNameToDisplayName[cat],
            value: cat,
          });
        });
        const modifiedOptions = sortPrimaryQuota(quotaOpts);
        setQuotaOptions(modifiedOptions);
        setQuota(modifiedOptions?.[0]?.value);
      }
    }
    if (!isEmpty(iData) || !isEmpty(trackerData) || !isEmpty(teamTrData)) {
      setQuotaLoaded(true);
    }
  }, [trackerData, hasReportees, teamTrData, iData]);

  useEffect(() => {
    if (trackerData && trackerData.payeeQuotaAttainmentTracker && quota) {
      let allData = JSON.parse(trackerData.payeeQuotaAttainmentTracker);

      if (!("error" in allData) && quota in allData) {
        setChartMessage(null);
        let qaData = isQtd ? allData[quota].qa_qtd : allData[quota].qa;
        // let chartData = [];
        let chartObj = {
          categories: [
            {
              category: [],
            },
          ],
          dataset: [
            {
              color: themeColor[0],
              data: [],
            },
            {
              color: themeColor[51],
              data: [],
            },
          ],
        };
        setQaQtd(!isEmpty(allData[quota]?.qa_qtd));
        let qaCurrentPeriod = allData[quota].curr_period;
        setPeriodLength(Object.keys(qaData).length);

        fiscalPeriods &&
          fiscalPeriods.forEach((fm) => {
            let fiscalPeriodIndex = fiscalPeriods.indexOf(fm) + 1;
            let qa = qaData[fiscalPeriodIndex]
              ? isPercent
                ? qaData[fiscalPeriodIndex].qa
                : qaData[fiscalPeriodIndex].cum_qe
              : 0;

            chartObj.categories[0].category.push({ label: fm });
            const val = Number(qa);
            const threshold = isPercent ? 100 : qaData[fiscalPeriodIndex]?.qv;
            chartObj.dataset[0].data.push({
              value: String(val < threshold ? val : threshold),
            });
            chartObj.dataset[1].data.push({
              value: String(val < threshold ? 0 : val - threshold),
              showValue: val < threshold ? false : true,
            });
          });
        setQaTrackerData(chartObj);
        setSelectedChartMonth(qaCurrentPeriod.index + 1);
        setYtd(isPercent ? allData[quota]?.ytd : allData[quota]?.ytd_cum_qe);
      } else {
        setQaTrackerData([]);
        setChartMessage(allData["error"]);
        setYtd(0);
      }
    }
  }, [trackerData, fiscalPeriods, quota, isQtd, isPercent]);

  useEffect(() => {
    if (teamTrData?.quotaAttainmentsForTeam?.quotas && quota) {
      let allData = JSON.parse(teamTrData.quotaAttainmentsForTeam.quotas);
      if (!("error" in allData) && quota in allData) {
        let qaData = isQtd
          ? allData[quota].team_qa_qtd
          : allData[quota].team_qa;
        // let chartData = [];
        let chartObj = {
          categories: [
            {
              category: [],
            },
          ],
          dataset: [
            {
              color: themeColor[0],
              data: [],
            },
            {
              color: themeColor[51],
              data: [],
            },
          ],
        };

        let qaCurrentPeriod = allData[quota].curr_period;
        setPeriodLength(Object.keys(qaData).length);
        setQaQtd(!isEmpty(allData[quota]?.team_qa_qtd));
        fiscalPeriods &&
          fiscalPeriods.forEach((fm) => {
            let fiscalPeriodIndex = fiscalPeriods.indexOf(fm) + 1;
            let qa = qaData[fiscalPeriodIndex]
              ? isPercent
                ? qaData[fiscalPeriodIndex].team_qa
                : qaData[fiscalPeriodIndex].cum_qe
              : 0;

            chartObj.categories[0].category.push({ label: fm });
            const val = Number(qa);
            const threshold = isPercent ? 100 : qaData[fiscalPeriodIndex]?.qv;
            chartObj.dataset[0].data.push({
              value: String(val < threshold ? val : threshold),
            });
            chartObj.dataset[1].data.push({
              value: String(val < threshold ? 0 : val - threshold),
              showValue: val < threshold ? false : true,
            });
          });
        setQaTrackerData(chartObj);
        setSelectedChartMonth(qaCurrentPeriod.index + 1);
        setYtd(isPercent ? allData[quota]?.ytd : allData[quota]?.ytd_cum_qe);
      } else {
        setQaTrackerData([]);
        setYtd(0);
      }
    }
  }, [teamTrData, fiscalPeriods, quota, isQtd, isPercent]);

  useEffect(() => {
    if (iData && fiscalPeriods && quota) {
      let cat = fiscalPeriods.map((fm) => {
        return { label: fm };
      });
      setQaQtd(!isEmpty(iData.quotaAttainmentForPayeeAndTeams[quota]?.qa_qtd));
      setIndvCategory(cat);
      let iApiData = JSON.parse(
        iData.quotaAttainmentForPayeeAndTeams.quotas ?? "{}"
      );
      const payeesInfo = JSON.parse(
        iData.quotaAttainmentForPayeeAndTeams.payeesInfo
      );
      let ds = [];
      if (
        !("error" in iApiData) &&
        quota in iApiData &&
        !isEmpty(iApiData[quota].team_qa)
      ) {
        Object.entries(
          isQtd ? iApiData[quota].team_qa_qtd : iApiData[quota].team_qa
        ).forEach(([key, value]) => {
          setPeriodLength(Object.keys(value).length);
          ds.push({
            seriesname: `${payeesInfo[key]} & Team`,
            data: Object.values(value).map((x) => {
              return { value: x };
            }),
          });
        });
      }
      if (
        !("error" in iApiData) &&
        quota in iApiData &&
        !isEmpty(iApiData[quota].payee_qa)
      ) {
        let quotaData = isQtd
          ? isPercent
            ? iApiData[quota].payee_qa_qtd
            : iApiData[quota].payee_cum_qe_qtd
          : isPercent
          ? iApiData[quota].payee_qa
          : iApiData[quota].payee_cum_qe;
        Object.entries(quotaData).forEach(([key, value]) => {
          setPeriodLength(Object.keys(value).length);
          ds.push({
            seriesname: `${payeesInfo[key]}`,
            data: Object.values(value).map((x) => {
              return { value: x };
            }),
          });
        });
      }
      setIndvDataSet(ds);
      if (switchToIndv)
        setYtd(isPercent ? iApiData[quota]?.ytd : iApiData[quota]?.ytd_cum_qe);
    } else {
      setIndvDataSet([]);
      setYtd(0);
    }
  }, [iData, fiscalPeriods, quota, isQtd, isPercent]);

  useEffect(() => {
    if (selectedChartMonth) {
      if (hasReportees && teamTrData?.quotaAttainmentsForTeam?.quotas) {
        let allData = JSON.parse(teamTrData.quotaAttainmentsForTeam.quotas);
        const payeesInfo = JSON.parse(
          teamTrData.quotaAttainmentsForTeam.payeesInfo
        );
        if (!("error" in allData) && quota in allData) {
          let qaData = isQtd
            ? allData[quota].team_qa_qtd
            : allData[quota].team_qa;
          let reporteeList =
            qaData &&
            qaData[selectedChartMonth] &&
            qaData[selectedChartMonth].reportee_list;
          if (!isEmpty(reporteeList)) {
            let ds = [];
            reporteeList.forEach((r) => {
              ds.push({
                key: r.employee_email_id,
                payee: `${payeesInfo[r.employee_email_id]}`,
                qa: Number(r.qa).toFixed(2),
                cum_qe: Number(r.cum_qe).toFixed(2),
              });
            });
            setTableDS(
              sortBy(ds, [
                (o) => {
                  return isPercent ? parseFloat(o.qa) : parseFloat(o.cum_qe);
                },
              ]).reverse()
            );
          } else {
            setTableDS([]);
          }
        } else {
          setTableDS([]);
        }
      }
    }
  }, [selectedChartMonth, teamTrData, quota]);

  const hideCategoryOptions = (quotaOptions) => {
    if (!("error" in quotaOptions)) {
      const hideCategories = clientFeatures?.hideCategories || [];
      const options = { ...quotaOptions };
      hideCategories.forEach((category) => {
        delete options[category];
      });
      return options;
    } else {
      return quotaOptions;
    }
  };

  const dataSource = {
    chart: {
      paletteColors: themeColor,
      baseFontColor: baseFontColor,
      // usePlotGradientColor: 1,
      // plotGradientColor: "#F9FAFB",

      baseFont: fontFamily,
      valueFont: fontFamily,
      baseFontSize: 12,
      valueFontSize: 12,
      showShadow: 0,
      showValues: 1,
      showSum: 1,
      showHoverEffect: 0,
      showXAxisLine: 1,
      xAxisLineColor: base[300],
      canvasTopPadding: 20,
      formatNumberScale: 1,
      numberSuffix: isPercent ? " %" : "",
      divLineAlpha: 0,
      theme: "fusion",
      showToolTip: qaTrackerData && !isQtd && hasReportees ? 0 : 1,
      // plottooltext:
      //   qaTrackerData && !isQtd
      //     ? toolTipDom(
      //         tableDS,
      //         hasReportees,
      //         selectedChartMonth,
      //         isPercent
      //       ).replace(/,/g, "")
      //     : "",
      toolTipBgColor: base?.DEFAULT,
      toolTipColor: base?.content?.DEFAULT,
      toolTipBorderColor: base?.[300],
      //chartLeftMargin: 20,
      //chartRightMargin: 20,
      bgColor: base?.DEFAULT,
    },
    ...qaTrackerData,
    ...(isPercent && {
      trendlines: [
        {
          line: [{ startValue: 100, showOnTop: "1", dashed: "1" }],
        },
      ],
    }),
  };

  const indvDataSource = {
    chart: {
      showhovereffect: "1",
      formatNumberScale: 0,
      numbersuffix: isPercent ? " %" : "",
      drawcrossline: "1",
      plottooltext: "<b>$dataValue</b> - $seriesName",
      divLineColor: base?.DEFAULT,
      baseFontSize: 12,
      valueFontSize: 12,
      showXAxisLine: "1",
      showYAxisLine: "1",
      showLegend: "0",
      theme: "fusion",
      chartLeftMargin: 20,
      chartRightMargin: 20,
      toolTipBgColor: base?.content?.DEFAULT,
      toolTipColor: primary?.ring,
      toolTipBorderColor: base?.content?.DEFAULT,
      bgColor: base?.DEFAULT,
    },
    categories: [
      {
        category: indvCategory,
      },
    ],
    dataset: indvDataSet,
  };

  const onHandleChange = (value, option) => {
    setSelectedTeam(value);
    setSelectedChartMonth(null);
    setSwitchToIndv(false);
    setIsQtd(false);
    setIsPercent(true);
    setSelectedOption(option);
  };

  const compareIndividuals = (event, members) => {
    setSwitchToIndv(true);
    setSelectedTeam(null);
    setIndvSelectedTeams([]);
    setIndvSelectedPayees(members);
  };

  const selectTeamLoading =
    !switchToIndv && (isEmpty(selectedTeam) || employeeLoading);
  const isLoading =
    loading ||
    onLoading ||
    iLoading ||
    employeeLoading ||
    (!switchToIndv && isEmpty(selectedTeam)) ||
    (switchToIndv && isEmpty(indvSelectedPayees));
  const showSkeleton = (hasReportees && !reporteesLoaded) || !quotaLoaded;

  const renderCategory = () => {
    return (
      <>
        {switchToIndv ? (
          indvDataSet && indvCategory ? (
            <>
              {((!isEmpty(indvDataSource) && periodLength === 12 && qaQtd) ||
                isQtd) && (
                <>
                  <div>
                    <EverButtonGroup
                      className="bg-ever-base-200 mr-2"
                      activeBtnType="text"
                      activeBtnColor="primary"
                      defActiveBtnIndex={isQtd ? 1 : 0}
                      size="small"
                      activeButtonClassname="shadow-sm"
                    >
                      <EverButton
                        onClick={() => {
                          setIsQtd(false);
                        }}
                      >
                        Monthly
                      </EverButton>
                      <EverButton
                        onClick={() => {
                          setIsQtd(true);
                        }}
                      >
                        Quarterly
                      </EverButton>
                    </EverButtonGroup>
                  </div>
                </>
              )}
              {!isEmpty(indvDataSource) &&
                indvSelectedTeams &&
                indvSelectedTeams.length === 0 && (
                  <div className="mr-2">
                    <EverButtonGroup
                      className="bg-ever-base-200"
                      activeBtnType="text"
                      activeBtnColor="primary"
                      defActiveBtnIndex={isPercent ? 0 : 1}
                      size="small"
                      activeButtonClassname="shadow-sm"
                    >
                      <EverButton
                        onClick={() => {
                          setIsPercent(true);
                        }}
                      >
                        %
                      </EverButton>
                      <EverButton
                        onClick={() => {
                          setIsPercent(false);
                        }}
                      >
                        #
                      </EverButton>
                    </EverButtonGroup>
                  </div>
                )}
            </>
          ) : null
        ) : (
          <>
            {((!isEmpty(qaTrackerData) && periodLength === 12 && qaQtd) ||
              isQtd) && (
              <>
                <div>
                  <EverButtonGroup
                    className="bg-ever-base-200 mr-2"
                    activeBtnType="text"
                    activeBtnColor="primary"
                    defActiveBtnIndex={isQtd ? 1 : 0}
                    size="small"
                    activeButtonClassname="shadow-sm"
                  >
                    <EverButton
                      onClick={() => {
                        setIsQtd(false);
                      }}
                    >
                      Monthly
                    </EverButton>
                    <EverButton
                      onClick={() => {
                        setIsQtd(true);
                      }}
                    >
                      Quarterly
                    </EverButton>
                  </EverButtonGroup>
                </div>
              </>
            )}
            {!isEmpty(qaTrackerData) && (
              <div className="mr-2">
                <EverButtonGroup
                  className="bg-ever-base-200"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={isPercent ? 0 : 1}
                  size="small"
                  activeButtonClassname="shadow-sm"
                >
                  <EverButton
                    onClick={() => {
                      setIsPercent(true);
                    }}
                  >
                    %
                  </EverButton>
                  <EverButton
                    onClick={() => {
                      setIsPercent(false);
                    }}
                  >
                    #
                  </EverButton>
                </EverButtonGroup>
              </div>
            )}
          </>
        )}
      </>
    );
  };

  return (
    <>
      {showSkeleton && <WidgetLoading />}
      <div className={cx("payee-qa-tracker-card", showSkeleton && "hidden")}>
        <div className="flex mb-4">
          <EverTg.Heading3 className="text-ever-base-content">
            {t("QUOTA_ATTAINMENT_TRACKER")}
          </EverTg.Heading3>
        </div>
        <div className="flex gap-2 mb-8">
          {hasReportees && (
            <div className="flex w-60">
              <LazySelect
                placeholder="Select Team"
                className="dashboard-select border-left w-full h-full flex items-center"
                bordered={true}
                value={selectedTeam || null}
                size="small"
                onChange={onHandleChange}
                loading={selectTeamLoading}
                open={selectOpen}
                onDropdownVisibleChange={(openState) => {
                  setSelectOpen(openState);
                }}
                dropdownRender={(menu) => (
                  <div
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {menu}
                    <div
                      className="!px-2 !py-1 text-center h-auto flex-nowrap"
                      onClick={() => {
                        setSelectOpen(false);
                        setIsQtd(false);
                      }}
                    >
                      <CompareIndividuals
                        compareIndividuals={compareIndividuals}
                        year={year && year.year()}
                        quota={individualQuota}
                        setQuota={setIndividualQuota}
                      />
                    </div>
                  </div>
                )}
                disabled={loading || onLoading}
                // custom props
                selectedOption={selectedOption}
                {...lazyLoadProps}
                dropdownMatchSelectWidth={false}
                getPopupContainer={({ parentNode }) => parentNode}
              />
            </div>
          )}
          <div className="flex w-40">
            <EverSelect
              bordered={true}
              onChange={(value) => {
                setQuota(value);
                setIsQtd(false);
              }}
              {...(isLoading
                ? { loading: true, disabled: true }
                : { suffixIcon: <ChevronDownIcon className="h-5 w-5" /> })}
              value={quota}
              className="dashboard-select b w-full h-full flex items-center"
              suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
              placeholder={
                quotaOptions.length !== 0 || isLoading
                  ? t("SELECT_QUOTA")
                  : t("QUOTA_UNAVAILABLE")
              }
              options={quotaOptions}
              disabled={loading || onLoading}
              size="small"
              dropdownMatchSelectWidth={false}
              getPopupContainer={({ parentNode }) => parentNode}
              dropdownAlign={{ offset: [0, 6] }}
            ></EverSelect>
          </div>
          {renderCategory()}
          <div className="flex  w-28  items-center">
            <EverDatePicker.Legacy
              onChange={(date) => {
                setYear(date);
                setIsQtd(false);
              }}
              picker="year"
              bordered={true}
              value={year}
              allowClear={false}
              size="small"
              className="w-full h-full"
              disabled={loading || onLoading}
              getPopupContainer={({ parentNode }) => parentNode}
            />
          </div>
        </div>

        <EverLoader
          indicatorType="spinner"
          spinning={isLoading}
          className="!static"
          wrapperClassName="rounded-xl"
        >
          <>
            {switchToIndv ? (
              indvDataSet && indvCategory ? (
                <>
                  <Row>
                    <EverBadge
                      type={`success`}
                      icon={
                        <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                      }
                      title={`YTD: ${isPercent ? ytd : formatCurrency(ytd)}${
                        isPercent ? "%" : ""
                      }`}
                      className={"text-ever-success-lite-content"}
                    />
                    {/* <EverTg.Text className="text-ever-base-content mr-1">
                      YTD:
                    </EverTg.Text>
                    <EverTg.Text className="font-semibold text-ever-success-content-lite">
                      {isPercent ? ytd : formatCurrency(ytd)}
                      {isPercent ? "%" : ""}
                    </EverTg.Text> */}
                  </Row>
                  <Row>
                    <Col span={24}>
                      <ReactFusioncharts
                        type="msline"
                        width="100%"
                        height="280"
                        dataFormat="JSON"
                        dataUpdated={base?.DEFAULT}
                        dataSource={indvDataSource}
                      />
                    </Col>
                  </Row>
                </>
              ) : (
                <Empty
                  className="block mx-auto mt-[10%]"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )
            ) : !isEmpty(qaTrackerData) ? (
              <>
                <Row>
                  <EverBadge
                    type={`success`}
                    icon={
                      <div className="w-1.5 h-1.5 rounded-full bg-ever-success-lite-content"></div>
                    }
                    title={` YTD: ${isPercent ? ytd : formatCurrency(ytd)}${
                      isPercent ? "%" : ""
                    }`}
                    className={"text-ever-success-lite-content"}
                  />
                  {/* <EverTg.Text className="text-ever-base-content mr-1">
                    YTD:
                  </EverTg.Text>
                  <EverTg.Text className="font-semibold text-ever-success-content-lite">
                    {isPercent ? ytd : formatCurrency(ytd)}
                    {isPercent ? "%" : ""}
                  </EverTg.Text> */}
                </Row>
                <Row>
                  <Col span={24}>
                    <Layout className="bg-ever-base-default">
                      <Layout>
                        {showChart ? (
                          <Content className="bg-ever-base-default">
                            <ReactFusioncharts
                              type="stackedcolumn2d"
                              width="100%"
                              height="350"
                              dataFormat="json"
                              dataUpdated={base?.DEFAULT}
                              dataSource={dataSource}
                              events={{
                                dataplotRollOver: (eventObj, dataObj) => {
                                  show(eventObj, dataObj);
                                  document.addEventListener(
                                    "mousemove",
                                    handleMouseMove
                                  );
                                  setSelectedChartMonth(dataObj.dataIndex + 1);
                                },
                                dataplotRollOut: () => {
                                  hide();
                                  document.removeEventListener(
                                    "mousemove",
                                    handleMouseMove
                                  );
                                },
                              }}
                              dataEmptyMessage={chartMessage}
                            />
                            {qaTrackerData && !isQtd && hasReportees ? (
                              <FusionTooltip
                                ref={tooltipRef}
                                showTooltip={showTooltip}
                                tooltipContent={tooltipContent}
                              >
                                <ToolTipComponent
                                  tableData={tableDS}
                                  tooltipLabel={tooltipLabel}
                                  isPercent={isPercent}
                                  base={base}
                                  t={t}
                                />
                              </FusionTooltip>
                            ) : null}
                          </Content>
                        ) : null}
                      </Layout>
                    </Layout>
                  </Col>
                </Row>
              </>
            ) : (
              <div className="mt-5 w-[80%] mx-auto">
                <img
                  src={emptyQuotaAttainmentTracker}
                  alt="empty-quota-attainment-tracker"
                  className="w-full"
                />
                <div className="!mt-5 text-center w-[60%] m-auto">
                  <EverTg.Text className="text-ever-base-content-mid">
                    {t("TRACK_YOUR_QUOTA_ATTAINMENT")}
                  </EverTg.Text>
                </div>
              </div>
            )}
          </>
        </EverLoader>
      </div>
    </>
  );
});

export default QuotaAttainmentTracker;
