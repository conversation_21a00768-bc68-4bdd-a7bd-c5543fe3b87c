import { useQuery, gql } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { TargetIcon, CalendarIcon } from "@everstage/evericons/solid";
import { Row } from "antd";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { QUOTA_CATEGORIES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { sortPrimaryQuota } from "~/Utils/QuotaUtils";
import { EverSelect, EverLoader, EverTg, EverLabel } from "~/v2/components";
import WidgetLoading from "~/v2/features/dashboard23/dashboard-detail-view/WidgetLoading";

const QUOTA_ATTAINMENT = gql`
  query PayeeCurrentQuotaStructure($payeeId: String!) {
    payeeCurrentQuotaStructure(payeeId: $payeeId)
    allQuotaCategories {
      quotaCategoryName
      displayName
    }
  }
`;

const QuotaStructure = observer((props) => {
  const { email: loggedInUser } = useAuthStore();
  const { setShowQuota } = props;
  const [selectedTeam, setSelectedTeam] = useState();
  const [periodList, setPeriodList] = useState([]);
  const [period, setPeriod] = useState();
  const [quota, setQuota] = useState();
  const [quotaAmount, setQuotaAmount] = useState("NA");
  const [quotaPeriod, setQuotaPeriod] = useState("NA");
  const [quotaOptions, setQuotaOptions] = useState([]);
  const { t } = useTranslation();

  const { loading, data } = useQuery(QUOTA_ATTAINMENT, {
    variables: {
      payeeId: selectedTeam,
    },
    fetchPolicy: selectedTeam ? "network-only" : "cache-only",
  });
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  useEffect(() => {
    if (data && data.payeeCurrentQuotaStructure) {
      let qa = hideCategoryOptions(JSON.parse(data.payeeCurrentQuotaStructure));
      const systemNameToDisplayName = {};
      data.allQuotaCategories.forEach((item) => {
        systemNameToDisplayName[item.quotaCategoryName] = item.displayName;
      });
      if (!("error" in qa)) {
        if (Object.keys(qa).length > 0) {
          let quotaOpts = [];
          Object.keys(qa).map((cat) => {
            quotaOpts.push({
              label:
                cat === QUOTA_CATEGORIES.PRIMARY_QUOTA
                  ? t("PRIMARY_QUOTA")
                  : systemNameToDisplayName[cat],
              value: cat,
            });
          });
          const modifiedOptions = sortPrimaryQuota(quotaOpts);
          setQuotaOptions(modifiedOptions);
          setQuota(modifiedOptions?.[0]?.value);
        } else {
          setShowQuota(false);
        }
      } else {
        if (qa?.error == "Quota not available") setShowQuota(false);
      }
    }
  }, [data]);

  useEffect(() => {
    if (data && data.payeeCurrentQuotaStructure && quota) {
      let qa = JSON.parse(data.payeeCurrentQuotaStructure);
      if (!("error" in qa) && quota && quota in qa) {
        setPeriodList(Object.keys(qa[quota]));
        setPeriod(Object.keys(qa[quota])[0]);
      } else {
        setPeriodList([]);
      }
    }
  }, [data, quota]);

  useEffect(() => {
    setSelectedTeam(loggedInUser);
  }, [loggedInUser]);

  useEffect(() => {
    if (data && data.payeeCurrentQuotaStructure && quota) {
      let qa = JSON.parse(data.payeeCurrentQuotaStructure);
      const quotaDetail = qa[quota][period] || {};
      setQuotaPeriod(quotaDetail?.period || "NA");
      setQuotaAmount(quotaDetail?.amount?.ramped_quota || "NA");
    }
  }, [quota, period]);

  const hideCategoryOptions = (quotaOptions) => {
    if (!("error" in quotaOptions)) {
      const hideCategories = clientFeatures?.hideCategories || [];
      const options = { ...quotaOptions };
      hideCategories.forEach((category) => {
        delete options[category];
      });
      return options;
    } else {
      return quotaOptions;
    }
  };

  if (loading) {
    return <WidgetLoading />;
  }

  return (
    <>
      <div className="flex w-full mb-4">
        <EverTg.Heading3
          //style={{ fontSize: 16, fontWeight: 500, margin: "12px 0" }}
          className=" text-ever-base-content"
        >
          {t("QUOTA_STRUCTURE")}
        </EverTg.Heading3>
      </div>
      <Row>
        <div className="flex w-6/12 pr-1">
          <EverSelect
            defaultValue={quota}
            placeholder={t("NO_QUOTA_AVAILABLE")}
            bordered
            onChange={(value) => {
              setQuota(value);
            }}
            dropdownAlign={{ offset: [0, 6] }}
            value={quota}
            options={quotaOptions}
            className="dashboard-select h-full w-full "
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            disabled={loading}
            size="small"
            dropdownMatchSelectWidth={false}
            getPopupContainer={({ parentNode }) => parentNode}
          />
        </div>
        <div className="flex w-6/12 pl-1">
          <EverSelect
            className="dashboard-select h-full w-full"
            bordered
            defaultValue={period}
            suffixIcon={<ChevronDownIcon className="h-5 w-5" />}
            onChange={(value) => {
              setPeriod(value);
            }}
            value={period}
            disabled={loading}
            size="small"
            getPopupContainer={({ parentNode }) => parentNode}
            dropdownAlign={{ offset: [0, 6] }}
          >
            {periodList.map((period) => (
              <EverSelect.Option key={period} value={period}>
                {period}
              </EverSelect.Option>
            ))}
          </EverSelect>
        </div>
      </Row>

      <EverLoader
        indicatorType="spinner"
        spinning={loading}
        className="!static flex "
        wrapperClassName="rounded-xl"
      >
        <div className="mt-8">
          <div className="flex mb-6">
            <div className="pr-4 text-right flex">
              <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-42  mr-2">
                <CalendarIcon className="w-5 h-5 text-ever-chartColors-7" />
              </div>
              <EverLabel
                className={"justify-end text-ever-base-content text-lg"}
              >
                Period
              </EverLabel>
            </div>
            <div className="pl-4 flex items-center">
              {quotaPeriod !== "NA" ? (
                <EverTg.SubHeading3>{quotaPeriod}</EverTg.SubHeading3>
              ) : (
                <EverTg.SubHeading3>NA</EverTg.SubHeading3>
              )}
            </div>
          </div>
          <div className="flex">
            <div className=" pr-4 text-right flex">
              <div className="w-10 h-10 rounded-full flex items-center justify-center bg-ever-chartColors-12 bg-opacity-10  mr-2">
                <TargetIcon className="w-5 h-5 text-ever-chartColors-12" />
              </div>
              <EverLabel
                className={"justify-end text-ever-base-content text-lg"}
              >
                {t("QUOTA")}
              </EverLabel>
            </div>
            <div className=" pl-4 flex items-center">
              {quotaAmount !== "NA" ? (
                <EverTg.SubHeading3>
                  {formatCurrency(quotaAmount)}
                </EverTg.SubHeading3>
              ) : (
                <EverTg.SubHeading3>NA</EverTg.SubHeading3>
              )}
            </div>
          </div>
        </div>
      </EverLoader>
    </>
  );
});

export default QuotaStructure;
