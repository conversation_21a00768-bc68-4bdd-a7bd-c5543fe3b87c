import { AlertTriangleIcon } from "@everstage/evericons/solid";
import { Row, Col, Alert } from "antd";
import { observer } from "mobx-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverCard, EverTg } from "~/v2/components";
import { wavingHand } from "~/v2/images";

import CommissionBuddy from "./CommissionBuddy";
import CommissionPayouts from "./CommissionPayouts";
import CommissionPayoutsTracker from "./CommissionPayoutTracker";
import CommissionStructure from "./CommissionStructure";
import QueriesAndApprovals from "./QueriesAndApprovals";
import QuotaAttainment from "./QuotaAttainment";
import QuotaAttainmentTracker from "./QuotaAttainmentTracker";
import QuotaStructure from "./QuotaStructure";

const Render = observer((props) => {
  const [showDataConfigureElement, setShowDataConfigureElement] =
    useState(null);
  const [showQuota, setShowQuota] = useState(true);

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { hasPermissions } = useUserPermissionStore();
  const { store, onLoading, loggedInUserName } = props;

  const { t } = useTranslation();

  const showErrorBanner = () => {
    if (showDataConfigureElement === null) setShowDataConfigureElement(true);
  };

  const cardClasses =
    "relative h-full !bg-ever-base border border-solid border-ever-base-400 p-5 duration-300 ease-in-outshadow-sm rounded-xl";

  return (
    <>
      <div className="payee-dashboard">
        {loggedInUserName && (
          <div className="mb-5">
            <EverTg.Heading1 className="flex">
              {`Hi`}
              {`, ${loggedInUserName}!`}
              <img
                className="p-0 !my-0 !mr-0 !ml-1 "
                src={wavingHand}
                alt="waving hand"
              />
            </EverTg.Heading1>
          </div>
        )}
        {showDataConfigureElement && (
          <Alert
            className="rounded-md  border border-solid border-ever-warning bg-ever-warining-lite mb-4"
            type="warning"
            showIcon
            icon={<AlertTriangleIcon className="w-4 h-4 text-ever-warning" />}
            closable
            message={
              <EverTg.Caption className="text-ever-warning-lite-content font-medium">
                {t("DASHBOARD_EMPTY")}
              </EverTg.Caption>
            }
            onClose={() => setShowDataConfigureElement(false)}
          />
        )}

        <Row gutter={[20, 20]}>
          <Col span={8}>
            <CommissionPayouts store={store} />
          </Col>
          <Col span={16}>
            <EverCard
              outlined={false}
              className={`min-h-[350px] flex flex-col ${cardClasses}`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <CommissionPayoutsTracker
                store={store}
                showErrorBanner={showErrorBanner}
              />
            </EverCard>
          </Col>
        </Row>
        <Row gutter={[20, 20]}>
          {clientFeatures?.showCommissionBuddy ||
          clientFeatures?.showCommissionBuddy == undefined ? (
            <Col span={8}>
              <EverCard
                outlined={false}
                className={`min-h-[200px] flex flex-col ${cardClasses}`}
                bodyStyle={{ height: "100%", padding: 0 }}
                shadowSize="none"
              >
                <CommissionBuddy store={store} />
              </EverCard>
            </Col>
          ) : null}
          <Col span={8}>
            <EverCard
              outlined={false}
              className={`min-h-[200px] flex flex-col ${cardClasses}`}
              bodyStyle={{ height: "100%", padding: 0 }}
              shadowSize="none"
            >
              <CommissionStructure store={store} />
            </EverCard>
          </Col>

          {hasPermissions(RBAC_ROLES.VIEW_QUOTAS) && showQuota && (
            <Col span={8}>
              <EverCard
                outlined={false}
                className={`min-h-[200px] flex flex-col ${cardClasses} flex flex-col`}
                shadowSize="none"
              >
                <QuotaStructure store={store} setShowQuota={setShowQuota} />
              </EverCard>
            </Col>
          )}
        </Row>
        {hasPermissions(RBAC_ROLES.VIEW_QUOTAS) && showQuota && (
          <Row gutter={[20, 20]}>
            <Col span={8}>
              <EverCard
                outlined={false}
                className={`min-h-[200px] flex flex-col ${cardClasses}`}
                bodyStyle={{ height: "100%", padding: 0 }}
                shadowSize="none"
              >
                <QuotaAttainment store={store} />
              </EverCard>
            </Col>
            <Col span={16}>
              <EverCard
                outlined={false}
                className={`min-h-[200px] flex flex-col ${cardClasses}`}
                bodyStyle={{ height: "100%", padding: 0 }}
                shadowSize="none"
              >
                <QuotaAttainmentTracker store={store} onLoading={onLoading} />
              </EverCard>
            </Col>
          </Row>
        )}
        <Row gutter={[20, 20]}>
          <Col span={16}>
            <QueriesAndApprovals store={store} />
          </Col>
        </Row>
      </div>
    </>
  );
});
export default Render;
