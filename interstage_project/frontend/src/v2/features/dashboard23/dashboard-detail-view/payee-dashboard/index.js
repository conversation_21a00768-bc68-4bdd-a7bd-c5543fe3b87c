import { useQuery, gql } from "@apollo/client";
import { observer, useLocalStore } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFiscalYear } from "~/Utils/DateUtils";

import Render from "./Render";
import PayeeDashboardStore from "./store";

// import { RBAC_ROLES } from "~/Enums/index.js";

// import { Result } from "antd";
// import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";

const PAYEE_DETAILS = gql`
  query PayeeDashboard($payeeId: String!, $year: String!) {
    userRole(payeeId: $payeeId, year: $year)
    payeeCurrencySymbol(payeeId: $payeeId, year: $year)
  }
`;

export const MANAGERS = gql`
  query ManagersWithLimit(
    $limitValue: Int!
    $offsetFullName: String
    $offsetEmail: String
    $searchTerm: String
  ) {
    managersWithLimit(
      limitValue: $limitValue
      offsetFullName: $offsetFullName
      offsetEmail: $offsetEmail
      searchTerm: $searchTerm
    ) {
      headers
      data
    }
  }
`;

export const GET_EMPLOYEE = gql`
  query Employee($emailId: String!) {
    employeeNameDetail(emailId: $emailId) {
      employeeEmailId
      firstName
      lastName
    }
  }
`;

export const getTeamOption = (option, loggedInUser) => {
  const label = option.fullName
    ? option.fullName
    : `${option.firstName} ${option.lastName}`;

  return {
    value: option.employeeEmailId,
    label: option.employeeEmailId === loggedInUser ? "Mine" : `${label} & Team`,
    key: option.employeeEmailId,
  };
};

const PayeeDashboard = observer(() => {
  const { email: loggedInUser, name } = useAuthStore();
  const payeeDashboardStore = useLocalStore(() => new PayeeDashboardStore());
  const myClient = useRecoilValue(myClientAtom);
  const fiscalYearMonth = myClient.fiscalStartMonthZero;
  // const { hasPermissions } = useUserPermissionStore();
  const { setHasReportees, setCurrencySymbol } = payeeDashboardStore;

  const [year, setYear] = useState();

  const { loading, data } = useQuery(PAYEE_DETAILS, {
    variables: {
      payeeId: loggedInUser,
      year: year && String(year.year()),
    },
    fetchPolicy: loggedInUser && year ? "network-only" : "cache-only",
  });

  useEffect(() => {
    if (data) {
      setHasReportees(data.userRole === "Manager" ? true : false);
      setCurrencySymbol(data.payeeCurrencySymbol);
    }
  }, [data]);

  useEffect(() => {
    if (fiscalYearMonth != undefined) {
      setYear(getClientFiscalYear(fiscalYearMonth));
    }
  }, [fiscalYearMonth]);

  return (
    <Render
      store={payeeDashboardStore}
      loggedInUserName={name}
      onLoading={loading}
    />
  );
});

export default PayeeDashboard;
