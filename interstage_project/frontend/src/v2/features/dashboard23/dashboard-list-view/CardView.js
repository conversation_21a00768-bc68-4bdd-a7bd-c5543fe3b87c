import { useQuery } from "@apollo/client";
import { formatInTimeZone } from "date-fns-tz";
import PropTypes from "prop-types";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverTg,
  EverGroupAvatar,
  EverCard,
  EverTooltip,
} from "~/v2/components";
import { everstageLogoIcon } from "~/v2/images";

import { MoreOption } from "./MoreOption";
import { ShareOption } from "./ShareOption";
import { GET_PROFILE_DETAILS } from "../graphql";
import { dashboardsUrl } from "../utils";
/**
 *
 * @param {object} item - The details of the current dashboard.
 * @param {number} index - The index of the current dashboard.
 * @param {function} handleShareModalClick - The function to call to set whether the More menu is visible.
 * @param {function} getDashboardList - The function to call to get the list of dashboards.
 * @param {string} supersetHostUrl - The URL of the Superset server.
 * @returns {React.ReactNode} The JSX for the dashboard card.
 */

export function CardView({
  item,
  index,
  handleShareModalClick,
  getDashboardList,
  supersetHostUrl,
  refetchDashboardOpts,
}) {
  const navigate = useNavigate();
  const { hasPermissions } = useUserPermissionStore();
  const [userTimeZone, setUserTimeZone] = React.useState("UTC");
  const [moreVisible, setMoreVisible] = useState(false);

  const { data } = useQuery(GET_PROFILE_DETAILS, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (data?.payeeProfileDetails) {
      // get the timezone from payeeprofile data
      // convert the last modified time into the payee timezone.
      const profileData = JSON.parse(data.payeeProfileDetails);
      const { timezone } = profileData;
      if (timezone.includes("GMT")) {
        setUserTimeZone(timezone.split(" ")[1]);
      } else {
        setUserTimeZone(timezone);
      }
    }
  }, [data]);

  const lastUpdated = (lastUpdatedTime) => {
    // checking whether the last updated time and current date are the same or not.
    //  if yes, then return the value as today; otherwise, return the value in date and time format.
    const currentDate = new Date();
    if (
      formatInTimeZone(lastUpdatedTime, userTimeZone, "dd-MM-yyyy") ===
      formatInTimeZone(currentDate, userTimeZone, "dd-MM-yyyy")
    ) {
      return "today";
    }
    return formatInTimeZone(
      item.lastModified,
      userTimeZone,
      `${"MMM d, yyyy"} 'at' ${"h:mm a"}`
    );
  };

  const cardTitle = (item) => {
    return (
      <div className="h-11 flex justify-between">
        <div className="grid">
          <EverTooltip placement="topLeft" title={item.name}>
            <div className="h-6 mb-1 text-ellipsis overflow-hidden whitespace-nowrap">
              <EverTg.SubHeading3 className="text-ever-base-content">
                {item.name}
              </EverTg.SubHeading3>
            </div>
          </EverTooltip>
          {!item.isDefault && (
            <div className="h-4">
              <EverTg.Caption className="text-ever-base-content-mid">
                {item.lastModified && (
                  <>
                    <span className="mr-1">Last updated</span>
                    {lastUpdated(item.lastModified)}
                  </>
                )}
              </EverTg.Caption>
            </div>
          )}
        </div>
        {!item.isDefault && (
          <div className="flex items-center">
            <ShareOption
              item={item}
              handleShareModalClick={handleShareModalClick}
            />
            <MoreOption
              dashboardDetail={item}
              setMoreVisible={setMoreVisible}
              getDashboardList={getDashboardList}
              supersetHostUrl={supersetHostUrl}
              moreVisible={moreVisible}
              refetchDashboardOpts={refetchDashboardOpts}
            />
          </div>
        )}
      </div>
    );
  };

  const renderSharedUserList = (dashboardType, users) => {
    // checking whether the user has the permission to manage the dashboard or not.
    if (
      dashboardType === "superset_dashboard" &&
      hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS)
    ) {
      const formattedUsers = users.map((user) => ({
        firstName: user.firstName,
        lastName: user.lastName,
        image: user.profilePicture,
      }));
      return (
        <EverGroupAvatar
          avatars={formattedUsers}
          groupMaxCount={2}
          limitInPopover={10}
        />
      );
    }
    return null;
  };

  return (
    <EverCard
      key={`${item.name}-${index}`}
      className={`h-40 flex flex-col cursor-pointer ${
        item.isDefault
          ? "bg-gradient-to-r from-ever-chartColors-8/50 via-ever-chartColors-8/20 to-ever-chartColors-8/50 border-ever-chartColors-8"
          : "bg-ever-base"
      }`}
      interactive
      onClick={() => {
        navigate(dashboardsUrl(item.dashboardId, item.dashboardType));
      }}
    >
      {(item.isDefault ||
        item.createdBy?.firstName !== undefined ||
        item?.createdBy?.lastName !== undefined) && (
        <>
          {cardTitle(item)}
          <div className="flex items-end h-full w-full justify-between">
            <div className={twMerge("flex", item.isDefault && "flex-col")}>
              <div className={"h-8 items-center flex leading-[18px] mr-1"}>
                <EverTg.Caption className={"text-ever-base-content-mid"}>
                  Created by
                </EverTg.Caption>
              </div>
              <div className={"flex h-8 gap-2 leading-[20px] items-center"}>
                {item.isDefault ? (
                  <div className="w-8 flex items-center justify-center h-8 rounded bg-ever-base-25">
                    <EverGroupAvatar
                      avatars={[
                        {
                          image: everstageLogoIcon,
                          className: "w-7 h-7",
                          name: "Everstage",
                        },
                      ]}
                    />
                  </div>
                ) : null}
                {item.isDefault ? (
                  <EverTg.SubHeading4 className={"text-ever-base-content"}>
                    Everstage
                  </EverTg.SubHeading4>
                ) : (
                  <EverTg.Caption className={"text-ever-base-content-mid"}>
                    {`${item.createdBy?.firstName} ${item?.createdBy?.lastName}`}
                  </EverTg.Caption>
                )}
              </div>
            </div>
            {!item.isDefault &&
              renderSharedUserList(item?.dashboardType, item?.allUsers || [])}
          </div>
        </>
      )}
    </EverCard>
  );
}

CardView.propTypes = {
  item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  handleShareModalClick: PropTypes.func.isRequired,
  getDashboardList: PropTypes.func.isRequired,
  supersetHostUrl: PropTypes.string.isRequired,
};
