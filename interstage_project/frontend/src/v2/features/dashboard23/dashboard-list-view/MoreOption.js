import { DotsVerticalIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu, message } from "antd";
import PropTypes from "prop-types";
import React, { useState } from "react";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { ClickBoundary, EverButton, EverModal } from "~/v2/components";
import Lottie from "~/v2/components/Lottie";

import DeleteAnimationData from "../dashboard-lotties/delete-animation.json";
import { deleteDashboard } from "../restApi";
/**
 *
 * @param {Object} dashboardDetail - The details of the current dashboard.
 * @returns {Array} List of Menu.Item that needs to be displayed in the menu dropdown
 */

function useDropdownMenu() {
  const menuOptions = [];
  const { isLoggedInAsUser } = useAuthStore();
  if (!isLoggedInAsUser) {
    //menuOptions.push("Manage", "Delete");
    menuOptions.push(
      { label: "Manage", value: "edit" },
      { label: "Delete", value: "delete" }
    );
  } else {
    menuOptions.push({ label: "Delete", value: "delete" });
  }

  return menuOptions;
}

useDropdownMenu.propTypes = {
  dashboardDetail: PropTypes.object,
};

/**
 *
 * @param {Object} dashboardDetail - The details of the current dashboard.
 * @param {function} setMoreVisible - The function to call to set whether the More menu is visible.
 * @param {function} getDashboardList - The function to call to get the list of dashboards.
 * @param {string} supersetHostUrl - The URL of the Superset server.
 * @param {boolean} moreVisible - Whether the More menu is visible.
 * @returns {React.ReactNode} The JSX for the dashboard More menu popover.
 */
export function MoreOption({
  dashboardDetail,
  setMoreVisible,
  getDashboardList,
  supersetHostUrl,
  moreVisible,
  refetchDashboardOpts,
}) {
  const { hasPermissions } = useUserPermissionStore();
  const [showConfirmationPopup, setShowConfirmationPopup] = useState(false);
  const { accessToken } = useAuthStore();

  const lottieDefaultOptions = {
    loop: true,
    autoplay: true,
    animationData: DeleteAnimationData,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  const handleDelete = async () => {
    const result = await deleteDashboard(dashboardDetail, accessToken);

    // Check if result is an object with status property
    if (result?.status === "SUCCESS") {
      message.success("Dashboard successfully deleted");
      getDashboardList();
      refetchDashboardOpts();
    } else {
      message.error(result?.message || "Dashboard deletion failed");
    }
  };

  const getRedirectUrl = () => {
    if (dashboardDetail.dashboardType === "superset_dashboard") {
      window.open(
        `${supersetHostUrl}/superset/dashboard/${dashboardDetail?.supersetDashboardId}`
      );
    }
  };
  const menuOptions = useDropdownMenu();
  const menuOptionsElements = menuOptions.map((menuOption) => {
    return (
      <Menu.Item key={menuOption.value}>
        <div
          className={
            menuOption.value === "delete"
              ? "text-ever-error"
              : "text-ever-base-content"
          }
          onClick={(event) => {
            event.stopPropagation();
            setMoreVisible(false);
            if (menuOption.value === "edit") {
              getRedirectUrl();
            } else {
              //onDeleteConfirm(handleDelete);
              setShowConfirmationPopup(true);
            }
          }}
          data-testid={menuOption.value}
        >
          {menuOption.label}
        </div>
      </Menu.Item>
    );
  });

  if (
    dashboardDetail.dashboardType === "superset_dashboard" &&
    hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS)
  ) {
    return (
      <>
        <EverModal.Confirm
          confirmationButtons={[
            <EverButton
              key="cancel"
              color="base"
              onClick={(event) => {
                event.stopPropagation();
                setShowConfirmationPopup(false);
              }}
              type="ghost"
            >
              Cancel
            </EverButton>,
            <EverButton
              type="filled"
              color="error"
              key="accept"
              onClick={(event) => {
                event.stopPropagation();
                setShowConfirmationPopup(false);
                handleDelete();
              }}
              data-testid="delete-dashboard"
            >
              Delete
            </EverButton>,
          ]}
          subtitle="You can't undo this action"
          title="Are you sure you want to permanently delete this dashboard?"
          visible={showConfirmationPopup}
          icon={
            <Lottie options={lottieDefaultOptions} width={64} height={64} />
          }
        />
        <ClickBoundary
          onClickOutside={() => {
            setMoreVisible(false);
          }}
        >
          <Dropdown
            overlay={<Menu>{menuOptionsElements}</Menu>}
            overlayClassName="dashboard-list-context-menu"
            onClick={(event) => {
              event.stopPropagation();
              setMoreVisible(!moreVisible);
            }}
            getPopupContainer={(trigger) => trigger.parentNode}
            visible={moreVisible}
            trigger={["click"]}
            destroyPopupOnHide
            placement="bottomRight"
          >
            <div className="cursor-pointer hover:text-ever-base-content text-ever-base-content-mid mr-1 hover:bg-ever-base-200 w-10 h-10 flex items-center justify-center rounded-lg">
              <DotsVerticalIcon
                className="w-5 h-5"
                data-testid={dashboardDetail?.name}
              />
            </div>
          </Dropdown>
        </ClickBoundary>
      </>
    );
  }
  return "";
}

MoreOption.propTypes = {
  dashboardDetail: PropTypes.object,
  setMoreVisible: PropTypes.func,
  getDashboardList: PropTypes.func,
  supersetHostUrl: PropTypes.string,
  moreVisible: PropTypes.bool,
};
