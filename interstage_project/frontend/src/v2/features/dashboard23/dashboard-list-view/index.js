import { message } from "antd";
import PropTypes from "prop-types";
import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useDashboardsStore } from "~/GlobalStores/DashboardsStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverLink,
  EverInput,
  EverLoader,
  EverBreadcrumbPortal,
  EverList,
} from "~/v2/components";
import { addMissingDashboardIds } from "~/v2/features/dashboard23/utils.js";
import { useQueryParams } from "~/v2/hooks";
import { emptyNoDashboardState } from "~/v2/images";

import { CardView } from "./CardView";
import { ListViewSkeleton } from "./ListViewSkeleton";
import { getAllDashboards } from "../restApi";
import { ShareUsersModal } from "../ShareUsersModal";
import { dashboardsUrl } from "../utils";

/**
 * Renders a list of dashboard objects.
 
 * @param {Object[]} dashboardList - The list of dashboard objects to be displayed in the sidebar.
 * @param {function} setDashboardList - The function to update the list of dashboards.
 * @param {string} supersetHostUrl - The URL of the Superset server used to generate dashboard links.
 * @returns {React.ReactNode} The JSX for the dashboard list.
 */
function RenderHeader({
  dashboardList,
  setDashboardList,
  supersetHostUrl,
  showSupersetDashboard,
}) {
  // This is the code that is responsible for displaying the list of dashboards

  const [defaultList, setDefaultList] = useState([]);
  const { hasPermissions } = useUserPermissionStore();
  const { isLoggedInAsUser } = useAuthStore();
  // const [inSearchMode, setInSearchMode] = useState(false);

  // This is the code that is responsible for displaying the list of dashboards
  useEffect(() => {
    // If the defaultList is empty, set it to the dashboardList
    if (defaultList.length <= dashboardList.length) {
      setDefaultList(dashboardList);
    }
  }, [dashboardList, defaultList]);

  return (
    <>
      <EverBreadcrumbPortal dividerIcon={<></>}>
        <div className="flex w-full">
          <div className="flex w-full justify-between ">
            {defaultList.length > 10 ? (
              <EverInput.Search
                size="small"
                placeholder="Search by name"
                className={"w-[314px] ml-3"}
                allowClear
                onChange={(event) => {
                  const searchValue = event.target.value;
                  // this code is to search the dashboard by name and store the result in dashboardlist state
                  const searchResult = defaultList.filter((item) => {
                    if (
                      item.name
                        .toLowerCase()
                        .includes(searchValue.toLowerCase())
                    ) {
                      return item;
                    }
                    return null;
                  });
                  setDashboardList(searchResult);
                  //setInSearchMode(true);
                }}
                handleClear={() => {
                  setDashboardList(defaultList);
                  //setInSearchMode(false);
                }}
              />
            ) : (
              <></>
            )}
            <div className="grow"></div>
            {hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS) &&
              showSupersetDashboard &&
              !isLoggedInAsUser && (
                <div className="flex items-center">
                  <EverLink
                    href={`${supersetHostUrl}/dashboard/list/`}
                    target="_blank"
                    label={"Go to analytics"}
                    tooltipTitle={
                      "You can create and manage your dashboards from analytics."
                    }
                  />
                  {/* {hasPermissions(RBAC_ROLES.MANAGE_DASHBOARD) && (
                    <Divider type="vertical" className="mx-4" />
                  )} */}
                </div>
              )}
          </div>
        </div>
      </EverBreadcrumbPortal>
    </>
  );
}

RenderHeader.propTypes = {
  dashboardList: PropTypes.arrayOf(PropTypes.object).isRequired,
  setDashboardList: PropTypes.func.isRequired,
  supersetHostUrl: PropTypes.string.isRequired,
  showSupersetDashboard: PropTypes.bool.isRequired,
};

function ListView({
  // This function is used to handle the click event for the Share Dashboard Modal.
  // It sets the showShareModal state to true, which displays the modal.
  // It also sets the selectedDashboard state to the dashboard that was clicked.

  dashboardList,
  handleShareModalClick,
  refetch,
  supersetHostUrl,
  refetchDashboardOpts,
}) {
  return (
    <EverList
      dataSource={dashboardList}
      grid={{
        gutter: 16,
        column: 3,
        xxl: 4,
        xl: 3,
        lg: 2,
        md: 2,
        sm: 1,
        xs: 1,
      }}
      renderItem={(item, index) => {
        return (
          <EverList.Item
            key={index}
            className="relative ease-in-out duration-100"
          >
            <CardView
              item={item}
              index={index}
              key={`card-${index}`}
              handleShareModalClick={handleShareModalClick}
              getDashboardList={refetch}
              supersetHostUrl={supersetHostUrl}
              refetchDashboardOpts={refetchDashboardOpts}
            />
          </EverList.Item>
        );
      }}
    ></EverList>
  );
}

ListView.propTypes = {
  dashboardList: PropTypes.arrayOf(PropTypes.object).isRequired,
  handleShareModalClick: PropTypes.func.isRequired,
  refetch: PropTypes.func.isRequired,
  supersetHostUrl: PropTypes.string.isRequired,
};

export default function DashboardListView() {
  const { accessToken } = useAuthStore();
  const { fetchDashboards: refetchDashboardOpts, setDashboards } =
    useDashboardsStore();
  const query = useQueryParams();
  const { hasPermissions } = useUserPermissionStore();
  const [isSharedUserModalVisible, setIsSharedUserModalVisible] =
    useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState({});
  const [isDashboardLoading, setIsDashboardLoading] = useState(false);
  const [showEmptyState, setShowEmptyState] = useState(false);
  const [isErrorPage, setErroPage] = useState(false);
  const [dashboardList, setDashboardList] = useState([]);
  const [supersetHostUrl, setSupersetHostUrl] = useState("");

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const navigate = useNavigate();

  if (query.get("type") !== "all") {
    // DashboardRedirect HOC will take care of redirecting to specific dashboard
    throw new Promise(() => {});
  }

  const getDashboardList = useCallback(async () => {
    if (clientFeatures?.showSupersetDashboard !== undefined) {
      setIsDashboardLoading(true);
      let dashboardsType = "default_dashboard";
      if (clientFeatures.showSupersetDashboard) {
        dashboardsType = "superset_dashboard";
      }
      const result = await getAllDashboards(accessToken, dashboardsType);
      setIsDashboardLoading(false);
      if (result?.status === "SUCCESS") {
        const list = addMissingDashboardIds(result.data);
        setDashboards(list);
        if (list.length === 0) {
          setShowEmptyState(true);
        } else {
          if (
            query.get("type") != "all" ||
            (list.length == 1 &&
              !hasPermissions(RBAC_ROLES.MANAGE_DASHBOARD) &&
              !hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS))
          ) {
            navigate(
              dashboardsUrl(list[0]?.dashboardId, list[0].dashboardType)
            );
          }
          setDashboardList(list);
          setSupersetHostUrl(result?.supersetHostUrl);
        }
      } else {
        message.error("Something went wrong,please try again");
        setErroPage(true);
      }
    }
  }, [accessToken, clientFeatures.showSupersetDashboard, history, query]);

  useEffect(() => {
    getDashboardList();
  }, [getDashboardList]);

  const handleShareModalClose = () => {
    setIsSharedUserModalVisible(false);
    setSelectedDashboard({});
  };

  // This function is used to open the share modal by passing the dashboard details.
  const handleShareModalClick = (dashboardDetail) => {
    setIsSharedUserModalVisible(true);
    setSelectedDashboard(dashboardDetail);
  };

  // If the company name is undefined, show the loading indicator
  if (clientFeatures.showSupersetDashboard === undefined) {
    return (
      <div className="h-full w-full flex justify-center items-center">
        <EverLoader.SpinnerLottie className="w-20 h-20" />
      </div>
    );
  }

  if (isErrorPage) {
    return (
      <div className="h-full w-full">
        Something went wrong.please try again later
      </div>
    );
  }

  // If the dashboard is loading, display the skeleton component
  if (isDashboardLoading) {
    return <ListViewSkeleton />;
  }

  if (showEmptyState) {
    return (
      <div className="w-full bg-ever-base h-full mb-24 flex items-center justify-center">
        <img src={emptyNoDashboardState} alt="empty-quota-attainment-tracker" />
      </div>
    );
  }

  return (
    <>
      <div className="dashboard-list-view  min-h-full w-full bg-ever-base ">
        <RenderHeader
          dashboardList={dashboardList}
          setDashboardList={setDashboardList}
          supersetHostUrl={supersetHostUrl}
          showSupersetDashboard={clientFeatures?.showSupersetDashboard}
        />
        <ListView
          dashboardList={dashboardList}
          handleShareModalClick={handleShareModalClick}
          refetch={getDashboardList}
          supersetHostUrl={supersetHostUrl}
          refetchDashboardOpts={refetchDashboardOpts}
        />
      </div>
      {isSharedUserModalVisible && (
        <ShareUsersModal
          isVisible={isSharedUserModalVisible}
          selectedDashboard={selectedDashboard}
          handleClose={handleShareModalClose}
          refetch={getDashboardList}
          supersetHostUrl={supersetHostUrl}
        />
      )}
    </>
  );
}
