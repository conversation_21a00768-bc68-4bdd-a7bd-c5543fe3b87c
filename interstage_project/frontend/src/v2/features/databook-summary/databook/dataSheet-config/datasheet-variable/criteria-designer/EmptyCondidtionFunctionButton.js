import { FunctionIcon } from "@everstage/evericons/outlined";
import { Space, Row, Col } from "antd";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { SPLDATATYPE, DATATYPE } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverSelect,
  EverModal,
  EverButton,
  EverInput,
  EverTg,
  EverLoader,
  EverInteractiveChip,
} from "~/v2/components";

import { useVariables } from "./useVariables";

export const EmptyConditionFunction = observer((props) => {
  const {
    name,
    onSelection,
    enabled,
    optionType,
    onFocusChange,
    datasheetId,

    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;
  const [visible, showModal] = useState(false);
  const [token, setToken] = useState(null);
  const [lookbackPeriod, setLookbackPeriod] = useState(0);
  const { loading } = dsVariableStore;
  const allVariables = useVariables({
    datasheetId,
    excludeSelectedCalculatedField: true,
    dsVariableStore: dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  });

  const onCancel = () => {
    showModal(false);
    setToken(null);
    setLookbackPeriod(0);
  };

  const getSelectOptions = () => {
    return allVariables
      .filter((token) => token.dataType != DATATYPE.HIERARCHY)
      .map((option) => (
        <EverSelect.Option
          value={option.meta.systemName}
          label={option.name}
          type={option.type}
          key={option.meta.systemName}
          dataType={option.dataType}
        >
          {option.name}
        </EverSelect.Option>
      ));
  };

  const disableClassName = enabled
    ? "text-ever-base-content"
    : "text-ever-base-content-low";

  const chipClassName = `font-medium rounded-xl bg-ever-base-100 ${disableClassName}`;

  return (
    <div>
      <EverInteractiveChip
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        prepend={<FunctionIcon className="w-3 h-3" />}
        title={name}
        className={chipClassName}
      />

      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{name}</EverTg.Heading3>
          </Space>
        }
        width={500}
        onCancel={onCancel}
        okButtonProps={token ? { disabled: false } : { disabled: true }}
        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={!token}
                onClick={() => {
                  const fnDisplayLabel = `${name}(${token.name})`;
                  const fnArgs = [token];
                  onSelection({
                    functionName: name,
                    name: fnDisplayLabel,
                    args: fnArgs,
                    dataType: optionType,
                  });
                  showModal(false);
                  setToken(null);
                  setLookbackPeriod(0);
                }}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
      >
        <Row>
          <Col span={24}>
            <Space>
              <EverTg.SubHeading4 className="text-ever-base-content">{`${name} (`}</EverTg.SubHeading4>
              <EverSelect
                className={"w-60"}
                showArrow={false}
                filterOption
                onChange={(v) => {
                  setToken(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      allVariables
                    )
                  );
                }}
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions()}
              </EverSelect>
              <EverTg.SubHeading4 className="text-ever-base-content">
                {")"}
              </EverTg.SubHeading4>
            </Space>
          </Col>
        </Row>
        {optionType === SPLDATATYPE.QUOTA && (
          <Row className="pt-[10px]">
            <Col span={24}>
              <Space>
                {"Lookback period"}
                <EverInput.Number
                  min={0}
                  max={11}
                  value={lookbackPeriod}
                  onChange={setLookbackPeriod}
                />
              </Space>
            </Col>
          </Row>
        )}
      </EverModal>
    </div>
  );
});

export default EmptyConditionFunction;
