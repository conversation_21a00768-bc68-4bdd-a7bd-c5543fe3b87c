import {
  XCloseIcon,
  ChevronDownIcon,
  FunctionIcon,
} from "@everstage/evericons/outlined";
import { Tag } from "antd";
import { isEmpty } from "lodash";
import { useState } from "react";
import { useMutation } from "react-query";

import {
  PIVOT_AGG_MAP,
  RBAC_ROLES,
  PIVOT_AGG,
  DATASHEET_VIEW_ID,
} from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverTg,
  EverButton,
  EverSwitch,
  EverLabel,
  EverSelect,
  EverTooltip,
} from "~/v2/components";

import { SaveViewModal } from "./SaveViewModal";
import useFetchApiWithAuth from "../../../useFetchApiWithAuth";

/**
 * CustomiseColumns component allows users to customize the columns of a datasheet by reordering and toggling visibility.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.gridRef - Reference to the aggrid table

 */
export const PivotTable = ({
  gridRef,
  columns,
  columnTypeMap,
  viewId,
  datasheetId,
  defaultPivotMode,
  pivotConfig,
  setPivotConfig,
  handleCreateViewSuccess,
  setPivotConfigPayload,
}) => {
  const { hasPermissions } = useUserPermissionStore();
  const { fetchData } = useFetchApiWithAuth();
  const { dataTypesById } = useVariableStore();
  const [isPivotEnabled, setIsPivotEnabled] = useState(defaultPivotMode);
  const [isSaveViewModalOpen, setIsSaveViewModalOpen] = useState(false);
  const [rowGroups, setRowGroups] = useState(
    pivotConfig?.[viewId]?.pivotData?.index || []
  );
  const [columnLabels, setColumnLabels] = useState(
    pivotConfig?.[viewId]?.pivotData?.columns || []
  );
  const [functionValues, setFunctionValues] = useState(
    pivotConfig?.[viewId]?.pivotData?.values || []
  );
  const [aggFunc, setAggFunc] = useState(
    pivotConfig?.[viewId]?.pivotData?.aggfunc || {}
  );
  const handleClose = () => {
    gridRef?.api?.setSideBarVisible(false);
  };

  const constructVariableIdMap = (allPivotVariables = []) => {
    const variableIdMap = {};
    console.log("allPivotVariables", allPivotVariables);

    // get variable id from columns
    columns.forEach((col) => {
      if (allPivotVariables.includes(col.value)) {
        variableIdMap[col.value] = col.variableId;
      }
    });

    return variableIdMap;
  };

  const savePivotData = useMutation(
    (data) =>
      fetchData(
        `/datasheets/${datasheetId}/views/${viewId}/pivots`,
        "POST",
        data
      ),
    {
      onError: (error) => {
        console.error("error", error);
      },
      onSuccess: (data) => {
        console.log(data);
        gridRef?.api?.setSideBarVisible(false);
        setPivotConfig({
          ...pivotConfig,
          [viewId]: {
            pivotData: {
              aggfunc: aggFunc,
              fill_value: "",
              index: rowGroups,
              columns: columnLabels,
              values: functionValues,
            },
            isPivotMode: true,
          },
        });
        setPivotConfigPayload({
          ...pivotConfig,
          [viewId]: {
            pivotData: {
              aggfunc: aggFunc,
              fill_value: "",
              index: rowGroups,
              columns: columnLabels,
              values: functionValues,
            },
            isPivotMode: true,
          },
        });
      },
    }
  );

  const onValueFieldSelect = (value) => {
    setAggFunc({
      ...aggFunc,
      [value]: PIVOT_AGG[dataTypesById[columnTypeMap[value]]][0]["value"],
    });
  };

  const onValueFieldDeselect = (value) => {
    const { [value]: _, ...rest } = aggFunc;
    setAggFunc(rest);
  };

  const tagRender = (props) => {
    const { label, closable, onClose, value } = props;

    const onSelect = (sl) => {
      setAggFunc({ ...aggFunc, [value]: sl });
    };

    const labelToShow = aggFunc[value]
      ? `${PIVOT_AGG_MAP[aggFunc[value]]} of ${label}`
      : "";

    return (
      <Tag
        onMouseDown={(event) => {
          event.preventDefault();
          event.stopPropagation();
        }}
        closable={closable}
        onClose={onClose}
        className="selected-pivot-tag w-48 h-6 mb-1 flex items-center "
      >
        <EverTooltip
          title={labelToShow}
          placement="bottom"
          overlayClassName="roundedTooltip"
        >
          <span className="selected-pivot-content-tag">{labelToShow}</span>
        </EverTooltip>

        <EverSelect
          id="aggFunc"
          value={[]}
          disabled={!isPivotEnabled}
          bordered={false}
          className="agg-function-tag-select !h-6 !w-12 cursor-text"
          placeholder={
            <div className="flex items-center">
              <FunctionIcon className="w-4 h-4 text-ever-base-content-mid pointer-events-none" />
              <ChevronDownIcon className="w-4 h-4 text-ever-base-content-low" />
            </div>
          }
          showArrow={false}
          onSelect={onSelect}
          dropdownMatchSelectWidth={120}
        >
          {columnTypeMap &&
            dataTypesById &&
            PIVOT_AGG[dataTypesById[columnTypeMap[value]]] &&
            PIVOT_AGG[dataTypesById[columnTypeMap[value]]].map((item, i) => (
              <EverSelect.Option key={i} value={item.value}>
                {item.label}
              </EverSelect.Option>
            ))}
        </EverSelect>
      </Tag>
    );
  };

  return (
    <>
      <div className="bg-ever-base-25 p-0 border-0 flex flex-col justify-between gap-3 h-full datasheet-pivot-table">
        <div className="flex flex-col gap-4 px-4 pt-4 h-34">
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <EverTg.SubHeading4 className="text-ever-base-content">
                Pivot Data
              </EverTg.SubHeading4>
              <XCloseIcon
                className="w-6 h-6 text-ever-base-content-mid cursor-pointer"
                onClick={handleClose}
              />
            </div>
          </div>
          <div className="w-full flex flex-col gap-4">
            <div className="flex gap-2">
              <EverSwitch
                onChange={() => {
                  setIsPivotEnabled(!isPivotEnabled);
                  if (!isEmpty(pivotConfig?.[viewId]?.pivotData)) {
                    setPivotConfig({
                      ...pivotConfig,
                      [viewId]: {
                        pivotData: pivotConfig?.[viewId]?.pivotData,
                        isPivotMode: !isPivotEnabled,
                      },
                    });
                    setPivotConfigPayload({
                      ...pivotConfig,
                      [viewId]: {
                        pivotData: pivotConfig?.[viewId]?.pivotData,
                        isPivotMode: !isPivotEnabled,
                      },
                    });
                  }
                }}
                checked={isPivotEnabled}
              />
              <EverLabel>Pivot Mode</EverLabel>
            </div>
            <div className="flex flex-col gap-2 w-60 overflow-hidden">
              <EverLabel required>Row Groups</EverLabel>
              <EverSelect
                mode="multiple"
                disabled={!isPivotEnabled}
                options={columns
                  .filter((col) => !columnLabels.includes(col.value))
                  .filter((col) => !functionValues.includes(col.value))}
                onChange={(values) => {
                  setRowGroups(values);
                }}
                value={rowGroups}
                className={"w-full h-auto"}
                multiSelectOverflow={"default"}
                maxTagTextLength={16}
                placeholder="Select Row Groups"
              />
            </div>
            <div className="flex flex-col gap-2 w-60 overflow-hidden">
              <EverLabel required>Column Labels</EverLabel>
              <EverSelect
                mode="multiple"
                disabled={!isPivotEnabled}
                onChange={(values) => {
                  setColumnLabels(values);
                }}
                value={columnLabels}
                className={"w-full h-auto"}
                multiSelectOverflow={"default"}
                placeholder="Select Column Labels"
                options={columns
                  .filter((col) => !rowGroups.includes(col.value))
                  .filter((col) => !functionValues.includes(col.value))}
              />
            </div>
            <div className="flex flex-col gap-2 w-60 overflow-hidden">
              <EverLabel required>Values</EverLabel>
              <EverSelect
                mode="multiple"
                className={"w-full h-auto agg-values-select"}
                placeholder="Select Values"
                onSelect={onValueFieldSelect}
                onDeselect={onValueFieldDeselect}
                tagRender={tagRender}
                disabled={!isPivotEnabled}
                multiSelectOverflow={"default"}
                options={columns
                  .filter((col) => !rowGroups.includes(col.value))
                  .filter((col) => !columnLabels.includes(col.value))}
                onChange={(values) => {
                  setFunctionValues(values);
                }}
                value={functionValues}
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col justify-end h-full overflow-hidden">
          <div className="h-14 bg-ever-base-50 flex-shrink-0 mt-4 flex items-center justify-end px-4 gap-4">
            {viewId === DATASHEET_VIEW_ID.ALL_DATA ? (
              <EverButton
                type="link"
                color="primary"
                onClick={() => setIsSaveViewModalOpen(true)}
                disabled={
                  !hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) ||
                  functionValues.length === 0 ||
                  rowGroups.length === 0 ||
                  columnLabels.length === 0 ||
                  !isPivotEnabled
                }
              >
                Apply & save
              </EverButton>
            ) : (
              <EverButton type="ghost" color="base" onClick={handleClose}>
                Cancel
              </EverButton>
            )}
            <EverButton
              disabled={
                !hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) ||
                functionValues.length === 0 ||
                rowGroups.length === 0 ||
                columnLabels.length === 0 ||
                !isPivotEnabled
              }
              onClick={() => {
                const payload = {
                  aggfunc: aggFunc,
                  fill_value: "",
                  index: rowGroups,
                  columns: columnLabels,
                  values: functionValues,
                  variableIdMap: constructVariableIdMap([
                    ...new Set([
                      ...rowGroups,
                      ...columnLabels,
                      ...functionValues,
                      ...Object.keys(aggFunc),
                    ]),
                  ]),
                };
                if (viewId === DATASHEET_VIEW_ID.ALL_DATA) {
                  setPivotConfig({
                    ...pivotConfig,
                    [viewId]: {
                      pivotData: payload,
                      isPivotMode: true,
                    },
                  });
                  setPivotConfigPayload({
                    ...pivotConfig,
                    [viewId]: {
                      pivotData: payload,
                      isPivotMode: true,
                    },
                  });
                } else {
                  savePivotData.mutate({
                    pivot_data: payload,
                  });
                }
              }}
            >
              {viewId === DATASHEET_VIEW_ID.ALL_DATA ? "Apply" : "Save"}
            </EverButton>
          </div>
        </div>
      </div>
      {isSaveViewModalOpen && (
        <SaveViewModal
          isModalOpen={isSaveViewModalOpen}
          setIsModalOpen={setIsSaveViewModalOpen}
          datasheetId={datasheetId}
          pivotData={{
            aggfunc: aggFunc,
            fill_value: "",
            index: rowGroups,
            columns: columnLabels,
            values: functionValues,
            variableIdMap: constructVariableIdMap([
              ...new Set([
                ...rowGroups,
                ...columnLabels,
                ...functionValues,
                ...Object.keys(aggFunc),
              ]),
            ]),
          }}
          handleCreateViewSuccess={handleCreateViewSuccess}
          setPivotConfig={setPivotConfig}
          pivotConfig={pivotConfig}
          setPivotConfigPayload={setPivotConfigPayload}
        />
      )}
    </>
  );
};
