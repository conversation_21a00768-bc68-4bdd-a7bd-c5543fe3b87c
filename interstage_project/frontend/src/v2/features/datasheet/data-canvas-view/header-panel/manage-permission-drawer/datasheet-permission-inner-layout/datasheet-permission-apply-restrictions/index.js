import { UsersIcon } from "@everstage/evericons/duotone";
import React from "react";

import { MultiTabDropdownSelect } from "~/v2/components";

import FetchUsers from "./FetchUsers";
import {
  UPDATE_USERS,
  UPDATE_USER_GROUPS,
} from "../../useDataSheetPermissions";

export function DataSheetPermissionApplyRestrictions({
  allUserGroups,
  allUserGroupsLoading,
  selectedUsers,
  selectedUserGroups,
  onUpdateSelection,
  onToggleUserSelection,
  onToggleUserGroupSelection,
}) {
  const selectedUsersFormatted = [];
  for (const user of selectedUsers) {
    selectedUsersFormatted.push({
      value: user.employeeEmailId,
      label: user.fullName,
    });
  }
  const selectedUserGroupsFormatted = [];
  for (const group of selectedUserGroups) {
    selectedUserGroupsFormatted.push({
      value: group.userGroupId,
      label: group.userGroupName,
    });
  }

  const userGroupOptions = [];
  for (const userGroup of allUserGroups) {
    const assignedTo = userGroup.all_members?.length ?? 0;
    userGroupOptions.push({
      value: userGroup.user_group_id,
      label: userGroup.user_group_name,
      description:
        assignedTo > 0
          ? assignedTo > 1
            ? `${assignedTo} users`
            : `${assignedTo} user`
          : "Not assigned to anyone",
      icon: (
        <UsersIcon className="h-5 w-5 mt-1.5 text-ever-success-content-lite" />
      ),
      avatarClass: "!bg-ever-success/20 flex justify-center",
    });
  }

  const tabs = [
    {
      name: "Users",
      key: "users",
      isLazy: true,
      selectedValues: selectedUsersFormatted,
      renderLazyList: () => (
        <FetchUsers
          selectedUsers={selectedUsersFormatted}
          onClickItem={(object) =>
            onToggleUserSelection({
              employeeEmailId: object.value,
              fullName: object.label,
            })
          }
        />
      ),
    },
    {
      name: "Groups",
      key: "groups",
      isLazy: false,
      loading: allUserGroupsLoading,
      searchPlaceholder: "Search by group name",
      showSearch: false,
      selectedValues: selectedUserGroupsFormatted,
      options: userGroupOptions,
      onToggleItem: (object) =>
        onToggleUserGroupSelection({
          userGroupId: object.value,
          userGroupName: object.label,
        }),
    },
  ];

  const onRemove = (objects) => {
    const finalObjects = {
      [UPDATE_USER_GROUPS]: [],
      [UPDATE_USERS]: [],
    };
    const selectedUserGroupsIds = new Set(
      selectedUserGroups.map((userGroup) => userGroup.userGroupId)
    );
    const selectedUserIds = new Set(
      selectedUsers.map((user) => user.employeeEmailId)
    );
    for (const object of objects) {
      if (selectedUserGroupsIds.has(object.value)) {
        finalObjects.UPDATE_USER_GROUPS.push({
          userGroupId: object.value,
          userGroupName: object.label,
        });
      } else if (selectedUserIds.has(object.value)) {
        finalObjects.UPDATE_USERS.push({
          employeeEmailId: object.value,
          fullName: object.label,
        });
      }
    }
    onUpdateSelection(finalObjects);
  };

  return (
    <div className="w-full mt-3.5">
      <MultiTabDropdownSelect
        placeholder="Select by users and groups..."
        selectClassName="w-full "
        tabsClassName="h-52"
        tabs={tabs}
        maxTagCount={10_000}
        onRemove={onRemove}
      />
    </div>
  );
}
