import { cloneDeep, isEmpty, debounce } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useMutation } from "react-query";
import { useImmer } from "use-immer";
import { v4 as uuidv4 } from "uuid";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  NEW_DATATYPES,
  EXPRESSION_TOKEN_TYPES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  WINDOW_CALCULATED_FIELDS,
  EXPRESSION_FUNCTION_TYPES,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverModal,
  EverTg,
  EverButton,
  EverLabel,
  EverInput,
  EverSelect,
  EverButtonGroup,
  message,
} from "~/v2/components";

import { ConditionalType } from "./conditionalType";
import { DatasheetContext } from "../../../DatasheetStore";
import { DatabookExpressionBox } from "../../../helperComponents/DatabookExpressionBox";
import useFetchApiWithAuth from "../../../useFetchApiWithAuth";
import {
  compareIfIntegerOrPercentage,
  generateSystemName,
  snakeToCamel,
} from "../../utils";

const defaultFormulaFieldConfig = {
  name: "",
  datatype: null,
  expressionType: "simple",
  expression: null,
  initialExpression: [],
  isExpressionValid: false,
};

/**
 * Renders a component for creating or updating a formula field.
 * @param {object} props - The props object.
 * @param {boolean} props.modalVisible - Flag indicating if the create formula field modal is visible.
 * @param {function} props.setShowFormulaWizard - Function to set the create formula field modal visibility.
 * @param {Array} props.sourceOptions - Array of source options.
 * @param {object} props.datasheetMap - Map of datasheets.
 * @param {string} props.currentDatasheetName - Name of the current datasheet.
 * @param {object} props.selectedFormulaField - Selected formula Field details.
 * @param {boolean} props.hasIntermediateVariables - Flag indicating if intermediate variables exist.
 * @param {Array} props.dataFields - Array of source variables.
 * @param {string} props.datasheetId - ID of the datasheet.
 * @param {boolean} props.isUpdateView - Flag indicating if it's an update view.
 * @param {function} props.setSourceVariables - Function to set source variables.
 * @param {function} props.setHasIntermediateVariables - Function to set intermediate variables flag.
 * @param {object} props.savedTransformations - The list of saved transformations.
 * @param {string} props.databookId - ID of the databook.
 * @param {function} props.setIsUpdateView - Function to set update view flag.
 * @param {function} props.setSelectedFormulaField - Function to set selected formula field.
 * * @param {object} props.baseVariables - The list of variables from selected source.
 * @param {function} props.setUnsavedEdits - Function to set the unsaved edits flag.
 * @param {boolean} props.unsavedEdits - Flag indicating unsaved edits.
 * @returns {JSX.Element} The JSX element representing the formula field component.
 */

export const FormulaWizard = observer(
  ({
    modalVisible,
    setShowFormulaWizard,
    sourceOptions,
    datasheetMap,
    currentDatasheetName,
    selectedFormulaField,
    hasIntermediateVariables,
    dataFields,
    datasheetId,
    isUpdateView,
    setSourceVariables,
    setHasIntermediateVariables,
    savedTransformations,
    databookId,
    setIsUpdateView,
    setSelectedFormulaField,
    baseVariables,
    setUnsavedEdits,
    unsavedEdits,
    isInitialValidation,
    setIsInitialValidation,
    setUpdateCalculatedFieldDetails,
    setSearchKey,
    selectedSource,
  }) => {
    // Initialize the formula field state using Immer for immutable updates
    const [formulaField, setFormulaField] = useImmer(defaultFormulaFieldConfig);

    const { fetchData } = useFetchApiWithAuth();
    const datasheetStore = useContext(DatasheetContext);
    const { dataTypeIdsFromNames } = useVariableStore();
    const { accessToken } = useAuthStore();
    const [isVariableTooLong, setIsVariableTooLong] = useState(false);

    // Handler to close the modal and reset state
    const handleCloseDrawer = () => {
      resetModalState();
      setShowFormulaWizard(false);
      setUpdateCalculatedFieldDetails(null);
    };

    // Function to reset modal state
    const resetModalState = () => {
      setIsUpdateView(false);
      setFormulaField(defaultFormulaFieldConfig);
      setSelectedFormulaField({});
    };

    // Mutation for creating or updating a formula field
    const createFormulaFieldMutation = useMutation(
      (variables) => {
        const body = {
          transformations: savedTransformations,
          variables,
          sourceVariables: baseVariables,
          initialValidation: isInitialValidation,
          sourceType: selectedSource.type,
          sourceId: selectedSource.id,
          additional_source_variables:
            datasheetStore.datasheetDetailsForEdit?.additional_source_variables,
        };
        return fetchData(`datasheets/${datasheetId}/validate`, "POST", body);
      },
      {
        onSuccess: (res) => {
          setSourceVariables(res.variables || []);
          setHasIntermediateVariables(true);
          handleCloseDrawer();
          !unsavedEdits && setUnsavedEdits(true);

          setSearchKey("");
          setIsInitialValidation(false);
          if (res.has_config_changed) {
            message.warning(
              "Config changed. All the records will be considered as new records in the associated workflows.",
              3000,
              "h-auto py-2"
            );
          }
        },
        onError: (error) => {
          message.error(
            error.message || "Error occurred while creating formula field",
            3000,
            "h-auto py-2"
          );
          console.error("Error:", error);
        },
      }
    );

    // Effect to set the formula field state when a formula field is selected
    useEffect(() => {
      if (!isEmpty(selectedFormulaField)) {
        setFormulaField((draft) => {
          draft.name = selectedFormulaField.name;
          draft.datatype = selectedFormulaField.dataType || null;
          draft.initialExpression =
            selectedFormulaField.initialExpression || [];
          draft.expressionType =
            selectedFormulaField.expressionType || "simple";
        });
      }
    }, [selectedFormulaField, setFormulaField]);

    const hierarchyFields =
      dataFields?.filter(
        (x) =>
          x?.meta_data?.token?.data_type === EXPRESSION_FUNCTION_TYPES.Hierarchy
      ) || [];
    const hierarchyFieldMap = {};
    hierarchyFields?.forEach((item) => {
      hierarchyFieldMap[item.system_name] = snakeToCamel(item);
    });

    // Function to update existing fields with new calculated field data
    const updateExistingFields = useCallback(() => {
      const clonedFields = cloneDeep(dataFields);
      const expressionList =
        formulaField.expressionType === "simple"
          ? formulaField.expression
          : formulaField.expression?.token?.args?.flatMap((token) => token) ||
            [];
      for (const field of clonedFields) {
        if (field.system_name === selectedFormulaField.systemName) {
          // if existing field is not case insensitive set it as case insensitive
          const case_insensitive = field?.meta_data?.case_insensitive || false;
          if (
            WINDOW_CALCULATED_FIELDS.includes(
              expressionList[0]?.token?.functionName?.toLowerCase()
            )
          ) {
            Object.assign(field, {
              display_name: formulaField.name?.trim(),
              meta_data: {
                criteria_type: formulaField.expressionType,
                ...formulaField.expression[0],
                used_system_names: getUsedSystemName(expressionList),
                case_insensitive: case_insensitive,
              },
            });
          } else {
            Object.assign(field, {
              display_name: formulaField.name?.trim(),
              meta_data: {
                criteria_type: formulaField.expressionType,
                infix: formulaField.expression,
                used_system_names: getUsedSystemName(expressionList),
                case_insensitive: case_insensitive,
              },
            });
          }
        }
      }
      return clonedFields;
    }, [dataFields, formulaField, selectedFormulaField]);

    // Handler for creating or updating a formula field
    const handleCreateOrUpdate = useCallback(
      async () => {
        sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.ADD_FORMULA_FIELD, {
          [ANALYTICS_PROPERTIES.DATASHEET_NAME]: currentDatasheetName,
          [ANALYTICS_PROPERTIES.DATA_TYPE]: formulaField.datatype,
          [ANALYTICS_PROPERTIES.FORMULA_FIELD_NAME]: formulaField.name,
        });
        if (isUpdateView) {
          await createFormulaFieldMutation.mutate(updateExistingFields());
        } else {
          const existingFormulaFields = [];
          const nonFormulaFields = [];
          for (const field of dataFields) {
            if (field.variable_id === datasheetId) {
              existingFormulaFields.push(field.system_name);
            } else {
              nonFormulaFields.push(field.system_name);
            }
          }

          const newCalField = createCalculatedField(
            formulaField,
            existingFormulaFields,
            nonFormulaFields,
            dataTypeIdsFromNames,
            datasheetId,
            currentDatasheetName
          );

          await createFormulaFieldMutation.mutate([...dataFields, newCalField]);
        }
      }, // Debounce time in milliseconds
      [
        isUpdateView,
        createFormulaFieldMutation,
        dataFields,
        formulaField,
        dataTypeIdsFromNames,
        datasheetId,
        currentDatasheetName,
        updateExistingFields,
      ]
    );

    const debouncedHandleCreateOrUpdate = useMemo(
      () => debounce(handleCreateOrUpdate, 100),
      [handleCreateOrUpdate]
    );

    // JSX for the modal and its contents
    return (
      <>
        <EverModal
          maskClosable={false}
          title={<EverTg.Heading3>Add Formula Column</EverTg.Heading3>}
          onCancel={handleCloseDrawer}
          visible={modalVisible}
          destroyOnClose={true}
          width="1200px"
          className="calculatedFieldModal"
          bodyStyle={{ display: "flex", overflow: "auto" }}
          footer={
            <div className="w=full flex justify-end gap-2">
              <EverButton
                type="filled"
                color="base"
                onClick={handleCloseDrawer}
              >
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                onClick={debouncedHandleCreateOrUpdate}
                loading={createFormulaFieldMutation.isLoading}
                disabled={
                  createFormulaFieldMutation.isLoading ||
                  isEmpty(formulaField.name?.trim()) ||
                  isEmpty(formulaField.datatype) ||
                  !formulaField.isExpressionValid ||
                  isVariableTooLong
                }
              >
                {isUpdateView ? "Update" : "Create"}
              </EverButton>
            </div>
          }
          centered
        >
          <div
            className={
              "bg-ever-base-25 text-ever-base-content text-xs min-h-[400px] w-full flex flex-col"
            }
          >
            <div className="flex gap-10">
              <div className="flex ">
                <EverLabel required>Name</EverLabel>

                <EverInput
                  className="w-60"
                  placeholder="Enter Name"
                  value={formulaField?.name}
                  onChange={(event) => {
                    const variableName = event.target.value;
                    setFormulaField((draft) => {
                      draft.name = event.target?.value;
                    });
                    if (variableName?.trim()?.length < 254) {
                      setIsVariableTooLong(false);
                    } else {
                      setIsVariableTooLong(true);
                    }
                  }}
                />
              </div>
              <div className="flex ">
                <EverLabel required>Type</EverLabel>
                <EverSelect
                  className="w-60"
                  placeholder="Select Data Type"
                  value={formulaField.datatype}
                  onChange={(value) => {
                    setFormulaField((draft) => {
                      draft.datatype = value;
                    });
                  }}
                  disabled={isUpdateView}
                >
                  {Object.values(NEW_DATATYPES).map((t) => (
                    <EverSelect.Option key={t} value={t}>
                      {t}
                    </EverSelect.Option>
                  ))}
                </EverSelect>
              </div>
              <div className="flex ">
                <EverLabel required>Expression</EverLabel>
                <EverButtonGroup
                  className="bg-ever-base-200 !border-0"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={
                    formulaField.expressionType === "simple" ? 0 : 1
                  }
                  size="large"
                >
                  <EverButton
                    onClick={() =>
                      setFormulaField((draft) => {
                        draft.expressionType = "simple";
                      })
                    }
                  >
                    Simple
                  </EverButton>
                  <EverButton
                    onClick={() =>
                      setFormulaField((draft) => {
                        draft.expressionType = "conditional";
                      })
                    }
                  >
                    Conditional
                  </EverButton>
                </EverButtonGroup>
              </div>
            </div>
            {isVariableTooLong && (
              <EverTg.Caption className="text-ever-error mt-1">
                Column name should be less than 254 characters
              </EverTg.Caption>
            )}
            <div>
              {formulaField.expressionType === "simple" ? (
                <DatabookExpressionBox
                  datasheetId={datasheetId || ""}
                  databookId={databookId || ""}
                  onChange={(expression, response) => {
                    setFormulaField((draft) => {
                      if (
                        response.status === "VALID" &&
                        (response.dataType?.toLowerCase() ===
                          formulaField.datatype?.toLowerCase() ||
                          compareIfIntegerOrPercentage(
                            response.dataType,
                            formulaField.datatype
                          ))
                      ) {
                        draft.isExpressionValid = true;
                        draft.expression = expression;
                      } else {
                        draft.isExpressionValid = false;
                      }
                    });
                  }}
                  showHierarchyFunction
                  initialExpression={formulaField.initialExpression}
                  resultType={formulaField.datatype}
                  criteriaContext={{
                    datasheet: "calculated_fields",
                    criteriaType: "custom_simple",
                  }}
                  excludeInSuggestions={
                    isUpdateView ? [selectedFormulaField.systemName] : []
                  }
                  databooks={sourceOptions.databook || []}
                  datasheetMap={datasheetMap}
                  hasIntermediateVariables={hasIntermediateVariables}
                  hierarchyFieldMap={hierarchyFieldMap}
                />
              ) : (
                <ConditionalType
                  datasheetId={datasheetId || ""}
                  databookId={databookId || ""}
                  onASTChange={(expression, isValid) => {
                    setFormulaField((draft) => {
                      draft.isExpressionValid = isValid;
                      draft.expression = expression;
                    });
                  }}
                  showHierarchyFunction={false}
                  initialExpression={formulaField.initialExpression}
                  expectedType={formulaField?.datatype}
                  criteriaContext={{
                    datasheet: "calculated_fields",
                    criteriaType: "custom_simple",
                  }}
                  hasIntermediateVariables={hasIntermediateVariables}
                  excludeInSuggestions={
                    isUpdateView ? [selectedFormulaField.systemName] : []
                  }
                />
              )}
            </div>
          </div>
        </EverModal>
      </>
    );
  }
);

function createCalculatedField(
  formulaField,
  existingFormulaFields,
  nonFormulaFields,
  dataTypeIdsFromNames,
  datasheetId,
  currentDatasheetName
) {
  // Generate a unique system name for the calculated field
  const calculatedFieldSystemName = generateSystemName(
    formulaField.name,
    ["cf"],
    existingFormulaFields,
    nonFormulaFields
  );

  // Determine the expression list based on the expression type
  const expressionList =
    formulaField.expressionType === "simple"
      ? formulaField.expression
      : formulaField.expression?.token?.args?.flatMap((token) => token) || [];

  // Check if the expression is a window calculated field
  const isWindowCalculatedField = WINDOW_CALCULATED_FIELDS.includes(
    expressionList[0]?.token?.functionName?.toLowerCase()
  );

  // Create metadata based on whether it's a window calculated field
  const metaData = isWindowCalculatedField
    ? {
        criteria_type: formulaField.expressionType,
        ...formulaField.expression[0],
        used_system_names: getUsedSystemName(expressionList),
        case_insensitive: true, // In Datasheet v2 all string comparisons are case insensitive
      }
    : {
        criteria_type: formulaField.expressionType,
        infix: formulaField.expression,
        used_system_names: getUsedSystemName(expressionList),
        case_insensitive: true, // In Datasheet v2 all string comparisons are case insensitive
      };

  // Return the calculated field object
  return {
    display_name: formulaField.name.trim(),
    system_name: calculatedFieldSystemName,
    data_type_id: dataTypeIdsFromNames[formulaField.datatype],
    meta_data: metaData,
    field_order: 0,
    variable_id: uuidv4(),
    source_variable_id: null,
    source_id: datasheetId,
    source_type: "datasheet",
    source_name: currentDatasheetName,
    is_selected: true,
    is_primary: false,
  };
}

function getUsedSystemName(expression = []) {
  // Return an empty array if the expression is empty
  if (expression.length === 0) return [];

  const systemNames = expression
    .flatMap((exp) => {
      if (exp?.tokenType === EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES) {
        // Return the system name for DATASHEET_VARIABLES tokens
        return exp.token.systemName;
      }
      if (exp?.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS) {
        // Recursively get system names for function arguments
        return getUsedSystemName(exp.token.args || []);
      }
      return null;
      // If neither condition is met, implicitly return undefined
    })
    // Remove any falsy values (undefined, null, etc.) from the array
    .filter(Boolean);

  // Return a unique array of system names
  return [...new Set(systemNames)];
}
