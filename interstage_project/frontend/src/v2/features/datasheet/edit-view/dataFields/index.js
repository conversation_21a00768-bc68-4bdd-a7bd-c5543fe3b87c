// @ts-nocheck
import { Cube01Icon } from "@everstage/evericons/duotone";
import { FunctionIcon } from "@everstage/evericons/outlined";
import {
  //Server03Icon,
  LayoutGrid01Icon,
} from "@everstage/evericons/solid";
import { cloneDeep, isEmpty } from "lodash";
import React, { useEffect, useState, useMemo } from "react";
import { useMutation } from "react-query";
import { v4 as uuidv4 } from "uuid";

import { CHECKBOX_STATUS } from "~/Enums";
import {
  EverTg,
  EverInput,
  EverCheckbox,
  EverLabel,
  message,
  EverModal,
  EverTooltip,
} from "~/v2/components";
import { noData } from "~/v2/images";

import { DataFieldViewer } from "./DataFieldViewer";
import { FormulaWizard } from "./formulaWizard";
import useFetchApiWithAuth from "../../useFetchApiWithAuth";
import { groupByType, showSupersetNotification } from "../utils";

const FORMULA_COLUMN_NAME = "Formula Columns";

/**
 * Renders a component for managing data fields.
 *
 * @param {object} props - The props object.
 * @param {Array} [props.dataFields=[]] - Array of data fields.
 * @param {object} props.sourceOptions - Source options object.
 * @param {object} props.datasheetMap - Map of datasheets.
 * @param {string} props.datasheetId - ID of the datasheet.
 * @param {boolean} props.hasIntermediateVariables - Flag indicating if intermediate variables exist.
 * @param {string} props.currentDatasheetName - Name of the current datasheet.
 * @param {object} props.databookId - ID of the databook.
 * @param {function} props.setSourceVariables - Function to set source variables.
 * @param {function} props.setHasIntermediateVariables - Function to set intermediate variables flag.
 * @param {object} props.savedTransformations - The list of saved transformations.
 * @param {object} props.baseVariables - The list of variables from selected source.
 * @param {function} props.setUnsavedEdits - Function to set the unsaved edits flag.
 * @param {boolean} props.unsavedEdits - Flag indicating unsaved edits.
 * @param {boolean} props.isInitialValidation - Flag indicating if initial validation is in progress.
 * @param {function} props.setIsInitialValidation - Function to set the initial validation flag.
 * @param {boolean} props.isValidationInProgress - Flag indicating if validation is in progress.
 * @param {function} props.setUpdateCalculatedFieldDetails - Function to set the update calculated field details.
 * @param {object} props.updateCalculatedFieldDetails - The list of update calculated field details.
 * @param {function} props.setIsValidationInProgress - Function to set the validation in progress flag.
 * @returns {JSX.Element} The JSX element representing the data fields component.
 */
export function DataFields({
  dataFields = [],
  sourceOptions,
  datasheetMap,
  datasheetId,
  databookId,
  hasIntermediateVariables,
  currentDatasheetName,
  setSourceVariables,
  setHasIntermediateVariables,
  savedTransformations,
  baseVariables,
  setUnsavedEdits,
  unsavedEdits,
  isInitialValidation,
  setIsInitialValidation,
  updateCalculatedFieldDetails,
  setUpdateCalculatedFieldDetails,
  isValidationInProgress,
  setIsValidationInProgress,
  supersetDetails,
  selectedSource,
  shouldDisplaySupersetNotification,
  handleSupersetNotificationLogic,
}) {
  const [showFormulaWizard, setShowFormulaWizard] = useState(false);
  const [isUpdateView, setIsUpdateView] = useState(false);
  const [selectAllChecked, setSelectAllChecked] = useState(
    CHECKBOX_STATUS.NONE
  );
  const { fetchData } = useFetchApiWithAuth();
  const [allDataFields, setAllDataFields] = useState([]);
  const [pendingVariables, setPendingVariables] = useState(null);

  const [selectedFormulaField, setSelectedFormulaField] = useState({});

  const groupedTypes = useMemo(
    () => groupByType(allDataFields, datasheetId),
    [allDataFields, datasheetId]
  );
  const [searchKey, setSearchKey] = useState("");

  useEffect(() => {
    setAllDataFields([...dataFields]);
  }, [dataFields]);

  useEffect(() => {
    const selectedFields = allDataFields.filter((field) => field.is_selected);
    setSelectAllChecked(
      selectedFields.length === 0
        ? CHECKBOX_STATUS.NONE
        : selectedFields.length === allDataFields.length
        ? CHECKBOX_STATUS.ALL
        : CHECKBOX_STATUS.INDETERMINATE
    );
  }, [allDataFields]);

  const handleVariableAction = useMutation(
    (payload) => {
      const body = {
        transformations: savedTransformations,
        variables: payload.variables,
        sourceVariables: baseVariables,
        initialValidation: isInitialValidation,
        sourceType: selectedSource.type,
        sourceId: selectedSource.id,
      };

      return fetchData(`datasheets/${datasheetId}/validate`, "POST", body);
    },
    {
      onSuccess: (res) => {
        setSourceVariables(res.variables || []);
        setHasIntermediateVariables(true);
        !unsavedEdits && setUnsavedEdits(true);

        setAllDataFields(
          (res.variables || [])?.filter((dataField) =>
            dataField?.display_name
              ?.toLowerCase()
              .includes(searchKey?.toLowerCase())
          )
        );
        setIsInitialValidation(false);
        if (
          supersetDetails?.isDatasheetUsedInSuperset &&
          shouldDisplaySupersetNotification
        ) {
          showSupersetNotification(supersetDetails);
        }
        if (res.has_config_changed) {
          message.warning(
            "Config changed. All the records will be considered as new records in the associated workflows.",
            3000,
            "h-auto py-2"
          );
        }
        setPendingVariables(null);
        handleSupersetNotificationLogic(true);
      },
      onError: (error) => {
        message.error(error.message || "Error occurred", 3000, "h-auto py-2");
        setAllDataFields(
          dataFields?.filter((dataField) =>
            dataField?.display_name
              ?.toLowerCase()
              .includes(searchKey?.toLowerCase())
          )
        );
        setPendingVariables(null);
        handleSupersetNotificationLogic(true);
      },
    }
  );

  useEffect(() => {
    setIsValidationInProgress(handleVariableAction.isLoading);
  }, [handleVariableAction.isLoading]);

  const handleDelete = (field) => {
    EverModal.confirm({
      title: `Are you sure to delete this field?`,

      onOk() {
        const updatedVariables = cloneDeep(dataFields).filter(
          (x) => x.system_name !== field.system_name
        );
        handleVariableAction.mutate({ variables: updatedVariables });
      },
      onCancel() {
        return;
      },
    });
  };

  const handleClone = (field) => {
    // Function to find the maximum integer for copies of the given name pattern
    const findMaxNumber = (allFields, name, nameType) => {
      let maxNumber = 0;
      const namePattern = new RegExp(`^${name}_copy_(\\d+)$`);
      allFields.forEach((field) => {
        const match = field[nameType].match(namePattern);
        if (match) {
          const number = parseInt(match[1], 10);
          if (number > maxNumber) {
            maxNumber = number;
          }
        }
      });
      return maxNumber;
    };

    // Generate the new name with the next integer
    const newDisplayName = `${field.display_name.trim()}_copy_${
      findMaxNumber(allDataFields, field.display_name.trim(), "display_name") +
      1
    }`;
    const newSystemName = `${field.system_name}_copy_${
      findMaxNumber(allDataFields, field.system_name, "system_name") + 1
    }`;

    const clonedField = {
      ...cloneDeep(field),
      system_name: newSystemName,
      display_name: newDisplayName,
      variable_id: uuidv4(),
    };
    handleVariableAction.mutate({ variables: [...dataFields, clonedField] });
  };

  useEffect(() => {
    if (!shouldDisplaySupersetNotification && pendingVariables) {
      handleVariableAction.mutate({ variables: pendingVariables });
    }
  }, [shouldDisplaySupersetNotification, pendingVariables]);

  const handleRename = ({ systemName, displayName }) => {
    const updatedVariables = dataFields.map((variable) =>
      variable.system_name === systemName
        ? { ...variable, display_name: displayName.trim() }
        : variable
    );
    setPendingVariables(updatedVariables);
    handleSupersetNotificationLogic(false);
  };

  const handleCheckboxToggle = (updatedVariables) => {
    handleVariableAction.mutate({ variables: updatedVariables });
  };

  const renderFieldGroup = (icon, name, fields) => (
    <div className="flex flex-col gap-2 w-full">
      <div className="flex items-center gap-1">
        {icon}
        <EverTg.SubHeading4 className="text-ever-base-content-mid font-medium truncate">
          <EverTooltip title={name}>{name}</EverTooltip>
        </EverTg.SubHeading4>
      </div>
      <div className="flex flex-col gap-1">
        {fields
          ?.sort((a, b) => {
            // Sort based on is_primary (true first)
            if (a.is_primary !== b.is_primary) {
              return b.is_primary - a.is_primary; // true (1) comes before false (0)
            }
            // If both are primary or both are non-primary, sort alphabetically by display_name
            return a.display_name.localeCompare(b.display_name);
          })
          .map((field) => (
            <DataFieldViewer
              key={field.system_name}
              field={field}
              triggerRename={handleRename}
              handleCheckboxToggle={(selectedFieldName) => {
                const updatedVariables = cloneDeep(dataFields).map(
                  (variable) => {
                    if (variable.system_name === selectedFieldName) {
                      variable.is_selected = !variable.is_selected;
                    }
                    // setAllDataFields(setAllFieldsDisabled());
                    return variable;
                  }
                );

                handleCheckboxToggle(updatedVariables);
              }}
              isFormulaField={name === FORMULA_COLUMN_NAME}
              setShowFormulaWizard={setShowFormulaWizard}
              setSelectedFormulaField={setSelectedFormulaField}
              setIsUpdateView={setIsUpdateView}
              onDelete={handleDelete}
              onClone={handleClone}
              isValidationInProgress={isValidationInProgress}
              updateCalculatedFieldDetails={updateCalculatedFieldDetails}
            />
          ))}
      </div>
    </div>
  );

  const groupedFields = () => {
    const { formula_field, ...otherTypes } = groupedTypes;
    return (
      <div className="flex flex-col gap-4">
        {formula_field &&
          renderFieldGroup(
            <FunctionIcon className="w-4 h-4 text-ever-base-content" />,
            FORMULA_COLUMN_NAME,
            formula_field.formulaField
          )}
        {Object.entries(otherTypes).map(([key, fields]) => {
          const icon =
            key === "datasheet" ? (
              <LayoutGrid01Icon className="w-4 h-4 text-ever-chartColors-53" />
            ) : (
              <Cube01Icon className="w-4 h-4 text-ever-base-content" />
            );
          return Object.keys(fields).map((name) => {
            return renderFieldGroup(icon, name, fields[name]);
          });
        })}
      </div>
    );
  };

  return (
    <>
      <div className="min-h-full relative w-full  border border-r-0 border-t-0 border-b-0 border-solid border-ever-base-400 flex flex-col">
        <div className="flex h-36 fixed z-50 bg-ever-base w-1/4 flex-col px-5 py-4 gap-3 border border-t-0 border-r-0 border-l-0  border-solid border-ever-base-400">
          <EverTg.Heading3 className="text-ever-base-content">
            Columns
          </EverTg.Heading3>
          {isEmpty(dataFields) ? (
            <div className="w-full h-full flex flex-col items-center justify-center">
              <img src={noData} className="w-48 h-48" />
              <EverTg.Heading3>No Columns</EverTg.Heading3>
            </div>
          ) : (
            <>
              <EverInput.Search
                allowClear
                className="w-full"
                placeholder="Search"
                onChange={(event) => {
                  const value = event.target.value;
                  setSearchKey(value);
                  const searchedFields = dataFields.filter((dataField) =>
                    dataField?.display_name
                      ?.toLowerCase()
                      .includes(value?.toLowerCase())
                  );
                  setAllDataFields(searchedFields);
                }}
                size="medium"
                value={searchKey}
              />

              {searchKey === "" && (
                <div className="flex items-center justify-between">
                  {!isEmpty(allDataFields) && (
                    <EverCheckbox
                      onChange={(event) => {
                        const isChecked = event.target.checked;

                        if (
                          !dataFields?.every(
                            (dataField) => dataField.is_primary
                          )
                        ) {
                          const updatedDataFields = cloneDeep(dataFields).map(
                            (field) => {
                              if (!field.is_primary) {
                                field.is_selected = isChecked;
                              }
                              return field;
                            }
                          );
                          // setAllDataFields(updatedDataFields);
                          handleCheckboxToggle(updatedDataFields);
                        }
                      }}
                      checked={
                        selectAllChecked === CHECKBOX_STATUS.ALL ? true : false
                      }
                      disabled={isValidationInProgress}
                    >
                      <EverLabel>Select All</EverLabel>
                    </EverCheckbox>
                  )}
                  <div
                    className={`flex items-center gap-2 cursor-pointer ${
                      isValidationInProgress
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    }`}
                    onClick={() => {
                      if (!isValidationInProgress) {
                        setShowFormulaWizard(true);
                      }
                    }}
                  >
                    <EverTg.SubHeading4 className="text-ever-primary">
                      Add Formula Column
                    </EverTg.SubHeading4>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
        {!isEmpty(dataFields) && (
          <div className="flex mt-36  flex-col w-full h-full overflow-scroll px-5 py-4 ">
            {allDataFields.length > 0 ? (
              groupedFields()
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center">
                <img src={noData} className="w-48 h-48" />
                <EverTg.Heading3>No Columns</EverTg.Heading3>
              </div>
            )}
          </div>
        )}
      </div>
      {showFormulaWizard && (
        <FormulaWizard
          modalVisible={showFormulaWizard}
          setShowFormulaWizard={setShowFormulaWizard}
          sourceOptions={sourceOptions}
          datasheetMap={datasheetMap}
          selectedFormulaField={selectedFormulaField}
          hasIntermediateVariables={hasIntermediateVariables}
          dataFields={dataFields}
          datasheetId={datasheetId}
          databookId={databookId}
          currentDatasheetName={currentDatasheetName}
          isUpdateView={isUpdateView}
          setSourceVariables={setSourceVariables}
          setHasIntermediateVariables={setHasIntermediateVariables}
          savedTransformations={savedTransformations}
          setIsUpdateView={setIsUpdateView}
          setSelectedFormulaField={setSelectedFormulaField}
          baseVariables={baseVariables}
          setUnsavedEdits={setUnsavedEdits}
          unsavedEdits={unsavedEdits}
          isInitialValidation={isInitialValidation}
          setIsInitialValidation={setIsInitialValidation}
          setUpdateCalculatedFieldDetails={setUpdateCalculatedFieldDetails}
          setSearchKey={setSearchKey}
          selectedSource={selectedSource}
        />
      )}
    </>
  );
}
