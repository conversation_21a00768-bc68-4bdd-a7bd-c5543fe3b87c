import { AlertSquareIcon } from "@everstage/evericons/solid";
import { isEmpty, debounce } from "lodash";
import { observer } from "mobx-react";
import React, {
  useState,
  useContext,
  useEffect,
  useCallback,
  useRef,
} from "react";
import { useQuery, useMutation } from "react-query";
import { useImmer } from "use-immer";

import { RBAC_ROLES } from "~/Enums";
import { useSupabasePresence } from "~/everstage-supabase";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverDrawer,
  EverTg,
  EverLoader,
  message,
  EverButton,
  EverModal,
  EverHotToastNotification,
  toast,
} from "~/v2/components";

import { DataFields } from "./dataFields";
import { SourceTracker } from "./SourceTracker";
import { Transformation } from "./transformations";
import { sources, getDatasheetMap } from "./utils";
import { DatasheetContext } from "../DatasheetStore";
import { UsersList } from "../helperComponents/UsersList";
import useFetchApiWithAuth from "../useFetchApiWithAuth";

const defaultState = {
  type: null,
  id: null,
  name: null,
  databookId: null,
  objects: [],
};

/**
 * EditView component represents a component for editing datasheets.
 * It allows users to edit source data, apply transformations, and save changes.
 *
 * @param {function} setShowGenerateSheetModal - A function to set the visibility of the generate sheet modal.
 * @returns {JSX.Element} JSX element representing the EditView component.
 */
export const EditView = observer(({ setShowGenerateSheetModal }) => {
  const [selectedSource, setSelectedSource] = useImmer(defaultState);
  const [hasSourceChanged, setHasSourceChanged] = useState(false);
  const [isSourceValidationSuccessful, setIsSourceValidationSuccessful] =
    useState(false);
  const [updatedSourceId, setUpdatedSourceId] = useState("");
  const [
    shouldDisplaySupersetNotification,
    setShouldDisplaySupersetNotification,
  ] = useState(true);
  const [baseVariables, setBaseVariables] = useState([]);
  const [sourceVariables, setSourceVariables] = useState([]);
  const [savedTransformationSpec, setSavedTransformationSpec] = useState([]);
  const [transformations, setTransformations] = useState([]);
  const [sourceOptions, setSourceOptions] = useState([]);
  const [datasheetMap, setDatasheetMap] = useState({});
  const [hasIntermediateVariables, setHasIntermediateVariables] =
    useState(false);
  const [isInitialValidation, setIsInitialValidation] = useState(true);
  const [warningModalVisible, setWarningModalVisible] = useState(false);
  const [isValidationInProgress, setIsValidationInProgress] = useState(false);
  const [isValidateButtonDisabled, setIsValidateButtonDisabled] =
    useState(true);
  const [unsavedEdits, setUnsavedEdits] = useState(false);

  const { fetchData } = useFetchApiWithAuth();
  const { hasPermissions } = useUserPermissionStore();
  const datasheetStore = useContext(DatasheetContext);
  const [supersetDetails, setSupersetDetails] = useState(null);
  const { email, accessToken } = useAuthStore();
  const { firstName, lastName, profilePicture } = useEmployeeStore();

  const currentDsSource = useRef(null);

  // Supabase
  const trackingObj = {
    email: email,
    profilePicture: profilePicture,
    name: `${firstName} ${lastName}`,
    online_at: new Date().toISOString(),
    context: "edit",
  };
  const event = ["sync"];
  const presenceConfig = { key: datasheetStore.datasheetId };
  const params = {
    channelPrefix: "online-users-datasheet",
    presenceConfig,
    trackingObj,
  };
  const presence = useSupabasePresence(event, params);

  const handleSupersetNotificationLogic = (value) => {
    setShouldDisplaySupersetNotification(value);
  };

  // Mutations/Queries

  const { refetch, isLoading, isRefetching } = useQuery(
    ["getDatasheetDetailsForEdit", datasheetStore.datasheetId],
    () => {
      return fetchData(
        `/datasheets/${datasheetStore.datasheetId}/details/edit`,
        "GET"
      );
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: () => {
        toast.custom(
          (t) => (
            <EverHotToastNotification
              type="error"
              title="Loading datasheet failed."
              description={"Please contact your admin."}
              toastId={t.id}
            />
          ),
          { position: "top-right", duration: 5000 }
        );
        //close edit drawer when datasheet is not available
        datasheetStore.setEditDrawerVisiblity(false);
      },
      onSettled: (data, error) => {
        if (error) return console.log("Error occurred:", error);
        datasheetStore.setDatasheetDetailsForEdit(data);
        datasheetStore.setDatabookId(data?.databook_id);
      },
    }
  );

  useQuery(
    [
      "getSupersetDetails",
      datasheetStore.datasheetId,
      datasheetStore.databookId,
      accessToken,
    ],
    async () => {
      if (
        datasheetStore.datasheetId &&
        datasheetStore.databookId &&
        accessToken
      ) {
        const postData = {
          databookId: datasheetStore.databookId,
          datasheetId: datasheetStore.datasheetId,
        };
        const requestOptions = {
          method: "POST",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": `application/json`,
          },
          body: JSON.stringify(postData),
        };
        const url = `/superset/is-datasheet-used-in-superset`;
        const response = await fetch(url, requestOptions);
        if (response.ok) {
          return response.json();
        } else {
          throw new Error("Request failed, please try again.");
        }
      }

      return null;
    },
    {
      lazy: true,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: hasPermissions(RBAC_ROLES.MANAGE_ANALYTICS),
      onSuccess: (response) => {
        setSupersetDetails(response);
      },
      onError: (error) => {
        console.log("error", error);
      },
    }
  );

  const validateTransformationsRequest = useMutation(
    () => {
      if (selectedSource.id) {
        const savedTransformationKeys = savedTransformationSpec.map(
          (transformation) => transformation.key
        );
        const body = {
          transformations:
            transformations
              ?.filter((transformation) =>
                savedTransformationKeys.includes(
                  transformation?.transformationSpec?.transformation_id
                )
              )
              ?.map((transformation) => transformation?.transformationSpec) ||
            [],
          // transformations: savedTransformationSpec,
          variables: sourceVariables,
          sourceVariables: [],
          initialValidation: isInitialValidation,
          sourceType: selectedSource.type,
          sourceId: selectedSource.id,
          hasSourceChanged: true,
        };
        return fetchData(
          `datasheets/${datasheetStore.datasheetId}/validate`,
          "POST",
          body
        );
      }
    },
    {
      onSuccess: (data) => {
        setHasIntermediateVariables(true);
        setIsInitialValidation(false);
        //setTransformations([]);
        setTransformations(() => {
          const newTransformations = data?.transformations || [];
          setSavedTransformationSpec(newTransformations);
          return [];
        });

        if (data.variables) setSourceVariables(data.variables);
        setUpdatedSourceId(selectedSource.id);
        setIsSourceValidationSuccessful(true);
        // Update currentDsSource with the latest selected source details
        currentDsSource.current = {
          databookId: selectedSource.databookId,
          id: selectedSource?.id,
          type: selectedSource?.type,
          name: selectedSource?.name || "",
        };
      },
      onError: (error) => {
        message.error(error.message, 3000, "h-auto");
        setHasSourceChanged(false);
        // Revert selectedSource state to the last known valid source details
        setSelectedSource((draft) => {
          draft.databookId = currentDsSource?.current?.databookId;
          draft.id = currentDsSource?.current?.id;
          draft.type = currentDsSource?.current?.type;
          draft.name = currentDsSource?.current?.name || "";
        });
      },
    }
  );

  const { isLoading: getSourceOptionsLoading } = useQuery(
    ["getSourceOptions", datasheetStore?.datasheetId],
    () => {
      if (datasheetStore?.datasheetId) {
        return fetchData(
          `datasheets/source-options?datasheet_id=${datasheetStore.datasheetId}`,
          "GET"
        );
      }
    },
    {
      lazy: true,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      onSuccess: (data) => {
        setDatasheetMap(getDatasheetMap(data?.datasheet || []));
        setSourceOptions(data);
      },
      enabled: !!datasheetStore.datasheetId,
    }
  );

  const { isLoading: getSourceVariablesLoading, refetch: refetchVariables } =
    useQuery(
      ["getSourceVariables", selectedSource.id],
      () => {
        if (selectedSource.id) {
          const apiUrls = {
            object: `spm/custom-objects/${selectedSource.id}/variables`,
            report: `spm/ever-objects/${selectedSource.id}/variables`,
            datasheet: `datasheets/${selectedSource.id}/variables?only-selected=true`,
          };
          return fetchData(apiUrls[selectedSource.type], "GET");
        }
        return [];
      },
      {
        lazy: true,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        onSuccess: (data = []) => {
          if (selectedSource.id) {
            setBaseVariables(data);
            if (selectedSource?.id !== currentDsSource.current.id) {
              hasSourceChanged && validateTransformationsRequest.mutate(data);
            }
          }
        },
        onError: () => {
          if (selectedSource?.id !== currentDsSource.current.id) {
            // Revert selectedSource state to the last known valid source details
            setSelectedSource((draft) => {
              draft.databookId = currentDsSource?.current?.databookId;
              draft.id = currentDsSource?.current?.id;
              draft.type = currentDsSource?.current?.type;
              draft.name = currentDsSource?.current?.name || "";
            });
          }
        },
        // enabled: hasSourceChanged,
      }
    );

  const handleValidateTransformation = useMutation(
    (payload) =>
      handleMutation(
        `datasheets/${datasheetStore.datasheetId}/validate`,
        payload
      ),
    {
      onSuccess: () => {
        onSave();
      },
      onError: (error) => {
        message.error(
          error.message || "An error occurred!",
          3000,
          "h-auto py-2"
        );
        setWarningModalVisible(false);
      },
    }
  );

  const handleDiscard = useMutation(() => {
    return fetchData(
      `datasheet_ninja/${datasheetStore.datasheetId}/flush-draft-details`,
      "POST"
    );
  });

  const handleSave = useMutation(
    () => {
      const body = {
        ...(hasSourceChanged
          ? {
              sourceType: selectedSource.type,
              sourceId: selectedSource.id,
              hasSourceChanged: true,
            }
          : {}),
      };
      return fetchData(
        `datasheets/${datasheetStore.datasheetId}`,
        "PATCH",
        body
      );
    },
    {
      onSuccess: async () => {
        setHasIntermediateVariables(false);
        removeEditQueryParam();
        datasheetStore.setIsDatasheetDetailsRefetching(true);

        datasheetStore
          .refetchDatasheetDetailsForEdit()
          .then(({ data, error }) => {
            if (error) return console.log("Error occurred:", error);
            datasheetStore.setDatasheetDetailsForEdit(data);
            datasheetStore.setDatabookId(data?.databook_id);
            datasheetStore.setIsDatasheetDetailsRefetching(false);
            setShowGenerateSheetModal(true);
          });
      },
      onError: (error) => {
        message.error(
          error?.message || "something went wrong",
          3000,
          "h-auto py-2"
        );
      },
    }
  );

  // Functions/Callbacks
  // Handler for save
  const onSave = useCallback(
    debounce(async () => {
      handleSave.mutate();
    }, 200), // Debounce time in milliseconds
    [handleSave]
  );

  function setInitialSource() {
    if (!isEmpty(datasheetStore.datasheetDetailsForEdit)) {
      setSelectedSource((draft) => {
        draft.databookId =
          datasheetStore.datasheetDetailsForEdit?.source_databook_id;
        draft.id = datasheetStore.datasheetDetailsForEdit?.source_id;
        draft.type = datasheetStore.datasheetDetailsForEdit?.source_type;
        draft.name = datasheetStore.datasheetDetailsForEdit?.name || "";
      });
      currentDsSource.current = {
        databookId: datasheetStore.datasheetDetailsForEdit?.source_databook_id,
        id: datasheetStore.datasheetDetailsForEdit?.source_id,
        type: datasheetStore.datasheetDetailsForEdit?.source_type,
        name: datasheetStore.datasheetDetailsForEdit?.name || "",
      };
      setSourceVariables(
        [
          ...(datasheetStore.datasheetDetailsForEdit?.variables || []),
          ...(datasheetStore.datasheetDetailsForEdit
            ?.additional_source_variables || []),
        ] || []
      );
      setSavedTransformationSpec(
        datasheetStore.datasheetDetailsForEdit?.transformation_spec || []
      );
      //setIsValidateButtonDisabled(
      //  (datasheetStore.datasheetDetailsForEdit?.transformation_spec || [])?.length > 0
      //    ? true
      //    : false
      // );
    }
  }

  const removeEditQueryParam = () => {
    const url = new URL(window.location);
    const searchParams = new URLSearchParams(url.search);
    searchParams.delete("isEditView");
    url.search = searchParams.toString();
    // Use history.replaceState to update the URL without reloading the page
    window.history.replaceState({}, "", url);
    datasheetStore.setEditDrawerVisiblity(false);
    setUnsavedEdits(false);
  };

  const handleClose = () => {
    if (
      !unsavedEdits &&
      transformations.every((transformation) => transformation.isValid) &&
      !hasSourceChanged
    ) {
      removeEditQueryParam();
    } else {
      const modal = EverModal.confirm({
        width: 600,
        title: "Are you sure to close?",
        subtitle: (
          <EverTg.Text>This will discard all changes made.</EverTg.Text>
        ),
        centered: true,
        closable: true,
        confirmationButtons: [
          <EverButton
            key="back"
            color="base"
            onClick={() => {
              handleDiscard.mutate();
              removeEditQueryParam();
              closeModal();
            }}
          >
            Close this page
          </EverButton>,
          <EverButton
            key="submit"
            color="primary"
            onClick={() => {
              closeModal();
            }}
          >
            Continue to edit
          </EverButton>,
        ],
      });
      const closeModal = () => {
        modal.destroy();
      };
    }
  };

  function handleMutation(url, payload) {
    const body = {
      transformations: payload?.transformations || [],
      variables: sourceVariables,
      sourceVariables: baseVariables,
      initialValidation: isInitialValidation,
      sourceType: selectedSource.type,
      sourceId: selectedSource.id,
    };
    return fetchData(url, "POST", body);
  }

  const handleValidateAndSave = () => {
    handleValidateTransformation.mutate({
      transformations:
        transformations.map(
          (transformation) => transformation.transformationSpec
        ) || [],
    });
  };

  // debounce the save and validate button
  const debouncedSaveAndValidate = useCallback(
    debounce(() => {
      handleValidateAndSave();
    }, 300), // Debounce time in milliseconds
    [handleValidateAndSave]
  );

  // useEffects

  useEffect(() => {
    datasheetStore.setDatasheetDetailsForEditIsLoading(
      isLoading || isRefetching
    );
  }, [isLoading, isRefetching]);

  useEffect(() => {
    datasheetStore.setRefetchDatasheetDetailsForEdit(refetch);
  }, []);

  /**
   * Effect to handle closing the edit view if an active sync is present.
   */
  useEffect(() => {
    if (datasheetStore.datasheetDetailsForEdit?.is_active_sync_present) {
      handleClose();
    }
  }, [datasheetStore?.datasheetDetailsForEdit?.is_active_sync_present]);

  /**
   * Effect to update the list of active users editing the datasheet.
   * Filters out the current user and sets the active users in the datasheet store.
   */
  useEffect(() => {
    const arr = [];
    if (presence?.presenceType === "sync") {
      let users = presence?.data[datasheetStore.datasheetId] || [];
      for (const user of users) {
        user.context === "edit" &&
          user.email != email &&
          arr.push({
            email: user.email,
            profilePicture: user.profilePicture,
            name: user.name,
          });
      }
      datasheetStore.setActiveUsers(arr);
    }
  }, [presence]);

  /**
   * Effect to update the selected source's objects when the source type or options change.
   */
  useEffect(() => {
    setSelectedSource((draft) => {
      draft.objects = sourceOptions[selectedSource.type] || [];
    });
  }, [selectedSource.type, sourceOptions, setSelectedSource]);

  /**
   * Effect to initialize the selected source when datasheet details or the setSelectedSource function change.
   */
  useEffect(() => {
    setInitialSource();
  }, [datasheetStore.datasheetDetailsForEdit, setSelectedSource]);

  return (
    <>
      <EverDrawer
        title={
          <div className="flex items-center justify-between pr-8 h-10">
            <div className="flex gap-2 items-center">
              {selectedSource?.name && (
                <>
                  {datasheetStore.datasheetDetailsForEditIsLoading ||
                  datasheetStore.datasheetDetailsForEditIsLoading ? (
                    <EverLoader.Skeleton
                      config={[2]}
                      className="h-[27px] !p-0 !pl-1 !w-36"
                    />
                  ) : (
                    <EverTg.Heading3 className="semi-bold text-ever-base-content">
                      Edit {datasheetStore.datasheetDetailsForEdit?.name}
                    </EverTg.Heading3>
                  )}
                </>
              )}
            </div>
            {hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) && (
              <div className="flex gap-2 items-center">
                <UsersList />
                <EverButton
                  onClick={() => {
                    if (
                      transformations.every(
                        (transformation) => transformation.isValid
                      ) &&
                      isValidateButtonDisabled
                    ) {
                      if (unsavedEdits || hasSourceChanged) {
                        onSave();
                      } else {
                        removeEditQueryParam();
                      }
                    } else {
                      setWarningModalVisible(true);
                    }
                  }}
                  loading={handleSave.isLoading}
                  size="small"
                  disabled={
                    validateTransformationsRequest.isLoading ||
                    getSourceVariablesLoading ||
                    !selectedSource.id ||
                    // if any transformation is invalid, then disable the button
                    !transformations.every(
                      (transformation) => transformation.isValid
                    )
                  }
                >
                  Save
                </EverButton>
              </div>
            )}
          </div>
        }
        headerStyle={{
          paddingTop: "8px",
          paddingBottom: "8px",
        }}
        keyboard={false}
        height="100vh"
        visible={datasheetStore.editDrawerVisiblity}
        destroyOnClose
        onClose={handleClose}
        className="datasheet-edit-drawer"
      >
        {/* <EditDatasheet /> */}

        {hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) ? (
          <div className="flex w-full h-full">
            {datasheetStore.datasheetDetailsForEditIsLoading ||
            datasheetStore.datasheetDetailsForEditIsLoading ||
            getSourceOptionsLoading ||
            getSourceVariablesLoading ? (
              <EverLoader indicatorType="spinner" spinning={true} />
            ) : (
              <div className="flex w-full h-max min-h-full">
                <div className=" w-3/4 flex flex-col gap-4">
                  <SourceTracker
                    setSelectedSource={setSelectedSource}
                    selectedSource={selectedSource}
                    sourceOptions={sourceOptions}
                    datasheetMap={datasheetMap}
                    savedTransformationSpec={savedTransformationSpec}
                    currentDatasheetName={datasheetStore.currentDatasheetName}
                    setHasSourceChanged={setHasSourceChanged}
                    transformations={transformations}
                    refetchVariables={refetchVariables}
                    setIsValidateButtonDisabled={setIsValidateButtonDisabled}
                  />

                  {/* Transformation Manager */}
                  <Transformation
                    sourceVariables={sourceVariables}
                    sources={sources}
                    selectedSource={selectedSource}
                    databookList={sourceOptions?.databook || []}
                    baseVariables={baseVariables}
                    sourceOptions={sourceOptions}
                    datasheetMap={datasheetMap}
                    savedTransformations={savedTransformationSpec || []}
                    datasheetId={datasheetStore.datasheetId}
                    hasIntermediateVariables={hasIntermediateVariables}
                    setSavedTransformationSpec={setSavedTransformationSpec}
                    setSourceVariables={setSourceVariables}
                    setHasIntermediateVariables={setHasIntermediateVariables}
                    setTransformations={setTransformations}
                    transformations={transformations}
                    currentDatasheetName={datasheetStore.currentDatasheetName}
                    setUnsavedEdits={setUnsavedEdits}
                    unsavedEdits={unsavedEdits}
                    isInitialValidation={isInitialValidation}
                    setIsInitialValidation={setIsInitialValidation}
                    setIsValidateButtonDisabled={setIsValidateButtonDisabled}
                    isValidateButtonDisabled={isValidateButtonDisabled}
                    setIsValidationInProgress={setIsValidationInProgress}
                    isValidationInProgress={isValidationInProgress}
                    supersetDetails={supersetDetails}
                    updatedSourceId={updatedSourceId}
                    hasSourceChanged={hasSourceChanged}
                    isSourceValidationSuccessful={isSourceValidationSuccessful}
                    setIsSourceValidationSuccessful={
                      setIsSourceValidationSuccessful
                    }
                  />
                </div>
                <div className=" w-1/4">
                  <DataFields
                    dataFields={sourceVariables}
                    baseVariables={baseVariables}
                    setSourceVariables={setSourceVariables}
                    sourceOptions={sourceOptions}
                    datasheetMap={datasheetMap}
                    datasheetId={datasheetStore.datasheetId}
                    databookId={datasheetStore.databookId}
                    hasIntermediateVariables={hasIntermediateVariables}
                    currentDatasheetName={datasheetStore.currentDatasheetName}
                    setHasIntermediateVariables={setHasIntermediateVariables}
                    savedTransformations={savedTransformationSpec}
                    setUnsavedEdits={setUnsavedEdits}
                    unsavedEdits={unsavedEdits}
                    isInitialValidation={isInitialValidation}
                    setIsInitialValidation={setIsInitialValidation}
                    updateCalculatedFieldDetails={
                      datasheetStore.updateCalculatedFieldDetails
                    }
                    setUpdateCalculatedFieldDetails={
                      datasheetStore.setUpdateCalculatedFieldDetails
                    }
                    isValidationInProgress={isValidationInProgress}
                    setIsValidationInProgress={setIsValidationInProgress}
                    supersetDetails={supersetDetails}
                    selectedSource={selectedSource}
                    shouldDisplaySupersetNotification={
                      shouldDisplaySupersetNotification
                    }
                    handleSupersetNotificationLogic={
                      handleSupersetNotificationLogic
                    }
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full w-full">
            <AlertSquareIcon className="text-ever-warning w-12 h-12" />
            <EverTg.Heading3 className="text-ever-base-content">
              Please ask your administrator to access this page.
            </EverTg.Heading3>
          </div>
        )}
      </EverDrawer>
      <EverModal
        visible={warningModalVisible}
        onCancel={() => setWarningModalVisible(false)}
        width={600}
        centered
        closable
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2 items-center">
            <AlertSquareIcon className="text-ever-warning w-12 h-12" />
            <EverTg.Heading3 className="text-ever-base-content">
              Some transformation changes have not been validated
            </EverTg.Heading3>
            <EverTg.Text className="text-ever-base-content-mid">
              How do you want to proceed?
            </EverTg.Text>
          </div>
          <div className="flex gap-2 items-center justify-center">
            <EverButton
              color="base"
              onClick={() => {
                setWarningModalVisible(false);
              }}
            >
              Continue to edit
            </EverButton>
            <EverButton
              color="primary"
              onClick={debouncedSaveAndValidate}
              loading={handleValidateTransformation.isLoading}
              // disabled the button if the save is in progress
              disabled={
                handleValidateTransformation.isLoading || handleSave.isLoading
              }
            >
              Validate and save
            </EverButton>
          </div>
        </div>
      </EverModal>
    </>
  );
});
