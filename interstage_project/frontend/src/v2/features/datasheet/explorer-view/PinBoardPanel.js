import { PinboardIcon, MinusCircleIcon } from "@everstage/evericons/duotone";
import { ChevronDownArrowIcon } from "@everstage/evericons/solid";
import { motion } from "framer-motion";
import { observer } from "mobx-react";
import React, { useState, useContext, useEffect } from "react";
import { useQuery, useMutation } from "react-query";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverTg, EverTooltip, message } from "~/v2/components";

import { DsDisplayCard } from "./helperComponent/DsDisplayCard";
import { DatasheetContext } from "../DatasheetStore";
import { getPinnedList, setPinAction } from "../restApi";

/**
 * PinBoardPanel component displays a panel for managing pinned items.
 *
 * @returns {JSX.Element} JSX element representing the PinBoardPanel component.
 */
export const PinBoardPanel = observer(() => {
  // State for managing the panel's open/close state and pinned items
  const [isOpen, setIsOpen] = useState(true);
  const { hasPermissions } = useUserPermissionStore();
  const datasheetStore = useContext(DatasheetContext);
  const { accessToken } = useAuthStore();
  const { data, refetch } = useQuery(
    ["getPinnedList"],
    () => {
      return getPinnedList(accessToken);
    },
    {
      lazy: true,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      onSettled: (_, error) => {
        console.log("error", error);
      },
    }
  );
  const unPinRequest = useMutation(
    (id) => setPinAction(accessToken, id, "unpin"),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.toString());
      },
      onSuccess: async (res) => {
        if (res.message === "Datasheet unpinned successfully") {
          refetch();
        }
      },
    }
  );

  useEffect(() => {
    if (data?.pinned_datasheets) {
      datasheetStore.setPinnedDatasheetList(data?.pinned_datasheets);
    } else {
      datasheetStore.setPinnedDatasheetList([]);
    }
  }, [data]);

  useEffect(() => {
    datasheetStore.setPinnedRefetchFn(refetch);
  }, [refetch]);

  return (
    <div
      className="px-1 flex flex-col gap-2 cursor-pointer"
      onClick={() => {
        setIsOpen(!isOpen);
      }}
    >
      {/* Panel header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {/* Pinboard icon */}
          <PinboardIcon className="w-[18px] h-[18px] text-ever-chartColors-5" />
          {/* Text label */}
          <EverTg.Text className="text-sm font-semibold">Pinned</EverTg.Text>
        </div>
        {/* Toggle button for opening/closing the panel */}
        {datasheetStore.pinnedDatasheetList?.length > 0 && (
          <div className="w-6 h-6 flex items-center border-solid border border-transparent rounded justify-center hover:bg-ever-base-200 hover:border-ever-base-400">
            <ChevronDownArrowIcon
              className={`w-5 h-5 transition-transform duration-400 ease  ${
                isOpen
                  ? "rotate-0 text-ever-base-content"
                  : "-rotate-90 text-ever-base-content-mid"
              }`}
            />
          </div>
        )}
      </div>
      {/* Panel content */}
      <motion.div
        key="pinBoardPanel"
        variants={{
          open: {
            height: "auto",
            opacity: 1,
          },
          closed: { height: 0, opacity: 0 },
        }}
        initial={"closed"}
        animate={isOpen ? "open" : "closed"}
        exit={{ height: 0, opacity: 0 }}
        transition={{
          height: { duration: 0.4 },
          opacity: { duration: 0.4 },
        }}
      >
        <motion.ul
          variants={{
            open: {
              transition: { staggerChildren: 0.07, delayChildren: 0.5 },
            },
            closed: {
              transition: { staggerChildren: 0.05, staggerDirection: -1 },
            },
          }}
          initial={"closed"}
          animate={isOpen ? "open" : "closed"}
          className="p-0 m-0"
        >
          {datasheetStore.pinnedDatasheetList?.length > 0 ? (
            // Render pinned items
            datasheetStore.pinnedDatasheetList.map((item) => (
              <motion.li
                variants={{
                  open: {
                    y: 0,
                    opacity: 1,
                    transition: {
                      y: { stiffness: 1000, velocity: -100 },
                    },
                  },
                  closed: {
                    y: 50,
                    opacity: 0,
                    transition: {
                      y: { stiffness: 1000 },
                    },
                  },
                }}
                key={`pinned-list-${item.datasheet_id}`}
                className="list-none"
              >
                <DsDisplayCard
                  item={item}
                  key={item.datasheet_id}
                  moreOption={
                    hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) && (
                      <div
                        className="flex items-center justify-center"
                        onClick={async () => {
                          await unPinRequest.mutate(item.datasheet_id);
                        }}
                      >
                        <EverTooltip title="Unpin">
                          <MinusCircleIcon className="w-6 h-6 text-ever-base-content-mid" />
                        </EverTooltip>
                      </div>
                    )
                  }
                  setDatasheetId={datasheetStore.setDatasheetId}
                  handleClick={() => {
                    datasheetStore.viewByData?.some((databook) =>
                      databook.datasheets.some((ds) => {
                        if (ds.datasheet_id === item.datasheet_id) {
                          datasheetStore.setOpenedAccordionItem(databook.id);
                          return true;
                        }
                        return false;
                      })
                    );
                  }}
                />
              </motion.li>
            ))
          ) : (
            // If there are no pinned items, display a message
            <EverTg.Description>
              Keep your most important sheets within reach for your team
            </EverTg.Description>
          )}
        </motion.ul>
      </motion.div>
    </div>
  );
});
