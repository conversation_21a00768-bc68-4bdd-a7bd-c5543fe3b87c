import React, { useState, useEffect, useRef, lazy, Suspense } from "react";
import React<PERSON><PERSON>Parser from "react-html-parser";

import { EverTg } from "~/v2/components";

// Icon registry to manage lazy-loaded components
const iconRegistry = {};

// Factory function to create lazy-loaded icon components
const createLazyIcon = (iconName, iconType) => {
  if (!iconRegistry[iconName]) {
    iconRegistry[iconName] = lazy(() =>
      (iconType === "outlined"
        ? import("@everstage/evericons/outlined")
        : import("@everstage/evericons/duotone")
      )
        .then((module) => ({
          default: module[iconName] || (() => null),
        }))
        .catch(() => ({
          default: () => null,
        }))
    );
  }
  return iconRegistry[iconName];
};

const KeywordCard = (props) => {
  const { result } = props;
  const { document, highlights } = result;
  const { module, icon, breadcumb } = document;
  const cardRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [LazyIcon, setLazyIcon] = useState(null);

  useEffect(() => {
    const card = cardRef.current;

    // Setup intersection observer to detect when card is visible
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);

        // When card becomes visible, set up the lazy icon component
        if (entry.isIntersecting && icon.name && !LazyIcon) {
          setLazyIcon(createLazyIcon(icon.name, icon.type));
        }
      },
      { threshold: 0.1 } // Trigger when at least 10% of the card is visible
    );

    if (card) {
      observer.observe(card);
    }

    return () => {
      if (card) {
        observer.unobserve(card);
      }
    };
  }, [icon.name, LazyIcon]);

  return (
    <div className="flex items-center gap-2 py-2" ref={cardRef}>
      {isVisible && LazyIcon && (
        <div className="w-8 h-8 flex items-center justify-center bg-ever-primary-lite text-ever-primary rounded-lg rounded-tl-none">
          <Suspense
            fallback={
              <div className="w-6 h-6 bg-ever-base-200 animate-pulse rounded-full" />
            }
          >
            <LazyIcon className="text-ever-sidebar-base-content w-6 h-6" />
          </Suspense>
        </div>
      )}
      <div className="flex items-center gap-1">
        {breadcumb &&
          breadcumb.map((item, index) => (
            <React.Fragment key={index}>
              <EverTg.SubHeading4 className="text-ever-base-content-low">
                {/* {highlights?.breadcumb
                  ? ReactHTMLParser(highlights.breadcumb[index].snippet)
                  : item} */}
                {item}
              </EverTg.SubHeading4>
              <EverTg.SubHeading4 className="text-ever-base-content-low">
                /
              </EverTg.SubHeading4>
            </React.Fragment>
          ))}
        <EverTg.Heading3 className="text-ever-base-content">
          {highlights?.module
            ? ReactHTMLParser(highlights.module.snippet)
            : module}
        </EverTg.Heading3>
      </div>
    </div>
  );
};

export default KeywordCard;
