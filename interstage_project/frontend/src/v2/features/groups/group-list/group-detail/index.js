import {
  EditPencilIcon,
  CheckIcon,
  XCloseIcon,
  EditPencilAltIcon,
} from "@everstage/evericons/outlined";
import { Col, Row } from "antd";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";

import {
  <PERSON><PERSON>utton,
  EverGroupAvatar,
  EverHotToastMessage,
  EverInput,
  EverTg,
  toast,
  EverVirtualizerDynamic,
} from "~/v2/components";
import { sadCat } from "~/v2/images";

import { ContextMenu } from "./ContextMenu";
import { createOrUpdateGroup } from "../../restApi";

const noMembers = (isSystemGenerated) => {
  return (
    <div className="flex justify-center items-center w-full h-full transform -translate-y-20">
      <div className="flex flex-col text-center">
        <img src={sadCat} alt="empty icon" />
        <EverTg.Heading2 className="mb-3">
          No users in this group!
        </EverTg.Heading2>
        {!isSystemGenerated && (
          <EverTg.Text className="text-ever-base-content-mid">
            {`Click on 'Edit' to add users to this group.`}
          </EverTg.Text>
        )}
      </div>
    </div>
  );
};

const getMembers = (member, index) => (
  <div
    key={index}
    className="w-full flex py-2 items-center h-14 cursor-default"
  >
    <EverGroupAvatar
      size="large"
      avatars={[
        {
          firstName: member?.firstName,
          lastName: member?.lastName,
          image: member.profilePicture,
          className: "h-[42px] w-[42px]",
        },
      ]}
    />
    <div className="ml-2 flex flex-col h-full">
      <EverTg.SubHeading4 className="text-ever-base-content mb-[2px]">{`${member?.firstName} ${member?.lastName}`}</EverTg.SubHeading4>
      <EverTg.Text className="text-ever-base-content-low">
        {member?.employeeEmailId}
      </EverTg.Text>
    </div>
  </div>
);

const membersList = (members) => {
  return (
    <Col className="w-[548px] h-full bg-ever-base">
      <Row className="h-full">
        <EverTg.SubHeading4 className="h-[44px]">
          {members?.length} Group members
        </EverTg.SubHeading4>
        <div className="h-full w-full">
          <EverVirtualizerDynamic
            data={members}
            columns={1}
            itemHeight={56}
            getOption={getMembers}
            listHeightClass="h-[calc(100%-120px)]"
          />
        </div>
      </Row>
    </Col>
  );
};

export const GroupDetail = observer((props) => {
  const {
    selectedIndex,
    setSelectedIndex,
    groupListRefetch,
    store,
    accessToken,
    handleEditGroup,
  } = props;
  const [editGroupName, setEditGroupName] = useState(false);
  const [newGroupName, setNewGroupName] = useState("");

  const { groupFilteredList } = store;

  const selectedTeam = groupFilteredList[selectedIndex];
  const teamName = selectedTeam?.userGroupName;

  const members = selectedTeam?.allMembers || [];
  const isSystemGenerated = selectedTeam?.createdBy === "Everstage";
  const createdBy = !isSystemGenerated
    ? `${selectedTeam?.createdBy?.firstName || ""} ${
        selectedTeam?.createdBy?.lastName || ""
      }`
    : selectedTeam?.createdBy;

  const [avatars, setAvatars] = useState([]);

  useEffect(() => {
    members &&
      setAvatars(
        members?.map((ele) => {
          return {
            firstName: ele.firstName,
            lastName: ele.lastName,
            image: ele.profilePicture,
          };
        })
      );
  }, [members]);

  return (
    <div className="w-full h-full">
      <div className="flex justify-between bg-ever-primary-lite w-full h-max items-center px-6 py-3">
        <div className="flex gap-4">
          <div className="flex flex-col">
            {editGroupName ? (
              <div className="flex">
                <EverInput.Group
                  onChange={(event) => {
                    setNewGroupName(event.target.value);
                  }}
                  onBlur={() => {
                    setEditGroupName(false);
                  }}
                  defaultValue={teamName}
                  controls={[
                    <EverButton.Icon
                      icon={<CheckIcon />}
                      className="rounded-none"
                      key="Yes"
                      size="small"
                      onMouseDown={async () => {
                        setEditGroupName(false);
                        if (newGroupName !== "") {
                          const body = {
                            userGroupId: selectedTeam?.userGroupId,
                            userGroupName: newGroupName,
                          };
                          const result = await createOrUpdateGroup(
                            body,
                            accessToken
                          );

                          if (result.status === "FAILED") {
                            const errorMsg = result?.reason
                              ? result?.reason
                              : "Group name update failed";
                            toast.custom(() => (
                              <EverHotToastMessage
                                description={errorMsg}
                                type="error"
                              />
                            ));
                          } else {
                            groupListRefetch();
                          }
                        }
                      }}
                    />,
                    <EverButton.Icon
                      icon={<XCloseIcon />}
                      size="small"
                      key="no"
                      className="rounded-tl-none rounded-bl-none"
                      onMouseDown={() => {
                        setEditGroupName(false);
                      }}
                    />,
                  ]}
                  className="flex w-[250px]"
                />
              </div>
            ) : (
              <>
                {isSystemGenerated ? (
                  <EverTg.Heading3>{teamName}</EverTg.Heading3>
                ) : (
                  <div className="flex gap-2 items-center">
                    <EverTg.Heading3>{teamName}</EverTg.Heading3>
                    <EditPencilIcon
                      className="size-4 text-ever-base-content-mid cursor-pointer"
                      onClick={() => {
                        setEditGroupName(true);
                      }}
                    />
                  </div>
                )}
              </>
            )}
            <EverTg.Text className="text-ever-base-content-mid">
              Created by {createdBy}
            </EverTg.Text>
          </div>
          <EverGroupAvatar
            groupMaxCount={3}
            limitInPopover={10}
            avatars={avatars}
            size="large"
          />
        </div>
        <div className="flex h-8 gap-4 pt-2 items-center">
          {!isSystemGenerated && (
            <EverButton
              disabled={isSystemGenerated}
              type="ghost"
              color="base"
              className="bg-ever-base-25"
              onClick={() => {
                // setEditGroupName(false);
                handleEditGroup();
              }}
              prependIcon={<EditPencilAltIcon />}
              size="small"
            >
              Edit
            </EverButton>
          )}
          <ContextMenu
            selectedTeam={selectedTeam}
            setSelectedIndex={setSelectedIndex}
            groupListRefetch={groupListRefetch}
            store={store}
            accessToken={accessToken}
            isSystemGenerated={isSystemGenerated}
          />
        </div>
      </div>
      <div className="p-8 h-full">
        {members?.length > 0
          ? membersList(members)
          : noMembers(isSystemGenerated)}
      </div>
    </div>
  );
});
