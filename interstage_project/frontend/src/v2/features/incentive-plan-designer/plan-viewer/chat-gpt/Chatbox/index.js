import { message } from "antd";
import { get, set } from "idb-keyval";
import { isEmpty, last } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { twMerge } from "tailwind-merge";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useAbortController } from "~/v2/components/custom-hooks";
import {
  deleteIcon,
  faqCircleIcon,
  sendIcon,
  everstageLogoIcon,
} from "~/v2/images";
import EverProgress from "~/v2/legacy/components/EverProgress";

import { FAQComponent, TypingBubble } from "./components";
import { QUERY, RESPONSE } from "./constants";
import styles from "./styles.module.scss";
import { getChatResponseFromGPT } from "../services";

function Chatbox({ styleChatBubble, styleCard, styleInput, planId, emailId }) {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");
  const messagesEndRef = useRef(null);
  const [chatWaiting, setChatWaiting] = useState(false);
  const { accessToken, email: loggedInUserEmail } = useAuthStore();
  const [showFAQ, setShowFAQ] = useState(false);
  const { signal, abort } = useAbortController();
  const [initialLoading, setInitialLoading] = useState(true);
  const [hash, setHash] = useState("");

  const scrollToBottom = () => {
    // smooth scroll to bottom of messages
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    // abort all pending requests on unmount
    return () => {
      abort();
    };
  }, []);

  // ChatGPT chat API call
  useEffect(() => {
    // make this call only for the latest message of type QUERY

    if (isEmpty(messages)) return;
    if (last(messages).type === RESPONSE) return;
    if (chatWaiting) return;

    (async () => {
      setChatWaiting(true);
      const data = await getChatResponseFromGPT(
        {
          emailId,
          planId,
          accessToken,
          messages: messages.map((item) => item.content), // Array of strings
        },
        signal
      );

      setChatWaiting(false);

      if (data.error) {
        message.error(data.data);
        return;
      }

      setMessages((prev) => [
        ...prev,
        { content: data.data || "", type: RESPONSE },
      ]);

      // update chatHistory
      set(
        hash,
        JSON.stringify([
          ...messages,
          { content: data.data || "", type: RESPONSE },
        ])
      )
        .then(() => {
          console.log("Chat history updated");
        })
        .catch((err) => {
          console.log(err);
        });
    })();
  }, [messages, accessToken, emailId, planId]);

  useEffect(scrollToBottom, [messages, chatWaiting]);

  useEffect(() => {
    get(hash)
      .then((val) => {
        if (val) {
          setMessages(JSON.parse(val));
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setInitialLoading(false);
      });
  }, [hash]);

  useEffect(() => {
    getSHA256Hash(emailId + "-" + planId + "-" + loggedInUserEmail)
      .then((hash) => {
        setHash(hash);
      })
      .catch((err) => {
        console.log(err);
      });
  }, [emailId, planId, loggedInUserEmail]);

  const handleSend = (text) => {
    // if chat is waiting for response, don't send anything
    if (chatWaiting) return;

    // if text is passed, send that text
    if (text) {
      if (text.trim() === "") return;
      const newMessage = { content: text, type: QUERY };
      setMessages((prev) => [...prev, newMessage]);
      return;
    }

    if (input.trim() === "") return;

    const newMessage = { content: input, type: QUERY };
    setMessages((prev) => [...prev, newMessage]);
    clearInput();
  };

  const clearInput = () => {
    setInput("");
  };

  const handleInput = (e) => {
    setInput(e.target.value);
  };

  const clearChat = () => {
    if (chatWaiting) return;
    setMessages([]);
    set(hash, JSON.stringify([]))
      .then(() => {
        console.log("Chat history cleared");
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // const getMenu = () => {
  //   return (
  //     <Menu>
  //       <Menu.Item key="0" onClick={clearChat}>
  //         Clear Chat
  //       </Menu.Item>
  //     </Menu>
  //   );
  // };

  return (
    <div
      style={styleCard}
      className={twMerge(
        "bg-gradient-to-br from-ever-chartColors-38 via-ever-chartColors-35 to-ever-chartColors-37",
        // "bg-[conic-gradient(at_top_right,_var(--gradient-stops))] from-ever-chartColors-35 via-ever-chartColors-37 to-ever-chartColors-38",
        styles.chat
      )}
    >
      {initialLoading ? (
        <EverProgress type="spinner" />
      ) : (
        <div className={`${styles.messages}`}>
          {messages.length === 0 || showFAQ ? (
            <FAQComponent
              showFAQ={showFAQ}
              setShowFAQ={setShowFAQ}
              chatWaiting={chatWaiting}
              handleSend={handleSend}
            />
          ) : (
            <div className={`${styles.messageList}`}>
              {messages.map((message, idx) => (
                <div
                  key={idx}
                  className={`flex mb-2 ${
                    message.type === QUERY
                      ? `${styles.scaleUpBr} flex-row-reverse`
                      : `${styles.scaleUpBl} flex-row pl-2`
                  }`}
                >
                  {message.type === RESPONSE && (
                    <div>
                      <img src={everstageLogoIcon} />
                    </div>
                  )}
                  <div
                    className={`flex flex-col ${
                      message.type === QUERY ? "items-end" : ""
                    }`}
                  >
                    <div className="mx-[10px] from-neutral-900 font-medium">
                      {message.type === QUERY ? "You" : "Ever AI"}
                    </div>
                    <div
                      style={styleChatBubble}
                      className={`${styles.messageItem} ${
                        message.type === QUERY
                          ? `${styles.itemPrimary}`
                          : `${styles.itemSecondary}`
                      }`}
                    >
                      <ReactMarkdown
                        className={styles.markdown}
                        remarkPlugins={[remarkGfm]}
                      >
                        {message.content.length > 0
                          ? message.content
                          : "Sorry, there is no plan document available for this plan."}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              ))}
              {chatWaiting && (
                <div className="flex">
                  <div>
                    <img src={everstageLogoIcon} />
                  </div>
                  <div className="flex flex-col">
                    <div className="ml-[10px] from-neutral-900 font-medium">
                      Ever AI
                    </div>
                    <TypingBubble />
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          )}

          <div className={`${styles.activityBox}`}>
            <div className={`${styles.messageInput}`}>
              <input
                style={styleInput}
                type="text"
                placeholder="Ask Ever AI"
                value={input}
                onChange={handleInput}
                onKeyUp={(e) => {
                  // send on enter
                  if (e.keyCode === 13) {
                    handleSend();
                  }
                }}
              />
              <button
                className={styles.sendButton}
                type="button"
                onClick={() => handleSend()}
              >
                <img
                  src={sendIcon}
                  style={{
                    cursor: "pointer",
                  }}
                />
              </button>

              {messages.length > 0 && (
                <>
                  <button
                    className={styles.faqButton}
                    type="button"
                    onClick={() => setShowFAQ((prev) => !prev)}
                  >
                    <img
                      src={faqCircleIcon}
                      style={{
                        cursor: "pointer",
                      }}
                    />
                  </button>
                  <button
                    className={styles.clearButton}
                    type="button"
                    onClick={clearChat}
                  >
                    <img
                      src={deleteIcon}
                      style={{
                        cursor: "pointer",
                      }}
                    />
                  </button>

                  {/* <Dropdown overlay={getMenu()} placement="topRight" arrow>
                    <button type="button">
                      <IconFactory name="horizontalEllipsis" />
                    </button>
                  </Dropdown> */}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Chatbox;

// This function takes avg. 15ms to run .
const getSHA256Hash = async (input) => {
  const textAsBuffer = new TextEncoder().encode(input);
  const hashBuffer = await window.crypto.subtle.digest("SHA-256", textAsBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hash = hashArray
    .map((item) => item.toString(16).padStart(2, "0"))
    .join("");
  return hash;
};
