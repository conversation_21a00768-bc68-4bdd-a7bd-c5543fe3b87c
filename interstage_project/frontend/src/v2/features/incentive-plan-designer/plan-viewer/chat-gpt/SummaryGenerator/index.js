import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

import { notAvailable } from "~/v2/images";
import EverProgress from "~/v2/legacy/components/EverProgress";

import styles from "./styles.module.scss";

function SummaryGenerator({ initialLoading, summaryData }) {
  return (
    <div
      className={`h-full px-6 pt-6 pb-4 overflow-y-scroll ${styles.summaryContainer}`}
    >
      {initialLoading ? (
        <EverProgress type="spinner" />
      ) : (
        <>
          {summaryData ? (
            <div className="mb-2 text-[#737E99] font-medium">Summary</div>
          ) : (
            <div className="flex text-[#737E99] justify-center items-center h-full">
              <img
                className="!mr-1"
                src={notAvailable}
                style={{
                  width: " 24px",
                  height: "24px",
                }}
              />
              No summary available
            </div>
          )}
          <div className={styles.summaryMarkdownContainer}>
            <ReactMarkdown
              className={styles.markdown}
              remarkPlugins={[remarkGfm]}
            >
              {summaryData}
            </ReactMarkdown>
          </div>
        </>
      )}
    </div>
  );
}

export default SummaryGenerator;
