import { gql, useQuery } from "@apollo/client";
import { File02Icon } from "@everstage/evericons/outlined";
import { Col, Row, Tooltip } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import React, { Fragment, useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  COMPONENTS,
  DOCUMENT_VIEW_SOURCE,
  RBAC_ROLES,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverB<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onGroup,
  EverDrawer,
  EverTg,
} from "~/v2/components";

import ChatGPT from "./chat-gpt";
import { getPlanSummaryFromGPT } from "./chat-gpt/services";
import PlanDocuments from "./plan-documents";
import PlanReadOnly from "./plan-read-only-component";
import PlanBuilderStore from "../plan-builder/store";

export const AdditonalConfigContext = React.createContext();

const PlanViewerWrapper = (props) => {
  const {
    planId,
    planName,
    emailId,
    buttonTooltipTitle,
    periodLabel,
    textStyle = {},
    isSettlement = false,
  } = props;
  const [drawerVisible, setDrawerVisible] = useState();
  const [selectedPage, setSelectedPage] = useState("planSummary");
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const { accessToken } = useAuthStore();

  const onCloseDrawer = () => {
    setDrawerVisible(false);
  };
  const radioOptions = [
    { label: "Plan Summary", value: "planSummary" },
    { label: "Plan Document", value: "planDocument" },
  ];

  if (clientFeatures?.showChatgpt) {
    radioOptions.push({ label: "Ever AI", value: "ChatGPT" });
  }

  const onRadioChange = (val) => {
    setSelectedPage(val);
  };

  return (
    <Fragment>
      {isSettlement ? (
        <EverButton
          // calling stopPropagation() from synthetic event(onClick) will not stop the real event
          // to avoid ag-grid row click, ref is used here which will stop real event
          ref={(ref) => {
            if (!ref) {
              return null;
            }
            ref.onclick = (event) => {
              event.stopPropagation();
              setDrawerVisible(true);
              sendAnalyticsEvent(
                accessToken,
                ANALYTICS_EVENTS.VIEW_PLAN_DETAILS,
                {
                  [ANALYTICS_PROPERTIES.PAYEE_NAME]: emailId,
                  [ANALYTICS_PROPERTIES.PERIOD]: periodLabel,
                }
              );
            };

            return null;
          }}
          type="link"
          className="!p-0"
          size="small"
          icon={
            !isEmpty(buttonTooltipTitle) ? (
              <Tooltip title={buttonTooltipTitle}>
                <File02Icon
                  className="w-5 h-5 text-ever-base-content-mid hover:text-ever-primary"
                  style={{ ...textStyle }}
                />
              </Tooltip>
            ) : (
              <File02Icon
                className="w-5 h-5 text-ever-base-content-mid hover:text-ever-primary"
                style={{ ...textStyle }}
              />
            )
          }
        />
      ) : (
        <EverButton
          type="link"
          className="!p-0 w-full justify-start [&>div]:min-w-0"
          size="small"
          onClick={() => setDrawerVisible(true)}
          title={planName}
        >
          <EverTg.Text className="truncate">{planName}</EverTg.Text>
        </EverButton>
      )}

      <EverDrawer
        title={
          <Row>
            <Col span={8} style={{ marginTop: 7 }}>
              {selectedPage === "ChatGPT" ? (
                <span style={{ color: "#fff" }}>ChatGPT</span>
              ) : (
                "View Plan Details"
              )}
            </Col>
            <Col span={8}>
              <Row justify="center">
                <EverButtonGroup
                  className="bg-ever-base-200"
                  activeBtnType="text"
                  activeBtnColor="primary"
                  defActiveBtnIndex={radioOptions.findIndex(
                    (option) => option.value === selectedPage
                  )}
                >
                  {radioOptions.map((option, index) => (
                    <EverButton
                      key={`radiobtn_${index}`}
                      onClick={() => onRadioChange(option.value)}
                    >
                      {option.label}
                    </EverButton>
                  ))}
                </EverButtonGroup>
              </Row>
            </Col>
          </Row>
        }
        placement="top"
        closable={true}
        onClose={onCloseDrawer}
        visible={drawerVisible}
        height="100vh"
        headerStyle={{
          padding: "15px 24px",
        }}
        destroyOnClose={true}
        bodyStyle={{ padding: 0, paddingTop: "32px" }}
      >
        <PlanViewer
          planId={planId}
          emailId={emailId}
          onCloseDrawer={onCloseDrawer}
          selectedPage={selectedPage}
        />
      </EverDrawer>
    </Fragment>
  );
};

export default PlanViewerWrapper;

const GET_PAYEES = gql`
  query CommissionPlan(
    $planId: UUID!
    $component: String
    $expressionVersion: String
  ) {
    commissionPlanCriteriaBasicDetails(
      planId: $planId
      component: $component
      expressionVersion: $expressionVersion
    ) {
      planName
      planType
      planStartDate
      planEndDate
      isDraft
      planCriterias {
        criteriaId
        criteriaName
        criteriaType
        criteriaDescription
        criteriaDisplayOrder
        criteriaData
        criteriaColumn
        criteriaConfig {
          criteriaIsHidden
          showDoNothing
          intermediateOnly
          sortCols
        }
      }
      settlementRules
      additionalConfig {
        name
        type
        values {
          email
          value
        }
      }
    }
  }
`;

export const PlanViewer = observer((props) => {
  const {
    planId,
    selectedPage,
    emailId,
    viewSource = "",
    documentPath = null,
  } = props;
  const { hasPermissions } = useUserPermissionStore();
  const { accessToken } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const expressionBoxVersion = clientFeatures.expressionboxVersion || "v1";

  const [summaryData, setSummaryData] = useState("");
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    if (!clientFeatures?.showChatgpt) {
      setInitialLoading(false);
      setSummaryData(null);
      return null;
    }

    let abortController = new AbortController();
    (async () => {
      const data = await getPlanSummaryFromGPT(
        {
          emailId,
          planId,
          accessToken,
        },
        abortController.signal
      );
      setSummaryData(data.data);
      setInitialLoading(false);
    })();

    return () => {
      abortController.abort();
    };
  }, []);

  // use observable props
  const planBuilderStore = useLocalStore(
    (observableProps) => new PlanBuilderStore(observableProps),
    {
      planId: planId,
      userCanViewHiddenCriterias: hasPermissions(
        RBAC_ROLES.VIEW_HIDDENCRITERIA
      ),
    }
  );

  const { data } = useQuery(GET_PAYEES, {
    variables: {
      planId: planId,
      component: COMPONENTS.PAYOUTS_STATEMENTS,
      expressionVersion: expressionBoxVersion,
    },
    fetchPolicy: "no-cache",
    skip: viewSource === DOCUMENT_VIEW_SOURCE,
  });

  let additionalConfig = null;

  if (data?.commissionPlanCriteriaBasicDetails?.[0]?.additionalConfig) {
    additionalConfig =
      data.commissionPlanCriteriaBasicDetails[0].additionalConfig;
    additionalConfig = additionalConfig.map((obj) => {
      let filteredConfig = obj.values.find((value) => {
        return value.email == emailId;
      });
      if (filteredConfig) {
        return { name: obj.name, value: filteredConfig.value };
      } else {
        return { name: obj.name, value: "NO-DATA" };
      }
    });
  }

  useEffect(() => {
    if (data && data.commissionPlanCriteriaBasicDetails) {
      if (data.commissionPlanCriteriaBasicDetails[0].planCriterias) {
        let criteriaList = [];
        data.commissionPlanCriteriaBasicDetails[0].planCriterias.forEach(
          (x) => {
            criteriaList.push({
              id: x.criteriaId,
              name: x.criteriaName,
              type: x.criteriaType,
              description: x.criteriaDescription,
              displayOrder: x.criteriaDisplayOrder,
              data: JSON.parse(x.criteriaData),
              criteriaColumns: x.criteriaColumn,
              criteriaConfig: {
                showDoNothing: x.criteriaConfig.showDoNothing
                  ? x.criteriaConfig.showDoNothing
                  : false,
                criteriaIsHidden: x.criteriaConfig.criteriaIsHidden
                  ? x.criteriaConfig.criteriaIsHidden
                  : false,
                intermediateOnly: x.criteriaConfig.intermediateOnly
                  ? true
                  : false,
                sortCols: x.criteriaConfig.sortCols,
              },
            });
          }
        );
        planBuilderStore.setCriteriasInitial(criteriaList);
        planBuilderStore.setCriteriasR(cloneDeep(criteriaList));
        planBuilderStore.setCriteriasW(cloneDeep(criteriaList));
      }

      if (data.commissionPlanCriteriaBasicDetails[0].settlementRules) {
        let settlementRuleList = [];
        data.commissionPlanCriteriaBasicDetails[0].settlementRules.forEach(
          (rule) => {
            settlementRuleList.push({
              settlementRuleId: rule?.settlementRuleId,
              name: rule?.name,
              type: rule?.amountExpr?.type,
              amountExpr: rule?.amountExpr,
              flagExpr: rule?.settlementFlagExpr,
              criteriaIds: rule?.criteriaIds,
            });
          }
        );
        planBuilderStore.setSettlementRulesR(cloneDeep(settlementRuleList));
        planBuilderStore.setSettlementRulesW(cloneDeep(settlementRuleList));
      }
    }
  }, [data]);

  return (
    <>
      {selectedPage == "planDocument" && (
        <>
          <PlanDocuments
            planId={planId}
            employeeEmailId={emailId}
            viewSource={viewSource}
            documentPath={documentPath}
          />
        </>
      )}
      {selectedPage == "planSummary" && (
        <Row>
          <Col span={24}>
            <AdditonalConfigContext.Provider value={additionalConfig}>
              <PlanReadOnly
                planId={planId}
                planBuilderStore={planBuilderStore}
                expressionBoxVersion={expressionBoxVersion}
              />
            </AdditonalConfigContext.Provider>
          </Col>
        </Row>
      )}
      {selectedPage === "ChatGPT" && (
        <ChatGPT
          planId={planId}
          emailId={emailId}
          summaryData={summaryData}
          initialLoading={initialLoading}
        />
      )}
    </>
  );
});
