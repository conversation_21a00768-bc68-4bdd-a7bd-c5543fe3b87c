import { useQuery, gql } from "@apollo/client";
import { Row, Divider, message, Layout, Empty } from "antd";
import { observer } from "mobx-react";
import React, { useState, useEffect, Fragment } from "react";
import { Document, Page } from "react-pdf";

import { downloadSupportingDoc } from "~/Api/CommissionPlanService";
import { DOCUMENT_VIEW_SOURCE } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import usePdfWorker from "~/Utils/usePdfWorker";
import { EverButton, EverTabs } from "~/v2/components";
import EverProgress from "~/v2/legacy/components/EverProgress";

const { Content } = Layout;
const { TabPane } = EverTabs;

const PLAN_DOCS = gql`
  query SupportingDocsAndPayeeSupportingDocs($planId: UUID!) {
    supportingDocs(planId: $planId) {
      temporalId
      planId
      doc
      fileName
    }
    payeeSupportingDocs(planId: $planId) {
      temporalId
      planId
      employeeEmailId
      doc
      fileName
    }
  }
`;

const PlanDocuments = observer((props) => {
  const { accessToken } = useAuthStore();
  const { planId, employeeEmailId, viewSource, documentPath } = props;
  const [supportingDocs, setSupportingDocs] = useState([]);
  const [payeeSupportingDocs, setPayeeSupportingDocs] = useState([]);

  const { data: docsData } = useQuery(PLAN_DOCS, {
    variables: { planId: planId },
    fetchPolicy: "no-cache",
    skip: viewSource === DOCUMENT_VIEW_SOURCE,
  });

  useEffect(() => {
    if (docsData && docsData.supportingDocs) {
      setSupportingDocs(docsData.supportingDocs);
      setPayeeSupportingDocs(docsData.payeeSupportingDocs);
    }
  }, [docsData]);

  const DocTabPane = ({ docs, employeeEmailId }) => {
    const onDownload = (file) => {
      const hide = message.loading({
        content: `Downloading ${file.fileName}`,
        duration: 0,
        icon: <EverProgress type="spinner" size={40} />,
      });
      downloadSupportingDoc(
        { planId, filePath: file.doc, employeeEmailId },
        accessToken
      )
        .then((response) => response.blob())
        .then((blobby) => {
          hide();
          let objectUrl = window.URL.createObjectURL(blobby);
          // window.open(objectUrl);
          let anchor = document.createElement("a");
          anchor.href = objectUrl;
          anchor.download = file.fileName;
          anchor.click();

          window.URL.revokeObjectURL(objectUrl);
        });
    };

    const urlData = {
      plan_id: planId,
      file_path: docs.doc,
    };

    if (employeeEmailId) urlData.employee_email_id = employeeEmailId;

    const docURL =
      "/spm/commissionplan/download-supporting-doc?" +
      new URLSearchParams(urlData).toString();

    return (
      <Row style={{ display: "inline-block" }}>
        {viewSource !== DOCUMENT_VIEW_SOURCE && (
          <Row justify="end">
            <EverButton
              onClick={() => {
                onDownload(docs);
              }}
              type="ghost"
              color="primary"
              size="small"
              className="mb-3"
            >
              Download
            </EverButton>
          </Row>
        )}
        <RenderPdf accessToken={accessToken} documentPath={docURL} />
      </Row>
    );
  };

  const filteredPayeeSupportingDocs = payeeSupportingDocs.filter(
    (docs) => docs.employeeEmailId === employeeEmailId
  );

  return (
    <>
      {viewSource === DOCUMENT_VIEW_SOURCE && documentPath ? (
        <div className="flex justify-center">
          <Row className="inline-block">
            <RenderPdf documentPath={documentPath} />
          </Row>
        </div>
      ) : (
        accessToken &&
        (supportingDocs.length > 0 ||
          filteredPayeeSupportingDocs.length > 0) && (
          <>
            <EverTabs centered style={{ padding: "5px 0" }}>
              {filteredPayeeSupportingDocs.map((docs) => (
                <TabPane
                  tab={docs.fileName}
                  style={{ margin: 0, textAlign: "center" }}
                  key={docs.temporalId}
                >
                  <DocTabPane docs={docs} employeeEmailId={employeeEmailId} />
                </TabPane>
              ))}
              {supportingDocs.map((docs) => (
                <TabPane
                  tab={docs.fileName}
                  style={{ margin: 0, textAlign: "center" }}
                  key={docs.temporalId}
                >
                  <DocTabPane docs={docs} />
                </TabPane>
              ))}
            </EverTabs>
          </>
        )
      )}
      {accessToken &&
        supportingDocs.length === 0 &&
        filteredPayeeSupportingDocs.length === 0 &&
        !documentPath && (
          <Empty
            description="No documents present"
            style={{ top: "30%", position: "relative" }}
          />
        )}
    </>
  );
});

const RenderPdf = (props) => {
  usePdfWorker();
  const { accessToken = null, documentPath } = props;
  const [numPages, setNumPages] = useState(null);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };
  return (
    <Content
      style={{
        margin: 0,
        height: "75vh",
        overflow: "scroll",
        border: "1px solid #E0E3EE",
        minWidth: 612,
      }}
    >
      <Document
        file={{
          url: documentPath,
          ...(accessToken && {
            httpHeaders: { Authorization: `Bearer ${accessToken}` },
          }),
        }}
        onLoadSuccess={onDocumentLoadSuccess}
      >
        {Array.from(new Array(numPages), (_, index) => (
          <Fragment key={`page_${index + 1}`}>
            <Page pageNumber={index + 1} renderTextLayer={false} />
            <Divider plain>
              {index + 1}/{numPages}
            </Divider>
          </Fragment>
        ))}
      </Document>
    </Content>
  );
};

export default PlanDocuments;
