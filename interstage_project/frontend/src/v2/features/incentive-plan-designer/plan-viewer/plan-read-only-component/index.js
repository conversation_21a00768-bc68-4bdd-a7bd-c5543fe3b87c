import { Empty, Typography, Row, Select } from "antd";
import { observer } from "mobx-react";
import React from "react";
import { useTranslation } from "react-i18next";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverTabs } from "~/v2/components";
import EverProgress from "~/v2/legacy/components/EverProgress";

import CriteriaReadOnly from "./criteria-read-only";
import styles from "../styles.module.scss";

const { TabPane } = EverTabs;

const PlanReadOnly = observer((props) => {
  const { planBuilderStore, planId, expressionBoxVersion } = props;

  const {
    criteriaListR,
    isEmpty,
    activeIndex,
    setActiveIndex,
    loading,
    settlementRulesListR,
  } = planBuilderStore;

  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  if (loading) {
    return <EverProgress type="spinner" />;
  }

  const populateCriteria = () => {
    let criterias = [];
    criteriaListR.map((criteria) => {
      if (
        hasPermissions(RBAC_ROLES.VIEW_HIDDENCRITERIA) ||
        !criteria.criteriaConfig.criteriaIsHidden
      ) {
        criterias.push(
          <TabPane tab={criteria.name} key={criteria.id} className="py-6">
            <div className="mb-4">
              <CriteriaReadOnly
                dataProp={criteria.data}
                type={criteria.type}
                planId={planId}
                expressionBoxVersion={expressionBoxVersion}
              />
            </div>
          </TabPane>
        );
      }
    });
    return criterias;
  };

  const populateSettlementRules = () => {
    let settlementRuleList = [];
    settlementRulesListR.map((rule) => {
      let settlementRulesCriterias = [];
      criteriaListR.forEach((criteria) => {
        if (rule.criteriaIds.includes(criteria.id))
          settlementRulesCriterias.push(criteria.name);
      });
      settlementRuleList.push(
        <TabPane tab={rule.name} key={rule.settlementRuleId} className="py-6">
          <Row className={styles.settlementHeaderText}>
            <Typography.Text>Component</Typography.Text>
          </Row>
          <Select
            showSearch
            mode="multiple"
            placeholder="Component"
            style={{ width: 220, textAlign: "left", pointerEvents: "none" }}
            value={settlementRulesCriterias}
          />
          <Row style={{ paddingTop: "25px" }}></Row>
          <Row className={styles.settlementHeaderText}>
            <Typography.Text>is_Settled Flag Logic</Typography.Text>
          </Row>
          <CriteriaReadOnly
            dataProp={rule.flagExpr}
            type={"Simple"}
            planId={planId}
            expressionBoxVersion={expressionBoxVersion}
          />
          <Row style={{ paddingTop: "25px" }}></Row>
          <Row className={styles.settlementHeaderText}>
            <Typography.Text>{t("COMMISSION_PAYOUT_LOGIC")}</Typography.Text>
          </Row>
          <CriteriaReadOnly
            dataProp={rule.amountExpr}
            type={rule.amountExpr?.type}
            planId={planId}
            expressionBoxVersion={expressionBoxVersion}
          />
        </TabPane>
      );
    });
    return settlementRuleList;
  };
  return (
    <>
      {isEmpty ? (
        <Empty description="No criteria" />
      ) : (
        <EverTabs
          activeKey={activeIndex && activeIndex}
          onTabClick={setActiveIndex}
          style={{ padding: "0 40px" }}
        >
          {populateCriteria()}
          {populateSettlementRules()}
        </EverTabs>
      )}
    </>
  );
});

export default PlanReadOnly;
