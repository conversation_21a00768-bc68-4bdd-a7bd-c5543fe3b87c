import { useQueryClient } from "react-query";

import useFetchA<PERSON><PERSON>ith<PERSON><PERSON> from "./restapi";

const useInvokeLLMWithTag = () => {
  const { generateAI } = useFetchApiWithAuth();
  const queryClient = useQueryClient();

  // /**
  //  * Hook invokes LLM (Language Learning Model) with a specific tag.
  //  *
  //  * @param {string} prompt - The prompt for the LLM invocation.
  //  * @param {string} tag - The tag of the agent to be used for LLM invocation.
  //  * @param {Function} onSuccess - The callback function to be called on successful LLM invocation.
  //  * @param {Function} onError - The callback function to be called on error during LLM invocation.
  //  * @param {string} entityType - The type of entity to be used for LLM invocation. skill/agent/collection
  //  * @param {Array} useQueryDependencies - The dependencies for the useQuery hook.
  //  * @param {boolean} retry - Flag indicating whether to retry LLM invocation on failure.
  //  * @param {boolean} toolsRequired - Flag indicating whether tools are required for the LLM invocation.
  //  * @param {boolean} returnResult - Flag indicating whether to return the result of the LLM invocation.
  //  */
  const invoke = ({
    prompt,
    tag,
    onSuccess,
    onError,
    entityType,
    useQueryDependencies = [],
    retry = false,
    toolsRequired = false,
    returnResult = false,
  }) => {
    queryClient.prefetchQuery(
      useQueryDependencies.length > 0
        ? useQueryDependencies
        : ["invokeLLMWithTag"],
      async () => {
        try {
          const data = await generateAI({
            userPrompt: prompt,
            tag: tag,
            executionStrategy: entityType,
            toolRequired: toolsRequired,
            returnResult: returnResult,
          });
          onSuccess(data); // Call your success handler here
          return data; // Return data to cache (optional)
        } catch (error) {
          onError(); // Handle error here
          throw error; // Rethrow error to let queryClient handle it
        }
      },
      {
        retry: retry,
      }
    );
  };

  return { invoke };
};

export default useInvokeLLMWithTag;
