import {
  ChevronDownIcon,
  LayoutAltIcon,
  XlsxFileIcon,
  CsvFileIcon,
  XlsFileIcon,
} from "@everstage/evericons/outlined";
import { FilePlusIcon, InfoCircleIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { Upload, Row, Col, Dropdown } from "antd";
import languageEncoding from "detect-file-encoding-and-language";
import React, { useState, useEffect } from "react";
import ReactHTMLParser from "react-html-parser";
import { read, utils, writeFile } from "xlsx";

import {
  EverTg,
  EverChip,
  EverLabel,
  EverButton,
  EverDropdownMenu,
  EverBanner,
} from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";
import { EverHotToastMessage, toast } from "~/v2/components/ever-popups";

const { Dragger } = Upload;

const DataUpload = ({
  selectedObject,
  onObjectClose,
  onUploadCSV,
  fileData,
  fieldDefs,
  instructions,
  onResetFile,
  setFileObj,
  selectedStrategy,
  setInitialLines,
  setValidationData,
  setDisplayData,
  displayData,
  isXlsx,
  setIsXlsx,
  fileName,
  setFileName,
  hasPermissionToUploadExcel,
}) => {
  const [fileList, setFileList] = useState([]);
  const [previewColumns, setPreviewColumns] = useState([]);

  useEffect(async () => {
    if (fileList.length > 0) {
      const file = fileList[0];
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          let data;
          let lines;
          let blob;

          if (file.type === "text/csv") {
            // Handle CSV files
            const contents = e.target.result;
            lines = contents.split(/\r\n|\n/);
            const workbook = read(contents, { type: "binary", raw: true });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            data = utils.sheet_to_json(worksheet, { header: 1 });
            const first11Lines = lines.slice(0, Math.min(11, lines.length));
            const csvContent = first11Lines.join("\n");
            blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
          } else {
            // Handle Excel files
            setIsXlsx(true);
            const workbook = read(e.target.result, {
              type: "array",
              cellDates: true,
            });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const contents = utils.sheet_to_csv(worksheet);
            lines = contents.split(/\r\n|\n/);
            const workbook2 = read(contents, { type: "binary", raw: true });
            const firstSheetName2 = workbook2.SheetNames[0];
            const worksheet2 = workbook2.Sheets[firstSheetName2];
            data = utils.sheet_to_json(worksheet2, { header: 1 });
            blob = file.originFileObj;
          }
          if (data.length === 0 || data.length === 1) {
            toast.custom(
              (t) => (
                <EverHotToastMessage
                  type="error"
                  description="File is empty."
                  toastId={t.id}
                />
              ),
              { position: "top-center" }
            );
            setFileList([]);
            setIsXlsx(false);
          } else if (e.loaded > 25 * 1024 * 1024) {
            toast.custom(
              (t) => (
                <EverHotToastMessage
                  type="error"
                  description="File size is larger than 25MB."
                  toastId={t.id}
                />
              ),
              { position: "top-center" }
            );
            setFileList([]);
            setIsXlsx(false);
          } else {
            const headers = data[0].map((header) => header?.toString() || "");
            const empty_headers = [];
            for (let i = 0; i < headers.length; i++) {
              if (!(i in headers)) {
                empty_headers.push(i + 1);
              }
            }
            if (empty_headers.length > 0) {
              toast.custom(
                (t) => (
                  <EverHotToastMessage
                    type="error"
                    description={`Column number ${empty_headers.join(
                      ", "
                    )} missing header name.`}
                    toastId={t.id}
                  />
                ),
                { position: "top-center" }
              );
              setFileList([]);
              onResetFile(1);
              setIsXlsx(false);
              setDisplayData([]);
              setFileObj(null);
              return;
            }
            const columnDefs = headers.map((header) => ({
              headerName: header,
              field: header,
              sortable: false,
              filter: false,
              valueGetter: (params) => params.data[header],
            }));
            setPreviewColumns(columnDefs);
            setFileName(file.name);
            setFileObj(file.originFileObj);
            onUploadCSV(file.name, headers);
            setInitialLines(lines.length > 20 ? lines.slice(0, 20) : lines);
            setValidationData(blob);
            setDisplayData(data.length > 11 ? data.slice(0, 11) : data);
            toast.custom(
              (t) => (
                <EverHotToastMessage
                  type="success"
                  description={`${file.name} file read successfully.`}
                  toastId={t.id}
                />
              ),
              { position: "top-center" }
            );
          }
        } catch (error) {
          toast.custom(
            (t) => (
              <EverHotToastMessage
                type="error"
                const
                description={`Error reading file. Please ensure it's a valid CSV${
                  hasPermissionToUploadExcel ? " or Excel" : ""
                } file.`}
                toastId={t.id}
              />
            ),
            { position: "top-center" }
          );
          setFileList([]);
        }
      };

      if (file.type === "text/csv") {
        const detectedEncodingResult = await languageEncoding(
          file.originFileObj
        );
        reader.readAsText(
          file.originFileObj,
          detectedEncodingResult?.encoding || "UTF-8"
        );
      } else {
        reader.readAsArrayBuffer(file.originFileObj);
      }
    }
  }, [fileList]);

  const handleDraggerChange = (info) => {
    let fileList = [...info.fileList];
    fileList = fileList.slice(-1);

    fileList = fileList.filter((file) => {
      const allowedTypes = [
        "text/csv", // Always allow CSV
        ...(hasPermissionToUploadExcel
          ? [
              "application/vnd.ms-excel",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ]
          : []),
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.custom(
          (t) => (
            <EverHotToastMessage
              type="error"
              description={`Only CSV and Excel file types are allowed!`}
              toastId={t.id}
            />
          ),
          { position: "top-center" }
        );
        fileList = [];
        return false;
      }
      return true;
    });

    setFileList(fileList);
  };

  const downloadTemplate = (format) => {
    let fields = [];
    if (selectedStrategy === "delete") {
      fields = fieldDefs.filter((def) => def.required).map((def) => def.label);
    } else {
      fields = fieldDefs.map((def) => def.label);
    }

    const data = [fields];
    for (let i = 0; i < 3; i++) {
      data.push([]);
    }

    /* create worksheet */
    const ws = utils.aoa_to_sheet(data);

    /* add to workbook */
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, "template");

    let filename;
    switch (format) {
      case "xlsx":
        filename = "template.xlsx";
        writeFile(wb, filename, { bookType: "xlsx" });
        break;
      case "xls":
        filename = "template.xls";
        writeFile(wb, filename, { bookType: "xls" });
        break;
      case "csv":
      default:
        filename = "template.csv";
        writeFile(wb, filename, { bookType: "csv" });
    }

    toast.custom(
      (t) => (
        <EverHotToastMessage
          type="success"
          description={`Template downloaded successfully in ${format.toUpperCase()} format`}
          toastId={t.id}
        />
      ),
      { position: "top-center" }
    );
  };

  const getRowData = () => {
    if (!fileData || !displayData || displayData.length <= 1) return [];
    const headers = displayData[0];
    const rows = displayData.slice(1, 6);

    return rows.map((row) => {
      const result = {};
      for (const [index, header] of headers.entries()) {
        result[header] = row[index] ? String(row[index]) : "";
      }
      return result;
    });
  };
  return (
    <Row>
      <Col span={24} justify={"center"} className="w-full">
        {selectedObject && (
          <div className="flex flex-row gap-6 mb-6">
            <div className="flex flex-row gap-2 items-center">
              <EverLabel className="text-lg font-medium text-ever-base-content">
                Selected Object
              </EverLabel>
              <EverChip
                size="large"
                title={
                  <EverTg.Text className="text-ever-base-content leading-4">
                    {selectedObject.name}
                  </EverTg.Text>
                }
                onClose={() => onObjectClose(0)}
                iconClassName="text-ever-base-content"
                className="bg-ever-chartColors-8"
              />
            </div>
            {fileData && (
              <div className="flex flex-row gap-2 items-center">
                <EverLabel className="text-lg font-medium text-ever-base-content">
                  Uploaded file
                </EverLabel>
                <EverChip
                  size="large"
                  title={
                    <EverTg.Text className="text-ever-base-content leading-4">
                      {fileName}
                    </EverTg.Text>
                  }
                  onClose={() => {
                    onResetFile(1);
                    setFileList([]);
                    setIsXlsx(false);
                    setFileObj(null);
                    setValidationData(null);
                  }}
                  iconClassName="text-ever-base-content"
                  className="bg-ever-chartColors-2"
                />
              </div>
            )}
          </div>
        )}

        {fileList.length === 0 && (
          <>
            <div className="flex flex-nowrap mt-8">
              <EverLabel className="text-lg font-medium text-ever-base-content">
                Upload file
              </EverLabel>
            </div>

            <div className="bg-ever-base-400 rounded-xl h-60 !my-5">
              <Dragger
                name="file"
                accept={
                  hasPermissionToUploadExcel ? ".csv, .xlsx, .xls" : ".csv"
                }
                multiple={false}
                showUploadList={false}
                onChange={handleDraggerChange}
                beforeUpload={() => false}
                onRemove={() => true}
                fileList={fileList}
                className="!rounded-xl"
              >
                <div className="flex flex-col gap-6 justify-center">
                  <div className="w-full flex justify-center">
                    <div className="flex items-center justify-center rounded-full h-16 w-16 bg-ever-primary-lite">
                      <FilePlusIcon className="text-ever-primary w-6 h-6 " />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-center align-center">
                      <EverTg.Text className="text-ever-base-content self-center mr-1.5">
                        Drag and drop file here, or
                      </EverTg.Text>
                      <a className="self-center text-ever-primary hover:text-ever-primary-hover">
                        Browse
                      </a>
                    </div>
                    <EverTg.Description>
                      Supports CSV
                      {hasPermissionToUploadExcel ? ", XLS and XLSX" : ""} files
                    </EverTg.Description>
                  </div>
                </div>
              </Dragger>
            </div>
          </>
        )}
        {fileData && displayData?.length > 1 && isXlsx && (
          <EverBanner
            className="w-fit !bg-ever-info-lite border border-solid !border-ever-info"
            content={
              <div className="flex flex-row gap-4 py-2 items-center">
                <div className="text-ever-info">
                  <InfoCircleIcon className="w-4 h-4" />
                </div>
                <div className="flex flex-col text-ever-info-content-lite">
                  <EverTg.Caption.Medium>
                    You have uploaded an .XLSX/.XLS file. Please note that only
                    the first sheet has been imported.
                  </EverTg.Caption.Medium>
                </div>
              </div>
            }
          />
        )}
        {fileData && displayData?.length > 1 && (
          <div className="flex flex-col gap-4 mt-2">
            <EverTg.SubHeading4 className="text-ever-base-content">
              Preview first 5 lines of your file
            </EverTg.SubHeading4>
            <div className="h-full w-auto ag-theme-material zebra-grid no-border">
              <AgGridReact
                {...everAgGridOptions.getDefaultOptions()}
                rowData={getRowData()}
                columnDefs={previewColumns}
                domLayout="autoHeight"
                enableRangeSelection={false}
                suppressCellFocus={true}
              />
            </div>
          </div>
        )}
        {instructions && (
          <div className="flex flex-col gap-6 border border-solid border-ever-base-400 rounded-xl py-5 px-8 !my-6">
            <div className="flex justify-between">
              <div className="flex flex-col gap-2">
                <EverLabel className="text-lg font-medium text-ever-base-content">
                  Download Template
                </EverLabel>
                <EverTg.Description className="font-normal">
                  To get started, download the template that includes the fields
                  in the selected object.
                </EverTg.Description>
              </div>
              <div className="flex gap-4">
                <Dropdown
                  overlay={
                    <EverDropdownMenu
                      menu={[
                        {
                          name: "CSV",
                          key: "csv",
                          icon: (
                            <CsvFileIcon className="w-5 h-5 text-ever-base-content" />
                          ),
                        },
                        ...(hasPermissionToUploadExcel
                          ? [
                              {
                                name: "XLSX",
                                key: "xlsx",
                                icon: (
                                  <XlsxFileIcon className="w-5 h-5 text-ever-base-content" />
                                ),
                              },
                              {
                                name: "XLS",
                                key: "xls",
                                icon: (
                                  <XlsFileIcon className="w-5 h-5 text-ever-base-content" />
                                ),
                              },
                            ]
                          : []),
                      ]}
                      onClick={(e) => {
                        downloadTemplate(e.key);
                      }}
                    />
                  }
                  trigger={["click"]}
                  className="w-50"
                >
                  <EverButton
                    type="filled"
                    target="_blank"
                    prependIcon={
                      <LayoutAltIcon className="text-primary-content w-4 h-4" />
                    }
                    appendIcon={
                      <ChevronDownIcon className="text-primary-content w-4 h-4" />
                    }
                  >
                    Download template
                  </EverButton>
                </Dropdown>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <EverLabel className="text-base font-medium text-ever-base-content">
                Instruction:
              </EverLabel>
              <ul className="flex flex-col text-ever-base-content-mid pl-6 gap-1">
                {instructions.map((note, idx) => (
                  <li key={idx}>
                    <EverTg.Description>
                      - {ReactHTMLParser(note)}
                    </EverTg.Description>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </Col>
    </Row>
  );
};

export default DataUpload;
