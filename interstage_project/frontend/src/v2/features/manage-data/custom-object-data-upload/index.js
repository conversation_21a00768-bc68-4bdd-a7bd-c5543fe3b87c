import { ArrowNarrowLeftIcon } from "@everstage/evericons/solid";
import { Row, Col, Space } from "antd";
import { isNil } from "lodash";
import React, { useState, useEffect, useMemo } from "react";
import ReactDom from "react-dom";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverDrawer,
  EverStepper,
  EverUserEmailModal,
} from "~/v2/components";
import { EverHotToastMessage, toast } from "~/v2/components/ever-popups";

import BaseConfirmationModal from "./BaseConfirmationModal";
import ChooseObject from "./choose-object";
import ChooseDateFormat from "./ChooseDateFormat";
import ChooseStrategy from "./ChooseStrategy";
import DataMapColumns from "./DataMapColumns";
import DataUpload from "./DataUploadCsv";
import DataValidate from "./DataValidate";
import {
  getActiveDataImportObjects,
  postDataImportTask,
  validateData,
} from "../api";
import { dateFormatKeyMap } from "../helper";

const EverDataUpload = ({
  title,
  currentSelectedStrategy,
  visible,
  onCloseDrawer,
  isRedirectFromDataSourcePage = false,
  selectedObjectId = null,
}) => {
  const { accessToken, email } = useAuthStore();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedObject, setSelectedObject] = useState(null);
  const [fileData, setFileData] = useState(null);
  const [fileObj, setFileObj] = useState(null);
  const [fieldDefs, setFieldDefs] = useState([]);
  const [csvHeaderMap, setCsvHeaderMap] = useState(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [runningObjects, setRunningObjects] = useState([]);
  const [isE2eRunning, setIsE2eRunning] = useState(false);
  const [emailIdModalVisible, setEmailIdModalVisible] = useState(false);
  const [fieldsColumnMap, setFieldsColumnMap] = useState({});
  const [proceedToImport, setProceedToImport] = useState(false);
  const [initialLines, setInitialLines] = useState([]); // This state is used to store the initial lines of the file
  const [selectedDateFormat, setSelectedDateFormat] = useState(null); // This state is used to store the selected date format
  const [validationData, setValidationData] = useState(null);
  const [validatedData, setValidatedData] = useState([]);
  const [isXlsx, setIsXlsx] = useState(false);
  const [fileName, setFileName] = useState(null);
  const [selectedStrategy, setSelectedStrategy] = useState(
    currentSelectedStrategy
  );
  const [isValidated, setIsValidated] = useState(false);
  const [isDataValid, setIsDataValid] = useState(false);
  const [displayData, setDisplayData] = useState([]);
  const [validationLoading, setValidationLoading] = useState(true);

  const [errorCount, setErrorCount] = useState(0);
  const [validatedCount, setValidatedCount] = useState(0);
  const [ignoreCount, setIgnoreCount] = useState(0);
  const dateVarsOfObj = useMemo(() => getDateVarsOfObject(), [selectedObject]);
  const isDateMapped = useMemo(() => checkDateIsMapped(), [fieldsColumnMap]);
  const { hasPermissions } = useUserPermissionStore();

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const hasPermissionToUploadExcel =
    clientFeatures?.uploadExcelFilesInCustomObject;

  const { t } = useTranslation();

  useEffect(() => {
    if (visible === true && currentStep === 0) {
      getActiveDataImportObjectsList();
    }
  }, [visible, currentStep]);

  useEffect(() => {
    setSelectedStrategy(currentSelectedStrategy);
  }, [currentSelectedStrategy]);

  const handleBack = () => {
    const navigationContainer = document.createElement("div");
    document.body.append(navigationContainer);

    ReactDom.render(
      <BaseConfirmationModal
        type="warning"
        title={t("HANDLE_BACK_TITLE")}
        subtitle={t("HANDLE_BACK_DESCRIPTION")}
        buttons={[
          {
            name: "Confirm",
            onClick: () => {
              onResetMappingScreen();
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
          {
            name: "Cancel",
            onClick: () => {
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
        ]}
      />,
      navigationContainer
    );
  };

  const handleDelete = () => {
    const navigationContainer = document.createElement("div");
    document.body.append(navigationContainer);

    ReactDom.render(
      <BaseConfirmationModal
        type="error"
        title={t("HANDLE_DELETE_TITLE")}
        subtitle={`Once the records are deleted, they will no longer be present in the ${selectedObject.name} object.`}
        buttons={[
          {
            name: "Confirm",
            onClick: () => {
              onImportData();
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
          {
            name: "Cancel",
            onClick: () => {
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
        ]}
      />,

      navigationContainer
    );
  };

  function getDateVarsOfObject() {
    const dateVars = selectedObject?.customObjectVariables.filter(
      (variable) => {
        return variable.dataType.dataType === "Date";
      }
    );
    return dateVars?.map((variable) => variable.systemName);
  }

  const handleCancel = () => {
    const navigationContainer = document.createElement("div");
    document.body.append(navigationContainer);

    ReactDom.render(
      <BaseConfirmationModal
        type="error"
        title="Your data import is incomplete!"
        subtitle={t("HANDLE_CANCEL")}
        buttons={[
          {
            name: "Confirm",
            onClick: () => {
              onResetAll();
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
          {
            name: "Cancel",
            onClick: () => {
              ReactDom.unmountComponentAtNode(navigationContainer);
            },
          },
        ]}
      />,
      navigationContainer
    );
  };

  const getActiveDataImportObjectsList = async () => {
    const response = await getActiveDataImportObjects(accessToken);
    if (response.ok) {
      const responseData = await response.json();
      const { data } = responseData;
      const { isE2eRunning, activeObjectsNames } = data;
      setRunningObjects(activeObjectsNames);
      setIsE2eRunning(isE2eRunning);
    }
  };

  const errorMessage = () => {
    toast.custom(
      (t) => (
        <EverHotToastMessage
          type="error"
          description="Please try again after some time!"
          toastId={t.id}
        />
      ),
      { position: "top-center" }
    );
  };

  const instructions = [
    "The accepted date formats are <b>DD-MMM-YYYY, DD/MMM/YYYY, DD.MMM.YYYY,</b> and <b>DD MMM YYYY.</b> E.g., 01-Jan-2023, 31 January 2023, 01/Jan/2023, 01.Jan.2023, 01.January.2023 or 31/January/2023.",
    "To create new or update existing data, ensure that the chosen object's primary key(s) are mapped along with the file fields that must be imported.",
    "To delete an object's data, mapping the primary key(s) alone is sufficient. The file must contain the primary field values of the records that need to be deleted.",
    "If you choose an Integration Object, any updates detected during data sync with the source may alter the imported data.",
    "When the import is in progress for an object, you cannot initiate another import for the same object. Please wait until the previous import is complete.",
    "Once the import is complete, you'll receive an email with files that contain successful and erroneous records, if any.",
  ];

  if (hasPermissionToUploadExcel) {
    instructions.push(
      "For .XLSX and .XLS file uploads, only the first sheet will be considered. If your data is on another sheet, please move it to the first sheet before uploading."
    );
  }

  const onUploadCSV = (name, headers) => {
    const csvHeaderMap = JSON.parse(selectedObject.csvHeaderMap);
    const primaryConfig = csvHeaderMap?.find(
      (header) => header.systemName === "primaryConfig"
    );
    const config = primaryConfig?.config || [];
    const data = { name, headers };
    const columnMap = {};

    for (const field of fieldDefs) {
      if (
        config &&
        field.key in config &&
        headers.includes(config[field.key])
      ) {
        columnMap[field.key] = config[field.key];
      } else {
        for (const header of headers) {
          if (header === field.label) columnMap[field.key] = header;
        }
      }
    }

    setFileData(data);
    setFieldsColumnMap(columnMap);
    updateCsvHeaderMap(columnMap);
    setCurrentStep(1);
  };

  const updateCsvHeaderMap = (columnMap) => {
    const primaryKeyKeys = JSON.parse(selectedObject.primaryKey);
    const snapshotKeyKeys = JSON.parse(selectedObject.snapshotKey);

    const allKeysDefined = primaryKeyKeys.every(
      (key) => columnMap[key] !== undefined
    );
    const allSnapshotKeysDefined = snapshotKeyKeys.every(
      (key) => columnMap[key] !== undefined
    );

    const config = {};
    Object.entries(columnMap).forEach(([key, label]) => {
      config[key] = label;
    });
    const csvHeaderMap = [
      {
        displayName: "Primary Config",
        systemName: "primaryConfig",
        config,
      },
    ];

    setCsvHeaderMap(csvHeaderMap);
    setProceedToImport(allKeysDefined && allSnapshotKeysDefined);
  };

  const mapFieldWithHeader = (field, value) => {
    let updatedFieldsColumnMap = { ...fieldsColumnMap, [field]: value };
    setFieldsColumnMap(updatedFieldsColumnMap);
    updateCsvHeaderMap(updatedFieldsColumnMap);
  };

  function checkDateIsMapped() {
    let dateMapping = false;

    // In cases other than delete, check if any of the date variables are mapped
    dateVarsOfObj?.forEach((datVar) => {
      if (fieldsColumnMap[datVar] !== undefined) {
        dateMapping = true;
      }
    });

    // If the strategy is delete, then check if any of the primary keys are date variables
    // Since we need map only the primary keys for delete
    if (selectedStrategy === "delete" && !isNil(selectedObject)) {
      const primaryKeys = JSON.parse(selectedObject?.primaryKey);
      dateMapping = false;
      dateVarsOfObj?.forEach((datVar) => {
        if (primaryKeys?.includes(datVar)) {
          dateMapping = true;
        }
      });
    }
    return dateMapping;
  }

  const constructData = (emailToNotify) => {
    let data = new FormData();
    data.append("csvFile", fileObj);
    data.append("objectName", selectedObject.name);
    data.append("objectId", selectedObject.customObjectId);
    data.append("task", selectedStrategy);
    data.append("csvHeaderMap", JSON.stringify(csvHeaderMap));
    data.append("isAtomic", false);
    data.append("emailToNotify", String(emailToNotify));
    let choosenDateFormat = {};
    dateVarsOfObj.forEach((datVar) => {
      if (fieldsColumnMap[datVar] !== undefined) {
        choosenDateFormat[datVar] = dateFormatKeyMap[selectedDateFormat];
      }
    });
    data.append("dateFormatMap", JSON.stringify(choosenDateFormat));
    return data;
  };

  function getPayloadForDateFormat() {
    let data = new FormData();
    data.append("objectId", selectedObject.customObjectId);
    data.append("csvHeaderMap", JSON.stringify(csvHeaderMap));
    data.append("csvFile", JSON.stringify(initialLines));

    return data;
  }

  const onImportData = () => {
    if (hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)) {
      setEmailIdModalVisible(true);
    } else {
      submitImportRequest(email);
    }
  };

  const submitImportRequest = (email) => {
    setShowImportModal(true);
    const postData = constructData(email);
    postDataImportTask(postData, accessToken)
      .then((response) => {
        if (response.ok) {
          response.json().then(() => {});
        } else {
          throw new Error("Data Import Failed");
        }
        setEmailIdModalVisible(false);
      })
      .catch(() => {
        setShowImportModal(false);
        errorMessage();
      });
  };

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const goToPreviousStep = () => {
    setCurrentStep(currentStep - 1);
    setIsValidated(false);
    setIsDataValid(false);
    setValidatedData([]);
  };

  const onFileClose = (step) => {
    setCurrentStep(step);
    setFieldsColumnMap({});
    setFileData(null);
    setFileObj(null);
    setIsXlsx(false);
    setValidationData(null);
  };

  const onObjectClose = (step) => {
    setSelectedObject(null);
    onFileClose(step);
    setIsXlsx(false);
    setValidationData(null);
  };

  const onResetMappingScreen = () => {
    onFileClose(1);
    setProceedToImport(false);
    setCsvHeaderMap(null);
  };

  const onResetAll = () => {
    setSelectedStrategy(null);
    onObjectClose(0);
    onCloseDrawer();
    setProceedToImport(false);
    setShowImportModal(false);
    setCsvHeaderMap(null);
    setValidatedData([]);
    setIsDataValid(false);
    setIsValidated(false);
  };

  const constructValidateData = () => {
    let data = new FormData();
    data.append("objectId", selectedObject.customObjectId);
    data.append("csvHeaderMap", JSON.stringify(csvHeaderMap));
    data.append("validationFile", validationData);
    data.append("objectName", selectedObject.name);
    data.append("task", selectedStrategy);
    data.append("fileName", fileName);
    let choosenDateFormat = {};
    dateVarsOfObj.forEach((datVar) => {
      if (fieldsColumnMap[datVar] !== undefined) {
        choosenDateFormat[datVar] = dateFormatKeyMap[selectedDateFormat];
      }
    });
    data.append("dateFormatMap", JSON.stringify(choosenDateFormat));
    return data;
  };

  const handleValidate = () => {
    setValidationLoading(true);
    validateData(constructValidateData(), accessToken)
      .then((response) => {
        if (response.ok) {
          response.json().then((data) => {
            setIsValidated(true);
            setValidatedData(JSON.parse(data?.validatedData.data));
            if (data?.validatedData.errorCount == 0) {
              setIsDataValid(true);
            }
            setErrorCount(data?.validatedData.errorCount);
            setIgnoreCount(data?.validatedData.ignoreCount);
            setValidatedCount(data?.validatedData.validCount);
          });
        } else {
          throw new Error("Data Validation Failed");
        }
      })
      .catch(() => {
        errorMessage();
      });
  };
  return (
    <EverDrawer
      title={title}
      visible={visible}
      onClose={handleCancel}
      closable={true}
      placement="top"
      height="95vh"
      bodyStyle={{ padding: "0px" }}
      footer={
        <Row>
          <Col span={12} className="flex text-left text-ever-base-content">
            {(currentStep === 1 ||
              currentStep === 2 ||
              currentStep === 3 ||
              currentStep === 4) && (
              <EverButton
                type="link"
                color="base"
                onClick={() =>
                  currentStep === 1
                    ? onObjectClose(0)
                    : currentStep === 2
                    ? handleBack()
                    : goToPreviousStep()
                }
                prependIcon={<ArrowNarrowLeftIcon />}
              >
                Back
              </EverButton>
            )}
          </Col>
          <Col span={12} className="text-right">
            {currentStep === 0 && (
              <Space>
                <EverButton
                  type="text"
                  color="base"
                  onClick={handleCancel}
                  size="medium"
                >
                  Cancel
                </EverButton>
                <EverButton
                  type="filled"
                  color={
                    isRedirectFromDataSourcePage
                      ? selectedStrategy
                        ? "primary"
                        : "base"
                      : selectedObject
                      ? "primary"
                      : "base"
                  }
                  shadowSize="sm"
                  onClick={handleNext}
                  disabled={
                    isRedirectFromDataSourcePage
                      ? selectedStrategy === null
                      : selectedObject === null
                  }
                  size="medium"
                >
                  Next
                </EverButton>
              </Space>
            )}
            {currentStep === 1 && (
              <Space>
                <EverButton
                  type="filled"
                  color={"base"}
                  onClick={handleCancel}
                  disabled={false}
                  size="medium"
                >
                  Cancel
                </EverButton>
                <EverButton
                  type="filled"
                  color={fileData ? "primary" : "base"}
                  onClick={handleNext}
                  disabled={fileData === null}
                  size="medium"
                >
                  Next
                </EverButton>
              </Space>
            )}
            {currentStep === 2 && (
              <Space>
                <EverButton
                  type="filled"
                  color={"base"}
                  onClick={handleCancel}
                  disabled={false}
                  size="medium"
                >
                  Cancel
                </EverButton>
                <EverButton
                  type="filled"
                  color={fileData && proceedToImport ? "primary" : "base"}
                  onClick={handleNext}
                  disabled={fileData === null || proceedToImport === false}
                  size="medium"
                >
                  Next
                </EverButton>
              </Space>
            )}
            {currentStep === 3 && (
              <Space>
                <EverButton
                  type="filled"
                  color={"base"}
                  onClick={handleCancel}
                  disabled={false}
                  size="medium"
                >
                  Cancel
                </EverButton>
                {isDateMapped ? (
                  <EverButton
                    type="filled"
                    color={fileData ? "primary" : "base"}
                    onClick={handleNext}
                    disabled={
                      fileData === null ||
                      (selectedDateFormat === null && isDateMapped === true)
                    }
                    size="medium"
                  >
                    Next
                  </EverButton>
                ) : isValidated ? (
                  <EverButton
                    type="filled"
                    color={proceedToImport === true ? "primary" : "base"}
                    onClick={
                      selectedStrategy === "delete"
                        ? handleDelete
                        : onImportData
                    }
                    disabled={!isDataValid}
                    size="medium"
                  >
                    {selectedStrategy === "delete" ? "Delete" : "Import"}
                  </EverButton>
                ) : (
                  <EverButton
                    type="filled"
                    color={fileData ? "primary" : "base"}
                    onClick={handleValidate}
                    disabled={
                      selectedDateFormat === null && isDateMapped === true
                    }
                    size="medium"
                  >
                    Validate
                  </EverButton>
                )}
                <EverUserEmailModal
                  isVisible={emailIdModalVisible}
                  onCancel={() => setEmailIdModalVisible(false)}
                  onSubmit={(data) => submitImportRequest(data?.email)}
                />
              </Space>
            )}
            {currentStep === 4 && (
              <Space>
                <EverButton
                  type="filled"
                  color={"base"}
                  onClick={handleCancel}
                  disabled={false}
                  size="medium"
                >
                  Cancel
                </EverButton>
                {isValidated ? (
                  <EverButton
                    type="filled"
                    color={proceedToImport === true ? "primary" : "base"}
                    onClick={
                      selectedStrategy === "delete"
                        ? handleDelete
                        : onImportData
                    }
                    disabled={!isDataValid}
                    size="medium"
                  >
                    {selectedStrategy === "delete" ? "Delete" : "Import"}
                  </EverButton>
                ) : (
                  <EverButton
                    type="filled"
                    color="primary"
                    onClick={handleValidate}
                    size="medium"
                    disabled={
                      selectedDateFormat === null && isDateMapped === true
                    }
                  >
                    Validate
                  </EverButton>
                )}
                <EverUserEmailModal
                  isVisible={emailIdModalVisible}
                  onCancel={() => setEmailIdModalVisible(false)}
                  onSubmit={(data) => submitImportRequest(data?.email)}
                />
              </Space>
            )}
          </Col>
        </Row>
      }
    >
      <div className="flex flex-nowrap items-center bg-ever-primary-lite p-4 justify-center">
        <div className="w-6/12">
          {isDateMapped ? (
            <EverStepper
              steps={[
                isRedirectFromDataSourcePage
                  ? "Choose Upload Type"
                  : "Choose Object",
                "Upload File",
                "Map Fields",
                "Format",
                "Validate",
              ]}
              size="small"
              current={currentStep}
            />
          ) : (
            <EverStepper
              steps={[
                isRedirectFromDataSourcePage
                  ? "Choose Upload Type"
                  : "Choose Object",
                "Upload File",
                "Map Fields",
                "Validate",
              ]}
              size="small"
              current={currentStep}
            />
          )}
        </div>
      </div>
      <div className="h-[90%] pl-40 pr-40 pt-8">
        {currentStep === 0 &&
          (isRedirectFromDataSourcePage ? (
            <ChooseStrategy
              selectedStrategy={selectedStrategy}
              setSelectedStrategy={(strategy) => {
                setSelectedStrategy(strategy);
              }}
              setSelectedObject={(object) => {
                setSelectedObject(object);
              }}
              selectedObjectId={selectedObjectId}
              setFieldDefs={(_fieldDefs) => {
                setFieldDefs(_fieldDefs);
              }}
            />
          ) : (
            <ChooseObject
              drawerVisibility={visible}
              runningObjects={runningObjects}
              isE2eRunning={isE2eRunning}
              setChosenObject={setSelectedObject}
              setFieldDefs={setFieldDefs}
            />
          ))}
        {currentStep === 1 && (
          <div className="flex flex-col">
            <DataUpload
              selectedObject={selectedObject}
              onObjectClose={onObjectClose}
              fileData={fileData}
              onUploadCSV={onUploadCSV}
              fieldDefs={fieldDefs}
              instructions={instructions}
              onResetFile={onFileClose}
              templateData={[]}
              setFileObj={setFileObj}
              selectedStrategy={selectedStrategy}
              setInitialLines={setInitialLines}
              setValidationData={setValidationData}
              setDisplayData={setDisplayData}
              displayData={displayData}
              isXlsx={isXlsx}
              setIsXlsx={setIsXlsx}
              fileName={fileName}
              setFileName={setFileName}
              hasPermissionToUploadExcel={hasPermissionToUploadExcel}
            />
          </div>
        )}
        {currentStep === 2 && (
          <DataMapColumns
            selectedObject={selectedObject}
            fieldDefs={fieldDefs}
            fileData={fileData}
            fieldsColumnMap={fieldsColumnMap}
            filedOptions={fileData.headers.map((val) => ({
              label: val,
              value: val,
            }))}
            mapFieldWithHeader={mapFieldWithHeader}
            onObjectClose={onObjectClose}
            onFileClose={onFileClose}
            selectedStrategy={selectedStrategy}
            displayData={displayData}
          />
        )}
        {(currentStep === 3 || currentStep === 4) &&
          (isDateMapped && currentStep === 3 ? (
            <ChooseDateFormat
              fieldDefs={fieldDefs}
              fileData={fileData}
              fieldsColumnMap={fieldsColumnMap}
              filedOptions={fileData.headers.map((val) => ({
                label: val,
                value: val,
              }))}
              mapFieldWithHeader={mapFieldWithHeader}
              objectName={selectedObject.name}
              onObjectClose={onObjectClose}
              onFileClose={onFileClose}
              selectedDateFormat={selectedDateFormat}
              setSelectedDateFormat={setSelectedDateFormat}
              payload={getPayloadForDateFormat()}
              accessToken={accessToken}
            />
          ) : (
            <DataValidate
              data={isValidated && validatedData ? validatedData : displayData}
              headerType={selectedObject.customObjectVariables}
              isValidated={isValidated}
              errorCount={errorCount}
              ignoreCount={ignoreCount}
              validatedCount={validatedCount}
              fieldsColumnMap={fieldsColumnMap}
              validationLoading={validationLoading}
              setValidationLoading={setValidationLoading}
              fieldDefs={fieldDefs}
            />
          ))}
        {showImportModal === true && (
          <>
            <BaseConfirmationModal
              type="success"
              title={t("HANDLE_IMPORT_TITLE")}
              subtitle={t("HANDLE_IMPORT_DESCRIPTION")}
              buttons={[
                {
                  name: "Got it",
                  onClick: onResetAll,
                },
              ]}
            />
          </>
        )}
      </div>
    </EverDrawer>
  );
};

export default EverDataUpload;
