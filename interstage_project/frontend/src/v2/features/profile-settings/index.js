import { gql, useQuery } from "@apollo/client";
import { AlertCircleIcon } from "@everstage/evericons/outlined";
import { EditPencilLineIcon } from "@everstage/evericons/solid";
import { isEqual } from "lodash";
import { observer } from "mobx-react";
import React, { Fragment, useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { updateProfile } from "~/Api/SettingsService";
import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverForm,
  EverButton,
  EverSelect,
  EverInput,
  EverTg,
  EverGroupAvatar,
  EverModal,
  EverHotToastMessage,
  toast,
  EverLoader,
} from "~/v2/components";
import { bannerImage } from "~/v2/images";

import useProfileHook from "./profileHook";
import ProfileModal from "./ProfileModal";
import "./profile-settings.scss";

const { Option } = EverSelect;

const GET_PROFILE_DETAILS = gql`
  query PayeeProfileDetails($emailId: String!) {
    payeeProfileDetails(emailId: $emailId)
  }
`;

const Profile = observer(() => {
  const {
    accessToken,
    email: loggedInUserEmail,
    clients: clientsData,
  } = useAuthStore();
  const [profileDetails, setProfileDetails] = useState({});
  const [showConfirmationPopup, SetShowConfirmationPopup] = useState(false);
  const [submitDisabled, setSubmitDisabled] = useState(true);

  const { profilePicture, refetchProfilePicture } = useEmployeeStore();

  const clientList = [
    {
      clientId: -1,
      clientName: "Ask me everytime",
      src: <AlertCircleIcon className="text-ever-base-content" />,
    },
    ...clientsData,
  ];

  const {
    openModal,
    setOpenModal,
    imageLoading,
    setImageLoading,
    prompt,
    setPrompt,
    generatedImageBase64,
    setGeneratedImageBase64,
    profileImageBase64,
    setProfileImageBase64,
    generatedImageUrl,
    setGeneratedImageUrl,
    imageStyle,
    setImageStyle,
    onRemove,
    saveProfilePicture,
    sendPrompt,
    email,
  } = useProfileHook(loggedInUserEmail);

  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const { hasPermissions } = useUserPermissionStore();

  const { loading, data, refetch } = useQuery(GET_PROFILE_DETAILS, {
    variables: {
      emailId: email,
    },
    fetchPolicy: "no-cache",
  });

  const [form] = EverForm.useForm();

  const isPreferredClientChanged = (prefferedClient) => {
    return (
      formInitalValues["prefferedclientid"] !== prefferedClient &&
      prefferedClient !== undefined
    );
  };
  const isNameChanged = (firstName, lastName) => {
    const isFirstNameChanged = formInitalValues["firstname"] !== firstName;
    const isLastNameChanged = formInitalValues["lastname"] !== lastName;
    return isFirstNameChanged || isLastNameChanged;
  };

  const handleSubmit = (values) => {
    const inProgress = toast.custom(
      () => (
        <EverHotToastMessage type="loading" description="Save in progress" />
      ),
      { position: "top-center" }
    );

    const { firstname, lastname, emailaddress, prefferedclientid } = values;

    const restData = {
      email_id: emailaddress,
      first_name: firstname,
      last_name: lastname,
      is_name_changed: isNameChanged(firstname, lastname),
      preffered_client_id: prefferedclientid,
      is_preffered_client_changed: isPreferredClientChanged(prefferedclientid),
    };

    updateProfile(restData, accessToken)
      .then((res) => {
        if (res.ok) {
          toast.dismiss(inProgress);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description="Profile changes saved successfully"
              />
            ),
            { position: "top-center" }
          );

          refetch();
        } else {
          toast.dismiss(inProgress);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description="Something went wrong. Please try again!"
              />
            ),
            { position: "top-center" }
          );
        }
      })
      .catch((err) => {
        console.log("PROFILE SETTINGS SAVE ERROR" + err);
      });
  };

  const {
    first_name,
    last_name,
    email: userEmail,
    preffered_client_id,
    // similar_clients: similarClients,
  } = profileDetails;
  const formInitalValues = {
    firstname: first_name,
    lastname: last_name,
    emailaddress: userEmail,
    prefferedclientid: preffered_client_id ? preffered_client_id : -1,
  };

  const checkIfFormChanged = () => {
    if (!isEqual(formInitalValues, form.getFieldsValue(true))) {
      const firstname_error = form.getFieldError("firstname");
      const lastname_error = form.getFieldError("lastname");

      if (firstname_error.length !== 0 || lastname_error.length !== 0) {
        setSubmitDisabled(true);
        return;
      }
      setSubmitDisabled(false);
    } else {
      setSubmitDisabled(true);
    }
  };

  const formFieldsChange = () => {
    checkIfFormChanged();
  };

  useEffect(() => {
    if (data) {
      const profileData = JSON.parse(data.payeeProfileDetails);
      setProfileDetails(profileData);
    }
  }, [data]);

  useEffect(() => {
    form.resetFields();
    checkIfFormChanged(); //required to set initial values in form for async data
  }, [profileDetails]);

  const avatarDetails = {
    firstName: first_name,
    lastName: last_name,
    image: profilePicture,
  };

  const isUserNameEditEnabled =
    clientFeatures?.allowOnlyAdminsToModifyUserName ?? false
      ? hasPermissions(RBAC_ROLES.MANAGE_USERS)
      : true;

  return (
    <>
      <img src={bannerImage} className="w-full" />
      <div className="flex flex-col items-center">
        {loading ? (
          // <Spin tip="Loading..." />
          <div className="w-full h-full flex justify-center items-center">
            <EverLoader.SpinnerLottie className="w-20 h-20" />
          </div>
        ) : (
          <Fragment>
            <div>
              <div className="flex flex-row gap-8 h-24 relative">
                <div className="w-32">
                  <div
                    className={
                      "absolute top-[-60px] flex justify-center items-center bg-ever-base-25 w-32 h-32 rounded-full p-1"
                    }
                  >
                    <EverGroupAvatar
                      //className={styles.avatar}
                      containerClassName="w-[120px] h-[120px]"
                      avatars={[
                        {
                          image: avatarDetails.image,
                          firstName: avatarDetails.firstName,
                          lastName: avatarDetails.lastName,
                          className: "w-full h-full text-[64px]",
                        },
                      ]}
                    />
                  </div>
                  <div className=" w-9 h-9 bg-ever-base-25 absolute top-[32px] left-[90px] rounded-full z-10 flex justify-center items-center p-1">
                    <div
                      className="w-full h-full  cursor-pointer  bg-ever-primary rounded-full flex justify-center items-center"
                      onClick={() => {
                        if (
                          ["SELF", "ALL"].includes(
                            clientFeatures?.profilePicturePermission
                          )
                        ) {
                          setOpenModal(true);
                        }
                      }}
                    >
                      <EditPencilLineIcon className="text-ever-base-25 w-4 h-4" />
                    </div>
                  </div>
                </div>
                {/* </Popover> */}
                <div className="mt-6">
                  <EverTg.Heading1 className="text-3xl">
                    {first_name} {last_name}
                  </EverTg.Heading1>
                </div>
              </div>

              <EverForm
                className="mt-10"
                form={form}
                initialValues={formInitalValues}
                onFinish={handleSubmit}
                onFieldsChange={formFieldsChange}
              >
                <div className="flex gap-10 mb-6">
                  <div className="w-96">
                    <EverForm.Item
                      name="firstname"
                      label="First name"
                      labelCol={{ span: 24 }}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      className={"mb-0"}
                    >
                      <EverInput
                        placeholder="Enter First Name"
                        disabled={!isUserNameEditEnabled}
                      />
                    </EverForm.Item>
                  </div>
                  <div className="w-96">
                    <EverForm.Item
                      name="lastname"
                      label="Last name"
                      labelCol={{ span: 24 }}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                      className={"mb-0"}
                    >
                      <EverInput
                        placeholder="Enter Last Name"
                        disabled={!isUserNameEditEnabled}
                      />
                    </EverForm.Item>
                  </div>
                </div>
                <div className="mb-6">
                  <div className="w-96">
                    <EverForm.Item
                      name="emailaddress"
                      label="Email"
                      labelCol={{ span: 24 }}
                      className={"mb-0"}
                      required
                    >
                      <EverInput disabled={true} placeholder="Enter Email" />
                    </EverForm.Item>
                  </div>
                </div>
                {clientList.length > 2 ? (
                  <div className="mb-6">
                    <div className="w-full">
                      <EverForm.Item
                        name="prefferedclientid"
                        label="Default account"
                        labelCol={{ span: 24 }}
                        className={"mb-0"}
                        required
                      >
                        <EverSelect
                          placeholder="Select Preffered Client"
                          // options={similarClients}
                          value
                          showSearch
                          optionLabelProp="title"
                          filterOption={(input, option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {clientList
                            ? clientList.map((client) => {
                                //const notFakeItem = client.clientId !== -1;

                                return (
                                  <Option
                                    key={`client_${client.clientId}`}
                                    value={client.clientId}
                                    label={client.clientName}
                                    title={client.clientName}
                                  >
                                    <div className="flex align-middle items-center">
                                      <span
                                        role="img"
                                        aria-label={client.clientName}
                                        className="flex items-center"
                                      >
                                        <EverGroupAvatar
                                          avatars={[
                                            {
                                              firstName: client.clientName,
                                              image: client.src,
                                              className: "w-6 h-6",
                                            },
                                          ]}
                                        />
                                      </span>
                                      <span className="ml-2">
                                        {client.clientName}
                                      </span>
                                    </div>
                                  </Option>
                                );
                              })
                            : null}
                        </EverSelect>
                      </EverForm.Item>
                    </div>
                  </div>
                ) : null}
                <div className="w-full pt-2 flex gap-4 items-center">
                  <EverButton
                    type="filled"
                    color="base"
                    disabled={submitDisabled}
                    onClick={() => {
                      SetShowConfirmationPopup(true);
                    }}
                  >
                    Cancel
                  </EverButton>
                  <EverButton
                    htmlType="submit"
                    type="filled"
                    color="primary"
                    disabled={submitDisabled}
                  >
                    Save
                  </EverButton>
                </div>
              </EverForm>
            </div>
          </Fragment>
        )}
      </div>
      <ProfileModal
        openModal={openModal}
        avatarDetails={avatarDetails}
        setOpenModal={setOpenModal}
        imageStyle={imageStyle}
        setImageStyle={setImageStyle}
        prompt={prompt}
        setPrompt={setPrompt}
        generatedImageBase64={generatedImageBase64}
        setGeneratedImageBase64={setGeneratedImageBase64}
        generatedImageUrl={generatedImageUrl}
        setGeneratedImageUrl={setGeneratedImageUrl}
        imageLoading={imageLoading}
        setImageLoading={setImageLoading}
        accessToken={accessToken}
        email={email}
        sendPrompt={sendPrompt}
        saveProfilePicture={saveProfilePicture}
        onRemove={onRemove}
        profileImageBase64={profileImageBase64}
        setProfileImageBase64={setProfileImageBase64}
        refetchProfilePicture={refetchProfilePicture}
      />
      <EverModal.Confirm
        visible={showConfirmationPopup}
        title={"Discard Changes?"}
        type={"info"}
        subtitle="You have made some changes to some fields. Are you sure you want to leave without saving?"
        confirmationButtons={[
          <EverButton
            key="close"
            type="filled"
            color="primary"
            onClick={() => {
              form.resetFields();
              setSubmitDisabled(true);
              SetShowConfirmationPopup(false);
            }}
          >
            Discard
          </EverButton>,
          <EverButton
            key="resolve"
            color="base"
            type="filled"
            onClick={() => {
              SetShowConfirmationPopup(false);
            }}
          >
            Cancel
          </EverButton>,
        ]}
      />
    </>
  );
});

export default Profile;
