import { useQuery, gql } from "@apollo/client";
import {
  TicketSolidIcon,
  ChartBreakoutSquareIcon,
  UsersPlusIcon,
} from "@everstage/evericons/solid";
import { Row, Col } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState, Fragment } from "react";
import ReactHtmlParser, { convertNodeToElement } from "react-html-parser";
import { useQuery as useReactQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { DRS_STATUS, RBAC_ROLES } from "~/Enums";
import { isTextFieldValid } from "~/Utils/Validators";
import {
  EverInput,
  EverTg,
  EverBadge,
  EverSelect,
  EverButton,
  EverLabel,
  EverTooltip,
  EverModal,
  message,
  EverDivider,
  filterAndFormatOptions,
  EverList,
  CKEditor,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import useFetchApiWithAuth from "~/v2/features/datasheet/useFetchApiWithAuth";

const GET_EMPLOPYEES = gql`
  query AllEmployeeNames {
    allEmployeeNames {
      employeeEmailId
      firstName
      lastName
      canUserManageAdmins
    }
  }
`;

const GET_UPDATES = gql`
  query AllUpdates($drsId: String!) {
    allUpdates(drsId: $drsId) {
      drsId
      logger
      assignee
      subject
      status
      categoryId
      category
      loggedTime
      involvedUsers
      loggerDetails {
        employeeEmailId
        firstName
        lastName
      }
      assigneeDetails {
        employeeEmailId
        firstName
        lastName
      }
      drsUpdates {
        drsId
        meta
        message
        updatedBy
        updatedTime
      }
    }
  }
`;

const ALL_REASON_CATEGORIES = gql`
  query AllCustomCategories($moduleName: String) {
    allCustomCategories(moduleName: $moduleName) {
      customCategoryId
      customCategoryName
      isActive
    }
  }
`;

const addClassToNode = (node, index, className) => {
  node.attribs = {
    ...node.attribs,
    class: `${node.attribs.class || ""} ${className}`.trim(),
  };
  return convertNodeToElement(node, index, transform);
};

function transform(node, index, navigate) {
  let elementClass;

  switch (node.name) {
    case "blockquote":
      return addClassToNode(
        node,
        index,
        "text-xl italic border-0 border-l-4 border-solid border-ever-base-500 pl-4"
      );
    case "pre":
      return addClassToNode(
        node,
        index,
        "border border-solid border-ever-base-500 p-2"
      );
    case "figure": {
      const hasTable = node.children.some((child) => child.name === "table");

      if (hasTable) {
        node.attribs.class = `whitespace-nowrap overflow-x-auto`.trim();
      }

      return convertNodeToElement(node, index, transform);
    }
    case "table": {
      const tableEl = addClassToNode(node, index, "table-auto");

      const wrappedAlready =
        node.parent &&
        node.parent.type === "tag" &&
        node.parent.name === "figure";

      if (wrappedAlready) {
        return tableEl;
      }
      return (
        <figure
          key={`figure-${index}`}
          className="whitespace-nowrap overflow-x-auto"
        >
          {tableEl}
        </figure>
      );
    }
    case "tr":
      elementClass = `border-0 ${
        index === 0 ? "border-t border-b" : "border-b"
      } border-solid border-ever-base-500`;
      return addClassToNode(node, index, elementClass);
    case "td":
      elementClass = `${
        index === 0 ? "border-0 border-r" : "border-0"
      } border-solid border-ever-base-500 px-6 py-4`;
      return addClassToNode(node, index, elementClass);
    case "a": {
      let href = node?.attribs?.href || "";

      if (!href) {
        return (
          <span>
            {node.children.map((child, idx) =>
              convertNodeToElement(child, idx, transform)
            )}
          </span>
        );
      }

      href = href.replace(/^[\\'"]+|[\\'"]+$/g, "");
      if (!/^https?:\/\//i.test(href)) {
        href = `http://${href}`;
      }

      const appDomain = window.location.origin;
      const isInternal = href.startsWith(appDomain);

      const children = node.children.map((child, idx) =>
        convertNodeToElement(child, idx, transform)
      );

      if (isInternal) {
        return (
          <a
            href={href}
            onClick={(e) => {
              e.preventDefault();
              try {
                const url = new URL(href);
                const path = url.pathname + url.search + url.hash;
                navigate(path);
              } catch (error) {
                console.log("Could not find the url");
              }
            }}
          >
            {children}
          </a>
        );
      } else {
        return (
          <a href={href} target="_blank" rel="noopener noreferrer">
            {children}
          </a>
        );
      }
    }
    case "ul":
      return addClassToNode(node, index, "list-disc pl-6");
    case "ol":
      return addClassToNode(node, index, "list-decimal pl-6");
    default:
      return;
  }
}

const getCategoryOptions = (categories, drsCategoryId) => {
  if (categories.length == 0) return [];
  return categories.filter(
    (item) => item.isActive === true || item.customCategoryId === drsCategoryId
  );
};

const QueryComment = (props) => {
  const { author, content, datetime, navigate } = props;

  return (
    <div className="flex-auto min-w-px break-words text-sm my-2">
      <div className="flex justify-start text-sm [&>*]:pr-2">
        {author ? (
          <span className="text-ever-base-content-mid">{author}</span>
        ) : null}
        <span className="whitespace-nowrap">{datetime}</span>
      </div>
      <div>
        {typeof content === "string"
          ? ReactHtmlParser(content, {
              transform: (node, index) => transform(node, index, navigate),
            })
          : content}
      </div>
    </div>
  );
};

const AddQueryComponent = observer((props) => {
  const { store, setVisible, savedId, isDrawer } = props;
  const { saveQuery, summaryRefetch, setDrsRefetch, queryClientConfig } = store;
  const [employeeMap, setEmployeeMap] = useState({});
  const [isValid, setIsValid] = useState(true);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [status, setStatus] = useState(null);
  const [subject, setSubject] = useState(null);
  const [assignee, setAssignee] = useState();
  const [threadCC, setThreadCC] = useState([]);
  const [category, setCategory] = useState(null);
  const [drsCategory, setDrsCategory] = useState(null);
  const [drsUpdates, setDrsUpdates] = useState(null);
  const [queryMessage, setQueryMessage] = useState(null); //Storing as HTML string.
  const [assigneeOptions, setAssigneeOptions] = useState([]);
  const [ccOptions, setCcOptions] = useState([]);

  const [isResolveQueryModalVisible, setIsResolveQueryModalVisible] =
    useState(false);

  const navigate = useNavigate();
  const { fetchData } = useFetchApiWithAuth();

  const { data: employeeData } = useQuery(GET_EMPLOPYEES, {
    fetchPolicy: "network-only",
    notifyOnNetworkStatusChange: true,
  });

  const { data: updatesData, refetch: updatesRefetch } = useQuery(GET_UPDATES, {
    variables: {
      drsId: savedId,
    },
    fetchPolicy: "no-cache",
    skip: savedId === undefined,
  });

  // Fetch assignee user list for "Assignees" and "CC" dropdown
  const { data: assigneeUserList } = useReactQuery(
    ["assigneeUserList"],
    async () => {
      const response = await fetchData("/spm/drs/assignee-list", "GET");
      const formattedResponse = filterAndFormatOptions(response, {
        label: "fullName",
        value: "employeeEmailId",
      });
      setAssigneeOptions(formattedResponse);
      setCcOptions(formattedResponse);
      return formattedResponse;
    }
  );

  useQuery(ALL_REASON_CATEGORIES, {
    fetchPolicy: "no-cache",
    variables: { moduleName: "QUERIES" },
    onCompleted: (data) => {
      setCategoryOptions(data?.allCustomCategories);
    },
  });

  useEffect(() => {
    if (!savedId) {
      setStatus(DRS_STATUS.OPEN);
    }
  }, []);

  useEffect(() => {
    if (assignee && status !== DRS_STATUS.CLOSED) {
      setStatus(DRS_STATUS.ASSIGNED);
    }
  }, [assignee, status]);

  useEffect(() => {
    if (employeeData && employeeData.allEmployeeNames) {
      const employeeMap = {};
      employeeData.allEmployeeNames.map((employee) => {
        if (employee.canUserManageAdmins) return;
        employeeMap[employee["employeeEmailId"]] =
          employee["firstName"] + " " + employee["lastName"];
      });
      setEmployeeMap(employeeMap);
    }
  }, [employeeData]);

  useEffect(() => {
    if (
      updatesData &&
      updatesData.allUpdates &&
      updatesData.allUpdates.length > 0
    ) {
      let drsData = updatesData.allUpdates[0];

      if (drsData === null) {
        // if assignee is changed to someone not in the reporting team, then the updatesData.allUpdates returned is [null]
        return;
      }
      setQueryMessage(null);
      setSubject(drsData.subject);
      setCategory({
        customCategoryId: drsData.categoryId,
        customCategoryName: drsData.category,
      });
      setDrsCategory({
        customCategoryId: drsData.categoryId,
        customCategoryName: drsData.category,
      });
      setAssignee(drsData.assignee);
      setDrsUpdates(drsData.drsUpdates);
      if (!drsData.status) {
        if (!drsData.assignee) setStatus(DRS_STATUS.OPEN);
      } else {
        setStatus(drsData.status);
      }
      setThreadCC(JSON.parse(drsData.involvedUsers));

      // Append threadCC and assignee to the options
      if (drsData.assignee) {
        const assigneeExists = assigneeUserList?.find(
          (option) => option.value === drsData.assignee
        );
        setAssigneeOptions(() => [
          ...(assigneeUserList || []),
          ...(assigneeExists
            ? []
            : [
                {
                  value: drsData.assignee,
                  label: employeeMap[drsData.assignee],
                },
              ]),
        ]);
      } else {
        setAssigneeOptions(assigneeUserList);
      }

      if (drsData.involvedUsers) {
        let involvedUserList = [];
        JSON.parse(drsData.involvedUsers)?.map((cc) => {
          if (!assigneeUserList?.find((option) => option.value === cc)) {
            involvedUserList.push({
              value: cc,
              label: employeeMap[cc],
            });
          }
        });
        setCcOptions(() => [...(assigneeUserList || []), ...involvedUserList]);
      } else {
        setCcOptions(assigneeUserList);
      }

      setDrsRefetch(updatesRefetch);
    }
  }, [updatesData, employeeMap, assigneeUserList]);

  const onAssigneeChange = (value) => {
    setAssignee(value);
  };

  const onCategoryChange = (value) => {
    const selectedCategory = categoryOptions.find(
      (option) => option["customCategoryId"] === value
    );
    setCategory(selectedCategory);
  };

  const onThreadCCChange = (values) => {
    setThreadCC(values);
  };

  const onMessageChange = async (_, editor) => {
    const value = editor.getData();
    setQueryMessage(value);
  };

  const onSubjectChange = async (e) => {
    setSubject(e.target.value);
  };

  const onUpdate = (use) => {
    return new Promise((settled) => {
      if (
        !assignee ||
        !isTextFieldValid(subject) ||
        !category?.customCategoryId ||
        (use == "newQuery" && !isTextFieldValid(queryMessage))
      ) {
        !isTextFieldValid(subject) && setIsValid(false);
        !category?.customCategoryId && setIsValid(false);
        use == "newQuery"
          ? !isTextFieldValid(queryMessage) && setIsValid(false)
          : null;
        !assignee && setIsValid(false);
        settled();
      } else {
        saveQuery(
          status,
          assignee,
          category,
          threadCC,
          isTextFieldValid(queryMessage) ? queryMessage.trim() : null,
          subject.trim(),
          savedId
        )
          .then((response) => {
            if (response.ok) {
              clearAllvalues();
              message.success("Query saved successfully");
              if (!savedId) {
                setVisible(false);
                setStatus("Open");
                summaryRefetch();
              } else {
                summaryRefetch();
                updatesRefetch();
              }
              setIsValid(true);
            } else {
              response.json().then((errorResponse) => {
                try {
                  let errorMessage = "Something went wrong. Please Try again."; // default error message
                  let errorMessageArray = []; // error message array
                  if (errorResponse?.drsUpdateError) {
                    // Handle the "drsUpdateError" case
                    errorMessageArray = errorResponse.drsUpdateError?.message;
                  }

                  if (errorResponse?.message) {
                    // Handle the general "message" case
                    errorMessageArray = errorResponse.message;
                  }
                  // if error message array is not empty, then set the first error message to errorMessage
                  if (
                    Array.isArray(errorMessageArray) &&
                    errorMessageArray.length > 0
                  ) {
                    errorMessage = errorMessageArray[0];
                  }
                  message.error(
                    errorMessage.replace("this field", "the comment")
                  );
                } catch (err) {
                  // if something goes wrong, then show the default error message
                  message.error("Something went wrong. Please Try again.");
                }
              });
            }
          })
          .catch((err) => console.log("CONFIG DELETE ERROR" + err))
          .finally(() => {
            settled();
          });
      }
    });
  };

  const clearAllvalues = () => {
    setQueryMessage(null);
    setStatus(DRS_STATUS.OPEN);
    setAssignee(null);
    setThreadCC([]);
    setCategory(null);
    setSubject(null);
  };

  const onClose = () => {
    if (!subject || !category?.customCategoryId) {
      !subject && setIsValid(false);
      !category?.customCategoryId && setIsValid(false);
    } else {
      saveQuery(
        DRS_STATUS.CLOSED,
        assignee,
        category,
        threadCC,
        queryMessage,
        subject,
        savedId
      )
        .then((response) => {
          if (response.ok) {
            // Setting only the status of the selected query to closed.
            // Other states will be remain unchanged, unless the user
            // selects another query.
            setStatus(DRS_STATUS.CLOSED);
            setIsResolveQueryModalVisible(false);
            message.success("Query resolved successfully");
            if (!savedId) {
              setVisible(false);
            } else {
              updatesRefetch();
            }
            setIsValid(true);
            summaryRefetch();
          } else {
            response.json().then((errorResponse) => {
              try {
                let errorMessage = "Something went wrong. Please Try again."; // default error message
                let errorMessageArray = []; // error message array
                if (errorResponse?.drsUpdateError) {
                  // Handle the "drsUpdateError" case
                  errorMessageArray = errorResponse.drsUpdateError?.message;
                }

                if (errorResponse?.message) {
                  // Handle the general "message" case
                  errorMessageArray = errorResponse.message;
                }
                // if error message array is not empty, then set the first error message to errorMessage
                if (
                  Array.isArray(errorMessageArray) &&
                  errorMessageArray.length > 0
                ) {
                  errorMessage = errorMessageArray[0];
                }
                message.error(
                  errorMessage.replace("this field", "the comment")
                );
              } catch (err) {
                // if something goes wrong, then show the default error message
                message.error("Something went wrong. Please Try again.");
              }
            });
          }
        })
        .catch((err) => console.log("CONFIG DELETE ERROR" + err));
    }
  };

  return (
    <div
      className={`drsDrawer flex flex-col space-y-5 ${
        !savedId ? "!p-0" : "!pl-6 !p-4"
      }`}
    >
      <div className={isDrawer ? "p-6" : ""}>
        {!savedId && (
          <Row className="!mb-6">
            <Row className="!my-2">
              <EverLabel className="flex items-center" required={true}>
                Subject
              </EverLabel>
            </Row>
            <EverInput
              placeholder="Mention the subject"
              onChange={onSubjectChange}
              value={subject}
            />
            {!isValid && !isTextFieldValid(subject) && (
              <div className="text-ever-error">
                <div role="alert">Please mention the subject</div>
              </div>
            )}
          </Row>
        )}
        {savedId && (
          <Row className="!mb-6">
            <div className="flex w-full">
              <EverTg.Heading3>{subject}</EverTg.Heading3>
              <EverBadge
                className={`ml-3`}
                title={status == "Closed" ? "Resolved" : status}
                icon={
                  status == "Open" ? (
                    <TicketSolidIcon className="w-3.5 h-3.5 " />
                  ) : status == "Assigned" ? (
                    <UsersPlusIcon className="w-3.5 h-3.5" />
                  ) : (
                    <ChartBreakoutSquareIcon className="w-3.5 h-3.5" />
                  )
                }
                type={
                  status == "Open"
                    ? "warning"
                    : status == "Assigned"
                    ? "base"
                    : "success"
                }
              />
            </div>
          </Row>
        )}
        <div className="!mb-6">
          <Row className="!my-2">
            <EverLabel required className="flex items-center">
              Assigned to
            </EverLabel>
          </Row>
          <Row>
            <EverSelect
              maxTagTextLength={16}
              showSearch
              value={assignee}
              options={assigneeOptions}
              onChange={onAssigneeChange}
              placeholder="Select assignee"
            />
          </Row>
          {!isValid && !assignee && (
            <div className="text-ever-error">
              <div role="alert">Please choose an assignee</div>
            </div>
          )}
        </div>
        {queryClientConfig?.cc_others && (
          <div className="mb-6">
            <Row className="!my-2">
              <EverLabel className="flex items-center">CC</EverLabel>
            </Row>
            <Row>
              <EverSelect
                mode="multiple"
                maxTagTextLength={16}
                allowClear
                showSearch
                maxTagCount="responsive"
                value={threadCC}
                options={ccOptions}
                onChange={onThreadCCChange}
                placeholder="Choose other Payees"
              />
            </Row>
          </div>
        )}
        <div className="mb-6">
          <Row className="!my-2">
            <EverLabel className="flex items-center" required={true}>
              Category
            </EverLabel>
          </Row>
          <Row>
            <EverSelect
              placeholder="Select category"
              value={category?.customCategoryId}
              onChange={onCategoryChange}
              dropdownMatchSelectWidth={220}
              //suffixIcon={<CheckIcon className="w-5 h-5 text-ever-primary" />}
            >
              {getCategoryOptions(
                categoryOptions,
                drsCategory?.customCategoryId
              ).map((option) => (
                <EverSelect.Option
                  key={option["customCategoryId"]}
                  value={option["customCategoryId"]}
                >
                  {option["customCategoryName"]}
                </EverSelect.Option>
              ))}
            </EverSelect>
            {!isValid && !category?.customCategoryId && (
              <div className="text-ever-error">
                <div role="alert">Please choose a category</div>
              </div>
            )}
          </Row>
        </div>
        <div className="mb-16">
          <Row align="top" className="!my-2">
            <EverLabel
              required={savedId ? false : true}
              className="flex items-center"
            >
              Description
            </EverLabel>
          </Row>
          <Row>
            <div className="w-full">
              <CKEditor
                data={queryMessage ?? ""}
                onChange={onMessageChange}
                config={{
                  placeholder: "Provide an update here",
                }}
              />
            </div>
          </Row>
          {!isValid && !isTextFieldValid(queryMessage) && (
            <div className="text-ever-error">
              <div role="alert">Please enter description</div>
            </div>
          )}
        </div>
      </div>

      <Row className={isDrawer ? "absolute bottom-0 right-0 w-[506px]" : ""}>
        <div
          className={twMerge(
            "flex w-full justify-end gap-4 mt-2.5",
            isDrawer
              ? "bg-ever-base-100 rounded-b-lg border-t border-r-0 border-b-0 border-l-0 border-solid border-ever-base-400 px-6 py-3"
              : ""
          )}
        >
          <div>
            <RBACProtectedComponent permissionId={RBAC_ROLES.EDIT_QUERIES}>
              {savedId && (
                <EverButton
                  color="success"
                  disabled={
                    savedId
                      ? status === DRS_STATUS.CLOSED
                        ? true
                        : false
                      : true
                  }
                  onClick={() => {
                    if (!subject || !category?.customCategoryId) {
                      !subject && setIsValid(false);
                      !category?.customCategoryId && setIsValid(false);
                    } else {
                      setIsResolveQueryModalVisible(true);
                    }
                  }}
                >
                  Resolve
                </EverButton>
              )}
            </RBACProtectedComponent>
          </div>
          <div>
            {savedId ? (
              <RBACProtectedComponent permissionId={RBAC_ROLES.EDIT_QUERIES}>
                <EverButton
                  onClick={onUpdate}
                  disabled={status === DRS_STATUS.CLOSED}
                >
                  Update
                </EverButton>
              </RBACProtectedComponent>
            ) : (
              <RBACProtectedComponent permissionId={RBAC_ROLES.CREATE_QUERIES}>
                <EverButton
                  onClick={() => onUpdate("newQuery")}
                  disabled={status === DRS_STATUS.CLOSED}
                >
                  Raise Query
                </EverButton>
              </RBACProtectedComponent>
            )}
          </div>
        </div>
      </Row>
      {savedId && !isEmpty(employeeMap) && (
        <Fragment>
          <EverDivider></EverDivider>
          <UpdateComponent
            updates={drsUpdates}
            employeeMap={employeeMap}
            navigate={navigate}
          />
        </Fragment>
      )}
      <EverModal.Confirm
        type="success"
        subtitle="Are you sure you want to resolve this ticket?"
        visible={isResolveQueryModalVisible}
        confirmationButtons={[
          <EverButton
            key="close"
            type="ghost"
            color="base"
            onClick={() => setIsResolveQueryModalVisible(false)}
          >
            No
          </EverButton>,
          <EverButton key="resolve" color="success" onClick={() => onClose()}>
            Yes, resolve
          </EverButton>,
        ]}
      />
    </div>
  );
});

export default AddQueryComponent;

const UpdateComponent = observer((props) => {
  const { updates, employeeMap, navigate } = props;
  const [updateData, setUpdateData] = useState([]);

  useEffect(() => {
    if (updates && updates.length > 0) {
      const data = [];
      updates.map((update) => {
        if (update.message)
          data.push({
            author: employeeMap[update.updatedBy],
            content: update.message,
            datetime: (
              <EverTooltip
                title={moment(update.updatedTime).format("YYYY-MM-DD HH:mm:ss")}
              >
                <EverTg.Text className="text-ever-base-content-low">
                  {moment(update.updatedTime).fromNow()}
                </EverTg.Text>
              </EverTooltip>
            ),
          });
        let meta = JSON.parse(update.meta);
        if (meta.status["new_value"] !== meta.status["old_value"]) {
          data.push({
            content: (
              <div className="flex gap-1">
                <EverTg.Heading4 className="text-ever-base-content">
                  {employeeMap[update.updatedBy]}
                </EverTg.Heading4>
                <EverTg.Text className="text-ever-base-content">
                  {" "}
                  changed status to{" "}
                </EverTg.Text>
                <EverTg.Heading4 className="text-ever-base-content">
                  {meta.status["new_value"]}
                </EverTg.Heading4>
                <EverTooltip
                  title={moment(update.updatedTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )}
                >
                  <EverTg.Text className="text-ever-base-content-low">
                    {moment(update.updatedTime).fromNow()}
                  </EverTg.Text>
                </EverTooltip>
              </div>
            ),
          });
        }
        if (meta.category["new_value"] !== meta.category["old_value"]) {
          data.push({
            content: (
              <Row>
                <Col>
                  <div className="flex gap-1">
                    <EverTg.Heading4 className="text-ever-base-content">
                      {employeeMap[update.updatedBy]}
                    </EverTg.Heading4>
                    <EverTg.Text className="text-ever-base-content">
                      {" "}
                      changed category to{" "}
                    </EverTg.Text>
                    <EverTg.Heading4 className="text-ever-base-content">
                      {meta.category["new_value"]}
                    </EverTg.Heading4>
                  </div>
                  <EverTooltip
                    title={moment(update.updatedTime).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )}
                  >
                    <EverTg.Text className="text-ever-base-content-low">
                      {moment(update.updatedTime).fromNow()}
                    </EverTg.Text>
                  </EverTooltip>
                </Col>
              </Row>
            ),
          });
        }
        if (meta.assignee["new_value"] !== meta.assignee["old_value"]) {
          data.push({
            content: (
              <div className="flex gap-1">
                <EverTg.Heading4 className="text-ever-base-content">
                  {employeeMap[update.updatedBy]}
                </EverTg.Heading4>
                <EverTg.Text className="text-ever-base-content">
                  {" "}
                  changed assignee to{" "}
                </EverTg.Text>
                <EverTg.Heading4 className="text-ever-base-content">
                  {employeeMap[meta.assignee["new_value"]]}
                </EverTg.Heading4>
                <EverTooltip
                  title={moment(update.updatedTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )}
                >
                  <EverTg.Text className="text-ever-base-content-low">
                    {moment(update.updatedTime).fromNow()}
                  </EverTg.Text>
                </EverTooltip>
              </div>
            ),
          });
        }
        let old_cc = meta.involved_users["old_value"];
        let new_cc = meta.involved_users["new_value"];
        let difference = old_cc
          .filter((x) => !new_cc.includes(x))
          .concat(new_cc.filter((x) => !old_cc.includes(x)));
        difference &&
          difference.length > 0 &&
          difference.map((user) => {
            if (old_cc.includes(user) && !new_cc.includes(user)) {
              data.push({
                content: (
                  <div className="flex gap-1">
                    <EverTg.Heading4 className="text-ever-base-content">
                      {employeeMap[update.updatedBy]}
                    </EverTg.Heading4>
                    <EverTg.Text className="text-ever-base-content">
                      {" "}
                      removed{" "}
                    </EverTg.Text>
                    <EverTg.Heading4 className="text-ever-base-content">
                      {employeeMap[user]}
                    </EverTg.Heading4>
                    <EverTg.Text className="text-ever-base-content">
                      {" "}
                      from the thread{" "}
                    </EverTg.Text>
                    <EverTooltip
                      title={moment(update.updatedTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    >
                      <EverTg.Text className="text-ever-base-content-low">
                        {moment(update.updatedTime).fromNow()}
                      </EverTg.Text>
                    </EverTooltip>
                  </div>
                ),
              });
            } else if (new_cc.includes(user) && !old_cc.includes(user)) {
              data.push({
                content: (
                  <div className="flex gap-1">
                    <EverTg.Heading4 className="text-ever-base-content">
                      {employeeMap[update.updatedBy]}
                    </EverTg.Heading4>
                    <EverTg.Text className="text-ever-base-content">
                      {" "}
                      added{" "}
                    </EverTg.Text>
                    <EverTg.Heading4 className="text-ever-base-content">
                      {employeeMap[user]}
                    </EverTg.Heading4>
                    <EverTg.Text className="text-ever-base-content">
                      {" "}
                      to the thread{" "}
                    </EverTg.Text>
                    <EverTooltip
                      title={moment(update.updatedTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    >
                      <EverTg.Text className="text-ever-base-content-low">
                        {moment(update.updatedTime).fromNow()}
                      </EverTg.Text>
                    </EverTooltip>
                  </div>
                ),
              });
            }
          });
      });
      setUpdateData(data);
    }
  }, [updates]);

  return (
    <div className="drsUpdates">
      {updateData.length > 0 && (
        <EverList
          className="comment-list"
          itemLayout="horizontal"
          dataSource={updateData}
          renderItem={(item) => (
            <li>
              <QueryComment
                author={item.author}
                avatar={item.avatar}
                content={item.content}
                datetime={item.datetime}
                navigate={navigate}
              />
            </li>
          )}
        />
      )}
    </div>
  );
});
