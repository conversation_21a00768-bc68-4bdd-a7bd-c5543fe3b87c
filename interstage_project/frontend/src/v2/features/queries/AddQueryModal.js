import { useQuery, gql } from "@apollo/client";
import { HtmlToMarkdown } from "@ckeditor/ckeditor5-markdown-gfm/src/html2markdown/html2markdown";
import { Row } from "antd";
import { encode as base64_encode } from "base-64";
import { useEffect, useState } from "react";
import { useForm, useWatch, Controller } from "react-hook-form";
import { useQuery as useReactQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { encode as utf8_encode } from "utf8";
import { v4 as uuidv4 } from "uuid";

import { DRS_STATUS } from "~/Enums";
import { isTextFieldValid } from "~/Utils/Validators";
import {
  EverInput,
  EverSelect,
  EverButton,
  EverLabel,
  EverModal,
  message,
  filterAndFormatOptions,
  toast,
  EverHotToast<PERSON><PERSON><PERSON>,
  EverForm,
  CKEditor,
} from "~/v2/components";
import {
  alterTableStyleAttr,
  removeCodeClass,
} from "~/v2/components/ckeditor/ckeditor-utils";
import useFetchApiWithAuth from "~/v2/features/datasheet/useFetchApiWithAuth";

const GET_QUERY_CONFIG = gql`
  query clientQuerySetting {
    clientQuerySetting
  }
`;

const ALL_REASON_CATEGORIES = gql`
  query AllCustomCategories($moduleName: String) {
    allCustomCategories(moduleName: $moduleName) {
      customCategoryId
      customCategoryName
      isActive
    }
  }
`;

/**
 * AddQueryModal component.
 *
 * @param {Object} props - The component props.
 * @param {boolean} props.isVisible - Flag indicating whether the modal is visible.
 * @param {function} props.setIsVisible - Function to set the visibility of the modal.
 * @param {string} props.subject - The subject to prefill in the modal.
 * @param {Object} props.contextAttributes - The context attributes to prefill in the description.
 */
const AddQueryModal = (props) => {
  const {
    isVisible,
    setIsVisible,
    subject: prefillSubject,
    contextAttributes,
  } = props;

  const navigate = useNavigate();
  const { fetchData } = useFetchApiWithAuth();

  const [categoryOptions, setCategoryOptions] = useState([]);

  const { data: queryConfigData } = useQuery(GET_QUERY_CONFIG, {
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
  });

  useQuery(ALL_REASON_CATEGORIES, {
    fetchPolicy: "no-cache",
    variables: { moduleName: "QUERIES" },
    onCompleted: (data) => {
      const newCategoryOptions = data?.allCustomCategories.filter(
        (item) => item.isActive === true
      );
      setCategoryOptions(newCategoryOptions);
    },
  });

  let showCC = false;
  if (queryConfigData && queryConfigData.clientQuerySetting) {
    // Parse the clientQuerySetting JSON string
    const clientQuerySettings = JSON.parse(queryConfigData.clientQuerySetting);
    showCC = clientQuerySettings?.cc_others;
  }

  // React Hook Form
  const {
    control,
    setValue,
    formState: { isValid: isFormValid },
    reset: resetForm,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      subject: prefillSubject,
      category: null,
      assignee: null,
      threadCC: [],
      queryMessage: null,
    },
  });
  const watchedInputs = useWatch({ control });

  /**
   * useEffect hook to prefill the query message in the form.
   *
   * This hook listens for changes in `contextAttributes` to dynamically update the prefill message
   * in the query form. 'contextAttributes' changes when the query is raised from the line-item level,
   * the details of the line-item row should be included in the pre-filled query message.
   */
  useEffect(() => {
    const currentUrl = window.location.href;
    let formattedPrefillQueryMessage = `<p>&nbsp;</p><p>-------<br> <a href="${currentUrl}">Link</a> to statement <br>&nbsp;</p><figure class="table"><table><thead><tr>`;
    let keys = Object.keys(contextAttributes);
    let values = Object.values(contextAttributes);
    keys.forEach((key) => {
      formattedPrefillQueryMessage += `<th>&nbsp;${key}&nbsp;</th>`;
    });

    formattedPrefillQueryMessage += `</tr></thead><tbody><tr>`;

    values.forEach((value) => {
      formattedPrefillQueryMessage += `<td>&nbsp;${value || "-"}&nbsp;</td>`;
    });

    formattedPrefillQueryMessage += `</tr></tbody></table></figure>`;

    setValue("queryMessage", formattedPrefillQueryMessage);
  }, [contextAttributes]);

  // Fetch assignee user list for "Assignees" and "CC" dropdown
  const { data: assigneeUserList } = useReactQuery(
    ["assigneeUserList"],
    async () => {
      const response = await fetchData("/spm/drs/assignee-list", "GET");
      const formattedResponse = filterAndFormatOptions(response, {
        label: "fullName",
        value: "employeeEmailId",
      });
      return formattedResponse;
    }
  );

  const onClose = () => {
    setIsVisible(false);
    resetForm();
  };

  /**
   * Creates a new DRS query by sending a POST request to the server.
   *
   * @param {string} assignee - The user assigned to the query.
   * @param {string} category_id - The category id of the query.
   * @param {string} category - The category name of the query.
   * @param {Array} threadCCUsers - Users to be CC'd in the query thread.
   * @param {string} message - The message or description of the query.
   * @param {string} subject - The subject of the query.
   * @returns {Promise} A promise that resolves with the response of the fetch operation.
   */
  const createQuery = async (
    assignee,
    category_id,
    category,
    threadCCUsers,
    message,
    subject
  ) => {
    const html2markdown = new HtmlToMarkdown();

    let data = {
      id: uuidv4(),
      status: DRS_STATUS.ASSIGNED,
      assignee: assignee,
      subject: subject,
      category_id: category_id,
      category: category,
      involvedUsers: threadCCUsers,
      message: base64_encode(utf8_encode(alterTableStyleAttr(message))),
      messageMarkdown: base64_encode(
        utf8_encode(html2markdown.parse(removeCodeClass(message)))
      ),
    };
    return fetchData("/spm/drs/save", "POST", data);
  };

  /**
   * Handles the creation of a new query based on the form inputs watched by the component.
   * It triggers the createQuery function with the necessary parameters and handles the UI response accordingly.
   */
  const onCreate = async () => {
    const { subject, category, assignee, threadCC, queryMessage } =
      watchedInputs;

    try {
      const { customCategoryId, customCategoryName } = categoryOptions.find(
        (item) => item.customCategoryId === category
      );
      await createQuery(
        assignee,
        customCategoryId,
        customCategoryName,
        threadCC,
        queryMessage,
        subject
      );
      onClose(); // Calls the onClose function to reset/clear the form or close the modal

      // Displays a success toast message with custom content
      toast.custom(
        (toastObj) => {
          const { subject } = watchedInputs;
          return (
            <EverHotToastAlert
              type="success"
              title={"Query Saved Successfully"}
              description={subject}
              toastId={toastObj.id}
              buttons={[
                {
                  buttonText: "View Query",
                  onClick: () => navigate("/queries/allTickets"),
                  className: "text-ever-primary",
                },
              ]}
            />
          );
        },
        {
          position: "top-right",
          duration: 3000,
        }
      );
    } catch (error) {
      console.debug("Error creating query", error);
      message.error("Something went wrong. Please Try again.");
    }
  };

  const formLayout = {
    labelCol: {
      span: 24,
    },
    wrapperCol: {
      span: 24,
    },
  };

  return (
    <EverModal
      title="Create Query"
      visible={isVisible}
      onCancel={onClose}
      footer={
        <>
          <EverButton key="back" color="base" type="ghost" onClick={onClose}>
            Cancel
          </EverButton>
          <EverButton
            key="submit"
            disabled={!isFormValid}
            htmlType="submit"
            form="query-input-form"
          >
            Create
          </EverButton>
        </>
      }
      width={652}
      destroyOnClose
    >
      <EverForm
        {...formLayout}
        className=""
        name="query-input-form"
        onFinish={onCreate}
      >
        <Row className="!mb-6">
          <Row className="!mb-2">
            <EverLabel className="flex items-center" required={true}>
              Subject
            </EverLabel>
          </Row>
          <Controller
            control={control}
            name="subject"
            rules={{ required: true, validate: isTextFieldValid }}
            render={({ field }) => (
              <EverInput
                placeholder="Mention the subject"
                onChange={(event) => field.onChange(event.target.value)}
                value={field.value}
              />
            )}
          />
        </Row>
        <div className="!mb-6">
          <Row className="!mb-2">
            <EverLabel className="flex items-center" required={true}>
              Category
            </EverLabel>
          </Row>
          <Row>
            <Controller
              control={control}
              name="category"
              rules={{ required: true, message: "Please choose a category" }}
              render={({ field }) => (
                <EverSelect
                  placeholder="Select category"
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  dropdownMatchSelectWidth={220}
                >
                  {categoryOptions.map((option) => (
                    <EverSelect.Option
                      key={option["customCategoryId"]}
                      value={option["customCategoryId"]}
                    >
                      {option["customCategoryName"]}
                    </EverSelect.Option>
                  ))}
                </EverSelect>
              )}
            />
          </Row>
        </div>
        <div className="flex flex-row gap-4 w-full !mb-6">
          <div className={twMerge(showCC ? "w-1/2" : "w-full")}>
            <Row className="mb-2">
              <EverLabel className="flex items-center" required={true}>
                Assigned to
              </EverLabel>
            </Row>
            <Row>
              <Controller
                control={control}
                name="assignee"
                rules={{ required: true }}
                render={({ field }) => (
                  <EverSelect
                    {...field}
                    maxTagTextLength={16}
                    showSearch
                    options={assigneeUserList}
                    onChange={(value) => field.onChange(value)}
                    placeholder="Select assignee"
                  />
                )}
              />
            </Row>
          </div>
          {showCC && (
            <div className="w-1/2">
              <Row className="mb-3">
                <EverLabel className="flex items-center">CC</EverLabel>
              </Row>
              <Row>
                <Controller
                  control={control}
                  name="threadCC"
                  render={({ field }) => (
                    <EverSelect
                      {...field}
                      mode="multiple"
                      maxTagTextLength={16}
                      allowClear
                      showSearch
                      maxTagCount="responsive"
                      options={assigneeUserList}
                      onChange={(value) => field.onChange(value)}
                      placeholder="Choose other Payees"
                    />
                  )}
                />
              </Row>
            </div>
          )}
        </div>
        <div>
          <Row align="top" className="mb-2">
            <EverLabel required className="flex items-center">
              Description
            </EverLabel>
          </Row>
          <Row>
            <Controller
              control={control}
              name="queryMessage"
              rules={{ required: true, validate: isTextFieldValid }}
              render={({ field }) => (
                <div className="w-full">
                  <CKEditor
                    data={field.value ?? ""}
                    onChange={(_, editor) => field.onChange(editor.getData())}
                    config={{
                      placeholder: "Provide an update here",
                    }}
                  />
                </div>
              )}
            />
          </Row>
        </div>
      </EverForm>
    </EverModal>
  );
};

export default AddQueryModal;
