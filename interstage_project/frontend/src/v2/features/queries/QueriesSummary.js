import { useQuery, gql } from "@apollo/client";
import { TicketLineIcon } from "@everstage/evericons/outlined";
import {
  TicketSolidIcon,
  ChartBreakoutSquareIcon,
  UsersPlusIcon,
} from "@everstage/evericons/solid";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { RBAC_ROLES } from "~/Enums";
import { everBodyDomAtom } from "~/GlobalStores/atoms";
import {
  EverBadge,
  EverCard,
  EverTg,
  EverLoader,
  EverLabel,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { brokenLaptop } from "~/v2/images";

import AddQueryComponent from "./AddQueryComponent";

const GET_ALL_DRS = gql`
  query AllDrs {
    allDrs {
      drsId
      logger
      assignee
      subject
      status
      category
      categoryId
      loggedTime
      involvedUsers
      sequenceNumber
      loggerDetails {
        employeeEmailId
        firstName
        lastName
      }
      assigneeDetails {
        employeeEmailId
        firstName
        lastName
      }
      drsUpdates {
        drsId
        meta
        message
        updatedBy
        updatedTime
      }
    }
  }
`;

const detailsList = {
  "Initiated by": "loggerName",
  "Assigned to": "assigneeName",
  "Created on": "loggedTime",
  "Last updated": "lastUpdatedTime",
};

const QueriesSummary = observer((props) => {
  const { store, drsId } = props;
  const [listData, setListData] = useState([]);
  //const [selectedId, setSelectedId] = useState();
  const {
    setSummaryRefetch,
    setAllDrsData,
    allDrs,
    currentUser,
    selectedId,
    setSelectedId,
  } = store;

  const setEverbodyDOM = useSetRecoilState(everBodyDomAtom);

  const { loading, data, refetch } = useQuery(GET_ALL_DRS, {
    variables: {
      logger: currentUser,
    },
    fetchPolicy: "no-cache",
  });

  const queriesDOM = useCallback((node) => {
    if (node !== null) {
      setEverbodyDOM(node);
    }
  }, []);

  const { t } = useTranslation();

  useEffect(() => {
    if (allDrs) {
      setListData(allDrs);
      if (allDrs && allDrs.length > 0) {
        if (drsId) {
          setSelectedId(drsId);
        } else {
          setSelectedId(selectedId || allDrs[0].drsId);
        }
      }
    }
  }, [allDrs, drsId]);

  useEffect(() => {
    if (data) {
      const drsData = data.allDrs;
      const listData = [];
      const listMap = {};
      drsData &&
        drsData.map((drs) => {
          let obj = {
            drsId: drs.drsId,
            subject: drs.subject,
            status: drs.status,
            categoryId: drs.categoryId,
            category: drs.category,
            loggedTime: moment(drs.loggedTime),
            assigneeEmail: drs.assignee,
            assigneeName: drs.assigneeDetails
              ? drs.assigneeDetails.firstName +
                " " +
                drs.assigneeDetails.lastName
              : "Unassigned",
            loggerEmail: drs.logger,
            loggerName: drs.loggerDetails
              ? drs.loggerDetails.firstName + " " + drs.loggerDetails.lastName
              : "Unassigned",
            involvedUsers: JSON.parse(drs.involvedUsers),
            lastUpdatedTime: drs.drsUpdates
              ? moment(drs.drsUpdates[0].updatedTime)
              : null,
            lastUpdatedBy: drs.drsUpdates ? drs.drsUpdates[0].updatedBy : null,
            sequenceNumber: drs.sequenceNumber,
          };
          listData.push(obj);
          listMap[drs.drsId] = obj;
        });
      setSummaryRefetch(refetch);
      setAllDrsData(listData);
    }
  }, [data]);

  /*const colorsMap = {
    open: {
      border: "ever-chartColors-15",
      ring: "ever-chartColors-15",
      bg: "ever-chartColors-15",
    },
    closed: {
      border: "ever-success",
      ring: "ever-success-ring",
      bg: "ever-success-lite",
    },
    assigned: {
      border: "ever-chartColors-10",
      ring: "ever-chartColors-8",
      bg: "ever-chartColors-8",
    },
  };

  const gradients = {
    open: `bg-gradient-to-r from-${colorsMap["open"]["bg"]}/10 from-10% via-${colorsMap["open"]["bg"]}/5 via-30% to-${colorsMap["open"]["bg"]}/10 to-90% border border-solid border-${colorsMap["open"]["border"]}/25 hover:ring-${colorsMap["open"]["ring"]}/10 hover:border-${colorsMap["open"]["border"]}`,
    closed: `bg-gradient-to-r from-${colorsMap["closed"]["bg"]}/90 from-10% via-${colorsMap["closed"]["bg"]}/50 via-30% to-${colorsMap["closed"]["bg"]}/90 to-90% border border-solid border-${colorsMap["closed"]["border"]}/50 hover:ring-${colorsMap["closed"]["ring"]} hover:border-${colorsMap["closed"]["border"]}`,
    assigned: `bg-gradient-to-r from-${colorsMap["assigned"]["bg"]}/50 from-10% via-${colorsMap["assigned"]["bg"]}/30 via-30% to-${colorsMap["assigned"]["bg"]}/50 to-90% border border-solid border-${colorsMap["assigned"]["border"]}/25 hover:ring-${colorsMap["assigned"]["ring"]} hover:border-${colorsMap["assigned"]["border"]}`,
  };*/

  const gradients = {
    open: "bg-gradient-to-r from-ever-chartColors-6/50 via-ever-chartColors-6/25 to-ever-chartColors-6/50 border border-solid border-ever-chartColors-29/20 hover:ring-ever-chartColors-29/10 hover:border-ever-chartColors-29/50",
    assigned:
      "bg-gradient-to-r from-ever-chartColors-6/50 via-ever-chartColors-6/25 to-ever-chartColors-6/50 border border-solid border-ever-chartColors-29/20 hover:ring-ever-chartColors-29/10 hover:border-ever-chartColors-29/50",
    closed:
      "bg-gradient-to-r from-ever-success-lite via-ever-success-lite/50 to-ever-success-lite border border-solid border-ever-success/40 hover:ring-ever-success/20 hover:border-ever-success/80",
  };

  return (
    <div className="drsSummary h-full justify-center items-center pt-4">
      {loading ? (
        <EverLoader indicatorType="spinner" />
      ) : store.allDrs.length > 0 ? (
        <div className="h-full flex gap-6">
          <div
            ref={queriesDOM}
            className={twMerge(
              "overflow-auto h-full",
              listData.length ? "w-7/12" : "w-full"
            )}
          >
            <div className="px-3 -mt-0.5">
              {listData.map((item, idx) => {
                return (
                  <EverCard
                    key={idx}
                    className={twMerge(
                      item.drsId !== selectedId &&
                        gradients[item.status.toLowerCase()],
                      "px-6 mb-4 mt-2",
                      item.drsId == selectedId &&
                        (item.status.toLowerCase() == "open" ||
                        item.status.toLowerCase() == "assigned"
                          ? "border border-solid border-ever-chartColors-29/20 ring-4 ring-ever-chartColors-29/10 hover:ring-ever-chartColors-29/10 hover:border-ever-chartColors-29/50"
                          : "border border-solid border-ever-success/40 ring-4 ring-ever-success/20 hover:ring-ever-success/20 hover:border-ever-success/80"),
                      idx == listData.length - 1 ? "mb-20" : ""
                      /*item.drsId == selectedId &&
                      `bg-ever-base border-${
                        colorsMap[item.status.toLowerCase()]["border"]
                      } ring-${
                        colorsMap[item.status.toLowerCase()]["ring"]
                      } ring-4 hover:border-${
                        colorsMap[item.status.toLowerCase()]["border"]
                      }`*/
                    )}
                    onClick={() => {
                      setSelectedId(item.drsId);
                    }}
                    interactive
                    shadowSize="none"
                    roundedSize="xl"
                  >
                    <div className="mb-10 w-full">
                      <div className="flex justify-between w-full">
                        <EverTg.SubHeading3
                          title={item.subject}
                          className="text-ever-base-content"
                        >
                          {item.subject}
                        </EverTg.SubHeading3>
                        <EverBadge
                          title={"DRS#".concat(item.sequenceNumber)}
                          type="base"
                          className="text-base"
                        />
                      </div>
                      <div className="flex w-full gap-2 my-2">
                        {item.status === "Assigned" && (
                          <EverBadge
                            title="Open"
                            icon={<TicketSolidIcon className="w-3.5 h-3.5" />}
                            type="warning"
                          />
                        )}
                        <EverBadge
                          title={
                            item.status == "Closed" ? "Resolved" : item.status
                          }
                          icon={
                            item.status == "Open" ? (
                              <TicketSolidIcon className="w-3.5 h-3.5 " />
                            ) : item.status == "Assigned" ? (
                              <UsersPlusIcon className="w-3.5 h-3.5" />
                            ) : (
                              <ChartBreakoutSquareIcon className="w-3.5 h-3.5" />
                            )
                          }
                          type={
                            item.status == "Open"
                              ? "warning"
                              : item.status == "Assigned"
                              ? "base"
                              : "success"
                          }
                        />
                        <EverBadge
                          title={item.category}
                          icon={<TicketLineIcon className="w-3.5 h-3.5" />}
                          type="base"
                        />
                      </div>
                    </div>
                    <div className="flex justify-between">
                      {Object.keys(detailsList).map((label, idx) => {
                        return (
                          <div key={idx}>
                            <div className="flex flex-col gap-2">
                              <EverLabel className="text-xs">{label}</EverLabel>
                              <EverTg.SubHeading4 className="text-ever-base-content">
                                {detailsList[label] === "loggedTime" ||
                                detailsList[label] === "lastUpdatedTime"
                                  ? dateFormat(item[detailsList[label]])
                                  : item[detailsList[label]]}
                              </EverTg.SubHeading4>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </EverCard>
                );
              })}
            </div>
          </div>
          <RBACProtectedComponent permissionId={[RBAC_ROLES.EDIT_QUERIES]}>
            {listData && listData.length > 0 ? (
              <div className="overflow-hidden h-full w-5/12 mt-1">
                <div className="rounded-xl overflow-auto h-full border-solid border border-ever-base-400">
                  <AddQueryComponent store={store} savedId={selectedId} />
                </div>
              </div>
            ) : null}
          </RBACProtectedComponent>
        </div>
      ) : (
        <div className="h-full flex flex-col items-center justify-center text-center min-h-full">
          <img src={brokenLaptop} className="w-72 h-auto" />
          <div className="w-[676px] h-[374px] flex flex-col gap-2">
            <EverTg.Heading2>Get the conversation started!</EverTg.Heading2>
            <EverLabel>{t("SUBMIT_QUERY")}</EverLabel>
          </div>
        </div>
      )}
    </div>
  );
});

export default QueriesSummary;

function dateFormat(dateStr) {
  if (dateStr) {
    dateStr = dateStr.format();
    let dateTime = dateStr.split("T");
    let dateFormat = moment(dateTime[0]).format("YYYY-MMM-DD");
    let str = dateFormat + " at " + dateTime[1].substring(0, 5) + " hrs";
    return str;
  }

  return null;
}
