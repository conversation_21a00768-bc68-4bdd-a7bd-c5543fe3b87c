import { TicketSolidIcon } from "@everstage/evericons/solid";
import { observer } from "mobx-react";
import React from "react";

import { EverDrawer, EverBadge, EverTg } from "~/v2/components";

import AddQueryComponent from "./AddQueryComponent";

const RaiseQuery = observer((props) => {
  const { store, visible, setVisible } = props;

  return (
    <div className="drsSummary">
      <EverDrawer
        title={
          <div className="flex w-full gap-3">
            <EverTg.Heading3>Raise Query</EverTg.Heading3>
            <EverBadge
              title="Open"
              type="warning"
              icon={<TicketSolidIcon className="w-3.5 h-3.5" />}
            />
          </div>
        }
        placement="right"
        closable={true}
        onClose={() => {
          setVisible(false);
        }}
        visible={visible}
        width="504px"
        className="[&_.ant-drawer-body]:!p-0"
      >
        <AddQueryComponent
          store={store}
          setVisible={setVisible}
          isDrawer={true}
        />
      </EverDrawer>
    </div>
  );
});

export default RaiseQuery;
