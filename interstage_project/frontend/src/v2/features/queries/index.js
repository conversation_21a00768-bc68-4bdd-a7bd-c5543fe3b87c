import { useQuery, gql } from "@apollo/client";
import { PlusSquareIcon } from "@everstage/evericons/outlined";
import { cloneDeep, isEmpty } from "lodash";
import { useLocalStore, observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { navPortalAtom, breadcrumbAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverButtonGroup,
  EverSelect,
  EverButton,
  EverLabel,
  EverNavPortal,
  EverNumberBadge,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import DrsStore from "~/v2/features/queries/store";

import QueriesSummary from "./QueriesSummary";
import RaiseQuery from "./RaiseQuery";

const filterList = [
  {
    label: "All",
    key: "allTickets",
  },
  {
    label: "Open",
    key: "open",
  },
  {
    label: "Resolved",
    key: "closed",
  },
  {
    label: "Assigned to me",
    key: "assignedToMe",
  },
  {
    label: "Shared with me",
    key: "amPartOf",
  },
  {
    label: "Raised by me",
    key: "raisedByMe",
  },
];

const GET_QUERY_CONFIG = gql`
  query clientQuerySetting {
    clientQuerySetting
  }
`;

const DrsView = observer((props) => {
  const { ticketFilter, drsId } = useParams();
  const drsStore = useLocalStore(() => new DrsStore(), props);
  const { accessToken, user, email } = useAuthStore();
  const [visible, setVisible] = useState(false);
  const { setSortBy, setFilterOption, setQueryClientConfig } = drsStore;
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);

  const navPortalLocation = useRecoilValue(navPortalAtom);

  const { data: queryConfigData } = useQuery(GET_QUERY_CONFIG, {
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
  });

  const [defActiveBtnIndexst, setDefActiveBtnIndex] = useState(0);
  const [filterCounts, setFilterCounts] = useState({});

  useEffect(() => {
    if (accessToken) drsStore.setAccessToken(accessToken);
    if (email) drsStore.setCurrentUser(email);
    if (
      !isEmpty(queryConfigData) &&
      !isEmpty(queryConfigData.clientQuerySetting)
    ) {
      setQueryClientConfig(
        cloneDeep(JSON.parse(queryConfigData.clientQuerySetting))
      );
    }
  }, [accessToken, drsStore, user, email, queryConfigData]);

  // Update filter counts when data changes
  useEffect(() => {
    if (drsStore.allDrsData.length > 0) {
      setFilterCounts(drsStore.filterCounts);
    }
  }, [drsStore.allDrsData]);

  useEffect(() => {
    if (ticketFilter) {
      const index = filterList.findIndex((x) => x.key === ticketFilter);
      if (index !== -1) {
        setDefActiveBtnIndex(index);
      }
      setFilterOption(ticketFilter);
    }
  }, [ticketFilter]);

  useEffect(() => {
    setBreadcrumbName([
      { index: 0, disabled: true },
      {
        index: 1,
        title: "",
        hidden: true,
      },
    ]);
  }, []);

  return (
    <div className="drsView h-full">
      <EverNavPortal target={navPortalLocation}>
        <div className="flex w-full">
          <div className="flex grow gap-8">
            <EverButtonGroup
              className="bg-ever-base-200"
              activeBtnType="text"
              activeBtnColor="primary"
              defActiveBtnIndex={defActiveBtnIndexst}
              size="small"
            >
              {filterList.map((x) => {
                return (
                  <EverButton
                    key={x.key}
                    onClick={() => setFilterOption(x.key)}
                    className="!px-3"
                    appendIcon={
                      <EverNumberBadge
                        className="bg-ever-base-300 text-ever-base-content"
                        count={Number(filterCounts[x.key] || 0)}
                      />
                    }
                  >
                    <div className="flex flex-row gap-2 items-center">
                      {x.label}
                    </div>
                  </EverButton>
                );
              })}
            </EverButtonGroup>
          </div>
          <div className="flex gap-3">
            <div className="flex items-center">
              <EverLabel>Sort by</EverLabel>
              <EverSelect
                size="small"
                defaultValue="createdOn"
                dropdownMatchSelectWidth={false}
                onChange={(value) => {
                  setSortBy(value);
                }}
                options={[
                  {
                    key: "createdOn",
                    label: "Created on",
                    value: "createdOn",
                  },
                  {
                    key: "lastUpdatedOn",
                    label: "Last updated on",
                    value: "lastUpdatedOn",
                  },
                ]}
                placeholder="Please Select"
                className="w-40"
              />
            </div>
            <RBACProtectedComponent permissionId={RBAC_ROLES.CREATE_QUERIES}>
              <EverButton
                onClick={() => setVisible(true)}
                prependIcon={<PlusSquareIcon />}
                className="py-2"
                size="small"
              >
                Raise Query
              </EverButton>
            </RBACProtectedComponent>
          </div>
        </div>
      </EverNavPortal>
      <QueriesSummary store={drsStore} drsId={drsId} />
      <RaiseQuery store={drsStore} visible={visible} setVisible={setVisible} />
    </div>
  );
});

export default DrsView;
