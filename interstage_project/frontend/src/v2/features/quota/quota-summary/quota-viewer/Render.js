import {
  CalendarIcon,
  PlusCircleIcon,
  ArrowNarrowRightIcon,
  UploadCloudIcon,
  ChevronDownIcon,
  TrashIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu, Col, Empty, Row, Space, Table } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { Fragment, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  effectiveQuotaBulkUploadImport,
  effectiveQuotaBulkUploadValidate,
  quotaBulkUploadImport,
  quotaBulkUploadValidate,
} from "~/Api/QuotaService";
import { RBAC_ROLES, ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { myClient<PERSON>tom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  getBulkQuotaFieldDefs,
  getBulkQuotaInstructions,
  getColumns,
  getColumnsFiltered,
  getEffectiveQuotaFieldDefs,
  getEffectiveQuotaInstructions,
  getFiscalStartDateAndEndDate,
} from "~/Utils/QuotaUtils";
import {
  BULK_QUOTA_TEMPLATE_DATA,
  EFFECTIVE_QUOTA_TEMPLATE_DATA,
} from "~/Utils/QuotaUtils/constants";
import {
  EverLoader,
  EverSelect,
  EverBreadcrumbPortal,
  EverTg,
  EverLabel,
  EverCollapse,
  EverActiveBadge,
  EverButtonGroup,
  EverButton,
  toast,
  EverHotToastMessage,
  EverTooltip,
  EverEmptyData,
} from "~/v2/components";
import EverUpload from "~/v2/components/ever-upload";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { removeQuotaFromPayeesServ } from "~/v2/features/quota/settings-button/QuotaSettingsService";
import { dogTearingNewspaper, lostAstronaut } from "~/v2/images";

import QuotaButton from "../../quota-button";
import SettingsButton from "../../settings-button";

const BULK_QUOTA_MODES = {
  BULK_QUOTA: "BULK_QUOTA",
  EFFECTIVE_QUOTA: "EFFECTIVE_QUOTA",
  NOT_VISIBLE: "NOT_VISIBLE",
};

const Render = observer((props) => {
  const {
    store,
    onCancel: handleCancel,
    loading,
    startMonth,
    empFullName,
    hasReportees,
    reportingQuotaLoading,
    handleReportsFreqChange,
  } = props;

  const {
    quotaCatOptions,
    fiscalYearOptions,
    setSelectedCategory,
    setSelectedFiscalYear,
    selectedCategory,
    selectedFiscalYear,
    lTeamQuota,
    payeeQuota,
    selectedPayee,
    isValidSelection,
    quotaCurrencySymbol,
    reportsQuota,
    reportFrequencies,
    accessToken,
  } = store;

  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { hasPermissions } = useUserPermissionStore();
  const allowAnnualQuotaEffectiveDated =
    clientFeatures.allowAnnualQuotaEffectiveDated;

  const { t } = useTranslation();

  // State to toggle between quota bulk upload and bulk effective upload
  const [bulkMode, setBulkMode] = useState(BULK_QUOTA_MODES.NOT_VISIBLE);

  useEffect(() => {
    const quotaOptions = hideQuotaCategories();
    if (quotaOptions && quotaOptions.length > 0) {
      setSelectedCategory(quotaOptions[0].value);
    }
  }, [quotaCatOptions, setSelectedCategory]);

  useEffect(() => {
    if (fiscalYearOptions && fiscalYearOptions.length > 0) {
      if (selectedFiscalYear === null) {
        setSelectedFiscalYear(fiscalYearOptions[0].value);
      }
    }
  }, [fiscalYearOptions, setSelectedFiscalYear]);

  const hideQuotaCategories = () => {
    const options = [];
    quotaCatOptions.forEach((item) => {
      if (
        hasPermissions(RBAC_ROLES.VIEW_HIDDENQUOTAS) ||
        !clientFeatures.hideCategories.includes(item.value)
      ) {
        options.push(item);
      }
    });
    return options;
  };

  function localizeQuotaOptions(options) {
    options.forEach((item) => {
      item.label =
        item.label == "Primary quota" ? t("PRIMARY_QUOTA") : item.label;
    });
    return options;
  }

  function handleCategoryChange(value) {
    setSelectedCategory(value);
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SWITCH_QUOTA_CATEGORIES, {
      [ANALYTICS_PROPERTIES.QUOTA_CATEGORY]: value,
      [ANALYTICS_PROPERTIES.PAYEE_NAME]: selectedPayee,
    });
  }

  function handleFiscalYearChange(value) {
    setSelectedFiscalYear(value);
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SWITCH_FISCAL_YEAR, {
      [ANALYTICS_PROPERTIES.FISAL_YEAR]: value,
      [ANALYTICS_PROPERTIES.PAYEE_NAME]: selectedPayee,
    });
  }

  const { startDate: fiscalStartDate } = getFiscalStartDateAndEndDate(
    startMonth,
    moment.utc().year(selectedFiscalYear)
  );

  const bulkQuotaTitle = t("UPLOAD_QUOTA__BULK");
  const effectiveQuotaTitle = t("EFFECTIVE_DATE__BULK");

  const bulkUploadMenu = (
    <Menu onClick={(event) => setBulkMode(event.key)}>
      <Menu.Item key={BULK_QUOTA_MODES.BULK_QUOTA}>{bulkQuotaTitle}</Menu.Item>
      {clientFeatures.quotaEffectiveDated && (
        <Menu.Item key={BULK_QUOTA_MODES.EFFECTIVE_QUOTA}>
          {effectiveQuotaTitle}
        </Menu.Item>
      )}
    </Menu>
  );

  const removeQuotaFromPayees = useMutation(
    (payload) => removeQuotaFromPayeesServ(payload, accessToken),
    {
      onSuccess: (successData) => {
        toast.custom(() => (
          <EverHotToastMessage description={successData} type="success" />
        ));
        // handleCancel() refetches the employee quota data
        handleCancel();
      },
      onError: (errorData) => {
        toast.custom(() => (
          <EverHotToastMessage description={errorData?.error} type="error" />
        ));
      },
    }
  );

  const removeQuotaButton = (
    <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_QUOTA_SETTINGS}>
      <EverTooltip title={`${t("REMOVE_PAYEE_QUOTA")}`}>
        <EverButton.Icon
          type="text"
          icon={
            <TrashIcon
              className={`h-5 w-5 ${
                removeQuotaFromPayees.isLoading
                  ? "text-ever-error-lite"
                  : "text-ever-error-hover"
              }`}
            />
          }
          disabled={
            isEmpty(selectedFiscalYear) ||
            isEmpty(selectedCategory) ||
            removeQuotaFromPayees.isLoading
          }
          onClick={() => {
            removeQuotaFromPayees.mutate({
              employeeEmailIds: [selectedPayee],
              fiscalYear: selectedFiscalYear,
              quotaCategoryName: selectedCategory,
            });
          }}
        >
          Remove
        </EverButton.Icon>
      </EverTooltip>
    </RBACProtectedComponent>
  );

  return (
    <>
      <EverBreadcrumbPortal dividerIcon={<></>}>
        <div className="w-full flex items-center gap-3 justify-end">
          <SettingsButton onCancel={handleCancel} />
          <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_QUOTAS}>
            <Dropdown overlay={bulkUploadMenu}>
              <EverButton
                size="small"
                color="base"
                type="ghost"
                className="bg-ever-base-25"
                prependIcon={
                  <UploadCloudIcon className="w-5 h-5 text-ever-base-content-mid" />
                }
                appendIcon={
                  <ChevronDownIcon className="w-5 h-5 text-ever-base-content" />
                }
              >
                {bulkQuotaTitle}
              </EverButton>
            </Dropdown>

            <EverUpload
              title={bulkQuotaTitle}
              instructions={getBulkQuotaInstructions(t)}
              fieldDefs={getBulkQuotaFieldDefs(t)}
              templateData={BULK_QUOTA_TEMPLATE_DATA}
              validateDataService={quotaBulkUploadValidate}
              importDataService={quotaBulkUploadImport}
              isVisible={bulkMode === BULK_QUOTA_MODES.BULK_QUOTA}
              handleCloseUpload={() =>
                setBulkMode(BULK_QUOTA_MODES.NOT_VISIBLE)
              }
            />

            <EverUpload
              title={effectiveQuotaTitle}
              instructions={getEffectiveQuotaInstructions(t)}
              fieldDefs={getEffectiveQuotaFieldDefs(t)}
              templateData={EFFECTIVE_QUOTA_TEMPLATE_DATA}
              validateDataService={effectiveQuotaBulkUploadValidate}
              importDataService={effectiveQuotaBulkUploadImport}
              isVisible={bulkMode === BULK_QUOTA_MODES.EFFECTIVE_QUOTA}
              handleCloseUpload={() =>
                setBulkMode(BULK_QUOTA_MODES.NOT_VISIBLE)
              }
            />
          </RBACProtectedComponent>
        </div>
      </EverBreadcrumbPortal>
      {loading ? (
        <div className="flex h-[90%] w-full items-center justify-center">
          <EverLoader.SpinnerLottie className="w-12 h-12" />
        </div>
      ) : isValidSelection ? (
        <div className="flex flex-col h-full">
          <div className="flex items-center pt-2 px-6 flex-wrap sticky top-0">
            <Space className="mr-6">
              <EverLabel>Fiscal Year</EverLabel>
              <EverSelect
                prependIcon={
                  <CalendarIcon className="w-5 h-5 text-ever-base-content-low" />
                }
                options={fiscalYearOptions}
                className="text-xs w-40"
                onChange={handleFiscalYearChange}
                value={selectedFiscalYear}
                disabled={!selectedCategory}
                placeholder="Select Year"
              />
            </Space>
            <Space>
              <EverLabel>{t("QUOTA_CATEGORY")}</EverLabel>
              <EverSelect
                showSearch
                options={localizeQuotaOptions(hideQuotaCategories())}
                filterOption={(input, option) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                className="text-xs w-[200px]"
                onChange={handleCategoryChange}
                value={selectedCategory}
                placeholder="Select Category"
                data-testid="pt-quota-selector"
              />
            </Space>

            <div className="ml-auto">
              <Space>
                {selectedPayee && (
                  <QuotaButton
                    type="filled"
                    buttonText={t("ADD_QUOTA")}
                    employeeEmail={selectedPayee}
                    isTeam={false}
                    onFinish={handleCancel}
                    loading={loading}
                    icon={<PlusCircleIcon />}
                  />
                )}
              </Space>
            </div>
          </div>
          <div className="pb-4 px-6 overflow-auto">
            {hasReportees ? (
              <Fragment>
                <Row>
                  <Col span={24}>
                    <Space size="small" className="pt-8 pb-4">
                      <EverTg.Heading4 className="text-ever-base-content">
                        {empFullName} & Team{" "}
                        <EverTg.Description>
                          ({t("QUOTA_AS_MANAGER")})
                        </EverTg.Description>
                      </EverTg.Heading4>
                      <QuotaButton
                        type="editIcon"
                        employeeEmail={selectedPayee}
                        isTeam={true}
                        quotaCategory={selectedCategory}
                        fiscalYear={selectedFiscalYear}
                        onFinish={handleCancel}
                      />
                      {removeQuotaButton}
                    </Space>
                  </Col>
                </Row>

                {!isEmpty(lTeamQuota) ? (
                  <QuotaTablesComp
                    employeeQuota={lTeamQuota}
                    fiscalStartDate={fiscalStartDate}
                    startMonth={startMonth}
                    isMultiEffectiveAnnual={allowAnnualQuotaEffectiveDated}
                  />
                ) : (
                  <Empty
                    className={"h-2.5 mt-2.5"}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Fragment>
            ) : null}
            <Row>
              <Col span={24}>
                <Space size="small" className="pt-8 pb-4">
                  <EverTg.Heading4 className="text-ever-base-content">
                    {empFullName}{" "}
                    {hasReportees && (
                      <EverTg.Description>
                        ({t("QUOTA_AS_PLAYER")})
                      </EverTg.Description>
                    )}
                  </EverTg.Heading4>
                  <QuotaButton
                    type="editIcon"
                    employeeEmail={selectedPayee}
                    isTeam={false}
                    quotaCategory={selectedCategory}
                    fiscalYear={selectedFiscalYear}
                    onFinish={handleCancel}
                  />
                  {removeQuotaButton}
                </Space>
              </Col>
            </Row>

            {!isEmpty(payeeQuota) ? (
              <QuotaTablesComp
                employeeQuota={payeeQuota}
                fiscalStartDate={fiscalStartDate}
                startMonth={startMonth}
                isMultiEffectiveAnnual={allowAnnualQuotaEffectiveDated}
              />
            ) : (
              <Empty
                className={"h-2.5 mt-2.5"}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}

            {hasReportees && !reportingQuotaLoading && (
              <Fragment>
                <div className="flex items-center gap-4 pt-8 pb-4">
                  <EverTg.Heading4 className="text-ever-base-content">{`${empFullName}'s Reports`}</EverTg.Heading4>
                  {!isEmpty(reportsQuota) && (
                    <>
                      <EverTg.Text className="text-ever-base-content-mid ml-auto">
                        {t("QUOTA_FREQUENCY")}
                      </EverTg.Text>
                      <EverButtonGroup
                        // Passed key as selectedCategory and selectedFiscalYear to
                        // remount the component whenever either of them changes.
                        key={`${selectedCategory}-${selectedFiscalYear}`}
                        className="bg-ever-base-200 border border-solid border-ever-base-300"
                        activeBtnType="text"
                        activeBtnColor="primary"
                        defActiveBtnIndex={0}
                        size="small"
                      >
                        {reportFrequencies.map((freq) => {
                          return (
                            <EverButton
                              key={freq}
                              onClick={() => handleReportsFreqChange(freq)}
                            >
                              {freq}
                            </EverButton>
                          );
                        })}
                      </EverButtonGroup>
                    </>
                  )}
                </div>
                <Row>
                  <Col span={24}>
                    {!isEmpty(reportsQuota) ? (
                      <QuotaTable
                        data={reportsQuota}
                        startMonth={startMonth}
                        quotaCurrencySymbol={quotaCurrencySymbol}
                        hideRamp
                      />
                    ) : (
                      <div>Not Available</div>
                    )}
                  </Col>
                </Row>
              </Fragment>
            )}
          </div>
        </div>
      ) : selectedPayee ? (
        <div className="h-full flex items-center justify-center">
          <div className="flex items-center justify-center h-full transform -translate-y-16">
            <div className="flex flex-col h-full items-center gap-1 justify-center">
              <img src={dogTearingNewspaper} />
              {hasPermissions(RBAC_ROLES.MANAGE_QUOTAS) ? (
                <>
                  <EverTg.Heading2 className="text-ever-base-content">
                    It&apos;s time for a little motivation ⚡️
                  </EverTg.Heading2>
                  <EverTg.Text className="text-ever-base-content-mid mt-2 mb-6">
                    {t("NO_QUOTA_AVAILABLE")}
                  </EverTg.Text>
                  <QuotaButton
                    employeeEmail={selectedPayee}
                    isTeam={false}
                    quotaCategory={selectedCategory}
                    fiscalYear={selectedFiscalYear}
                    onFinish={handleCancel}
                    buttonText={t("ADD_QUOTA")}
                    type="filled"
                    icon={<PlusCircleIcon />}
                  />
                </>
              ) : (
                <>
                  <EverTg.Heading2 className="text-ever-base-content">
                    {`Hang tight! Your ${t(
                      "QUOTAS"
                    ).toLowerCase()} are coming soon.`}
                  </EverTg.Heading2>
                  <EverTg.Text className="text-ever-base-content-mid flex items-center">
                    {`Enjoy the serenity while your admin works their magic to
                    unleash your ${t("QUOTAS").toLowerCase()}.`}
                  </EverTg.Text>
                </>
              )}
            </div>
          </div>
        </div>
      ) : (
        <EverEmptyData
          title="Set targets, track progress, and unlock the full potential"
          description={t("NO_PAYEE_SELECTED_DESCRIPTION_1")}
          imgSrc={lostAstronaut}
        />
      )}
    </>
  );
});

const QuotaTable = observer((props) => {
  const {
    data,
    startMonth,
    fiscalStartDate,
    currStart,
    nextStart,
    quotaScheduleType,
    quotaCurrencySymbol,
    hideRamp,
    isMultiEffectiveAnnual,
  } = props;
  // hideRamp will be true for reportees table, there we don't have to
  // filter the columns based on effective dates.
  const cols =
    hideRamp || isMultiEffectiveAnnual
      ? getColumns(data, startMonth, quotaCurrencySymbol)
      : getColumnsFiltered(
          data,
          quotaScheduleType,
          fiscalStartDate,
          currStart,
          nextStart,
          quotaCurrencySymbol
        );
  const colsWithHeader = [
    {
      title: "",
      key: "rowHeader",
      dataIndex: "rowHeader",
      width: 194,
      fixed: "left",
    },
  ].concat(cols);

  const { t } = useTranslation();

  return (
    <Table
      className="quotasDrawsTable"
      columns={colsWithHeader}
      dataSource={getRows(hideRamp, t)}
      pagination={false}
      scroll={{ x: true }}
      size="small"
    />
  );
});

const QuotaTablesComp = observer((props) => {
  const { employeeQuota, startMonth, fiscalStartDate, isMultiEffectiveAnnual } =
    props;
  const { t } = useTranslation();
  return (
    <div>
      <div className="flex gap-3 mb-3 items-center">
        <EverTg.Text>
          {/* scheduleQuota[0] is the active quota */}
          {employeeQuota[0].quotaType} {t("QUOTA_FROM")}
          {moment(employeeQuota[0].effectiveStartDate).format("MMM DD, YYYY")}
        </EverTg.Text>
        <EverActiveBadge type="success" />
      </div>
      <div className="flex flex-col gap-6">
        {employeeQuota.map((quota, idx) => {
          const esd = moment.utc(quota.effectiveStartDate);
          const eed = moment.utc(quota.effectiveEndDate);
          // Note that employeeQuota is sorted in descending order of esd
          // resulting in latest quota being in index 0 and so on.
          const nextEsd = employeeQuota?.[idx - 1]?.effectiveStartDate
            ? moment.utc(employeeQuota[idx - 1].effectiveStartDate)
            : null;

          if (idx === 0) {
            return (
              <QuotaTable
                data={quota.scheduleQuota}
                startMonth={startMonth}
                fiscalStartDate={fiscalStartDate}
                currStart={esd.clone()}
                quotaScheduleType={quota.quotaScheduleType}
                quotaCurrencySymbol={quota.quotaCurrencySymbol}
                key={idx}
              />
            );
          }

          return (
            <EverCollapse key={idx} bordered={true} onClick={() => {}}>
              <EverCollapse.Panel
                header={
                  <div className="flex items-center gap-1">
                    <EverTg.Text>
                      {quota.quotaType} {t("QUOTA_FROM")}
                      {esd.format("MMM DD, YYYY")}
                    </EverTg.Text>
                    <ArrowNarrowRightIcon className="w-4 h-4 text-ever-primary" />
                    <span>{eed.format("MMM DD, YYYY")}</span>
                  </div>
                }
              >
                <QuotaTable
                  data={quota.scheduleQuota}
                  startMonth={startMonth}
                  fiscalStartDate={fiscalStartDate}
                  currStart={esd.clone()}
                  nextStart={nextEsd?.clone()}
                  quotaScheduleType={quota.quotaScheduleType}
                  quotaCurrencySymbol={quota.quotaCurrencySymbol}
                  isMultiEffectiveAnnual={
                    isMultiEffectiveAnnual &&
                    quota.quotaScheduleType === "Annual"
                  }
                />
              </EverCollapse.Panel>
            </EverCollapse>
          );
        })}
      </div>
    </div>
  );
});

const getRows = (hideRamp, t) => {
  const rows = [{ rowHeader: t("QUOTA"), rowSystemName: "quota" }];
  if (!hideRamp) {
    rows.push({ rowHeader: "Ramp %", rowSystemName: "ramp" });
  }
  rows.push({
    rowHeader: t("RAMP_ADJUSTED_QUOTA"),
    rowSystemName: "rampedQuota",
  });
  return rows;
};

export default Render;
