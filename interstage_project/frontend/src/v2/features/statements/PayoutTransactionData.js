import { AgGridReact } from "ag-grid-react";
import { format } from "date-fns";
import { isNumber, isEmpty } from "lodash";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverHotToastMessage, toast } from "~/v2/components";
import { everAgGridOptions, EverAgCellTooltip } from "~/v2/components/ag-grid";
import { TransactionText } from "~/v2/features/commissions/Common";
import { getCurrencyValue } from "~/v2/features/commissions/constants";
import { createQuotes } from "~/v2/images";

const PayoutTransactionComponent = (props) => {
  const { selectedPeriod, employeeEmailId } = props;
  const [payoutTransactionData, setPayoutTransactionData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const { accessToken } = useAuthStore();
  const { t } = useTranslation();
  useEffect(() => {
    setPayoutTransactionData(null);
    setIsLoading(true);

    const url = new URL(
      "/spm/statement_payout_transaction_data",
      window.location.origin
    );
    const ped =
      !isEmpty(selectedPeriod) &&
      Object.values(selectedPeriod)[0][1].format("DD-MM-YYYY");

    fetch(`${url}/${employeeEmailId}/${ped}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then(async (response) => {
        if (!response.ok) {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description="Something went wrong"
              />
            ),
            { position: "top-center", duration: 3 }
          );
          setIsLoading(false);
          setPayoutTransactionData([]);
          return;
        }
        const responseData = await response.json();
        setPayoutTransactionData(responseData.data);
        setIsLoading(false);
      })
      .catch(() => {
        setIsLoading(false);
        setPayoutTransactionData([]);
      });
  }, [selectedPeriod, employeeEmailId, accessToken]);

  const columnDefs = [
    {
      headerName: "Date",
      field: "date",
      minWidth: 200,
      suppressMenu: true,
      cellRenderer: (params) => {
        return (
          <TransactionText isPaid={params.data?.isPaid ?? true}>
            {params.value
              ? format(new Date(params.value), "dd MMM yyyy")
              : "--"}
          </TransactionText>
        );
      },
    },
    {
      headerName: "Amount",
      field: "amount",
      minWidth: 200,
      suppressMenu: true,
      cellRenderer: (params) => {
        const amount = Number.parseFloat(params.value);
        return (
          <TransactionText isPaid={params.data?.isPaid ?? true}>
            {!Number.isNaN(amount) && isNumber(amount)
              ? getCurrencyValue(params.data?.payeeCurrencySymbol, amount)
              : "--"}
          </TransactionText>
        );
      },
    },
    {
      headerName: "Comments",
      field: "comment",
      maxWidth: 450,
      suppressMenu: true,
      cellClass: "[&>*]:truncate",
      tooltipField: "comment",
      tooltipComponent: EverAgCellTooltip,
      cellRenderer: (params) => {
        return (
          <TransactionText isPaid={params.data?.isPaid ?? true}>
            {params.value || "--"}
          </TransactionText>
        );
      },
      tooltipValueGetter: (params) => params.value || "--",
    },
  ];

  const defaultColumnDefs = {
    sortable: false,
    headerClass: "!text-xs",
    resizable: true,
  };

  return (
    <div className="w-full h-full pt-4 pb-4">
      <div className="ag-theme-material zebra-grid flex flex-col flex-auto w-full h-[540px]">
        <AgGridReact
          {...everAgGridOptions.getDefaultOptions({ type: "md" })}
          noRowsOverlayComponentParams={{
            title: `No data to display`,
            subTitle: `${t("PAYOUTS")} and ${t(
              "ARREARS"
            )} for the current period have not been processed by the admin yet.`,
            imgSrc: createQuotes,
          }}
          loading={isLoading}
          rowData={payoutTransactionData}
          columnDefs={columnDefs}
          defaultColDef={defaultColumnDefs}
          pagination={false}
          suppressCellFocus={true}
          suppressRowClickSelection={true}
          debounceVerticalScrollbar={true}
        />
      </div>
    </div>
  );
};

export default PayoutTransactionComponent;
