import { WalletOpenIcon } from "@everstage/evericons/duotone";
import {
  SlashCircleIcon,
  CircleCheckIcon,
  LogOutIcon,
  ReverseLeftIcon,
} from "@everstage/evericons/outlined";
import {
  CoinsStackedIcon,
  LockIcon,
  HourglassIcon,
  MinusCircleIcon,
  UnlockIcon,
} from "@everstage/evericons/solid";
import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useSpring, animated } from "react-spring";
import { twMerge } from "tailwind-merge";

import {
  PAYMENT_STATUS,
  APPROVAL_STATUS,
  APPROVAL_STATUS_STATEMENTS,
} from "~/Enums";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverTooltip, EverBadge, EverTg } from "~/v2/components";

import styles from "./styles.module.scss";

const AmountCard = (props) => {
  const {
    status = "",
    title,
    amount,
    approvalStatus,
    currencySymbol,
    localeId,
    isLocked = false,
    showCommissionPayouts = false,
    store,
  } = props;
  const { selectedPeriod, payeePeriodOptions } = store;
  const [currentPeriod, setCurrentPeriod] = useState([]);
  const [hover, setHover] = useState(false);
  const [showEllipsis, setShowEllipsis] = useState(true);
  const containerRef = useRef(null);
  const contentRef = useRef(null);
  const [translateX, setTranslateX] = useState(0);

  useEffect(() => {
    const arr = payeePeriodOptions.filter(
      (ele) => ele.value == Object.keys(selectedPeriod)[0]
    );
    setCurrentPeriod(arr);
  }, [selectedPeriod, payeePeriodOptions]);

  const { t } = useTranslation();

  const renderStatusBadges = () => {
    return (
      <div className="mt-4 ml-1 flex flex-wrap gap-3">
        <EverBadge
          className="bg-ever-base-200 h-8 text-sm pr-2.5"
          icon={
            isLocked ? (
              <LockIcon className="text-ever-chartColors-20 w-5 h-5" />
            ) : (
              <UnlockIcon className="text-ever-chartColors-20 w-5 h-5" />
            )
          }
          title={isLocked ? t("STATEMENT_LOCKED") : t("STATEMENT_UNLOCKED")}
          type="base"
        />
        <EverBadge
          className={"bg-ever-base-200 h-8 text-sm pr-2.5"}
          icon={<CoinsStackedIcon className="text-ever-warning w-5 h-5" />}
          title={
            status === PAYMENT_STATUS.PARTIALLY_PAID
              ? "Partially paid"
              : status === PAYMENT_STATUS.PAID
              ? "Fully paid"
              : status === PAYMENT_STATUS.ZERO_PAYOUT
              ? "Zero payout"
              : status === PAYMENT_STATUS.OVER_PAID
              ? "Over paid"
              : "Not paid"
          }
          type={"base"}
        />
        {approvalStatus !== null && (
          <EverBadge
            className="bg-ever-base-200 h-8 text-sm pr-2.5"
            icon={
              approvalStatus === APPROVAL_STATUS.APPROVED ? (
                <CircleCheckIcon className="text-ever-success w-5 h-5" />
              ) : approvalStatus === APPROVAL_STATUS.REJECTED ? (
                <SlashCircleIcon className="text-ever-error w-5 h-5" />
              ) : approvalStatus === APPROVAL_STATUS.REQUESTED ? (
                <HourglassIcon className="text-ever-info-hover w-5 h-5" />
              ) : approvalStatus === APPROVAL_STATUS.WITHDRAWN ? (
                <LogOutIcon className="text-ever-chartColors-20 w-5 h-5" />
              ) : approvalStatus === APPROVAL_STATUS.ABORTED ? (
                <MinusCircleIcon className="text-ever-warning w-5 h-5" />
              ) : approvalStatus === APPROVAL_STATUS.REVOKED ? (
                <ReverseLeftIcon className="text-ever-error w-5 h-5" />
              ) : null
            }
            title={
              approvalStatus === APPROVAL_STATUS.APPROVED
                ? APPROVAL_STATUS_STATEMENTS.APPROVED
                : approvalStatus === APPROVAL_STATUS.REJECTED
                ? APPROVAL_STATUS_STATEMENTS.REJECTED
                : approvalStatus === APPROVAL_STATUS.REQUESTED
                ? APPROVAL_STATUS_STATEMENTS.PENDING
                : approvalStatus === APPROVAL_STATUS.WITHDRAWN
                ? APPROVAL_STATUS_STATEMENTS.WITHDRAWN
                : approvalStatus === APPROVAL_STATUS.ABORTED
                ? APPROVAL_STATUS_STATEMENTS.CANCELLED
                : approvalStatus === APPROVAL_STATUS.REVOKED
                ? APPROVAL_STATUS_STATEMENTS.REVOKED
                : null
            }
            type={"base"}
          />
        )}
      </div>
    );
  };

  const currencyAmount = getLocalizedCurrencyValue(
    amount,
    currencySymbol,
    localeId
  );

  const animationProps = useSpring({
    to: {
      transform: `translateX(${
        hover && currencyAmount.length > 20 ? `${translateX}px` : "0px"
      })`,
    },
    from: { transform: "translateX(0px)" },
    reset: false,
    reverse: !hover,
    config: { duration: hover ? 3000 : 1000 },
    onRest: () => {
      setShowEllipsis(true);
    },
    onStart: () => {
      setShowEllipsis(false);
    },
  });

  useEffect(() => {
    const contentWidth = contentRef.current
      ? contentRef.current.offsetWidth
      : 0;
    const containerWidth = containerRef.current
      ? containerRef.current.offsetWidth
      : 0;
    if (contentWidth > containerWidth) {
      setTranslateX(containerWidth - contentWidth);
    }
  }, [hover]);

  return (
    <EverTooltip
      mouseEnterDelay={0.5}
      title={
        showCommissionPayouts ? "" : "You don't have permission to view this."
      }
    >
      <div className="w-full">
        <div
          className={`${styles.cardBackground} flex items-center h-max flex-col p-4 h-36 rounded-xl`}
        >
          <div className="w-full flex justify-between items-start">
            <EverTooltip title={currentPeriod[0]?.label}>
              <div className="max-w-full truncate">
                <EverTg.Description className="text-ever-base-content">
                  {currentPeriod[0]?.label}
                </EverTg.Description>
              </div>
            </EverTooltip>
            <div className="flex p-2 bg-ever-chartColors-56 rounded-full">
              <WalletOpenIcon className="text-ever-chartColors-7 w-7 h-7" />
            </div>
          </div>
          <div className="flex flex-col gap-y-1 w-full justify-start">
            <div>
              <EverTg.SubHeading3 className="text-ever-base-content">
                {title}
              </EverTg.SubHeading3>
            </div>
            <div
              ref={containerRef}
              className="w-full truncate relative h-10"
              title={showCommissionPayouts ? currencyAmount : null}
              onMouseEnter={() => setHover(true)}
              onMouseLeave={() => setHover(false)}
            >
              <animated.div
                className={twMerge(
                  "absolute",
                  !hover && "flex",
                  showEllipsis && "w-full"
                )}
                style={animationProps}
              >
                <EverTg.Heading1
                  ref={contentRef}
                  className={twMerge(
                    "text-4xl font-semibold text-ever-primary-pressed tracking-[-1px] cursor-default",
                    showCommissionPayouts ? "" : "blur-sm",
                    showEllipsis && "truncate"
                  )}
                >
                  {showCommissionPayouts ? currencyAmount : "****"}
                </EverTg.Heading1>
              </animated.div>
            </div>
          </div>
        </div>
        {renderStatusBadges()}
      </div>
    </EverTooltip>
  );
};

export default AmountCard;
