import { AgGridReact } from "ag-grid-react";
import { isEmpty } from "lodash";
import React, { useMemo, useCallback } from "react";
import { twMerge } from "tailwind-merge";

import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverTg, EverTooltip } from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";
import PlanViewerWrapper from "~/v2/features/incentive-plan-designer/plan-viewer";

import "~/v2/features/statements/statement-right-container/styles.module.scss"; // Aggrid custom styling

const renderTooltip = (value, condition) => {
  return (
    <div
      className={twMerge(
        "[&>*]:truncate [&>*]:w-full [&>*]:inline-block [&>*]:align-middle",
        condition
      )}
    >
      <EverTooltip mouseEnterDelay={0.5} title={value} placement="bottom">
        {value}
      </EverTooltip>
    </div>
  );
};

const NonSettlementViewTable = (props) => {
  const {
    row,
    onGridReady,
    onRowClicked,
    currencySymbol,
    localeId,
    expandableList,
    LinkRenderer,
    employeeEmailId,
    variablePay,
    getCommissionPercent,
    showCommissionPercent,
    periodLabel,
    groupDefaultExpanded,
    showCommissionPayouts,
  } = props;

  const renderGroupCellParams = () => ({
    cellRenderer: "agGroupCellRenderer",
    cellClassRules: {
      notExpandable: (params) => {
        if (isEmpty(params?.data)) return true;
        return !params.data.children || params.data.id === "earnedCommissions";
      },
    },
  });

  const renderColumns = useMemo(() => {
    const columnDefs = [
      {
        headerName: "",
        field: "commissionName",
        cellStyle: {
          fontWeight: 500,
        },
        cellClass: "cell-auto-width",
        minWidth: 200,
        flex: 1,
        wrapText: true,
        autoHeight: true,
        ...renderGroupCellParams(),
      },
    ];

    if (row.id === "earnedCommissions" && showCommissionPercent) {
      columnDefs.push({
        headerName: "",
        field: "commissionPercentColumn",
        type: "rightAligned",
        cellClass: "cell-percentage-width",
        cellStyle: {
          fontWeight: 500,
          justifyContent: "flex-end",
        },
        maxWidth: 150,
      });
    }

    columnDefs.push(
      row.id === "earnedCommissions"
        ? {
            headerName: "",
            field: "amount",
            type: "rightAligned",
            maxWidth: 160,
            hide: !showCommissionPayouts,
          }
        : {
            headerName: "",
            field: "amount",
            type: "rightAligned",
            cellStyle: {
              paddingRight: 20,
            },
            cellClass: "cell-auto-width",
            hide: !showCommissionPayouts,
            cellRenderer: ({ value }) => {
              return getLocalizedCurrencyValue(value, currencySymbol, localeId);
            },
            cellClassRules: {
              ["text-ever-error"]: (params) => {
                if (isEmpty(params?.data)) return false;
                return Number(params.data.amount) < 0;
              },
              ["text-ever-success-hover"]: (params) => {
                if (isEmpty(params?.data)) return false;
                return Number(params.data.amount) >= 0;
              },
            },
          }
    );

    return columnDefs;
  }, [
    currencySymbol,
    localeId,
    showCommissionPayouts,
    showCommissionPercent,
    row.id,
  ]);

  const defaultColDef = useMemo(() => {
    return {
      sortable: true,
      suppressMenu: true,
      filter: true,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
    };
  }, []);

  const getGridOptions = (key) => {
    const amountColumn = (isPinned = false, cellStyle = {}) => ({
      headerName: "",
      field: "amount",
      suppressMenu: true,
      type: "rightAligned",
      maxWidth: 160,
      cellStyle: cellStyle,
      hide: !showCommissionPayouts,
      cellRenderer: ({ value }) => {
        const currencyAmount = getLocalizedCurrencyValue(
          value,
          currencySymbol,
          localeId
        );
        return renderTooltip(
          currencyAmount,
          value >= 0 ? "[&>*]:text-ever-success-hover" : "[&>*]:text-ever-error"
        );
      },
      ...(isPinned && {
        pinned: "right",
        lockPinned: true,
      }),
    });
    const commissionPercentColumn = (isPinned = false, showHeader = false) => ({
      headerName: showHeader ? "Commission %" : "",
      field: "",
      suppressMenu: true,
      type: "rightAligned",
      maxWidth: 150,
      cellRenderer: ({ data }) => {
        const amountPercentage = getCommissionPercent(data.amount, variablePay);
        return !isEmpty(data) ? renderTooltip(amountPercentage) : "";
      },
      ...(isPinned && {
        pinned: "right",
        lockPinned: true,
      }),
    });
    switch (key) {
      case "payoutArrears": {
        return {
          columnDefs: [
            {
              headerName: "Period",
              field: "period",
              suppressMenu: true,
            },
            amountColumn(),
          ],
        };
      }
      case "criteria": {
        const columnDefs = {
          columnDefs: [
            {
              headerName: "Criteria",
              field: "criteriaName",
              minWidth: 200,
              flex: 1,
              suppressMenu: true,
              cellStyle: {
                fontWeight: 500,
                paddingRight: 20,
                paddingLeft: 26,
              },
              cellClassRules: {
                notExpandableCriteria: (params) => {
                  if (isEmpty(params?.data)) return true;
                  return !params.data.children;
                },
              },
              cellRenderer: LinkRenderer,
              cellRendererParams: (params) => {
                return {
                  value: params?.value ?? "",
                  data: params?.data ?? {},
                };
              },
            },
            amountColumn(false, { paddingRight: 20, fontWeight: 500 }),
          ],
          rowStyle: { cursor: "initial" },
          headerHeight: 0,
        };
        if (row.id === "earnedCommissions" && showCommissionPercent) {
          columnDefs.columnDefs.splice(1, 0, commissionPercentColumn());
        }
        return columnDefs;
      }
      case "adjustments": {
        const adjustmentsColProps = {
          autoHeight: true,
          cellStyle: {
            whiteSpace: "no-wrap",
          },
        };
        return {
          columnDefs: [
            {
              headerName: "Reason",
              field: "reason",
              suppressMenu: true,
              minWidth: 200,
              cellRenderer: ({ value }) => {
                return renderTooltip(value);
              },
              ...adjustmentsColProps,
            },
            {
              headerName: "Reason Category",
              field: "reasonCategory",
              suppressMenu: true,
              minWidth: 150,
              ...adjustmentsColProps,
            },
            {
              headerName: "Plan",
              field: "planName",
              suppressMenu: true,
              minWidth: 150,
              cellRenderer: ({ value }) => {
                return renderTooltip(value);
              },
              ...adjustmentsColProps,
            },
            {
              headerName: "Criteria",
              field: "criteriaName",
              suppressMenu: true,
              minWidth: 150,
              ...adjustmentsColProps,
            },
            {
              headerName: "Line Item",
              field: "lineItemId",
              suppressMenu: true,
              minWidth: 150,
              ...adjustmentsColProps,
            },
            amountColumn(true),
          ],
        };
      }
      case "draws": {
        return {
          columnDefs: [
            {
              headerName: "Draw Availed",
              field: "drawAvailed",
              suppressMenu: true,
              cellRenderer: ({ value }) => {
                return (
                  <div data-testid="pt-draw-availed">
                    {getLocalizedCurrencyValue(value, currencySymbol, localeId)}
                  </div>
                );
              },
            },
            {
              headerName: "Draw Recovered",
              field: "drawRecovered",
              suppressMenu: true,
              cellRenderer: ({ value }) => {
                return (
                  <div data-testid="pt-draw-recovered">
                    {getLocalizedCurrencyValue(value, currencySymbol, localeId)}
                  </div>
                );
              },
            },
          ],
        };
      }
      case "previousDeferredCommissions": {
        return {
          columnDefs: [
            {
              headerName: "Period",
              field: "period",
              suppressMenu: true,
              ...renderGroupCellParams(),
            },
            amountColumn(),
          ],
          detailCellRendererParams: {
            ...getDetailCellRendererParams("plan"),
          },
        };
      }
      default: {
        const columnDefs = {
          columnDefs: [
            {
              headerName: "Plans",
              field: "planName",
              suppressMenu: true,
              ...renderGroupCellParams(),
              minWidth: 200,
              flex: 1,
              cellRendererParams: {
                innerRenderer: (params) => {
                  if (isEmpty(params?.data)) return <></>;
                  return (
                    <div className="flex flex-row gap-2 items-center">
                      <EverTooltip title={params.value}>
                        <div>
                          <EverTg.Text className="text-wrap h-min line-clamp-2">
                            {params.value}
                          </EverTg.Text>
                        </div>
                      </EverTooltip>
                      <div className="self-center">
                        <PlanViewerWrapper
                          planId={params.data.planId}
                          planName={params.data.planName}
                          emailId={employeeEmailId}
                          textStyle={{
                            fontSize: "14px",
                          }}
                          isSettlement={true}
                          buttonTooltipTitle="View plan details"
                          periodLabel={periodLabel}
                        />
                      </div>
                    </div>
                  );
                },
              },
            },
            amountColumn(false, { fontWeight: 600, paddingRight: 20 }),
          ],
          detailCellRendererParams: {
            ...getDetailCellRendererParams("criteria"),
          },
          headerHeight: 0,
        };

        if (row.id === "earnedCommissions" && showCommissionPercent) {
          columnDefs.columnDefs.splice(1, 0, commissionPercentColumn());
        }

        return columnDefs;
      }
    }
  };

  // separate expand/collapse function for child rows
  function onChildRowClicked(event) {
    const isExpanded = event.node.expanded;
    event.api.setRowNodeExpanded(event.node, !isExpanded);
  }

  const getDetailCellRendererParams = (key) => {
    const params = {
      detailGridOptions: {
        defaultColDef: {
          flex: 1,
        },
        headerHeight: 48,
        detailRowAutoHeight: true,
        masterDetail: true,
        suppressContextMenu: true,
        keepDetailRows: true,
        onRowClicked: onChildRowClicked,
        ...getGridOptions(key),
      },
      getDetailRowData: function (params) {
        params.successCallback(params.data.children);
      },
    };
    return params;
  };

  const detailCellRendererParams = useMemo(() => {
    return getDetailCellRendererParams(row.id);
  }, [currencySymbol, expandableList]);

  const getRowId = useCallback((params) => params?.data?.id, []);
  const getRowStyle = useCallback((params) => {
    if (params?.data?.id === "earnedCommissions") {
      return { cursor: "initial" };
    }
    return null;
  }, []);

  // expandable only if children are present
  const isRowMaster = useCallback((dataItem) => {
    return dataItem?.children?.length > 0;
  }, []);

  return (
    <div className="w-full h-auto">
      {row && (
        <div className="h-full w-full ag-theme-material statementTable nonSettlementStatementTable no-min-height">
          <AgGridReact
            {...everAgGridOptions.getDefaultOptions({ type: "md" })}
            columnDefs={renderColumns}
            defaultColDef={defaultColDef}
            rowData={[row]}
            suppressRowHoverHighlight={false}
            onGridReady={(params) => onGridReady(params, row.id)}
            suppressContextMenu={true}
            masterDetail={true}
            isRowMaster={isRowMaster}
            detailCellRendererParams={detailCellRendererParams}
            detailRowAutoHeight={true}
            enableRangeSelection={false}
            keepDetailRows={true}
            getRowId={getRowId}
            onRowClicked={onRowClicked}
            getRowStyle={getRowStyle}
            groupDefaultExpanded={groupDefaultExpanded}
            animateRows={false}
          />
        </div>
      )}
    </div>
  );
};

export default NonSettlementViewTable;
