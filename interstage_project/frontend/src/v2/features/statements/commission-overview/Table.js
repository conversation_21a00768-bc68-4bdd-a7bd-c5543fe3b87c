import { AgGridReact } from "ag-grid-react";
import { Space } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import React, { useMemo, useCallback, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverLabel, EverSelect, EverTg, EverTooltip } from "~/v2/components";
import {
  everAgGridCallbacks,
  everAgGridOptions,
} from "~/v2/components/ag-grid";
import PlanViewerWrapper from "~/v2/features/incentive-plan-designer/plan-viewer";

import "~/v2/features/statements/statement-right-container/styles.module.scss"; // Aggrid custom styling

const PeriodTotal = (props) => {
  const { currencySymbol, periodTotalAmount, localeId } = props;

  return (
    <EverTg.Text
      className={`ml-auto ${
        periodTotalAmount >= 0 ? "text-ever-success-hover" : "text-ever-error"
      }`}
    >
      {getLocalizedCurrencyValue(periodTotalAmount, currencySymbol, localeId)}
    </EverTg.Text>
  );
};

const renderTooltip = (value, condition) => {
  return (
    <div
      className={twMerge(
        "[&>*]:truncate [&>*]:w-full [&>*]:inline-block [&>*]:align-middle",
        condition
      )}
    >
      <EverTooltip mouseEnterDelay={0.5} title={value} placement="bottom">
        {value}
      </EverTooltip>
    </div>
  );
};

const PeriodsDropdown = (props) => {
  const {
    prevDeferredCommPeriods,
    setPrevDeferredCommPeriod,
    prevDeferredCommPeriod,
    expandPrevDeferredCommissionRow,
  } = props;

  const { t } = useTranslation();

  return prevDeferredCommPeriods.length === 0 ? (
    <EverTg.Text className="text-ever-base-content">
      {t("NO_PREVIOUS_DEFERRED_COMMISSIONS")}
    </EverTg.Text>
  ) : (
    <>
      <Space>
        <EverLabel>{t("PAYOUTS_FROM_DEFERRED_COMMISSIONS")}</EverLabel>
        <EverSelect
          className="w-64"
          value={prevDeferredCommPeriod}
          onChange={(value) => {
            setPrevDeferredCommPeriod(value);
            // Make the tab one level opened whenever the period is changed
            expandPrevDeferredCommissionRow();
          }}
          options={prevDeferredCommPeriods.map((period) => ({
            value: period,
            label: period,
          }))}
        />
      </Space>
    </>
  );
};

const Table = (props) => {
  const {
    row,
    onGridReady,
    gridApiAll,
    onRowClicked,
    currencySymbol,
    localeId,
    expandableList,
    LinkRenderer,
    employeeEmailId,
    showCommissionPayouts,
    periodLabel,
    expandEarnedCommissionRow = null,
    expandPrevDeferredCommissionRow = null,
  } = props;

  const periodPrevCommissionMap = useMemo(() => {
    const periodPrevCommissionMap = {};

    // creating a map of period to previous deferred commission details
    if (row && row.id === "previousDeferredCommissions" && row.children) {
      for (const item of row.children) {
        periodPrevCommissionMap[item.period] = item;
      }
    }
    return periodPrevCommissionMap;
  }, [row]);

  const prevDeferredCommPeriods = Object.keys(periodPrevCommissionMap);

  const { t } = useTranslation();

  // Take recent period as default
  const [prevDeferredCommPeriod, setPrevDeferredCommPeriod] = useState(
    prevDeferredCommPeriods.length > 0 ? prevDeferredCommPeriods.at(-1) : null
  );

  // cloning the row value with period to avoid mutating the original row value
  const rowValue =
    cloneDeep(periodPrevCommissionMap[prevDeferredCommPeriod]) ?? row;

  const periodTotalAmount = useMemo(() => {
    return rowValue?.amount;
  }, [prevDeferredCommPeriod]);

  /* 
    For previous deferred commissions:
    row: {
      id: "previousDeferredCommissions",
      amount: 1000,
      ...
      children: [
        id: "previousDeferredCommissions_prevDeferredDetails_0",
        period: "January 2022",
        amount: 500,
        ...
      ]
    }

    rowValue: {
      id: "previousDeferredCommissions_prevDeferredDetails_0",
      period: "January 2022",
      amount: 500,
      ...
      children: [
        amount: 100,
          ...
      ]
    }

    Had to update commissionName, id and amount in `rowValue` from `row` to make 
    the rowValue same format as of row.
  */
  if (row.id === "previousDeferredCommissions") {
    rowValue.commissionName = t("PREVIOUSLY_DEFERRED_COMMISSIONS");
    rowValue.id = "previousDeferredCommissions";
    rowValue.amount = row.amount;
  }

  const getCellClassRules = useCallback(
    (component) => {
      // STYLES:
      // PayoutPaid: GREEN
      // PayoutPending: RED
      // PayoutNeutral: BLACK
      // Except for criteria all are in bold
      return {
        [component === "criteria"
          ? "text-ever-error"
          : "text-ever-error font-medium"]: (params) => {
          if (isEmpty(params?.data)) return false;
          return (
            rowValue.id === "deferredCommissions" ||
            (rowValue.id === "currentPayouts" &&
              Number(params.data.amount) < 0) ||
            (rowValue.id === "draws" && Number(params.data.amount) < 0) ||
            (rowValue.id === "adjustments" && Number(params.data.amount) < 0)
          );
        },
        [component === "criteria"
          ? "text-ever-success-hover"
          : "text-ever-success-hover font-medium"]: (params) => {
          if (isEmpty(params?.data)) return false;
          return (
            rowValue.id === "previousDeferredCommissions" ||
            rowValue.id === "payoutArrears" ||
            (rowValue.id === "currentPayouts" &&
              Number(params.data.amount) >= 0) ||
            (rowValue.id === "draws" && Number(params.data.amount) >= 0) ||
            (rowValue.id === "adjustments" && Number(params.data.amount) >= 0)
          );
        },
        [component === "criteria"
          ? "text-ever-base-content"
          : "text-ever-base-content font-medium"]: () => {
          return rowValue.id === "earnedCommissions";
        },
      };
    },
    [rowValue.id]
  );

  const renderGroupCellParams = () => ({
    cellRenderer: "agGroupCellRenderer",
    cellClassRules: {
      notExpandable: (params) => {
        if (isEmpty(params?.data)) return true;
        return !params.data.children;
      },
    },
  });

  const renderColumns = useMemo(() => {
    return [
      {
        headerName: "",
        field: "commissionName",
        cellStyle: {
          fontWeight: 500,
        },
        cellClass: "cell-auto-width",
        width: 300,
        wrapText: true,
        autoHeight: true,
        ...renderGroupCellParams(),
      },
      {
        headerName: "",
        field: "amount",
        type: "rightAligned",
        hide: !showCommissionPayouts,
        cellClass: "cell-auto-width",
        cellStyle: {
          fontWeight: 500,
          paddingRight: 20,
        },
        cellRenderer: ({ value }) => {
          return getLocalizedCurrencyValue(value, currencySymbol, localeId);
        },
        cellClassRules: getCellClassRules(),
      },
    ];
  }, [currencySymbol, localeId, showCommissionPayouts, getCellClassRules]);

  const defaultColDef = useMemo(() => {
    return {
      sortable: true,
      suppressMenu: true,
      filter: true,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
    };
  }, []);

  const getColumnDefs = (key) => {
    const amountColumn = (isPinned = false, component = null) => {
      return {
        headerName: "",
        field: "amount",
        suppressMenu: true,
        type: "rightAligned",
        width: 180,
        cellClassRules: getCellClassRules(component),
        hide: !showCommissionPayouts,
        cellRenderer: ({ value }) => {
          return getLocalizedCurrencyValue(value, currencySymbol, localeId);
        },
        ...(isPinned && {
          pinned: "right",
          lockPinned: true,
        }),
      };
    };
    switch (key) {
      case "payoutArrears": {
        return {
          columnDefs: [
            {
              headerName: "Period",
              field: "period",
              suppressMenu: true,
            },
            amountColumn(),
          ],
        };
      }
      case "criteria": {
        return {
          columnDefs: [
            {
              headerName: "Criteria",
              field: "criteriaName",
              suppressMenu: true,
              cellStyle: {
                fontWeight: 500,
                paddingRight: 20,
                paddingLeft: 26,
              },
              cellClassRules: {
                notExpandableCriteria: (params) => {
                  if (isEmpty(params?.data)) return true;
                  return !params.data.children;
                },
              },
              cellRenderer: LinkRenderer,
              cellRendererParams: (params) => {
                return {
                  value: params?.value ?? "",
                  data: params?.data ?? {},
                };
              },
            },
            amountColumn(false, "criteria"),
          ],
          headerHeight: 0,
        };
      }
      case "adjustments": {
        const adjustmentsColProps = {
          autoHeight: true,
          cellStyle: {
            whiteSpace: "break-spaces",
          },
        };

        return {
          columnDefs: [
            {
              headerName: "Reason",
              field: "reason",
              suppressMenu: true,
              minWidth: 200,
              cellRenderer: ({ value }) => {
                return renderTooltip(value);
              },
              ...adjustmentsColProps,
            },
            {
              headerName: "Reason Category",
              field: "reasonCategory",
              suppressMenu: true,
              minWidth: 150,
              cellRenderer: ({ value }) => {
                return renderTooltip(value);
              },
              ...adjustmentsColProps,
            },
            {
              headerName: "Plan",
              field: "planName",
              suppressMenu: true,
              minWidth: 150,
              cellRenderer: ({ value }) => {
                return renderTooltip(value);
              },
              ...adjustmentsColProps,
            },
            {
              headerName: "Criteria",
              field: "criteriaName",
              suppressMenu: true,
              minWidth: 150,
              ...adjustmentsColProps,
            },
            {
              headerName: "Line Item",
              field: "lineItemId",
              suppressMenu: true,
              minWidth: 150,
              ...adjustmentsColProps,
            },
            amountColumn(true),
          ],
        };
      }
      case "draws": {
        return {
          columnDefs: [
            {
              headerName: "Draw Availed",
              field: "drawAvailed",
              suppressMenu: true,
              cellRenderer: ({ value }) => {
                return getLocalizedCurrencyValue(
                  value,
                  currencySymbol,
                  localeId
                );
              },
            },
            {
              headerName: "Draw Recovered",
              field: "drawRecovered",
              suppressMenu: true,
              cellRenderer: ({ value }) => {
                return getLocalizedCurrencyValue(
                  value,
                  currencySymbol,
                  localeId
                );
              },
            },
          ],
        };
      }
      case "previousDeferredCommissions": {
        return {
          columnDefs: [
            {
              headerName: "Plans",
              headerComponent: PeriodsDropdown,
              headerComponentParams: {
                prevDeferredCommPeriods: prevDeferredCommPeriods,
                prevDeferredCommPeriod: prevDeferredCommPeriod,
                setPrevDeferredCommPeriod: setPrevDeferredCommPeriod,
                expandPrevDeferredCommissionRow:
                  expandPrevDeferredCommissionRow,
              },
              field: "planName",
              flex: 2,
              suppressMenu: true,
              ...renderGroupCellParams(),
              wrapText: true,
              autoHeight: true,
              cellRendererParams: {
                innerRenderer: (params) => {
                  if (isEmpty(params?.data)) return <></>;
                  return (
                    <div className="flex flex-row gap-2 items-center">
                      <EverTooltip title={params.value}>
                        <div>
                          <EverTg.Text className="text-wrap h-min line-clamp-2">
                            {params.value}
                          </EverTg.Text>
                        </div>
                      </EverTooltip>
                      <div className="self-center">
                        <PlanViewerWrapper
                          planId={params.data.planId}
                          planName={params.data.planName}
                          emailId={employeeEmailId}
                          textStyle={{
                            fontSize: "14px",
                          }}
                          isSettlement={true}
                          periodLabel={periodLabel}
                          buttonTooltipTitle="View plan details"
                        />
                      </div>
                    </div>
                  );
                },
              },
            },
            {
              headerComponent: PeriodTotal,
              headerComponentParams: {
                currencySymbol: currencySymbol,
                periodTotalAmount: periodTotalAmount,
                localeId: localeId,
              },
              headerName: "",
              field: "amount",
              suppressMenu: true,
              type: "rightAligned",
              width: 100,
              cellClassRules: getCellClassRules(),
              hide: !showCommissionPayouts,
              cellRenderer: ({ value }) => {
                return getLocalizedCurrencyValue(
                  value,
                  currencySymbol,
                  localeId
                );
              },
            },
          ],
          detailCellRendererParams: {
            ...getDetailGridOptions("criteria"),
          },
        };
      }
      default: {
        return {
          columnDefs: [
            {
              headerName: "Plan",
              field: "planName",
              suppressMenu: true,
              ...renderGroupCellParams(),
              wrapText: true,
              autoHeight: true,
              cellRendererParams: {
                innerRenderer: (params) => {
                  if (isEmpty(params?.data)) return <></>;
                  return (
                    <div className="flex flex-row gap-2 items-center">
                      <EverTooltip title={params.value}>
                        <div>
                          <EverTg.Text className="text-wrap h-min line-clamp-2">
                            {params.value}
                          </EverTg.Text>
                        </div>
                      </EverTooltip>
                      <div className="self-center">
                        <PlanViewerWrapper
                          planId={params.data.planId}
                          planName={params.data.planName}
                          emailId={employeeEmailId}
                          textStyle={{
                            fontSize: "14px",
                          }}
                          isSettlement={true}
                          periodLabel={periodLabel}
                          buttonTooltipTitle="View plan details"
                        />
                      </div>
                    </div>
                  );
                },
              },
            },
            amountColumn(),
          ],
          detailCellRendererParams: {
            ...getDetailGridOptions("criteria"),
          },
          headerHeight: 0,
        };
      }
    }
  };

  // separate expand/collapse function for child rows
  function onChildRowClicked(event) {
    const isExpanded = event.node.expanded;
    event.api.setRowNodeExpanded(event.node, !isExpanded);
  }

  const getDetailGridOptions = (key) => {
    return {
      detailGridOptions: {
        defaultColDef: {
          flex: 1,
        },
        headerHeight: 48,
        detailRowAutoHeight: true,
        masterDetail: true,
        suppressContextMenu: true,
        keepDetailRows: true,
        onRowClicked: onChildRowClicked,
        rowClass: key !== "criteria" ? "nonSettlementStatementTable" : "",
        ...getColumnDefs(key),
      },
      getDetailRowData: function (params) {
        params.successCallback(params.data.children);
      },
    };
  };

  // To update the total period amount whenever a period chosen in the dropdown.
  // The period is rendered as a header component in the grid. So changing the columnDefs
  // would eventually update the header component.
  useEffect(() => {
    const gridApi = gridApiAll[row.id];
    if (isEmpty(gridApi) || gridApi.destroyCalled) return;

    const gridInfo = gridApi?.getDetailGridInfo(
      "detail_previousDeferredCommissions"
    );
    if (gridInfo) {
      const colDefs = gridInfo.api.getColumnDefs();

      const nameCol = colDefs[0];
      nameCol.headerComponentParams = {
        prevDeferredCommPeriods: prevDeferredCommPeriods,
        setPrevDeferredCommPeriod: setPrevDeferredCommPeriod,
        prevDeferredCommPeriod: prevDeferredCommPeriod,
        expandPrevDeferredCommissionRow: expandPrevDeferredCommissionRow,
      };

      const amountCol = colDefs[1];
      amountCol.headerComponentParams = {
        currencySymbol: currencySymbol,
        periodTotalAmount: periodTotalAmount,
      };

      const newColDefs = [nameCol, amountCol];
      gridInfo.api.setGridOption("columnDefs", newColDefs);
    }
  }, [prevDeferredCommPeriod, currencySymbol, gridApiAll]);

  const detailCellRendererParams = useMemo(() => {
    return getDetailGridOptions(rowValue.id);
  }, [currencySymbol, expandableList, prevDeferredCommPeriod]);

  const getRowId = useCallback((params) => params?.data?.id, []);

  return (
    <div className="w-full h-auto">
      {/* tables by default have 175px of minimmum height, no-min-height overrides it and unsets it */}
      <div className="h-full w-full ag-theme-material statementTable no-min-height">
        <AgGridReact
          {...everAgGridOptions.getDefaultOptions({ type: "md" })}
          columnDefs={renderColumns}
          defaultColDef={defaultColDef}
          rowData={[rowValue]}
          suppressRowHoverHighlight={false}
          onGridReady={(params) => onGridReady(params, rowValue.id)}
          onFirstDataRendered={(params) => {
            if (
              expandEarnedCommissionRow &&
              typeof expandEarnedCommissionRow === "function"
            ) {
              expandEarnedCommissionRow();
            }
            everAgGridCallbacks.adjustColumnWidth(params);
          }}
          suppressContextMenu={true}
          masterDetail={true}
          detailCellRendererParams={detailCellRendererParams}
          detailRowAutoHeight={true}
          enableRangeSelection={false}
          keepDetailRows={true}
          getRowId={getRowId}
          onRowClicked={onRowClicked}
        />
      </div>
    </div>
  );
};

export default Table;
