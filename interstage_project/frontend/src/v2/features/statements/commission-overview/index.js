import { EyeOffIcon } from "@everstage/evericons/outlined";
import { cloneDeep, isEmpty } from "lodash";
import React, { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  PAYMENT_STATUS,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverButton, EverTooltip, EverTg } from "~/v2/components";

import NonSettlementViewTable from "./NonSettlementViewTable";
import Table from "./Table";

let gridApi = {};

const getCommissionPercent = (amount, variablePay) => {
  const _amount = Number.parseFloat(amount);
  const _variablePay = Number.parseFloat(variablePay);
  const percent =
    _variablePay > 0 ? ((_amount * 100) / _variablePay).toFixed(2) : 0;
  return `${percent}%`;
};

const CommissionOverview = (props) => {
  const {
    view,
    currencySymbol,
    localeId,
    rowData,
    totalPayout,
    currentPayout,
    periodLabel,
    updateDrawerData,
    paidAmount,
    pendingAmount,
    paymentStatus,
    variablePay,
    employeeEmailId,
    hasActiveSettlementRules,
    previousCommissionDeferred,
    showCommissionPercent,
    showSettlementView,
    showCommissionPayouts,
  } = props;
  const [expandableList, setExpandableList] = useState({});
  const { accessToken } = useAuthStore();

  const { t } = useTranslation();

  const onRowClicked = (event) => {
    if (
      event.data?.children?.length > 0 &&
      (showSettlementView ||
        (!showSettlementView && event.data?.id !== "earnedCommissions"))
    ) {
      const isExpanded = event.node.expanded;
      const expandableListTemp = cloneDeep(expandableList);
      expandableListTemp[event.data.id] = !isExpanded;
      event.api.setRowNodeExpanded(event.node, !isExpanded);
      setExpandableList(expandableListTemp);
    }
  };

  const onGridReady = (params, key) => {
    gridApi[key] = params?.api;
    const expandableListTemp = cloneDeep(expandableList);
    const getExpandableList = (rowData) => {
      for (const data of rowData) {
        if (data.id === "previousDeferredCommissions" && data?.children) {
          expandableListTemp["previousDeferredCommissions"] = false;
          for (const child of data.children) {
            for (const subchildren of child.children) {
              expandableListTemp[subchildren.id] = false;
            }
          }
        } else if (data?.children) {
          expandableListTemp[data.id] =
            !showSettlementView && data.id === "earnedCommissions";
          getExpandableList(data?.children);
        }
      }
    };

    window.addEventListener("resize", () => {
      setTimeout(() => {
        try {
          if (params?.api?.destroyCalled) return;
          params?.api?.sizeColumnsToFit();
        } catch (error) {
          console.log("AG Grid sizeColumnsToFit error", error);
        }
      }, 250);
    });
    setTimeout(() => {
      try {
        if (params?.api?.destroyCalled) return;
        params?.api?.sizeColumnsToFit();
      } catch (error) {
        console.log("AG Grid sizeColumnsToFit error", error);
      }
    }, 250);

    getExpandableList(rowData);
    setExpandableList(expandableListTemp);
  };

  const expandEarnedCommissionRow = useCallback(() => {
    if (!isEmpty(gridApi["earnedCommissions"])) {
      if (gridApi["earnedCommissions"].destroyCalled) return;
      setTimeout(() => {
        gridApi["earnedCommissions"].forEachNode((node) => {
          gridApi["earnedCommissions"].setRowNodeExpanded(node, true);
        });
      }, 1000);
    }
  }, []);

  const expandPrevDeferredCommissionRow = useCallback(() => {
    if (!isEmpty(gridApi["previousDeferredCommissions"])) {
      if (gridApi["previousDeferredCommissions"].destroyCalled) return;
      setTimeout(() => {
        gridApi["previousDeferredCommissions"].forEachNode((node) => {
          gridApi["previousDeferredCommissions"].setRowNodeExpanded(node, true);
        });
      }, 1000);
    }
  }, []);

  const LinkRenderer = ({ value, data }) => {
    return (
      <>
        {data?.isHiddenCriteria && (
          <EverTooltip title="Hidden component">
            <EyeOffIcon className="h-4 w-4 !text-ever-base-content-low absolute left-[18px] z-10 cursor-auto" />
          </EverTooltip>
        )}
        <EverButton
          type="link"
          className="z-0"
          onClick={() => {
            updateDrawerData({
              planId: data.planId,
              criteriaId: data.criteriaId,
              commissionType:
                data.planType === "earnedCommissions"
                  ? "EARNED_COMMISSIONS"
                  : data.planType === "previousDeferredCommissions"
                  ? "PREV_DEFERRED_COMMISSIONS"
                  : data.planType === "deferredCommissions"
                  ? "DEFERRED_COMMISSIONS"
                  : "CURRENT_PERIOD_PAYOUT",
              planName: data.planName,
              planType: data.planType,
              criteriaName: data.criteriaName,
              datasheetId: data.datasheetId,
              total: data.amount,
              quotaErosionTotal: data.quotaErosion ?? 0,
              commPeriod: data.commPeriod,
              period: data?.period || null,
            });
            sendAnalyticsEvent(
              accessToken,
              ANALYTICS_EVENTS.VIEW_CRITERIA_TABLE_VIEW,
              {
                [ANALYTICS_PROPERTIES.PAYEE_NAME]: employeeEmailId,
                [ANALYTICS_PROPERTIES.PERIOD]: periodLabel,
                [ANALYTICS_PROPERTIES.CRITERIA_NAME]: value,
              }
            );
          }}
        >
          <EverTg.Text>{value}</EverTg.Text>
        </EverButton>
      </>
    );
  };

  return showSettlementView ? (
    <>
      {(PAYMENT_STATUS.PARTIALLY_PAID === paymentStatus ||
        PAYMENT_STATUS.OVER_PAID === paymentStatus) && (
        <div className="flex justify-between items-start">
          <div className="h-full flex flex-wrap">
            <EverTooltip
              mouseEnterDelay={0.5}
              title={
                showCommissionPayouts
                  ? undefined
                  : "You don't have permission to view this."
              }
            >
              <div className="flex gap-4">
                <div className="py-2 px-4 bg-ever-success-lite rounded-lg flex flex-col min-w-[128px] shrink-0">
                  <EverTg.Caption className="text-ever-success-content-lite font-medium">
                    Paid
                  </EverTg.Caption>
                  <EverTg.Text
                    className={`text-ever-success-content-lite font-semibold ${
                      !showCommissionPayouts ? "blur-sm" : ""
                    }`}
                  >
                    {showCommissionPayouts
                      ? getLocalizedCurrencyValue(
                          paidAmount,
                          currencySymbol,
                          localeId
                        )
                      : "****"}
                  </EverTg.Text>
                </div>
                <div className="py-2 px-4 bg-ever-warning-lite rounded-lg flex flex-col min-w-[128px] shrink-0">
                  <EverTg.Caption className="text-ever-warning-content-lite font-medium">
                    {t("DUE_ARREAR")}
                  </EverTg.Caption>
                  <EverTg.Text
                    className={`text-ever-warning-content-lite font-semibold ${
                      !showCommissionPayouts ? "blur-sm" : ""
                    }`}
                  >
                    {showCommissionPayouts
                      ? getLocalizedCurrencyValue(
                          pendingAmount,
                          currencySymbol,
                          localeId
                        )
                      : "****"}
                  </EverTg.Text>
                </div>
              </div>
            </EverTooltip>
          </div>
        </div>
      )}
      <div className="h-full w-full overflow-auto flex flex-col gap-4">
        {rowData.map((row, index) => {
          return (
            <Table
              key={index}
              row={row}
              gridApiAll={gridApi}
              onGridReady={onGridReady}
              onRowClicked={onRowClicked}
              currencySymbol={currencySymbol}
              localeId={localeId}
              expandableList={expandableList}
              LinkRenderer={LinkRenderer}
              employeeEmailId={employeeEmailId}
              {...(row["id"] === "earnedCommissions" &&
                !hasActiveSettlementRules &&
                previousCommissionDeferred === 0 && {
                  expandEarnedCommissionRow,
                })}
              {...(row["id"] === "previousDeferredCommissions" &&
                showSettlementView && {
                  expandPrevDeferredCommissionRow,
                })}
              showCommissionPayouts={showCommissionPayouts}
              periodLabel={periodLabel}
            />
          );
        })}
      </div>
      <SettlementViewPayoutSummaryFooter
        totalPayout={view === "commissionSummary" ? currentPayout : totalPayout}
        currencySymbol={currencySymbol}
        localeId={localeId}
        showCommissionPayouts={showCommissionPayouts}
        totalPayoutText={
          view === "commissionSummary"
            ? t("CURRENT_PAYOUT_MESSAGE")
            : t("TOTAL_PAYOUT")
        }
      />
    </>
  ) : (
    <>
      {(PAYMENT_STATUS.PARTIALLY_PAID === paymentStatus ||
        PAYMENT_STATUS.OVER_PAID === paymentStatus) && (
        <div className="flex justify-between items-start">
          <div className="h-full flex flex-wrap">
            <EverTooltip
              mouseEnterDelay={0.5}
              title={
                showCommissionPayouts
                  ? undefined
                  : "You don't have permission to view this."
              }
            >
              <div className="flex gap-4">
                <div className="py-2 px-4 bg-ever-success-lite rounded-lg flex flex-col min-w-[128px] shrink-0">
                  <EverTg.Caption className="text-ever-success-content-lite font-medium">
                    Paid
                  </EverTg.Caption>
                  <EverTg.Text
                    className={`text-ever-success-content-lite font-semibold ${
                      !showCommissionPayouts ? "blur-sm" : ""
                    }`}
                  >
                    {showCommissionPayouts
                      ? getLocalizedCurrencyValue(
                          paidAmount,
                          currencySymbol,
                          localeId
                        )
                      : "****"}
                  </EverTg.Text>
                </div>
                <div className="py-2 px-4 bg-ever-warning-lite rounded-lg flex flex-col min-w-[128px] shrink-0">
                  <EverTg.Caption className="text-ever-warning-content-lite font-medium">
                    {t("DUE_ARREAR")}
                  </EverTg.Caption>
                  <EverTg.Text
                    className={`text-ever-warning-content-lite font-semibold ${
                      !showCommissionPayouts ? "blur-sm" : ""
                    }`}
                  >
                    {showCommissionPayouts
                      ? getLocalizedCurrencyValue(
                          pendingAmount,
                          currencySymbol,
                          localeId
                        )
                      : "****"}
                  </EverTg.Text>
                </div>
              </div>
            </EverTooltip>
          </div>
        </div>
      )}
      <div className="h-full w-full overflow-auto flex flex-col gap-4">
        {rowData.map((row, index) => {
          return (
            <NonSettlementViewTable
              key={index}
              row={row}
              onGridReady={onGridReady}
              onRowClicked={onRowClicked}
              currencySymbol={currencySymbol}
              localeId={localeId}
              expandableList={expandableList}
              LinkRenderer={LinkRenderer}
              employeeEmailId={employeeEmailId}
              groupDefaultExpanded={
                row["id"] === "earnedCommissions" &&
                !hasActiveSettlementRules &&
                previousCommissionDeferred === 0
                  ? -1
                  : 0
              }
              variablePay={variablePay}
              getCommissionPercent={getCommissionPercent}
              showCommissionPercent={showCommissionPercent}
              showCommissionPayouts={showCommissionPayouts}
              periodLabel={periodLabel}
            />
          );
        })}
      </div>
      <NonSettlementViewPayoutSummaryFooter
        totalPayout={totalPayout}
        currencySymbol={currencySymbol}
        localeId={localeId}
        variablePay={variablePay}
        showCommissionPercent={showCommissionPercent}
        showCommissionPayouts={showCommissionPayouts}
      />
    </>
  );
};

const SettlementViewPayoutSummaryFooter = ({
  totalPayout,
  currencySymbol,
  localeId,
  showCommissionPayouts,
  totalPayoutText,
}) => {
  if (!showCommissionPayouts) return <></>;
  return (
    <div
      className={twMerge(
        "font-semibold py-3 px-4 mb-4 mt-auto rounded-lg",
        totalPayout >= 0
          ? "text-ever-success-lite-content bg-ever-success-lite"
          : "text-ever-error-lite-content bg-ever-error-lite"
      )}
    >
      <div className="flex items-center justify-between">
        <EverTg.Text>{totalPayoutText}</EverTg.Text>
        <EverTg.Text>
          {getLocalizedCurrencyValue(totalPayout, currencySymbol, localeId)}
        </EverTg.Text>
      </div>
    </div>
  );
};

const NonSettlementViewPayoutSummaryFooter = ({
  totalPayout,
  variablePay,
  currencySymbol,
  localeId,
  showCommissionPercent,
  showCommissionPayouts,
}) => {
  const { t } = useTranslation();
  return (
    <>
      <div
        className={`mt-auto font-semibold py-3 px-4 mb-4 rounded-lg ${
          totalPayout >= 0
            ? "text-ever-success-lite-content bg-ever-success-lite"
            : "text-ever-error-lite-content bg-ever-error-lite"
        }`}
      >
        <div className="flex items-center justify-between">
          <span>{t("TOTAL_PAYOUT")}</span>
          <div className="flex items-center gap-x-20">
            {showCommissionPercent && (
              <span className="text-ever-base-content">
                {getCommissionPercent(totalPayout, variablePay)}
              </span>
            )}
            {showCommissionPayouts && (
              <span>
                {getLocalizedCurrencyValue(
                  totalPayout,
                  currencySymbol,
                  localeId
                )}
              </span>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CommissionOverview;
