import { CheckCircleIcon, XCircleIcon } from "@everstage/evericons/solid";
import cx from "classnames";
import { isArray, isEmpty } from "lodash";
import React from "react";

import { EverModal, EverTg } from "~/v2/components";

import {
  CONDITION_POPUP_TITLE,
  CONDITIONAL_TYPE,
  EXPRESSION_TYPE,
  STATEMENT_TYPE,
} from "./constants";
import { ConditionExpressionStyled } from "./CTStyledComponent";
import styles from "./styles.module.scss";
import TraceExpressions from "./TraceExpressions";

function ConditionResult({ conditions, getTraceItemDetail }) {
  const getIcon = (isConditionPassed = false) => {
    return isConditionPassed ? (
      <CheckCircleIcon className="text-ever-success w-6 h-6" />
    ) : (
      <XCircleIcon className="text-ever-error w-6 h-6" />
    );
  };

  return (
    <>
      {conditions.map((condition, index) => {
        const { result = {}, expression = [], expressionType = "" } = condition;

        return (
          <ConditionExpressionStyled
            key={index}
            level={index}
            isLast={conditions.length - 1 === index}
            className={cx(styles.conditionExpression, "relative", {
              [styles.conditionExpressionChild]: index > 0,
              "before:hidden": index === 0,
              "after:hidden": index === conditions.length - 1,
            })}
          >
            {conditions.length > 1 && conditions.length - 1 !== index && (
              <div
                className={cx(styles.conditionPathIndicator, {
                  [styles.conditionPathIndicatorChild]: index > 0,
                })}
              />
            )}
            <div
              className={cx("flex items-center gap-x-4", {
                [`${styles.conditionWrapperChild} ml-10`]: index > 0,
              })}
            >
              {!isEmpty(result) && (
                <div className="flex">{getIcon(result.value)}</div>
              )}
              <TraceExpressions
                expression={expression}
                expressionType={expressionType}
                getTraceItemDetail={getTraceItemDetail}
                conditionalTraceType={CONDITIONAL_TYPE.DEFAULT}
              />
            </div>
          </ConditionExpressionStyled>
        );
      })}
    </>
  );
}

function DetailedCondition({ conditions, getTraceItemDetail }) {
  const getStatementContent = (index, arg) => {
    switch (true) {
      case index === 1 && conditions.thenIsNested:
      case index === 2 && conditions.elseIsNested: {
        return (
          <DetailedCondition
            conditions={arg}
            getTraceItemDetail={getTraceItemDetail}
          />
        );
      }
      case index === 2 && conditions.elseDoNothing: {
        return (
          <EverTg.Text className="trace_aggregation_wrapper flex items-center font-medium">
            Do Nothing
          </EverTg.Text>
        );
      }
      default: {
        return (
          <span className="trace_aggregation_wrapper flex items-center">
            <TraceExpressions
              expression={arg}
              getTraceItemDetail={getTraceItemDetail}
              conditionalTraceType={CONDITIONAL_TYPE.AGGREGATION}
            />
          </span>
        );
      }
    }
  };

  return (
    <div className="w-full">
      {conditions.args.map((arg, index) => (
        <EverTg.Caption.Thick
          className="flex items-center py-2 relative font-medium"
          key={index}
        >
          <div
            className={cx(
              "w-16 h-full flex items-center justify-center px-4 py-2 border border-solid border-transparent text-ever-primary-pressed bg-ever-chartColors-2 rounded-lg flex-none",
              {
                "connector-after": index === 0,
                "connector-before connector-after": index === 1,
                "connector-before": index === 2,
              }
            )}
          >
            {STATEMENT_TYPE[index]}
          </div>
          <div className="w-4 h-px bg-ever-base-400 flex-none" />
          <div className="flex-auto flex items-center px-4 py-0.5 border border-solid rounded-lg border-ever-base-200">
            {getStatementContent(index, arg)}
          </div>
        </EverTg.Caption.Thick>
      ))}
    </div>
  );
}

export default function ConditionsPopup({ conditions, onClose }) {
  const getTraceItemDetail = (traceItem, index) => ({
    key: `condition_level_${index}`,
    traceItem,
  });

  const conditionProps = {
    conditions,
    getTraceItemDetail,
  };

  const getTitle = () => {
    if (
      isArray(conditions) &&
      conditions.length === 1 &&
      conditions[0].expressionType === EXPRESSION_TYPE.SIMPLE
    ) {
      return CONDITION_POPUP_TITLE.FORMULA;
    }
    return CONDITION_POPUP_TITLE.CONDITION;
  };

  return (
    <EverModal
      title={getTitle()}
      className="condition-modal"
      visible={!isEmpty(conditions)}
      onCancel={onClose}
      footer={null}
      destroyOnClose={true}
      width={812}
    >
      {isArray(conditions) ? (
        <ConditionResult {...conditionProps} />
      ) : (
        <DetailedCondition {...conditionProps} />
      )}
    </EverModal>
  );
}
