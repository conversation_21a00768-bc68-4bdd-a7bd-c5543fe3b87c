import cx from "classnames";
import { isEmpty } from "lodash";
import React from "react";

import { LineItemCard, LineItemTable } from "./LineItemDetail";
import styles from "./styles.module.scss";
import { Trace } from "./Trace";

export default function Render({
  periodLabel,
  traceData,
  currencySymbol,
  selectedTierName,
  hyperlinkMap,
}) {
  const traceRecordsLength = traceData.records?.length || 0;
  const lineItemProps = {
    columns: traceData.columns || {},
    records: traceData.records || [],
    currencySymbol,
    hyperlinkMap,
    criteriaColumns: traceData.criteriaColumns || [],
  };

  return (
    <div className="flex h-full w-full">
      {traceRecordsLength === 1 && <LineItemCard {...lineItemProps} />}
      <div
        className={cx(
          styles.customScrollBar,
          "w-full flex flex-col gap-y-8 pt-4 pb-2 px-4 m-2 overflow-y-auto",
          {
            ["w-[calc(100%_-_340px)]"]:
              !isEmpty(traceData.columns) && traceRecordsLength === 1,
          }
        )}
      >
        {traceRecordsLength > 1 && <LineItemTable {...lineItemProps} />}
        <div className="flex flex-col gap-y-6">
          <Trace
            traces={traceData.trace || []}
            subTraces={traceData.subTraces || {}}
            tierRecords={traceData.tierRecords || []}
            traceInfo={traceData.traceInfo || {}}
            criteriaType={traceData.criteriaType || ""}
            periodLabel={periodLabel}
            selectedTierName={selectedTierName}
          />
        </div>
      </div>
    </div>
  );
}
