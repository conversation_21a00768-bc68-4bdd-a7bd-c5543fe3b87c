import { Table } from "antd";
import { cloneDeep, isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";

import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import {
  EverButton,
  EverTabs,
  EverLabel,
  EverSwitch,
  EverTg,
} from "~/v2/components";

import {
  COLOR_PALETTE_LIST,
  SHOW_FULL_TRACE,
  TIER_COMPARISON_TYPE,
} from "./constants";
import { getRangeValue, getTraceInfo } from "./helper";
import { CommissionTraceStoreProvider, useCommissionTraceStore } from "./store";
import TraceExpressionBox from "./TraceExpressionBox";

const LinkRenderer = ({ value, onClick }) => (
  <EverButton type="link" onClick={onClick}>
    {value}
  </EverButton>
);

function TierSplitUpTable({
  tierRecords,
  criteriaType,
  tierComparisonType,
  setActiveKey,
}) {
  const { t } = useTranslation();

  const columns = useMemo(() => {
    const isQuota = ["Quota", "CustomQuota"].includes(criteriaType);
    const isTier = ["Tier", "CustomTier"].includes(criteriaType);
    const comparisonType = isEmpty(tierComparisonType)
      ? ""
      : tierComparisonType.toLowerCase();
    const isQuotaAttainment =
      isQuota && comparisonType === TIER_COMPARISON_TYPE.QUOTA_ATTAINMENT;

    const getRangeHeaderName = () => {
      if (isQuota) {
        return comparisonType === TIER_COMPARISON_TYPE.QUOTA_VALUE
          ? t("COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.QUOTA_EROSION_RANGE")
          : t("COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.QUOTA_ATTAINMENT_RANGE");
      } else if (isTier) {
        return t("COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.TIER_VALUE_RANGE");
      }
      return null;
    };

    const cols = [
      {
        title: "",
        dataIndex: "actualTierName",
        key: "actualTierName",
        render: (text, record) => (
          <LinkRenderer
            value={text}
            onClick={() => setActiveKey(String(record.index + 1))}
          />
        ),
      },
      {
        title: getRangeHeaderName(),
        dataIndex: "range",
        key: "range",
        render: (_, record) => {
          return getRangeValue(record, isQuotaAttainment);
        },
      },
      isTier && {
        title: t("COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.TIER_VALUE"),
        dataIndex: "tieredValue",
        key: "tieredValue",
        align: "right",
        render: (text) => {
          return (
            <EverTg.Text className="font-medium text-ever-success-hover">
              {formatCurrencyWrapper(text)}
            </EverTg.Text>
          );
        },
      },
      isQuota && {
        title: t(
          "COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.CUMULATIVE_QUOTA_ATTAINMENT"
        ),
        dataIndex: "cumulativeQuotaAttainment",
        key: "cumulativeQuotaAttainment",
        render: (text) => {
          return `${text}%`;
        },
      },
      isQuota && {
        title: t(
          "COMMISSION_TRACE.TIER_SPLIT_UP_HEADER.CUMULATIVE_QUOTA_EROSION"
        ),
        dataIndex: "cumulativeQuotaRetirement",
        key: "cumulativeQuotaRetirement",
        align: "right",
        render: (text) => {
          return (
            <EverTg.Text className="font-medium text-ever-success-hover">
              {formatCurrencyWrapper(text)}
            </EverTg.Text>
          );
        },
      },
    ];

    return cols.filter(Boolean);
  }, [criteriaType, setActiveKey, tierComparisonType]);

  const data = useMemo(() => {
    return tierRecords.map((tierRecord, index) => ({
      ...tierRecord,
      index,
    }));
  }, [tierRecords]);

  return (
    <Table
      rowKey={(record) => record.actualTierName}
      className="max-content tierSplitUpTable"
      columns={columns}
      dataSource={data}
      pagination={false}
      bordered
      scroll={{ x: "max-content" }}
    />
  );
}

function TraceInfo({ traceInfo, setActiveKey }) {
  const { t } = useTranslation();

  if (isEmpty(traceInfo)) {
    return null;
  }

  const getValue = (traceKey) => {
    switch (true) {
      case traceKey === "tierValue" || traceKey === "quotaRetirement": {
        return (
          <div
            className="text-ever-primary cursor-pointer"
            onClick={() => setActiveKey("0")}
          >
            {traceInfo[traceKey]}
          </div>
        );
      }
      case traceKey === "overriddenTier": {
        return (
          <div className="text-ever-success-hover">{`to ${traceInfo[traceKey]}`}</div>
        );
      }
      default: {
        return traceInfo[traceKey];
      }
    }
  };

  return (
    <div className="flex gap-8 mb-8">
      {Object.keys(traceInfo).map((traceKey) => {
        return (
          <div key={traceKey} className="flex flex-col gap-y-1">
            <EverLabel>
              {t(`COMMISSION_TRACE.TRACE_INFO_LABEL.${traceKey}`)}
            </EverLabel>
            <EverTg.Text className="font-medium">
              {getValue(traceKey)}
            </EverTg.Text>
          </div>
        );
      })}
    </div>
  );
}

const IndividualTrace = observer(
  ({ className, trace, subTraces, traceInfo, setActiveKey }) => {
    const colorPaletteIndex = useRef(0);
    const expandableTraceKeys = useRef([]);
    const { showFullTrace, setShowFullTrace, setExpandableUniqueKeys } =
      useCommissionTraceStore();

    const modifyExpWithColorPalette = useCallback(
      (expression) => {
        const tempSubTraces = [];
        const expandableTraceInfo = {};

        for (const [index, traceItem] of expression.entries()) {
          const uniqueKey = uuidv4();
          traceItem["uniqueKey"] = uniqueKey;

          if (!isEmpty(traceItem.traceId)) {
            if (isNil(expandableTraceInfo[traceItem.traceId])) {
              const subTrace = {
                traceId: traceItem.traceId,
                expression: [],
                result: {},
              };
              traceItem["trace"] = subTrace;
              traceItem["colorPalette"] = colorPaletteIndex.current;
              expandableTraceKeys.current.push(uniqueKey);
              tempSubTraces.push(subTrace);
              expandableTraceInfo[traceItem.traceId] = {
                colorPalette: colorPaletteIndex.current,
                subTrace,
                uniqueKey,
              };
              colorPaletteIndex.current += 1;

              if (colorPaletteIndex.current > COLOR_PALETTE_LIST.length - 1) {
                colorPaletteIndex.current = 0;
              }
            } else {
              const { uniqueKey, subTrace, colorPalette } =
                expandableTraceInfo[traceItem.traceId];
              traceItem["trace"] = subTrace;
              traceItem["colorPalette"] = colorPalette;
              traceItem["uniqueKey"] = uniqueKey;
            }
          }
          expression[index] = traceItem;
        }

        if (!isEmpty(tempSubTraces) && !isEmpty(subTraces)) {
          for (const tempSubTrace of tempSubTraces) {
            if (!isEmpty(subTraces[tempSubTrace.traceId])) {
              const { condition, expression, result } = cloneDeep(
                subTraces[tempSubTrace.traceId]
              );
              tempSubTrace["result"] = result;
              tempSubTrace["condition"] = condition;
              tempSubTrace["expression"] =
                modifyExpWithColorPalette(expression);
            }
          }
        }

        return expression;
      },
      [subTraces]
    );

    const expression = useMemo(() => {
      let modifiedExpression = {};
      if (!isEmpty(trace?.expression)) {
        colorPaletteIndex.current = 0;
        expandableTraceKeys.current = [];
        modifiedExpression = modifyExpWithColorPalette(
          cloneDeep(trace.expression)
        );
        setExpandableUniqueKeys(expandableTraceKeys.current);
      }

      return modifiedExpression;
    }, [trace, setExpandableUniqueKeys, modifyExpWithColorPalette]);

    if (isEmpty(expression)) {
      return null;
    }

    return (
      <div className={`${className ?? ""} pb-2`}>
        <TraceInfo traceInfo={traceInfo} setActiveKey={setActiveKey} />
        {!isEmpty(expandableTraceKeys.current) && (
          <div className="flex items-center mb-6 gap-x-2">
            <EverSwitch
              checked={!isEmpty(showFullTrace)}
              onChange={(checked) =>
                setShowFullTrace(checked ? SHOW_FULL_TRACE.SWITCH : "")
              }
            />
            <EverLabel>Show full trace</EverLabel>
          </div>
        )}
        <TraceExpressionBox
          level={0}
          expression={expression}
          condition={trace?.condition ?? trace?.infix ?? []}
          result={trace.result}
          isOverAllSum={!isEmpty(trace?.infix)}
          expressionType={
            isEmpty(trace?.expressionType)
              ? ""
              : trace.expressionType.toLowerCase()
          }
          tierType={
            isEmpty(trace?.tierType) ? "" : trace.tierType.toLowerCase()
          }
        />
      </div>
    );
  }
);

const Trace = ({
  traces,
  subTraces,
  tierRecords,
  traceInfo,
  criteriaType,
  periodLabel,
  selectedTierName,
}) => {
  const [activeKey, setActiveKey] = useState("0");
  const items = traces.map((trace, index) => {
    const newTraceInfo = getTraceInfo({
      tierRecords,
      traceInfo,
      criteriaType,
      periodLabel,
      traces,
      index,
    });
    return {
      label: trace.name,
      key: String(index),
      children: (
        <CommissionTraceStoreProvider>
          <IndividualTrace
            className={`${traces.length > 1 ? "pt-2" : ""}`}
            trace={trace}
            subTraces={subTraces}
            traceInfo={newTraceInfo}
            setActiveKey={setActiveKey}
          />
        </CommissionTraceStoreProvider>
      ),
    };
  });

  useEffect(() => {
    if (
      selectedTierName &&
      ["Quota", "CustomQuota", "Tier", "CustomTier"].includes(criteriaType) &&
      traces.length > 2
    ) {
      const activeIndex = traces.findIndex(
        (trace) =>
          trace.name === selectedTierName || trace.tierName === selectedTierName
      );
      if (activeIndex >= 0) {
        setActiveKey(String(activeIndex));
      }
    }
  }, [selectedTierName, criteriaType, traces]);

  if (items.length === 1) {
    return items[0].children;
  }

  return (
    <>
      <EverTabs
        className="!h-auto"
        activeKey={activeKey}
        onChange={setActiveKey}
        items={items}
      />
      {Number(activeKey) === 0 && (
        <TierSplitUpTable
          tierRecords={tierRecords}
          criteriaType={criteriaType}
          tierComparisonType={traceInfo.tierComparisonType}
          setActiveKey={setActiveKey}
        />
      )}
    </>
  );
};

export { Trace };
