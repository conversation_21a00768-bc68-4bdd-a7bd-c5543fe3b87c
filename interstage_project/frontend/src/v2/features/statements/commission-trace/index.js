import { isEmpty } from "lodash";
import React, { Fragment, useState } from "react";
import { useQuery } from "react-query";
import { twMerge } from "tailwind-merge";

import { getCommissionTrace } from "~/Api/CommissionTraceService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { <PERSON><PERSON><PERSON><PERSON>, EverDrawer, EverHotToastBanner } from "~/v2/components";

import { ERROR_MESSAGE } from "./constants";
import Render from "./Render";
import { LocaleIdContext } from "./store";

/**
 * @typedef {Object} DrawerData
 * @property {string} planName - The name of the commission plan.
 * @property {string} criteriaName - The name of the commission criteria.
 * @property {string} planId - The ID of the commission plan.
 * @property {string} criteriaId - The ID of the commission criteria.
 * @property {string} payeeEmail - The email address of the commission payee.
 * @property {Date} psd - The start date of the commission period.
 * @property {Date} ped - The end date of the commission period.
 */

/**
 * @typedef {Object} SelectedTrace
 * @property {string} rowKey - The selected trace unique key.
 * @property {string} tierName - Tier name.
 */

/**
 *  A component that displays the commission trace for a line item/overall sum.
 *
 * @param {object} props - Component props
 * @param {SelectedTrace} props.selectedTrace - The selected trace details.
 * @param {DrawerData} props.drawerData - The data to display in the drawer.
 * @param {string} props.currencySymbol - The currency symbol to display.
 * @param {Function} props.onClose - Function to close the drawer.
 * @param {object} props.hyperlinkMap - The Hyperlink map.
 * @returns {JSX.Element} - CommissionTrace component UI.
 */

export default function CommissionTrace({
  selectedTrace,
  periodLabel,
  isBaseCurrency,
  drawerData,
  currencySymbol,
  localeId,
  onClose,
  hyperlinkMap,
}) {
  const { planName, criteriaName, planId, criteriaId, payeeEmail, psd, ped } =
    drawerData;
  const { accessToken } = useAuthStore();
  const [showError, setShowError] = useState(false);

  const { data, isLoading } = useQuery(
    ["getCommissionTrace", selectedTrace.rowKey],
    () =>
      getCommissionTrace(
        {
          planId,
          criteriaId,
          payeeEmail,
          isBaseCurrency,
          periodStartDate: psd,
          periodEndDate: ped,
          rowKey: selectedTrace.rowKey,
        },
        accessToken
      ),
    {
      lazy: true,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: !isEmpty(selectedTrace),
      onSettled: (_, error) => {
        setShowError(!!error);
      },
    }
  );

  return (
    <EverDrawer
      destroyOnClose={true}
      height={"95%"}
      className="commission-trace-drawer"
      title={
        <CommissionTraceHeader
          breadcrumbs={[planName, criteriaName, selectedTrace.rowKey]}
        />
      }
      placement="top"
      onClose={onClose}
      visible={!isEmpty(selectedTrace)}
    >
      {isLoading ? (
        <EverLoader indicatorType="spinner" tip="" />
      ) : showError ? (
        <ErrorMsgComponent />
      ) : (
        // Since the localeId would be same for all commission trace components,
        //  providiing it as a context to avoid passing it as a prop to all the
        //  components.
        <LocaleIdContext.Provider value={localeId}>
          <Render
            periodLabel={periodLabel}
            traceData={data || {}}
            currencySymbol={currencySymbol}
            selectedTierName={selectedTrace.tierName || ""}
            hyperlinkMap={hyperlinkMap}
          />
        </LocaleIdContext.Provider>
      )}
    </EverDrawer>
  );
}

function CommissionTraceHeader({ breadcrumbs }) {
  return (
    <div>
      {breadcrumbs.map((breadcrumb, index) => {
        const lastIndex = index === breadcrumbs.length - 1;
        return (
          <Fragment key={index}>
            <div
              className={twMerge(
                "inline-block truncate max-w-32 align-middle",
                lastIndex && "text-ever-base-content-mid"
              )}
              title={breadcrumb}
            >
              {breadcrumb}
            </div>
            {!lastIndex && <span className="mx-2">/</span>}
          </Fragment>
        );
      })}
    </div>
  );
}

function ErrorMsgComponent() {
  return (
    <EverHotToastBanner
      className="mt-2 mx-auto"
      type="error"
      description={ERROR_MESSAGE}
    />
  );
}
