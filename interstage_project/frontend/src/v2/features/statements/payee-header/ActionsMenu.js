import {
  AlignTopArrowIcon,
  DownloadStraightIcon,
  DataflowIcon,
  MailIcon,
} from "@everstage/evericons/outlined";
import { DotsVerticalIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import React, { useEffect, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import { RBAC_ROLES, PAYOUT_APPROVAL_TYPES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverButton } from "~/v2/components";
import { EverTg } from "~/v2/components/EverTypography";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
const { SubMenu } = Menu;

const ActionsMenu = ({
  onExport,
  onExportAsPdf,
  onRequestApproval,
  showCommissionPayouts,
  onShowEmailStatementsModal,
}) => {
  const { accessToken } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const { hasPermissions } = useUserPermissionStore();

  const [payoutApprovalsEnabled, setPayoutApprovalsEnabled] = useState(false);

  const [payoutLevel, setPayoutLevel] = useState();

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;

      setPayoutLevel(approvalType);
      setPayoutApprovalsEnabled(config?.payoutApprovals?.enabled || false);
    }
  }, [approvalConfigData]);

  const menu = (
    <Menu>
      {clientFeatures.showApprovalFeature && payoutApprovalsEnabled && (
        <RBACProtectedComponent permissionId={RBAC_ROLES.VIEW_REQUESTAPPROVALS}>
          {(props) => (
            <Menu.Item
              {...props}
              key="request-approval"
              icon={
                <DataflowIcon className="w-5 h-5 text-ever-base-content-mid" />
              }
              onClick={() => {
                onRequestApproval(
                  payoutLevel === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
                );
              }}
            >
              <span className="text-ever-base-content">Request Approval</span>
            </Menu.Item>
          )}
        </RBACProtectedComponent>
      )}
      {showCommissionPayouts &&
        hasPermissions([
          RBAC_ROLES.EXPORT_STATEMENT,
          RBAC_ROLES.EXPORT_PAYOUTS,
        ]) && (
          <SubMenu
            key="export-submenu"
            title="Export statement"
            icon={
              <AlignTopArrowIcon className="w-5 h-5 text-ever-base-content-mid" />
            }
          >
            <Menu.Item
              key="export-statement"
              icon={
                <DownloadStraightIcon className="w-5 h-5 text-ever-base-content-mid" />
              }
              onClick={onExport}
            >
              <EverTg.Text className="text-ever-base-content">
                Microsoft Excel (.xlsx)
              </EverTg.Text>
            </Menu.Item>
            {clientFeatures.showStatementsPdf && (
              <Menu.Item
                key="export-statement-pdf"
                icon={
                  <DownloadStraightIcon className="w-5 h-5 text-ever-base-content-mid" />
                }
                onClick={onExportAsPdf}
              >
                <EverTg.Text className="text-ever-base-content">
                  PDF (.pdf)
                </EverTg.Text>
              </Menu.Item>
            )}
          </SubMenu>
        )}

      <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_PAYOUTS}>
        {(props) => {
          if (showCommissionPayouts) {
            return (
              <Menu.Item
                {...props}
                key="email-statement"
                icon={
                  <MailIcon className="w-5 h-5 text-ever-base-content-mid" />
                }
                onClick={onShowEmailStatementsModal}
              >
                <span className="text-ever-base-content">Email Statements</span>
              </Menu.Item>
            );
          }
          return null;
        }}
      </RBACProtectedComponent>
    </Menu>
  );

  // Check if no menu item is present then remove action menu
  if (
    !(
      hasPermissions(RBAC_ROLES.CREATE_QUERIES) ||
      (clientFeatures.showApprovalFeature &&
        hasPermissions(RBAC_ROLES.VIEW_REQUESTAPPROVALS)) ||
      (showCommissionPayouts &&
        hasPermissions(
          [
            RBAC_ROLES.EXPORT_STATEMENT,
            RBAC_ROLES.EXPORT_PAYOUTS,
            RBAC_ROLES.MANAGE_PAYOUTS,
          ],
          false
        ))
    )
  )
    return null;

  return (
    <div>
      <Dropdown trigger={["click"]} overlay={menu} overlayClassName="w-60">
        <EverButton.Icon
          size="small"
          type="ghost"
          color="base"
          data-testid="statement-menu"
          icon={<DotsVerticalIcon className="text-ever-base-content-mid" />}
        />
      </Dropdown>
    </div>
  );
};

export default ActionsMenu;
