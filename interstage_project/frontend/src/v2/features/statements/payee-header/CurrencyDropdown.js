import { CoinsSwapIcon } from "@everstage/evericons/outlined";
import { Dropdown } from "antd";
import React from "react";

import { EverTooltip, EverButton } from "~/v2/components";

const CurrencyDropdown = ({ currencyMenu }) => {
  return (
    <Dropdown
      overlay={currencyMenu}
      trigger={["click"]}
      overlayClassName="w-72"
    >
      <EverTooltip title="Display Currency" placement="top">
        <EverButton.Icon
          size="small"
          type="ghost"
          color="base"
          data-testid="currency-button"
          icon={<CoinsSwapIcon className="text-ever-primary" />}
        />
      </EverTooltip>
    </Dropdown>
  );
};

export default CurrencyDropdown;
