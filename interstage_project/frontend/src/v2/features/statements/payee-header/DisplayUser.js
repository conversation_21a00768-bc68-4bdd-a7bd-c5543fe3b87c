import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@everstage/evericons/outlined";
import { Dropdown } from "antd";
import { isEmpty } from "lodash";
import React from "react";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverGroupAvatar,
  LazyListWrapper,
  LazyListContent,
  EverListItem,
  EverDivider,
  EverTg,
} from "~/v2/components";

const DisplayUser = ({
  payeeDetails,
  //onPayeeChange,
  showStatementsFilter,
  showUserOption,
  payeeDesignation,
  onUserChange,
  //setCloseOption,
  displayUserLazyLoadProps,
  selectedUserEmailId,
  showViewProfileChildren = null,
}) => {
  const { accessToken } = useAuthStore();

  const listItemRenderer = (option, index) => {
    return (
      <EverListItem
        key={`${option.value}_${index}`}
        className={`p-2 mx-2 ${
          option.value === selectedUserEmailId ? "bg-ever-primary-lite" : ""
        }`}
        title={
          <span
            className={
              option.value === selectedUserEmailId
                ? "text-ever-primary font-medium"
                : "text-ever-base-content"
            }
          >
            {option.label}
          </span>
        }
        selectable={option.value !== selectedUserEmailId}
        append={
          option.value === selectedUserEmailId ? (
            <CheckIcon className="w-4 h-4 text-ever-primary stroke-2" />
          ) : null
        }
        onSelect={() => {
          onUserChange(option.value);
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SWITCH_PAYEES, {
            [ANALYTICS_PROPERTIES.PAYEE_NAME]: option.label,
          });
        }}
      />
    );
  };

  const lazyListContent = (
    <div className="py-2">
      <LazyListContent
        searchPlaceholder="Search User"
        skipSelectAll
        listItemRenderer={listItemRenderer}
        {...displayUserLazyLoadProps}
      />
    </div>
  );

  return (
    <div>
      <Dropdown
        overlay={<LazyListWrapper content={lazyListContent} />}
        trigger={["click"]}
        overlayClassName="w-60"
        disabled={!showStatementsFilter}
        destroyPopupOnHide={true}
      >
        <div className="flex items-center">
          <div className="flex flex-row gap-x-3 items-center cursor-pointer">
            <EverGroupAvatar
              avatars={[
                {
                  firstName: payeeDetails.firstName,
                  lastName: payeeDetails.lastName,
                  image: payeeDetails.profilePicture,
                  className: "w-9 h-9",
                },
              ]}
              groupMaxCount={1}
              limitInPopover={1}
            />
            <div className="flex flex-col">
              <div className="flex flex-row gap-x-3">
                <EverTg.Heading3 className="text-ever-base-content">{`${payeeDetails.firstName} ${payeeDetails.lastName}`}</EverTg.Heading3>
                {showStatementsFilter && (
                  <div className="flex items-center text-ever-base-content">
                    {showUserOption ? (
                      <ChevronUpIcon className="w-4 h-4" />
                    ) : (
                      <ChevronDownIcon className="w-4 h-4" />
                    )}
                  </div>
                )}
              </div>
              {(!isEmpty(payeeDesignation) || showViewProfileChildren) && (
                <div className="mt-0.5 flex flex-row">
                  {!isEmpty(payeeDesignation) && (
                    <>
                      <EverTg.Caption className="text-ever-base-content-mid">
                        {payeeDesignation}
                      </EverTg.Caption>
                      {showViewProfileChildren && (
                        <span className="mx-2">
                          <EverDivider type="vertical" className="h-5" />
                        </span>
                      )}
                    </>
                  )}
                  {showViewProfileChildren ? showViewProfileChildren : null}
                </div>
              )}
            </div>
          </div>
        </div>
      </Dropdown>
    </div>
  );
};

export default DisplayUser;
