import React, { useState } from "react";

import {
  EverModal,
  EverButton,
  EverInput,
  EverRadio,
  EverForm,
} from "~/v2/components";

export const SENDOPTIONS = {
  sendUser: "SENDUSER",
  sendOther: "SENDOTHER",
};

const layout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

export const EmailStatementsModal = ({ isVisible, onCancel, onConfirm }) => {
  const [form] = EverForm.useForm();
  const [radioValue, setRadioValue] = useState(SENDOPTIONS.sendUser);

  const onSubmitClick = () => {
    form
      .validateFields()
      .then((values) => {
        onConfirm(values);
      })
      .catch((error) => console.log(error));
  };

  return (
    <EverModal
      title="Email Statements"
      visible={isVisible}
      onCancel={onCancel}
      footer={
        <EverButton color="primary" onClick={onSubmitClick}>
          Submit
        </EverButton>
      }
    >
      <EverForm {...layout} form={form} name="email-wrapper">
        <EverForm.Item
          name={"emailUserOption"}
          className={radioValue === SENDOPTIONS.sendOther ? "" : "!mb-0"}
          initialValue={SENDOPTIONS.sendUser}
        >
          <EverRadio.Group
            className="flex gap-5"
            value={radioValue}
            options={[
              {
                label: "Send to self",
                value: SENDOPTIONS.sendUser,
              },
              {
                label: "Send to other user",
                value: SENDOPTIONS.sendOther,
              },
            ]}
            onChange={(event) => setRadioValue(event.target.value)}
          />
        </EverForm.Item>
        {radioValue === SENDOPTIONS.sendOther && (
          <EverForm.Item
            name={"recipientEmailId"}
            label="Enter Email ID of the person to send the statements to"
            rules={[
              {
                type: "email",
                message: "Enter the valid email!",
              },
              { required: true, message: "This field is required!" },
            ]}
          >
            <EverInput placeholder="Email" />
          </EverForm.Item>
        )}
      </EverForm>
    </EverModal>
  );
};
