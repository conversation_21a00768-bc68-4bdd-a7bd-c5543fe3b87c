import { AlignTopArrowIcon } from "@everstage/evericons/outlined";
import React from "react";

import { EverTooltip, EverButton } from "~/v2/components";

const ExportButton = (props) => {
  return (
    <EverTooltip title="Export" placement="top">
      <EverButton.Icon
        size="small"
        type="ghost"
        color="base"
        icon={<AlignTopArrowIcon className="text-ever-primary" />}
        {...props}
      />
    </EverTooltip>
  );
};

export default ExportButton;
