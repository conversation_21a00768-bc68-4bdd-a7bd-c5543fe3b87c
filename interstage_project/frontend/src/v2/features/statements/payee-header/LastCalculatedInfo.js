import React from "react";

import { EverLabel } from "~/v2/components";

const LastCalculatedInfo = ({ updatedTime, showSettlementView }) => {
  return (
    <div className="flex items-center">
      <EverLabel>
        {`Last ${showSettlementView ? "calculated" : "updated"} :`}
      </EverLabel>
      <span className="text-ever-base-content">{updatedTime}</span>
    </div>
  );
};

export default LastCalculatedInfo;
