import { LockIcon, UnlockIcon } from "@everstage/evericons/outlined";
import React, { useState } from "react";

import { APPROVAL_STATUS, APPROVAL_ENTITY_TYPES } from "~/Enums";
import { EverButton, EverTooltip } from "~/v2/components";
import { CommissionAdjustmentModal } from "~/v2/features/commissions/commission-table/CommissionAdjustmentModal";

const LockUnlockButton = (props) => {
  const {
    showApprovalFeature,
    handleLockUnlock,
    setShowUnFreezePopUp,
    isLocked,
    isLockChanging,
    approvalStatus,
    employeeEmailId,
    selectedPeriod,
    allowAdjustmentsToFrozenCommission,
  } = props;
  const [warningModalVisible, setWarningModalVisible] = useState(false);

  const showUnfreezeModal =
    showApprovalFeature &&
    approvalStatus?.getLatestApprovalEntityKey ===
      APPROVAL_ENTITY_TYPES.PAYOUT &&
    [APPROVAL_STATUS.APPROVED, APPROVAL_STATUS.REQUESTED].includes(
      approvalStatus?.getApprovalStatus
    ) &&
    isLocked;

  const iconClass = `${
    isLockChanging ? "text-ever-base-content-mid" : "text-ever-primary"
  }`;

  return (
    <>
      <EverTooltip title={isLocked ? "Unlock" : "Lock"} placement="top">
        <EverButton.Icon
          size="small"
          type="ghost"
          color="base"
          disabled={isLockChanging}
          icon={
            isLocked ? (
              <UnlockIcon className={iconClass} />
            ) : (
              <LockIcon className={iconClass} />
            )
          }
          data-testid="lock-unlock-button"
          onClick={() => {
            if (showUnfreezeModal) {
              setShowUnFreezePopUp(true);
            } else {
              if (
                !isLocked &&
                approvalStatus?.getCommAdjStatus ===
                  APPROVAL_STATUS.REQUESTED &&
                !allowAdjustmentsToFrozenCommission
              ) {
                setWarningModalVisible(true);
              } else {
                handleLockUnlock();
              }
            }
          }}
        />
      </EverTooltip>
      <CommissionAdjustmentModal
        visible={warningModalVisible}
        onCancel={() => setWarningModalVisible(false)}
        onLock={handleLockUnlock}
        period={selectedPeriod}
        payeeEmail={employeeEmailId}
      />
    </>
  );
};

export default LockUnlockButton;
