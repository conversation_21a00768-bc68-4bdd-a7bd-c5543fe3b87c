import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { navPortalAtom } from "~/GlobalStores/atoms";
import EverNavPortal from "~/v2/components/EverNavPortal";

import DisplayUser from "./DisplayUser";

const PayeeHeaderWithoutStatements = observer((props) => {
  const [showUserOption, setShowUserOption] = useState(false);
  const [closeOption, setCloseOption] = useState(false);

  const navPortalLocation = useRecoilValue(navPortalAtom);

  const {
    payeeDetails,
    payeeDesignation,
    showStatementsFilter,
    displayUserLazyLoadProps,
    selectedUserEmailId,
    onUserChange,
  } = props;

  const payeeDetailsOther = isEmpty(payeeDetails)
    ? {
        firstName: "Restricted",
        lastName: "User",
        employeeEmailId: "",
        profilePicture: "",
      }
    : payeeDetails;

  useEffect(() => {
    if (closeOption) {
      setShowUserOption(false);
      setTimeout(() => setCloseOption(false), 200);
    }
  }, [closeOption]);

  const onPayeeChange = () => {
    if (showStatementsFilter && !closeOption) {
      setShowUserOption(!showUserOption);
    }
  };

  return (
    <EverNavPortal target={navPortalLocation}>
      <div className="flex flex-wrap">
        <div className="grow flex items-center">
          <DisplayUser
            payeeDetails={payeeDetailsOther}
            onPayeeChange={onPayeeChange}
            showStatementsFilter={showStatementsFilter}
            showUserOption={showUserOption}
            payeeDesignation={payeeDesignation}
            onUserChange={onUserChange}
            setCloseOption={setCloseOption}
            displayUserLazyLoadProps={displayUserLazyLoadProps}
            selectedUserEmailId={selectedUserEmailId}
          />
        </div>
      </div>
    </EverNavPortal>
  );
});

export default PayeeHeaderWithoutStatements;
