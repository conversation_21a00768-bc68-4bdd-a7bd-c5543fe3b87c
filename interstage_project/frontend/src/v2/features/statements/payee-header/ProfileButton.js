import React from "react";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverTg } from "~/v2/components";

const ProfileButton = ({ payeeDetails, setDrawerVisibility }) => {
  const { accessToken } = useAuthStore();

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        setDrawerVisibility(true);
        sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.VIEW_PROFILE_CARD, {
          [ANALYTICS_PROPERTIES.PAYEE_NAME]: payeeDetails.employeeEmailId,
        });
      }}
    >
      <EverTg.Caption className="text-ever-primary font-medium cursor-pointer hover:underline">
        View profile
      </EverTg.Caption>
    </div>
  );
};

export default ProfileButton;
