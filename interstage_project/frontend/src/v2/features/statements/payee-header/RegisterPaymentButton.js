import { CreditCardIcon } from "@everstage/evericons/outlined";
import _ from "lodash";
import moment from "moment";
import React from "react";

import { RBAC_ROLES, CALCULATION_STATUS } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverButton, EverTooltip } from "~/v2/components";
import MakePaymentPopup from "~/v2/features/commissions/MakePaymentPopup";

const NESTED_OBJECT_CAMEL_TO_SNAKE_CASE = (obj) =>
  _.transform(obj, (acc, value, key, target) => {
    const snakeKey = _.isArray(target) ? key : _.snakeCase(key);

    acc[snakeKey] = _.isObject(value)
      ? NESTED_OBJECT_CAMEL_TO_SNAKE_CASE(value)
      : value;
  });

const RegisterPaymentButton = ({
  overviewData,
  handleRegisterPayment,
  registerPayment,
  setRegisterPayment,
  isPayeeOnAction,
  periodLabel,
  isDisabled,
}) => {
  // converting overviewData to snake case as MakePaymentPopup component expects the tableData to be in snake case
  const tableData = [NESTED_OBJECT_CAMEL_TO_SNAKE_CASE(overviewData || {})];
  const { hasPermissions } = useUserPermissionStore();
  const isFrozen =
    overviewData?.commCalcStatus === CALCULATION_STATUS.FROZEN &&
    overviewData?.settlementCalcStatus === CALCULATION_STATUS.FROZEN;
  const isRegisterPaymentDisabled =
    isDisabled ||
    Number(overviewData?.totalPayout) === Number(overviewData?.paidAmount) ||
    Number(overviewData?.processedAmount) > 0 ||
    Number(overviewData?.ignoredAmount) > 0 ||
    isPayeeOnAction ||
    (!isFrozen && !hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS));

  if (!hasPermissions(RBAC_ROLES.REGISTER_PAYOUTS)) {
    return null;
  }

  return (
    <>
      <EverTooltip title="Register Payment" placement="top">
        <EverButton.Icon
          size="small"
          type="ghost"
          color="base"
          icon={
            <CreditCardIcon
              className={`${
                isRegisterPaymentDisabled
                  ? "text-ever-base-content-mid"
                  : "text-ever-primary"
              }`}
            />
          }
          disabled={isRegisterPaymentDisabled}
          onClick={() => setRegisterPayment([overviewData?.payeeEmailId])}
        />
      </EverTooltip>
      <MakePaymentPopup
        keys={registerPayment}
        onClose={() => {
          if (!isPayeeOnAction) {
            setRegisterPayment([]);
          }
        }}
        onConfirm={handleRegisterPayment}
        tableData={tableData}
        period={moment(overviewData.periodEndDate).format("DD-MMMM-YYYY")}
        periodLabel={periodLabel}
        isLoading={isPayeeOnAction}
        viewPayoutValueOfOthers={hasPermissions(
          RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS
        )}
      />
    </>
  );
};

export default RegisterPaymentButton;
