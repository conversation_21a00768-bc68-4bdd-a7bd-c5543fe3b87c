import { CalendarIcon } from "@everstage/evericons/duotone";
import { cloneDeep } from "lodash";
import React from "react";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverSelect, EverInput, EverLoader } from "~/v2/components";

const SelectPeriod = ({
  onPeriodChange,
  selectedPeriod,
  periodSearchValue,
  setPeriodSearchValue,
  payeePeriodOptions,
  setPeriodOptions,
  periodOptions,
  payeeDetails,
}) => {
  const { accessToken } = useAuthStore();
  return (
    <div className="flex items-center">
      <EverSelect
        size="small"
        className="w-64 ml-2"
        onChange={(val, option) => {
          onPeriodChange(val);
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SWITCH_PERIOD, {
            [ANALYTICS_PROPERTIES.PERIOD]: option.label,
            [ANALYTICS_PROPERTIES.PAYEE_NAME]: payeeDetails.employeeEmailId,
          });
        }}
        value={selectedPeriod && Object.keys(selectedPeriod)[0]}
        options={periodOptions}
        dropdownRender={(menu) => (
          <div className="flex flex-col gap-1">
            <EverInput.Search
              size="small"
              placeholder="Search Period"
              onChange={(e) => {
                setPeriodSearchValue(e.target.value);
                const filteredPeriodOptions = cloneDeep(
                  payeePeriodOptions
                ).filter((period) =>
                  period.label
                    .toLowerCase()
                    .includes(e.target.value.toLowerCase())
                );
                setPeriodOptions(filteredPeriodOptions);
              }}
              value={periodSearchValue}
              allowClear
              autoFocus
            />
            <div>{menu}</div>
          </div>
        )}
        prependIcon={
          !periodOptions ? (
            <EverLoader.SpinnerLottie className="size-4" />
          ) : (
            <CalendarIcon className="size-4 text-ever-base-content-mid" />
          )
        }
      />
    </div>
  );
};

export default SelectPeriod;
