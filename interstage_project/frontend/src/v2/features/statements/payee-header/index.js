import { gql, useQuery } from "@apollo/client";
import {
  CheckIcon,
  DownloadStraightIcon,
  MessageNotificationSquareIcon,
  AlertCircleIcon,
} from "@everstage/evericons/outlined";
import { Menu, Dropdown } from "antd";
import { format, isValid, parse } from "date-fns";
import { cloneDeep } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { Link } from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  updateFreezeStatus,
  updatePaidStatus,
  updatePaidStatusV2,
  validateUpdateFreezeStatus,
} from "~/Api/CommissionActionService";
import {
  APPROVAL_ENTITY_TYPES,
  APPROVAL_STATUS,
  RBAC_ROLES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  LINE_ITEM_TYPE_ENTITY_TYPE,
} from "~/Enums";
import { breadcrumbAtom, navPortalAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { currentUTCTimeMsInString } from "~/Utils/DateUtils";
import {
  EverTooltip,
  EverButton,
  EverModal,
  EverNavPortal,
  EverHotToastMessage,
  toast,
  EverHotToastAlert,
} from "~/v2/components";
import { EverTg } from "~/v2/components/EverTypography";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { ApprovalWorkFlowTemplateSelectorWrapper } from "~/v2/features/approvals";
import UnFreezeConfirmationPopup from "~/v2/features/commissions/UnfreezeConfirmationPopup";
import AddQueryModal from "~/v2/features/queries/AddQueryModal";
import ProfileDrawer from "~/v2/features/statements/profile-drawer";

import ActionsMenu from "./ActionsMenu";
import CurrencyDropdown from "./CurrencyDropdown";
import DisplayUser from "./DisplayUser";
import { EmailStatementsModal, SENDOPTIONS } from "./EmailStatements";
import ExportButton from "./ExportButton";
import LockUnlockButton from "./LockUnlockButton";
import ProfileButton from "./ProfileButton";
import RegisterPaymentButton from "./RegisterPaymentButton";
import SelectPeriod from "./SelectPeriod";

const PayeeHeader = observer((props) => {
  const [registerPayment, setRegisterPayment] = useState([]);
  const [showUserOption, setShowUserOption] = useState(false);
  const [closeOption, setCloseOption] = useState(false);
  const [periodSearchValue, setPeriodSearchValue] = useState("");
  const [showApprovalTemplateSelector, setShowApprovalTemplateSelector] =
    useState(false);
  const [approvalParams, setApprovalParams] = useState(null);
  const [showUnFreezePopUp, setShowUnFreezePopUp] = useState(false);
  const [showEmailStatementsModal, setShowEmailStatementsModal] =
    useState(false);

  const navPortalLocation = useRecoilValue(navPortalAtom);
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const {
    store,
    overviewData,
    localeId,
    payeeDetails,
    payeeDesignation,
    payeePeriodOptions,
    selectedPeriod,
    showStatementsFilter,
    onPeriodChange,
    onUserChange,
    exportStatements,
    exportStatementsAsPdf,
    emailStatements,
    setDisplayCurrency,
    displayCurrency,
    refreshData,
    isLocked,
    isPayeeOnAction,
    setIsPayeeOnAction,
    clientFeatures,
    setRefetchTimeLineData,
    showCommissionPayouts,
    setShowApprovalBanner,
    setApprovalRequestedCount,
    approvalRequestedCount,
    displayUserLazyLoadProps,
    selectedUserEmailId,
    refetchApprovalStatusData,
    approvalStatusData,
  } = props;

  const { accessToken, setBaseCurrency } = store;
  const { t } = useTranslation();
  const [raiseQueryModalVisible, setRaiseQueryModalVisible] = useState(false);

  const GET_CURRENCY_DETAILS = gql`
    query CurrencySymbol($emailId: String!, $termEndDate: String) {
      myClient {
        baseCurrency
      }
      baseCurrencySymbol
      employeePayTimeSlice(emailId: $emailId, termEndDate: $termEndDate)
    }
  `;

  const [periodOptions, setPeriodOptions] = useState(
    cloneDeep(payeePeriodOptions)
  );
  const [drawerVisibility, setDrawerVisibility] = useState(false);
  const [isLockChanging, setIsLockChanging] = useState(false);

  const { data } = useQuery(GET_CURRENCY_DETAILS, {
    variables: {
      emailId: payeeDetails.employeeEmailId,
      termEndDate: Object.values(selectedPeriod)[0][1].format("DD/MM/YYYY"),
    },
    fetchPolicy: "no-cache",
  });
  const [currencyOption, setCurrencyoption] = useState([]);
  const { email } = useAuthStore();

  const [pendingChangesModalDetails, setPendingChangesModalDetails] = useState({
    show: false,
    title: "",
    message: "",
  });

  useEffect(() => {
    if (data && data.employeePayTimeSlice) {
      const payeeCurrencyDetails = JSON.parse(data.employeePayTimeSlice);
      setBaseCurrency(data.myClient.baseCurrency);
      setCurrencyoption({
        payeeCurrency: payeeCurrencyDetails.currency,
        payeeCurrencySymbol: payeeCurrencyDetails.currency_symbol,
        orgCurrency: data.myClient.baseCurrency,
        orgCurrencySymbol: data.baseCurrencySymbol,
      });
    }
  }, [data]);

  useEffect(() => {
    if (closeOption) {
      setShowUserOption(false);
      setTimeout(() => setCloseOption(false), 200);
    }
  }, [closeOption]);

  useEffect(() => {
    if (payeeDetails && selectedPeriod && periodOptions) {
      const selectedPeriodRange = Object.keys(selectedPeriod)[0];

      const selectedPeriodLabel = payeePeriodOptions
        .find((item) => item.value === selectedPeriodRange)
        .label.toString();

      /* Handle Breadcrumb title */

      const { firstName, lastName, employeeEmailId } = payeeDetails;
      const getPeriod = parse(
        selectedPeriodLabel.split("<->")[0],
        "MMMM yyyy",
        new Date()
      );
      const checkIfValidDate = isValid(getPeriod);
      const checkIfLoggedInUser = email === employeeEmailId;

      const getShortenedPeriod = checkIfValidDate
        ? format(getPeriod, "MMM yyyy")
        : selectedPeriodLabel;

      if (checkIfLoggedInUser) {
        setBreadcrumbName([
          {
            index: 0,
            title: `${getShortenedPeriod} - My Statement`,
            name: `${getShortenedPeriod} - My Statement`,
          },
        ]);
      } else {
        setBreadcrumbName([
          { index: 2 },
          {
            index: 1,
            title: `${firstName} ${lastName} - ${getShortenedPeriod} - Statements`,
          },
        ]);
      }
    }
  }, [selectedPeriod, payeeDetails, payeePeriodOptions, email]);

  const handleOnchange = (e) => {
    if (e.key) {
      setDisplayCurrency(currencyOption[e.key]);
      const currency_type =
        e.key == "orgCurrency"
          ? "Global Currency"
          : e.key == "payeeCurrency"
          ? "Payee Currency"
          : "New Currency";
      sendAnalyticsEvent(
        accessToken,
        ANALYTICS_EVENTS.SWITCH_DISPLAY_CURRENCY,
        {
          [ANALYTICS_PROPERTIES.CURRENCY_NAME]: currencyOption[e.key],
          [ANALYTICS_PROPERTIES.CURRENCY_TYPE]: currency_type,
          [ANALYTICS_PROPERTIES.PAYEE_NAME]: payeeDetails.employeeEmailId,
        }
      );
    }
  };

  function findPeriodLabel(period) {
    const periodOption = payeePeriodOptions.find(
      (payeePeriod) => payeePeriod.value == Object.keys(period)[0]
    );
    return periodOption.label;
  }

  const currencyMenu = (
    <Menu onClick={handleOnchange}>
      <div className="mb-2">
        <span className="text-ever-base-content-mid font-medium ml-4">
          Display Currency
        </span>
      </div>
      <Menu.Item
        key="orgCurrency"
        className={`flex justify-between ${
          displayCurrency === currencyOption.orgCurrency
            ? "bg-ever-primary-lite rounded-lg"
            : ""
        }`}
        disabled={currencyOption.payeeCurrency === currencyOption.orgCurrency}
      >
        <div
          className={`${
            currencyOption.payeeCurrency === currencyOption.orgCurrency
              ? "text-ever-base-content-low"
              : displayCurrency === currencyOption.orgCurrency
              ? "text-ever-base-content font-medium"
              : "text-ever-base-content"
          }`}
        >
          <div className="flex items-center">
            <span>Global</span>
            <span className="ml-2 text-ever-base-content-low">{`${currencyOption.orgCurrency} (${currencyOption.orgCurrencySymbol})`}</span>
          </div>
        </div>
        <CheckIcon
          className={`w-4 h-4 text-ever-primary stroke-2 ${
            currencyOption.payeeCurrency !== currencyOption.orgCurrency &&
            displayCurrency === currencyOption.orgCurrency
              ? "visible"
              : "invisible"
          }`}
        />
      </Menu.Item>
      <Menu.Item
        key="payeeCurrency"
        className={`flex justify-between ${
          displayCurrency === currencyOption.payeeCurrency ||
          displayCurrency === ""
            ? "bg-ever-primary-lite rounded-lg"
            : ""
        }`}
      >
        <div
          className={`${
            displayCurrency === currencyOption.payeeCurrency ||
            displayCurrency === ""
              ? "text-ever-base-content font-medium"
              : "text-ever-base-content"
          }`}
        >
          <div className="flex items-center">
            <span>Payee Currency</span>
            <span className="ml-2 text-ever-base-content-low">{`${currencyOption.payeeCurrency} (${currencyOption.payeeCurrencySymbol})`}</span>
          </div>
        </div>
        <CheckIcon
          className={`w-4 h-4 text-ever-primary stroke-2 ${
            displayCurrency === currencyOption.payeeCurrency ||
            displayCurrency === ""
              ? "visible"
              : "invisible"
          }`}
        />
      </Menu.Item>
    </Menu>
  );

  const onPayeeChange = () => {
    if (showStatementsFilter && !closeOption) {
      setShowUserOption(!showUserOption);
    }
  };
  const closeDrawer = () => setDrawerVisibility(false);

  const onExport = () => {
    const toastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Exporting statements as Excel..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    exportStatements()
      .then((response) => {
        if (response.ok) {
          return response;
        } else {
          throw new Error("Request failed");
        }
      })
      .then((response) => response.blob())
      .then((blobby) => {
        let objectUrl = window.URL.createObjectURL(blobby);
        let anchor = document.createElement("a");
        anchor.href = objectUrl;
        anchor.download =
          payeeDetails.employeeEmailId +
          "_ " +
          Object.values(selectedPeriod)[0][1].format("DD_MM_YYYY") +
          ".xlsx";
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
        toast.remove(toastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description="Downloaded Successfully!!"
            />
          ),
          { position: "top-center" }
        );
      })
      .catch((error) => {
        toast.remove(toastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description="Error while exporting statements as Excel"
            />
          ),
          { position: "top-center" }
        );
        console.log(error.message);
      });
  };

  function onExportAsPdf() {
    const msgs = [
      "Gathering necessary data...",
      "Setting up the layout...",
      "Building your PDF document...",
      "Compiling the PDF...",
      "Wrapping up the document creation...",
      "Hang tight, Taking longer than usual...",
    ];

    let toastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Exporting your statement as PDF..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );

    let timeouts = [];
    for (let id = 0; id < msgs.length; id++) {
      timeouts.push(
        setTimeout(() => {
          console.log(id, toastId);
          if (id <= msgs.length - 1 && toastId) {
            toast.remove(toastId);

            toastId = toast.custom(
              () => (
                <EverHotToastMessage type="loading" description={msgs[id]} />
              ),
              { position: "top-center", duration: Infinity }
            );
          }
        }, 50000 * (id + 1)) // 50 seconds
      );
    }

    exportStatementsAsPdf()
      .then((response) => {
        if (response.ok) {
          return response.blob();
        } else {
          throw new Error("Request failed");
        }
      })
      .then((blobby) => {
        let objectUrl = window.URL.createObjectURL(blobby);
        let anchor = document.createElement("a");
        anchor.href = objectUrl;
        const empId = payeeDetails.employeePayroll?.[0]?.employeeId
          ? `${payeeDetails.employeePayroll[0].employeeId}_`
          : "";
        const fileName = `${empId}${payeeDetails.firstName}_${
          payeeDetails.lastName
        }_${Object.values(selectedPeriod)[0][0].format(
          "DD-MM-YYYY"
        )}_${Object.values(selectedPeriod)[0][1].format("DD-MM-YYYY")}.pdf`;
        anchor.download = fileName;
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
        anchor.remove();
        toast.remove(toastId);
        timeouts.forEach((timeout) => clearTimeout(timeout));
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description="Downloaded Successfully!!"
            />
          ),
          { position: "top-center" }
        );
      })
      .catch((error) => {
        toast.remove(toastId);
        timeouts.forEach((timeout) => clearTimeout(timeout));
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description="Error while exporting statements as PDF"
            />
          ),
          { position: "top-center" }
        );
        console.log(error.message);
      });
  }
  const onEmailStatements = ({ recipientEmailId, emailUserOption }) => {
    const emailIdtoSend =
      emailUserOption === SENDOPTIONS.sendUser
        ? payeeDetails.employeeEmailId
        : recipientEmailId;

    const toastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={`Emailing statements to ${emailIdtoSend}...`}
        />
      ),
      { position: "top-center", duration: Infinity }
    );

    emailStatements(emailIdtoSend)
      .then((response) => {
        if (response.ok) {
          toast.remove(toastId);
          setShowEmailStatementsModal(false);
          return response;
        } else {
          throw new Error("Emailing statements failed");
        }
      })
      .catch((error) => {
        toast.remove(toastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description="Error while emailing statements"
            />
          ),
          { position: "top-center" }
        );
        console.log(error.message);
      });
  };

  const onClickRequestApproval = (payeeIds, bulk_mode) => {
    const payeeIdAmountCurrency = payeeIds.map((email) => {
      return {
        email_id: email,
        currency: overviewData.payeeCurrency,
        payout: overviewData.totalPayout,
        name: overviewData.payeeName,
      };
    });

    const ped = moment.utc(overviewData.periodEndDate);

    const data = {
      entity_type: APPROVAL_ENTITY_TYPES.PAYOUT,
      bulk_mode: bulk_mode,
      instance_params: {
        date: ped.format("DD-MM-YYYY"),
        instance_details: payeeIdAmountCurrency,
      },
    };
    setApprovalParams(data);
  };

  const onRequestApproval = (isLineItemLevel) => {
    const statusToCheck =
      approvalStatusData?.getLatestApprovalEntityKey ===
      LINE_ITEM_TYPE_ENTITY_TYPE
        ? [APPROVAL_STATUS.REQUESTED, APPROVAL_STATUS.NEEDS_ATTENTION]
        : [APPROVAL_STATUS.REQUESTED, APPROVAL_STATUS.APPROVED];
    if (
      isLocked &&
      !statusToCheck.includes(approvalStatusData?.getApprovalStatus)
    ) {
      setShowApprovalTemplateSelector(true);
      onClickRequestApproval([payeeDetails.employeeEmailId], false);
      setShowApprovalBanner(true);
    } else {
      EverModal.warning({
        title: (
          <div>
            <p className="text-left text-base">
              You cannot request approval when,
            </p>
            <p className="text-left text-sm font-normal">
              1. {t("PAYOUT")} is unlocked (or)
            </p>
            {isLineItemLevel ? (
              <>
                <p className="text-left text-sm font-normal">
                  2. Earned {t("COMMISSION")} is 0 (or)
                </p>
                <p className="text-left text-sm font-normal">
                  3. {t("PAYOUT")} is already approved or submitted for approval
                </p>
              </>
            ) : (
              <p className="text-left text-sm font-normal">
                2. {t("PAYOUT")} is already approved or submitted for approval
              </p>
            )}
          </div>
        ),
      });
    }
  };

  const handleError = (error, toastDescription) => {
    console.log(`ERROR - ${error}`);
    toast.custom(
      () => <EverHotToastMessage type="error" description={toastDescription} />,
      { position: "top-center" }
    );
  };
  const showSyncRunningToastAlert = () => {
    toast.custom(
      (t) => (
        <EverHotToastAlert
          type="error"
          description="Locking or unlocking statements is disabled while Commission Sync is in progress. Please try again after the sync is complete."
          title="Locking Temporarily Disabled"
          toastId={t.id}
          className="w-[519px]"
          truncateDescription={false}
          buttons={[
            {
              buttonText: "Dismiss",
              onClick: () => toast.dismiss(t.id),
              className: "border-l",
            },
          ]}
        />
      ),
      { position: "top-right", duration: Infinity }
    );
  };

  const handleLockUnlockFailureWithPendingChanges = (responseData) => {
    if (responseData?.messageCode === "CANNOT_LOCK_ON_ONGOING_SYNC") {
      showSyncRunningToastAlert();
    } else if (
      responseData?.messageCode == "LOCK_RESTRICTED_SINGLE_PAYEE_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Locking Restricted",
        message:
          "This payee has unsynced commission-impacting changes, hence their statement could not be locked. Run Commission Sync for them in this payout period, then return to complete locking their statement",
      });
    }
  };

  const handleLockUnlockAction = async (data, toLock) => {
    try {
      const response = await updateFreezeStatus(data, accessToken);
      const responseData = await response.json();
      if (response.ok) {
        await refreshData();
        await refetchApprovalStatusData();
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={`Statement ${
                toLock ? "locked" : "unlocked"
              } successfully`}
            />
          ),
          { position: "top-center" }
        );
        setRefetchTimeLineData(currentUTCTimeMsInString());
      } else if (
        clientFeatures.canAvoidLockingWithPendingChanges &&
        !response.ok &&
        responseData?.messageCode
      ) {
        handleLockUnlockFailureWithPendingChanges(responseData);
      } else {
        throw new Error("Request failed");
      }
    } catch (error) {
      handleError(
        error,
        `Error while ${
          toLock ? "locking" : "unlocking"
        } statement. Please try again later`
      );
    }
  };

  const handlePaymentFailureWithPendingChanges = (responseData) => {
    if (responseData?.messageCode == "CANNOT_LOCK_ON_ONGOING_SYNC") {
      showSyncRunningToastAlert();
    } else if (
      responseData?.messageCode == "PAYMENT_RESTRICTED_SINGLE_PAYEE_MESSAGE"
    ) {
      setPendingChangesModalDetails({
        show: true,
        title: "Payment registration restricted",
        message:
          "This payee has unsynced commission-impacting changes, hence their payment could not be registered. Run Commission Sync for them in this payout period, then return to complete registering their payment",
      });
    }
  };

  const handleLockUnlock = async () => {
    const toLock = !isLocked;
    if (isLocked) setShowApprovalBanner(false);
    const ped = moment.utc(overviewData.periodEndDate);
    const handleLockUrl = clientFeatures.canAvoidLockingWithPendingChanges
      ? "/spm/settlements/comm_lock_with_pending_changes"
      : "/spm/settlements/update_settlement_comm_freeze_status";
    const data = {
      payeeIds: [payeeDetails.employeeEmailId],
      date: ped.format("DD-MM-YYYY"),
      isLocked: toLock,
      url: handleLockUrl,
      abort_instance_check: !toLock && clientFeatures?.showApprovalFeature,
    };
    setIsPayeeOnAction(true);
    setIsLockChanging(true);
    setShowUnFreezePopUp(false);
    try {
      if (!toLock && clientFeatures.warnOnUnlock == "SUMMARY") {
        const validationResponse = await validateUpdateFreezeStatus(
          data,
          accessToken
        );
        if (!validationResponse.ok) {
          throw new Error("Validation API call failed");
        }
        const validationData = await validationResponse.json();

        if (validationData.payoutChanged) {
          EverModal.confirm({
            width: 600,
            title: "Payout values may change",
            subtitle:
              "The payout values may have been updated. Unlocking this section will modify the locked commission values.",
            noteMessage: (
              <div className="text-left">
                <EverTg.Caption>
                  To ensure accurate data, we recommend running the{" "}
                  <a
                    href="/settings/commissions-and-data-sync"
                    target="_blank"
                    className="text-ever-primary font-medium hover:underline hover:text-ever-primary"
                  >
                    {t("COMMISSION").toLowerCase()} sync
                  </a>{" "}
                  after unlocking. This will calculate{" "}
                  {t("COMMISSIONS").toLowerCase()} using the latest data and
                  ensure accuracy. If you need the current{" "}
                  {t("COMMISSION").toLowerCase()} data before unlocking, go back
                  and export the statement.
                </EverTg.Caption>
                <p className="mt-4">
                  <EverTg.Caption>Proceed to unlock?</EverTg.Caption>
                </p>
              </div>
            ),
            okText: "Confirm",
            cancelText: "Discard",
            centered: true,
            onOk: async () => {
              await handleLockUnlockAction(data, toLock);
            },
            onCancel: () => {
              setIsLockChanging(false);
              setIsPayeeOnAction(false);
            },
          });
          return;
        }
      }
      await handleLockUnlockAction(data, toLock);
    } catch (error) {
      handleError(
        error,
        `Error while ${
          toLock ? "locking" : "unlocking"
        } statement. Please try again later`
      );
    } finally {
      setIsLockChanging(false);
      setIsPayeeOnAction(false);
    }
  };
  const handleRegisterPayment = async (paymentDetails) => {
    const ped = moment.utc(overviewData.periodEndDate);
    const data = {
      ...paymentDetails,
      date: ped.format("DD-MM-YYYY"),
    };
    const avoidConcurrentRegisterPayment =
      clientFeatures?.avoidConcurrentRegisterPayment;
    if (data?.payeeIds?.length > 0) {
      setIsPayeeOnAction(true);

      if (
        avoidConcurrentRegisterPayment ||
        clientFeatures.canAvoidLockingWithPendingChanges
      ) {
        try {
          const response = await updatePaidStatusV2(data, accessToken);
          const resData = await response.json();
          if (
            response.ok &&
            "messageCode" in resData &&
            resData.messageCode === "PROCESSED_COMPLETELY"
          ) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description="Payment registered successfully"
                />
              ),
              { position: "top-center" }
            );
            refreshData();
            refetchApprovalStatusData();
            setRegisterPayment([]);
          } else if (
            response.ok &&
            "messageCode" in resData &&
            resData.messageCode === "PROCESS_FAILED"
          ) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description="Previous payment is still in progress"
                />
              ),
              { position: "top-center" }
            );
            refreshData();
            refetchApprovalStatusData();
            setRegisterPayment([]);
          } else if (
            !response.ok &&
            "messageCode" in resData &&
            [
              "PAYMENT_RESTRICTED_SINGLE_PAYEE_MESSAGE",
              "CANNOT_LOCK_ON_ONGOING_SYNC",
            ].includes(resData.messageCode)
          ) {
            handlePaymentFailureWithPendingChanges(resData);
          } else if (response.ok) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description="Payment registered successfully"
                />
              ),
              { position: "top-center" }
            );
            refreshData();
            refetchApprovalStatusData();
            setRegisterPayment([]);
          } else {
            throw new Error("Request failed");
          }
        } catch (error) {
          console.log(`PAYMENT UPDATE ERROR - ${error}`);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description="Error while registering payment. Please try again later"
              />
            ),
            { position: "top-center" }
          );
        }
      } else {
        try {
          const response = await updatePaidStatus(data, accessToken);
          const resData = await response.json();
          if (response.ok) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description="Payment registered successfully"
                />
              ),
              { position: "top-center" }
            );
            refreshData();
            refetchApprovalStatusData();
            setRegisterPayment([]);
          } else {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="error"
                  description={
                    resData?.error ||
                    resData ||
                    "Error Occured while registering payouts"
                  }
                />
              ),
              { position: "top-center" }
            );
          }
        } catch (error) {
          console.log(`PAYMENT UPDATE ERROR - ${error}`);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description="Error while registering payment. Please try again later"
              />
            ),
            { position: "top-center" }
          );
        }
      }
      setRegisterPayment([]);
      setIsPayeeOnAction(false);
    }
  };

  const exportMenu = (
    <Menu>
      <Menu.Item
        key="export-csv"
        icon={
          <DownloadStraightIcon className="w-5 h-5 text-ever-base-content-mid" />
        }
        onClick={onExport}
      >
        <EverTg.Text className="text-ever-base-content">
          Microsoft Excel (.xlsx)
        </EverTg.Text>
      </Menu.Item>
      <Menu.Item
        key="export-pdf"
        icon={
          <DownloadStraightIcon className="w-5 h-5 text-ever-base-content-mid" />
        }
        onClick={onExportAsPdf}
      >
        <EverTg.Text className="text-ever-base-content">PDF (.pdf)</EverTg.Text>
      </Menu.Item>
    </Menu>
  );

  const { hasPermissions } = useUserPermissionStore();

  return (
    <>
      <EverNavPortal target={navPortalLocation}>
        <div className="flex flex-wrap">
          <div className="grow flex items-center">
            <DisplayUser
              payeeDetails={payeeDetails}
              onPayeeChange={onPayeeChange}
              showStatementsFilter={showStatementsFilter}
              showUserOption={showUserOption}
              payeeDesignation={payeeDesignation}
              onUserChange={onUserChange}
              setCloseOption={setCloseOption}
              displayUserLazyLoadProps={displayUserLazyLoadProps}
              selectedUserEmailId={selectedUserEmailId}
              showViewProfileChildren={
                <ProfileButton
                  setDrawerVisibility={setDrawerVisibility}
                  payeeDetails={payeeDetails}
                />
              }
            />
          </div>
          <div className="ml-auto flex flex-row gap-3 items-center">
            <SelectPeriod
              onPeriodChange={onPeriodChange}
              selectedPeriod={selectedPeriod}
              periodSearchValue={periodSearchValue}
              setPeriodSearchValue={setPeriodSearchValue}
              payeePeriodOptions={payeePeriodOptions}
              setPeriodOptions={setPeriodOptions}
              periodOptions={periodOptions}
              payeeDetails={payeeDetails}
            />
            <RBACProtectedComponent permissionId={RBAC_ROLES.CREATE_QUERIES}>
              {showCommissionPayouts && (
                <EverTooltip title="Raise Query" placement="top">
                  <EverButton.Icon
                    size="small"
                    type="ghost"
                    color="base"
                    icon={
                      <MessageNotificationSquareIcon className="text-ever-primary" />
                    }
                    onClick={() => setRaiseQueryModalVisible(true)}
                  />
                </EverTooltip>
              )}
              <AddQueryModal
                isVisible={raiseQueryModalVisible}
                setIsVisible={setRaiseQueryModalVisible}
                subject={`${findPeriodLabel(selectedPeriod)} Statement`}
                contextAttributes={{
                  Period: findPeriodLabel(selectedPeriod),
                  "Payee Email Id": payeeDetails.employeeEmailId,
                }}
              />
            </RBACProtectedComponent>
            {/* 
            Action menu will be visible only for admin-users. So, checking for 
            manage:payouts and register:payouts permissions.
          */}
            {hasPermissions([
              RBAC_ROLES.MANAGE_PAYOUTS,
              RBAC_ROLES.REGISTER_PAYOUTS,
            ]) ? (
              <>
                {currencyOption.payeeCurrency !==
                  currencyOption.orgCurrency && (
                  <CurrencyDropdown currencyMenu={currencyMenu} />
                )}
                {hasPermissions(RBAC_ROLES.MANAGE_PAYOUTS) && (
                  <LockUnlockButton
                    approvalStatus={approvalStatusData}
                    setShowUnFreezePopUp={setShowUnFreezePopUp}
                    isLocked={isLocked}
                    isLockChanging={isLockChanging}
                    showApprovalFeature={clientFeatures?.showApprovalFeature}
                    allowAdjustmentsToFrozenCommission={
                      clientFeatures.allowAdjustmentsToFrozenCommission
                    }
                    handleLockUnlock={handleLockUnlock}
                    employeeEmailId={payeeDetails.employeeEmailId}
                    selectedPeriod={
                      new Date(Object.values(selectedPeriod)[0][1])
                    }
                  />
                )}
                {hasPermissions(RBAC_ROLES.REGISTER_PAYOUTS) && (
                  <RegisterPaymentButton
                    handleRegisterPayment={handleRegisterPayment}
                    overviewData={overviewData}
                    registerPayment={registerPayment}
                    setRegisterPayment={setRegisterPayment}
                    isPayeeOnAction={isPayeeOnAction}
                    periodLabel={findPeriodLabel(selectedPeriod)}
                    isDisabled={
                      !(
                        displayCurrency === currencyOption.payeeCurrency ||
                        displayCurrency === ""
                      )
                    }
                  />
                )}
                {hasPermissions([
                  RBAC_ROLES.EXPORT_STATEMENT,
                  RBAC_ROLES.EXPORT_PAYOUTS,
                  RBAC_ROLES.MANAGE_PAYOUTS,
                ]) && (
                  <ActionsMenu
                    onExport={onExport}
                    onExportAsPdf={onExportAsPdf}
                    onRequestApproval={onRequestApproval}
                    showCommissionPayouts={showCommissionPayouts}
                    onShowEmailStatementsModal={() =>
                      setShowEmailStatementsModal(true)
                    }
                  />
                )}
                {showApprovalTemplateSelector && (
                  <ApprovalWorkFlowTemplateSelectorWrapper
                    requestParams={approvalParams}
                    showSelector={showApprovalTemplateSelector}
                    setShowSelector={setShowApprovalTemplateSelector}
                    refetch={() => {
                      setRefetchTimeLineData(currentUTCTimeMsInString());
                      refreshData();
                      setApprovalRequestedCount(approvalRequestedCount + 1);
                      refetchApprovalStatusData();
                    }}
                  />
                )}
              </>
            ) : (
              // For payee experience, action menu won't be visible. They can
              // export statements and change display currency.
              <>
                <RBACProtectedComponent
                  permissionId={[
                    RBAC_ROLES.EXPORT_STATEMENT,
                    RBAC_ROLES.EXPORT_PAYOUTS,
                  ]}
                >
                  {showCommissionPayouts ? (
                    <Dropdown overlay={exportMenu} trigger={["click"]}>
                      <ExportButton />
                    </Dropdown>
                  ) : null}
                </RBACProtectedComponent>
                {currencyOption.payeeCurrency !==
                  currencyOption.orgCurrency && (
                  <CurrencyDropdown currencyMenu={currencyMenu} />
                )}
              </>
            )}
          </div>
        </div>
      </EverNavPortal>
      {drawerVisibility && (
        <ProfileDrawer
          localeId={localeId}
          closeDrawer={closeDrawer}
          visible={drawerVisibility}
          selectedPayee={payeeDetails.employeeEmailId}
          dateRange={
            Object.keys(selectedPeriod)
              ? Object.values(selectedPeriod)[0][1]
              : null
          }
          periodStartDate={Object.values(selectedPeriod)[0][0]}
          periodEndDate={Object.values(selectedPeriod)[0][1]}
          customView={true}
        />
      )}
      {showUnFreezePopUp && (
        <UnFreezeConfirmationPopup
          isVisible={true}
          onConfirm={handleLockUnlock}
          onCancel={() => setShowUnFreezePopUp(false)}
        />
      )}
      {showEmailStatementsModal && (
        <EmailStatementsModal
          isVisible={showEmailStatementsModal}
          onConfirm={onEmailStatements}
          onCancel={() => setShowEmailStatementsModal(false)}
        />
      )}
      {clientFeatures.canAvoidLockingWithPendingChanges && (
        <EverModal.Confirm
          title={pendingChangesModalDetails.title}
          subtitle={pendingChangesModalDetails.message}
          visible={pendingChangesModalDetails.show}
          width={620}
          onClose={() =>
            setPendingChangesModalDetails({
              show: false,
              title: "",
              message: "",
            })
          }
          confirmationButtons={[
            <EverButton
              key="cancel"
              color="base"
              onClick={() => {
                setPendingChangesModalDetails({
                  show: false,
                  title: "",
                  message: "",
                });
              }}
            >
              Dismiss
            </EverButton>,
            <EverButton
              key="run-commission-sync"
              color="primary"
              onClick={() => {
                setPendingChangesModalDetails({
                  show: false,
                  title: "",
                  message: "",
                });
              }}
            >
              <Link
                to={"/settings/commissions-and-data-sync"}
                className="text-ever-primary-content hover:text-ever-primary-content"
              >
                Run Commission Sync
              </Link>
            </EverButton>,
          ]}
          icon={<AlertCircleIcon className="h-6 w-6 text-ever-warning" />}
          iconContainerClasses="bg-ever-warning-lite"
        />
      )}
    </>
  );
});

export default PayeeHeader;
