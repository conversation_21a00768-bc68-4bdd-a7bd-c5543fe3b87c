import { AlertSquareIcon } from "@everstage/evericons/solid";
import { isEmpty } from "lodash";
import React, { useState } from "react";

import { ANALYTICS_PROPERTIES, COMMISSION_TYPE } from "~/Enums";
import { EverButton, EverModal, EverSelect, EverTg } from "~/v2/components";
import { CRITERIA_COLUMN_COMPONENT } from "~/v2/features/commission-canvas-modified/constants";
import StatementColumnsWrapper from "~/v2/features/commission-canvas-modified/reusable/StatementColumnsWrapper";

function CustomizeColumnComponent({
  visible,
  criteriaList,
  commissionPlanDetails,
  defaultCriteriaId,
  handleClose,
  handleApply,
}) {
  const [isModify, setIsModify] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const selectedCriteria = criteriaList.find(
    (criteria) => criteria.id === defaultCriteriaId
  );
  return (
    <EverModal
      title={
        <div className="flex flex-col gap-0.5">
          <EverTg.Heading3>Customize component columns</EverTg.Heading3>
          <EverTg.Caption className="text-ever-base-content-mid font-normal">
            Choose the columns to display in the Statement table view for each
            component. These updates will apply to all payees in the plan.
          </EverTg.Caption>
        </div>
      }
      onCancel={() => {
        if (isModify) {
          setOpenPopup(true);
        } else {
          handleClose();
        }
      }}
      width="787px"
      bodyStyle={{ height: 650, paddingTop: "12px" }}
      visible={visible}
      destroyOnClose
    >
      {openPopup && (
        <div className="bg-ever-base-25 absolute right-0 top-10 mr-6 mt-6 px-3 py-3 inline-flex flex-col items-start gap-4 border border-solid rounded-xl border-ever-base-300 shadow">
          <div className="max-w-[305px] h-24">
            <div className="flex items-center">
              <AlertSquareIcon className="w-5 h-5 text-ever-warning mr-2" />
              <EverTg.Heading3>Unsaved changes detected</EverTg.Heading3>
            </div>
            <EverTg.Text className="text-sm text-ever-base-content whitespace-nowrap">
              Do you want to save your changes before leaving?
            </EverTg.Text>
            <div className="flex justify-end items-end self-stretch gap-3 mt-4">
              <EverButton
                className="!h-4 !py-4"
                color="base"
                onClick={() => {
                  handleClose();
                  setIsModify(false);
                  setOpenPopup(false);
                }}
              >
                Discard
              </EverButton>
              <EverButton
                className="!h-4 !py-4"
                color="primary"
                onClick={() => {
                  setOpenPopup(false);
                }}
              >
                Continue
              </EverButton>
            </div>
          </div>
        </div>
      )}
      {visible && !isEmpty(selectedCriteria) && (
        <CustomizeColumnsWrapper
          criteriaList={criteriaList}
          commissionPlanDetails={commissionPlanDetails}
          defaultCriteriaId={defaultCriteriaId}
          handleClose={() => {
            if (isModify) {
              setOpenPopup(true);
            } else {
              handleClose();
            }
          }}
          handleApply={(criteriaList) => {
            handleApply(criteriaList)
              .then(() => {
                setIsModify(false);
                setOpenPopup(false);
              })
              .catch((error) => {
                console.log(error);
              });
          }}
          setIsModify={setIsModify}
        />
      )}
    </EverModal>
  );
}

const CustomizeColumnsWrapper = ({
  criteriaList,
  commissionPlanDetails,
  defaultCriteriaId,
  handleApply,
  handleClose,
  setIsModify,
}) => {
  const [localSelectedCriteriaId, setLocalSelectedCriteriaId] =
    useState(defaultCriteriaId);

  const [localCriteriaList, setLocalCriteriaList] = useState(criteriaList);

  const localCriteriaListInfo = localCriteriaList.find(
    (criteria) => criteria.id === localSelectedCriteriaId
  );

  const items = criteriaList.map((criteriaDetail) => ({
    label: criteriaDetail.name,
    value: criteriaDetail.id,
  }));

  const handleCriteriaChange = (newSelectedCriteriaId) => {
    setLocalSelectedCriteriaId(newSelectedCriteriaId);
  };

  const handleApplyLocal = () => {
    const modifiedList = localCriteriaList.map(({ id, criteriaColumns }) => ({
      criteriaId: id,
      criteriaColumn: criteriaColumns,
    }));

    handleApply(modifiedList);
  };

  const updateCriteriaColumns = (criteriaId, columns) => {
    const updatedList = localCriteriaList.map((criteria) => {
      if (criteria.id === criteriaId) {
        return {
          ...criteria,
          criteriaColumns: columns,
        };
      }
      return criteria;
    });
    setIsModify(true);
    setLocalCriteriaList(updatedList);
  };

  return (
    <>
      <div className="flex gap-2 items-center mb-5">
        <EverTg.Caption className="text-ever-base-content-mid font-normal text-sm">
          Component
        </EverTg.Caption>
        <EverSelect
          value={localSelectedCriteriaId}
          onChange={handleCriteriaChange}
          className="w-52"
          options={items}
          placeholder="Select Criteria"
        />
      </div>
      <StatementColumnsWrapper
        key={localSelectedCriteriaId}
        planType={COMMISSION_TYPE.COMMISSION_PLAN}
        component={CRITERIA_COLUMN_COMPONENT.STATEMENT}
        componentDisplayName={"Statement"}
        commissionPlanDetails={commissionPlanDetails}
        criteriaId={localCriteriaListInfo.id}
        criteriaType={localCriteriaListInfo.criteriaType}
        criteriaW={localCriteriaListInfo}
        analyticsProperties={{ [ANALYTICS_PROPERTIES.LOCATION]: "Statement" }}
        handleClose={handleClose}
        handleApply={() => handleApplyLocal()}
        updateCriteriaColumns={updateCriteriaColumns}
        footerSubtitleComponent={
          <div className="flex items-center">
            <AlertSquareIcon className="w-5 h-5 text-ever-warning mr-2" />
            <EverTg.SubHeading4 className="text-ever-base-content">
              To update the table view of one or more components, click the
              “Save” button.
            </EverTg.SubHeading4>
          </div>
        }
      />
    </>
  );
};

export default CustomizeColumnComponent;
