import { gql, useQuery } from "@apollo/client";
import {
  CommissionTraceIcon,
  MessageNotificationSquareIcon,
} from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { debounce, isEmpty, isNil, isString, startCase } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useDSVariableStore } from "~/GlobalStores/DSVariableStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { numComparator } from "~/Utils/agGridUtils";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  formatCurrencyWrapper,
  getLocalizedCurrencyValue,
} from "~/Utils/CurrencyUtils";
import { formatDateDDMMMYYYY } from "~/Utils/DateUtils";
import { sortCallbackUtil } from "~/Utils/sortColumnsUtils";
import { EverButton, EverTg, EverTooltip } from "~/v2/components";
import {
  everAgGridCallbacks,
  everAgGridOptions,
  DynamicPagination as Pagination,
} from "~/v2/components/ag-grid";
import AddQueryModal from "~/v2/features/queries/AddQueryModal";
import CommissionTrace from "~/v2/features/statements/commission-trace";
import {
  getIsDateType,
  isNumberType,
  isPercentageType,
} from "~/v2/features/statements/store";

import "./styles.module.scss";

const { adjustColumnWidth } = everAgGridCallbacks;

/**
 * @typedef {import("~/Utils/sortColumnsUtils").SortInfoType} SortInfoType
 */

export const PAYOUT_DETAILS = gql`
  query payoutDetails(
    $payeeEmail: String!
    $psd: String!
    $ped: String!
    $displayCurrency: String!
    $criteriaId: String!
    $planId: String!
    $commissionType: String!
    $offsetValue: Int!
    $limitValue: Int!
    $commPeriod: String
    $sortFields: [String]
    $sortOrders: [String]
  ) {
    payoutDetails(
      payeeEmail: $payeeEmail
      psd: $psd
      ped: $ped
      currency: $displayCurrency
      criteriaId: $criteriaId
      planId: $planId
      commissionType: $commissionType
      offsetValue: $offsetValue
      limitValue: $limitValue
      commPeriod: $commPeriod
      sortFields: $sortFields
      sortOrders: $sortOrders
    )
    hyperlinkMapForStatements(criteriaId: $criteriaId, planId: $planId)
    selectedCriteriaColumns(criteriaId: $criteriaId)
  }
`;

const PAYOUT_RECORDS_COUNT = gql`
  query payoutRecordsCount(
    $payeeEmail: String!
    $psd: String!
    $ped: String!
    $displayCurrency: String!
    $criteriaId: String!
    $planId: String!
    $commissionType: String!
    $commPeriod: String
  ) {
    payoutRecordsCount(
      payeeEmail: $payeeEmail
      psd: $psd
      ped: $ped
      currency: $displayCurrency
      criteriaId: $criteriaId
      planId: $planId
      commissionType: $commissionType
      commPeriod: $commPeriod
    )
  }
`;

const extractNumericValue = (value) => {
  const [_, numericValue] = value.split(" ");
  return numericValue || value;
};

const quotaErosionComparator = (valueA, valueB) => {
  const numericValue1 = extractNumericValue(valueA);
  const numericValue2 = extractNumericValue(valueB);
  return numComparator(numericValue1, numericValue2);
};

const PayoutDetailsTable = observer(
  ({
    drawerData,
    currencySymbol,
    localeId,
    showCommissionPayouts,
    periodLabel,
    isBaseCurrency,
    criteriaDescription,
    showSettlementView,
  }) => {
    const { dataTypesById: dsDataTypesById } = useVariableStore();
    const {
      optionsMapByDS,
      systemNameToDisplayNameMap: dsSystemNameToDisplayNameMap,
    } = useDSVariableStore(drawerData.datasheetId);
    const variablesMap = optionsMapByDS[drawerData.datasheetId];
    const systemNameToDisplayNameMap =
      dsSystemNameToDisplayNameMap[drawerData.datasheetId] || {};
    const [pageSize, setPageSize] = useState(100);
    const [currentPage, setCurrentPage] = useState(1);
    const [orderbyFields, setOrderbyFields] = useState(
      /** @type {SortInfoType[]}*/ ([])
    );
    const [dataCount, setDataCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [selectedTrace, setSelectedTrace] = useState({});
    const [isRaiseQueryModalVisible, setIsRaiseQueryModalVisible] =
      useState(false);
    const [selectedRowForQuery, setSelectedRowForQuery] = useState({});

    const myAtom = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myAtom);

    const { hasPermissions } = useUserPermissionStore();

    const { t } = useTranslation();

    const getURL = (item, params, hyperlinkMap) => {
      let url = hyperlinkMap[item]["url"];
      hyperlinkMap[item]["url_identifier_fields"].forEach((key) => {
        url = url.replace(key, params.data[String(key)]);
      });
      return url;
    };

    const checkAllFieldsArePresent = (item, params, hyperlinkMap) => {
      const dataKeys = Object.keys(params.data);
      return hyperlinkMap[item]["url_identifier_fields"].every((item) =>
        dataKeys.includes(String(item))
      );
    };

    const { loading: loadingPayoutDetails, data } = useQuery(PAYOUT_DETAILS, {
      fetchPolicy: "network-only",
      notifyOnNetworkStatusChange: true,
      variables: {
        ...drawerData,
        ...(drawerData.period
          ? {
              commPeriod: drawerData.commPeriod,
            }
          : {}),
        offsetValue: currentPage - 1,
        limitValue: pageSize,
        sortOrders: orderbyFields.map((item) => item.order),
        sortFields: orderbyFields.map((item) => item.column),
      },
    });

    const { loading: loadingPayoutRecordsCount } = useQuery(
      PAYOUT_RECORDS_COUNT,
      {
        fetchPolicy: "network-only",
        notifyOnNetworkStatusChange: true,
        variables: {
          ...drawerData,
          ...(drawerData.period
            ? {
                commPeriod: drawerData.commPeriod,
              }
            : {}),
        },
        onCompleted: (data) => {
          setDataCount(data?.payoutRecordsCount || 0);
        },
      }
    );

    useEffect(() => {
      setLoading(loadingPayoutDetails || loadingPayoutRecordsCount);
    }, [loadingPayoutDetails, loadingPayoutRecordsCount]);

    const { payoutDetails, rowData, columns } = useMemo(() => {
      let payoutDetails = {};
      let rowData = [];
      let columns = [];
      if (!isEmpty(data?.payoutDetails)) {
        payoutDetails = JSON.parse(data?.payoutDetails || "{}");
        rowData = payoutDetails.records || [];
        columns = payoutDetails.columns || [];
      }
      return { payoutDetails, rowData, columns };
    }, [data]);

    const sortCallback = (column) => {
      sortCallbackUtil(column, orderbyFields, setOrderbyFields);
    };

    const getCommissionColumnDefs = (hasCommission) => {
      return {
        pinned: "right",
        type: "rightAligned",
        comparator: numComparator,
        valueFormatter: (params) => {
          if (
            (!hasCommission && params.node.rowPinned !== "bottom") ||
            (hasCommission &&
              payoutDetails.is_line_item_level === false &&
              params.node.rowIndex !== 0 &&
              params.node.rowPinned !== "bottom")
          ) {
            return "";
          }
          return getLocalizedCurrencyValue(
            params.value,
            currencySymbol,
            localeId
          );
        },
        cellClass: "justify-end font-medium",
        cellClassRules: {
          [""]: () => payoutDetails.is_line_item_level === false,
          ["text-ever-success-hover"]: (params) => Number(params.value > 0),
          ["text-ever-error"]: (params) => Number(params.value < 0),
          "font-semibold": (params) => params.node.rowPinned === "bottom",
        },
      };
    };

    const querySection = (() => {
      const planType = drawerData.planType;
      if (planType === "earnedCommissions") {
        return showSettlementView
          ? t("QUERIES_SECTION_COMM_SUMM_EC")
          : t("PAYOUT_SUMMARY");
      } else if (planType === "deferredCommissions") {
        return t("QUERIES_SECTION_DEFF_COMM");
      } else if (planType === "previousDeferredCommissions") {
        return t("QUERIES_SECTION_PREV_DEFF_COMM");
      } else if (planType === "currentPayouts") {
        return t("QUERIES_SECTION_CURR_PAYOUT");
      }
      return ""; // Default case if needed
    })();

    const columnDefs = useMemo(() => {
      if (
        isEmpty(payoutDetails) ||
        isEmpty(rowData) ||
        isEmpty(systemNameToDisplayNameMap)
      ) {
        return [];
      }
      const colDefs = [
        {
          pinned: "left",
          lockPosition: "left",
          suppressMenu: true,
          field: "rowNumbers",
          headerName: "#",
          suppressColumnsToolPanel: true,
          suppressFiltersToolPanel: true,
          valueGetter: (params) => {
            return params.node?.childIndex === null
              ? ""
              : params.node.childIndex + 1 + (currentPage - 1) * pageSize;
          },
          cellRenderer: (params) => {
            return params.node.rowPinned === "bottom" ? (
              <span className="text-ever-base-content font-semibold">
                {params.data.rowNumbers}
              </span>
            ) : (
              <span className="text-ever-base-content">{params.value}</span>
            );
          },
          width: 80,
          maxWidth: 80,
          minWidth: 80,
          suppressSizeToFit: true,
          cellStyle: { paddingRight: 0 },
        },
      ];
      const hasCommission = columns.includes("commission");
      const hyperlinkMap = JSON.parse(data.hyperlinkMapForStatements);
      for (const item of columns) {
        if (!data.selectedCriteriaColumns?.includes(item)) {
          continue;
        }
        let isDate = false,
          isNumber = false,
          isPercentage = false;
        if (!isEmpty(variablesMap) && !isEmpty(systemNameToDisplayNameMap)) {
          isDate = getIsDateType(
            variablesMap,
            systemNameToDisplayNameMap,
            dsDataTypesById,
            item
          );
          isNumber = isNumberType(
            variablesMap,
            systemNameToDisplayNameMap,
            dsDataTypesById,
            item
          );
          isPercentage = isPercentageType(
            variablesMap,
            systemNameToDisplayNameMap,
            dsDataTypesById,
            item
          );
        }
        const sortInfo = orderbyFields.find(
          (orderbyField) => orderbyField.column === item
        );
        colDefs.push({
          cellDataType: false,
          field: item,
          headerName: systemNameToDisplayNameMap[item],
          headerComponentParams: {
            enableSorting: false,
            serverSideSortable: true,
            sortOrder: sortInfo?.order ?? "",
            sortByField: item,
            sortCallback: sortCallback,
          },
          valueFormatter: (params) => {
            if (typeof params.value === "boolean") {
              return startCase(params.value);
            } else if (params.value) {
              const val = params.value.toString();
              return isPercentage
                ? val + "%"
                : isDate
                ? formatDateDDMMMYYYY(val)
                : isNumber
                ? formatCurrencyWrapper(val)
                : val;
            }
            if (params?.raiseQuery === true) {
              return params.value;
            }
            return params.node.rowPinned === "bottom" ? "" : "-";
          },
          ...(item === "commission" && {
            hide: !showCommissionPayouts,
            headerName: `${t("COMMISSION")}${
              payoutDetails.is_line_item_level === false ? " (Summation)" : ""
            }`,
            ...getCommissionColumnDefs(hasCommission),
          }),
          ...(isPercentage && {
            type: "rightAligned",
          }),
          ...(isNumber && {
            type: "rightAligned",
            cellClass: "justify-end",
            comparator: numComparator,
            cellRenderer: (params) => (
              <EverTooltip
                title={formatCurrencyWrapper(params.value, {
                  decimalPlaces: 6,
                  truncate: true,
                })}
                placement="bottom"
              >
                {formatCurrencyWrapper(params.value)}
              </EverTooltip>
            ),
          }),
          ...(clientFeatures?.crmHyperlinks &&
            Object.keys(hyperlinkMap).includes(item) && {
              cellRenderer: (params) => {
                if (checkAllFieldsArePresent(item, params, hyperlinkMap)) {
                  return (
                    <a
                      href={getURL(item, params, hyperlinkMap)}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {params.data[item]}
                    </a>
                  );
                }
                return <p>{params.data[item]}</p>;
              },
            }),

          ...(item === "quotaErosion" && {
            field: "quota_erosion",
            headerName: `${t("QUOTA_EROSION")}`,
            type: "rightAligned",
            cellClass: "justify-end",
            comparator: quotaErosionComparator,
            headerComponentParams: {
              serverSideSortable: true,
              sortOrder:
                orderbyFields.find(
                  (orderbyField) => orderbyField.column === "quota_erosion"
                )?.order ?? "",
              sortByField: "quota_erosion",
              sortCallback: sortCallback,
            },
            cellRenderer: (params) => {
              if (params && params.value) {
                if (
                  drawerData.planType === "earnedCommissions" &&
                  params.node.rowPinned === "bottom"
                ) {
                  return (
                    <EverTg.Heading4 className="text-ever-base-content">
                      {params.value}
                    </EverTg.Heading4>
                  );
                }
                const [value1, value2] = params.value.split(" ");
                const numericValue = value2 ? value2 : value1;
                const formattedValueForToolTip = formatCurrencyWrapper(
                  numericValue,
                  {
                    decimalPlaces: 6,
                    truncate: true,
                  }
                );
                const formattedValueForLabel =
                  formatCurrencyWrapper(numericValue);

                const titleWithSymbol = value2
                  ? `${value1}${formattedValueForToolTip}`
                  : formattedValueForToolTip;

                const labelWithSymbol = value2
                  ? `${value1}${formattedValueForLabel}`
                  : formattedValueForLabel;

                return (
                  <EverTooltip title={titleWithSymbol} placement="bottom">
                    {labelWithSymbol}
                  </EverTooltip>
                );
              } else {
                return "";
              }
            },
          }),
        });
      }
      if (showCommissionPayouts && !hasCommission) {
        colDefs.push({
          field: "commission",
          maxWidth: 160,
          headerName: "",
          resizable: false,
          suppressMenu: true,
          ...getCommissionColumnDefs(hasCommission),
        });
      }

      if (
        showCommissionPayouts &&
        payoutDetails.trace_enabled &&
        drawerData.planType === "earnedCommissions"
      ) {
        colDefs.push({
          pinned: "right",
          suppressMenu: true,
          field: "actions",
          headerName: "",
          suppressColumnsToolPanel: true,
          suppressFiltersToolPanel: true,
          width: 60,
          maxWidth: 60,
          minWidth: 60,
          suppressSizeToFit: true,
          cellRenderer: (params) => {
            if (
              params.node.rowPinned === "bottom" ||
              (payoutDetails.is_line_item_level === false &&
                params.node.rowIndex !== 0)
            ) {
              return "";
            }

            return (
              <div className="flex items-center justify-center w-full h-full">
                {payoutDetails.trace_enabled && (
                  <div
                    className="flex cursor-pointer"
                    onClick={() =>
                      setSelectedTrace({
                        rowKey: params.data.row_key,
                        ...(params.data.tierName && {
                          tierName: params.data.tierName,
                        }),
                      })
                    }
                  >
                    <CommissionTraceIcon
                      data-testid={`pt-${params?.data?.row_key}-trace-icon`}
                      className="w-5 h-5"
                    />
                  </div>
                )}
              </div>
            );
          },
        });
      }

      if (showCommissionPayouts && hasPermissions(RBAC_ROLES.CREATE_QUERIES)) {
        // We only display the raise query icon if the user has permission to view own/others' payouts and create queries
        colDefs.push({
          pinned: "right",
          suppressMenu: true,
          field: "actions",
          headerName: "",
          suppressColumnsToolPanel: true,
          suppressFiltersToolPanel: true,
          width: 60,
          maxWidth: 60,
          minWidth: 60,
          suppressSizeToFit: true,
          cellRenderer: (params) => {
            if (params.node.rowPinned === "bottom") {
              return "";
            }

            const handleRaiseQueryOnClick = () => {
              let selectedRow = {};
              const allColumns = params.api.getAllGridColumns();

              if (allColumns) {
                allColumns.forEach((col) => {
                  const colDef = col.getColDef();
                  const field = colDef.field;

                  if (
                    field &&
                    field !== "actions" &&
                    (data.selectedCriteriaColumns?.includes(field) ||
                      (field === "quota_erosion" &&
                        data.selectedCriteriaColumns?.includes("quotaErosion")))
                  ) {
                    let value = params.data[field];
                    if ((isString(value) && isEmpty(value)) || isNil(value)) {
                      // Handling null case explicitly as some values of the columns could be empty,
                      // and the valueFormatter will throws an error in that case
                      value = "";
                    } else if (field === "commission") {
                      value = getLocalizedCurrencyValue(
                        value,
                        currencySymbol,
                        localeId
                      );
                    } else if (field === "quota_erosion") {
                      value = formatCurrencyWrapper(value);
                    } else if (colDef.valueFormatter) {
                      value = colDef.valueFormatter({
                        value: value,
                        raiseQuery: true,
                      });
                    }

                    const headerName =
                      field === "tierName"
                        ? "Tier Name"
                        : colDef.headerName || field;
                    selectedRow[headerName] = value;
                  }
                });
              }
              setSelectedRowForQuery(selectedRow);
              setIsRaiseQueryModalVisible(true);
            };

            return (
              <div className="flex items-center justify-center w-full h-full">
                <EverTooltip title="Raise Query" placement="left">
                  <EverButton.Icon
                    onClick={handleRaiseQueryOnClick}
                    type="ghost"
                    color="base"
                    size="small"
                    className="flex-shrink-0 hover:!bg-ever-base-100"
                    icon={
                      <MessageNotificationSquareIcon className="text-ever-base-content-mid w-5 h-5" />
                    }
                  ></EverButton.Icon>
                </EverTooltip>
              </div>
            );
          },
        });
      }

      return colDefs;
    }, [
      rowData,
      variablesMap,
      systemNameToDisplayNameMap,
      dsDataTypesById,
      orderbyFields,
    ]);

    /* ********************** AG-GRID STATES AND METHODS ******************** */
    const gridRef = useRef();
    const parentDivRef = useRef();
    const [displayPagination, setDisplayPagination] = useState(false);

    const menuItemsCallback = useCallback((params) => {
      if (params.column.pinned === "left") {
        return [
          {
            name: "Pin to Right",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "right");
            },
          },
          {
            name: "Unpin",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, null);
            },
          },
        ];
      } else if (params.column.pinned === "right") {
        return [
          {
            name: "Pin to Left",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "left");
            },
          },
          {
            name: "Unpin",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, null);
            },
          },
        ];
      } else {
        return [
          {
            name: "Pin to Left",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "left");
            },
          },
          {
            name: "Pin to Right",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "right");
            },
          },
        ];
      }
    }, []);

    const defaultColDef = useMemo(() => {
      return {
        menuTabs: ["generalMenuTab"],
        flex: 1,
        minWidth: 100,
        resizable: true,
        suppressMenu: true,
      };
    }, []);

    const onColumnPinned = useCallback((event) => {
      const allCols = event.api.getAllGridColumns();
      const allFixedCols = allCols.filter(
        (col) => col.getColDef().lockPosition
      );
      const allNonFixedCols = allCols.filter(
        (col) => !col.getColDef().lockPosition
      );
      const pinnedCount = allNonFixedCols.filter(
        (col) => col.getPinned() === "left"
      ).length;
      const pinFixed = pinnedCount > 0;
      const columnStates = [];
      for (const col of allFixedCols) {
        if (pinFixed !== col.isPinned()) {
          columnStates.push({
            colId: col.getId(),
            pinned: pinFixed ? "left" : null,
          });
        }
      }
      if (columnStates.length > 0) {
        event.api.applyColumnState({ state: columnStates });
      }
    }, []);

    // if width of window is expanded
    useEffect(() => {
      const reAdjustColumns = () => {
        debounce(() => {
          adjustColumnWidth(gridRef.current);
        }, 500);
      };
      window.addEventListener("resize", reAdjustColumns);
      return () => {
        window.removeEventListener("resize", reAdjustColumns);
      };
    }, []);

    const pinnedBottomRowData = useMemo(() => {
      return [
        {
          rowNumbers: "Total",
          commission: drawerData.total,
          quota_erosion: drawerData.quotaErosionTotal ?? 0,
        },
      ];
    }, [drawerData.total, drawerData.quotaErosionTotal]);

    const getRowClass = (params) => {
      if (params.node.rowPinned === "bottom") {
        return "text-ever-base-50";
      }
      return null;
    };

    return (
      <div className="h-full w-full flex flex-col">
        {!isEmpty(criteriaDescription) && (
          <div className="w-2/3 ml-3 mb-3">
            <EverTg.Text>{criteriaDescription}</EverTg.Text>
          </div>
        )}
        <div
          ref={parentDivRef}
          className="ag-theme-material payoutDetailsTable h-full w-full"
        >
          <AgGridReact
            {...everAgGridOptions.getDefaultOptions({ type: "md" })}
            loading={loading}
            ref={gridRef}
            getRowClass={getRowClass}
            columnDefs={columnDefs}
            rowData={rowData}
            suppressCellFocus={true}
            suppressRowTransform={true}
            defaultColDef={defaultColDef}
            onFirstDataRendered={(params) => {
              setDisplayPagination(true);
              adjustColumnWidth(params);
            }}
            onGridReady={(params) => {
              params.api.closeToolPanel();
              adjustColumnWidth(params);
            }}
            getMainMenuItems={menuItemsCallback}
            onColumnPinned={onColumnPinned}
            onRowDataUpdated={(params) => {
              setTimeout(() => {
                params.api.autoSizeAllColumns();
                adjustColumnWidth(params);
              });
            }}
            suppressColumnMoveAnimation={true}
            suppressColumnVirtualisation={true}
            {...(payoutDetails.is_line_item_level === false && {
              // this is done due to row spanning of commission summation column
              suppressRowVirtualisation: true,
            })}
            getContextMenuItems={() => {
              return ["copy", "copyWithHeaders"];
            }}
            suppressFieldDotNotation={true}
            {...(showCommissionPayouts &&
              !isEmpty(rowData) && {
                pinnedBottomRowData,
              })}
            noRowsOverlayComponentParams={{
              title: "No records to display",
              subTitle: "Oops! There are currently no records to display.",
            }}
            paginationPageSize={pageSize}
            cacheBlockSize={pageSize}
          />
        </div>
        {/* {showCommissionPayouts && !isEmpty(rowData) && (
          <div
            className={`px-6 w-full h-14 flex justify-between items-center font-semibold border-x-0 border-y border-solid`}
          >
            <div>Total</div>
            <span
              className={
                Number(drawerData.total) > 0
                  ? "text-ever-success"
                  : Number(drawerData.total) < 0
                  ? "text-ever-error"
                  : ""
              }
            >
              {getCurrencyValue(currencySymbol, drawerData.total)}
            </span>
          </div>
        )} */}
        {displayPagination && (
          <Pagination
            rowPerPageOption={[20, 50, 100]}
            pageCount={Math.ceil(dataCount / pageSize)} //number of pages
            pageSize={pageSize} // rows per page
            totalRows={dataCount} // total rows
            setPageSize={setPageSize}
            currentPage={currentPage - 1} // current page number
            setCurrentPage={setCurrentPage}
            gridRef={gridRef}
          ></Pagination>
        )}
        <CommissionTrace
          selectedTrace={selectedTrace}
          periodLabel={periodLabel}
          isBaseCurrency={isBaseCurrency}
          onClose={() => setSelectedTrace({})}
          drawerData={drawerData}
          currencySymbol={currencySymbol}
          localeId={localeId}
          hyperlinkMap={JSON.parse(
            data ? data.hyperlinkMapForStatements : JSON.stringify({})
          )}
        />
        <AddQueryModal
          isVisible={isRaiseQueryModalVisible}
          setIsVisible={setIsRaiseQueryModalVisible}
          subject={`${periodLabel} - ${drawerData.planName}`}
          contextAttributes={{
            Section: querySection,
            Period: periodLabel,
            "Plan Name": drawerData.planName,
            "Criteria Name": drawerData.criteriaName,
            ...selectedRowForQuery,
          }}
        />
      </div>
    );
  }
);

export default PayoutDetailsTable;
