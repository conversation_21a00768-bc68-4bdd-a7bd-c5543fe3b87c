import { gql, useQuery } from "@apollo/client";
import { StatementColumnIcon } from "@everstage/evericons/duotone";
import { ClockIcon, WalletIcon, TargetIcon } from "@everstage/evericons/solid";
import { isEmpty, get } from "lodash";
import { observer } from "mobx-react";
import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { COMPONENTS } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import {
  EverDrawer,
  EverLabel,
  EverTabs,
  EverTg,
  message,
} from "~/v2/components";
import { updateAllCriteriaColumns } from "~/v2/features/commission-canvas-modified/services/http";

import CustomizeColumnComponent from "./CustomizeColumnComponent";
import PayoutDetailsHeader from "./PayoutDetailsHeader";
import PayoutDetailsTable from "./PayoutDetailsTable";

const GET_CRITERIA_DETAILS = gql`
  query CommissionPlan(
    $planId: UUID!
    $component: String
    $expressionVersion: String
  ) {
    commissionPlanCriteriaBasicDetails(
      planId: $planId
      component: $component
      expressionVersion: $expressionVersion
    ) {
      planCriterias {
        criteriaId
        criteriaName
        criteriaType
        criteriaData
        criteriaColumn
      }
    }
  }
`;

const GET_COMMISSION_PLAN_BASIC_DETAILS = gql`
  query CommissionPlanBasicDetails($planId: UUID!, $component: String) {
    commissionPlanBasicDetails(planId: $planId, component: $component) {
      planScope {
        canEdit
      }
    }
  }
`;

const InfoCard = ({ label, value, icon }) => {
  return (
    <div className="flex py-3 px-4 gap-2 bg-ever-base-100 rounded-md">
      {icon && <div>{icon}</div>}
      <div className="flex flex-col gap-y-0.5">
        {label && <EverLabel>{label}</EverLabel>}
        {value && (
          <EverTg.Heading4 className="text-ever-base-content">
            {value}
          </EverTg.Heading4>
        )}
      </div>
    </div>
  );
};

const PayoutTabs = observer((props) => {
  const {
    drawerData,
    currencySymbol,
    localeId,
    selectedOverview,
    updateDrawerData,
    periodLabel,
  } = props;

  const { accessToken } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const expressionBoxVersion = clientFeatures.expressionboxVersion || "v1";
  const selectedPlan = selectedOverview?.[drawerData.planId] || {};
  const criteriaDetails = selectedPlan?.criteriaDetails || [];
  const planLevelCommission = selectedPlan?.amount || 0;
  const [currentTime, setCurrentTime] = useState(new Date().getTime());
  const [
    isCustomizeStatementColumnsVisible,
    setIsCustomizeStatementColumnsVisible,
  ] = useState(false);

  const { t } = useTranslation();

  const { data, refetch } = useQuery(GET_CRITERIA_DETAILS, {
    variables: {
      planId: drawerData.planId,
      component: COMPONENTS.PAYOUTS_STATEMENTS,
      expressionVersion: expressionBoxVersion,
    },
    fetchPolicy: "no-cache",
    skip: !drawerData.planId,
    notifyOnNetworkStatusChange: true,
  });

  const { data: commissionPlanBasicDetails } = useQuery(
    GET_COMMISSION_PLAN_BASIC_DETAILS,
    {
      variables: {
        planId: drawerData.planId,
        component: COMPONENTS.PAYOUTS_STATEMENTS,
      },
      fetchPolicy: "no-cache",
      skip: !drawerData.planId,
      notifyOnNetworkStatusChange: true,
    }
  );

  const totalCommissionAmount = (
    <span
      className={
        Number(planLevelCommission) > 0
          ? "text-ever-success-hover"
          : Number(planLevelCommission) < 0
          ? "text-ever-error"
          : ""
      }
    >
      {getLocalizedCurrencyValue(planLevelCommission, currencySymbol, localeId)}
    </span>
  );

  const totalQuotaErosionAmount = <span>{drawerData.quotaErosionTotal}</span>;

  const handleCustomizeColumnToggle = () => {
    setIsCustomizeStatementColumnsVisible(!isCustomizeStatementColumnsVisible);
  };

  const handleSaveStatementColumns = (criteriaList) => {
    return updateAllCriteriaColumns(accessToken, {
      planId: drawerData.planId,
      criteriaList: criteriaList,
    })
      .then(() => {
        setIsCustomizeStatementColumnsVisible(false);
        message.success(`Statement columns updated successfully`);
        setCurrentTime(new Date().getTime());
        refetch();
      })
      .catch((msg) => {
        message.error(msg);
        throw Error(msg);
      });
  };

  const onCriteriaChange = (key) => {
    const criteriaDetail = criteriaDetails.find(
      (criteria) => criteria.criteriaId === key
    );
    if (!isEmpty(criteriaDetail)) {
      updateDrawerData({
        planId: drawerData.planId,
        criteriaId: criteriaDetail.criteriaId,
        commissionType:
          drawerData.planType === "earnedCommissions"
            ? "EARNED_COMMISSIONS"
            : drawerData.planType === "previousDeferredCommissions"
            ? "PREV_DEFERRED_COMMISSIONS"
            : drawerData.planType === "deferredCommissions"
            ? "DEFERRED_COMMISSIONS"
            : "CURRENT_PERIOD_PAYOUT",
        planName: drawerData.planName,
        planType: drawerData.planType,
        criteriaName: criteriaDetail.criteriaName,
        datasheetId: criteriaDetail.datasheetId,
        total: criteriaDetail.amount,
        quotaErosionTotal: criteriaDetail.quotaErosion ?? 0,
        period: drawerData.period,
        commPeriod: drawerData.commPeriod,
      });
    }
  };

  const items = criteriaDetails.map((criteriaDetail) => ({
    label: criteriaDetail.criteriaName,
    key: criteriaDetail.criteriaId,
    children: (
      <div className="pt-3 w-full h-full">
        <PayoutDetailsTable
          {...props}
          criteriaDescription={criteriaDetail.criteriaDescription ?? null}
        />
      </div>
    ),
  }));

  const criteriaList = useMemo(() => {
    if (
      data &&
      data.commissionPlanCriteriaBasicDetails &&
      data.commissionPlanCriteriaBasicDetails[0].planCriterias
    ) {
      const criteriaItems =
        data.commissionPlanCriteriaBasicDetails[0].planCriterias.map((x) => ({
          id: x.criteriaId,
          name: x.criteriaName,
          criteriaType: x.criteriaType,
          data: JSON.parse(x.criteriaData),
          criteriaColumns: x.criteriaColumn,
        }));
      return criteriaItems.filter((criteria) => {
        return items.some((item) => {
          return item.key === criteria.id;
        });
      });
    }
    return [];
  }, [data]);

  const selectedCriteriaData = useMemo(() => {
    return criteriaList.find(
      (criteria) => criteria.id === drawerData.criteriaId
    );
  }, [criteriaList, drawerData.criteriaId]);

  const hasEditPermission = useMemo(() => {
    return get(
      commissionPlanBasicDetails,
      "commissionPlanBasicDetails[0].planScope.canEdit",
      false
    );
  }, [commissionPlanBasicDetails]);

  // isDraft parameter is false because only published plans are listed under the payouts.
  // planScope -> canEdit is fetched based on the plan RBAC
  const commissionPlanDetails = {
    planId: drawerData.planId,
    isDraft: false,
    planScope: {
      canEdit: hasEditPermission,
    },
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex gap-x-4 mb-6">
        <InfoCard
          icon={<ClockIcon className="w-6 h-6 text-ever-base-content-mid" />}
          label="Period"
          value={periodLabel}
        />
        <InfoCard
          icon={<WalletIcon className="w-6 h-6 text-ever-base-content-mid" />}
          label={t("PLAN_LEVEL_COMM")}
          value={totalCommissionAmount}
        />
        {["Quota", "CustomQuota"].includes(
          selectedCriteriaData?.criteriaType
        ) &&
          drawerData.planType === "earnedCommissions" && (
            <InfoCard
              icon={
                <TargetIcon className="w-6 h-6 text-ever-base-content-mid" />
              }
              label={
                t("QUOTA_EROSION") + " (" + selectedCriteriaData?.name + ")"
              }
              value={totalQuotaErosionAmount}
            />
          )}

        {hasEditPermission && (
          <div className="ml-auto flex items-center gap-2">
            <div
              className="flex items-center justify-center p-2 border border-solid border-ever-base-400 rounded-lg bg-ever-base-25  hover:bg-ever-base-50 active:bg-transparent focus:bg-transparent cursor-pointer"
              onClick={() => handleCustomizeColumnToggle()}
            >
              <StatementColumnIcon className="w-4 h-4 text-ever-chartColors-26 mr-2" />
              <EverTg.SubHeading4 className="text-ever-base-content">
                Customize component columns
              </EverTg.SubHeading4>
            </div>
          </div>
        )}
        <CustomizeColumnComponent
          visible={isCustomizeStatementColumnsVisible}
          criteriaList={criteriaList}
          commissionPlanDetails={commissionPlanDetails}
          handleClose={() => setIsCustomizeStatementColumnsVisible(false)}
          handleApply={handleSaveStatementColumns}
          defaultCriteriaId={drawerData.criteriaId}
        />
      </div>
      <EverTabs
        key={currentTime}
        activeKey={drawerData.criteriaId}
        onChange={onCriteriaChange}
        items={items}
      />
    </div>
  );
});

const PayoutDetails = observer(
  ({
    handleClose,
    currencySymbol,
    localeId,
    periodLabel,
    drawerData,
    updateDrawerData,
    selectedOverview,
    showSettlementView,
    showCommissionPayouts,
    isBaseCurrency,
  }) => {
    return (
      <EverDrawer
        destroyOnClose={true}
        height="95%"
        title={
          <PayoutDetailsHeader
            drawerData={drawerData}
            selectedOverview={selectedOverview}
            showSettlementView={showSettlementView}
            updateDrawerData={updateDrawerData}
          />
        }
        placement="top"
        onClose={handleClose}
        visible={!isEmpty(drawerData)}
      >
        <PayoutTabs
          currencySymbol={currencySymbol}
          localeId={localeId}
          drawerData={drawerData}
          updateDrawerData={updateDrawerData}
          periodLabel={periodLabel}
          selectedOverview={selectedOverview}
          showCommissionPayouts={showCommissionPayouts}
          isBaseCurrency={isBaseCurrency}
          showSettlementView={showSettlementView}
        />
      </EverDrawer>
    );
  }
);

export default PayoutDetails;
