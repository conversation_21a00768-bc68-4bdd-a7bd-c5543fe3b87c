import { Descriptions } from "antd";
import { isEmpty } from "lodash";
import React from "react";

import {
  EverLoader,
  EverGroupAvatar,
  EverDrawer,
  EverTg,
} from "~/v2/components";

import RenderCustomView from "./RenderCustomView";
import RenderDefaultView from "./RenderDefaultView";

const Render = (props) => {
  const {
    selectedEmployeeData,
    onClose,
    visible,
    loading,
    clientProfileFields,
    employeePayrollDetails,
    employeeReportingDetails,
    options,
    customView,
    selectedPayee,
    dateRange,
  } = props;

  return (
    <>
      <EverDrawer
        title={selectedPayee ? "Profile" : "Profile Preview"}
        placement="right"
        onClose={onClose}
        visible={visible}
        width={650}
      >
        {loading ? (
          <div className="w-full h-full">
            <EverLoader indicatorType="spinner" tip="" />
          </div>
        ) : (
          <div className="pb-6">
            <div className="flex items-center gap-x-3 mb-5">
              <EverGroupAvatar
                avatars={[
                  {
                    firstName: selectedEmployeeData
                      ? selectedEmployeeData.firstName &&
                        `${selectedEmployeeData.firstName}`
                      : "Profile",
                    lastName: selectedEmployeeData
                      ? selectedEmployeeData.lastName &&
                        `${selectedEmployeeData.lastName}`
                      : "Name",
                    image: selectedEmployeeData
                      ? selectedEmployeeData.profilePicture &&
                        selectedEmployeeData.profilePicture
                      : "",
                    className: "w-9 h-9",
                  },
                ]}
                groupMaxCount={1}
                limitInPopover={1}
              />
              <div className="flex flex-col gap-0.5">
                <EverTg.Heading3 className="text-ever-base-content">
                  {selectedEmployeeData
                    ? selectedEmployeeData.firstName &&
                      `${selectedEmployeeData.firstName} ${selectedEmployeeData.lastName}`
                    : "Profile Name"}
                </EverTg.Heading3>
                <EverTg.Caption className="text-ever-base-content-mid">
                  {selectedEmployeeData
                    ? selectedEmployeeData.designation &&
                      selectedEmployeeData.designation
                    : "Role"}
                </EverTg.Caption>
              </div>
            </div>
            {!isEmpty(clientProfileFields) && (
              <div className="max-h-[18rem] overflow-y-auto">
                <Descriptions
                  column={1}
                  colon={false}
                  labelStyle={{ width: 240 }}
                  className="p-6 rounded bg-ever-chartColors-8"
                >
                  {clientProfileFields.map((item, index) => (
                    <Descriptions.Item
                      key={item.name}
                      label={
                        <EverTg.Text className="text-ever-base-content">
                          {item.name}
                        </EverTg.Text>
                      }
                      className={
                        clientProfileFields.length - 1 === index ? "!p-0" : ""
                      }
                    >
                      <EverTg.Text className="font-semibold text-ever-base-content">
                        {item.value}
                      </EverTg.Text>
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </div>
            )}
            {customView ? (
              <RenderCustomView
                selectedPayee={selectedPayee}
                dateRange={dateRange}
                options={options}
              />
            ) : (
              <RenderDefaultView
                employeePayrollDetails={employeePayrollDetails}
                employeeReportingDetails={employeeReportingDetails}
                selectedPayee={selectedPayee}
                dateRange={dateRange}
              />
            )}
          </div>
        )}
      </EverDrawer>
    </>
  );
};

export default Render;
