import { useQuery, gql } from "@apollo/client";
import { DownloadIcon, FileIcon } from "@everstage/evericons/outlined";
import { isEmpty, cloneDeep } from "lodash";
import { useLocalStore, observer } from "mobx-react";
import React, { useEffect, useMemo, useState } from "react";

import { downloadPayeeContractDoc } from "~/Api/CommissionPlanService";
import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverTg, EverTooltip, message, EverLoader } from "~/v2/components";

import { DocumentViewerWrapper } from "./RenderDocumentDrawer";
import RenderTable from "./RenderTable";
import PayeeProfileStore from "../store";

const GET_TEAM_DETAILS = gql`
  query ($employeeEmailId: String!, $effectiveDate: String!) {
    allTeamsOfMemberPayoutsModule(
      teamType: ["team"]
      memberEmailId: $employeeEmailId
      effectiveDate: $effectiveDate
    ) {
      teamName
      teamType
    }
  }
`;

const GET_POD_DETAILS = gql`
  query ($employeeEmailId: String!, $effectiveDate: String!) {
    allPodMembers(
      podOwnerEmailId: $employeeEmailId
      effectiveDate: $effectiveDate
    ) {
      employee {
        firstName
        lastName
      }
    }
  }
`;

const GET_PAYEE_DOCUMENTS = gql`
  query EmployeeDocuments($emailId: String!, $effectiveDate: String!) {
    documentsForEmployee(emailId: $emailId, effectiveDate: $effectiveDate) {
      fileName
      s3Url
      fiscalYear
      employeeEmailId
      envelopeId
    }
  }
`;

const RenderCustomView = observer((props) => {
  const { accessToken } = useAuthStore();
  const { options, selectedPayee, dateRange } = props;
  const [teamData, setTeamData] = useState({});
  const [podData, setPodData] = useState({});
  const [documentsData, setDocumentsData] = useState({});

  const { hasPermissions } = useUserPermissionStore();

  const payeeProfileStore = useLocalStore(() => new PayeeProfileStore());
  const { setDownloadDocumentLoading } = payeeProfileStore;

  const { data: teamDetails } = useQuery(GET_TEAM_DETAILS, {
    variables: {
      employeeEmailId: selectedPayee,
      effectiveDate: dateRange?.format("YYYY-MM-DD"),
    },
    fetchPolicy: "no-cache",
    skip:
      selectedPayee === undefined || dateRange === undefined || !options?.teams,
  });
  const { data: podDetails } = useQuery(GET_POD_DETAILS, {
    variables: {
      employeeEmailId: selectedPayee,
      effectiveDate: dateRange?.format("YYYY-MM-DD"),
    },
    fetchPolicy: "no-cache",
    skip:
      selectedPayee === undefined ||
      dateRange === undefined ||
      !options?.pod_details,
  });

  const { data: employeeDocuments } = useQuery(GET_PAYEE_DOCUMENTS, {
    variables: {
      emailId: selectedPayee,
      effectiveDate: dateRange?.format("YYYY-MM-DD"),
    },
    fetchPolicy: "no-cache",
    skip: selectedPayee === undefined || dateRange === undefined,
  });

  const onDownloadPayeeDoc = ({ envelopeId, fileName, employeeEmailId }) => {
    setDownloadDocumentLoading(true);
    message.loading({
      content: `Downloading ${fileName}`,
      key: "loading",
      duration: 0,
      icon: <EverLoader.SpinnerLottie />,
    });
    downloadPayeeContractDoc(
      {
        employeeEmailId: employeeEmailId,
        envelopeId: envelopeId,
        fileName: fileName,
      },
      accessToken
    )
      .then((response) => response.blob())
      .then((blobby) => {
        let objectUrl = window.URL.createObjectURL(blobby);
        let anchor = document.createElement("a");
        anchor.href = objectUrl;
        anchor.download = fileName;
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
      })
      .finally(() => {
        message.destroy("loading");
        setDownloadDocumentLoading(false);
      });
  };

  useEffect(() => {
    if (teamDetails) {
      let tempTeamData = {
        columns: [
          {
            headerName: "Name",
            field: "name",
          },
        ],
        rows: [],
      };
      if (!isEmpty(teamDetails.allTeamsOfMemberPayoutsModule)) {
        for (const item of teamDetails.allTeamsOfMemberPayoutsModule) {
          tempTeamData["rows"].push({
            name: item.teamName,
          });
        }
      }
      setTeamData(cloneDeep(tempTeamData));
    }
  }, [teamDetails]);

  useEffect(() => {
    if (podDetails) {
      let tempPodData = {
        columns: [
          {
            headerName: "Name",
            field: "name",
          },
        ],
        rows: [],
      };
      if (!isEmpty(podDetails.allPodMembers)) {
        for (const item of podDetails.allPodMembers) {
          tempPodData["rows"].push({
            name: item.employee.firstName + " " + item.employee.lastName,
          });
        }
      }
      setPodData(cloneDeep(tempPodData));
    }
  }, [podDetails]);

  const payeeDocumentColumns = useMemo(() => {
    return {
      columns: [
        {
          headerName: "File Name",
          field: "fileName",
          cellRenderer: (record) => {
            return (
              <div className="flex flex-nowrap gap-2 items-center">
                <div className="flex bg-ever-chartColors-2 rounded-lg p-1.5">
                  <FileIcon
                    className="w-5 h-5 text-ever-chartColors-12"
                    key="view"
                  />
                </div>
                <EverTg.Text>{record.value}</EverTg.Text>
              </div>
            );
          },
        },
        {
          headerName: "",
          field: "actions",
          width: 80,
          suppressMenu: true,
          pinned: "right",
          cellRenderer: (params) => {
            return (
              <div className="flex flex-nowrap gap-4 items-center">
                <EverTooltip title="Fileee"></EverTooltip>
                <EverTooltip title="View Document">
                  <DocumentViewerWrapper
                    store={payeeProfileStore}
                    envelopeId={params.data.envelopeId}
                    emailId={selectedPayee}
                    documentName={params.data.fileName}
                    documentPath={params.data.filePath}
                    onDownloadPayeeDoc={onDownloadPayeeDoc}
                  />
                </EverTooltip>
                <EverTooltip title="Download">
                  <DownloadIcon
                    className="w-5 h-5 text-ever-base-content-mid cursor-pointer"
                    key="downlaod"
                    onClick={() =>
                      onDownloadPayeeDoc({
                        employeeEmailId: selectedPayee,
                        fileName: params.data.fileName,
                        envelopeId: params.data.envelopeId,
                      })
                    }
                  />
                </EverTooltip>
              </div>
            );
          },
        },
      ],
      rows: [],
    };
  }, [selectedPayee]);

  useEffect(() => {
    if (employeeDocuments) {
      let employeeDocumentsData = cloneDeep(payeeDocumentColumns);
      if (!isEmpty(employeeDocuments.documentsForEmployee)) {
        for (const item of employeeDocuments.documentsForEmployee) {
          employeeDocumentsData["rows"].push({
            fileName: item.fileName,
            filePath: item.s3Url,
            envelopeId: item.envelopeId,
          });
        }
      }
      setDocumentsData(cloneDeep(employeeDocumentsData));
    }
  }, [employeeDocuments, payeeDocumentColumns]);

  return (
    <>
      {employeeDocuments &&
      employeeDocuments.documentsForEmployee.length != 0 ? (
        <RenderTable
          key="documents"
          dataSource={isEmpty(documentsData) ? [] : documentsData["rows"]}
          columns={isEmpty(documentsData) ? [] : documentsData["columns"]}
          header="Documents"
          topMargin={true}
          hasPermission={hasPermissions([
            RBAC_ROLES.VIEW_PAYOUTS,
            RBAC_ROLES.VIEW_STATEMENTS,
          ])}
        />
      ) : null}
      {options?.teams &&
        (selectedPayee === undefined || dateRange === undefined ? (
          <div key="reportteams" className="mt-6">
            <RenderTable
              key="preview"
              dataSource={[{ name: "Name goes here.." }]}
              columns={[
                {
                  headerName: "Name",
                  field: "name",
                  cellRenderer: (props) => {
                    return (
                      <span className="text-ever-base-content-low">
                        {props.value}
                      </span>
                    );
                  },
                },
              ]}
              header="Teams"
              topMargin={true}
              hasPermission={true}
            />
          </div>
        ) : (
          <RenderTable
            key="teams"
            dataSource={isEmpty(teamData) ? [] : teamData["rows"]}
            columns={isEmpty(teamData) ? [] : teamData["columns"]}
            header="Teams"
            topMargin={true}
            hasPermission={hasPermissions(RBAC_ROLES.VIEW_TEAMS)}
          />
        ))}
      {options?.pod_details &&
        (selectedPayee === undefined || dateRange === undefined ? (
          <div key="reportpods" className="mt-6">
            <RenderTable
              key="preview"
              dataSource={[{ name: "Name goes here.." }]}
              columns={[
                {
                  headerName: "Name",
                  field: "name",
                  cellRenderer: (props) => {
                    return (
                      <span className="text-ever-base-content-low">
                        {props.value}
                      </span>
                    );
                  },
                },
              ]}
              header="Pods"
              topMargin={true}
              hasPermission={true}
            />
          </div>
        ) : (
          <RenderTable
            key="pods"
            dataSource={isEmpty(podData) ? [] : podData["rows"]}
            columns={isEmpty(podData) ? [] : podData["columns"]}
            header="Pods"
            topMargin={true}
            hasPermission={hasPermissions(RBAC_ROLES.VIEW_TEAMS)}
          />
        ))}
    </>
  );
});

export default RenderCustomView;
