import { useQuery, gql } from "@apollo/client";
import { AgGridReact } from "ag-grid-react";
import { Descriptions } from "antd";
import { isEmpty, cloneDeep, groupBy } from "lodash";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { DRAW_TYPE_NAMES, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { getFiscalYearForDate } from "~/Utils/DateUtils";
import {
  getColumns,
  getFiscalStartDateAndEndDate,
  getLimitsEffectiveQuota,
} from "~/Utils/QuotaUtils";
import { EverTabs, useCurrentTheme } from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";

import RenderTable from "./RenderTable";

const GET_TEAMS_AND_PODS = gql`
  query ($employeeEmailId: String!, $effectiveDate: String!) {
    allTeamsOfMemberPayoutsModule(
      teamType: ["team"]
      memberEmailId: $employeeEmailId
      effectiveDate: $effectiveDate
    ) {
      teamName
      teamType
    }
    allPodMembers(
      podOwnerEmailId: $employeeEmailId
      effectiveDate: $effectiveDate
    ) {
      employee {
        firstName
        lastName
      }
    }
  }
`;
// modify
const GET_EMPLOYEE_QUOTAS = gql`
  query ($employeeEmailId: String!, $year: Int!) {
    employeeQuota(emailId: $employeeEmailId, year: $year) {
      quotaCategoryName
      displayName
      effectiveStartDate
      effectiveEndDate
      quotaScheduleType
      quotaYear
      isTeamQuota
      quotaCurrencySymbol
      scheduleQuota {
        ramp
        quota
        rampedQuota
      }
    }
  }
`;

const GET_EMPLOYEE_DRAWS = gql`
  query ($employeeEmailId: String!, $year: Int!) {
    employeeDraws(employeeEmailId: $employeeEmailId, year: $year) {
      drawYear
      draws {
        drawPeriod
        drawAmount
        drawTypeName
      }
    }
  }
`;

const RenderDefaultView = (props) => {
  const {
    selectedPayee,
    employeePayrollDetails,
    employeeReportingDetails,
    dateRange,
  } = props;

  const startMonth = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const [drawData, setDrawData] = useState([]);
  const [quotaData, setQuotaData] = useState([]);
  const [teamData, setTeamData] = useState({});
  const [podData, setPodData] = useState({});

  const { hasPermissions } = useUserPermissionStore();

  const { base } = useCurrentTheme();

  const { t } = useTranslation();

  const { data: teamsAndPodsData, loading: teamsAndPodsLoading } = useQuery(
    GET_TEAMS_AND_PODS,
    {
      variables: {
        employeeEmailId: selectedPayee,
        effectiveDate: dateRange?.format("YYYY-MM-DD"),
      },
      fetchPolicy: "no-cache",
      skip: selectedPayee === undefined || dateRange === undefined,
    }
  );

  const { data: employeeQuotaData, loading: employeeQuotaLoading } = useQuery(
    GET_EMPLOYEE_QUOTAS,
    {
      variables: {
        employeeEmailId: selectedPayee,
        year: getFiscalYearForDate(startMonth + 1, dateRange),
      },
      fetchPolicy: "no-cache",
      skip: selectedPayee === undefined || dateRange === undefined,
    }
  );

  const { data: employeeDrawsData, loading: employeeDrawsLoading } = useQuery(
    GET_EMPLOYEE_DRAWS,
    {
      variables: {
        employeeEmailId: selectedPayee,
        year: getFiscalYearForDate(startMonth + 1, dateRange),
      },
      fetchPolicy: "no-cache",
      skip: selectedPayee === undefined || dateRange === undefined,
    }
  );

  const fiscalYear = getFiscalYearForDate(startMonth + 1, dateRange);
  const { startDate: fiscalStartDate, endDate: fiscalEndDate } =
    getFiscalStartDateAndEndDate(startMonth, fiscalYear);

  useEffect(() => {
    if (isEmpty(teamsAndPodsData)) {
      setTeamData({});
      setPodData({});
      return;
    }

    const tempTeamData = {
      columns: [
        {
          headerName: "Team Name",
          field: "name",
        },
      ],
      rows: [],
    };
    for (const item of teamsAndPodsData.allTeamsOfMemberPayoutsModule ?? []) {
      tempTeamData["rows"].push({
        name: item.teamName,
      });
    }
    const tempPodData = {
      columns: [
        {
          headerName: "Name",
          field: "name",
        },
      ],
      rows: [],
    };
    for (const item of teamsAndPodsData.allPodMembers ?? []) {
      tempPodData["rows"].push({
        name: item.employee.firstName + " " + item.employee.lastName,
      });
    }

    setTeamData(tempTeamData);
    setPodData(tempPodData);
  }, [teamsAndPodsData]);

  useEffect(() => {
    if (isEmpty(employeeQuotaData?.employeeQuota ?? [])) {
      setQuotaData([]);
      return;
    }

    const employeeQuota = employeeQuotaData.employeeQuota;

    const teamQuotaCols = employeeQuota.filter(
      (item) => item.isTeamQuota === true
    );
    const individualQuotaCols = employeeQuota.filter(
      (item) => item.isTeamQuota === false
    );

    const setEmployeeQuotaCols = (employeeQuota, isTeam) => {
      // Group quota values based on quota category as there might be several
      // entries for a single quota if the quota is effective dated. The logic
      // assumes the start dates are in increasing order.
      const groupedByQuota = groupBy(employeeQuota, "quotaCategoryName");

      for (const quotaData of Object.values(groupedByQuota)) {
        for (const [idx, item] of quotaData.entries()) {
          const esd = moment.utc(item.effectiveStartDate);
          let eed = item.effectiveEndDate;
          eed = eed ? moment.utc(eed) : fiscalEndDate;

          const nextEsd = quotaData?.[idx + 1]?.effectiveStartDate
            ? moment.utc(quotaData[idx + 1].effectiveStartDate)
            : null;

          // periods is to just get the column names
          const periods = getColumns(item.scheduleQuota, startMonth);
          const { start, end } = getLimitsEffectiveQuota(
            fiscalStartDate,
            item.quotaScheduleType,
            esd,
            nextEsd
          );
          // When nextEsd is null, the end would be Infinity which in turn would
          // take the whole array from start.
          const scheduleQuotaDetailsMapped = item.scheduleQuota.map(
            (eachItem, index) => {
              return {
                month: periods[index]["title"],
                currencySymbol: item.quotaCurrencySymbol,
                ...cloneDeep(eachItem),
              };
            }
          );

          // If the quota is annual then no need to splice the array
          const scheduleQuotaDetails =
            item.quotaScheduleType === "Annual" && quotaData?.length > 1
              ? scheduleQuotaDetailsMapped
              : scheduleQuotaDetailsMapped.slice(start, end);

          const quotaTitle = `${item.displayName} ${
            isTeam ? " (As Manager)" : " (As Individual)"
          }  `;

          const quotaPeriodTitle = `${esd.format("MMM YYYY")} - ${eed.format(
            "MMM YYYY"
          )}`;

          rowData.push({
            quotas: quotaTitle,
            quota_period: quotaPeriodTitle,
            scheduleQuota: [...cloneDeep(scheduleQuotaDetails)],
          });
        }
      }
    };

    const rowData = [];
    setEmployeeQuotaCols(teamQuotaCols, true);
    setEmployeeQuotaCols(individualQuotaCols, false);
    setQuotaData(rowData);
  }, [employeeQuotaData]);

  useEffect(() => {
    if (isEmpty(employeeDrawsData?.employeeDraws ?? [])) {
      setDrawData([]);
      return;
    }

    let tableData = [];
    for (const ed of employeeDrawsData.employeeDraws) {
      let year = ed.drawYear;
      for (const draw of ed.draws.filter((x) => !isEmpty(x))) {
        for (const drawData of draw) {
          tableData.push({
            drawYear: year,
            drawPeriod: drawData.drawPeriod,
            drawTypeName: DRAW_TYPE_NAMES[drawData.drawTypeName],
            drawAmount: drawData.drawAmount,
          });
        }
      }
    }

    setDrawData(tableData);
  }, [employeeDrawsData]);

  const renderTabContents = (tab) => {
    switch (tab) {
      case "Payroll Details": {
        return (
          <Descriptions
            bordered
            colon={false}
            labelStyle={{ width: "240px" }}
            size="small"
          >
            {Object.keys(employeePayrollDetails).map((item, index) => (
              <Descriptions.Item
                contentStyle={{
                  backgroundColor: index % 2 === 0 ? base[100] : base.DEFAULT,
                }}
                labelStyle={{
                  backgroundColor: index % 2 === 0 ? base[100] : base.DEFAULT,
                }}
                label={<span className="text-ever-base-content">{item}</span>}
                key={item}
                span={3}
              >
                <span className="font-medium text-ever-base-content">
                  {employeePayrollDetails[item]}
                </span>
              </Descriptions.Item>
            ))}
          </Descriptions>
        );
      }
      case "Draws": {
        const drawsColumns = [
          {
            headerName: "Fiscal Year",
            field: "drawYear",
          },
          {
            headerName: "Period",
            field: "drawPeriod",
          },
          {
            headerName: "Draw Type",
            field: "drawTypeName",
          },
          {
            headerName: "Draw Amount",
            field: "drawAmount",
          },
        ];
        return hasPermissions(RBAC_ROLES.VIEW_DRAWS) ? (
          <div className="ag-theme-material w-full">
            <AgGridReact
              {...everAgGridOptions.getDefaultOptions({ type: "md" })}
              domLayout="autoHeight"
              columnDefs={drawsColumns}
              rowData={drawData}
            />
          </div>
        ) : (
          <div className="w-full mt-4">
            <span>
              You don&apos;t have permissions to view the data. Please reach out
              to your administrator.
            </span>
          </div>
        );
      }
      case "Reporting and Teams": {
        return (
          <>
            <RenderTable
              dataSource={
                isEmpty(employeeReportingDetails)
                  ? []
                  : employeeReportingDetails["Reporting Details"]["rows"]
              }
              columns={
                isEmpty(employeeReportingDetails)
                  ? []
                  : employeeReportingDetails["Reporting Details"]["columns"]
              }
              header="Reporting Details"
            />
            <RenderTable
              dataSource={isEmpty(teamData) ? [] : teamData["rows"]}
              columns={isEmpty(teamData) ? [] : teamData["columns"]}
              header="Teams"
              hasPermission={hasPermissions(RBAC_ROLES.VIEW_TEAMS)}
            />
            <RenderTable
              dataSource={isEmpty(podData) ? [] : podData["rows"]}
              columns={isEmpty(podData) ? [] : podData["columns"]}
              header="Pods"
              hasPermission={hasPermissions(RBAC_ROLES.VIEW_TEAMS)}
            />
          </>
        );
      }
      case "Quota": {
        const columnDefs = [
          {
            field: "quotas",
            headerName: t("QUOTAS"),
            cellRenderer: "agGroupCellRenderer",
          },
          { field: "quota_period", headerName: `${t("QUOTA")} periods` },
        ];
        const detailCellRendererParams = {
          // provide the Grid Options to use on the Detail Grid
          detailGridOptions: {
            columnDefs: [
              { field: "month", headerName: "" },
              {
                field: "quota",
                headerName: t("QUOTA"),
                cellRenderer: (props) => {
                  return getLocalizedCurrencyValue(
                    props.value,
                    props.data.currencySymbol
                  );
                },
              },
              {
                field: "ramp",
                headerName: "Ramp %",
                cellRenderer: (props) => {
                  return `${props.value} %`;
                },
              },
              {
                field: "rampedQuota",
                headerName: t("RAMP_ADJUSTED_QUOTA"),
                cellRenderer: (props) => {
                  return getLocalizedCurrencyValue(
                    props.value,
                    props.data.currencySymbol
                  );
                },
              },
            ],
            onGridReady: (params) => {
              params.api.autoSizeAllColumns();
            },
          },
          // get the rows for each Detail Grid
          getDetailRowData: (params) => {
            params.successCallback(params.data.scheduleQuota);
          },
        };
        return hasPermissions(RBAC_ROLES.VIEW_QUOTAS) ? (
          <div className="ag-theme-material w-full">
            <AgGridReact
              {...everAgGridOptions.getDefaultOptions({ type: "md" })}
              domLayout="autoHeight"
              columnDefs={columnDefs}
              rowData={quotaData}
              masterDetail={true}
              detailCellRendererParams={detailCellRendererParams}
            />
          </div>
        ) : (
          <div className="w-full mt-4">
            <span>
              You don&apos;t have permissions to view the data. Please reach out
              to your administrator.
            </span>
          </div>
        );
      }
      // No default
    }
    return null;
  };

  const loading =
    teamsAndPodsLoading || employeeQuotaLoading || employeeDrawsLoading;

  return (
    <>
      {!loading && (
        <EverTabs className="mt-6">
          <EverTabs.TabPane tab="Payroll Details" key="1">
            <div className="p-4">{renderTabContents("Payroll Details")}</div>
          </EverTabs.TabPane>
          <EverTabs.TabPane tab="Reporting and Teams" key="2">
            <div className="p-4">
              {renderTabContents("Reporting and Teams")}
            </div>
          </EverTabs.TabPane>
          <EverTabs.TabPane tab={t("QUOTA")} key="3">
            <div className="p-4">{renderTabContents("Quota")}</div>
          </EverTabs.TabPane>
          <EverTabs.TabPane tab="Draws" key="4">
            <div className="p-4">{renderTabContents("Draws")}</div>
          </EverTabs.TabPane>
        </EverTabs>
      )}
    </>
  );
};

export default RenderDefaultView;
