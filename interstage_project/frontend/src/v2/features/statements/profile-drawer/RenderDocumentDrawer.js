import { DownloadIcon, EyeIcon } from "@everstage/evericons/outlined";
import { Col, Row } from "antd";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { DOCUMENT_VIEW_SOURCE } from "~/Enums";
import { EverButton, EverDrawer, EverTooltip } from "~/v2/components";
import { PlanViewer } from "~/v2/features/incentive-plan-designer/plan-viewer";

export const DocumentViewerWrapper = observer((props) => {
  const {
    documentName,
    emailId,
    store,
    documentPath,
    envelopeId,
    onDownloadPayeeDoc,
  } = props;
  const [drawerVisible, setDrawerVisible] = useState(false);
  const { downloadDocumentLoading } = store;

  return (
    <>
      <EverTooltip title="View Document">
        <EyeIcon
          className="w-5 h-5 text-ever-base-content-mid cursor-pointer"
          key="view"
          onClick={() => setDrawerVisible(true)}
        />
      </EverTooltip>
      <EverDrawer
        title={
          <Row className="flex items-center">
            <Col span={16}>{documentName}</Col>
            <Col span={8}>
              <div className="flex justify-end w-10/12">
                <EverButton
                  type="filled"
                  color="base"
                  size="small"
                  prependIcon={<DownloadIcon />}
                  loading={downloadDocumentLoading}
                  onClick={() =>
                    onDownloadPayeeDoc({
                      employeeEmailId: emailId,
                      fileName: documentName,
                      envelopeId: envelopeId,
                    })
                  }
                >
                  Download
                </EverButton>
              </div>
            </Col>
          </Row>
        }
        onClose={() => {
          setDrawerVisible(false);
        }}
        placement="right"
        closable={true}
        visible={drawerVisible}
        width="75%"
        height="100vh"
        destroyOnClose={true}
      >
        <PlanViewer
          emailId={emailId}
          onCloseDrawer={() => setDrawerVisible(false)}
          selectedPage={"planDocument"}
          viewSource={DOCUMENT_VIEW_SOURCE}
          documentPath={documentPath}
        />
      </EverDrawer>
    </>
  );
});
