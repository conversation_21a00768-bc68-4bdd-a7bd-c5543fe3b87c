import { AgGridReact } from "ag-grid-react";
import React from "react";

import { EverTg } from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";
import { sadIcecream } from "~/v2/images";

const RenderTable = (props) => {
  const {
    dataSource,
    columns,
    header,
    topMargin,
    hasPermission = true,
  } = props;
  return (
    <div
      key={`reportteams_${header}`}
      className={"reportingTeamTable" + (topMargin ? " mt-6" : " mb-6")}
    >
      <span className="text-ever-base-content">{header}</span>
      {hasPermission ? (
        <div className="mt-2">
          <div className="ag-theme-material w-full">
            <AgGridReact
              {...everAgGridOptions.getDefaultOptions({ type: "md" })}
              key={header}
              domLayout="autoHeight"
              columnDefs={columns}
              rowData={dataSource}
              noRowsOverlayComponentParams={{
                title: "No data to display",
                imgStyle: { height: 64 },
              }}
            />
          </div>
        </div>
      ) : (
        <div className="w-full mt-2 flex flex-col gap-1.5 items-center">
          <img src={sadIcecream} className="w-24 h-24" />
          <EverTg.Caption.Thick className="text-ever-base-content">
            No permission
          </EverTg.Caption.Thick>
          <EverTg.Text className="text-ever-base-content-mid">
            You don&apos;t have permissions to view the data. Please reach out
            to your administrator.
          </EverTg.Text>
        </div>
      )}
    </div>
  );
};
export default RenderTable;
