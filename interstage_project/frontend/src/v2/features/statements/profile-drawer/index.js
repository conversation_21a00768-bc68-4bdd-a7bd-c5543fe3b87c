import { useQuery, gql } from "@apollo/client";
import { isEmpty, camelCase, cloneDeep } from "lodash";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { COMPONENTS, RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverGroupAvatar } from "~/v2/components";

import { getProfileDataQuery } from "./graphql";
import Render from "./Render";

const ProfileDrawer = (props) => {
  const {
    localeId,
    closeDrawer,
    visible,
    // Email ID of selected payee
    selectedPayee,
    dateRange,
    customView,
    periodStartDate,
    periodEndDate,
  } = props;
  const [employeePayrollDetails, setEmployeePayrollDetails] = useState({});
  const [employeeReportingDetails, setEmployeeReportingDetails] = useState({});
  const [selectedEmployeeData, setSelectedEmployeeData] = useState({});
  const [clientProfileFields, setClientProfileFields] = useState([]);
  const [options, setOptions] = useState({});

  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  const { data: employeeData, loading } = useQuery(
    gql`
      ${getProfileDataQuery({
        employeeEmailId: selectedPayee,
        effectiveDate: dateRange.format("YYYY-MM-DD"),
        termEndDate: dateRange?.format("DD/MM/YYYY") ?? null,
        secondaryKdAware: customView ? true : false,
        component: COMPONENTS.PAYOUTS_STATEMENTS,
        withPay: hasPermissions(RBAC_ROLES.VIEW_PAYROLL),
        psd: periodStartDate?.format("YYYY-MM-DD") ?? null,
        ped: periodEndDate?.format("YYYY-MM-DD") ?? null,
      })}
    `,
    {
      fetchPolicy: "no-cache",
    }
  );

  useEffect(() => {
    let tempEmployeePayrollDetails = {};
    const employeePayTimeSlice = JSON.parse(
      employeeData?.employeePayTimeSlice ?? "{}"
    );
    const payeeCurrencySumbol = employeePayTimeSlice?.currency_symbol ?? "";

    // These data will be used in case of "default view" (not custom view).
    // Default view of payee profile is used only in commission plan builder.
    // The default view will display the following fields:
    // 1. Joining Date
    // 2. Employee Id
    // 3. Designation
    // 4. Crystal Access
    // 5. Employment Country
    // 6. Pay Currency
    // 7. Payout Frequency
    // 8. Base Pay and variable pay - only when view:payroll is enabled
    if (
      employeeData &&
      !isEmpty(employeeData.employeePayForAll) &&
      !isEmpty(employeeData.employeePayForAll.employeePayroll)
    ) {
      tempEmployeePayrollDetails =
        employeeData.employeePayForAll.employeePayroll[0];
      let data = {
        "Joining Date": moment(tempEmployeePayrollDetails.joiningDate).format(
          "DD-MMM-YYYY"
        ),
        "Employee Id": tempEmployeePayrollDetails.employeeId,
        Designation: tempEmployeePayrollDetails.designation,
        "Crystal Access":
          tempEmployeePayrollDetails.payeeRole === "Revenue" ? "Yes" : "No",
        "Employment Country": tempEmployeePayrollDetails.employmentCountry,
        "Pay Currency": tempEmployeePayrollDetails.payCurrency,
        [t("PAYOUT_FREQUENCY")]: tempEmployeePayrollDetails.payoutFrequency,
      };
      if (hasPermissions(RBAC_ROLES.VIEW_PAYROLL)) {
        data["Base Pay"] = getLocalizedCurrencyValue(
          tempEmployeePayrollDetails.fixedPay,
          payeeCurrencySumbol,
          localeId
        );
        data["Variable Pay"] = getLocalizedCurrencyValue(
          tempEmployeePayrollDetails.variablePay,
          payeeCurrencySumbol,
          localeId
        );
      }
      setEmployeePayrollDetails(cloneDeep(data));
    } else {
      setEmployeePayrollDetails({});
    }

    // These data will be used in custom view of payee profile.
    // Used in statements page and showing preview in admin settings.
    // The fields to display can be configured in the "Statement settings" of
    // admin settings. Possible default fields are same as that of "default view".
    // Additionally, custom fields, team and pod details can also be configured.
    let tempFieldOptions = {};
    if (
      employeeData &&
      !isEmpty(JSON.parse(employeeData.clientProfileFields))
    ) {
      const data = JSON.parse(employeeData.clientProfileFields);
      tempFieldOptions = data.options;
      if (
        !isEmpty(tempFieldOptions) &&
        (!customView || tempFieldOptions.user_fields)
      ) {
        const customFieldsValue = JSON.parse(
          employeeData.customFieldsDataForEmployee
        );
        let customFieldsNameMap = {};
        let customFieldsTypeMap = {};
        let customFieldsOptionsMap = {};
        if (!isEmpty(employeeData.activeCustomFieldsByClient)) {
          for (const item of employeeData.activeCustomFieldsByClient) {
            customFieldsNameMap[item.systemName] = item.displayName;
            customFieldsTypeMap[item.systemName] = item.fieldType;
            customFieldsOptionsMap[item.systemName] = item.options;
          }
        }
        if (!customView) {
          data.selected_fields = [
            "pay_currency",
            "variable_pay",
            "reporting_manager",
            "bcr",
          ];
        }
        if (!hasPermissions(RBAC_ROLES.VIEW_PAYROLL)) {
          data.selected_fields = data.selected_fields.filter(
            (field) => !["variable_pay", "fixed_pay"].includes(field)
          );
        }
        const profileFields = data.selected_fields
          .filter((item) => item !== "bcr")
          .map((item) => {
            if (Object.keys(data.default_fields).includes(item)) {
              let value = "";
              if (
                Object.keys(tempEmployeePayrollDetails).includes(
                  camelCase(item)
                )
              ) {
                value = tempEmployeePayrollDetails[camelCase(item)];
              }
              if (item === "variable_pay") {
                value = getLocalizedCurrencyValue(
                  tempEmployeePayrollDetails.onTargetVariablePay,
                  payeeCurrencySumbol,
                  localeId
                );
              }
              if (item === "fixed_pay") {
                value = getLocalizedCurrencyValue(
                  tempEmployeePayrollDetails.fixedPay,
                  payeeCurrencySumbol,
                  localeId
                );
              }
              if (
                item === "reporting_manager" &&
                !isEmpty(employeeData.employeePayForAll.employeeHierarchy)
              ) {
                const firstName =
                  employeeData.employeePayForAll.employeeHierarchy[0]
                    .managerDetails.firstName;
                const lastName =
                  employeeData.employeePayForAll.employeeHierarchy[0]
                    .managerDetails.lastName;

                value = `${firstName} ${lastName}`;
              }
              if (item === "payee_role") {
                value = value === "Revenue" ? "Yes" : "No";
              }
              return { name: data.default_fields[item], value: value };
            } else if (Object.keys(customFieldsNameMap).includes(item)) {
              let value = "";
              if (Object.keys(customFieldsValue).includes(item)) {
                if (customFieldsTypeMap[item] === "Checkbox") {
                  value = customFieldsValue[item] ? "True" : "False";
                } else if (customFieldsTypeMap[item] === "Dropdown") {
                  value = JSON.parse(customFieldsOptionsMap[item]).find(
                    (option) => option.system_name === customFieldsValue[item]
                  )?.display_name;
                } else {
                  value = customFieldsValue[item];
                }
              }
              return {
                name: customFieldsNameMap[item],
                value: value,
              };
            }
            return null;
          });

        const index = data.selected_fields.indexOf("bcr");

        if (index !== -1) {
          const bcrList = [];
          const metrics = employeeData.employeePayForAll.employeePayroll[0];

          if (metrics && metrics.bcr) {
            for (const metric of metrics.bcr) {
              bcrList.push({
                name: metric.key,
                value: metric.value,
              });
            }
          }

          profileFields.splice(index, 0, ...bcrList);
        }

        setClientProfileFields(profileFields);
      } else {
        setClientProfileFields([]);
      }
      setOptions(cloneDeep(tempFieldOptions));
    } else {
      setClientProfileFields([]);
    }

    if (employeeData && !isEmpty(employeeData.employeePayForAll)) {
      // General employee data to display full name, profile picture, and
      // designation in both views.
      setSelectedEmployeeData({
        firstName: employeeData.employeePayForAll.firstName ?? "",
        lastName: employeeData.employeePayForAll.lastName ?? "",
        emailId: employeeData.employeePayForAll.employeeEmailId,
        profilePicture: employeeData.employeePayForAll.profilePicture ?? "",
        designation: tempEmployeePayrollDetails.designation,
      });

      // Used to display payee's reporting manager details in "default view"
      let data = {
        "Reporting Details": {
          columns: [
            {
              headerName: "Reporting Manager",
              field: "",
              cellRenderer: ({ data: manager }) => {
                return (
                  <>
                    {manager && (
                      <div className="flex items-center">
                        <EverGroupAvatar
                          avatars={[
                            {
                              firstName: manager.firstName,
                              lastName: manager.lastName,
                              image: manager.profilePicture,
                            },
                          ]}
                          groupMaxCount={1}
                          limitInPopover={1}
                        />
                        <span className="ml-2">
                          {`${manager.firstName} ${manager.lastName}`}
                        </span>
                      </div>
                    )}
                  </>
                );
              },
            },
          ],
          rows: [],
        },
      };
      if (!isEmpty(employeeData.employeePayForAll.employeeHierarchy)) {
        data["Reporting Details"]["rows"] = [
          {
            managerEmailId:
              employeeData.employeePayForAll.employeeHierarchy[0].managerDetails
                .employeeEmailId,
            firstName:
              employeeData.employeePayForAll.employeeHierarchy[0].managerDetails
                .firstName,
            lastName:
              employeeData.employeePayForAll.employeeHierarchy[0].managerDetails
                .lastName,
            image:
              employeeData.employeePayForAll.employeeHierarchy[0].managerDetails
                .profilePicture,
          },
        ];
      }
      setEmployeeReportingDetails(cloneDeep(data));
    } else {
      setSelectedEmployeeData({
        firstName: "",
        lastName: "",
        emailId: "",
        profilePicture: "",
        designation: "",
      });
      setEmployeeReportingDetails({});
    }
  }, [employeeData]);

  const onClose = () => {
    closeDrawer();
  };

  return (
    <Render
      selectedEmployeeData={selectedEmployeeData}
      onClose={onClose}
      visible={visible}
      loading={loading}
      clientProfileFields={clientProfileFields}
      employeePayrollDetails={employeePayrollDetails}
      employeeReportingDetails={employeeReportingDetails}
      customView={customView}
      options={options}
      selectedPayee={selectedPayee}
      dateRange={dateRange}
    />
  );
};
export default ProfileDrawer;
