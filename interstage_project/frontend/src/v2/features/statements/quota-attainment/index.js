import { CalendarIcon } from "@everstage/evericons/duotone";
import { UserCircleIcon } from "@everstage/evericons/outlined";
import { Carousel } from "antd";
import { isNumber } from "lodash";
import React, { Fragment } from "react";
import { twMerge } from "tailwind-merge";

import {
  getLocalizedCurrencyValue,
  currencyColor,
} from "~/Utils/CurrencyUtils";
import { EverBadge, EverLoader, EverTg } from "~/v2/components";

import styles from "./styles.module.scss"; // for Carousel styling and grid auto fill

const QuotaAttainmentCard = (props) => {
  const { quota, isCarousel, hasReportees, showQuotaCards } = props;
  const isQuotaValueNumber = isNumber(Number(quota?.quotaValue));
  const isQuotaAttainmentNumber = isNumber(Number(quota.quotaAttainment));

  // Get quota locale ID from quota or set it to null
  const quotaLocaleId = quota?.quotaLocaleId ?? null;
  const quotaAttainmentPercent = isQuotaAttainmentNumber
    ? getLocalizedCurrencyValue(quota.quotaAttainment, "")
    : "0";

  return (
    <div className="flex flex-col gap-5 p-4 rounded-xl border border-solid border-ever-base-400 bg-ever-base-25 min-h-[200px] justify-between">
      <div className="flex flex-col gap-2">
        <EverTg.Heading3
          className={`text-ever-base-content ${
            showQuotaCards ? "" : "blur-sm"
          }`}
        >
          <EverTg.SubHeading2 className="text-ever-base-content">
            {quota.label}
          </EverTg.SubHeading2>
        </EverTg.Heading3>
        <div className="flex gap-2">
          <div className={`${showQuotaCards ? "" : "blur-sm"}`}>
            <EverBadge
              className="bg-ever-chartColors-2/80 text-ever-chartColors-20 font-medium border-ever-chartColors-2"
              icon={<CalendarIcon className="text-ever-base-content" />}
              outlined={false}
              title={quota?.quotaSchedule ? quota.quotaSchedule : "NA"}
              type="custom"
            />
          </div>
          {isCarousel && hasReportees && (
            <EverBadge
              className="bg-ever-chartColors-2 text-ever-chartColors-20 font-medium border-ever-chartColors-2"
              icon={<UserCircleIcon className="text-ever-base-content" />}
              outlined={false}
              title={quota.isTeam ? "As Manager" : "As Individual"}
              type="custom"
            />
          )}
        </div>
      </div>
      <div className="flex flex-col gap-3.5">
        <div className={`${showQuotaCards ? "" : "blur-sm"} flex gap-2`}>
          <EverTg.Heading1
            className={twMerge(
              "leading-6",
              isQuotaAttainmentNumber
                ? currencyColor(quota.quotaAttainment, "text-ever-base-content")
                : "text-ever-base-content"
            )}
          >
            {isQuotaAttainmentNumber
              ? getLocalizedCurrencyValue(
                  quota.attainedQuota,
                  quota.quotaCurrencySymbol,
                  quotaLocaleId
                )
              : "NA"}
          </EverTg.Heading1>
          <EverTg.Description className="self-end font-medium">
            of
          </EverTg.Description>
          <EverTg.Description
            className={twMerge(
              "self-end font-medium",
              isQuotaValueNumber &&
                currencyColor(quota.quotaValue, "text-ever-base-content-mid")
            )}
          >
            {isQuotaValueNumber
              ? getLocalizedCurrencyValue(
                  quota.quotaValue,
                  quota.quotaCurrencySymbol,
                  quotaLocaleId
                )
              : "NA"}
          </EverTg.Description>
        </div>
        <div className={`${showQuotaCards ? "" : "blur-sm"}`}>
          <EverLoader.Progress
            percent={
              isNumber(Number(quota?.quotaAttainment))
                ? quota.quotaAttainment
                : null
            }
            customPercentText={
              isNumber(Number(quota?.quotaAttainment))
                ? quotaAttainmentPercent
                : "NA"
            }
            type={quota?.quotaAttainment > 100 ? "warning" : "info"}
          />
          {quota?.quotaAttainment > 100 && !isCarousel && (
            <div className="mt-1 text-ever-base-content-mid">
              <span>{`You're on fire!`}</span>
              <EverTg.SubHeading3 className="ml-1">🔥</EverTg.SubHeading3>
              <EverTg.Text className="ml-1">{`You've achieved`}</EverTg.Text>
              <EverTg.Text className="ml-1 text-ever-base-content font-semibold">
                {quotaAttainmentPercent}%
              </EverTg.Text>
              <EverTg.Text className="ml-1">of your target</EverTg.Text>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const QuotaAttainment = (props) => {
  const { quotaOptions, store, showQuotaCards, isCarousel = false } = props;
  const { hasReportees } = store;
  const managerQuota = [];
  const individualQuota = [];

  if (!isCarousel && hasReportees) {
    for (const quota of quotaOptions) {
      if (quota.isTeam) {
        managerQuota.push(quota);
      } else {
        individualQuota.push(quota);
      }
    }
  }

  const renderQuotaList = (label, options, showQuotaCards) => {
    return options.length > 0 ? (
      <div className="mb-10">
        {label && (
          <div className="pb-2 mb-4 text-ever-base-content-mid font-medium border-0 border-b border-solid border-ever-base-400">
            {label}
          </div>
        )}
        <div className={styles.quotaAttainmentCard}>
          {options.map((quota, index) => (
            <QuotaAttainmentCard
              quota={quota}
              key={index}
              isCarousel={isCarousel}
              hasReportees={hasReportees}
              showQuotaCards={showQuotaCards}
            />
          ))}
        </div>
      </div>
    ) : (
      <></>
    );
  };

  return (
    <Fragment>
      {isCarousel ? (
        <Carousel
          className="w-full"
          autoplay
          dots={{ className: "carouselWrapper" }}
        >
          {quotaOptions.map((quota, index) => (
            <QuotaAttainmentCard
              quota={quota}
              key={index}
              isCarousel={isCarousel}
              hasReportees={hasReportees}
              showQuotaCards={showQuotaCards}
            />
          ))}
        </Carousel>
      ) : (
        <div>
          {hasReportees ? (
            <>
              {renderQuotaList("As Manager", managerQuota, showQuotaCards)}
              {renderQuotaList(
                "As Individual",
                individualQuota,
                showQuotaCards
              )}
            </>
          ) : (
            renderQuotaList("", quotaOptions, showQuotaCards)
          )}
        </div>
      )}
    </Fragment>
  );
};

export default QuotaAttainment;
