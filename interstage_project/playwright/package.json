{"name": "playwright", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "pretty": "prettier --write --cache \"tests/**/*.+(js|jsx|json|css|scss|md)\"", "pretty:check": "prettier --check --cache \"tests/**/*.+(js|jsx|json|css|scss|md)\"", "refresh-auth": "playwright test --project=auth-refresh", "copy-creds-to-s3": "aws s3 cp tests/auth-storage/ s3://everstage-coverage/playwright-auth-storage --recursive", "copy-creds-from-s3": "aws s3 sync s3://everstage-coverage/playwright-auth-storage/ tests/auth-storage/", "apiTests": "playwright test tests/specific/api/**/*.spec.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.39.0", "csv-writer": "^1.6.0", "aws-sdk": "^2.1692.0", "eslint": "^8.38.0", "pdf-parse": "^1.1.1", "eslint-config-prettier": "^8.8.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-playwright": "^0.12.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "prettier": "^2.8.7"}, "dependencies": {"@playwright/test": "^1.39.0", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "dotenv": "^16.0.3", "jwt-decode": "^4.0.0", "mockaroo": "^0.1.7", "moment": "^2.29.4", "path": "^0.12.7", "uuid": "^9.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}}