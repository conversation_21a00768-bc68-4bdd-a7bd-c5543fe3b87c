"""
Builds and runs application images and sets it up for ephemeral/throway testing by allowing to clone from
template postgres and snowflake databases

TODO: Move this file to scripts/ci
"""

import multiprocessing
import os
import subprocess
import uuid
from datetime import datetime
from enum import Enum

import typer

from interstage_project.db_setup import run_snowflake_db_setup
from interstage_project.snowflake_db_pool_setup import (
    database_clone_name,
    get_snowflake_test_db_from_pool,
)


# Purpose maps the reason for which we are building the app and we choose the appropriate docker image file,
# docker compose file and image name
class DockerConfig(Enum):
    playwright = (
        "./Dockerfile.ci.playwright",
        "playwright",
    )
    playwright_admin_ui = (
        "./Dockerfile.ci.playwright-admin-ui",
        "playwright-admin-ui",
    )
    prodlocal = (
        "./Dockerfile.ci.playwright",
        "prodlocal",
    )
    prodlocal_all = (  # image includes the admin-ui
        "./Dockerfile.prodlocal-all",
        "prodlocal-all",
    )

    def __init__(self, docker_file, compose_project_name):
        self.docker_file = docker_file
        self.compose_project_name = compose_project_name
        self.web_image_name = f"{self.compose_project_name}-web-image"
        self.nginx_image_name = f"{self.compose_project_name}-nginx-image"


#   Innvocation Example:
#   To build and run the app:
#   python run-app-docker.py
#   To skip building the app:
#   python run-app-docker.py --no-build
#   To build and run the app with caching:
#   python run-app-docker.py --cache
#   To build and run the app with a specific snowflake database:
#   python run-app-docker.py --snowflake-db-name PLAYWRIGHT_TEST_DB_TEMP


app = typer.Typer(
    help="Builds and runs playwright tests against a local instance on top of temporary databases"
)


def get_registry_token():
    #   Get the registry token from the github secrets
    registry_token = os.environ["NPM_REGISTRY_TOKEN"]
    if registry_token is None:
        raise Exception("NPM_REGISTRY_TOKEN not set in environment")
    return registry_token


def db_unique_identifier():
    date_str = str(datetime.now().date()).replace("-", "_")
    return f"{uuid.uuid4().hex[:6].upper()}_{date_str}"


def clone_postgres_database():
    # Setup database with name "playwright_test_db"
    db_name = f"playwright_test_db_{db_unique_identifier()}"
    subprocess.run(
        [
            "../interstage_project/setup-playwright-postgres-db.sh",
            db_name,
            "playwright-test-template.gz",
        ],
        check=True,
    )
    return db_name


def clone_snowflake_database(
    snowflake_template_name="PLAYWRIGHT_TEST_TEMPLATE",
    wait_queue=multiprocessing.Queue(),
):
    test_db_name = database_clone_name(prefix="PLAYWRIGHT")
    typer.echo(f"Going to clone {snowflake_template_name} as {test_db_name}")
    run_snowflake_db_setup(
        template_db_name=snowflake_template_name, test_db_name=test_db_name
    )
    #   Put the snowflake db name in the queue
    wait_queue.put(("clone_snowflake_database", test_db_name))


def build_app(
    cache=False,
    docker_config: DockerConfig = DockerConfig.playwright,
    wait_queue=multiprocessing.Queue(),
):
    # Steps to use separate base image for local build
    playwright_test_env = os.environ.get("PLAYWRIGHT_TEST_ENV", "LOCAL")

    if playwright_test_env == "CI":
        base_image = os.environ.get("BASE_IMAGE")
    else:
        base_image = os.environ.get("BASE_IMAGE_LOCAL")

    # Build the web image with the "localdev" environment
    web_build_args = [
        "python",
        "../scripts/cd/build_image.py",
        "--file",
        docker_config.docker_file,
        "--tag",
        f"{docker_config.web_image_name}:latest",
        "--args",
        "env_name=LOCALDEV",
        f"BASE_IMAGE={base_image}",
        f"NPM_REGISTRY_TOKEN={get_registry_token()}",
        "--context",
        "../",
    ]
    #   If we're using cache, we need to add the cache args
    if cache:
        #   These are set in the github action
        pr_number = os.environ["PR_NUMBER"]
        aws_access_key_id = os.environ["AWS_ACCESS_KEY_ID"]
        aws_secret_access_key = os.environ["AWS_SECRET_ACCESS_KEY"]
        cache_args = [
            "--cache-from",
            f"type=s3,region=us-west-2,bucket=everstage-cd,prefix=cache/docker-pr/buildx-cache-{pr_number}/,access_key_id={aws_access_key_id},secret_access_key={aws_secret_access_key},name=buildx-cache-{pr_number},ignore-error=true",
            "--cache-to",
            f"type=s3,region=us-west-2,bucket=everstage-cd,prefix=cache/docker-pr/buildx-cache-{pr_number}/,access_key_id={aws_access_key_id},secret_access_key={aws_secret_access_key},name=buildx-cache-{pr_number},mode=max",
        ]
        #   Add the cache args to the web build args
        web_build_args.extend(cache_args)
    # Build the nginx image
    subprocess.run(
        [
            "python",
            "../scripts/cd/build_image.py",
            "--file",
            "nginx/Dockerfile",
            "--tag",
            f"{docker_config.nginx_image_name}:latest",
            "--context",
            "../nginx",
        ],
        check=True,
    )
    print(f"Building web image with args: {' '.join(web_build_args)}")
    # Build the web image with the "localdev" environment
    subprocess.run(
        web_build_args,
        check=True,
    )
    #   Put the build app done message in the queue
    wait_queue.put(("build_app", "DONE"))


def get_compose_env(
    db_name, snowflake_db_name, docker_config: DockerConfig = DockerConfig.playwright
):
    compose_env = os.environ.copy()
    compose_env["DB_NAME"] = db_name
    compose_env["SNOWFLAKE_DATABASE"] = snowflake_db_name
    compose_env["COMPOSE_PROJECT_NAME"] = docker_config.compose_project_name
    compose_env["WEB_IMAGE"] = docker_config.web_image_name
    compose_env["NGINX_IMAGE"] = docker_config.nginx_image_name
    compose_env["APP_VERSION"] = datetime.now().strftime("%Y%m%d_%H%M%S")
    return compose_env


def run_app(env):
    subprocess.run(
        [
            "docker",
            "compose",
            "-f",
            "../docker-compose-playwright-local.yml",
            "--env-file",
            "../.env.ci",
            "up",
            "-d",
            "--remove-orphans",
            "--force-recreate",
        ],
        check=True,
        env=env,  # Pass the playwright env to the docker-compose subprocess
    )


def main(
    build: bool = typer.Option(True, help="Build the nginx and web docker images"),
    pool: bool = typer.Option(True, help="Use Snowflake database pool"),
    cache: bool = typer.Option(
        False, help="Use the docker cache"
    ),  # For caching in github
    run: bool = typer.Option(True, help="Run the app and tests"),
    db_name: str = typer.Option(
        None,
        help="The name of the postgres database against which tests are run.  Clones from the unit-test-template if not specified",
    ),
    snowflake_db_name: str = typer.Option(
        None,
        help="The name of the snowflake database against which tests are run.  Clones from the snowflake template database if not specified ",
    ),
    docker_config: str = typer.Option(
        DockerConfig.playwright.name,
        case_sensitive=False,
        help="The reason for running this script - for running playwright, playwright_admin_ui or for running the app in prodlocal mode",
    ),
):
    try:
        docker_config_enum = DockerConfig[docker_config]
        # Snowflake Template Database Name
        snowflake_template_name = "PLAYWRIGHT_TEST_TEMPLATE"
        #   If db_name is None, clone the postgres database
        if db_name is None:
            db_name = clone_postgres_database()

        #  Create a queue to pass the snowflake db name from the snowflake process to the main process
        wait_queue = multiprocessing.Queue()

        #   Create multiprocessing processes for building and cloning the snowflake database
        build_process = multiprocessing.Process(
            target=build_app, args=(cache, docker_config_enum, wait_queue)
        )
        snowflake_process = multiprocessing.Process(
            target=clone_snowflake_database, args=(snowflake_template_name, wait_queue)
        )

        #   If build is true, start the build process
        if build:
            build_process.start()

        #   If snowflake_db_name is None, start the snowflake process
        if snowflake_db_name is None:
            #   Get a database from the pool
            test_id = f"playwright_test_{str(uuid.uuid4())[:8]}".upper()
            if pool:
                snowflake_pool = get_snowflake_test_db_from_pool(
                    test_id=test_id, template_name=snowflake_template_name
                )

                #   If the pool is available, use the database from the pool
                if snowflake_pool["success"]:
                    snowflake_db_name = snowflake_pool["database_name"]
                else:
                    snowflake_process.start()
            else:
                snowflake_process.start()

        #   If build is true, wait for the build process to finish
        if build:
            build_process.join()

        #   If snowflake_db_name is None, wait for the snowflake process to finish and get the snowflake_db_name from the queue
        if snowflake_db_name is None:
            snowflake_process.join()
            while not wait_queue.empty():
                #   Get the snowflake_db_name from the queue
                op, result = wait_queue.get()
                print(f"Operation: {op}, Result: {result}")
                #   Set the snowflake_db_name
                if op == "clone_snowflake_database":
                    snowflake_db_name = result

        if run:
            playwright_env = get_compose_env(
                db_name=db_name,
                snowflake_db_name=snowflake_db_name,
                docker_config=docker_config_enum,
            )
            run_app(env=playwright_env)
    except Exception as e:
        typer.echo(f"Error: {e}")


if __name__ == "__main__":
    typer.run(main)
