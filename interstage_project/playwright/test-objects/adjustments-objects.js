class AdjustmentsPage {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
    this.searchAdjLocator = page.getByPlaceholder("Search", {
      exact: true,
    });
  }

  async searchAdjustment(name) {
    await this.searchAdjLocator.click();
    await this.searchAdjLocator.fill(name);
    await this.page.waitForTimeout(2000);
  }

  async setThresholdValues(from, to) {
    await this.page.getByRole("link", { name: "take me there" }).click();
    await this.page
      .getByRole("tab", { name: "Settings" })
      .locator("span")
      .click();
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Currency" })
      .click();
    await this.page.getByTitle("AUD").locator("div").first().click();
    await this.page.getByPlaceholder("From").click();
    await this.page.getByPlaceholder("From").fill(from);
    await this.page.getByPlaceholder("To", { exact: true }).click();
    await this.page.getByPlaceholder("To", { exact: true }).fill(to);
  }

  async saveThresholdAndVerifySuccessMessage() {
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page
      .getByText("Threshold values saved successfully.")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async addAdjustment(payee, period, amount, planName) {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Commission" }).click();
    await this.page.getByLabel("Payee*").click();
    await this.page.getByTitle(payee).locator("div").first().click();
    await this.page.getByLabel("Effective Period*").click();
    await this.page.getByText(period).click();
    await this.page.getByPlaceholder("Add amount").click();
    await this.page.getByPlaceholder("Add amount").fill(amount);
    await this.page.getByLabel("Commission Plan").click();
    await this.page.locator(`div[label="${planName}"]`).click();
  }

  async submitAdjustment() {
    await this.page.getByRole("button", { name: "Submit" }).click();
  }

  async closePopup() {
    await this.page.getByRole("button", { name: "Close" }).click();
  }

  async exitCanvas() {
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
    await this.page.getByRole("button", { name: "Exit", exact: true }).click();
  }

  async selectPlan(planName) {
    await this.page.waitForTimeout(2000);
    await this.page.getByText(planName).click();
  }

  async deletePayeeFromPlan() {
    await this.page.getByTitle("Hide panel").click();
    await this.page
      .locator("//div[@col-id='validationStatus']//div[2]//*[name()='svg']")
      .nth(1)
      .click();
  }

  async viewTimeline(index, status) {
    await this.page
      .getByRole("gridcell", { name: status })
      .locator("svg")
      .nth(index)
      .click();
  }

  async validateApprovedByApproverTimeline(approvedTimelineMessage) {
    await this.page
      .getByText(approvedTimelineMessage)
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async editRecord(index, name) {
    const payeeNameLoc = this.page.locator(
      "div.ag-center-cols-container div.ag-row"
    );
    const updateLoc = this.page.locator(
      "div.ag-pinned-right-cols-container > div.ag-row"
    );
    await payeeNameLoc.nth(index).getByText(name);
    await updateLoc.nth(index).getByRole("button").click();
    await this.page.getByText("Edit").click();
  }

  async hoverEditRecord(index, name) {
    const payeeNameLoc = this.page.locator(
      "div.ag-center-cols-container div.ag-row"
    );
    const updateLoc = this.page.locator(
      "div.ag-pinned-right-cols-container > div.ag-row"
    );
    await payeeNameLoc.nth(index).getByText(name);
    await updateLoc.nth(index).getByRole("button").click();
    await this.page.getByText("Edit").hover();
  }

  async validateTooltip(context) {
    await this.page
      .getByRole("tooltip", {
        name: context,
      })
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async hoverDeleteRecord() {
    await this.page.getByText("Delete").hover();
  }

  async selectAutoApproval() {
    await this.page
      .locator("div")
      .filter({
        hasText:
          /^Auto ApprovedAs the adjustment amount is lesser than the threshold value\.$/,
      })
      .first()
      .click();
  }

  async validateTextContext(context) {
    await this.page
      .getByText(context)
      .waitFor({ state: "visible", timeout: 10000 });
    await this.page.waitForTimeout(2000);
  }

  async validateValidationContext(validationMessage) {
    await this.page
      .getByRole("treegrid")
      .getByText(validationMessage)
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async selectWorkflow(wfName) {
    await this.page.locator("div").filter({ hasText: wfName }).nth(1).click();
  }

  async checkSkipWorkflow(reason) {
    await this.page
      .getByLabel("Skip approval workflow mandate for a scenario.")
      .check();
    await this.page
      .getByPlaceholder("Write the reason for skipping the approval mandate...")
      .click();
    await this.page
      .getByPlaceholder("Write the reason for skipping the approval mandate...")
      .fill(reason);
  }

  async viewSkipWfTimeline(index, status) {
    await this.page
      .locator("//div[@row-index=1]")
      .getByRole("gridcell", { name: status })
      .locator("svg")
      .nth(index)
      .click();
  }

  async goToPayouts() {
    await this.page.locator("#Commission").first().click();
    await this.page.getByRole("link", { name: "Payouts" }).click();
  }

  async goToReviewAdjustmentsFromPayouts() {
    await this.page.getByRole("link", { name: "here" }).click();
  }

  async goToCommissionAdjustments() {
    await this.page.goto(
      "/approvals/commission-adjustments?period=all&status=all",
      { waitUntil: "networkidle" }
    );
  }

  async goToCommAdjApprovalPendingRequests() {
    await this.page
      .getByRole("button", { name: /Pending Requests.*./ })
      .click();
  }

  async rejectRequest(rowIndex, rejectComment) {
    await this.page
      .getByRole("gridcell", { name: "Pending" })
      .nth(rowIndex)
      .click();
    await this.page.getByRole("button", { name: "Reject" }).nth(1).click();
    await this.page.getByPlaceholder("Add your comments").click();
    await this.page.getByPlaceholder("Add your comments").fill(rejectComment);
    await this.page.getByRole("button", { name: "Reject Request" }).click();
  }

  async selectPeriod(period) {
    const periodSelector = this.page.locator(
      'div[data-testid="period-select"]'
    );
    await periodSelector.click();
    await this.page.getByText(period).click();
  }

  async lockStatements(name) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(name);
    await this.page.waitForTimeout(1000);
    await this.page
      .locator("div.ag-pinned-left-cols-container div.ag-row")
      .getByText(name);
    await this.page
      .locator("div.ag-pinned-right-cols-container > div.ag-row")
      .getByRole("button")
      .nth(1)
      .click();
    await this.page.getByRole("menuitem", { name: "Lock Statements" }).click();
  }

  async goToCommissionPlans() {
    await this.page.locator("#Commission").first().click();
    await this.page.getByRole("link", { name: "Plans" }).click();
  }

  async deletePlan(planName) {
    await this.page.getByTestId(`pt-actions-${planName}`).click();
    await this.page.getByText("Delete", { exact: true }).click();
  }

  async goToSettingsAdjustments() {
    await this.page.goto("/settings/adjustments", { waitUntil: "networkidle" });
  }
}

module.exports = AdjustmentsPage;
