import CommissionsSyncPrevPeriod from "./commissionSyncPrevPeriod-objects";
const CommonUtils = require("./common-utils-objects");

class AdjustementsV2Page {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async navigate(URL) {
    await this.page.goto(URL, { waitUntil: "networkidle" });
  }

  async clickCommissionAdjustmentBtn() {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Commission" }).click();
  }

  async addPayee(payeeName) {
    await this.page.getByLabel("Payee*").click();
    await this.page.getByLabel("Payee*").fill(payeeName);
    await this.page.waitForTimeout(2000);
    await this.page
      .getByRole("listitem")
      .getByText(payeeName, { exact: true })
      .click();
  }

  async addCommissionAdjustment(
    payeeName,
    effectivePeriod,
    amount,
    commissionPlan,
    criteria,
    reasonCategory,
    lineItemID,
    reason
  ) {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Commission" }).click();
    await this.page.getByLabel("Payee*").click();
    await this.page.getByLabel("Payee*").fill(payeeName);
    await this.page.waitForTimeout(2000);
    await this.page
      .getByRole("listitem")
      .getByText(payeeName, { exact: true })
      .click();
    await this.page.getByLabel("Effective Period*").click();
    await this.page.getByLabel("Effective Period*").fill(effectivePeriod);
    await this.page.locator(`(//span[@title='${effectivePeriod}'])[1]`).click();
    await this.page.getByPlaceholder("Add amount").click();
    await this.page.getByPlaceholder("Add amount").fill(amount);
    await this.page.getByLabel("Commission Plan").click();
    await this.page.locator(`(//span[@title='${commissionPlan}'])[1]`).click();
    await this.page.getByLabel("Component Name").click();
    await this.page.locator(`(//span[@title='${criteria}'])[1]`).click();
    await this.page.getByLabel("Reason Category").click();
    await this.page.locator(`div[title='${reasonCategory}']`).click();
    await this.page.getByLabel("Line Item Id").click();
    await this.page.getByLabel("Line Item Id").fill(lineItemID);
    await this.page.getByPlaceholder("Enter a description...").click();
    await this.page.getByPlaceholder("Enter a description...").fill(reason);
  }

  async addDraws(payee, period, year, type, amount) {
    await this.page.getByText(payee).click();
    await this.page.getByRole("button", { name: "Add Draws" }).click();
    // await this.page.getByRole("button", { name: "Modify" }).click();
    await this.page
      .getByLabel("Add Draws")
      .getByPlaceholder("Select Year")
      .click();
    await this.page.getByText(year).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Period$/ })
      .nth(2)
      .click();
    await this.page.locator("span").filter({ hasText: period }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Type$/ })
      .nth(2)
      .click();
    await this.page.getByText(type, { exact: true }).click();
    await this.page.getByPlaceholder("Enter Amount").click();
    await this.page.getByPlaceholder("Enter Amount").fill(amount);
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async drawsAddedValidation() {
    await this.page
      .getByText("Draws Added Successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async addDrawsAdjustment(payee, fy, period, amount) {
    await this.page.getByRole("button", { name: "Recover Draw" }).click();
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Recover Draw" }).click();
    await this.page.getByLabel("Payee*").click();
    await this.page.getByLabel("Payee*").fill(payee);
    await this.page.getByText(payee).click();
    await this.page.getByLabel("Fiscal Year*").click();
    await this.page.getByRole("listitem").getByText(fy).click();
    await this.page.getByLabel("Period*").click();
    await this.page.getByLabel("Period*").fill(period);
    await this.page.getByRole("listitem").getByText(period).click();
    await this.page.getByLabel("Amount*").click();
    await this.page.getByLabel("Amount*").fill(amount);
  }

  async modifyDraws(fiscalYear, payee, oldDrawtype, NewDrawType) {
    await this.page.getByText(payee).click();
    await this.page.getByPlaceholder("Select Year").click();
    await this.page.getByText(fiscalYear).click();
    await this.page.getByRole("button", { name: "Modify" }).click();
    await this.page.getByTitle(oldDrawtype).click();
    await this.page.getByText(NewDrawType, { exact: true }).click();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async deleteDrawsAdjustment(draw, drawAmount) {
    await this.page.getByRole("button", { name: "Recover Draw" }).click();
    await this.page
      .getByTestId(`pt-delete-DA-${draw}-${drawAmount}`)
      .locator("svg")
      .click();
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async deleteDrawsAdjustmentValidation() {
    await this.page
      .getByText("Adjustment deleted Successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async getDrawsAmount() {
    return await this.page
      .locator("div[data-testid='pt-draw-recovered']")
      .innerText();
  }

  async clickSubmitBtn() {
    await this.page.getByRole("button", { name: "Submit" }).click();
  }

  async adjustmentValidation() {
    await this.page
      .getByText("Adjustment saved successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async adjustmentUpdateValidation() {
    await this.page
      .getByText("Adjustment updated successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  // Helper to convert '30 Jun 2024' to '2024-06-30'
  _convertToISODate(dateStr) {
    const [day, mon, year] = dateStr.trim().split(" ");
    const months = {
      Jan: "01",
      Feb: "02",
      Mar: "03",
      Apr: "04",
      May: "05",
      Jun: "06",
      Jul: "07",
      Aug: "08",
      Sep: "09",
      Oct: "10",
      Nov: "11",
      Dec: "12",
    };
    return `${year}-${months[mon]}-${day.padStart(2, "0")}`;
  }

  async selectPeriod(placeholder, date) {
    await this.page.getByPlaceholder(placeholder).click();

    // Convert input date to ISO format
    const isoDate = this._convertToISODate(date);
    const [targetYear, targetMonth, targetDay] = isoDate.split("-").map(Number);

    // Get current displayed year and month from the picker
    const currentYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const currentMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const monthMap = {
      Jan: 1,
      Feb: 2,
      Mar: 3,
      Apr: 4,
      May: 5,
      Jun: 6,
      Jul: 7,
      Aug: 8,
      Sep: 9,
      Oct: 10,
      Nov: 11,
      Dec: 12,
    };
    const currentMonth = monthMap[currentMonthName.trim()];

    // Year navigation (reuse existing logic)
    if (targetYear < currentYear) {
      const csPrev = new CommissionsSyncPrevPeriod(this.page);
      await csPrev.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    } else if (targetYear > currentYear) {
      const csPrev = new CommissionsSyncPrevPeriod(this.page);
      await csPrev.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    }

    // Refresh currentMonth/currentYear after year navigation
    const displayedYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const displayedMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const displayedMonth = monthMap[displayedMonthName.trim()];

    // Month navigation
    if (targetYear === displayedYear) {
      if (targetMonth < displayedMonth) {
        for (let i = 0; i < displayedMonth - targetMonth; i++) {
          await this.page.locator(".ant-picker-header-prev-btn").click();
        }
      } else if (targetMonth > displayedMonth) {
        for (let i = 0; i < targetMonth - displayedMonth; i++) {
          await this.page.locator(".ant-picker-header-next-btn").click();
        }
      }
      // else: same month, do nothing
    }

    console.log(isoDate);
    // Click the date cell
    await this.page.locator(`td[title='${isoDate}']`).click();
  }

  async selectPeriodAdjustment(date) {
    await this.page.getByPlaceholder("Select Period").click();

    // Convert input date to ISO format
    const isoDate = this._convertToISODate(date);
    const [targetYear, targetMonth, targetDay] = isoDate.split("-").map(Number);

    // Get current displayed year and month from the picker
    const currentYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const currentMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const monthMap = {
      Jan: 1,
      Feb: 2,
      Mar: 3,
      Apr: 4,
      May: 5,
      Jun: 6,
      Jul: 7,
      Aug: 8,
      Sep: 9,
      Oct: 10,
      Nov: 11,
      Dec: 12,
    };
    const currentMonth = monthMap[currentMonthName.trim()];

    // Year navigation (reuse existing logic)
    if (targetYear < currentYear) {
      const csPrev = new CommissionsSyncPrevPeriod(this.page);
      await csPrev.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    } else if (targetYear > currentYear) {
      const csPrev = new CommissionsSyncPrevPeriod(this.page);
      await csPrev.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    }

    // Refresh currentMonth/currentYear after year navigation
    const displayedYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const displayedMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const displayedMonth = monthMap[displayedMonthName.trim()];

    // Month navigation
    if (targetYear === displayedYear) {
      if (targetMonth < displayedMonth) {
        for (let i = 0; i < displayedMonth - targetMonth; i++) {
          await this.page.locator(".ant-picker-header-prev-btn").click();
        }
      } else if (targetMonth > displayedMonth) {
        for (let i = 0; i < targetMonth - displayedMonth; i++) {
          await this.page.locator(".ant-picker-header-next-btn").click();
        }
      }
      // else: same month, do nothing
    }

    console.log(isoDate);
    // Click the date cell
    await this.page.locator(`td[title='${isoDate}']`).click();
  }

  async selectPayee(payee) {
    await this.page
      .getByTestId(`pt-${payee}-name-cell`)
      .getByRole("link", { name: "Payee" })
      .click();
  }

  async clickComponent(component) {
    await this.page.getByText(component).click();
  }

  async getAdjustedAmount() {
    return await this.page.locator("div.text-ever-success-hover").innerText();
  }

  async getErrorMessages() {
    return await this.page
      .locator("div.ant-form-item-explain-error div")
      .allInnerTexts();
  }

  async getAllAdjustments() {
    return await this.page
      .locator("//span[contains(@id,'cell-employeeName')]")
      .allInnerTexts();
  }

  async getAdjustedAmountNonSettlement() {
    return await this.page
      .locator("div.nonSettlementStatementTable div.text-ever-success-hover")
      .innerText();
  }

  async toggleStatementLock(email, action) {
    const oppositeAction = action === "Lock" ? "Unlock" : "Lock";

    await this.page.getByTestId(`${email}-actions-dd`).click();

    // Check and handle if statements are in opposite state
    const oppositeButton = this.page.getByText(`${oppositeAction} Statements`, {
      exact: true,
    });
    if (await oppositeButton.isVisible({ timeout: 5000 })) {
      await oppositeButton.click();
      await this.waitForLockStatusUpdate();
      await this.page.getByTestId(`${email}-actions-dd`).click();
    }

    // Perform requested action
    await this.page
      .getByText(`${action} Statements`, {
        exact: true,
      })
      .click();
    await this.waitForLockStatusUpdate();
  }

  // Helper method for common wait
  async waitForLockStatusUpdate() {
    await this.page
      .getByText("Lock status updated successfully")
      .waitFor({ state: "visible", timeout: 20000 });
    await this.page.waitForTimeout(3000);
  }

  // Usage methods for backward compatibility
  async lockStatements(email) {
    await this.toggleStatementLock(email, "Lock");
  }

  async unlockStatements(email) {
    await this.toggleStatementLock(email, "Unlock");
  }

  async clickEditAdjustments() {
    await this.page.waitForTimeout(3000);
    await this.page
      .locator("div[role='presentation'] button:nth-child(1)")
      .click();
  }

  async editAmount(amount) {
    await this.page.getByPlaceholder("Add amount").click();
    await this.page.getByPlaceholder("Add amount").fill(amount);
  }

  async editLineItemId(lineItemID) {
    await this.page.getByLabel("Line Item Id").click();
    await this.page.getByLabel("Line Item Id").fill(lineItemID);
  }

  async editReasonCategory(oldReasoncategory, NewreasonCategory) {
    await this.page.getByTitle(oldReasoncategory).click();
    await this.page.locator(`div[title='${NewreasonCategory}']`).click();
  }

  async editReason(reason) {
    await this.page.getByPlaceholder("Enter a description...").fill(reason);
  }

  async searchAdjustments(payeeName) {
    await this.page.getByPlaceholder("Search").click();
    await this.page.getByPlaceholder("Search").fill(payeeName);
    await this.page.waitForTimeout(3000);
  }

  async filterByCommissionAmount(amount) {
    await this.page
      .locator('button:left-of(:text("Import / Export")):nth-child(2)')
      .first()
      .click();
    await this.page.locator("input[type$='number']").click();
    await this.page.locator("input[type$='number']").fill(amount);
    await this.page.getByRole("button", { name: "Apply" }).click();
  }

  async clickDeleteAdjustments() {
    await this.page
      .locator("div[role='presentation'] button:nth-child(2)")
      .click();
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async deleteAdjustmentValidation() {
    await this.page
      .getByText("Adjustment deleted successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async getAllAdjustmentAmounts() {
    return await this.page
      .locator("div[col-id$='amount'] span span")
      .allInnerTexts();
  }

  async ToggleApprovalsAdj() {
    await this.page.getByRole("link", { name: "take me there" }).click();
    await this.page
      .getByRole("tab", { name: "Settings" })
      .locator("span")
      .click();
    await this.page.locator("(//button[@role='switch'])[2]").click();
    await this.page.waitForTimeout(3000);
  }

  async setApprovalThreshold(FromAmount, ToAmount) {
    await this.page
      .locator(
        "div.ant-select-selector input.ant-select-selection-search-input"
      )
      .click();
    await this.page.locator("div").filter({ hasText: /^INR$/ }).nth(2).click();
    await this.page.getByPlaceholder("From").click();
    await this.page.getByPlaceholder("From").fill(FromAmount);
    await this.page.getByPlaceholder("To", { exact: true }).click();
    await this.page.getByPlaceholder("To", { exact: true }).fill(ToAmount);
    await this.page.getByRole("button", { name: "Update" }).click();
  }

  async thresholdSetValidation() {
    await this.page
      .getByText("Threshold values saved successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async selectAutoApproval() {
    await this.page.locator("(//input[@type='radio'])[1]").click();
  }

  async selectApprovalWorkflow() {
    await this.page.locator("(//input[@type='radio'])[2]").click();
  }

  async changeApprovalsFilter() {
    await this.page.getByText("Approval Status:All").click();
    await this.page.getByRole("menuitem", { name: "Pending" }).click();
  }

  async getApprovalText() {
    await this.page
      .locator("div.text-ever-success-lite-content span")
      .innerText();
  }

  async clearAllBtn() {
    await this.page.getByText("Clear all", { exact: true }).click();
  }

  async closeAdjustmentModal() {
    await this.page.getByLabel("Close").click();
  }

  async clickConfirmToggleOff() {
    await this.page.getByRole("button", { name: "Confirm" }).click();
    await this.page.waitForTimeout(3000);
  }

  async getReason() {
    return await this.page
      .locator("div[col-id='reason'] span div span")
      .innerText();
  }

  async getReasonCategory() {
    return await this.page
      .locator("div[col-id='reasonCategory'] div span[class='ag-cell-value']")
      .innerText();
  }

  async getLineItemId() {
    return await this.page
      .locator("div[col-id='lineItemId'] div span[class='ag-cell-value']")
      .innerText();
  }

  async getAllEmployees() {
    await this.page.waitForTimeout(2000);
    return await this.page
      .locator("div[role='gridcell'][col-id='employeeName'] span")
      .last()
      .innerText();
  }

  async searchUser(payee) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(payee);
    await this.page.waitForTimeout(3000);
    await this.page.locator(`(//div[normalize-space()='${payee}'])[1]`).click();
  }

  async changePayeeCurrency(CurrentCurrency, NewCurrency) {
    await this.page.getByText(CurrentCurrency).click();
    await this.page.locator("span").filter({ hasText: NewCurrency }).click();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByLabel("Save by overwriting current").check();
    await this.page.getByRole("button", { name: "Save", exact: true }).click();
    await this.page
      .getByText("Employee details saved!")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async checkCurrency(payee, period) {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Commission" }).click();
    await this.page.getByLabel("Payee*").click();
    await this.page.locator(`span[title='${payee}']`).click();
    await this.page.getByLabel("Effective Period*").click();
    await this.page
      .locator(`div[title='${period}'] div[role='listitem']`)
      .click();
  }

  async getCurrencyValue(title) {
    return await this.page
      .locator(`div[class='ant-select-selector'] span[title='${title}']`)
      .innerText();
  }

  async changeAdjPeriod(oldPeriod, newPeriod) {
    await this.page.getByLabel("Add Adjustment").getByText(oldPeriod).click();
    await this.page
      .locator(`div[title='${newPeriod}'] div[role='listitem']`)
      .click();
  }

  async changeStatementPeriod(currentPeriod, newPeriod) {
    await this.page.getByTestId("ever-select").getByText(currentPeriod).click();
    await this.page.getByText(newPeriod).click();
  }

  async impersonatePayee(payee) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(payee);
    await this.page.waitForTimeout(5000);
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page.waitForTimeout(5000);
  }

  async approveCommissionAdj(reason) {
    await this.page
      .locator(`button[data-testid='${reason} approve button']`)
      .click();
    await this.page.getByRole("button", { name: "Yes, approve" }).click();
    await this.page
      .getByText("You've approved the request!")
      .first()
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async exitImpersonation() {
    await this.page.getByRole("button", { name: "Exit" }).click();
    await this.page.waitForTimeout(4000);
  }

  async generateDatasheet() {
    const generateText = await this.page.getByText(
      "Datasheet sync request has been submitted"
    );
    await generateText.waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible", timeout: 250000 });
  }

  async updateDatasheet() {
    await this.page.waitForTimeout(3000);
    await this.page.getByRole("button", { name: "Update Data" }).click();
  }

  async getvalueDatasheet(colId, amount) {
    const locatorInnerText = this.page.locator(
      `div[col-id='${colId}'] div[data-testid='pt-${amount}']`
    );
    locatorInnerText.scrollIntoViewIfNeeded();
    return locatorInnerText.innerText();
  }

  async switchDatasheet(newDatasheet) {
    await this.page.getByText(newDatasheet).click();
  }

  async getAdjustedAmountDatasheet(amount) {
    return await this.page
      .locator(
        `div[col-id='comm_adj_for_plan'] div[data-testid='pt-${amount}']`
      )
      .innerText();
  }

  async clickSearchButton() {
    await this.page.getByPlaceholder("Search").click();
  }

  async clickWorkflowInAdjustment(workflowname) {
    await this.page.getByText(workflowname).click();
  }

  async clickAllAdjustmentCheckbox() {
    await this.page
      .locator("//label[@class='ant-checkbox-wrapper tick w-max ml-1']")
      .click();
  }

  async clickDeleteButton() {
    await this.page.getByRole("button", { name: "Delete" }).click();
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async editAdjustments(index) {
    await this.page
      .locator("div[role='presentation'] button:nth-child(1)")
      .nth(index)
      .click();
  }

  async setUserWhenApprovalIsOverridden(username) {
    await this.page.getByPlaceholder("Choose who needs to be").click();
    await this.page.getByRole("tab", { name: "Users" }).click();
    await this.page.getByText(username).click();
  }

  async replaceIdinLineItemid(id) {
    await this.page.getByLabel("Line Item Id").click();
    await this.page.getByLabel("Line Item Id").press("ControlOrMeta+a");
    await this.page.getByLabel("Line Item Id").fill(id);
  }

  async createCustomWorkflow(notificationmail) {
    await this.page
      .getByRole("button", { name: "Create Custom Workflow" })
      .click();
    await this.page.getByPlaceholder("Choose who needs to be").first().click();
    await this.page.getByRole("tab", { name: "Users" }).click();
    await this.page.getByText(notificationmail).click();
    // await this.page.getByText("Payee", { exact: true }).click();
    // await this.page.getByPlaceholder("Choose approvers").click();
    await this.page.locator('input[placeholder="Choose approvers"]').click();
    await this.page.getByText("Payee", { exact: true }).click();
    await this.page.getByLabel("Anyone approvesStage is").check();
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
  }

  async skipApprovalWorkflow(reason) {
    await this.page.getByLabel("Skip approval workflow").check();
    await this.page.getByPlaceholder("Write the reason for skipping").click();
    await this.page
      .getByPlaceholder("Write the reason for skipping")
      .fill(reason);
    await this.page.getByRole("button", { name: "Submit" }).click();
  }

  async registerPayment(payeeEmail) {
    await this.page.getByTestId(`pt-${payeeEmail}-register-payment`).click();
    await this.page.getByRole("button", { name: "Register" }).click();
    await this.page
      .getByText("Payment registered successfully")
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(3000);
  }

  async invalidatePayment(payeeEmail) {
    await this.page
      .locator(`.ag-group-contracted:left-of(:text("${payeeEmail}"))`)
      .first()
      .click();
    await this.page.getByRole("button", { name: "Invalidate" }).click();
    await this.page
      .getByText("Payment invalidated successfully", { exact: true })
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(3000);
  }

  async clickBulkImport(buttonName) {
    await this.page.getByRole("button", { name: "Import / Export" }).click();
    await this.page.locator(`text="${buttonName}"`).click();
    await this.page.waitForTimeout(3000);
  }

  async getAllStatementPeriods() {
    // Wait until the visible dropdown is rendered
    const dropdown = this.page.locator(
      "div.ant-select-dropdown:not(.ant-select-dropdown-hidden)"
    );

    // Wait for at least one visible dropdown item to appear
    await dropdown
      .locator("div.ant-select-item-option")
      .first()
      .waitFor({ state: "visible" });

    // Return visible dropdown item texts only
    return await dropdown.locator("span").allInnerTexts();
  }

  async clickStatementPeriodDropdown() {
    const dropdownTrigger = this.page.locator("div[name='statementPeriod']");
    await dropdownTrigger.click();

    // Wait for the dropdown to be visible (and not hidden)
    const visibleDropdown = this.page.locator(
      "div.ant-select-dropdown:not(.ant-select-dropdown-hidden)"
    );
    await visibleDropdown.waitFor({ state: "visible", timeout: 2000 });
  }

  async clickStatementPeriod(period) {
    // Ensure dropdown is still open
    const dropdown = this.page.locator(
      "div.ant-select-dropdown:not(.ant-select-dropdown-hidden)"
    );
    await dropdown.waitFor({ state: "visible", timeout: 2000 });

    const periodOption = dropdown.locator(`span[title='${period}']`);
    await periodOption.click();
  }

  async closeBulkImportWizard() {
    await this.page.locator("button.ant-drawer-close").click();
  }

  async clickBrowse() {
    await this.page.getByText("Browse").click();
  }

  async UploadAsCSVhover() {
    await this.page.locator("div.ant-upload-drag-container a").hover();
  }

  async getTooltipMessage() {
    return await this.page.locator("div.ant-tooltip-inner").innerText();
  }

  async clickNextButton() {
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async clickImportButton() {
    await this.page
      .getByRole("button", { name: "Import", exact: true })
      .click();
  }

  async clickImportButtonConfirm() {
    await this.page.getByRole("button", { name: "Yes, Import" }).click();
  }

  async clickImportandalidateButton() {
    await this.page
      .getByRole("button", { name: "Import and Validate" })
      .click();
  }

  async validateSuccess() {
    await this.page
      .getByText("Validation successful.")
      .first()
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async fillEmail(email) {
    await this.page.getByPlaceholder("Email").click();
    await this.page.getByPlaceholder("Email").fill(email);
  }

  async checkUploadstatus() {
    await this.page
      .getByText("Upload in progress...")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async getPayeeCurrencies() {
    return await this.page
      .locator("div.ag-row div[col-id='payee_currency']")
      .allInnerTexts();
  }

  async getallCurrencies() {
    return await this.page
      .locator("div[col-id='currency'] span")
      .allInnerTexts();
  }

  async clickDownloadTemplate() {
    await this.page.getByRole("button", { name: "Download" }).click();
  }

  async hoverValidationErrorMessage() {
    await this.page.locator("div[col-id='errors'] span span").hover();
  }

  async requiredFieldsMessage() {
    await this.page
      .getByText("Select all required fields.")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async crossComponentbutton() {
    await this.page.locator("div.ag-row-hover span.ant-select-clear").click();
  }

  async getStatamentPeriod() {
    return await this.page
      .locator("div[name='statementPeriod'] span[title]")
      .innerText();
  }

  async getValidateError() {
    await this.page
      .getByText("Something went wrong. Please try again.")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async clickDownloadValidatedData() {
    await this.page
      .getByRole("button", { name: "Download the below data" })
      .click();
  }

  // Email. Amount, Reason and Line Item ID
  async editBulkAdjustment(field, newValue) {
    await this.page
      .getByRole("gridcell", { name: field, exact: true })
      .dblclick();
    await this.page.getByLabel("Input Editor").fill(newValue);
  }

  // Payee Currency
  async editBulkAdjustmentpayeeCurrency(field, newValue) {
    await this.page
      .getByRole("gridcell", { name: field, exact: true })
      .dblclick();
    await this.page.getByTitle(newValue).locator("div").click();
  }

  // Reason Category
  async editBulkAdjustmentReasonCategory(previous, newValue) {
    await this.page
      .locator(`(//div[contains(text(),'${previous}')])`)
      .dblclick();
    await this.page.getByTitle(newValue).locator("div").click();
  }

  // Plan and Component
  async editBulkAdjustmentPlan(previous, newValue) {
    await this.page
      .locator(`(//div[contains(text(),'${previous}')])`)
      .dblclick();
    await this.page.getByLabel("Input Editor").fill(newValue);
  }

  async clickRevalidatebutton() {
    await this.page.getByRole("button", { name: "Revalidate" }).click();
  }

  async changeCustomTerminology(fieldName, newValue) {
    await this.page.getByTestId(`pt-${fieldName}`).click();
    await this.page.getByLabel("Input Editor").fill(newValue);
  }

  async clickUpdateButton() {
    await this.page.getByRole("button", { name: "Update" }).click();
  }

  async clickShowmoreDetails() {
    await this.page.getByRole("button", { name: "Show more details" }).click();
  }

  async getAllResultsCount() {
    return await this.page
      .locator("div[class='ant-col flex items-center'] span")
      .allInnerTexts();
  }

  async hoverPlanField() {
    await this.page.locator("(//div[@class='text-ellipsis'])[1]").hover();
  }

  async getTooltipPlanField() {
    return await this.page
      .locator(
        "div[class='ant-tooltip ant-tooltip-placement-top '] div[role='tooltip']"
      )
      .innerText();
  }

  async selectApprovalWorkflowBulk(workflowName) {
    await this.page
      .getByRole("dialog")
      .getByTestId("ever-select")
      .locator("span")
      .nth(1)
      .click();
    await this.page.getByText(workflowName).click();
  }

  async addCurrencyThreshold(previous, currency) {
    await this.page.getByText(previous).first().click();
    await this.page.locator("span").filter({ hasText: currency }).click();
  }

  async selectOverrideCheckbox() {
    await this.page.getByLabel("Override and send all 2").check();
  }

  async getAllEmployeesAdj() {
    return await this.page
      .locator("div[col-id='employeeName'] span div span")
      .allInnerTexts();
  }

  async getDropdownValues() {
    return await this.page
      .locator(
        ".ant-select-dropdown:not(.ant-select-dropdown-hidden) .ant-select-item-option-content span"
      )
      .allTextContents();
  }

  async selectFilterBulk(column) {
    await this.page
      .getByTestId(column)
      .locator("div")
      .filter({ hasText: "Select" })
      .click();
  }

  async clickStatementPeriodLink(period) {
    await this.page.getByRole("link", { name: period, exact: true }).click();
  }

  async getPeriodValue() {
    return await this.page.locator("div.text-xs").last().innerText();
  }

  async openFilter() {
    await this.page.getByText("Filters").click();
  }

  async selectFilter(filterName, filterOption, filterItem) {
    await this.page.locator(`div[data-testid="option-${filterName}"]`).click();
    const optionLocator = this.page.locator(`div[label="${filterOption}"]`);
    await optionLocator.scrollIntoViewIfNeeded();
    await optionLocator.click();
    if (filterItem) {
      await this.page.locator(`div[data-testid="${filterName}"]`).click();
      const itemLocator = this.page.locator(`div[label="${filterItem}"]`);
      await itemLocator.scrollIntoViewIfNeeded();
      await this.page.evaluate(
        (el) => el.scrollIntoView(),
        await itemLocator.elementHandle()
      );
      await itemLocator.click();
    }
  }

  async applyFilter() {
    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.page.waitForTimeout(6000);
  }

  async clearFilter() {
    await this.page.getByText("Clear all").click();
    await this.page.waitForTimeout(6000);
  }

  async adjustmentsCount() {
    const text = await this.page
      .locator("div")
      .filter({ hasText: /adjustments$/ })
      .nth(1)
      .textContent();
    const match = text.match(/^(\d+)\s+adjustments$/);
    const dynamicNumber = match ? parseInt(match[1], 10) : 0;
    return dynamicNumber;
  }

  async filterAdjustmentAmount(filterOption, filterItemtype, filterItem) {
    await this.page.locator(`div[data-testid="ever-select"]`).nth(0).click();

    const optionLocator = this.page.locator(`div[label="${filterOption}"]`);
    await optionLocator.scrollIntoViewIfNeeded();
    await optionLocator.click();

    if (filterItemtype === "number") {
      const numberInput = this.page.locator("input[type$='number']");
      await numberInput.click();
      await numberInput.fill(filterItem.toString());
      await this.applyFilter();
    } else if (filterItemtype === "dropdown") {
      const dropdownLocator = this.page.locator(
        `.flex > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow .ant-select-selection-search-input`
      );

      for (const item of filterItem) {
        await dropdownLocator.click();
        await dropdownLocator.fill(item);
        await this.page.getByTitle(item).locator("div").click();
      }

      await this.applyFilter();
    } else if (filterItemtype === null) {
      await this.applyFilter();
    } else if (filterOption === "In Between") {
      const numberInputOne = this.page.locator("input[type$='number']").nth(0);
      const numberInputTwo = this.page.locator("input[type$='number']").nth(1);

      await numberInputOne.click();
      await numberInputOne.fill(filterItem[0].toString());

      await numberInputTwo.click();
      await numberInputTwo.fill(filterItem[1].toString());
      await this.applyFilter();
    }
  }

  async filterLineItem(filterOption, filterItemtype, filterItem) {
    await this.page.locator(`div[data-testid="ever-select"]`).nth(1).click();

    const optionLocator = this.page.locator(`div[label="${filterOption}"]`);
    await optionLocator.scrollIntoViewIfNeeded();
    await optionLocator.click();

    if (filterItemtype === "text") {
      const textInput = this.page.locator("input[type='text']");
      await textInput.click();
      await textInput.fill(filterItem.toString());
      await this.applyFilter();
    } else if (filterItemtype === "dropdown") {
      const dropdownLocator = this.page
        .locator(
          ".flex-1 > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow .ant-select-selection-search-input"
        )
        .last();

      for (const item of filterItem) {
        await dropdownLocator.click();
        await dropdownLocator.fill(item);
        await this.page.getByTitle(item).locator("div").click();
      }
      await this.applyFilter();
    } else if (filterItemtype === null) {
      await this.applyFilter();
    } else if (filterOption === "In Between") {
      const numberInputs = this.page.locator("input[type='text']");
      await numberInputs.nth(0).click();
      await numberInputs.nth(0).fill(filterItem[0].toString());

      await numberInputs.nth(1).click();
      await numberInputs.nth(1).fill(filterItem[1].toString());
      await this.applyFilter();
    }
  }

  async clickEffectivePeriodDropdown() {
    await this.page.getByLabel("Effective Period*").click();
  }

  async selectEffectivePeriod(effectivePeriod) {
    await this.page.getByLabel("Effective Period*").click();
    await this.page.getByLabel("Effective Period*").fill(effectivePeriod);
    await this.page.locator(`(//span[@title='${effectivePeriod}'])[1]`).click();
  }

  async clickComponentDropdown() {
    await this.page.getByLabel("Component Name").click();
  }

  async selectReasonCategory(reasonCategory) {
    await this.page.getByLabel("Reason Category").click();
    await this.page.locator(`div[title='${reasonCategory}']`).click();
  }

  async visibleSettingsCta() {
    const isSettingsButtonVisible = await this.page
      .locator(
        "button:right-of(:text('Enable the Approval Workflow for Commission Adjustments, '))"
      )
      .first();

    if (isSettingsButtonVisible) {
      await isSettingsButtonVisible.click();
      console.log("Settings button clicked");
      return true; // Return true if the button is visible and clicked
    } else {
      console.log("Settings button is not visible");
      return false; // Return false if the button is not visible
    }
  }

  async clickSettingsCta() {
    await this.page
      .locator("button:right-of(span a:has-text('take me there'))")
      .first()
      .click();
  }

  async closeFilterview() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Filter$/ })
      .getByRole("button")
      .click();
  }

  async verifySettingsText(customTerminology) {
    const settingsText = await this.page
      .getByText(
        "Assign categories to Commission " +
          customTerminology +
          " that best describe them. " +
          "Utilize our predefined categories or create new ones as required."
      )
      .isVisible();
    return settingsText;
  }

  async addCategory(categoryName) {
    await this.page.getByRole("button", { name: "Add category" }).click();
    await this.page.getByPlaceholder("Enter name").click();
    await this.page.getByPlaceholder("Enter name").fill(categoryName);
    await this.page.getByRole("button", { name: "Create" }).click();
    await this.page.getByText("Category added successfully.").click();
  }

  async addedCategorySuccessPopUp() {
    await this.page
      .getByText(
        'Category renamed successfully. Click "Update" to apply the changes.'
      )
      .isVisible();
  }

  async waitForHiddenPopUp(text) {
    await this.page.getByText(text).waitFor({ state: "hidden" });
  }

  async updateCustomCategory(old_name, updated_adj_name) {
    // Locate the edit button next to the old name
    const editButton = this.page
      .locator(`//span[text()='${old_name}']/../..//button`)
      .first();
    await editButton.click();

    // Clear and fill the updated name in the input field
    const nameInput = this.page.getByPlaceholder("Enter name");
    await nameInput.clear();
    await nameInput.fill(updated_adj_name);

    // Click the Done button
    const doneButton = this.page.getByRole("button", { name: "Done" });
    await doneButton.click();
  }

  async applyCustomCategory() {
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page.waitForLoadState("networkidle");
  }

  async waitForAlert() {
    await this.page.waitForTimeout(2000);
  }

  async saveCustomChanges() {
    const saveChanges = await this.page
      .getByText("Changes saved successfully")
      .isVisible();
    if (saveChanges) {
      return true;
    } else {
      return false;
    }
  }

  async dafaultCategoryCount(category_type) {
    // Locate all spans (categories) and their respective divs
    const categories = await this.page.locator(
      "div.ant-modal-body div[class*='overflow-scroll'] span"
    );
    const values = await this.page.locator(
      "div.ant-modal-body div[class*='overflow-scroll'] span + div"
    );

    // Count the number of "default" values
    const totalItems = await values.count();
    const defaultValues = [];
    let defaultCount = 0;

    // Loop through the values to find and count "default" entries
    for (let i = 0; i < totalItems; i++) {
      const valueText = await values.nth(i).innerText();
      const category = await categories.nth(i).innerText();
      if (valueText.includes(category_type)) {
        defaultCount++;
        defaultValues.push(category);
      }
    }
    // Return the results
    return {
      totalItems,
      defaultCount,
      defaultCategories: defaultValues,
    };
  }

  async verifyEditedCategory(categoryName) {
    const editedCategory = await this.page
      .locator(
        `//div[contains(@class, 'ag-cell-wrapper')]//span[.='${categoryName}']`
      )
      .last()
      .isVisible();
    return editedCategory;
  }

  async archiveCustomcategory(categoryName) {
    const editButton = this.page
      .locator(`//span[text()='${categoryName}']/../..//button`)
      .last();
    await editButton.click();
  }

  async archiveCategoryPopup() {
    await this.page
      .getByText("Are you sure you want to archive this category?")
      .isVisible();
  }

  async archiveSuccessfull() {
    await this.page
      .getByText(
        'Category archived successfully. Click "Update" to apply the changes.'
      )
      .isVisible();
  }

  async archiveLabel(categoryName) {
    const archiveLabel = await this.page
      .locator(`//span[text()='${categoryName}']/..//div/div`)
      .first()
      .textContent();
    console.log(archiveLabel);
    return archiveLabel;
  }

  async unarchiveSuccessfull() {
    await this.page
      .getByText(
        'Category unarchived successfully. Click "Update" to apply the changes.'
      )
      .isVisible();
  }

  async getCategoryButtonCounts() {
    const results = [];
    const items = await this.page.locator('div[draggable="true"]');
    const count = await items.count();

    for (let i = 0; i < count; i++) {
      const item = items.nth(i);
      const isDefault = await item.locator("text=Default").isVisible();
      const buttonCount = await item.locator("button").count();

      results.push({
        index: i,
        category: isDefault ? "Default" : "Custom",
        buttonCount: buttonCount,
      });
    }
    return results;
  }

  async verifyNameInApprovals(username, categoryName) {
    await this.page.getByPlaceholder("Search").click();
    await this.page.getByPlaceholder("Search").fill(username);
    await this.page.waitForTimeout(3000);
    await this.page
      .locator("div[role='presentation'] button:nth-child(1)")
      .click();
    const categoryNameVisible = await this.page
      .getByText(categoryName)
      .nth(1)
      .isVisible();
    if (categoryNameVisible) {
      return true;
    } else {
      return false;
    }
  }

  async clickFilterCTA() {
    await this.page
      .locator('button:left-of(:text("Import / Export")):nth-child(2)')
      .first()
      .click();
  }

  async openFilterDropdown() {
    await this.page
      .locator("div[data-testid='reasonCategory'] div.ant-select-selector")
      .click();
  }

  async fetchFilterOptions() {
    const allElements = await this.page
      .locator(
        'div [data-testid="reasonCategory"] .ant-select-item-option-content span'
      )
      .allTextContents();
    console.log(allElements);
    return allElements;
  }

  async verifyCategoryInPayouts(category_name) {
    await this.page
      .getByRole("row", { name: "Approve Reject" })
      .getByRole("button")
      .first()
      .click();
    await this.page
      .getByRole("button", { name: "Commission adjustments" })
      .click();
    await this.page.waitForTimeout(1000);
    const isCategoryVisible = await this.page
      .getByRole("gridcell", { name: category_name })
      .isVisible();
    if (isCategoryVisible) {
      return true;
    } else {
      return false;
    }
  }

  async closePayoutApproval() {
    await this.page.getByLabel("Close", { exact: true }).click();
  }
}

export default AdjustementsV2Page;
