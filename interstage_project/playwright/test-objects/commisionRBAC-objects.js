import { expect } from "@playwright/test";
const path = require("path");

// Share Page objects
const closebtn = "div.ant-modal-body>div>button>div>svg";

// Commission Plan Page Objects
const Editbtn = "div.ant-drawer-body>div>div>div>div>div>div>button>div>svg";
const shareBtn = "div.ant-drawer-body>div>div>div>div>button>div>svg";

class commissionRBAC {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  /**
   * Navigate to plans / users and Exit if logged in as Impersonate user when given true
   *
   * @param {Boolean} flag
   */
  async navigate(flag, path) {
    if (flag) await this.validateImpersonateLogin();

    await Promise.all([
      this.page.waitForNavigation({ waitUntil: "networkidle" }),
      this.page.goto(path, { waitUntil: "networkidle" }),
    ]);
  }

  /**
   * Share the plan to all users and validate whether the added users are displaying in the shared screen
   *
   * @param {String} planName
   * @returns Total Number of users in Share Screen
   */
  async validateShareScreenDisplay(planID) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share").click();
    await this.page.getByRole("listitem").first().waitFor();
    let NoofSharedUsers = await this.page.getByRole("listitem").count();
    console.log(NoofSharedUsers);
    await this.page.getByRole("listitem").first().waitFor();
    await this.page.getByText("Can view").first().click();
    await this.page.getByRole("menuitem", { name: "Can edit" }).click();
    await this.page.locator("div>[role='combobox']").click();
    await this.page.getByRole("tab").filter({ hasText: "User" }).click();
    await this.page.waitForSelector(
      'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
    );
    const UserCheckboxes = await this.page
      .locator(
        'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
      )
      .all();
    console.log("UserCheckboxes", UserCheckboxes);
    for (const userCheckbox of UserCheckboxes) {
      await userCheckbox.click({ timeout: 5000 });
      NoofSharedUsers++;
    }
    await this.page.getByRole("button", { name: "Add" }).click();
    await this.page
      .getByText("Successfully added members to the shared list")
      .waitFor({ state: "visible" });
    await this.page.getByText("Shared with").waitFor({ state: "visible" });
    return NoofSharedUsers;
  }

  /**
   * User with view Commission plan access will be passed as an argument
   * Validate whether User given access to view all plan is displayed in share screen/not display if Shared plan access is given
   *
   * @param {String} planID
   * @param {String} userName
   */
  async validateViewAccess(planID, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share").click();
    await this.page.getByRole("listitem").first().waitFor();
    expect(
      await this.page
        .locator(`//div[@role="listitem"][.//span[@title="${userName}"]]//span`)
        .last()
        .innerText()
    ).toBe("Can view");

    await expect(this.page.getByText(`${userName}`)).toBeVisible();
    await this.page.locator(closebtn).click();
    await this.page.goto("/settings/user-roles", { waitUntil: "networkidle" });
    await this.page
      .locator("[role='listitem']>div>span")
      .filter({ hasText: "View commision plan" })
      .click();
    await this.page.getByText("Commission Plans").click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page
      .locator("div.ant-select-selector>span")
      .filter({ hasText: "All Plans" })
      .click();
    await this.page.locator("span[title='Shared Plans']").click();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.goto("/plans", { waitUntil: "networkidle" });
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share").click();
    await this.page.getByRole("listitem").first().waitFor();
    await expect(this.page.getByText(`${userName}`)).toBeHidden();
  }

  /**
   *Function to generate a unique 10-letter string based on the current date and time
   *
   * @returns {String}
   */
  async dateTime() {
    const now = new Date();

    // Extract date and time components
    const year = now.getFullYear(); // Get the full year (e.g., 2024)
    const month = now.getMonth() + 1; // Get the month (1 for January, 2 for February, etc.)
    const day = now.getDate(); // Get the day of the month
    const hours = now.getHours(); // Get the hour (0-23)
    const minutes = now.getMinutes(); // Get the minutes (0-59)
    const seconds = now.getSeconds(); // Get the seconds (0-59)

    // Format components as strings, ensuring they are the correct length
    const yearPart = (year % 100).toString().padStart(2, "0"); // Last 2 digits of the year
    const monthPart = month.toString().padStart(2, "0"); // 2-digit month
    const dayPart = day.toString().padStart(2, "0"); // 2-digit day
    const hoursPart = hours.toString().padStart(2, "0"); // 2-digit hours
    const minutesPart = minutes.toString().padStart(2, "0"); // 2-digit minutes
    const secondsPart = seconds.toString().padStart(2, "0"); // 2-digit seconds

    const uniqueString = `${yearPart}${monthPart}${dayPart}${hoursPart}${minutesPart}${secondsPart}`;

    return uniqueString.toUpperCase(); // Convert to uppercase for consistency
  }

  /**
   * Clone a Commision plan with name {userName}_Simple
   *
   * @param {String} planName
   * @param {String} userName
   */
  async cloneCommissionPlan(planID, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("With Payees").click();
    await this.page.locator(Editbtn).click();
    await this.page.locator("#edit_plan_planName").clear();
    const uniqueString = await this.dateTime();
    const CloneplanName = userName.slice(0, 2) + uniqueString;
    await this.page.locator("#edit_plan_planName").fill(CloneplanName);

    // Start waiting for file chooser before clicking. Note no await.
    const fileChooserPromise = this.page.waitForEvent("filechooser");
    await this.page.getByText("Upload", { exact: true }).click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(
      path.join(__dirname, "pdffiles/playwright_contract.pdf")
    );
    await this.page.getByRole("button", { name: "Update" }).click();
    // const url = this.page.url();
    // const urlObject = new URL(url);
    // const CloneplanId = urlObject.searchParams.get('plan_id');
    await this.publishPlan();
    console.log("clone created :", CloneplanName);
    return CloneplanName;
  }

  /**
   * Publish plan
   *
   */
  async publishPlan() {
    await this.page
      .getByRole("button", { name: "Publish", exact: true })
      .click();
    await this.page.waitForSelector(
      "div.ant-modal-confirm-content>div>div>button"
    );
    await this.page
      .locator("div.ant-modal-confirm-content>div>div>button")
      .filter({ hasText: "Publish" })
      .click();
    await this.page
      .getByText("You've successfully published ")
      .waitFor({ state: "visible" });
    await this.page.waitForSelector("button.ant-modal-close");
    const elementSelector = "div.ant-modal-content>button.ant-modal-close";

    while (await this.page.isVisible(elementSelector)) {
      await this.page.keyboard.press("Escape");
      await this.page.waitForTimeout(100);
    }
    await this.page.keyboard.press("Escape");
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  /**
   * Validate whether user has edit access to the plan
   *
   * @param {String} planName
   * @param {String} userName
   */
  async validateEditUser(planID, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share").click();
    await this.page.getByRole("listitem").first().waitFor();
    await this.validateUserRoleinSharescreen(userName, "Can edit");
    await expect(
      this.page.locator("span.text-base").filter({ hasText: userName })
    ).toBeVisible();
    await this.page.waitForSelector(closebtn);
    if (await this.page.locator(closebtn).isVisible()) {
      await this.page.locator(closebtn).click();
    }
  }

  /**
   * Validate whether user is impersonated
   */
  async validateImpersonateLogin() {
    if (
      await this.page
        .getByRole("button", { name: "Exit", exact: true })
        .isVisible({ timeout: 20000 })
    ) {
      await this.page
        .getByRole("button", { name: "Exit", exact: true })
        .click({ timeout: 5000 });
      await this.page
        .getByRole("link")
        .filter({ hasText: "Users" })
        .waitFor({ state: "visible" });
      await this.page.waitForLoadState("networkidle");
    }
  }

  /**
   * Login as user by using user Email
   *
   * @param {String} userEmail
   */
  async loginAsUser(userEmail) {
    await this.searchByNameOrEmail(userEmail);
    await this.impersonate();
  }

  /**
   * Log out Impersonate User
   *
   */
  async logoutUser() {
    await this.page.getByRole("button", { name: "Exit" }).click();
    await this.page
      .getByRole("link")
      .filter({ hasText: "Users" })
      .waitFor({ state: "visible" });
    await this.page.waitForLoadState("networkidle");
  }

  /**
   * Share view access to the user and validate whether added user is displayed in Share screen
   *
   * @param {String} planName
   * @param {String} userName
   */
  async shareViewPlan(planID, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share", { exact: true }).click();
    await this.page.getByRole("listitem").first().waitFor();
    await this.selectOneUser(userName);
    await this.page.getByRole("button", { name: "Add" }).click();
    await this.page
      .getByText("Successfully added members to the shared list")
      .waitFor({ state: "visible" });
    await this.validateUserRoleinSharescreen(userName, "Can view");
    await this.page.locator(closebtn).click();
  }

  /**
   * Share Edit access to the user and validate whether added user is displayed in Share screen
   *
   * @param {String} planName
   * @param {String} userName
   */
  async shareEditPlan(planID, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await this.page.getByText("Share", { exact: true }).click();
    await this.page.getByRole("listitem").first().waitFor();
    await this.page.getByText("Can view").first().click();
    await this.page.getByRole("menuitem", { name: "Can edit" }).click();
    await this.selectOneUser(userName);
    await this.page.getByRole("button", { name: "Add" }).click();
    await this.page
      .getByText("Successfully added members to the shared list")
      .waitFor({ state: "visible" });
    await this.validateUserRoleinSharescreen(userName, "Can edit");
    await this.page.locator(closebtn).click();
  }

  /**
   * Validate whether user has Can View or Can Edit in the Share screen
   *
   * @param {String} userName
   * @param {String} role
   */
  async validateUserRoleinSharescreen(userName, role) {
    await this.page
      .locator("div.ant-select-dropdown")
      .waitFor({ state: "hidden" });
    await this.page.waitForSelector("div[role='listitem']");
    const roleRecieved = await this.page
      .locator(
        `//div[@role="listitem" and .//span[@title="${userName}"]]//span`
      )
      .last()
      .innerText();
    expect(roleRecieved).toBe(role);
  }

  /**
   * Validating whether user has View only access in Commission page
   *
   * @param {String} planName
   */
  async validateOnlyViewAccessPlanPage(planID) {
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeHidden();
    // await expect(this.page.getByRole('menuitem',{name : 'Add to Forecast'})).toBeHidden();
    await this.page.getByText(planID).click();
    await expect(this.page.locator(Editbtn)).toBeHidden();
    await expect(this.page.locator(shareBtn)).toBeHidden();
    await expect(await this.page.getByTitle("Hide Panel")).toBeHidden();
    await expect(await this.page.getByTitle("Show Panel")).toBeHidden();
    await expect(await this.page.getByText("Edit")).toBeHidden();
    await expect(
      await this.page.getByTitle("playwright_contract.pdf")
    ).toBeVisible();
    await this.page.getByTitle("playwright_contract.pdf").click();
    await expect(this.page.locator(".ant-modal-content")).toBeVisible();
    await this.page.locator(".ant-modal-close").click();
    await expect(
      await this.page.getByRole("button", { name: "Time Machine" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Time Machine" }).click();
    await expect(
      await this.page.locator("div.time-machine-drawer")
    ).toBeVisible();
    await this.page.locator(".ant-drawer-close").click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  /**
   * Validating whether user has Edit access in Commission page
   *
   * @param {String} planName
   */
  async validateOnlyEditAccessPlanPage(planID) {
    await this.page.getByPlaceholder("Search by plan name").fill(planID);
    await expect(
      this.page.locator(`[data-testid='pt-actions-${planID}']`)
    ).toBeVisible();
    await this.page.locator(`[data-testid='pt-actions-${planID}']`).click();
    await expect(
      this.page.getByRole("menuitem", { name: "Add to Forecast" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("menuitem", { name: "Share" })
    ).toBeVisible();
    await this.page.getByText(planID).click();
    await expect(this.page.locator(Editbtn)).toBeVisible();
    await expect(this.page.locator(shareBtn)).toBeVisible();
    await expect(this.page.getByTitle("Hide Panel")).toBeVisible();
    await this.page.getByTitle("Hide Panel").click();
    await expect(this.page.getByTitle("Show Panel")).toBeVisible();
    await expect(this.page.getByText("Edit")).toBeVisible();
    await expect(this.page.getByTitle("playwright_contract.pdf")).toBeVisible();
    await this.page.getByTitle("playwright_contract.pdf").click();
    await expect(this.page.locator(".ant-modal-content")).toBeVisible();
    await this.page.locator(".ant-modal-close").click();
    await expect(
      this.page.getByRole("button", { name: "Time Machine" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Time Machine" }).click();
    await expect(this.page.locator("div.time-machine-drawer")).toBeVisible();
    await this.page.locator(".ant-drawer-close").click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  /**
   * Remove Edit Access for the role
   *
   * @param {String}
   *
   */
  async removeEditAccess(RoleName) {
    await this.page.getByText(RoleName).click();
    await this.page.getByText("Commission Plans", { exact: true }).click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page.waitForSelector("label.ant-checkbox-wrapper");
    const labelElements = await this.page.locator("label.ant-checkbox-wrapper");
    // console.log(labelElements.count())
    for (let i = 0; i < (await labelElements.count()); i++) {
      console.log(
        await labelElements
          .nth(i)
          .locator("span>>div>div.ant-space-item>label>span")
          .innerText()
      );
      if (
        (await labelElements
          .nth(i)
          .locator("span>div>div.ant-space-item>label>span")
          .innerText()) === "Edit commission plans"
      ) {
        while (
          await labelElements
            .nth(i)
            .locator("span.ant-checkbox-checked")
            .isVisible()
        ) {
          await labelElements.nth(i).click();
        }
        console.log(i);
        break;
      }
    }
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async removeViewAccess(RoleName) {
    await this.page.getByText(RoleName).click();
    await this.page.getByText("Commission Plans", { exact: true }).click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page.waitForSelector("label.ant-checkbox-wrapper");
    const labelElements = await this.page.locator("label.ant-checkbox-wrapper");
    // console.log(labelElements.count())
    for (let i = 0; i < (await labelElements.count()); i++) {
      console.log(
        await labelElements
          .nth(i)
          .locator("span>>div>div.ant-space-item>label>span")
          .innerText()
      );
      if (
        (await labelElements
          .nth(i)
          .locator("span>div>div.ant-space-item>label>span")
          .innerText()) === "View commission plans"
      ) {
        while (
          await labelElements
            .nth(i)
            .locator("span.ant-checkbox-checked")
            .isVisible()
        ) {
          await labelElements.nth(i).click();
        }
        console.log(i);
        break;
      }
    }

    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async openShareModal(clonePlanName) {
    await this.page.getByPlaceholder("Search by plan name").fill(clonePlanName);
    await this.page.getByTestId(`pt-actions-${clonePlanName}`).click();
    await this.page.getByText("Share").click();
  }

  async DeletePlan(clonePlanName) {
    await this.page.getByTestId(`pt-actions-${clonePlanName}`).click();
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    await this.page.getByRole("button", { name: "Yes, delete" }).click();
  }

  async verifyDeletePlanMessage(cloneplanName) {
    await this.page
      .getByText(`${cloneplanName} deleted successfully!`)
      .first()
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async selectEditAccess() {
    await this.page.getByText("Can view").click();
    await this.page.getByRole("menuitem", { name: "Can edit" }).click();
  }

  async selectUsersForAccess(numberOfUsers) {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .nth(1)
      .click({ timeout: 5000 });
    await this.page
      .getByRole("tab")
      .filter({ hasText: "User" })
      .click({ timeout: 5000 });
    await this.page.waitForSelector(
      'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
    );
    const userCheckboxes = await this.page.locator(
      'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
    );
    console.log("userCheckboxes", userCheckboxes);
    for (let i = 0; i < numberOfUsers; i++) {
      await userCheckboxes.nth(i).click({ timeout: 5000 });
      console.log("userCheckbox checked:", userCheckboxes.nth(i));
    }
  }

  async selectAllUsersForViewAccess() {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .nth(1)
      .click({ timeout: 5000 });
    await this.page
      .getByRole("tab")
      .filter({ hasText: "User" })
      .click({ timeout: 5000 });
    await this.page.waitForSelector(
      'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
    );
    const userCheckboxes = await this.page
      .locator(
        'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
      )
      .all();
    console.log("userCheckboxes", userCheckboxes);
    for (const userCheckbox of userCheckboxes) {
      await userCheckbox.click({ timeout: 5000 });
      console.log("userCheckbox checked:", userCheckbox);
    }
  }

  async selectGroupForViewAccess(groupName) {
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page.getByRole("tab", { name: "Group" }).click();
    await this.page.waitForTimeout(2000);
    await this.page.getByText(groupName, { exact: true }).last().click();
  }

  async clickAddBtn() {
    await this.page.getByRole("button", { name: "Add" }).click();
  }

  async verifySharedSuccessMessage() {
    await this.page
      .getByText("Successfully added members to the shared list")
      .waitFor({ state: "visible" });
    await this.page.waitForSelector(
      "span[class$='truncate font-medium text-ever-base-content']"
    );
  }

  async getSharedUsers() {
    return await this.page
      .locator("span[class$='truncate font-medium text-ever-base-content']")
      .allInnerTexts();
  }

  async selectOneUser(user) {
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page
      .locator(
        "div.ant-select-selection-search input.ant-select-selection-search-input"
      )
      .fill(`${user} `);
    await this.page.waitForTimeout(3000);
    await this.page
      .locator(
        "div.ant-select-selection-search input.ant-select-selection-search-input"
      )
      .press(" ");
    await this.page.getByTitle(user).click();
  }

  async searchByNameOrEmail(nameOrEmail) {
    await this.searchUserLocator.click();
    await this.page
      .getByPlaceholder("Search by name or email", { exact: true })
      .fill(nameOrEmail);
    await this.page.waitForTimeout(5000);
  }

  async impersonate() {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page.waitForLoadState("networkidle");
    await this.page
      .getByText("Logged in as ")
      .first()
      .waitFor({ stats: "visible" });
  }

  async selectUsersFromAllSection(numberOfUsers) {
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page.getByRole("tab", { name: "All" }).click();
    const userCheckAllboxes = await this.page.locator(
      'div[role="tabpanel"] div[role="listitem"] span[title]:has(label)'
    );

    for (let i = 0; i < numberOfUsers; i++) {
      await userCheckAllboxes.nth(i).click();
    }
  }

  async clickCommissionPlan(cloneplanName) {
    await this.page.getByText(cloneplanName).click();
  }

  async EditCommissionPlan(cloneplanName) {
    await this.page
      .locator("div.ant-drawer-body>div>div>div>div>div>div>button>div>svg")
      .click();
    await this.page.getByPlaceholder("Enter name").click();
    await this.page
      .getByPlaceholder("Enter name")
      .fill(`${cloneplanName}_Edited`);
    await this.page.waitForTimeout(4000);
    await this.page.getByRole("button", { name: "Update" }).click();
  }

  async clickExitCanvas() {
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async getPlanNames() {
    return await this.page
      .locator("div.rounded-xl span[title]")
      .allInnerTexts();
  }

  async getCommissionPlanName() {
    return await this.page
      .locator("span[class='text-lg font-semibold !font-[IBM Plex Sans]']")
      .innerText();
  }

  async ExitImpersonation() {
    await this.page.getByRole("button", { name: "Exit", exact: true }).click();
    await this.page.waitForTimeout(3000);
  }

  async EditGroup(groupName, payeeName) {
    await this.page.locator(`[data-test-id="group-${groupName}"]`).click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Euadmin EuadminEuropepayee1" })
      .first()
      .click();
    await this.page.getByText(payeeName).nth(2).click();
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page
      .getByText(`${groupName} updated`)
      .waitFor({ state: "visible" });
  }

  async RevertEditGroup(groupName, payeeName) {
    await this.page.locator(`[data-test-id="group-${groupName}"]`).click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Euadmin EuadminEuropepayee1" })
      .first()
      .click();
    await this.page.getByText(payeeName).click();
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page
      .getByText(`${groupName} updated`)
      .waitFor({ state: "visible" });
  }

  async getNoPlansSharedMessage() {
    return await this.page
      .locator("span[class='text-xl font-semibold !font-[IBM Plex Sans]']")
      .innerText();
  }

  async getGroupNames() {
    return await this.page
      .locator("div.ml-2 span.text-ever-base-content")
      .allInnerTexts();
  }

  async saveEditSharedPlanPermission() {
    await this.page
      .locator("span[title]>span")
      .filter({ hasText: "view and edit shared plans" })
      .click();
    await this.page.getByText("Commission Plans").click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async shareAccess(planName, access, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planName);
    await this.page
      .locator(`span[title="${planName}"]`)
      .waitFor({ state: "visible" });
    await this.page.getByTestId(`pt-actions-${planName}`).click();
    await this.page.getByText("Share").click();
    const checkAddUser = async () => {
      await this.page
        .getByRole("dialog")
        .getByTestId("ever-select")
        .locator("div")
        .nth(1)
        .click();
      await this.page.getByRole("tab", { name: "User" }).click();
      await this.page.getByText("Can view").first().click();
      await this.page.getByRole("menuitem", { name: access }).click();
      await this.page
        .getByRole("dialog")
        .getByTestId("ever-select")
        .locator("div")
        .nth(1)
        .click();
      await this.page.getByText(userName).click();
      await this.page
        .getByRole("button", { name: "Add" })
        .click({ timeout: 5000 });
    };
    try {
      await checkAddUser();
    } catch {
      await checkAddUser();
    }
  }

  async EdittoView(planName, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planName);
    await this.page.getByTestId(`pt-actions-${planName}`).click();
    await this.page.getByText("Share").click();
    await this.page
      .locator("div")
      .filter({ hasText: userName })
      .locator("svg")
      .click();
    await this.page
      .getByRole("menuitem", { name: "Can view" })
      .locator("span")
      .click();
  }

  async ViewtoEdit(planName, userName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planName);
    await this.page.getByTestId(`pt-actions-${planName}`).click();
    await this.page.getByText("Share").click();
    await this.page
      .locator("div")
      .filter({ hasText: userName })
      .locator("svg")
      .click();
    await this.page
      .getByRole("menuitem", { name: "Can edit" })
      .locator("span")
      .click();
  }

  async editPlanName(currentName, editName) {
    // await this.page
    //   .locator(
    //     "(//*[name()='svg'][@class='!w-4 !h-4 m-auto text-ever-base-content-mid h-full w-full'])[1]"
    //   )
    //   .click();
    await this.page.waitForSelector(
      `//div[span[text()='${currentName}']]/following-sibling::div/button`,
      { state: "visible" }
    );
    await this.page
      .locator(
        `//div[span[text()='${currentName}']]/following-sibling::div/button`
      )
      .click();
    await this.page.locator("#edit_plan_planName").click();
    await this.page.getByPlaceholder("Enter name").fill(editName);
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async checkTimeMachine() {
    await expect(this.page.locator("share-button")).toBeHidden();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async shareButtonInCanvas() {
    await expect(
      this.page
        .locator("div")
        .filter({ hasText: /^Time MachineExit Canvas$/ })
        .getByRole("button")
        .nth(1)
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
    await this.page.getByRole("button", { name: "Exit" }).click();
  }

  async removeSharing(planName) {
    await this.page.getByPlaceholder("Search by plan name").fill(planName);
    await this.page.getByTestId(`pt-actions-${planName}`).click();
    await this.page.getByText("Share").click();
    await this.page.locator("span.text-ever-info svg").first().click();
    await this.page.getByText("Remove", { exact: true }).click();
    await this.page
      .getByText("Successfully removed the member")
      .waitFor({ state: "visible" });
  }

  async yearOfPlan(currentYear) {
    // await this.page
    //   .getByTestId("pt-fiscal-year-select")
    //   .getByTitle(currentYear)
    //   .click();
    await this.page.getByTestId("pt-fiscal-year-select").click();
    await this.page.locator(`div[title="2024"]`).last().click();
  }

  async impersonateAsUser(userEmail) {
    await this.page.goto("/users");
    await this.page.getByTestId(`${userEmail} users dd button`).click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page.waitForTimeout(2000);
  }

  async navigation(path) {
    await this.page.goto(path, { waitUntil: "networkidle" });
  }

  /**
   * Clone a Simple Plan and change the datasheet configuration
   *
   * @param {String} planName - plan which needs to be cloned
   * @param {String} userName - Any Name for unique plan Name
   * @param {String} componentName - Component name for which configuration needed to changes
   * @param {String} databookName - Databook which need to be changed
   * @param {String} datasheetName - DatasheetName which need to be changed
   * @param {String} emailField - Email which need to be changed
   * @param {String} dateField - Datefield which need to be changed
   * @param {String} formula - Formula which need to be changed
   * @returns - {String} - Cloned plan name
   */
  async cloneSimplePlan(
    planName,
    userName,
    componentName,
    databookName,
    datasheetName,
    emailField,
    dateField,
    formula
  ) {
    await this.page.locator(`[data-testid='pt-actions-${planName}']`).click();
    await this.page.getByText("With Payees").click();
    await this.page.locator(Editbtn).click();
    await this.page.locator("#edit_plan_planName").clear();
    const uniqueString = await this.dateTime();
    const CloneplanName = userName.slice(0, 2) + uniqueString;
    await this.page.locator("#edit_plan_planName").fill(CloneplanName);
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page
      .locator(".relative > .p-4")
      .getByText(componentName)
      .click();
    await this.page.getByRole("button", { name: "Configuration" }).click();
    await this.changeComponentConfiguration("Databook", databookName);
    await this.changeComponentConfiguration("Datasheet", datasheetName);
    await this.changeComponentConfiguration("Email field", emailField);
    await this.changeComponentConfiguration("Date field", dateField);
    await this.enterFomula(formula);
    await this.publishPlan();
    return CloneplanName;
  }

  /**
   * Change Configuration of component based on option and its value
   * @param {String} option
   * @param {String} optionValue
   */
  async changeComponentConfiguration(option, optionValue) {
    await this.page
      .locator(`[data-testid='ever-select']:below(:text('${option}'))`)
      .first()
      .click();
    await this.page.getByText(optionValue, { exact: true }).click();
    if (await this.page.getByRole("button", { name: "Confirm" }).isVisible()) {
      await this.page.getByRole("button", { name: "Confirm" }).click();
    }
  }

  /**
   * Split the incoming string by " " and enter the formula in plan page
   * @param {String} formula
   */
  async enterFomula(formula) {
    for (const formulaComponent of formula.split(" ")) {
      await this.page
        .getByPlaceholder("Press Ctrl + H for help")
        .fill(formulaComponent);
      await this.page.keyboard.press("Enter");
    }
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Plan Component details saved successfully", { exact: true })
      .waitFor({ state: "visible" });
  }
}

module.exports = commissionRBAC;
