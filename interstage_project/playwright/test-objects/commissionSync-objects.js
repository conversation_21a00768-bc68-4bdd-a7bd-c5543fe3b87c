import { expect } from "@playwright/test";

class CommissionSync {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async closeExpandedBtns() {
    await this.page.waitForFunction(() => {
      const elements = document.querySelectorAll("div.ant-collapse-header");
      return elements.length === 6;
    });
    const buttons = await this.page.locator("div.ant-collapse-header").all();
    console.log(buttons);

    for (const button of buttons) {
      const ariaExpanded = await button.getAttribute("aria-expanded");
      if (ariaExpanded === "true") {
        console.log("Button expanded, closing it");
        // Close the button if it is expanded
        await button.click();
      }
    }
  }

  async action(actionName) {
    await this.closeExpandedBtns(); // Ensure all buttons are closed
    await this.closeExpandedBtns(); // Ensure all buttons are closed again

    switch (actionName) {
      case "Calculate Commissions Run":
        await this.page
          .getByRole("button", { name: "Calculate Commissions Run" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate commissions`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Calculate Settlements Run":
        await this.page
          .getByRole("button", { name: "Calculate Settlements Run" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate settlements`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Commission Forecasting":
        await this.page
          .getByRole("button", { name: "Commission Forecasting" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate commission forecast`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Refresh Databooks Uploaded":
        await this.page
          .getByRole("button", { name: "Refresh Databooks Uploaded" })
          .click();
        break;
      case "Sync Data from Connectors":
        await this.page
          .getByRole("button", { name: "Sync Data from Connectors" })
          .click();
        await this.page
          .locator("label")
          .filter({ hasText: "Run only upstream" })
          .waitFor({ state: "visible" });
        break;
      case "Report ETL Run ETL for report":
        await this.page
          .getByRole("button", { name: "Report ETL Run ETL for report" })
          .click();
        await this.page
          .locator("label")
          .filter({ hasText: "Run for all payees and periods" })
          .waitFor({ state: "visible" });
        break;
      default:
        console.log("Input did not match any given case.");
        break;
    }
  }

  async verifyText(text) {
    await expect(this.page.getByText(text)).toBeVisible();
  }

  async checkPayeesInCommissionPlanOption() {
    await this.page
      .getByRole("radio", { name: "Payees in Commission Plan" })
      .check();
  }

  async checkUpstreamSyncOption() {
    await this.page.getByLabel("Run only upstream sync").check();
    await this.page.waitForTimeout(1000);
  }

  async checkAllConnectorsOption() {
    await this.page.getByLabel("All Connectors").check({ timeout: 1000 });
    await this.page.waitForTimeout(2000);
  }

  async checkSelectedConnectorsOption(connectorName) {
    await this.page.getByLabel("Selected Connectors").check();
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select connectors" })
      .click();
    await this.page.getByText(connectorName).click();
    await this.page.waitForTimeout(2000);
  }

  async selectPlansCommissionSync(plans) {
    // Click the "Choose Commission Plans" dropdown
    const planDropdown = this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Choose Commission Plans" });
    await planDropdown.waitFor({ state: "visible" });
    await planDropdown.click();

    for (const plan of plans) {
      const input = this.page
        .getByTestId("ever-select")
        .locator("div input.ant-select-selection-search-input");

      await input.fill(""); // Clear the field
      await input.type(plan, { delay: 100 }); // Optional delay for realism

      const option = this.page
        .getByRole("listitem")
        .getByText(plan, { exact: true });
      await option.waitFor({ state: "visible" });
      await option.click();
    }

    // Verify all selected plans are visible in the dropdown area
    for (const plan of plans) {
      await expect(
        this.page.getByTestId("ever-select").getByText(plan, { exact: true })
      ).toBeVisible();
    }
  }

  async selectAllPayeesinCommissionPlan() {
    await this.page.getByLabel("Select All").check();
    await this.page.waitForTimeout(3000);
    await this.page.getByRole("button", { name: "Add Payees" }).click();
    await this.page.waitForTimeout(3000);
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async searchUser(email) {
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(4000);
  }

  async clickShowPastActivities() {
    await this.page.getByText("show past activities").click();
  }

  async closeShowPastActivities() {
    await this.page.getByLabel("Close").click();
  }

  async checkRefreshdatabooks(plan) {
    await this.page
      .getByLabel(`Refresh databooks & then calculate ${plan}`)
      .check({ timeout: 5000 });
  }

  async checkAllPayeesOption() {
    await this.page.getByRole("radio", { name: "All Payees" });
  }

  async checkSelectedPayeesOption() {
    await this.page
      .locator("div.ant-collapse-item-active input[value$='selected-payees']")
      .check();
  }

  async selectPayeeSelectedPayeesOption(payeeName) {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select payees" })
      .click();
    await this.page
      .getByTestId("ever-select")
      .locator("div input.ant-select-selection-search-input")
      .type(payeeName);
    await this.page.getByRole("listitem").getByText(payeeName).click();
    await this.checkSelectedPayeesOption();
  }

  async clickDetailedView() {
    await this.page.getByText("Show Details").waitFor({ state: "visible" });
    await this.page
      .locator("//div[text()='Show Details']")
      .click({ timeout: 5000 });
  }

  async verifyReportSyncJobTotal(number) {
    await this.page
      .locator(
        `[data-test-id="Report Sync (commission object)-Completed-${number}"]`
      )
      .waitFor({ state: "visible" });
  }

  async verifyReportSyncJobFinish(number) {
    await this.page
      .locator(
        `[data-test-id="Report Sync (commission object)-Total system tasks-${number}"]`
      )
      .waitFor({ state: "visible" });
  }

  async closeShowDetails() {
    const closeButton = this.page.getByRole("button", { name: "Close" }).last();
    await closeButton.click();
  }

  async selectCriteria(criteria) {
    const criteriaLocator = this.page.locator(
      `.ant-collapse-content-active input[value="${criteria}"]`
    );
    await criteriaLocator.click();
  }

  async selectDropdown(dropdownList) {
    const InputSelector = this.page.locator(
      ".ant-collapse-content-active .ant-select-selection-overflow input"
    );
    await InputSelector.click();

    for (const option of dropdownList) {
      await InputSelector.fill(option);
      await this.page.locator(`div[title="${option}"]`).click();
    }
    await this.page.keyboard.press("Tab");
  }

  async selectObject(object) {
    await this.page
      .getByText(
        `All datasheets associated with the selected objects will be refreshed`
      )
      .waitFor({ state: "visible", timeout: 5000 });

    const InputSelector = this.page.locator(
      ".ant-collapse-content-active .ant-select-selection-item"
    );
    await InputSelector.click();
    await this.page.locator(`div[title="${object}"]`).click();
    await this.page.keyboard.press("Tab");
  }

  async selectDate(date) {
    const dateLocator = this.page.locator(
      ".ant-collapse-item-active input[placeholder='Select date']"
    );

    await dateLocator.click();
    await dateLocator.fill(date);
    await dateLocator.press("Enter");
  }

  async runETLReportforAllpayeesAndPeriod(reportObject) {
    await this.page.getByTestId("ever-select").click();
    await this.page.locator(`div[label='${reportObject}']`).click();
    await this.page.getByRole("switch").click();
  }

  async selectMonth() {
    const monthDropdown = this.page.getByPlaceholder("Select month").last();

    // Wait for the dropdown to be visible before clicking
    await monthDropdown.waitFor({ state: "visible", timeout: 5000 });
    await monthDropdown.click();

    // Wait for the year button and click
    const yearButton = this.page.getByRole("button", { name: "2025" });
    await yearButton.waitFor({ state: "visible", timeout: 5000 });
    await yearButton.click();

    // Click on 2024 and May (assuming these elements appear after selecting 2025)
    await this.page
      .getByText("2024")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText("2024").click();

    await this.page
      .getByText("May")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByText("May").click();
  }
}

export default CommissionSync;
