import { expect } from "@playwright/test";
class CommissionsSyncPrevPeriod {
  constructor(page) {
    this.page = page;
  }

  async navigate(path) {
    await this.page.goto(path, { waitUntil: "networkidle" });
    await this.page.waitForTimeout(5000);
  }

  async selectPayees(payee) {
    await this.page
      .locator(
        "div[class$='ant-collapse-content-active'] input[value$='selected-payees']"
      )
      .check();
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page
      .getByRole("listitem")
      .getByText(payee, { exact: true })
      .click();
  }

  // Helper to convert '30 Jun 2024' to '2024-06-30'
  _convertToISODate(dateStr) {
    const [day, mon, year] = dateStr.trim().split(" ");
    const months = {
      Jan: "01",
      Feb: "02",
      Mar: "03",
      Apr: "04",
      May: "05",
      Jun: "06",
      Jul: "07",
      Aug: "08",
      Sep: "09",
      Oct: "10",
      Nov: "11",
      Dec: "12",
    };
    return `${year}-${months[mon]}-${day.padStart(2, "0")}`;
  }

  async selectDate(date) {
    await this.page
      .getByRole("textbox", { name: "Select date" })
      .first()
      .click();
    // Convert input date to ISO format
    const isoDate = this._convertToISODate(date);
    const [targetYear, targetMonth, targetDay] = isoDate.split("-").map(Number);

    // Get current displayed year and month from the picker
    const currentYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const currentMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const monthMap = {
      Jan: 1,
      Feb: 2,
      Mar: 3,
      Apr: 4,
      May: 5,
      Jun: 6,
      Jul: 7,
      Aug: 8,
      Sep: 9,
      Oct: 10,
      Nov: 11,
      Dec: 12,
    };
    const currentMonth = monthMap[currentMonthName.trim()];

    // Year navigation (reuse existing logic)
    if (targetYear < currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    } else if (targetYear > currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    }

    // Refresh currentMonth/currentYear after year navigation
    const displayedYear = parseInt(
      await this.page.locator(".ant-picker-year-btn").textContent()
    );
    const displayedMonthName = await this.page
      .locator(".ant-picker-month-btn")
      .textContent();
    const displayedMonth = monthMap[displayedMonthName.trim()];

    // Month navigation
    if (targetYear === displayedYear) {
      if (targetMonth < displayedMonth) {
        for (let i = 0; i < displayedMonth - targetMonth; i++) {
          await this.page.locator(".ant-picker-header-prev-btn").click();
        }
      } else if (targetMonth > displayedMonth) {
        for (let i = 0; i < targetMonth - displayedMonth; i++) {
          await this.page.locator(".ant-picker-header-next-btn").click();
        }
      }
      // else: same month, do nothing
    }

    console.log(isoDate);
    // Click the date cell
    await this.page.locator(`td[title='${isoDate}']`).click();
  }

  /**
   * Click an btnSelector - until elementSelector is visible
   *
   * @param {String} btnSelector
   * @param {String} elementSelector
   * @param {BigInteger} timeout
   * @param {BigInteger} clickDelay
   * @returns
   */
  async clickUntilVisible(
    btnSelector,
    elementSelector,
    timeout = 30000,
    clickDelay = 1000
  ) {
    const startTime = Date.now();
    while (true) {
      try {
        if (Date.now() - startTime > timeout) {
          console.log("Timeout reached! Element is still not visible.");
          return false;
        }
        await this.page.locator(btnSelector).click();
        console.log("Clicked the element, waiting for visibility...");
        const isVisible = await this.page.isVisible(elementSelector);
        if (isVisible) {
          console.log("Element is now visible!");
          return true;
        }
        console.log("Element is not visible, retrying...");
      } catch (error) {
        console.log("Error while trying to click:", error);
      }

      await this.page.waitForTimeout(clickDelay);
    }
  }

  async selectDateinCommissions(date) {
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").fill("");
    await this.page.waitForTimeout(3000);
    await this.page.getByPlaceholder("Select date").fill(date);
    await this.page.getByPlaceholder("Select date").press("Enter");
  }

  async runCommissions() {
    const locator = await this.page
      .locator("button:not([disabled])", {
        hasText: "Run",
      })
      .first();
    await expect(locator).toBeEnabled();
    await this.page
      .locator("button:not([disabled])", { hasText: "Run" })
      .first()
      .click();
  }

  async clickSkipandRun() {
    const skipAndRunButton = this.page.getByRole("button", {
      name: "Skip & Run",
    });

    if (await skipAndRunButton.isVisible({ timeout: 10000 })) {
      await skipAndRunButton.click();
    } else {
      console.log("'Skip & Run' button is not visible.");
    }
  }

  async waitForCalculationMessage() {
    await this.page
      .getByText("Calculating Commissions...")
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async waitForCommissionsSuccess() {
    await this.page
      .getByText("Commission Calculations Completed")
      .waitFor({ state: "visible", timeout: 600000 });
  }

  async waitForForecastCalculationMessage() {
    await this.page
      .getByText("Calculating Commissions forecast...")
      .waitFor({ state: "visible", timeout: 90000 });
  }

  async waitForForecastSuccess() {
    await this.page
      .getByText("Commission Forecast Calculations Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async waitForSettlementCalculationMessage() {
    await this.page
      .getByText("Calculating Settlements...")
      .waitFor({ state: "visible", timeout: 90000 });
  }

  async waitForSettlementSuccess() {
    await this.page
      .getByText("Settlement Calculations Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async waitForRefreshMessage() {
    await this.page
      .getByText("Refreshing Databooks...")
      .waitFor({ state: "visible", timeout: 90000 });
  }

  async waitForRefreshSuccess() {
    await this.page
      .getByText("Databook Refresh Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async waitForE2ECalculationMessage() {
    await this.page
      .getByText("Syncing Connectors...")
      .waitFor({ state: "visible", timeout: 90000 });
  }

  async waitForE2ESuccess() {
    await this.page
      .getByText("End to End Data Sync Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async waitForReportETLCalculationMessage() {
    await this.page
      .locator("//span[text()='Running Report ETL...']")
      .waitFor({ state: "visible", timeout: 90000 });
  }

  async waitForReportETLSuccess() {
    await this.page
      .getByText("ETL For Report Object Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async waitForUpstreamSuccess() {
    await this.page
      .getByText("Upstream Sync Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  async clickUser(user) {
    await this.page.getByRole("link", { name: user }).click();
  }

  async selectPeriod(period) {
    await this.page.getByTestId("ever-select").getByText(period).click();
    await this.page.waitForTimeout(3000);
  }

  async getPeriods() {
    return await this.page
      .locator("div.ant-select-item-option span.inline-block")
      .allInnerTexts();
  }

  async waitForDatasheetLoad() {
    await this.page.waitForSelector(
      "div[class$='ag-center-cols-container'] div[col-id$='period_label']"
    );
    await this.page.waitForTimeout(3000);
  }

  async getPeriodsFromDatasheet() {
    return await this.page
      .locator(
        "div[class$='ag-center-cols-container'] div[col-id$='period_label']"
      )
      .allInnerTexts();
  }

  async getDateOfSalesQuar() {
    return await this.page
      .locator(
        "div[class$='ag-center-cols-container'] div[col-id$='co_3_date_of_sale']"
      )
      .allInnerTexts();
  }

  async getDateOfSalesMonthly() {
    return await this.page
      .locator(
        "div[class$='ag-center-cols-container'] div[col-id$='co_1_date_of_sale']"
      )
      .allInnerTexts();
  }

  async getDateOfSalesHalf() {
    return await this.page
      .locator(
        "div[class$='ag-center-cols-container'] div[col-id$='co_4_date_of_sale']"
      )
      .allInnerTexts();
  }

  async clickPlan(plan) {
    await this.page.getByText(plan).click();
  }

  async clickCriteria(criteria) {
    await this.page.getByRole("button", { name: criteria }).click();
  }

  async getAllColumns() {
    await this.page.getByText("Customize component columns").click();
    await this.page.getByLabel("Select all").nth(1).check();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async getSuccessMessageColumnSave() {
    await this.page
      .getByText("Statement columns updated")
      .first()
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async PrevPeriodCheck() {
    await this.page.getByLabel("Run commission sync for the").check();
  }

  async refreshDatabookForecastCheck() {
    await this.page
      .getByLabel("Refresh databooks & then calculate commission forecast")
      .check();
  }

  async PrevPeriodForecastCheck() {
    await this.page.getByLabel("Run commissions forecast sync").check();
  }

  async clickPrevPeriod(period) {
    await this.page.getByText(period).click();
  }

  async clickPrevPeriodSettlement(prevPeriod) {
    await this.page.getByText(prevPeriod).nth(1).click();
  }

  async clickPeriod(period) {
    await this.page.getByText(period).click();
  }

  async closeStatementsBtn() {
    await this.page.locator("button.ant-drawer-close").click();
  }

  async clickCalculateCommissionsRun() {
    await this.page
      .getByRole("button", { name: "Calculate Commissions Run" })
      .click();
  }

  async clickE2ERun() {
    await this.page
      .getByRole("button", { name: "Sync Data from Connectors" })
      .click();
  }

  async clickCommissionForecastingRun() {
    await this.page
      .getByRole("button", { name: "Commission Forecasting Run" })
      .click();
  }

  async clickDatabook(dataBook) {
    await this.page.getByRole("link", { name: dataBook }).click();
  }

  async clickPrevPeriodE2E() {
    await this.page
      .getByRole("checkbox", { name: "Run commission sync for the" })
      .first()
      .check();
  }

  async clickDetailedView() {
    await this.page
      .getByTestId("login-indicator")
      .getByText("Show Details")
      .click();
    await this.page.waitForTimeout(2000);
  }

  async getDetailedViewList() {
    return await this.page
      .locator(
        "div[class$='ant-modal-content'] span[class$='font-semibold !font-[IBM Plex Sans] type']"
      )
      .allInnerTexts();
  }

  async selectETLReport(reportName) {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Choose report object" })
      .click();
    await this.page.getByText(reportName, { exact: true }).click();
  }
}

export default CommissionsSyncPrevPeriod;
