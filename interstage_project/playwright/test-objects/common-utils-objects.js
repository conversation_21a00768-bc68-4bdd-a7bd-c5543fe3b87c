const { expect } = require("@playwright/test");
import GlobalSearch from "./globalSearch-objects";
class CommonUtils {
  constructor(page) {
    this.page = page;
  }

  /**
   * This function is to wait for grid/Table to change its state from initial selector to target selector
   *
   * @param {String} initialSelector
   * @param {String} targetSelector
   */
  async waitForLazyload(initialSelector, targetSelector) {
    await this.page.locator(initialSelector).waitFor({
      state: "attached",
      timeout: 10000,
    });
    await this.page.locator(initialSelector).waitFor({
      state: "detached",
      timeout: 10000,
    });
    await this.page.locator(targetSelector).waitFor({ state: "visible" });
  }

  /**
   * Navigates to the approval request drawer based on the specified status.
   *
   * This method iterates through all approval status cells in the grid, checks if the status matches the provided status,
   * and if so, clicks on the corresponding drawer to navigate to it.
   *
   * @param {String} status - The status of the approval request to navigate to.
   */
  async navigateToApprovalRequestDrawer(status) {
    const approvalStatusLocators = await this.page
      .locator("[col-id='approval_status'].ag-cell")
      .all();
    for (const approvalStatusLocator of approvalStatusLocators) {
      const elements = approvalStatusLocator.locator("div svg+div");
      const elementCount = await elements.count();
      if (elementCount > 0) {
        if (
          (await approvalStatusLocator.locator("div svg+div").innerText()) ===
          status
        ) {
          await approvalStatusLocator.locator("div.cursor-pointer").click();
          break;
        }
      }
    }
  }

  /**
   * Clicks an element based on the provided target and options.
   *
   * @param {(string|Locator)} target - The target to click. Can be a string or a Locator object.
   * If a string, it can start with "button=", "text=", "selector=", or "placeholder=" to specify the type of target.
   * @param {Object} options - Optional configuration object.
   * @param {boolean} options.wait - If true, waits for the page to reach network idle after clicking.
   * @param {number} options.timeoutValue - Timeout in ms for network idle wait (default: 10000).
   * @param {number} options.nth - The nth occurrence of the target to click (default: 0).
   * @example
   * // Clicks the first button with the text "Save"
   * await commonUtils.clickBy("text=Save");
   * @example
   * // Clicks the second button with the text "Save"
   * await commonUtils.clickBy("text=Save", { nth: 1 });
   * @example
   * // Clicks the element with the selector "#save-button"
   * await commonUtils.clickBy("selector=#save-button");
   * @example
   * // Clicks the button with the text "Save" and waits for network idle
   * await commonUtils.clickBy("text=Save", { wait: true });
   * @example
   * // Clicks the first button with the name "Save"
   * await commonUtils.clickBy("Role-button=Save");
   * @example
   * // Clicks the second button with the name "Save"
   * await commonUtils.clickBy("Role-button=Save", { nth: 1 });
   * @example
   * // Clicks the element with the locator object
   * await commonUtils.clickBy(locatorObject);
   */
  async clickBy(target, options = {}) {
    const { wait = false, timeoutValue = 10000, nth = 0 } = options;
    let elementLocator;
    let prefix;

    if (typeof target === "string") {
      const [firstPart, ...rest] = target.split("=");
      prefix = firstPart;
      const value = rest.join("=").trim();
      if (prefix.includes("Role")) {
        elementLocator = this.page
          .getByRole(prefix.substring(5), { name: value, exact: true })
          .nth(nth);
      } else if (prefix === "text") {
        elementLocator = this.page.getByText(value, { exact: true }).nth(nth);
      } else if (prefix === "selector") {
        elementLocator = this.page.locator(value).nth(nth);
      } else if (prefix === "placeholder") {
        elementLocator = this.page
          .getByPlaceholder(value, { exact: true })
          .nth(nth);
      } else {
        throw new Error(`Unsupported target prefix: ${prefix}`);
      }
    } else {
      elementLocator = target.nth(nth);
    }

    if (prefix !== "placeholder") {
      await elementLocator.waitFor({ state: "visible" });
    }
    await elementLocator.click();

    if (wait) {
      await this.page.waitForLoadState("networkidle", {
        timeout: timeoutValue,
      });
    }
  }

  async closeExpandedBtns(buttonCount) {
    await this.page.waitForFunction((count) => {
      const buttonElements = document.querySelectorAll(
        "div.ant-collapse-header"
      );
      return buttonElements.length === parseInt(count);
    }, buttonCount);
    const buttons = await this.page.locator("div.ant-collapse-header").all();
    console.log(buttons);

    for (const button of buttons) {
      const ariaExpanded = await button.getAttribute("aria-expanded");
      if (ariaExpanded === "true") {
        console.log("Button expanded, closing it");
        await button.click();
      }
    }
  }

  /**
   * Expand the commission sync Button in sync page based on action
   *
   * @param {String} actionName  - Commission Sync Name
   * @param {String} buttonCount - Number of commission Sync Present in the page
   */
  async expandMenu(actionName, buttonCount) {
    await this.closeExpandedBtns(buttonCount); // Ensure all buttons are closed

    switch (actionName) {
      case "Calculate Commissions Run":
        await this.page
          .getByRole("button", { name: "Calculate Commissions Run" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate commissions`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Calculate Settlements Run":
        await this.page
          .getByRole("button", { name: "Calculate Settlements Run" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate settlements`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Commission Forecasting":
        await this.page
          .getByRole("button", { name: "Commission Forecasting" })
          .click();
        await this.page
          .getByLabel(`Refresh databooks & then calculate commission forecast`)
          .waitFor({ state: "visible", timeout: 5000 });
        break;
      case "Refresh Databooks Uploaded":
        await this.page
          .getByRole("button", { name: "Refresh Databooks Uploaded" })
          .click();
        break;
      case "Sync Data from Connectors":
        await this.page
          .getByRole("button", { name: "Sync Data from Connectors" })
          .click();
        await this.page
          .locator("label")
          .filter({ hasText: "Run only upstream" })
          .waitFor({ state: "visible" });
        break;
      case "Report ETL Run ETL for report":
        await this.page
          .getByRole("button", { name: "Report ETL Run ETL for report" })
          .click();
        await this.page
          .locator("label")
          .filter({ hasText: "Run for all payees and periods" })
          .waitFor({ state: "visible" });
        break;
      default:
        console.log("Input did not match any given case.");
        break;
    }
  }

  /**
   *
   * Run Commission Sync for selected Payees for a given month
   * if month belongs to 2023, Click prev button if 2025, Click After button
   * @param {Array} payeeList
   * @param {String} month
   */
  async runSyncForSelectedPayees(payeeList, month) {
    await this.page.goto("/settings/commissions-and-data-sync", {
      waitUntil: "networkidle",
    });
    const isExpanded = await this.page
      .getByRole("button", { name: "Calculate Commissions Run" })
      .getAttribute("aria-expanded");
    if (isExpanded === "false")
      await this.page
        .getByRole("button", { name: "Calculate Commissions Run" })
        .click();
    await this.page.getByLabel("Selected Payees").click();
    await this.page.getByTestId("ever-select").click();
    for (const payee of payeeList) {
      await this.page.locator(`div[label='${payee}']`).click();
    }
    await this.page.keyboard.press("Escape");
    await this.setSelectMonthComboBox(month);
    await this.page.getByRole("button", { name: "Run", exact: true }).click();
    await this.page.getByRole("button", { name: "Skip & Run" }).click();
    await expect(
      await this.page.getByText("Calculating Commissions...")
    ).toBeVisible();
    await this.page
      .getByText("Commission Calculations Completed")
      .waitFor({ state: "visible", timeout: 300000 });
  }

  /**
   * Click an btnSelector - until elementSelector is visible
   *
   * @param {String} btnSelector
   * @param {String} elementSelector
   * @param {BigInteger} timeout
   * @param {BigInteger} clickDelay
   * @returns
   */
  async clickUntilVisible(
    btnSelector,
    elementSelector,
    timeout = 30000,
    clickDelay = 1000
  ) {
    const startTime = Date.now();
    while (true) {
      try {
        if (Date.now() - startTime > timeout) {
          console.log("Timeout reached! Element is still not visible.");
          return false;
        }
        await this.page.locator(btnSelector).click();
        console.log("Clicked the element, waiting for visibility...");
        const isVisible = await this.page.isVisible(elementSelector);
        if (isVisible) {
          console.log("Element is now visible!");
          return true;
        }
        console.log("Element is not visible, retrying...");
      } catch (error) {
        console.log("Error while trying to click:", error);
      }

      await this.page.waitForTimeout(clickDelay);
    }
  }

  async setCustomCalendarDate(date) {
    await this.page.getByPlaceholder("Select date").click();
    const currentYear = new Date().getFullYear();
    const targetYear = parseInt(date.split(" ")[2]);
    if (targetYear < currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear - 1}']`
      );
    } else if (targetYear > currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear + 1}`
      );
    } else {
      console.log("No button clicked for year: " + date);
    }
  }

  async setCustomCalendarDateAdj(date) {
    await this.page.getByPlaceholder("Select Period").click();
    const currentYear = new Date().getFullYear();
    const targetYear = parseInt(date.split(" ")[2]);
    if (targetYear < currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear - 1}']`
      );
    } else if (targetYear > currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear + 1}`
      );
    } else {
      console.log("No button clicked for year: " + date);
    }
  }

  async setSelectMonthComboBox(month) {
    await this.page.getByPlaceholder("Select month").last().click();
    const currentYear = new Date().getFullYear();
    console.log(currentYear);
    const targetYear = parseInt(month.split("-")[0]);
    console.log(targetYear);
    if (targetYear < currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `td[title='${month}']`
      );
    } else if (targetYear > currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `td[title='${month}']`
      );
    } else {
      console.log("No button clicked for year: " + month);
    }
    await this.page.locator(`td[title='${month}']`).click();
  }

  async selectDate(date) {
    await this.setCustomCalendarDate(date);
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").fill("");
    await this.page.waitForTimeout(3000);
    await this.page.getByPlaceholder("Select date").fill(date);
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
  }

  async setStorageCommValue(value) {
    const key = "commission-view-period";
    await this.page.evaluate(
      ({ key, value }) => {
        // Set the localStorage value for the current page
        localStorage.setItem(key, value);
      },
      { key, value }
    );
    await this.page.reload({ waitUntil: "load" });
  }

  async runETLReportforAllpayeesAndPeriod(reportObject, runAllBtn) {
    await this.page.getByTestId("ever-select").click();
    await this.page.locator(`div[label='${reportObject}']`).click();
    if (runAllBtn) await this.page.getByRole("switch").click();
  }

  async login(username, password) {
    const globalSearch = new GlobalSearch(this.page);
    await globalSearch.navigate("/");
    await this.page.fill("#username", username);
    await this.page.click('button[name="action"]');
    await this.page.fill("#password", password);
    await this.page.click('button[name="action"]');
  }

  async runCommissionSyncForAllPayees(month, datasheet) {
    await this.page.goto("/settings/commissions-and-data-sync", {
      waitUntil: "networkidle",
    });
    await this.expandMenu("Calculate Commissions Run", 7);
    await this.selectDatenew(month);
    if (datasheet) {
      await this.page
        .getByLabel("Refresh databooks & then calculate commissions")
        .click();
    }
    await this.page.getByRole("button", { name: "Run", exact: true }).click();
    await this.page.getByRole("button", { name: "Skip & Run" }).click();
    await expect(
      await this.page.getByText("Calculating Commissions...")
    ).toBeVisible();
    await this.page
      .getByText("Commission Calculations Completed")
      .waitFor({ state: "visible", timeout: 420000 });
  }

  async runCommissionSyncForAllPayeesToday(month, datasheet) {
    await this.page.goto("/settings/commissions-and-data-sync", {
      waitUntil: "networkidle",
    });
    await this.page
      .getByLabel(`Refresh databooks & then calculate commissions`)
      .nth(0)
      .click({ timeout: 5000 });
    await this.selectDatenew(month);
    await this.page.getByRole("button", { name: "Run", exact: true }).click();
    await this.page.getByRole("button", { name: "Skip & Run" }).click();
    await expect(
      await this.page.getByText("Calculating Commissions...")
    ).toBeVisible();
    await this.page
      .getByText("Commission Calculations Completed")
      .waitFor({ state: "visible", timeout: 420000 });
  }

  async selectDatenew(date) {
    await this.setCustomCalendarDate(date);
    const [day, month, year] = date.split(" ");
    const monthIndex = new Date(Date.parse(month +" 1, 2022")).getMonth() + 1;
    const formattedDate = `${year}-${String(monthIndex).padStart(2, "0")}-31`;
    await this.page.click(`td[title='${formattedDate}']`);
  }
}

module.exports = CommonUtils;
