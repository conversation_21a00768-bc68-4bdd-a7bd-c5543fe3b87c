import { expect } from "@playwright/test";

class ConnectorsPage {
  constructor(page) {
    this.page = page;
  }

  async navigateToConnectors(toNew = false) {
    const url = toNew ? "/settings/connectors/new" : "/settings/connectors";
    await this.page.goto(url, {
      waitUntil: "networkidle",
    });
  }

  async createConenction(isSelfServe, IntegrationName, ConnectionName) {
    await this.page
      .locator(`//div//span[text()='${IntegrationName}']`)
      .click({ timeout: 7000 });
    await this.createConnectionModal(
      isSelfServe,
      IntegrationName,
      ConnectionName
    );
  }

  async createConnectionModal(isSelfServe, IntegrationName, ConnectionName) {
    await this.page
      .locator(
        "//div[@class='ant-modal-content']//span[text()='Create a new connection']"
      )
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .locator(
        `//div[@class='ant-modal-content']//span[text()='${IntegrationName}']`
      )
      .waitFor({ state: "visible", timeout: 5000 });

    if (isSelfServe) {
      await this.page
        .locator(
          "//div[@class='ant-modal-content']//span[text()='Give your connection a name']"
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await this.page.getByPlaceholder("Connection name").fill(ConnectionName);
      await this.clickBtn("Create connection");

      await this.page
        .getByText("New Connection", { exact: true })
        .waitFor({ state: "visible", timeout: 5000 });
      const expectedText = `${ConnectionName} Edit New Connection Integration ${IntegrationName} STEP 2/2 Authenticate ${IntegrationName}`;
      // await this.verifyPageText(expectedText, ".ant-drawer-body");
    } else {
      const expectedText =
        "This is a managed integration. Please share your requirement below. Our team will follow up with details and guide you through the process.";
      await this.verifyPageText(expectedText, ".ant-modal-body");
    }
  }

  async accessToken(tokenID) {
    await this.page.locator(".ant-drawer-content .ant-input").fill(tokenID);
  }

  async clickBtn(btnName) {
    await this.page
      .getByRole("button", { name: btnName, exact: true })
      .last()
      .click({ timeout: 5000 });
  }

  async verifyConnectionPopup(connectionName) {
    const expectedText =
      "Next up! Create Objects from your connection. Objects will help you create Datasheets for Commission calculations. I'll do it later Create Object";

    // Wait for heading and modal to be visible
    await this.page
      .getByText(`Successfully Connected to ${connectionName} !`)
      .waitFor({ state: "visible" });
    await this.verifyPageText(expectedText, ".ant-modal-body");
  }

  async verifyPageText(expectedText, className) {
    await this.page.locator(className).last().waitFor({ state: "visible" });
    // Get modal text
    const modalText = await this.page.locator(className).last().innerText();
    // Normalize modalText and expected text
    const normalizedModalText = modalText.replace(/\s+/g, " ").trim();
    // Check if the normalized modal text contains the expected text
    if (!normalizedModalText.includes(expectedText)) {
      throw new Error(
        `Expected text not found in the modal body.\nModal Text: ${normalizedModalText}\nExpected Text: ${expectedText}`
      );
    }
  }

  async salesforceCreds(creds) {
    const inputs = [
      creds.clientId,
      creds.clientSecret,
      creds.userName,
      creds.password,
    ]; // Array of values
    for (let i = 0; i < inputs.length; i++) {
      await this.page
        .locator(".ant-drawer-content .ant-input")
        .nth(i)
        .fill(inputs[i]); // Fill each input field
    }
  }

  async netsuiteCreds(creds) {
    await this.page.getByPlaceholder("Enter Port").fill(creds.port);
    await this.page.getByPlaceholder("Enter Role Id").fill(creds.RoleId);
    await this.page.getByPlaceholder("Enter Token Id").fill(creds.TokenId);
    await this.page
      .getByPlaceholder("Enter Token Secret")
      .fill(creds.TokenSecret);
    await this.page
      .getByPlaceholder("Enter Consumer Key")
      .fill(creds.ConsumerKey);
    await this.page
      .getByPlaceholder("Enter Consumer Secret")
      .fill(creds.ConsumerSecret);
    await this.page.getByPlaceholder("Enter Account Id").fill(creds.AccountId);
    await this.page
      .getByPlaceholder("Enter Service Host")
      .fill(creds.ServiceHost);
  }

  async sqlServerCreds(creds) {
    await this.page.getByPlaceholder("Enter DB Host").fill(creds.port);
    await this.page.getByPlaceholder("Enter DB Port").fill(creds.port);
    await this.page.getByPlaceholder("Enter DB Name").fill(creds.port);
    await this.page.getByPlaceholder("Enter DB Username").fill(creds.port);
    await this.page.getByPlaceholder("Enter DB Password").fill(creds.port);
    await this.page.getByPlaceholder("Enter Auth Type").fill(creds.port);
    const schemaInput = this.page.locator('input[id*="dbSchema"]');
    await schemaInput.click();
    await schemaInput.fill(creds.port);
    await schemaInput.press("Enter");
  }

  async postgreSQLCreds(creds) {
    await this.page
      .locator(
        '//div[@data-testid="ever-select" and .//span[text()="Select DB Source"]]//input'
      )
      .click();
    await this.page.locator(`div[title="${creds.dbSource}"]`).click();
    await this.page.locator("input[id*='middlewareName']").click();
    await this.page.locator(`div[title="${creds.middleware}"]`).click();
  }

  async snowflakeCreds(creds) {
    await this.page.getByPlaceholder("Enter DB Name").fill(creds.dbName);
    await this.page
      .getByPlaceholder("Enter DB Username")
      .fill(creds.dbUserName);
    await this.page
      .getByPlaceholder("Enter DB Password")
      .fill(creds.dbPassword);
    await this.page.getByPlaceholder("Enter DB Account").fill(creds.dbAccount);
    await this.page
      .getByPlaceholder("Enter DB Warehouse")
      .fill(creds.dbWarehouse);
    await this.page.getByPlaceholder("Enter DB Role").fill(creds.dbRole);
    await this.page.getByPlaceholder("Enter DB Region").fill(creds.dbRegion);
    const schemaInput = this.page.locator('input[id*="dbSchema"]');
    await schemaInput.click();
    await schemaInput.fill(creds.schemaName);
    await schemaInput.press("Enter");
  }

  async isConnectionPresent(connectionName) {
    return await this.page
      .locator(
        `//div[@role="row" and .//div[@col-id="connectionName"]//*[text()="${connectionName}"]]//div[@col-id="actions"]//button`
      )
      .isVisible({ timeout: 5000 });
  }

  async deleteConnection(connectionName, popupTxt) {
    await this.page
      .locator(
        `//div[@role="row" and .//div[@col-id="connectionName"]//*[text()="${connectionName}"]]//div[@col-id="actions"]//button`
      )
      .click();

    if (popupTxt && popupTxt.trim()) {
      await this.page.getByText(popupTxt).waitFor({ state: "visible" });
    }

    await this.page.getByRole("button", { name: "Yes, Delete" }).click();
    const connectionMessage = this.page.getByText(
      "Connection deleted successfully"
    );
    await connectionMessage.waitFor({ state: "visible" });
    await connectionMessage.waitFor({ state: "hidden" });
    console.log(connectionName, "connection deleted successfully");
  }

  async conenctorsHub() {
    await this.page
      .getByRole("button", { name: "Go to Connectors Hub" })
      .click();
  }

  async search(IntegrationName) {
    await this.page.getByPlaceholder("Find your integration").fill(IntegrationName);
  }

  async searchIntegrationsList() {
    await this.page.waitForTimeout(1000);
    const totalCount = await this.page
      .locator("div.grid div.bg-ever-base")
      .count();
    const IntegrationNames = await this.page
      .locator("div.grid div.bg-ever-base span.text-lg")
      .allTextContents();
    const IntegrationTypes = await this.page
      .locator("div.grid div.bg-ever-base span.text-xs")
      .allTextContents();

    console.log("Total Count:", totalCount);
    console.log("Integration Names:", IntegrationNames);
    console.log("Integration Types:", IntegrationTypes);

    return { totalCount, IntegrationNames, IntegrationTypes };
  }

  async fillRequirements(requirements) {
    await this.page
      .getByPlaceholder("Enter your requirements...")
      .fill(requirements);
  }

  async verifyEmailIsSent(request, token, IntegrationName, requirement) {
    const requestBody = {
      integration_name: IntegrationName,
      requirements: requirement,
    };

    console.log("Request Body:", requestBody);
    console.log("Authorization Header:", `Bearer ${token}`);

    const response = await request.post(
      "https://qa.everstage.com/send_email_to_everstage/integration_request",
      {
        data: requestBody,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    const responseBody = await response.json();

    console.log("Response Status:", response.status());
    console.log("Response Body:", responseBody);

    // Validate response status and message
    expect(response.status()).toBe(200);
    expect(responseBody.message).toContain("Email sent to Everstage");
  }

  async verifyToast(msg) {
    await this.page
      .getByText(msg, { exact: true })
      .waitFor({ state: "visible" });
    await this.page
      .getByText(msg, { exact: true })
      .waitFor({ state: "hidden" });
  }
  // Zoho self serve
  async zohoCreds(creds) {
    await this.page.locator('//div[@data-testid="ever-select" and .//span[text()="Select Access type"]]//input').click();
    await this.page.locator( `//span[@title='Refresh token']`).click();
    await this .page.locator('//div[@data-testid="ever-select" and .//span[text()="Select Data center"]]//input').click();
    await this.page.locator( `//span[@title='IN']`).click();
    await this.page.getByPlaceholder("Enter Client ID").fill(creds.clientId);
    await this.page.getByPlaceholder("Enter Client Secret").fill(creds.clientSecret);
    await this.page.getByPlaceholder("Enter Refresh Token").fill(creds.refreshToken); 
  }

}
export default ConnectorsPage;
