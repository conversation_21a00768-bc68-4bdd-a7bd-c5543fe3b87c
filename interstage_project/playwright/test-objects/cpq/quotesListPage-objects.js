import { expect } from "@playwright/test";
import BasePage from "./basePage.js";

class QuoteListPage extends BasePage {
  constructor(page) {
    super(page);
    this.searchField = "input[placeholder='Search']";
    this.createButton = page.getByRole("button", { name: "Create" });
    this.newQuote = page.getByText(/New Quote/);
    this.continueButton = page.getByRole("button", { name: "Continue" });
    this.cloneQuote = page.getByText(/Clone a Quote/);
    this.closeButton = page.getByLabel("Close");
    this.chooseQuoteToClone = ".ant-select-selector";
    this.allOptions = ".ant-select-item-option";
    this.statusValue =
      "//div[contains(@col-id, 'status') and @role='gridcell']/div";
    this.table = ".quote-list-table";
    this.editButton = page.getByRole("button", { name: "Edit" });
    this.cloneButton = page.getByRole("button", { name: "<PERSON>lone" });
    this.markAsPrimaryButton = page.getByRole("button", {
      name: "Mark primary",
    });
    this.deleteButton = page.getByRole("button", { name: "Delete" });
    this.yesDelete = page.getByRole("button", { name: "Yes, delete" });
    this.cancel = page.getByRole("button", { name: "Cancel" });
  }
  async openApp(url) {
    await super.goToPage(url);
    await this.page.setViewportSize({ width: 1512, height: 982 });
  }

  // searchTerm could be Quote name, ID, or Opp name
  async searchQuote(searchTerm) {
    // Wait for the table to load
    await this.waitForElement(this.table, 120000);
    await this.fill(this.searchField, "");
    await this.fill(this.searchField, searchTerm);
    await this.keyPress(this.searchField, "Enter");
  }
  async clickOnQuote(quoteName) {
    const name = this.page
      .locator(`//a[normalize-space()='${quoteName}']`)
      .first();
    await this.click(name);
  }
  async clickCreate() {
    await this.click(this.createButton);
  }
  async clickNewquote() {
    await this.click(this.newQuote);
  }
  async clickContinue() {
    await this.click(this.continueButton);
  }
  async clickClone() {
    await this.click(this.cloneQuote);
    await this.click(this.chooseQuoteToClone);
    await this.wait(3000);
    const firstOption = await this.page.locator(this.allOptions).first();
    await this.click(firstOption);
    //await this.click(quoteToBeCloned);
    // await page.locator('span').filter({ hasText: '${searchQuote}' }).locator('span').first().click();
  }

  async closeCreateModal() {
    await expect(this.newQuote).toBeVisible();
    await expect(this.cloneQuote).toBeVisible();
    await this.click(this.closeButton);
  }
  /**
   *
   * @param {String} quoteName = provide name of the quote
   */
  async hoverOnQuote(quoteName) {
    const quotes = await this.page.locator(
      `div[col-id='quoteName'] div span div a:has-text('${quoteName}')`
    );

    await quotes.first().hover();
  }
  /**
   *
   * @param {String} actionButton  = Quote name as per 1st col with ID and no. of products
   */
  async clickMoreButton(actionButton) {
    const moreButton = await this.page
      .getByRole("row", { name: actionButton })
      .getByRole("button");
    await this.click(moreButton);
  }
  // Click "Mark as primary" button from actions menu
  async markAsPrimary() {
    await this.click(this.markAsPrimaryButton);
  }
  // Click "Edit" button from actions menu
  async edit() {
    await this.click(this.editButton);
  }
  // Click "Clone" button from actions menu
  async clickCloneQuote() {
    await this.click(this.cloneButton);
  }
  // Click "Delete" button from actions menu
  async delete() {
    await this.click(this.deleteButton);
  }
  async clickYesDelete() {
    await this.click(this.yesDelete);
  }
  async clickCancel() {
    await this.click(this.cancel);
  }
  async getFirstQuote() {
    await this.waitForElement(this.table, 90000);
    const firstQuote = await this.page
      .locator("div[role='rowgroup'] div span div a")
      .first()
      .textContent();
    return firstQuote;
  }

  async getQuoteCount(quoteName) {
    await this.waitForElement(this.table, 90000);
    const quoteRows = await this.page.locator("div[role='row']").filter({
      has: await this.page.locator("div[col-id='quoteName']").locator("a"),
      hasText: quoteName,
    });
    const quoteCount = await quoteRows.count();

    return quoteCount;
  }

  async getQuoteStatus() {
    await this.page.locator(this.statusValue);
    const statusElement = await this.page.locator(this.statusValue).first();
    const status = await statusElement.getAttribute("title");
    return status.trim(); // Return the trimmed status text
  }

  /**
   *
   * @param {String} colName = pass col-id value
   */
  async getColValues(colName) {
    await this.waitForElement(this.table, 80000);
    const quotes = await this.page.locator(`div[col-id='${colName}']`);
    await this.wait(3000);
    const quoteValues = await quotes.allTextContents();
    // Ignore the first row
    const filteredValues = quoteValues.slice(1);
    return filteredValues;
  }

  async letColumnsLoad(colName) {
    const locator = this.page.locator(
      `//div[@role='columnheader']//div[text()="${colName}"]`
    );
    try {
      await locator.waitFor({ state: "visible", timeout: 1800 });
    } catch (error) {
      await this.reloadPage();
      await locator.waitFor({ state: "visible", timeout: 90000 });
    }
    await this.wait(5000);
  }

  async reloadPage() {
    await this.page.reload();
  }
  // Compare two arrays and log the difference
  async compareValues(beforeValues, afterValues) {
    if (JSON.stringify(beforeValues) === JSON.stringify(afterValues)) {
      console.log("Column values did not change after reload.");
    } else {
      console.log("Column values changed after reload.");
    }
  }

  /**
   *@param {String} name (choose one from below)
    this.statusFilter = "span:text('Status')";
    this.ownerFilter = "span:text('Owners')";
    this.acocuntFilter = "span:text('Account')";
   * @param {array} options 
   *  
   */
  async filter(name, options = []) {
    const locator = `span:text("${name}")`;
    await this.click(locator);

    if (options.length === 0) {
      // If no options are provided, simply close the dropdown
      await this.click(locator);
    } else {
      for (const option of options) {
        const optionToSelect = await this.page.locator(`li[role="menuitem"]`, {
          hasText: `${option}`,
        });
        // Select each option provided in the array
        await this.click(optionToSelect);
      }
      await this.wait(3000);
      // Fetch all that match the selected statuses
      const status = await this.getColValues("status");
      console.log("Status: ", status);
      // Reset filter by deselecting selected options
      for (const option of options) {
        const optionToReset = await this.page.locator(`li[role="menuitem"]`, {
          hasText: `${option}`,
        });
        await this.click(optionToReset);
      }

      // Close the dropdown
      await this.click(locator);
    }
  }
}

export default QuoteListPage;
