import { expect } from "@playwright/test";
import BasePage from "./basePage.js";

class SettingsPage extends BasePage {
  constructor(page) {
    super(page);
    this.settings = "#Settings";
    this.dataSync = "span:has-text('Commission & Data Sync (On-demand)')";
    this.refreshDatabooks = page.getByRole("button", {
      name: "Refresh Databooks",
    });
    this.run =
      "//span[normalize-space()='Refresh Databooks']//following::button";
    this.skipAndRun = "//div[normalize-space()='Skip & Run']";
    this.refreshedAlert = page.getByText("Databook Refresh Completed");
  }
  /**
   * Expand the commission sync Button in sync page based on action
   *
   * @param {String} buttonCount - Number of commission Sync Present in the page
   */
  async refreshDatasheets() {
    await this.click(this.settings);
    await this.wait(5000);
    await this.click(this.dataSync);
    await this.expandMenu(5);
    await this.click(this.run);
    await this.click(this.skipAndRun);
    await this.waitForElement(this.refreshedAlert, 300000);
  }

  async expandMenu(buttonCount) {
    await this.closeExpandedBtns(buttonCount); // Ensure all buttons are closed
    await this.click(this.refreshDatabooks);
  }

  async closeExpandedBtns(buttonCount) {
    await this.page.waitForFunction((count) => {
      const buttonElements = document.querySelectorAll(
        "div.ant-collapse-header"
      );
      return buttonElements.length === parseInt(count);
    }, buttonCount);
    const buttons = await this.page.locator("div.ant-collapse-header").all();
    //console.log(buttons);

    for (const button of buttons) {
      const ariaExpanded = await button.getAttribute("aria-expanded");
      if (ariaExpanded === "true") {
        console.log("Button expanded, closing it");
        await button.click();
      }
    }
  }
}
export default SettingsPage;
