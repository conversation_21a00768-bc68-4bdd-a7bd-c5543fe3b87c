import { expect } from "@playwright/test";

const fs = require("fs"); // File system module
const csv = require("csv-parser"); // CSV parser
const CommonUtils = require("./common-utils-objects");

class CrystalPage {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async goToCrystalPage() {
    await this.page.goto("/crystal", { waitUntil: "networkidle" });
  }

  async gotoUsersPage() {
    await this.page.goto("/users", { waitUntil: "networkidle" });
  }

  async navigate(url) {
    await this.page.goto(url, { waitUntil: "networkidle" });
  }

  async searchUser(email) {
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(4000);
  }

  async assignRoles(Role) {
    await this.page.goto("/settings", { waitUntil: "networkidle" });
    // await page.getByRole('link', { name: 'Setting<PERSON>' }).click();
    await this.page
      .getByRole("link", { name: "Roles Tailor roles and" })
      .click();
    try {
      await this.page.getByText(Role, { exact: true }).click();
    } catch {
      await this.page.getByText(Role).click();
    }
    await this.page
      .locator("[role='listitem']>div>span")
      .filter({ hasText: "Crystal" })
      .click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Manage crystal views$/ })
      .nth(2)
      .click();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async getCurrentURL() {
    const currentURL = await this.page.url();
    console.log("Current URL:", currentURL);
    return currentURL;
  }

  async navigateToCrystalDraft() {
    // await this.page
    //   .getByRole("link", {
    //     name: "Test crystal Draft Last modified by Everstage Admin",
    //   })
    //   .click();
    await this.page.getByText("Test crystal", { exact: true }).click();
    await this.page.getByText("test", { exact: true }).click();
  }

  async loader() {
    await this.page.waitForTimeout(5000);
    const loader = await this.page
      .locator(
        "(//div[@class='ag-react-container']//*[name()='svg' and @preserveAspectRatio='xMidYMid meet']//*[name()='path' and @stroke-linejoin='round'])[2]"
      )
      .first();
    await loader.waitFor({ state: "hidden", timeout: 30000 });
  }

  async loader1(page1) {
    await page1.waitForTimeout(5000);
    const loader = await page1
      .locator(
        "(//div[@class='ag-react-container']//*[name()='svg' and @preserveAspectRatio='xMidYMid meet']//*[name()='path' and @stroke-linejoin='round'])[2]"
      )
      .first();
    await loader.waitFor({ state: "hidden", timeout: 30000 });
  }

  async navigateTosettings() {
    await this.page.goto("/settings", {
      waitUntil: "networkidle",
    });
  }

  async navigateToCrystal() {
    await this.page.goto("/crystal", {
      waitUntil: "networkidle",
    });
  }

  async openSimulator(name) {
    await this.page.getByRole("link", { name }).click();
    const page1Promise = this.page.waitForEvent("popup");
    await this.page.getByRole("button", { name: "Preview as payee" }).click();
    const page1 = await page1Promise;
    // await this.page1
    //   .getByRole("button", { name: "Select opportunities" })
    //   .click();
    return page1;
  }

  async openDraftSimulator() {
    const page1Promise = this.page.waitForEvent("popup");
    await this.page.getByRole("button", { name: "Preview as payee" }).click();
    const page1 = await page1Promise;
    return page1;
  }

  async clickOpenFilters(page1) {
    await page1.getByRole("tablist").getByRole("button").click();
  }

  async applyFilter(page1, columnName, operator, value) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1.locator("#filter-component-card").getByText(columnName).click();
    await page1
      .locator("#displayConditions_displayConditions_0_operatorId")
      .click();
    // await page1.getByText(operator).click();
    await page1.getByText(operator, { exact: true }).click();
    await page1.locator("#displayConditions_displayConditions_0_value").click();
    await page1
      .locator("#displayConditions_displayConditions_0_value")
      .fill(value);
  }

  async applyFilter1(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal available")
      .click();
    await page1
      .locator("#displayConditions_displayConditions_0_operatorId")
      .click();
    await page1.getByText("Equal To", { exact: true }).click();
    await page1.locator("#displayConditions_displayConditions_0_value").click();
    await page1.locator("span").filter({ hasText: "True" }).click();
    await page1.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async addAnotherFilter(page1, columnName, operator, value) {
    await page1.getByRole("button", { name: "Add Filter" }).click();
    await page1
      .locator("#displayConditions_displayConditions_1_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText(columnName)
      .nth(1)
      .click();
    await page1
      .locator("#displayConditions_displayConditions_1_operatorId")
      .click();
    await page1.getByText(operator).nth(1).click();
    await page1.locator("#displayConditions_displayConditions_1_value").click();
    await page1
      .locator("#displayConditions_displayConditions_1_value")
      .fill(value);
  }

  async addAnotherFilter1(page1, columnName, operator, value) {
    await page1.getByRole("button", { name: "Add Filter" }).click();
    await page1
      .locator("#displayConditions_displayConditions_1_colName")
      .fill("Amount");
    await page1
      .locator("#filter-component-card")
      .getByText(columnName, { exact: true })
      .nth(1)
      .click();
    await page1
      .locator("#displayConditions_displayConditions_1_operatorId")
      .click();
    await page1.getByText(operator, { exact: true }).nth(1).click();
    await page1.locator("#displayConditions_displayConditions_1_value").click();
    await page1
      .locator("#displayConditions_displayConditions_1_value")
      .fill(value);
  }

  async apply(page1) {
    await page1.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async expectVisible(page1, text) {
    const locator = page1.locator(`text=${text}`);

    return await locator.textContent();
  }

  async removeFilter(page1) {
    await page1.locator("div.bg-ever-base-200 svg.cursor-pointer").click();
  }

  async clickMinusButton(page1) {
    // await page1
    //   .locator("//*[name()='svg'][@class='h-5 w-5 text-ever-error'])[1]")
    //   .click();
    await page1
      .getByTestId("pt-removable-component-remove")
      .locator("svg")
      .click();
  }

  async clearAllFilters(page1) {
    await page1.getByRole("button", { name: "Clear All" }).click();
  }

  async removeFilter2(page1) {
    await page1
      .locator("div[class='w-full mb-2'] div:nth-child(2) svg")
      .click();
  }

  async removeSavedFilter(page1) {
    await page1
      .locator("(//*[name()='svg'][@class='cursor-pointer w-3 h-3'])")
      .click();
    // await page1.locator(".w-max > .cursor-pointer").click();
  }

  async previewAsPayee() {
    const page1Promise = this.page.waitForEvent("popup");
    await this.page.getByRole("button", { name: "Preview as payee" }).click();
    const page1 = await page1Promise;
    return new CrystalPage(page1);
  }

  async selectColumnamount(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .fill("Amount");
    await page1
      .locator("#filter-component-card")
      .getByText("Amount", { exact: true })
      .click();
  }

  async selectColumndate(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal close date")
      .click();
  }

  async selectColumnstring(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal name")
      .click();
  }

  async selectColumnboolean(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal available")
      .click();
  }

  async selectColumnEmail(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal owner email")
      .click();
  }

  async selectOperator(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_operatorId")
      .click();
  }

  async clickOnTest(page1) {
    await page1.getByText("test", { exact: true }).click();
  }

  async selectOpportunities(page1) {
    await page1.getByRole("button", { name: "Select opportunities" }).click();
  }

  async selectAmountFilter(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Amount", { exact: true })
      .click();
  }

  async selectDealnameFilter(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal id", { exact: true })
      .click();
  }

  async selectstringFilter(page1) {
    await page1
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await page1
      .locator("#filter-component-card")
      .getByText("Deal name", { exact: true })
      .click();
  }

  async selectOperatorField(page1, operator) {
    await page1
      .locator("#displayConditions_displayConditions_0_operatorId")
      .click();
    await page1.getByText(operator, { exact: true }).click();
  }

  async enterValue(page1, value) {
    await page1.locator("#displayConditions_displayConditions_0_value").click();
    await page1
      .locator("#displayConditions_displayConditions_0_value")
      .fill(value);
  }

  async enterValue2(page1, value) {
    await page1.locator("#displayConditions_displayConditions_0_value").click();
    await page1
      .locator("#displayConditions_displayConditions_0_value")
      .fill(value);
  }

  async selectRole(roleName) {
    await this.page
      .getByRole("link", {
        name: `Roles Tailor roles and permissions to suit your organization's needs with ease.`,
      })
      .click();
    await this.page.getByText(roleName, { exact: true }).click();
  }

  async filterWithoutRbacPermission(columnName, operator, value) {
    await this.page
      .getByRole("button", { name: "Select opportunities" })
      .click();
    await this.page.waitForTimeout(2000);
    await this.page.getByRole("button", { name: "Filters" }).click();
    await this.page
      .locator("#displayConditions_displayConditions_0_colName")
      .click();
    await this.page
      .locator("#filter-component-card")
      .getByText(columnName)
      .click();
    await this.page
      .locator("#displayConditions_displayConditions_0_operatorId")
      .click();
    // await page1.getByText(operator).click();
    await this.page.getByText(operator, { exact: true }).click();
    await this.page
      .locator("#displayConditions_displayConditions_0_value")
      .click();
    await this.page
      .locator("#displayConditions_displayConditions_0_value")
      .fill(value);
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async clickEditRole() {
    await this.page.getByRole("button", { name: "Edit" }).click();
  }

  async manageCrystalViews() {
    await this.page.getByRole('listitem').getByText('Crystal').click();
    await this.page.getByLabel("Manage crystal views").check();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async impersonateUser(email) {
    await this.page.goto("/users");
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(2000);
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page.waitForTimeout(3000);
  }

  async exitImpersonation() {
    await this.page.waitForTimeout(3000);
    await this.page.getByRole("button", { name: "Exit" }).click();
  }

  async verifyQuotaAttaimentInCrystal(simulatorname) {
    await this.page.getByRole("link", { name: simulatorname }).click();
    await this.page.getByRole("button", { name: "Preview as payee" }).click();
  }

  async applyProjections(page1) {
    await page1.getByRole("button", { name: "Apply projections" }).click();
    await page1.waitForTimeout(3000);
  }

  async switchView(page1, viewName) {
    // await page1.getByText(viewName).click();
    await page1.getByText(viewName, { exact: true }).click();
  }

  async verifyOperatorDropdownoptions(condition, expectedoptions) {
    await this.navigateToCrystalDraft();
    const page1 = await this.openDraftSimulator();
    await this.selectOpportunities(page1);
    await this.clickOpenFilters(page1);
    // await this.selectColumnamount(page1);
    if (condition === "amount") {
      await this.selectColumnamount(page1);
    } else if (condition === "date") {
      await this.selectColumndate(page1);
    } else if (condition === "string") {
      await this.selectColumnstring(page1);
    } else if (condition === "boolean") {
      await this.selectColumnboolean(page1);
    } else if (condition === "email") {
      await this.selectColumnEmail(page1);
    }
    await this.selectOperator(page1);
    const dropdownvalues = await page1
      .locator(
        "//div[contains(@class,'ant-select-dropdown') and not(contains(@class,'ant-select-dropdown-hidden'))]//span"
      )
      .allInnerTexts();
    // console.log(dropdownvalues);
    console.log("Dropdown Values:", dropdownvalues);
    console.log("Expected Values:", expectedoptions);
    const filteredDropdownValues = dropdownvalues.filter(
      (value) => value.trim() !== "" && value
    );

    // Check if all expected values are present in the filtered dropdown values
    const containsAllExpectedValues = expectedoptions.every((value) =>
      filteredDropdownValues.includes(value)
    );

    if (!containsAllExpectedValues) {
      throw new Error(
        `Test case failed: Not all expected operators for ${condition} are displayed.`
      );
    }
  }

  async openCrystal(crystalName) {
    await this.page.locator(".ant-list-item").getByText(crystalName).click();
  }

  async openCrystalView(crystalView) {
    await this.page
      .getByRole("tab")
      .getByText(crystalView, { exact: true })
      .click();
  }

  async navigateToPayeeCrystal(crystalName, crystalView) {
    await this.openCrystal(crystalName);
    await this.openCrystalView(crystalView);
  }

  async validateVariableLabel(labelName) {
    await this.page
      .locator(".crystal_bar_chart")
      .getByText(labelName, { exact: true })
      .last()
      .waitFor({ state: "visible" });
  }

  async switchPayee(payeeName, variablePay) {
    await this.page.getByTestId("pt-crystal-payee-dropdown").last().click();
    await this.page.getByPlaceholder("Search User").fill(payeeName);
    await this.page
      .getByRole("listitem")
      .getByText(payeeName)
      .waitFor({ state: "visible" });
    await this.page.getByRole("listitem").getByText(payeeName).click();
    await this.page
      .locator(".tracking-tight")
      .getByText(variablePay)
      .last()
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(5000);
  }

  async addCrystalCSVData(inputFile, commission) {
    // Get the current month and year
    const currentMonth = new Date().toLocaleString("default", {
      month: "short",
    });
    const currentYear = new Date().getFullYear();

    // Get the next month
    const nextMonthDate = new Date();
    nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
    const nextMonth = nextMonthDate.toLocaleString("default", {
      month: "short",
    });
    const nextMonthYear = nextMonthDate.getFullYear();

    // Calculate the date string formats
    const currentMonthDate = `01-${currentMonth}-${currentYear}`;
    const nextMonthDateStr = `01-${nextMonth}-${nextMonthYear}`;

    // Read the CSV file
    const rows = [];
    fs.createReadStream(inputFile)
      .pipe(csv())
      .on("data", (row) => {
        rows.push(row);
      })
      .on("end", async () => {
        let useCurrentMonth = true;

        for (let row of rows) {
          if (useCurrentMonth) {
            row["Deal Date"] = currentMonthDate;
          } else {
            row["Deal Date"] = nextMonthDateStr;
          }
          useCurrentMonth = !useCurrentMonth;

          row["Commission"] = commission;
        }

        const writeStream = fs.createWriteStream(inputFile);
        const header = Object.keys(rows[0]);

        writeStream.write(header.join(",") + "\n");
        rows.forEach((row) => {
          const values = header.map((col) => row[col]);
          writeStream.write(values.join(",") + "\n");
        });

        console.log(
          `CSV updated successfully with alternating Deal Dates and Commission set to ${commission}`
        );
      });
  }

  /**
   * Create,update,delete Data based on input variable {data}
   *
   * @param {String} data
   */
  async manageData(data, csvFilePath, objectName) {
    let taskName;

    switch (data) {
      case "Create":
        await this.page.waitForTimeout(5000);
        await this.page.getByText("Create new data", { exact: true }).click();
        taskName = "Create New Records";
        break;
      case "Update":
        await this.page
          .getByText("Update existing data", { exact: true })
          .click();
        taskName = "Update Records";
        break;
      case "Delete":
        await this.page.getByText("Delete data", { exact: true }).click();
        taskName = "Delete Records";
        break;
      default:
        await this.page
          .getByText("Create new and update existing data", { exact: true })
          .click();
        taskName = "Create or Update Records";
        break;
    }

    await this.page.locator("span").getByText(objectName).click();
    await this.page.getByRole("button", { name: "Next" }).click();
    const fileChooserPromise = this.page.waitForEvent("filechooser");
    await this.page.getByText("Browse").click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(csvFilePath);
    await this.page.getByRole("button", { name: "Next" }).click();
    if (data === "Delete") {
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page.getByRole("button", { name: "Validate" }).click();
      await this.page.getByRole("button", { name: "Delete" }).click();
      await this.page.getByRole("button", { name: "Confirm" }).click();
    } else {
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page.getByRole("button", { name: "Validate" }).click();
      await this.page.getByRole("button", { name: "Import" }).click();
    }

    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText("We'll notify you via email once the import is complete.")
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Got it" }).click();
    await this.navigate("/settings/manage-data");
    try {
      await this.page
        .locator("[row-id='0']>[col-id='taskName']")
        .filter({ hasText: taskName })
        .waitFor({ state: "visible", timeout: 60000 });
    } catch (error) {
      console.log("running Error Block");
      await this.page.reload();
      await this.page
        .locator("[row-id='0']>[col-id='taskName']")
        .filter({ hasText: taskName })
        .waitFor({ state: "visible", timeout: 60000 });
    }
    await this.page
      .locator("[row-id='0']>[col-id='object']")
      .filter({ hasText: objectName })
      .waitFor({ state: "visible", timeout: 60000 });
    try {
      await this.page
        .locator("[row-id='0']>[col-id='status']>div>div")
        .filter({ hasText: "Completed" })
        .waitFor({ state: "visible", timeout: 60000 });
    } catch (error) {
      console.log("running Error Block");
      await this.page.reload();
      await this.page
        .locator("[row-id='0']>[col-id='status']>div>div")
        .filter({ hasText: "Completed" })
        .waitFor({ state: "visible", timeout: 60000 });
    }
  }

  async validatePayouts(projectedPayout, currentPayout, zeroVariablePay) {
    if (zeroVariablePay) {
      await expect(
        await this.page
          .locator("div.crystal_bar_chart div")
          .getByText("Projected Payout")
          .locator("+span")
      ).toBeHidden();
      await expect(
        await this.page
          .locator("div.crystal_bar_chart div")
          .getByText("Current Payout")
          .locator("+span")
      ).toBeHidden();
    } else {
      await expect(
        await this.page
          .locator("div.crystal_bar_chart div")
          .getByText("Projected Payout")
          .locator("+span")
          .getByText(projectedPayout)
      ).toBeVisible();
      await expect(
        await this.page
          .locator("div.crystal_bar_chart div")
          .getByText("Current Payout")
          .locator("+span")
          .getByText(currentPayout)
      ).toBeVisible();
    }
  }

  async selectFirstOpportunities() {
    await this.page
      .getByRole("button", { name: "Select opportunities" })
      .click();
    await this.page.locator(".ag-selection-checkbox input").first().check();
    await this.page
      .locator(".ant-drawer-footer")
      .getByRole("button", { name: "Apply projections" })
      .click();
    await this.page.locator(".backdrop-filter").waitFor({ state: "visible" });
    await this.page.locator(".backdrop-filter").waitFor({ state: "hidden" });
  }

  async validateQuotaAttainment(page, quotaType, expectedPercentage) {
    await page.locator(".ant-select-selection-item").nth(1).click();
    await page.getByText(quotaType, { exact: true }).click();
    await page
      .locator(".tracking-tight")
      .getByText(`${expectedPercentage}%`)
      .last()
      .waitFor({ state: "visible" });
  }

  async validateMultipleQuotas(page, quotaValidations) {
    for (const { type, percentage } of quotaValidations) {
      await this.validateQuotaAttainment(page, type, percentage);
    }
  }

  async validateQuotaDropdown(page, isDisabled = false) {
    const dropdownInput = page
      .locator("input.ant-select-selection-search-input")
      .nth(1);
    if (isDisabled) {
      await expect(dropdownInput).toHaveAttribute("readonly", "");
      await expect(dropdownInput).toHaveAttribute("disabled", "");
    }
  }

  async validateQuotaCard(page, quotaType, percentage) {
    const quotaCard = page.locator("div").filter({ hasText: quotaType });
    await expect(
      quotaCard.locator(`span:text("${percentage}.00%")`).nth(1)
    ).toBeVisible();
  }

  async uploadCrystalData(csvFilePath, value, objectType, formattedDate) {
    await this.navigate("/settings/manage-data");
    await this.addCrystalCSVData(csvFilePath, value);
    await this.manageData("Create", csvFilePath, objectType);
    await this.runCommissionSync(formattedDate);
  }

  async runCommissionSync(formattedDate) {
    const commonPage = new CommonUtils(this.page);
    await commonPage.runCommissionSyncForAllPayeesToday(formattedDate, true);
  }

  async getFormattedDate() {
    const currentDate = new Date();
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return `${String(currentDate.getDate()).padStart(2, "0")} ${
      months[currentDate.getMonth()]
    } ${currentDate.getFullYear()}`;
  }

  async cleanupCrystalData(csvFilePath, objectType) {
    await this.navigate("/settings/manage-data");
    await this.manageData("Delete", csvFilePath, objectType);
  }
}

export default CrystalPage;
