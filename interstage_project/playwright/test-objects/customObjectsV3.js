import { expect } from "@playwright/test";
import CommonUtils from "./common-utils-objects";

class CustomObjectsV3 {
  constructor(page) {
    this.page = page;
  }

  async navigateToConnectorsPage() {
    await this.page.goto("/connectors", {
      waitUntil: "networkidle",
    });
  }

  async navigateToObjectsPage() {
    await this.page.goto("/objects", { waitUntil: "networkidle" });
    while (await this.page.getByText("Something went wrong").isVisible()) {
      await this.page.reload({ waitUntil: "networkidle" });
    }
  }

  async navigateToActivityLogsPage() {
    await this.page.goto("/settings/activity-logs", {
      waitUntil: "networkidle",
    });
  }

  async navigateToCommissionSyncPage() {
    await this.page.goto("/settings/commissions-and-data-sync", {
      waitUntil: "networkidle",
    });
  }

  async clickNewExperienceButton() {
    // await this.page.waitForRole('button', { name: 'Try the new experience' });
    await this.page
      .getByRole("button", { name: "Try the new experience" })
      .click();
  }

  async clickDiscardButton() {
    // await this.page.waitForRole('button', { name: 'Try the new experience' });
    await this.page
      .locator("//div/button/div[contains(text(),'Discard & go back')]")
      .click();
  }

  async validateBreadcrumbAndTitle() {
    expect(await this.page.title()).toBe("Objects - Everstage");
    await expect(
      this.page.locator(".grow > div > a > span").first()
    ).toContainText("Objects");
  }

  async clickCreateObjectFromScratchButton() {
    await this.page.getByRole("button", { name: "Add new object" }).click();
    await this.page
      .getByRole("menuitem", { name: "From scratch Create an object" })
      .click();
  }

  async enterObjectName(objName) {
    await this.page.locator("svg:left-of(:text('Edit'))").first().click();
    await this.page.getByPlaceholder("Enter name").press("Shift+ArrowUp");
    await this.page.getByPlaceholder("Enter name").fill(objName);
    await this.page.getByPlaceholder("Enter name").press("Enter");
  }

  async addFields(fields) {
    const inputGenerateButtonLocator = this.page.getByText(
      "StringNumberBooleanDateEmailPercentage"
    );
    for (const field of fields) {
      await inputGenerateButtonLocator.getByText(field.type).click();
      await this.page
        .getByPlaceholder("Enter Field Name")
        .last()
        .fill(field.name);
    }
    await inputGenerateButtonLocator.getByText("Number").click();
    await this.page.getByPlaceholder("Enter Field Name").last().fill("amount");
  }

  async save() {
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async cancel() {
    await this.page.getByRole("button", { name: "Cancel" }).click();
  }

  async assertDuplicateFieldError() {
    await expect(
      this.page.getByText("Object field names should be unique")
    ).toBeVisible();
    await this.page.click("button.p-0");
  }

  async assertPrimaryKeyError() {
    await expect(
      this.page
        .locator('text="Set atleast 1 Primary Key and 1 Snapshot Key"')
        .last()
    ).toBeVisible();
    await this.page.click("button.p-0");
  }

  async removeDuplicateField() {
    await this.page
      .locator('[data-test-id="delete-field-row-icon"]')
      .last()
      .click();
  }

  async setPrimaryKey() {
    await this.page.getByText("Set as").first().click();
    await this.page
      .getByRole("menuitem", { name: "Both Primary & Snapshot Key" })
      .click();
  }

  async assertManualObjectSuccessMsg() {
    await expect
      .soft(this.page.getByText("Successfully created object"))
      .toBeVisible({ timeout: 120000 });
    await this.page.locator("button.p-0").click();
  }

  async verifyCreatedObjectInList(objName) {
    await expect.soft(this.page.getByText(objName)).toBeVisible();
  }

  async clickCreateObjectFromConnection() {
    await this.page.getByRole("button", { name: "Add new object" }).click();
    await this.page
      .getByRole("menuitem", { name: "From an existing connection" })
      .click();
  }

  async pickConnection(connectionName) {
    await this.page
      .getByText("Pick a connection")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .locator(`//span[text()='${connectionName}']`)
      .click({ timeout: 5000 });
    await this.page
      .getByPlaceholder("Search by table/object name")
      .waitFor({ state: "visible", timeout: 100000 });
  }

  async selectHubspotConnection() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Hubspot$/ })
      .getByRole("img")
      .click();
  }

  async removeSuccessMessage(){
    for(let i=0;i<2;i++){
      await this.page.locator("div[class*=go] div button").first().click();}
    
  }

  async ManagePermissons(){
    await this.page
    .getByText("Manage Permissions")
    .click();
    await this.page
    .locator("//input[@type='radio'][@value='include']")
    .click();
     await this.page}


     async SelectUserInManage(){
      await this.page
     .locator("//div[@data-testid='ever-select']").click();
     await this.page
    .locator("//input[@placeholder='Search']")
    .fill("alice");
    await this.page
    .locator("//div/span[contains(text(),'<EMAIL>')]")
    .click();

    }

  async pickSourceObject(sourceObjectName, sourceObjectTitle) {
    await this.page
      .getByPlaceholder("Search by table/object name")
      .waitFor({ state: "visible" });
    await this.page
      .getByPlaceholder("Search by table/object name")
      .fill(sourceObjectName);
    await this.page.getByTitle(sourceObjectTitle, { exact: true }).click();
  }

  async selectAllSourceFields() {
    await this.page
      .locator("//div[./div[text()='Source field']]//input")
      .click();
  }

  async searchFieldsConnection(sourceName) {
    await this.page
      .getByPlaceholder("Search by field name or label")
      .fill(sourceName);
    await this.page.waitForTimeout(2000);
  }

  

 

  async changesSyncField(field) {
    await this.page
      .locator(
        '//div[@data-testid="ever-select" and .//span[text()="Select changes sync field"]]'
      )
      .click();
    await this.page.locator(`div[title="${field}"]`).click();
  }

  async selectDataType(objectFieldName, DataType) {
    await this.page
      .locator(
        `(//div[contains(@class, 'grid') and .//*[@value='${objectFieldName}']])[last()]//div[@data-testid="ever-select"]`
      )
      .click();
    await this.page.locator(`div[title="${DataType}"]`).last().click();
  }

  async searchFieldsManual(sourceName) {
    await this.page.getByPlaceholder("Search by field name").click();
    await this.page.getByPlaceholder("Search by field name").fill(sourceName);
  }

  async selectChecbox() {
    await this.page.getByLabel("", { exact: true }).nth(1).check();
  }

  async selectDropDown() {
    await this.page.locator(`(//div[@data-testid='ever-select'])[1]`)
    .click();
  }

  async selectAlreadyCreatedVariable(VariableName) {
    await this.page.locator(`(//span[@title='${VariableName}'])[1]`)
    .click();
  }



  async selectStringDataType() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select data type$/ })
      .nth(2)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: /^String$/ })
      .locator("div")
      .click();
  }


  async selectNumberDataType() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select data type$/ })
      .nth(2)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: /^Number$/ })
      .locator("div")
      .click();
  }

  async assertConnectionObjectSuccessMsg() {
    await expect.soft(this.page.getByText("Object added")).toBeVisible({timeout: 45000 }); 
    await expect
      .soft(this.page.getByText("You can create datasheets"))
      .toBeVisible();
    await this.page.locator("button.p-0").click();
  }

  async errorMessage() {
    await expect
      .soft(
        this.page.getByText(
          "Error: Invalid Data Format, The data received from the source is not in the expected format."
        )
      )
      .toBeVisible();
  }

  async uploadNewData(objectName, fileName) {
    //Upload Data to the created Object
    await this.page.getByPlaceholder("Search by name").clear();
    await this.page.getByPlaceholder("Search by name").fill(objectName);
    await this.page.waitForTimeout(2000);
    await this.page
      .locator("div.ag-cell-first-right-pinned>div>button")
      .first()
      .click({ force: true });
    await this.page.getByText("Create new data", { exact: true }).click();
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page
      .locator("span>input[type='file']")
      .setInputFiles("./upload-files/" + fileName);
    await this.page
      .getByText(fileName + " file read successfully.")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page
      .getByText("Map fields", { exact: true })
      .waitFor({ state: "visible", timeout: 6000 });
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page
      .getByText("Choose date format")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByRole("button", { name: "Validate" }).click();
    await this.page
      .getByText(`${fileName} file read successfully.`)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByRole("button", { name: "Import" }).click();
    // await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    // await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page.getByRole("button", { name: "Got it" }).click();
    await this.page.waitForTimeout(3000);
  }

  async searchObject(objectName) {
    await this.page.getByPlaceholder("Search by name").click();
    await this.page.getByPlaceholder("Search by name").fill(objectName);
    await this.page.waitForTimeout(2000);
  }

  async selectMenu() {
    await this.page.getByRole("treegrid").getByRole("button").nth(2).click();
    //await this.page.getByRole('button').nth(4).click();
    await this.page.waitForTimeout(2000);
  }

  async selectChangeSyncStartDateButton() {
    await this.page
      .getByRole("menuitem", { name: "Change Sync Start Date" })
      .click();
  }

  async selectObjectDetailsButton() {
    await this.page.getByText("Object Details").click();
  }

  async selectClearDataFromObject() {
    await this.page
      .getByRole("menuitem", { name: "Clear data from object" })
      .click();
  }

  async assertClearDataConfirmationMsg() {
    await expect(
      this.page.getByText(
        "Are you sure to delete all data from this object?This action cannot be"
      )
    ).toBeVisible();
  }

  async assertClearDataPopupButtons() {
    await expect(this.page.getByText("CancelYes, clear data")).toBeVisible();
  }

  async selectClearDataButton() {
    await this.page.getByRole("button", { name: "Yes, clear data" }).click();
  }

  async assertClearDataSuccessMsg() {
    await expect(
      this.page.getByText("Deleted data successfully -")
    ).toBeVisible({ timeout: 60000 });
  }

  async selectDeleteObjectButton() {
    await this.page.getByRole("menuitem", { name: "Delete object" }).click();
  }

  async verifyDeleteObjectButtonIsHidden() {
    await expect(
      this.page.getByRole("menuitem", { name: "Delete object" })
    ).toBeHidden();
  }

  async verifyDeleteObjectButtonIsVisible() {
    await expect(
      this.page.getByRole("menuitem", { name: "Delete object" })
    ).toBeVisible();
  }

  async verifyClearDataFromObjectIsHidden() {
    await expect(
      this.page.getByRole("menuitem", { name: "Clear data from object" })
    ).toBeHidden();
  }

  async verifyClearDataFromObjectIsVisible() {
    await expect(
      this.page.getByRole("menuitem", { name: "Clear data from object" })
    ).toBeVisible();
  }

  async verifySyncDateDetails() {
    const date = new Date();
    var month = date.toLocaleString("default", { month: "short" });
    const day = date.toLocaleString("default", { day: "2-digit" });
    const year = date.getFullYear();
    const time = date.toLocaleString("default", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });

    // await expect(this.page.getByText('Data Synced From'+month + ' ' + day + ', ' + year + ', ' + time)).toBeVisible()
    // await expect(this.page.getByText('Last Synced At'+month + ' ' + day + ', ' + year + ', ' + time)).toBeVisible()
    await expect(
      this.page.getByText(
        "Data Synced From" + month + " " + day + ", " + year + ", "
      )
    ).toBeVisible();
    await expect(
      this.page.getByText(
        "Last Synced At" + month + " " + day + ", " + year + ", "
      )
    ).toBeVisible();
  }

  async verifyLastSyncedAtObjectDetails(date) {
    await expect(
      this.page.getByText("Data Synced From" + date, { exact: true })
    ).toBeVisible();
  }

  async verifyLinkedSheetsCount(count) {
    await expect(
      this.page.getByText("Linked Sheets" + count, { exact: true })
    ).toBeVisible();
  }

  async verifyLinkedBookName(sheetName) {
    await expect(
      this.page.getByRole("button", { name: sheetName })
    ).toBeVisible();
  }

  async verifyTotalRowsInObject(count) {
    await expect(
      this.page.getByText("Total Rows In Object" + count)
    ).toBeVisible();
  }

  async closeButton() {
    await this.page.getByLabel("Close").click();
  }

  async selectSyncDate(date) {
    await expect(
      this.page.getByText("Date from which data will be pulledSave")
    ).toBeVisible();
    //await this.page.getByPlaceholder('Select date').clear();
    //press('ControlOrMeta+a')
    await this.page.locator(".ant-picker>div>input").click();
    await this.page.locator(".ant-picker>div>input").clear();
    await this.page.getByPlaceholder("Select date").fill(date);
    await this.page.getByPlaceholder("Select date").press("Enter");
  }
  async assertSyncDateUpdationMsg() {
    await expect(this.page.getByText("Updated sync start date")).toBeVisible();
  }

  async selectLinkToAConnectionButton() {
    await this.page
      .getByRole("menuitem", { name: "Link to a Connection" })
      .click();
  }

  async linkHubspotConnection() {
    await this.page.getByText("New Connection 1Hubspot").click();
  }

  async selectFieldToMapInLinkConnection(fieldName) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select field$/ })
      .nth(2)
      .click();
    await this.page.getByText(fieldName).click();
  }

  async clickMapAndUpdateButton() {
    await this.page.getByRole("button", { name: "Map & Update" }).click();
    //await expect(this.page.getByRole('button', { name: 'Map & Update' })).toBeHidden()
  }

  async assertLinkObjectSuccessMsg() {
    await expect(
      this.page.getByText("Object linked to connection")
    ).toBeVisible({ timeout: 60000 });
  }

  async verifyLinkedConnectionIsListed(objName) {
    // Verify whether connection is linked to connection is listed through search and filter
    // await this.page.getByLabel('Link object to Connection').getByText('Hubspot').click();
    await this.page
      .locator("span.text-base")
      .filter({ hasText: "Hubspot" })
      .first()
      .click();
    await this.page.waitForTimeout(2000);
    await expect(
      this.page.getByRole("gridcell", { name: objName }).locator("div")
    ).toBeVisible();
    await this.page.getByText("Hubspot").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Manual Upload/ })
      .locator("span")
      .first()
      .click();
    await expect(
      this.page.getByRole("gridcell", { name: objName }).locator("div")
    ).not.toBeVisible();
    await this.page
      .locator("div")
      .filter({ hasText: /^Manual Upload/ })
      .locator("span")
      .first()
      .click();
  }

  async verifyAuditLogDeleteObject(objectname) {
    await this.page.goto("/settings/audit-logs", { waitUntil: "networkidle" });
    await expect(
      this.page.getByRole("gridcell", {
        name: objectname + " has been deleted",
        exact: true,
      })
    ).toBeVisible();
  }

  async verifyAuditLogClearDataFromObject(objectname) {
    await this.page.goto("/settings/audit-logs", { waitUntil: "networkidle" });
    await expect(
      this.page.getByRole("gridcell", {
        name: "Data invalidated for " + objectname,
      })
    ).toBeVisible();
  }

  async verifyAuditLogDeleteVariable(objectname) {
    await this.page.goto("/settings/audit-logs", { waitUntil: "networkidle" });
    await expect(
      this.page.getByText(
        "email from object " + objectname + " has been deleted"
      )
    ).toBeVisible();
  }

  async deleteObject(objectname) {
    await this.searchObject(objectname);
    if (
      await this.page
        .locator(`//span[text()='${objectname}']`)
        .isVisible({ timeout: 3000 })
    ) {
      await this.selectMenu();
      await this.page.getByRole("menuitem", { name: "Delete object" }).click();
      await expect(
        this.page.getByText("Object can be deleted", { exact: true })
      ).toBeVisible();
      await expect(
        this.page.getByText(
          "Are you sure you want to delete this object?CancelYes, Confirm"
        )
      ).toBeVisible();
      await this.page
        .getByRole("dialog")
        .getByRole("button", { name: "Cancel" })
        .click();

      await this.selectMenu();
      await this.page.getByRole("menuitem", { name: "Delete object" }).click();
      await this.page
        .getByRole("dialog")
        .getByRole("button", { name: "Yes, Confirm" })
        .click();
      await expect(
        this.page.getByText("Deleted object successfully")
      ).toBeVisible();
      console.log(objectname, "object deleted successfully");
    } else {
      console.log(objectname, "object not found");
    }
  }

  async verifyUploadStatusInActivityLogs(objectName, status) {
    this.navigateToActivityLogsPage();
    await this.page.locator(".w-max > div").first().click();
    await expect(
      this.page.getByRole("cell", { name: "Object Name " + objectName })
    ).toBeVisible({ timeout: 80000 });

    let statusValue = await this.page
      .locator(
        'table tbody tr:nth-child(4) td>div>span[class="ant-descriptions-item-content"]>span>div>div'
      )
      .textContent();
    console.log(statusValue);
    try {
      await expect(
        this.page.getByRole("cell", { name: "Status " + status })
      ).toBeVisible({ timeout: 150000 });
    } catch (error) {
      await this.page.reload();
      await expect(
        this.page.getByRole("cell", { name: "Status " + status })
      ).toBeVisible({ timeout: 150000 });
    }
  }

  async clickStartDataSyncIcon() {
    await this.page.getByRole("treegrid").getByRole("button").nth(1).click();
    await this.page.waitForTimeout(1000);
  }

  async triggerDataSync() {
    await expect(
      this.page.getByText("Confirm data syncTrigger data")
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Confirm" }).click();
    await expect(this.page.getByText("Data sync triggered")).toBeVisible();
  }

  async assertDataSyncSuccessMsg(connectionObjName) {
    try {
      await expect(
        this.page.getByText("Data sync complete for " + connectionObjName)
      ).toBeVisible({ timeout: 180000 });
    } catch (error) {
      await expect(
        this.page.getByText("Data sync complete for " + connectionObjName)
      ).toBeVisible({ timeout: 180000 });
    }
    await this.selectMenu();
    await this.selectObjectDetailsButton();
    await this.verifyLastSyncedAt();
  }

  async verifyText(text) {
    await expect(this.page.getByText(text)).toBeVisible();
  }

  async verifyTextIsHidden(text) {
    await expect(this.page.getByText(text)).toBeHidden();
  }

  async verifyLastSyncedAt() {
    const date = new Date();
    var month = date.toLocaleString("default", { month: "short" });
    const day = date.toLocaleString("default", { day: "2-digit" });
    const year = date.getFullYear();
    const time = date.toLocaleString("default", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
    await expect(
      this.page.getByText(month + " " + day + ", " + year + ", ")
    ).toBeVisible();
  }

  async verifyReportObjectsArePresent() {
    const reportObjects = [
      "Commission",
      "Commission Summary",
      "Quota",
      "Quota Attainment",
      "Settlement",
      "User",
    ];
    for (const value of reportObjects) {
      const cellvalue = this.page
        .getByRole("gridcell", { name: value, exact: true })
        .locator("span");
      await expect(cellvalue).toBeVisible();
    }
  }

  async filterManuallyManagedVerify() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Manual Upload/ })
      .locator("span")
      .first()
      .click();
    await this.page.waitForTimeout(1000);
    await expect(this.page.getByRole("treegrid")).toContainText(
      "Object Name Automation"
    );
    await expect(this.page.getByRole("treegrid")).not.toContainText(
      "Report Object"
    );
    await expect(this.page.getByRole("treegrid")).not.toContainText(
      "Connection Object Automation"
    );
  }

  async searchConnectionObjectInManuallyManagedFilter() {
    await this.page
      .getByPlaceholder("Search by name")
      .fill("Connection Object Automation");
    await expect(
      this.page.locator(".ag-center-cols-clipper>div>div>div")
    ).toHaveCount(0);
    await this.page.getByPlaceholder("Search by name").clear();
    await this.page
      .locator("div")
      .filter({ hasText: /^Manual Upload/ })
      .locator("span")
      .first()
      .click();
  }
  async filterHubspotVerify() {
    await this.page.getByText("Hubspot").click();
    await this.page.waitForTimeout(2000);
    await expect(this.page.getByRole("treegrid")).toContainText(
      "Connection Object Automation"
    );
    await expect(this.page.getByRole("treegrid")).not.toContainText(
      "Report Object"
    );
    await expect(this.page.getByRole("treegrid")).not.toContainText(
      "Object Name Automation"
    );
  }

  async searchManualObjectInHubspotFilter() {
    await this.page
      .getByPlaceholder("Search by name")
      .fill("Object Name Automation");
    await expect(
      this.page.locator(".ag-center-cols-clipper>div>div>div")
    ).toHaveCount(0);
    await this.page.getByPlaceholder("Search by name").clear();
    await this.page
      .getByPlaceholder("Search by name")
      .fill("Connection Object Automation");
    await expect(
      this.page.getByText("Connection Object Automation").first()
    ).toBeVisible();
  }

  async deleteDatasheet() {
    await this.page
      .getByRole("link", { name: "Custom Obj Book - Playwright" })
      .click();
    await expect(this.page.getByText("Playwright Sheet")).toBeVisible();
    await this.page.getByLabel("remove").nth(1).click();
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    await expect(this.page.getByText("Datasheet deleted!")).toBeVisible({
      timeout: 30000,
    });
    await expect(this.page.getByText("Add Filter")).toBeVisible();

    await this.page.getByLabel("remove").first().click();
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    await expect(this.page.getByText("Datasheet deleted!")).toBeVisible({
      timeout: 30000,
    });

    await expect(
      this.page.getByRole("button", { name: "Create Datasheet" })
    ).toBeVisible();
  }

  async createDatasheetFromObject() {
    await this.page.goto("/databook/d592ba0a-f888-4ae0-b719-eaddcb17f724", {
      waitUntil: "networkidle",
    });

    //await this.page.getByRole('link', { name: 'Custom Obj Book - Playwright' }).click();
    await expect(
      this.page.getByRole("button", { name: "Create Datasheet" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Create Datasheet" }).click();

    await this.page.getByPlaceholder("Enter Name").click();
    await this.page.getByPlaceholder("Enter Name").fill("Playwright Sheet");
    await this.page.click(".ant-select-selector");
    await this.page.getByText("Object Name Automation").first().click();

    await this.page.getByRole("button", { name: "Validate" }).click();
    await this.page
      .getByText("Datasheet config validated successfully")
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByRole("button", { name: "Generate Datasheet" })
      .first()
      .click();
    await this.page
      .getByText("Datasheet sync request has been submitted")
      .waitFor({ state: "visible" });
    const generateText = await this.page.getByText(
      "This datasheet is currently being generated"
    );

    await generateText.waitFor({ state: "visible", timeout: 5000 });
    await generateText.waitFor({ state: "hidden", timeout: 180000 });
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(2000);
    await expect(this.page.getByText("<EMAIL>")).toBeVisible();
  }

  async createDatasheetFromDatasheet() {
    await this.page.getByRole("button", { name: "Add tab" }).click();
    await this.page.getByPlaceholder("Enter Name").click();
    await this.page
      .getByPlaceholder("Enter Name")
      .fill("Playwright Child Sheet");
    await this.page.getByLabel("Datasheet").check();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page.getByRole("listitem").getByText("Playwright Sheet").click();
    await this.page.getByRole("button", { name: "Validate" }).click();
    await this.page
      .getByText("Datasheet config validated successfully")
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByRole("button", { name: "Generate Datasheet" })
      .first()
      .click();
    await this.page
      .getByText("Datasheet sync request has been submitted")
      .waitFor({ state: "visible" });
    const generateText = await this.page.getByText(
      "This datasheet is currently being generated"
    );
    await generateText.waitFor({ state: "visible", timeout: 5000 });
    await generateText.waitFor({ state: "hidden", timeout: 180000 });
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible" });
    await this.page.waitForTimeout(2000);
    await expect(this.page.getByText("<EMAIL>")).toBeVisible();
  }

  async checkStalenessAndUpdateSheet() {
    const updateMsg = this.page.getByText(
      "There are some updates in this datasheet or one of the dependencies of this datasheet. Click on the 'Update Data' button to view the latest data."
    );
    await updateMsg.waitFor({ state: "visible", timeout: 300000 });
    await this.page.getByRole("button", { name: "Update Data" }).click();
    await this.page.waitForTimeout(2000);
    // const generatedMsg = this.page.getByText('Datasheet has been generated successfully')
    // await generatedMsg.waitFor({ state: "visible", timeout: 300000  });

    const generateTextSplit = await this.page.getByText(
      "This datasheet is currently being generated"
    );
    await generateTextSplit.waitFor({ state: "visible", timeout: 5000 });
    await generateTextSplit.waitFor({ state: "hidden", timeout: 200000 });
    await expect(this.page.getByText("Add Filter")).toBeVisible();
  }

  async pickMoreFieldsButton() {
    await this.page.getByRole("button", { name: "Pick more fields" }).click();
  }

  async AddMoreFieldsButton() {
    await this.page.getByRole("button", { name: "Add more fields" }).click();
  }

  async AddSingleFields() {
    await this.page.locator('text="String"').nth(0).click();
  }

  async AddNameToSingleFields(VariableName) {
    await this.page.getByPlaceholder("Enter field name").fill(VariableName);
  }



  async clickObject(objectName) {
    await this.page
      .getByRole("gridcell", { name: objectName })
      .locator("div")
      .click();
    await this.page.waitForTimeout(2000);
  }

  async removeMappingIcon() {
    await this.page.locator('[data-test-id="remove-mapping"]').click();
  }

  async removesMappingIcon() {
    await this.page
      .locator('(//button[@data-test-id="remove-mapping"])[last()]')
      .click();
  }

  async deleteVariableIcon() {
    await this.page.locator('[data-test-id="delete-field"]').click();
  }

  async deletesVariableIcon() {
    await this.page.locator('(//button[@data-test-id="delete-field"])[last()]').click();
  }



  async uploadNewDataV1(objectName) {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select a Data Object" })
      .click();
    //await this.page.locator('div').filter({ hasText: objectName }).nth(1).click();
    await this.page.getByText("Object Name Automation").last().click();
    await this.page.locator(".ant-select-selection-overflow").click();
    await this.page.getByLabel("Select All").check();

    await this.page
      .locator(".ag-row-even > div:nth-child(2)")
      .first()
      .dblclick();
    await this.page.getByLabel("Input Editor").fill("350");
    await this.page.getByLabel("Input Editor").press("Tab");
    await this.page.getByLabel("Input Editor").fill("<EMAIL>");
    await this.page.getByLabel("Input Editor").press("Tab");
    await this.page.getByLabel("Input Editor").fill("50000");
    await this.page.getByLabel("Input Editor").press("Tab");
    await this.page.getByText("Today").click();
    await this.page
      .locator(".ag-row-first")
      .locator('[col-id="co_7_stage"]')
      .dblclick();
    await this.page.getByLabel("Input Editor").fill("New Data via V1");

    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(this.page.getByText("validating")).toBeVisible();
    await expect(this.page.getByText("Validation Completed")).toBeVisible();
    await this.page.getByRole("button", { name: "Save" }).click();
    await expect(
      this.page.getByText("Object Saved Successfully")
    ).toBeVisible();
  }

  async uploadNewDataV2(objectName, fileName) {
    await this.page.goto("/settings/manage-data", { waitUntil: "networkidle" });
    await this.page.getByText("Create new data", { exact: true }).click();
    await this.page.getByText("Integration objects").click();
    await this.page.getByPlaceholder("Choose Object").click();
    await this.page.getByPlaceholder("Choose Object").fill(objectName);
    await this.page
      .getByLabel("Integration objects1")
      .getByText("Object Name Automation")
      .click();
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page
      .locator("span>input[type='file']")
      .setInputFiles("./upload-files/" + fileName);
    await this.page
      .getByText(fileName + " file read successfully.")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page
      .getByText("Map fields", { exact: true })
      .waitFor({ state: "visible", timeout: 6000 });
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByRole("button", { name: "Validate" }).click();
    await this.page
      .getByText(`${fileName} file read successfully.`)
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.getByRole("button", { name: "Import" }).click();
    // await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    // await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page.getByRole("button", { name: "Got it" }).click();
    await this.page.waitForTimeout(3000);
  }

  async navigateToLegacyUpload() {
    await this.page.getByText("Legacy Upload").click();
  }

  async checkPayeesInCommissionPlanOption() {
    await this.page.getByLabel("Payees in Compensation Plan").check();
  }

  async clickMonthPicker() {
    await this.page.getByPlaceholder("Select month").click();
  }

  async selectYear(year) {
    const commonPage = new CommonUtils(this.page);
    const currentYear = new Date().getFullYear();
    if (year < currentYear) {
      await commonPage.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${year}']`
      );
    } else if (targetYear > currentYear) {
      await commonPage.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${year}'`
      );
    } else {
      console.log("No button clicked for year: " + year);
    }
  }

  async selectSyncMonth(month) {
    await this.page.getByRole("cell", { name: month }).click();
  }

  async runSync() {
    await this.page.getByRole("button", { name: "Run", exact: true }).click();
  }

  async verifyDataInCommissionPlan() {
    await this.page.goto("/plans", { waitUntil: "networkidle" });
    // await this.page
    //   .getByText("Clear data PlanJan 01, 2024 - Dec 31, 2024")
    //   .click();
    await this.page.getByTitle("Clear data Plan").click();
    await this.page
      .locator("span")
      .filter({ hasText: "Simple" })
      .first()
      .click();
    await this.page.getByRole("button", { name: "Simulate" }).click();
    await this.page.getByPlaceholder("Start date").click();
    await this.page.waitForTimeout(5000);
    await this.page.getByPlaceholder("Start date").fill("");
    await this.page.getByPlaceholder("Start date").fill("Jun 01, 2024");
    await this.page.getByPlaceholder("Start date").press("Enter");
    await this.page.keyboard.press("Enter");
    await this.page.getByRole("button", { name: "Run" }).click();
    await expect(this.page.getByText("Evaluating Simple")).toBeHidden();
    await expect(
      this.page
        .locator("div")
        .filter({ hasText: /^Total Compensation:\$50,000\.00$/ })
        .nth(1)
    ).toBeHidden();
  }

  async verifyDataInPayouts() {
    await this.page.goto("/commissions", { waitUntil: "networkidle" });
    await this.page.getByTestId("period-select").locator("div").first().click();
    await this.page
      .locator("div")
      .filter({ hasText: /^June 2024$/ })
      .nth(1)
      .click();
    await expect(
      this.page.getByRole("gridcell", { name: "₹50,000.00" })
    ).toBeHidden();
    await this.page.getByRole("link", { name: "Rijo Cric" }).click();
    await expect(this.page.getByText("Payout Summary")).toBeVisible();
    await this.page
      .locator("span")
      .filter({ hasText: "Clear data Plan" })
      .nth(1)
      .click();
    await expect(
      this.page.getByRole("button", { name: "Simple" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Simple" }).click();
    await expect(
      this.page
        .locator("div")
        .filter({ hasText: /^PeriodJune 2024$/ })
        .first()
    ).toBeVisible();
    await expect(
      this.page
        .locator("div")
        .filter({ hasText: /^Plan level compensation₹0\.00$/ })
        .first()
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "<EMAIL>" })
    ).toBeHidden();
  }

  async verifyDataInCrystal() {
    await this.page.goto("/crystal", { waitUntil: "networkidle" });
    await this.page
      .getByRole("link", { name: "Playwright Simulator" })
      .click();
    await expect(this.page.getByText("Play View")).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "<EMAIL>" })
    ).toBeHidden();
    await expect(this.page.locator(".ag-row")).toHaveCount(0);
    await expect(
      this.page.getByRole("gridcell", { name: "50,000" })
    ).toBeHidden();
  }

  async verifyDataInDashboards() {
    await this.page.goto("/dashboards?type=all", { waitUntil: "networkidle" });
    await this.page
      .locator("div")
      .filter({ hasText: /^QA_Forecast_Commission Metrics$/ })
      .nth(1)
      .click();
    await expect(this.page.getByText("Payouts Tracker")).toBeVisible();
    await this.page.locator(".ant-picker-input").first().click();
    await this.page.getByRole("cell", { name: "2024" }).click();
    await expect(this.page.getByText("YTD payouts: $20,030")).toBeVisible();
  }

  async createSplitAdjustment() {
    await this.page
      .locator("[role='row'][row-index='0']>div[col-id='edit']>div")
      .click();
    await this.page.getByLabel("Duplicate / Split").check();
    await this.page.getByText("Add new").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Column name\*$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page.locator("span").filter({ hasText: "stage" }).click();
    await this.page.locator(".w-14 > .h-full").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Record 1Record 1$/ })
      .getByRole("textbox")
      .nth(1)
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Record 1Record 1$/ })
      .getByRole("textbox")
      .nth(1)
      .fill("Split 1");
    await this.page
      .locator("div")
      .filter({ hasText: /^Record 2Record 2$/ })
      .getByRole("textbox")
      .nth(1)
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Record 2Record 2$/ })
      .getByRole("textbox")
      .nth(1)
      .fill("Split 2");
    await this.page.locator("section textarea").click();
    await this.page.locator("section textarea").fill("Split Data Updated");
    await this.page.getByTestId("update-button-adjustment-modal").click();
    await expect(
      this.page.getByText("Record adjusted successfully")
    ).toBeVisible();
    await expect(
      this.page.getByText(
        "New adjustments have been added. Click on the Update Data button to view the latest data"
      )
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Update Data" }).click();

    await this.page.waitForTimeout(2000);
    const generateTextSplit = await this.page.getByText(
      "This datasheet is currently being generated"
    );
    await generateTextSplit.waitFor({ state: "visible", timeout: 5000 });
    await generateTextSplit.waitFor({ state: "hidden", timeout: 200000 });
    await expect(this.page.getByText("Add Filter")).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "Split 1" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "Split 2" })
    ).toBeVisible();
  }

  async archiveDatabook(databook) {
    await this.page.goto("/databook", { waitUntil: "networkidle" });
    await this.page.getByPlaceholder("Search Databook").click();
    await this.page.getByPlaceholder("Search Databook").fill(databook);
    await this.page
      .getByRole("link", { name: databook })
      .getByRole("button")
      .click();
    await this.page
      .getByRole("menuitem", { name: "Archive" })
      .locator("div")
      .click();
    await this.verifyText(databook + " is archived successfully");
  }

  async unarchiveDatabook(databook) {
    await this.page.goto("databook", { waitUntil: "networkidle" });
    await this.page.getByRole("button", { name: "Archived" }).click();
    await this.page
      .getByRole("link", { name: databook })
      .getByRole("button")
      .click();
    await this.page
      .getByRole("menuitem", { name: "Move to active" })
      .locator("div")
      .click();
    await this.verifyText(databook + " is activated successfully");
  }

  async clickDatePicker() {
    await this.page.getByPlaceholder("Select date").click();
  }
}

export default CustomObjectsV3;
