const { expect } = require("@playwright/test");
const CommonPage = require("../../playwright/test-objects/common-utils-objects");
class CwPage {
  constructor(page) {
    this.page = page;
    this.commonUtils = new CommonPage(page);
  }

  triggerCategoryMap = {
    Event: "Event Based Trigger",
    Scheduled: "Scheduled Trigger",
    demand: "On demand Trigger",
  };

  triggerByMap = {
    Add: "When a record is added",
    Update: "When a record is updated",
    Delete: "When a record is deleted",
  };

  async workflowScreen() {
    await this.page.goto("/settings/workflow-builders");
  }

  async selectingDatabook(bookname, sheetname, triggerCategory, triggerBy) {
    await this.page.locator(".custom-scroll").getByText("Databook").click();
    // await this.page.getByText("Datasheet data changes").click();
    await this.page.getByLabel("Databook*").click();
    await this.page.getByText(bookname).click();
    await this.page.getByLabel("Datasheet*").click();
    await this.page.getByText(sheetname, { exact: true }).click();
    await this.selectTriggerCategory(triggerCategory);
    await this.selectTriggerBy(triggerBy);
    // await this.page.getByText("Are you sure you want to change the datasheet?");
    // await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async selectTriggerCategory(triggerCategory) {
    const mappedTriggerCategory =
      this.triggerCategoryMap[triggerCategory] || "Event Based Trigger";

    await this.page.getByLabel("Trigger Category*").click();
    await this.page.locator(`span[title="${mappedTriggerCategory}"]`).click();
  }

  async selectTriggerBy(triggerBy) {
    const mappedTriggerBy =
      this.triggerByMap[triggerBy] || "When a record is added";

    await this.page.getByLabel("Trigger*").click();
    await this.page.locator(`span[title="${mappedTriggerBy}"]`).click();
  }

  /**
   * This method is used to select a dropdown option and click the next button.
   * @param {Array} actions - The actions to be performed in the dropdown.
   */
  async selectDropdown(actions) {
    await this.page.getByLabel("Type of change*").click();
    for (const action of actions) {
      await this.page.getByText(action, { exact: true }).click();
    }
    await this.clickNextButton();
  }

  async publishWorkflow() {
    await this.page.getByRole("button", { name: "Save Draft" }).click();
    await this.page.getByRole("button", { name: "Publish" }).click();
  }

  async thenAction(type, value) {
    await this.page
      .locator(
        `(//div[contains(@class, "react-flow__node") and contains(@class, "react-flow__node-placeholder") and contains(@class, "selectable")])[${value}]`
      )
      .click();
    await this.page.getByText("Then: Add an action").click();
    await this.page
      .getByText("Send in-app notification", { exact: true })
      .click();
    await this.page.getByRole("switch").click();
    await this.page.locator(".ant-select-selection-overflow").click();
    await this.page.getByPlaceholder("Search").fill("everstage");
    await this.page.getByText("everstage admin").click();
    await this.page.getByRole("button", { name: "close-circle" }).click();
    await this.page.locator("#user-avatar-element").last().click();
    try {
      await this.page
        .locator("//span[text()='To']")
        .waitFor({ state: "visible", timeout: 10000 });
      await this.page.locator("//span[text()='To']").click({ timeout: 10000 });
    } catch (error) {
      console.error(
        "Failed to find 'To' span, trying the placeholder instead..."
      );
      console.error(error.message);
      try {
        await this.page
          .getByPlaceholder("Type something...")
          .waitFor({ state: "visible", timeout: 10000 });
        await this.page
          .getByPlaceholder("Type something...")
          .click({ timeout: 1000 });
      } catch (err) {
        console.error("Failed to click on the placeholder 'Type something...'");
        console.error(error.message);
        throw err.message();
      }
    }
    await this.page
      .getByPlaceholder("Type something...")
      .click({ timeout: 10000 });
    await this.page.getByPlaceholder("Type something...").fill("tes");
    await this.page.getByLabel("Notification Status*").click();
    await this.page.getByText(type, { exact: true }).click();
    await this.page.getByRole("switch").nth(1).click();
    await this.page.getByRole("paragraph").click();
    await this.clickNextButton();
    await this.saveDraft();
  }

  async nameWorkflow(wfname) {
    await this.page.locator(".ant-btn").first().click();
    await this.page.getByPlaceholder("Enter workflow name").click();
    await this.page.keyboard.press("Control+A"); // Select all text (Cmd+A on macOS)
    await this.page.keyboard.press("Backspace");
    await this.page.getByPlaceholder("Enter workflow name").fill(wfname);
    await this.page
      .getByLabel("Update details")
      .getByRole("button", { name: "Done" })
      .click();
  }

  async searchWorkflow(wf) {
    await this.page.getByPlaceholder("Search").click();
    await this.page.getByPlaceholder("Search").fill(wf);
    await this.page.waitForTimeout(10000);
  }

  async clickNextButton() {
    await this.commonUtils.clickBy("Role-button=Next");
  }

  async selectActions(wf) {
    await this.page
      .locator(
        `(//div//span[text()="${wf}"]/ancestor::div[2]/following-sibling::div//button)[2]`
      )
      .first()
      .click();
    await this.page.waitForTimeout(10000);
  }

  async actionButton(actionName) {
    await this.page
      .getByRole("menuitem", { name: actionName })
      .locator("span")
      .click();
  }

  async validate(message) {
    await this.page.getByText(message).isVisible();
  }

  async validateduplicate(message1) {
    await this.page.getByLabel("Update details").getByText(message1).click();
  }

  async buttonVisibllity(buttonName) {
    await this.page.getByText(buttonName, { exact: true }).isVisible();
  }

  async deletePopup(msg) {
    await this.page.getByRole("button", { name: msg }).click();
  }

  async validatingName(name) {
    await this.page.getByText(name).nth(1).isVisible();
  }

  async closeButton() {
    await this.page.getByLabel("Close", { exact: true }).click();
  }

  async editbutton() {
    await this.page.locator("//div[text()='Edit Workflow']").click();
  }

  async toggle() {
    // await this.page
    //   .locator("div")
    //   .filter({
    //     hasText: /^Move to ActiveInactiveLast modified: Jun 26, 2024$/,
    //   })
    //   .getByRole("switch")
    //   .click();
    await this.page
      .locator('button:below(:text("New Workflow"))')
      .nth(0)
      .click();
  }

  async selectWf(wf1) {
    await this.page.getByText(wf1).click();
  }

  async updateButton() {
    await this.page
      .getByLabel("Update details")
      .getByRole("button", { name: "Done" })
      .click();
  }

  async buttonInsideWorkflow(buttonname) {
    await this.page.getByRole("button", { name: buttonname }).click();
  }

  async navigateUrl(urlLink) {
    await expect(this.page).toHaveURL(urlLink);
  }

  async link() {
    await this.page.getByRole("link", { name: "Click here to view" }).click();
  }

  async workflowhistorySearch() {
    await this.page.getByPlaceholder("Search by Workflow name").click();
  }

  async workflowhistorySearchwf(wfName, history) {
    await this.page.getByPlaceholder("Search by Workflow name").fill(wfName);
    await this.page.getByPlaceholder("Search by Workflow name").press("Enter");
    await this.page.getByRole("gridcell", { name: history }).click();
  }

  async buttonVisible(buttonNames) {
    const button = await this.page.getByRole("button", {
      name: buttonNames,
    });
    const isVisible = await button.isVisible();
    if (!isVisible) {
      console.log("The button is not visible");
    } else {
      console.log("The button is visible");
    }
  }

  async selectDrop() {
    await this.page
      .locator(
        "div:nth-child(3) > div:nth-child(2) > .ant-form-item-control-input > .ant-form-item-control-input-content > .relative > .ant-select > .ant-select-selector"
      )
      .click();
  }

  async createTrigger() {
    await this.page
      .getByRole("link", { name: "Set up Custom Trigger" })
      .click();
    await this.page.getByRole("button", { name: "Create Trigger" }).click();
  }

  async clickCategory() {
    await this.page
      .locator(
        '(//span[@class="ant-select-selection-search"]//input[@class="ant-select-selection-search-input"])[2]'
      )
      .click();
  }

  async createCategory(cname) {
    await this.page.getByRole("button", { name: "Add Category" }).click();
    await this.page.getByPlaceholder("Category Name").click();
    await this.page.getByPlaceholder("Category Name").fill(cname);
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.waitForTimeout(3000);
    await this.page.getByText("Category added successfully").isVisible();
    await this.page.waitForTimeout(3000);
  }

  async addTrigger(tname) {
    await this.page.getByPlaceholder("Trigger name").click();
    await this.page.getByPlaceholder("Trigger name").fill(tname);
    await this.page
      .locator(
        '(//span[@class="ant-select-selection-search"]//input[@class="ant-select-selection-search-input"])[3]'
      )
      .click();
    await this.page.keyboard.press("ArrowDown");

    await this.page.keyboard.press("Enter");
  }

  async categorySelect(catName) {
    await this.page.getByText(catName, { exact: true }).click();
  }

  async description(dname) {
    await this.page.getByPlaceholder("Description").click();
    await this.page.getByPlaceholder("Description").fill(dname);
  }

  async triggerDatabook(dataname, sheetnam) {
    await this.page
      .locator("div")
      .filter({ hasText: /^DatabookDatabook$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page.getByText(dataname).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Datasheet$/ })
      .nth(2)
      .click();
    await this.page.getByText(sheetnam, { exact: true }).click();
  }

  async buttonDisabled(but) {
    await this.page.getByRole("button", { name: but }).isDisabled();
  }

  async selectCategoryExisting(tnam) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Category$/ })
      .nth(2)
      .click();
    await this.page.getByText("Datasheet New").click();
    await this.page.getByPlaceholder("Trigger name").click();
    await this.page.getByPlaceholder("Trigger name").press("CapsLock");
    await this.page.getByPlaceholder("Trigger name").fill(tnam);
  }

  async newWorkflowButton() {
    await this.commonUtils.clickBy("Role-link=New Workflow");
  }

  async saveDraft() {
    await this.page.getByRole("button", { name: "Save Draft" }).click();
  }

  async editTrigger(tnames) {
    await this.page
      .getByRole("link", { name: "Set up Custom Trigger" })
      .click();
    await this.page.locator("//span[text()='edit']").click();
    await this.page.getByText("edit mode").click();
    await this.page.getByRole("switch").click();
    await this.page.getByPlaceholder("Trigger name").dblclick();
    await this.page.getByPlaceholder("Trigger name").fill("");
    await this.page.getByPlaceholder("Trigger name").press("CapsLock");
    await this.page.getByPlaceholder("Trigger name").fill(tnames);
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async downloadButton() {
    const button = await this.page
      .getByTestId("rf__wrapper")
      .locator("button")
      .nth(3);
    const isVisible = await button.isVisible();
    if (!isVisible) {
      console.log("The button is not visible");
    } else {
      console.log("The button is visible");
    }
  }

  async checkInapp(databookid, popup, title, description) {
    await this.page.goto(databookid, {
      waitUntil: "networkidle",
    });
    await this.page.getByText(popup).isVisible();
    await this.page.getByRole("button", { name: "Update Data" }).click();
    await this.page
      .getByText("Datasheet sync request has been submitted")
      .waitFor({ state: "visible" });
    const generateText = await this.page.getByText(
      "This datasheet is currently being generated"
    );

    await generateText.waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible", timeout: 1000000 });

    await this.page.getByText(title).isVisible();
    await this.page.getByText(description).isVisible();
    await this.page.locator("#tsparticles+div button").click();
  }

  async checkInappNoChanges(databookid, title, description) {
    await this.page.goto(databookid, {
      waitUntil: "networkidle",
    });
    await this.page.getByRole("button", { name: "Generate Datasheet" }).click();
    await this.page
      .getByText("Datasheet sync request has been submitted")
      .waitFor({ state: "visible" });
    const generateText = await this.page.getByText(
      "This datasheet is currently being generated"
    );

    await generateText.waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible", timeout: 1000000 });

    await this.page.getByText(title).toBeHidden()();
    await this.page.getByText(description).toBeHidden();
  }

  async navigateToDatasheet(datasheetID, databookname) {
    await this.page.goto(`http://localhost:3000/databook/${datasheetID}`, {
      waitUntil: "networkidle",
    });
    await this.page.getByRole("tab", { name: databookname }).click();
  }

  async deleteDatasheet(datasheetName) {
    await this.page
      .locator(".ant-tabs-tab")
      .filter({ hasText: datasheetName })
      .getByLabel("remove")
      .click();
    await this.page.getByRole("menuitem", { name: "Delete" }).last().click();
  }

  async editDatasheet(datasheetName) {
    await this.page
      .locator(".ant-tabs-tab")
      .filter({ hasText: datasheetName })
      .getByLabel("remove")
      .click();
    await this.page.getByRole("menuitem", { name: "Edit" }).click();
  }

  async addTransformation(datasheetname) {
    await this.page.getByRole("button", { name: "Add Transformation" }).click();
    await this.page.getByText("Union", { exact: true }).click();
    await this.page
      .getByTestId("ever-select")
      .filter({ hasText: "Select Datasheet" })
      .click();
    await this.page.getByRole("listitem").getByText(datasheetname).click();
    await this.page
      .getByTestId("ever-select")
      .filter({ hasText: "Choose Column" })
      .first()
      .click();
    await this.page.getByRole("listitem").getByText("Email").click();
    await this.page.keyboard.press("Escape");
    await this.page.waitForTimeout(2000);
    await this.page
      .getByTestId("ever-select")
      .filter({ hasText: "Choose Column" })
      .first()
      .click();
    await this.page.getByRole("listitem").getByText("Email").click();
    await this.page.getByRole("button", { name: "Validate" }).click();
  }

  async closeEditPage() {
    await this.page.locator(".ant-drawer-close").click();
  }

  async openWorkFlow(wfName) {
    await this.page.getByPlaceholder("Search").fill(wfName);
    await this.page.getByText(wfName, { exact: true }).click();
  }

  async validateDatasheetErrorMessage() {
    await this.page
      .locator("span[title='Test1'].ant-select-selection-item")
      .click();
    await this.page.locator("div[label='Test2']").click();
    await expect(
      await this.page.getByText(
        "This will reset the existing datasheet fields used in the actions."
      )
    ).toBeVisible();
    await expect(
      await this.page.getByRole("button", { name: "Yes" })
    ).toBeVisible();
    await expect(
      await this.page.getByRole("button", { name: "No", exact: true })
    ).toBeVisible();
  }

  async clickAdd(value) {
    await this.page
      .locator(
        `(//div[contains(@class, "react-flow__node") and contains(@class, "react-flow__node-placeholder") and contains(@class, "selectable")])[${value}]`
      )
      .click();
  }

  async addComponent(value) {
    await this.page
      .locator("div.custom-scroll>div>div>span")
      .filter({ hasText: value })
      .click();
  }

  async getFormulaBoxValues() {
    await this.page.waitForTimeout(2000);
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    return await this.page.locator("div[role='listitem']").allTextContents();
  }

  async addElseIf() {
    await this.page.getByText("Add ELSE IF", { exact: true }).click();
  }

  async addInvalidFormula() {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page
      .locator("div[role='listitem']")
      .filter({ hasText: "EmailEmail" })
      .click();
  }

  async validateErrorMsg() {
    await this.page.keyboard.press("Escape");
    await expect(
      await this.page.locator(".text-ever-error>path").first()
    ).toBeVisible({
      timeout: 5000,
    });
    await expect(
      await this.page
        .locator(".text-ever-error")
        .filter({ hasText: "Formula is incorrect" })
    ).toBeVisible();
  }

  /**
   * Validates the tooltip message displayed on hover.
   * @param {string} toolTipMessage - The expected tooltip message.
   * @param {number} [nth=0] - The nth occurrence of the tooltip to validate.
   */
  async validateToolTip(toolTipMessage, nth = 0) {
    await this.page.waitForTimeout(2000);
    const buttonLocator = this.page
      .getByTestId("rf__wrapper")
      .locator("button");
    await buttonLocator.nth(2).click();
    const locator = await this.page.locator("svg.absolute").nth(nth);
    await locator.hover();
    const toolTipLocator = this.page.getByRole("tooltip", {
      name: toolTipMessage,
    });
    await expect(toolTipLocator).toBeVisible();
  }

  async selectRecipient(recipient) {
    await this.page.locator(".ant-select-selection-overflow").first().click();
    await this.page.getByText(recipient).click();
  }

  async enterSubjectLine(subject) {
    await this.page.getByPlaceholder("Enter a subject line").click();
    await this.page.getByPlaceholder("Enter a subject line").fill(subject);
  }

  /**
   * Clicks the "Add" action button at a specified position.
   * @param {number} nthPosition - The position of the "Add" button to click.
   */
  async clickAddAction(nthPosition) {
    await this.commonUtils.clickBy("Role-button=Add", { nth: nthPosition });
  }

  /**
   * Clicks the "Then: Add an action" button and then selects a specific action.
   * @param {string} action - The action to select after clicking "Then: Add an action".
   */
  async clickThenAddAnAction(action = "Send an email") {
    await this.commonUtils.clickBy("text=Then: Add an action");
    await this.commonUtils.clickBy(`text=${action}`);
  }

  /**
   * Clicks the "If: Add a condition" button.
   */
  async clickIfAddACondition() {
    await this.commonUtils.clickBy("text=If: Add a condition");
  }

  async clickSendAnEmail() {
    await this.page.getByText("Send an email").last().click();
  }

  async selectDeliveryCriteria(deliveryCriteria) {
    await this.page
      .locator(`//*[contains(text(),'${deliveryCriteria}')]`)
      .click();
  }

  async insertDynamicValue(value) {
    await this.page.locator("button.ck.ck-button.ck-off").nth(7).click();
    await this.page
      .locator(`//span[@class='custom-badge'][normalize-space()='${value}']`)
      .click();
  }

  async clickEmbedTable() {
    await this.page
      .locator('button[data-cke-tooltip-text="Show more items"]')
      .click();
    await this.page
      .locator('button[data-cke-tooltip-text="Insert data table"]')
      .click();
  }

  async clickExit() {
    await this.page.getByText("Exit").click();
  }

  async clickCkEditor() {
    await this.page.getByLabel("Rich Text Editor. Editing").click();
  }

  async clickAlignRight() {
    await this.page.locator("button.ck.ck-button.ck-off").nth(9).click();
  }

  async showMoreItems() {
    await this.page.getByLabel("Show more items").click();
  }

  async selectItalic() {
    await this.page.getByLabel("Italic").last().click();
  }

  async selectBold() {
    await this.page.getByLabel("Bold").last().click();
  }

  async selectStrikethrough() {
    await this.page.getByLabel("Strikethrough").last().click();
  }

  async addTextInCkEditor(newText) {
    // Focus on the CKEditor contenteditable area
    const editor = await this.page.locator(
      '.ck-editor__editable[contenteditable="true"]'
    );
    // Type a newline (Enter key) and then the new text to append it
    await editor.press("Enter");
    await editor.type(newText);
  }

  async quoteFormat() {
    await this.page.getByLabel("Block quote").last().click();
  }

  async addLink(link) {
    await this.page.getByLabel("Link").click();
    await this.page.getByLabel("Link URL").fill(link);
    await this.page.getByLabel("Save").click();
  }

  async codeBlockFormat() {
    const elements = this.page.locator(
      "button[data-cke-tooltip-text='Insert code block']"
    );
    const count = await elements.count();
    await elements.nth(count - 2).click();
  }

  async selectAll() {
    await this.page
      .getByLabel("Rich Text Editor. Editing")
      .last()
      .press("ControlOrMeta+a");
  }

  async deleteWorkflow() {
    await this.page.locator("button.workflow-more-button").click();
    // await this.page.getByText("Delete").click();
    await this.page.getByText("Delete", { exact: true }).click();
    await this.page.getByRole("button", { name: "Yes, delete" }).click();
  }

  async verifyTagName(tagname, descriptionText) {
    const element = await this.page.locator(`text=${descriptionText}`).last();
    const tagName = await element.evaluate((el) => el.tagName.toLowerCase());
    return tagName === tagname.toLowerCase();
  }

  async verifyParentTagName(tagname, descriptiontext, parentTag) {
    try {
      // Locate the parent element and then find the child element with specified text
      const parent = this.page.locator(parentTag);
      const element = parent.locator(`text=${descriptiontext}`).last();
      console.log("element", element);

      // Wait for the child element to be visible
      await element.waitFor({ timeout: 60000 });

      // Get the tag name of the child element
      const tagName = await element.evaluate((el) => el.tagName.toLowerCase());

      return tagName === tagname.toLowerCase();
    } catch (error) {
      console.error(`Error verifying tag name: ${error.message}`);
      return false;
    }
  }

  /**
   * Clicks the collapse '>' or expand '<' button .
   */
  async clickCollapseOrExpandButton() {
    await this.commonUtils.clickBy(
      "selector=.transition-all > div:nth-child(2) > .transition-all"
    );
  }

  /**
   * Clicks a button by its name.
   * @param {string} buttonName - The name of the button to click.
   */
  async clickButton(buttonName) {
    await this.commonUtils.clickBy(`Role-button=${buttonName}`);
  }

  /**
   * Fills in the details for the "Then" section of the workflow.
   *
   * This method fills in the details for the "Then" section of the workflow based on the provided options.
   * It supports filling in the "content", "to", "notificationStatus", "title", and "subject" fields.
   *
   * @param {Object} options - An object containing the details to fill in.
   * @param {string} options.to - The recipient of the notification.
   * @param {string} options.notificationStatus - The status of the notification.
   * @param {string} options.subject - The subject of the notification.
   * @param {string} options.title - The title of the notification.
   * @param {string} options.content - The content of the notification.
   *
   * @example
   * await fillThenDetails({
   *   to: "<EMAIL>",
   *   notificationStatus: "Success",
   *   subject: "Test Notification",
   *   title: "Notification Title",
   *   content: "This is a test notification."
   * });
   */

  async fillThenDetails(options = {}) {
    const { to, notificationStatus, subject, title, content } = options;

    // Fill "content" if Provided
    if (content) {
      await this.page.getByRole("paragraph").click();
      await this.page.getByRole("paragraph").fill(content);
    }
    // Fill "to" if Provided
    if (to) {
      await this.page.locator(".ant-select-selection-overflow").first().click();
      await this.page.getByPlaceholder("Search").fill("everstage");
      await this.commonUtils.clickBy(`text=${to}`);
      await this.commonUtils.clickBy("Role-button=close-circle");
      try {
        await this.clickNextButton();
      } catch (error) {
        await this.page.getByLabel("To").click();
      }
    }
    // Fill "notificationStatus" if Provided
    if (notificationStatus) {
      await this.page.getByLabel("Notification Status*").click();
      await this.commonUtils.clickBy(`text=${notificationStatus}`);
    }
    // Fill "title" if Provided
    if (title) {
      await this.page.getByLabel("Title*").click();
      await this.page.getByLabel("Title*").fill(title);
    }
    // Fill "subject" if Provided
    if (subject) {
      await this.page.getByLabel("Subject*").click();
      await this.page.getByLabel("Subject*").fill(subject);
    }
  }

  /**
   * Fills a condition with the specified action and formulas.
   * @param {string} action - The action to perform.
   * @param {Array<string>} enteredFormulas - The formulas to enter.
   */
  async fillCondition(action, enteredFormulas) {
    const actionLocator = this.page
      .locator("div")
      .filter({ hasText: new RegExp(`^${action}$`) });
    const placeholderLocator = actionLocator.getByPlaceholder(
      "Press Ctrl + H for help"
    );
    await placeholderLocator.click();
    for (const enteredFormula of enteredFormulas) {
      const locator = await this.page
        .locator("//div[@data-testid='expression-input-box']/input")
        .last();
      await locator.click();
      await locator.type(enteredFormula);
      await this.page.keyboard.press("Enter");
    }
  }

  /**
   * Verifies the visibility of retained values on the page.
   *
   * This method checks if the provided values are visible on the page. It supports
   * verification of "toValue", "notificationStatusValue", "subjectOrTitleValue", and "contentValue".
   *
   * @param {Object} options - An object containing the values to verify.
   * @param {string} options.toValue - The value to verify for the "To" field.
   * @param {string} options.notificationStatusValue - The value to verify for the "Notification Status" field.
   * @param {string} options.subjectOrTitleValue - The value to verify for the "Subject" or "Title" field.
   * @param {string} options.contentValue - The value to verify for the "Content" field.
   *
   * @example
   * await verifyRetainedValues({
   *   toValue: "<EMAIL>",
   *   notificationStatusValue: "Success",
   *   subjectOrTitleValue: "Test Notification",
   *   contentValue: "This is a test notification."
   * });
   */
  async verifyRetainedValues(options = {}) {
    const {
      toValue,
      notificationStatusValue,
      subjectOrTitleValue,
      contentValue,
    } = options;

    // Validate "toValue" if provided
    if (toValue) {
      const toLocator = this.page
        .getByTestId("login-indicator")
        .getByText(`${toValue}`, { exact: true });
      await expect(toLocator).toBeVisible();
    }
    // Validate "notificationStatusValue" if provided
    if (notificationStatusValue) {
      const notificationStatusLocator = this.page
        .getByTestId("login-indicator")
        .getByText(`${notificationStatusValue}`, { exact: true });
      await expect(notificationStatusLocator).toBeVisible();
    }
    // Validate "subjectValue" if provided
    if (subjectOrTitleValue) {
      const subjectLocator = this.page.getByText(`${subjectOrTitleValue}`, {
        exact: true,
      });
      await expect(subjectLocator).toBeVisible();
    }
    // Validate "contentValue" if provided
    if (contentValue) {
      const contentLocator = this.page.getByText(`${contentValue}`, {
        exact: true,
      });
      await expect(contentLocator).toBeVisible();
    }
  }

  /**
   * Verifies if the expected text is contained within the formula entered in the expression input box.
   * @param {string} expectedText - The text to verify within the formula.
   */
  async verifyRetainedFormula(expectedText) {
    const enteredFormula = await this.page
      .getByTestId("expression-input-box")
      .textContent();
    expect(enteredFormula).toContain(expectedText);
  }

  async clickUpdateData() {
    await this.page.getByText("Update Data", { exact: true }).click();
  }

  async verifyDatasheetGenerated() {
    await this.page
      .getByText("Datasheet has been generated successfully")
      .waitFor({ state: "visible", timeout: 1000000 });
  }

  async clickClearFilter() {
    try {
      await this.page
        .locator("button.ant-btn-ghost", { hasText: "Clear filter" })
        .click();
    } catch (error) {
      console.log("Clear filter button not found, skipping...");
    }
  }

  async addFilterinDatasheet(columnname, operatorname, payeemail) {
    await this.page.getByRole("button", { name: "Add Filter" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Column$/ })
      .nth(3)
      .click();
    await this.page
      .locator("[data-testid='ever-select'] div span input")
      .first()
      .fill(columnname);
    await this.page.getByRole("listitem").getByText(columnname).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Operator$/ })
      .nth(3)
      .click();
    await this.page.getByText(operatorname, { exact: true }).click();
    await this.page.getByRole("textbox").nth(1).click();
    await this.page.getByRole("textbox").nth(1).fill(payeemail);
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
  }
}

module.exports = CwPage;
// export default CwPage;
