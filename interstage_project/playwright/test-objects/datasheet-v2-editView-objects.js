import { expect } from "@playwright/test";

class DatasheetV2EditViewPage {
  constructor(page) {
    this.page = page;
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async dateDiffFn(fieldName, sdColumn, edColumn, unit) {
    console.log(`Creating DATEDIFF function for unit - ${unit}`);
    await this.clickAddFormula();
    const title = await this.verifyTitle();
    expect(title).toEqual("Add Formula Column");
    await this.formulaNameType(fieldName, "Integer");
    const formulaLocator = await this.selectFormula("FUNCTIONS", "DATEDIFF");
    await formulaLocator.click();
    const allUnits = ["DAYS", "MONTH", "QUARTER", "HALF-YEAR", "YEAR"];
    await this.clickDropdown("Select Unit");
    for (const unit of allUnits) {
      console.log("verify DATEDIFF units in dropdown - ", unit);
      const isUnitPresent = await this.verifyUnit("Select Unit", unit);
      expect(isUnitPresent).toBe(true);
    }

    await this.selectDropDown("Start Date Column", sdColumn);
    await this.selectDropDown("End Date Column", edColumn);
    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("Select Unit", unit);
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
    await this.delay(1500);
  }

  async dateFn(fieldName, Fn, dateCol, unit) {
    console.log(`Creating ${Fn} function for unit - ${unit}`);
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Date");
    const formulaLocator = await this.selectFormula("FUNCTIONS", Fn);
    await formulaLocator.click();
    const allUnits = ["MONTH", "QUARTER", "HALF-YEAR", "YEAR"];
    await this.clickDropdown("Select Unit");
    for (const unit of allUnits) {
      console.log(`verify ${Fn} units in dropdown - `, unit);
      const isUnitPresent = await this.verifyUnit("Select Unit", unit);
      expect(isUnitPresent).toBe(true);
    }
    await this.selectDropDown("Select Date Column", dateCol);
    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("Select Unit", unit);
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
  }

  async dateAddFn(fieldName, dateColumn, unit, inputType, inputValue) {
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Date");
    const formulaLocator = await this.selectFormula("FUNCTIONS", "DateAdd");
    await formulaLocator.click();

    await this.selectDropDown("Select Date Column", dateColumn);
    await this.selectDropDown("Days", unit);
    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    if (unit === "HH:mm") {
      await this.page.getByPlaceholder("Select time").click();
      await this.page.getByPlaceholder("Select time").fill(inputValue);
      await this.page.keyboard.press("Enter");
    } else {
      if (inputType === "Constant") {
        await this.page.getByPlaceholder("Enter Value").fill(inputValue);
      } else if (inputType === "Datasheet Field") {
        await this.selectDropDown("Constant", "Datasheet Field");
        await this.selectDropDown("Select Numeric Column", inputValue);
      }
    }
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
    console.log(
      `Created DateAdd function with parameters: ${dateColumn} ${unit} ${inputType} ${inputValue}`
    );
  }

  async convertTimezoneFn(fieldName, dateColumn, from, to) {
    console.log(
      `Creating ConvertTimezone function : ${fieldName} on column ${dateColumn} from ${from} to ${to}`
    );
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Date");
    const formulaLocator = await this.selectFormula(
      "FUNCTIONS",
      "ConvertTimezone"
    );
    await formulaLocator.click();

    const btnApply = await this.applyFnBtn();
    await this.selectDropDown("Select Date Column", dateColumn);
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("(GMT+00:00) UTC", from);
    await this.selectDropDown("To Timezone", to);
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
  }

  async getDateFn(fieldName, dateColumn, period, calendarType) {
    await this.clickAddFormula();
    await this.formulaNameType(
      `GetDate_${dateColumn}_${period}_${calendarType}`,
      "Integer"
    );
    const formulaLocator = await this.selectFormula("FUNCTIONS", "GetDate");
    await formulaLocator.click();

    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("Select Date Column", dateColumn);
    await this.selectDropDown("Month", period);
    if (calendarType === "Calendar") {
      await this.selectDropDown("Fiscal", calendarType);
    }
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
    console.log(
      `Created GetDate function for column:${dateColumn} with period:${period} of calendar type:${calendarType}`
    );
  }

  async roundFn(fieldName, formula, numColumn, decimal) {
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Integer");
    const formulaLocator = await this.selectFormula("FUNCTIONS", formula);
    await formulaLocator.click();

    const columnValues = ["ID", "Amount", "deliveredIn", "percentage"];
    await this.clickDropdown("Select Number Column");
    for (const column of columnValues) {
      const isColumnPresent = await this.verifyUnit(
        "Select Number Column",
        column
      );
      expect(isColumnPresent).toBe(true);
    }
    await this.clickDropdown("Select Number Column");

    const btnApply = await this.applyFnBtn();
    await this.selectDropDown("Select Number Column", numColumn);
    await expect(btnApply).toBeDisabled();
    await this.page.getByPlaceholder("Decimal Places").fill(decimal);
    if (decimal < 0) {
      await expect(btnApply).toBeDisabled();
      await this.closeFormulaPopup();
    } else {
      await expect(btnApply).toBeEnabled();
      await btnApply.click();
      await this.clickCreateFn();
      console.log(
        `Created ${formula} function for column: ${numColumn} with decimal value : ${decimal}`
      );
    }
  }

  async emptyFn(fieldName, columnValues = [], formula, column) {
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Boolean");
    const formulaLocator = await this.selectFormula("FUNCTIONS", formula);
    await formulaLocator.click();

    await this.clickDropdown("Please Select");
    // verify columnvalues are present in the dropdown
    for (const column of columnValues) {
      const isColumnPresent = await this.verifyUnit("Please Select", column);
      expect(isColumnPresent).toBe(true);
    }
    await this.clickDropdown("Please Select");

    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("Please Select", column);
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
    console.log(`Applied ${formula} function on column : ${column}`);
  }

  async containFn(fieldName, colValues = [], formula, textColumn, value) {
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, "Boolean");
    const formulaLocator = await this.selectFormula("FUNCTIONS", formula);
    await formulaLocator.click();

    await this.clickDropdown("Text field");
    for (const column of colValues) {
      const isColumnPresent = await this.verifyUnit("Text field", column);
      expect(isColumnPresent).toBe(true);
    }
    await this.clickDropdown("Text field");

    const btnApply = await this.applyFnBtn();
    await expect(btnApply).toBeDisabled();
    await this.selectDropDown("Text field", textColumn);
    await this.page.locator("//input").nth(-1).click();
    await this.page.locator("//input").nth(-1).fill(value);
    await expect(btnApply).toBeEnabled();
    await btnApply.click();
    await this.clickCreateFn();
    console.log(
      `Applied ${formula} function on column : ${textColumn} for value : ${value}`
    );
  }

  async conditionalFn(fieldType, datasheetColumn, operation, value) {
    await this.clickAddFormula();
    await this.page
      .getByRole("button", { name: "Conditional", exact: true })
      .click();
    await this.delay(500);
    const fieldName = `${datasheetColumn}_conditional_null`;
    await this.formulaNameType(fieldName, fieldType); // fill formula name and type

    const checkNull = await await this.checkNullBoxLocator();
    await this.delay(500);
    await checkNull.click();
    await this.page.locator("//button[ ./span[text()='Yes']]").click(); // enable null in ELSE block

    const formulaExpressionIFbox = await this.page
      .locator("//div[@data-testid='expression-input-box']/input[@placeholder]")
      .nth(-2);
    await formulaExpressionIFbox.click();
    await formulaExpressionIFbox.type(datasheetColumn);
    let locator = await this.page
      .locator(`//span[@title="${datasheetColumn}"]`)
      .first();
    await locator.waitFor({ state: "visible" });
    await this.page.keyboard.press("Enter");

    await formulaExpressionIFbox.click();
    await formulaExpressionIFbox.type(operation);
    locator = await this.page.locator(`//span[@title="${operation}"]`).first();
    await locator.waitFor({ state: "visible" });
    await this.page.keyboard.press("Enter");

    await formulaExpressionIFbox.click();
    await formulaExpressionIFbox.type(value);
    locator = await this.page.locator(`//span[@title="${value}"]`).first();
    await locator.waitFor({ state: "visible" });
    await this.page.keyboard.press("Enter");

    await this.page.locator(`//input[@value="${fieldName}"]`).click();

    const formulaExpressionTHENbox = await this.page
      .locator("//div[@data-testid='expression-input-box']/input[@placeholder]")
      .nth(-1);
    await formulaExpressionTHENbox.click();
    await formulaExpressionTHENbox.type(datasheetColumn);
    await this.delay(500);
    locator = await this.page
      .locator(`//span[@title="${datasheetColumn}"]`)
      .first();
    await locator.waitFor({ state: "visible" });
    await this.page.keyboard.press("Enter");
    await this.delay(1000);
    await this.clickCreateFn();

    console.log(
      `----- Conditiaonl function created with fieldname:${fieldName} -----`
    );
  }

  async verifyTitle() {
    const locator = await this.page.locator(
      "//div[contains(@id, 'DialogTitle')]/span"
    );
    await locator.waitFor({ state: "visible" });
    return await locator.textContent();
  }

  async clickAddFormula() {
    const locator = await this.page.getByText("Add Formula Column").first();

    await locator.waitFor({ state: "visible" });
    await locator.click();
  }

  async setCalculatedFieldName(name) {
    const inputElement = await this.page.locator(
      'input[placeholder="Enter Name"]'
    );
    await inputElement.waitFor({ state: "visible" });
    await inputElement.fill("");
    await inputElement.fill(name);
  }

  async setCalculatedFieldFormulaType(type) {
    await this.page.getByTestId("ever-select").last().click();
    const selectType = await this.page.locator(`//div[@title="${type}"]`);
    await selectType.click();
  }

  async formulaNameType(name, type) {
    await this.setCalculatedFieldName(name);
    await this.setCalculatedFieldFormulaType(type);
  }

  async clickFormulaExpressionBox() {
    const searchBar = await this.page
      .locator('//div[@data-testid="expression-input-box"]/input[@placeholder]')
      .nth(-1);
    await searchBar.waitFor({ state: "visible" });
    await searchBar.click();
    return searchBar;
  }

  async selectFormula(from, formula) {
    const searchBar = await this.clickFormulaExpressionBox();
    await searchBar.fill(formula);
    const formulaLocator = await this.page.locator(
      `//div[contains(text(), '${from}')]/following-sibling::ul[1]//span[@title="${formula}"]`
    );
    await formulaLocator.waitFor({ state: "visible" });
    await expect(formulaLocator).toBeVisible();
    return formulaLocator;
  }

  async typeFormulaExpression(expression) {
    const locator = await this.page
      .locator("//div[@data-testid='expression-input-box']/input")
      .last();
    await locator.click();
    await locator.type(expression);
    await this.page.keyboard.press("Enter");
  }

  async dropDownLocator(dropdwnName) {
    const locator = await this.page.locator(
      `//span[text()="${dropdwnName}"]/ancestor::div[2]`
    );
    return locator;
  }

  async clickDropdown(dropdwnName) {
    const locator = await this.page
      .locator(
        `//div[@data-testid="ever-select" and .//span[text()="${dropdwnName}"]]`
      )
      .last();
    await locator.click();
  }

  async verifyUnit(dropdwnName, unit) {
    let dropdwnInput = await this.page.locator(
      `//div[@data-testid="ever-select" and .//span[text()="${dropdwnName}"]]//input`
    );
    await dropdwnInput.fill(unit);
    const targetLocator = await this.page.locator(`//div[@title="${unit}"]`);
    const scrollContainer = this.page.locator(".rc-virtual-list-holder");
    const isUnitPresent = await this.scrollToElement(
      targetLocator,
      scrollContainer
    );
    dropdwnInput = await this.page.locator(
      `//div[@data-testid="ever-select"]//input[@value="${unit}"]`
    );
    await dropdwnInput.fill("");
    return isUnitPresent;
  }

  async scrollToElement(targetLocator, scrollContainer) {
    const timeout = 20000;
    const startTime = Date.now();

    try {
      while (true) {
        if (await targetLocator.isVisible()) {
          return true;
        }
        if (Date.now() - startTime > timeout) {
          console.log("Target element not found within 20 seconds.");
          return false;
        }
        await scrollContainer.evaluate((element) => {
          element.scrollBy(0, 100);
        });
        await this.delay(2000);
      }
    } catch (error) {
      console.error("An error occurred during scrolling:", error);
      return false;
    }
  }

  async selectDropDown(dropdwnName, value) {
    await this.clickDropdown(dropdwnName);
    await this.page
      .locator(
        `//div[@data-testid="ever-select" and .//span[text()="${dropdwnName}"]]//input`
      )
      .fill(value);
    const targetLocator = this.page.locator(`//div[@title="${value}"]`).last();
    const scrollContainer = this.page.locator(".rc-virtual-list-holder").last();
    if (await this.scrollToElement(targetLocator, scrollContainer)) {
      await targetLocator.click();
    }
  }

  async createBtnLocator() {
    const createBtn = await this.page.getByRole("button", {
      name: "Create",
      exact: true,
    });
    return createBtn;
  }

  async clickCreateFn() {
    const createBtn = await this.createBtnLocator();
    await expect(createBtn).toBeEnabled({ timeout: 20000 });
    await createBtn.click({ timeout: 20000 });
    await createBtn.waitFor({ state: "hidden", timeout: 30000 });
  }

  async clickDeleteFn() {
    const deleteBtn = await this.page.getByRole("button", {
      name: "OK",
      exact: true,
    });
    await deleteBtn.click();
  }

  async clickUpdateFn() {
    const updateBtn = await this.page.getByRole("button", {
      name: "Update",
      exact: true,
    });
    await updateBtn.click({ timeout: 20000 });
    return updateBtn;
  }

  async saveEdits() {
    const saveBtn = await this.page.getByRole("button", {
      name: "Save",
      exact: true,
    });
    await saveBtn.click({ timeout: 30000 });
    await this.page.waitForLoadState("networkidle");
  }

  async applyFnBtn() {
    return await this.page
      .getByLabel("Add Formula Column")
      .getByRole("button", { name: "Apply", exact: true });
  }

  async closeFormulaPopup() {
    await this.page.locator("button[aria-label='Close']").last().click();
  }

  async hoverOnField(colName) {
    await this.page
      .locator(`(//span[text()="${colName}"]/ancestor::div[3])//span[./input]`)
      .hover();
  }

  async unCheckColumn(colName, unchecked = true) {
    const checkBoxLocator = `(//span[text()="${colName}"]/ancestor::div[3])//span[contains(@class, 'ant-checkbox-checked') and ./input]`;
    await this.page.locator(checkBoxLocator).waitFor({ state: "visible" });
    await this.page.locator(checkBoxLocator).click();
    if (unchecked) {
      await this.page.locator(checkBoxLocator).waitFor({ state: "hidden" });
    }
  }

  async verifyUnCheckColumnDisabled(colName) {
    const checkBoxLocator = this.page.locator(
      `(//span[text()="${colName}"]/ancestor::div[3])//span[contains(@class, 'ant-checkbox-checked') and ./input]`
    );
    await checkBoxLocator.waitFor({ state: "visible" });
    const isDisabled = await checkBoxLocator.locator("input").isDisabled();
    expect(isDisabled).toBeTruthy();
  }

  async toggleCheckbox(colName) {
    const locator = await this.page.locator(
      `(//span[text()="${colName}"]/ancestor::div[3])//span[./input]`
    );
    await locator.click();
    await this.delay(3000);
  }

  async concatExpressionBox(col) {
    const locator = await this.page
      .locator("//div[@data-testid='expression-input-box']/input")
      .last();
    await locator.click();
    await locator.fill(col);
    const formulaLocator = await this.page.locator(
      `//div[contains(text(), 'DATASHEET')]/following-sibling::ul[1]//span[@title="${col}"]`
    );
    await formulaLocator.waitFor({ state: "visible" });
    return formulaLocator;
  }

  async hoverFormulaColumn(colName) {
    const locator = this.page.locator(`//span[text()="${colName}"]`);
    await locator.nth(-1).hover();
  }

  async formulaMoreActions(colName) {
    await this.page
      .locator(
        ` //span[text()="${colName}"]/ancestor::div[3]/following-sibling::div`
      )
      .nth(-1)
      .click();
  }

  async clickFormulaMoreActions(option) {
    const locator = await this.page.locator(
      `//li[@role="menuitem"]/span[text()="${option}"]`
    );
    await locator.nth(-1).click();
  }

  async isPopUpMsgVisible(text) {
    const popUpLocator = await this.page.locator(
      `//div/span[text()="${text}"]`
    );
    // await popUp.waitFor({ state: "visible" });
    const startTime = Date.now();
    const timeout = 5000;
    while (Date.now() - startTime < timeout) {
      if ((await popUpLocator.count()) > 0) {
        return true;
      }
      await this.delay(500);
    }
    return false;
  }

  async verifyFormulaView(id) {
    const formulaIcon = await this.page.locator(
      `(//div[@col-id="${id}"]//*[name()='svg'])[1]`
    );
    await formulaIcon.scrollIntoViewIfNeeded();
    await formulaIcon.hover();
    await formulaIcon.click();
    const isTitlePresent = await this.page
      .locator(`(//div[@col-id="${id}"])[1]`)
      .isVisible();

    const spans = await this.page.locator(
      '(//div[contains(@class, "expression-token")]//span)[1]'
    );
    const texts = await spans.allTextContents();
    const combinedText = texts.join("");

    return { isTitlePresent, combinedText: combinedText.trim() };
  }

  async clearDropdown(columnName) {
    const locator = await this.page.locator(
      `//div[@data-testid="ever-select"]//span[@title="${columnName}"]/ancestor::div[2]/span[2]`
    );
    await locator.click();
  }

  async clickEditFormulaExpression(formula) {
    const locator = await this.page.locator(
      ` //div[contains(@class, "expression-token")]//span[text()="${formula}"]`
    );
    await locator.click();
  }

  async clickEditFormulaColumn(columnName) {
    const locator = await this.page.locator(
      `//div[@data-testid="ever-select"]//span[@title="${columnName}"]`
    );
    await locator.click();
  }

  async nullCheckBoxCount() {
    const nullCheckBoxCountLocator = await this.page.locator(
      `//span[text()="ELSE"]/ancestor::div[2]/div[.//span[text()="NULL"]]//input`
    );
    return await nullCheckBoxCountLocator.count();
  }

  async checkNullBoxLocator(index = 1) {
    const checkBoxLocator = await this.page.locator(
      `(//span[text()="ELSE"]/ancestor::div[2]/div[.//span[text()="NULL"]]//span[./input])[${index}]`
    );
    return checkBoxLocator;
  }

  async formulaBoxCount(block) {
    const formulaExpressionLocator = await this.page.locator(
      `//span[text()="${block}"]/ancestor::div[2]/following-sibling::div//div[@data-testid="expression-input-box"]`
    );
    return await formulaExpressionLocator.count();
  }

  async switchAddIFLocator(block, index = 1) {
    const button = await this.page.locator(
      `(//div[.//span[text()="${block}"]]/following-sibling::div[text()="Add IF"]//button)[${index}]`
    );
    return button;
  }

  async switchPrompt() {
    await this.delay(5000);
    const buttonLocators = this.page.locator("//button[ ./span[text()='Yes']]");
    const count = await buttonLocators.count();

    for (let i = 0; i < count; i++) {
      const button = buttonLocators.nth(i);
      if (await button.isVisible()) {
        await button.click();
        await this.delay(1000);
        return;
      }
    }

    throw new Error("No visible 'Yes' button found.");
  }

  async isRankModalOpen() {
    const modalLocator = await this.page.locator(
      '//div[@data-state="open" and @role="dialog" and .//span[text()="Rank"]]'
    );
    await expect(modalLocator).toBeVisible();
  }

  async fillRankModal(inputVariable, partitionBy, skipRun = "disable") {
    await this.selectDropDown("Select Input Variable", inputVariable);
    await this.selectDropDown("Select Partition", partitionBy);
    await this.page
      .locator(
        "//div[@data-state='open' and @role='dialog']//span[text()='Rank']"
      )
      .click();
    if (skipRun === "enable") {
      await this.page.getByRole("switch").click();
    }
  }

  async renameFields(fieldPairs) {
    for (const [currentFieldName, newFieldName] of fieldPairs) {
      await this.page
        .locator(`//span[text()='${currentFieldName}']`)
        .last()
        .dblclick();
      await this.page.locator("input[type='text']").nth(-1).fill(newFieldName);
      await this.page.keyboard.press("Enter");
      await expect(
        this.page.locator(`//span[text()='${newFieldName}']`).last()
      ).toBeVisible({ timeout: 10000 });
    }
  }

  async verifyCoalesceModal() {
    await this.clickAddFormula();
    const formula = await this.selectFormula("FUNCTIONS", "Coalesce");
    await formula.click();
    const datatypes = ["String", "Date", "Boolean", "Email"];

    for (const each of datatypes) {
      const dateType = await this.page
        .locator('div[data-testid="ever-select"]')
        .last();
      await dateType.click();

      await this.page.locator(`div[title="${each}"]`).last().click();
      await this.page
        .getByPlaceholder("Press Ctrl + H for help")
        .last()
        .click();

      const receivedDataTypes = await this.page
        .locator(
          "//div[@data-testid='auto-suggestion-view']//div[@role='listitem']/div[3]//span//span"
        )
        .allTextContents(); // Getting all text contents

      // Check if all values are of same data type
      const isMatching = receivedDataTypes.every(
        (value) => value.trim() === each
      );

      if (!isMatching) {
        throw new Error(
          `Test failed: Found values in receivedDataTypes: ${receivedDataTypes} when expected data type of ${each}`
        );
      }
      await this.page
        .getByPlaceholder("Press Ctrl + H for help")
        .last()
        .press("Tab");
    }
    // close the add formula modal
    await this.page.locator(".ant-modal-close-x").click();
  }

  async clickOnPlaceholder() {
    await this.page.getByPlaceholder("Press Ctrl + H for help").last().click();
  }

  async fillPlaceholder(value) {
    await this.page
      .getByPlaceholder("Press Ctrl + H for help")
      .last()
      .fill(value);
  }

  async selectConstant(value, type) {
    await this.page
      .locator(
        `//div[@role="listitem" and .//span[text()='${value}'] and .//span[text()='${type}']]`
      )
      .click();
  }

  async getEBvalue() {
    return await this.page.locator(".expression-token").allTextContents();
  }

  async quitEdit(toQuit) {
    await this.page.locator("button[aria-label='Close']").click();
    await this.page
      .getByText("Are you sure to close?")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page
      .getByText("This will discard all changes made.")
      .waitFor({ state: "visible", timeout: 5000 });
    if (toQuit) {
      await this.page
        .getByRole("button", { name: "Close this page" })
        .last()
        .click();
      expect(this.page.url()).not.toContain("isEditView=true");
    } else {
      await this.page
        .getByRole("button", { name: "Continue to edit" })
        .last()
        .click();
      expect(this.page.url()).toContain("isEditView=true");
    }
  }


  async coalesceFn(fieldName, dataType, params) {
    await this.delay(2000);
    await this.clickAddFormula();
    await this.formulaNameType(fieldName, dataType);
    const formulaLocator = await this.selectFormula("FUNCTIONS", "Coalesce");
    await formulaLocator.click();
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    await this.fillCoalesceModal(dataType, params);
    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(this.page.getByText(fieldName, { exact: true })).toBeVisible();
  }

  /**
   * Fills out the Coalesce modal by selecting a data type and entering multiple values.
   *
   * This function automates the process of selecting a data type from a dropdown
   * and entering a list of values into a search bar within the Coalesce modal.
   * It ensures that each value is selected from the visible options that appear
   * after typing in the search bar.
   *
   * @param {string} dataType - The type of data to select from the dropdown.
   * @param {Array<string>} values - An array of values to enter into the search bar.
   */
  async fillCoalesceModal(dataType, values) {
    const dateTypeLocator = await this.page
      .locator('div[data-testid="ever-select"]')
      .last();
    await dateTypeLocator.click();

    const dataTypeLocator = await this.page
      .locator(`div[title="${dataType}"]`)
      .last();
    await dataTypeLocator.click();
    const searchBar = await this.clickFormulaExpressionBox();
    for (const value of values) {
      await searchBar.type(value);
      const valueLocator = await this.page
        .locator(`span[title="${value}"]`)
        .last();
      await expect(valueLocator).toBeVisible();
      await valueLocator.click();
    }
    await searchBar.press("Tab");
  }

  async LeftRightFn(
    newFieldName,
    InputFn,
    selectColumn,
    inputType,
    inputValue
  ) {
    console.log(
      "Left and Right functions - inputs received are",
      newFieldName,
      InputFn,
      selectColumn,
      inputType,
      inputValue
    );
    await this.delay(3000);
    await this.clickAddFormula();
    await this.formulaNameType(newFieldName, "String");
    const formulaLocator = await this.selectFormula("FUNCTIONS", InputFn);
    await formulaLocator.click();
    await this.selectDropDown("Select Column", selectColumn);
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    if (inputType === "Constant") {
      await this.page.getByPlaceholder("Enter Value").fill(inputValue);
    } else if (inputType === "Datasheet Field") {
      await this.selectDropDown("Constant", "Datasheet Field");
      await this.selectDropDown("Select Numeric Column", inputValue);
    }
    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(
      this.page.getByText(newFieldName, { exact: true })
    ).toBeVisible();
  }

  async midFn(
    newFieldName,
    inputFn,
    selectColumn,
    startingIndex,
    inputType,
    inputValue
  ) {
    console.log(
      "Mid function inputs received are:",
      newFieldName,
      inputFn,
      selectColumn,
      startingIndex,
      inputType,
      inputValue
    );
    await this.delay(3000);
    await this.clickAddFormula();
    await this.formulaNameType(newFieldName, "String");
    const formulaLocator = await this.selectFormula("FUNCTIONS", inputFn);
    await formulaLocator.click();
    await this.selectDropDown("Select Column", selectColumn);
    await this.selectDropDown("Starting index", startingIndex);
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    if (inputType === "Constant") {
      await this.page.getByPlaceholder("Enter Value").fill(inputValue);
    } else if (inputType === "Datasheet Field") {
      await this.selectDropDown("Constant", "Datasheet Field");
      await this.selectDropDown("Select Numeric Column", inputValue);
    }
    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(
      this.page.getByText(newFieldName, { exact: true })
    ).toBeVisible();
  }

  async findFn(newFieldName, selectColumn, inputType, inputValue) {
    console.log(
      "find function inputs received are:",
      newFieldName,
      selectColumn,
      inputType,
      inputValue
    );
    await this.delay(3000);
    await this.clickAddFormula();
    await this.formulaNameType(newFieldName, "Integer");
    const formulaLocator = await this.selectFormula("FUNCTIONS", "Find");
    await formulaLocator.click();
    await this.selectDropDown("Text field", selectColumn);
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    if (inputType === "Constant") {
      // enter the value of constant in input field
      await this.page
        .locator('div[data-state="open"] input')
        .last()
        .fill(inputValue);
    } else if (inputType === "Datasheet Field") {
      await this.selectDropDown("Constant", "Datasheet Field");
      await this.selectDropDown("Text field", inputValue);
    }
    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(
      this.page.getByText(newFieldName, { exact: true })
    ).toBeVisible();
  }

  async RollingSumFn(newfieldName, inputVariable, partitionBy, orderBy) {
    console.log(
      "RollingSum function inputs received are:",
      newfieldName,
      inputVariable,
      partitionBy,
      orderBy
    );
    await this.delay(3000);
    await this.clickAddFormula();
    await this.formulaNameType(newfieldName, "Integer");
    const formulaLocator = await this.selectFormula("FUNCTIONS", "RollingSum");
    await formulaLocator.click();
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    await this.selectDropDown("Select Input Variable", inputVariable);

    // Handle Partition By
    const partitionByInput = await this.page
      .locator('.ant-modal-body div[data-testid="ever-select"] input')
      .nth(-2);
    for (const each of partitionBy) {
      await partitionByInput.click();
      await partitionByInput.fill(each);
      const targetLocator = this.page.locator(`//div[@title="${each}"]`).last();
      const scrollContainer = this.page
        .locator(".rc-virtual-list-holder")
        .last();
      if (await this.scrollToElement(targetLocator, scrollContainer)) {
        await targetLocator.click();
      }
    }

    // Handle Order By
    const orderByInput = await this.page
      .locator('.ant-modal-body div[data-testid="ever-select"] input')
      .nth(-1);
    for (const each of orderBy) {
      await orderByInput.click();
      await orderByInput.fill(each);
      const targetLocator = this.page.locator(`//div[@title="${each}"]`).last();
      const scrollContainer = this.page
        .locator(".rc-virtual-list-holder")
        .last();
      if (await this.scrollToElement(targetLocator, scrollContainer)) {
        await targetLocator.click();
      }
    }

    // Set order direction (assuming first column should be "asc")
    await this.page
      .locator(`//div[./*/span[text()='${orderBy[0]}']]//input[@value="asc"]`)
      .click();

    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(
      this.page.getByText(newfieldName, { exact: true })
    ).toBeVisible();
  }

  async GenerateHierarchyFn(newfieldName, params) {
    console.log(
      "Generate hierarchy function with inputs:",
      newfieldName,
      params
    );
    await this.delay(3000);
    await this.clickAddFormula();
    await this.formulaNameType(newfieldName, "Hierarchy");
    const formulaLocator = await this.selectFormula(
      "FUNCTIONS",
      "GenerateHierarchy"
    );
    await formulaLocator.click();
    const applyBtn = await this.applyFnBtn();
    await expect(applyBtn).toBeDisabled();
    for (let i = 0; i < params.length; i++) {
      const field = this.page
        .locator(
          '//div[@data-state="open"]//div[@data-testid="ever-select" and //input]'
        )
        .nth(i);
      await field.click();
      if (i !== 2 && i !== 3) {
        await field.locator("input").fill(params[i]);
      }
      await this.page.locator(`div[title="${params[i]}"]`).last().click();
    }
    await expect(applyBtn).toBeEnabled();
    await applyBtn.click();
    await this.clickCreateFn();
    await expect(
      this.page.getByText(newfieldName, { exact: true })
    ).toBeVisible();
  }

  async switchTopLevel(param) {
    const switchButton = await this.page
      .locator('button[role="switch"]')
      .last();
    const isChecked = await switchButton.getAttribute("aria-checked");
    if (param === "on" && isChecked !== "true") {
      await switchButton.click();
    } else if (param === "off" && isChecked === "true") {
      await switchButton.click();
    }
    await this.delay(500);
    const finalState = await switchButton.getAttribute("aria-checked");
    return finalState;
  }

  async clickValidateAndSaveBtn() {
    await this.page.getByRole("button", { name: "Validate and save" }).click();
    await this.page.waitForLoadState("networkidle");
  }

  async selectOperator(value) {
    await this.page
      .locator(`//div[@role="listitem" and .//span[text()='${value}']]`)
      .click();
  }

  async verifyColumn(colName, expectedState) {
    const checkBoxLocator = `(//span[text()="${colName}"]/ancestor::div[3])//span[contains(@class, 'ant-checkbox-checked') and ./input]`;

    if (expectedState === "Checked") {
      await this.page.locator(checkBoxLocator).waitFor({ state: "visible" });
      console.log(`${colName} is Checked`);
    } else if (expectedState == "Unchecked") {
      await this.page.locator(checkBoxLocator).waitFor({ state: "hidden" });
      console.log(`${colName} is Unchecked`);
      console.log(`${colName} is Unchecked`);
    } else {
      throw new Error("Invalid expected state. Use 'Checked' or 'Unchecked'.");
    }
  }

  async selectConstantByIndex(value, type, index) {
    await this.page
      .locator(
        `//div[@role="listitem" and .//span[text()='${value}'] and .//span[text()='${type}']]`
      )
      .nth(index)
      .click();
  }

  async addAdjustment(adjType)
  {
    await this.page.locator('.ag-cell > div > .ant-btn').first().click();
    await this.page.getByText(adjType).click();
  }

  async verifyCheckboxesCountToBe(number) {
    await expect(
      this.page.locator("label.ant-checkbox-wrapper >span.ant-checkbox>input")
    ).toHaveCount(number);
  }
}

export default DatasheetV2EditViewPage;
