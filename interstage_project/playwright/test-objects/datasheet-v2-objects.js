import { expect } from "@playwright/test";

const moment = require("moment");
class DatasheetV2Page {
  constructor(page) {
    this.page = page;
  }

  async hoverDatabook(dbname) {
    return await this.page
      .locator(`//button//span[text()='${dbname}']`)
      .hover();
  }

  async clickDatabook(dbname) {
    // const dbBtn = this.page.locator(`//button//span[text()='${dbname}']`);
    const dbBtn = this.page.locator(
      `//span[text()='${dbname}']/ancestor::div[1]/preceding-sibling::div/div`
    );
    return await dbBtn.click();
  }

  async createDatabook() {
    const plusLocator = this.page.locator(
      "//div/span[contains(text(), 'Databook')]/ancestor::div[4]/following-sibling::div"
    );
    await plusLocator.waitFor({ state: "visible" });
    const createDBookLocator = this.page.getByRole("menuitem", {
      name: "Create Databook",
    });
    await plusLocator.click();
    await createDBookLocator.waitFor({ state: "visible" });
    await createDBookLocator.click();
  }

  async createDatasheet(
    DataBookName,
    DataSheetName,
    getDataFrom,
    selectObj1,
    selectObj2
  ) {
    await this.page
      .locator(
        `//button//span[text()='${DataBookName}']/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click({ timeout: 90000 });
    const datasheetPopup = await this.page.locator("span", {
      hasText: "Create New Datasheet",
    });
    await datasheetPopup.waitFor({ state: "visible" });
    await this.page.getByPlaceholder("Enter sheet name").fill(DataSheetName);
    await this.delay(1000);
    await this.page
      .locator(`input[type="radio"][value="${getDataFrom}"]`)
      .click();
    const selector = await this.page.getByTestId("ever-select");
    if (getDataFrom === "datasheet") {
      await selector.nth(-2).click();
      await selector.nth(-2).locator("input").fill(selectObj1);
      await expect(
        this.page.locator(`//span[@title="${selectObj1}"]`)
      ).toBeVisible();
      await this.page.locator(`//span[@title="${selectObj1}"]`).click();

      await expect(selector.nth(-1).locator("input")).toBeEnabled();
      await selector.nth(-1).click();
      await selector.nth(-1).locator("input").fill(selectObj2);
      await expect(
        this.page.locator(`//span[@title="${selectObj2}"]`)
      ).toBeVisible();
      await this.page.locator(`//span[@title="${selectObj2}"]`).click();
    } else {
      await expect(selector.nth(-1).locator("input")).toBeEnabled();
      await selector.nth(-1).click();
      await selector.nth(-1).locator("input").fill(selectObj1);
      await this.delay(10000);
      await expect(
        this.page.locator(`//span[@title="${selectObj1}"]`).last()
      ).toBeVisible();
      await this.page.locator(`//span[@title="${selectObj1}"]`).last().click();
    }

    const createButton = this.page.getByRole("button", {
      name: "Create",
      exact: true,
    });
    await expect(createButton).toBeEnabled();
    await createButton.click();
  }

  async verifyDatasheetGeneration() {
    const generateText = await this.page.getByText(
      "Refreshing this sheet with latest data from all sources"
    );
    await generateText.waitFor({ state: "visible" });
    await generateText.waitFor({ state: "hidden", timeout: 900000 });
    await this.page.waitForLoadState("networkidle");
  }

  async databookMoreActions(dbname, toClick = true) {
    if (toClick) {
      await this.clickDatabook(dbname);
    }
    await this.page
      .locator(
        `//span[text()='${dbname}']/ancestor::div[2]/following-sibling::div/div[2]`
      )
      .click();
  }

  async datasheetMoreActions(dsname) {
    await this.page
      .locator(".AccordionRoot .text-base")
      .filter({ hasText: new RegExp(`^${dsname}$`), exact: true })
      .waitFor({ state: "visible" });

    await this.page
      .locator(`//div[@class='AccordionRoot']//span[text()="${dsname}"]`)
      .hover();

    await this.page
      .locator(
        `//div[@class='AccordionRoot']//span[text()="${dsname}"]/ancestor::div[3]/following-sibling::div`
      )
      .waitFor({ state: "visible", timeout: 5000 });

    await this.page
      .locator(
        `//div[@class='AccordionRoot']//span[text()="${dsname}"]/ancestor::div[3]/following-sibling::div`
      )
      .click({ timeout: 5000 });
  }

  async databookRefreshSheets(dbname, columnPresence, toClick = true) {
    await this.databookMoreActions(dbname, toClick);
    await this.page
      .getByRole("menuitem", { name: "Refresh all sheets" })
      .last()
      .click();
    await this.page.getByText("Refresh sheets?").waitFor({ state: "visible" });
    await this.page
      .getByText("Only sheets with pending updates will be refreshed")
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Yes, refresh" }).click();
    await this.dialogPrompt(
      "span",
      "Refreshing this sheet with latest data from all sources"
    );
    await this.page.waitForLoadState("networkidle");
    await this.letColumnsLoad(columnPresence);
  }

  async renameDatabook(newdbook, existingdbook, toClick = true) {
    await this.databookMoreActions(newdbook, toClick);
    await this.page.getByRole("menuitem", { name: "Rename" }).last().click();
    const inputLocator = await this.page.locator(".AccordionRoot input");
    await inputLocator.fill("");
    await inputLocator.fill(existingdbook);
    await inputLocator.press("Enter");
    await this.page.waitForLoadState("networkidle");
  }

  async renameDatasheet(existingdsheet, newdsheet) {
    await this.datasheetMoreActions(existingdsheet);
    await this.page.getByRole("menuitem", { name: "Rename" }).last().click();
    const inputLocator = await this.page.locator(".AccordionRoot input");
    await inputLocator.fill("");
    await inputLocator.fill(newdsheet);
    await inputLocator.press("Enter");
    await this.page.waitForLoadState("networkidle");
    await this.page.waitForSelector(".AccordionRoot span", {
      hasText: newdsheet,
    });
  }

  async deleteDatabook(dbname, toClick = true) {
    await this.page.waitForLoadState("networkidle");
    await this.databookMoreActions(dbname, toClick);
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    const promtText = this.page.getByText(
      "Are you sure you want to delete this Databook?"
    );
    await promtText.waitFor({ state: "visible" });
    await this.page
      .getByRole("button", { name: "Delete", exact: true })
      .click();
  }

  async deleteDatasheet() {
    await this.clickDatasheetMenu();
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    const promptText = await this.page.getByText(
      "Are you sure you want to delete this sheet?"
    );
    await promptText.waitFor({ state: "visible" });
    await this.page.locator("button", { hasText: "Delete" }).click();
  }

  async goToDatasheet(dbname, dsname) {
    await this.delay(5000);
    const dsheetLocator = this.page.locator(
      `//div[@class="AccordionRoot"]//div[@data-state="open" and .//span[text()='${dbname}']]//span[text()='${dsname}']`
    );

    if (await dsheetLocator.isVisible({ timeout: 10000 })) {
      await this.delay(1000);
      await dsheetLocator.click();
      await this.page.waitForLoadState("networkidle");
    } else {
      await this.clickDatabook(dbname);
      await dsheetLocator.isVisible({ timeout: 5000 });
      await this.delay(1000);
      await dsheetLocator.click();
      await this.page.waitForLoadState("networkidle");
    }
  }

  async validateDataSources(
    dbname,
    dsname,
    getDataFrom,
    sourceObj1,
    sourceObj2
  ) {
    await this.delay(3000);
    await this.createDatasheet(
      dbname,
      dsname,
      getDataFrom,
      sourceObj1,
      sourceObj2
    );
    await this.dialogPrompt(
      "span",
      "A new datasheet has been created successfully"
    );
    const generateSheetBtn = this.page.locator("button", {
      hasText: "Load data into this sheet",
    });
    await this.closeEditsheetPage();
    await generateSheetBtn.waitFor({ state: "visible" });
  }

  async clickDatasheetMenu() {
    await this.page
      .locator("#datasheet-canvas-view > div > div > button")
      .last()
      .click();
  }

  async cancelDeletion() {
    await this.page
      .locator(".ant-modal-content button", { hasText: "Cancel" })
      .click();
  }

  async dialogPrompt(element, text) {
    const prompt = this.page.locator(element, { hasText: text });
    await expect(prompt).toBeVisible({ timeout: 20000 });
    await prompt.waitFor({ state: "hidden", timeout: 420000 });
  }

  async closeDatasheetPopup() {
    await this.page.locator(".ant-modal-close-x").click();
  }

  async closeEditsheetPage() {
    await this.page.locator("button[aria-label='Close']").last().click();
  }

  async generateDatasheet() {
    const generateSheetBtn = this.page.locator("button", {
      hasText: "Load data into this sheet",
    });
    await generateSheetBtn.waitFor({ state: "visible" });
    await generateSheetBtn.click();
    const currentDateTime = moment();
    const formattedCurrentDateTime = currentDateTime.format(
      "MMMM DD, YYYY, hh:mm A"
    );
    await this.verifyDatasheetGeneration();
    return formattedCurrentDateTime;
  }

  async validateInRecents(dsname) {
    const recentLocator = await this.page.waitForSelector(
      `//span[text()='Recents']/ancestor::div[1]/following-sibling::div//span[text()="${dsname}"]`
    );
    return await recentLocator.isVisible();
  }

  async validateInPinned(dsname) {
    const pinnedLocator = this.page.locator(
      `//span[text()="Pinned"]/ancestor::div[2]/following-sibling::div//span[text()="${dsname}"]`
    );
    await this.delay(1500);
    return await pinnedLocator.isVisible();
  }

  async collapseLeftPane() {
    const collapseLocator =
      "((//div[@id='datasheet-canvas-view'])[1]/preceding-sibling::div/div)[1]";

    await this.page.locator(collapseLocator).click();
  }

  async expandLeftPane() {
    const expandLocator =
      "(//div[@id='datasheet-canvas-view'])[1]/preceding-sibling::div";
    await this.page.locator(expandLocator).click();
  }

  async viewBy(dropdownName) {
    await this.page
      .locator(
        "//div[contains(@class, 'ant-dropdown-trigger')]//span[contains(text(), 'Databooks')]"
      )
      .click();
    await this.page
      .getByRole("menuitem", { name: dropdownName, exact: true })
      .click();
    await this.delay(1000);
  }

  async verifyCommissionPlans(planName) {
    const plan = await this.page.locator(
      "button[data-radix-collection-item] .text-base",
      { hasText: planName }
    );

    return {
      isPlanVisible: await plan.isVisible(),
    };
  }

  async getDatasheetCanvasHeading() {
    const canvasHeading = this.page.locator(
      '(//div[@id="datasheet-canvas-view"])[1]//span'
    );
    const name = await canvasHeading.first().textContent();
    return name;
  }

  async verifyOpenNewTab() {
    const [newPage] = await Promise.all([
      this.page.context().waitForEvent("page"),
      this.page.getByText("Open in new tab").click(),
    ]);
    await newPage.waitForLoadState("load");
    return newPage;
  }

  async verifySheetDetails() {
    await this.page.getByRole("menuitem", { name: "Sheet details" }).click();
    await this.page.waitForSelector(
      '//div[contains(@class, "ant-drawer-wrapper-body")]//span[text()="Sheet Details"]',
      { state: "visible" }
    );
    await expect(this.page.locator(".ant-drawer-content")).toContainText(
      "commission-table"
    );
    await expect(this.page.locator(".ant-drawer-content")).toContainText(
      "Data Sourcefreelance-commissions"
    );
    await expect(this.page.locator(".ant-drawer-content")).toContainText(
      "Linked Commission Plans 2"
    );
    await expect(this.page.locator(".ant-drawer-content")).toContainText(
      "freelance-commission-plan1freelance-commission-plan1-simple-componentfreelance-commission-plan1-conditional-component"
    );
    await expect(this.page.locator(".ant-drawer-content")).toContainText(
      "freelance-commission-plan2freelance-commission-plan1-simple-componentfreelance-commission-plan1-conditional-componentfreelance-commission-plan1-tier-component"
    );
    await this.page
      .locator(
        "(//div[contains(@class, 'ant-drawer-wrapper-body')]//button)[1]"
      )
      .click();
  }

  async verifyDbookExpansion() {
    const specificLocator1 = this.page.locator(
      '(//div[@class="AccordionRoot"]//div[@data-state="open"])[1]'
    );
    await specificLocator1.waitFor({ state: "visible" });
  }

  async verifyTabs(Tabs) {
    for (const tabname of Tabs) {
      const locator = this.page.locator(
        `//div[@role='tab' and contains(., '${tabname}')]`
      );
      try {
        await locator.waitFor({ state: "visible" });
      } catch (e) {
        return false;
      }
    }
    return true;
  }

  async goToTab(tabname) {
    await this.page
      .locator(`//div[@role='tab' and contains(., '${tabname}')]`)
      .click();
    await this.page.waitForLoadState("networkidle");
  }

  async getTabCount(tabname) {
    const locator = this.page.locator(
      `//div[@role='tab' and contains(., '${tabname}')]//span`
    );
    await locator.waitFor({ state: "visible" });
    return await locator.textContent();
  }

  async isFilterVisible() {
    const locator = this.page.locator(
      "(//div[@data-testid='expression-input-box'])[2]"
    );
    await locator.waitFor({ state: "visible" });
    return await locator.isVisible();
  }

  async getFilterCalc() {
    const locator = this.page.locator(
      "//div[@data-testid='expression-input-box']/div/div"
    );

    // Get all visible elements
    const elements = await locator.elementHandles();
    const visibleTexts = [];

    for (const element of elements) {
      if (await element.isVisible()) {
        let text = await element.textContent();
        if (text) visibleTexts.push(text.trim().replace(/\s+/g, "")); // Remove all spaces
      }
    }

    return visibleTexts.join("");
  }

  async sortColumn(colName, sortBy) {
    const locator = this.page
      .locator(`//div[@role='columnheader']//div[text()="${colName}"]`)
      .last();
    await locator.waitFor({ state: "visible", timeout: 90000 });

    if (sortBy === "ASC") {
      await locator.click();
      await locator.waitFor({ state: "visible" });
    } else if (sortBy === "DESC") {
      await locator.click();
      await locator.waitFor({ state: "visible" });
      await this.page.waitForTimeout(7000);
      await locator.click();
      await locator.waitFor({ state: "visible" });
    }
    return locator;
  }

  async getColumnValues(colName, locatorName) {
    await this.letColumnsLoad(colName);
    await this.delay(5000);
    const colmValuesLocator = this.page.locator(
      `//div[contains(@class, 'ant-tabs-tabpane-active')]//div[contains(@col-id, '${locatorName}')]`
    );
    const count = await colmValuesLocator.count();
    const values = [];
    for (let i = 0; i < count; i++) {
      const value = await colmValuesLocator.nth(i).textContent();
      values.push(value);
    }
    return values.slice(1);
  }

  async clickTabButton(btn) {
    await this.letColumnsLoad("id");
    const locator = this.page.locator(".ant-tabs-extra-content button");
    let index;
    switch (btn) {
      case "Filter":
        index = 0;
        break;
      case "Show/hide columns":
        index = 1;
        break;
      case "Pivot table":
        index = 2;
        break;
      default:
        throw new Error(`Unsupported button type: ${btn}`);
    }

    await locator.nth(index).waitFor({ state: "visible", timeout: 5000 });
    await locator.nth(index).click();
  }

  async customizeColumn(column) {
    const locator = this.page.locator(".ant-tabs-extra-content button");
    await locator.nth(1).waitFor({ state: "visible", timeout: 5000 });
    await locator.nth(1).click();

    const sidebar = this.page.locator(
      "//div[contains(@class, 'side-bar')]//span[contains(text(), 'Choose columns')]"
    );
    await sidebar.waitFor({ state: "visible" });

    const checkbox = this.page.locator(
      `//div[contains(@class, 'side-bar')]//span[contains(., '${column}')]/ancestor::div[1]/preceding-sibling::label`
    );
    await checkbox.waitFor({ state: "visible" });
    await checkbox.click();

    await this.page.getByRole("button", { name: "Save" }).click();

    await this.dialogPrompt("span", "Changes saved successfully");
    await this.page.waitForLoadState("networkidle");
  }

  async pivot(row, column, value) {
    const locator = this.page.locator(".ant-tabs-extra-content button");
    await locator.nth(2).waitFor({ state: "visible", timeout: 5000 });
    await locator.nth(2).click();

    await this.page.locator('//button[@role="switch"]').click();
    await this.page.locator('//div[@data-testid="ever-select"]').nth(0).click();
    await this.page.locator(`span[title="${row}"]`).last().click();
    await this.page.keyboard.press("Tab");

    await this.page.locator('//div[@data-testid="ever-select"]').nth(1).click();
    await this.page.locator('input[type="search"]').nth(1).fill(column);
    await this.page.locator(`span[title="${column}"]`).last().click();
    await this.page.keyboard.press("Tab");

    await this.page.locator('//div[@data-testid="ever-select"]').nth(2).click();
    await this.page.locator(`span[title="${value}"]`).last().click();
    await this.page.keyboard.press("Tab");

    await this.page
      .getByRole("button", { name: "Apply", exact: true })
      .last()
      .click();
    await this.page.waitForLoadState("networkidle");
  }

  async verifyCustomizeColumn(colName) {
    const locator = this.page
      .locator(`//div[@role='columnheader']//div[text()="${colName}"]`)
      .nth(-1);
    return await locator.isVisible();
  }

  async goToAdjustDataMenu(option) {
    const adjustDataMenu = this.page.getByRole("menuitem", { name: option });
    await adjustDataMenu.waitFor({ state: "visible" });
    await adjustDataMenu.click();
  }

  async applyAdjustmentChanges() {
    const locator = this.page.getByText("Sheet has new updates");
    await locator.waitFor({ state: "visible", timeout: 120000 });
    await this.page.locator("button", { hasText: "Refresh" }).click();
    const prompt = this.page.getByText("Fetching latest data...", {
      exact: true,
    });
    await prompt.waitFor({ state: "visible", timeout: 15000 });
    await prompt.waitFor({ state: "hidden", timeout: 300000 });
    await this.page.waitForLoadState("networkidle");
  }

  async validateAdjusment(adjustData, id, scope, comment) {
    console.log(
      `Running Adjustment for id: ${id} with adjusment data as ${adjustData}-${scope}`
    );
    await this.sortColumn("id", "ASC");
    await this.page.waitForLoadState("networkidle");
    await this.letColumnsLoad("id");

    const actionLocator = this.page.locator(
      `//div[@row-id="${id - 1}"]/div[@col-id='action']//button`
    );
    await actionLocator.waitFor({ state: "visible" });
    await actionLocator.click({ timeout: 3000 });
    await this.goToAdjustDataMenu(adjustData);
    const applyPage = this.page.getByText("Where do you want to apply this?");
    await applyPage.waitFor({ state: "visible" });
    if (scope === "local") {
      await this.page
        .locator("//span[@title='On all dependent sheets (Global)']")
        .click();
      const drpDownLocator = this.page.locator("span", {
        hasText: "Only in this sheet",
      });
      await drpDownLocator.waitFor({ state: "visible" });
      await drpDownLocator.click();
    }
    if (adjustData === "Update Record") {
      const name = await this.page
        .locator("//div[contains(@col-id, 'username')]/div")
        .nth(-1)
        .textContent();
      const username = this.page.locator(
        "//div[contains(@col-id, 'username')]"
      );
      await username.nth(-1).dblclick();
      const editUser = this.page
        .locator("//div[contains(@col-id, 'username')]//input")
        .nth(-1);
      await editUser.waitFor({ state: "visible" });
      await editUser.press("Backspace");
      await editUser.fill(`${name}` + "_updated");
      await editUser.press("Enter");
    } else if (adjustData === "Split Record") {
      const name = await this.page
        .locator("//div[contains(@col-id, 'username')]/div")
        .nth(-2)
        .textContent();
      const username = this.page.locator(
        "//div[contains(@col-id, 'username')]"
      );
      await username.nth(-2).dblclick();
      const editUser = this.page
        .locator("//div[contains(@col-id, 'username')]//input")
        .nth(-1);
      await editUser.waitFor({ state: "visible" });
      await editUser.press("Backspace");
      await editUser.fill(`${name}` + "_updated");
      await editUser.press("Enter");
    }
    await this.page
      .getByPlaceholder("Enter reason for adjustment")
      .fill(comment);
    const updateBt = await this.page.locator("button div", {
      hasText: "Update",
    });
    await updateBt.click();
    await this.applyAdjustmentChanges();
  }

  async clickEditBtn() {
    const editBtn = await this.page.getByRole("button", {
      name: "Edit",
      exact: true,
    });
    await expect(editBtn).toBeVisible();
    await expect(editBtn).toBeEnabled({ timeout: 120000 });
    await editBtn.click();
    await this.delay(3000);
    await expect(this.page.locator("button[aria-label='Close']")).toBeVisible();
  }

  async cloneDatasheet() {
    await this.page
      .getByRole("menuitem", { name: "Clone this sheet" })
      .last()
      .click();
    await this.page
      .getByText("Cloning datasheet...")
      .waitFor({ state: "visible" });
    await this.page
      .getByText("Cloning datasheet...")
      .waitFor({ state: "hidden" });
    await this.page
      .getByText("Datasheet cloned!")
      .waitFor({ state: "visible" });
    await this.page.getByText("Datasheet cloned!").waitFor({ state: "hidden" });
  }

  async cloneDataBook() {
    await this.page
      .getByRole("menuitem", { name: "Clone databook" })
      .last()
      .click();
    await this.page
      .getByText("Cloning databook...")
      .waitFor({ state: "visible" });
    await this.page
      .getByText("Cloning databook...")
      .waitFor({ state: "hidden" });
    await this.page
      .getByText("Databook cloned successfully")
      .waitFor({ state: "visible" });
    await this.page
      .getByText("Databook cloned successfully")
      .waitFor({ state: "hidden" });
  }

  async waitForRefresh() {
    const refreshBtn = await this.page.getByRole("button", {
      name: "Refresh Datasheet",
    });
    await refreshBtn.waitFor({ state: "visible" });
    return refreshBtn;
  }

  async fetchLatestData() {
    const refreshBtn = await this.waitForRefresh();
    await refreshBtn.click();
    const prompt = this.page.getByText("Fetching latest data...", {
      exact: true,
    });
    await prompt.waitFor({ state: "visible", timeout: 15000 });
    await prompt.waitFor({ state: "hidden", timeout: 300000 });
  }

  async refreshDatasheet() {
    const refreshBtn = await this.waitForRefresh();
    await refreshBtn.click();
    const text = await this.page.getByText(
      "Refreshing this sheet with latest data from all sources"
    );
    await text.waitFor({ state: "visible", timeout: 20000 });
    await text.waitFor({ state: "hidden", timeout: 240000 });
  }

  async getSpanColumnValues(colName, locatorName) {
    await this.letColumnsLoad(colName);
    const colmValuesLocator = this.page.locator(
      `//div[contains(@class, 'ant-tabs-tabpane-active')]//div[contains(@col-id, '${locatorName}')]/span`
    );
    const count = await colmValuesLocator.count();
    const values = [];
    for (let i = 0; i < count; i++) {
      const value = await colmValuesLocator.nth(i).textContent();
      values.push(value.trim());
    }
    return values;
  }

  async getBooleanColumnValues(colName, locatorName) {
    await this.letColumnsLoad(colName);
    const colmValuesLocator = this.page.locator(
      `//div[contains(@col-id, '${locatorName}')]//div[@class='flex']`
    );
    const count = await colmValuesLocator.count();
    const values = [];
    for (let i = 0; i < count; i++) {
      const value = await colmValuesLocator.nth(i).textContent();
      if (value) {
        values.push(value.trim());
      }
    }
    return values;
  }

  async isColumnPresent(colName) {
    const locator = this.page.locator(
      `//div[@role='columnheader']//div[text()="${colName}"]`
    );
    await expect(locator).toBeVisible();
  }

  async locateColumnData(colname, colvalue) {
    await this.page
      .locator(
        `//div[contains(@col-id,"${colname}")]/div[contains(text(), "${colvalue}")]`
      )
      .click();
  }

  async letColumnsLoad(colName) {
    const locator = this.page
      .locator(`//div[@role='columnheader']//div[text()="${colName}"]`)
      .last();
    try {
      await locator.waitFor({ state: "visible", timeout: 180000 });
    } catch (error) {
      await this.page.reload();
      await locator.waitFor({ state: "visible", timeout: 180000 });
    }
    await this.delay(5000);
  }

  async isTooltipVisible(columnValue, tooltipValue) {
    const locator = await this.page
      .locator(`//span[text()="${columnValue}"]`)
      .first();

    await locator.scrollIntoViewIfNeeded();
    await locator.hover();
    const tooltipLocator = this.page
      .locator(`//div[@role="tooltip"]`)
      .filter({ hasText: tooltipValue });

    await tooltipLocator.waitFor({ state: "attached", timeout: 5000 });
    const tooltipCount = await tooltipLocator.count();
    return tooltipCount > 0;
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async tooltipMessage(message) {
    const locator = await this.page.getByRole("tooltip", { name: message });
    await locator.isVisible();
  }

  async createNewView(colName) {
    const addNewView = this.page.getByRole("button", { name: "Add tab" });
    await addNewView.waitFor({ state: "visible", timeout: 10000 });
    await addNewView.click();
    await this.dialogPrompt("span", "Datasheet View created successfully");
    await this.letColumnsLoad(colName);
  }

  async renameView(oldName, newName) {
    const currViewBtn = this.page.locator(
      `//div[./div[@role="tab" and .//div[text()="${oldName}"]]]/button`
    );
    const newViewBtn = this.page.locator(
      `//div[./div[@role="tab" and .//div[text()='${newName}']]]/button`
    );
    await currViewBtn.click();
    await this.page.getByRole("menuitem", { name: "Rename" }).last().click();
    const inputLocator = this.page.locator("//div[@role='tab']//input").last();
    expect(inputLocator).toBeAttached();
    await inputLocator.fill("");
    await inputLocator.fill(newName);
    await inputLocator.press("Enter");
    await this.dialogPrompt("span", "Datasheet View updated successfully");
    await currViewBtn.waitFor({ state: "hidden" });
    await newViewBtn.waitFor({ state: "visible" });
  }

  async saveNewView() {
    const btn = this.page.getByRole("button", { name: "Apply and save" });
    await expect(btn).toBeEnabled();
    await btn.click();
  }

  /**
   * Renames multiple fields in a datasheet and returns locators for the renamed fields
   * @param {Page} page - The Playwright Page object to interact with (defaults to this.page)
   * @param {string[]} renameFields - Array of current field names to rename
   * @param {string[]} newNames - Array of new names to rename the fields to
   * @returns {Promise<Locator[]>} Array of Playwright Locators for the renamed fields
   * @example
   * // Rename two fields and get their locators
   * const newLocators = await renameFieldsAndReturnRenameFieldLocators(
   *   page,
   *   ['oldName1', 'oldName2'],
   *   ['newName1', 'newName2']
   * )
   */
  async renameFieldsAndReturnRenameFieldLocators(
    page = this.page,
    renameFields,
    newNames
  ) {
    const newFieldLocators = [];
    for (let i = 0; i < renameFields.length; i++) {
      const fieldLocator = page.locator(
        `(//span[.='RenameFields'])[2]/../following-sibling::div//span[.='${renameFields[i]}']`
      );
      await fieldLocator.waitFor({ state: "visible" });
      await fieldLocator.dblclick();
      const inputLocator = page.locator(
        `(//span[.='RenameFields'])[2]/../following-sibling::div//input[@type='text']`
      );
      await inputLocator.waitFor({ state: "visible" });
      await inputLocator.fill("");
      await inputLocator.fill(newNames[i]);
      await inputLocator.press("Enter");
      await this.delay(1000);
      const newFieldLocator = page.locator(
        `(//span[.='RenameFields'])[2]/../following-sibling::div//span[.='${newNames[i]}']`
      );
      newFieldLocators.push(newFieldLocator);
    }
    return newFieldLocators;
  }

  async EditSheetName(currentName, newName) {
    await this.page
      .locator(
        `//div[@id="datasheet-canvas-view"]//span[text()='${currentName}']`
      )
      .click();

    await this.page
      .locator('//div[@id="datasheet-canvas-view"]//input')
      .first()
      .fill(newName);
    await this.page.keyboard.press("Enter");
    await this.page
      .getByText("Datasheet successfully renamed")
      .waitFor({ state: "visible" });
    await this.page
      .getByText("Datasheet successfully renamed")
      .waitFor({ state: "hidden" });
    await expect(this.page.locator(`//span[text()='${newName}']`)).toHaveCount(
      3
    );
  }

  async ValidatePlanVariables(
    newVariables,
    planType,
    planName,
    componentName1,
    component1Vadildation,
    componentName2,
    component2Vadildation
  ) {
    if (planType === "plans") {
      await this.page.goto("/plans", { waitForLoadState: "networkidle" });
    } else if (planType === "forecasts") {
      await this.page.goto("/forecasts", { waitForLoadState: "networkidle" });
    }

    console.log(
      `Validating variable changes for the ${planType} - ${planName}`
    );
    await this.page.getByTitle(planName).click();
    console.log("Validating variable names in component", componentName1);
    await this.page
      .locator(`span:has-text("${componentName1}")`)
      .first()
      .click();
    for (const [variable, count] of component1Vadildation) {
      await expect(
        this.page.locator(
          `//div[contains(@class, 'expression-token') and text()='${variable}']`
        )
      ).toHaveCount(count);
    }

    await this.page.getByRole("button", { name: "Simulate" }).click();
    await this.page.getByRole("button", { name: "Run" }).click();
    await this.page
      .getByText(`Evaluating ${componentName1}`)
      .waitFor({ state: "visible" });
    await this.page
      .getByText(`Evaluating ${componentName1}`)
      .waitFor({ state: "hidden" });
    for (const variable of newVariables) {
      const locator = this.page
        .getByRole("treegrid")
        .getByText(variable, { exact: true });
      await locator.waitFor({ state: "visible", timeout: 20000 });
    }

    console.log("Validating variable names in component", componentName2);
    await this.page
      .locator(`span:has-text("${componentName2}")`)
      .first()
      .click();
    for (const [variable, count] of component2Vadildation) {
      await expect(
        this.page.locator(
          `//div[contains(@class, 'expression-token') and text()='${variable}']`
        )
      ).toHaveCount(count);
    }
    await this.page.getByRole("button", { name: "Simulate" }).click();
    await this.page.getByRole("button", { name: "Run" }).click();
    await this.page
      .getByText(`Evaluating ${componentName2}`)
      .waitFor({ state: "visible" });
    await this.page
      .getByText(`Evaluating ${componentName2}`)
      .waitFor({ state: "hidden" });
    for (const variable of newVariables) {
      const locator = this.page
        .getByRole("treegrid")
        .getByText(variable, { exact: true });
      await locator.waitFor({ state: "visible", timeout: 20000 });
    }

    await this.page.getByRole("button", { name: "Configuration" }).click();
    await this.page.getByText("Data source").waitFor({ state: "visible" });
    await expect(
      await this.page.getByTitle("commission-table-new")
    ).toBeVisible();
    await expect(await this.page.getByTitle("email")).toBeVisible();
    await expect(await this.page.getByTitle("sale date")).toBeVisible();
    await this.page.locator(".transition-all > button").last().click();

    if (planType === "plans") {
      await this.page
        .getByRole("button", { name: "Statement columns" })
        .click();
      for (const variable of newVariables) {
        await expect(
          this.page
            .getByRole("document")
            .locator(`span[title='${variable}']`, { exact: true })
        ).toHaveCount(2);
      }
      await this.page.getByLabel("Close").last().click();
    }

    await this.page
      .locator("//button[./div[text()='Export']]/preceding-sibling::button")
      .last()
      .click();
    for (const variable of newVariables) {
      await expect(
        this.page
          .getByRole("document")
          .locator(`span[title='${variable}']`, { exact: true })
      ).toHaveCount(2);
    }

    await this.page.getByLabel("Close").last().click();
    await this.page.getByRole("button", { name: "Exit Canvas" });
  }

  async ValidateCrytalVariables(crystalName, newVariables, isPublished) {
    newVariables.splice(-2, 2);
    await this.page.goto("/crystal", { waitForLoadState: "networkidle" });
    await this.page
      .getByText(crystalName)
      .click({ waitForLoadState: "networkidle" });

    await this.page.getByText("View 1").waitFor({ state: "visible" });
    for (const variable of newVariables) {
      const locator = this.page
        .getByRole("treegrid")
        .getByText(variable, { exact: true });
      await locator.waitFor({ state: "visible", timeout: 20000 });
    }

    const [newTab] = await Promise.all([
      this.page.waitForEvent("popup"), // Waits for the new tab to open
      this.page.getByRole("button", { name: "Preview as payee" }).click(), // Click the element to open the new tab
    ]);
    await newTab.waitForLoadState("networkidle");
    await newTab
      .getByRole("button", { name: "Select opportunities" })
      .click({ waitForLoadState: "networkidle", timeout: 15000 });
    for (const variable of newVariables) {
      const locator = this.page
        .getByRole("treegrid")
        .getByText(variable, { exact: true });
      await locator.waitFor({ state: "visible", timeout: 20000 });
    }
    await newTab.close();

    await this.page.bringToFront();
    await this.page.waitForLoadState("networkidle");
    if (isPublished === false) {
      await this.page
        .locator("//div[@role='tab']//*[name()='svg']")
        .last()
        .click();
      await this.page
        .getByRole("menu")
        .getByText("Edit")
        .click({ waitForLoadState: "networkidle", timeout: 5000 });

      for (const variable of newVariables) {
        const element = await this.page
          .locator(".text-xs", {
            hasText: variable,
            exact: true,
          })
          .last();
        await expect(element).toBeVisible();
      }
    }
  }

  async verifyClone(DBName, sheetName) {
    console.log(
      `Verify cloned datasheet : DB - ${DBName} of sheet - ${sheetName} is present`
    );

    const dbookLocator = await this.page.locator(
      `//button[.//span[text()='${DBName}']]`
    );
    const ariaExpanded = await dbookLocator.getAttribute("aria-expanded");
    if (ariaExpanded !== "true") {
      await this.clickDatabook(DBName);
    }
    await expect(
      await this.page.locator(`//span[text()='${sheetName}']`)
    ).toBeVisible();
  }

  async managePermissions() {
    await this.page
      .getByRole("menuitem", { name: "Manage permission" })
      .last()
      .click();
    await expect(await this.page.locator(".ant-drawer-title")).toContainText(
      "Manage Permissions"
    );
  }

  async pinColumn(ColumnName) {
    const recentLocator = await this.page
      .locator(`//div[contains(@col-id, "${ColumnName}")]`)
      .first();
    await recentLocator.waitFor({ state: "visible" });
    await recentLocator.hover();
    const pinLocator = await this.page
      .locator(`//div[contains(@col-id, "${ColumnName}")]//*[name()='svg']`)
      .last();
    await pinLocator.click();
    await this.page.getByText("Pin to Left").waitFor({ state: "visible" });
    await this.page.getByText("Pin to Right").waitFor({ state: "visible" });

    await this.page.getByText("Pin to Left").click();
    await this.delay(1500);

    await recentLocator.hover();
    await pinLocator.click();
    await this.page.getByText("Pin to Left").waitFor({ state: "hidden" });
    await this.page.getByText("Pin to Right").waitFor({ state: "visible" });
    await this.page.getByText("Unpin").waitFor({ state: "visible" });

    await this.page.getByText("Pin to Right").click();
    await this.delay(1500);

    await recentLocator.hover();
    await pinLocator.click();
    await this.page.getByText("Pin to Right").waitFor({ state: "hidden" });
    await this.page.getByText("Pin to Left").waitFor({ state: "visible" });
    await this.page.getByText("Unpin").waitFor({ state: "visible" });
  }

  /**
   * Validate if the variable is present in the Datasheet
   *
   * @param {String Array} variables
   */
  async validateVariables(variables) {
    await this.clickEditBtn();
    for (const variable of variables) {
      await expect(
        await this.page.getByText(variable, { exact: true }).last()
      ).toBeVisible();
    }
    await this.closeEditsheetPage();
  }

  /**
   * Validate if the variable is not present in the Datasheet
   *
   * @param {String Array} variables
   */
  async validateVariablesisNotPresent(variables) {
    await this.clickEditBtn();
    for (const variable of variables) {
      await expect(
        await this.page.getByText(variable, { exact: true })
      ).toBeHidden();
    }
    await this.closeEditsheetPage();
  }

  /**
   * validate whether Warning symbol is present near the Variables
   *
   * @param {String Array} variables
   */
  async validateWarningSymbol(variables) {
    await this.clickEditBtn();
    for (const variable of variables) {
      await expect(
        await this.page
          .locator(`:text("${variable}")`)
          .last()
          .locator("..")
          .locator("..")
          .locator("..")
          .locator("svg")
          .last()
      ).toBeVisible();
    }
    await this.closeEditsheetPage();
  }

  /**
   * Validate the column value present in datasheet
   *
   * @param {String} columnName - Column Name which need to be validated
   * @param {Array} expectedColumnValues - Expected column values
   * @param {String} colID - Css selector for the column
   */
  async validateColumnValuesinDatasheet(
    columnName,
    expectedColumnValues,
    colID
  ) {
    await this.page
      .locator(`div[col-id*='co_${colID}_${columnName}'].ag-cell`)
      .first()
      .waitFor({ state: "visible" });
    await this.delay(2000);
    const columnValues = await this.page
      .locator(`div[col-id*='co_${colID}_${columnName}'].ag-cell`)
      .allInnerTexts();
    expect(columnValues).toEqual(expectedColumnValues);
  }

  /**
   * Remove variable from datasheet
   *
   * @param {Array} variables
   */
  async removeVariableFromDatasheet(variables) {
    await this.clickEditBtn();
    for (const variable of variables) {
      await this.page
        .locator(`.ant-checkbox:left-of(:text("${variable}"))`)
        .first()
        .click();
    }
    await this.delay(2000);
    await this.clickSaveBtn();
  }

  /**
   * Click save button
   */
  async clickSaveBtn() {
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  // ============Filters============
  async createVerifyFilterFunction(functionName) {
    let sortOnce = 0;
    const dropdownValues = [
      "ID",
      "First Name",
      "Last Name",
      "Email",
      "Date",
      "Amount",
      "Percentage",
    ];
    const outputColumn = [
      "id",
      "first_name",
      "last_name",
      "email",
      "date",
      "amount",
      "percentage",
    ];
    const valuesExpected = {
      IsEmpty: {
        first_name: ["", "", ""],
        last_name: ["", "", ""],
        email: ["", "", ""],
        date: [""],
        amount: ["", "", "", "", ""],
        percentage: ["", "", "", ""],
      },
      IsNotEmpty: {
        id: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        first_name: [
          "Siffre",
          "Gianina",
          "Gunther",
          "Kaja",
          "Jeromy",
          "Silvia",
          "Dasya",
        ],
        last_name: [
          "Fildes",
          "Crowch",
          "Lermit",
          "Sarrell",
          "Child",
          "Foskett",
          "Werny",
        ],
        email: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
        date: [
          "Oct 24, 2024",
          "Apr 10, 2024",
          "Jun 05, 2024",
          "Sep 05, 2024",
          "Nov 02, 2023",
          "Apr 11, 2025",
          "Jan 14, 2024",
          "May 02, 2024",
          "Nov 29, 2024",
        ],
        amount: ["94.22", "290.57", "457.20", "931.20", "576.94"],
        percentage: ["55.38", "27.7", "42.73", "89.08", "36.32", "19.25"],
      },
    };

    for (let i = 0; i < dropdownValues.length; i++) {
      let value = dropdownValues[i];
      console.log("createVerifyFilterFunction - ", value);
      await expect(
        this.page.locator('div[data-testid="expression-input-box"]').last()
      ).toBeVisible();
      await this.page
        .locator('[class="anticon cursor-pointer"]>svg')
        .last()
        .click();

      await this.page.keyboard.type(functionName);
      await expect(this.page.getByText(functionName + "Boolean")).toBeVisible();
      await this.page.getByText(functionName + "Boolean").click();
      await this.page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Please Select" })
        .click();
      await this.page.keyboard.type(value);

      await this.page
        .locator("span", { hasText: value + " All data PW Obj" })
        .click();
      await expect(
        this.page.getByRole("button", { name: "Apply", exact: true })
      ).toBeEnabled();
      await this.page
        .getByRole("button", { name: "Apply", exact: true })
        .click();
      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");

      // Verification
      console.log("test1: " + value);
      console.log("test2: " + outputColumn[i]);
      if (value === "ID" && functionName === "IsEmpty") {
        await expect(
          this.page.getByText("No data is available").last()
        ).toBeVisible();
      } else {
        // Sort only for the first time before verifying the results
        console.log("sortvalue: " + sortOnce);
        if (sortOnce < 1) {
          await this.sortColumn("ID", "ASC");
        }
        sortOnce++;

        const valuesReceived = await this.getColumnValues(
          value,
          outputColumn[i]
        );

        console.log("Received: " + valuesReceived);
        expect(valuesReceived).toEqual(
          valuesExpected[functionName][outputColumn[i]]
        );
      }

      // Reset
      // await this.page
      //   .locator(
      //     "//div[contains(@class, 'ant-tabs-tabpane-active')]//button[./*[text()='Apply and save']]/following-sibling::button"
      //   )
      //   .click();
      await this.clearFilter();
      await this.delay(5000);
      // await expect(
      //   this.page.locator(".ag-cell > div > .ant-btn").first()
      // ).toBeVisible();
      await this.page.getByText("Kaja").last().waitFor({ state: "visible" });
    }
  }

  async createVerifyConstantFilterFunction(functionName) {
    let sortOnce = 0;
    const containsDropdownFields = ["First Name", "Last Name", "Email"];
    const outputColumn = ["first_name", "last_name", "email"];
    const valuesField = ["Dasya", "Lermit", "<EMAIL>"];
    const valuesField2 = ["Si", "Lerm", "s"];
    const valuesField3 = ["a", "tt", ".au"];
    const valuesExpected = {
      Contains: {
        first_name: ["Dasya"],
        last_name: ["Lermit"],
        email: ["<EMAIL>"],
      },
      NotContains: {
        first_name: [
          "Siffre",
          "",
          "Gianina",
          "Gunther",
          "",
          "Kaja",
          "Jeromy",
          "",
          "Silvia",
        ],
        last_name: [
          "",
          "Fildes",
          "Crowch",
          "",
          "Sarrell",
          "Child",
          "Foskett",
          "Werny",
          "",
        ],
        email: [
          "<EMAIL>",
          "<EMAIL>",
          "",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "",
          "<EMAIL>",
          "",
        ],
      },
      StartsWith: {
        first_name: ["Siffre", "Silvia"],
        last_name: ["Lermit"],
        email: ["<EMAIL>", "<EMAIL>"],
      },
      EndsWith: {
        first_name: ["Gianina", "Kaja", "Silvia", "Dasya"],
        last_name: ["Foskett"],
        email: ["<EMAIL>"],
      },
    };

    for (let i = 0; i < containsDropdownFields.length; i++) {
      let value = containsDropdownFields[i];
      let fieldValue_Contains_NotContains = valuesField[i];
      let fieldValue_StartsWith = valuesField2[i];
      let fieldValue_EndsWith = valuesField3[i];
      console.log("createVerifyConstantFilterFunction - ", value);
      await expect(
        this.page.locator('div[data-testid="expression-input-box"]').last()
      ).toBeVisible();
      await this.page
        .locator('[class="anticon cursor-pointer"]>svg')
        .last()
        .click();

      await this.page.keyboard.type(functionName);
      await expect(
        this.page.getByText(functionName + "Boolean", { exact: true })
      ).toBeVisible();
      await this.page
        .getByText(functionName + "Boolean", { exact: true })
        .click();

      await this.page
        .locator("div")
        .filter({ hasText: /^Text field$/ })
        .nth(2)
        .click();
      await this.page.keyboard.type(value);
      await this.page
        .locator("span", { hasText: value + " All data PW Obj" })
        .click();

      if (functionName === "StartsWith") {
        await this.page
          .getByRole("dialog")
          .locator('[type="text"]')
          .type(fieldValue_StartsWith);
      } else if (functionName === "EndsWith") {
        await this.page
          .getByRole("dialog")
          .locator('[type="text"]')
          .type(fieldValue_EndsWith);
      } else {
        await this.page
          .getByRole("dialog")
          .locator('[type="text"]')
          .type(fieldValue_Contains_NotContains);
      }
      await expect(
        this.page.getByRole("button", { name: "Apply", exact: true })
      ).toBeEnabled();
      await this.page
        .getByRole("button", { name: "Apply", exact: true })
        .click();
      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");

      // Verification
      console.log("test1: " + value);
      console.log("test2: " + outputColumn[i]);

      // Sort only for the first time before verifying the results
      console.log("sortvalue: " + sortOnce);
      if (sortOnce < 1) {
        await this.sortColumn("ID", "ASC");
      }
      sortOnce++;

      const valuesReceived = await this.getColumnValues(value, outputColumn[i]);

      console.log("Received: " + valuesReceived);
      expect(valuesReceived).toEqual(
        valuesExpected[functionName][outputColumn[i]]
      );

      // Reset
      // await this.page
      //   .locator(
      //     "//div[contains(@class, 'ant-tabs-tabpane-active')]//button[./*[text()='Apply and save']]/following-sibling::button"
      //   )
      //   .click();
      await this.clearFilter();
      await this.delay(5000);
      // await expect(
      //   this.page.locator(".ag-cell > div > .ant-btn").first()
      // ).toBeVisible();
      await this.page.getByText("Kaja").last().waitFor({ state: "visible" });
    }
  }

  async createVerifyDatasheetFieldFilterFunction(functionName) {
    let sortOnce = 0;
    const containsDropdownFields = ["First Name", "Last Name", "Email"];
    const outputColumn = ["first_name", "last_name", "email"];
    const valuesField = ["Dasya", "Lermit", "<EMAIL>"];
    const valuesExpected = {
      Contains: {
        first_name: ["hello"],
        last_name: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
        email: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
      NotContains: {
        first_name: [
          "<EMAIL>",
          "",
          "<EMAIL>",
          "<EMAIL>",
          "",
          "<EMAIL>",
          "",
        ],
        last_name: ["", "", ""],
        email: ["", "", ""],
      },
      StartsWith: {
        first_name: [],
        last_name: [],
        email: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
      EndsWith: {
        first_name: [],
        last_name: [],
        email: [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
    };

    for (let i = 0; i < containsDropdownFields.length; i++) {
      let value = containsDropdownFields[i];
      let fieldValue = valuesField[i];
      console.log("createVerifyDatasheetFieldFilterFunction - ", value);
      await expect(
        this.page.locator('div[data-testid="expression-input-box"]').last()
      ).toBeVisible();
      await this.page
        .locator('[class="anticon cursor-pointer"]>svg')
        .last()
        .click();

      await this.page.keyboard.type(functionName);
      await expect(
        this.page.getByText(functionName + "Boolean", { exact: true })
      ).toBeVisible();
      await this.page
        .getByText(functionName + "Boolean", { exact: true })
        .click();

      await this.page
        .locator("div")
        .filter({ hasText: /^Text field$/ })
        .nth(2)
        .click();
      await this.page.keyboard.type("Email");
      await this.page
        .locator("span", { hasText: "Email All data PW Obj" })
        .click();

      await this.page
        .getByRole("dialog")
        .locator('[data-testid="ever-select"]')
        .filter({ hasText: /^Constant$/ })
        .click();
      await this.page.getByTitle("Datasheet Field").last().click();

      // await this.page.getByTestId('ever-select').locator('div').filter({ hasText: 'Text field' }).nth(2).click();
      await this.page
        .getByRole("dialog")
        .locator('[role="combobox"]')
        .last()
        .click();
      await this.page
        .locator("span")
        .filter({ hasText: value + " All data PW Obj" })
        .last()
        .click();

      await expect(
        this.page.getByRole("button", { name: "Apply", exact: true })
      ).toBeEnabled();
      await this.page
        .getByRole("button", { name: "Apply", exact: true })
        .click();
      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");

      // Verification
      console.log("test1: " + value);
      console.log("test2: " + outputColumn[i]);
      if (value === "First Name" && functionName === "Contains") {
        await expect(
          this.page.getByText("No data is available").last()
        ).toBeVisible();
      } else if (
        value in ["First Name", "Last Name"] &&
        functionName in ["StartsWith", "EndsWith"]
      ) {
        await expect(
          this.page.getByText("No data is available").last()
        ).toBeVisible();
      } else {
        // Sort only for the first time before verifying the results
        console.log("sortvalue: " + sortOnce);
        if (sortOnce < 1) {
          await this.sortColumn("ID", "ASC");
        }
        sortOnce++;

        const valuesReceived = await this.getColumnValues("Email", "email");

        console.log("Received: " + valuesReceived);
        expect(valuesReceived).toEqual(
          valuesExpected[functionName][outputColumn[i]]
        );
      }
      // Reset
      // await this.page
      //   .locator(
      //     "//div[contains(@class, 'ant-tabs-tabpane-active')]//button[./*[text()='Apply and save']]/following-sibling::button"
      //   )
      //   .click();
      await this.clearFilter();
      await this.delay(5000);
      // await expect(
      //   this.page.locator(".ag-cell > div > .ant-btn").first()
      // ).toBeVisible();
      await this.page.getByText("Kaja").last().waitFor({ state: "visible" });
    }
  }

  async verifyDuplicateAndEmptyViewName(currName, newName) {
    const currViewBtn = this.page.locator(
      `//div[./div[@role="tab" and .//div[text()="${currName}"]]]/button`
    );
    await currViewBtn.click();
    await this.page.getByRole("menuitem", { name: "Rename" }).last().click();
    const inputLocator = this.page.locator("//div[@role='tab']//input").last();
    expect(inputLocator).toBeAttached();
    await inputLocator.fill("");
    await inputLocator.fill(newName);
    await inputLocator.press("Enter");
    if (newName === "") {
      await this.dialogPrompt("span", "Datasheet view name cannot be empty");
    } else {
      await this.dialogPrompt("span", "View name already exists");
    }
    await currViewBtn.waitFor({ state: "visible" });
  }

  async createFilterFormula(variable) {
    await this.delay(5000);
    await expect(
      this.page.locator('div[data-testid="expression-input-box"]').last()
    ).toBeVisible();
    await this.page
      .locator('[class="anticon cursor-pointer"]>svg')
      .last()
      .click();

    if (variable === "ID") {
      await this.page.keyboard.type("ID");
      await expect(
        this.page.getByText("ID All data PW ObjInteger")
      ).toBeVisible();
      await this.page.getByText("ID All data PW ObjInteger").click();
      await this.page.keyboard.type(">");
      await this.page.keyboard.press("Enter");
      await this.page.keyboard.type("6");
      await this.page.keyboard.press("Enter");

      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");
      await this.sortColumn("ID", "ASC");
      const valuesExpected = ["7", "8", "9", "10"];
      const valuesReceived = await this.getColumnValues("ID", "id");
      console.log("Received: " + valuesReceived);
      expect(valuesReceived).toEqual(valuesExpected);
    } else if (variable === "Amount") {
      await this.page.keyboard.type(variable);
      await expect(
        this.page.getByText(variable + " All data PW ObjInteger")
      ).toBeVisible();
      await this.page.getByText(variable + " All data PW ObjInteger").click();
      await this.page.keyboard.type(">");
      await this.page.keyboard.press("Enter");
      await this.page.keyboard.type("300");
      await this.page.keyboard.press("Enter");

      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");
      await this.sortColumn("ID", "ASC");
      const valuesExpected = ["457.20", "931.20", "576.94"];
      const valuesReceived = await this.getColumnValues("Amount", "amount");
      console.log("Received: " + valuesReceived);
      expect(valuesReceived).toEqual(valuesExpected);
    }
  }

  async createVerifyFilterOperators() {
    let sortOnce = 0;
    const columnValues = [
      "First Name",
      "Last Name",
      "Amount",
      "Amount",
      "First Name",
      "Email",
      "Amount",
      "Amount",
      "Amount",
      "Amount",
      "First Name",
    ];
    const outputColumn = [
      "first_name",
      "last_name",
      "amount",
      "amount",
      "first_name",
      "email",
      "amount",
      "amount",
      "amount",
      "amount",
      "first_name",
    ];

    const filtersName = [
      "AND",
      "OR",
      "IN",
      "NOTIN",
      "Equals",
      "NotEquals",
      "GreaterThan",
      "LesserThan",
      "GreaterThanANDEqualTo",
      "LesserThanANDEqualTo",
    ];
    const inputValue = {
      AND: ["ID", ">", "5", "AND", "Percentage", ">", "60"],
      OR: ["ID", ">", "5", "OR", "Percentage", ">", "60"],
      IN: ["Amount", "IN", "[457.203500,94.219300]"],
      NOTIN: ["Amount", "NOTIN", "[457.203500,94.219300]"],
      Equals: ["First Name", "==", "Kaja"],
      NotEquals: ["Email", "!=", "<EMAIL>"],
      GreaterThan: ["Amount", ">", "500"],
      LesserThan: ["Amount", "<", "500"],
      GreaterThanANDEqualTo: ["Amount", ">=", "94.219300"],
      LesserThanANDEqualTo: ["Amount", "<=", "290.566300"],
      AllDataTypes: [
        "ID",
        ">",
        "0",
        "AND",
        "First Name",
        "IN",
        '["Dasya","Silvia","Gunther","Siffre","Gianina"]',
        "AND",
        "Date",
        ">",
        "02/05/2024",
        "AND",
        "Amount",
        ">",
        "200",
        "AND",
        "Percentage",
        ">",
        "0",
      ],
    };

    const valuesExpected = {
      AND: ["Jeromy"],
      OR: ["Sarrell", "Child", "Foskett", "Werny", ""],
      IN: ["94.22", "457.20"],
      NOTIN: ["290.57", "931.20", "576.94"],
      Equals: ["Kaja"],
      NotEquals: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ],
      GreaterThan: ["931.20", "576.94"],
      LesserThan: ["94.22", "290.57", "457.20"],
      GreaterThanANDEqualTo: ["94.22", "290.57", "457.20", "931.20", "576.94"],
      LesserThanANDEqualTo: ["94.22", "290.57"],
      AllDataTypes: ["Dasaya"],
    };

    for (let i = 0; i < filtersName.length; i++) {
      const operator = filtersName[i];
      console.log("createVerifyFilterOperators - ", operator);
      console.log("filtername:" + operator);
      const value = columnValues[i];

      await expect(
        this.page.locator('div[data-testid="expression-input-box"]').last()
      ).toBeVisible();
      await this.page
        .locator('[class="anticon cursor-pointer"]>svg')
        .last()
        .click();

      for (let i = 0; i < inputValue[operator].length; i++) {
        console.log("Operator:" + inputValue[operator][i]);
        const formula = inputValue[operator][i];
        await this.page.keyboard.type(formula);
        await this.delay(500);
        await this.page.keyboard.press("Enter");
      }
      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
      await this.page.getByRole("button", { name: "Apply and save" }).click();
      await this.delay(3000);
      await this.letColumnsLoad("ID");

      // Verification
      // Sort only for the first time before verifying the results
      console.log("sortvalue: " + sortOnce);
      if (sortOnce < 1) {
        await this.sortColumn("ID", "ASC");
      }
      sortOnce++;

      const valuesReceived = await this.getColumnValues(value, outputColumn[i]);
      console.log("Received: " + valuesReceived);
      expect(valuesReceived).toEqual(valuesExpected[operator]);

      // Reset
      // await this.page
      //   .locator(
      //     "//div[contains(@class, 'ant-tabs-tabpane-active')]//button[./*[text()='Apply and save']]/following-sibling::button"
      //   )
      //   .click();
      await this.clearFilter();
      await this.delay(5000);
      await this.page.getByText("Kaja").last().waitFor({ state: "visible" });
    }
  }

  async selectAllCheckBox() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select AllAdd Formula Column$/ })
      .locator("span")
      .first()
      .click();
  }

  async isColumnNotPresent(colName) {
    const locator = this.page.locator(
      `//div[@role='columnheader']//div[text()="${colName}"]`
    );
    // Expect the column locator to not be visible
    await expect(locator).toBeHidden();
  }

  async rearrangeColumns(sourceLocator, targetLocator) {
    const sourceBox = await sourceLocator.boundingBox();
    const targetBox = await targetLocator.boundingBox();

    if (sourceBox && targetBox) {
      await this.page.mouse.move(
        sourceBox.x + sourceBox.width / 2,
        sourceBox.y + sourceBox.height / 2
      );
      await this.page.mouse.down();
      await this.page.mouse.move(
        targetBox.x + targetBox.width / 2,
        targetBox.y + targetBox.height / 2,
        { steps: 5 }
      );
      await this.page.mouse.up();
    }
  }

  async isColumnOrderCorrect(expectedOrder) {
    const actualOrder = await this.page
      .locator('//div[@role="columnheader"]')
      .evaluateAll((headers) =>
        headers.map((header) => header.textContent.trim())
      );

    return JSON.stringify(actualOrder) === JSON.stringify(expectedOrder);
  }

  async createViewWithFunction(functionName) {
    await expect(
      this.page.locator('div[data-testid="expression-input-box"]').last()
    ).toBeVisible();
    await this.page
      .locator('[class="anticon cursor-pointer"]>svg')
      .last()
      .click();
    await this.page.getByPlaceholder("Press Ctrl + H for help").last().click();
    await this.page
      .getByPlaceholder("Press Ctrl + H for help")
      .last()
      .fill(functionName);
    await this.page
      .locator(
        `//div[@role="listitem" and .//span[text()='${functionName}'] and .//span[text()='Boolean']]`
      )
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Text field$/ })
      .nth(2)
      .click();
    await this.page.keyboard.type("First Name");
    await this.page
      .locator("span", { hasText: "First Name All data PW Obj" })
      .click();

    if (functionName === "StartsWith") {
      await this.page.getByRole("dialog").locator('[type="text"]').type("S");
    } else if (functionName === "EndsWith") {
      await this.page.getByRole("dialog").locator('[type="text"]').type("e");
    }

    await expect(
      this.page.getByRole("button", { name: "Apply", exact: true })
    ).toBeEnabled();
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
    await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
    await this.page.getByRole("button", { name: "Apply and save" }).click();
    await this.letColumnsLoad("ID");
  }

  async verifyEditBtnIsVisible() {
    const editBtn = await this.page.getByRole("button", {
      name: "Edit",
      exact: true,
    });
    await expect(editBtn).toBeVisible();
    await expect(editBtn).toBeEnabled({ timeout: 120000 });
  }

  async clearFilter() {
    const inputs = await this.page.locator(
      "//div[contains(@class, 'ant-tabs-tabpane-active')]//div[@data-testid='expression-input-box']/input"
    );
    const count = await inputs.count();
    console.log(`Total Inputs: ${count}`);
    for (let i = count - 1; i >= 0; i--) {
      await inputs.nth(i).fill("");
    }
    await this.page.keyboard.press("Escape");
  }

  async verifyViewName(viewName) {
    const lastViewActionBtn = this.page
      .locator('//div[./div[@role="tab"]]/button')
      .last();
    await lastViewActionBtn.click();
    await this.page.getByRole("menuitem", { name: "Rename" }).last().click();
    const inputLocator = this.page.locator("//div[@role='tab']//input").last();
    const receivedViewName = await inputLocator.getAttribute("value");
    console.log("UI Value: " + receivedViewName);
    expect(viewName).toEqual(receivedViewName);
    await inputLocator.press("Enter");
  }

  async enterFilterFormula(values = []) {
    await expect(
      this.page.locator('div[data-testid="expression-input-box"]').last()
    ).toBeVisible();
    await this.page
      .locator('[class="anticon cursor-pointer"]>svg')
      .last()
      .click();

    for (let i = 0; i < values.length; i++) {
      console.log("formula value: " + values[i]);
      await this.page.keyboard.type(values[i]);
      await this.delay(500);
      await this.page.keyboard.press("Enter");
      await expect(this.page.getByRole("img").locator("svg")).toBeVisible();
    }
  }

  async clickApplyBtn(column) {
    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.delay(3000);
    await this.letColumnsLoad(column);
  }

  async selectViewTab(tabName) {
    const tabLocator = this.page.locator(
      `//div[@role="tablist"]//div[text()="${tabName}"]`
    );
    await tabLocator.scrollIntoViewIfNeeded();
    await tabLocator.click();
    await this.delay(2000);
  }

  async getActiveViewName() {
    const activeViewNameWithNumbers = await this.page
      .locator(".ant-tabs-tab-active>div>div")
      .textContent();
    const activeViewName = activeViewNameWithNumbers.replace(/\d+/g, "").trim(); //this removes the line item numbers
    console.log(activeViewName.trim());
    return activeViewName.trim();
  }

  async clickFormulaExpressionBox() {
    const searchBar = await this.page
      .locator('//div[@data-testid="expression-input-box"]/input[@placeholder]')
      .nth(-1);
    await searchBar.waitFor({ state: "visible" });
    await searchBar.click();
    return searchBar;
  }

  async selectFormula(from, formula) {
    const searchBar = await this.clickFormulaExpressionBox();
    await searchBar.fill(formula);
    const formulaLocator = await this.page.locator(
      `//div[contains(text(), '${from}')]/following-sibling::ul[1]//span[@title="${formula}"]`
    );
    await formulaLocator.waitFor({ state: "visible" });
    await expect(formulaLocator).toBeVisible();
    return formulaLocator;
  }

  async typeFormulaExpression(expression) {
    const locator = await this.page
      .locator("//div[@data-testid='expression-input-box']/input")
      .last();
    await locator.click();
    await locator.type(expression);
    await this.page.keyboard.press("Enter");
  }

  async adjustmentUpdate(
    id,
    scope,
    column,
    existingAmount,
    newAmount,
    comment
  ) {
    await this.sortColumn("ID", "ASC");
    await this.page.waitForLoadState("networkidle");
    await this.letColumnsLoad("ID");
    const actionLocator = this.page.locator(
      `//div[@row-id="${id - 1}"]/div[@col-id='action']//button`
    );
    await actionLocator.waitFor({ state: "visible" });
    await actionLocator.click({ timeout: 3000 });
    await this.goToAdjustDataMenu("Update Record");
    const applyPage = this.page.getByText("Where do you want to apply this?");
    await applyPage.waitFor({ state: "visible" });
    if (scope === "local") {
      await this.page
        .locator("//span[@title='On all dependent sheets (Global)']")
        .click();
      const drpDownLocator = this.page.locator("span", {
        hasText: "Only in this sheet",
      });
      await drpDownLocator.waitFor({ state: "visible" });
      await drpDownLocator.click();
    }
    await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]/div[contains(text(), "${existingAmount}")]`
      )
      .click();
    await this.page.keyboard.press("Backspace");
    await this.page
      .locator(`//div[contains(@col-id,"${column}")]/input`)
      .fill(`${newAmount}`);
    await this.page.keyboard.press("Enter");
    const existingValue = await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]//span[contains(text(), "${existingAmount}")]`
      )
      .isVisible();
    const newValue = await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]//span[contains(text(), "${newAmount}")]`
      )
      .isVisible();
    expect(existingValue).toBeTruthy();
    expect(newValue).toBeTruthy();
    await this.page
      .getByPlaceholder("Enter reason for adjustment")
      .fill(comment);
    const updateBt = await this.page.locator("button div", {
      hasText: "Update",
    });
    await updateBt.click();
    await this.applyAdjustmentChanges();
  }

  async adjustmentIgnore(id, scope, comment) {
    await this.sortColumn("ID", "ASC");
    await this.page.waitForLoadState("networkidle");
    await this.letColumnsLoad("ID");

    const actionLocator = this.page.locator(
      `//div[@row-id="${id - 1}"]/div[@col-id='action']//button`
    );
    await actionLocator.waitFor({ state: "visible" });
    await actionLocator.click({ timeout: 3000 });
    await this.goToAdjustDataMenu("Ignore Record");
    const applyPage = this.page.getByText("Where do you want to apply this?");
    await applyPage.waitFor({ state: "visible" });
    if (scope === "local") {
      await this.page
        .locator("//span[@title='On all dependent sheets (Global)']")
        .click();
      const drpDownLocator = this.page.locator("span", {
        hasText: "Only in this sheet",
      });
      await drpDownLocator.waitFor({ state: "visible" });
      await drpDownLocator.click();
    }
    await this.page
      .getByPlaceholder("Enter reason for adjustment")
      .fill(comment);
    const updateBt = await this.page.locator("button div", {
      hasText: "Update",
    });
    await updateBt.click();
    await this.applyAdjustmentChanges();
  }

  async adjustmentSplit(
    id,
    scope,
    column,
    existingAmount,
    newAmount1,
    newAmount2,
    comment
  ) {
    await this.sortColumn("ID", "ASC");
    await this.page.waitForLoadState("networkidle");
    await this.letColumnsLoad("ID");
    const actionLocator = this.page.locator(
      `//div[@row-id="${id - 1}"]/div[@col-id='action']//button`
    );
    await actionLocator.waitFor({ state: "visible" });
    await actionLocator.click({ timeout: 3000 });
    await this.goToAdjustDataMenu("Split Record");
    const applyPage = this.page.getByText("Where do you want to apply this?");
    await applyPage.waitFor({ state: "visible" });
    if (scope === "local") {
      await this.page
        .locator("//span[@title='On all dependent sheets (Global)']")
        .click();
      const drpDownLocator = this.page.locator("span", {
        hasText: "Only in this sheet",
      });
      await drpDownLocator.waitFor({ state: "visible" });
      await drpDownLocator.click();
    }
    //updating split record 1
    await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]/div[contains(text(), "${existingAmount}")]`
      )
      .nth(-2)
      .click();
    await this.page.keyboard.press("Backspace");
    await this.page
      .locator(`//div[contains(@col-id,"${column}")]/input`)
      .fill(`${newAmount1}`);
    await this.page.keyboard.press("Enter");
    //updating split record 2
    await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]/div[contains(text(), "${existingAmount}")]`
      )
      .nth(-1)
      .click();
    await this.page.keyboard.press("Backspace");
    await this.page
      .locator(`//div[contains(@col-id,"${column}")]/input`)
      .fill(`${newAmount2}`);
    await this.page.keyboard.press("Enter");
    await this.page
      .getByPlaceholder("Enter reason for adjustment")
      .fill(comment);
    const updateBt = await this.page.locator("button div", {
      hasText: "Update",
    });
    await updateBt.click();
    await this.applyAdjustmentChanges();
  }

  async validateAdjustmentFormatting(
    column,
    existingValue,
    inputValue,
    formattedValue
  ) {
    await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]//*[self::div or self::span][contains(text(), "${existingValue}")]`
      )
      .click();
    await this.page.keyboard.press("Backspace");
    const inputLocator = this.page.locator(
      `//div[contains(@col-id,"${column}")]/input`
    );
    await inputLocator.click();
    await inputLocator.type(inputValue, { delay: 100 });
    await this.page.keyboard.press("Enter");
    const newValue = await this.page
      .locator(
        `//div[contains(@col-id,"${column}")]//span[contains(text(), "${formattedValue}")]`
      )
      .isVisible();
    expect(newValue).toBeTruthy();
  }

  async clickFilterButton() {
    await this.page.getByRole("button", { name: "Filter" }).click();
  }

  async clickCancelButton() {
    await this.page.getByRole("button", { name: "Cancel" }).click();
  }
}

export default DatasheetV2Page;
