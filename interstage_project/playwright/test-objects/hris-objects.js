class HRISPage {
  constructor(page) {
    this.page = page;
  }

  async navigateToHRIS() {
    await this.page.goto("/settings/hris-integration", {
      waitUntil: "networkidle",
    });
  }

  async reviewUpdatesScreen() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Review new updates before import$/ })
      .nth(1)
      .click();
  }

  async reviewIgnoredScreen() {
    await this.page.goto(
      "/settings/hris-integration/processed-ignored-records",
      {
        waitUntil: "networkidle",
      }
    );
  }

  async newUsertab() {
    await this.page.getByText("New users").first().click();
  }

  async loader() {
    const loader = await this.page
      .locator(
        "//*[name()='svg' and @preserveAspectRatio='xMidYMid meet']//*[name()='path' and @stroke-linecap='round']"
      )
      .first();
    await loader.waitFor({ state: "hidden", timeout: 60000 });
  }

  async updatedUser(updated_payee) {
    const user = await this.page.getByText(updated_payee).first();
    await user.waitFor({ state: "visible", timeout: 30000 });
  }

  async selectUser(payee) {
    await this.page.getByText(payee).click();
  }

  async checkUser() {
    await this.page.getByLabel("").check();
  }

  async filterUser(first_name, condition, filter_payee) {
    await this.page.getByRole("button", { name: "Filter" }).click();
    await this.page
      .locator("//span[text()='Column']/..//input")
      .fill(first_name);
    await this.page.getByText(first_name, { exact: true }).first().click();
    await this.page.getByRole("button", { name: "Filter" }).click();
    await this.page.locator("//span[text()='Operator']/..//input/..").click();
    await this.page.getByText(condition, { exact: true }).click();
    await this.page.getByRole("textbox").first().click();
    await this.page.getByRole("textbox").first().fill(filter_payee);
    await this.page.getByRole("button", { name: "Apply" }).click();
  }

  async ignoreRecord(number) {
    await this.page
      .getByRole("button", { name: "Ignore " + number + " Record(s)" })
      .click();
    const number_records = await this.page.getByText(
      "You’re about to ignore " + number + " user"
    );
    await number_records.waitFor({ state: "visible", timeout: 30000 });
    await this.page.getByRole("button", { name: "Yes, proceed" }).click();
    const submit = await this.page.getByText("Submitting HRIS Approval task");
    await submit.waitFor({ state: "hidden", timeout: 90000 });
  }

  async importeRecord(number) {
    await this.page
      .getByRole("button", { name: "Import " + number + " Record(s)" })
      .click();
    const number_records = await this.page.getByText(
      "You’re about to import " + number + " user"
    );
    await number_records.waitFor({ state: "visible", timeout: 30000 });
    await this.page.getByRole("button", { name: "Yes, proceed" }).click();
    const pop_up = await this.page.getByText("Notify when complete");
    await pop_up.waitFor({ state: "visible", timeout: 30000 });
    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();

    const messages = [
      "Submitting HRIS Approval task",
      "HRIS Approval task submitted",
      "Data Imported successfully",
    ];

    for (const message of messages) {
      const element = await this.page.getByText(message);
      await element.waitFor({ state: "visible" });
      await element.waitFor({ state: "hidden", timeout: 60000 });
    }
  }

  async existingimporteRecord(number2) {
    await this.page
      .getByRole("button", { name: "Import " + number2 + " Record(s)" })
      .click();

    const number_records = await this.page.getByText(
      `You’re about to import ${number2} existing user updates. Do you want to proceed?`
    );
    await number_records.waitFor({ state: "visible", timeout: 30000 });

    await this.page.getByRole("button", { name: "Yes, proceed" }).click();

    const pop_up = await this.page.getByText("Notify when complete");
    await pop_up.waitFor({ state: "visible", timeout: 30000 });

    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();

    const messages = [
      "Submitting HRIS Approval task",
      "HRIS Approval task submitted",
      "Data Imported successfully",
    ];

    for (const message of messages) {
      const element = await this.page.getByText(message);
      await element.waitFor({ state: "visible" });
      await element.waitFor({ state: "hidden", timeout: 60000 });
    }
  }

  async verifyIgnoredRecords() {
    const ignored_btn = await this.page.getByRole("gridcell", {
      name: "Ignored",
    });
    await ignored_btn.waitFor({ state: "visible", timeout: 30000 });
  }

  async processrecords() {
    const process = await this.page
      .getByText("Processed", { exact: true })
      .first();
    await process.waitFor({ state: "visible", timeout: 30000 });
  }

  async existingUserUpdate() {
    await this.page.getByText("Existing user updates").click();
  }

  async groupViewdropdown(date) {
    await this.page
      .getByRole("button", { name: "Start Date: " + date + "" })
      .click();
  }

  async groupDataheaderValue() {
    await this.page
      .locator(
        "div[class*='ant-collapse-content-active'] div[class*='ant-collapse-item-active']"
      )
      .waitFor({ state: "visible" });
    var header_actual = [];
    const header_value = await this.page.locator("//tr//th");
    for (let q = 0; q < (await header_value.count()); q++) {
      const header = await header_value.nth(q).textContent();
      header_actual.push(header);
    }
    return header_actual;
  }

  async groupRowCount() {
    // const parent = await this.page.locator("table");
    // return await parent.locator("tr").count();
    return await this.page.locator("//tr//th").count();
  }

  async groupByUserToggle() {
    await this.page.getByRole("switch").click();
  }

  async nongroupView(email) {
    await this.page
      .getByRole("gridcell", { name: "" + email + "" })
      .last()
      .click();
  }

  async checkNonGroup() {
    await this.page.waitForFunction(() => {
      return document.querySelectorAll(".ant-checkbox").length === 2;
    });
    await this.page.locator('input[name="row-0"]').check();
  }
}

module.exports = HRISPage;
