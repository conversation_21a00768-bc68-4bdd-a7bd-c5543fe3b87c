import { expect } from "@playwright/test";
// Page object model class containing methods for interacting with payout filter functionality
class PayoutFilterPageObjs {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async navigateToPayouts() {
    await this.page.goto("http://localhost:3000/commissions");
  }

  async setPayoutDate(Date) {
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").press("ControlOrMeta+a");
    await this.page.getByPlaceholder("Select date").fill(Date);
    await this.page.getByPlaceholder("Select date").press("Enter");
  }

  async searchName(name) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(name);
    await this.page.waitForTimeout(2000);
  }

  async unlockStatement(name) {
    await this.page.getByTestId(`${name}@crick.com-actions-dd`).click();
    await this.page.getByText("Unlock Statements").click();
  }

  async clickDiscard() {
    await this.page.getByRole("button", { name: "Discard" }).click();
  }

  async bulkSelectPayouts() {
    await this.page.locator('input[name="selectAll"]').click();
  }

  async clickLockUnlockButtoninStatements() {
    await this.page.getByTestId("lock-unlock-button").click();
  }

  async clickUnlockSavedFilter() {
    await this.page.getByText("Unlock", { exact: true }).click();
    await this.page.getByRole("button", { name: "Unlock" }).click();
  }

  async clickFilterButton() {
    await this.page
      .locator(
        "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[1]"
      )
      .first()
      .click();
  }

  async closeFilterView() {
    await this.page.locator(".h-14 > div > .ant-btn").first().click();
  }

  async clickApplyButton() {
    await this.page.getByRole("button", { name: "Apply" }).click();
  }

  async clickClearButton() {
    await this.page.getByRole("button", { name: "Clear" }).click();
  }

  async clickYesDeleteButton() {
    await this.page.getByRole("button", { name: "Yes, Delete" }).click();
  }

  async selectLockedStatements() {
    await this.page.getByText("Locked Statements", { exact: true }).click();
  }

  async selectUnlockedStatements() {
    await this.page.getByText("Unlocked Statements").click();
  }

  async selectUnpaidPayouts() {
    await this.page.locator("//span[text()='Unpaid']").first().click();
  }

  async selectPaidPayouts(month) {
    await this.page.getByText(month).click();
    await this.page
      .locator(
        "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[1]"
      )
      .first()
      .click();
    await this.page.getByTestId("payment_status").click();
    await this.page
      .locator("span")
      .filter({ hasText: /^Paid$/ })
      .click();
    await this.clickApplyButton();
  }

  async selectZeroPayouts() {
    await this.clickFilterButton();
    await this.page.getByTestId("payment_status").click();
    await this.page.getByTitle("Zero Payout").locator("span").click();
    await this.clickApplyButton();
  }

  async selectNotInZeroPayouts() {
    await this.clickFilterButton();
    await this.page.getByTestId("option-payment_status").click();
    await this.page.getByText("Not In").click();
    await this.page.getByTestId("payment_status").click();
    await this.page.getByTitle("Zero Payout").locator("div").first().click();
    await this.page.keyboard.press("Escape");
    await this.clickApplyButton();
  }

  async selectMonthlyPayoutFrequency() {
    await this.clickFilterButton();
    await this.page.getByTestId("payout_frequency").click();
    await this.page.getByTitle("Monthly").locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectQuarterlyPayoutFrequency() {
    await this.clickFilterButton();
    await this.page.getByTestId("payout_frequency").click();
    await this.page.getByTitle("Quarterly").locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectInCommPlan(commPlan) {
    await this.clickFilterButton();
    await this.page.getByTestId("commission_plan").click();
    await this.page.getByTitle(commPlan).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectNotInCommPlan(commPlan) {
    await this.clickFilterButton();
    await this.page.getByTestId("option-commission_plan").click();
    await this.page.getByTitle("Not In").locator("div").nth(2).click();
    await this.page.getByTestId("commission_plan").click();
    await this.page.getByTitle(commPlan).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectNotRequested() {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("approval_status").click();
    await this.page.getByText("Not Requested").click();
    await this.clickApplyButton();
  }

  async selectNotInApproved() {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("option-approval_status").click();
    await this.page.getByText("Not In").click();
    await this.page.getByTestId("approval_status").click();
    await this.page.getByTitle("Approved").locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectReportingManagerIn(manager) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("reporting_manager").click();
    await this.page.getByTitle(manager).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectReportingManagerNotIn(manager) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("option-reporting_manager").click();
    await this.page.getByText("Not In").click();
    await this.page.getByTestId("reporting_manager").click();
    await this.page.getByTitle(manager).locator("div").first().click();
    await this.clickApplyButton();
  }
  // div.input-left-border-radius-none input.ant-input

  async selectDesignationContains(value) {
    await this.clickFilterButton();
    await this.page.getByText("Contains").click();
    await this.page.getByRole("textbox", { name: "Type" }).click();
    await this.page.getByRole("textbox", { name: "Type" }).fill(value);
    await this.clickApplyButton();
  }

  async selectPendingAmountGreaterThan(amount) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payout AmountEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page.getByText("Greater Than", { exact: true }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payout Amount>Greater Than$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payout AmountGreater Than$/ })
      .getByPlaceholder("Type")
      .fill(amount);
    await this.clickApplyButton();
  }

  async selectEmploymentCountry(country) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("employment_country").click();
    await this.page.getByTitle(country).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectPayoutCurrencyIn(currency) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByTestId("pay_currency").click();
    await this.page.getByTitle(currency).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectPayoutAmountInBetween(minAmount, maxAmount) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page
      .locator("div")
      .filter({ hasText: /^Payout AmountEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page.getByText("In Between").click();
    await this.page.getByRole("spinbutton").nth(1).click();
    await this.page.getByRole("spinbutton").nth(1).fill(minAmount);
    await this.page.getByRole("spinbutton").nth(2).click();
    await this.page.getByRole("spinbutton").nth(2).fill(maxAmount);
    await this.clickApplyButton();
  }

  async selectCommissionGreaterthan(value) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page
      .locator("div")
      .filter({ hasText: /^Commission %Equal To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page
      .getByTitle("Greater Than", { exact: true })
      .locator("div")
      .first()
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Commission %>Greater Than$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Commission %Greater Than$/ })
      .getByPlaceholder("Type")
      .fill(value);
    await this.clickApplyButton();
  }

  async selectCommissionNotEqualTo(value) {
    await this.page.getByText("January 2023").click();
    await this.clickFilterButton();
    await this.page.getByText("Equal To").nth(2).click();
    await this.page.getByText("Not Equal To").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Commission %!=Not Equal To$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Commission %Not Equal To$/ })
      .getByPlaceholder("Type")
      .fill(value);
    await this.clickApplyButton();
  }

  async clickMoreFiltersButton() {
    await this.page.getByRole("button", { name: "More filters" }).click();
  }

  async selectBasePayGreaterThan(value) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page.getByRole("menuitem", { name: "Base Pay" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Base PayEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page.getByText("Greater Than", { exact: true }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Base Pay>Greater Than$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Base PayGreater Than$/ })
      .getByPlaceholder("Type")
      .fill(value);
    await this.clickApplyButton();
  }

  async exportWithoutFilters() {
    await this.page.getByText("January 2023").click();
    const downloadPromise = this.page.waitForEvent("download");
    await this.page
      .locator(
        "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[2]"
      )
      .click();
    await downloadPromise;
  }

  async exportWithFilters() {
    await this.page.getByText("Locked Statements", { exact: true }).click();
    await this.page
      .locator(
        "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[2]"
      )
      .click();
    const downloadPromise = this.page.waitForEvent("download");
    await this.page.getByRole("button", { name: "Proceed" }).click();
    await downloadPromise;
  }

  async selectProcessedEqualTo(value) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page.getByRole("menuitem", { name: "Processed" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^ProcessedEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page.getByTitle(value).locator("div").first().click();
    await this.clickApplyButton();
  }

  async selectRoles(value) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page
      .getByRole("menuitem", { name: "Roles", exact: true })
      .click();
    await this.page.getByTestId("role").click();
    await this.page
      .getByTitle(value, { exact: true })
      .locator("div")
      .first()
      .click();
    await this.clickApplyButton();
  }

  async selectJoiningDate(startDate, endDate) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page
      .getByTitle("Joining Date")
      .getByText("Joining Date")
      .click();
    await this.page.getByPlaceholder("Start date").click();
    await this.page.getByPlaceholder("Start date").fill(startDate);
    await this.page.keyboard.press("Enter");
    // await this.page.getByPlaceholder("End date").click();
    await this.page.getByPlaceholder("End date").fill(endDate);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(2000);
    await this.clickApplyButton();
  }

  async applyJoiningDateFilter(startDate) {
    await this.page
      .locator(
        '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
      )
      .first()
      .click();
    await this.page.getByRole("button", { name: "More filters" }).click();
    await this.page
      .getByTitle("Joining Date")
      .getByText("Joining Date")
      .click();
    await this.page.getByPlaceholder("Start date").click();
    await this.page
      .locator(
        "//div[@class='ant-picker-panels']/div[1]/div[1]/div[1]/button[1]"
      )
      .click();
    // Clicking the next month button four times to select January 2023
    for (let i = 0; i < 4; i++) {
      await this.page
        .locator(
          "//div[@class='ant-picker-panels']/div[1]/div[1]/div[1]/button[2]"
        )
        .first()
        .click();
    }
    await this.page.getByTitle(startDate).locator("div").click();
    await this.page.getByTitle(startDate).getByText("1").click();
    await this.page.getByPlaceholder("End date").click();
    await this.page.getByText("31", { exact: true }).first().click();
    await this.clickApplyButton();
  }

  async selectCustomFilter(Payoutstatus, frequency) {
    await this.clickFilterButton();
    await this.page.getByTestId("payment_status").click();
    await this.page
      .getByTestId("payment_status")
      .locator("span")
      .filter({ hasText: Payoutstatus })
      .click();
    await this.page.getByTestId("payment_status").click();
    await this.page.getByTestId("payout_frequency").click();
    await this.page
      .getByTestId("payout_frequency")
      .locator("span")
      .filter({ hasText: frequency })
      .click();
    await this.page.getByTestId("payout_frequency").click();
    await this.page.locator("svg.ml-1").click();
    await this.page.getByRole("textbox").first().fill("custom filter 1");
    await this.clickApplyButton();
  }

  async applyExitDateFilter(date1, date2) {
    await this.clickFilterButton();
    await this.page.getByRole("button", { name: "More filters" }).click();
    await this.page.getByText("Exit Date").click();
    await this.page.getByPlaceholder("Start date").click();
    await this.page.getByPlaceholder("Start date").fill(date1);
    await this.page.keyboard.press("Enter");
    // await this.page.getByPlaceholder("End date").click();
    await this.page.getByPlaceholder("End date").fill(date2);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(2000);
    await this.clickApplyButton();
  }

  async saveFilterView(filterName) {
    await this.clickFilterButton();
    await this.page.getByTestId("payment_status").click();
    await this.page.getByTitle("Unpaid").first().click();
    await this.page.getByTestId("payment_status").click();
    await this.page.locator("svg.ml-1").click();
    await this.page
      .locator("//input[@value='New filter view']")
      .fill(filterName);
    await this.page.getByRole("button", { name: "Save as View" }).click();
    await this.page.getByRole("button", { name: "Yes, save" }).click();
  }

  async deleteSavedFilter(filterName) {
    await this.page
      .locator("div")
      .filter({ hasText: /^More$/ })
      .nth(1)
      .click();
    await this.page.getByText(filterName, { exact: true }).click();
    await this.page
      .locator("svg.text-ever-error.h-full.w-full")
      .first()
      .click();
    await this.page.getByRole("button", { name: "Yes, Delete" }).click();
  }

  async selectIgnoredGreaterThan(value) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page.getByTitle("Ignored").getByText("Ignored").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^IgnoredEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page
      .getByTitle("Greater Than", { exact: true })
      .locator("div")
      .first()
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Ignored>Greater Than$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^IgnoredGreater Than$/ })
      .getByPlaceholder("Type")
      .fill(value);
    await this.clickApplyButton();
  }

  async addFilterAndView(filterName) {
    await this.clickFilterButton();
    await this.page.getByTestId("payment_status").click();
    await this.page.getByTitle("Unpaid").click();
    await this.page.locator(".ml-1").click();
    await this.page.getByRole("textbox").first().fill(filterName);
    await this.clickApplyButton();
    await this.page.getByRole("button", { name: "Save as View" }).click();
    await this.page.getByRole("button", { name: "Yes, save" }).click();
  }

  // (//div[@class='flex text-inherit items-center justify-center h-5 w-5'])[2]
  async cloneFilterAndView(filterName) {
    await this.page
      .locator(
        "(//*[name()='svg'][@class='text-ever-base-content-mid hover:text-ever-base-content h-full w-full'])[2]"
      )
      .click();
    await this.page.getByRole("button", { name: "Create" }).click();
  }

  async editSavedFilter(filtername2) {
    await this.page
      .locator("div[class$='h-10 flex items-center justify-end'] button")
      .first()
      .click();
    await this.page.getByTestId("payment_status").click();
    await this.page.locator("span").filter({ hasText: filtername2 }).click();
    await this.page.getByRole("button", { name: "Update View" }).click();
    await this.page.getByRole("button", { name: "Yes, update" }).click();
  }

  async moresavedfilter(filterName) {
    await this.page.getByRole("button", { name: "Clear" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^More$/ })
      .nth(1)
      .click();
    await this.page.getByText(filterName, { exact: true }).click();
  }

  async applyVariablePayFilterLessThan(amount) {
    await this.clickFilterButton();
    await this.clickMoreFiltersButton();
    await this.page.getByText("Variable Pay").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Variable PayEqual To$/ })
      .getByTitle("Equal To")
      .click();
    await this.page.getByText("Less Than", { exact: true }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Variable Pay<Less Than$/ })
      .getByPlaceholder("Type")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Variable PayLess Than$/ })
      .getByPlaceholder("Type")
      .fill(amount);
    await this.page.waitForTimeout(1000);
    await this.clickApplyButton();
  }

  async waitForTimeout(timeout) {
    await this.page.waitForTimeout(timeout);
  }

  async isTextVisible(actualText, validationName) {
    let flag = false;
    if (!flag) {
      if (validationName.includes("Filter Action")) {
        const filterTextContent = await this.page
          .locator("//*[@class='w-fit']")
          .first()
          .textContent();
        expect(filterTextContent).toContain(actualText);
        flag = true;
      } else if (validationName.includes("Banner Message")) {
        const bannerTextContent = await this.page
          .getByText(actualText)
          .textContent();
        expect(bannerTextContent).toEqual(actualText);
        flag = true;
      } else if (validationName.includes("Filter Name")) {
        await this.page.waitForTimeout(5000);
        flag = await this.page
          .locator(`//*[contains(text(), "${actualText}")]`)
          .first()
          .isVisible();
        // //*[@class='w-fit']//preceding-sibling::span[contains(text(),'${actualText}')]
      }
    }
    return flag;
  }

  async ClickMoreSavedfilters() {
    return await this.page.getByText("More").click();
  }

  async waitForDeletedMessage(timeoutvalue) {
    await this.page
      .getByText("Payout filter was deleted successfully")
      .waitFor({ state: "visible", timeout: timeoutvalue });
  }

  async verifyAndLogVisibility(
    text,
    successMessage,
    validationName = "Filter Action"
  ) {
    const isVisible = await this.isTextVisible(text, validationName);
    expect(isVisible).toBe(true);
    if (isVisible) {
      console.log(successMessage);
    }
  }

  /**
   * Enter Value in Textbox based on the field name
   *
   * @param {String} labelName - The name of the label.
   * @param {String} action - The action to perform.
   * @param {Array} value - The value to enter.
   * @param {String} [valueType="text"] - The type of the value (optional, defaults to "text").
   */
  async enterValue(labelName, action, value, valueType = "text") {
    // Click on the label.
    await this.page
      .locator(`[data-testid='ever-select']:near(:text('${labelName}'))`)
      .first()
      .click();
    // Click on the action.
    await this.page.locator(`div[label='${action}']`).click();
    // If the action is not "Is Empty" or "Is Not Empty", enter the value.
    if (!["Is Empty", "Is Not Empty"].includes(action)) {
      // If the action includes "In Between", enter the value for each field.
      if (action.includes("In Between")) {
        const numberFields = this.page.locator(
          `input[type="${valueType.toLowerCase()}"]:below(:text("${labelName}"))`
        );
        for (let i = 0; i < (await numberFields.count()); i++) {
          await numberFields.nth(i).fill(value[i]);
        }
      } else {
        // If the action does not include "In Between", enter the value for the first field.
        const numberFields = this.page.locator(
          `input[type="${valueType.toLowerCase()}"]:below(:text("${labelName}"))`
        );
        await numberFields.nth(0).fill(value[0]);
      }
    }
  }

  /**
   * Add More Fields with the name of input Array
   * @param {Array} labelNames
   */
  async addMoreFilters(labelNames) {
    for (const labelName of labelNames) {
      await this.clickMoreFiltersButton();
      await this.page.getByRole("menuitem", { name: labelName }).click();
    }
  }

  /**
   * Selects a dropdown option based on the provided dropdown name test ID and option value.
   *
   * This method first locates the dropdown option by its test ID. If the option is "In", it expects the option to be visible.
   * If the option is not "In", it clicks on the "In" option and then clicks on the specified option.
   *
   * @param {String} dropdownNameTestID - The test ID of the dropdown.
   * @param {String} option - The option to select from the dropdown.
   */
  async selectDropdownOption(dropdownNameTestID, option) {
    const optionLocator = await this.page.getByTestId(
      `option-${dropdownNameTestID}`
    );
    if (option === "In") {
      await expect(
        optionLocator.locator("div").filter({ hasText: `In` })
      ).toBeVisible();
    } else {
      await optionLocator.locator("div").filter({ hasText: `In` }).click();
      await this.page.getByText(option, { exact: true }).click();
    }
  }

  /**
   * Selects the date field dropdown type and its date value(s) based on the input in the User Filter Section.
   *
   * @param {String} fieldName - The name of the field.
   * @param {String} option - The dropdown option to select.
   * @param {String} dateOrFromValue - The single date value or "From" value for range.
   * @param {String} [toValue] - The "To" value for range (optional).
   */
  async enterDateFields(fieldName, action, dateOrFromValue, toValue = null) {
    // Click on the dropdown and select the option.
    await this.page
      .locator(`[data-testid='ever-select']:below(:text('${fieldName}'))`)
      .first()
      .click();
    await this.page.locator(`div[label='${action}']`).click();

    if (toValue) {
      // Handle "From" and "To" values for date range.
      const fromLocator = this.page
        .locator(`[placeholder="Start date"]:below(:text("${fieldName}"))`)
        .first();
      const toLocator = this.page
        .locator(`[placeholder="End date"]:below(:text("${fieldName}"))`)
        .first();

      await fromLocator.click();
      await fromLocator.fill(dateOrFromValue);

      await toLocator.click();
      await toLocator.fill(toValue);
    } else {
      // Handle single date value.
      const dateLocator = this.page
        .locator(`[placeholder="Select date"]:below(:text("${fieldName}"))`)
        .first();

      await dateLocator.click();
      await dateLocator.fill(dateOrFromValue);
    }

    // Finalize input by pressing "Enter".
    await this.page.keyboard.press("Enter");
  }

  /**
   * Selects dropdown values based on the dropdown name
   *
   * @param {String} dropdownNameTestID - The test ID of the dropdown
   * @param {Array} options - An array of options to select
   */
  async selectDropdownValue(dropdownNameTestID, options) {
    await this.page.getByTestId(dropdownNameTestID).click();
    for (const option of options) {
      await this.page
        .getByTestId(dropdownNameTestID)
        .locator("input")
        .fill(option);
      await this.page
        .getByTestId(dropdownNameTestID)
        .getByText(option, { exact: true })
        .last()
        .click();
      await this.page.keyboard.press("Escape");
      await expect(
        this.page.locator("label").filter({ hasText: option }).last()
      ).toBeVisible();
    }
  }
}
export default PayoutFilterPageObjs;
