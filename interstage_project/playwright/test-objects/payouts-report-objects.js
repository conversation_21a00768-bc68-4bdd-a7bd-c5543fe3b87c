import { expect } from "@playwright/test";

const moment = require("moment");
class PayoutsReportPage {
  constructor(page) {
    this.page = page;
    this.tabPanel = page.locator(
      "//div[@role='tabpanel' and @class='ant-tabs-tabpane ant-tabs-tabpane-active' and @aria-hidden='false']"
    );
    this.columnLocators = [
      page.getByText("Payee EmailPrimary"),
      page.getByText("Payout Period End DatePrimary"),
      page.getByText("Payout Period Start DatePrimary"),
      page.locator("span").filter({ hasText: "Approval Status" }),
      page.locator("span").filter({ hasText: "Arrear Processed Amount (" }),
      page.locator("span").filter({ hasText: "Commission Adjustment Amount" }),
      page.locator("span").filter({ hasText: "Commission Percentage" }),
      page.locator("span").filter({ hasText: "Commission Plan Amount (Payee" }),
      page.locator("span").filter({ hasText: "Conversion Rate" }),
      page.locator("span").filter({ hasText: "Draw Adjustment Amount (Payee" }),
      page.locator("span").filter({ hasText: "Ignored Amount (Payee" }),
      page.locator("span").filter({ hasText: "Last Paid Date" }),
      page.locator("span").filter({ hasText: "Lock Status" }),
      page.locator("span").filter({ hasText: "Paid Amount (Org Currency)" }),
      page.locator("span").filter({ hasText: "Paid Amount (Payee Currency)" }),
      page.locator("span").filter({ hasText: "Payable Amount (Org Currency)" }),
      page.locator("span").filter({ hasText: "Payable Amount (Payee" }),
      page.locator("span").filter({ hasText: "Payee Name" }),
      page.locator("span").filter({ hasText: "Payee Status" }),
      page.locator("span").filter({ hasText: "Payout Currency" }),
      page.locator("span").filter({ hasText: "Payout Status" }),
      page.locator("span").filter({ hasText: "Pending Amount (Payee" }),
      page.locator("span").filter({ hasText: "Primary Commission Plans" }),
      page
        .locator("span")
        .filter({ hasText: /^Processed Amount \(Payee Currency\)$/ }),
      page.locator("span").filter({ hasText: "Spiff Plan Amount (Payee" }),
      page.locator("span").filter({ hasText: "Spiff Plans" }),
      page.locator("span").filter({ hasText: "Statement Approved Date" }),
      page.locator("span").filter({ hasText: "Statement Locked Date" }),
      page.locator("span").filter({ hasText: "Statement Period" }),
      page.locator("span").filter({ hasText: "Variable Pay (Org Currency)" }),
      page.locator("span").filter({ hasText: "Variable Pay (Payee Currency)" }),
    ];
    this.emailLocators = [
      this.tabPanel.locator(
        "//*[@row-index='0']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='1']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='2']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='3']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='4']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='5']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='6']/div[@col-id='payee_email_id']"
      ),
      this.tabPanel.locator(
        "//*[@row-index='7']/div[@col-id='payee_email_id']"
      ),
    ];
  }

  async validateStaleness() {
    const stalenessToastLocator1 = this.page.locator(
      "//div[contains(text(), 'Sheet has new updates')]"
    );
    const stalenessToastLocator2 = this.page.locator(
      "//span[contains(text(), 'Refresh sheet')]"
    );
    await expect(stalenessToastLocator1).toBeVisible();
    await expect(stalenessToastLocator2).toBeVisible();
    const stalenessToastMessage1 = await stalenessToastLocator1.textContent();
    const stalenessToastMessage2 = await stalenessToastLocator2.textContent();
    return [stalenessToastMessage1, stalenessToastMessage2];
  }

  async refreshDatasheet() {
    const refreshButton = this.page.locator("//button[text()='Refresh']");
    const fetchData = this.page.locator(
      "//div[text()='Fetching latest data...']"
    );
    if (await refreshButton.isVisible({ timeout: 10000 })) {
      await refreshButton.click();
      await fetchData.waitFor({ state: "visible", timeout: 10000 });
      await fetchData.waitFor({ state: "hidden", timeout: 300000 });
    }
  }

  async goToDatasheetView(viewId) {
    await this.page.goto(
      `/datasheet?id=c9a32152-e22e-46d4-ad8b-db803cca4549&name=payouts+ro&viewId=${viewId}`,
      { waitUntil: "networkidle" }
    );
    await this.page.locator(".h-screen > div").first().click();
  }

  async scrollRightInTable(pixels) {
    const agGrid = await this.page.locator(".ag-root");
    await agGrid.waitFor({ state: "visible" });
    await agGrid.evaluate((node, pixels) => {
      const scrollContainer = node.querySelector(
        ".ag-body-horizontal-scroll-container"
      );
      scrollContainer.scrollLeft += pixels;
    }, pixels);
  }

  async getStatusValue(columnId) {
    let finalLockStatus = undefined;
    const tabPanel = await this.page.locator(
      "//div[@role='tabpanel' and @class='ant-tabs-tabpane ant-tabs-tabpane-active' and @aria-hidden='false']"
    );
    const lockStatusEle = await tabPanel.locator(
      `//*[@row-id='0']/div[@col-id='${columnId}']`
    );
    const lockStatusValue = await lockStatusEle.textContent();
    const formattedLockStatus = await this.formattedValue(lockStatusValue);
    finalLockStatus = formattedLockStatus;
    return finalLockStatus;
  }
  async formattedValue(lockStatusValue) {
    const statusAsNumbers = /^\d{1,3}(,\d{3})*(\.\d+)?$/.test(
      lockStatusValue.trim()
    );
    if (statusAsNumbers) {
      const formattedAmount = await this.formatAmount(lockStatusValue);
      return formattedAmount;
    } else {
      return lockStatusValue;
    }
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async formatAmount(amountNumberText) {
    const splitText = amountNumberText.replace(",", "");
    return parseFloat(splitText);
  }
  async setCommissionPeriod(period) {
    const key = "commission-view-period";
    const value = period;
    await this.page.evaluate(
      ({ key, value }) => {
        // Set the localStorage value for the current page
        localStorage.setItem(key, value);
      },
      { key, value }
    );
  }

  async getCurrentFormattedDate() {
    const currentDateTime = moment();
    const formattedCurrentDateTime = currentDateTime.format("MMM DD, YYYY");
    return formattedCurrentDateTime;
  }

  async goToPayouts() {
    await this.page.goto("http://localhost:3000/commissions", {
      waitUntil: "networkidle",
    });
  }

  async lockOrUnlockStatements(lockOrUnlock, payeeEmail) {
    await this.setCommissionPeriod("31-December-2024");
    await this.goToPayouts();
    await this.page.getByTestId(`${payeeEmail}-actions-dd`).click();
    await this.page.getByRole("menuitem", { name: `${lockOrUnlock}` }).click();
    const lockedToastMessage = this.page.locator(
      "//span[text()='Lock status updated successfully']"
    );
    await lockedToastMessage.waitFor({ state: "visible" });
  }

  async registerPayment() {
    await this.setCommissionPeriod("30-November-2024");
    await this.goToPayouts();
    await this.page
      .getByTestId("<EMAIL>-register-payment")
      .click();
    await this.page.getByRole("button", { name: "Register" }).click();
    await this.page
      .getByText("Payment registered successfully")
      .waitFor({ state: "visible" });
  }

  async invalidatePayment() {
    await this.setCommissionPeriod("30-November-2024");
    await this.goToPayouts();
    await this.page
      .getByTestId("<EMAIL>-name-cell")
      .locator("//parent::span/preceding-sibling::span[2]")
      .click();
    await this.page.getByRole("button", { name: "Invalidate" }).click();
  }

  async processArrears() {
    await this.setCommissionPeriod("30-November-2024");
    await this.goToPayouts();
    await this.page.getByRole("button", { name: "Arrears View" }).click();
    try {
      const rowLocator = await this.page
        .locator("//*[@col-id='pending_amount']")
        .getByTestId("<EMAIL>-pending-amount")
        .getByText("₹90.00")
        .locator("//ancestor::div[@role='row']");
      const rowIndex = await rowLocator.getAttribute("row-index");
      await this.page
        .locator(
          `//*[@row-index='${rowIndex}']/div[@col-id='action']//button[1]`
        )
        .click();
    } catch (error) {
      console.error(
        "Not the right payee to process arrears: Pointing to other payee",
        error
      );
    }
    await this.page.getByRole("button", { name: "Move" }).click();
    await this.page
      .getByText("Arrear processed successfully")
      .waitFor({ state: "visible" });
  }

  async ignoreArrears() {
    await this.setCommissionPeriod("30-November-2024");
    await this.goToPayouts();
    await this.page.getByRole("button", { name: "Arrears View" }).click();
    try {
      const rowLocator = await this.page
        .locator("//*[@col-id='pending_amount']")
        .getByTestId("<EMAIL>-pending-amount")
        .getByText("₹40.00")
        .locator("//ancestor::div[@role='row']");
      const rowIndex = await rowLocator.getAttribute("row-index");
      await this.page
        .locator(
          `//*[@row-index='${rowIndex}']/div[@col-id='action']//button[2]`
        )
        .click();
    } catch (error) {
      console.error(
        "Not the right payee to ignore arrears: Pointing to other payee",
        error
      );
    }
    await this.page.getByRole("menuitem", { name: "Ignore arrear" }).click();
    await this.page
      .getByText("Arrear ignored successfully")
      .waitFor({ state: "visible" });
  }

  async bulkLockUnlock(lockOrUnlock) {
    await this.setCommissionPeriod("31-October-2024");
    await this.goToPayouts();
    await this.page
      .getByRole("row", { name: "Name" })
      .getByRole("columnheader")
      .first()
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: new RegExp(`^${lockOrUnlock}$`) })
      .click();
    await this.page.getByRole("button", { name: lockOrUnlock }).click();
    const lockedToastMessage = this.page.locator(
      "//span[text()='Lock status updated successfully']"
    );
    await lockedToastMessage.waitFor({ state: "visible" });
  }

  async getColumnTexts() {
    const columnTexts = [];
    for (const locator of this.columnLocators) {
      columnTexts.push(await locator.textContent());
    }
    return columnTexts;
  }

  async expectColumnsToBe(expectedColumnNames) {
    const actualColumnNames = await this.getColumnTexts();
    expect(actualColumnNames).toEqual(expectedColumnNames);
  }

  async getProcessArrearStatuses(columnId) {
    const tabPanel = await this.page.locator(
      "//div[@role='tabpanel' and @class='ant-tabs-tabpane ant-tabs-tabpane-active' and @aria-hidden='false']"
    );
    // await tabPanel.locator("//*[@row-index]").first().waitFor({ timeout: 10000 });
    await this.delay(15000);
    const rowCount = await tabPanel.locator("//*[@row-index]").count();
    let finalLockStatus = undefined;
    const matchingStatuses = [];
    for (let i = 0; i < rowCount; i++) {
      const rowIndex = i.toString();
      const pendingAmountLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='pending_amount_payee_currency']`
      );
      const processedAmountLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='processed_amount_payee_currency']`
      );
      const arrearprocessedAmountLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='arrears_processed_amount_payee_currency']`
      );
      const periodLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='statement_period']`
      );
      const periodText = await periodLocator.textContent();
      const pendingAmountText = await pendingAmountLocator.textContent();
      const pendingAmountValue = parseFloat(pendingAmountText);
      const processedAmountText = await processedAmountLocator.textContent();
      const processedAmountValue = parseFloat(processedAmountText);
      const arrearprocessedAmountText =
        await arrearprocessedAmountLocator.textContent();
      const arrearprocessedAmountValue = parseFloat(arrearprocessedAmountText);
      if (
        pendingAmountValue !== 0 &&
        processedAmountValue === 0 &&
        periodText === "November 2024 (Monthly)"
      ) {
        const lockStatusLocator = tabPanel.locator(
          `//*[@row-index='${rowIndex}']/div[@col-id='${columnId}']`
        );
        const lockStatusValue = await lockStatusLocator.textContent();
        const formattedLockStatus = await this.formattedValue(lockStatusValue);
        matchingStatuses.push(formattedLockStatus);
      } else if (
        pendingAmountValue === 0 &&
        processedAmountValue !== 0 &&
        periodText === "November 2024 (Monthly)"
      ) {
        const lockStatusLocator = tabPanel.locator(
          `//*[@row-index='${rowIndex}']/div[@col-id='${columnId}']`
        );
        const lockStatusValue = await lockStatusLocator.textContent();
        const formattedLockStatus = await this.formattedValue(lockStatusValue);
        matchingStatuses.push(formattedLockStatus);
      }
    }
    if (matchingStatuses.length > 0) {
      finalLockStatus = matchingStatuses[0];
    }
    return finalLockStatus;
  }

  async getBulkLockStatuses(columnId) {
    const tabPanel = await this.page.locator(
      "//div[@role='tabpanel' and @class='ant-tabs-tabpane ant-tabs-tabpane-active' and @aria-hidden='false']"
    );
    await this.delay(15000);
    const rowCount = await tabPanel.locator("//*[@row-index]").count();
    const allLockStatuses = [];
    for (let i = 0; i < rowCount; i++) {
      const rowIndex = i.toString();
      const emailLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='payee_email_id']`
      );
      const payeeEmail = await emailLocator.textContent();
      const lockStatusLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='${columnId}']`
      );
      const lockStatusValue = await lockStatusLocator.textContent();
      const formattedLockStatus = await this.formattedValue(lockStatusValue);
      allLockStatuses.push(formattedLockStatus);
    }
    return allLockStatuses;
  }

  async getFutureArrearProcessedValue(columnId) {
    const tabPanel = await this.page.locator(
      "//div[@role='tabpanel' and @class='ant-tabs-tabpane ant-tabs-tabpane-active' and @aria-hidden='false']"
    );
    await this.delay(15000);
    const rowCount = await tabPanel.locator("//*[@row-index]").count();
    let finalLockStatus = undefined;
    const matchingStatuses = [];
    for (let i = 0; i < rowCount; i++) {
      const rowIndex = i.toString();
      const periodLocator = tabPanel.locator(
        `//*[@row-index='${rowIndex}']/div[@col-id='statement_period']`
      );
      const periodText = await periodLocator.textContent();
      if (periodText === "December 2024 (Monthly)") {
        const lockStatusLocator = tabPanel.locator(
          `//*[@row-index='${rowIndex}']/div[@col-id='${columnId}']`
        );
        const lockStatusValue = await lockStatusLocator.textContent();
        const formattedLockStatus = await this.formattedValue(lockStatusValue);
        matchingStatuses.push(formattedLockStatus);
      }
    }
    if (matchingStatuses.length > 0) {
      finalLockStatus = matchingStatuses[0];
    }
    return finalLockStatus;
  }
}

export default PayoutsReportPage;
