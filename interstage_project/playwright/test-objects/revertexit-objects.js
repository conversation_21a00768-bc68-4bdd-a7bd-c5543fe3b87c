import { expect } from "@playwright/test";
class Revertexitpage {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async gotoUsersPage() {
    await this.page.goto("/users", { waitUntil: "networkidle" });
  }

  async goToTeamspage() {
    await this.page.goto("/teams", { waitUntil: "networkidle" });
  }

  async goToQuotas() {
    await this.page.goto("/quotas", { waitUntil: "networkidle" });
  }

  async goToDraws() {
    await this.page.goto("/draws", { waitUntil: "networkidle" });
  }

  async Logout() {
    await this.page.locator("#user-avatar-element").click();
    await this.page.getByText("Logout").click();
  }

  async assignRoles(Role) {
    await this.page.goto("/settings", { waitUntil: "networkidle" });
    await this.page
      .getByRole("link", { name: "Roles Tailor roles and" })
      .click();
    await this.page.getByText(Role, { exact: true }).click();
    await this.page.getByText("Teams, Groups & Users").click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Create & Edit Users$/ })
      .nth(2)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: "Users can see all data" })
      .first()
      .click();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async impersonateUser() {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Login as user" }).click();
    await this.page
      .getByText("Logged in as")
      .waitFor({ state: "visible", timeout: 60000 });
  }

  async exitImpersonation() {
    await this.page.getByRole("button", { name: "Exit" }).click();
    await this.page.waitForTimeout(3000);
  }

  async searchUser(email) {
    await this.searchUserLocator.fill(email);
    await this.page.waitForTimeout(4000);
  }

  async clearSearchbox() {
    await this.searchUserLocator.click();
    await this.page.keyboard.press("Meta+A");
    await this.page.keyboard.press("Backspace");
  }

  async wait(time) {
    await this.page.waitForTimeout(time);
  }

  async clickUserInTeams(username) {
    await this.page.locator("span.min-w-0").getByText(username).click();
  }

  async clickRevertExit(userEmail) {
    await this.page.locator("button[data-testid*='users dd button']").click();
    // await this.page.getByTestId(`${userEmail} users dd button`).click();
    await this.page.getByRole("button", { name: "Revert Exit" }).click();
  }

  async clickDeleteUser(userEmail) {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.waitForTimeout(2000);
    await this.page
      .locator('button.ant-btn:has(span.text-base:has-text("Delete user"))')
      .click();

    // await this.page.getByRole("button", { name: "Delete user" }).click();
  }

  async clickDelete() {
    await this.page
      .locator(".ant-modal-confirm-body")
      .getByRole("button", { name: "Delete" })
      .click();
  }

  async clickCancel() {
    await this.page
      .locator(".ant-modal-confirm-body")
      .getByRole("button", { name: "Cancel" })
      .click();
  }

  async verifyManagerDelete() {
    await expect(
      this.page.getByText("Are you sure you want to delete this user?")
    ).toBeVisible();
  }

  async verifyCannotDelete() {
    await expect(
      this.page.getByText(/This user cannot be deleted/i)
    ).toBeVisible();
  }

  async clickGotIt() {
    await this.page.getByText("Got it").click();
  }

  async verifyUserDeleted() {
    await expect(this.page.getByText("User has been deleted")).toBeVisible();
  }

  async login(emailid, Password) {
    await this.page.goto("/", { waitUntil: "networkidle" });
    await this.page.waitForTimeout(3000);
    await this.page.getByLabel("Email").fill(emailid);
    await this.page
      .getByRole("button", { name: "Continue", exact: true })
      .click();
    await this.page.getByLabel("Password").fill(Password);
    await this.page.getByRole("button", { name: "Continue" }).click();
  }

  async clickOptions() {
    await this.page.waitForTimeout(3000);
    await this.page.locator("button[data-testid*='users dd button']").click();
  }

  async confirmAction() {
    await this.page.getByRole("button", { name: "Confirm" }).click();
    await this.page.waitForTimeout(3000);
  }

  async isRevertExitVisible() {
    return await this.page.isVisible("text=revert exit");
  }

  async initiateExit() {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Initiate Exit" }).click();
  }

  async selectDate(dateTitle) {
    await this.page.getByPlaceholder("Select date").click();
    // await this.page.getByTitle(dateTitle).locator("div").click();
    await this.page.getByPlaceholder("Select date").fill(dateTitle);
    await this.page.getByPlaceholder("Select date").press("Enter");
  }

  async validateAction() {
    await this.page.getByRole("button", { name: "Validate" }).click();
  }

  async waitForRevertedMessage(timeoutvalue) {
    await this.page
      .getByText("Exit Reverted Successful!")
      .first()
      .waitFor({ state: "visible", timeout: timeoutvalue });
  }

  async isTextVisible(text) {
    return await this.page.isVisible(`text=${text}`);
  }

  async VerifyTextIsHidden(text) {
    await expect(this.page.getByRole("gridcell", { name: text })).toBeHidden();
  }

  async verifyAndLogVisibility(text, successMessage) {
    const isVisible = await this.isTextVisible(text);
    expect(isVisible).toBe(true);
    if (isVisible) {
      console.log(successMessage);
    }
  }

  async verifyPendingQuery(username) {
    await expect(
      this.page.getByText(
        `${username} has 1 open queries. You can close the queries or reassign them to another user.`
      )
    ).toBeVisible();
  }

  async verifyPendingApproval(username) {
    // Construct the dynamic message using the provided approver name
    const message = `${username} cannot be deleted because they are an approver in one or more approval workflows. To delete this user, first remove them from the workflow(s).`;

    // Check if the constructed message is visible on the page
    await expect(this.page.getByText(message)).toBeVisible();
  }

  async addNewUser(mailid, firstname, lastname, Role) {
    await this.page.getByText("New User", { exact: true }).click();
    await this.page.getByPlaceholder("Enter Email").click();
    await this.page.getByPlaceholder("Enter Email").fill(mailid);
    await this.page.getByPlaceholder("Enter First Name").click();
    await this.page.getByPlaceholder("Enter First Name").fill(firstname);
    await this.page.getByPlaceholder("Enter Last Name").click();
    await this.page.getByPlaceholder("Enter Last Name").fill(lastname);
    await this.page.locator("#add-user_userRole").click();
    await this.page.locator("span[title='Payee']").click();
    await this.page
      .locator(
        "button[type='submit'] div[class='flex gap-2 justify-center items-center']"
      )
      .click();
  }

  async verifyUserAddedSucessfully() {
    await expect(
      this.page.getByText("User Added successfully", { exact: true })
    ).toBeVisible();
  }
}

export default Revertexitpage;
