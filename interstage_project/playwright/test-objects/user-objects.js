const { expect } = require("@playwright/test");
const fs = require("fs");
const csv = require("csv-parser");
const path = require("path");

class UserPage {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async navigateToUser() {
    await this.page.goto("/users", { waitUntil: "networkidle" });
  }

  async noUserFound() {
    const no_user_txt = await this.page.locator(
      "//span[text()='No Users Found']"
    );
    await no_user_txt.waitFor({ state: "visible", timeout: 20000 });
  }

  async navigateToNewUserForm() {
    await this.page.getByRole("button", { name: "New User" }).click();
  }

  async fillEmail(email) {
    await this.page.getByPlaceholder("Enter Email").fill(email);
  }

  async fillFirstName(firstName) {
    await this.page.getByPlaceholder("Enter First Name").fill(firstName);
  }

  async fillLastName(lastName) {
    await this.page.getByPlaceholder("Enter Last Name").fill(lastName);
  }

  async selectRole(role) {
    await this.page.getByLabel("Role*").click();
    await this.page.click(
      `div[class*='ant-select-item-option'][title='${role}']`
    );
  }

  async submitNewUser() {
    await this.page.getByRole("button", { name: "Add User" }).click();
  }

  async waitForIdleState(timeout) {
    await this.page.waitForTimeout(timeout);
  }

  async verifySuccessMessage() {
    const successPopup = await this.page.getByText("User Added successfully");
    await successPopup.waitFor({ state: "visible" });
    await successPopup.waitFor({ state: "hidden" });
  }

  async verifyToastMessage() {
    const successPopup = await this.page.getByText("User Added successfully");
    await successPopup.waitFor({ state: "visible" });
  }

  async checkForEmailExistsMessage() {
    const emailExists = await this.page
      .locator("div")
      .filter({
        hasText: "EMAIL ALREADY EXISTS - <EMAIL>",
      })
      .nth(3);
    await emailExists.waitFor({ state: "visible" });
  }

  async clickCancelButton() {
    await this.page.getByRole("button", { name: "Cancel" }).last().click();
  }

  async fillSearch(searchText) {
    await this.searchUserLocator.fill(searchText);

    await this.page.waitForTimeout(2000);
  }

  async waitForSearchResults() {
    await this.page.waitForLoadState("networkidle");
    await this.page
      .locator("//span[text()='1']/following-sibling::span[1][text()='rows']")
      .waitFor({ state: "visible", timeout: 30000 });
    await this.page.waitForTimeout(2000); // Consider re-evaluating the need for static waits.
  }

  async initiateMapping() {
    await this.page.getByRole("button", { name: "Map Payee" }).click();
  }

  async fillEmployeeDetails(employeeId, designation, basePay, variablePay) {
    await this.page.getByLabel("Joining Date").click();
    await this.page.getByText("Today").click();
    await this.page.getByPlaceholder("Enter Employee ID").fill(employeeId);
    await this.page.getByPlaceholder("Enter Designation").fill(designation);
    await this.page.getByLabel("Crystal Access").click();
    await this.page.getByText("Yes").click();
    await this.page.getByLabel("Employment Country").click();
    await this.page.getByText("United States Of America").click();
    await this.page.getByLabel("Payout Currency").click();
    await this.page.getByTitle("USD").getByText("USD").click();
    await this.page.getByLabel("Payout Frequency").click();
    await this.page.getByTitle("Monthly").getByText("Monthly").click();
    await this.page.getByPlaceholder("Enter Base Pay").fill(basePay);
    await this.page.getByPlaceholder("Enter Variable Pay").fill(variablePay);
  }

  async saveChangesFromUpdateUserDetails() {
    await this.page
      .getByRole("dialog", { name: "Update User Details" })
      .getByRole("button", { name: "Save" })
      .click();
  }

  async verifyMapPayeeSuccessMessage() {
    const successPopup = await this.page
      .locator("div")
      .filter({
        hasText: "Employee details saved!",
      })
      .nth(3);
    await successPopup.waitFor({ state: "visible" });
  }

  async initiateExit() {
    await this.page
      .getByRole("gridcell", { name: "MAP PAYEE" })
      .getByRole("button")
      .nth(1)
      .click();
    await this.page.getByRole("menuitem", { name: "Initiate Exit" }).click();
  }

  async selectExitDateAndValidate(date = null) {
    await this.page.getByPlaceholder("Select date").click();
    if (date === null) {
      await this.page.getByText("Today").click();
    } else {
      await this.page.getByPlaceholder("Select date").fill(date);
      await this.page.keyboard.press("Enter");
    }
    await this.page.getByRole("button", { name: "Validate" }).click();
  }

  async verifyrecentData(date) {
    await this.page.waitForTimeout(2000);
    // await this.page
    //   .getByRole("button", { name: "Start Date : " + date + "" })
    //   .click();
    await this.page
      .locator(`//span[contains(text(), '${date}')]`)
      .first()
      .click();
  }

  async verifyPayrollValues(payroll_value) {
    const payrollElement = await this.page
      .locator('//span[text()="' + payroll_value + '"]')
      .last();
    if (await payrollElement.isVisible()) {
      const payElement = await payrollElement.locator(
        "xpath=following::span[2]"
      );
      return await payElement.textContent();
    }
  }

  async verifyExitValidationSuccess() {
    const validateSuccessPopup = await this.page
      .locator("div")
      .filter({
        hasText: "Validation Successful!",
      })
      .nth(3);
    await validateSuccessPopup.waitFor({ state: "visible" });
  }

  async confirmExit() {
    await this.page.getByRole("button", { name: "Confirm" }).click();
    await this.page.waitForTimeout(2000);
  }

  async clickRevertExit() {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Revert Exit" }).click();
  }

  async verifyExitSaveSuccess() {
    const successPopup = await this.page
      .locator("div")
      .filter({
        hasText: "Save Successful!",
      })
      .nth(3);
    await successPopup.waitFor({ state: "visible" });
  }

  async openImportExportMenu() {
    await this.page.getByRole("button", { name: "Import / Export" }).click();
  }

  async exportUsers() {
    await this.page.getByRole("button", { name: "Export users" }).click();
  }

  async EditExistingUsers() {
    await this.page.getByText("Edit Existing Users").click();
  }

  async verifyExportSuccess(timeout) {
    await this.page
      .getByText("Downloaded Successfully!!")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async updatePayeeRole(currentRole, newRole) {
    await this.page
      .getByRole("gridcell", { name: "MAP PAYEE" })
      .getByRole("button")
      .nth(1)
      .click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page.getByTitle(currentRole).click();
    await this.page.getByText(newRole, { exact: true }).click();
    await this.page.getByRole("button", { name: "Update User" }).click();
    await this.page
      .getByText("User Updated successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async navigateToUsergroup() {
    await this.page.goto("/groups", { waitUntil: "networkidle" });
  }

  async changeProfilePicture(filePath) {
    await this.page
      .getByRole("gridcell", { name: "MAP PAYEE" })
      .getByRole("button")
      .nth(1)
      .click();
    await this.page
      .getByRole("button", { name: "Change Profile Picture" })
      .click();
    await this.page.locator("span>input[type='file']").setInputFiles(filePath);
    await this.page.waitForTimeout(1000); // Consider re-evaluating the need for static waits.
    await this.page.getByRole("button", { name: "Save" }).last().click();
    await this.page.getByRole("button", { name: "Save" }).first().click();
    await this.page
      .getByText("Profile picture uploaded successfully!")
      .waitFor({ state: "visible", timeout: 15000 });
  }

  async clickQuickFilter(name) {
    await this.page.getByText(name, { exact: true }).click();
  }

  async verifyNoUsersFound() {
    await this.page
      .getByText("No Users Found")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async setJoiningDate(date) {
    await this.page.getByLabel("Joining Date").click();
    await this.page.getByTitle(date).click();
  }

  async verifyDateValidationMessage(expectedMessage) {
    await this.page
      .getByText(expectedMessage)
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async saveChanges() {
    await this.page.getByRole("button", { name: "Save" }).last().click();
  }

  async closeDialog() {
    await this.page.getByRole("button", { name: "Close" }).click();
  }

  async verifySaveConfirmation(message) {
    await this.page
      .getByText(message)
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async clickPayeeName(name) {
    await this.page.getByText(name).click();
  }

  async openFilters() {
    // filter user
    await this.page
      .locator(
        '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
      )
      .first()
      .click();
  }

  async applyDropdownFilterWithStatus(type, status) {
    await this.page.getByTestId("status").locator("div").nth(1).click();
    await this.page
      .getByTestId("status")
      .locator("span")
      .filter({ hasText: /^Invited$/ })
      .click();
    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.page.waitForTimeout(2000);
    await this.waitForclearAllFilters();
  }

  async waitForclearAllFilters() {
    await this.page
      .getByRole("button", { name: "Clear all" })
      .last()
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async mapPayeeCount() {
    return this.page.getByRole("button", { name: "Map Payee" }).count();
  }

  async userCount() {
    const index =
      parseInt(
        await this.page
          .locator(".ag-row-last")
          .first()
          .getAttribute("row-index")
      ) + 1;
    return index;
  }

  async mapPayee(email) {
    await this.page.waitForTimeout(2000);
    // await this.page
    //   .locator(`button[data-testid='${email} users dd button']`)
    //   .click();
    // await this.page
    //   .getByRole("menuitem", { name: "Map Payee" })
    //   .getByRole("button")
    //   .click();
    await this.page
      .locator('//div[@col-id="actions"]//*[text()="Map Payee"]')
      .click();
  }

  async mapPayeeByEmail(email) {
    await this.page.waitForTimeout(2000);
    await this.page
      .locator(`button[data-testid='${email} users dd button']`)
      .click();
    await this.page
      .getByRole("menuitem", { name: "Map Payee" })
      .getByRole("button")
      .click();
  }

  async fetchUserStatus() {
    await this.page.waitForTimeout(3000);
    return this.page
      .locator("div[col-id='status'] span[class*='text']")
      .allInnerTexts();
  }

  async edituserMenu(email) {
    await this.page.waitForTimeout(2000);
    await this.page
      .locator(`button[data-testid='${email} users dd button']`)
      .click();
  }

  async loginasPayee(email) {
    await this.page.waitForTimeout(2000);
    await this.page
      .locator(`button[data-testid='${email} users dd button']`)
      .click();
    await this.page
      .getByRole("menuitem", { name: "Login as user" })
      .getByRole("button")
      .click();
  }

  async logout() {
    await this.page.locator("#user-avatar-element").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Logout$/ })
      .nth(1)
      .click();
  }

  async validateImpersonation(payee_name) {
    await this.page.waitForLoadState("networkidle");
    await this.page.waitForTimeout(5000);
    return this.page.getByText("Logged in as " + payee_name + "").count();
  }

  async validateexitImpersonation() {
    await this.page.getByText("Exit").click();
    await this.page.waitForLoadState("networkidle");
    await this.page.waitForTimeout(3000);
  }

  async sendInvite(email2) {
    await this.page.waitForTimeout(2000);
    await this.page
      .locator(`button[data-testid='${email2} users dd button']`)
      .click();
    await this.page.getByRole("button", { name: "Send Invite" }).click();
    await this.page.getByRole("button", { name: "Yes, send" }).click();
    const SEND_INVITE_ALERT = await this.page
      .getByText("Sending Invite")
      .first();
    await SEND_INVITE_ALERT.waitFor({ state: "visible", timeout: 10000 });
  }

  async overwriteSinglePayroll(payroll_text, payroll_value) {
    await this.page.getByPlaceholder(payroll_text).click();
    await this.page.getByPlaceholder(payroll_text).fill(payroll_value);
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByText("Save by overwriting current").click();
    await this.page.getByRole("button", { name: "Save", exact: true }).click();
  }

  async futurePayroll(payroll_text1, payroll_value1) {
    await this.page.getByPlaceholder(payroll_text1).click();
    await this.page.getByPlaceholder(payroll_text1).fill(payroll_value1);
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByLabel("Update User Details")
      .getByRole("button", { name: "Next" })
      .click();
    await this.page.getByRole("textbox", { name: "Select date" }).click();
    await this.page.getByText("Today").click();
    await this.page.getByRole("button", { name: "Save", exact: true }).click();
  }

  async nextAction() {
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async dataUpload(file_path) {
    await this.page
      .locator("span>input[type='file']")
      .setInputFiles("./upload-files/" + file_path + "");
  }

  async overwritehierarchyDetails() {
    await this.page.getByRole("switch").click();
    await this.page.getByRole("button", { name: "Okay" }).click();
  }

  async setNewmanager(manager_name) {
    await this.page
      .locator("div.ant-drawer-body div.ant-select-selector")
      .click();
    await this.page.getByText(manager_name).first().click();
  }

  async changeDate() {
    await this.page.getByPlaceholder("Select date").first().click();
    await this.page.getByText("Today").click();
  }

  async cancelEndDate() {
    await this.page.locator("#reporting-mgr_reportingEffectiveEndDate").hover();
    const clearButton = this.page.locator(".ant-picker-clear");
    if (await clearButton.isVisible({ timeout: 3000 })) {
      await clearButton.click();
    }
  }

  async saveHierarchychanges() {
    await this.page.getByRole("button", { name: "Save Changes" }).click();
    const HIERARCHY_CREATED = await this.page
      .getByText("Hierarchy created successfully!")
      .first();
    await HIERARCHY_CREATED.waitFor({ state: "visible", timeout: 10000 });
  }

  async updateHierarchychanges() {
    await this.page.getByRole("button", { name: "Save Changes" }).click();
    const HIERARCHY_UPDATED = await this.page
      .getByText("Hierarchy updated successfully!")
      .first();
    await HIERARCHY_UPDATED.waitFor({ state: "visible", timeout: 10000 });
  }

  async importExportUsers() {
    await this.openImportExportMenu();
    await this.exportUsers();
  }

  async updateBulkuser() {
    await this.page.getByRole("button", { name: "Update" }).click();
  }

  async confirmExportwithMatchingNumbers(numberOfUsers) {
    await this.page
      .getByText(
        "Export " + numberOfUsers.toString() + " users who match the filter"
      )
      .waitFor({ state: "visible", timeout: 5000 });
    await this.proceedToExport();
    await this.verifyExportSuccess();
  }

  async proceedToExport() {
    await this.page.getByRole("button", { name: "Proceed" }).click();
  }

  /**
   * Validate dropdown and their values in User Filter section
   * @param {String} dropdownName
   * @param {Array} dropdownValues
   */
  async validateDropdownValues(dropdownName, dropdownValues) {
    await this.page
      .getByTestId(`option-${dropdownName}`)
      .locator("div")
      .filter({ hasText: "In" })
      .click();
    await this.page.waitForTimeout(1000);
    const list = await this.getDropdownValues();
    console.log(list);
    expect(list).toEqual(["In", "Not In"]);
    await this.page
      .getByTestId(dropdownName)
      .locator("div")
      .filter({ hasText: "Select" })
      .click();
    const list1 = await this.getDropdownValues();
    expect(list1).toEqual(dropdownValues);
  }

  /**
   * Validate Textfield and their dropdown values in User Filter section
   * @param {String} fieldName
   * @param {Array} dropdownValues
   */
  async validateTextBox(fieldName, dropdownValues) {
    await this.page
      .locator(`[data-testid='ever-select']:near(:text('${fieldName}'))`)
      .first()
      .click();
    const list = await this.getDropdownValues();
    console.log("received", list);
    for (const value of dropdownValues) {
      expect(list).toContain(value);
    }
  }

  /**
   * Get the Dropdown values
   * @returns {Array} listitem text values
   */
  async getDropdownValues() {
    const list = await this.page.getByRole("listitem").allInnerTexts();
    await this.page.keyboard.press("Escape");
    await this.page.waitForTimeout(2000);
    return list;
  }

  /**
   * Add More Fields with the name of input Array
   * @param {Array} fieldNames
   */
  async addMoreFilters(fieldNames) {
    for (const fieldName of fieldNames) {
      await this.page.getByRole("button", { name: "More filters" }).click();
      await this.page.getByRole("menuitem", { name: fieldName }).click();
    }
  }

  /**
   * Close the Filter Drawer in User Screen
   *
   */
  async closeFilters() {
    await this.page
      .locator("div.transition-all  div.flex-col button.ant-btn")
      .first()
      .click();
  }

  /**
   * Function to click on Apply button if Flag True
   * Flag - false validate Apply Button if it is disabled
   * @param {Boolean} flag
   */
  async applyFilters(flag) {
    if (flag) {
      await expect(
        this.page.getByRole("button", { name: "Apply" })
      ).toBeEnabled();
      await this.page.getByRole("button", { name: "Apply" }).click();
    } else
      await expect(
        this.page.getByRole("button", { name: "Apply" })
      ).toBeDisabled();
  }

  /**
   * Select Dropdown Type select option
   *
   * @param {String} dropdownName
   * @param {String} option
   */
  async selectDropdownOption(dropdownName, option) {
    await this.page
      .getByTestId(`option-${dropdownName}`)
      .locator("div")
      .filter({ hasText: "In" })
      .click();
    await this.page.getByText(option, { exact: true }).click();
  }

  /**
   * Select Dropdown value based on dropdown name
   *
   * @param {String} dropdownName
   * @param {String} option
   */
  async selectDropdownValue(dropdownName, option) {
    await this.page.getByTestId(dropdownName).click();
    await this.page.getByTestId(dropdownName).locator("input").fill(option);
    await this.page
      .getByTestId(dropdownName)
      .getByText(option, { exact: true })
      .last()
      .click();
    await this.page.keyboard.press("Escape");
    await expect(
      this.page.locator("label").filter({ hasText: option }).last()
    ).toBeVisible();
  }

  /**
   * Validate user count in the User page
   * flag - true validate export users module also
   *
   * @param {Integer} expectedCount
   * @param {Boolean} flag
   */
  async validateUserCount(expectedCount, flag) {
    // await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000);
    await this.scrollTillLast();
    if (!flag) {
    } else await this.validateExportInFilteredResults(expectedCount);
    console.log(
      await this.page
        .locator("div.ag-row-last")
        .first()
        .getAttribute("row-index")
    );
    const actualCount =
      parseInt(
        await this.page
          .locator("div.ag-row-last")
          .first()
          .getAttribute("row-index")
      ) + 1;
    expect(actualCount).toBe(expectedCount);
  }

  /**
   * Validate Labels present above the user list
   *
   * @param {Array} labels
   */
  async validateLabelinUsersScreen(labels) {
    for (const label of labels) {
      await expect(this.page.getByText(label)).toBeVisible();
    }
    await expect(
      this.page.getByRole("button", { name: "Clear all" })
    ).toBeVisible();
  }

  /**
   * validate if the label above the user list are removed
   *
   * @param {Array} labels
   */
  async labelRemovedInUsersScreen(labels) {
    for (const label of labels) {
      await expect(this.page.getByText(label)).toBeHidden();
    }
    await expect(
      this.page.getByRole("button", { name: "Clear all" })
    ).toBeHidden();
  }

  /**
   * Remove the label above user list
   *
   * @param {Array} options
   */
  async removeFilter(options) {
    for (const option of options) {
      await this.page
        .locator("label")
        .filter({ hasText: option })
        .locator("svg")
        .click();
    }
  }

  /**
   * Remove label above user list
   *
   * @param {Array} options
   */
  async removeFilterFromUserScreen(options) {
    // for (const option of options) {
    //   await this.page
    //     .locator("div")
    //     .filter({ hasText: new RegExp(`^Applied Filters${option}Clear all$`) })
    //     .locator("svg")
    //     .click();
    // }
    await this.page.getByRole("button", { name: "Clear all" }).click();
  }

  /**
   * Function to increase pagination from 20 to 100
   */
  async increasePagination() {
    await this.page.getByTitle("20").click();
    await this.page.getByTitle("100").click();
  }

  /**
   * Function to scroll till end of the user lists
   */
  async scrollTillLast() {
    await this.page.evaluate(async () => {
      const scrollContainer = document.querySelector(
        "div[class*='ag-body-viewport']"
      );
      const scrollStep = 500;

      if (!scrollContainer) return;

      let previousScrollTop = -1;

      while (scrollContainer.scrollTop !== previousScrollTop) {
        previousScrollTop = scrollContainer.scrollTop;
        scrollContainer.scrollBy(0, scrollStep);
        await new Promise((resolve) => setTimeout(resolve, 300)); // smooth scroll delay
      }
    });
  }

  /**
   * Enter Value in Textbox based on the field name
   *
   * @param {String} fieldName
   * @param {String} option
   * @param {String} value
   * @param {String} flag
   */
  async enterTextBox(fieldName, option, value, flag) {
    await this.page
      .locator(`[data-testid='ever-select']:near(:text('${fieldName}'))`)
      .first()
      .click();
    await this.page.locator(`div[label='${option}']`).click();
    if (!flag)
      await this.page
        .locator(`[placeholder="Type"]:near(:text("${fieldName}"))`)
        .fill(value);
  }

  async enterDateFields(fieldName, option, FromValue, ToValue) {
    await this.page
      .locator(`[data-testid='ever-select']:below(:text('${fieldName}'))`)
      .first()
      .click();
    await this.page.locator(`div[label='${option}']`).click();
    await this.page
      .locator(`[placeholder="From"]:below(:text("${fieldName}"))`)
      .first()
      .click();
    await this.page
      .locator(`[placeholder="From"]:below(:text("${fieldName}"))`)
      .first()
      .fill(FromValue);
    await this.page
      .locator(`[placeholder="To"]:below(:text("${fieldName}"))`)
      .first()
      .click();
    await this.page
      .locator(`[placeholder="To"]:below(:text("${fieldName}"))`)
      .first()
      .fill(ToValue);
    await this.page.keyboard.press("Enter");
  }

  /**
   * Select Date field Dropdown Type and Its date value based on the input in User Filter Section
   *
   * @param {String} fieldName
   * @param {String} option
   * @param {String} date
   */
  async enterDateField(fieldName, option, date) {
    await this.page
      .locator(`[data-testid='ever-select']:below(:text('${fieldName}'))`)
      .first()
      .click();
    await this.page.locator(`div[label='${option}']`).click();
    await this.page
      .locator(`[placeholder="Select date"]:below(:text("${fieldName}"))`)
      .first()
      .click();
    await this.page
      .locator(`[placeholder="Select date"]:below(:text("${fieldName}"))`)
      .first()
      .fill(date);
    await this.page.keyboard.press("Enter");
  }

  /**
   * Click on Clear All button above the user list
   */
  async clearAll() {
    await this.page.getByRole("button", { name: "Clear all" }).click();
    await this.page.waitForTimeout(2000);
  }

  /**
   * validate Exported excel sheet in the user list
   *
   * @param {Integer} expectedCount
   */
  async validateExportInFilteredResults(expectedCount) {
    await this.page.getByRole("button", { name: "Import / Export" }).click();
    await this.page.getByText("Export users", { exact: true }).click();
    const downloadPromise = this.page.waitForEvent("download");
    await this.page.getByRole("button", { name: "Proceed" }).click();
    const download = await downloadPromise;
    const downloadPath = path.join(
      __dirname,
      "downloads",
      download.suggestedFilename()
    );
    await download.saveAs(downloadPath);
    const filePath = await download.path();
    if (filePath) {
      let rowCount = 0;
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", () => rowCount++)
        .on("end", () => {
          console.log(`Row count: ${rowCount}`);
          expect(rowCount).toBe(expectedCount);
        });
    }
  }

  /**
   * Validate if the clear in filter section is clicked and Labels are removed in filter section
   *
   * @param {Array} options
   */
  async clearFromFilters(options) {
    await this.page.getByText("Clear").click();
    for (const option of options) {
      await this.labelRemovedCheck(option);
    }
    await this.applyFilters(false);
  }

  async labelRemovedCheck(option) {
    await expect(
      this.page.locator("label").filter({ hasText: option }).last()
    ).toBeHidden();
  }

  /**
   * Search for results in the user screen
   * @param {String} value
   */
  async searchInFilteredResults(value) {
    await this.searchUserLocator.fill(value);
    await this.page.waitForTimeout(3000);
  }

  /**
   * Function to Perform Bulk Invite in User screen
   */
  async bulkInvite() {
    // await this.page.getByRole('checkbox',{name : "selectAll"}).click();
    await this.page.locator('[name="selectAll"]').click();
    await this.page.getByText("Send Invite").click();
    await this.page.getByRole("button", { name: "Yes, proceed" }).click();
    await this.page
      .getByText("Invites sent successfully")
      .waitFor({ state: "visible", timeout: 180000 });
  }

  /**
   * Function to perform Bulk Set Source in Users page
   */
  async bulkSetSource() {
    await this.page.locator('[name="selectAll"]').click();
    await this.page.getByText("Set User Source").click();
    await this.page.locator('[role="combobox"][type="search"]').last().click();
    await this.page
      .getByText("Managed via integrations", { exact: true })
      .click();
    await this.page.getByRole("button", { name: "Ok, update" }).click();
    await this.page
      .getByText("User source updated successfully")
      .waitFor({ state: "visible" });
  }

  /**
   * Validate Pagination after filter applied
   */
  async validatePagination() {
    await expect(this.page.getByText("1 - 20of129rowsPage of7")).toBeVisible();
    await this.increasePagination();
    await expect(this.page.getByText("1 - 100of129rowsPage of2")).toBeVisible();
  }

  /**
   * Validate if empty results are displayed
   */
  async validateEmptyResult() {
    await expect(await this.page.locator(".ag-row-odd")).toHaveCount(0);
  }

  /**
   * Modify Custom Terminology :
   * @param {String} fieldName
   */
  async modifyCustomTerminology(fieldName, value) {
    await this.page.goto("/settings/custom-terminology", {
      waitUntil: "networkidle",
    });
    await this.page.getByTestId(`pt-${fieldName}`).click();
    await this.page.getByLabel("Input Editor").fill(value);
    await this.page.keyboard.press("Tab");
    const button = await this.page.getByRole("button", { name: "Update" });
    if (await button.isEnabled()) {
      await button.click();
    } else {
      console.log("Button is disabled, cannot click.");
    }
    await this.page.waitForLoadState("load");
  }
  /**
   * Performs specified actions for a given user based on email.
   *
   * @param {string} email - The user's email address.
   * @param {string} action - The action to perform (e.g., Edit, Change Profile Picture, Map Payee,
   * Send Invite, Login as user, Initiate Exit, or Revert Exit if the user has already exited).
   */

  async clickTripleDotsAndPerformAction(email, action) {
    const tripleDotsButton = this.page.locator(
      `button[data-testid='${email} users dd button']`
    );
    await tripleDotsButton.waitFor({ state: "visible" });
    await tripleDotsButton.click();
    const actionButton = this.page.getByRole("menuitem", { name: action });
    await actionButton.waitFor({ state: "visible" });
    await actionButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async deleteUser(email) {
    await this.page
      .locator(`button[data-testid='${email} users dd button']`)
      .click();
    await this.page.locator(`button:has-text("Delete user")`).click();
    await this.page.waitForLoadState("networkidle");
  }

  async deletePayroll() {
    await this.page.getByText("Delete", { exact: true }).click();
  }

  async editleastPayroll() {
    await this.page
      .getByLabel("Basic & Payroll History")
      .locator("button")
      .click();
  }

  async editleastCustomField() {
    await this.page
      .getByLabel("Custom Field History")
      .locator("button")
      .click();
  }

  async clickCustomField() {
    await this.page.getByRole("tab", { name: "Custom Field History" }).click();
  }

  async acceptBulkImport() {
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async startImport() {
    await this.page.getByRole("button", { name: "Start Import" }).click();

    await this.page
      .getByLabel(
        "Enter Email ID of the person who needs to be notified once job is complete"
      )
      .fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText(
        "Import has started. You will be notified by email once complete."
      )
      .waitFor({ state: "visible" });
    await this.page
      .getByRole("button", { name: "Filters" })
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.waitForTimeout(3000);
  }

  async verifyPayrollbutton() {
    const locator = this.page.locator("//ul/li//span");
    const text = await locator.allTextContents();
    console.log(text);
    return text;
  }

  async editParollfields(payroll_fields) {
    await this.page
      .locator("span")
      .filter({ hasText: payroll_fields })
      .first()
      .click();
  }

  async editcustomfields(custom_fields) {
    await this.page.getByLabel(custom_fields, { exact: true }).check();
  }

  async checkIfTextExists(menu_text) {
    const payrollText = await this.verifyPayrollbutton();
    const searchText = menu_text;
    const isPresent = payrollText.some((text) =>
      text.toLowerCase().includes(searchText.toLowerCase())
    ); // Case-insensitive match

    return isPresent; // Return true or false
  }

  async editPayroll(date) {
    await this.page
      .getByRole("button", { name: date })
      .getByRole("button")
      .first()
      .click();
  }

  async datePopup(payroll_date) {
    const locator = this.page.locator("//span[text()='" + payroll_date + "']");
    const isVisible = await locator.isVisible();
    return isVisible;
  }

  async cancelCTA() {
    const cancelButton = await this.page
      .getByRole("dialog")
      .getByRole("button", { name: "Cancel" });
    const isCancelButtonVisible = await cancelButton.isVisible();
    return isCancelButtonVisible;
  }

  async deletedialog() {
    await this.page.getByRole("button", { name: "Delete" }).click();
  }

  async payrollDeleteAlert() {
    const alert = this.page.getByText("Payroll period deleted successfully");

    try {
      await alert.waitFor({ state: "visible", timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async hierarchyDeleteAlert() {
    const alert = this.page.getByText("Reporting manager deleted successfully");

    try {
      await alert.waitFor({ state: "visible", timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async customFieldDeleteAlert() {
    const alert = this.page.getByText("Custom field deleted successfully");

    try {
      await alert.waitFor({ state: "visible", timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  async deleteHierarchy(manager_name) {
    await this.page
      .getByRole("row", { name: manager_name })
      .getByRole("button")
      .click();
  }

  async navigateToTeams() {
    await this.page.goto("/teams", { waitUntil: "networkidle" });
  }

  async verifyHierarchy(manager_name, reporterEmail) {
    const managerRow = this.page.getByRole("row", { name: manager_name });
    const svgElement = managerRow.locator("svg");

    // Check if SVG element (expand button) is visible before clicking
    if (await svgElement.isVisible()) {
      await svgElement.click();
    } else {
      return false; // Return false if the SVG element is not visible
    }

    try {
      await this.page
        .locator(`[row-id="${reporterEmail}"]`)
        .first()
        .waitFor({ state: "visible" });
      return true; // Return true if the element is visible
    } catch (error) {
      return false; // Return false if the element is not visible
    }
  }

  async navigatetoCalendarYear(date) {
    const targetYear = date.split("-")[2];
    const currentYear = await this.page
      .locator(".ant-picker-year-btn")
      .last()
      .innerText();
    if (parseInt(targetYear) < parseInt(currentYear)) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-prev-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    } else if (targetYear > currentYear) {
      await this.clickUntilVisible(
        ".ant-picker-header-super-next-btn",
        `//button[@class='ant-picker-year-btn' and text()='${targetYear}']`
      );
    } else {
      console.log("No button clicked for year: " + date);
    }

    const monthMapping = {
      Jan: 1,
      Feb: 2,
      Mar: 3,
      Apr: 4,
      May: 5,
      Jun: 6,
      Jul: 7,
      Aug: 8,
      Sep: 9,
      Oct: 10,
      Nov: 11,
      Dec: 12,
    };

    const monthName = date.split("-")[1].trim();
    const expectedMonth = monthMapping[date.split("-")[1].trim()];
    const currentMonth = await this.page
      .locator(".ant-picker-month-btn")
      .last()
      .innerText();
    const month = monthMapping[currentMonth.trim()];
    console.log(expectedMonth, month);

    if (expectedMonth < month) {
      await this.clickUntilVisible(
        ".ant-picker-header-prev-btn",
        `//button[@class='ant-picker-month-btn' and text()='${monthName}']`
      );
    } else if (expectedMonth > month) {
      await this.clickUntilVisible(
        ".ant-picker-header-next-btn",
        `//button[@class='ant-picker-month-btn' and text()='${monthName}']`
      );
    } else {
      console.log("No button clicked for year: " + date);
    }
    if (expectedMonth < 10) {
      return `${targetYear}-0${expectedMonth}`;
    } else {
      return `${targetYear}-${expectedMonth}`;
    }
  }

  async clickUntilVisible(
    btnSelector,
    elementSelector,
    timeout = 30000,
    clickDelay = 1000
  ) {
    const startTime = Date.now();
    while (true) {
      try {
        if (Date.now() - startTime > timeout) {
          console.log("Timeout reached! Element is still not visible.");
          return false;
        }
        await this.page.locator(btnSelector).last().click();
        console.log("Clicked the element, waiting for visibility...");
        const isVisible = await this.page
          .locator(elementSelector)
          .last()
          .isVisible();
        if (isVisible) {
          console.log("Element is now visible!");
          return true;
        }
        console.log("Element is not visible, retrying...");
      } catch (error) {
        console.log("Error while trying to click:", error);
      }

      await this.page.waitForTimeout(clickDelay);
    }
  }

  async setEffectiveStartDate(startDate) {
    await this.page.getByLabel("Effective Start Date*").click();
    const monthYear = await this.navigatetoCalendarYear(startDate);
    const day = startDate.split("-")[0];
    await this.page.locator(`td[title='${monthYear}-${day}']`).first().click();
    await this.page.keyboard.press("Tab");
  }

  async setEffectiveEndDate(endDate) {
    if (endDate === null) {
      await this.page.getByLabel("Effective End Date").click();
      await this.page
        .locator("#reporting-mgr_reportingEffectiveEndDate")
        .fill("");
      if (await this.page.locator(".ant-picker-clear").isVisible())
        await this.page.locator(".ant-picker-clear").click();
      return;
    }
    await this.page.getByLabel("Effective End Date").click();
    const monthYear = await this.navigatetoCalendarYear(endDate);
    const day = endDate.split("-")[0];
    await this.page.locator(`td[title='${monthYear}-${day}']`).last().click();
    await this.page.keyboard.press("Enter");
  }

  async setManager(managerName, StartDate, EndDate, overwriteFlag) {
    if (overwriteFlag) {
      await this.overwritehierarchyDetails();
    }
    await this.setNewmanager(managerName);
    await this.setEffectiveStartDate(StartDate);
    await this.setEffectiveEndDate(EndDate);
  }

  async clickSaveChangesBtn() {
    await this.page
      .getByRole("button", { name: "Save Changes", exact: true })
      .click();
  }

  async validateCyclicHierarchy() {
    await this.page
      .getByText(
        "Adding this reporting manager would result in cyclic hierarchy. Please check!"
      )
      .waitFor({ state: "visible" });
    await this.page
      .getByText(
        "Adding this reporting manager would result in cyclic hierarchy. Please check!"
      )
      .waitFor({ state: "hidden" });
  }

  async closeMapPayee() {
    await this.page.getByLabel("Close", { exact: true }).click();
  }

  async openSplitModal() {
    await this.page.locator(".ant-table-cell button").first().click();
    await this.page.getByRole("menuitem", { name: "Split" }).click();
  }

  async openEditModal() {
    await this.page.locator(".ant-table-cell button").first().click();
    await this.page.getByRole("menuitem", { name: "Edit" }).click();
  }

  async splitManager(name, date) {
    await this.page
      .locator(".ant-modal-content")
      .locator("[data-testid='ever-select']:below(:text('Reporting manager'))")
      .first()
      .click();
    await this.page.locator("#reportingManagerEmailId").fill(name);
    await this.page.getByText(name, { exact: true }).last().click();
    await this.page
      .locator(".ant-modal-content")
      .getByLabel("Effective Start Date*")
      .click();
    const monthYear = await this.navigatetoCalendarYear(date);
    const day = date.split("-")[0];
    await this.page.locator(`td[title='${monthYear}-${day}']`).first().click();
    await this.page.keyboard.press("Tab");
    await this.page.getByRole("button", { name: "Proceed" }).click();
    await this.page.getByRole("button", { name: "Apply changes" }).click();
  }

  async backToEdit() {
    await this.page.getByRole("button", { name: "Back to edit" }).click();
  }

  async validateHierarchyError() {
    await this.page
      .getByText("Editing this hierarchy results in a cycle. Please check!")
      .waitFor({ state: "visible" });
    await this.page
      .getByText("Editing this hierarchy results in a cycle. Please check!")
      .waitFor({ state: "hidden" });
  }

  async updateExcelFile(filePath, data) {
    const updatedRows = [];

    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (row) => {
        updatedRows.push(row);
      })
      .on("end", () => {
        updatedRows.length = 0;

        const headers = [
          "Email",
          "Manager Email",
          "Manager Effective Start Date",
          "Manager Effective End Date",
        ];
        updatedRows.push(headers);

        data.forEach((row) => {
          updatedRows.push(row);
        });

        const csvData = updatedRows.map((row) => row.join(",")).join("\n");

        fs.writeFileSync(filePath, csvData, "utf8");

        console.log("CSV file updated successfully!");
      });
  }

  async importEditUsers(labels, filePath, overwriteFlag) {
    await this.page.getByRole("button", { name: "Import / Export" }).click();
    await this.page
      .getByRole("menuitem", { name: "Edit Existing Users" })
      .click();
    for (const label of labels) {
      await this.page.getByLabel(label).click();
    }
    if (overwriteFlag) {
      await this.page.getByRole("switch").click();
    }
    await this.page.getByRole("button", { name: "Next" }).click();
    const fileChooserPromise = this.page.waitForEvent("filechooser");
    await this.page.getByText("Browse").click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(filePath);
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async validateErrorDesc(errorMessages) {
    await this.page.locator("[col-id='validation_result'].ag-cell").waitFor();
    const actualErrorMessages = await this.page
      .locator("[col-id='validation_result'].ag-cell")
      .allInnerTexts();
    console.log(actualErrorMessages);
    for (let i = 0; i < (await actualErrorMessages.length); i++) {
      expect(actualErrorMessages[i]).toBe(errorMessages[i]);
    }
  }

  async closeImportModal() {
    await this.page.locator(".ant-drawer-close").click();
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async acceptCommissionSyncMsg() {
    await this.page.getByText("Got it", { exact: true }).click();
  }
}

module.exports = UserPage;
