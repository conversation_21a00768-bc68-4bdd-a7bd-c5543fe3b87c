/**
 * Here we will generate fixtures for:
 * Environment: whatever is passed as --project in command.
 * Login creds: this wll be taken from the respective loginCreds.js file
 */

const { createPageFixtures } = require("./fixtureUtils");

export const defaultFixtures = createPageFixtures();
export const crystalFixtures = createPageFixtures("CRYSTAL_CREDS");
export const queriesFixtures = createPageFixtures("QUERIES_CREDS");
export const rankAndLeaderboardFixtures = createPageFixtures(
  "RANK_LEADERBOARD_CREDS"
);
export const qaFixtures = createPageFixtures("LOCAL_QACREDS");
export const qaNotificationFixtures = createPageFixtures(
  "LOCAL_NOTIFICATION_CREDS"
);
export const localCloneFixtures = createPageFixtures("LOCAL_CLONE_CREDS");
export const localplayFixtures = createPageFixtures("LOCALPLAY_CREDS");
export const contractFixtures = createPageFixtures("LOCAL_CONTRACTS_CREDS");
export const simulationFixtures = createPageFixtures("SIMULATIONV2_CREDS");
export const localplaywrightFixtures = createPageFixtures(
  "LOCAL_PLAYWRIGHT_CREDS"
);
export const commissionplanFixtures = createPageFixtures(
  "COMMISSIONPLAN_CREDS"
);
export const commisionplanRBACFixtures = createPageFixtures(
  "COMMISSIONRBAC_CREDS"
);

export const addedFixtures = createPageFixtures("ADDED_CREDS");
export const invitedFixtures = createPageFixtures("INVITED_CREDS");
export const activeFixtures = createPageFixtures("ACTIVE_CREDS");
export const inactiveFixtures = createPageFixtures("INACTIVE_CREDS");
export const commissionplanv2Fixtures = createPageFixtures(
  "COMMISSIONPLAN_V2_CREDS"
);
export const enrichFixtures = createPageFixtures("LOCAL_ENRICH_CREDS");
export const mapPayeeFixtures = createPageFixtures("MAPPAYEE_CREDS");
export const lineitemFixtures = createPageFixtures("LINEITEM_CREDS");
export const forecastFixtures = createPageFixtures("FORECAST_CREDS");
export const canvasFixtures = createPageFixtures("CANVAS_CREDS");

export const bulkUploadFixtures = createPageFixtures("BULKUPLOAD_USERS_CREDS");
export const teamsFixtures = createPageFixtures("TEAMS_USERS_CREDS");
export const playwrightAddPeopleFixtures = createPageFixtures(
  "PLAYWRIGHT_PEOPLE_CREDS"
);
export const playwrightAddPeopleFixtures2 = createPageFixtures(
  "PLAYWRIGHT_PEOPLE_CREDS_2"
);
export const playwrightMapPayeeFixtures = createPageFixtures(
  "PLAYWRIGHT_MAP_PAYEE_CREDS"
);

export const playwrightHierarchyFixtures = createPageFixtures(
  "PLAYWRIGHT_HIERARCHY_CREDS"
);
export const newappFixtures = createPageFixtures("NEWAPP_CREDS");
export const adjApprovalsFixtures = createPageFixtures("ADJ_APPROVALS_CREDS");
export const canvasUIFixtures = createPageFixtures("CANVAS_CREDS_UI");
export const BYOTFixtures = createPageFixtures("BYOT_TEST_USER_CREDS");
export const customCalendarFixtures = createPageFixtures(
  "CUSTOM_CALENDAR_TEST_USER_CREDS"
);
export const objectPermissionsFixtures = createPageFixtures(
  "OBJECT_PERMISSIONS_TEST_USER_CREDS"
);
export const objectPermissionsDisabledFixtures = createPageFixtures(
  "OBJECT_PERMISSIONS_DISABLED_TEST_USER_CREDS"
);
export const lineitemapprovalsFixtures = createPageFixtures(
  "LINEITEM_APPROVALS_CREDS"
);
export const lineitemapprovalsadminFixtures = createPageFixtures(
  "LINEITEM_MANAGER_APPROVALS_CREDS"
);
export const payoutapprovalsadminFixtures = createPageFixtures(
  "PAYOUT_APPROVALS_CREDS"
);
export const settlementFixtures = createPageFixtures("SETTLEMENT_ADMIN_CREDS");

export const userlockFixtures = createPageFixtures("USER_LOCK_CREDS");
export const customObjectCrmFlagEnabledFixtures = createPageFixtures(
  "CUSTOM_OBJECT_CREDS"
);
export const customObjectCrmFlagDisabledFixtures = createPageFixtures(
  "CUSTOM_OBJECT_CRM_FLAG_DISABLE_CREDS"
);
export const rolesFixtures = createPageFixtures("ROLES_CREDS");
export const reportEnrichmentFixtures = createPageFixtures(
  "REPORT_ENRICHENT_CREDS"
);
export const reportEnrichmentLocalGlobalFixtures = createPageFixtures(
  "REPORT_ENRICHENT_CLIENT_CREDS"
);
export const commissionSyncFixtures = createPageFixtures(
  "COMMISSION_SYNC_CREDS"
);
export const commissionAdjustmentFixtures = createPageFixtures(
  "COMMISSION_ADJUSTMENTS_CREDS"
);
export const commissionAdjustmentOffFixtures = createPageFixtures(
  "COMMISSION_ADJUSTMENTS_OFF_CREDS"
);
export const drawsAdjustmentsFixtures = createPageFixtures(
  "DRAW_ADJUSTMENTS_CREDS"
);
export const commissionSyncAdminFixtures = createPageFixtures(
  "COMMISSION_SYNC_FOR_ADMINS_CREDS"
);
export const playwrightCommPlanCanvasFixtures = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_CANVAS_CREDS"
);
export const playwrightCommPlanForecastFixtures = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS"
);
export const playwrightCommPlanForecastFixturesSa = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_SA"
);
export const playwrightCommPlanForecastFixtures2 = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_2"
);
export const playwrightCommPlanForecastFixtures3 = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_3"
);
export const playwrightCommPlanTierFixtures = createPageFixtures(
  "PLAYWRIGHT_COMM_PLAN_TIER_CREDS"
);

export const quotaFunctionFixtures = createPageFixtures("QUOTA_FUNCTION_CREDS");
export const commSyncCredsWithoutCustomCalender =
  createPageFixtures("COMM_SYNC_CREDS");
export const quotaFixtures = createPageFixtures("QUOTA_CREDS");
export const quotaPayeeFixtures = createPageFixtures("QUOTA_PAYEE_CREDS");
export const globalSearchFixtures = createPageFixtures("GLOBAL_SEARCH_CREDS");
export const globalSearchRevampFixtures = createPageFixtures(
  "GLOBAL_SEARCH_REVAMP_CREDS"
);
export const cwFixtures = createPageFixtures("CW_CREDS");
export const settlementFixtures1 = createPageFixtures("SETTLEMENT_CREDS");
export const dashboardCredsFixtures = createPageFixtures("DASHBOARD_CREDS");
export const dashboardAdminCredsFixtures = createPageFixtures(
  "DASHBOARD_ADMIN_CREDS"
);

export const settingsNewFixtures = createPageFixtures(
  "SETTINGS_NEW_BALA_CREDS"
);
export const settingsNewAdminFixtures = createPageFixtures(
  "SETTINGS_NEW_BALA_ADMIN_CREDS"
);
export const playwrightSettlementFixtures = createPageFixtures(
  "PLAYWRIGHT_SETTLEMENT_CREDS"
);
export const queriesTestFixtures = createPageFixtures("QUERY_TEST_CREDS");
export const drawsTestFixtures1 = createPageFixtures("DRAWS_TEST_CREDS_1");
export const drawsTestFixtures = createPageFixtures("DRAWS_TEST_CREDS");
export const commAdjAppFixtures = createPageFixtures("COMM_ADJ_APP_CREDS");
export const commAdjAppFixtures2 = createPageFixtures("COMM_ADJ_APP_CREDS_2");
export const commAdjAppFixtures3 = createPageFixtures("COMM_ADJ_APP_CREDS_3");
export const plansBalaFixtures = createPageFixtures("PLANS_BALA_CREDS");
export const plansBalaPayeeFixtures = createPageFixtures(
  "PLANS_BALA_PAYEE_CREDS"
);
export const plansBalaPayeeFixtures2 = createPageFixtures(
  "PLANS_BALA_PAYEE_CREDS_2"
);
export const revertFixtures = createPageFixtures("REVERTUSER_TEST_CREDS");
export const payoutsBalaFixtures = createPageFixtures("PAYOUTS_BALA_CREDS");
export const payoutsPayeeFixtures = createPageFixtures("PAYOUTS_PAYEE_CREDS");
export const payoutsPayeeFixtures2 = createPageFixtures(
  "PAYOUTS_PAYEE_CREDS_2"
);
export const payoutsPayeeFixtures3 = createPageFixtures(
  "PAYOUTS_PAYEE_CREDS_3"
);
export const payoutsBalaFixtures2 = createPageFixtures("PAYOUTS_BALA_CREDS_2");
export const payoutsBalaFixtures3 = createPageFixtures("PAYOUTS_BALA_CREDS_3");
export const roundOffTierFixtures = createPageFixtures("ROUNDOFFTIER_CREDS");

export const datasheetV2Fixtures = createPageFixtures("DATASHEET_V2_CREDS");
export const statementsSnapFixtures = createPageFixtures(
  "STATEMENT_SNAP_CREDS"
);
export const hrisFixtures = createPageFixtures("HRIS_CREDS");
export const crystalBalaFixtures = createPageFixtures("CRYSTAL_BALA_CREDS");
export const crystalPayeeFixtures1 = createPageFixtures(
  "CRYSTAL_PAYEE_CREDS_1"
);
export const crystalPayeeFixtures2 = createPageFixtures(
  "CRYSTAL_PAYEE_CREDS_2"
);

export const queriesLineItemsFixtures = createPageFixtures(
  "QUERIES_LINE_ITEMS_CREDS"
);

export const breakglassFixtures = createPageFixtures("BREAKGLASS_CREDS");

export const commissionSyncPrevperiodFixtures = createPageFixtures(
  "COMMISSION_SYNC_RUN_PREV_PERIOD_CREDS"
);

export const databookUI2Fixtures = createPageFixtures("DATABOOK_UI_2_CREDS");

export const celeryTaskFixtures = createPageFixtures("CELERY_TASK_CREDS");

export const quotaAdminFixtures = createPageFixtures("QUOTA_ADMIN_CREDS");
export const quotaManageQuotaFixtures = createPageFixtures(
  "QUOTA_MANAGEQUOTA_CREDS"
);

export const payeecurrencyFixtures = createPageFixtures("PAYEE_CURRENCY_CREDS");

export const usergroupFixtures = createPageFixtures("USER_GROUP_CREDS");
export const ikUsersFixtures = createPageFixtures("IK_USERS_CREDS");

export const reEvaluateFixtures = createPageFixtures("REEVALUATE_CREDS");

export const g2Review = createPageFixtures("G2REVIEW_CREDS");

export const payoutFixtures = createPageFixtures("PAYOUT_CREDS");

export const renameHyperlinkFixtures = createPageFixtures(
  "RENAME_HYPERLINK_CREDS"
);

export const commissionSyncETLFixtures = createPageFixtures(
  "COMMISSION_SYNC_ETL_CREDS"
);
export const ckEditorCwFixtures = createPageFixtures("CKEDITORCW_CREDS");

export const adjustmentv2Fixtures = createPageFixtures("ADJUSTMENTS_V2_CREDS");

export const quotaAllowOverrideFixtures = createPageFixtures(
  "QUOTA_ALLOW_OVERRIDE_CREDS"
);

export const quotaAnnualEDFixtures = createPageFixtures(
  "QUOTA_ANNUAL_ED_CREDS"
);

export const mapPayeeHistoricalEditsFixtures = createPageFixtures(
  "MAP_PAYEE_HISTORICAL_EDITS_CREDS"
);

export const commisionLineItemSortFixttures = createPageFixtures(
  "COMMISSION_LINE_ITEM_SORT_CREDS"
);

export const cpqFixtues = createPageFixtures("CPQ_CREDS");

export const payoutikFixtures = createPageFixtures("PAYOUTSIKPLAYWRIGHT_CREDS");

export const autoenrichFixtures = createPageFixtures(
  "AUTO_ENRICH_REPORT_CREDS"
);

export const notificationAuditFixtures = createPageFixtures(
  "NOTIFICATION_AUDIT_TABLE_CREDS"
);

export const userInviteMultiUser1Fixtures = createPageFixtures(
  "USER_INVITE_MULTI_USER_1_CREDS"
);

export const userInviteMultiUser2Fixtures = createPageFixtures(
  "USER_INVITE_MULTI_USER_2_CREDS"
);

export const invitedUserFixtures = createPageFixtures("INVITED_USER_CREDS");

export const validateUser1Fixtures = createPageFixtures(
  "VALIDATE_USER_1_CREDS"
);

export const validateUser2Fixtures = createPageFixtures(
  "VALIDATE_USER_2_CREDS"
);
export const automateQA1Fixtures = createPageFixtures("AUTOMATE_QA1_CREDS");

export const planPeriodExlusionFixtures = createPageFixtures(
  "PLAN_PERIOD_EXCLUSION_CREDS"
);

export const bulkAdjustmentv2Fixtures = createPageFixtures(
  "BULK_ADJUSTMENTS_V2_CREDS"
);

export const datasheetV2ManagePermissionFixtures = createPageFixtures(
  "DATASHEET_V2_MANAGE_PERMISSION_CREDS"
);

export const splitSummationFixtures = createPageFixtures(
  "SPLIT_SUMMATION_CREDS"
);

export const advancedFiltersFixtures = createPageFixtures(
  "ADVANCEDFILTERS_CREDS"
);
export const deletePayrollFixtures = createPageFixtures("DELETE_PAYROLL_CREDS");

export const manualuploadRevamp = createPageFixtures(
  "MANUAL_UPLOAD_REVAMP_CREDS"
);

export const commissionAdjustmentFilterFixtures = createPageFixtures(
  "COMMISSION_ADJUSTMENT_FILTER_CREDS"
);

export const cpqTestFixtures = createPageFixtures("CPQTEST_CREDS");

export const datasheetProdAutomateFixtures = createPageFixtures(
  "DATASHEET_PROD_CREDS"
);

export const dsV2EnrichmentFixtures = createPageFixtures(
  "DS_V2_ENRICHMENT_CREDS"
);

export const cpqSubscriptionDateFixtures = createPageFixtures(
  "CPQ_SUBSCRIPTION_DATE_CREDS"
);

export const emailStatementsFixtures = createPageFixtures(
  "EMAIL_STATEMENTS_CREDS"
);

export const splitEmployeeRecordsFixtures = createPageFixtures(
  "SPLIT_EMPLOYEE_CREDS"
);

export const crystalNewFixtures = createPageFixtures("CRYSTAL_NEW_CREDS");

export const bulkRegisterPaymwnts = createPageFixtures(
  "BULK_PAYMENTS_REGISTER_CREDS"
);

export const customCategoryFixtures = createPageFixtures(
  "CUSTOM_CATEGORY_CREDS"
);
