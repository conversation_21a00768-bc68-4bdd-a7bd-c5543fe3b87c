import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
// import DatasheetV2EditViewPage from "../../../../test-objects/datasheet-v2-editView-objects";
import DatasheetTransformations from "../../../../test-objects/datasheetV2_transformations-objects";

const {
  advancedFiltersFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  // Extend test timeout
  testInfo.setTimeout(testInfo.timeout + 2400000);

  // Navigate to the datasheet page
  const page = adminPage.page;
  await page.goto("/datasheet", { waitUntil: "networkidle" });
});
test.describe(
  "Advanced Filters",
  { tag: ["@datasheet", "@regression", "@adminchamp-6"] },
  () => {
    test(
      "Validate operators and  functions in Datasheet transformations for all units-",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T24817, INTER-T24818,INTER-T24819,INTER-T24820,INTER-T24821,INTER-T24822,INTER-T24823,INTER-T24824,INTER-T24825,INTER-T24826,INTER-T24827,INTER-T24828,INTER-T24829,INTER-T24830,INTER-T24831,INTER-T24832,INTER-T24833,INTER-T24834,INTER-T24835,INTER-T24836,INTER-T24837,INTER-T24837,INTER-T24838,INTER-T24839,INTER-T24840",
          },
          {
            type: "Description",
            description:
              "Validate operators and  functions  in Datasheet transformations for all units-",
          },
          {
            type: "Precondition",
            description:
              "User should have an custom object with all the datatype columns",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to only the filtered data after datasheet generation",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const datasheetV2Page = new DatasheetV2Page(page);
        const datasheetTransformations = new DatasheetTransformations(page);

        // test cases for different operators and expected values
        const testCases = [
          {
            operator: "/",
            filterValue: "2",
            endValue: "5",
            expectedValues: ["10"], // Expected output after using "/" Operator
            columnName: "Deal ID",
          },
          {
            operator: "+",
            filterValue: "2",
            endValue: "5",
            expectedValues: ["3"], // Expected output after using "+" Operator
            columnName: "Deal ID",
          },
          {
            operator: "-",
            filterValue: "2",
            endValue: "5",
            expectedValues: ["7"], // Expected output after using "-" Operator
            columnName: "Deal ID",
          },

          {
            operator: "*",
            filterValue: "2",
            endValue: "4",
            expectedValues: ["2"], // Expected output after using "*" Operator
            columnName: "Deal ID",
          },
          {
            operator: "IN",
            filterValue: "[1,13]",
            expectedValues: ["1"], // Expected output after using "IN" Operator
            columnName: "Deal ID",
          },
          {
            operator: "NOTIN",
            filterValue: "[2,3,4,5,6,7,8,9,10,11,12]",
            expectedValues: ["1"], // Expected output after using "NOT IN" Operator
            columnName: "Deal ID",
          },
          {
            operator: "==",
            filterValue: "10",
            operator1: "AND",
            expectedValues: ["10"], // Expected output after using "== & AND" Operator
            columnName: "Deal ID",
            ColumnToFilter1: "Deal Name",
            endValue: "Miss",
          },
          {
            operator: "==",
            filterValue: "2",
            operator1: "OR",
            expectedValues: ["2", "10"], // Expected output after using "== & OR" Operator
            columnName: "Deal ID",
            ColumnToFilter1: "Deal Name",
            endValue: "Miss",
          },

          {
            operator: ">",
            filterValue: "10",
            expectedValues: ["11", "12"], // Expected output after using ">" Operator
            columnName: "Deal ID",
          },
          {
            operator: "<",
            filterValue: "3",
            expectedValues: ["1", "2"], // Expected output after using "<" Operator
            columnName: "Deal ID",
          },
          {
            operator: "==",
            filterValue: "1",
            expectedValues: ["1"], // Expected output after using "==" Operator
            columnName: "Deal ID",
          },
          {
            operator: "!=",
            filterValue: "10",
            expectedValues: [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "11",
              "12",
            ], // Expected output after using "!=" Operator
            columnName: "Deal ID",
          },
          {
            operator: ">=",
            filterValue: "10",
            expectedValues: ["10", "11", "12"], // Expected output after using ">=" Operator
            columnName: "Deal ID",
          },
          {
            operator: "<=",
            filterValue: "10",
            expectedValues: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], // Expected output after using "<=" Operator
            columnName: "Deal ID",
          },
          {
            operator: "LEN",
            filterValue: "13",
            expectedValues: ["7", "9", "10"], // Expected output after using "LEN" function
            columnName: "Deal ID",
            columnToFilter: "Payee email",
          },
          {
            operator: "Find",
            findText: "od",
            filterValue: "2",
            expectedValues: ["4"], // Expected output after using "Find" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
          {
            operator: "Round",
            findText: "1",
            filterValue: "199",
            expectedValues: ["1"], // Expected output after "Round" function
            columnName: "Deal ID",
            columnToFilter: "Earnings",
          },
          {
            operator: "RoundUp",
            findText: "1",
            filterValue: "199",
            expectedValues: ["1"], // Expected output after "RoundUp" function
            columnName: "Deal ID",
            columnToFilter: "Earnings",
          },
          {
            operator: "RoundDown",
            findText: "1",
            filterValue: "198.90",
            expectedValues: ["1"], // Expected output after "RoundDown" function
            columnName: "Deal ID",
            columnToFilter: "Earnings",
          },
          {
            operator: "Contains",
            findText: "od",
            expectedValues: ["7", "8", "11"], // Expected output after "Contains" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
          {
            operator: "NotContains",
            findText: "od",
            expectedValues: ["1", "2", "3", "4", "5", "6", "9", "10", "12"], // Expected output after "Not Contains" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
          {
            operator: "IsEmpty",
            expectedValues: ["12"], // Expected output after "Is Empty" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
          {
            operator: "IsNotEmpty",
            expectedValues: [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
            ], // Expected output after "Is not Empty" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
          {
            operator: "Lower",
            filterValue: "good",
            expectedValues: ["7", "8"], // Expected output after "Lower" function
            columnName: "Deal ID",
            columnToFilter: "Deal Name",
          },
        ];

        const updatedTestCases = testCases.map((testCase, index) => ({
          ...testCase,
          sheetName: `sheet${index + 1}`, // Assigns sheet1, sheet2, sheet3, ...
        }));
        console.log("Looping through the updated test cases :");
        console.log(updatedTestCases);

        await datasheetV2Page.goToDatasheet("DB1", "sheet");
        // Iterating through each test case
        for (const testCase of updatedTestCases) {
          console.log("Creating datasheet for", testCase);
          console.log(`Starting test case for operator: ${testCase.operator}`);

          // Create a new datasheet for each test case
          await datasheetV2Page.createDatasheet(
            "DB1",
            testCase.sheetName + testCase.operator,
            "object",
            "Datasheet Data",
            ""
          );

          await datasheetTransformations.addTransformation("Filter");

          if (testCase.operator === "LEN" || testCase.operator === "Lower") {
            const valueSuffix =
              testCase.operator === "LEN" ? "Integer" : "String";

            await datasheetTransformations.createFilterwithFunctions(
              testCase.operator,
              testCase.columnToFilter,
              "",
              "==",
              testCase.filterValue,
              testCase.filterValue + valueSuffix,
              ""
            );
          } else if (testCase.operator === "Find") {
            await datasheetTransformations.createFilterwithFunctions(
              "Find",
              testCase.columnToFilter,
              testCase.findText,
              "==",
              testCase.filterValue,
              testCase.filterValue + "Integer",
              ""
            );
          } else if (
            testCase.operator === "Contains" ||
            testCase.operator === "NotContains"
          ) {
            await datasheetTransformations.createFilterwithFunctions(
              testCase.operator,
              testCase.columnToFilter,
              testCase.findText,
              "",
              "",
              "",
              ""
            );
          } else if (
            testCase.operator === "IsEmpty" ||
            testCase.operator === "IsNotEmpty"
          ) {
            await datasheetTransformations.createFilterwithFunctions(
              testCase.operator,
              testCase.columnToFilter,
              "",
              "",
              "",
              ""
            );
          } else if (
            testCase.operator === "Round" ||
            testCase.operator === "RoundUp" ||
            testCase.operator === "RoundDown"
          ) {
            await datasheetTransformations.createFilterwithFunctions(
              testCase.operator,
              testCase.columnToFilter,
              testCase.findText,
              "",
              testCase.filterValue,
              testCase.filterValue + "Integer",
              testCase.columnName
            );
          } else if (testCase.operator === "DateAdd") {
            await datasheetTransformations.dateAdd(
              "DateAdd",
              testCase.columnToFilter,
              testCase.unit,
              testCase.findText,
              testCase.filterValue,
              testCase.filterValue + "Date"
            );
          } else if (
            testCase.operator === "/" ||
            testCase.operator === "*" ||
            testCase.operator === "+" ||
            testCase.operator === "-"
          ) {
            await datasheetTransformations.createFilter(
              testCase.columnName,
              testCase.operator,
              testCase.filterValue,
              testCase.filterValue + "Integer",
              testCase.endValue,
              testCase.endValue + "Integer"
            );
          } else if (
            testCase.operator === "IN" ||
            testCase.operator === "NOTIN"
          ) {
            await datasheetTransformations.createFilter(
              testCase.columnName,
              testCase.operator,
              testCase.filterValue,
              testCase.filterValue + "IntArray"
            );
          } else if (
            (testCase.operator === "==" && testCase.operator1 === "AND") ||
            (testCase.operator === "==" && testCase.operator1 === "OR")
          ) {
            await datasheetTransformations.createFilterAndORFunction(
              testCase.columnName,
              testCase.operator,
              testCase.filterValue,
              testCase.filterValue + "Integer",
              testCase.operator1,
              testCase.ColumnToFilter1,
              testCase.endValue,
              testCase.endValue + "String"
            );
          } else {
            await datasheetTransformations.createFilter(
              testCase.columnName,
              testCase.operator,
              testCase.filterValue,
              testCase.filterValue + "Integer"
            );
          }
          await datasheetTransformations.validateAndSave();
        }
        await datasheetV2Page.databookRefreshSheets("DB1", "Deal ID", false);

        for (const testCase of updatedTestCases) {
          await datasheetV2Page.goToDatasheet(
            "DB1",
            testCase.sheetName + testCase.operator
          );

          // Retrieve column values
          const valuesReceived = await datasheetV2Page.getColumnValues(
            testCase.columnName,
            "deal_id"
          );
          // Sort both received and expected values
          const sortedReceived = valuesReceived.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          const sortedExpected = testCase.expectedValues.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          // Debug logs
          console.log(
            `Sheet: ${testCase.sheetName} with Operator: ${testCase.operator}`
          );
          console.log(`Received (sorted):`, sortedReceived);
          console.log(`Expected (sorted):`, sortedExpected);
          // Validate
          expect(sortedReceived).toEqual(sortedExpected);
        }
        await datasheetV2Page.deleteDatabook("DB1", false);
        await datasheetV2Page.dialogPrompt(
          "span",
          "Databook deleted successfully."
        );
      }
    );

    test(
      "Validate DateAdd function in Datasheet transformations for all units-Constant",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T24846,INTER-T24847,INTER-24848,INTER-24849,INTER-24850,INTER-24856",
          },
          {
            type: "Description",
            description:
              "Validate DateAdd function in Datasheet transformations for all units-Constant",
          },
          {
            type: "Precondition",
            description:
              "User should have an custom object with 2 date columns",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to only the filtered data after datasheet generation",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const datasheetV2Page = new DatasheetV2Page(page);
        const datasheetTransformations = new DatasheetTransformations(page);

        // Define test cases for different DateAdd scenarios
        const testCases = [
          {
            operator: "DateAdd",
            unit: "",
            findText: "2", // Dates to be added
            filterValue: "12/03/2024",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },
          {
            operator: "DateAdd",
            unit: "MONTH",
            findText: "2",
            operator1: "==", // Months to be added
            filterValue: "02/01/2025",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },
          {
            operator: "DateAdd",
            unit: "QUARTER",
            findText: "1", // quarters to be added
            filterValue: "03/01/2025",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },

          {
            operator: "DateAdd",
            unit: "YEAR",
            findText: "1", // year to be added
            filterValue: "12/01/2025",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },
          {
            operator: "DateAdd",
            unit: "HALF-YEAR",
            findText: "1",
            filterValue: "05/01/2025",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },
          {
            operator: "DateAdd",
            unit: "HH:mm",
            findText: "02",
            filterValue: "12/01/2024",
            expectedValues: ["1", "10"], // Expected output after "DateAdd" transformation
            columnName: "Deal ID",
            columnToFilter: "Close Date",
          },
        ];

        const dateAddTestCases = testCases.map((testCase, index) => ({
          ...testCase,
          sheetName: `sheet${index + 1}`,
        }));
        console.log("Looping through the updated test cases :");
        console.log(dateAddTestCases);

        await datasheetV2Page.goToDatasheet("DB2", "sheet");
        // Iterate through each DateAdd test case
        for (const testCase of dateAddTestCases) {
          console.log("Creating datasheet for", testCase);
          console.log(
            `Starting DateAdd test case for unit /constant: ${testCase.unit}`
          );

          // Create a new datasheet for each test case
          await datasheetV2Page.createDatasheet(
            "DB2",
            testCase.sheetName + testCase.unit,
            "object",
            "Datasheet Data",
            ""
          );

          await datasheetTransformations.addTransformation("Filter");

          // Apply DateAdd transformation

          await datasheetTransformations.dateAdd(
            "DateAdd",
            testCase.columnToFilter,
            testCase.unit,
            testCase.findText,
            testCase.filterValue,
            testCase.filterValue + "Date"
          );

          await datasheetTransformations.validateAndSave();
        }
        await datasheetV2Page.databookRefreshSheets("DB2", "Deal ID", false);

        for (const testCase of dateAddTestCases) {
          await datasheetV2Page.goToDatasheet(
            "DB2",
            testCase.sheetName + testCase.unit
          );
          // Retrieve column values
          const valuesReceived = await datasheetV2Page.getColumnValues(
            testCase.columnName,
            "deal_id"
          );

          // Sort both received and expected values
          const sortedReceived = valuesReceived.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          const sortedExpected = testCase.expectedValues.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );

          // Debug logs
          console.log("Validating results");
          console.log(`Unit: ${testCase.unit}`);
          console.log(`Received (sorted):`, sortedReceived);
          console.log(`Expected (sorted):`, sortedExpected);

          // Validate
          expect(sortedReceived).toEqual(sortedExpected);
        }
        await datasheetV2Page.deleteDatabook("DB2", false);
        await datasheetV2Page.dialogPrompt(
          "span",
          "Databook deleted successfully."
        );
      }
    );

    test(
      "Validate DateAdd function in Datasheet transformations for all units-Datasheet Field",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T24851, INTER-T24852,INTER-T24853,INTER-T24854,INTER-T24855",
          },
          {
            type: "Description",
            description:
              "Validate DateAdd function in Datasheet transformations for all units-Datasheet Field",
          },
          {
            type: "Precondition",
            description:
              "User should have an custom object with 2 date columns",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to only the filtered data after datasheet generation",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const datasheetV2Page = new DatasheetV2Page(page);
        const datasheetTransformations = new DatasheetTransformations(page);

        // Define test cases for different DateAdd scenarios
        const testCases = [
          {
            unit: "",
            datasheetField: "Deal ID",
            filterValue: "12/02/2024",
            expectedValues: ["1"], // Expected output after "DateAdd" transformation
            columnToFilter: "Close Date",
            columnName: "Deal ID",
          },
          {
            unit: "MONTH",
            datasheetField: "Deal ID",
            filterValue: "01/01/2025",
            expectedValues: ["1"], // Expected output after "DateAdd" transformation
            columnToFilter: "Close Date",
            columnName: "Deal ID",
          },
          {
            unit: "QUARTER",
            datasheetField: "Deal ID",
            filterValue: "03/01/2025",
            expectedValues: ["1"], // Expected output after "DateAdd" transformation
            columnToFilter: "Close Date",
            columnName: "Deal ID",
          },
          {
            unit: "YEAR",
            datasheetField: "Deal ID",
            filterValue: "12/01/2025",
            expectedValues: ["1"], // Expected output after "DateAdd" transformation
            columnToFilter: "Close Date",
            columnName: "Deal ID",
          },
          {
            unit: "HALF-YEAR",
            datasheetField: "Deal ID",
            filterValue: "06/01/2025",
            expectedValues: ["1"], // Expected output after "DateAdd" transformation
            columnToFilter: "Close Date",
            columnName: "Deal ID",
          },
        ];

        const dateAddTestCases = testCases.map((testCase, index) => ({
          ...testCase,
          sheetName: `sheet${index + 1}`, // Assigns sheet30, sheet31,...
        }));
        console.log("Looping through the updated test cases :");
        console.log(dateAddTestCases);

        await datasheetV2Page.goToDatasheet("DB3", "sheet");
        // Iterate through each DateAdd test case
        for (const testCase of dateAddTestCases) {
          console.log("Creating datasheet for", testCase);
          console.log(
            `Starting DateAdd test case for unit/Datasheet Field: ${testCase.unit}`
          );

          // Create a new datasheet for each test case
          await datasheetV2Page.createDatasheet(
            "DB3",
            testCase.sheetName + testCase.unit,
            "object",
            "Datasheet Data",
            ""
          );

          await datasheetTransformations.addTransformation("Filter");

          // Apply DateAdd transformation
          await datasheetTransformations.dateAddDatasheetField(
            "DateAdd",
            testCase.columnToFilter,
            testCase.unit,
            testCase.datasheetField,
            "==",
            testCase.filterValue,
            testCase.filterValue + "Date"
          );

          await datasheetTransformations.validateAndSave();
        }
        await datasheetV2Page.databookRefreshSheets("DB3", "Deal ID", false);

        for (const testCase of dateAddTestCases) {
          await datasheetV2Page.goToDatasheet(
            "DB3",
            testCase.sheetName + testCase.unit
          );
          // Retrieve column values
          const valuesReceived = await datasheetV2Page.getColumnValues(
            testCase.columnName,
            "deal_id"
          );
          // Sort both received and expected values
          const sortedReceived = valuesReceived.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          const sortedExpected = testCase.expectedValues.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          // Debug logs
          console.log("Validating results");
          console.log(`Unit: ${testCase.unit}`);
          console.log(`Received (sorted):`, sortedReceived);
          console.log(`Expected (sorted):`, sortedExpected);
          // Validate
          expect(sortedReceived).toEqual(sortedExpected);
        }
        await datasheetV2Page.deleteDatabook("DB3", false);
        await datasheetV2Page.dialogPrompt(
          "span",
          "Databook deleted successfully."
        );
      }
    );

    test(
      "Validate Datediff function in Datasheet transformations for all units",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T24841, INTER-T24842,INTER-T24843,INTER-T24844,INTER-T24845,,INTER-T25043,INTER-T25045",
          },
          {
            type: "Description",
            description:
              "Validate Datediff function in Datasheet transformations for all units",
          },
          {
            type: "Precondition",
            description:
              "User should have an custom object with 2 date columns",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to only the filtered data after datasheet generation",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const datasheetV2Page = new DatasheetV2Page(page);
        const datasheetTransformations = new DatasheetTransformations(page);

        // Define test cases for different DateDiff scenarios
        const testCases = [
          {
            unit: "DAYS",
            period: "",
            expectedValues: ["1"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            filterValue: "31",
            columnName: "Deal ID",
          },
          {
            unit: "MONTH",
            period: "",
            expectedValues: ["1"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            filterValue: "1",
            columnName: "Deal ID",
          },
          {
            unit: "QUARTER",
            period: "CALENDAR",
            filterValue: "1",
            expectedValues: ["1", "9"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
          {
            unit: "QUARTER",
            period: "FISCAL",
            filterValue: "1",
            expectedValues: ["1", "9"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
          {
            unit: "YEAR",
            period: "FISCAL",
            filterValue: "1",
            expectedValues: ["1", "2", "3", "5", "6", "7", "10"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
          {
            unit: "YEAR",
            period: "CALENDAR",
            filterValue: "1",
            expectedValues: ["1", "2", "3", "5", "6", "7", "10"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
          {
            unit: "HALF-YEAR",
            period: "CALENDAR",
            filterValue: "1",
            expectedValues: ["1", "6"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
          {
            unit: "HALF-YEAR",
            period: "FISCAL",
            filterValue: "1",
            expectedValues: ["1", "6"], // Expected output after "DateDiff" transformation
            columnToFilter: "Close Date",
            secondvariable: "Invoice Date",
            columnName: "Deal ID",
          },
        ];
        const dateAddTestCases = testCases.map((testCase, index) => ({
          ...testCase,
          sheetName: `sheet${index + 1}`,
        }));
        console.log("Looping through the updated test cases :");
        console.log(dateAddTestCases);

        await datasheetV2Page.goToDatasheet("DB4", "sheet");
        // Iterate through each DateDiff test case
        for (const testCase of dateAddTestCases) {
          console.log("Creating datasheet for", testCase);
          console.log(
            `Starting DateDiff test case for unit: ${testCase.unit}${testCase.period}`
          );

          // Create a new datasheet for each test case
          await datasheetV2Page.createDatasheet(
            "DB4",
            testCase.sheetName + testCase.unit + testCase.period,
            "object",
            "Datasheet Data",
            ""
          );

          await datasheetTransformations.addTransformation("Filter");

          // Apply DateDiff transformation
          await datasheetTransformations.dateDiff(
            "DATEDIFF",
            testCase.columnToFilter,
            testCase.secondvariable,
            testCase.unit,
            testCase.period,
            testCase.filterValue,
            testCase.filterValue + "Integer"
          );

          await datasheetTransformations.validateAndSave();
        }
        await datasheetV2Page.databookRefreshSheets("DB4", "Deal ID", false);

        for (const testCase of dateAddTestCases) {
          await datasheetV2Page.goToDatasheet(
            "DB4",
            testCase.sheetName + testCase.unit + testCase.period
          );
          // Retrieve column values
          const valuesReceived = await datasheetV2Page.getColumnValues(
            testCase.columnName,
            "deal_id"
          );

          // Sort both received and expected values
          const sortedReceived = valuesReceived.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          const sortedExpected = testCase.expectedValues.sort(
            (a, b) => parseInt(a) - parseInt(b)
          );

          // Debug logs
          console.log("Validating results");
          console.log(`Unit: ${testCase.unit}`);
          console.log(`Received (sorted):`, sortedReceived);
          console.log(`Expected (sorted):`, sortedExpected);

          // Validate
          expect(sortedReceived).toEqual(sortedExpected);
        }
        await datasheetV2Page.deleteDatabook("DB4", false);
        await datasheetV2Page.dialogPrompt(
          "span",
          "Databook deleted successfully."
        );
      }
    );
  }
);
