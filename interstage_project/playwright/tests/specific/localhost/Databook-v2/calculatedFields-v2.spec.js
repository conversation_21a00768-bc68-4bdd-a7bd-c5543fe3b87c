import DatasheetV2EditViewPage from "../../../../test-objects/datasheet-v2-editView-objects";
import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import CanvasCommission from "../../../../test-objects/canvas-objects";

const {
  datasheetV2Fixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 2400000);
  const page = adminPage.page;
  await page.goto("/datasheet", { waitUntil: "networkidle" });
});

test.describe(
  "Calculated Fields Testcases",
  { tag: ["@datasheet", "@regression", "@adminchamp-4"] },
  () => {
    test(
      "Tests related to DATEDIFF",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T1981, INTER-T1984, INTER-T1985, INTER-T1986, INTER-T1987, INTER-T1988, INTER-T1989, INTER-T1990, INTER-T1991",
          },
          {
            type: "Description",
            description: "Validate the testcases related to DATEDIFF function",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-DATEDIFF");

        await dsV2Page.clickEditBtn();
        await dsV2EditPage.dateDiffFn(
          "DATEDIFF_DAYS",
          "StartDate",
          "EndDate",
          "DAYS"
        );
        await dsV2EditPage.dateDiffFn(
          "DATEDIFF_MONTH",
          "StartDate",
          "EndDate",
          "MONTH"
        );
        await dsV2EditPage.dateDiffFn(
          "DATEDIFF_QUARTER",
          "StartDate",
          "EndDate",
          "QUARTER"
        );
        await dsV2EditPage.dateDiffFn(
          "DATEDIFF_HALF-YEAR",
          "StartDate",
          "EndDate",
          "HALF-YEAR"
        );
        await dsV2EditPage.dateDiffFn(
          "DATEDIFF_YEAR",
          "StartDate",
          "EndDate",
          "YEAR"
        );
        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log(
          "----- Verify popup message on unchecking dependant fields -----"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.unCheckColumn("StartDate", false);
        await expect(
          page.getByText(
            "Variable/Variables StartDate used in calculated field are either unselected or invalid"
          )
        ).toBeVisible();
        await dsV2EditPage.unCheckColumn("EndDate", false);
        await expect(
          page.getByText(
            "Variable/Variables EndDate used in calculated field are either unselected or invalid"
          )
        ).toBeVisible();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log("Verifying column values of: DATEDIFF_DAYS");
        let valuesReceived = await dsV2Page.getSpanColumnValues(
          "DATEDIFF_DAYS",
          "datediffdays"
        );
        let valuesExpected = [
          "359",
          "169",
          "96",
          "559",
          "6",
          "372",
          "190",
          "98",
          "0",
          "372",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: DATEDIFF_MONTH");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "DATEDIFF_MONTH",
          "datediffmonth"
        );
        valuesExpected = ["11", "5", "3", "18", "0", "12", "6", "3", "0", "12"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: DATEDIFF_QUARTER");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "DATEDIFF_QUARTER",
          "datediffquarter"
        );
        valuesExpected = ["4", "2", "1", "6", "0", "4", "2", "1", "0", "4"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: DATEDIFF_HALF-YEAR");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "DATEDIFF_HALF-YEAR",
          "datediffhalfyear"
        );
        valuesExpected = ["2", "1", "1", "3", "0", "2", "1", "1", "0", "2"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: DATEDIFF_YEAR");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "DATEDIFF_YEAR",
          "datediffyear"
        );
        valuesExpected = ["1", "0", "1", "1", "0", "1", "1", "0", "0", "1"];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );

    test(
      "Tests related to StartDate and LastDate",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T1992, INTER-T1993, INTER-T1994, INTER-T1995, INTER-T1996, INTER-T1997, INTER-T1998, INTER-T1999, INTER-T2000, INTER-T2001, INTER-T2002, INTER-T2003, INTER-T2015",
          },
          {
            type: "Description",
            description:
              "Validate the testcases related to StartDate and LastDate functions",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet(
          "db-calculatedFields",
          "cf-StartDate-LastDate"
        );

        console.log("Creating StartDate and LastDate function for all periods");
        await dsV2Page.clickEditBtn();

        await dsV2EditPage.dateFn(
          "StartDate_MONTH",
          "StartDate",
          "StartDate",
          "MONTH"
        );
        await dsV2EditPage.dateFn(
          "StartDate_QUARTER",
          "StartDate",
          "StartDate",
          "QUARTER"
        );
        await dsV2EditPage.dateFn(
          "StartDate_HALF-YEAR",
          "StartDate",
          "StartDate",
          "HALF-YEAR"
        );
        await dsV2EditPage.dateFn(
          "StartDate_YEAR",
          "StartDate",
          "StartDate",
          "YEAR"
        );

        await dsV2EditPage.dateFn(
          "LastDate_MONTH",
          "LastDate",
          "EndDate",
          "MONTH"
        );
        await dsV2EditPage.dateFn(
          "LastDate_QUARTER",
          "LastDate",
          "EndDate",
          "QUARTER"
        );
        await dsV2EditPage.dateFn(
          "LastDate_HALF-YEAR",
          "LastDate",
          "EndDate",
          "HALF-YEAR"
        );
        await dsV2EditPage.dateFn(
          "LastDate_YEAR",
          "LastDate",
          "EndDate",
          "YEAR"
        );

        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log("Verifying column values of: StartDate_MONTH");
        let valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_MONTH",
          "start_date_month"
        );
        let valuesExpected = [
          "Jan 01, 2023",
          "Jun 01, 2023",
          "Nov 01, 2022",
          "Mar 01, 2021",
          "Aug 01, 2024",
          "Feb 01, 2022",
          "Oct 01, 2023",
          "May 01, 2022",
          "",
          "Sep 01, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: StartDate_QUARTER");
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_QUARTER",
          "start_date_quarter"
        );
        valuesExpected = [
          "Jan 01, 2023",
          "Apr 01, 2023",
          "Oct 01, 2022",
          "Jan 01, 2021",
          "Jul 01, 2024",
          "Jan 01, 2022",
          "Oct 01, 2023",
          "Apr 01, 2022",
          "",
          "Jul 01, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: StartDate_HALF-YEAR");
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_HALF-YEAR",
          "start_date_halfyear"
        );
        valuesExpected = [
          "Jan 01, 2023",
          "Jan 01, 2023",
          "Jul 01, 2022",
          "Jan 01, 2021",
          "Jul 01, 2024",
          "Jan 01, 2022",
          "Jul 01, 2023",
          "Jan 01, 2022",
          "",
          "Jul 01, 2024",
        ];

        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: StartDate_YEAR");
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_YEAR",
          "start_date_year"
        );
        valuesExpected = [
          "Jan 01, 2023",
          "Jan 01, 2023",
          "Jan 01, 2022",
          "Jan 01, 2021",
          "Jan 01, 2024",
          "Jan 01, 2022",
          "Jan 01, 2023",
          "Jan 01, 2022",
          "",
          "Jan 01, 2024",
        ];

        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: LastDate_MONTH");
        valuesReceived = await dsV2Page.getColumnValues(
          "LastDate_MONTH",
          "last_date_month"
        );
        valuesExpected = [
          "Jan 31, 2024",
          "Dec 31, 2023",
          "Feb 28, 2023",
          "Sep 30, 2022",
          "Sep 30, 2024",
          "Feb 28, 2023",
          "Apr 30, 2024",
          "Aug 31, 2022",
          "Jan 31, 2022",
          "Sep 30, 2025",
        ];

        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: LastDate_QUARTER");
        valuesReceived = await dsV2Page.getColumnValues(
          "LastDate_QUARTER",
          "last_date_quarter"
        );
        valuesExpected = [
          "Mar 31, 2024",
          "Dec 31, 2023",
          "Mar 31, 2023",
          "Sep 30, 2022",
          "Sep 30, 2024",
          "Mar 31, 2023",
          "Jun 30, 2024",
          "Sep 30, 2022",
          "Mar 31, 2022",
          "Sep 30, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: LastDate_HALF-YEAR");
        valuesReceived = await dsV2Page.getColumnValues(
          "LastDate_HALF-YEAR",
          "last_date_halfyear"
        );
        valuesExpected = [
          "Jun 30, 2024",
          "Dec 31, 2023",
          "Jun 30, 2023",
          "Dec 31, 2022",
          "Dec 31, 2024",
          "Jun 30, 2023",
          "Jun 30, 2024",
          "Dec 31, 2022",
          "Jun 30, 2022",
          "Dec 31, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying column values of: LastDate_YEAR");
        valuesReceived = await dsV2Page.getColumnValues(
          "LastDate_YEAR",
          "last_date_year"
        );
        valuesExpected = [
          "Dec 31, 2024",
          "Dec 31, 2023",
          "Dec 31, 2023",
          "Dec 31, 2022",
          "Dec 31, 2024",
          "Dec 31, 2023",
          "Dec 31, 2024",
          "Dec 31, 2022",
          "Dec 31, 2022",
          "Dec 31, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verify when this datasheet is cloned, All calculated fields are present in cloned datasheet"
        );
        await dsV2Page.createDatasheet(
          "db-calculatedFields",
          "cloned-cf-StartDate-LastDate",
          "datasheet",
          "db-calculatedFields",
          "cf-StartDate-LastDate"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        const checkColumns = [
          "StartDate_MONTH",
          "StartDate_QUARTER",
          "StartDate_HALF-YEAR",
          "StartDate_YEAR",
          "LastDate_MONTH",
          "LastDate_QUARTER",
          "LastDate_HALF-YEAR",
          "LastDate_YEAR",
        ];
        for (const column of checkColumns) {
          await dsV2Page.isColumnPresent(column);
        }
      }
    );

    test(
      "Tests related to DateAdd",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T2004, INTER-T2005, INTER-T2006, INTER-T2007, INTER-T2008, INTER-T2009, INTER-T2010, INTER-T2011, INTER-T2012, INTER-T2013, INTER-T2014, INTER-T2019, INTER-T2045, INTER-T2046, INTER-T2047, INTER-T2048, INTER-T2049, INTER-T2050, INTER-T2051, INTER-T2052, INTER-T2053, INTER-T2054, INTER-T2055, INTER-T2056, INTER-T2057, INTER-T2058, INTER-T2059, INTER-T2060, INTER-T2061, INTER-T2062, INTER-T2063, INTER-T2064, INTER-T2065, INTER-T2066, INTER-T2067",
          },
          {
            type: "Description",
            description: "Validate the testcases related to DateAdd function",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-DateAdd");

        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        let formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "DateAdd"
        );
        await formulaLocator.click();

        let columnUnits = ["StartDate", "EndDate"];
        let dropDownLocator = await dsV2EditPage.dropDownLocator(
          "Select Date Column"
        );
        await dropDownLocator.waitFor({ state: "visible" });
        expect(await dropDownLocator.isVisible()).toBe(true);
        await dsV2EditPage.clickDropdown("Select Date Column");
        for (const unit of columnUnits) {
          console.log("verify DateAdd units in dropdown - ", unit);
          const isUnitPresent = await dsV2EditPage.verifyUnit(
            "Select Date Column",
            unit
          );
          expect(isUnitPresent).toBe(true);
        }

        const periodUnits = [
          "Days",
          "MONTH",
          "QUARTER",
          "HALF-YEAR",
          "YEAR",
          "HH:mm",
        ];
        dropDownLocator = await dsV2EditPage.dropDownLocator("Days");
        await dropDownLocator.waitFor({ state: "visible" });
        expect(await dropDownLocator.isVisible()).toBe(true);
        await dsV2EditPage.clickDropdown("Days");
        for (const unit of periodUnits) {
          console.log("verify DateAdd units in dropdown - ", unit);
          const isUnitPresent = await dsV2EditPage.verifyUnit("Days", unit);
          expect(isUnitPresent).toBe(true);
        }

        const typeUnits = ["Constant", "Datasheet Field"];
        dropDownLocator = await dsV2EditPage.dropDownLocator("Constant");
        await dropDownLocator.waitFor({ state: "visible" });
        expect(await dropDownLocator.isVisible()).toBe(true);
        await dsV2EditPage.clickDropdown("Constant");
        for (const unit of typeUnits) {
          console.log("verify DateAdd type units in dropdown - ", unit);
          const isUnitPresent = await dsV2EditPage.verifyUnit("Constant", unit);
          expect(isUnitPresent).toBe(true);
        }
        await dsV2EditPage.clickDropdown("Constant");

        const datasheetFields = [
          "percentage",
          "Amount",
          "deliveredIn",
          "ID",
          "CF_INT",
          "CF_PERC_DECIMAL",
          "CF_DECIMAL",
        ];
        await dsV2EditPage.selectDropDown("Constant", "Datasheet Field");
        dropDownLocator = await dsV2EditPage.dropDownLocator(
          "Select Numeric Column"
        );
        await dropDownLocator.waitFor({ state: "visible" });
        expect(await dropDownLocator.isVisible()).toBe(true);
        await dsV2EditPage.clickDropdown("Select Numeric Column");
        for (const unit of datasheetFields) {
          console.log("verify DateAdd datasheetFields in dropdown - ", unit);
          const isUnitPresent = await dsV2EditPage.verifyUnit(
            "Select Numeric Column",
            unit
          );
          expect(isUnitPresent).toBe(true);
        }

        await dsV2EditPage.selectDropDown("Days", "HH:mm");

        dropDownLocator = await dsV2EditPage.dropDownLocator("Constant");
        expect(await dropDownLocator.isVisible()).toBe(false);

        dropDownLocator = await dsV2EditPage.dropDownLocator("Datasheet Field");
        expect(await dropDownLocator.isVisible()).toBe(false);

        dropDownLocator = await dsV2EditPage.dropDownLocator("Enter Value");
        expect(await dropDownLocator.isVisible()).toBe(false);

        dropDownLocator = await dsV2EditPage.dropDownLocator(
          "Select Numeric Column"
        );
        expect(await dropDownLocator.isVisible()).toBe(false);

        expect(await page.getByPlaceholder("Select time").isVisible()).toBe(
          true
        );

        await dsV2EditPage.closeFormulaPopup();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "----- Creating DateAdd function with Constant inputs on All periods -----"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.dateAddFn(
          "StartDate_Days_Constant_10",
          "StartDate",
          "Days",
          "Constant",
          "10"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_MONTH_Constant_-3",
          "StartDate",
          "MONTH",
          "Constant",
          "-3"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_QUARTER_Constant_4",
          "StartDate",
          "QUARTER",
          "Constant",
          "4"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_HALF-YEAR_Constant_-1",
          "EndDate",
          "HALF-YEAR",
          "Constant",
          "-1"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_YEAR_Constant_2",
          "EndDate",
          "YEAR",
          "Constant",
          "2"
        );

        console.log(
          "----- Creating DateAdd function with Datasheet Field inputs on All periods -----"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_Days_Datasheet Field_ID",
          "StartDate",
          "Days",
          "Datasheet Field",
          "ID"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_MONTH_Datasheet Field_deliveredIn",
          "StartDate",
          "MONTH",
          "Datasheet Field",
          "deliveredIn"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_QUARTER_Datasheet Field_ID",
          "StartDate",
          "QUARTER",
          "Datasheet Field",
          "ID"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_HALF-YEAR_Datasheet Field_ID",
          "EndDate",
          "HALF-YEAR",
          "Datasheet Field",
          "ID"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_YEAR_Datasheet Field_percentage",
          "EndDate",
          "YEAR",
          "Datasheet Field",
          "percentage"
        );

        console.log(
          "----- Creating DateAdd function with Calculated Datasheet Field inputs on All periods -----"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_Days_Datasheet Field_CF_INT",
          "StartDate",
          "Days",
          "Datasheet Field",
          "CF_INT"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_MONTH_Datasheet Field_CF_INT",
          "StartDate",
          "MONTH",
          "Datasheet Field",
          "CF_INT"
        );
        await dsV2EditPage.dateAddFn(
          "StartDate_QUARTER_Datasheet Field_CF_INT",
          "StartDate",
          "QUARTER",
          "Datasheet Field",
          "CF_INT"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_HALF-YEAR_Datasheet Field_CF_INT",
          "EndDate",
          "HALF-YEAR",
          "Datasheet Field",
          "CF_INT"
        );
        await dsV2EditPage.dateAddFn(
          "EndDate_YEAR_Datasheet Field_CF_INT",
          "EndDate",
          "YEAR",
          "Datasheet Field",
          "CF_INT"
        );

        // console.log("Creating DateAdd function with decimal inputs");
        // await dsV2EditPage.dateAddFn("StartDate", "Days", "Constant", "10.23");
        // await dsV2EditPage.dateAddFn(
        //   "EndDate",
        //   "YEAR",
        //   "Datasheet Field",
        //   "CF_PERC_DECIMAL"
        // );

        console.log("Creating DateAdd function with HH:mm input");
        await dsV2EditPage.dateAddFn(
          "EndDate_HH:mm_Column_23:00",
          "EndDate",
          "HH:mm",
          "Column",
          "23:00"
        );

        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log("Verifying error message on unchecking dependant columns");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.unCheckColumn("StartDate", false);
        await expect(
          page.getByText(
            "Variable/Variables StartDate used in calculated field are either unselected or invalid"
          )
        ).toBeVisible();
        await dsV2EditPage.unCheckColumn("deliveredIn", false);
        await expect(
          page.getByText(
            "Variable/Variables deliveredIn used in calculated field are either unselected or invalid"
          )
        ).toBeVisible();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "Verify newly created date columns are visible in date dropdown of DateAdd function"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "DateAdd"
        );
        await formulaLocator.click();

        columnUnits = [
          "StartDate",
          "EndDate",
          "StartDate_Days_Constant_10",
          "StartDate_MONTH_Constant_-3",
          "StartDate_QUARTER_Constant_4",
          "EndDate_HALF-YEAR_Constant_-1",
          "EndDate_YEAR_Constant_2",
          "StartDate_Days_Datasheet Field_ID",
          "StartDate_MONTH_Datasheet Field_deliveredIn",
          "StartDate_QUARTER_Datasheet Field_ID",
          "EndDate_HALF-YEAR_Datasheet Field_ID",
          "EndDate_YEAR_Datasheet Field_percentage",
          "StartDate_Days_Datasheet Field_CF_INT",
          "StartDate_MONTH_Datasheet Field_CF_INT",
          "StartDate_QUARTER_Datasheet Field_CF_INT",
          "EndDate_HALF-YEAR_Datasheet Field_CF_INT",
          "EndDate_YEAR_Datasheet Field_CF_INT",
          "EndDate_HH:mm_Column_23:00",
        ];
        dropDownLocator = await dsV2EditPage.dropDownLocator(
          "Select Date Column"
        );
        await dropDownLocator.waitFor({ state: "visible" });
        await dsV2EditPage.clickDropdown("Select Date Column");
        for (const unit of columnUnits) {
          console.log("verify DateAdd units in dropdown - ", unit);
          const isUnitPresent = await dsV2EditPage.verifyUnit(
            "Select Date Column",
            unit
          );
          expect(isUnitPresent).toBe(true);
        }
        await dsV2EditPage.closeFormulaPopup();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "Verify calculated field name(ex:StartDate_Days_Constant_10) is not visible in dropdown options when editing it."
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.hoverFormulaColumn("StartDate_Days_Constant_10");
        await dsV2EditPage.formulaMoreActions("StartDate_Days_Constant_10");
        await dsV2EditPage.clickFormulaMoreActions("Edit");
        await dsV2EditPage.clickEditFormulaExpression("DateAdd");
        await dsV2EditPage.clickEditFormulaColumn("StartDate");
        await dsV2EditPage.clearDropdown("StartDate");
        await dsV2EditPage.clickDropdown("Select Date Column");
        const isUnitPresent = await dsV2EditPage.verifyUnit(
          "Select Date Column",
          "StartDate_Days_Constant_10"
        );
        expect(isUnitPresent).toBe(false);
        await dsV2EditPage.closeFormulaPopup();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "Verify error message is displayed on deleting dependant calculated field."
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.hoverFormulaColumn("CF_INT");
        await dsV2EditPage.formulaMoreActions("CF_INT");
        await dsV2EditPage.clickFormulaMoreActions("Delete");
        let isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Are you sure to delete this field?"
        );
        expect(isPopupVisible).toBe(true);
        await dsV2EditPage.clickDeleteFn();
        isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Please select CF_INT since it is used in the formula column"
        );
        expect(isPopupVisible).toBe(true);
        await dsV2Page.closeEditsheetPage();

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log(
          "---------- Verifying Date columns generated by Constant inputs -----------"
        );
        console.log(
          "Verifying values of date column: StartDate_Days_Constant_10"
        );
        let valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_Days_Constant_10",
          "start_date_days_constant_10"
        );
        let valuesExpected = [
          "Jan 25, 2023",
          "Jun 25, 2023",
          "Nov 30, 2022",
          "Mar 20, 2021",
          "Sep 09, 2024",
          "Feb 28, 2022",
          "Oct 18, 2023",
          "Jun 01, 2022",
          "",
          "Sep 22, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_MONTH_Constant_-3"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_MONTH_Constant_-3",
          "start_date_month_constant_3"
        );
        valuesExpected = [
          "Oct 15, 2022",
          "Mar 15, 2023",
          "Aug 20, 2022",
          "Dec 10, 2020",
          "May 30, 2024",
          "Nov 18, 2021",
          "Jul 08, 2023",
          "Feb 22, 2022",
          "",
          "Jun 12, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_QUARTER_Constant_4"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_QUARTER_Constant_4",
          "start_date_quarter_constant_4"
        );
        valuesExpected = [
          "Jan 15, 2024",
          "Jun 15, 2024",
          "Nov 20, 2023",
          "Mar 10, 2022",
          "Aug 30, 2025",
          "Feb 18, 2023",
          "Oct 08, 2024",
          "May 22, 2023",
          "",
          "Sep 12, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: EndDate_HALF-YEAR_Constant_-1"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_HALF-YEAR_Constant_-1",
          "end_date_halfyear_constant_1"
        );
        valuesExpected = [
          "Jul 10, 2023",
          "Jun 01, 2023",
          "Aug 25, 2022",
          "Mar 20, 2022",
          "Mar 05, 2024",
          "Aug 25, 2022",
          "Oct 15, 2023",
          "Feb 28, 2022",
          "Jul 21, 2021",
          "Mar 19, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of date column: EndDate_YEAR_Constant_2");
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_YEAR_Constant_2",
          "end_date_year_constant_2"
        );
        valuesExpected = [
          "Jan 10, 2026",
          "Dec 01, 2025",
          "Feb 25, 2025",
          "Sep 20, 2024",
          "Sep 05, 2026",
          "Feb 25, 2025",
          "Apr 15, 2026",
          "Aug 29, 2024",
          "Jan 21, 2024",
          "Sep 19, 2027",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying Date columns generated by Datasheet Field inputs -----------"
        );
        console.log(
          "Verifying values of date column: StartDate_Days_Datasheet Field_ID"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_Days_Datasheet Field_ID",
          "start_date_days_datasheet_field_id"
        );
        valuesExpected = [
          "Jan 16, 2023",
          "Jun 17, 2023",
          "Nov 23, 2022",
          "Mar 14, 2021",
          "Sep 04, 2024",
          "Feb 24, 2022",
          "Oct 15, 2023",
          "May 30, 2022",
          "",
          "Sep 22, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_MONTH_Datasheet Field_deliveredIn"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_MONTH_Datasheet Field_deliveredIn",
          "start_date_month_datasheet_fielddelivered_in"
        );
        valuesExpected = [
          "Apr 15, 2024",
          "Oct 15, 2025",
          "Jun 20, 2023",
          "Mar 10, 2021",
          "Jan 30, 2025",
          "Aug 18, 2024",
          "Aug 08, 2025",
          "Dec 22, 2023",
          "",
          "Oct 12, 2024",
        ];

        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_QUARTER_Datasheet Field_ID"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_QUARTER_Datasheet Field_ID",
          "start_date_quarter_datasheet_field_id"
        );
        valuesExpected = [
          "Apr 15, 2023",
          "Dec 15, 2023",
          "Aug 20, 2023",
          "Mar 10, 2022",
          "Nov 30, 2025",
          "Aug 18, 2023",
          "Jul 08, 2025",
          "May 22, 2024",
          "",
          "Mar 12, 2027",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: EndDate_HALF-YEAR_Datasheet Field_ID"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_HALF-YEAR_Datasheet Field_ID",
          "end_date_halfyear_datasheet_field_id"
        );
        valuesExpected = [
          "Jul 10, 2024",
          "Dec 01, 2024",
          "Aug 25, 2024",
          "Sep 20, 2024",
          "Mar 05, 2027",
          "Feb 25, 2026",
          "Oct 15, 2027",
          "Aug 29, 2026",
          "Jul 21, 2026",
          "Sep 19, 2030",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: EndDate_YEAR_Datasheet Field_percentage"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_YEAR_Datasheet Field_percentage",
          "end_date_year_datasheet_fieldpercentage"
        );
        valuesExpected = [
          "Jan 10, 2029",
          "Dec 01, 2033",
          "Feb 25, 2026",
          "Sep 20, 2022",
          "Sep 05, 2027",
          "Feb 25, 2030",
          "Apr 15, 2024",
          "Aug 29, 2029",
          "Jan 21, 2024",
          "Sep 19, 2026",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying Date columns generated by Calculated Datasheet Field inputs -----------"
        );
        console.log(
          "Verifying values of date column: StartDate_Days_Datasheet Field_CF_INT"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_Days_Datasheet Field_CF_INT",
          "start_date_days_datasheet_field_cfint"
        );
        valuesExpected = [
          "Jan 31, 2023",
          "Jul 14, 2023",
          "Nov 28, 2022",
          "Mar 11, 2021",
          "Sep 05, 2024",
          "Mar 21, 2022",
          "Oct 31, 2023",
          "Jun 11, 2022",
          "",
          "Sep 14, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_MONTH_Datasheet Field_CF_INT"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_MONTH_Datasheet Field_CF_INT",
          "start_date_month_datasheet_field_cfint"
        );
        valuesExpected = [
          "May 15, 2024",
          "Nov 15, 2025",
          "Jul 20, 2023",
          "Apr 10, 2021",
          "Feb 28, 2025",
          "Sep 18, 2024",
          "Sep 08, 2025",
          "Jan 22, 2024",
          "",
          "Nov 12, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: StartDate_QUARTER_Datasheet Field_CF_INT"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_QUARTER_Datasheet Field_CF_INT",
          "start_date_quarter_datasheet_field_cfint"
        );
        valuesExpected = [
          "Jan 15, 2027",
          "Sep 15, 2030",
          "Nov 20, 2024",
          "Jun 10, 2021",
          "Feb 28, 2026",
          "Nov 18, 2029",
          "Jul 08, 2029",
          "May 22, 2027",
          "",
          "Mar 12, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: EndDate_HALF-YEAR_Datasheet Field_CF_INT"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_HALF-YEAR_Datasheet Field_CF_INT",
          "end_date_halfyear_datasheet_field_cfint"
        );
        valuesExpected = [
          "Jan 10, 2032",
          "Jun 01, 2038",
          "Feb 25, 2027",
          "Mar 20, 2023",
          "Sep 05, 2027",
          "Aug 25, 2038",
          "Oct 15, 2035",
          "Aug 29, 2032",
          "Jul 21, 2026",
          "Sep 19, 2026",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of date column: EndDate_YEAR_Datasheet Field_CF_INT"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_YEAR_Datasheet Field_CF_INT",
          "end_date_year_datasheet_field_cfint"
        );
        valuesExpected = [
          "Jan 10, 2040",
          "Dec 01, 2052",
          "Feb 25, 2031",
          "Sep 20, 2023",
          "Sep 05, 2030",
          "Feb 25, 2054",
          "Apr 15, 2047",
          "Aug 29, 2042",
          "Jan 21, 2031",
          "Sep 19, 2027",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying Date columns generated by HH:mm input -----------"
        );
        console.log(
          "Verifying values of date column: EndDate_HH:mm_Column_23:00"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "EndDate_HH:mm_Column_23:00",
          "end_date_h_hmm_column_2300"
        );
        valuesExpected = [
          "Jan 11, 2024",
          "Dec 02, 2023",
          "Feb 26, 2023",
          "Sep 21, 2022",
          "Sep 06, 2024",
          "Feb 26, 2023",
          "Apr 16, 2024",
          "Aug 30, 2022",
          "Jan 22, 2022",
          "Sep 20, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );

    test(
      "Tests related to ConvertTimezone",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T1982, INTER-T1983",
          },
          {
            type: "Description",
            description:
              "Validate the testcases related to ConvertTimezone function",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet(
          "db-calculatedFields",
          "cf-ConvertTimezone"
        );

        await dsV2Page.clickEditBtn();
        await dsV2EditPage.convertTimezoneFn(
          "Converted_StartDate_(GMT+05:30) Asia/Kolkata",
          "StartDate",
          "(GMT+00:00) UTC",
          "(GMT+05:30) Asia/Kolkata"
        );

        await dsV2EditPage.convertTimezoneFn(
          "Converted_EndDate_(GMT+09:00) Japan",
          "EndDate",
          "(GMT-05:00) Canada/Central",
          "(GMT+09:00) Japan"
        );

        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log(
          "Verifying converted Timezone date values of column: Converted_StartDate_(GMT+05:30) Asia/Kolkata"
        );
        let valuesReceived = await dsV2Page.getColumnValues(
          "Converted_StartDate_(GMT+05:30) Asia/Kolkata",
          "converted_start_date_gmt_0530_asia_kolkata"
        );
        let valuesExpected = [
          "Jan 16, 2023",
          "Jun 15, 2023",
          "Nov 21, 2022",
          "Mar 10, 2021",
          "Aug 30, 2024",
          "Feb 18, 2022",
          "Oct 08, 2023",
          "May 23, 2022",
          "",
          "Sep 12, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying converted Timezone date values of column: Converted_EndDate_(GMT+09:00) Japan"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "Converted_EndDate_(GMT+09:00) Japan",
          "converted_end_date_gmt_0900_japan"
        );
        valuesExpected = [
          "Jan 11, 2024",
          "Dec 02, 2023",
          "Feb 25, 2023",
          "Sep 21, 2022",
          "Sep 06, 2024",
          "Feb 26, 2023",
          "Apr 16, 2024",
          "Aug 30, 2022",
          "Jan 21, 2022",
          "Sep 20, 2025",
        ];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );

    test(
      "Tests related to functions - Round, RoundUp, RoundDown, GetDate, IsEmpty, IsNotEmpty, Contains, NotContains, Lower, Concat and Validate calculated field names.",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T2016, INTER-T2020, INTER-T2021, INTER-T2022, INTER-T2043, INTER-T2044, INTER-T2068, INTER-T2069, INTER-T2070, INTER-T2071, INTER-T2072, INTER-T2073, INTER-T2074, INTER-T2075, INTER-T2076, INTER-T2077, INTER-T2078, INTER-T2079",
          },
          {
            type: "Description",
            description: "Validate the testcases related to Round function",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-Round");

        console.log(
          "----- Running GetDate function with different inputs -----"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.getDateFn(
          "GetDate_StartDate_Day_",
          "StartDate",
          "Day",
          ""
        );
        await dsV2EditPage.getDateFn(
          "GetDate_StartDate_Quarter_Fiscal",
          "StartDate",
          "Quarter",
          "Fiscal"
        );
        await dsV2EditPage.getDateFn(
          "GetDate_StartDate_Quarter_Calendar",
          "StartDate",
          "Quarter",
          "Calendar"
        );
        await dsV2EditPage.getDateFn(
          "GetDate_StartDate_Year_Fiscal",
          "StartDate",
          "Year",
          "Fiscal"
        );
        await dsV2EditPage.getDateFn(
          "GetDate_StartDate_Year_Calendar",
          "StartDate",
          "Year",
          "Calendar"
        );

        console.log("----- Running 3 Variations of Round functions -----");
        await dsV2EditPage.roundFn("Round_Amount_2", "Round", "Amount", "2");
        await dsV2EditPage.roundFn(
          "RoundUp_Amount_1",
          "RoundUp",
          "Amount",
          "1"
        );
        await dsV2EditPage.roundFn(
          "RoundDown_Amount_1",
          "RoundDown",
          "Amount",
          "1"
        );
        await dsV2EditPage.roundFn(
          "RoundDown_Amount_-1",
          "RoundDown",
          "Amount",
          "-1"
        );

        console.log("----- Running IsEmpty/IsNotEmpty functions -----");
        await dsV2EditPage.emptyFn(
          "IsEmpty_LastName",
          [
            "ID",
            "FirstName",
            "LastName",
            "StartDate",
            "EndDate",
            "Amount",
            "deliveredIn",
            "profit",
            "percentage",
            "email",
          ],
          "IsEmpty",
          "LastName"
        );
        await dsV2EditPage.emptyFn(
          "IsNotEmpty_LastName",
          undefined,
          "IsNotEmpty",
          "LastName"
        );
        await dsV2EditPage.emptyFn(
          "IsEmpty_deliveredIn",
          undefined,
          "IsEmpty",
          "deliveredIn"
        );
        await dsV2EditPage.emptyFn(
          "IsNotEmpty_deliveredIn",
          undefined,
          "IsNotEmpty",
          "deliveredIn"
        );
        await dsV2EditPage.emptyFn(
          "IsEmpty_profit",
          undefined,
          "IsEmpty",
          "profit"
        );

        console.log("----- Running Contains/NotContains functions -----");
        await dsV2EditPage.containFn(
          "Contains_FirstName_o",
          ["FirstName", "LastName", "email"],
          "Contains",
          "FirstName",
          "o"
        );
        await dsV2EditPage.containFn(
          "NotContains_LastName_o",
          undefined,
          "NotContains",
          "LastName",
          "o"
        );

        console.log(
          "----- Running Lower and Concat functions, validate incorrect data type and provide formula before selecting data type  -----"
        );
        console.log(
          "Creating Lower function by providing forumla before selecting data type and also by validate incorrect field type"
        );
        await dsV2EditPage.clickAddFormula();
        let formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "Lower"
        );
        await formulaLocator.click();

        const columnValues = ["FirstName", "LastName", "email"];
        await dsV2EditPage.clickDropdown("Please Select");
        for (const column of columnValues) {
          const isColumnPresent = await dsV2EditPage.verifyUnit(
            "Please Select",
            column
          );
          expect(isColumnPresent).toBe(true);
        }
        await dsV2EditPage.clickDropdown("Please Select");

        const btnApply = await dsV2EditPage.applyFnBtn();
        await expect(btnApply).toBeDisabled();
        await dsV2EditPage.selectDropDown("Please Select", "FirstName");
        await expect(btnApply).toBeEnabled();
        await btnApply.click();
        await btnApply.waitFor({ state: "hidden" });
        let createBtn = await dsV2EditPage.createBtnLocator();
        await expect(createBtn).toBeDisabled();
        await dsV2EditPage.delay(1500);
        await dsV2EditPage.setCalculatedFieldName("Lower_FirstName");
        await dsV2EditPage.setCalculatedFieldFormulaType("String");
        await dsV2EditPage.delay(1000);
        await dsV2EditPage.clickCreateFn();
        console.log("Applied Lower function on column : FirstName");

        console.log("Creating Concat function");
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType(
          "Concat_FirstName_LastName",
          "String"
        );
        formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "Concat"
        );
        await formulaLocator.click();

        await expect(btnApply).toBeDisabled();
        let columnLocator = await dsV2EditPage.concatExpressionBox("FirstName");
        await columnLocator.click();
        columnLocator = await dsV2EditPage.concatExpressionBox("LastName");
        await columnLocator.click();
        await page.keyboard.press("Tab");
        await expect(btnApply).toBeEnabled();
        await btnApply.click();
        await btnApply.waitFor({ state: "hidden" });
        await dsV2EditPage.clickCreateFn();
        console.log(
          "Applied Concat function on columns: FirstName and LastName"
        );

        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log(
          "Verify error message is displayed on creating a new calculated filed with existing name."
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("Lower_FirstName", "String");
        formulaLocator = await dsV2EditPage.selectFormula("FUNCTIONS", "Lower");
        await formulaLocator.click();
        await dsV2EditPage.selectDropDown("Please Select", "FirstName");
        await btnApply.click();
        createBtn = await dsV2EditPage.createBtnLocator();
        await createBtn.click();
        let isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Multiple columns have the same name: lower_firstname"
        );
        expect(isPopupVisible).toBe(true);
        await dsV2EditPage.closeFormulaPopup();

        console.log(
          "Verify error message is displayed on updating an existing calculated filed with existing name."
        );
        await dsV2EditPage.hoverFormulaColumn("IsEmpty_deliveredIn");
        await dsV2EditPage.formulaMoreActions("IsEmpty_deliveredIn");
        await dsV2EditPage.clickFormulaMoreActions("Edit");
        await dsV2EditPage.setCalculatedFieldName("IsEmpty_profit");
        await dsV2EditPage.clickUpdateFn();
        isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Multiple columns have the same name: isempty_profit"
        );
        expect(isPopupVisible).toBe(true);
        await dsV2EditPage.closeFormulaPopup();
        await dsV2Page.closeEditsheetPage();

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log(
          "---------- Verifying columns generated with GetDate function -----------"
        );
        console.log("Verifying values of column: GetDate_StartDate_Day_");
        let valuesReceived = await dsV2Page.getColumnValues(
          "GetDate_StartDate_Day_",
          "get_date_start_date_day"
        );
        let valuesExpected = [
          "15",
          "15",
          "20",
          "10",
          "30",
          "18",
          "8",
          "22",
          "0",
          "12",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of column: GetDate_StartDate_Quarter_Fiscal"
        );
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "GetDate_StartDate_Quarter_Fiscal",
          "get_date_start_date_quarter_fiscal"
        );
        valuesExpected = ["1", "2", "4", "1", "3", "1", "4", "2", "0", "3"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of column: GetDate_StartDate_Quarter_Calendar"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "GetDate_StartDate_Quarter_Calendar",
          "get_date_start_date_quarter_calendar"
        );
        valuesExpected = ["1", "2", "4", "1", "3", "1", "4", "2", "0", "3"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of column: GetDate_StartDate_Year_Fiscal"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "GetDate_StartDate_Year_Fiscal",
          "get_date_start_date_year_fiscal"
        );
        valuesExpected = [
          "2,023",
          "2,023",
          "2,022",
          "2,021",
          "2,024",
          "2,022",
          "2,023",
          "2,022",
          "0",
          "2,024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "Verifying values of column: GetDate_StartDate_Year_Calendar"
        );
        valuesReceived = await dsV2Page.getColumnValues(
          "GetDate_StartDate_Year_Calendar",
          "get_date_start_date_year_calendar"
        );
        valuesExpected = [
          "2,023",
          "2,023",
          "2,022",
          "2,021",
          "2,024",
          "2,022",
          "2,023",
          "2,022",
          "0",
          "2,024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying columns generated with Round functions -----------"
        );
        console.log("Verifying values of column: Round_Amount_2");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "Round_Amount_2",
          "round_amount_2"
        );
        valuesExpected = [
          "99.23",
          "99.88",
          "6,543.21",
          "3,211",
          "1,111.22",
          "5,678.89",
          "0",
          "7,890.46",
          "2,345.68",
          "8,765.43",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: RoundUp_Amount_1");
        valuesReceived = await dsV2Page.getColumnValues(
          "RoundUp_Amount_1",
          "round_up_amount_1"
        );
        valuesExpected = [
          "99.30",
          "99.90",
          "6,543.30",
          "3,211",
          "1,111.30",
          "5,678.90",
          "0",
          "7,890.50",
          "2,345.70",
          "8,765.50",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: RoundDown_Amount_1");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "RoundDown_Amount_1",
          "round_down_amount_1"
        );
        valuesExpected = [
          "99.20",
          "99.80",
          "6,543.20",
          "3,210.90",
          "1,111.20",
          "5,678.80",
          "0",
          "7,890.40",
          "2,345.60",
          "8,765.40",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying columns generated with IsEmpty/IsNotEmpty functions -----------"
        );
        console.log("Verifying values of column: IsEmpty_LastName");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "IsEmpty_LastName",
          "is_empty_last_name"
        );
        valuesExpected = [
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "true",
          "false",
          "false",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: IsNotEmpty_LastName");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "IsNotEmpty_LastName",
          "is_not_empty_last_name"
        );
        valuesExpected = [
          "true",
          "true",
          "true",
          "true",
          "true",
          "true",
          "true",
          "false",
          "true",
          "true",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: IsEmpty_deliveredIn");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "IsEmpty_deliveredIn",
          "is_emptydelivered_in"
        );
        valuesExpected = [
          "false",
          "false",
          "false",
          "true",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: IsNotEmpty_deliveredIn");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "IsNotEmpty_deliveredIn",
          "is_not_emptydelivered_in"
        );
        valuesExpected = [
          "true",
          "true",
          "true",
          "false",
          "true",
          "true",
          "true",
          "true",
          "true",
          "true",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: IsEmpty_profit");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "IsEmpty_profit",
          "is_emptyprofit"
        );
        valuesExpected = [
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
          "false",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying columns generated with Contains/NotContains functions -----------"
        );
        console.log("Verifying values of column: Contains_FirstName_o");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "Contains_FirstName_o",
          "contains_first_nameo"
        );
        valuesExpected = [
          "true",
          "true",
          "false",
          "true",
          "false",
          "false",
          "false",
          "false",
          "false",
          "true",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: NotContains_LastName_o");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "NotContains_LastName_o",
          "not_contains_last_nameo"
        );
        valuesExpected = [
          "true",
          "false",
          "false",
          "false",
          "false",
          "true",
          "true",
          "false",
          "true",
          "false",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying columns generated with Lower function -----------"
        );
        console.log("Verifying values of column: Lower_FirstName");
        valuesReceived = await dsV2Page.getColumnValues(
          "Lower_FirstName",
          "lower_first_name"
        );
        valuesExpected = [
          "naruto",
          "goku",
          "luffy",
          "ichigo",
          "sakura",
          "edward",
          "mikasa",
          "saitama",
          "levi",
          "roronoa",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "---------- Verifying columns generated with Concat function -----------"
        );
        console.log("Verifying values of column: Concat_FirstName_LastName");
        valuesReceived = await dsV2Page.getColumnValues(
          "Concat_FirstName_LastName",
          "concat_first_name_last_name"
        );
        valuesExpected = [
          "NarutoUzumaki",
          "GokuSon",
          "LuffyMonkey D.",
          "IchigoKurosaki",
          "SakuraHaruno",
          "EdwardElric",
          "MikasaAckerman",
          "Saitama",
          "LeviAckerman",
          "RoronoaZoro",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log(
          "----- Validate information is displayed when hovering over decimal values -----"
        );
        const toolTip = await dsV2Page.isTooltipVisible("99.23", "99.230000");
        expect(toolTip).toBe(true);

        console.log(
          "Validate that user is able to create a new view with decimal value filter"
        );
        const tabName = "Amount Decimal Check";
        await dsV2Page.createNewView("Amount");
        await dsV2Page.renameView("New View 1", tabName);
        await dsV2Page.letColumnsLoad("Amount");
        formulaLocator = await dsV2EditPage.selectFormula(
          "DATASHEET",
          "Amount"
        );
        await formulaLocator.click();
        await dsV2EditPage.typeFormulaExpression("<");
        await dsV2EditPage.typeFormulaExpression("111.11");
        await dsV2Page.saveNewView();
        // Wait until the tab count becomes "02"
        await page.waitForFunction((tabName) => {
          const element = document.evaluate(
            `//div[@role='tab' and contains(., '${tabName}')]//span`,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
          ).singleNodeValue;
          return element && element.textContent === "02";
        }, tabName);

        console.log(
          "Verify function is avaiable to view, on hover and click on function icon next to calculated field column."
        );
        await dsV2Page.letColumnsLoad("ID");
        const { isTitlePresent: title1, combinedText: formula1 } =
          await dsV2EditPage.verifyFormulaView("cf_is_emptyprofit");
        expect(title1).toBe(true);
        expect(formula1).toEqual("IsEmpty(profit)");
        await dsV2EditPage.closeFormulaPopup();

        const { isTitlePresent: title2, combinedText: formula2 } =
          await dsV2EditPage.verifyFormulaView(
            "cf_get_date_start_date_year_calendar"
          );
        expect(title2).toBe(true);
        expect(formula2).toEqual("GetDate(StartDate, Year, Calendar)");
        await dsV2EditPage.closeFormulaPopup();

        console.log(
          "----- Validate user is able to create a calculated field using an existing calculated field -----"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType(
          "Clone-GetDate_StartDate_Day_",
          "Integer"
        );
        formulaLocator = await dsV2EditPage.selectFormula(
          "DATASHEET",
          "GetDate_StartDate_Day_"
        );
        await formulaLocator.click();
        await dsV2EditPage.typeFormulaExpression("+");
        await dsV2EditPage.typeFormulaExpression("10");
        await dsV2EditPage.clickCreateFn();
        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.isColumnPresent("Clone-GetDate_StartDate_Day_");
      }
    );

    test(
      "Tests related to conditional expression in Add Formula field and Tests related to Rank function",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T2025, INTER-T2026, INTER-T2027, INTER-T2028, INTER-T2029, INTER-T2031, INTER-T2032, INTER-T2033, INTER-T2034, INTER-T2035, INTER-T2036, INTER-T2037, INTER-T2038, INTER-T2039, INTER-T2040, INTER-T2041, INTER-T2042",
          },
          {
            type: "Description",
            description: "Validate the testcases related to Round function",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-OtherFns");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();

        await page
          .getByRole("button", { name: "Conditional", exact: true })
          .click();
        await dsV2EditPage.delay(1000);

        let nullCheckBoxCount = await dsV2EditPage.nullCheckBoxCount();
        expect(nullCheckBoxCount).toBe(1); // verify only 1 null box is present in else block
        let addIfBtn = await dsV2EditPage.switchAddIFLocator("THEN");
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // enable add if in THEN block
        nullCheckBoxCount = await dsV2EditPage.nullCheckBoxCount();
        expect(nullCheckBoxCount).toBe(2); // verify 2 null checkbox are visible in ELSE blocks

        addIfBtn = await dsV2EditPage.switchAddIFLocator("ELSE", 2);
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // enable add if in ELSE block
        nullCheckBoxCount = await dsV2EditPage.nullCheckBoxCount();
        expect(nullCheckBoxCount).toBe(3); // verify 3 null checkbox are visible in ELSE blocks

        addIfBtn = await dsV2EditPage.switchAddIFLocator("THEN");
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // disable add if in THEN block
        addIfBtn = await dsV2EditPage.switchAddIFLocator("ELSE");
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // disbale add if in ELSE block

        expect(await dsV2EditPage.formulaBoxCount("ELSE")).toBe(1); // verify 1 formula exp box is visible in ELSE block
        let checkNull = await await dsV2EditPage.checkNullBoxLocator();
        await dsV2EditPage.delay(1500);
        await checkNull.click();
        await dsV2EditPage.switchPrompt(); // check the null box in ELSE block
        expect(await dsV2EditPage.formulaBoxCount("ELSE")).toBe(0); // verify no formula expression is asked
        addIfBtn = await dsV2EditPage.switchAddIFLocator("ELSE");
        await expect(addIfBtn).toBeDisabled(); // verify add if button in ELSE block is disabled
        checkNull = await await dsV2EditPage.checkNullBoxLocator();
        await checkNull.click();
        await dsV2EditPage.delay(1500);
        expect(await dsV2EditPage.formulaBoxCount("ELSE")).toBe(1); // verify 1 exp box is visible agagin on disabling null

        addIfBtn = await dsV2EditPage.switchAddIFLocator("ELSE");
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // enable add if in ELSE block
        checkNull = await dsV2EditPage.checkNullBoxLocator();
        await expect(checkNull).toBeDisabled(); // verify null box is disabled when add if is enabled in parent ELSE block
        expect(await dsV2EditPage.formulaBoxCount("ELSE")).toBe(3); // verify 3 exp box are visible in enabled add if PARENT ELSE block
        await dsV2EditPage.delay(1500);
        checkNull = await dsV2EditPage.checkNullBoxLocator(2);
        await checkNull.click();
        await dsV2EditPage.switchPrompt(); // check the null box in Nested ELSE block
        expect(await dsV2EditPage.formulaBoxCount("ELSE")).toBe(2); // verify only 2 exp box are visible after enabling null in nested ELSE block

        addIfBtn = await dsV2EditPage.switchAddIFLocator("ELSE");
        await addIfBtn.click();
        await dsV2EditPage.switchPrompt(); // disable add if in Parent ELSE block
        checkNull = await dsV2EditPage.checkNullBoxLocator();
        await expect(checkNull).toBeEnabled();
        await dsV2EditPage.closeFormulaPopup();

        console.log(
          "----- Applying conditional functions on all 6 types of fields -----"
        );
        await dsV2EditPage.conditionalFn("Integer", "deliveredIn", ">", "25");
        await dsV2EditPage.conditionalFn(
          "String",
          "LastName",
          "==",
          "Ackerman"
        );
        await dsV2EditPage.conditionalFn("Date", "StartDate", ">", "EndDate");
        await dsV2EditPage.conditionalFn("Boolean", "profit", "==", "True");
        await dsV2EditPage.conditionalFn(
          "Percentage",
          "percentage",
          ">",
          "0.3"
        );
        await dsV2EditPage.conditionalFn(
          "Email",
          "email",
          "!=",
          "<EMAIL>"
        );

        console.log("----- Running tests related to Rank function -----");
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("rank_id_profit", "Integer");
        let formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "Rank"
        );
        await formulaLocator.click();
        await dsV2EditPage.isRankModalOpen();
        await dsV2EditPage.fillRankModal("ID", "profit");
        let btnApply = await dsV2EditPage.applyFnBtn();
        await expect(btnApply).toBeEnabled();
        await btnApply.click();
        await dsV2EditPage.clickCreateFn();
        console.log("Created Rank function with fieldname:rank_id_profit");

        console.log(
          "----- Convert exisitng round function:CF_round to Rank function -----"
        );
        await dsV2EditPage.hoverFormulaColumn("CF_round");
        await dsV2EditPage.formulaMoreActions("CF_round");
        await dsV2EditPage.clickFormulaMoreActions("Edit");
        await dsV2EditPage.setCalculatedFieldName("converted_round_to_rank");
        let searchBar = await dsV2EditPage.clickFormulaExpressionBox();
        await searchBar.clear();
        formulaLocator = await dsV2EditPage.selectFormula("FUNCTIONS", "Rank");
        await formulaLocator.click();
        await dsV2EditPage.isRankModalOpen();
        await dsV2EditPage.fillRankModal("ID", "profit");
        btnApply = await dsV2EditPage.applyFnBtn();
        await expect(btnApply).toBeEnabled();
        await btnApply.click();
        let updateBtn = await dsV2EditPage.clickUpdateFn();
        await updateBtn.waitFor({ state: "hidden", timeout: 30000 });

        console.log(
          "----- Convert exisitng rank function:CF_rank1 to Round function -----"
        );
        await dsV2EditPage.hoverFormulaColumn("CF_rank1");
        await dsV2EditPage.formulaMoreActions("CF_rank1");
        await dsV2EditPage.clickFormulaMoreActions("Edit");
        await dsV2EditPage.setCalculatedFieldName("converted_rank_to_round");
        searchBar = await dsV2EditPage.clickFormulaExpressionBox();
        await searchBar.clear();
        formulaLocator = await dsV2EditPage.selectFormula("FUNCTIONS", "Round");
        await formulaLocator.click();
        await dsV2EditPage.selectDropDown("Select Number Column", "Amount");
        await page.getByPlaceholder("Decimal Places").fill("2");
        await expect(btnApply).toBeEnabled();
        await btnApply.click();
        updateBtn = await dsV2EditPage.clickUpdateFn();
        await updateBtn.waitFor({ state: "hidden", timeout: 30000 });

        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");

        console.log(
          "----- Verify all newly generated calculated field columns data -----"
        );
        await dsV2Page.sortColumn("ID", "ASC");
        console.log(
          "---------- Verifying columns generated with conditionalFn function -----------"
        );
        console.log("Verifying values of column: deliveredIn_conditional_null");
        let valuesReceived = await dsV2Page.getColumnValues(
          "deliveredIn_conditional_null",
          "delivered_inconditionalnull"
        );
        let valuesExpected = [
          "0",
          "28",
          "0",
          "0",
          "0",
          "30",
          "0",
          "0",
          "0",
          "0",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: LastName_conditional_null");
        valuesReceived = await dsV2Page.getColumnValues(
          "LastName_conditional_null",
          "last_nameconditionalnull"
        );
        valuesExpected = [
          "",
          "",
          "",
          "",
          "",
          "",
          "Ackerman",
          "",
          "Ackerman",
          "",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: StartDate_conditional_null");
        valuesReceived = await dsV2Page.getColumnValues(
          "StartDate_conditional_null",
          "start_dateconditionalnull"
        );
        valuesExpected = ["", "", "", "", "", "", "", "", "", ""];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: profit_conditional_null");
        valuesReceived = await dsV2Page.getBooleanColumnValues(
          "profit_conditional_null",
          "profitconditionalnull"
        );
        valuesExpected = ["true", "true", "true", "true", "true", "true"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: email_conditional_null");
        valuesReceived = await dsV2Page.getColumnValues(
          "email_conditional_null",
          "emailconditionalnull"
        );
        valuesExpected = [
          "",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: percentage_conditional_null");
        valuesReceived = await dsV2Page.getColumnValues(
          "percentage_conditional_null",
          "percentageconditionalnull"
        );
        valuesExpected = ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: rank_id_profit");
        valuesReceived = await dsV2Page.getColumnValues(
          "rank_id_profit",
          "rankidprofit"
        );
        valuesExpected = ["1", "1", "2", "2", "3", "1", "4", "3", "5", "6"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: converted_round_to_rank");
        valuesReceived = await dsV2Page.getColumnValues(
          "converted_round_to_rank",
          "addedround"
        );
        valuesExpected = ["1", "1", "2", "2", "3", "1", "4", "3", "5", "6"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: converted_rank_to_round");
        valuesReceived = await dsV2Page.getColumnValues(
          "converted_rank_to_round",
          "c_frank_1"
        );
        valuesExpected = [
          "99.23",
          "99.88",
          "6,543.21",
          "3,211",
          "1,111.22",
          "5,678.89",
          "0",
          "7,890.46",
          "2,345.68",
          "8,765.43",
        ];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );

    test(
      "Tests related to commission plan and adjustments",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T2017, INTER-T2018, INTER-T2030, INTER-T2023, INTER-T2024",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);

        console.log(
          "----- Verify calculated field presence in Draft Plan formula dropdown. Verify error message on deleting depandant calculated field used in plans -----"
        );
        const commission = new CanvasCommission(page);
        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan(
          "test-calculated-field-in-draft-plan"
        );
        await commission.clickCommissionComponent("simple-comp");
        const searchBar = await dsV2EditPage.clickFormulaExpressionBox();
        await searchBar.clear();
        let formulaLocator = await dsV2EditPage.selectFormula(
          "DATASHEET COLUMNS",
          "CF_rank"
        ); // CF_rank presence check is performed in formula dropdown.
        await formulaLocator.click();
        await dsV2EditPage.typeFormulaExpression("+");
        await dsV2EditPage.typeFormulaExpression("ID");
        await commission.clickSaveComponentButton();
        await commission.waitForComponentSavedMessage(10000);
        await commission.publishCommissionPlanV2(
          "test-calculated-field-in-draft-plan"
        ); // publish the draft plan
        await commission.exitCanvas();
        console.log("Draft plan is published successfully");

        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-OtherFns");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.hoverFormulaColumn("CF_rank");
        await dsV2EditPage.formulaMoreActions("CF_rank");
        await dsV2EditPage.clickFormulaMoreActions("Delete");
        let isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Are you sure to delete this field?"
        );
        expect(isPopupVisible).toBe(true);
        await dsV2EditPage.clickDeleteFn();
        isPopupVisible = await dsV2EditPage.isPopUpMsgVisible(
          "Column's CF_rank used in plan - test-calculated-field-in-draft-plan"
        );
        expect(isPopupVisible).toBe(true);

        console.log(
          "----- Verify commission plan formula preview shows Do Nothing in ELSE block in users page for selected payee -----"
        );
        await adminPage.page.goto("/users", { waitUntil: "networkidle" });
        await page.getByPlaceholder("Search by name or email").fill("Payee 2");
        await dsV2EditPage.delay(1500);
        await page
          .getByText("commissionPlan-previewNothing, +2")
          .last()
          .hover();
        await dsV2EditPage.delay(1000);
        await page
          .getByRole("button", {
            name: "commissionPlan-previewNothing",
            exact: true,
          })
          .last()
          .click();
        await page.getByText("View Plan Details").waitFor({ state: "visible" });
        await expect(await page.getByText("Do Nothing")).toBeVisible();

        console.log(
          "----- Verify validation for calculated fields applied on adjustment sheet -----"
        );
        await adminPage.page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-adjustments");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("CF_ID_Boolean", "Boolean");
        formulaLocator = await dsV2EditPage.selectFormula("DATASHEET", "CF_ID");
        await formulaLocator.click();
        await dsV2EditPage.typeFormulaExpression(">");
        await dsV2EditPage.typeFormulaExpression("3");
        await dsV2EditPage.clickCreateFn();
        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("ID");
      }
    );

    test(
      "Test functions - Trim, Len, Left, Right, Mid, Find, Rollingsum, Coalesce, GenerateHierarchy, GetvaluefromHierarchy",
      {
        annotation: [
          {
            type: "Test ID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Validate the testcases related to above mentioned functions",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to create calcualted fields with all above functions",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);

        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-functions");
        await dsV2Page.clickEditBtn();

        console.log(
          "Verify user is able to create calculated field using Trim function"
        );
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("Trim-Fn", "String");
        let formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "Trim"
        );
        await formulaLocator.click();
        let applyBtn = await dsV2EditPage.applyFnBtn();
        await expect(applyBtn).toBeDisabled();
        await dsV2EditPage.selectDropDown("Please Select", "FirstName");
        await expect(applyBtn).toBeEnabled();
        await applyBtn.click();
        await dsV2EditPage.clickCreateFn();
        await expect(page.getByText("Trim-Fn", { exact: true })).toBeVisible();

        console.log(
          "Verify user is able to calculated field formula using Len function"
        );
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("Len-Fn", "Integer");
        formulaLocator = await dsV2EditPage.selectFormula("FUNCTIONS", "Len");
        await formulaLocator.click();
        applyBtn = await dsV2EditPage.applyFnBtn();
        await expect(applyBtn).toBeDisabled();
        await dsV2EditPage.selectDropDown("Please Select", "email");
        await expect(applyBtn).toBeEnabled();
        await applyBtn.click();
        await dsV2EditPage.clickCreateFn();
        await expect(page.getByText("Len-Fn", { exact: true })).toBeVisible();

        console.log(
          "Verify user is able to create formula using Left function"
        );
        await dsV2EditPage.LeftRightFn(
          "Left-fn1",
          "Left",
          "FirstName",
          "Constant",
          "3"
        );
        await dsV2EditPage.LeftRightFn(
          "Left-fn2",
          "Left",
          "email",
          "Datasheet Field",
          "Amount"
        );

        console.log(
          "Verify user is able to create calculated field using Right function"
        );
        await dsV2EditPage.LeftRightFn(
          "Right-fn1",
          "Right",
          "FirstName",
          "Constant",
          "2"
        );
        await dsV2EditPage.LeftRightFn(
          "Right-fn2",
          "Right",
          "email",
          "Datasheet Field",
          "Amount"
        );

        console.log(
          "Verify user is able to create calculated field using Mid function"
        );
        await dsV2EditPage.midFn(
          "midFn1",
          "Mid",
          "email",
          "ID",
          "Constant",
          "3"
        );
        await dsV2EditPage.midFn(
          "midFn2",
          "Mid",
          "email",
          "ID",
          "Datasheet Field",
          "Amount"
        );

        console.log(
          "Verify user is able to create calculated field using Find function"
        );
        await dsV2EditPage.findFn("Find-Fn1", "FirstName", "Constant", "a");
        await dsV2EditPage.findFn(
          "Find-Fn2",
          "LastName",
          "Datasheet Field",
          "LastName"
        );
        await dsV2EditPage.findFn(
          "Find-Fn3",
          "LastName",
          "Datasheet Field",
          "FirstName"
        );

        console.log(
          "Verify user is able to create calculated field using Rollingsum function"
        );
        await dsV2EditPage.RollingSumFn(
          "RollingSum-Fn",
          "Amount",
          ["FirstName", "email"],
          ["email"]
        );
        await dsV2EditPage.saveEdits();
        await dsV2Page.waitForRefresh();

        console.log(
          "Verify user is able to create calculated field using Coalesce function"
        );
        await dsV2Page.goToDatasheet("db-calculatedFields", "Coalesce-sheet");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.verifyCoalesceModal();
        const coalesceParams = [
          {
            fnName: "Coalesce-string-Fn1",
            type: "String",
            fields: ["string1", "string2", "string3"],
          },
          {
            fnName: "Coalesce-email-Fn1",
            type: "Email",
            fields: ["email1", "email2", "email3"],
          },
          {
            fnName: "Coalesce-date-Fn1",
            type: "Date",
            fields: ["date1", "date2", "date3"],
          },
          {
            fnName: "Coalesce-boolean-Fn1",
            type: "Boolean",
            fields: ["boolean1", "boolean2", "boolean3"],
          },
        ];

        for (const param of coalesceParams) {
          await dsV2EditPage.coalesceFn(param.fnName, param.type, param.fields);
        }
        await dsV2EditPage.saveEdits();
        await dsV2Page.waitForRefresh();

        console.log(
          "Verify user is able to create calculated field using GenerateHierarchy function"
        );
        await dsV2Page.goToDatasheet("db-calculatedFields", "hierarchy");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.GenerateHierarchyFn("generateHierarchy-Fn", [
          "freelancer email",
          "sales date",
          "report-DB",
          "User",
          "Employee Email Id",
          "Reporting Manager EmailId",
        ]);
        await dsV2EditPage.saveEdits();
        await dsV2Page.waitForRefresh();

        console.log(
          "Verify user is able to create calculated field using GetvaluefromHierarchy function"
        );
        await dsV2Page.goToDatasheet(
          "db-calculatedFields",
          "getValuefromHierarchy"
        );
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("GetValueFromHierarchy-Fn", "Email");
        formulaLocator = await dsV2EditPage.selectFormula(
          "FUNCTIONS",
          "GetValueFromHierarchy"
        );
        await formulaLocator.click();
        let isChecked = await dsV2EditPage.switchTopLevel("on");
        expect(isChecked).toBe("true");
        await expect(page.getByPlaceholder("Enter Level")).toBeDisabled();
        isChecked = await dsV2EditPage.switchTopLevel("off");
        expect(isChecked).toBe("false");
        await expect(page.getByPlaceholder("Enter Level")).toBeEnabled();
        await dsV2EditPage.selectDropDown("Hierarchy Field", "hierarchy-Fn");
        applyBtn = await dsV2EditPage.applyFnBtn();
        await expect(applyBtn).toBeDisabled();
        await page.getByPlaceholder("Enter Level").fill("1");
        await expect(applyBtn).toBeEnabled();
        await applyBtn.click();
        await dsV2EditPage.clickCreateFn();
        await expect(
          page.getByText("GetValueFromHierarchy-Fn", { exact: true })
        ).toBeVisible();
        await dsV2EditPage.saveEdits();
        await dsV2Page.waitForRefresh();

        await dsV2Page.databookRefreshSheets(
          "db-calculatedFields",
          "clientName",
          false
        );

        console.log(
          "***** Veriyfy data of all calculated fields generated *****"
        );
        console.log(
          "Verify the calcualted fields created in databook - cf-functions"
        );
        await dsV2Page.goToDatasheet("db-calculatedFields", "cf-functions");
        await dsV2Page.sortColumn("ID", "ASC");

        console.log("Verifying values of column: Trim-Fn");
        let valuesReceived = await dsV2Page.getColumnValues(
          "Trim-Fn",
          "trim_fn"
        );
        let valuesExpected = [
          "Naruto",
          "Goku",
          "Luffy",
          "Ichigo",
          "Sakura",
          "Edward",
          "Mikasa",
          "Saitama",
          "Levi",
          "Roronoa",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Len-Fn");
        valuesReceived = await dsV2Page.getSpanColumnValues("Len-Fn", "len_fn");
        valuesExpected = [
          "26",
          "20",
          "24",
          "27",
          "25",
          "24",
          "27",
          "19",
          "25",
          "24",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Left-fn1");
        valuesReceived = await dsV2Page.getColumnValues("Left-fn1", "leftfn_1");
        valuesExpected = [
          "Nar",
          "Gok",
          "Luf",
          "Ich",
          "Sak",
          "Edw",
          "Mik",
          "Sai",
          "Lev",
          "Ror",
        ];

        console.log("Verifying values of column: Left-fn2");
        valuesReceived = await dsV2Page.getColumnValues("Left-fn2", "leftfn_2");
        valuesExpected = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Right-fn1");
        valuesReceived = await dsV2Page.getColumnValues(
          "Right-fn1",
          "rightfn_1"
        );
        valuesExpected = [
          "to",
          "ku",
          "fy",
          "go",
          "ra",
          "rd",
          "sa",
          "ma",
          "vi",
          "oa",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Right-fn2");
        valuesReceived = await dsV2Page.getColumnValues(
          "Right-fn2",
          "rightfn_2"
        );
        valuesExpected = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: midFn1");
        valuesReceived = await dsV2Page.getColumnValues("midFn1", "mid_fn_1");
        valuesExpected = [
          "nar",
          "oku",
          "ffy",
          "igo",
          "ra.",
          "d.e",
          ".ac",
          "@ex",
          "erm",
          "oro",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: midFn2");
        valuesReceived = await dsV2Page.getColumnValues("midFn2", "mid_fn_2");
        valuesExpected = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "",
          "@example.com",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Find-Fn1");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "Find-Fn1",
          "find_fn_1"
        );
        valuesExpected = ["2", "0", "0", "0", "2", "4", "4", "2", "0", "7"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Find-Fn2");
        valuesReceived = await dsV2Page.getColumnValues(
          "Find-Fn2",
          "find_fn_2"
        );
        valuesExpected = ["1", "1", "1", "1", "1", "1", "1", "0", "1", "1"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Find-Fn3");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "Find-Fn3",
          "find_fn_3"
        );
        valuesExpected = ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: RollingSum-Fn");
        valuesReceived = await dsV2Page.getSpanColumnValues(
          "RollingSum-Fn",
          "rolling_sum_fn"
        );
        valuesExpected = [
          "99.23",
          "99.88",
          "6,543.21",
          "3,211.00",
          "1,111.22",
          "5,678.89",
          "",
          "7,890.46",
          "2,345.68",
          "8,765.43",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        await dsV2Page.goToDatasheet("db-calculatedFields", "Coalesce-sheet");
        await dsV2Page.sortColumn("id", "ASC");
        console.log("Verifying values of column: Coalesce-string-Fn1");
        // valuesReceived = await dsV2Page.getColumnValues(
        //   "Coalesce-string-Fn1",
        //   "coalescestring_fn_1"
        // );
        // valuesExpected = ["Naruto", "Goku", "Luffy", "Ichigo", "Sakura"];
        // expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Coalesce-email-Fn1");
        valuesReceived = await dsV2Page.getColumnValues(
          "Coalesce-email-Fn1",
          "coalesceemail_fn_1"
        );
        valuesExpected = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Coalesce-date-Fn1");
        valuesReceived = await dsV2Page.getColumnValues(
          "Coalesce-date-Fn1",
          "coalescedate_fn_1"
        );
        valuesExpected = [
          "Jan 10, 2024",
          "Feb 20, 2024",
          "Dec 25, 2023",
          "Mar 01, 2024",
          "Feb 10, 2024",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        console.log("Verifying values of column: Coalesce-boolean-Fn1");
        valuesReceived = await dsV2Page.getColumnValues(
          "Coalesce-boolean-Fn1",
          "coalesceboolean_fn_1"
        );
        valuesExpected = ["true", "false", "true", "false", "true"];
        expect(valuesReceived).toEqual(valuesExpected);

        await dsV2Page.goToDatasheet("db-calculatedFields", "hierarchy");
        await dsV2Page.goToTab("Tab1");
        await dsV2Page.sortColumn("id", "ASC");
        console.log("Verifying values of column: generateHierarchy-Fn");
        valuesReceived = await dsV2Page.getColumnValues(
          "generateHierarchy-Fn",
          "generate_hierarchy_fn"
        );
        valuesExpected = [
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
          "<EMAIL>--><EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);

        await dsV2Page.goToDatasheet(
          "db-calculatedFields",
          "getValuefromHierarchy"
        );
        await dsV2Page.goToTab("Tab1");
        await dsV2Page.sortColumn("id", "ASC");
        console.log("Verifying values of column: GetValueFromHierarchy-Fn");
        valuesReceived = await dsV2Page.getColumnValues(
          "GetValueFromHierarchy-Fn",
          "get_value_from_hierarchy_fn"
        );
        valuesExpected = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );
  }
);
