import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import DatasheetV2ManagePermission from "../../../../test-objects/datasheetV2ManagePermission-objects";

const {
  datasheetV2ManagePermissionFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 900000);
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }

  await page.goto("/datasheet", { waitUntil: "networkidle" });
});

test.describe(
  "Manage Permissions",
  { tag: ["@datasheet", "@regression", "@adminchamp-3"] },
  () => {
    test(
      "Manage Permission On/Off, View datasheet access and verify",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9525, INTER-T9525,INTER-T9540",
          },
          {
            type: "Description",
            description:
              "Validate Whether Manage Permission Button visible for user if the access is On/Off and user has only view datasheet access",
          },
          {
            type: "Precondition",
            description:
              "User should have access to view and manage datasheets.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to see the button when turned on, not visible when turned off, with only view access, the button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        await page.goto("/users", { waitUntil: "networkidle" });
        // on
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Isabel I (<EMAIL>)");
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        const managePermission1 = page.getByRole("menuitem", {
          name: "Manage permission",
        });
        await expect(managePermission1).toBeVisible();
        await dsV2MPage.exitLoginAsUser();
        // off
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Alice A (<EMAIL>)");
        await page.goto("/datasheet");
        const managePermission2 = await page.getByText(
          `Sorry, you’re not authorized to access this page.`
        );
        //   `span:has-text("Sorry, you're not authorized to access this page.")` // use getByText-> exact
        // );
        // const manage_permission_2 = page.locator(
        //     `span:has-text("Sorry, you're not authorized to access this page.")` // use getByText-> exact
        //   );
        await expect(managePermission2).toBeVisible();
        await dsV2MPage.exitLoginAsUser();
        // view only
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Henry H (<EMAIL>)");
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        const managePermission3 = page.getByRole("menuitem", {
          name: "Manage permission",
        });
        await expect(managePermission3).toBeHidden();
        await dsV2MPage.exitLoginAsUser();
      }
    );

    test(
      "Validate column visibility and selection in hide columns section",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9541, INTER-T9542, INTER-T9543",
          },
          {
            type: "Description",
            description:
              "Validate user is able to hide all columns, hide 'Primary key' column, and select specific columns in the hide columns section.",
          },
          {
            type: "Precondition",
            description: "Datasheet with columns must exist.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to hide all columns, hide 'Primary key' column, and select specific columns successfully in the hide columns section.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await page
          .locator(
            '//div[@data-testid="ever-select" and .//span[text()="Select by users and groups..."]]'
          )
          .last()
          .click();
        await page.getByPlaceholder("Search").last().click();
        await page.getByPlaceholder("Search").last().fill("<EMAIL>");
        await page.locator(`li:has-text("<EMAIL>")`).click();
        await page.click("body", { position: { x: 0, y: 0 } });
        await page
          .locator(
            `//div[@data-testid="ever-select" and .//span[text()="Select columns to hide"]]`
          )
          .click();
        await page.locator(`span[title="ID"]`).click();
        await page.locator('span[title="Name"]').click();
        await page.locator('span[title="Email"]').click();
        await page.locator('span[title="Client Name"]').click();
        await page.locator('span[title="Sale Amount"]').click();
        await page.locator('span[title="Commission percentage"]').click();
        await page.locator('span[title="commission amount"]').click();
        await page.locator('span[title="Sale Date"]').click();
        await page.locator('span[title="Payment Date"]').click();
        await page.locator('span[title="Deal Status"]').click();
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        await page.goto("/users", { waitUntil: "networkidle" });
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Henry H (<EMAIL>)");
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1");
        await dsV2Page.isColumnNotPresent("ID");
        await dsV2Page.isColumnNotPresent("Name");
        await dsV2Page.isColumnNotPresent("Email");
        await dsV2Page.isColumnNotPresent("Client Name");
        await dsV2Page.isColumnNotPresent("Sale Amount");
        await dsV2Page.isColumnNotPresent("Commission Percentage");
        await dsV2Page.isColumnNotPresent("commission amount");
        await dsV2Page.isColumnNotPresent("Sale Date");
        await dsV2Page.isColumnNotPresent("Payment Date");
        await dsV2Page.isColumnNotPresent("Deal Status");
        await dsV2MPage.exitLoginAsUser();
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );

    test(
      "Validate 'manage permissions' restrictions is not applied for users with 'EDIT ACCESS' and applied for user with EDIT ACCESS",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9546, INTER_T9547",
          },
          {
            type: "Description",
            description:
              "Validate 'manage permissions' restrictions are not applied for users with 'EDIT ACCESS' and applied for user with EDIT ACCESS.",
          },
          {
            type: "Precondition",
            description:
              "Ensure multiple users with differing access levels exist.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Users with 'EDIT ACCESS' should be able to manage permissions without restrictions and not be able to access with EDIT ACCESS permission.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          true
        );
        await page
          .getByRole("button", { name: "Create New Permission Set (1)" })
          .click();
        await dsV2MPage.addPermissionSet(
          1,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        const promptOne = page.locator(
          "text=Datasheet permission saved successfully"
        );
        await expect(promptOne).toBeVisible({ timeout: 10000 });
        await page.goto("/users", { waitUntil: "networkidle" });
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Henry H (<EMAIL>)");
        await page.waitForLoadState("networkidle");
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.isColumnNotPresent("ID");
        await dsV2MPage.exitLoginAsUser();
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Isabel I (<EMAIL>)");
        await page.waitForLoadState("networkidle");
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.isColumnPresent("ID");
        await dsV2MPage.exitLoginAsUser();
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.deletePermissionSet(0);
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );

    test(
      "Validate user actions in permission sets and list selections",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9544, INTER-T9545",
          },
          {
            type: "Description",
            description:
              "Validate user is able to add N+1 permission sets and select - All users from user list, All groups from group list.",
          },
          {
            type: "Precondition",
            description:
              "At least one permission set should exist in the system.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to successfully add N+1 permission sets and select all users from the user list and all groups from the group list.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          true
        );
        await page
          .getByRole("button", { name: "Create New Permission Set (1)" })
          .click();
        await dsV2MPage.addPermissionSet(
          1,
          "Groups",
          "All Payees",
          "Name",
          false
        );
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        const promptOne = page.locator(
          "text=Datasheet permission saved successfully"
        );
        await expect(promptOne).toBeVisible({ timeout: 10000 });
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.deletePermissionSet(0);
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );

    test(
      "Validate permission set management operations",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9555, INTER-T9556, INTER-T9557, INTER-9568",
          },
          {
            type: "Description",
            description:
              "Validate user is able to Clone permission set,Delete permission set by self and other",
          },
          {
            type: "Precondition",
            description: "Have Permission conditions",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to successfully clone and delete permission sets.by self and other, Rename Permission Set",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);

        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        const promptOne = page.locator(
          "text=Datasheet permission saved successfully"
        );
        await expect(promptOne).toBeVisible({ timeout: 10000 });

        await page.goto("/users", { waitUntil: "networkidle" });
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Isabel I (<EMAIL>)");
        await page.waitForLoadState("networkidle");
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.clonePermissionSet(0);
        await expect(
          page.getByRole("button", { name: "Create New Permission Set (1)" })
        ).toBeVisible();

        await dsV2MPage.deletePermissionSet(1);
        await expect(
          page.getByRole("button", { name: "Create New Permission Set (1)" })
        ).toBeHidden();
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        const promptTwo = page
          .locator("text=Datasheet permission saved successfully")
          .first();
        await expect(promptTwo).toBeVisible({ timeout: 10000 });

        await dsV2MPage.exitLoginAsUser();
        await page.goto("/datasheet");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.clonePermissionSet(0);
        await expect(
          page.getByRole("button", { name: "Create New Permission Set (1)" })
        ).toBeVisible();
        await dsV2MPage.deletePermissionSet(1);
        await dsV2MPage.deletePermissionSet(0);
        await expect(
          page.getByRole("button", { name: "Create New Permission Set (1)" })
        ).toBeHidden();
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();

        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await dsV2MPage.editPermissionName(0, "seta");
        await page.getByText(`Save Permissions`, { exact: true }).click();
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await expect(page.getByText("seta")).toBeTruthy();
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        await page.waitForTimeout(1000);
      }
    );

    test(
      "Validate 'Manage Permission' visibility for restricted custom object",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9565",
          },
          {
            type: "Description",
            description:
              'Validate if a custom object is restricted, the "Manage Permission" option should not be displayed for that datasheet.',
          },
          {
            type: "Precondition",
            description:
              "A custom object must be created and restricted for specific users or groups before validating the visibility of the 'Manage Permission' option.",
          },
          {
            type: "Expected Behaviour",
            description:
              'The "Manage Permission" option should not be displayed for datasheets linked to the restricted custom object.',
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);

        await page.goto("/objects", { waitUntil: "networkidle" });
        await page.locator(`span:has-text("New Object 1")`).click();
        await page.getByText("Manage Permissions").click();
        await page.locator(`//span[text()="Exclude certain users"]`).click();
        await page
          .locator(
            '//div[@data-testid="ever-select" and .//span[text()="Select by users and groups..."]]'
          )
          .last()
          .click();
        await page.locator(`//span[text()="<EMAIL>"]`).click();
        await page.click("body", { position: { x: 0, y: 0 } });
        await page.getByText("Save").click();
        const promptOne = page.locator(
          "text=Object permissions saved successfully"
        );
        await expect(promptOne).toBeVisible({ timeout: 10000 });
        await page.waitForTimeout(2000);
        await page.goto("/users", { waitUntil: "networkidle" });
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Henary H (<EMAIL>)");
        await page.waitForLoadState("networkidle");
        await page.goto(`/datasheet`);
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1");
        await expect(
          page.getByText(`You have limited access to data in this sheet`)
        ).toBeVisible();
        await expect(
          page.getByText(
            `Please reach out to your administrator for more details.`
          )
        ).toBeVisible();
        await expect(
          page.getByText(
            "User <EMAIL> doesn't have permission to all the custom objects used in the datasheet permission"
          )
        ).toBeVisible();
        await dsV2MPage.exitLoginAsUser();
        async function resetAllPermissions() {
          await page.locator(`span:has-text("New Object 1")`).click();
          await page.getByText("Manage Permissions").click();
          await page.locator(`//span[text()="Everyone can access"]`).click();
          await page.getByText("Save").click();
          const promptTwo = page.locator(
            "text=Object permissions saved successfully"
          );
          await expect(promptTwo).toBeVisible({ timeout: 10000 });
        }
        try {
          await page.goto("/objects", { waitUntil: "networkidle" });
          await resetAllPermissions();
        } catch {
          await page.goto("/objects", { waitUntil: "networkidle" });
          await page.reload();
          await resetAllPermissions();
        }
      }
    );

    test(
      "Validate permission data and rules behavior across datasheets",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9538, INTER-T9552, INTER-T9554",
          },
          {
            type: "Description",
            description:
              "Ensure permission data is not retained in cloned datasheets, rules in one sheet do not affect others, and hidden columns in D0 are visible in D1 for users with edit access.",
          },
          {
            type: "Precondition",
            description:
              "Datasheets with permissions must exist. Multiple datasheets should be present in the same databook. Datasheet D0 should have hidden columns.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Cloned datasheets should not retain permissions, rules should not propagate between datasheets, and hidden columns in D0 should display in D1 for edit access users.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);

        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        await dsV2Page.dialogPrompt(
          "span",
          "Datasheet permission saved successfully"
        );
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.cloneDatasheet();
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1_Copy");
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        await page.goto("/users", { waitUntil: "networkidle" });
        await dsV2MPage.loginAsUser("<EMAIL>");
        await dsV2MPage.validateImpersonation("Henry H (<EMAIL>)");
        await page.waitForLoadState("networkidle");
        await page.goto("/datasheet");
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1");
        await dsV2Page.letColumnsLoad("Name");
        await expect(
          await page.getByText("You have limited access to data in this sheet")
        ).toBeVisible();
        await dsV2Page.isColumnNotPresent("ID");
        // cloned and sheet in same book
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1_Copy");
        await dsV2Page.letColumnsLoad("Name");
        await dsV2Page.isColumnPresent("ID");
        // dependency
        await dsV2Page.goToDatasheet("DataBook", "Datasheet 2");
        await dsV2Page.letColumnsLoad("Name");
        await dsV2Page.isColumnPresent("ID");
        await dsV2MPage.exitLoginAsUser();
        await page.goto("/datasheet");
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1_Copy");
        await dsV2Page.deleteDatasheet();
        await dsV2Page.goToDatasheet("DataBook", "DataSheet 1");
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        await page.waitForTimeout(3000);
      }
    );

    test(
      "Validate Row Permissions functionality",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9560, INTER-T9561, INTER-T9562, INTER-T9563",
          },
          {
            type: "Description",
            description:
              "Validate adding multiple row permissions, using all operators, discarding row permissions, and disabling the 'Value' field for 'Is Empty' and 'Is not empty' operators.",
          },
          {
            type: "Precondition",
            description:
              "User should have 'Manage Permissions' access and a datasheet with rows of various data types.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User can add, use, and discard row permissions correctly, and 'Value' field disables for specified operators.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);

        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );

        await page
          .locator(`span:has-text("Add row permission")`)
          .nth(0)
          .click();
        await page
          .locator(`div[data-testid="ever-select"]:has(span:text-is("Column"))`)
          .click();
        await page.locator('div[title="ID"]').last().click();
        await page
          .locator(
            `div[data-testid="ever-select"]:has(span:text-is("Operator"))`
          )
          .click();
        await page.locator('div[title="Is Empty"]').last().click();

        await page
          .locator(`span:has-text("Add row permission")`)
          .nth(0)
          .click();
        await page
          .locator(`div[data-testid="ever-select"]:has(span:text-is("Column"))`)
          .click();
        await page.locator('div[title="Name"]').last().click();
        await page
          .locator(
            `div[data-testid="ever-select"]:has(span:text-is("Operator"))`
          )
          .click();
        await page.locator('div[title="Is Not Empty"]').last().click();

        await expect(
          await page.locator(
            'div[data-testid="ever-select"]:has(span:has-text("Compare With"))'
          )
        ).toBeHidden();
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        const promptOne = page.locator(
          "text=Datasheet permission saved successfully"
        );
        await expect(promptOne).toBeVisible({ timeout: 10000 });
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await page
          .locator(`div[class="flex self-center justify-center w-14"]`)
          .nth(1)
          .click();
        await expect(
          await page.locator(
            `div[data-testid="ever-select"]:has(span:has-text("Name"))`
          )
        ).toBeHidden();
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );

    test(
      "Validate operators for various data types",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T9561, INTER-T9624, INTER-T9625, INTER-T9626, INTER-T9627, INTER-T9628, INTER-T9629, INTER-T9570",
          },
          {
            type: "Description",
            description:
              "Validate Row Permissions and operators for various data types, including date, boolean, integer, percentage, email, string, and date operators (both relative and absolute).",
          },
          {
            type: "Precondition",
            description:
              "Ensure the necessary data types and row permissions are set up in the application.",
          },
          {
            type: "Expected Behaviour",
            description:
              "All operators should work correctly for respective data types, including relative and absolute date operators.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        const dateOperators = [
          "Is After",
          "Is Before",
          "Is Empty",
          "Is Not Empty",
          "Is on or After",
          "Is on or Before",
          "Is in Current Fiscal Halfyear",
          "Is in Current Fiscal Quarter",
          "Is in Current Fiscal year",
          "Is in Current Month",
          "Is in Last Fiscal Halfyear",
          "Is in Last Fiscal Quarter",
          "Is in Last Fiscal year",
          "Is in Last Month",
        ];
        const percentageOperator = [
          "Equal To",
          "Greater Than",
          "Greater than or Equal to",
          "Is Empty",
          "Is Not Empty",
          "Less Than",
          "Less than or Equal to",
          "Not Equal To",
        ];
        const emailOperator = [
          "Belongs To",
          "Contains",
          "Does not Contain",
          "Ends With",
          "Equal To",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
          "Starts With",
        ];
        const booleanOperator = [
          "Equal To",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
        ];
        const nameOperator = [
          "Contains",
          "Does not Contain",
          "Ends With",
          "Equal To",
          "In",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
          "Not In",
          "Starts With",
        ];
        const numberOperators = [
          "Equal To",
          "Greater Than",
          "Greater than or Equal to",
          "In",
          "Is Empty",
          "Is Not Empty",
          "Less Than",
          "Less than or Equal to",
          "Not Equal To",
          "Not In",
        ];
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await page
          .locator(`span:has-text("Add row permission")`)
          .nth(0)
          .click();
        await dsV2MPage.rowPermissionColumn("ID");
        await dsV2MPage.performRowPermissionOperators(numberOperators);
        await dsV2MPage.rowPermissionColumn("Name");
        await dsV2MPage.performRowPermissionOperators(nameOperator);
        await dsV2MPage.rowPermissionColumn("Email");
        await dsV2MPage.performRowPermissionOperators(emailOperator);
        await dsV2MPage.rowPermissionColumn("Commission percentage");
        await dsV2MPage.performRowPermissionOperators(percentageOperator);
        await dsV2MPage.rowPermissionColumn("Deal Status");
        await dsV2MPage.performRowPermissionOperators(booleanOperator);
        await dsV2MPage.rowPermissionColumn("Payment Date");
        await dsV2MPage.performRowPermissionOperators(dateOperators);
        await page.waitForTimeout(1000);
        await page
          .locator(
            `div[data-testid="ever-select"]:has(span:text-is("Is in Last Month"))`
          )
          .click();
        await page.waitForTimeout(1000);
        await expect(page.getByText("ABSOLUTE")).toBeVisible();
        await expect(page.getByText("RELATIVE")).toBeVisible();
        await page.waitForTimeout(1000);
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );

    test(
      "Validate Column and Permission Set Functionality",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T9566, INTER-T9569",
          },
          {
            type: "Description",
            description:
              "Validate column display including columns with primary key and validate user is able to set a unique name for each permission set.",
          },
          {
            type: "Precondition",
            description:
              "Ensure that columns and permission sets are configured properly.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Columns, including the column with primary key, should display correctly. Users should be able to set unique names for each permission set.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2MPage = new DatasheetV2ManagePermission(page);
        const columnHeaderTexts = await page
          .locator(`//div[@role='columnheader']`)
          .evaluateAll((elements) =>
            elements.map((element) => element.textContent.trim())
          );
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await page
          .locator(`span:has-text("Add row permission")`)
          .nth(0)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        const rowPermissionColumn = await page
          .locator(`.ant-select-item-option-content`)
          .evaluateAll((elements) =>
            elements.map((element) => element.textContent.trim())
          );
        const columnHeaderTextsExceptLast = columnHeaderTexts.slice(0, -1);
        const matchingItems = columnHeaderTextsExceptLast.filter((header) =>
          rowPermissionColumn.includes(header)
        );
        if (matchingItems.length !== columnHeaderTextsExceptLast.length) {
          throw new Error("Some column headers do not match.");
        }
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
        await dsV2Page.clickDatasheetMenu();
        await dsV2Page.managePermissions();
        await dsV2MPage.addPermissionSet(
          0,
          "Users",
          "<EMAIL>",
          "ID",
          true
        );
        await page
          .getByRole("button", { name: "Create New Permission Set (1)" })
          .click();
        await dsV2MPage.addPermissionSet(
          1,
          "Users",
          "<EMAIL>",
          "ID",
          false
        );
        await dsV2MPage.editPermissionName(0, "seta");
        await dsV2MPage.editPermissionName(1, "seta");
        await expect(
          page.getByRole("button", { name: "Save Permissions", exact: true })
        ).toBeDisabled();
        await dsV2MPage.editPermissionName(1, "setb");
        await expect(
          page.getByRole("button", { name: "Save Permissions", exact: true })
        ).toBeEnabled();
        await dsV2MPage.deletePermissionSet(0);
        await dsV2MPage.deletePermissionSet(0);
        await page
          .getByRole("button", { name: "Save Permissions", exact: true })
          .click();
      }
    );
  }
);
