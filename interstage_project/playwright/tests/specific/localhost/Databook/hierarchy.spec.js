const {
  lineitemFixtures: { test },
} = require("../../../fixtures");

test.describe(
  "Hierarchy",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test("Create HF in existing sheet", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Create Hierarchy CF in existing sheet",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 100000);
      await page.goto("/databook/c791bd61-4145-439c-81ee-71e3408278dd", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "remove" }).click();
      await page.getByRole("menuitem", { name: "Edit" }).click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await genHierarchy(
        page,
        "Payee Email",
        "ASD",
        "Hierarchy",
        "Users",
        "User",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });

    test("Create HF in new sheet-CURRENT DATE", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Create Hierarchy CF in new sheet-Current date as date function",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 900000);
      await page.goto("/databook/c791bd61-4145-439c-81ee-71e3408278dd", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("New Sheet");
      await page.locator(".ant-select-selector").click();
      await page.getByTitle("Deals").locator("div").first().click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await genHierarchy(
        page,
        "Payee Email",
        "CURRENT_DATE",
        "Hierarchy",
        "Users",
        "User",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });

    test("Create HF in new sheet-ASD", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Create Hierarchy CF in new sheet-ASD as date function",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 900000);
      await page.goto("/databook/c791bd61-4145-439c-81ee-71e3408278dd", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("New Sheet1");
      await page.locator(".ant-select-selector").click();
      await page.getByTitle("Deals").locator("div").first().click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await genHierarchy(
        page,
        "Payee Email",
        "ASD",
        "Hierarchy",
        "Users",
        "User",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });

    test("Create HF in Self book new sheet-ASD", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Create Hierarchy CF in Same databook sheet-ASD as date function",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 900000);
      await page.goto("/databook/6a326238-8ffe-44e0-9d30-3100cd8bae6e", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("New Sheet2");
      await page.locator(".ant-select-selector").click();
      await page.getByTitle("Deals").locator("div").first().click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await genHierarchy(
        page,
        "Payee Email",
        "ASD",
        "Hierarchy_SELF SHEET",
        "Hierarchy_SELF SHEET",
        "Report",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });

    test("Create HF in Self book current sheet-current date", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Create Hierarchy CF in Same databook sheet-Current date as date function",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 900000);
      await page.goto("/databook/6a326238-8ffe-44e0-9d30-3100cd8bae6e", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("New Sheet3");
      await page.getByLabel("Report Object").check();
      await page
        .locator(".w-fit > .relative > .ant-select > .ant-select-selector")
        .click();
      await page.getByTitle("User").locator("div").first().click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await genHierarchy(
        page,
        "Employee Email Id",
        "CURRENT_DATE",
        "Hierarchy_SELF SHEET",
        "Hierarchy_SELF SHEET",
        "CURRENT_SHEET",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });

    test("Create HF in Self book current sheet-ASD", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Create Hierarchy CF in Same databook with same sheet-ASD as date function",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Datasheet should get generated successfully",
        }
      );
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 900000);
      await page.goto("/databook/6a326238-8ffe-44e0-9d30-3100cd8bae6e", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("New Sheet4");
      await page.getByLabel("Report Object").check();
      await page
        .locator(".w-fit > .relative > .ant-select > .ant-select-selector")
        .click();
      await page.getByTitle("User").locator("div").first().click();
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").press("CapsLock");
      await page.getByPlaceholder("Enter Field Name").fill("HF1");
      await page.getByLabel("Type*").click();
      await page.getByTitle("Hierarchy").getByText("Hierarchy").click();
      await page.waitForTimeout(3000);
      await genHierarchy(
        page,
        "Employee Email Id",
        "Joining Date",
        "Hierarchy_SELF SHEET",
        "Hierarchy_SELF SHEET",
        "CURRENT_SHEET",
        "Employee Email Id",
        "Reporting Manager EmailId",
        "Effective Start Date",
        "Effective End Date"
      );
      await page.getByRole("button", { name: "Add Formula Field" }).click();
      await page.getByPlaceholder("Enter Field Name").click();
      await page.getByPlaceholder("Enter Field Name").fill("Node");
      await page.click("//input[@id='dataType']/..");
      await page.getByTitle("Email").locator("div").first().click();
      await getTopValueFromHierarchy(page, "HF1");
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Datasheet saved successfully").isVisible();
      await generateDatasheet(page);
    });
  }
);
// hierarchy
async function genHierarchy(
  page,
  columnToGenerate,
  forPeriod,
  defaultBook,
  joinWithBook,
  joinWithSheet,
  parentColumn,
  joinOnColumn,
  effecStartDate,
  effecEndDate
) {
  await page.getByPlaceholder("Press Ctrl + H for help").click();
  await page.getByPlaceholder("Press Ctrl + H for help").fill("GEN");
  await page.getByText("GenerateHierarchy").click();
  await page
    .locator('input:below(:text("Column to generate hierarchy for"))')
    .first()
    .click();
  await page.getByTitle(columnToGenerate).locator("div").first().click();
  await page.locator('input:below(:text("For Period"))').first().click();
  await page.getByTitle(forPeriod).locator("div").first().click();
  await page.locator("#radix-1").getByTitle(defaultBook).click();
  await page.getByTitle(joinWithBook).locator("div").first().click();
  await page.click("//span[text()='Select Datasheet']/..");
  await page
    .getByRole("listitem")
    .getByText(joinWithSheet, { exact: true })
    .click();
  await page.locator('input:below(:text("Join on column"))').first().click();
  await page.getByTitle(joinOnColumn).locator("div").last().click();
  await page.locator('input:below(:text("Parent column"))').first().click();
  await page.getByText(parentColumn, { exact: true }).last().click();
  await page
    .locator('input:below(:text("Period effective start date"))')
    .first()
    .click();
  await page
    .getByTitle(effecStartDate)
    .getByText(effecStartDate)
    .last()
    .click();
  await page
    .locator('input:below(:text("Period effective end date"))')
    .first()
    .click();
  await page
    .getByTitle(effecEndDate)
    .getByText(effecEndDate, { exact: true })
    .last()
    .click();
  await page.getByRole("button", { name: "Apply" }).click();
  await page.getByRole("button", { name: "Create" }).click();
}
// node
async function getTopValueFromHierarchy(page, hierarchyField) {
  await page.getByPlaceholder("Press Ctrl + H for help").click();
  await page.getByPlaceholder("Press Ctrl + H for help").fill("get");
  await page.getByText("GetValueFromHierarchy").click();
  await page.click("//span[text()='Hierarchy Field']/..");
  await page.getByTitle(hierarchyField).locator("div").first().click();
  await page.getByRole("switch").click();
  await page.getByRole("button", { name: "Apply" }).click();
  await page.getByRole("button", { name: "Create" }).click();
}

async function generateDatasheet(page) {
  await page
    .getByRole("button", { name: "Generate Datasheet" })
    .first()
    .click();
  await page
    .getByText("Datasheet sync request has been submitted")
    .waitFor({ state: "visible" });
  const generateText = await page.getByText(
    "This datasheet is currently being generated"
  );
  await generateText.waitFor({ state: "visible", timeout: 5000 });
  await page
    .getByText("Datasheet has been generated successfully")
    .waitFor({ state: "visible", timeout: 1500000 });
}
