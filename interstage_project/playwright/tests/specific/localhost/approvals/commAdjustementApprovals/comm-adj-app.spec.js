const {
  commAdjAppFixtures: { test, expect },
  commAdjAppFixtures2: { test: test2, expect: expect2 },
  commAdjAppFixtures3: { test: test3, expect: expect3 },
} = require("../../../../fixtures");

test.describe(
  "commission adjustment app test",
  {
    tag: [
      "@approvals",
      "@regression",
      "@commissionAdjustments",
      "@adminchamp-2",
    ],
  },
  () => {
    test("Lock Payouts while pending adjustment approval", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "January-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("<EMAIL>-actions-dd").click();
      await page.getByRole("menuitem", { name: "Lock Statements" }).click();
      await expect(
        page.getByText(
          "The selected statement has commission adjustments that need to be approved."
        )
      ).toBeVisible();
    });

    test("Edit Cancelled approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );

      await page.getByTestId("pt-action-button-Cancelled Approval").click();
      await page.getByRole("menuitem", { name: "Edit" }).click();
      await page.getByLabel("Reason Category").click();
      await page.getByPlaceholder("Enter a description...").click();
      await page.getByPlaceholder("Enter a description...").fill("Edit");
      await page.getByText("WorkflowsComm Adj WF").click();
      await page
        .locator("div")
        .filter({ hasText: /^Comm Adj WF$/ })
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        page.getByText("Adjustment updated successfully")
      ).toBeVisible();
    });

    test("Delete Cancelled approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("pt-action-button-Cancelled Approval 1").click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByRole("button", { name: "Yes" }).click();
      await expect(
        page.getByText("Adjustment deleted successfully")
      ).toBeVisible();
    });

    test("Edit Approved approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("pt-action-button-Approved Approval").click();
      await page.getByRole("menuitem", { name: "Edit" }).click();
      await page.getByLabel("Reason Category").click();
      await page.getByPlaceholder("Enter a description...").click();
      await page.getByPlaceholder("Enter a description...").fill("Edit");
      await page
        .locator("div")
        .filter({ hasText: /^Comm Adj WF$/ })
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        page.getByText("Adjustment updated successfully")
      ).toBeVisible();
    });

    test("Delete Approved approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("pt-action-button-Approved Approval 1").click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByRole("button", { name: "Yes" }).click();
      await expect(
        page.getByText("Adjustment deleted successfully")
      ).toBeVisible();
    });

    test.describe("Requested Approvals", () => {
      test("Edit Requested approvals", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Requested Approval").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByText("Comm Adj WF").click();
        await page.getByPlaceholder("Enter a description...").click();
        await page.getByPlaceholder("Enter a description...").fill("Edit");

        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          page.getByText("Adjustment updated successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test("Delete Requested approvals", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Requested Approval").click();
        await page.getByRole("menuitem", { name: "Delete" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await expect(
          page.getByText("Adjustment deleted successfully")
        ).toBeVisible();
      });
    });

    test("Approve Reject adjustment approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
        {
          waitUntil: "networkidle",
        }
      );

      await page.getByTestId("Pending pending status").click();
      await expect(page.getByRole("tab", { name: "Timeline" })).toBeVisible();
      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Approve" }).nth(1)
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Reject" }).nth(1)
      ).toBeVisible();
      await expect(page.getByLabel("Timeline")).toContainText("Yet to approve");
    });

    test("Withdraw adjustment approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("Withdraw pending status").click();
      await page
        .locator("div")
        .filter({ hasText: /^Stage #1$/ })
        .nth(1)
        .click();
      await page.getByLabel("Timeline").locator("svg").click();
      await page
        .getByRole("menuitem", { name: "Withdraw pending requests" })
        .getByRole("listitem")
        .click();
      await page.getByRole("button", { name: "Withdraw Request" }).click();
      await expect(page.getByText("Request withdrawn")).toBeVisible();
    });

    test.describe("Approve adjustment approvals ", () => {
      test("Approve adjustment approvals action", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await page.getByTestId("Approve approve button").click();
        await page.getByRole("button", { name: "Yes, approve" }).click();
        await expect(
          page.getByText("You've approved the request!")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test("Approve adjustment approvals check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key = "commission-view-period";
        const value = "January-2024";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹10,011.00");
      });
    });

    test.describe("Reject adjustment approvals ", () => {
      test("Reject adjustment approvals adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await page.getByTestId("Reject reject button").click();
        await page.getByPlaceholder("Add your comments").click();
        await page.getByPlaceholder("Add your comments").fill("Reject");
        await page.getByRole("button", { name: "Reject Request" }).click();
        await expect(
          page.getByText("You've rejected the request.")
        ).toBeVisible();
      });

      test("Reject adjustment approvals check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key = "commission-view-period";
        const value = "January-2024";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹20,000.00");
      });
    });

    test("Pending adjustment approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );

      await page.getByTestId("Pending arrow icon").click();
      await expect(page.getByText("Track Progress")).toBeVisible();
      await expect(page.getByRole("tab", { name: "Timeline" })).toBeVisible();
      await expect(page.getByLabel("Timeline")).toContainText("Yet to approve");
      await page.getByLabel("Close").click();
    });

    test("Auto approve approvals", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/settings/adjustments?period=all&status=all",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("Auto Approval arrow icon").click();
      await expect(page.getByLabel("Timeline")).toContainText(
        "As the adjustment amount is within the threshold range."
      );
      await expect(page.getByLabel("Timeline")).toContainText("Auto Approved");
      await page.getByLabel("Close").click();
    });

    test.describe("Without Skip", () => {
      test("Without Skip adj", async ({ adminPage }) => {
        const page = adminPage.page;

        // Raise request Without Skip
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page.getByRole("menuitem", { name: "Commission" }).click();
        await page.getByLabel("Payee*").click();
        await page.getByTitle("RBV R").nth(1).click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("August 2024").click();
        await expect(
          page.getByLabel("Add Adjustment").getByText("INR")
        ).toBeVisible();
        await page.getByText("Comm Adj WF").click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("21");

        await page.getByPlaceholder("Enter a description...").click();
        await page
          .getByPlaceholder("Enter a description...")
          .fill("Without Skip Test");

        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test("Without Skip check", async ({ adminPage }) => {
        const page = adminPage.page;

        // Raise request Without Skip
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Without Skip Test arrow icon").click();
        await expect(page.getByText("Approval Initiated")).toBeVisible();
        await expect(page.getByLabel("Timeline")).toContainText(
          "Yet to approve"
        );
      });
    });

    test.describe("Create and Skip", () => {
      test("Create and Skip adj", async ({ adminPage }) => {
        const page = adminPage.page;

        // Raise request Without Skip
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page.getByRole("menuitem", { name: "Commission" }).click();
        await page.getByLabel("Payee*").click();
        await page.getByTitle("RBV R").nth(1).click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("December 2023").click();
        await expect(
          page.getByLabel("Add Adjustment").getByText("INR")
        ).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("51");
        await page.getByPlaceholder("Enter a description...").click();
        await expect(
          page.getByRole("button", { name: "Create Custom Workflow" })
        ).toBeEnabled();
        await page
          .getByRole("button", { name: "Create Custom Workflow" })
          .click();
        await page.getByPlaceholder("Choose who needs to be").first().click();
        await page.getByRole("tab", { name: "Users" }).click();
        await page.getByText("ARAdmin <EMAIL>").click();
        await page.getByText("Notify when Rejected*").click();
        await page.getByPlaceholder("Choose approvers").click();
        await page.getByRole("tab", { name: "Users" }).click();
        await page.getByText("ARAdmin <EMAIL>").click();
        await page.getByText("Configure approvers, due date").click();
        await page.getByRole("button", { name: "Save as template" }).click();

        await page.getByPlaceholder("Enter workflow name").click();
        await page.getByPlaceholder("Enter workflow name").fill("Test Created");
        await page
          .getByLabel("Request Approval")
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Enter module name" })
          .click();
        await page.getByText("Commission Adjustments", { exact: true }).click();
        await page
          .getByRole("button", { name: "Save as Template", exact: true })
          .click();
        await expect(
          page.getByText("Workflow created successfully.")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");

        await page.getByPlaceholder("Enter a description...").click();
        await page
          .getByPlaceholder("Enter a description...")
          .fill("Create Workflow - Skip");
        await page.getByText("Test Created").click();
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Skipping");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test("Create and Skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Create Workflow - Skip arrow icon").click();
        await expect(
          page.getByText("The adjustment was skipped for approval")
        ).toBeVisible();

        const key = "commission-view-period";
        const value = "December-2023";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹51.00");
      });
    });

    test.describe("Auto Approve", () => {
      test("Auto Approve adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page.getByRole("menuitem", { name: "Commission" }).click();
        await page.getByLabel("Payee*").click();
        await page.getByTitle("RBV R").nth(1).click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("August 2024").click();
        await expect(page.getByLabel("Effective Period*")).toBeVisible();
        await expect(
          page.getByLabel("Add Adjustment").getByText("INR")
        ).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("5");
        await page.getByText("Auto Approved").click();
        await page
          .getByPlaceholder("Enter a description...")
          .fill("Auto March");
        await expect(
          page.getByRole("button", { name: "Submit" })
        ).toBeEnabled();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      // View Statements
      test("Auto Approve check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyYnZAZXZlLmNvbSIsInBzZCI6IjIwMjQtMDgtMDEiLCJwZWQiOiIyMDI0LTA4LTMxIn0=",
          { waitUntil: "networkidle" }
        );

        await expect(page.getByTestId("login-indicator")).toContainText(
          "₹5.00"
        );

        const key1 = "commission-view-period";
        const value1 = "August-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });

        await expect(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹5.00");
      });
    });

    test.describe("Disable Edit and Delete", () => {
      test("Editing and Deleting Disabled 1", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "September-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await page.getByTestId("<EMAIL>-actions-dd").click();
        await page
          .getByRole("menuitem", { name: "Lock Statements", exact: true })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByText("Lock status updated")
          .waitFor({ state: "visible", timeout: 30000 });
        await page.waitForLoadState("networkidle");
      });

      test("Editing and Deleting Disabled 2", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Approve September").click();
        await page
          .getByRole("menuitem", { name: "Edit" })
          .locator("span")
          .hover();
        await expect(
          page.getByText("Editing disabled since the statement is locked.")
        ).toBeVisible();
        await page.getByText("Delete").hover();
        await expect(
          page.getByText("Deleting disabled since the statement is locked.")
        ).toBeVisible();
      });
    });

    // Delete and Edit user
    test("Delete user in plan", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/plans", {
        waitUntil: "networkidle",
      });
      await page.getByText("Plan", { exact: true }).click();
      await page.getByTestId("<EMAIL> delete user icon").click();
      await expect(
        page.getByRole("treegrid").getByText("Active Commission adjustments")
      ).toBeVisible();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Validation failed")).toBeVisible();
    });

    test("Delete plan", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/plans", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("pt-actions-Plan").click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await expect(
        page.getByText("This Commission plan can't be")
      ).toBeVisible();
      await expect(page.getByRole("dialog")).toContainText(
        "• Certain commission adjustments here associated with this plan need to be removed before the plan can be deleted. (Review here)"
      );
      await page.getByRole("link", { name: "here" }).click();
      const currentUrl = await page.url();
      const expectedUrl =
        "http://localhost:3000/settings/adjustments?status=requested&planId=66d0718c-1b6f-46c9-aaea-0a9dcbcde272";
      await expect(currentUrl).toBe(expectedUrl);
    });

    test("Edit Payee Period", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/plans", {
        waitUntil: "networkidle",
      });

      await page.getByText("Plan", { exact: true }).click();
      await page
        .locator(
          ".ag-row-odd > div:nth-child(2) > .w-full > .flex > .cursor-pointer > .w-4"
        )
        .click();
      await page.getByPlaceholder("Start date").click();
      await page
        .locator(
          "div:nth-child(2) > .ant-picker-date-panel > .ant-picker-header > .ant-picker-header-next-btn"
        )
        .click();
      await page
        .locator(
          "div:nth-child(2) > .ant-picker-date-panel > .ant-picker-header > .ant-picker-header-next-btn"
        )
        .click();
      await page
        .locator(
          "div:nth-child(2) > .ant-picker-date-panel > .ant-picker-header > .ant-picker-header-next-btn"
        )
        .click();
      await page
        .locator(
          "div:nth-child(2) > .ant-picker-date-panel > .ant-picker-header > .ant-picker-header-next-btn"
        )
        .click();
      await page.getByTitle("-04-01").locator("div").click();
      await expect(
        page.getByText("Active Commission adjustments")
      ).toBeVisible();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Validation failed")).toBeVisible();
    });
  }
);

test2.describe(
  "commission adjustment app test2",
  { tag: ["@regression", "@comm-adj-app"] },
  () => {
    // Set Allow Adjustments To Locked Commission before running the following (Club with client 2)
    test2.describe("Reject with skip", () => {
      test2("Reject with skip adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Reject with skip").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Reject with skip");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test2("Reject with skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Reject with skip arrow icon").click();
        await expect2(
          page.getByText("The adjustment was skipped")
        ).toBeVisible();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Reject with skip"
        );
        await page.getByLabel("Close").click();
        const key1 = "commission-view-period";
        const value1 = "May-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹91.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAY29tbWFkai5jb20iLCJwc2QiOiIyMDI0LTA1LTAxIiwicGVkIjoiMjAyNC0wNS0zMSJ9",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "₹91.00"
        );
      });
    });

    test2.describe("Reject with auto", () => {
      test2("Reject with auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        // Reject with auto
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Reject with auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await expect2(page.getByTitle("INR")).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("2.000000");
        await page.waitForLoadState("networkidle");
        await page
          .locator("div")
          .filter({
            hasText:
              /^Auto ApprovedAs the adjustment amount is lesser than the threshold value\.$/,
          })
          .first()
          .click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Reject with auto verify", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Reject with auto arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await page.getByLabel("Close").click();
        const key1 = "commission-view-period";
        const value1 = "June-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹2.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAY29tbWFkai5jb20iLCJwc2QiOiIyMDI0LTA2LTAxIiwicGVkIjoiMjAyNC0wNi0zMCJ9",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "2.00"
        );
      });
    });

    test2.describe("Reject without auto", () => {
      test2("Reject without auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        // Reject without auto
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("pt-action-button-Reject without auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("193.000000");
        await page.getByText("Comm Adj WF").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Reject without auto verify", async ({ adminPage }) => {
        const page = adminPage.page;
        // Reject without auto
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("Reject without auto arrow icon").click();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Yet to approve"
        );

        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(
          page.getByTestId("Reject without auto pending status")
        ).toBeVisible();
      });
    });

    test2.describe("Approve with skip", () => {
      test2("Approve with skip adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Approve with skip").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("1.000000");
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Approve with skip");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Approve with skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "May-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );

        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });

        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹1.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJhcHByb3ZlQGNvbW1hZGouY29tIiwicHNkIjoiMjAyNC0wNS0wMSIsInBlZCI6IjIwMjQtMDUtMzEifQ==",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹1.00"
        );
      });
    });

    test2.describe("Approve with auto", () => {
      test2("Approve with auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Approve with auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await expect2(page.getByTitle("INR")).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("2.000000");
        await page
          .locator("div")
          .filter({
            hasText:
              /^Auto ApprovedAs the adjustment amount is lesser than the threshold value\.$/,
          })
          .first()
          .click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Approve with auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "June-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );

        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹2.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJhcHByb3ZlQGNvbW1hZGouY29tIiwicHNkIjoiMjAyNC0wNi0wMSIsInBlZCI6IjIwMjQtMDYtMzAifQ==",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹2.00"
        );
      });
    });

    test2.describe("Approve without auto", () => {
      test2("Approve without auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "July-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹103.00");
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Approve without auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("113.000000");
        await page.getByText("Comm Adj WF").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test2("Approve without auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹0.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJhcHByb3ZlQGNvbW1hZGouY29tIiwicHNkIjoiMjAyNC0wNy0wMSIsInBlZCI6IjIwMjQtMDctMzEifQ==",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹0.00"
        );
      });
    });

    test2.describe("Pending with skip", () => {
      test2("Pending with skip adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Pending with skip").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("221.000000");
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Pending with skip");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(
          page.getByText("Adjustment updated successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test2("Pending with skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByTestId("Pending with skip arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Pending with skip"
        );
        await page.getByLabel("Close").click();

        const key1 = "commission-view-period";
        const value1 = "May-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });

        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹221.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyYnZAZXZlLmNvbSIsInBzZCI6IjIwMjQtMDUtMDEiLCJwZWQiOiIyMDI0LTA1LTMxIn0=",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹221.00"
        );
      });
    });

    test2.describe("Pending with auto", () => {
      test2("Pending with auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Pending with auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await expect2(page.getByTitle("INR")).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("1.000000");
        await page
          .locator("div")
          .filter({
            hasText:
              /^Auto ApprovedAs the adjustment amount is lesser than the threshold value\.$/,
          })
          .first()
          .click();
        await page.waitForLoadState("networkidle");
        await expect2(
          page.getByRole("button", { name: "Submit" })
        ).toBeEnabled();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.getByTestId("Pending with auto arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "As the adjustment amount is within the threshold range."
        );
        await page.waitForLoadState("networkidle");
      });

      test2("Pending with auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "June-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹1.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyYnZAZXZlLmNvbSIsInBzZCI6IjIwMjQtMDYtMDEiLCJwZWQiOiIyMDI0LTA2LTMwIn0=",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹1.00"
        );
      });
    });

    test2.describe("Pending without auto", () => {
      test2("Pending without auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("pt-action-button-Pending without auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("223.000000");
        await page.getByText("Comm Adj WF").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Pending without auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("Pending without auto arrow icon").click();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Yet to approve"
        );

        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(
          page.getByTestId("Pending without auto pending status")
        ).toBeVisible();
      });
    });

    test2.describe("Withdraw without skip", () => {
      test2("Withdraw with skip adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        // await page
        //   .locator("div")
        //   .filter({ hasText: /^1 - 20of/ })
        //   .getByRole("button")
        //   .nth(2)
        //   .click();
        await page.getByTestId("pt-action-button-Withdraw with skip").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByText("Comm Adj WF").click();
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Withdraw with skip");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.getByTestId("Withdraw with skip arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Withdraw with skip"
        );
      });

      test2("Withdraw with skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        const key1 = "commission-view-period";
        const value1 = "August-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });

        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹131.00");

        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAY29tbWFkai5jb20iLCJwc2QiOiIyMDI0LTA4LTAxIiwicGVkIjoiMjAyNC0wOC0zMSJ9",
          {
            waitUntil: "networkidle",
          }
        );

        await expect(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹131.00"
        );
      });
    });

    test2.describe("Withdraw with auto", () => {
      test2("Withdraw with auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Withdraw with auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await expect2(page.getByTitle("INR")).toBeVisible();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("2.000000");
        await page
          .locator("div")
          .filter({
            hasText:
              /^Auto ApprovedAs the adjustment amount is lesser than the threshold value\.$/,
          })
          .first()
          .click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Withdraw with auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Withdraw with auto arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "As the adjustment amount is within the threshold range."
        );
        const key1 = "commission-view-period";
        const value1 = "September-2024";

        await page.evaluate(
          ({ key1, value1 }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key1, value1);
          },
          { key1, value1 }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹2.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAY29tbWFkai5jb20iLCJwc2QiOiIyMDI0LTA5LTAxIiwicGVkIjoiMjAyNC0wOS0zMCJ9",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹2.00"
        );
      });
    });

    test2.describe("Withdraw without auto", () => {
      test2("Withdraw without auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page
          .getByTestId("pt-action-button-Withdraw without auto")
          .click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("134.000000");
        await page.getByText("Comm Adj WF").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test2("Withdraw without auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Withdraw without auto arrow icon").click();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Yet to approve"
        );

        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(
          page.getByTestId("Withdraw without auto pending status")
        ).toBeVisible();
      });
    });

    test2.describe("Aborted with skip", () => {
      test2("Aborted with skip adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Aborted with skip").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByLabel("Skip approval workflow").check();
        await page.getByPlaceholder("Write the reason for skipping").click();
        await page
          .getByPlaceholder("Write the reason for skipping")
          .fill("Aborted with skip");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test2("Aborted with skip check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Aborted with skip arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();

        const key = "commission-view-period";
        const value = "January-2024";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });
        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹20,010.00");
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJyZWplY3RAY29tbWFkajIuY29tIiwicHNkIjoiMjAyNC0wMS0wMSIsInBlZCI6IjIwMjQtMDEtMzEifQ==",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹20,010.00"
        );
      });
    });

    test2.describe("Aborted with auto", () => {
      test2("Aborted with auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("pt-action-button-Aborted with auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("1.000000");
        await page.getByText("Auto Approved").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await page.waitForLoadState("networkidle");
      });

      test2("Aborted with auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Aborted with auto arrow icon").click();
        await expect2(page.getByText("Auto Approved")).toBeVisible();
        await page.getByLabel("Close").click();

        const key = "commission-view-period";
        const value = "February-2024";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("http://localhost:3000/commissions", {
          waitUntil: "networkidle",
        });

        await expect2(
          page.getByTestId("<EMAIL> payout").locator("span")
        ).toContainText("₹1.00");
        await page.getByRole("link", { name: "Reject commadj2" }).click();
        await expect2(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹1.00"
        );
      });
    });

    test2.describe("Aborted without auto", () => {
      test2("Aborted without auto adj", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("pt-action-button-Aborted without auto").click();
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("14.000000");
        await page.getByText("Comm Adj WF 2").click();
        await page.getByRole("button", { name: "Submit" }).click();
        await expect2(page.getByText("Adjustment updated")).toBeVisible();
        await page.waitForLoadState("networkidle");
      });
      test2("Aborted without auto check", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/settings/adjustments?period=all&status=all",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("Aborted without auto arrow icon").click();
        await expect2(page.getByLabel("Timeline")).toContainText(
          "Yet to approve"
        );

        await page.goto(
          "http://localhost:3000/approvals/commission-adjustments?period=all&status=all",
          {
            waitUntil: "networkidle",
          }
        );
        await expect2(
          page.getByTestId("Aborted without auto pending status")
        ).toBeVisible();
      });
    });
  }
);

// Client 3 Toggle On OFF
test3.describe("Toggle", { tag: ["@regression", "@comm-adj-app"] }, () => {
  test3("Toggle 1", async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto("http://localhost:3000/settings/workflows", {
      waitUntil: "networkidle",
    });
    await page.getByRole("tab", { name: "Settings" }).locator("span").click();
    await expect3(page.getByRole("cell", { name: "INR" })).toBeVisible();
    await expect3(page.getByText("15")).toBeVisible();
    await expect3(page.getByText("payee commadjapp3")).toBeVisible();
    await expect3(page.getByText("super admin")).toBeVisible();

    await page.getByRole("switch").nth(1).click();
    await page.getByRole("button", { name: "Confirm" }).click();
    await page.waitForLoadState("networkidle");
  });

  test3("Toggle 2", async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/settings/adjustments?period=all&status=all",
      { waitUntil: "networkidle" }
    );
    await expect3(
      page.getByRole("columnheader", { name: "Approval Status" })
    ).not.toBeVisible();
    await page.getByRole("button", { name: "Add Adjustment" }).click();
    await page.getByRole("menuitem", { name: "Commission" }).click();
    await expect3(
      page.getByText("Pick an approval workflow")
    ).not.toBeVisible();
    await page.getByLabel("Payee*").click();
    await page.getByText("payee commadjapp3").click();
    await page.getByLabel("Effective Period*").click();
    await page.getByText("February 2024").click();
    await expect3(page.getByTitle("INR")).toBeVisible();
    await page.getByPlaceholder("Add amount").click();
    await page.getByPlaceholder("Add amount").fill("11");
    await page.getByRole("button", { name: "Submit" }).click();
    await page.waitForLoadState("networkidle");
  });

  test3("Toggle 3", async ({ adminPage }) => {
    const page = adminPage.page;
    const key = "commission-view-period";
    const value = "February-2024";

    await page.evaluate(
      ({ key, value }) => {
        // Set the localStorage value for the current page
        localStorage.setItem(key, value);
      },
      { key, value }
    );
    await page.goto("http://localhost:3000/commissions", {
      waitUntil: "networkidle",
    });
    await expect3(
      page.getByTestId("<EMAIL> payout").locator("span")
    ).toContainText("₹11.00");
  });

  test3("Toggle 4", async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto("http://localhost:3000/settings/workflows", {
      waitUntil: "networkidle",
    });
    await page.getByRole("tab", { name: "Settings" }).locator("span").click();
    // after toggle on
    await page.getByRole("switch").nth(1).click();
    await page.waitForNavigation({ waitUntil: "networkidle" });
    await expect3(page.getByText("payee commadjapp3")).not.toBeVisible();
    await expect3(page.getByText("super admin")).not.toBeVisible();
    await expect3(page.getByRole("cell", { name: "INR" })).not.toBeVisible();
    await expect3(page.getByText("15")).not.toBeVisible();

    await page.goto(
      "http://localhost:3000/settings/adjustments?period=all&status=all",
      { waitUntil: "networkidle" }
    );
    await expect(page.getByText("aborted")).toBeVisible();
  });
});
