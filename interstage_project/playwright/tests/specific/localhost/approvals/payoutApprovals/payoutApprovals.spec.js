const {
  payoutapprovalsadminFixtures: { test, expect },
} = require("../../../../fixtures");
const {
  datasheetProdAutomateFixtures: { test: test1, expect: expect1 },
} = require("../../../../fixtures");

const moment = require("moment");

test1.beforeEach(async ({ adminPage }, testInfo) => {
  const page = adminPage.page;
  testInfo.setTimeout(testInfo.timeout + 200000);
  await page.goto("/settings/workflows", { waitUntil: "networkidle" });
});

test.describe(
  "Payout Approvals Testcases",
  { tag: ["@regression", "@payouts", "@approvals", "@adminchamp-2"] },
  () => {
    test.describe("1. Create request in bulk & validate banner & add approver & perform unlock", () => {
      test("Create request in bulk & validate banner", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        const key = "commission-view-period";
        const value = "January-2024";

        await page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        await page.goto("/commissions", {
          waitUntil: "networkidle",
        });

        await page.waitForSelector("text=Approval Status", { timeout: 25000 });
        await page.locator('input[name="row-0"]').check();
        await page.waitForTimeout(2000);
        await page.getByText("Request Approval").click();
        await page
          .getByRole("button", { name: "Ok, request approval" })
          .click();
        await page.getByText("Create Custom Workflow").click();
        await page.getByPlaceholder("Choose approvers").click();
        await page.getByRole("tab", { name: "Users" }).click();
        await page
          .getByRole("tabpanel", { name: "Users" })
          .getByText("power admin")
          .click();
        await page
          .getByText(
            "Stage #1Configure approvers, due date, etc. for this stage."
          )
          .click();
        await page.getByRole("switch").click();
        await page.getByPlaceholder("Choose who needs to be").first().click();
        await page.getByRole("listitem").first().click();
        await page
          .getByRole("button", { name: "Send Approval Request" })
          .nth(1)
          .click();
        await page.waitForSelector(
          "text=Processing Approval Instance creation...Process running in background, you",
          { timeout: 15000 }
        );
      });

      test("Add approver", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMUBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
          { networkIdle: "networkidle" }
        );
        await page.getByTestId("approvals-tab").locator("path").first().click();

        await page
          .getByTestId("approval-request-dropdown")
          .locator("path")
          .click();

        await page.getByText("Add approver").click();
        await page.getByPlaceholder("Choose Approvers").click();
        await page.getByRole("tab", { name: "Users" }).click();
        await page
          .getByRole("tabpanel", { name: "Users" })
          .getByText("User 1")
          .click();
        await page.getByRole("button", { name: "Add Approvers" }).click();
        await page
          .getByRole("tabpanel", { name: "Approvals" })
          .getByText("User 1")
          .click();
        await page.getByText("Due in ").click();
      });

      test("Perform unlock", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMUBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
          { networkIdle: "networkidle" }
        );
        await page.getByTestId("lock-unlock-button").click();
        await page.getByText("Confirm Unlock").click();
        await page
          .getByText(
            "Any pending approval requests and previous approvals will be cancelled if unlock"
          )
          .click();
        await page.getByRole("button", { name: "Confirm" }).click();
      });
    });

    test.describe("1. Create workflow & raise request & remove approver from workflow", () => {
      test("Create workflow", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/workflows", {
          waitUntil: "networkidle",
        });

        await page.getByRole("button", { name: "Create Workflow" }).click();
        await page.getByRole("button", { name: "Save Workflow" }).click();
        await page.getByText("Please fill all mandatory fields.").click();
        await page.getByPlaceholder("Enter workflow name").click();
        await page.getByPlaceholder("Enter workflow name").fill("New");
        await page.getByRole("button", { name: "Save Workflow" }).click();
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Enter module name" })
          .click();
        await page.getByTitle("Payouts").getByText("Payouts").click();
        await page.getByRole("button", { name: "Save Workflow" }).click();
        await page.getByText("Please fill all mandatory fields.").click();
        await page.getByPlaceholder("Choose approvers").click();
        await page.getByRole("tab", { name: "Users" }).click();
        await page
          .getByRole("tabpanel", { name: "Users" })
          .getByText("power admin")
          .click();
        await page.getByText("User 1").click();
        await page.getByRole("button", { name: "Add Stage" }).click();
        await page.getByPlaceholder("Choose approvers").click();
        await page
          .getByText("Manager of approvers in previous stage")
          .isVisible();
        await page.getByRole("tab", { name: "Users" }).click();
        await page
          .getByRole("tabpanel", { name: "Users" })
          .getByText("power admin")
          .click();
        await page.getByPlaceholder("Choose who needs to be").first().click();
        await page.getByRole("listitem").first().click();
        await page.getByRole("button", { name: "Save Workflow" }).click();
      });

      test("Raise request using workflow", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMkBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
          { waitUntil: "networkidle" }
        );
        await page.getByTestId("statement-menu").click();
        await page.getByText("Request Approval").click();
        await page.getByText("New").click();
        await page
          .getByRole("button", { name: "Send Approval Request" })
          .click();
        await page.waitForSelector("text=Approval requested successfully.", {
          timeout: 20000,
        });
        await page
          .getByRole("tab", { name: "Approvals" })
          .getByText("Approvals")
          .click();

        await page.getByText("User 1").click();
        await page.getByText("power admin").nth(1).click();
        await page.getByText("power admin").nth(2).click();
      });

      test("Remove approver from workflow", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings/workflows", {
          waitUntil: "networkidle",
        });

        await page.getByRole("button", { name: "Edit" }).first().click();
        await page.getByRole("button", { name: "Remove item" }).nth(2).click();
        await page.getByRole("button", { name: "Save Workflow" }).click();
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMkBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
          { waitUntil: "networkidle" }
        );

        await page.getByTestId("approvals-tab").locator("path").first().click();

        await page.getByText("User 1").click();
        await page.getByText("power admin").nth(1).click();
        await page.getByText("power admin").nth(2).click();
      });
    });

    test("3. Admin approval page actions 1", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("all-requests-tab").click();
      await page.getByPlaceholder("Search by name or email").click();
      await page.getByPlaceholder("Search by name or email").fill("user");

      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNEBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("tab", { name: "Approvals" }).click();
      await page.getByText("Withdrawn since payouts was unlocked at ").click();
      await page.getByText("Yet to approve").isVisible();
      await page.getByText("by power admin").first().isVisible();
      await page.getByText("by power admin").nth(1).isVisible();
      await page
        .locator("div")
        .filter({ hasText: /^User 4$/ })
        .locator("svg")
        .click();
      await page.getByText("User 3").click();

      await page.getByRole("tab", { name: "Approvals" }).click();
      await page.getByRole("button", { name: "Approve" }).click();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      await page.getByText("Yet to approve").isVisible();
      await page.getByRole("button", { name: "Approve" }).first().click();
      await page.getByRole("button", { name: "Yes, approve" }).click();
    });

    test("4. Admin approval page actions 2", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.getByTitle("All").click();
      await page
        .getByTitle("March 2024")
        .getByRole("listitem")
        .locator("div")
        .click();
      await page.getByTestId("pending-requests-tab").click();
      await page.getByRole("tab", { name: "Customize Columns" }).click();
      await page
        .getByLabel("Designation Column")
        .getByText("Designation")
        .click();
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.getByTitle("All").click();
      await page
        .getByTitle("March 2024")
        .getByRole("listitem")
        .locator("div")
        .click();
      await page.getByRole("button", { name: "Reject" }).nth(1).click();
      await page.getByText("Reject approval request").click();
      await page.getByPlaceholder("Add your comments").click();
      await page.getByPlaceholder("Add your comments").fill("Amount");
      await page.getByRole("button", { name: "Reject Request" }).click();

      await page.waitForSelector("text=You've rejected the request.", {
        timeout: 20000,
      });
      await page.getByTestId("all-requests-tab").click();
      await page.getByRole("gridcell", { name: "Rejected" }).isVisible();
      await page
        .locator(
          "//div[@class='ag-pinned-right-cols-container']/div[@row-id='0']/div[@col-id='action']//button"
        )
        .first()
        .click();
      await page.getByText('"Amount"').isVisible();
      await page.getByLabel("Close", { exact: true }).click();
      await page.getByRole("gridcell", { name: "Pending" }).isVisible();
      await page
        .locator(
          "//div[@class='ag-pinned-right-cols-container']/div[@row-id='1']/div[@col-id='action']//button"
        )
        .nth(0)
        .click();
      await page.getByRole("link", { name: "View Statement" }).click();
      await page.getByText("Waiting for your approval").isVisible();
    });

    test("5.Withdraw request everyone startegy", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyM0BwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await expect(page.getByText("On 21 May 2024, 11:")).toBeVisible();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(page.getByText("User").nth(1)).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User").nth(2)).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();

      await page
        .getByTestId("approval-request-dropdown")
        .locator("path")
        .click();
      await page.getByText("Withdraw pending requests").click();
      await expect(page.getByText("Withdraw this approval")).toBeVisible();
      await expect(
        page.getByText("Users will no longer be able")
      ).toBeVisible();
      await page.getByRole("button", { name: "Withdraw Request" }).click();
      await page.waitForTimeout(1000);
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by power on"
      );
      await expect(page.getByText("User").nth(1)).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by power on"
      );
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User").nth(2)).toBeVisible();
      await expect(page.getByText("Yet to approve")).toBeVisible();
    });

    test("5.Withdraw request anyone startegy", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNEBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await page.getByText("On 21 May 2024, 11:").click();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByLabel("Approvals").locator("svg").click();
      await page.getByText("Withdraw pending requests").click();
      await expect(page.getByText("Withdraw this approval")).toBeVisible();
      await expect(
        page.getByText("Users will no longer be able")
      ).toBeVisible();
      await page.getByRole("button", { name: "Withdraw Request" }).click();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by power on"
      );
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by power on"
      );
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Yet to approve")).toBeVisible();
    });

    test("6. Anyone startegy - approve", async ({ adminPage }) => {
      // User 7 in april payout
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyN0BwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("statement-menu").click();
      await page.getByText("Request Approval").click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByLabel("Anyone approvesStage is").check();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByText("User 1").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("User 2").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();
      await page.waitForSelector("text=Approval requested successfully.", {
        timeout: 20000,
      });

      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByRole("button", { name: "Approve" }).click();
      await expect(page.getByText("Approve Request")).toBeVisible();
      await expect(
        page.getByText("You're about to approve this")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText("Approved on");
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by Everstage on"
      );
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Yet to approve")).toBeVisible();

      await page.getByRole("button", { name: "Delete all requests" }).click();
      await expect(
        page.getByText("This will delete all approval")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
    });

    test("7. Anyone startegy - reject", async ({ adminPage }) => {
      //  User 6 in april payout
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNkBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("statement-menu").click();

      await page.getByText("Request Approval").click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .getByText(
          "Stage #1Configure approvers, due date, etc. for this stage.Approvers*"
        )
        .click();
      await page.getByLabel("Anyone approvesStage is").check();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .locator("div")
        .filter({ hasText: /^Users$/ })
        .nth(1)
        .click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByText("User 1").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("User 2").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();
      await page.waitForSelector("text=Approval requested successfully.", {
        timeout: 20000,
      });
      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByRole("button", { name: "Reject" }).click();
      await expect(page.getByText("Reject approval request")).toBeVisible();
      await expect(page.getByText("Comments*")).toBeVisible();
      await page.getByPlaceholder("Add your comments").click();
      await page.getByPlaceholder("Add your comments").fill("Reject");
      await page.getByRole("button", { name: "Reject Request" }).click();
      await expect(page.getByText("Stage #1", { exact: true })).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText("Rejected on");
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by Everstage on"
      );
      await expect(page.getByLabel("Approvals")).toContainText(
        "payout Rejected @ Stage #1. You can address the feedback from reviewers and request approval again."
      );
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn since payouts got rejected at"
      );
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await expect(
        page.getByText("This will delete all approval")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.getByText("Approval requests deleted").click();
    });

    test("8. Everyone startegy - approve", async ({ adminPage }) => {
      // User 1 in april payout
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMUBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );

      await page.getByTestId("statement-menu").click();

      await page.getByText("Request Approval").click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByLabel("Users").getByText("User 1").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("User 2").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();

      await page.waitForSelector("text=Approval requested successfully.", {
        timeout: 20000,
      });

      await expect(page.getByText("Waiting for your approval")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(
        page.getByLabel("Approvals").getByText("User 1")
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByRole("button", { name: "Approve" }).click();
      await expect(page.getByText("Approve Request")).toBeVisible();
      await expect(
        page.getByText("You're about to approve this")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText("Approved on");
      await expect(
        page.getByLabel("Approvals").getByText("User 1")
      ).toBeVisible();
      await expect(page.getByText("Yet to approve")).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(page.getByText("User 2")).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await expect(
        page.getByText("This will delete all approval")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
    });

    test("9. Everyone startegy - reject", async ({ adminPage }) => {
      // User 2 in april payout
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMkBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        { waitUntil: "networkidle" }
      );

      await page.getByTestId("statement-menu").click();

      await page.getByText("Request Approval").click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByRole("switch").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByText("User 1").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("User 2").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();

      await page.waitForSelector("text=Approval requested successfully.", {
        timeout: 20000,
      });

      await expect(page.getByText("Waiting for your approval [")).toBeVisible();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await expect(page.getByText("Approval Initiated")).toBeVisible();
      await expect(page.getByText("Stage #1")).toBeVisible();
      await expect(page.getByText("Due in")).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Yet to approve").first()).toBeVisible();
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByText("Yet to approve").nth(1)).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(
        page.getByLabel("Approvals").getByText("User 2")
      ).toBeVisible();
      await expect(page.getByText("Request not sent")).toBeVisible();
      await page.getByRole("button", { name: "Reject" }).click();
      await expect(page.getByText("Reject approval request")).toBeVisible();
      await expect(page.getByText("Comments*")).toBeVisible();
      await page.getByPlaceholder("Add your comments").click();
      await page.getByPlaceholder("Add your comments").fill("Reject");
      await page.getByRole("button", { name: "Reject Request" }).click();
      await expect(page.getByText("Stage #1", { exact: true })).toBeVisible();
      await expect(
        page.getByText("power admin", { exact: true })
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText("Rejected on");
      await expect(page.getByText("User 1")).toBeVisible();
      await expect(page.getByText("Withdrawn by Everstage on")).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn by Everstage on"
      );
      await expect(page.getByText("payout Rejected @ Stage #1.")).toBeVisible();
      await expect(page.getByText("Stage #2")).toBeVisible();
      await expect(
        page.getByLabel("Approvals").getByText("User 2")
      ).toBeVisible();
      await expect(page.getByLabel("Approvals")).toContainText(
        "Withdrawn since payouts got rejected at"
      );
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await expect(
        page.getByText("This will delete all approval")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
    });

    test("10. Admin approval page actions 3", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await expect(
        page.getByRole("button", { name: "Pending Requests" })
      ).toBeVisible();
      await expect(
        page.getByTestId("period-select").getByText("All")
      ).toBeVisible();
      await page.getByRole("gridcell", { name: "Pending" }).nth(1).isVisible();
      await page
        .locator(
          "//div[@class='ag-pinned-right-cols-container']/div[@row-id='0']/div[@col-id='action']//button"
        )
        .first()
        .click();
      const [newPage] = await Promise.all([
        page.context().waitForEvent("page"),
        page.getByRole("link", { name: "View Statement" }).click(),
      ]);
      await newPage.waitForLoadState("networkidle", { timeout: 20000 });
      await newPage.getByRole("tab", { name: "Approvals" }).click();
      await expect(newPage.getByText("Approval Initiated")).toBeVisible();
      await expect(newPage.getByText("Stage #1")).toBeVisible();
      await newPage.close();
      await page.bringToFront();
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.getByTitle("All").click();
      await page.getByText("August 2024").click();
      await page.locator(".ag-center-cols-viewport").isVisible();
    });

    test("13. Workflows", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/workflows", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Create Workflow" }).click();
      await page.getByPlaceholder("Enter workflow name").click();
      await page.getByPlaceholder("Enter workflow name").fill("Sample");
      await page
        .getByLabel("New Workflow")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Enter module name" })
        .click();
      await page.getByTitle("Payouts").getByText("Payouts").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("listitem").first().click();
      await page.getByRole("switch").click();
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("0");
      await page.getByRole("button", { name: "Save Workflow" }).click();

      await page.getByPlaceholder("Enter workflow name").click({
        clickCount: 3,
      });
      await page.getByPlaceholder("Enter workflow name").fill("New 1@ ");
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .locator("div")
        .filter({ hasText: /^Reporting manager of payee$/ })
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByText("Manager of approvers in").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Groups" }).click();
      await page.getByText("All Payees").click();
      await page.getByText("Include additional notes for").click();
      await page.getByPlaceholder("Type here").click();
      await page.getByPlaceholder("Type here").fill("Notes");
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page.getByRole("button", { name: "Save Workflow" }).click();

      await page
        .locator("div")
        .filter({ hasText: /^Last modified:/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByText("Clone").click();
      await expect(
        page.getByText("Workflow cloned successfully")
      ).toBeVisible();
    });

    test("Raise approval when payout is unlocked", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyM0BwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDYtMDEiLCJwZWQiOiIyMDI0LTA2LTMwIn0="
      );

      await page.getByTestId("statement-menu").click();
      await page.getByText("Request Approval").click();
      await expect(page.getByText("You cannot request approval")).toBeVisible();
      await expect(page.getByText("Payout is unlocked (or)")).toBeVisible();
      await expect(
        page.getByText("2. Payout is already approved")
      ).toBeVisible();
      await page.getByRole("button", { name: "OK" }).click();
    });

    test("Raise approval when approval is raised already", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMkBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDYtMDEiLCJwZWQiOiIyMDI0LTA2LTMwIn0="
      );

      await page.getByTestId("statement-menu").click();
      await page.getByText("Request Approval").click();
      await expect(page.getByText("You cannot request approval")).toBeVisible();
      await expect(page.getByText("Payout is unlocked (or)")).toBeVisible();
      await expect(
        page.getByText("2. Payout is already approved")
      ).toBeVisible();
      await page.getByRole("button", { name: "OK" }).click();
    });

    test.describe("Bulk approve", async () => {
      test("Bulk approve & Bulk reject", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/approvals/payouts", {
          waitUntil: "networkidle",
        });
        await page.getByTestId("period-select").getByText("All").click();
        await page.getByText("May 2024").nth(4).click();
        await page.getByTestId("pending-requests-tab").click();
        await page.waitForTimeout(5000);
        await page.getByTestId("User 1").check();
        await page.getByTestId("User 2").check();
        await page.getByRole("button", { name: "Approve" }).first().click();
        await expect(page.getByText("Approve Request")).toBeVisible();
        await page.getByRole("button", { name: "Yes, approve" }).click();

        await page.waitForSelector("text=You've approved all the", {
          timeout: 20000,
        });
        await page.getByTestId("all-requests-tab").click();
        await page.getByTestId("pending-requests-tab").click();

        await page.getByTestId("User 3").check();
        await page.getByTestId("User 4").check();
        await page.getByRole("button", { name: "Reject" }).first().click();
        await expect(page.getByText("Are you sure, you want to")).toBeVisible();
        await page.getByPlaceholder("Add your comments").click();
        await page
          .getByPlaceholder("Add your comments")
          .fill("Reject requests in bulk");
        await page.getByRole("button", { name: "Reject Request" }).click();
        await page.waitForSelector("text=You've rejected all the", {
          timeout: 20000,
        });
      });

      test("Validate bulk approve & bulk reject", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/approvals/payouts", {
          waitUntil: "networkidle",
        });
        await page.getByTestId("all-requests-tab").click();
        await page.getByTestId("period-select").getByText("All").click();
        await page.getByText("May 2024").nth(4).click();
        await page.getByTestId("pending-requests-tab").click();
        await page.getByTestId("all-requests-tab").click();
        await page.getByTestId("approved-icon-User 1").click();
        const user1Approved = page.getByText("Approved").nth(0);
        const user1ApprovedText = await user1Approved.innerText();

        await page.getByTestId("approved-icon-User 2").click();
        const user2Approved = page.getByText("Approved").nth(1);
        const user2ApprovedText = await user2Approved.innerText();
        expect(user1ApprovedText).toBe(user2ApprovedText);

        await page.getByTestId("rejected-icon-User 3").click();
        const user3Rejected = page.getByText("Rejected").nth(0);
        const user3RejectedText = await user3Rejected.innerText();

        await page.getByTestId("rejected-icon-User 4").click();
        const user4Rejected = page.getByText("Rejected").nth(1);
        const user4RejectedText = await user4Rejected.innerText();
        expect(user3RejectedText).toBe(user4RejectedText);
      });
    });

    test("Validate custom fields", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("period-select").getByText("All").click();
      await page.getByText("June 2024").click();
      await page.getByText("Text Field Label").isVisible();
      await page.getByText("User1Text").first().isVisible();
      await page.getByText("Email Field Label").isVisible();
      await page.getByText("<EMAIL>").first().isVisible();
      await page.getByText("Number Field Label").isVisible();
      await page.getByText("10101010").isVisible();
      await page.getByText("Dropdown Field Label").isVisible();
      await page.getByText("Choice1").isVisible();
      await page.getByText("Date Field Label").isVisible();
      await page.getByText("Checkbox Field Label").isVisible();
      await page.getByText("True").isVisible();
      await page.getByRole("gridcell", { name: "--" }).first().isVisible();
    });

    test("Update due date and withdraw request", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNUBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDctMDEiLCJwZWQiOiIyMDI0LTA3LTMxIn0=",
        { waitUntil: "networkidle" }
      );
      await page.getByTestId("approvals-tab").waitFor({ timeout: 30000 });
      await page.getByTestId("approvals-tab").click();
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await page.getByRole("button", { name: "Request Approval" }).click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByText("Include additional notes for").click();
      await page.getByPlaceholder("Type here").click();
      await page.getByPlaceholder("Type here").fill("Context message");
      await page.getByRole("switch").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();
      await expect(page.getByText("Approval requested")).toBeVisible();

      await expect(page.getByText("Context message")).toBeVisible();

      const today = moment.utc();
      const dueDate = today.clone().add(2, "days");
      const formattedDate = dueDate.format("MMMM D, YYYY");
      const expectedText = `Waiting for your approval [ Due by ${formattedDate} ]`;

      await expect(page.getByTestId("approval-banner")).toContainText(
        expectedText
      );

      const updateDate = today.clone().add(4, "days").format("YYYY-MM-DD");
      const updatedFormattedDate = today
        .clone()
        .add(4, "days")
        .format("MMMM D, YYYY");
      const updatedExpectedText = `Waiting for your approval [ Due by ${updatedFormattedDate} ]`;

      await page.getByTestId("approvals-tab").locator("path").first().click();

      await page
        .getByTestId("approval-request-dropdown")
        .locator("path")
        .click();

      await page.getByText("Change due date").click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill(updateDate);

      await page.getByPlaceholder("Select date").press("Enter");
      await page.getByRole("button", { name: "Update due date" }).click();
      await page.waitForSelector("text=Due date updated successfully.", {
        timeout: 20000,
      });
      await expect(page.getByTestId("approval-banner")).toContainText(
        updatedExpectedText
      );

      await page.getByTestId("approvals-tab").locator("path").first().click();

      await page
        .getByTestId("approval-request-dropdown")
        .locator("path")
        .click();
      await page.getByText("Withdraw pending requests").click();
      await expect(page.getByText("Withdraw this approval")).toBeVisible();
      await page.getByRole("button", { name: "Withdraw Request" }).click();
      await expect(page.getByText("Request withdrawn")).toBeVisible();
    });

    test("Update due date with invalid date", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyMUBwbGF5d3JpZ2h0cGF5b3V0YXBwcm92YWxzLmNvbSIsInBzZCI6IjIwMjQtMDgtMDEiLCJwZWQiOiIyMDI0LTA4LTMxIn0=",
        { waitUntil: "networkidle" }
      );

      await page.getByTestId("statement-menu").click();
      await page.getByText("Request Approval").click();
      await page
        .getByRole("button", { name: "Create Custom Workflow" })
        .click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("switch").click();
      await page.getByRole("button", { name: "Add Stage" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("switch").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page
        .getByRole("button", { name: "Send Approval Request" })
        .nth(1)
        .click();
      await expect(page.getByText("Approval requested")).toBeVisible();
      const today = moment.utc();
      const dueDate = today.clone().add(2, "days");
      const formattedDate = dueDate.format("MMMM D, YYYY");
      const expectedText = `Waiting for your approval [ Due by ${formattedDate} ]`;

      await expect(page.getByTestId("approval-banner")).toContainText(
        expectedText
      );
      await page.getByTestId("approvals-tab").locator("path").first().click();
      await page.getByRole("button", { name: "Approve" }).click();
      await expect(page.getByText("Approve Request")).toBeVisible();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      await expect(
        page.getByText("You've approved the request!")
      ).toBeVisible();
      await expect(page.getByTestId("approval-banner")).toContainText(
        expectedText
      );
      const todayFormatted = today.clone().format("YYYY-MM-DD");
      console.log("todayFormatted", todayFormatted);

      await page.getByTestId("approvals-tab").click();
      await page.getByText("Payout Summary").click();
      await page.getByTestId("approvals-tab").click();
      await page.waitForTimeout(2000);
      await page
        .getByTestId("approval-request-dropdown")
        .locator("path")
        .click();

      await page.getByTestId("change-due-date").click();

      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill(todayFormatted);

      await page.getByPlaceholder("Select date").press("Enter");

      let updateButton = await page.waitForSelector(
        'button:has-text("Update due date")',
        { state: "visible" }
      );
      let isButtonEnabled = await updateButton.isEnabled();

      // Due date should not be instance created date, or it should not be greater than a month
      let dueDateConditionMet = true;

      if (isButtonEnabled) {
        dueDateConditionMet = false;
        expect(dueDateConditionMet).toBe(true);
      } else {
        await page.getByRole("button", { name: "Cancel" }).click();
        const nextMonthDate = today.add(32, "days").format("YYYY-MM-DD");

        await page.getByTestId("approvals-tab").locator("path").first().click();

        await page
          .getByTestId("approval-request-dropdown")
          .locator("path")
          .waitFor({ timeout: 20000 });
        await page
          .getByTestId("approval-request-dropdown")
          .locator("path")
          .click();

        await page.getByText("Change due date").click();
        await page.getByPlaceholder("Select date").click();
        await page.getByPlaceholder("Select date").fill(nextMonthDate);

        await page.getByPlaceholder("Select date").press("Enter");
        updateButton = await page.waitForSelector(
          'button:has-text("Update due date")',
          { state: "visible" }
        );

        isButtonEnabled = await updateButton.isEnabled();

        if (isButtonEnabled) {
          dueDateConditionMet = false;
          expect(dueDateConditionMet).toBe(true);
        }
      }
    });
  }
);

test1.describe(
  "Datasheet V2 - Prod bugs",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test1(
      "Verify that approval stages remain correct and in order when an approval workflow is cloned.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-11731" },
          {
            type: "Description",
            description:
              "Verify that approval stages remain correct and in order when an approval workflow is cloned.",
          },
          {
            type: "Precondition",
            description:
              "An existing approval workflow with defined stages available for cloning",
          },
          {
            type: "Expected Behaviour",
            description:
              "When an approval workflow is cloned, the approval stages should be correctly retained in the original sequence without being incorrect or jumbled.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        //can be reusable with changing and passing the nth value
        const editButton = page.locator(
          `//button[descendant::text()[contains(., "Edit")]]/following-sibling::button`
        );
        await editButton.waitFor({ state: "visible" });
        await editButton.nth(0).click();
        await page.getByText("Clone").click();
        await expect(
          page.getByText("Workflow cloned successfully")
        ).toBeVisible();
        await expect(page.getByText("Copy of Simple")).toBeVisible();
        //can be reused by changing the span text with the workflows name!
        await page
          .locator(
            `//span[text()="Copy of Simple"]/ancestor::div[contains(@class, "bg-ever-base")]//button[descendant::text()[contains(., "Edit")]]`
          )
          .nth(0)
          .click();
        await expect(page.getByText("Payee").nth(1)).toBeVisible();
        await page.getByText("Stage #2").click();
        await expect(
          page.getByText("Manager of approvers in previous stage")
        ).toBeVisible();
        await page.getByText("Stage #3").click();
        await expect(page.getByText("Alice J")).toBeVisible();
        console.log("The workflow is in the original sequence");
      }
    );
  }
);
