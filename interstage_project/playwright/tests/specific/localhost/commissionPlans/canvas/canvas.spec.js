import { bearerTokenGenerator } from "../../../../bearerToken.js";
import CanvasCommission from "../../../../../test-objects/canvas-objects.js";
const {
  playwrightCommPlanCanvasFixtures: { test, expect },
} = require("../../../../fixtures.js");
const requestBodies = require("./canvasApi.js").default;
const creds = require("../../apiCreds.js");

const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");
let token = "";

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/plans", { waitUntil: "networkidle" });
});

test.describe(
  "canvas tests",
  { tag: ["@regression", "@commissionplan", "@adminchamp-5"] },
  () => {
    test("Test to create plan without settlement date", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Plan creation
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("New created plan 2");
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Jun 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Aug 31, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page
        .locator('[id="create_plan_spiff\\ Plan"]')
        .getByRole("switch")
        .click();
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();
      // Checking payees section
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select date function$/ })
        .nth(3)
        .click();
      await page.getByText("Before").click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Mar 01, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await page.getByRole("button", { name: "Add more filters" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select field$/ })
        .nth(3)
        .click();
      await page.getByText("Employment Country").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select operator$/ })
        .nth(3)
        .click();
      await page.getByText("In", { exact: true }).click();
      await page
        .locator(
          "div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.getByText("India").click();
      await page.getByRole("button", { name: "Add more filters" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select field$/ })
        .nth(3)
        .click();
      // await page.getByText("Payee Currency").nth(1).click();
      await page
        .locator("div[label='Payee Currency'][title='Payee Currency']")
        .last()
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select operator$/ })
        .nth(3)
        .click();
      await page.getByText("In", { exact: true }).nth(2).click();
      await page
        .locator(
          "div:nth-child(2) > .ant-row > div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.locator("span").filter({ hasText: "INR" }).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await page.getByRole("textbox", { name: "Search payees" }).click();
      await page.getByRole("textbox", { name: "Search payees" }).fill("user 1");
      await expect(page.getByText("Filtered payees: 4")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("Test to edit plan", async ({ adminPage }) => {
      const page = adminPage.page;
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2024");
      await page.getByText("New created plan 2").click();
      await page
        .locator("div")
        .filter({ hasText: /^DraftSPIFFMonthly$/ })
        .getByRole("button")
        .click();
      await page.getByPlaceholder("Enter name").click();
      await page
        .getByPlaceholder("Enter name")
        .fill("New created plan 2 update name");
      await page.getByTitle("Monthly").click();
      await page.locator("span").filter({ hasText: "Quarterly" }).click();
      await page.getByRole("button", { name: "Update" }).click();
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Sep 30, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByLabel("Yes").click();
      await page.getByRole("button", { name: "Done" }).click();
      await expect(page.getByText("Jun 01, 2024 - Sep 30, 2024")).toBeVisible();
    });

    test("Test to create plan with settlement end date and plan document", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Plan creation
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("New created plan 1");
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Feb 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Mar 31, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page.getByRole("switch").nth(1).click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Apr 02, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await page.getByRole("button", { name: "Upload" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/playwright_contract.pdf");
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();
      await expect(page.getByText("playwright_contract.pdf")).toHaveCount(1);
      // Checking payees section
      await page.getByRole("textbox", { name: "Search payees" }).fill("added");
      await page.waitForTimeout(2000);
      await page.getByText("Added Removed User").click();
      await page.getByText("Added User").click();
      await expect(page.getByText("Filtered payees: 2")).toBeVisible();
      await page.locator(".ant-checkbox").first().click();
      await page.getByRole("textbox", { name: "Search payees" }).fill("");
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page.locator(".ant-select-selection-overflow").first().click();
      await page.getByText("Criteria User").nth(2).click();
      await page.getByRole("button", { name: "Add more filters" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select field$/ })
        .nth(3)
        .click();
      await page.getByText("Payee Currency").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select operator$/ })
        .nth(3)
        .click();
      await page.getByText("In", { exact: true }).click();
      await page
        .locator(
          "div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.locator("span").filter({ hasText: "INR" }).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await expect(page.getByText("Filtered payees: 1")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("Test to check for disabled publish button", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByTestId("pt-fiscal-year-select").click();
      await page
        .locator("span")
        .filter({ hasText: /^2023$/ })
        .click();
      // Plan without criteria - disable check
      await page.getByText("Plan without criteria").click();
      const publishButton = page
        .getByRole("button", { name: "Publish" })
        .nth(1);
      await expect(publishButton).toBeDisabled();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Plan without payees - disable check
      await page.getByText("Plan without payees").click();
      await expect(publishButton).toBeDisabled();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.getByTestId("pt-fiscal-year-select").click();
      await page
        .locator("span")
        .filter({ hasText: /^2024$/ })
        .click();

      // Plan without settlement rules - not allow publish check
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Plan without settlement comp");
      await page.getByText("Plan without settlement comp").click();
      await page.getByRole("button", { name: "Publish", exact: true }).click();
      await page
        .getByRole("dialog")
        .getByRole("button", { name: "Publish" })
        .click();
      await page.waitForTimeout(3000);
      await expect(page.getByText("Publish could not be done")).toBeVisible();
      await expect(
        page.getByText(
          "Settlement end date was added, but a settlement component was not added."
        )
      ).toBeVisible();
      await page.keyboard.press("Escape");
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Plan with unsaved changes - not allow publish check
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Plan Criteria Check 1");
      await page.getByText("Plan Criteria Check 1", { exact: true }).click();
      await page.getByText("Simple").first().click();
      await page.waitForTimeout(2000);
      await page.getByTestId("expression-input-box").click();
      await page.getByRole("textbox").nth(4).fill("*");
      await page.getByTestId("auto-suggestion-view").getByText("*").click();
      await expect(publishButton).toBeDisabled();
      await page
        .getByTestId("expression-input-box")
        .getByRole("textbox")
        .nth(4)
        .fill("2");
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("2")
        .first()
        .click();
      await page.waitForTimeout(3000);
      await page.getByRole("button", { name: "Publish", exact: true }).click();
      await page
        .getByRole("dialog")
        .getByRole("button", { name: "Publish" })
        .click();
      await expect(page.getByText("Publish could not be done")).toBeVisible();
      await expect(
        page.getByText("You have pending unsaved changes in Components")
      ).toBeVisible();
      await page.keyboard.press("Escape");
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.getByRole("button", { name: "Exit", exact: true }).click();
    });

    test("Test to check basic plan based operations", async ({ adminPage }) => {
      const page = adminPage.page;
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2024");
      // Clone plan with payees
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("plan criteria check 1");
      await page.getByTestId("pt-actions-Plan Criteria Check 1").click();
      await page.getByText("With Payees").click();
      await expect(
        page.getByText("Plan Criteria Check 1_Copy").nth(1)
      ).toBeVisible();
      await expect(page.getByText("2payee saved")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Clone plan without payees
      await page.getByTestId("pt-actions-Plan Criteria Check 1").click();
      await page.getByText("Without Payees").click();
      await expect(
        page.getByText("Plan Criteria Check 1_Copy1").nth(1)
      ).toBeVisible();
      await expect(page.getByText("Add payees to this plan")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Search for a plan
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("settlement");
      await expect(
        page.getByText("Plan without settlement comp")
      ).toBeVisible();
      await page.getByLabel("close-circle").click();

      // Spiff plan count check
      await page.getByTestId("pt-fiscal-year-select").click();
      await page.locator("span").filter({ hasText: "2025" }).click();
      await page.locator('[data-testid*="pt-actions"]').first().waitFor();
      await page.locator("button.ant-btn-ghost").click();
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await expect(page.getByText("All02Published00Draft02")).toBeVisible();

      // Active plan count check
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await page.waitForTimeout(2000);
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();
      await expect(page.getByText("All03Published00Draft03")).toBeVisible();

      // Delete plan
      await page.locator("button.ant-btn-ghost").click();
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();
      await page.getByTestId("pt-fiscal-year-select").click();
      await page.locator("span").filter({ hasText: "2024" }).click();
      await page.waitForTimeout(2000);
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan to delete");
      await page.getByTestId("pt-actions-Plan To Delete").click();
      await page.getByText("Delete", { exact: true }).click();
      await expect(
        page.getByText("Are you sure you want to delete")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await expect(
        page.getByText("Plan To Delete deleted successfully!")
      ).toBeVisible();
    });

    test("Test to check filters for users in manage payees section", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Reporting manager filter
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2025");
      await page.getByText("Plan 5").click();
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page.locator(".ant-select-selection-overflow").first().click();
      await page.getByText("New User 3 Test").nth(1).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await expect(page.getByText("Filtered Payees: 2")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Other filters for payees
      await page.getByText("Plan 8").click();
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page
        .locator(
          "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
        )
        .click();
      await page.getByText("All Admins").click();
      await page.getByText("All Payees", { exact: true }).click();
      await page.getByText("New Users Group").click();
      await page.getByText("All AdminsAll PayeesNew Users").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select date function$/ })
        .nth(3)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^After$/ })
        .nth(1)
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Mar 01, 2020");
      await page.getByPlaceholder("Select date").press("Enter");
      await page
        .locator(
          "div:nth-child(4) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
        )
        .click();
      await page
        .locator("span")
        .filter({ hasText: /^Payee$/ })
        .click();
      await page.locator("span").filter({ hasText: "Payee Samp" }).click();
      await page.locator("label").filter({ hasText: "Designation" }).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await expect(page.getByText("Filtered Payees: 9")).toBeVisible();
    });

    test("Test to check bulk operations for user updates in manage payees", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2024");
      // Bulk add users to plan
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("criteria check 3");
      await page.getByText("Plan Criteria Check 3").click();
      await page.getByLabel("S1Samp 1 User").check();
      await page.getByLabel("S2Samp 2 User").check();
      await page.getByLabel("S3Samp 3 User").check();
      await page.getByLabel("S4Samp 4 User").check();
      await page.getByLabel("S5Samp 5 User").check();
      await page.getByLabel("S6Samp 6 User").check();
      await page.getByLabel("S7Samp 7 User").check();
      await page.getByLabel("S8Samp 8 User").check();
      await page.getByLabel("U0User").check();
      await page.getByLabel("U1User").check();
      await page.getByLabel("U2User").check();
      await page.getByLabel("U4User").check();
      await expect(page.getByText("12payee selected")).toBeVisible();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();

      // Bulk update plan period
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.locator('input[name="row-2"]').check();
      await page.locator('input[name="row-3"]').check();
      await page.getByText("Bulk actions").click();
      await page.getByRole("menuitem", { name: "Update plan period" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByTitle("-07-31").locator("div").click();
      await page.getByRole("button", { name: "Update" }).click();
      await expect(page.getByText("Limited period4")).toHaveCount(1);

      // Bulk update settlement end date
      await page.getByText("Bulk actions").click();
      await page
        .getByRole("menuitem", { name: "Update settlement end date" })
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Aug 14, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await page.getByRole("button", { name: "Update" }).click();

      // Bulk remove users from plan
      await page.locator(".w-max > .w-4 > path").click();
      await page.locator('input[name="row-8"]').check();
      await page.locator('input[name="row-9"]').check();
      await page.locator('input[name="row-7"]').check();
      await page.getByText("Bulk actions").click();
      await page.getByRole("menuitem", { name: "Remove payees" }).click();
      await expect(page.getByText("Are you sure to remove 3")).toBeVisible();
      await page.getByRole("button", { name: "Remove" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Successfully saved 10 payees")
      ).toBeVisible();

      // Download csv
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.locator('input[name="row-2"]').check();
      await page.locator('input[name="row-3"]').check();
      await page.locator('input[name="row-4"]').check();
      await page.getByText("Bulk actions").click();
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("menuitem", { name: "Download as CSV" }).click();
      const download = await downloadPromise;
      await expect(page.getByText("Successfully Exported")).toBeVisible();
    });

    test("Manage payee - payee period tests", async ({ adminPage }) => {
      const page = adminPage.page;
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2024");
      // Update payee plan end date greater than plan end date
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("criteria check 4");
      await page.getByText("Plan Criteria Check 4").click();
      await page.getByLabel("S1Samp 1 User").check();
      await page.getByLabel("S2Samp 2 User").check();
      await page.getByLabel("S3Samp 3 User").check();
      await page.getByLabel("S4Samp 4 User").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 1");
      await page.waitForTimeout(2000);
      await page
        .getByRole("gridcell", { name: "Jan 01, 2024 - Dec 31," })
        .locator("path")
        .first()
        .click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Jan 05, 2025");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByText("Samp 1 User").click();
      await expect(
        page.locator(
          "//div[@col-id='planDuration']//span[text()='Jan 01, 2024 - Dec 31, 2024']"
        )
      ).toHaveCount(1);

      // Update payee plan start date less than plan start date
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 2");
      await page.waitForTimeout(2000);
      await page
        .getByRole("gridcell", { name: "Jan 01, 2024 - Dec 31," })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Dec 15, 2023");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByText("Samp 2 User").click();
      await expect(
        page.locator(
          "//div[@col-id='planDuration']//span[text()='Jan 01, 2024 - Dec 31, 2024']"
        )
      ).toHaveCount(1);

      // Update payee plan start date lesser than joining date of the payee
      await page.getByTitle("Show panel").locator("svg").click();
      await page.getByPlaceholder("Search payees").first().click();
      await page.getByPlaceholder("Search payees").first().fill("samp 9");
      await page.getByLabel("S9Samp 9 Test").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 9");
      await page.waitForTimeout(2000);
      await expect(
        page.getByText(
          "Effective start date should be greater than oldest payroll date: 19-Jun-"
        )
      ).toBeVisible();
      await expect(
        page
          .getByRole("treegrid")
          .getByText(
            "Effective start date should be greater than joining date: 19-Jun-"
          )
      ).toBeVisible();
      await page
        .getByRole("gridcell", { name: "Jan 01, 2024 - Dec 31," })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jul 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await expect(page.getByText("Jul 01, 2024 - Dec 31,")).toHaveCount(1);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await expect(
        page.getByText("Review unsaved payee updates")
      ).toBeVisible();
      await page.getByRole("button", { name: "Exit", exact: true }).click();

      // Update plan start date, end date and settlement end date for all payees
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("criteria check 4");
      await page.getByText("Plan Criteria Check 4").click();
      await page.getByLabel("S1Samp 1 User").check();
      await page.getByLabel("S2Samp 2 User").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Dec 01, 2023");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByText("Add Settlement component in").click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Feb 28, 2025");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByText("Add Settlement component in").click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Apr 29, 2025");
      await page.getByPlaceholder("Select date").press("Enter");
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByTestId("pt-start date").click();
      await page.getByTestId("pt-end date").click();
      await page.getByTestId("pt-settlement end date").click();
      await page.getByRole("button", { name: "Done" }).click();
      await expect(page.getByText("Dec 01, 2023 - Feb 28, 2025")).toHaveCount(
        4
      );
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await expect(
        page.getByText("Review unsaved payee updates")
      ).toBeVisible();
      await page.getByRole("button", { name: "Exit", exact: true }).click();
    });

    test("Tests for settlement date modification cases", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const canvasPage = new CanvasCommission(page);

      await canvasPage.selectYear("2024");
      // Update payee settlement end date greater than plan settlement end date
      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("criteria check 5");
      await page.getByText("Plan Criteria Check 5").click();
      await page.getByLabel("S1Samp 1 User").check();
      await page.getByLabel("S2Samp 2 User").check();
      await page.getByLabel("S3Samp 3 User").check();
      await page.getByLabel("S4Samp 4 User").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 1");
      await page.waitForTimeout(4000);
      await page
        .getByRole("gridcell", { name: "Dec 31, 2024", exact: true })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Jan 02, 2025");
      await page.getByPlaceholder("Select date").press("Enter");
      await expect(
        page.getByText(
          "Settlement End Date should be less than or equal to plan settlement end date: 31-Dec-"
        )
      ).toBeVisible();

      // Update payee settlement end date less than plan settlement end date
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 2");
      await page.waitForTimeout(4000);
      await page
        .getByRole("gridcell", { name: "Dec 31, 2024", exact: true })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Dec 02, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await expect(
        page.getByText(
          "Payee Settlement End Date should not be less than payee end date: 31-Dec-"
        )
      ).toBeVisible();

      // Update payee settlement end date less than payee plan end date
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("samp 3");
      await page.waitForTimeout(4000);
      await page
        .getByRole("gridcell", { name: "Oct 01, 2023 - Dec 31, 2024" })
        .locator("svg")
        .click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Nov 01, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.waitForTimeout(4000);
      await page
        .getByRole("gridcell", { name: "Dec 31, 2024", exact: true })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Nov 01, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await expect(page.getByText("Custom config yet to be set")).toBeVisible();
      await page
        .getByRole("gridcell", { name: "Nov 01, 2024", exact: true })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Oct 29, 2024");
      await page.getByPlaceholder("Select date").press("Enter");
      await expect(
        page.getByText(
          "Payee Settlement End Date should not be less than payee end date: 01-Nov-"
        )
      ).toBeVisible();
    });

    test("Test to check criteria operations", async ({ adminPage }) => {
      const page = adminPage.page;
      // Create plan and add payee
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page
        .getByPlaceholder("Enter name")
        .fill("Plan check criteria operations");
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click({ force: true });
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Jan 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Feb 29, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();
      await page.getByLabel("S1Samp 1 User").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await expect(page.getByText("0payee saved")).toBeVisible();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Successfully saved 1 payees")).toBeVisible();
      await expect(page.getByText("1payee saved")).toBeVisible();

      // Import criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Import an existing").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page.waitForSelector("text=Select fiscal year", { timeout: 2000 });
      await page
        .locator("div")
        .filter({ hasText: /^Select fiscal year$/ })
        .nth(2)
        .click();
      await page
        .locator("div:nth-child(2) > .ant-select-item-option-content")
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator("div")
        .filter({ hasText: "Select commission plan" })
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator(".ant-select-selection-search-input")
        .fill("plan criteria check 6");
      await page
        .locator("span")
        .filter({ hasText: "Plan Criteria Check 6" })
        .locator("span")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Choose component$/ })
        .nth(2)
        .click();
      await page.getByText("Simple").first().click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(page.getByText("Component imported")).toBeVisible();

      // Clone criteria
      await page.waitForTimeout(2000);
      await page.locator(".relative > .p-4").click();
      await page.locator(".p-4 > div > div:nth-child(3) > .ant-btn").click();
      await page.getByText("Clone", { exact: true }).click();
      await expect(
        page.getByText("Component cloned successfully")
      ).toBeVisible();

      // Invalid expression in simple criteria check
      await page.getByText("Simple (Copy)").first().click();
      await page.getByTestId("expression-input-box").nth(1).click();
      await page.getByTestId("auto-suggestion-view").getByText("+").click();
      await expect(page.getByRole("button", { name: "Save" })).toBeDisabled();
      await page.getByRole("textbox").nth(3).fill("10");
      await page.getByText("10Integer").first().click();
      await expect(page.getByRole("button", { name: "Save" })).toBeEnabled();

      // Create new conditional criteria
      // Invalid expression in conditional criteria check
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page
        .locator("span")
        .filter({ hasText: "ConditionalChoose the" })
        .first()
        .click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Conditional");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByLabel("Databook*").click();
      await page.getByText("Deals 2").click();
      await page.getByLabel("Datasheet*").click();
      await page.getByText("ds 1").click();
      await page.getByLabel("Email field*").click();
      await page.getByText("Email", { exact: true }).click();
      await page.getByLabel("Date field*").click();
      await page.getByText("Deal Date").click();
      await page.getByRole("button", { name: "Create" }).click();
      await page.getByText("Conditional").first().click();
      await page.getByPlaceholder("Press Ctrl + H for help").first().click();
      await page.getByText("IsEmpty").click();
      await page
        .locator(
          '//div[@data-testid="ever-select"][.//span[text()="Please Select"]]'
        )
        .last()
        .click();
      await page.locator("span").filter({ hasText: "Email" }).click();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.keyboard.press("Escape");
      await page.getByText("THEN").click();
      await page.getByPlaceholder("Press Ctrl + H for help").first().click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByText("ELSE").click();
      await page.getByPlaceholder("Press Ctrl + H for help").click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByTestId("auto-suggestion-view").getByText("+").click();
      await expect(page.getByRole("button", { name: "Save" })).toBeDisabled();
      await page.locator("input[placeholder]").last().click();
      await page.locator("input[placeholder]").last().fill("10");
      await page.locator('[title="10"]').first().click();
      await expect(page.getByRole("button", { name: "Save" })).toBeEnabled();
      await page.getByRole("button", { name: "Save" }).click();

      // Import and check invalid expression in tier criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Import an existing").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select fiscal year$/ })
        .nth(2)
        .click();
      await page.getByText("2024", { exact: true }).last().click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator("div")
        .filter({ hasText: "Select commission plan" })
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator(".ant-select-selection-search-input")
        .fill("plan criteria check 2");
      await page
        .locator("span")
        .filter({ hasText: "Plan Criteria Check 2" })
        .locator("span")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Choose component$/ })
        .nth(2)
        .click();
      await page.getByText("Tier 1").click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Create" }).click();
      await page
        .getByText("Component imported successfully")
        .waitFor({ state: "visible" });
      await page
        .getByText("Component imported successfully")
        .waitFor({ state: "hidden" });
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page.getByTestId("auto-suggestion-view").getByText("*").click();
      await expect(page.getByRole("button", { name: "Save" })).toBeDisabled();
      await page.waitForTimeout(2000);
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .fill("5");
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .press("Enter");
      await expect(page.getByRole("button", { name: "Save" })).toBeEnabled();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Apply tier formula to").click();

      // Import and check invalid expression in quota criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Import an existing").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select fiscal year$/ })
        .nth(2)
        .click();
      await page.getByText("2024", { exact: true }).last().click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator("div")
        .filter({ hasText: "Select commission plan" })
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator(".ant-select-selection-search-input")
        .fill("plan criteria check 2");
      await page
        .locator("span")
        .filter({ hasText: "Plan Criteria Check 2" })
        .locator("span")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Choose component$/ })
        .nth(2)
        .click();
      await page.getByText("Quota").nth(1).click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Create" }).click();
      await page
        .getByText("Component imported successfully")
        .waitFor({ state: "visible" });
      await page
        .getByText("Component imported successfully")
        .waitFor({ state: "hidden" });
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page.getByTestId("auto-suggestion-view").getByText("*").click();
      await expect(page.getByRole("button", { name: "Save" })).toBeDisabled();
      await page.waitForTimeout(2000);
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .fill("5");
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .press("Enter");
      await expect(page.getByRole("button", { name: "Save" })).toBeEnabled();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Enter formula to compare").nth(1).click();

      // Clone and delete cloned criteria
      await page
        .locator("div")
        .filter({ hasText: /^Quota$/ })
        .nth(2)
        .click();
      await page.waitForTimeout(2000);
      await page
        .locator("//span[text()='Quota']/ancestor::div[3]//div[2]")
        .click();
      await page.getByText("Clone", { exact: true }).nth(1).click();
      await expect(
        page.getByText("Component cloned successfully")
      ).toBeVisible();
      await page
        .locator("div")
        .filter({ hasText: /^Quota \(Copy\)$/ })
        .nth(2)
        .click();
      await page
        .locator(`//span[text()='Quota (Copy)']/ancestor::div[3]//div[2]`)
        .click();
      await page
        .getByRole("menuitem", { name: "Delete" })
        .locator("span")
        .click();
      await expect(
        page.getByText("Delete component: Quota (Copy)")
      ).toBeVisible();
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(
        page.getByText("Component removed successfully")
      ).toBeVisible();

      // Save all criterias
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Plan Component details saved successfully")
      ).toBeVisible();
    });

    test("Test to create plan, publish it and check time machine simulation", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Create plan and add payee
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Plan New 1");
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Mar 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Apr 30, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();
      await page.getByLabel("PLPayout Locked User 2").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByRole("button", { name: "Save" }).click();

      // Import and save criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Import an existing").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select fiscal year$/ })
        .nth(2)
        .click();
      await page.getByText("2024", { exact: true }).last().click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator("div")
        .filter({ hasText: "Select commission plan" })
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator(".ant-select-selection-search-input")
        .fill("plan criteria check 6");
      await page
        .locator("span")
        .filter({ hasText: "Plan Criteria Check 6" })
        .locator("span")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Choose component$/ })
        .nth(2)
        .click();
      await page.getByText("Simple").first().click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Create" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Plan Component details saved successfully")
      ).toBeVisible();

      // Publish plan
      await page.getByRole("button", { name: "Publish", exact: true }).click();
      await page
        .getByRole("dialog")
        .getByRole("button", { name: "Publish" })
        .click();
      await expect(
        page.getByText("You've successfully published Plan New 1")
      ).toBeVisible();
      await page.waitForTimeout(2000);
      await page.keyboard.press("Escape");

      // Check time machine simulation
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Mar 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Mar 31, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page
        .locator(
          "div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.getByText("Payout Locked User 2").click();
      await page.getByRole("button", { name: "Simulate" }).nth(1).click();
      await page
        .getByRole("treegrid")
        .getByText("Payout Locked User 2")
        .click();

      // Check pagination
      await page.getByText("100", { exact: true }).click();
      await page
        .locator("div.ant-select-item.ant-select-item-option[title='10']")
        .click();
      await expect(page.getByText("1 - 10of12rows")).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^1 - 10of12rowsPage of2$/ })
          .getByRole("button")
          .nth(2)
      ).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^1 - 10of12rowsPage of2$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await expect(page.getByText("$3,100.00")).toBeVisible();
      await expect(page.getByText("11 - 12of12rows")).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^11 - 12of12rowsPage of2$/ })
          .getByRole("button")
          .nth(1)
      ).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^11 - 12of12rowsPage of2$/ })
        .getByRole("button")
        .nth(1)
        .click();

      // Check export csv of simulation data
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Export as .CSV" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);
      const results = [];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => {
          const trimmedData = {};
          for (const key in data) {
            trimmedData[key.trim()] = data[key];
          }
          results.push(trimmedData);
        })
        .on("end", () => {
          expect(results.length).toBe(12);
          expect(Object.keys(results[0]).length).toBe(2);
          console.log(results);
        })
        .on("error", (err) => {
          throw err;
        });
      await expect(
        page.getByText(
          "Exported simulated data successfully for Payout Locked User 2 in Simple component"
        )
      ).toBeVisible();

      await page.getByLabel("Close", { exact: true }).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    // 5 different sorting test cases
    const sortingTestCases = [
      {
        sortBy: "asc",
        expectedResult: [
          {
            planId: "3d19ac9a-8d4a-4636-8c24-9cc04463b6ee",
            planName: "Plan Criteria Check 1",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-01-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "a5f5eecb-0a49-4072-afe5-a85b781bf84c",
            planName: "Plan Criteria Check 5",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-12-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b2a4ae6d-588a-4a3d-ab18-7e61f6591039",
            planName: "Plan with Adjustments",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-12-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "4",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 4,
          },
          {
            planId: "77c5c958-318c-47bc-b807-c9d91a6aeb7e",
            planName: "Plan with locked Payees",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d219",
            planName: "Plan with payee errors",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d216",
            planName: "Plan without criteria",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d215",
            planName: "Plan without payees",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d218",
            planName: "Plan without settlement rules",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b223b95d-140c-4203-819d-df20f05086b6",
            planName: "Plan0",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "0",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d214",
            planName: "Plan1",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "4e261ad4-d257-4beb-bf23-7cc4d583d1c7",
            planName: "Plan1 Published",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d217",
            planName: "Published plan",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
        ],
      },
      {
        sortBy: "desc",
        expectedResult: [
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d217",
            planName: "Published plan",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "4e261ad4-d257-4beb-bf23-7cc4d583d1c7",
            planName: "Plan1 Published",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d214",
            planName: "Plan1",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b223b95d-140c-4203-819d-df20f05086b6",
            planName: "Plan0",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "0",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d218",
            planName: "Plan without settlement rules",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d215",
            planName: "Plan without payees",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d216",
            planName: "Plan without criteria",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d219",
            planName: "Plan with payee errors",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "77c5c958-318c-47bc-b807-c9d91a6aeb7e",
            planName: "Plan with locked Payees",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b2a4ae6d-588a-4a3d-ab18-7e61f6591039",
            planName: "Plan with Adjustments",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-12-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "4",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 4,
          },
          {
            planId: "a5f5eecb-0a49-4072-afe5-a85b781bf84c",
            planName: "Plan Criteria Check 5",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-12-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "3d19ac9a-8d4a-4636-8c24-9cc04463b6ee",
            planName: "Plan Criteria Check 1",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-01-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
        ],
      },
      {
        sortBy: "modified_date",
        expectedResult: [
          {
            planId: "a5f5eecb-0a49-4072-afe5-a85b781bf84c",
            planName: "Plan Criteria Check 5",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-12-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "3d19ac9a-8d4a-4636-8c24-9cc04463b6ee",
            planName: "Plan Criteria Check 1",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-01-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b2a4ae6d-588a-4a3d-ab18-7e61f6591039",
            planName: "Plan with Adjustments",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-12-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "4",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 4,
          },
          {
            planId: "4e261ad4-d257-4beb-bf23-7cc4d583d1c7",
            planName: "Plan1 Published",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d216",
            planName: "Plan without criteria",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d218",
            planName: "Plan without settlement rules",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d215",
            planName: "Plan without payees",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d217",
            planName: "Published plan",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d214",
            planName: "Plan1",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d219",
            planName: "Plan with payee errors",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "77c5c958-318c-47bc-b807-c9d91a6aeb7e",
            planName: "Plan with locked Payees",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b223b95d-140c-4203-819d-df20f05086b6",
            planName: "Plan0",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "0",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sortBy: "created_date",
        expectedResult: [
          {
            planId: "a5f5eecb-0a49-4072-afe5-a85b781bf84c",
            planName: "Plan Criteria Check 5",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-12-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "3d19ac9a-8d4a-4636-8c24-9cc04463b6ee",
            planName: "Plan Criteria Check 1",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-01-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b2a4ae6d-588a-4a3d-ab18-7e61f6591039",
            planName: "Plan with Adjustments",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-12-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "4",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 4,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d217",
            planName: "Published plan",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d214",
            planName: "Plan1",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d219",
            planName: "Plan with payee errors",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d216",
            planName: "Plan without criteria",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d218",
            planName: "Plan without settlement rules",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d215",
            planName: "Plan without payees",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "4e261ad4-d257-4beb-bf23-7cc4d583d1c7",
            planName: "Plan1 Published",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "77c5c958-318c-47bc-b807-c9d91a6aeb7e",
            planName: "Plan with locked Payees",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b223b95d-140c-4203-819d-df20f05086b6",
            planName: "Plan0",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "0",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sortBy: "plan_start_date",
        expectedResult: [
          {
            planId: "b223b95d-140c-4203-819d-df20f05086b6",
            planName: "Plan0",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "0",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "4e261ad4-d257-4beb-bf23-7cc4d583d1c7",
            planName: "Plan1 Published",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "77c5c958-318c-47bc-b807-c9d91a6aeb7e",
            planName: "Plan with locked Payees",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "b2a4ae6d-588a-4a3d-ab18-7e61f6591039",
            planName: "Plan with Adjustments",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-12-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User",
                  lastName: "4",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 4,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d217",
            planName: "Published plan",
            isDraft: false,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d214",
            planName: "Plan1",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d215",
            planName: "Plan without payees",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: null,
            totalPayeesInPlan: 0,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d216",
            planName: "Plan without criteria",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d218",
            planName: "Plan without settlement rules",
            isDraft: true,
            planStartDate: "2023-11-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Modified Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Modified",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Removed",
                  lastName: "User",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "a5f5eecb-0a49-4072-afe5-a85b781bf84c",
            planName: "Plan Criteria Check 5",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-12-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Payout Locked",
                  lastName: "User 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "3d19ac9a-8d4a-4636-8c24-9cc04463b6ee",
            planName: "Plan Criteria Check 1",
            isDraft: true,
            planStartDate: "2023-10-01T00:00:00+00:00",
            planEndDate: "2024-01-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "Criteria",
                  lastName: "User 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "372c28d1-6f86-4e35-ad5f-3bdbd610d219",
            planName: "Plan with payee errors",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-11-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User",
                  lastName: "3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
    ];

    // Changing this graphql to rest api - post that will be fixed
    test.describe.skip(
      "Sorting functionality in canvas plans display",
      { tag: ["@regression", "@commissionplan", "@adminchamp-5"] },
      () => {
        test.beforeAll(async ({ request }) => {
          token = await bearerTokenGenerator(request, creds.QACANVAS_CREDS);
        });

        sortingTestCases.forEach(({ sortBy, expectedResult }) => {
          test(`Sorts by ${sortBy}`, async ({ request }) => {
            const response = await request.post("/graphql", {
              data: {
                query: requestBodies.allPlans,
                variables: {
                  searchTerm: "",
                  component: "commission_plans",
                  fiscalYear: 2023,
                  planStatus: "all",
                  offsetValue: 0,
                  limitValue: 12,
                  showActivePlans: false,
                  showSpiffPlans: false,
                  sortBy: sortBy,
                },
              },
              headers: {
                Authorization: token,
              },
            });
            const resp = await response.json();
            console.log(resp.data);
            expect(resp.data.allPlans.length).toBe(12);
            expect(resp.data.allPlans).toEqual(expectedResult);
          });
        });
      }
    );
  }
);
