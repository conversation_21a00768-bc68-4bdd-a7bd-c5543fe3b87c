import ManagerEffectiveEndDate from "../../../../../test-objects/managerEffEndDate-objects";
import Reevaluate from "../../../../../test-objects/reEvaluate-objects";
import CrystalPage from "../../../../../test-objects/crystalselectoppfilters-objects";

const {
  reEvaluateFixtures: { test, expect },
} = require("../../../../fixtures");

/**
 * Clears local and session storage before each test.
 */
test.beforeEach(async ({ adminPage }) => {
  await adminPage.page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});

test.describe(
  "Re evaluate - Get localized currency",
  { tag: ["@commissionplan", "@regression", "@adminchamp-5"] },
  () => {
    test("plan page validation", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17799,",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The value should be 0.00 not -0.00",
        }
      );
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);

      // Validate plans
      await managerObj.navigate("/plans");
      await managerObj.openPlan("Plan 1_Copy");
      await managerObj.openPlanComponent("simple");
      await managerObj.simulatePlanComponent();
      await managerObj.setStartDateandEndDateinSimulate(
        "Jan 01, 2024",
        "Dec 31, 2024"
      );
      await managerObj.clickRun();
      await page.waitForTimeout(3000);
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Total Commission:\$0\.00$/ })
          .nth(1)
      ).toBeVisible();
      await managerObj.clickExitCanvas();
    });

    test("Payout validation", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17799, INTER-T17797,INTER-T17796, INTER-T17795, INTER-T17794 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The value should be 0.00 not -0.00",
        }
      );
      const page = adminPage.page;
      // Validate payouts
      const managerObj = new ManagerEffectiveEndDate(page);
      const reEvaluate = new Reevaluate(page);
      await managerObj.navigate("/commissions");
      await page.waitForTimeout(3000);
      //   await page.pause();
      await reEvaluate.setPayoutsDate("Aug 31, 2024");
      await managerObj.searchPayeeinPayouts("<EMAIL>");
      await page.waitForTimeout(3000);
      await managerObj.clickPayeeinPayouts("payee");
      // verify Payout widget
      await expect(page.getByTitle("₹0.00").locator("span")).toBeVisible();
      // verify statement
      // Payout summary - Payout from current period
      await reEvaluate.clickPayoutFromCurrentperiod();
      await reEvaluate.clickPlanFromCurrentperiod("plan 1_Copy");
      await reEvaluate.openComponentinpayout("simple");
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Plan level commission₹0\.00$/ })
          .first()
      ).toBeVisible();
      await page.getByRole("tab", { name: "cond" }).click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Plan level commission₹0\.00$/ })
          .first()
      ).toBeVisible();
      await page.getByRole("tab", { name: "quota", exact: true }).click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Plan level commission₹0\.00$/ })
          .first()
      ).toBeVisible();
      await page.getByLabel("Close").click();

      // commission summary
      await page.getByText("Commission Summary").click();
      await page.waitForTimeout(3000);
      await expect(
        page.getByRole("gridcell", { name: "₹" }).first()
      ).toBeVisible();
      // Earned commission
      await reEvaluate.clickEarnedCommissions();
      await page.waitForTimeout(3000);
      await expect(
        page.getByRole("gridcell", { name: "₹" }).nth(1)
      ).toBeVisible();
      await reEvaluate.clickEarnedCommissions();
      // Deferred Commission
      await reEvaluate.clickDeferredCommissions();
      await page
        .getByLabel("Commission Summary")
        .getByText("plan 1_Copy")
        .nth(1)
        .click();
      await page.waitForTimeout(3000);
      await expect(
        page.getByRole("row", { name: "simple ₹" }).getByRole("gridcell").nth(1)
      ).toBeVisible();
      await expect(
        page.getByRole("row", { name: "cond 1 ₹" }).getByRole("gridcell").nth(1)
      ).toBeVisible();
      await page
        .getByRole("row", { name: "quota ₹" })
        .getByRole("gridcell")
        .nth(1)
        .click();
    });

    test("Crystal validation", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17799, INTER-T17797,INTER-T17796, INTER-T17795, INTER-T17794 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The value should be 0.00 not -0.00",
        }
      );
      const page = adminPage.page;
      const crystalObj = new CrystalPage(page);
      // Validate in crystal
      await crystalObj.goToCrystalPage();
      const page1 = await crystalObj.openSimulator("Crystal 1");
      await expect(page1.getByText("₹0.00Current Payout")).toBeVisible();
      await crystalObj.selectOpportunities(page1);
      await expect(page1.getByText("₹0.00Current Payout")).toBeVisible();
    });

    test("Report object validation", async ({ adminPage }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17799, INTER-T17797,INTER-T17796, INTER-T17795, INTER-T17794 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The value should be 0.00 not -0.00",
        }
      );
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      await managerObj.navigate("/databook");
      await managerObj.openDatabook("Report book");
      await managerObj.opensheetInDatabook("comm summary");
      await managerObj.addFilterinDatasheet(
        "Payee Email",
        "Contains",
        "<EMAIL>"
      );
      await managerObj.addAnotherConditioninFilter(
        "Period Start Date",
        "Is in Last Fiscal year"
      );
      await expect(page.getByTestId("pt-0").first()).toBeVisible();
    });
  }
);
