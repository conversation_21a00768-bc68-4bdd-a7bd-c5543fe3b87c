const {
  plansBalaFixtures: { test, expect },
  plansBalaPayeeFixtures: { test: test1, expect: expect1 },
  plansBalaPayeeFixtures2: { test: test2, expect: expect2 },
} = require("../../../fixtures");

const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");

test.describe(
  "Simulation Testcases",
  { tag: ["@regression", "@commissionplan", "@adminchamp-5"] },
  () => {
    test("Simple Simulation Sort", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackSimple\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151005", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151064", { exact: "true" })).toBeHidden();

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151064", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151005", { exact: "true" })).toBeHidden();

      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page
          .getByRole("row", { name: "1 151005 1,000 30-Jan-2024" })
          .getByText("1,000", { exact: "true" })
      ).toBeVisible();
      await expect(
        page
          .getByRole("row", { name: "4 151061 3,000 30-Jan-2024" })
          .getByText("3,000", { exact: "true" })
      ).toBeHidden();

      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page
          .getByRole("row", { name: "2 151061 3,000 30-Jan-2024" })
          .getByText("3,000", { exact: "true" })
      ).toBeVisible();
      await expect(
        page
          .getByRole("row", { name: "1 151005 1,000 30-Jan-2024" })
          .getByText("1,000", { exact: "true" })
      ).toBeHidden();

      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeHidden();
      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("false", { exact: "true" }).first()
      ).toBeVisible();

      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeVisible();

      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$10,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$30,000.00", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$30,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$10,000.00", { exact: "true" }).first()
      ).toBeHidden();
    });

    test("Conditional Simulation Sort", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Conditional", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackConditional\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151005", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151064", { exact: "true" })).toBeHidden();

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151064", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151005", { exact: "true" })).toBeHidden();

      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeHidden();

      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeHidden();
      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("false", { exact: "true" }).first()
      ).toBeVisible();

      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeVisible();

      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$500.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$3,000.00", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$3,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$500.00", { exact: "true" }).first()
      ).toBeHidden();
    });

    test("Team Simulation Sort", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Team", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackTeam\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151005", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151064", { exact: "true" })).toBeHidden();

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151064", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151005", { exact: "true" })).toBeHidden();

      await page.getByText("amount", { exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeHidden();
      await page.getByText("amount", { exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeHidden();

      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeHidden();
      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("false", { exact: "true" }).first()
      ).toBeVisible();

      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeVisible();

      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$20,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$60,000.00", { exact: "true" })
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$60,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$20,000.00", { exact: "true" }).first()
      ).toBeHidden();
    });

    test("Quota Simulation Sort", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Quota", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackQuota\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151005", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151064", { exact: "true" })).toBeHidden();

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151064", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151005", { exact: "true" })).toBeHidden();

      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeHidden();

      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeHidden();
      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("false", { exact: "true" }).first()
      ).toBeVisible();

      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeVisible();

      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$5.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$15.00", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$15.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$5.00", { exact: "true" }).first()
      ).toBeHidden();
    });

    test("Tier Simulation Sort", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Tier", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackTier\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151005", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151064", { exact: "true" })).toBeHidden();

      await page.locator("div").filter({ hasText: /^id$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("151064", { exact: "true" })).toBeVisible();
      await expect(page.getByText("151005", { exact: "true" })).toBeHidden();

      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator('div[col-id*="amount"][role="columnheader"]')
        .locator("div", { hasText: "amount" })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("3,000", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("1,000", { exact: "true" }).first()
      ).toBeHidden();

      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeHidden();
      await page.locator("div").filter({ hasText: /^end$/ }).first().click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("30-Mar-2024", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("30-Jan-2024", { exact: "true" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("false", { exact: "true" }).first()
      ).toBeVisible();

      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^flag$/ })
        .first()
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "-", exact: true }).first()
      ).toBeVisible();

      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$5,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$60,000.00", { exact: "true" }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText("$60,000.00", { exact: "true" }).first()
      ).toBeVisible();
      await expect(
        page.getByText("$5,000.00", { exact: "true" }).first()
      ).toBeHidden();
    });

    test("Simulation Pagination", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();

      await expect(page.getByText("of1").nth(1)).toBeVisible();

      await page.getByText("100", { exact: true }).click();
      await page.locator("div").filter({ hasText: /^50$/ }).nth(2).click();
      await expect(
        page.getByRole("gridcell", { name: "51", exact: true })
      ).toBeHidden();
      await expect(page.locator("body")).toContainText("of2");
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^1 - 50of51rowsPage of2$/ })
          .getByRole("textbox")
      ).toHaveValue("1");
      await page
        .locator("div")
        .filter({ hasText: /^1 - 50of51rowsPage of2$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await expect(
        page.getByRole("gridcell", { name: "51", exact: true })
      ).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^51 - 51of51rowsPage of2$/ })
          .getByRole("textbox")
      ).toHaveValue("2");
      await page.getByText("50").first().click();

      await page.locator("span").filter({ hasText: /^20$/ }).click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^1 - 20of51rowsPage of3$/ })
          .getByRole("textbox")
      ).toHaveValue("1");
      await expect(page.locator("body")).toContainText("of3");
      await expect(
        page.getByRole("gridcell", { name: "21", exact: true })
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^1 - 20of51rowsPage of3$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^21 - 40of51rowsPage of3$/ })
          .getByRole("textbox")
      ).toHaveValue("2");
      await expect(
        page.getByRole("gridcell", { name: "41" }).first()
      ).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^21 - 40of51rowsPage of3$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^41 - 51of51rowsPage of3$/ })
          .getByRole("textbox")
      ).toHaveValue("3");
      await expect(
        page.getByRole("gridcell", { name: "51", exact: true }).first()
      ).toBeVisible();
    });

    test("Simulation Download", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page
        .locator("div")
        .filter({ hasText: /^BackSimple\/payee balaExport as \.CSV$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByLabel("amount").check();
      await page.getByLabel("end").check();
      await page.getByLabel("flag").check();
      await page.getByLabel("email").check();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForLoadState("networkidle");

      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Export as .CSV" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);

      const results = [];
      const requiredColumns = ["id", "amount", "end", "Commission", "flag"];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => results.push(data))
        .on("end", () => {
          expect(results.length).toBe(51);
          const allColumnsPresent = requiredColumns.every(
            (column) => column in results[0]
          );
          // Assert that all required columns are present
          expect(allColumnsPresent).toBe(true);
        })
        .on("error", (err) => {
          throw err;
        });
    });

    test("Simulation Download Pagination", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=3e0427aa-419f-41c0-adbc-8ec5e0869401",
        { waitUntil: "networkidle" }
      );
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for").click();

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();

      await page.getByText("100", { exact: true }).click();
      await page.locator("div").filter({ hasText: /^20$/ }).nth(2).click();

      await expect(
        page.getByRole("button", { name: "Export as .CSV" })
      ).toBeVisible();

      const results = [];
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Export as .CSV" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);

      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => results.push(data))
        .on("end", () => {
          expect(results.length).toBe(51);
        })
        .on("error", (err) => {
          throw err;
        });
    });

    test("Simulation Add and Remove and Edit Duration Payee", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=28b2e69d-3633-484d-8f71-7fea2435fd10",
        { waitUntil: "networkidle" }
      );
      await page.getByText("payee2 bala").click();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Successfully saved 2 payees")).toBeVisible();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee2").click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });

      await page.waitForLoadState("networkidle");
      await page.locator(".ag-cell > .ml-3").click();
      await expect(
        page.getByRole("gridcell", { name: "381008" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "$20,000.00" }).first()
      ).toBeVisible();

      await page.getByLabel("Close", { exact: true }).click();
      await page
        .locator(
          ".ag-row-odd > .ag-cell > .w-full > .ml-auto > div:nth-child(2) > .w-5"
        )
        .click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Successfully saved 1 payees")).toBeVisible();
      await page.waitForLoadState("networkidle");

      await page
        .getByRole("gridcell", { name: "Dec 01, 2023 -" })
        .locator("svg")
        .click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Feb 29, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Successfully saved 1 payees")).toBeVisible();
      await page.waitForLoadState("networkidle");

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await expect(
        page.getByRole("listitem").getByText("payee bala")
      ).toBeHidden();
    });

    test("Simulation Profile and Export Draft", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=e887b74c-0b8d-4142-be0d-b0366c32bd7b",
        { waitUntil: "networkidle" }
      );

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await expect(
        page.locator("//div[@col-id='0']//div[text()='PB']")
      ).toBeVisible();
      await page.locator("//div[@col-id='0']//div[text()='PB']").click();
      await expect(page.getByText("Profile", { exact: true })).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Payroll Details" })
      ).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Reporting and Teams" })
      ).toBeVisible();
      await expect(page.getByRole("tab", { name: "Quota" })).toBeVisible();
      await expect(page.getByRole("tab", { name: "Draws" })).toBeVisible();
      await page.getByLabel("Close").nth(4).click();

      await page.locator(".ag-cell > .ml-3").click();
      await expect(
        page.getByRole("button", { name: "Export as .CSV" })
      ).toBeEnabled();
      await expect(
        page.getByRole("gridcell", { name: "371010" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371007" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371009" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371011" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371012" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371013" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371015" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371014" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371016" })
      ).toBeVisible();
      await expect(page.locator("body")).toContainText(
        "Total Commission$18,000.00"
      );
    });

    test("Simulation Profile and Export Published", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=9b2904bb-bd62-4c07-95d6-5a4510b931d6",
        { waitUntil: "networkidle" }
      );

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await expect(
        page.locator("//div[@col-id='0']//div[text()='PB']")
      ).toBeVisible();
      await page.locator("//div[@col-id='0']//div[text()='PB']").click();
      await expect(page.getByText("Profile", { exact: true })).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Payroll Details" })
      ).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Reporting and Teams" })
      ).toBeVisible();
      await expect(page.getByRole("tab", { name: "Quota" })).toBeVisible();
      await expect(page.getByRole("tab", { name: "Draws" })).toBeVisible();
      await page.getByLabel("Close").nth(4).click();

      await page.locator(".ag-cell > .ml-3").click();
      await expect(
        page.getByRole("button", { name: "Export as .CSV" })
      ).toBeEnabled();
      await expect(
        page.getByRole("gridcell", { name: "371010" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371007" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371009" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371011" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371012" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371013" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371015" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371014" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371016" })
      ).toBeVisible();
      await expect(page.locator("body")).toContainText(
        "Total Commission$18,000.00"
      );
    });

    test("Simulation Profile and Export Spiff", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=16fb4a85-60f1-4571-a84e-ba99e0d2a73b",
        { waitUntil: "networkidle" }
      );

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Simple", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await expect(
        page.locator("//div[@col-id='0']//div[text()='PB']")
      ).toBeVisible();
      await page.locator("//div[@col-id='0']//div[text()='PB']").click();
      await expect(page.getByText("Profile", { exact: true })).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Payroll Details" })
      ).toBeVisible();
      await expect(
        page.getByRole("tab", { name: "Reporting and Teams" })
      ).toBeVisible();
      await expect(page.getByRole("tab", { name: "Quota" })).toBeVisible();
      await expect(page.getByRole("tab", { name: "Draws" })).toBeVisible();
      await page.getByLabel("Close").nth(4).click();

      await page.locator(".ag-cell > .ml-3").click();
      await expect(
        page.getByRole("button", { name: "Export as .CSV" })
      ).toBeEnabled();
      await expect(
        page.getByRole("gridcell", { name: "371010" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371007" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371009" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371011" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371012" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371013" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371015" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371014" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "371016" })
      ).toBeVisible();
      await expect(page.locator("body")).toContainText(
        "Total Commission$18,000.00"
      );
    });

    test("Simulation Sort and Column Rearrange", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=e411beb8-33a9-4fd7-a289-37b044a61b0f",
        { waitUntil: "networkidle" }
      );

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Quota Row", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.waitForLoadState("networkidle");

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      await page.waitForLoadState("networkidle");

      // Adjusted Value should not be visible when sorted ascending
      await page.getByRole("columnheader", { name: "amount" }).click();
      await expect(page.getByRole("gridcell", { name: "151032" })).toBeHidden();

      // Set Page Limit to 10
      await page.getByText("100per page").click();
      await page.getByText("10", { exact: true }).nth(4).click();
      await page.waitForLoadState("networkidle");

      // Sort ID
      await page.locator("div").filter({ hasText: /^id$/ }).nth(1).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "151006" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151023" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151024" }).first()
      ).toBeHidden();

      // Change Page
      await page
        .locator("div")
        .filter({ hasText: /^1 - 10of51rowsPage of6$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "151024" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151032" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151034" }).first()
      ).toBeHidden();

      await page
        .locator("div")
        .filter({ hasText: /^11 - 20of51rowsPage of6$/ })
        .getByRole("button")
        .nth(1)
        .click();

      // Sort Quota Erosion
      await page
        .locator("div")
        .filter({ hasText: /^Quota Erosion$/ })
        .nth(1)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "151005" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151032" }).nth(1)
      ).toBeHidden();

      // Sort Commission
      await page
        .locator("div")
        .filter({ hasText: /^Commission$/ })
        .nth(1)
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "151005" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151064" }).first()
      ).toBeHidden();
    });

    test("Simulation Column Rearrange Download", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=e411beb8-33a9-4fd7-a289-37b044a61b0f",
        { waitUntil: "networkidle" }
      );

      await page.getByRole("button", { name: "Time Machine" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Criteria$/ })
        .nth(2)
        .click();
      await page.getByText("Quota Row", { exact: true }).nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .fill("Jan 01, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("Start date")
        .press("Enter");

      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .fill("Mar 31, 2024");
      await page
        .locator("div")
        .filter({ hasText: /^Period$/ })
        .getByPlaceholder("End date")
        .press("Enter");

      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("payee bala").click();
      await page.getByRole("button", { name: "Simulate" }).click();

      await page.getByText("Loading Simulator").waitFor({ state: "visible" });
      await page
        .getByText("Loading Simulator")
        .waitFor({ state: "hidden", timeout: 90000 });
      await page.locator(".ag-cell > .ml-3").click();
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Export as .CSV" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);

      const results = [];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => results.push(data))
        .on("end", () => {
          expect(results.length).toBe(51);
          expect(Object.keys(results[0])[0]).toBe("amount");
          expect(Object.keys(results[0])[4]).toBe("id");
          expect(Object.keys(results[0])[5]).toBe("Commission");
        })
        .on("error", (err) => {
          throw err;
        });
    });

    test.describe(() => {
      test("Simulation Adjustment", async ({ adminPage }) => {
        const page = adminPage.page;
        // Make Adjustment Ignore 151016
        await page.goto(
          "http://localhost:3000/databook/91a9b92b-7f62-4e8c-8bba-459a316967f5",
          { waitUntil: "networkidle" }
        );
        await page.getByRole("tab", { name: "DS Temp Adj" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator(".ant-select-item-option-content").first().click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Contains$/ })
          .nth(1)
          .click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("151016");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByRole("row", { name: "1 <EMAIL> 3,000" })
          .locator("svg")
          .click();
        await page.locator("section").getByRole("textbox").click();
        await page.locator("section").getByRole("textbox").fill("Ignore");
        await page.getByTestId("update-button-adjustment-modal").click();
        await expect(
          page.getByText("Record adjusted successfully")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test("Simulation Adjustment Generate DS", async ({ adminPage }) => {
        const page = adminPage.page;
        // Make Adjustment Ignore 151016
        await page.goto(
          "http://localhost:3000/databook/91a9b92b-7f62-4e8c-8bba-459a316967f5",
          { waitUntil: "networkidle" }
        );
        await page.getByRole("tab", { name: "DS Temp Adj" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Update Data" }).click();
        await expect(
          page.getByText("Datasheet sync request has")
        ).toBeVisible();
        await expect(page.getByText("Please wait until it is")).toBeVisible();
        await page
          .getByText("Please wait until it is")
          .waitFor({ state: "hidden", timeout: 120000 });
        await expect(
          page.getByText("Datasheet has been generated")
        ).toBeVisible();
        await page.waitForLoadState("networkidle");
      });

      test("Simulation Adjustment Time Machine", async ({ adminPage }) => {
        const page = adminPage.page;
        // Run Simulation
        await page.goto(
          "http://localhost:3000/plans?plan_id=ee4ffab8-220d-45a1-9b8f-1f715bfa9cab",
          { waitUntil: "networkidle" }
        );

        await page.getByRole("button", { name: "Time Machine" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Criteria$/ })
          .nth(2)
          .click();
        await page.getByText("Simple", { exact: true }).nth(1).click();
        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("Start date")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("Start date")
          .fill("Jan 01, 2024");
        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("Start date")
          .press("Enter");

        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("End date")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("End date")
          .fill("Mar 31, 2024");
        await page
          .locator("div")
          .filter({ hasText: /^Period$/ })
          .getByPlaceholder("End date")
          .press("Enter");

        await page.getByText("Simulate for Select payee").click();
        await page.getByRole("listitem").getByText("payee2 bala").click();
        await page.getByRole("button", { name: "Simulate" }).click();

        await page.getByText("Loading Simulator").waitFor({ state: "visible" });
        await page
          .getByText("Loading Simulator")
          .waitFor({ state: "hidden", timeout: 90000 });
        await page.locator(".ag-cell > .ml-3").click();

        await expect(page.getByText("151016", { exact: "true" })).toBeHidden();
      });
    });
  }
);

test1(
  "Simulation DS Permission",
  { tag: ["@regression", "@commissionplan", "@adminchamp-5"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/plans?plan_id=272ff166-ed32-4253-9a11-f1b0eed5eb10",
      { waitUntil: "networkidle" }
    );
    await page.getByRole("button", { name: "Time Machine" }).click();
    await page
      .locator("div")
      .filter({ hasText: /^Select Criteria$/ })
      .nth(2)
      .click();
    await page.getByText("Simple", { exact: true }).nth(1).click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .fill("Jan 01, 2024");
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .press("Enter");

    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .fill("Mar 31, 2024");
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .press("Enter");

    await page.getByText("Simulate for Select payee").click();
    await page.getByRole("listitem").getByText("payee bala").click();
    await page.getByRole("button", { name: "Simulate" }).click();

    await page.getByText("Loading Simulator").waitFor({ state: "visible" });
    await page
      .getByText("Loading Simulator")
      .waitFor({ state: "hidden", timeout: 90000 });
    await page.locator(".ag-cell > .ml-3").click();
    await page.waitForLoadState("networkidle");
    await expect1(
      page.getByRole("gridcell", { name: "true", exact: true })
    ).toBeHidden();

    await expect1(
      page.getByRole("gridcell", { name: "<EMAIL>", exact: true })
    ).toBeHidden();
  }
);

test2(
  "Simulation DS Permission Payee2",
  { tag: ["@regression", "@commissionplan", "@adminchamp-5"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/plans?plan_id=272ff166-ed32-4253-9a11-f1b0eed5eb10",
      { waitUntil: "networkidle" }
    );
    await page.getByRole("button", { name: "Time Machine" }).click();
    await page
      .locator("div")
      .filter({ hasText: /^Select Criteria$/ })
      .nth(2)
      .click();
    await page.getByText("Simple", { exact: true }).nth(1).click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .fill("Jan 01, 2024");
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date")
      .press("Enter");

    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .click();
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .fill("Mar 31, 2024");
    await page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date")
      .press("Enter");

    await page.getByText("Simulate for Select payee").click();
    await page.getByRole("listitem").getByText("payee2 bala").click();
    await page.getByRole("button", { name: "Simulate" }).click();

    await page.getByText("Loading Simulator").waitFor({ state: "visible" });
    await page
      .getByText("Loading Simulator")
      .waitFor({ state: "hidden", timeout: 90000 });
    await page.locator(".ag-cell > .ml-3").click();
    await page.waitForLoadState("networkidle");
    await expect2(
      page.getByRole("button", { name: "Export as .CSV" })
    ).toBeDisabled();
  }
);
