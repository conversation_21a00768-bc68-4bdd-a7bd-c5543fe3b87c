import { expect } from "@playwright/test";
import OpportunityPage from "../../../../test-objects/cpq/opportunitiesPage-objects.js";
import QuoteListPage from "../../../../test-objects/cpq/quotesListPage-objects.js";
import ProductPage from "../../../../test-objects/cpq/selectProductPage-objects.js";
import SettingsPage from "../../../../test-objects/cpq/settingsPage-objects.js";

const testData = JSON.parse(
  JSON.stringify(require("../../../../testData/testData.json"))
);

const {
  cpqTestFixtures: { test },
} = require("../../../fixtures.js");

test.beforeAll(async ({ adminPage }) => {
  const quoteListPage = new QuoteListPage(adminPage.page);
  await quoteListPage.openApp("/cpq/quotes");
  const settingsPage = new SettingsPage(adminPage.page);
  await settingsPage.refreshDatasheets();
});

test.describe("CPQ - Create Quote", { tag: ["@cpq-1", "@regression"] }, () => {
  test("Validate user is able to publish quote", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T25500, INTER-T25510" },
      {
        type: "Description",
        description: "Validate user is able to Publish quote",
      },
      {
        type: "Precondition",
        description: "Login to an CPQ Client",
      },
      {
        type: "Expected Behaviour",
        description:
          "User should be able to publish quote without non-madatory fields",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.searchAndSelectQuote(testData.searchOpp);

    const productPage = new ProductPage(adminPage.page, {
      productNames: ["Standard White Glove Support", "One-time Implementation"],
      pricePointOpt: testData["Everstage Platform"].pricePointOption,
      colHead: "net_unit_price",
      rowHeads: [
        "CONNECTOR",
        "PLATFORM",
        "SUPPORT",
        "IMPLEMENTATION",
        "PREMIUMSUPPORT",
      ],
    });
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();
    await productPage.updateProductDetails(testData);

    await productPage.clickPublish();
    await opportunityPage.verifySuccessMessage(
      testData.successMessagePublishQuote
    );

    await productPage.clickExit();

    // Verify if the quote is in "Approved" status
    //await quoteListPage.searchQuote(generatedQuoteName);
    const status = await quoteListPage.getQuoteStatus();
    expect(status).toBe("Approved");
    console.log(`Status of the created quote: ${status}`);
  });

  test("Validate user is able to Publish quote with same name", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T25502, INTER-T25506" },
      {
        type: "Description",
        description: "Validate user is able to Publish quote with same name",
      },
      {
        type: "Precondition",
        description: "Login to an CPQ Client",
      },
      {
        type: "Expected Behaviour",
        description: "User should be able to publish quote",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    const firstQuoteName = await opportunityPage.createNewQuote();

    const productPage = new ProductPage(adminPage.page, {
      productNames: ["One-time Implementation"],
      pricePointOpt: testData["Everstage Platform"].pricePointOption,
      colHead: "net_unit_price",
      rowHeads: ["IMPLEMENTATION"],
    });
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();
    await productPage.clickPublish();
    await productPage.clickExit();

    //create 2nd quote with same name and opp
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    await opportunityPage.createNewQuote(true);
    await opportunityPage.fillQuoteName(firstQuoteName);
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();
    await productPage.clickPublish();
    await productPage.clickExit();

    //search quotes are available
    await quoteListPage.searchQuote(firstQuoteName);
    const quoteCount = await quoteListPage.getQuoteCount(firstQuoteName);
    expect(quoteCount).toBeGreaterThan(1);
    console.log(
      `Number of quotes with same name and opportunity ${firstQuoteName}:`,
      quoteCount
    );
  });

  test("Validate user is not able to publish an quote without adding 'valid till' date", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T25509" },
      {
        type: "Description",
        description: "Create quote without 'Valid Till' date",
      },
      {
        type: "Precondition",
        description: "Login to an CPQ Client",
      },
      {
        type: "Expected Behaviour",
        description:
          "Appropriate error message should be displayed and not able to publish",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.searchAndSelectQuote(testData.searchOpp);
    await opportunityPage.clearValidTillDate();
    await opportunityPage.verifySuccessMessage(
      testData.errorMessageMissingFieldValue
    );
    console.log(
      "Validation successful: Cannot publish quote without 'Valid Till' date."
    );
  });

  test("Validate user is able to Publish quote without setting price", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T25505" },
      {
        type: "Description",
        description: "Create quote without setting price",
      },
      {
        type: "Precondition",
        description: "Login to an CPQ Client",
      },
      {
        type: "Expected Behaviour",
        description:
          "Appropriate error message should be displayed and not able to publish",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.searchAndSelectQuote(testData.searchOpp);

    const productPage = new ProductPage(adminPage.page, {
      productNames: ["Connector Charges", "Everstage Platform"],
      pricePointOpt: testData["Everstage Platform"].pricePointOption,
      colHead: "net_unit_price",
      rowHeads: [
        "CONNECTOR",
        "PLATFORM",
        "SUPPORT",
        "IMPLEMENTATION",
        "PREMIUMSUPPORT",
      ],
    });
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();

    await productPage.clickPublish();
    await productPage.verifyErrorMessage();
  });
});
