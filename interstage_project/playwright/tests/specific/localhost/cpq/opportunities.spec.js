import { expect } from "@playwright/test";
import OpportunityPage from "../../../../test-objects/cpq/opportunitiesPage-objects.js";
import QuoteListPage from "../../../../test-objects/cpq/quotesListPage-objects.js";
import ProductPage from "../../../../test-objects/cpq/selectProductPage-objects.js";
import SettingsPage from "../../../../test-objects/cpq/settingsPage-objects.js";

const testData = JSON.parse(
  JSON.stringify(require("../../../../testData/testData.json"))
);

const {
  cpqTestFixtures: { test },
} = require("../../../fixtures.js");

test.beforeAll(async ({ adminPage }) => {
  const quoteListPage = new QuoteListPage(adminPage.page);
  await quoteListPage.openApp("/cpq/quotes");
  const settingsPage = new SettingsPage(adminPage.page);
  await settingsPage.refreshDatasheets();
});

test.describe("CPQ - Opportunities", { tag: ["@cpq-1", "@regression"] }, () => {
  test("Validate 'Valid Till' date is set correctly", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19577" },
      {
        type: "Description",
        description: "Validate 'Valid Till' date",
      },
      {
        type: "Precondition",
        description: "Quote should be created or cloned",
      },
      {
        type: "Expected Behaviour",
        description:
          "Valid Till date should be 30 days (including today) from the creation date",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.validateValidTillDate();
  });

  test("Validate 'Valid Till' date - cloned quote", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19578, INTER-T25508" },
      {
        type: "Description",
        description: "Validate 'Valid Till' date when cloning the quote",
      },
      { type: "Precondition", description: "None" },
      {
        type: "Expected Behaviour",
        description: "Valid Till date should be 30 days (including today)",
      }
    );

    const opportunityPage = new OpportunityPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    const quoteListPage = new QuoteListPage(adminPage.page);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickClone();
    await quoteListPage.clickContinue();
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCloneQuote
    );
    await opportunityPage.validateValidTillDate();
  });

  test("Validate user is not allowed to select any date behind current date", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19579" },
      {
        type: "Description",
        description: "Validate select yesterday as 'Valid Till' date",
      },
      { type: "Precondition", description: "None" },
      {
        type: "Expected Behaviour",
        description: "Should not be able to set",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.validatePastDates();
  });

  test("Validate user is able to search opportunities", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      {
        type: "Test ID",
        description: "INTER-T19575, INTER-T19582",
      },
      {
        type: "Description",
        description: "User is able to search opportunities",
      },
      {
        type: "Precondition",
        description: "User should have an opportunities available to search",
      },
      {
        type: "Expected Behaviour",
        description: "Selected opportunity to be displayed in pdf",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.searchAndSelectQuote(testData.searchOpp);
    await opportunityPage.getPdfData(testData.searchOpp);
  });

  test("Validate hitting 'Publish' button twice does not create duplicate quotes", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19595" },
      {
        type: "Description",
        description: "Clicking 'Publish' twice does not create two quotes",
      },
      {
        type: "Expected Behaviour",
        description:
          "Only one quote should be published, even if 'Publish' is clicked twice",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    const generatedQuoteName = await opportunityPage.createNewQuote();

    const productPage = new ProductPage(adminPage.page, {
      productNames: ["Connector Charges", "One-time Implementation"],
      pricePointOpt: testData["Everstage Platform"].pricePointOption,
      colHead: "net_unit_price",
      rowHeads: ["CONNECTOR", "IMPLEMENTATION"],
    });
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();
    await productPage.updateProductDetails(testData);
    await productPage.doubleClickPublish();
    await productPage.clickExit();

    await quoteListPage.searchQuote(generatedQuoteName);
    const quoteCount = await quoteListPage.getQuoteCount(generatedQuoteName);
    expect(quoteCount).toBe(1);
    console.log(
      `Number of quotes with name ${generatedQuoteName}:`,
      quoteCount
    );
  });

  test("Validate creating a quote without adding a quote name and mandatory fields", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      {
        type: "Test ID",
        description:
          "INTER-T19581, INTER-T19580, INTER-T25501, INTER-T25504, INTER-T25507",
      },
      {
        type: "Expected Behaviour",
        description: "User should not be able to publish the quote",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );

    await opportunityPage.fillQuoteName("");
    await opportunityPage.clickPublishButton();
    await opportunityPage.isRedAlertVisible();
    console.log("Red alert appeared as expected.");
  });

  test("Validate exit button saves quote as draft", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19589" },
      {
        type: "Description",
        description:
          "Validating that clicking exit without publishing saves changes as draft",
      },
      {
        type: "Expected Behaviour",
        description:
          "User exits without publishing, quote should be in 'Draft' status",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.createNewQuote();

    const productPage = new ProductPage(adminPage.page, {
      productNames: ["Connector Charges", "One-time Implementation"],
      pricePointOpt: testData["Everstage Platform"].pricePointOption,
      colHead: "net_unit_price",
      rowHeads: ["CONNECTOR", "IMPLEMENTATION"],
    });
    await productPage.clickSelectProduct();
    await productPage.addProduct();
    await productPage.selectProducts();
    await productPage.clickAdd();
    await productPage.updateProductDetails(testData);
    // Instead of publishing, click Exit
    await productPage.clickExit();
    // Verify if the quote is in Draft status
    //await quoteListPage.searchQuote(generatedQuoteName);
    const status = await quoteListPage.getQuoteStatus();
    expect(status).toBe("Draft");
    console.log(status);
  });

  test("Validate edit auto populated fields and its refelction in pdf", async ({
    adminPage,
  }, testInfo) => {
    testInfo.annotations.push(
      {
        type: "Test ID",
        description: "INTER-T19594, INTER-T19596, INTER-T19587",
      },
      {
        type: "Expected Behaviour",
        description: "Updated info to be displayed",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.searchAndSelectQuote(testData.searchOpp);
    console.log("Customer info - auto-populated: ");
    await opportunityPage.getPdfData(testData.searchOpp);
    await opportunityPage.editFields();
    console.log("Customer info - edited: ");
    await opportunityPage.getPdfData("Test Account");
  });

  test("Validate adding invalid email", async ({ adminPage }, testInfo) => {
    testInfo.annotations.push(
      { type: "Test ID", description: "INTER-T19588" },
      {
        type: "Expected Behaviour",
        description: "Appropriate error message should be displayed",
      }
    );
    const quoteListPage = new QuoteListPage(adminPage.page);
    testInfo.setTimeout(testInfo.timeout + 800000);
    await quoteListPage.openApp("/cpq/quotes");
    await quoteListPage.clickCreate();
    await quoteListPage.clickNewquote();
    await quoteListPage.clickContinue();
    const opportunityPage = new OpportunityPage(adminPage.page);
    await opportunityPage.verifySuccessMessage(
      testData.successMessageCreateQuote
    );
    await opportunityPage.createNewQuote();
    await opportunityPage.validateEmailField();
  });
});
