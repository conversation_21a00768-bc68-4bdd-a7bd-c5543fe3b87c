/* eslint-disable no-unused-vars */
import ProductPage from "../../../../test-objects/cpq/selectProductPage-objects.js";
import QuotesPage from "../../../../test-objects/cpq/quotesListPage-objects.js";
import SettingsPage from "../../../../test-objects/cpq/settingsPage-objects.js";

const {
  cpqSubscriptionDateFixtures: { test, expect },
} = require("../../../fixtures");



test.describe(
  "CPQ - Subscription Date",
  { tag: ["@cpq-1", "@regression"] },
  () => {
    test.beforeAll("Databook Setup", async ({ adminPage }) => {
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await page.locator("#Settings").click();
      await page
        .locator(
          "//span[normalize-space()='Commission & Data Sync (On-demand)']"
        )
        .click();
      await page.getByText("Refresh Databooks", { exact: true }).click();
      await page
        .locator(
          "//span[text()='Refresh Databooks']/ancestor::div[1]/following::button[1]"
        )
        .click();
      await page.getByText("Skip & Run").click();
      await page
        .locator("text=Databook Refresh Completed")
        .waitFor({ timeout: 300000 });
    });

    test("Subscription Period Basic Validations", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T25471,INTER-T25472,INTER-T25473,INTER-T25474,INTER-T25475,INTER-T25476,INTER-T25477,INTER-T25478,INTER-T25479,INTER-T25480",
        },
        {
          type: "Description",
          description: "Basic Validations for the various periods available",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User must be able to perform these actions related to changing period & duration of a product quote",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 1");
      await prodpage.clickSelectProduct();
    /*  await page.getByText("Subscription Date Test 1").click();
      await page.getByText("Select Product").first().click(); */
      // Check button working
      await prodpage.selectSubscriptionPeriod("Months");
      await prodpage.validateSubsPeriod("1 month");
      await prodpage.selectSubscriptionPeriod("Years");
      await prodpage.validateSubsPeriod("1 year");
      await prodpage.selectSubscriptionPeriod("Forever");
      await prodpage.validateSubsPeriod("Forever");
      // Check period switching
      await prodpage.switchPeriods("Months", "Years");
      await prodpage.validateSubsPeriod("1 year");
      await prodpage.switchPeriods("Months", "Forever");
      await prodpage.validateSubsPeriod("Forever");
      await prodpage.switchPeriods("Years", "Forever");
      await prodpage.validateSubsPeriod("Forever");
      await prodpage.switchPeriods("Forever", "Years");
      await prodpage.validateSubsPeriod("1 year");
      await prodpage.switchPeriods("Forever", "Months");
      await prodpage.validateSubsPeriod("1 month");
      // Check duration switch with phases
      await prodpage.changeDuration("Years", "3");
      await prodpage.validateSubsPeriod("3 years");
      await prodpage.selectSubscriptionPeriod("Forever");
      await prodpage.validateSubsPeriod("Forever");
      await page.keyboard.press("Enter");
      await prodpage.changeDuration("Months", "13");
      await prodpage.validateSubsPeriod("13 months");

      // revert back to original case
      await prodpage.changeDuration("Years", "1");
      await prodpage.validateSubsPeriod("1 year");
    });

    test("Calendar Date Validations", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T25468,INTER-T25469,INTER-T25470,INTER-T25494,INTER-T25498",
        },
        {
          type: "Description",
          description: "Basic Validations for the various periods available",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Subscription dates can be changed to both future, past & present dates",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 1");
      await prodpage.clickSelectProduct();
      await prodpage.changeSubscriptionStartDate("Jan 14, 2030");
      await prodpage.validateSubStartPeriod("Jan 14, 2030");
      await prodpage.validateSubPhaseDateChange("2", "Jan 14, 2030");
      await prodpage.changeSubscriptionStartDate("Aug 17, 2002");
      await prodpage.validateSubStartPeriod("Aug 17, 2002");
      await prodpage.validateSubPhaseDateChange("2", "Aug 17, 2002");
      // change back to original subscription start dates
      await prodpage.changeSubscriptionStartDate("Feb 14, 2025");
      await prodpage.validateSubStartPeriod("Feb 14, 2025");
      await prodpage.validateSubPhaseDateChange("2", "Feb 14, 2025");
    });
    test("Date change across phases", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25481,INTER-T25495" },
        {
          type: "Description",
          description: "Validate user is able to change date across phases",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to edit start and end date in middle of the Phases.",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 2");
      await prodpage.clickSelectProduct();
      await prodpage.clickEditBtn();
      await prodpage.changeSubscriptionPhaseDate("3", "Jan 10, 2026");
      await prodpage.validateSubPhaseDateChange("3", "Jan 10, 2026");
      await prodpage.clickExitBtnInPublish();
    });
    test("Validate add phases & clone quote after changing subscription date", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25482,INTER-T25483" },
        {
          type: "Description",
          description:
            "Validate user is able to add phases after changing the subscription date,Validate user is able to clone a quote where the subscription date is changed",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "New phases should get added based on latest edited dates.User should be able to clone dated edited quotes.",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 1");
      await prodpage.clickSelectProduct();
      await prodpage.changeSubscriptionStartDate("Jan 14, 2030");
      await prodpage.validateSubStartPeriod("Jan 14, 2030");
      await prodpage.deleteSubscriptionPhase("Copy of Year 1");
      await prodpage.addSubscriptionPhase();
      await prodpage.validatePhaseAddition("Copy of Year 1");
      await prodpage.clickExit();
      await quotespage.letColumnsLoad("Name");
      await quotespage.hoverOnQuote("Subscription Date Test 1");
      await quotespage.clickMoreButton("Subscription Date Test 1 es1487 3 products");
      await quotespage.clickCloneQuote();
    });
    test("Middle phase delete", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25485" },
        {
          type: "Description",
          description:
            "Validate on deleting any middle phase -end of deleted phase should be set to previous phase",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "End date of deleted phase should be set as end date of previous phase",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 2");
      await prodpage.clickSelectProduct();
      await prodpage.clickEditBtn();
      await prodpage.deleteSubscriptionPhase("Year 2");
      await prodpage.validatePhaseDelete("Year 2");
      await prodpage.validateSubPhaseDateChange("3", "Feb 12, 2027");
      await prodpage.clickExitBtnInPublish();
    });
    test("First phase delete", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25486" },
        {
          type: "Description",
          description: "Validate deleting first phase",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Second phase should be moved to first phase and start date of that phase hould be adjusted as per subscription start date",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 2");
      await prodpage.clickSelectProduct();
      await prodpage.clickEditBtn();
      await prodpage.deleteSubscriptionPhase("Year 1");
      await prodpage.validatePhaseDelete("Year 1");
      const text = await page
        .locator('span:right-of(:text("Year 2"))')
        .first()
        .textContent();
      await expect(text).toContain("Feb 13, 2025");
      await prodpage.clickExitBtnInPublish();
    });
    test("Validate adding duration", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T25491,INTER-T25492,INTER-T25493,INTER-T25497,INTER-T25496,INTER-T25499",
        },
        {
          type: "Description",
          description:
            "Validate increasing the duration adds dates in last phase,Validate adding dates when duration of phases is != 1 Day,Validate adding dates when duration of phases is == 1 Day,Validate screen break is not seen on selecting the value without selecting the duration,Validate changing the subscription date should get reflected across phases,Validate duration when subscription end date is february",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Dates should get added in the last Phase & no screen break is observed",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 2");
      await prodpage.clickSelectProduct();
      await prodpage.clickEditBtn();
      await prodpage.changeDuration("Years", "4");
      await prodpage.checkProductCount("Everstage Platform", 4);
      // check for screen break
      await expect(page.locator(':text("Something went wrong")')).toHaveCount(
        0
      );
      await prodpage.clickExitBtnInPublish();
      await quotespage.clickOnQuote("Subscription Date Test 3");
      await prodpage.clickSelectProduct();
      await prodpage.changeDuration("Years", "2");
      await prodpage.validateSubPhaseDateChange("5", "Mar 3, 2027");
    });
    test("Last phase delete", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25484" },
        {
          type: "Description",
          description:
            "Validate on deleting the last phase -end of deleted phase should be set to previous phase",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "End date of deleted phase should be set as end date of current last phase.",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 3");
      await prodpage.clickSelectProduct();
      await prodpage.addSubscriptionPhase();
      await prodpage.validatePhaseAddition("Copy of Phase 3");
      await prodpage.deleteSubscriptionPhase("Copy of Phase 3");
      await prodpage.validatePhaseDelete("Copy of Phase 3");
      await prodpage.validateSubPhaseDateChange("5", "Mar 3, 2027");
    });
    test("Phase =1 & Start & End date not editable", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25488" },
        {
          type: "Description",
          description:
            "Validate when duration of Phase = 1Day and if its not the first phase , start and end date should not be in editable format",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should not be able to edit the start and end dates",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await quotespage.clickOnQuote("Subscription Date Test 4");
      await prodpage.clickSelectProduct();
      // preset the dates :
      await prodpage.changeSubscriptionPhaseDate("4", "Jan 3, 2025");
      await prodpage.validateSubPhaseDateChange("4", "Jan 3, 2025");
      await prodpage.changeSubscriptionPhaseDate("5", "Jan 4, 2025");
      await prodpage.validateSubPhaseDateChange("5", "Jan 4, 2025");
      // change date but expect the date to not change since edit is not allowed
      await prodpage.changeSubscriptionPhaseDate("4", "Jan 7, 2025");
      await prodpage.validateSubPhaseDateChange("4", "Jan 3, 2025");
    });
    test("February Date check", async ({ adminPage }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25499" },
        {
          type: "Description",
          description:
            "Validate duration when subscription end date is february",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Duration value to be calculated properly.",
        }
      );
      const page = adminPage.page;
      const quotespage = new QuotesPage(page);
      const prodpage = new ProductPage(page);
      await quotespage.openApp("/cpq/quotes");
      await quotespage.letColumnsLoad("Name");
      await page.getByText("February Month Check").click();
      await page.getByText("Select Product").first().click();

      await prodpage.changeSubscriptionStartDate("Feb 3, 2025");
      await prodpage.validateSubStartPeriod("Feb 3, 2025");
      await prodpage.validateSubPhaseDateChange("3", "Feb 3, 2026");
      await prodpage.validateSubPhaseDateChange("4", "Feb 2, 2027");
      // revert changes
      await prodpage.changeSubscriptionStartDate("Feb 1, 2025");
      await prodpage.validateSubStartPeriod("Feb 1, 2025");
    });
  }
);
