const {
  crystalNewFixtures: { test, expect },
} = require("../../../fixtures");
import CrystalPage from "../../../../test-objects/crystalselectoppfilters-objects";
const CommonUtils = require("../../../../test-objects/common-utils-objects");
const path = require("path");

test.describe(
  "Crystal Quota Attainment Widget Tests",
  {
    tag: ["@regression", "@crystal", "@rbac", "@repconnect-1"],
    timeout: 600000,
  },
  () => {
    let crystalPage;
    let commonPage;
    let currentDirectory;

    test.beforeEach(async ({ adminPage }) => {
      const page = adminPage.page;
      await page.setViewportSize({ width: 1600, height: 1200 });
      crystalPage = new CrystalPage(page);
      commonPage = new CommonUtils(page);
      currentDirectory = __dirname;
    });

    test("Validate the quota category dropdown behavior and visibility for managers and individual payees.", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T28861, INTER-T28862, INTER-T28863, INTER-T28864",
        },
        {
          type: "Description",
          description:
            "Validate the quota category dropdown behavior and visibility for managers and individual payees. Ensure correct ordering, visibility restrictions, and chart updates based on selected quota types under a team calculation plan.",
        },
        {
          type: "Expected behaviour",
          description:
            "Manager dropdown should display quota categories with type labels in brackets. Manager quotas should appear first, followed by individual quotas. Payees should only see their applicable quota categories. Charts in crystal widget should update correctly for both manager and individual quotas based on selection.",
        }
      );
      const csvFilePath = path.join(
        currentDirectory,
        "./uploadFiles/CrystalQuotaChart.csv"
      );
      testInfo.setTimeout(testInfo.timeout + 600000);
      const formattedDate = await crystalPage.getFormattedDate();
      await crystalPage.uploadCrystalData(
        csvFilePath,
        "10",
        "Crystal Object",
        formattedDate
      );
      await crystalPage.goToCrystalPage();
      await crystalPage.navigateToPayeeCrystal("Crystal", "Crystal View");
      const newPage = await crystalPage.previewAsPayee();
      await adminPage.page.waitForLoadState("networkidle");
      await newPage.switchPayee("user 3", "₹0.00");
      await newPage.validateMultipleQuotas(newPage.page, [
        { type: "Primary Quota (As Individual)", percentage: "30" },
        { type: "Secondary Quota (As Manager)", percentage: "120" },
      ]);
      await newPage.switchPayee("user 4", "₹0.00");
      await newPage.validateMultipleQuotas(newPage.page, [
        { type: "Primary Quota", percentage: "30" },
        { type: "Secondary Quota (Team Quota)", percentage: "0" },
      ]);
      await crystalPage.cleanupCrystalData(csvFilePath, "Crystal Object");
    });

    test("Verify if individual quota widgets are rendered correctly", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T28860",
        },
        {
          type: "Description",
          description:
            "Verify if individual quota widgets are rendered correctly in the Crystal widget for a payee.",
        },
        {
          type: "Expected behaviour",
          description:
            "Individual quota widgets should be visible and display accurate quota attainment details in the Crystal widget.",
        }
      );
      const csvFilePath = path.join(
        currentDirectory,
        "./uploadFiles/Crystal -UI Validation.csv"
      );
      testInfo.setTimeout(testInfo.timeout + 600000);

      const formattedDate = await crystalPage.getFormattedDate();
      await crystalPage.uploadCrystalData(
        csvFilePath,
        "-3",
        "Crystal Object",
        formattedDate
      );

      await crystalPage.goToCrystalPage();
      await crystalPage.navigateToPayeeCrystal("Crystal", "Crystal View");
      const newPage = await crystalPage.previewAsPayee();
      await adminPage.page.waitForLoadState("networkidle");
      await newPage.switchPayee("crystal payee 03", "-₹510.00");
      await newPage.validateQuotaDropdown(newPage.page, true);
      await newPage.validateQuotaAttainment(
        newPage.page,
        "Primary Quota",
        "30"
      );

      await newPage.switchPayee("crystal payee 04", "-₹510.00");
      await newPage.validateQuotaDropdown(newPage.page, true);
      await newPage.validateQuotaAttainment(
        newPage.page,
        "Primary Quota",
        "30"
      );
    });

    test("Verify quota attainment value and percentage consistency across Crystal and Statements modules", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T28866",
        },
        {
          type: "Description",
          description:
            "Verify the quota attainment value and percentage displayed in the Crystal widget is consistent with the values shown in the Statements module.",
        },
        {
          type: "Expected behaviour",
          description:
            "Quota attainment value and percentage should match exactly across both Crystal and Statements modules for the same payee and period.",
        }
      );
      testInfo.setTimeout(testInfo.timeout + 600000);

      const formattedDate = await crystalPage.getFormattedDate();

      await crystalPage.navigate("plans");
      await adminPage.page
        .getByText("Crystal Quota Plan_Copy1", { exact: true })
        .click();
      await adminPage.page.waitForLoadState("networkidle");

      await adminPage.page.getByRole("button", { name: /^Publish$/ }).click();
      const modalPublishBtn = adminPage.page
        .locator(".ant-modal")
        .getByRole("button", { name: /^Publish$/ });
      await modalPublishBtn.waitFor({ state: "visible" });
      await modalPublishBtn.click();

      await commonPage.runCommissionSyncForAllPayeesToday(formattedDate, true);
      await crystalPage.goToCrystalPage();
      await crystalPage.navigateToPayeeCrystal("Crystal", "Crystal View");
      const newPage = await crystalPage.previewAsPayee();
      await adminPage.page.waitForLoadState("networkidle");
      await newPage.switchPayee("crystal payee 03", "0.00");
      await newPage.validateMultipleQuotas(newPage.page, [
        { type: "Secondary Quota", percentage: "60" },
        { type: "Primary Quota", percentage: "30" },
      ]);

      await newPage.switchPayee("crystal payee 04", "₹1,00,000.00");
      await newPage.validateMultipleQuotas(newPage.page, [
        { type: "Secondary Quota", percentage: "60" },
        { type: "Primary Quota", percentage: "30" },
      ]);

      await newPage.page
        .getByTestId("pt-crystal-payee-dropdown")
        .last()
        .click();
      await newPage.page.getByPlaceholder("Search User").fill("User1");
      await newPage.page
        .getByRole("listitem")
        .getByText("User 1")
        .waitFor({ state: "visible" });
      await newPage.page.getByRole("listitem").getByText("User 1").click();
      await newPage.page
        .locator(".tracking-tight")
        .getByText("1,000")
        .last()
        .waitFor({ state: "visible" });
      await newPage.page.waitForTimeout(5000);

      await newPage.validateQuotaDropdown(newPage.page, true);
      await newPage.validateQuotaAttainment(
        newPage.page,
        "Secondary Quota",
        "0"
      );

      await crystalPage.navigate("/commissions");
      await adminPage.page.locator(".ant-picker-input").click();

      const today = new Date();
      const currentMonth = (today.getMonth() + 1).toString().padStart(2, "0");
      await adminPage.page.locator(`[title$="-${currentMonth}-30"]`).click();
      await adminPage.page
        .getByText("crystal payee 03", { exact: true })
        .click();
      await adminPage.page
        .getByText("Quota Attainment", { exact: true })
        .click();

      await crystalPage.validateQuotaCard(
        adminPage.page,
        "Primary Quota",
        "30"
      );
      await crystalPage.validateQuotaCard(
        adminPage.page,
        "Secondary Quota",
        "60"
      );
    });
  }
);