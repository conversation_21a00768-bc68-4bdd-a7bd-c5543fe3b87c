import ManualUploadRevamp from "../../../../test-objects/manual-upload-revamp-objects";
import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import CrystalPage from "../../../../test-objects/crystalsReorderTabs-objects";

const fs = require("fs");
const path = require("path");

const {
  crystalNewFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 600000);
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "In before Each, unable to click on Exit button for Logged in user"
      );
    }
  }
});

test.describe(
  "Crystals reordering",
  {
    tag: ["@crystal", "@regression", "@repconnect-1"],
  },
  () => {
    test(
      "Update date, Add Data to Object, Creation of datsheets",
      {
        annotation: [
          {
            type: "Description",
            description:
              "Validate whether updates to dates, the addition of data to objects, and the creation of datasheets function correctly.",
          },
          {
            type: "Precondition",
            description:
              "Existing records available in sheet with dates and datasheets configured.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The system should correctly update dates, add new data to objects and create datasheets without affecting existing configurations.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const manualRevamp = new ManualUploadRevamp(page);
        const datasheet = new DatasheetV2Page(page);
        const crystalPage = new CrystalPage(page);
        const filePath = path.resolve(
          __dirname,
          "../../../../upload-files/manual-upload-revamp-bulk/Create new data - Crystals Data.csv"
        );
        crystalPage.processCSV(filePath);

        await manualRevamp.navigate("/settings/manage-data");
        await manualRevamp.selectUploadMode("Create new data");
        await manualRevamp.selectObject("Crystal Object");
        await manualRevamp.clickNextButton();
        await manualRevamp.clickBrowse();
        await crystalPage.setInputFiles(
          "./upload-files/manual-upload-revamp-bulk/Create new data - Crystals Data.csv"
        );
        await manualRevamp.clickNextButton();
        await manualRevamp.clickNextButton();
        await manualRevamp.selectDateFormatDropdownPrefilled("(MM/DD/YYYY)");
        await manualRevamp.selectDateFormat("31/01/");
        await manualRevamp.clickNextButton();
        await manualRevamp.clickValidate();
        await manualRevamp.clickimport();

        await crystalPage.navigate("/datasheet");
        await datasheet.goToDatasheet("Crystal DataBook", "Crystal Data");
        await datasheet.applyAdjustmentChanges();
        await datasheet.clickDatasheetMenu();
        await datasheet.cloneDatasheet();
        await datasheet.goToDatasheet("Crystal DataBook", "Crystal Data_Copy");
        await datasheet.generateDatasheet();
        await datasheet.letColumnsLoad("Deal Name");
        await datasheet.goToDatasheet("Crystal DataBook", "Crystal Data2");
        await datasheet.applyAdjustmentChanges();
        await crystalPage.removeUserFromPlan(
          page,
          "Simple",
          "<EMAIL>"
        );
        await crystalPage.removeUserFromPlan(
          page,
          "Crystal Quota Plan_Copy1",
          "<EMAIL>"
        );
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(0).click();
        await page
          .getByRole("menuitem", { name: "Delete Simulator" })
          .locator("span")
          .click();
        await page.getByRole("button", { name: "Delete" }).click();
      }
    );

    test(
      "Verify Crystal page elements and create view",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20313, INTER-T20314" },
          {
            type: "Description",
            description:
              "To check whether the user is able to create a new simulator and a new view.",
          },
          {
            type: "Precondition",
            description:
              "User has access to the Crystal page and required permissions.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to successfully create a new simulator and a new view.",
          },
        ],
      },

      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.verifyCrystalPageElements();
        await crystalPage.createCrystal(
          "Crystal test simulator",
          "Get Started"
        );
        await crystalPage.VerifyCrystalSuccessMsg();
        await page.getByRole("button", { name: "Add View" }).click();
        await crystalPage.createView(
          "View 1",
          "Playwright test for new simulator",
          "Crystal Data"
        );
        await crystalPage.configureTableConditions("Deal Date", "Is Not Empty");
        await crystalPage.setSuccessActions("Deal Status", "True");
        await crystalPage.setEmailAndDateField(2, 3);
        await crystalPage.setRowName("Deal Amount");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.confirmCreation();
      }
    );

    test(
      "Create second view",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20315, INTER-T20316" },
          {
            type: "Description",
            description:
              "To check whether the user is able to create multiple views and if the added views are displayed in the tabs.",
          },
          {
            type: "Precondition",
            description:
              "User has an existing view and access to create additional views.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to create multiple views, and each created view should be visible in the tabs.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);

        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.getByRole("button", { name: "Add tab" }).click();
        await crystalPage.createView(
          "View 2",
          "Playwright test for new simulator2",
          "Crystal Data_Copy"
        );
        await crystalPage.configureTableConditions("Deal Date", "Is Not Empty");
        await crystalPage.setSuccessActions("Deal Status", "True");
        await crystalPage.setEmailAndDateField(3, 4);
        await crystalPage.setRowName("Deal Amount");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.confirmCreation();
        await page.getByRole("button", { name: "Add tab" }).click();
        await crystalPage.createView(
          "View 3",
          "Playwright test for new simulator3",
          "Crystal Data2"
        );
        await crystalPage.configureTableConditions("Deal Date", "Is Not Empty");
        await crystalPage.setSuccessActions("Deal Status", "True");
        await crystalPage.setEmailAndDateField(3, 4);
        await crystalPage.setRowName("Deal Amount");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.confirmCreation();
        await page.waitForTimeout(1000);

        const tabElements = await page.getByRole("tab").all();
        const tabNames = await Promise.all(
          tabElements.map((tab) => tab.textContent())
        );

        expect(tabNames[tabNames.length - 1]).toBe("View 3");
      }
    );

    test(
      "Reorder view tabs",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20317, INTER-T20318" },
          {
            type: "Description",
            description:
              "To check whether the user is able to reorder a single view tab and multiple view tabs.",
          },
          {
            type: "Precondition",
            description: "Multiple views are available in the Crystal page.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to reorder a single tab as well as multiple tabs successfully.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        const view1Tab = page.getByRole("tab", { name: "View 1" });
        const view2Tab = page.getByRole("tab", { name: "View 2" });
        const view3Tab = page.getByRole("tab", { name: "View 3" });
        await expect(view1Tab).toBeVisible();
        await expect(view2Tab).toBeVisible();
        await expect(view3Tab).toBeVisible();
        let view1Position = await view1Tab.boundingBox();
        let view2Position = await view2Tab.boundingBox();
        let view3Position = await view3Tab.boundingBox();
        await page.mouse.move(view3Position.x + 10, view3Position.y + 10);
        await page.mouse.down();
        await page.mouse.move(view1Position.x + 10, view1Position.y + 10, {
          steps: 10,
        });
        await page.mouse.up();
        await crystalPage.CrystalOrderUpdateMsg();
        await page.mouse.move(view2Position.x + 10, view2Position.y + 10);
        await page.mouse.down();
        await page.mouse.move(view3Position.x + 10, view3Position.y + 10, {
          steps: 10,
        });
        await page.mouse.up();
        await crystalPage.CrystalOrderUpdateMsg();
      }
    );
    test(
      "Verify order of newly added and edited views",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20319, INTER-T20321" },
          {
            type: "Description",
            description:
              "To check the order of a newly added view and ensure the order remains correct after editing a view.",
          },
          {
            type: "Precondition",
            description:
              "At least one view is added and available for editing.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The newly added view should appear in the expected order, and the order should remain correct after editing.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.waitForTimeout(1000);
        const initialTabNames = await crystalPage.getTabNames();
        console.log("🔹 Initial tab order:", initialTabNames);
        await page.getByRole("tab", { name: "View 2" }).click();
        const view2Tab = page.getByRole("tab", { name: "View 2" });
        const settingsIcon = view2Tab.locator(".ant-dropdown-trigger");
        await settingsIcon.click();
        await page.waitForTimeout(500);
        await page.getByRole("menuitem", { name: "Edit" }).click();
        await page.fill(
          '[placeholder="Enter the View name"]',
          "View 2 - Edited"
        );
        await crystalPage.clickConfirm();
        await page.waitForTimeout(1000);
        const updatedTabNames = await crystalPage.getTabNames();
        console.log("🔹 Updated tab order:", updatedTabNames);
        expect(updatedTabNames).toContain("View 2 - Edited");
        expect(updatedTabNames.length).toBe(initialTabNames.length);
        expect(updatedTabNames.indexOf("View 2 - Edited")).toBe(
          initialTabNames.indexOf("View 2")
        );
      }
    );
    test(
      "Verify order after deleting and publishing a view",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20322, INTER-T20323" },
          {
            type: "Description",
            description:
              "To check the order of views after deleting a view and after publishing a simulator.",
          },
          {
            type: "Precondition",
            description:
              "At least one view should be available for deletion, and a simulator should be available for publishing.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The view order should update correctly after a view is deleted, and remain consistent after publishing a simulator.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.waitForTimeout(1000);
        const initialTabNames = await crystalPage.getTabNames();
        console.log("🔹 Initial tab order:", initialTabNames);
        await crystalPage.deleteTab("View 2 - Edited");
        const afterDeleteTabNames = await crystalPage.getTabNames();
        console.log("🔹 Tab order after deletion:", afterDeleteTabNames);
        expect(afterDeleteTabNames).not.toContain("View 2 - Edited");
        expect(afterDeleteTabNames.length).toBe(initialTabNames.length - 1);
        await page.waitForTimeout(1000);
        await crystalPage.publishChanges();
        const afterPublishTabNames = await crystalPage.getTabNames();
        console.log("🔹 Tab order after publishing:", afterPublishTabNames);
        expect(afterPublishTabNames).toEqual(afterDeleteTabNames);
      }
    );
    test(
      "User should NOT be able to reorder tabs in published simulator",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20324" },
          {
            type: "Description",
            description:
              "Application should not allow users to reorder tabs in a published simulator.",
          },
          {
            type: "Precondition",
            description:
              "A simulator must be published with multiple tabs available.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Users should be restricted from reordering tabs once the simulator is published.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.waitForTimeout(1000);
        const tab1Initial = await crystalPage.getTabPosition(page, "View 1");
        const tab2Initial = await crystalPage.getTabPosition(page, "View 3");
        await crystalPage.attemptTabReorder(page, "View 3", "View 1");
        const tab1Final = await crystalPage.getTabPosition(page, "View 1");
        const tab2Final = await crystalPage.getTabPosition(page, "View 3");
        expect(tab2Final.x).toBe(tab2Initial.x);
        expect(tab1Final.x).toBe(tab1Initial.x);
      }
    );
    test(
      'To check Reordering functionality in "Select Projections" screen in Payee Preview mode',
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20326" },
          {
            type: "Description",
            description:
              'Reordering tabs functionality should not work in the "Select Projections" screen within Payee Preview mode.',
          },
          {
            type: "Precondition",
            description:
              "User is in Payee Preview mode and has access to the 'Select Projections' screen.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Users should not be able to reorder tabs within the 'Select Projections' screen in Payee Preview mode.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.waitForTimeout(1000);
        const page1 = await crystalPage.openDraftSimulator();
        await crystalPage.selectOpportunities(page1);
        const tab1Initial = await crystalPage.getTabPosition(page1, "View 1");
        const tab2Initial = await crystalPage.getTabPosition(page1, "View 3");
        await crystalPage.attemptTabReorder(page1, "View 3", "View 1");
        const tab1Final = await crystalPage.getTabPosition(page1, "View 1");
        const tab2Final = await crystalPage.getTabPosition(page1, "View 3");
        expect(tab2Final.x).toBe(tab2Initial.x);
        expect(tab1Final.x).toBe(tab1Initial.x);
      }
    );
    test(
      "Save the Crystal view",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20236" },
          {
            type: "Description",
            description:
              "When the admin enters the name and adds payees to the Crystal view but doesnt proceed with the configuration and exits, the Crystal view name should be saved as a draft.",
          },
          {
            type: "Precondition",
            description:
              "Admin must enter the name and add payees to the Crystal view without proceeding with the configuration.",
          },
          {
            type: "Expected Behaviour",
            description: "The Crystal view should be saved as a draft",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.createCrystal("Crystal 2");
        await crystalPage.VerifyCrystalSuccessMsg();
        await crystalPage.navigate("/crystal");
        await page
          .getByText("Crystal 2")
          .waitFor({ state: "visible", timeout: 20000 });
        const delayElement = await page.getByText("Crystal 2").first();
        const parentDiv = await delayElement.locator(
          "xpath=./parent::div/parent::div"
        );
        const draftExists = await parentDiv.getByText("Draft").isVisible();
        expect(draftExists).toBeTruthy();
      }
    );
    test(
      "Verify 'Crystal Table' mandatory and non-mandatory fields",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20237" },
          {
            type: "Description",
            description:
              "Validate that the 'Crystal Table' contains mandatory and non-mandatory fields as expected.",
          },
          {
            type: "Precondition",
            description:
              "The 'Crystal Table' should be accessible with fields configured as mandatory and non-mandatory.",
          },
          {
            type: "Expected Behaviour",
            description:
              "All mandatory fields should be required before proceeding, while non-mandatory fields should be optional.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal 2").click();
        await page.getByRole("button", { name: "Add View" }).click();
        await crystalPage.clickConfirm();
        const mandatoryFields1 = [
          "Please enter the view name",
          "Please enter description",
          "Please select source",
        ];
        const mandatoryFields2 = [
          "Please select email field",
          "Please select date field",
          "Please set row name",
          "Please select type",
          "Please select operator",
          "Please select field",
          "Please enter value",
        ];
        for (const field of mandatoryFields1.concat(mandatoryFields2)) {
          await expect(page.getByText(field)).toBeVisible();
        }
        const disabledFields = [
          "#newTableForm_displayConditions_0_colName",
          "#newTableForm_successActions_0_columnName",
          "#newTableForm_emailField",
          "#newTableForm_dateField",
          "#newTableForm_rowName",
        ];
        for (const field of disabledFields) {
          await expect(page.locator(field)).toBeDisabled();
        }
        await crystalPage.createView(
          "Test View",
          "Test Description",
          "Crystal Data2"
        );
        for (const field of mandatoryFields1) {
          await expect(page.getByText(field)).not.toBeVisible();
        }
        for (const field of disabledFields) {
          await expect(page.locator(field)).toBeEnabled();
        }
        await crystalPage.clickConfirm();
        for (const field of mandatoryFields2) {
          await expect(page.getByText(field)).toBeVisible();
        }
        await crystalPage.setEmailAndDateField(0, 0);
        await crystalPage.setRowName("Deal ID");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.clickConfirm();
        await page
          .getByText("Table created successfully")
          .waitFor({ state: "hidden", timeout: 1000 });
        await crystalPage.removeCondition(page, 0);
        await expect(
          page.locator("#newTableForm_displayConditions_0_colName")
        ).not.toBeVisible();
        await crystalPage.removeCondition(page, 0);
        await expect(
          page.locator("#newTableForm_successActions_0_columnName")
        ).not.toBeVisible();
        for (const field of mandatoryFields2) {
          await expect(page.getByText(field)).not.toBeVisible();
        }
        await crystalPage.confirmCreation();
      }
    );
    test(
      "Add Another Payee and check if shown in dropdown",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20239" },
          {
            type: "Description",
            description:
              "Verify that when data is generated for one payee by default, the admin can select another payee from the dropdown to view their associated data.",
          },
          {
            type: "Precondition",
            description:
              "At least one payee must have generated data, and the dropdown should be available for selecting other payees.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The payee selection dropdown should display all available payees, and selecting another payee should update the data accordingly.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(0).click();
        await crystalPage.addPayee("MMManager M");
        await page.getByRole("link", { name: "Crystal 2" }).click();
        await page.getByText("MMManager M").click();
        await expect(page.getByText("MUMonthly User")).toBeVisible();
      }
    );
    test(
      "Published plan should have at least one Payee",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20240" },
          {
            type: "Description",
            description:
              "Verify that when an admin tries to edit a ‘Published’ Crystal view, they cannot remove all payees. At least one payee should be required.",
          },
          {
            type: "Precondition",
            description:
              "A Crystal view must be in the 'Published' state with at least one payee assigned.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The system should prevent the admin from removing all payees from a Published Crystal view and should require at least one payee to remain.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(1).click();
        await page
          .getByRole("menuitem", { name: "Manage Payees" })
          .locator("span")
          .click();
        await expect(
          page.getByRole("button", { name: "Remove" })
        ).toBeDisabled();
      }
    );
    test(
      "Validate payee selection constraints",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20242, INTER-T20258" },
          {
            type: "Description",
            description:
              "Ensure that only payees tagged to a commission plan can be added, while those without plans are listed but not selectable.",
          },
          {
            type: "Precondition",
            description: "Payees with and without commission plans exist.",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. Admin can only add payees who are tagged to a commission plan.\n" +
              "2. Payees without any plans should be listed in the payee selection tab but should not be selectable.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(1).click();
        await page
          .getByRole("menuitem", { name: "Manage Payees" })
          .locator("span")
          .click();
        await page.getByTestId("ever-select").locator("div").nth(1).click();
        await page.locator("#horizontal_login_payees").fill("user 1");
        await page
          .getByRole("option", { name: "U1 User" })
          .locator("svg")
          .hover();
        await expect(
          page.getByText("User 1 is not mapped to a commission plan yet")
        ).toBeVisible();
        await page.getByLabel("Close").click();
      }
    );
    test(
      'Validate "Preview as Payee" navigates to projection screen and allows payee selection',
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20243,INTER-T20255" },
          {
            type: "Description",
            description:
              'Ensure that clicking on "Preview as Payee" navigates the admin to the projection screen.',
          },
          {
            type: "Precondition",
            description:
              "A published Crystal view must be available with at least one payee added.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. Clicking "Preview as Payee" should navigate to the projection screen.\n' +
              "2. Admin should be able to select payees from the dropdown.\n" +
              "3. Calendar field should remain editable.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        const page1 = await crystalPage.openDraftSimulator();
        const monthlyUserDropdown = page1
          .getByTestId("pt-crystal-payee-dropdown")
          .getByRole("button");
        await monthlyUserDropdown.click();
        await expect(page1.getByPlaceholder("Search User")).toBeVisible();
        const dateDropdown = page1.getByTestId("ever-select");
        await dateDropdown.click();
        await expect(page1.getByPlaceholder("Search Period")).toBeVisible();
      }
    );
    test(
      "Validate published Crystal view deletion restrictions",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20244, INTER-T20245" },
          {
            type: "Description",
            description:
              "Ensure that a published Crystal view cannot be deleted and that tables can only be deleted in edit mode.",
          },
          {
            type: "Precondition",
            description:
              "A published Crystal view should exist with at least one table present.",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. Admins should NOT be able to delete a published Crystal view.\n" +
              "2. Admins should only be able to delete the table when in edit mode.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(0).click();
        await expect(
          page
            .getByRole("menuitem", { name: "Delete Simulator" })
            .locator("span")
        ).toBeVisible();
        await page.locator(".ant-dropdown-trigger").nth(1).click();
        await expect(
          page
            .getByRole("menuitem", { name: "Delete Simulator" })
            .locator("span")
        ).not.toBeVisible();
      }
    );
    test(
      "Validate unique payee assignment to Crystal views",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20246" },
          {
            type: "Description",
            description:
              "Ensure that an admin can assign a payee to only one Crystal view and receives an error when attempting to add the same payee to multiple views.",
          },
          {
            type: "Precondition",
            description:
              "A Crystal view should exist with at least one payee assigned to it.",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. Admin should be able to add a payee to only one Crystal view.\n" +
              "2. If an admin attempts to add the same payee to another view, an error popup should be displayed.\n",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal 2").click();
        await page.getByRole("button", { name: "Publish" }).click();
        expect(
          page.getByText(
            "Are you sure you want to publish Crystal 2 crystal? 1 out of 2 payees are already added to other views. If you chose to continue to publish, the user will not be added to this view. Please remove the user from other views first."
          )
        ).toBeVisible;
        await page.getByRole("button", { name: "Yes, Publish" }).click();
        expect(page.getByText("Crystal View Updated")).toBeVisible();
        await page.getByRole("button", { name: "Close" }).click();
      }
    );
    test(
      "Validate edit restrictions and table deletion behavior",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20261, INTER-T20245" },
          {
            type: "Description",
            description:
              '1. Users should be able to make edits only after clicking on the "Edit" button.\n' +
              "2. Deleting all tables in a published Crystal view should move it to draft status.\n" +
              "3. Tables should be deletable only in edit mode.",
          },
          {
            type: "Precondition",
            description:
              "A published Crystal view should exist with at least one table.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. Edits should be disabled until the user clicks on the "Edit" button.\n' +
              "2. If all tables in a published Crystal view are deleted, the view should move to draft status.\n" +
              "3. Tables should only be deletable when the view is in edit mode.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.waitForTimeout(1000);
        await page
          .getByText("Published")
          .waitFor({ state: "visible", timeout: 5000 });
        await expect(page.getByText("Published")).toBeVisible();
        const view1Tab = page.getByRole("tab", { name: "View 1" });
        const view3Tab = page.getByRole("tab", { name: "View 3" });

        const Dropdown = view1Tab.locator(".ant-dropdown-trigger");
        const Dropdown2 = view3Tab.locator(".ant-dropdown-trigger");

        const deleteButton = page.getByRole("menuitem", { name: "Delete" });
        const editButton = page.getByRole("menuitem", {
          name: "Edit Connfiguration",
        });
        await page.getByRole("button", { name: "Edit" }).click();
        await page
          .getByText("Cloning the Crystal View")
          .waitFor({ state: "hidden", timeout: 5000 });
        await Dropdown.click();
        await deleteButton.click();
        await crystalPage.confirmAndVerify(
          page,
          "Are you sure you want to permanently delete the table?"
        );
        await crystalPage.confirmAndVerify(
          page,
          "This table is a part of a published simulator. After you delete it, your payees can no longer access it."
        );
        await crystalPage.clickConfirm();
        await crystalPage.confirmAndVerify(
          page,
          "Crystal Table successfully deleted..."
        );
        await page.waitForLoadState("networkidle");
        await Dropdown2.click();
        await deleteButton.click();
        await crystalPage.clickConfirm();
        await crystalPage.confirmAndVerify(
          page,
          "Crystal Table successfully deleted..."
        );
        await crystalPage.clickUpdate();
        await crystalPage.confirmAndVerify(
          page,
          "Are you sure you want to make this update?"
        );
        await crystalPage.confirmAndVerify(
          page,
          "A simulator with no table will automatically move to the 'Draft' state. All the payees added to this simulator will lose access."
        );
        await crystalPage.clickConfirm();
        await expect(page.getByText("Published")).not.toBeVisible();
        await expect(page.getByText("Draft")).toBeVisible();
      }
    );
    test(
      "Validate error pop-up when a single user is tagged to a duplicate view",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20247" },
          {
            type: "Description",
            description:
              "Ensure that an error pop-up is displayed when the admin tries to publish a view where the only payee is already tagged to another view.",
          },
          {
            type: "Precondition",
            description:
              "A Crystal view exists where the only payee is already assigned to another view.",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. Admin should receive an error pop-up preventing them from publishing the view.\n" +
              "2. The message should indicate that the payee is already assigned to another view.\n" +
              "3. The admin should be required to remove the duplicate payee before proceeding.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.locator(".ant-dropdown-trigger").nth(0).click();
        await crystalPage.addPayee("MMManager M");
        await page.locator(".ant-dropdown-trigger").nth(0).click();
        await crystalPage.removePayee("MUMonthly User");
        await page.getByText("Crystal test simulator").click();
        await page.getByRole("button", { name: "Add View" }).click();
        await crystalPage.createView("View 1", "View 1", "Crystal Data");
        await crystalPage.configureTableConditions("Deal Date", "Is Not Empty");
        await crystalPage.setSuccessActions("Deal Status", "True");
        await crystalPage.setEmailAndDateField(2, 3);
        await crystalPage.setRowName("Deal Amount");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.confirmCreation();
        await page.getByRole("button", { name: "Publish" }).click();
        expect(
          page.getByText(
            "Are you sure you want to publish Crystal test simulator crystal? 1 out of 1 payee is already added to other views. If you chose to continue to publish, the user will not be added to this view. Please remove the user from other views first."
          )
        ).toBeVisible;
        await expect(
          page.getByRole("button", { name: "Yes, Publish" })
        ).toBeDisabled();
        await crystalPage.clickCancel();
      }
    );
    test(
      "Validate payee manager's reportees are displayed in the table",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20248" },
          {
            type: "Description",
            description:
              "Ensure that when a payee manager is added, all his reportees (if present in the table) are displayed.",
          },
          {
            type: "Precondition",
            description:
              "A payee manager is added to the Crystal view, and there are reportees assigned under them.",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. When a payee manager is added, all their reportees should be listed in the table.\n" +
              "2. The admin should be able to view the reportees linked to the payee manager.\n" +
              "3. The displayed reportees should match the assigned hierarchy.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test simulator").click();
        await page.getByRole("columnheader", { name: "Deal Name" }).click();
        await page.waitForTimeout(2000);
        const emailCells = await page
          .locator('[role="gridcell"][col-id="co_1_deal_email"]')
          .allTextContents();
        const expectedEmails = [
          "<EMAIL>",
          "<EMAIL>",
        ];
        const missingEmails = expectedEmails.filter(
          (email) => !emailCells.includes(email)
        );
        expect(missingEmails).toEqual([]);
      }
    );
    test(
      "Validate crystal view filters and name constraints",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20253, INTER-T20257" },
          {
            type: "Description",
            description:
              "Ensure correct filtering of Crystal views and validation of Crystal view names.",
          },
          {
            type: "Precondition",
            description:
              "Crystal views exist in both Draft and Published states.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. "All" should display both Published and Draft Crystal views.\n' +
              '2. "Draft" should display only the Draft Crystal views.\n' +
              '3. "Published" should display only the Published Crystal views.\n' +
              "4. Users should not be able to add a duplicate Crystal view name.\n" +
              "5. Users should be able to update the Crystal view name.\n" +
              "6. The name field should allow alphabets, numbers, spaces, and special characters.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByRole("button", { name: "All" }).click();
        expect(page.getByText("Crystal test simulator")).toBeVisible();
        expect(page.getByText("Crystal 2")).toBeVisible();
        await page.getByRole("button", { name: "Draft" }).click();
        expect(page.getByText("Crystal test simulator")).toBeVisible();
        expect(page.getByText("Crystal 2")).not.toBeVisible();
        await page.getByRole("button", { name: "Published" }).click();
        expect(page.getByText("Crystal test simulator")).not.toBeVisible();
        expect(page.getByText("Crystal 2")).toBeVisible();
        await page.getByRole("button", { name: "All" }).click();
        await crystalPage.createCrystal("Crystal test simulator");
        await page
          .getByText("A Crystal Simulator with the same name exists")
          .waitFor({ state: "visible", timeout: 10000 });
        await page.getByRole("button", { name: "Close" }).click();
        await crystalPage.createCrystal("Crystal test simulator2");
        await crystalPage.VerifyCrystalSuccessMsg();
        await page
          .getByText("Showing for")
          .waitFor({ state: "visible", timeout: 20000 });
        await page.locator(".h-5.w-5.text-ever-base-content-mid").click();
        await page.getByRole("textbox").fill("Test 123 @#$%&!");
        await page.getByRole("button").nth(0).click();
        await crystalPage.confirmAndVerify(page, "Crystal View Updated");
      }
    );
    test(
      "Validate plan removal and projection retention",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20254, INTER-T20264" },
          {
            type: "Description",
            description:
              '1. Removing a user from a plan should remove the plan from "Projected payout".\n' +
              "2. After applying projections, navigating back to the projections screen should retain the selected rows.",
          },
          {
            type: "Precondition",
            description:
              "Users should be assigned to a plan, and projections should be applied.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. When a user is removed from a plan, the associated plan should no longer appear under "Projected payout".\n' +
              "2. Navigating back to the projections screen should persist the previously selected rows.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.addPayeeAndSelectPlan("CUCustom User", "Crystal 2");

        const page1 = await crystalPage.openDraftSimulator();
        await crystalPage.selectAndApplyOpportunities(page1, [
          "Deal-145",
          "Deal-46",
          "Deal-101",
        ]);
        await crystalPage.validateProjectionPresence(page1, true);
        await crystalPage.removeUserFromPlan(
          page,
          "Simple",
          "<EMAIL>"
        );
        await crystalPage.navigate("/crystal");
        await crystalPage.CrystalPlan("Crystal 2");
        const page2 = await crystalPage.openDraftSimulator();
        await crystalPage.selectAndApplyOpportunities(page2, [
          "Deal-145",
          "Deal-46",
          "Deal-101",
        ]);
        await crystalPage.validateProjectionPresence(page2, false);
        await crystalPage.selectOpportunities(page2);
        await crystalPage.validateOpportunitiesChecked(page2, [
          "Deal-145",
          "Deal-46",
          "Deal-101",
        ]);
      }
    );
    test(
      "Validate update functionality and preview as payee pop-up",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20260" },
          {
            type: "Description",
            description:
              'Ensure that clicking on "Update" prompts a pop-up for previewing as payee and that the latest details are only reflected after confirming the update.',
          },
          {
            type: "Precondition",
            description:
              "Payees should be assigned to a plan with pending updates.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. Clicking "Update" should trigger a confirmation pop-up for previewing as payee.\n' +
              "2. The latest changes should only be applied after the update button is confirmed.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal 2").click();
        await page.getByRole("button", { name: "Edit" }).click();
        await crystalPage.confirmAndVerify(page, "Cloning the Crystal View");
        await crystalPage.confirmAndVerify(
          page,
          "You're now editing a published Crystal view. Once you're done, hit 'Update' to ensure your payees see the latest version of this Crystal view"
        );
        await crystalPage.openSettings(page, "Test View");
        await crystalPage.editViewName(page, "View 2 - Edited");
        await page.getByRole("button", { name: "Confirm" }).click();
        await crystalPage.confirmAndVerify(
          page,
          "Crystal Table Modified successfully"
        );
        await page.getByRole("button", { name: "Cancel" }).nth(0).click();
        await page.getByRole("button", { name: "Confirm" }).click();
        await crystalPage.verifyTabName(page, "Test View", "Test View");
        await page.getByRole("button", { name: "Edit" }).click();
        await crystalPage.verifyPopupMessages(page);
        await crystalPage.openSettings(page, "Test View");
        if (!(await page.getByRole("menuitem", { name: "Edit" }).isVisible())) {
          await crystalPage.openSettings(page, "Test View");
        }
        await crystalPage.editViewName(page, "View 2 - Edited");
        await page.getByRole("button", { name: "Confirm" }).click();
        await page.getByRole("button", { name: "Update" }).click();
        await crystalPage.confirmAndVerify(
          page,
          "Do you want to update the changes?"
        );
        await page.getByRole("button", { name: "Confirm" }).click();
        await crystalPage.confirmAndVerify(
          page,
          "Updating Crystal Simulator.."
        );

        await crystalPage.verifyTabName(
          page,
          "View 2 - Edited",
          "View 2 - Edited"
        );
      }
    );
    test(
      "Validate commission calculation message and payout summary display",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20267, INTER-T20271" },
          {
            type: "Description",
            description:
              "Ensure that:\n" +
              '1. After applying projections, a message "Calculating commissions... hang tight, your riches are coming!" is displayed while processing.\n' +
              "2. Payout Summary shows correct total payout value and updates based on selected view.",
          },
          {
            type: "Precondition",
            description:
              "User should have access to a Crystal view with valid payout data and ability to apply projections.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. When projections are applied, a loading message should appear: "Calculating commissions... hang tight, your riches are coming!"\n' +
              "2. Payout Summary section should display:\n" +
              "   - Total payout value\n" +
              "   - Payout by View, reflecting data based on the selected view",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.clickManagePayee();
        await crystalPage.confirmAndVerify(page, "User 1");
        await crystalPage.removalCrystalPermission(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.clickManagePayee();
        const userCount = await page
          .locator(".ant-select-dropdown")
          .getByText("User 1")
          .count();
        expect(userCount).toBe(0);
        await crystalPage.clickClose();
        await page.getByText("Crystal 2").click();
        const page1 = await crystalPage.openDraftSimulator();
        await page1.waitForLoadState("networkidle");
        const crystalTitle = page1.getByText(
          /Crystal\s*\/\s*Crystal 2 - Custom User - .* - Crystal\(Payee Preview\)/
        );
        await expect(crystalTitle).toBeVisible();

        await crystalPage.selectAndApplyOpportunities(page1, [
          "Deal-145",
          "Deal-46",
          "Deal-101",
        ]);
        await crystalPage.confirmAndVerify(
          page1,
          "While we crunch the numbers, why not practice your best victory dance"
        );
        await crystalPage.confirmAndVerify(
          page1,
          "Calculating commissions... hang tight, your riches are coming"
        );
        await crystalPage.confirmAndVerify(
          page1,
          "While we crunch the numbers, why not practice your best victory dance"
        );
        const expectedPayoutSummaryData = [
          {
            plan: "Crystal Monthly Plan",
            before: "₹0.00",
            after: ["₹50,260.00", "₹52,560.00"],
            difference: ["+ ₹50,260.00", "+ ₹52,560.00"],
          },
          {
            plan: "Total",
            before: "₹0.00",
            after: ["₹50,260.00", "₹52,560.00"],
            difference: ["+ ₹50,260.00", "+ ₹52,560.00"],
          },
        ];
        const expectedPayoutByViewData = [
          {
            dealName: "101",
            crystalMonthlyPlan: "₹4,920.00",
          },
          {
            dealName: "145",
            crystalMonthlyPlan: "₹2,790.00",
          },
          {
            dealName: "46",
            crystalMonthlyPlan: "₹1,820.00",
          },
        ];
        await crystalPage.navigateToPayoutSummary(page1);
        await crystalPage.validatePayoutSummaryTable(
          page1,
          expectedPayoutSummaryData
        );

        // Navigate and validate Payout By View table
        await crystalPage.navigateToPayoutByView(page1);
        await crystalPage.validatePayoutByViewTable(
          page1,
          expectedPayoutByViewData
        );
      }
    );
    test(
      'Validate "Add opportunities" is disabled when Crystal is not published',
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20252" },
          {
            type: "Description",
            description:
              'Ensure that if a user has Crystal access but the Crystal is not yet published, the "Add opportunities" button remains disabled.',
          },
          {
            type: "Precondition",
            description:
              "User should have active Crystal access.\nCrystal view assigned to the user should be in an unpublished state.",
          },
          {
            type: "Expected Behaviour",
            description:
              '1. The "Add opportunities" button should be visible but disabled.\n' +
              "2. User should not be able to interact with or perform any add opportunity actions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await crystalPage.navigate("/users");
        await crystalPage.loginAsUser("<EMAIL>");
        await crystalPage.validateImpersonation(
          " Monthly user (<EMAIL>)"
        );
        await crystalPage.navigate("/crystal");
        await expect(
          page.getByRole("button", { name: "Select opportunities" })
        ).toBeDisabled();
        await page.getByRole("button", { name: "Exit" }).click();
        await crystalPage.navigate("/crystal");
      }
    );
    test(
      'Validate cell editing functionality in "Apply Projection" screen',
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20265" },
          {
            type: "Description",
            description:
              'Ensure that when cell editing is enabled, users can edit cell values in the "Apply Projection" screen and the original value is displayed at the top-left corner.',
          },
          {
            type: "Precondition",
            description:
              'Cell editing should be enabled in the "Apply Projection" configuration or settings.',
          },
          {
            type: "Expected Behaviour",
            description:
              '1. User should be able to edit the cell value in the "Apply Projection" screen.\n' +
              "2. The original (pre-edit) value should appear in the top-left corner of the screen as a reference.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        await page.getByRole("button", { name: "Add tab" }).click();
        await crystalPage.createView(
          "View 2",
          "Playwright test for new simulator2",
          "Crystal Data_Copy"
        );
        await crystalPage.configureTableConditions("Deal Date", "Is Not Empty");
        await crystalPage.setSuccessActions("Deal Status", "True");
        await crystalPage.setEmailAndDateField(3, 4);
        await crystalPage.setRowName("Deal ID");
        await crystalPage.enableCellEditing("Deal ID");
        await crystalPage.enableCellEditing("Deal Date");
        await crystalPage.enableCellEditing("Deal Email");
        await crystalPage.enableCellEditing("Commission");
        await crystalPage.enableCellEditing("Deal Amount");
        await crystalPage.confirmCreation();
        const page1 = await crystalPage.openDraftSimulator();
        await crystalPage.selectOpportunities(page1);
        await page1.getByText("View 2").click();
        await page1.waitForLoadState("networkidle");

        const newDealId = "129";
        const newDealEmail = "<EMAIL>";
        const newDealAmount = "10,000";

        const row = page1.locator('div[role="row"][row-id="0"]');

        console.log("Updating Deal ID...");
        const dealId = row.locator('div[col-id="co_1_deal_id"]').nth(1);
        await crystalPage.updateDealField(dealId, newDealId, page1);
        await crystalPage.validateUpdatedText(dealId, newDealId);

        console.log("Updating Deal Date...");
        await crystalPage.updateDealDate(row, page1);

        console.log("Updating Deal Email...");
        const dealEmail = row.locator('div[col-id="co_1_deal_email"]').nth(1);
        await crystalPage.updateDealField(dealEmail, newDealEmail, page1);
        await crystalPage.validateUpdatedText(
          dealEmail.locator("span:first-child"),
          newDealEmail
        );
        await crystalPage.validateStrikethroughText(
          dealEmail,
          "<EMAIL>"
        );

        console.log("Updating Deal Amount...");
        const dealAmount = row.locator('div[col-id="co_1_deal_amount"]').nth(1);
        await crystalPage.updateDealField(dealAmount, newDealAmount, page1);
        await crystalPage.validateUpdatedText(dealAmount, newDealAmount);

        console.log("Validating Commission Color Change...");
        const commissionCell = row
          .locator('div[col-id="co_1_commission"]')
          .nth(1);
        await crystalPage.validateColorChange(
          commissionCell,
          "rgb(242, 245, 254)",
          "rgb(255, 241, 219)",
          page1
        );

        console.log("✅ All validations passed!");
      }
    );
    test(
      "Validate cell editing restricts input to column data type only",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T20266" },
          {
            type: "Description",
            description:
              "Ensure that when editing is enabled for cells, users can only input values that match the expected data type of the column.",
          },
          {
            type: "Precondition",
            description:
              "Cell editing should be enabled in the relevant screen (e.g., Apply Projection or Crystal view). Column should have a defined data type (e.g., number, text, date).",
          },
          {
            type: "Expected Behaviour",
            description:
              "1. User should be restricted from entering values that do not match the column’s data type.\n" +
              "2. Invalid data types should either be blocked or show a validation error.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const crystalPage = new CrystalPage(page);
        await crystalPage.navigate("/crystal");
        await page.getByText("Crystal test Simulator").click();
        const page1 = await crystalPage.openDraftSimulator();
        await crystalPage.selectOpportunities(page1);
        await page1.getByText("View 2").click();
        await page1.waitForLoadState("networkidle");
        const row = page1.locator('div[role="row"][row-id="0"]');

        const dealAmount = row.locator('div[col-id="co_1_deal_amount"]').nth(1);
        await crystalPage.validateNumericFieldRestriction(
          dealAmount,
          "abc123",
          page1
        );
      }
    );
  }
);