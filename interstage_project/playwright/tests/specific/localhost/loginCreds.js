export const baseUrl = "http://localhost:3000";

export const DEFAULT_CREDS = {
  adminUsername: process.env.DEFAULT_LOCAL_ADMIN_USERNAME,
  adminPassword: process.env.DEFAULT_LOCAL_ADMIN_PASSWORD,
  // payeeUsername: process.env.LOCAL_PAYEE_USERNAME,
  // payeePassword: process.env.LOCAL_PAYEE_PASSWORD,
};

export const LOCAL_QACREDS = {
  adminUsername: process.env.LOCAL_QA_ADMIN_USERNAME,
  adminPassword: process.env.LOCAL_QA_ADMIN_PASSWORD,
  payeeUsername: process.env.LOCAL_QA_PAYEE_USERNAME,
  payeePassword: process.env.LOCAL_QA_PAYEE_PASSWORD,
};

export const LOCAL_NOTIFICATION_CREDS = {
  adminUsername: process.env.LOCAL_QA_ADMIN_USERNAME,
  adminPassword: process.env.LOCAL_QA_ADMIN_PASSWORD,
  payeeUsername: process.env.LOCAL_NOTIFICATION_PAYEE_USERNAME,
  payeePassword: process.env.LOCAL_NOTIFICATION_PAYEE_PASSWORD,
};

export const LOCAL_CLONE_CREDS = {
  adminUsername: process.env.LOCAL_CLONE_ADMIN_USERNAME,
  adminPassword: process.env.LOCAL_CLONE_ADMIN_PASSWORD,
  payeeUsername: process.env.QA_PLAYWRIGHT_COMMON_USERNAME,
  payeePassword: process.env.QA_PLAYWRIGHT_COMMON_PASSWORD,
};

export const LOCALPLAY_CREDS = {
  adminUsername: process.env.LOCALPLAY_ADMIN_USERNAME,
  adminPassword: process.env.LOCALPLAY_ADMIN_PASSWORD,
};

export const LOCAL_CONTRACTS_CREDS = {
  adminUsername: process.env.LOCAL_CONTRACT_ADMIN_USERNAME,
  adminPassword: process.env.LOCAL_CONTRACT_ADMIN_PASSWORD,
};

export const LOCAL_PLAYWRIGHT_CREDS = {
  adminUsername: process.env.LOCAL_PLAYWRIGHT_ADMIN_USERNAME,
  adminPassword: process.env.LOCAL_PLAYWRIGHT_ADMIN_PASSWORD,
  payeeUsername: process.env.LOCAL_PLAYWRIGHT_PAYEE_USERNAME,
  payeePassword: process.env.LOCAL_PLAYWRIGHT_PAYEE_PASSWORD,
};

export const ADDED_CREDS = {
  adminUsername: process.env.ADDED_CREDS_ADMIN_USERNAME,
  adminPassword: process.env.ADDED_CREDS_ADMIN_PASSWORD,
};

export const INVITED_CREDS = {
  adminUsername: process.env.INVITED_CREDS_ADMIN_USERNAME,
  adminPassword: process.env.INVITED_CREDS_ADMIN_PASSWORD,
};

export const ACTIVE_CREDS = {
  adminUsername: process.env.INVITED_CREDS_ADMIN_USERNAME,
  adminPassword: process.env.INVITED_CREDS_ADMIN_PASSWORD,
};

export const INACTIVE_CREDS = {
  adminUsername: process.env.INACTIVE_CREDS_ADMIN_USERNAME,
  adminPassword: process.env.INACTIVE_CREDS_ADMIN_PASSWORD,
};

export const SEND_TO_ACTIVE_CREDS = {
  adminUsername: process.env.SEND_TO_ACTIVE_CREDS_ADMIN_USERNAME,
  adminPassword: process.env.SEND_TO_ACTIVE_CREDS_ADMIN_PASSWORD,
};

export const COMMISSIONPLAN_CREDS = {
  adminUsername: process.env.COMMISSION_PLAN_USERNAME,
  adminPassword: process.env.COMMISSION_PLAN_PASSWORD,
};

export const COMMISSIONPLAN_V2_CREDS = {
  adminUsername: process.env.COMMISSION_PLAN_V2_USERNAME,
  adminPassword: process.env.COMMISSION_PLAN_V2_PASSWORD,
};

export const COMMISSIONRBAC_CREDS = {
  adminUsername: process.env.PLANRBAC_USERNAME,
  adminPassword: process.env.PLANRBAC_PASSWORD,
};

export const LOCAL_ENRICH_CREDS = {
  adminUsername: process.env.LOCAL_ENRICH_USERNAME,
  adminPassword: process.env.LOCAL_ENRICH_PASSWORD,
};

export const MAPPAYEE_CREDS = {
  adminUsername: process.env.MAPPAYEE_USERNAME,
  adminPassword: process.env.MAPPAYEE_PASSWORD,
};

export const LINEITEM_CREDS = {
  adminUsername: process.env.LINEITEM_USERNAME,
  adminPassword: process.env.LINEITEM_PASSWORD,
  payeeUsername: process.env.LINEITEM_PAYEE_USERNAME,
  payeePassword: process.env.LINEITEM_PAYEE_PASSWORD,
};

export const FORECAST_CREDS = {
  adminUsername: process.env.FORECAST_USERNAME,
  adminPassword: process.env.FORECAST_PASSWORD,
};

export const CANVAS_CREDS = {
  adminUsername: process.env.CANVAS_USERNAME,
  adminPassword: process.env.CANVAS_PASSWORD,
};

export const LINEITEM_APPROVALS_CREDS = {
  adminUsername: process.env.LINEITEMAPPROVALS_USERNAME,
  adminPassword: process.env.LINEITEMAPPROVALS_PASSWORD,
  payeeUsername: process.env.LINEITEMAPPROVALS_PAYEE_USERNAME,
  payeePassword: process.env.LINEITEMAPPROVALS_PAYEE_PASSWORD,
};

export const LINEITEM_MANAGER_APPROVALS_CREDS = {
  adminUsername: process.env.LINEITEMAPPROVALS_MANAGER_USERNAME,
  adminPassword: process.env.LINEITEMAPPROVALS_MANAGER_PASSWORD,
};

export const BULKUPLOAD_USERS_CREDS = {
  adminUsername: process.env.BULKUPLOAD_USERNAME,
  adminPassword: process.env.BULKUPLOAD_PASSWORD,
};

export const NEWAPP_CREDS = {
  adminUsername: process.env.NEWAPP_USERNAME,
  adminPassword: process.env.NEWAPP_PASSWORD,
  payeeUsername: process.env.PUSER_PAYEE_USERNAME,
  payeePassword: process.env.PPWD_PLAYWRIGHT_PAYEE_PASSWORD,
};

export const ADJ_APPROVALS_CREDS = {
  adminUsername: process.env.ADJ_APPROVALS_USERNAME,
  adminPassword: process.env.ADJ_APPROVALS_PASSWORD,
};

export const CANVAS_CREDS_UI = {
  adminUsername: process.env.CANVAS_UI_USERNAME,
  adminPassword: process.env.CANVAS_UI_PASSWORD,
};

export const BYOT_TEST_USER_CREDS = {
  adminUsername: process.env.BYOT_TESTS_USERNAME,
  adminPassword: process.env.BYOT_TESTS_PASSWORD,
};

export const CUSTOM_CALENDAR_TEST_USER_CREDS = {
  adminUsername: process.env.CUSTOM_CALENDAR_TESTS_USERNAME,
  adminPassword: process.env.CUSTOM_CALENDAR_TESTS_PASSWORD,
};

export const OBJECT_PERMISSIONS_TEST_USER_CREDS = {
  adminUsername: process.env.OBJECT_PERMISSION_ADMIN_USERNAME,
  adminPassword: process.env.OBJECT_PERMISSION_ADMIN_PASSWORD,
  payeeUsername: process.env.OBJECT_PERMISSION_PAYEE_USERNAME,
  payeePassword: process.env.OBJECT_PERMISSION_PAYEE_PASSWORD,
};

export const OBJECT_PERMISSIONS_DISABLED_TEST_USER_CREDS = {
  adminUsername: process.env.OBJECT_PERMISSION_DISABLED_ADMIN_USERNAME,
  adminPassword: process.env.OBJECT_PERMISSION_DISABLED_ADMIN_PASSWORD,
  payeeUsername: process.env.OBJECT_PERMISSION_DISABLED_PAYEE_USERNAME,
  payeePassword: process.env.OBJECT_PERMISSION_DISABLED_PAYEE_PASSWORD,
};

export const TEAMS_USERS_CREDS = {
  adminUsername: process.env.TEAMSTEST_USERNAME,
  adminPassword: process.env.TEAMSTEST_PASSWORD,
};

export const PLAYWRIGHT_PEOPLE_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_PEOPLE_ADMIN_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_PEOPLE_ADMIN_PASSWORD,
};

export const PLAYWRIGHT_PEOPLE_CREDS_2 = {
  adminUsername: process.env.PLAYWRIGHT_PEOPLE_2_ADMIN_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_PEOPLE_2_ADMIN_PASSWORD,
};

export const PLAYWRIGHT_MAP_PAYEE_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_MAP_PAYEE_ADMIN_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_MAP_PAYEE_ADMIN_PASSWORD,
};

export const PLAYWRIGHT_HIERARCHY_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_HIERARCHY_ADMIN_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_HIERARCHY_ADMIN_PASSWORD,
};

export const PAYOUT_APPROVALS_CREDS = {
  adminUsername: process.env.PAYOUTAPPROVALS_USERNAME,
  adminPassword: process.env.PAYOUTAPPROVALS_PASSWORD,
};
export const USER_LOCK_CREDS = {
  adminUsername: process.env.USER_LOCK_USERNAME,
  adminPassword: process.env.USER_LOCK_PASSWORD,
};
export const SETTLEMENT_ADMIN_CREDS = {
  adminUsername: process.env.SETTLEMENT_USERNAME,
  adminPassword: process.env.SETTLEMENT_PASSWORD,
};

export const CUSTOM_OBJECT_CREDS = {
  adminUsername: process.env.CUSTOM_OBJECT_USERNAME,
  adminPassword: process.env.CUSTOM_OBJECT_PASSWORD,
  payeeUsername: process.env.CUSTOM_OBJECT_PAYEE_USERNAME,
  payeePassword: process.env.CUSTOM_OBJECT_PAYEE_PASSWORD,
};

export const CUSTOM_OBJECT_CRM_FLAG_DISABLE_CREDS = {
  adminUsername: process.env.CUSTOM_OBJECT_CRM_DISABLE_USERNAME,
  adminPassword: process.env.CUSTOM_OBJECT_CRM_DISABLE_PASSWORD,
};

export const ROLES_CREDS = {
  adminUsername: process.env.ROLES_USERNAME,
  adminPassword: process.env.ROLES_PASSWORD,
  payeeUsername: process.env.ROLES_POWER_ADMIN_USERNAME,
  payeePassword: process.env.ROLES_POWER_ADMIN_PASSWORD,
};

export const REPORT_ENRICHENT_CREDS = {
  adminUsername: process.env.REPORT_ENRICHMENT_USERNAME,
  adminPassword: process.env.REPORT_ENRICHMENT_PASSWORD,
  payeeUsername: process.env.REPORT_ENRICHMENT_PAYEE_USERNAME,
  payeePassword: process.env.REPORT_ENRICHMENT_PAYEE_PASSWORD,
};

// export const COMMISSION_SYNC_CREDS = {
//   adminUsername: process.env.COMMISSION_SYNC_USERNAME,
//   adminPassword: process.env.COMMISSION_SYNC_PASSWORD,
// };
export const REPORT_ENRICHENT_CLIENT_CREDS = {
  adminUsername: process.env.REPORT_ENRICHMENT_CLIENT_USERNAME,
  adminPassword: process.env.REPORT_ENRICHMENT_CLIENT_PASSWORD,
};

export const COMMISSION_SYNC_CREDS = {
  adminUsername: process.env.COMMISSION_SYNC_USERNAME,
  adminPassword: process.env.COMMISSION_SYNC_PASSWORD,
  payeeUsername: process.env.COMMISSION_SYNC_CUSTOM_USERNAME,
  payeePassword: process.env.COMMISSION_SYNC_CUSTOM_PASSWORD,
};
export const COMMISSION_SYNC_FOR_ADMINS_CREDS = {
  adminUsername: process.env.COMMISSION_SYNC_USERNAME,
  adminPassword: process.env.COMMISSION_SYNC_PASSWORD,
  payeeUsername: process.env.COMMISSION_SYNC_PAYEE_USERNAME,
  payeePassword: process.env.COMMISSION_SYNC_PAYEE_PASSWORD,
};

export const COMMISSION_ADJUSTMENTS_CREDS = {
  adminUsername: process.env.COMMISSION_ADJUSTMENT_ADMIN_USERNAME,
  adminPassword: process.env.COMMISSION_ADJUSTMENT_ADMIN_PASSWORD,
  payeeUsername: process.env.COMMISSION_ADJUSTMENT_PAYEE_USERNAME,
  payeePassword: process.env.COMMISSION_ADJUSTMENT_PAYEE_PASSWORD,
};
export const GLOBAL_SEARCH_CREDS = {
  adminUsername: process.env.GLOBAL_SEARCH_USERNAME,
  adminPassword: process.env.GLOBAL_SEARCH_PASSWORD,
};

export const COMMISSION_ADJUSTMENTS_OFF_CREDS = {
  adminUsername: process.env.COMMISSION_ADJUSTMENT_OFF_ADMIN_USERNAME,
  adminPassword: process.env.COMMISSION_ADJUSTMENT_OFF_ADMIN_PASSWORD,
};

export const DRAW_ADJUSTMENTS_CREDS = {
  adminUsername: process.env.DRAW_ADJUSTMENTS_ADMIN_USERNAME,
  adminPassword: process.env.DRAW_ADJUSTMENTS_ADMIN_PASSWORD,
};
export const COMM_SYNC_CREDS = {
  adminUsername: process.env.COMM_SYNC_USERNAME,
  adminPassword: process.env.COMM_SYNC_PASSWORD,
};

export const PLAYWRIGHT_COMM_PLAN_CANVAS_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_CANVAS_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_CANVAS_PASSWORD,
};

export const PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_PASSWORD,
};

export const PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_SA = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_USERNAME_SA,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_PASSWORD_SA,
};

export const PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_2 = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_2_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_2_PASSWORD,
};

export const PLAYWRIGHT_COMM_PLAN_FORECAST_CREDS_3 = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_3_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_FORECAST_3_PASSWORD,
};

export const PLAYWRIGHT_COMM_PLAN_TIER_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_TIER_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_TIER_PASSWORD,
};

export const QUOTA_CREDS = {
  adminUsername: process.env.QUOTA_USERNAME,
  adminPassword: process.env.QUOTA_PASSWORD,
  payeeUsername: process.env.QUOTA_ADMIN_USERNAME,
  payeePassword: process.env.QUOTA_ADMIN_PASSWORD,
};

export const QUOTA_PAYEE_CREDS = {
  adminUsername: process.env.QUOTA_PAYEE_USERNAME,
  adminPassword: process.env.QUOTA_PAYEE_PASSWORD,
};

export const QUOTA_FUNCTION_CREDS = {
  adminUsername: process.env.QUOTA_FUNCTION_USERNAME,
  adminPassword: process.env.QUOTA_FUNCTION_PASSWORD,
  payeeUsername: process.env.QUOTA_FUNCTION_PAYEE_USERNAME,
  payeePassword: process.env.QUOTA_FUNCTION_PAYEE_PASSWORD,
};

export const CW_CREDS = {
  adminUsername: process.env.CW_USERNAME,
  adminPassword: process.env.CW_PASSWORD,
};

export const SETTINGS_NEW_BALA_CREDS = {
  adminUsername: process.env.SETTINGS_NEW_USERNAME,
  adminPassword: process.env.SETTINGS_NEW_PASSWORD,
  payeeUsername: process.env.SETTINGS_NEW_USERNAME,
  payeePassword: process.env.SETTINGS_NEW_PASSWORD,
};

export const SETTINGS_NEW_BALA_ADMIN_CREDS = {
  adminUsername: process.env.SETTINGS_NEW_ADMIN_USERNAME,
  adminPassword: process.env.SETTINGS_NEW_ADMIN_PASSWORD,
};

export const PLAYWRIGHT_SETTLEMENT_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_SETTLEMENT_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_SETTLEMENT_PASSWORD,
};

export const QUERY_TEST_CREDS = {
  adminUsername: process.env.QUERY_TEST_ADMIN_USERNAME,
  adminPassword: process.env.QUERY_TEST_ADMIN_PASSWORD,
  payeeUsername: process.env.QUERY_PAYEE_USERNAME,
  payeePassword: process.env.QUERY_PAYEE_PASSWORD,
};

export const DRAWS_TEST_CREDS_1 = {
  adminUsername: process.env.DRAWS_TEST_ADMIN_USERNAME,
  adminPassword: process.env.DRAWS_TEST_ADMIN_PASSWORD,
  payeeUsername: process.env.DRAWS_PAYEE_USERNAME,
  payeePassword: process.env.DRAWS_PAYEE_PASSWORD,
};

export const DRAWS_TEST_CREDS = {
  adminUsername: process.env.DRAWS_ADMIN_USERNAME,
  adminPassword: process.env.DRAWS_ADMIN_PASSWORD,
};

export const COMM_ADJ_APP_CREDS = {
  adminUsername: process.env.COMM_ADJ_APP_USERNAME,
  adminPassword: process.env.COMM_ADJ_APP_PASSWORD,
};

export const COMM_ADJ_APP_CREDS_2 = {
  adminUsername: process.env.COMM_ADJ_APP_USERNAME_2,
  adminPassword: process.env.COMM_ADJ_APP_PASSWORD_2,
};

export const COMM_ADJ_APP_CREDS_3 = {
  adminUsername: process.env.COMM_ADJ_APP_USERNAME_3,
  adminPassword: process.env.COMM_ADJ_APP_PASSWORD_3,
};

export const PLANS_BALA_CREDS = {
  adminUsername: process.env.PLANS_BALA_USERNAME,
  adminPassword: process.env.PLANS_BALA_PASSWORD,
};

export const PLANS_BALA_PAYEE_CREDS = {
  adminUsername: process.env.PLANS_BALA_PAYEE_USERNAME,
  adminPassword: process.env.PLANS_BALA_PAYEE_PASSWORD,
};

export const PLANS_BALA_PAYEE_CREDS_2 = {
  adminUsername: process.env.PLANS_BALA_PAYEE_USERNAME_2,
  adminPassword: process.env.PLANS_BALA_PAYEE_PASSWORD_2,
};

export const REVERTUSER_TEST_CREDS = {
  adminUsername: process.env.REVERTUSER_USERNAME,
  adminPassword: process.env.REVERTUSER_PASSWORD,
};

export const PAYOUTS_BALA_CREDS = {
  adminUsername: process.env.PAYOUTS_BALA_USERNAME,
  adminPassword: process.env.PAYOUTS_BALA_PASSWORD,
};

export const PAYOUTS_PAYEE_CREDS = {
  adminUsername: process.env.PAYOUTS_PAYEE_USERNAME,
  adminPassword: process.env.PAYOUTS_PAYEE_PASSWORD,
};

export const PAYOUTS_PAYEE_CREDS_2 = {
  adminUsername: process.env.PAYOUTS_PAYEE_USERNAME_2,
  adminPassword: process.env.PAYOUTS_PAYEE_PASSWORD_2,
};

export const PAYOUTS_PAYEE_CREDS_3 = {
  adminUsername: process.env.PAYOUTS_PAYEE_USERNAME_3,
  adminPassword: process.env.PAYOUTS_PAYEE_PASSWORD_3,
};

export const PAYOUTS_BALA_CREDS_2 = {
  adminUsername: process.env.PAYOUTS_BALA_USERNAME_2,
  adminPassword: process.env.PAYOUTS_BALA_PASSWORD_2,
};

export const PAYOUTS_BALA_CREDS_3 = {
  adminUsername: process.env.PAYOUTS_BALA_USERNAME_3,
  adminPassword: process.env.PAYOUTS_BALA_PASSWORD_3,
};

export const ROUNDOFFTIER_CREDS = {
  adminUsername: process.env.ROUNDOFFTIER_USERNAME,
  adminPassword: process.env.ROUNDOFFTIER_PASSWORD,
};

export const DATASHEET_V2_CREDS = {
  adminUsername: process.env.DATASHEET_V2_USERNAME,
  adminPassword: process.env.DATASHEET_V2_PASSWORD,
};

export const STATEMENT_SNAP_CREDS = {
  adminUsername: process.env.STATEMENT_SNAP_USERNAME,
  adminPassword: process.env.STATEMENT_SNAP_PASSWORD,
};

export const HRIS_CREDS = {
  adminUsername: process.env.HRIS_USERNAME,
  adminPassword: process.env.HRIS_PASSWORD,
};

export const CRYSTAL_BALA_CREDS = {
  adminUsername: process.env.CRYSTAL_BALA_USERNAME,
  adminPassword: process.env.CRYSTAL_BALA_PASSWORD,
};

export const CRYSTAL_PAYEE_CREDS_1 = {
  adminUsername: process.env.CRYSTAL_PAYEE_USERNAME_1,
  adminPassword: process.env.CRYSTAL_PAYEE_PASSWORD_1,
};

export const CRYSTAL_PAYEE_CREDS_2 = {
  adminUsername: process.env.CRYSTAL_PAYEE_USERNAME_2,
  adminPassword: process.env.CRYSTAL_PAYEE_PASSWORD_2,
};

export const QUERIES_LINE_ITEMS_CREDS = {
  adminUsername: process.env.QUERIES_LINE_ITEMS_USERNAME,
  adminPassword: process.env.QUERIES_LINE_ITEMS_PASSWORD,
};

export const BREAKGLASS_CREDS = {
  adminUsername: process.env.BREAKGLASS_USERNAME,
  adminPassword: process.env.BREAKGLASS_PASSWORD,
};
export const COMMISSION_SYNC_RUN_PREV_PERIOD_CREDS = {
  adminUsername: process.env.COMMISSION_SYNC_RUN_PREV_PERIOD_USERNAME,
  adminPassword: process.env.COMMISSION_SYNC_RUN_PREV_PERIOD_PASSWORD,
};

export const DATABOOK_UI_2_CREDS = {
  adminUsername: process.env.DATABOOK_UI_2_USERNAME,
  adminPassword: process.env.DATABOOK_UI_2_PASSWORD,
};

export const CELERY_TASK_CREDS = {
  adminUsername: process.env.CELERY_TASK_USERNAME,
  adminPassword: process.env.CELERY_TASK_PASSWORD,
};

export const QUOTA_ADMIN_CREDS = {
  adminUsername: process.env.PLAYWRIGHT_COMM_PLAN_TIER_USERNAME,
  adminPassword: process.env.PLAYWRIGHT_COMM_PLAN_TIER_PASSWORD,
  payeeUsername: process.env.USERTEST_ADMIN_USERNAME,
  payeePassword: process.env.USERTEST_ADMIN_PASSWORD,
};

export const QUOTA_MANAGEQUOTA_CREDS = {
  adminUsername: process.env.USERTEST_MANAGEQUOTA_USERNAME,
  adminPassword: process.env.USERTEST_MANAGEQUOTA_PASSWORD,
  payeeUsername: process.env.USERTEST_MANAGEQUOTASETTINGS_USERNAME,
  payeePassword: process.env.USERTEST_MANAGEQUOTASETTINGS_PASSWORD,
};

export const PAYEE_CURRENCY_CREDS = {
  adminUsername: process.env.PAYEE_CURRENCY_USERNAME,
  adminPassword: process.env.PAYEE_CURRENCY_PASSWORD,
};

export const USER_GROUP_CREDS = {
  adminUsername: process.env.USER_GROUP_USERNAME,
  adminPassword: process.env.USER_GROUP_PASSWORD,
};
export const IK_USERS_CREDS = {
  adminUsername: process.env.IKUSERS_MANAGERENDDATE_USERNAME,
  adminPassword: process.env.IKUSERS_MANAGERENDDATE_PASSWORD,
};

export const REEVALUATE_CREDS = {
  adminUsername: process.env.REEVALUATE_USERNAME,
  adminPassword: process.env.REEVALUATE_PASSWORD,
};

export const G2REVIEW_CREDS = {
  adminUsername: process.env.G2REVIEW_USERNAME,
  adminPassword: process.env.G2REVIEW_PASSWORD,
};

export const PAYOUT_CREDS = {
  adminUsername: process.env.PAYOUT_USERNAME,
  adminPassword: process.env.PAYOUT_PASSWORD,
};

export const RENAME_HYPERLINK_CREDS = {
  adminUsername: process.env.RENAME_HYPERLINK_USERNAME,
  adminPassword: process.env.RENAME_HYPERLINK_PASSWORD,
};

export const COMMISSION_SYNC_ETL_CREDS = {
  adminUsername: process.env.COMMISSION_SYNC_ETL_USERNAME,
  adminPassword: process.env.COMMISSION_SYNC_ETL_PASSWORD,
};

export const CKEDITORCW_CREDS = {
  adminUsername: process.env.CKEDITORCW_USERNAME,
  adminPassword: process.env.CKEDITORCW_PASSWORD,
};

export const ADJUSTMENTS_V2_CREDS = {
  adminUsername: process.env.ADJUSTMENTS_V2_USERNAME,
  adminPassword: process.env.ADJUSTMENTS_V2_PASSWORD,
};

export const QUOTA_ALLOW_OVERRIDE_CREDS = {
  adminUsername: process.env.QUOTA_ALLOW_OVVERIDE_POWER_ADMIN_USERNAME,
  adminPassword: process.env.QUOTA_ALLOW_OVVERIDE_POWER_ADMIN_PASSWORD,
  payeeUsername: process.env.QUOTA_ALLOW_OVVERIDE_ADMIN_USERNAME,
  payeePassword: process.env.QUOTA_ALLOW_OVVERIDE_ADMIN_PASSWORD,
};

export const QUOTA_ANNUAL_ED_CREDS = {
  adminUsername: process.env.QUOTA_ANNUAL_ED_USERNAME,
  adminPassword: process.env.QUOTA_ANNUAL_ED_PASSWORD,
};

export const MAP_PAYEE_HISTORICAL_EDITS_CREDS = {
  adminUsername: process.env.MAP_PAYEE_HISTORICAL_EDITS_USERNAME,
  adminPassword: process.env.MAP_PAYEE_HISTORICAL_EDITS_PASSWORD,
};

export const COMMISSION_LINE_ITEM_SORT_CREDS = {
  adminUsername: process.env.COMMISSION_LINE_ITEM_SORT_USERNAME,
  adminPassword: process.env.COMMISSION_LINE_ITEM_SORT_PASSWORD,
};

export const CPQ_CREDS = {
  adminUsername: process.env.CPQ_USERNAME,
  adminPassword: process.env.CPQ_PASSWORD,
};

export const PAYOUTSIKPLAYWRIGHT_CREDS = {
  adminUsername: process.env.PAYOUTSIKPLAYWRIGHT_USERNAME,
  adminPassword: process.env.PAYOUTSIKPLAYWRIGHT_PASSWORD,
};

export const AUTO_ENRICH_REPORT_CREDS = {
  adminUsername: process.env.AUTO_ENRICH_REPORT_USERNAME,
  adminPassword: process.env.AUTO_ENRICH_REPORT_PASSWORD,
};

export const USER_INVITE_MULTI_USER_1_CREDS = {
  adminUsername: process.env.USER_INVITE_MULTI_USER_1_USERNAME,
  adminPassword: process.env.USER_INVITE_MULTI_USER_1_PASSWORD,
};

export const USER_INVITE_MULTI_USER_2_CREDS = {
  adminUsername: process.env.USER_INVITE_MULTI_USER_2_USERNAME,
  adminPassword: process.env.USER_INVITE_MULTI_USER_2_PASSWORD,
};

export const INVITED_USER_CREDS = {
  adminUsername: process.env.INVITED_USER_USERNAME,
  adminPassword: process.env.INVITED_USER_PASSWORD,
};

export const VALIDATE_USER_1_CREDS = {
  adminUsername: process.env.VALIDATE_USER_1_USERNAME,
  adminPassword: process.env.VALIDATE_USER_1_PASSWORD,
};

export const VALIDATE_USER_2_CREDS = {
  adminUsername: process.env.VALIDATE_USER_2_USERNAME,
  adminPassword: process.env.VALIDATE_USER_2_PASSWORD,
};

export const PLAN_PERIOD_EXCLUSION_CREDS = {
  adminUsername: process.env.PLAN_PERIOD_EXCLUSION_USERNAME,
  adminPassword: process.env.PLAN_PERIOD_EXCLUSION_PASSWORD,
};

export const BULK_ADJUSTMENTS_V2_CREDS = {
  adminUsername: process.env.BULK_ADJUSTMENTS_V2_USERNAME,
  adminPassword: process.env.BULK_ADJUSTMENTS_V2_PASSWORD,
};

export const DATASHEET_V2_MANAGE_PERMISSION_CREDS = {
  adminUsername: process.env.DATASHEET_V2_MANAGE_PERMISSION_USERNAME,
  adminPassword: process.env.DATASHEET_V2_MANAGE_PERMISSION_PASSWORD,
};

export const SPLIT_SUMMATION_CREDS = {
  adminUsername: process.env.SPLIT_SUMMATION_USERNAME,
  adminPassword: process.env.SPLIT_SUMMATION_PASSWORD,
};

export const ADVANCEDFILTERS_CREDS = {
  adminUsername: process.env.AF_USERNAME,
  adminPassword: process.env.AF_PASSWORD,
};
export const DELETE_PAYROLL_CREDS = {
  adminUsername: process.env.DELETE_PAYROLL_USERNAME,
  adminPassword: process.env.DELETE_PAYROLL_PASSWORD,
};

export const MANUAL_UPLOAD_REVAMP_CREDS = {
  adminUsername: process.env.MANUAL_UPLOAD_REVAMP_USERNAME,
  adminPassword: process.env.MANUAL_UPLOAD_REVAMP_PASSWORD,
};

export const COMMISSION_ADJUSTMENT_FILTER_CREDS = {
  adminUsername: process.env.COMMISSION_ADJUSTMENT_FILTER_USERNAME,
  adminPassword: process.env.COMMISSION_ADJUSTMENT_FILTER_PASSWORD,
};

export const CPQTEST_CREDS = {
  adminUsername: process.env.CPQTEST_USERNAME,
  adminPassword: process.env.CPQTEST_PASSWORD,
};

export const DATASHEET_PROD_CREDS = {
  adminUsername: process.env.DATASHEET_PROD_USERNAME,
  adminPassword: process.env.DATASHEET_PROD_PASSWORD,
};

export const DS_V2_ENRICHMENT_CREDS = {
  adminUsername: process.env.DS_V2_ENRICHMENT_USERNAME,
  adminPassword: process.env.DS_V2_ENRICHMENT_PASSWORD,
};
export const CPQ_SUBSCRIPTION_DATE_CREDS = {
  adminUsername: process.env.CPQ_SUBSCRIPTION_DATES_USERNAME,
  adminPassword: process.env.CPQ_SUBSCRIPTION_DATES_PASSWORD,
};

export const BULK_PAYMENTS_REGISTER_CREDS = {
  adminUsername: process.env.BULK_PAYMENTS_REGISTER_USERNAME,
  adminPassword: process.env.BULK_PAYMENTS_REGISTER_PASSWORD,
};

export const CRYSTAL_NEW_CREDS = {
  adminUsername: process.env.CRYSTAL_TEST_USERNAME,
  adminPassword: process.env.CRYSTAL_TEST_PASSWORD,
};
export const SPLIT_EMPLOYEE_CREDS = {
  adminUsername: process.env.SPLIT_EMPLOYEE_RECORDS_USERNAME,
  adminPassword: process.env.SPLIT_EMPLOYEE_RECORDS_PASSWORD,
};

export const CUSTOM_CATEGORY_CREDS = {
  adminUsername: process.env.CUSTOM_CATEGORY_USERNAME,
  adminPassword: process.env.CUSTOM_CATEGORY_PASSWORD,
};
