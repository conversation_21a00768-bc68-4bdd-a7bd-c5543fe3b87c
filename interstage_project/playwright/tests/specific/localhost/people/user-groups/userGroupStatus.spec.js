const {
  usergroupFixtures: { test, expect },
} = require("../../../../fixtures");

const UserGroupPage = require("../../../../../test-objects/usergroup-objects.js");
const UserPage = require("../../../../../test-objects/user-objects");
const CommissionRBAC = require("../../../../../test-objects/commisionRBAC-objects");
const QueriesLineItem = require("../../../../../test-objects/querieslineitems-objects");
const PayoutPage = require("../../../../../test-objects/payout-objects");
const StatementPage = require("../../../../../test-objects/statement-objects");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/groups", { waitUntil: "networkidle" });
  await page
    .getByText("New Group")
    .waitFor({ state: "visible", timeout: 35000 });
});

test.describe(
  "Users",
  { tag: ["@groups", "@regression", "@adminchamp-2"] },
  () => {
    test(
      "Count should get updated in group after the status has been updated",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17718" },
          {
            type: "Description",
            description:
              "The count should get updated in user group after editing the role for user",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "User should be added to the application",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const userPage = new UserPage(adminPage.page);
        await usergroupPage.searchUsergroup("All Payees");
        await usergroupPage.verifyuserCount("6");
        await userPage.navigateToUser();
        await userPage.fillSearch("jennica Nyles");
        await userPage.waitForSearchResults();
        await userPage.updatePayeeRole("Payee", "Admin");
        await userPage.navigateToUsergroup();
        await usergroupPage.searchUsergroup("All Payees");
        await usergroupPage.verifyuserCount("5");
      }
    );

    test(
      "User status dropdown should be mandatory while creation and updating the user group",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17775, INTER-T17751, INTER-T17774",
          },
          {
            type: "Description",
            description:
              "User status dropdown should be mandatory while creation and updating the user group",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "User should be added to the application",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.searchUsergroup("Active group");
        await usergroupPage.editUsergroup();
        const isDisabledBefore =
          await usergroupPage.verifyUpdateButtonIsDisabled();
        console.log(isDisabledBefore);
        expect(isDisabledBefore).toBe(false);
        await usergroupPage.removeUserStatus("Active");
        const isDisabledAfter =
          await usergroupPage.verifyUpdateButtonIsDisabled();
        console.log(isDisabledAfter);
        expect(isDisabledAfter).toBe(true);
        const istooltipVisible = await usergroupPage.hoverDisabled();
        expect(istooltipVisible).toBe(true);
      }
    );

    test(
      "Inactive users should not be displayed in default user groups",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17747" },
          {
            type: "Description",
            description:
              "Inactive users should not be displayed in default user groups",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Exited User should not be displayed in the default user groups",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const userPage = new UserPage(adminPage.page);
        await userPage.navigateToUser();
        await userPage.fillSearch("sample");
        await userPage.waitForSearchResults();
        const userStatus = await userPage.fetchUserStatus();
        expect(userStatus[0]).toBe("Inactive");
        await userPage.navigateToUsergroup();
        await usergroupPage.searchUsergroup("All Admins");
        const isExitedUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isExitedUserInGroup).toBe(false);
      }
    );

    test(
      "Inactive users should be readded to the group once the status has been reverted",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17748, INTER-T17768" },
          {
            type: "Description",
            description:
              "Inactive users should be readded to the group once the status has been reverted",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Inactive users should be readded to the group once the status has been reverted",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const userPage = new UserPage(adminPage.page);
        await userPage.navigateToUser();
        await userPage.fillSearch("thomas");
        await userPage.waitForSearchResults();
        await userPage.clickRevertExit();
        await userPage.confirmExit();
        const userStatus1 = await userPage.fetchUserStatus();
        expect(userStatus1[0]).toBe("Added");
        await userPage.navigateToUsergroup();
        await usergroupPage.searchUsergroup("All Payees");
        const isExitedUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isExitedUserInGroup).toBe(true);
      }
    );

    test(
      "User should search and add users in user group",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17750, INTER-T17777" },
          {
            type: "Description",
            description: "User should search and add users in user group",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "Users should search and add users in user group",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.createNewGroup();
        await usergroupPage.nameNewGroup("Test User Group");
        await usergroupPage.userGroupStatus("Active");
        await usergroupPage.searchUser("Erin Rex");
        await usergroupPage.addUser();
        await usergroupPage.searchUsergroup("Test User Group");
        const isUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isUserInGroup).toBe(true);
      }
    );

    test(
      "User should not be displayed when no user status is selected.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17752" },
          {
            type: "Description",
            description:
              "User should not be displayed when no status is selected.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not be displayed when no status is selected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.createNewGroup();
        await usergroupPage.nameNewGroup("No status groups");
        await usergroupPage.userDropdown();
        const isUserVisible = await usergroupPage.noUSerInGroup();
        expect(isUserVisible).toBe(true);
      }
    );

    test(
      "User should be displayed with 'Active' and 'Yet to Join' statuses after cloning the default admin groups.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17753" },
          {
            type: "Description",
            description:
              "User should not be displayed with 'Active' and 'Yet to join' status",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not be displayed with 'Active' and 'Yet to join' status",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.cloneUserGroup(
          "group-All Admins",
          "Copy of All Admins"
        );
        await usergroupPage.searchUsergroup("Copy of All Admins");
        await usergroupPage.editUsergroup();
        const userStaus = await usergroupPage.fetchUserGroupStatus();
        console.log(userStaus);
        expect(userStaus[0]).toBe("Active");
        expect(userStaus[1]).toBe("Yet to join");
      }
    );

    test(
      "User should be displayed with 'Active' and 'Yet to Join' statuses after cloning the default payee groups.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17753" },
          {
            type: "Description",
            description:
              "User should not be displayed with 'Active' and 'Yet to join' status",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not be displayed with 'Active' and 'Yet to join' status",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.cloneUserGroup(
          "group-All Payees",
          "Copy of All Payees"
        );
        await usergroupPage.searchUsergroup("Copy of All Payees");
        await usergroupPage.editUsergroup();
        const userStaus = await usergroupPage.fetchUserGroupStatus();
        console.log(userStaus);
        expect(userStaus[0]).toBe("Active");
        expect(userStaus[1]).toBe("Yet to join");
      }
    );

    test(
      "User should be see the expected user once user selected the 'yet to join' status ",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17779, INTER-T17754" },
          {
            type: "Description",
            description:
              "User should be see the expected user once user selected the 'yet to join' status ",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should be see the expected user once user selected the 'yet to join' status ",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.createNewGroup();
        await usergroupPage.nameNewGroup("Sample group");
        await usergroupPage.userGroupStatus("Yet to join");
        await usergroupPage.searchUser("jennifer j");
        await usergroupPage.addUser();
        await usergroupPage.searchUsergroup("Sample group");
        const isUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isUserInGroup).toBe(true);
      }
    );

    test(
      "User should be see the expected user once user selected the 'Exited' status ",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17778,INTER-T17761" },
          {
            type: "Description",
            description:
              "User should be see the expected user once user selected the 'Exited' status ",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should be see the expected user once user selected the 'Exited' status ",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.createNewGroup();
        await usergroupPage.nameNewGroup("exit1");
        await usergroupPage.userGroupStatus("Exited");
        await usergroupPage.searchUser("sample s");
        await usergroupPage.addUser();
        await usergroupPage.searchUsergroup("exit1");
        const isUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isUserInGroup).toBe(true);
      }
    );

    test(
      "User should not see the exited user while sharing the commission plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17755" },
          {
            type: "Description",
            description:
              "User should not see the exited user while sharing the commission plan",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not see the exited user while sharing the commission plan",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const commissionPlan = new CommissionRBAC(adminPage.page);
        await usergroupPage.searchUsergroup("exit group");
        const isUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isUserInGroup).toBe(true);
        await commissionPlan.navigate(true, "/plans");
        await commissionPlan.openShareModal("grp-monthly plan");
        await commissionPlan.selectGroupForViewAccess("exit group");
        const isExitUser = await usergroupPage.verifyExitUserInPlan(
          "exit group",
          "Not assigned to anyone"
        );
        expect(isExitUser).toBe(true);
      }
    );

    test(
      "User should not see the exited user creation of queries ",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17756" },
          {
            type: "Description",
            description:
              "User should not see the exited user creation of queries ",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not see the exited user creation of queries ",
          },
        ],
      },
      async ({ adminPage }) => {
        const QueriesLI = new QueriesLineItem(adminPage.page);
        await QueriesLI.navigate("/queries/allTickets");
        await QueriesLI.clickRaiseQueryBtnModule();
        await QueriesLI.ccClick();
        const isExitUserVisible = await QueriesLI.fetchUserInCC();
        console.log(isExitUserVisible);
        expect(isExitUserVisible).not.toContain("sample s");
      }
    );

    test(
      "User should see the active user creation of queries ",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17757" },
          {
            type: "Description",
            description:
              "User should  see the active user creation of queries ",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should  see the active user creation of queries ",
          },
        ],
      },
      async ({ adminPage }) => {
        const QueriesLI = new QueriesLineItem(adminPage.page);
        await QueriesLI.navigate("/queries/allTickets");
        await QueriesLI.clickRaiseQueryBtnModule();
        await QueriesLI.ccClick();
        const isExitUserVisible = await QueriesLI.fetchUserInCC();
        console.log(isExitUserVisible);
        expect(isExitUserVisible).toContain("Benetta Blewett");
      }
    );

    test(
      "User should not see the exited user while sharing the forecast plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17758" },
          {
            type: "Description",
            description:
              "User should not see the exited user while sharing the forecast plan",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not see the exited user while sharing the forecast plan",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const commissionPlan = new CommissionRBAC(adminPage.page);
        await usergroupPage.searchUsergroup("exit group");
        const isUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isUserInGroup).toBe(true);
        await commissionPlan.navigate(true, "/forecasts");
        await commissionPlan.openShareModal("grp-monthly plan_Copy");
        await commissionPlan.selectGroupForViewAccess("exit group");
        const isExitUser = await usergroupPage.verifyExitUserInPlan(
          "exit group",
          "Not assigned to anyone"
        );
        expect(isExitUser).toBe(true);
      }
    );

    test(
      "User should not able to see the exit user in approvals group dropdown",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17760" },
          {
            type: "Description",
            description:
              "User should not able to see the exit user in approvals group dropdown",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not able to see the exit user in approvals group dropdown",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.navigateToApprovals();
        await usergroupPage.createWorkflow();
        await usergroupPage.selectapproversWorkflow();
        const user_count = await usergroupPage.verifyExitUser("exit group");
        expect(user_count).toBe("0 users");
        await usergroupPage.notifiedMember();
        const notified_user_count = await usergroupPage.verifyExitUser(
          "exit group"
        );
        expect(notified_user_count).toBe("0 users");
      }
    );

    test(
      "User should not able to see the exit user manage permission for custom objects",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17763" },
          {
            type: "Description",
            description:
              "User should not able to see the exit user manage permission for custom objects",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should not able to see the exit user manage permission for custom objects",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.navigateToConnectors();
        await usergroupPage.searchObject("sales");
        await usergroupPage.selectCustomObj("sales");
        await usergroupPage.isExitUserInManagePermission(
          "Include only specific users"
        );
        const notified_user_count =
          await usergroupPage.verifyExitUserInManagePermissions("exit group");
        expect(notified_user_count).toBe("Not assigned to anyone");
      }
    );

    test(
      "User should able to see the staleness pop up when the aded user status has been changed",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17762, INTER-T17772" },
          {
            type: "Description",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.searchUsergroup("Yet to join stale");
        await usergroupPage.editUsergroup();
        const isPopupVisible = await usergroupPage.userStatusPopUp();
        expect(isPopupVisible).toBe(true);
      }
    );

    test(
      "User should able to see the staleness pop up when the aded user status has been changed for Active user",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17771, INTER-T17766" },
          {
            type: "Description",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed for Active user",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed for Active user",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.searchUsergroup("Active stale group");
        await usergroupPage.editUsergroup();
        const isPopupVisible = await usergroupPage.userStatusPopUp();
        expect(isPopupVisible).toBe(true);
      }
    );

    test(
      "User should able to see the staleness pop up when the aded user status has been changed for Exited user",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17769, INTER-T17770, INTER-T17767",
          },
          {
            type: "Description",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed for Exited user",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the staleness pop up when the aded user status has been changed for Active user",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.searchUsergroup("Active stale group");
        await usergroupPage.editUsergroup();
        const isPopupVisible = await usergroupPage.userStatusPopUp();
        expect(isPopupVisible).toBe(true);
      }
    );

    test(
      "User should able to see the conditions once the exit user has been selected",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17764" },
          {
            type: "Description",
            description:
              "User should able to see the conditions once the exit user has been selected",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the conditions once the exit user has been selected",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        await usergroupPage.searchUsergroup("rule");
        await usergroupPage.editUsergroup();
        const exit_condition = await usergroupPage.checkForExitCondition();
        expect(exit_condition[0]).toBe("After");
        expect(exit_condition[1]).toBe("Before");
        expect(exit_condition[2]).toBe("In current quarter");
        expect(exit_condition[3]).toBe("In current fiscal year");
      }
    );

    test(
      "User should able to see thee conditions once the exit user has been selected",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17764" },
          {
            type: "Description",
            description:
              "User should able to see the conditions once the exit user has been selected",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the conditions once the exit user has been selected",
          },
        ],
      },
      async ({ adminPage }) => {
        const usergroupPage = new UserGroupPage(adminPage.page);
        const userPage = new UserPage(adminPage.page);
        await usergroupPage.createNewGroup();
        await usergroupPage.nameNewGroup("sales-final");
        await usergroupPage.userGroupStatus("Active");
        await usergroupPage.addRule();
        await usergroupPage.switchIncludePayeesInPlan();
        await usergroupPage.addUser();
        await userPage.navigateToUsergroup();
        await usergroupPage.searchUsergroup("sales-final");
        const isExitedUserInGroup = await usergroupPage.isUserInGroup(
          "<EMAIL>"
        );
        expect(isExitedUserInGroup).toBe(false);
      }
    );

    test(
      "User should able to the exited user payout screen if the exited user added in the group",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17765" },
          {
            type: "Description",
            description:
              "User should able to the exited user payout screen if the exited user added in the group",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to the exited user payout screen if the exited user added in the group",
          },
        ],
      },
      async ({ adminPage }) => {
        const key = "commission-view-period";
        const value = "31-August-2024";

        await adminPage.page.evaluate(
          ({ key, value }) => {
            // Set the localStorage value for the current page
            localStorage.setItem(key, value);
          },
          { key, value }
        );
        const statementPage = new StatementPage(adminPage.page);
        const payoutPage = new PayoutPage(adminPage.page);
        const userPage = new UserPage(adminPage.page);
        await userPage.navigateToUser();
        await userPage.fillSearch("kumar k");
        await userPage.loginasPayee("<EMAIL>");
        await userPage.validateImpersonation("kumar k (<EMAIL>)");
        await payoutPage.navigateToPayouts("/commissions");
        await payoutPage.navigateToPayee("Duffie Wroughton");
        await statementPage.navigateToCriteriafromPayout(
          "grp-monthly plan",
          "grp-simple"
        );
        await payoutPage.navigateToPayouts("/commissions");
        await userPage.validateexitImpersonation();
      }
    );
  }
);
