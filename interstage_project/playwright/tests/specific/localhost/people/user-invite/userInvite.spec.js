/* eslint-disable camelcase */
import UserPage from "../../../../../test-objects/user-objects";
import commonPage from "../../../../../test-objects/common-utils-objects";

const { test, expect } = require("@playwright/test");
const multiuser1 = "<EMAIL>";
const multiuser1_pwd = "97W!Cu-K/";
const multiuser2 = "<EMAIL>";
const multiuser2_pwd = "xU_/3?vB}";
const inviteUser = "<EMAIL>";
const inviteUserpwd = "!,i%6^F;y";

test.describe(
  "MultiUser Invites",
  { tag: ["@user", "@regression", "@adminchamp-2"] },
  () => {
    test(
      "Validate page elements and click button",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-9931, INTER-T9940, INTER-T9941",
          },
          {
            type: "Description",
            description:
              "Validate if an client has multiple accounts-user should not be allowed to login in new account until he is INVITED.Validate that an image is displayed when the user is not in the 'INVITED' state, and verify the presence of specific messages related to account readiness.",
          },
          {
            type: "Precondition",
            description: "User is not in the 'INVITED' state.",
          },
          {
            type: "Expected Behaviour",
            description:
              "An image should be displayed with the text 'Stay tuned for the magic.' Additionally, the message 'We're delighted by your eagerness to explore Everstage, but we're not quite ready to raise the curtains yet. Your admin will send an invite when your account is ready.' should be visible.",
          },
        ],
      },
      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login("<EMAIL>", "1VnM/QtcW");
        const header = page.getByText("Stay tuned for the magic!");
        await expect(header).toBeVisible();
        const description = page.getByText(
          "We're delighted by your eagerness to explore Everstage, but we're not quite ready to raise the curtains yet."
        );
        const description2 = page.getByText(
          "Your admin will invite you once your account is fully set up."
        );
        await expect(description).toBeVisible();
        await expect(description2).toBeVisible();
        const image = page.getByAltText("Not Invited");
        await expect(image).toBeVisible();
        const goBackButton = page.getByText("Go back");
        await expect(goBackButton).toBeVisible();
        await goBackButton.click();
        await page.waitForTimeout(5000);
      }
    );

    test(
      "Validate user is present in 2 accounts(Invited,Active) then Multi client login should be seen.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9950, INTER-T9946" },
          {
            type: "Description",
            description:
              "Validate if a user is present in 2 accounts - one 'INVITED' and the other 'ACTIVE', the multi-client login screen should be displayed. Also, validate that the Preferences screen displays the account as per the user's selection.",
          },
          {
            type: "Precondition",
            description:
              "User is part of two accounts with one 'INVITED' and the other 'ACTIVE' state.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The multi-client login screen should be displayed for the user. On the Preferences screen, the account should be shown according to the user's selection.",
          },
        ],
      },

      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login("<EMAIL>", "3)IoNr(GP");
        const title = page.getByText("Choose account");
        await expect(title).toBeVisible();
        const logOutButton = page.getByText(
          "Log out and use a different login"
        );
        await expect(logOutButton).toBeVisible();
        await logOutButton.click();
        await page.waitForTimeout(5000);
      }
    );

    test(
      "Validate multi-client login and account switch functionality",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9936" },
          {
            type: "Description",
            description:
              "Validate that if a user is present in two accounts and both are in the 'INVITED' state, the multi-client login screen should be displayed, and the 'Switch Account' button should be visible after logging in.",
          },
          {
            type: "Precondition",
            description:
              "User is present in two accounts with both in the 'INVITED' state.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The user should see the multi-client login screen upon logging in and should have access to the 'Switch Account' button to switch between accounts.",
          },
        ],
      },

      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login("<EMAIL>", "3)IoNr(GP");
        const title = page.getByText("Choose account");
        await expect(title).toBeVisible();
        const multiuser2 = page.locator(
          "div.flex.items-center >> text=multiuserinvite2"
        );
        await expect(multiuser2).toBeVisible();
        await multiuser2.click();
        const continueButton = page.locator('button:has-text("Continue")');
        await expect(continueButton).toBeVisible();
        await continueButton.click();
        const profileImage = page.locator("//div[@type='button']//div/img");
        await expect(profileImage).toBeVisible();
        await profileImage.click();
        const accountSwitchList = page.locator('div[role="dialog"]');
        await expect(accountSwitchList).toBeVisible();
        const account1 = accountSwitchList.getByText("multiuserinvite3");
        const account2 = accountSwitchList.getByText("multiuserinvite2");
        await expect(account1).toBeVisible();
        await expect(account2).toBeVisible();
      }
    );

    test(
      "Validate inactive user cant login",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9937" },
          {
            type: "Description",
            description:
              "Validate MULTI CLIENT- if a user is marked as 'INACTIVE' in one client and tries to login, 'You are not authorised to access this page' message should be seen.",
          },
          {
            type: "Precondition",
            description: "User is marked as INACTIVE in one client",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should see a message saying 'You are not authorised to access this page' when attempting to log into the INACTIVE client.",
          },
        ],
      },
      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login("<EMAIL>", "M0I&5ZrrJ");
        const header = page.getByText("Error 403");
        await expect(header).toBeVisible();
        const description = page.getByText(
          "Sorry, you are not authorized to access this page."
        );
        await expect(description).toBeVisible();
        const image = page.getByAltText("Not Authorized");
        await expect(image).toBeVisible();
        const goBackButton = page.getByText(
          "Log out and try with a different login"
        );
        await expect(goBackButton).toBeVisible();
        await goBackButton.click();
      }
    );

    test(
      "Validate Power Admin role is active",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9948" },
          {
            type: "Description",
            description:
              "Validate that on creating a new client, the 'everstage user' status is set to 'ACTIVE' and other users' statuses are set to 'ADDED'.",
          },
          {
            type: "Precondition",
            description: "A new client is created in the system.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The status of the 'everstage user' should be 'ACTIVE', and the statuses of other users should be 'ADDED' upon client creation.",
          },
        ],
      },

      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login(multiuser2, multiuser2_pwd);
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.fillSearch("<EMAIL>");
        await userPage.waitForSearchResults();
        const statusClientA = await userPage.fetchUserStatus();
        expect(statusClientA[0]).toBe("Active");
      }
    );

    test(
      "User status should not be shared across multiple clients",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9938, INTER-T9939" },
          {
            type: "Description",
            description:
              "Validate that the user's status is not shared across multiple clients and that the status reflects correctly as per the logged-in active account in a multi-account setup.",
          },
          {
            type: "Precondition",
            description: "User must have access to multiple clients.",
          },
          {
            type: "Expected Behaviour",
            description:
              "The user's status should remain isolated across clients, and the logged-in active account should display the correct status.",
          },
        ],
      },

      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login(multiuser2, multiuser2_pwd);
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.fillSearch("<EMAIL>");
        await userPage.waitForSearchResults();
        const statusClientA = await userPage.fetchUserStatus();
        expect(statusClientA[0]).toBe("Added");
        await userPage.sendInvite("<EMAIL>");
        await userPage.fillSearch("<EMAIL>");
        await userPage.waitForSearchResults();
        const userA = await userPage.fetchUserStatus();
        expect(userA[0]).toBe("Invited");
      }
    );

    test(
      "Invite user - Check if active status of invited user is able to login",
      { tag: ["@user", "@regression", "@adminchamp-2"] },
      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login(inviteUser, inviteUserpwd);
        await page.locator(".ant-alert-content").waitFor({ state: "visible" });
      }
    );

    test.describe("Invite cases - Multi user 2", () => {
      test(
        "Exit User Invite button validation",
        {
          annotation: [
            { type: "Test ID", description: "INTER-T9924" },
            {
              type: "Description",
              description:
                "Validate user should not be able to send invite to exited users",
            },
            { type: "Precondition", description: "User with Exited Status" },
            {
              type: "Expected Behaviour",
              description: "Invite cant be sent to inactive users",
            },
          ],
        },
        async ({ page }) => {
          const commonUtils = new commonPage(page);
          const user_actions = new UserPage(page);
          await commonUtils.login(multiuser2, multiuser2_pwd);
          const email = "<EMAIL>";
          await user_actions.navigateToUser();
          await user_actions.fillSearch(email);
          const tripleDotsButton = page.locator(
            `button[data-testid='${email} users dd button']`
          );
          await tripleDotsButton.waitFor({ state: "visible" });
          await tripleDotsButton.click();
          const actionButton = page.getByRole("menuitem", {
            name: "Send Invite",
          });
          expect(await actionButton.isVisible()).toBeFalsy();
        }
      );

      test(
        "User Added Status Verification",
        {
          annotation: [
            { type: "Test ID", description: "INTER-T9925" },
            {
              type: "Description",
              description:
                "Validate on adding new users status should be Added",
            },
            { type: "Precondition", description: "Users" },
            {
              type: "Expected Behaviour",
              description:
                "User should see Added under status after being added",
            },
          ],
        },
        async ({ page }) => {
          const commonUtils = new commonPage(page);
          const user_actions = new UserPage(page);
          await commonUtils.login(multiuser2, multiuser2_pwd);
          await user_actions.navigateToUser();
          await user_actions.navigateToNewUserForm();
          await user_actions.fillEmail("<EMAIL>");
          await user_actions.fillFirstName("Test");
          await user_actions.fillLastName("Test");
          await user_actions.selectRole("Payee");
          await user_actions.submitNewUser();
          await user_actions.verifySuccessMessage();

          await user_actions.fillSearch("<EMAIL>");
          const stats = await user_actions.fetchUserStatus();
          expect(stats[0]).toBe("Added");
          await user_actions.deleteUser("<EMAIL>");
        }
      );

      test(
        "User Active Status Verification",
        {
          annotation: [
            {
              type: "Test ID",
              description:
                "INTER-T9920 , INTER-T9922 , INTER-T9923 , INTER-T9927 , INTER-T9928",
            },
            {
              type: "Description",
              description:
                "Validate user is able to login only if the status is INVITED.Validate once the user logins status should change to ACTIVE",
            },
            { type: "Precondition", description: "Users" },
            {
              type: "Expected Behaviour",
              description:
                "User can only login if INVITED.Once invited and logged in status changes to ACTIVE",
            },
          ],
        },
        async ({ page }) => {
          const commonUtils = new commonPage(page);
          const user_actions = new UserPage(page);
          await commonUtils.login(multiuser2, multiuser2_pwd);
          await user_actions.navigateToUser();
          await user_actions.fillSearch("<EMAIL>");
          const stats = await user_actions.fetchUserStatus();
          expect(stats[0]).toBe("Active");
        }
      );
    });
  }
);

test.describe(
  "Invite cases - Multi user 1",
  { tag: ["@user", "@regression", "@adminchamp-2"] },
  () => {
    test(
      "Reset Link Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9932" },
          {
            type: "Description",
            description:
              "Validate Resend Invite should not be sent to user if he is not in INVITED / ACTIVE state.",
          },
          { type: "Precondition", description: "Users with added status" },
          {
            type: "Expected Behaviour",
            description:
              "Validate Resend Invite Visibility for user in added status",
          },
        ],
      },
      async ({ page }) => {
        const commonUtils = new commonPage(page);
        await commonUtils.login(multiuser1, multiuser1_pwd);
        const user_actions = new UserPage(page);
        const email = "<EMAIL>";
        await user_actions.navigateToUser();
        await user_actions.fillSearch(email);
        const tripleDotsButton = page.locator(
          `button[data-testid='${email} users dd button']`
        );
        await tripleDotsButton.waitFor({ state: "visible" });
        await tripleDotsButton.click();
        const actionButton = page.getByRole("menuitem", {
          name: "Resend Invite",
        });
        expect(await actionButton.isVisible()).toBeFalsy();
      }
    );

    test(
      "Impersonation Status Change",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T9930" },
          {
            type: "Description",
            description:
              "Validate on impersonating a user in ADDED state should not change their status to ACTIVE.",
          },
          { type: "Precondition", description: "Users with added status" },
          {
            type: "Expected Behaviour",
            description: "Impersonating user should not change status",
          },
        ],
      },
      async ({ page }) => {
        const commonUtils = new commonPage(page);
        const user_actions = new UserPage(page);
        await commonUtils.login(multiuser1, multiuser1_pwd);
        await user_actions.navigateToUser();
        await user_actions.fillSearch("<EMAIL>");
        await user_actions.loginasPayee("<EMAIL>");
        await user_actions.validateImpersonation("<EMAIL>");
        await user_actions.validateexitImpersonation();
        await user_actions.fillSearch("<EMAIL>");
        const stats = await user_actions.fetchUserStatus();
        expect(stats[0]).toBe("Added");
      }
    );
  }
);
