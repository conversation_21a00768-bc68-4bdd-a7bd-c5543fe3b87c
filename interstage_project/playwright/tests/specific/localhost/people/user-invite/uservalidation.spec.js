/* eslint-disable no-unused-vars */
import UserPage from "../../../../../test-objects/user-objects";
import GlobalSearch from "../../../../../test-objects/globalSearch-objects";

const {
  userInviteMultiUser1Fixtures: { test: test1, expect: expect1 },
  invitedUserFixtures: { test: test3, expect: expect3 },
  validateUser1Fixtures: { test: test4, expect: expect4 },
  validateUser2Fixtures: { test: test5, expect: expect5 },
} = require("../../../../fixtures");


test1(
  "User status should not be shared across multiple clients",
  {
    tag: ["@user", "@regression", "@adminchamp-2"],
    annotation: [
      { type: "Test ID", description: "INTER-T9938, INTER-T9939" },
      {
        type: "Description",
        description:
          "Validate that the user's status is not shared across multiple clients and that the status reflects correctly as per the logged-in active account in a multi-account setup.",
      },
      {
        type: "Precondition",
        description: "User must have access to multiple clients.",
      },
      {
        type: "Expected Behaviour",
        description:
          "The user's status should remain isolated across clients, and the logged-in active account should display the correct status.",
      },
    ],
  },

  async ({ adminPage }) => {
    const page = adminPage.page;
    const userPage = new UserPage(page);
    await userPage.navigateToUser();
    await userPage.fillSearch("<EMAIL>");
    await userPage.waitForSearchResults();
    const statusClientB = await userPage.fetchUserStatus();
    expect1(statusClientB[0]).toBe("Added");
    await userPage.fillSearch("<EMAIL>");
    await userPage.waitForSearchResults();
    const userB = await userPage.fetchUserStatus();
    expect1(userB[0]).toBe("Added");
  }
);

test3(
  "Validate user logs into INVITED account and Switch Account button is not visible",
  {
    tag: ["@user", "@regression", "@adminchamp-2"],
    annotation: [
      { type: "Test ID", description: "INTER-T9934, INTER-T9935" },
      {
        type: "Description",
        description:
          "Validate if a user is present in two accounts - one in the 'INVITED' state and the other in the 'ADDED' state, the user should be directly logged into the 'INVITED' account. Additionally, the user should not see the multi-client login screen or the 'Switch Account' button post-login.",
      },
      {
        type: "Precondition",
        description:
          "User is present in two accounts, one 'INVITED' and the other 'ADDED'.",
      },
      {
        type: "Expected Behaviour",
        description:
          "The user should be automatically logged into the 'INVITED' account. The multi-client login screen and the 'Switch Account' button should not be visible.",
      },
    ],
  },
  async ({ adminPage }) => {
    const page = adminPage.page;
    const image = page.locator(
      'img[src="https://d177w6gi8jmdvz.cloudfront.net/Logos/multiuserinvite3"]'
    );
    await expect3(image).toBeVisible();
    await image.hover();
    const tooltip = page.locator('div.ant-tooltip-inner[role="tooltip"]');
    await expect3(tooltip).toBeVisible();
    await expect3(tooltip).toHaveText("multiuserinvite3");
  }
);

test4(
  "Validate default Login",
  {
    tag: ["@user", "@regression", "@adminchamp-2"],
    annotation: [
      { type: "Test ID", description: "INTER-T9942" },
      {
        type: "Description",
        description:
          "Validate that when a user is INVITED in both accounts and one account is set as the default, the user is directly logged into the default account.",
      },
      {
        type: "Precondition",
        description:
          "User is INVITED in both accounts, and one account is set as default.",
      },
      {
        type: "Expected Behaviour",
        description:
          "The user should be directly logged into the default account without being prompted for account selection.",
      },
    ],
  },
  async ({ adminPage }) => {
    const page = adminPage.page;
    const globalSearch = new GlobalSearch(page);
    await globalSearch.navigate("/dashboard");
    const metricsElement = page.locator("span.ant-select-selection-item", {
      hasText: "multiuserinvite2 Metrics",
    });
    await expect4(metricsElement).toBeVisible();
    await expect4(metricsElement).toHaveText("multiuserinvite2 Metrics");
  }
);

test5(
  "Validate single-client user cannot access multi-client selector",
  {
    tag: ["@user", "@regression", "@adminchamp-2"],
    annotation: [
      { type: "Test ID", description: "INTER-T9945" },
      {
        type: "Description",
        description:
          "Validate that if a user is present in multiple clients, the Preferences screen does not display accounts where the user is in the 'ADDED' state.",
      },
      {
        type: "Precondition",
        description:
          "User is associated with multiple clients with different account states.",
      },
      {
        type: "Expected Behaviour",
        description:
          "The Preferences screen should exclude accounts where the user is in the 'ADDED' state and only display relevant accounts.",
      },
    ],
  },
  async ({ adminPage }) => {
    const page = adminPage.page;
    const image = page.locator(
      'img[src="https://d177w6gi8jmdvz.cloudfront.net/Logos/multiuserinvite3"]'
    );
    await expect5(image).toBeVisible();
    await image.hover();
    const tooltip = page.locator('div.ant-tooltip-inner[role="tooltip"]');
    await expect5(tooltip).toBeVisible();
    await expect5(tooltip).toHaveText("multiuserinvite3");
  }
);
