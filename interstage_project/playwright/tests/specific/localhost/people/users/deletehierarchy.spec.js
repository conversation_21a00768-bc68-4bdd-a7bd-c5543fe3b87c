import UserPage from "../../../../../test-objects/user-objects.js";
import databookPage from "../../../../../test-objects/databook-objects.js";
import datasheetPage from "../../../../../test-objects/datasheet-objects.js";
import DatasheetV2Page from "../../../../../test-objects/datasheet-v2-objects.js";
import quotaPage from "../../../../../test-objects/quota-objects.js";
import planPage from "../../../../../test-objects/plan-objects.js";
import CommissionSync from "../../../../../test-objects/commissionSync-objects";
import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommonUtils from "../../../../../test-objects/common-utils-objects.js";
import PayoutFilterPageObjs from "../../../../../test-objects/payfilterobj.js";
import PayoutPage from "../../../../../test-objects/payout-objects.js";
import UserGroupPage from "../../../../../test-objects/usergroup-objects.js";
import ApprovalPage from "../../../../../test-objects/approval-objects.js";
import { setupGraphQLRouteInterceptor } from "../../../../bearerToken";
import Revertexitpage from "../../../../../test-objects/revertexit-objects.js";
let token = "";
const {
  deletePayrollFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const key = "commission-view-period";
  const value = "May-2024";
  await page.evaluate(
    ({ key, value }) => {
      // Set the localStorage value for the current page
      localStorage.setItem(key, value);
    },
    { key, value }
  );
});

test.describe(
  "Delete user in user's module",
  { tag: ["@user", "@regression", "@adminchamp-1"] },
  () => {
    test("The user is not able to delete the Hierarchy record, if there is only one entry", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19162",
        },
        {
          type: "Description",
          description:
            "The user is not able to delete the Hierarchy record, if there is only one entry",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to delete the Hierarchy record, if there is only one entry",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Alameda Stollman Feb 05");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
    });

    test("The user is able to delete the Hierarchy record, if there is only one entry", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19167, INTER-T19168, INTER-T19169",
        },
        {
          type: "Description",
          description:
            "The user is not able to delete the Hierarchy record, if there is only one entry",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to delete the Hierarchy record, if there is only one entry",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const quotapage = new quotaPage(page);
      await admin.navigateToTeams();
      const isReporteeVisible = await admin.verifyHierarchy(
        "AD Alissa Dungay",
        "<EMAIL>"
      );
      expect(isReporteeVisible).toBe(true);
      if (isReporteeVisible) {
        console.log("Reportee visible");
      } else {
        console.log("Reportee not visible");
      }
      await quotapage.navigate("/quotas");
      const result = await quotapage.verifyQuotaHierarchy(
        "alissa_dungay",
        "Dmitri Starie"
      );
      expect(result).toBe(true);
      await quotapage.navigate("/draws");
      await page.locator('a[href="/draws"]').waitFor({ state: "visible" });
      const result2 = await quotapage.verifyQuotaHierarchy(
        "alissa_dungay",
        "Dmitri Starie"
      );
      expect(result2).toBe(true);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Alissa Dungay Jan 05");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      const isDateVisible = await admin.datePopup("January 05, 2024");
      if (isDateVisible) {
        console.log("The date 'January 05, 2024' is visible.");
      } else {
        console.log("The date 'January 05, 2024' is NOT visible.");
      }
      const isCancelButtonVisible = await admin.cancelCTA();
      if (isCancelButtonVisible) {
        console.log("Cancel button is visible");
      } else {
        console.log("Cancel button is not visible");
      }
      await admin.deletedialog();
      const isAlertDisplayed = await admin.hierarchyDeleteAlert();
      if (isAlertDisplayed) {
        console.log("Hierarchy alert is displayed");
      } else {
        console.log("Hierarchy alert is not displayed");
      }
      await admin.navigateToTeams();
      const isReporteeVisible2 = await admin.verifyHierarchy(
        "AD Alissa Dungay",
        "<EMAIL>"
      );
      expect(isReporteeVisible2).toBe(false);
      await quotapage.navigate("/quotas");
      const result1 = await quotapage.verifyQuotaHierarchy(
        "alissa_dungay",
        "Dmitri Starie"
      );
      expect(result1).toBe(false);
      await quotapage.navigate("/draws");
      await page.locator('a[href="/draws"]').waitFor({ state: "visible" });
      const result3 = await quotapage.verifyQuotaHierarchy(
        "alissa_dungay",
        "Dmitri Starie"
      );
      expect(result3).toBe(false);
    });

    test("The user is not able to delete the Hierarchy record, if there are multiple entry", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19170",
        },
        {
          type: "Description",
          description:
            "The user is not able to delete the Hierarchy record, if there are multiple entry",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to delete the Hierarchy record, if there are multiple entry",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Alameda Stollman Jan 25");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
    });

    test("The user is not able to see the deleted record in the user report", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "",
        },
        {
          type: "Description",
          description:
            "The user is not able to see the deleted hierarchy user record",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to see the deleted hierarchy user record",
        }
      );

      const page = adminPage.page;
      const datapage = new datasheetPage(page);
      const dsV2page = new DatasheetV2Page(page);
      const payoutpage = new PayoutPage(page);
      const databookpage = new databookPage(page);

      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("hierarchy sheet");
      await dsV2page.letColumnsLoad("Employee Id");
      const value = await datapage.getRowValues();
      console.log("Manager_name:", value);
      expect(value).toContain("Alissa Dungay");
      await datapage.updateDatasheet();
      await datapage.generateDatasheet();
      await payoutpage.noRowsToShow();
    });

    test("Line items should not roll up for the deleted manager in team criteria ", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T19172, INTER-T19173, INTER-T19174, INTER-T19175, INTER-T19182",
        },
        {
          type: "Description",
          description:
            "The user is not able to see the commission roll up for the deleted manager",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to see the deleted manager line item records",
        }
      );
      testInfo.setTimeout(testInfo.timeout + 500000);

      const page = adminPage.page;
      const planpage = new planPage(page);
      const commonpage = new CommonUtils(page);
      const dsV2page = new DatasheetV2Page(page);
      const commSyncPage = new CommissionSync(page);
      const payoutfilter = new PayoutFilterPageObjs(page);
      const payoutpage = new PayoutPage(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);
      const datapage = new datasheetPage(page);
      const admin = new UserPage(page);
      const databookpage = new databookPage(page);
      const quotapage = new quotaPage(page);

      await planpage.navigatetoPlan();
      await planpage.searchPlan("team plan");
      const isSecondPlanVisible = await planpage.selectPlan("team plan");
      expect(isSecondPlanVisible).toEqual(true);
      await planpage.selectCriteria("Simple");
      await planpage.simulateCriteria();
      await planpage.simulatePeriod("Dec", "Jan", "-01-01");
      await planpage.runSimulation();
      const amount_visible = await planpage.verifyCommissionNumber(
        "$512,197.27"
      );
      expect(amount_visible).toEqual(true);
      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("Commission");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 11of11rows");
      await datapage.selectDatasheet("inter-commission");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 11of11rows");
      await quotapage.navigate("/commissions");
      await payoutfilter.searchName("juliann");
      await payoutpage.navigateToPayee("Juliann Adamides");
      const isManagerVisible = await payoutpage.viewProfile("Jaimie Muslim");
      expect(isManagerVisible).toEqual(true);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Jaimie Muslim Mar 15");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      const isDateVisible = await admin.datePopup("January 05, 2024");
      if (isDateVisible) {
        console.log("The date 'March 15, 2024' is visible.");
      } else {
        console.log("The date 'March 15, 2024' is NOT visible.");
      }
      const isCancelButtonVisible = await admin.cancelCTA();
      if (isCancelButtonVisible) {
        console.log("Cancel button is visible");
      } else {
        console.log("Cancel button is not visible");
      }
      await admin.deletedialog();
      const isAlertDisplayed = await admin.hierarchyDeleteAlert();
      if (isAlertDisplayed) {
        console.log("Hierarchy alert is displayed");
      } else {
        console.log("Hierarchy alert is not displayed");
      }
      await quotapage.navigate("/settings/commissions-and-data-sync");
      await commSyncPage.selectCriteria("selected-payees");
      await commSyncPage.selectDropdown(["Jaimie Muslim"]);
      await commonpage.setSelectMonthComboBox("2024-05");
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForCalculationMessage();
      await csPrevPage.waitForCommissionsSuccess();
      await planpage.navigatetoPlan();
      await planpage.searchPlan("team plan");
      const isSecondPlanVisible1 = await planpage.selectPlan("team plan");
      expect(isSecondPlanVisible1).toEqual(true);
      await planpage.selectCriteria("Simple");
      await planpage.simulateCriteria();
      await planpage.simulatePeriod("Dec", "Jan", "-01-01");
      await planpage.runSimulation();
      const amount_visible1 = await planpage.verifyCommissionNumber(
        "$244,845.69"
      );
      expect(amount_visible1).toEqual(true);
      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("Commission");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 5of5rows");
      await datapage.selectDatasheet("inter-commission");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 5of5rows");
    });

    test("Line items should not roll up for the deleted manager for forecast plan ", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19176",
        },
        {
          type: "Description",
          description:
            "The user is not able to see the commission roll up for the deleted manager",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to see the deleted manager line item records",
        }
      );
      testInfo.setTimeout(testInfo.timeout + 500000);
      const page = adminPage.page;
      const planpage = new planPage(page);
      const commonpage = new CommonUtils(page);
      const commSyncPage = new CommissionSync(page);
      const csPrevPage = new CommissionsSyncPrevPeriod(page);
      const quotapage = new quotaPage(page);
      const dsV2page = new DatasheetV2Page(page);
      const datapage = new datasheetPage(page);
      const databookpage = new databookPage(page);
      const payoutfilter = new PayoutFilterPageObjs(page);
      const payoutpage = new PayoutPage(page);

      const commonutils = new CommonUtils(page);
      await quotapage.navigate("/forecasts");
      await planpage.searchPlan("team plan_Copy");
      const isSecondPlanVisible = await planpage.selectPlan("team plan_Copy");
      expect(isSecondPlanVisible).toEqual(true);
      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("forecast");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 11of11rows");
      await quotapage.navigate("/commissions");
      await payoutfilter.searchName("juliann");
      await payoutpage.navigateToPayee("Juliann Adamides");
      const isManagerVisible = await payoutpage.viewProfile("Jaimie Muslim");
      expect(isManagerVisible).toEqual(false);
      await quotapage.navigate("/settings/commissions-and-data-sync");
      await commonutils.expandMenu("Commission Forecasting", 7);
      await commSyncPage.selectCriteria("selected-payees");
      await commSyncPage.selectDropdown(["Jaimie Muslim"]);
      await commonpage.setSelectMonthComboBox("2024-05");
      await csPrevPage.runCommissions();
      await csPrevPage.clickSkipandRun();
      await csPrevPage.waitForForecastCalculationMessage();
      await csPrevPage.waitForForecastSuccess();
      await quotapage.navigate("/forecasts");
      await planpage.searchPlan("team plan_Copy");
      const isSecondPlanVisible1 = await planpage.selectPlan("team plan_Copy");
      expect(isSecondPlanVisible1).toEqual(true);
      await planpage.selectCriteria("Simple");
      await planpage.simulateCriteria();
      await planpage.simulatePeriod("Dec", "Jan", "-01-01");
      await planpage.runSimulation();
      const amount_visible1 = await planpage.verifyCommissionNumber(
        "$244,845.69"
      );
      expect(amount_visible1).toEqual(true);
      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("forecast");
      await dsV2page.letColumnsLoad("Payee");
      await datapage.noofRows("- 5of5rows");
    });

    test("The user is not able to delete the Hierarchy record if it is managed via HRIS", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19164",
        },
        {
          type: "Description",
          description:
            "The user is not able to delete the Hierarchy record, if there are multiple entry",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is not able to delete the Hierarchy record, if there are multiple entry",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Anjanette Antonik Mar 15");
      const isTextPresent1 = await admin.checkIfTextExists("Delete");
      expect(isTextPresent1).toBe(true);
      await admin.closeMapPayee();
      await admin.bulkSetSource();
      await admin.initiateMapping();
      await admin.nextAction();
      await admin.deleteHierarchy("Anjanette Antonik Mar 15");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
    });

    test("The user is able to see the reportees as expected in the usergroup ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19165, INTER-T19181",
        },
        {
          type: "Description",
          description:
            "The user is able to see the reportees as expcted in the user group",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is able to see the reportees as expected in the user group",
        }
      );

      const page = adminPage.page;
      const quotapage = new quotaPage(page);
      const Usergrouppage = new UserGroupPage(page);
      await quotapage.navigate("/groups");
      await Usergrouppage.searchUsergroup("manager");
      const isExitedUserInGroup = await Usergrouppage.isUserInGroup(
        "<EMAIL>"
      );
      expect(isExitedUserInGroup).toBe(true);
    });

    test("The user is able to see the approval request has been raised to proper manager ", async ({
      adminPage,
      request,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19179, INTER-T19180",
        },
        {
          type: "Description",
          description:
            "The user is able to see the reportees as expcted in the user group",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user is able to see the reportees as expected in the user group",
        }
      );

      const page = adminPage.page;
      token = await setupGraphQLRouteInterceptor(page);
      const approvalpage = new ApprovalPage(page);
      const admin = new Revertexitpage(page);
      const quotapage = new quotaPage(page);
      const userpage = new UserPage(page);
      const payoutpage = new PayoutPage(page);
      const payoutfilter = new PayoutFilterPageObjs(page);
      const resp = await request.put("spm/mappayee/hierarchy", {
        data: {
          employeeEmailId: "<EMAIL>",
          hierarchy: {
            reportingManagerEmailId: "<EMAIL>",
            effectiveStartDate: "15-Mar-2024",
            effectiveEndDate: null,
          },
          isEdit: false,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const response = await resp.json();
      console.log(response);
      expect(response.message).toBe("Hierarchy updated successfully!");
      await quotapage.navigate("/commissions");
      await payoutfilter.searchName("juliann");
      // await payoutpage.navigateToPayee("Juliann Adamides");
      await approvalpage.bulkSelectLock("<EMAIL>");
      await approvalpage.bulkApprovals(
        "<EMAIL>",
        "manager-flow"
      );
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(3000);
      await quotapage.navigate("/approvals/payouts");
      await approvalpage.tabName("Pending Requests");
      await approvalpage.checkRequestedpayee("Juliann Adamides");
      await admin.exitImpersonation();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await userpage.waitForSearchResults();
    });
  }
);
