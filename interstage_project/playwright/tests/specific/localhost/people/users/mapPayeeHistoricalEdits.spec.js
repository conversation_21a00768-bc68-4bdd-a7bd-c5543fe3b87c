/* eslint-disable playwright/no-wait-for-timeout */
import { expect } from "@playwright/test";
import UserPage from "../../../../../test-objects/user-objects";
import UserMapPayeePage from "../../../../../test-objects/userMapPayee-objects";
const {
  localCloneFixtures: { test },
} = require("../../../../fixtures");
const {
  mapPayeeHistoricalEditsFixtures: { test: test1 },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
  await page.waitForTimeout(2000);
});

test1.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
  await page.waitForTimeout(2000);
});

test.describe(
  "Map Payee Validations",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test("Frequency Change - Override - Bulk", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page.getByText("Edit Existing Users").click();
      await page.getByRole("switch").click();
      await page.getByLabel("Payout Frequency").check();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/override - validation.csv");
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForTimeout(2000);
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .getByText(
          "Payout Frequency: Cannot update the payout frequency for a period when the user has active commission plans."
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Close" }).click();
    });

    test("Frequency Change - Non - Override - Bulk", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page.getByText("Edit Existing Users").click();
      await page.getByLabel("Payout Frequency").check();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/override - validation.csv");
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForTimeout(2000);
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .getByText(
          "Payout Frequency: Cannot update the payout frequency for a period when the user has active commission plans."
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Close" }).click();
    });

    test("Clicking on Name takes to MapPayee Screen", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.waitForLoadState("load", { timeout: 20000 });
      await page
        .getByPlaceholder("Search by name or email", { exact: true })
        .fill("<EMAIL>", { timeout: 25000 });
      await page.waitForTimeout(1000);
      await page.getByText("Vaishali Psg", { exact: true }).click();
      await page
        .getByText("Basic details")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Cancel" }).last().click();
    });

    test("Map Payee step 3 Screen - buttons", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.waitForLoadState("load", { timeout: 20000 });
      await page
        .getByPlaceholder("Search by name or email", { exact: true })
        .fill("<EMAIL>", { timeout: 25000 });
      await page.waitForTimeout(1000);
      await page.getByRole("button", { name: "Vengatesh Shan" }).click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .getByText("Quota Details")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Add/Modify" }).first().click();
      await page.getByText("Add Quota").click();
      await page.getByRole("button", { name: "Cancel" }).last().click();
      await page.getByRole("button", { name: "Add/Modify" }).nth(1).click();
      await page.getByLabel("Add Draws").getByLabel("Close").click();
      await page.getByRole("button", { name: "Add/Modify" }).nth(2).click();
      await page.getByText("Add or Modify Custom Teams").click();
      await page
        .getByLabel("Add or Modify Custom Teams")
        .getByLabel("Close", { exact: true })
        .click();
      await page.getByRole("button", { name: "Add/Modify" }).nth(3).click();
      await page.getByText("Add pod relationship for Vengatesh Shan").click();
      await page
        .getByLabel("Add pod relationship for")
        .getByLabel("Close", { exact: true })
        .click();
      await page.getByRole("button", { name: "Done" }).click();
      await page.getByText("Processing complete!").click();
    });
  }
);

test1.describe(
  "User map payee - Edit historical records",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test1(
      "Validate creation of new records for Basic and Payroll History and the edit modal functionality.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        await userPage.fillSearch("Alice J");
        await userPage.mapPayeeByEmail("<EMAIL>");

        await mapPayeePage.updateDesignation("QA 1");
        await mapPayeePage.updateCountry("Canada");
        await mapPayeePage.updateBasePay("100000");
        await mapPayeePage.updateVariablePay("1000");
        await mapPayeePage.toggleCF("map-payee_cf_10448_remote");
        await mapPayeePage.updateCFEmergencyContact("123");
        await mapPayeePage.chooseGender("He");
        await mapPayeePage.clickBtn("Save");
        await mapPayeePage.updateFutureDetails(false, "Dec 31, 2024");
        await mapPayeePage.verifyToast("Employee details saved!");

        console.log(
          "----- Verify user(in plans) cannot save payee details on changing payout frequency-----"
        );
        await mapPayeePage.updatePayoutFrequency("Quarterly");
        await mapPayeePage.clickBtn("Save");
        await page
          .getByLabel("Update User Details")
          .getByRole("button", { name: "Next" })
          .click();
        await mapPayeePage.updateFieldByID("payout_frequency", "Jan 01, 2025");
        await mapPayeePage.clickBtn("Save");
        await mapPayeePage.verifyToast(
          "Cannot update the payout frequency for a period when the user has active commission plans."
        );

        console.log("----- Test - Basic and Payroll History -----");
        console.log(
          "----- Verify new records are created with correct dates -----"
        );
        let recordsToVerify = [
          { label: "Start Date", value: "Dec 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 30" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        console.log(
          "----- Payroll hustory: Verify the newly added field details -----"
        );
        await mapPayeePage.clickRecord("Dec 31");
        let fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 31",
          "Designation"
        );
        expect(fieldValue).toBe("QA 1");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 31",
          "Base Pay"
        );
        expect(fieldValue).toBe("100,000.00");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 31",
          "On-Target Variable Pay"
        );
        expect(fieldValue).toBe("1,000.00");

        await mapPayeePage.clickMoreOptions("Dec 31");
        await mapPayeePage.editRecord();

        console.log(
          "----- Payroll history: Verify all headings are present -----"
        );
        await page
          .getByText("Edit fields")
          .waitFor({ state: "visible", timeout: 5000 });
        // List of strings to verify
        const stringsList = [
          "Basic details",
          "Employee ID",
          "Employee details",
          "Designation",
          "Crystal access",
          "Employee and payroll details",
          "Employment country",
          "Payout currency",
          "Payout frequency",
          "Base pay",
          "Variable pay",
          "No fields updated",
        ];
        let allPresent = await mapPayeePage.verifyStringsPresent(stringsList);
        expect(allPresent).toBe(true);

        console.log(
          "----- Payroll history: Verify updating/reverting fields affect the note and proceed -----"
        );
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 30, 2024"
        );
        allPresent = await mapPayeePage.verifyStringsPresent([
          "Effective start date updated",
        ]);
        expect(allPresent).toBe(true);
        await expect(
          await page.getByRole("button", { name: "Proceed" })
        ).toBeEnabled();

        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 31, 2024"
        );
        allPresent = await mapPayeePage.verifyStringsPresent([
          "No fields updated",
        ]);
        expect(allPresent).toBe(true);
        await expect(
          await page.getByRole("button", { name: "Proceed" })
        ).toBeDisabled();

        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Apr 01, 2025"
        );
        await mapPayeePage.updateFieldByID("designation", "QA 2");
        await mapPayeePage.updateFieldByID("fixedPay", "180000");
        allPresent = await mapPayeePage.verifyStringsPresent([
          "3 fields updated",
        ]);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Proceed");

        console.log(
          "----- Payroll history:  Verify preview of changes made -----"
        );
        // Get all text content with 'line-through' class
        const textsWithLineThrough =
          await mapPayeePage.getTextWithLineThrough();
        expect(textsWithLineThrough[0]).toBe("Dec 31, 2024");
        expect(textsWithLineThrough[1]).toBe("QA 1");
        expect(textsWithLineThrough[2]).toBe("100000.00");

        allPresent = await mapPayeePage.verifyStringsPresent([
          "QA 2",
          "180000",
        ]);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyToast("Payroll updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        recordsToVerify = [
          { label: "Start Date", value: "Apr 01" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Mar 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        await mapPayeePage.clickRecord("Mar 31");
        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Mar 31",
          "Designation"
        );
        expect(fieldValue).toBe("");
        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Mar 31",
          "Base Pay"
        );
        expect(fieldValue).toBe("-");

        await mapPayeePage.clickMoreOptions("Mar 31");
        await mapPayeePage.editRecord();

        await mapPayeePage.updateFieldByID("designation", "QA 1");
        allPresent = await mapPayeePage.verifyStringsPresent([
          "1 field updated",
        ]);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Proceed");
        allPresent = await mapPayeePage.verifyStringsPresent(['""']);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyToast("Payroll updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        console.log(
          "----- Verify Edit and Delete options are present for the next records -----"
        );
        await mapPayeePage.clickMoreOptions("Apr 01");
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Edit" })
            .last()
        ).toBeVisible();
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Delete" })
            .last()
        ).toBeVisible();

        console.log(
          "----- Verify only Edit option is present for the first record -----"
        );
        await mapPayeePage.clickMoreOptions("Mar 31");
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Edit" })
            .last()
        ).toBeVisible();
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Delete" })
            .last()
        ).toBeHidden();

        await mapPayeePage.clickMoreOptions("Apr 01");
        await mapPayeePage.deleteRecord();
        await mapPayeePage.closeModal();

        await mapPayeePage.clickMoreOptions("Apr 01");
        await mapPayeePage.deleteRecord();
        await mapPayeePage.closeModal(false);

        await mapPayeePage.clickMoreOptions("Apr 01");
        await mapPayeePage.deleteRecord();
        await mapPayeePage.verifyRecordPopup(
          "Are you sure you want to delete this period?",
          "The entry with [Start date: Apr 01, 2025], along with the associated user field values for this period, will be permanently deleted. This action cannot be undone."
        );
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast("Payroll period deleted successfully");
      }
    );

    test1(
      "Validate creation of new records for Custom Field History and the edit modal functionality.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        await userPage.fillSearch("Bob lee");
        await userPage.mapPayeeByEmail("<EMAIL>");

        await mapPayeePage.toggleCF("map-payee_cf_10448_remote");
        await mapPayeePage.updateCFEmergencyContact("123");
        await mapPayeePage.clickBtn("Save");
        await expect(page.getByText("Please enter the field")).toBeVisible();
        await mapPayeePage.chooseGender("He");
        await mapPayeePage.fillCFstartedon("Jan 01, 2025");
        await mapPayeePage.clickBtn("Save");
        await mapPayeePage.updateFutureDetails();
        await mapPayeePage.verifyToast("Employee details saved!");

        console.log("----- Test - Custom Field History -----");
        console.log(
          "----- Verify new records are created with correct dates -----"
        );
        await mapPayeePage.goToTab("Custom Field History");
        const record = await mapPayeePage.verifyRecord("Start Date", "Dec 11");
        await expect(record).toBeVisible({ timeout: 5000 });

        console.log(
          "----- Custom Field history: Verify the newly added field details -----"
        );
        await mapPayeePage.clickRecord("Dec 11");
        let fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "Remote"
        );
        expect(fieldValue).toBe("True");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "Emergency contact"
        );
        expect(fieldValue).toBe("123");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "pronounced as"
        );
        expect(fieldValue).toBe("He");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "freelance started om"
        );
        expect(fieldValue).toBe("Jan 01, 2025");

        await mapPayeePage.clickMoreOptions("Dec 11");
        await mapPayeePage.editRecord();

        console.log(
          "----- Custom Field history: Verify all headings are present -----"
        );
        await page
          .getByText("Edit fields")
          .waitFor({ state: "visible", timeout: 5000 });
        // List of strings to verify
        const stringsList = [
          "Effective start date",
          "Custom fields",
          "Only the custom fields with effective dates are displayed below.",
          "Remote",
          "Emergency contact",
          "Emergency email",
          "pronounced as",
          "No fields updated",
        ];
        let allPresent = await mapPayeePage.verifyStringsPresent(stringsList);
        expect(allPresent).toBe(true);

        allPresent = await mapPayeePage.verifyStringsPresent([
          "freelance started om",
        ]);
        expect(allPresent).toBe(false);

        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Apr 01, 2025"
        );
        allPresent = await mapPayeePage.verifyStringsPresent([
          "Effective start date updated",
        ]);
        expect(allPresent).toBe(true);
        await expect(
          await page.getByRole("button", { name: "Proceed" })
        ).toBeEnabled();

        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 11, 2024"
        );
        allPresent = await mapPayeePage.verifyStringsPresent([
          "No fields updated",
        ]);
        expect(allPresent).toBe(true);
        await expect(
          await page.getByRole("button", { name: "Proceed" })
        ).toBeDisabled();

        await mapPayeePage.updateFieldByID(
          "cf_10448_emergency_contact",
          "987654321"
        );
        await mapPayeePage.updateFieldByID(
          "cf_10448_emergency_email",
          "<EMAIL>"
        );
        allPresent = await mapPayeePage.verifyStringsPresent([
          "2 fields updated",
        ]);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Proceed");

        console.log(
          "----- Custom Field history:  Verify preview of changes made -----"
        );
        // Get all text content with 'line-through' class
        const textsWithLineThrough =
          await mapPayeePage.getTextWithLineThrough();
        expect(textsWithLineThrough[0]).toBe("123");

        allPresent = await mapPayeePage.verifyStringsPresent(['""']);
        expect(allPresent).toBe(true);
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyToast("Custom field updated successfully");
        await verifyGotItPopup(page, mapPayeePage);
      }
    );

    test1(
      "Validate creation of new records for Reporting Details History and the edit modal functionality.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        await userPage.fillSearch("Bob lee");
        await userPage.mapPayeeByEmail("<EMAIL>");
        await mapPayeePage.clickBtn("Next");

        await mapPayeePage.updateReportingManager(
          "Mangaer 1",
          "reporting-mgr_reportingManagerEmailId"
        );
        await mapPayeePage.updateFieldByID(
          "reporting-mgr_reportingEffectiveStartDate",
          "Apr 01, 2025"
        );
        await mapPayeePage.clickBtn("Save Changes");
        await mapPayeePage.verifyToast("Hierarchy updated successfully!");

        await mapPayeePage.reportingManagerMoreOptions("Apr 01");
        await mapPayeePage.editRecord();

        console.log(
          "----- Reporting Manager history: Verify all headings are present -----"
        );
        await page
          .getByLabel("Edit")
          .getByText("Edit", { exact: true })
          .waitFor({ state: "visible", timeout: 5000 });
        // List of strings to verify
        const stringsList = [
          "Effective start date",
          "Reporting manager",
          "No fields ",
        ];
        let allPresent = await mapPayeePage.verifyStringsPresent(stringsList);
        expect(allPresent).toBe(true);

        // Verify "Proceed" button is initially disabled
        await expect(
          page.getByRole("button", { name: "Proceed" })
        ).toBeDisabled();

        // Update the reporting manager and verify success message
        await mapPayeePage.updateReportingManager(
          "Vicky 021",
          "reportingManagerEmailId"
        );
        let isUpdated = await mapPayeePage.verifyStringsPresent([
          "Reporting manager",
        ]);
        expect(isUpdated).toBe(true);

        // Verify "Proceed" button is enabled after update
        await expect(
          page.getByRole("button", { name: "Proceed" })
        ).toBeEnabled();

        // Revert the reporting manager and verify the "Proceed" button is disabled again
        await mapPayeePage.updateReportingManager(
          "Mangaer 1",
          "reportingManagerEmailId"
        );
        await expect(
          page.getByRole("button", { name: "Proceed" })
        ).toBeDisabled();

        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "May 01, 2025"
        );
        isUpdated = await mapPayeePage.verifyStringsPresent([
          "Effective start date",
        ]);
        expect(isUpdated).toBe(true);
        await mapPayeePage.clickBtn("Proceed");

        const textsWithLineThrough =
          await mapPayeePage.getTextWithLineThrough();
        expect(textsWithLineThrough[0]).toBe("Apr 01, 2025");

        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyToast("Hierarchy updated successfully");
        await verifyGotItPopup(page, mapPayeePage, false);

        await mapPayeePage.switch();
        await mapPayeePage.updateReportingManager(
          "Vicky 021",
          "reporting-mgr_reportingManagerEmailId"
        );
        await mapPayeePage.clickBtn("Save Changes");
        await mapPayeePage.verifyToast("Hierarchy updated successfully!");
        allPresent = await mapPayeePage.verifyManagerNames([
          "Vicky 021",
          "Vinith M",
        ]);
        expect(allPresent).toBe(true);

        console.log(
          "----- Verify user is able to cancel deletion process and delete a record -----"
        );
        await mapPayeePage.reportingManagerMoreOptions("Apr 30");
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Edit" })
            .last()
        ).toBeVisible();
        await expect(
          await page
            .getByRole("menuitem")
            .locator("span", { hasText: "Delete" })
            .last()
        ).toBeVisible();

        await mapPayeePage.deleteRecord();
        await mapPayeePage.verifyRecordPopup(
          "Are you sure you want to delete this entry?",
          "Vinith M was Bob lee's reporting manager for the period start date: Dec 11, 2024; end date: Apr 30, 2025. You are about to delete this relationship, and this action cannot be undone."
        );
        await mapPayeePage.closeModal();

        await mapPayeePage.reportingManagerMoreOptions("Apr 30");
        await mapPayeePage.deleteRecord();
        await mapPayeePage.closeModal(false);

        await mapPayeePage.reportingManagerMoreOptions("Apr 30");
        await mapPayeePage.deleteRecord();
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast(
          "Reporting manager deleted successfully"
        );
      }
    );

    test1(
      "Validate overlapping functionality is working for Payroll/Custom Fields/Reporting manager records.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        await userPage.fillSearch("Jane s");
        await userPage.mapPayeeByEmail("<EMAIL>");

        console.log("Test close modal");
        await mapPayeePage.clickMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 11, 2024"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Are you sure you want to delete overlapping periods?",
          "Updating the effective start date from Aug 01, 2025 to Dec 11, 2024 has resulted in overlapping periods, which will be removed. Here is the list of period(s) that will be deleted: Start date: Dec 11, 2024 End date: Dec 31, 2024 Start date: Jan 01, 2025 End date: Mar 31, 2025 Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.closeModal();

        console.log(
          "----- Verify overlapping records for payroll history -----"
        );
        await mapPayeePage.clickMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 11, 2024"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast("Payroll updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        console.log(
          "----- Verify prev records are deleted as part of overlapp -----"
        );
        let doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
          "Start Date",
          "Aug 01"
        );
        expect(doesNotExist).toBe(true);
        doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
          "Start Date",
          "Apr 01"
        );
        expect(doesNotExist).toBe(true);
        doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
          "Start Date",
          "Jan 01"
        );
        expect(doesNotExist).toBe(true);

        await mapPayeePage.clickRecord("Dec 11");
        console.log(
          "----- Verify the field values of this single record present are correct -----"
        );
        let fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "Designation"
        );
        expect(fieldValue).toBe("Sr QA1");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "Crystal Access"
        );
        expect(fieldValue).toBe("Yes");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "Base Pay"
        );
        expect(fieldValue).toBe("-");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Dec 11",
          "On-Target Variable Pay"
        );
        expect(fieldValue).toBe("40,000.00");

        console.log("Verify overlapping records for Custom Fields history");
        await mapPayeePage.goToTab("Custom Field History");

        console.log("Test close modal");
        await mapPayeePage.clickMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Jan 01, 2025"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Are you sure you want to delete overlapping periods?",
          "Updating the effective start date from Aug 01, 2025 to Jan 01, 2025 has resulted in overlapping periods, which will be removed. Here is the list of period(s) that will be deleted: Start date: Jan 01, 2025 End date: Mar 31, 2025 Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.closeModal(false);

        console.log(
          "----- Verify overlapping records for Custom fields history -----"
        );
        await mapPayeePage.clickMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Jan 01, 2025"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast("Custom field updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        console.log(
          "----- Verify prev records are deleted as part of overlapp -----"
        );
        doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
          "Start Date",
          "Aug 01"
        );
        expect(doesNotExist).toBe(true);
        doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
          "Start Date",
          "Apr 01"
        );
        expect(doesNotExist).toBe(true);

        const record = await mapPayeePage.verifyRecord("Start Date", "Dec 11");
        await expect(record).toBeVisible({ timeout: 5000 });

        await mapPayeePage.clickRecord("Jan 01");
        console.log(
          "----- Verify the field values of  remaining record present are correct -----"
        );
        fieldValue = await mapPayeePage.verifyRecordDetails("Jan 01", "Remote");
        expect(fieldValue).toBe("False");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Jan 01",
          "Emergency contact"
        );
        expect(fieldValue).toBe("123");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Jan 01",
          "Emergency email"
        );
        expect(fieldValue).toBe("<EMAIL>");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Jan 01",
          "pronounced as"
        );
        expect(fieldValue).toBe("She");

        console.log("Verify overlapping records for Reporting Manager history");
        await mapPayeePage.clickBtn("Next");

        console.log("Test close modal");
        await mapPayeePage.reportingManagerMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 11, 2024"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Are you sure you want to delete overlapping periods?",
          "Updating the effective start date from Aug 01, 2025 to Dec 11, 2024 has resulted in overlapping periods, which will be removed. Here is the list of period(s) that will be deleted: Start date: Dec 11, 2024 End date: Dec 31, 2024 Start date: Jan 01, 2025 End date: Mar 31, 2025 Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.closeModal();

        console.log(
          "----- Verify overlapping records for payroll history -----"
        );
        await mapPayeePage.reportingManagerMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "effectiveStartDate",
          "Dec 11, 2024"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast("Hierarchy updated successfully");
        await verifyGotItPopup(page, mapPayeePage, false);

        const managerChecks = [
          { name: "Vinith M", expected: true },
          { name: "Mangaer 1", expected: false },
          { name: "Vicky 021", expected: false },
        ];

        for (const { name, expected } of managerChecks) {
          const allPresent = await mapPayeePage.verifyManagerNames([name]);
          expect(allPresent).toBe(expected);
        }
      }
    );

    test1(
      "Validate Merge and keep functionality is working for Payroll/Custom Fields/Reporting manager records.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description:
              "Cover testcases for merging and keeping the records for Payroll/Custom fields/Reporting manager records. Test the manager is reflected in statements for specific periods",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        async function editRecordAndApplyChanges() {
          await mapPayeePage.clickMoreOptions("Aug 01");
          await mapPayeePage.editRecord();
          await mapPayeePage.updateFieldByID("designation", "QA 2");
          await mapPayeePage.updateFieldByID("variablePay", "10000");
          await mapPayeePage.clickBtn("Proceed");
          await mapPayeePage.clickBtn("Apply changes");
        }

        await userPage.fillSearch("John D");
        await userPage.mapPayeeByEmail("<EMAIL>");

        console.log("----- Payroll history : Verify Merge/Keep records -----");
        console.log("----- Verify the intial records before Merging -----");
        let recordsToVerify = [
          { label: "Start Date", value: "Aug 01" },
          { label: "Start Date", value: "Apr 01" },
          { label: "End Date", value: "Jul 31" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        console.log("Test close modal");
        await editRecordAndApplyChanges();
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "he same user details are present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same details: Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.closeModal();

        console.log("----- Veriify Merging records are working -----");
        await editRecordAndApplyChanges();
        await mapPayeePage.clickBtn("Merge");
        await mapPayeePage.verifyToast("Payroll updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        console.log("----- Verify the new records after Merging -----");
        recordsToVerify = [
          { label: "Start Date", value: "Apr 01" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        recordsToVerify = [
          { label: "Start Date", value: "Aug 01" },
          { label: "End Date", value: "Jul 31" },
        ];
        for (const { label, value } of recordsToVerify) {
          const doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
            label,
            value
          );
          expect(doesNotExist).toBe(true);
        }

        console.log("----- Veriify keeping Merged records are working -----");
        await mapPayeePage.clickMoreOptions("Apr 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID("designation", "QA 1");
        await mapPayeePage.updateFieldByID("variablePay", "5000");
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "The same user details are present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same details: Start date: Jan 01, 2025 End date: Mar 31, 2025"
        );
        await mapPayeePage.clickBtn("Keep");
        await mapPayeePage.verifyToast("Payroll updated successfully");
        verifyGotItPopup(page, mapPayeePage);

        console.log(
          "----- Verify the records are not changed after keeping the merge-----"
        );
        recordsToVerify = [
          { label: "Start Date", value: "Apr 01" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        await mapPayeePage.clickRecord("Apr 01");
        let fieldValue = await mapPayeePage.verifyRecordDetails(
          "Apr 01",
          "Designation"
        );
        expect(fieldValue).toBe("QA 1");

        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Apr 01",
          "On-Target Variable Pay"
        );
        expect(fieldValue).toBe("5,000.00");

        await mapPayeePage.goToTab("Custom Field History");
        console.log(
          "----- Custom Fields history : Verify Merge/Keep records -----"
        );
        console.log("----- Verify the intial records before Merging -----");
        recordsToVerify = [
          { label: "Start Date", value: "Aug 01" },
          { label: "Start Date", value: "Apr 01" },
          { label: "End Date", value: "Jul 31" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        console.log("----- Veriify Merging records are working -----");
        mapPayeePage.clickMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID(
          "cf_10448_emergency_contact",
          "123456"
        );
        await mapPayeePage.toggleCF("cf_10448_remote");
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "The same user details are present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same details: Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.clickBtn("Merge");
        await mapPayeePage.verifyToast("Custom field updated successfully");
        await verifyGotItPopup(page, mapPayeePage);

        console.log("----- Verify the new records after Merging -----");
        recordsToVerify = [
          { label: "Start Date", value: "Jan 01" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        recordsToVerify = [
          { label: "Start Date", value: "Aug 01" },
          { label: "End Date", value: "Jul 31" },
        ];
        for (const { label, value } of recordsToVerify) {
          const doesNotExist = await mapPayeePage.verifyRecordDoesNotExist(
            label,
            value
          );
          expect(doesNotExist).toBe(true);
        }

        console.log("----- Veriify keeping Merged records are working -----");
        await mapPayeePage.clickMoreOptions("Apr 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateFieldByID("cf_10448_emergency_contact", "123");
        await mapPayeePage.toggleCF("cf_10448_remote");
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "The same user details are present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same details: Start date: Jan 01, 2025 End date: Mar 31, 2025"
        );
        await mapPayeePage.clickBtn("Keep");
        await mapPayeePage.verifyToast("Custom field updated successfully");
        verifyGotItPopup(page, mapPayeePage);

        console.log(
          "----- Verify the records are not changed after keeping the merge-----"
        );
        recordsToVerify = [
          { label: "Start Date", value: "Jan 01" },
          { label: "Start Date", value: "Jan 01" },
          { label: "End Date", value: "Mar 31" },
          { label: "Start Date", value: "Dec 11" },
          { label: "End Date", value: "Dec 31" },
        ];

        for (const { label, value } of recordsToVerify) {
          const record = await mapPayeePage.verifyRecord(label, value);
          await expect(record).toBeVisible({ timeout: 5000 });
        }

        await mapPayeePage.clickRecord("Jan 01");
        fieldValue = await mapPayeePage.verifyRecordDetails(
          "Jan 01",
          "Emergency contact"
        );
        expect(fieldValue).toBe("123");

        await mapPayeePage.clickRecord("Apr 01");
        fieldValue = await mapPayeePage.verifyRecordDetails("Apr 01", "Remote");
        expect(fieldValue).toBe("False");

        await mapPayeePage.clickBtn("Next");
        console.log(
          "----- Reporting Manager history : Verify Merge/Keep records -----"
        );
        console.log("----- Verify the intial records before Merging -----");
        const managerChecks = [
          { name: "Vinith M", expected: true },
          { name: "Mangaer 1", expected: true },
          { name: "Vicky 021", expected: true },
        ];

        for (const { name, expected } of managerChecks) {
          const allPresent = await mapPayeePage.verifyManagerNames([name]);
          expect(allPresent).toBe(expected);
        }

        console.log("Merge records");
        await mapPayeePage.reportingManagerMoreOptions("Aug 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateReportingManager(
          "Mangaer 1",
          "reportingManagerEmailId"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "The same reporting manager is present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same reporting manager: Mangaer 1 Start date: Apr 01, 2025 End date: Jul 31, 2025"
        );
        await mapPayeePage.clickBtn("Merge");
        await mapPayeePage.verifyToast("Hierarchy updated successfully");
        await verifyGotItPopup(page, mapPayeePage, false);

        console.log("Keep merge records");
        await mapPayeePage.reportingManagerMoreOptions("Jan 01");
        await mapPayeePage.editRecord();
        await mapPayeePage.updateReportingManager(
          "Vinith M",
          "reportingManagerEmailId"
        );
        await mapPayeePage.clickBtn("Proceed");
        await mapPayeePage.clickBtn("Apply changes");
        await mapPayeePage.verifyRecordPopup(
          "Do you want to merge periods?",
          "The same reporting manager is present in adjacent historical period(s). Would you prefer to merge these periods or keep them as separate entries? Adjacent period(s) with the same reporting manager: Vinith M Start date: Dec 11, 2024 End date: Dec 31, 2024"
        );
        await mapPayeePage.clickBtn("Keep");
        await mapPayeePage.verifyToast("Hierarchy updated successfully");
        await verifyGotItPopup(page, mapPayeePage, false);
        const allPresent = await mapPayeePage.verifyManagerNames(["Vicky 021"]);
        expect(allPresent).toBe(false);

        await mapPayeePage.reportingManagerMoreOptions("Dec 31");
        await mapPayeePage.reportingManagerMoreOptions("Mar 31");
        await mapPayeePage.reportingManagerMoreOptions("Apr 01");

        console.log(
          "----- Verify the manager of the user in the statements are reflected correctly for specified months -----"
        );
        // Statements - User - John D, Month - April 2025
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJqb2huLmRvZUBleGFtcGxlLmNvbSIsInBzZCI6IjIwMjUtMDQtMDEiLCJwZWQiOiIyMDI1LTA0LTMwIn0=",
          { waitUntil: "networkidle" }
        );
        await page.getByText("View profile").click();
        await expect(page.getByText("Mangaer 1")).toBeVisible({
          timeout: 5000,
        });

        // Statements - User - John D, Month - Dec 2024
        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJqb2huLmRvZUBleGFtcGxlLmNvbSIsInBzZCI6IjIwMjQtMTItMDEiLCJwZWQiOiIyMDI0LTEyLTMxIn0=",
          { waitUntil: "networkidle" }
        );
        await page.getByText("View profile").click();
        await expect(page.getByText("Vinith M")).toBeVisible({
          timeout: 5000,
        });
      }
    );

    test1(
      "Validate system throws warning on exit user who is a active manager.",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description:
              "Cover testcases for merging and keeping the records for Payroll/Custom fields/Reporting manager records. Test the manager is reflected in statements for specific periods",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const mapPayeePage = new UserMapPayeePage(page);

        await userPage.fillSearch("Vinith M");
        await userPage.initiateExit();
        await userPage.selectExitDateAndValidate("Aug 01, 2025");
        await userPage.verifyExitValidationSuccess();
        await mapPayeePage.verifyRecordPopup(
          "Pick the exit date and the commissions end period to offboard your users seamlessly. You can even adjust commission if needed.",
          "The following payees must not <NAME_EMAIL> after Invalid date. Please change their reporting hierarchy: <EMAIL>"
        );
        await userPage.clickCancelButton();
      }
    );

    async function verifyGotItPopup(page, mapPayeePage, defaultText = true) {
      let txt;

      if (defaultText) {
        txt =
          "If the updated fields are part of a commission plan, please remember to run the commission sync from Settings → Commission & Data Sync to ensure the changes are reflected.";
      } else {
        txt =
          "Please run the commission sync from Settings → Commission & Data Sync for the impacted user(s) to ensure the changes are applied.";
      }

      const allPresent = await mapPayeePage.verifyStringsPresent([
        "Run commission sync",
      ]);
      expect(allPresent).toBe(true);

      await expect(page.locator(".ant-modal-body").last()).toContainText(txt);

      await mapPayeePage.verifyAnchorTag();
      await mapPayeePage.clickBtn("Got it");
    }
  }
);
