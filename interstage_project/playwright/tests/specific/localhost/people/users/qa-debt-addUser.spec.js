const {
  playwrightAddPeopleFixtures: { test, expect },
  playwrightAddPeopleFixtures2: { test: test1 },
} = require("../../../../fixtures");

const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 15000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }
  await page.goto("/users", { waitUntil: "networkidle" });
});

test1.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 15000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }
  await page.goto("/users", { waitUntil: "networkidle" });
});

test.describe(
  "Add User Testcases",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test.describe("Existing Users addition tests", () => {
      test("Add existing super admin user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "New User" }).click();
        await page.getByPlaceholder("Enter Email").click();
        await page
          .getByPlaceholder("Enter Email")
          .fill("<EMAIL>");
        await page.getByPlaceholder("Enter First Name").click();
        await page.getByPlaceholder("Enter First Name").fill("SA User");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("2");
        await page.getByLabel("Role*").click();
        // await page.getByTestId("pt-Super Admin").getByText("Super Admin").click();
        await page.getByTitle("Super Admin", { exact: true }).nth(1).click();
        await page.getByRole("button", { name: "Add User" }).click();
        await page
          .getByText("EMAIL ALREADY EXISTS - <EMAIL>")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      test("Add existing payee user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "New User" }).click();
        await page.getByPlaceholder("Enter Email").click();
        await page
          .getByPlaceholder("Enter Email")
          .fill("<EMAIL>");
        await page.getByPlaceholder("Enter First Name").click();
        await page.getByPlaceholder("Enter First Name").fill("Payee User");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("2");
        await page.getByLabel("Role*").click();
        // await page.getByTestId("pt-Payee").getByText("Payee").click();
        await page.getByTitle("Payee", { exact: true }).nth(1).click();
        await page.getByRole("button", { name: "Add User" }).click();
        await page
          .getByText("EMAIL ALREADY EXISTS - <EMAIL>")
          .waitFor({ state: "visible", timeout: 5000 });
      });
    });

    test.describe("User search tests", () => {
      test("Search for users using partial email", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("sa_user");
        await page
          .getByTestId("pt-Added")
          .waitFor({ status: "visible", timeout: 5000 });
        const cnt = await page.getByText("sa_user").count();
        expect(cnt).toBe(3);
      });

      test("Search for a user using an email", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("<EMAIL>");
        await page
          .getByTestId("pt-Added")
          .waitFor({ status: "visible", timeout: 5000 });
        const cnt = await page
          .getByText("<EMAIL>")
          .count();
        expect(cnt).toBe(1);
      });

      test("Search for a user using user name", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("Active Payee User 4");
        await page
          .getByTestId("pt-Added")
          .waitFor({ status: "visible", timeout: 5000 });
        const cnt = await page.getByText("Active Payee User 4").count();
        expect(cnt).toBe(1);
      });
    });

    // Basic check for success action
    test.describe("User Export successful tests", () => {
      test("Export all users", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        const downloadPromise = page.waitForEvent("download");
        await page
          .getByRole("button", { name: "Export users You can export" })
          .click();
        const download = await downloadPromise;
        await page
          .getByText("Downloaded Successfully!!")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      // Basic check for success action
      test("Export added/not invited users", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByText("Added / Not Invited Users").click();
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page
          .getByRole("button", { name: "Export users You can export" })
          .click();
        await page.getByText("Export 9 users who match the").click();
        const downloadPromise = page.waitForEvent("download");
        await page.getByRole("button", { name: "Proceed" }).click();
        const download = await downloadPromise;
        await page
          .getByText("Downloaded Successfully!!")
          .waitFor({ state: "visible", timeout: 5000 });
      });
    });

    test("Apply filters from filter options", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .locator(
          '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
        )
        .first()
        .click();
      await page.getByTestId("status").click();
      await page
        .locator("div")
        .filter({ hasText: /^Added\/ Not Invited$/ })
        .nth(1)
        .click();
      await page.keyboard.press("Escape");
      await page.getByTestId("payoutFrequency").click();
      await page
        .locator("div")
        .filter({ hasText: /^Quarterly$/ })
        .nth(2)
        .click();
      await page.keyboard.press("Escape");
      await page.getByTestId("mappingStatus").click();
      // await page.getByLabel("Mapped", { exact: true }).check();
      await page
        .locator("div")
        .filter({ hasText: /^Mapped$/ })
        .nth(2)
        .click();
      await page.keyboard.press("Escape");
      await page.getByTestId("userSource").click();
      await page
        .locator("div")
        .filter({ hasText: /^Manually managed$/ })
        .nth(1)
        .click();
      await page.keyboard.press("Escape");
      await page.getByTestId("role").click();
      // await page.getByTestId("pt-Payee").getByText("Payee").click();
      await page.getByTitle("Payee", { exact: true }).nth(1).click();
      // await page.getByTestId("pt-Super Admin").getByText("Super Admin").click();
      await page.getByTitle("Super Admin", { exact: true }).nth(1).click();
      await page.keyboard.press("Escape");
      await page.getByTestId("reportingManager").click();
      await page.getByRole("listitem").getByText("SA User 2 Test").click();
      await page.keyboard.press("Escape");
      await page.getByTestId("employmentCountry").click();
      await page.getByText("United States Of America").click();
      await page.keyboard.press("Escape");
      await page.getByTestId("payCurrency").click();
      await page.locator("span").filter({ hasText: "USD" }).click();
      await page.keyboard.press("Escape");
      await page.getByText("In Between").first().click();
      await page.getByText("Greater Than", { exact: true }).click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("2024-04-01");
      await page.getByPlaceholder("Select date").press("Enter");
      const cnt = await page.getByRole("button", { name: "Apply" }).count();
      expect(cnt).toBe(1);
    });

    // Later in serial mode as data gets modified
    test.describe("Invite user tests", () => {
      test("Invite added user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Send Invite" }).click();
        await page.getByRole("button", { name: "Yes, send" }).click();
        await page
          .getByText("Invite sent Successfully")
          .waitFor({ state: "visible", timeout: 10000 });
      });

      test("Resend invite to invited user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Resend Invite" }).click();
        await page.getByRole("button", { name: "Yes, send" }).click();
        await page
          .getByText("Invite sent Successfully")
          .waitFor({ state: "visible", timeout: 10000 });
      });

      test("Resend invite to active user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Resend Invite" }).click();
        await page.getByRole("button", { name: "Yes, send" }).click();
        await page
          .getByText("Invite sent Successfully")
          .waitFor({ state: "visible", timeout: 10000 });
      });

      test("Apply quick filters test - added/not invited filter", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const addedUsersButton = await page.getByTestId("pt-Added");
        const extractedText = await addedUsersButton.innerText();
        const addedCount = extractedText
          .split("\n")
          .filter((part) => part.trim() !== "")
          .pop();
        await addedUsersButton.click();
        await page.waitForTimeout(2000);
        const countText = await page.getByTestId("pt-row-count");
        const ans = await countText.innerText();
        const tempArray = ans.split("\n").filter((part) => part.trim() !== "");
        const endIndex = tempArray.indexOf("of") + 1;
        const totalRows = tempArray[endIndex];
        expect(addedCount).toBe(totalRows);
      });
    });

    test.describe("New user creation and export all users tests", () => {
      test("Add new super admin user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "New User" }).click();
        await page.getByPlaceholder("Enter Email").click();
        await page
          .getByPlaceholder("Enter Email")
          .fill("<EMAIL>");
        await page.getByPlaceholder("Enter First Name").click();
        await page
          .getByPlaceholder("Enter First Name")
          .fill("New Add SA User 11");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("Test 11");
        await page.getByLabel("Role*").click();
        // await page.getByTestId("pt-Super Admin").getByText("Super Admin").click();
        await page.getByTitle("Super Admin", { exact: true }).nth(1).click();
        await page.getByRole("button", { name: "Add User" }).click();
        await expect(page.getByText("User Added successfully")).toBeVisible();
      });

      test("Add new payee user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "New User" }).click();
        await page.getByPlaceholder("Enter Email").click();
        await page
          .getByPlaceholder("Enter Email")
          .fill("<EMAIL>");
        await page.getByPlaceholder("Enter First Name").click();
        await page
          .getByPlaceholder("Enter First Name")
          .fill("New Add Payee User 12");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("Test 12");
        await page.getByLabel("Role*").click();
        // await page.getByTestId("pt-Payee").getByText("Payee").click();
        await page.getByTitle("Payee", { exact: true }).nth(1).click();
        await page.getByRole("button", { name: "Add User" }).click();
        await page
          .getByText("User Added successfully")
          .waitFor({ state: "visible", timeout: 8000 });
      });

      test("Add user with custom role", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "New User" }).click();
        await page.getByPlaceholder("Enter Email").click();
        await page
          .getByPlaceholder("Enter Email")
          .fill("<EMAIL>");
        await page.getByPlaceholder("Enter First Name").click();
        await page
          .getByPlaceholder("Enter First Name")
          .fill("Custom User 13-1");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("Test 13-1");
        await page.getByLabel("Role*").click();
        await page
          .getByTestId("pt-Custom payee 1")
          .getByText("Custom payee")
          .click();
        await page.getByRole("button", { name: "Add User" }).click();
        await page
          .getByText("User Added successfully")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      test("Export csv for all users and check the csv", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.locator('[name="selectAll"]').click();
        await page.getByRole("button", { name: "Import / Export" }).click();
        const downloadPromise = page.waitForEvent("download");
        await page
          .getByRole("button", { name: "Export users You can export" })
          .click();
        const download = await downloadPromise;
        const downloadPath = path.join(
          __dirname,
          "downloads",
          download.suggestedFilename()
        );
        await download.saveAs(downloadPath);
        const results = [];
        fs.createReadStream(downloadPath)
          .pipe(csv())
          .on("data", (data) => {
            const trimmedData = {};
            for (const key in data) {
              trimmedData[key.trim()] = data[key];
            }
            results.push(trimmedData);
          })
          .on("end", () => {
            console.log(results);
            expect(results.length).toBe(20);
            expect(Object.keys(results[0]).length).toBe(18);
          })
          .on("error", (err) => {
            throw err;
          });
        await expect(page.getByText("Downloaded Successfully!!")).toBeVisible();
      });
    });

    test.describe("User modification and exit tests", () => {
      test("Modify added user details", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Edit" }).click();
        await page.getByPlaceholder("Enter First Name").click();
        await page
          .getByPlaceholder("Enter First Name")
          .fill("New Modified Payee User 12");
        await page.getByPlaceholder("Enter Last Name").click();
        await page.getByPlaceholder("Enter Last Name").fill("Test 12 New");
        await page.getByLabel("Update User").getByText("Payee").click();
        // await page.getByTestId("pt-Admin").getByText("Admin").click();
        await page.getByTitle("Admin", { exact: true }).nth(1).click();
        await page.getByRole("button", { name: "Update User" }).click();
        await expect(page.getByText("User Updated successfully")).toBeVisible();
      });

      test("Exit user without providing exit date", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Initiate Exit" }).click();
        await page.getByRole("button", { name: "Validate" }).click();
        await page
          .getByText("Enter exit date")
          .waitFor({ status: "visible", timeout: 3000 });
      });

      test("Exit user by providing exit date", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Initiate Exit" }).click();
        await page.getByPlaceholder("Select date").click();
        await page.getByPlaceholder("Select date").fill("Aug 20, 2024");
        await page.getByPlaceholder("Select date").press("Enter");
        await page.getByRole("button", { name: "Validate" }).click();
        await page.getByText("Validation Successful!").click();
        await page.getByRole("button", { name: "Confirm" }).click();
        await expect(page.getByText("Save Successful!")).toBeVisible();
      });
    });

    test("Export inactive user and check csv", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByText("Inactive / Exited Users").click();
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page
        .getByRole("button", { name: "Export users You can export" })
        .click();
      await page.getByLabel("Export 2 users who match the").check();
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Proceed" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);
      const results = [];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => {
          const trimmedData = {};
          for (const key in data) {
            trimmedData[key.trim()] = data[key];
          }
          results.push(trimmedData);
        })
        .on("end", () => {
          expect(results.length).toBe(2);
          expect(Object.keys(results[0]).length).toBe(18);
          expect(results[0].Email).toBe("<EMAIL>");
          expect(results[0].Name).toBe("Exit User 9 Test 9");
          expect(results[0].Designation).toBe("Payee");
          expect(results[0].Role).toBe("Payee");
          expect(results[0]["Primary Commission Plan"]).toBe("");
          expect(results[0]["SPIFF Plans"]).toBe("");
          expect(results[0]["Employee ID"]).toBe("");
          expect(results[0]["Employment Country"]).toBe("AUS");
          expect(results[0]["Base Pay"]).toBe("1000.00");
          expect(results[0]["Variable Pay"]).toBe("0.00");
          expect(results[0]["Payout Frequency"]).toBe("Half-yearly");
          expect(results[0]["Payout Currency"]).toBe("AUD");
          expect(results[0]["Reporting Manager"]).toBe("");
          expect(results[0]["Reporting Manager Name"]).toBe("");
          expect(results[0]["Created Date"]).toBe("28 May 2024");
          expect(results[0]["Joining Date"]).toBe("13 Jul 2022");
          expect(results[0]["Exit Date"]).toBe("31 May 2024");
          expect(results[0].Status).toBe("Inactive");
        })
        .on("error", (err) => {
          throw err;
        });
      await expect(page.getByText("Downloaded Successfully!!")).toBeVisible();
    });
  }
);

test1(
  "Impersonation of payee user",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto("/users", { waitUntil: "networkidle" });
    await page
      .getByTestId("<EMAIL> users dd button")
      .click();
    await page.getByRole("button", { name: "Login as user" }).click();
    await page.waitForSelector('text="Hi, Active Payee User 4 Test 4!"', {
      state: "visible",
    });
    await page.getByRole("button", { name: "Exit" }).click();
  }
);
