/* eslint-disable playwright/no-wait-for-timeout */
const {
  bulkUploadFixtures: { test, expect },
} = require("../../../../fixtures");
const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});

test.describe(
  "Bulk Upload Testcases",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test.describe("Bulk Upload - Add new user", () => {
      test("Download template", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        const downloadPromise = page.waitForEvent("download");
        await page.getByRole("button", { name: "Download template" }).click();
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toBe("template.csv");
      });

      test("With hierarchy", async ({ adminPage }) => {
        // Remove Test PlanPayee from db
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/uploaduser_local_with_manager.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        await page.getByRole("button", { name: "Import Users" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start Import Users" }).click();

        await page
          .getByLabel(
            "Enter Email ID of the person who needs to be notified once job is complete"
          )
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page
          .getByText(
            "Import has started. You will be notified by email once complete."
          )
          .waitFor({ state: "visible" });
        await page
          .locator(
            '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
          )
          .first()
          .waitFor({ state: "visible", timeout: 10000 });
        await page.reload({ waitUntil: "networkidle" });
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("<EMAIL>");
        await page
          .getByText("<EMAIL>")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      test("With Circular hierarchy", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/uploaduser_local_with_manager_circular.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("switch").nth(1).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(5000);
        const count = await page
          .getByText("Manager Email: Hierarchy cycle detected.")
          .count();
        expect(count).toBe(2);
      });

      test("With basic details and Payroll details", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/uploaduser_local.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Import Users" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start Import Users" }).click();

        await page
          .getByLabel(
            "Enter Email ID of the person who needs to be notified once job is complete"
          )
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page
          .getByText(
            "Import has started. You will be notified by email once complete."
          )
          .waitFor({ state: "visible" });
        await page
          .locator(
            '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
          )
          .first()
          .waitFor({ state: "visible", timeout: 5000 });
        await page.waitForTimeout(3000);

        await page.reload({ waitUntil: "networkidle" });
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("Planpayee");
        await page
          .getByText("<EMAIL>")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      test("Check error messages", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/uploaduser_local_with_errors.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);

        await page
          .getByText("Country: Invalid country code.")
          .waitFor({ state: "visible", timeout: 5000 });
        await page
          .getByText("Role:")
          .waitFor({ state: "visible", timeout: 5000 });
        await page
          .getByText("Crystal Access: Crystal Access should be Yes or No")
          .waitFor({ state: "visible", timeout: 5000 });
        await page
          .getByText("Pay Currency: Invalid currency code.")
          .waitFor({ state: "visible", timeout: 5000 });
      });

      test("Show only errors state", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/uploaduser_local_with_errors.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        await page
          .getByText("Validation Passed")
          .waitFor({ state: "visible", timeout: 5000 });
        await page.getByRole("switch").first().click();
        await page
          .getByText("Validation Passed")
          .waitFor({ state: "hidden", timeout: 5000 });
      });

      test("Joining date and Effective date validations should be made in circular hierarchy", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-uploads/bulk_upload_cir_check.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("switch").nth(1).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        const count1 = await page
          .getByText("Payroll Effective Start Date:")
          .count();
        expect(count1).toBe(2);
        const count2 = await page
          .getByText("Effective start date should")
          .count();
        expect(count2).toBe(2);
      });

      test("Adding user for a custom calendar should be possible", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Import New Users").click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-uploads/bulk_upload_custom_calendar.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").first().click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(
          await page.getByRole("gridcell", { name: "Test Custom calendar" })
        ).toBeVisible();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
      });
    });

    test.describe("Bulk Upload - Edit existing users", () => {
      test("Download template should download template with selected fields", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByLabel("Employee ID").check();
        await page.getByLabel("Country").check();
        await page.getByLabel("Base Pay").check();
        await page.getByLabel("Variable Pay").check();
        await page.getByLabel("Manager Email").check();
        await page.getByRole("button", { name: "Next" }).click();
        const downloadPromise = page.waitForEvent("download");
        await page.getByRole("button", { name: "Download template" }).click();
        const download = await downloadPromise;
        await expect(await page.getByText("Template Downloaded")).toBeVisible();
        const downloadPath = path.join(
          __dirname,
          "downloads",
          download.suggestedFilename()
        );
        await download.saveAs(downloadPath);
        const results = [];
        fs.createReadStream(downloadPath)
          .pipe(csv())
          .on("data", (data) => {
            const trimmedData = {};
            for (const key in data) {
              trimmedData[key.trim()] = data[key];
            }
            results.push(trimmedData);
          })
          .on("end", () => {
            expect(results.length).toBe(1);
            expect(Object.keys(results[0]).length).toBe(8);
            expect(results[0]["*Email"]).toBe("");
            expect(results[0]["Employee ID"]).toBe("");
            expect(results[0]["*Payroll Effective Start Date"]).toBe("");
            expect(results[0]["*Country"]).toBe("");
            expect(results[0]["Base Pay"]).toBe("");
            expect(results[0]["*Variable Pay"]).toBe("");
            expect(results[0]["*Manager Email"]).toBe("");
            expect(results[0]["*Manager Effective Start Date"]).toBe("");
          })
          .on("error", (err) => {
            throw err;
          });
      });

      test("User should be able to override details of payroll, hierarchy and custom fields", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page
          .getByText("Select the fields you want to update")
          .waitFor({ state: "visible" });
        await page.getByRole("switch").click();
        await page.getByLabel("Employee ID").check();
        await page.getByLabel("Manager Email").check();
        await page.getByLabel("Variable Pay").check();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/bulk-uploads/bulk_edit_override.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
        await page.getByRole("button", { name: "Update" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start" }).click();
        await page
          .getByLabel(
            "Enter Email ID of the person who needs to be notified once job is complete"
          )
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page
          .getByText(
            "Import has started. You will be notified by email once complete."
          )
          .waitFor({ state: "visible" });
        await page
          .locator(
            '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
          )
          .first()
          .waitFor({ state: "visible", timeout: 10000 });
        await page.reload({ waitUntil: "networkidle" });

        await page.getByRole("button", { name: "Payee test" }).click();
        await page.waitForTimeout(3000);
        const empId = await page
          .getByPlaceholder("Enter Employee ID")
          .inputValue();
        const varPay = await page
          .getByPlaceholder("Enter Variable Pay")
          .inputValue();
        expect(await empId).toBe("567");
        expect(await varPay).toBe("6565");
        await page.getByRole("button", { name: "Next" }).click();
        await expect(
          await page.locator("#reporting-mgr").getByText("Power Admin test")
        ).toBeVisible();
      });

      test("User should be able to add details of payroll, hierarchy and custom fields for new effective date", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page
          .getByText("Select the fields you want to update")
          .waitFor({ state: "visible" });
        await page.getByLabel("Basic Details").check();
        await page.getByLabel("Payroll Details").check();
        await page.getByLabel("Reporting Hierarchy").check();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-uploads/bulk_upload_new_payroll.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
        await page.getByRole("button", { name: "Update" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start" }).click();
        await page
          .getByLabel(
            "Enter Email ID of the person who needs to be notified once job is complete"
          )
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page
          .getByText(
            "Import has started. You will be notified by email once complete."
          )
          .waitFor({ state: "visible" });
        await page
          .locator(
            '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
          )
          .first()
          .waitFor({ state: "visible", timeout: 10000 });
        await page.reload({ waitUntil: "networkidle" });

        await page.getByRole("button", { name: "New Payroll" }).click();
        await page.waitForTimeout(3000);
        const empId = await page
          .getByPlaceholder("Enter Employee ID")
          .inputValue();
        const joiningDate = await page
          .getByPlaceholder("Select date")
          .inputValue();
        const designation = await page
          .getByPlaceholder("Enter Designation")
          .inputValue();
        const basePay = await page
          .getByPlaceholder("Enter Base Pay")
          .inputValue();
        const varPay = await page
          .getByPlaceholder("Enter Variable Pay")
          .inputValue();
        expect(await empId).toBe("123");
        expect(await varPay).toBe("1500");
        expect(await basePay).toBe("1500");
        expect(await designation).toBe("Payee");
        expect(await joiningDate).toBe("Jan 01, 2024");
        await expect(
          await page.getByRole("button", {
            name: "Start Date: Jan 05, 2024 End",
          })
        ).toBeVisible();
        await page.getByRole("button", { name: "Next" }).click();
        const managerEffDate = await page
          .getByLabel("Effective Start Date*")
          .inputValue();
        await expect(
          await page.locator("#reporting-mgr").getByText("Power Admin test")
        ).toBeVisible();
        expect(await managerEffDate).toBe("Jan 06, 2024");
      });

      test("User should be able to import only valid record eventhough there are erroneous records", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page
          .getByText("Select the fields you want to update")
          .waitFor({ state: "visible" });
        await page.getByRole("switch").click();
        await page.getByLabel("Employee ID").check();
        await page.getByLabel("Variable Pay").check();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-uploads/bulk_upload_error_and_valid_records.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
        await expect(
          await page.getByText("Email: Email does not exist.")
        ).toBeVisible();
        await page.getByRole("button", { name: "Update" }).click();
        await expect(
          await page.getByText("1 record will be imported")
        ).toBeVisible();
        await expect(
          await page.getByText("1 record skipped due to validation errors")
        ).toBeVisible();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start" }).click();
        await page
          .getByLabel(
            "Enter Email ID of the person who needs to be notified once job is complete"
          )
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page
          .getByText(
            "Import has started. You will be notified by email once complete."
          )
          .waitFor({ state: "visible" });
        await page
          .locator(
            '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
          )
          .first()
          .waitFor({ state: "visible", timeout: 10000 });
        await page.reload({ waitUntil: "networkidle" });

        await page.getByRole("button", { name: "Payee Test" }).click();
        await page.waitForTimeout(2000);
        const empId = await page
          .getByPlaceholder("Enter Employee ID")
          .inputValue();
        const varPay = await page
          .getByPlaceholder("Enter Variable Pay")
          .inputValue();
        expect(await empId).toBe("567");
        expect(await varPay).toBe("2500");
      });

      test("User should be able to download error records", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page
          .getByText("Select the fields you want to update")
          .waitFor({ state: "visible" });
        await page.getByRole("switch").click();
        await page.getByLabel("Employee ID").check();
        await page.getByLabel("Variable Pay").check();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-uploads/bulk_upload_error_and_valid_records.csv"
          );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
        await expect(
          await page.getByText("Email: Email does not exist.")
        ).toBeVisible();
        const downloadPromise = page.waitForEvent("download");
        await page
          .getByRole("button", { name: "Download error list CSV" })
          .click();
        const download = await downloadPromise;
        const downloadPath = path.join(
          __dirname,
          "downloads",
          download.suggestedFilename()
        );
        await download.saveAs(downloadPath);

        const results = [];
        fs.createReadStream(downloadPath)
          .pipe(csv())
          .on("data", (data) => results.push(data))
          .on("end", () => {
            expect(results.length).toBe(1);
            expect(results[0]['﻿"Email"']).toBe("<EMAIL>");
            expect(results[0]["Description"]).toBe(
              "Email: Email does not exist."
            );
          })
          .on("error", (err) => {
            throw err;
          });
      });

      test("Effective date check when user does not select overwrite option", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByRole("checkbox", { name: "Payroll Details" }).click();
        const effectiveStartDateCheckbox = await page
          .locator("div")
          .filter({ hasText: /^Payroll Effective Start Date$/ })
          .locator("span")
          .first();
        expect(await effectiveStartDateCheckbox.isDisabled()).toBe(true);
        await page.getByRole("switch").first().click();
        expect(await effectiveStartDateCheckbox.isDisabled()).toBe(false);
        await page.getByRole("switch").first().click();
        expect(await effectiveStartDateCheckbox.isDisabled()).toBe(true);
      });

      test("Error when tried to edit a non added user", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByRole("checkbox", { name: "Payroll Details" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/bulk-uploads/bulk_edit_not_added.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        await expect(
          await page.getByText("Email: Email does not exist.")
        ).toBeVisible();
      });

      test("Is user able to update values using AgGrid and revalidate", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByRole("checkbox", { name: "Payroll Details" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/bulk-uploads/bulk_edit_not_added.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        await expect(
          await page.getByText("Email: Email does not exist.")
        ).toBeVisible();
        const invalidEmailCell = await page.getByRole("gridcell", {
          name: "<EMAIL>",
        });
        await invalidEmailCell.dblclick();
        const cellInput = await page.getByLabel("Input Editor");
        await cellInput.fill("<EMAIL>");
        await cellInput.press("Enter");
        await page.getByRole("button", { name: "Revalidate" }).click();
        await expect(await page.getByText("Validation Passed")).toBeVisible();
      });

      test("Is power admin able to edit the joining date on bulk upload ", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByRole("checkbox", { name: "Joining Date" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/bulk-uploads/update_joining_date.csv");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(3000);
        await expect(await page.getByText("Validation Passed")).toBeVisible();
        await page.getByRole("button", { name: "Update" }).click();
        await page.getByRole("button", { name: "Yes" }).click();
        await page.getByRole("button", { name: "Start Import" }).click();
        await page
          .getByPlaceholder("Email", { exact: true })
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page.waitForTimeout(3000);
        await page.reload({ waitUntil: "networkidle" });
        await page.getByRole("button", { name: "Payee test" }).click();
        await page.waitForTimeout(3000);
        const joiningDateDiv = await page
          .locator("div")
          .filter({ hasText: /^Joining Date\*$/ })
          .nth(1)
          .locator("input");

        expect(await joiningDateDiv.inputValue()).toBe("Jan 01, 2024");
      });
    });

    test.describe("Draws", () => {
      test("Add/modify draws should open draws module", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Payee test" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Add/Modify" }).nth(1).click();
        await page.getByRole("button", { name: "Add Schedule" }).click();
        await expect(await page.getByText("Period*").nth(1)).toBeVisible();
        await expect(await page.getByText("Draw type*").nth(1)).toBeVisible();
        await expect(await page.getByText("Draw amount*").nth(1)).toBeVisible();
      });

      test("Added draws should be displayed in Team and quota section", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Payee test" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Add/Modify" }).nth(1).click();
        // await page.getByRole("button", { name: "Add Schedule" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Period$/ })
          .nth(2)
          .click();
        await page.locator('span[title="Mar"]').last().click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Type$/ })
          .nth(2)
          .click();
        await page.getByText("Recoverable", { exact: true }).click();
        await page.getByPlaceholder("Enter Amount").nth(0).click();
        const value = generateRandomFourDigitNumber();
        await page
          .getByPlaceholder("Enter Amount")
          .nth(0)
          .fill(value.toString());
        await page.getByPlaceholder("Enter Amount").nth(0).press("Enter");
        await page.getByRole("button", { name: "Save" }).click();
        await expect(
          await page.getByText("Draws Added Successfully")
        ).toBeVisible();
        await page.goto("/teams", { waitUntil: "networkidle" });
        await page.getByText("Power Admin test").click();
        await page
          .getByTestId("<EMAIL>")
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(
          await page.getByRole("cell", { name: "Recoverable", exact: true })
        ).toBeVisible();
        await expect(
          await page.getByRole("cell", { name: value.toString() })
        ).toBeVisible();
      });
    });

    test.describe("Quota", () => {
      test("Add/modify quota should open quota module", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Payee test" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Add/Modify" }).first().click();
        await expect(await page.getByText("Add Quota")).toBeVisible();
        await expect(await page.getByText("Quota Category")).toBeVisible();
        await expect(await page.getByText("Select Category")).toBeVisible();
        await expect(await page.getByPlaceholder("Select Year")).toBeVisible();
      });

      test("Added quota should be displayed in Team and quota section", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Payee test" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Add/Modify" }).first().click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Category$/ })
          .nth(3)
          .click();
        await page.getByRole("button", { name: "Add quota category" }).click();
        await page.getByPlaceholder("Enter category name").click();
        const newQuotaName = "Payee Quota 2";
        await page.getByPlaceholder("Enter category name").type(newQuotaName);
        await page
          .getByLabel("Add quota category")
          .getByRole("button", { name: "Save" })
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Category$/ })
          .nth(3)
          .click();
        await page.waitForTimeout(1000);
        await page.locator(`div[label='${newQuotaName}']`).click();
        await page.getByPlaceholder("Select Year").click();
        await page.getByTitle("2024").locator("div").click();
        await page.getByText("Enter new quota values").click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Quota$/ })
          .nth(2)
          .click();
        await page
          .locator("span")
          .filter({ hasText: /^Monthly$/ })
          .click();
        await page.waitForTimeout(2000);
        await page
          .locator("div")
          .filter({ hasText: /^Select Schedule frequency$/ })
          .nth(2)
          .click();
        await page.getByRole("listitem").getByText("Monthly").click();
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByRole("button", { name: "Done" }).click();
        await page.goto("/quotas", { waitUntil: "networkidle" });
        await page.getByPlaceholder("Search by name or email").click();
        await page
          .getByPlaceholder("Search by name or email")
          .fill("Payee Test");
        await page.getByPlaceholder("Search by name or email").press("Enter");
        await page.getByRole("listitem").getByText("Payee test").click();
        await page.getByTestId("pt-quota-selector").click();
        await expect(
          await page.locator(`span[title='${newQuotaName}']`).first()
        ).toBeVisible();
      });
    });
  }
);

function generateRandomFourDigitNumber() {
  const randomNum = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
  return randomNum;
}
