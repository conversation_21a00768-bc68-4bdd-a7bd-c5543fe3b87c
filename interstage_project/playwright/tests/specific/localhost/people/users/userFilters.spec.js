const UserPage = require("../../../../../test-objects/user-objects");
const {
  qaFixtures: { test, expect },
} = require("../../../../fixtures");
const {
  localCloneFixtures: { test: test1 },
} = require("../../../../fixtures");
const userCount = 28;

test.describe(
  "Users Screen Filters Validation",
  { tag: ["@regression", "@user", "@adminchamp-2"] },
  () => {
    test(
      "Validate Filter Section",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating whether the expected Dropdowns and Textbox are available in the Filters section",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Expected Dropdowns and Textbox should be available in the Filters section",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        await userPage.validateDropdownValues("status", [
          "Added/ Not Invited",
          "Invited",
          "Active",
          "Marked for exit",
          "Inactive",
        ]);
        await userPage.validateDropdownValues("payoutFrequency", [
          "Monthly",
          "Quarterly",
          "Half-yearly",
          "Annual",
        ]);
        await userPage.validateDropdownValues("mappingStatus", [
          "Mapped",
          "Unmapped",
        ]);
        await userPage.validateDropdownValues("userSource", [
          "Manually managed",
          "Managed by integrations",
        ]);
        await userPage.validateDropdownValues("reportingManager", [
          "Admin1 Payee",
          "Hardik Pandya",
          "Cazin Randie",
        ]);
        await userPage.validateDropdownValues("employmentCountry", [
          "India",
          "United States Of America",
          "Canada",
          "Netherlands",
          "United Kingdom",
          "France",
          "Australia",
          "Albania",
        ]);
        await userPage.validateDropdownValues("payCurrency", [
          "INR",
          "USD",
          "CAD",
          "EUR",
          "GBP",
          "AUD",
          "ALL",
        ]);
        await userPage.validateDropdownValues("commissionPlan", [
          "simpleplan",
          "conditionalplan",
        ]);
        await userPage.validateTextBox("Designation", [
          "Contains",
          "Does not Contain",
          "Ends With",
          "Equal To",
          "In",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
          "Not In",
          "Starts With",
        ]);
        await userPage.validateTextBox("Joining Date", [
          "In Between",
          "Greater Than",
          "Greater than or Equal to",
          "Less than",
          "Less than or Equal to",
        ]);
        await userPage.validateTextBox("Exit Date", [
          "In Between",
          "Greater Than",
          "Greater than or Equal to",
          "Less than",
          "Less than or Equal to",
        ]);
        await userPage.addMoreFilters(["Employee ID"]);
        await userPage.validateTextBox("Employee ID", [
          "Contains",
          "Does not Contain",
          "Ends With",
          "Equal To",
          "In",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
          "Not In",
          "Starts With",
        ]);
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );
    test(
      "Validate that when applying User Status dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        // Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["User StatusInAdded"]);
        await userPage.validateUserCount(17, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "User StatusInAdded, Invited",
        ]);
        await userPage.validateUserCount(22, true);
        await userPage.openFilters();
        await userPage.removeFilter(["Added/ Not Invited"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(5, true);
        await userPage.validateLabelinUsersScreen(["User StatusInInvited"]);
        await userPage.removeFilterFromUserScreen(["User StatusInInvited"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("status", "Not In");
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["User StatusNot InAdded"]);
        await userPage.validateUserCount(11, false);
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["User StatusNot InAdded,"]);
        await userPage.validateUserCount(6, false);
        await userPage.openFilters();
        await userPage.removeFilter(["Added/ Not Invited"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(23, false);
        await userPage.validateLabelinUsersScreen(["User StatusNot InInvited"]);
        await userPage.removeFilterFromUserScreen(["User StatusNot InInvited"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );
    test(
      "Validate that when applying Payout Frequency dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "When filter is applied, the expected results should be displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyInMonthly",
        ]);
        await userPage.validateUserCount(8, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("payoutFrequency", "Quarterly");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyInMonthly,",
        ]);
        await userPage.validateUserCount(13, true);
        await userPage.openFilters();
        await userPage.removeFilter(["Monthly"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(5), true;
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyInQuarterly",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Payout FrequencyInQuarterly",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("payoutFrequency", "Not In");
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyNot InMonthly",
        ]);
        await userPage.validateUserCount(9, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("payoutFrequency", "Quarterly");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyNot InMonthly",
        ]);
        await userPage.validateUserCount(4, true);
        await userPage.openFilters();
        await userPage.removeFilter(["Monthly"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(12);
        await userPage.validateLabelinUsersScreen([
          "Payout FrequencyNot InQuarterly",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Payout FrequencyNot InQuarterly",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Mapping Status dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("mappingStatus", "Mapped");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["Mapping StatusInMapped"]);
        await userPage.validateUserCount(6, true);
        await userPage.removeFilterFromUserScreen(["Mapping StatusInMapped"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("mappingStatus", "Not In");
        await userPage.selectDropdownValue("mappingStatus", "Mapped");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Mapping StatusNot InMapped",
        ]);
        await userPage.validateUserCount(22, false);
        await userPage.removeFilterFromUserScreen([
          "Mapping StatusNot InMapped",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying User Source dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("userSource", "Manually managed");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "User SourceInManually managed",
        ]);
        await userPage.validateUserCount(20, true);
        await userPage.removeFilterFromUserScreen([
          "User SourceInManually managed",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("userSource", "Not In");
        await userPage.selectDropdownValue("userSource", "Manually managed");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "User SourceNot InManually managed",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.removeFilterFromUserScreen([
          "User SourceNot InManually managed",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Reporting Manager dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("reportingManager", "Admin1 Payee");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerInAdmin1 Payee",
        ]);
        await userPage.validateUserCount(7, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("reportingManager", "Hardik Pandya");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerInAdmin1 Payee, Hardik Pandya",
        ]);
        await userPage.validateUserCount(14, true);
        await userPage.openFilters();
        await userPage.removeFilter(["Admin1 Payee"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(7, true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerInHardik Pandya",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Reporting ManagerInHardik Pandya",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("reportingManager", "Not In");
        await userPage.selectDropdownValue("reportingManager", "Admin1 Payee");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerNot InAdmin1 Payee",
        ]);
        await userPage.validateUserCount(8, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("reportingManager", "Hardik Pandya");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerNot InAdmin1 Payee, Hardik Pandya",
        ]);
        await userPage.validateUserCount(1, true);
        await userPage.openFilters();
        await userPage.removeFilter(["Admin1 Payee"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(8, true);
        await userPage.validateLabelinUsersScreen([
          "Reporting ManagerNot InHardik Pandya",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Reporting ManagerNot InHardik Pandya",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Employement country dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("employmentCountry", "India");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["Employment CountryInIND"]);
        await userPage.validateUserCount(10, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("employmentCountry", "France");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Employment CountryInIND, FRA",
        ]);
        await userPage.validateUserCount(14, true);
        await userPage.openFilters();
        await userPage.removeFilter(["India"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(4, true);
        await userPage.validateLabelinUsersScreen(["Employment CountryInFRA"]);
        await userPage.removeFilterFromUserScreen(["Employment CountryInFRA"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("employmentCountry", "Not In");
        await userPage.selectDropdownValue("employmentCountry", "India");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Employment CountryNot InIND",
        ]);
        await userPage.validateUserCount(7, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("employmentCountry", "France");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Employment CountryNot InIND, FRA",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.openFilters();
        await userPage.removeFilter(["India"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(13, true);
        await userPage.validateLabelinUsersScreen([
          "Employment CountryNot InFRA",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Employment CountryNot InFRA",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Payout Currency dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("payCurrency", "INR");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["Payout CurrencyInINR"]);
        await userPage.validateUserCount(12, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("payCurrency", "USD");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout CurrencyInINR, USD",
        ]);
        await userPage.validateUserCount(16, true);
        await userPage.openFilters();
        await userPage.removeFilter(["INR"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(4, true);
        await userPage.validateLabelinUsersScreen(["Payout CurrencyInUSD"]);
        await userPage.removeFilterFromUserScreen(["Payout CurrencyInUSD"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("payCurrency", "Not In");
        await userPage.selectDropdownValue("payCurrency", "INR");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["Payout CurrencyNot InINR"]);
        await userPage.validateUserCount(5, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("payCurrency", "USD");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Payout CurrencyNot InINR, USD",
        ]);
        await userPage.validateUserCount(1, true);
        await userPage.openFilters();
        await userPage.removeFilter(["INR"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(13, true);
        await userPage.validateLabelinUsersScreen(["Payout CurrencyNot InUSD"]);
        await userPage.removeFilterFromUserScreen(["Payout CurrencyNot InUSD"]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Commission Plan dropdown field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownValue("commissionPlan", "simpleplan");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansInsimpleplan",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.openFilters();
        await userPage.selectDropdownValue("commissionPlan", "conditionalplan");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansInsimpleplan, conditionalplan",
        ]);
        await userPage.validateUserCount(6);
        await userPage.openFilters();
        await userPage.removeFilter(["simpleplan"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(3, true);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansInconditionalplan",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Commission PlansInconditionalplan",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
        //Validating Not In Dropdown Values
        await userPage.openFilters();
        await userPage.selectDropdownOption("commissionPlan", "Not In");
        await userPage.selectDropdownValue("commissionPlan", "simpleplan");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansNot Insimpleplan",
        ]);
        await userPage.validateUserCount(25, false);
        await userPage.openFilters();
        await userPage.selectDropdownValue("commissionPlan", "conditionalplan");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansNot Insimpleplan, conditionalplan",
        ]);
        await userPage.validateUserCount(22);
        await userPage.openFilters();
        await userPage.removeFilter(["simpleplan"]);
        await userPage.applyFilters(true);
        await userPage.validateUserCount(25, false);
        await userPage.validateLabelinUsersScreen([
          "Commission PlansNot Inconditionalplan",
        ]);
        await userPage.removeFilterFromUserScreen([
          "Commission PlansNot Inconditionalplan",
        ]);
        await userPage.validateUserCount(userCount, false);
        await userPage.openFilters();
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate that when applying Designation Textfield, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        //Validating Designation Textfield - Contains
        await userPage.enterTextBox("Designation", "Contains", "Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["DesignationContainsTest"]);
        await userPage.validateUserCount(6, true);
        await userPage.removeFilterFromUserScreen(["DesignationContainsTest"]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Does Not Contain
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Does not Contain", "Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationDoes not ContainTest",
        ]);
        await userPage.validateUserCount(11, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationDoes not ContainTest",
        ]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Ends With
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Ends With", "Payee");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationEnds WithPayee",
        ]);
        await userPage.validateUserCount(7, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationEnds WithPayee",
        ]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Equal To
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Equal To", "Payee Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationEqual ToPayee Test",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationEqual ToPayee Test",
        ]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Equal To
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Equal To", "Payee Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationEqual ToPayee Test",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationEqual ToPayee Test",
        ]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Is Empty
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Is Empty", "", true);
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["DesignationIs Empty"]);
        await userPage.validateUserCount(12, false);
        await userPage.removeFilterFromUserScreen(["DesignationIs Empty"]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Is Not Empty
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Is Not Empty", "", true);
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen(["DesignationIs Not Empty"]);
        await userPage.validateUserCount(16, true);
        await userPage.removeFilterFromUserScreen(["DesignationIs Not Empty"]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Not Equal To
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Not Equal To", "Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationNot Equal ToTest",
        ]);
        await userPage.validateUserCount(17, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationNot Equal ToTest",
        ]);
        await userPage.validateUserCount(userCount, false);
        //Validating Designation Textfield - Starts With
        await userPage.openFilters();
        await userPage.enterTextBox("Designation", "Starts With", "Test");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "DesignationStarts WithTest",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.removeFilterFromUserScreen([
          "DesignationStarts WithTest",
        ]);
        await userPage.validateUserCount(userCount, false);
      }
    );

    test(
      "Validate that when applying Joining Date Field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        // validating Joining Date Field - In Between
        await userPage.openFilters();
        await userPage.enterDateFields(
          "Joining Date",
          "In Between",
          "Jan 01, 2024",
          "Jan 01, 2025"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Joining DateIn BetweenJan 01, 2024 to Jan 01, 2025",
        ]);
        await userPage.validateUserCount(10, true);
        await userPage.clearAll();
        await userPage.validateUserCount(userCount, false);
        // validating Joining Date Field - Greater Than
        await userPage.openFilters();
        await userPage.enterDateField(
          "Joining Date",
          "Greater Than",
          "Jul 01, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Joining DateGreater ThanJul 01, 2024",
        ]);
        await userPage.validateUserCount(2, true);
        await userPage.removeFilterFromUserScreen([
          "Joining DateGreater ThanJul 01, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        // validating Joining Date Field - Greater Than or Equal To
        await userPage.openFilters();
        await userPage.enterDateField(
          "Joining Date",
          "Greater than or Equal to",
          "Jul 01, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Joining DateGreater than or Equal toJul 01, 2024",
        ]);
        await userPage.validateUserCount(6, true);
        await userPage.removeFilterFromUserScreen([
          "Joining DateGreater than or Equal toJul 01, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        // validating Joining Date Field - Less Than
        await userPage.openFilters();
        await userPage.enterDateField(
          "Joining Date",
          "Less than",
          "Jul 01, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Joining DateLess thanJul 01, 2024",
        ]);
        await userPage.validateUserCount(11, true);
        await userPage.removeFilterFromUserScreen([
          "Joining DateLess thanJul 01, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        // validating Joining Date Field - Less Than or Equal To
        await userPage.openFilters();
        await userPage.enterDateField(
          "Joining Date",
          "Less than or Equal to",
          "Jul 01, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Joining DateLess than or Equal toJul 01, 2024",
        ]);
        await userPage.validateUserCount(15, true);
        await userPage.removeFilterFromUserScreen([
          "Joining DateLess than or Equal toJul 01, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
      }
    );

    test(
      "Validate that when applying Exit Date Field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //validating Joining Date Field - In Between
        await userPage.openFilters();
        await userPage.enterDateFields(
          "Exit Date",
          "In Between",
          "Jan 01, 2024",
          "Jan 01, 2025"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Exit DateIn BetweenJan 01, 2024 to Jan 01, 2025",
        ]);
        await userPage.validateUserCount(3, true);
        await userPage.removeFilterFromUserScreen([
          "Exit DateIn BetweenJan 01, 2024 to Jan 01, 2025",
        ]);
        await userPage.validateUserCount(userCount, false);
        //validating Exit Date Field - Greater Than
        await userPage.openFilters();
        await userPage.enterDateField(
          "Exit Date",
          "Greater Than",
          "Sep 30, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Exit DateGreater ThanSep 30, 2024",
        ]);
        await userPage.validateUserCount(1, true);
        await userPage.removeFilterFromUserScreen([
          "Exit DateGreater ThanSep 30, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        //validating Exit Date Field - Greater Than or Equal To
        await userPage.openFilters();
        await userPage.enterDateField(
          "Exit Date",
          "Greater than or Equal to",
          "Sep 30, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Exit DateGreater than or Equal toSep 30, 2024",
        ]);
        await userPage.validateUserCount(2, true);
        await userPage.removeFilterFromUserScreen([
          "Exit DateGreater than or Equal toSep 30, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        //validating Exit Date Field - Less Than
        await userPage.openFilters();
        await userPage.enterDateField("Exit Date", "Less than", "Sep 30, 2024");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Exit DateLess thanSep 30, 2024",
        ]);
        await userPage.validateUserCount(1, true);
        await userPage.removeFilterFromUserScreen([
          "Exit DateLess thanSep 30, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
        //validating Exit Date Field - Less Than or Equal To
        await userPage.openFilters();
        await userPage.enterDateField(
          "Exit Date",
          "Less than or Equal to",
          "Sep 30, 2024"
        );
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "Exit DateLess than or Equal toSep 30, 2024",
        ]);
        await userPage.validateUserCount(2, true);
        await userPage.removeFilterFromUserScreen([
          "Exit DateLess than or Equal toSep 30, 2024",
        ]);
        await userPage.validateUserCount(userCount, false);
      }
    );

    test(
      "Validate the clear button in Filters dropdown",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating when clear button is clicked in the User Filters , Selected value should be unselected and label also should be removed",
          },
          {
            type: "Expected Behaviour",
            description:
              "When clear button is clicked in the User Filters , Selected value should be unselected and label also should be removed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.selectDropdownValue("status", "Active");
        await userPage.clearFromFilters([
          "Added/ Not Invited",
          "Invited",
          "Active",
        ]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.selectDropdownValue("payoutFrequency", "Quarterly");
        await userPage.clearFromFilters(["Monthly", "Quarterly"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("mappingStatus", "Mapped");
        await userPage.clearFromFilters(["Mapped"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("userSource", "Manually managed");
        await userPage.clearFromFilters(["Manually managed"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("reportingManager", "Admin1 Payee");
        await userPage.clearFromFilters(["Admin1 Payee"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("employmentCountry", "India");
        await userPage.clearFromFilters(["India"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("payCurrency", "INR");
        await userPage.clearFromFilters(["INR"]);
        await userPage.closeFilters();
        await userPage.openFilters();
        await userPage.selectDropdownValue("commissionPlan", "simpleplan");
        await userPage.clearFromFilters(["simpleplan"]);
        await userPage.closeFilters();
      }
    );

    test(
      "Validate the clear all button in Users screen",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating when clear all button is clicked , Applied filter is removed and all users are displaying",
          },
          {
            type: "Expected Behaviour",
            description:
              " when clear all button is clicked , Applied filter should be removed and all users should be displayed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.selectDropdownValue("mappingStatus", "Mapped");
        await userPage.selectDropdownValue("mappingStatus", "Unmapped");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "User StatusInAdded",
          "Payout FrequencyInMonthly",
          "Mapping StatusInMapped, Unmapped",
        ]);
        await userPage.validateUserCount(6, true);
        await userPage.searchInFilteredResults("testpayee");
        await userPage.validateUserCount(2, false);
        await userPage.clearAll();
        await userPage.validateUserCount(12, false);
        await userPage.labelRemovedInUsersScreen([
          "User StatusInAdded",
          "Payout FrequencyInMonthly",
          "Mapping StatusInMapped, Unmapped",
        ]);
      }
    );

    test(
      "Validate that clicking the Cancel button removes any unsaved filters from the drawer",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validate that clicking the Cancel button removes any unsaved filters from the drawer",
          },
          {
            type: "Expected Behaviour",
            description:
              "Clicking the Cancel button should remove any unsaved filters from the drawer",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.selectDropdownValue("mappingStatus", "Mapped");
        await userPage.clickCancelButton();
        await userPage.openFilters();
        await userPage.labelRemovedCheck("Added/ Not Invited");
        await userPage.labelRemovedCheck("Monthly");
        await userPage.labelRemovedCheck("Mapped");
      }
    );

    test(
      "Empty results in NOT-IN / Not Equal to / Does not Contain condition",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validate whether Empty results are displayed in NOT-IN condition",
          },
          {
            type: "Expected Behaviour",
            description:
              "Empty results should be displayed in NOT-IN condition",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //Validating empty results in Not In Condition
        await userPage.openFilters();
        await userPage.selectDropdownOption("status", "Not In");
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.selectDropdownValue("status", "Active");
        await userPage.selectDropdownValue("status", "Marked for exit");
        await userPage.selectDropdownValue("status", "Inactive");
        await userPage.applyFilters(true);
        await userPage.validateEmptyResult();
        await userPage.clearAll();
      }
    );
  }
);

test1.describe(
  "Validate user filter section for custom terminology and custom fields",
  { tag: ["@regression", "@user", "@adminchamp-2"] },
  () => {
    test1(
      "Validate Custom Terminology Function In User Filters Section",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating whether the change in Terminology is getting displayed in user Filter section",
          },
          {
            type: "Expected Behaviour",
            description:
              "change in Terminology should get displayed in user Filter section",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.modifyCustomTerminology("Commission", "Test");
        await userPage.navigateToUser();
        await userPage.openFilters();
        await expect(
          await page.getByText("Test Plans", { exact: true })
        ).toBeVisible();
        await userPage.modifyCustomTerminology("Commission", "Commission");
      }
    );
    test1(
      "Validate Custom Field added are displayed in the User Filter Section",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating custom fields added in the Settings is displayed under more field section in User Filter",
          },
          {
            type: "Expected Behaviour",
            description:
              "Custom fields added in the Settings should be displayed under more field section in User Filter",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.openFilters();
        await userPage.addMoreFilters(["test", "Region"]);
        await userPage.validateDropdownValues("cf_1_region", [
          "Asia",
          "Africa",
          "South America",
          "North America",
          "Europe",
        ]);
        await userPage.validateTextBox("Test", [
          "Contains",
          "Does not Contain",
          "Ends With",
          "Equal To",
          "In",
          "Is Empty",
          "Is Not Empty",
          "Not Equal To",
          "Not In",
          "Starts With",
        ]);
        await userPage.applyFilters(false);
        await userPage.closeFilters();
      }
    );

    test1(
      "Validate Custom Fields filter along with default filter",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating when custom field filter is applied with default filters, Results are displayed as expected",
          },
          {
            type: "Expected Behaviour",
            description:
              "Custom field filter is applied with default filters, Results shoould be displayed as expected",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        await userPage.openFilters();
        await userPage.addMoreFilters(["Region"]);
        await userPage.selectDropdownValue("status", "Active");
        await userPage.selectDropdownValue("cf_1_region", "Asia");
        await userPage.applyFilters(true);
        await userPage.validateLabelinUsersScreen([
          "User StatusInActive",
          "RegionInAsia",
        ]);
        await userPage.validateUserCount(1, true);
      }
    );

    test1(
      "Validate Bulk Actions in Applied Filter results",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validating whether user is able to do bulk actions on applied filter results",
          },
          {
            type: "Expected Behaviour",
            description:
              "user should able to do bulk actions on applied filter results",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.increasePagination();
        //validating Bulk Invite
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.applyFilters(true);
        await userPage.bulkInvite();
        await userPage.clearAll();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.selectDropdownValue("payoutFrequency", "Monthly");
        await userPage.applyFilters(true);
        await userPage.validateUserCount(19, false);
        await userPage.clearAll();
        //Validating Bulk Set Source
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.selectDropdownValue("userSource", "Manually managed");
        await userPage.applyFilters(true);
        await userPage.bulkSetSource();
        await userPage.clearAll();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.selectDropdownValue(
          "userSource",
          "Managed by integrations"
        );
        await userPage.applyFilters(true);
        await userPage.validateUserCount(3, false);
      }
    );

    test1(
      "Validate whether pagination works after the filters are being applied",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validate whether pagination works after the filters are being applied",
          },
          {
            type: "Expected Behaviour",
            description:
              "pagination should works after the filters are being applied",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.selectDropdownValue("status", "Invited");
        await userPage.applyFilters(true);
        await userPage.validatePagination();
      }
    );

    test1(
      "Validate whether user have an access to group and applied filter, user is able to view the restricted users",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validate whether user have a limited access able to view the restricted amount of users when applied filter",
          },
          {
            type: "Expected Behaviour",
            description: "User should be able to view the expected results",
          },
        ],
      },
      async ({ payeePage }) => {
        const page = payeePage.page;
        const userPage = new UserPage(page);
        await userPage.navigateToUser();
        await userPage.openFilters();
        await userPage.selectDropdownValue("status", "Added/ Not Invited");
        await userPage.applyFilters(true);
        await userPage.validateUserCount(3, false);
      }
    );
  }
);
