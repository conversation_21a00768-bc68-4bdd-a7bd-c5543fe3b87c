import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import PayoutsReportPage from "../../../../test-objects/payouts-report-objects";

const {
  datasheetV2Fixtures: { test, expect },
} = require("../../../fixtures");

test.describe(
  "Payouts Report Object",
  { tag: ["@datasheet", "@regression", "@adminchamp-4"] },
  () => {
    test(
      "Payouts report object columns validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: [
              "INTER-T26380,INTER-T26394,INTER-T26393,INTER-T26392,INTER-T26391,INTER-T26390",
              "INTER-T26389,INTER-T26388,INTER-T26387,INTER-T26386,INTER-T26385,INTER-T26384,INTER-T26383",
              "INTER-T26382,INTER-T26381,INTER-T26365,INTER-T26379,,INTER-T26377,INTER-T26376,INTER-T26375",
              "INTER-T26374,INTER-T26373,INTER-T26372,INTER-T26371,INTER-T26370,INTER-T26369,INTER-T26368,INTER-T26367,INTER-T26366",
            ],
          },
          {
            type: "Description",
            description: [
              "Ensure that all the columns required are present in the Payouts report object.",
              "Ensure that the columns named as 'Payee Email', 'Payout Period Start Date', 'Payout Period End Date' are set as Primary keys",
            ].join("&#13;&#10;"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "All the columns required should present and some as Primary keys in the report object",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "all_data";
        await payoutsROpage.goToDatasheetView(viewId);
        await page.getByRole("button", { name: "Edit" }).click();
        await page.waitForLoadState("networkidle");
        const expectedColumnNames = [
          "Payee EmailPrimary",
          "Payout Period End DatePrimary",
          "Payout Period Start DatePrimary",
          "Approval Status",
          "Arrear Processed Amount (Payee Currency)",
          "Commission Adjustment Amount (Payee Currency)",
          "Commission Percentage",
          "Commission Plan Amount (Payee Currency)",
          "Conversion Rate",
          "Draw Adjustment Amount (Payee Currency)",
          "Ignored Amount (Payee Currency)",
          "Last Paid Date",
          "Lock Status",
          "Paid Amount (Org Currency)",
          "Paid Amount (Payee Currency)",
          "Payable Amount (Org Currency)",
          "Payable Amount (Payee Currency)",
          "Payee Name",
          "Payee Status",
          "Payout Currency",
          "Payout Status",
          "Pending Amount (Payee Currency)",
          "Primary Commission Plans",
          "Processed Amount (Payee Currency)",
          "Spiff Plan Amount (Payee Currency)",
          "Spiff Plans",
          "Statement Approved Date",
          "Statement Locked Date",
          "Statement Period",
          "Variable Pay (Org Currency)",
          "Variable Pay (Payee Currency)",
        ];
        await payoutsROpage.expectColumnsToBe(expectedColumnNames);
      }
    );
    test(
      "Lock status columns validation comparing with payouts",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T26337,INTER-T26341,INTER-T26342",
          },
          {
            type: "Description",
            description: [
              "Validate that locking the payee in payouts staleness should be shown in payouts report object sheet.",
              "Verify that locking the payee in payouts lock status should be 'True'.",
              "Verify that locking the payee in payouts statement locked date should be the date when lock is made.",
            ].join("\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, lock status, statement locked date should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "283d6927-4249-4262-8722-890b215accc7";
        const payeeEmail = "<EMAIL>";
        const statusBeforeLock = "False";
        const statusAfterLock = "True";
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        await payoutsROpage.goToDatasheetView(viewId);
        const oldLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        expect(oldLockStatus).toBe(statusBeforeLock);
        await payoutsROpage.lockOrUnlockStatements(
          "Lock Statements",
          payeeEmail
        );
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        await page.waitForLoadState("networkidle");
        const newLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        expect(newLockStatus).toBe(statusAfterLock);
      }
    );

    test(
      "Unlock status validation comparing with payouts",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T26338,INTER-T26343,INTER-T26344",
          },
          {
            type: "Description",
            description: [
              "Validate that unlocking the payee in payouts staleness should be shown in payouts report object sheet.",
              "Verify that unlocking the payee in payouts lock status should be 'False'.",
              "Verify that unlocking the payee in payouts statement locked date should be empty.",
            ].join("\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, lock status, statement locked date should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "0717a4d8-7c4d-44c7-bebd-d42d98b19bae";
        const payeeEmail = "<EMAIL>";
        const statusBeforeUnLock = "True";
        const statusAfterUnLock = "False";
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        await payoutsROpage.goToDatasheetView(viewId);
        const oldLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        expect(oldLockStatus).toBe(statusBeforeUnLock);
        await payoutsROpage.lockOrUnlockStatements(
          "Unlock Statements",
          payeeEmail
        );
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        await page.waitForLoadState("networkidle");
        const newLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        expect(newLockStatus).toBe(statusAfterUnLock);
      }
    );

    test(
      "Registering payment in payouts - Payouts report status column validations",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T26345,INTER-T26347,INTER-T26348,INTER-T26349,INTER-T26408",
          },
          {
            type: "Description",
            description: [
              "Ensure that registering payments for a payee in payouts staleness should be shown in payouts report object sheet",
              "Ensure that registering payment for a payee in payouts payment status should be changed to 'Paid'.",
              "Ensure that registering payment for a payee in payouts paid amount for org and payee currencies should be updated from 0 to the respective amount paid.",
              "Ensure that registering payment for a payee in payouts, pending amount for org and payee currencies should be updated from the respective amount pending to 0.",
              "Ensure that registering payment for a payee in payouts, last paid date should be the date when payment is registered.",
            ].join("\r\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, payment status, paid amount, pending amount, last paid date should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "b7e5b53f-8612-402e-a687-af0e8752c76a";
        const statusBeforePayment = "Unpaid";
        const statusAfterPayment = "Paid";
        const paidAmountBeforePayment = 0;
        const paidAmountAfterPayment = 967510.53;
        const pendingAmountBeforePayment = 967510.53;
        const pendingAmountAfterPayment = 0;
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        await payoutsROpage.goToDatasheetView(viewId);
        const oldPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const oldPaidAmount = await payoutsROpage.getStatusValue(
          "paid_amount_payee_currency"
        );
        const oldPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        const oldPaidDate = await payoutsROpage.getStatusValue(
          "last_paid_date"
        );
        expect(oldPaidDate).toBeFalsy();
        expect(oldPayoutStatus).toBe(statusBeforePayment);
        expect(oldPaidAmount).toBe(paidAmountBeforePayment);
        expect(oldPendingAmount).toBe(pendingAmountBeforePayment);
        await payoutsROpage.registerPayment();
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        await page.waitForLoadState("networkidle");
        const newPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const newPaidAmount = await payoutsROpage.getStatusValue(
          "paid_amount_payee_currency"
        );
        const newPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        const newPaidDate = await payoutsROpage.getStatusValue(
          "last_paid_date"
        );
        const currentDate = await payoutsROpage.getCurrentFormattedDate();
        expect(newPaidDate).toBe(currentDate);
        expect(newPayoutStatus).toBe(statusAfterPayment);
        expect(newPaidAmount).toBe(paidAmountAfterPayment);
        expect(newPendingAmount).toBe(pendingAmountAfterPayment);
      }
    );

    test(
      "Invalidating payment in payouts - Payouts report status column validations",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T26350,INTER-T26351,INTER-T26352,INTER-T26353,INTER-T26409",
          },
          {
            type: "Description",
            description: [
              "Ensure that invalidating payment for a payee in payouts staleness should be shown in payouts report object sheet",
              "Ensure that invalidating payment for a payee in payouts payment status should be changed to 'Unpaid'.",
              "Ensure that invalidating payment for a payee in payouts paid amount for org and payee currencies should be updated from the respective amount already paid to 0.",
              "Ensure that invalidating payment for a payee in payouts, pending amount for org and payee currencies should be updated from 0 to the respective amount already paid.",
              "Ensure that invalidating payment for a payee in payouts, last paid date should be updated to empty.",
            ].join("\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, payment status, paid amount, pending amount, last paid date should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "0d9f221d-a8d4-4e12-9310-49f1531ace00";
        const statusBeforeInvalidation = "Paid";
        const statusAfterInvalidation = "Unpaid";
        const paidAmountBeforeInvalidation = 531324.33;
        const paidAmountAfterInvalidation = 0;
        const pendingAmountBeforeInvalidation = 0;
        const pendingAmountAfterInvalidation = 531324.33;
        const lastPaidDateBeforeInvalidation = "May 14, 2025";
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        await payoutsROpage.goToDatasheetView(viewId);
        const oldPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const oldPaidAmount = await payoutsROpage.getStatusValue(
          "paid_amount_payee_currency"
        );
        const oldPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        const oldPaidDate = await payoutsROpage.getStatusValue(
          "last_paid_date"
        );
        expect(oldPaidDate).toBe(lastPaidDateBeforeInvalidation);
        expect(oldPayoutStatus).toBe(statusBeforeInvalidation);
        expect(oldPaidAmount).toBe(paidAmountBeforeInvalidation);
        expect(oldPendingAmount).toBe(pendingAmountBeforeInvalidation);
        await payoutsROpage.invalidatePayment();
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        await page.waitForLoadState("networkidle");
        const newPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const newPaidAmount = await payoutsROpage.getStatusValue(
          "paid_amount_payee_currency"
        );
        const newPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        const newPaidDate = await payoutsROpage.getStatusValue(
          "last_paid_date"
        );
        // expect(newPaidDate).toBeFalsy();    -----Bug-INTER-11107
        expect(newPayoutStatus).toBe(statusAfterInvalidation);
        expect(newPaidAmount).toBe(paidAmountAfterInvalidation);
        expect(newPendingAmount).toBe(pendingAmountAfterInvalidation);
      }
    );

    test.skip(
      "Processing arrears in payouts - Payouts report status column validations",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T26354,INTER-T26355,INTER-T26356,INTER-T26357,INTER-T26358,INTER-T26359,INTER-T26410",
          },
          {
            type: "Description",
            description: [
              "Ensure that processing arrears for a payee in payouts staleness should be shown in payouts report object sheet",
              "Ensure that processing arrears for a payee in payouts, processed amount for the current period should be updated from 0 to the amount processed.",
              "Ensure that processing arrears for a payee in payouts, arrears processed amount for the processed period should be updated from 0 to the amount processed from previous period.",
              "Ensure that processing arrears for a payee in payouts, payout status should be updated to 'Paid'.",
              "Ensure that processing arrears for a payee in payouts, lock status should be updated to 'True'.",
              "Ensure that processing arrears for a payee in payouts, pending amount for the current period should be updated from the pending amount to 0.",
            ].join("\r\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, payout status, processed amount, arrears processed amount, lock status, pending amount should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 120000);
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "438af5cb-e151-4afb-a847-052b57de9f15";
        const payoutStatusBeforeProcessing = "Partially Paid";
        const payoutStatusAfterProcessing = "Paid";
        const lockStatusBeforeProcessing = "False";
        const lockStatusAfterProcessing = "True";
        const pendingAmountBeforeProcessing = 90.0;
        const pendingAmountAfterProcessing = 0;
        const processedAmountBeforeProcessing = 0;
        const processedAmountAfterProcessing = 90.0;
        const arrearsProcessedAmountBeforeProcessing = 0;
        const arrearsProcessedAmountAfterProcessing = 90.0;
        // add locked date after Bug Fix
        await payoutsROpage.goToDatasheetView(viewId);
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        const oldPayoutStatus = await payoutsROpage.getProcessArrearStatuses(
          "payout_status"
        );
        const oldLockStatus = await payoutsROpage.getProcessArrearStatuses(
          "locked_status"
        );
        const oldProcessedAmount = await payoutsROpage.getProcessArrearStatuses(
          "processed_amount_payee_currency"
        );
        const oldArrearsProcessedAmount =
          await payoutsROpage.getFutureArrearProcessedValue(
            "arrears_processed_amount_payee_currency"
          );
        const oldPendingAmount = await payoutsROpage.getProcessArrearStatuses(
          "pending_amount_payee_currency"
        );
        expect(oldPayoutStatus).toBe(payoutStatusBeforeProcessing);
        expect(oldLockStatus).toBe(lockStatusBeforeProcessing);
        expect(oldProcessedAmount).toBe(processedAmountBeforeProcessing);
        expect(oldArrearsProcessedAmount).toBe(
          arrearsProcessedAmountBeforeProcessing
        );
        expect(oldPendingAmount).toBe(pendingAmountBeforeProcessing);
        await payoutsROpage.processArrears();
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        await page.waitForLoadState("networkidle");
        const newPayoutStatus = await payoutsROpage.getProcessArrearStatuses(
          "payout_status"
        );
        const newLockStatus = await payoutsROpage.getProcessArrearStatuses(
          "locked_status"
        );
        const newProcessedAmount = await payoutsROpage.getProcessArrearStatuses(
          "processed_amount_payee_currency"
        );
        const newArrearsProcessedAmount =
          await payoutsROpage.getFutureArrearProcessedValue(
            "arrears_processed_amount_payee_currency"
          );
        const newPendingAmount = await payoutsROpage.getProcessArrearStatuses(
          "pending_amount_payee_currency"
        );
        expect(newPayoutStatus).toBe(payoutStatusAfterProcessing);
        // expect(newLockStatus).toBe(lockStatusAfterProcessing); // -----Bug-INTER-11107
        expect(newProcessedAmount).toBe(processedAmountAfterProcessing);
        expect(newArrearsProcessedAmount).toBe(
          arrearsProcessedAmountAfterProcessing
        );
        expect(newPendingAmount).toBe(pendingAmountAfterProcessing);
      }
    );

    test(
      "Ignoring payment in payouts - Payouts report status column validations",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T26360,INTER-T26361,INTER-T26362,INTER-T26363,INTER-T26364,INTER-T26411",
          },
          {
            type: "Description",
            description: [
              "Ensure that ignoring arrears for a payee in payouts staleness should be shown in payouts report object sheet",
              "Ensure that ignoring arrears for a payee in payouts, ignored amount for the current period should be updated from 0 to the amount ignored.",
              "Ensure that ignoring arrears for a payee in payouts, pending amount for the current period should be updated from the pending amount to 0.",
              "Ensure that ignoring arrears for a payee in payouts, payout status should be updated to 'Paid'.",
              "Ensure that ignoring arrears for a payee in payouts, lock status should be updated to 'True'.",
              "Ensure that ignoring arrears for a payee in payouts, locked date should be updated when ignored.",
            ].join("\n"),
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, payout status, processed amount, arrears processed amount, lock status, pending amount should be updated accordingly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "5e987773-76f6-404a-a0ea-56a478eafbda";
        const payoutStatusBeforeIgnoring = "Partially Paid";
        const payoutStatusAfterIgnoring = "Paid";
        const lockStatusBeforeIgnoring = "True";
        const lockStatusAfterIgnoring = "True";
        const pendingAmountBeforeIgnoring = 40;
        const pendingAmountAfterIgnoring = 0;
        const ignoredAmountBeforeIgnoring = 0;
        const ignoredAmountAfterIgnoring = 40;
        // add locked date
        await payoutsROpage.goToDatasheetView(viewId);
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        const oldPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const oldLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        const oldIgnoredAmount = await payoutsROpage.getStatusValue(
          "ignored_amount_payee_currency"
        );
        const oldPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        expect(oldPayoutStatus).toBe(payoutStatusBeforeIgnoring);
        // expect(oldLockStatus).toBe(lockStatusBeforeIgnoring); -----Bug-INTER-11107
        expect(oldIgnoredAmount).toBe(ignoredAmountBeforeIgnoring);
        expect(oldPendingAmount).toBe(pendingAmountBeforeIgnoring);
        await payoutsROpage.ignoreArrears();
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        const newPayoutStatus = await payoutsROpage.getStatusValue(
          "payout_status"
        );
        const newLockStatus = await payoutsROpage.getStatusValue(
          "locked_status"
        );
        const newIgnoredAmount = await payoutsROpage.getStatusValue(
          "ignored_amount_payee_currency"
        );
        const newPendingAmount = await payoutsROpage.getStatusValue(
          "pending_amount_payee_currency"
        );
        expect(newPayoutStatus).toBe(payoutStatusAfterIgnoring);
        // expect(newLockStatus).toBe(lockStatusAfterIgnoring); -----Bug-INTER-11107
        expect(newIgnoredAmount).toBe(ignoredAmountAfterIgnoring);
        expect(newPendingAmount).toBe(pendingAmountAfterIgnoring);
      }
    );

    test(
      "Bulk Lock and Unlock status column validations in payouts report",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T26339,INTER-T26340,INTER-T26412,INTER-T26413",
          },
          {
            type: "Description",
            description: [
              "Validate that bulk locking the payee in payouts staleness should be shown in payouts report object sheet.\n",
              "Validate that bulk unlocking the payee in payouts staleness should be shown in payouts report object sheet.\n",
              "Validate that bulk locking the payee in payouts, lock status should be updated to 'True' for all payees selected in the report object\n",
              "Validate that bulk unlocking the payee in payouts, lock status should be updated to 'False' for all payees selected in the report object\n",
            ],
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Staleness, lock status should be updated accordingly for all the payees selected in bulk",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 120000);
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const payoutsROpage = new PayoutsReportPage(adminPage.page);
        const viewId = "baaead46-98bc-4394-8de2-ee93ffd710f1";
        const expectedToastMessage1 = "Sheet has new updates";
        const expectedToastMessage2 = "Refresh sheet to load latest data";
        await payoutsROpage.goToDatasheetView(viewId);
        const oldlockStatuses = await dsV2Page.getColumnValues(
          "Lock Status",
          "locked_status"
        );
        expect(oldlockStatuses.length).toBe(8);
        oldlockStatuses.forEach((status) => {
          expect(status).toBe("False");
        });
        await payoutsROpage.bulkLockUnlock("Lock");
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessage1, toastMessage2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessage1).toBe(expectedToastMessage1);
        expect(toastMessage2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        const newlockStatuses = await dsV2Page.getColumnValues(
          "Lock Status",
          "locked_status"
        );
        newlockStatuses.forEach((status) => {
          expect(status).toBe("True");
        });
        await payoutsROpage.bulkLockUnlock("Unlock");
        await payoutsROpage.goToDatasheetView(viewId);
        const [toastMessageU1, toastMessageU2] =
          await payoutsROpage.validateStaleness();
        expect(toastMessageU1).toBe(expectedToastMessage1);
        expect(toastMessageU2).toBe(expectedToastMessage2);
        await payoutsROpage.refreshDatasheet();
        const newUnlockStatuses = await dsV2Page.getColumnValues(
          "Lock Status",
          "locked_status"
        );
        newUnlockStatuses.forEach((status) => {
          expect(status).toBe("False");
        });
      }
    );
  }
);
