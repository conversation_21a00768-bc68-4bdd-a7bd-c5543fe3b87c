import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";
import PayoutPage from "../../../../../test-objects/payout-objects";
import UserPage from "../../../../../test-objects/user-objects";
import { setupGraphQLRouteInterceptor } from "../../../../bearerToken";
import ApprovalPage from "../../../../../test-objects/approval-objects";

const {
  customCategoryFixtures: { test, expect },
} = require("../../../../fixtures");

var token = "";

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
});

test.describe(
  "Custom_category Automation",
  { tag: ["@commission_adjustments", "@regression", "@primelogic-3"] },
  () => {
    test(
      "Create a custom category with alpha numeric characters",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "User should able to create custom category with alphanumeric characters",
          },
          {
            type: "Description",
            description:
              "User should able to create custom category with alphanumeric characters",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to create custom category with alphanumeric characters",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const isClicked = await adjustmentV2.visibleSettingsCta();
        expect(isClicked).toBe(true);
        const isSettingText = await adjustmentV2.verifySettingsText(
          "Correction"
        );
        expect(isSettingText).toBe(true);
        await adjustmentV2.addCategory("TestAdjust!@#$1234");
        await adjustmentV2.applyCustomCategory();
        await adjustmentV2.addedCategorySuccessPopUp();
      }
    );

    test(
      "Update the custom category",
      {
        annotation: [
          {
            type: "Test ID:INTER-T24425",
            description: "User should able to update custom category",
          },
          {
            type: "Description",
            description: "User should able to create custom category",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "User should able to update custom category",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        await adjustmentV2.updateCustomCategory("sales-adj", "edited-adj");
        await adjustmentV2.addedCategorySuccessPopUp();
        await adjustmentV2.applyCustomCategory();
        await adjustmentV2.waitForAlert();
        const isSavePopupDisplayed = await adjustmentV2.saveCustomChanges();
        expect(isSavePopupDisplayed).toBe(true);
        const isEditedCategory = await adjustmentV2.verifyEditedCategory(
          "edited-adj"
        );
        expect(isEditedCategory).toBe(true);
      }
    );

    test(
      "Check whether the 'Default' has been set as expected",
      {
        annotation: [
          {
            type: "Test ID -INTER-T24418",
            description:
              "User should able to see the Default category for the default and created adjustments",
          },
          {
            type: "Description",
            description:
              "User should able to see the Default category for the default adjustments",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the Default & custom category for the default adjustments",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        const { defaultCount, defaultCategories } =
          await adjustmentV2.dafaultCategoryCount("Default");
        // Validate the count of default categories
        expect(defaultCount).toBe(3);
        // Validate the names of the default categories
        expect(defaultCategories).toEqual([
          "Calculation Issue",
          "CRM Issue",
          "Others",
        ]);
      }
    );

    test(
      "Check whether the 'Custom' has been set as expected for the created categories",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24419",
            description:
              "User should able to see the Custom category for the created custom category",
          },
          {
            type: "Description",
            description:
              "User should able to see the  Custom category for the created custom category",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the  Custom category for the created custom category",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        const { defaultCategories } = await adjustmentV2.dafaultCategoryCount(
          "Custom"
        );
        // Validate the names of the default categories
        expect(defaultCategories).toEqual(
          expect.arrayContaining(["edited-adj"])
        );
      }
    );

    test(
      "Check whether the alert message is displayed when user archives the custom category",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24420",
            description:
              "User should able to see the alert message whne archieves the custom category",
          },
          {
            type: "Description",
            description:
              "User should able to see the  Custom category for the created custom category",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the  Custom category for the created custom category",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        await adjustmentV2.archiveCustomcategory("TestAdjust!@#$1234");
        await adjustmentV2.archiveSuccessfull();
        const archiveLabel = await adjustmentV2.archiveLabel(
          "TestAdjust!@#$1234"
        );
        expect(archiveLabel).toBe("Custom");
        await adjustmentV2.waitForHiddenPopUp(
          'Category renamed successfully. Click "Update" to apply the changes.'
        );
        const archiveLabel2 = await adjustmentV2.archiveLabel(
          "Calculation Issue"
        );
        expect(archiveLabel2).toBe("Default");
        await adjustmentV2.archiveCustomcategory("TestAdjust!@#$1234");
        await adjustmentV2.unarchiveSuccessfull();
        await adjustmentV2.waitForHiddenPopUp(
          'Category unar2chived successfully. Click "Update" to apply the changes.'
        );
        await adjustmentV2.applyCustomCategory();
        await adjustmentV2.waitForAlert();
        const isCategoryUnarchived = await adjustmentV2.saveCustomChanges();
        expect(isCategoryUnarchived).toBe(true);
      }
    );

    test.skip(
      "Check whether the alert is displayed when user try to archieve the already used category",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24420",
            description:
              "User should able to see the alert message when user try to archieve the already used category",
          },
          {
            type: "Description",
            description:
              "User should able to see the alert message when user try to archieve the already used category",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the alert message when user try to archieve the already used category",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        await adjustmentV2.archiveCustomcategory("Adj123!@#");
        await adjustmentV2.archiveSuccessfull();
        await adjustmentV2.waitForHiddenPopUp(
          'Category renamed successfully. Click "Update" to apply the changes.'
        );
        await adjustmentV2.archiveCustomcategory("TestAdjust!@#$1234");
        await adjustmentV2.unarchiveSuccessfull();
      }
    );

    test(
      "Check whether the user should not rename the 'Default' label category ",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24423",
            description:
              "User should not be able to rename the 'Default' label category",
          },
          {
            type: "Description",
            description:
              "User should not be able to rename the 'Default' label category",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should not be able to rename the 'Default' label category",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        const categoryButtonDetails =
          await adjustmentV2.getCategoryButtonCounts();
        const defaultButtonsAreCorrect = categoryButtonDetails
          .filter((item) => item.category === "Default")
          .every((item) => item.buttonCount === 1);
        expect(defaultButtonsAreCorrect).toBe(true);
      }
    );

    test(
      "Check whether the  user custom terminology should be applied in the 'Adjustment category' screen",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24434",
            description:
              "User should able to see the custom terminology in the 'Adjustment category' screen",
          },
          {
            type: "Description",
            description:
              "User should able to see the custom terminology in the 'Adjustment category' screen",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the custom terminology in the 'Adjustment category' screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        const modalTitle = await page
          .locator("div.ant-modal-title")
          .textContent();
        expect(modalTitle).toBe("Correction Categories");
      }
    );

    test(
      "Check whether the user is able to see the edited custom category in the Approvals",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24435",
            description:
              "User should able to see the edited custom category in the Approvals",
          },
          {
            type: "Description",
            description:
              "User should able to see the edited custom category in the Approvals",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the edited custom category in the Approvals",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const isNameVisible = await adjustmentV2.verifyNameInApprovals(
          "Ramya",
          "edited-adj"
        );
        expect(isNameVisible).toBe(true);
      }
    );

    test(
      "Check whether user is able to see all the active category listed in the dropdown ",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24433",
            description:
              "User should able to see the active category listed in the dropdown",
          },
          {
            type: "Description",
            description:
              "User should able to see the active category listed in the dropdown",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the active category listed in the dropdown",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.clickSettingsCta();
        await adjustmentV2.archiveCustomcategory("edited-adj");
        await adjustmentV2.archiveSuccessfull();
        await adjustmentV2.applyCustomCategory();
        await adjustmentV2.clickFilterCTA();
        await adjustmentV2.openFilterDropdown();
        const filterOptions = await adjustmentV2.fetchFilterOptions();
        expect(filterOptions).not.toContain("edited-adj");
        await adjustmentV2.closeFilterview();
        await adjustmentV2.clickSettingsCta();
        await adjustmentV2.archiveCustomcategory("edited-adj");
        await adjustmentV2.unarchiveSuccessfull();
        await adjustmentV2.applyCustomCategory();
        await adjustmentV2.clickFilterCTA();
        await adjustmentV2.openFilterDropdown();
        const filterOptions2 = await adjustmentV2.fetchFilterOptions();
        expect(filterOptions2).toContain("edited-adj");
      }
    );

    test(
      "Check whether user is able to see the custom category in the payout approval screen",
      {
        annotation: [
          {
            type: "Test ID - INTER-T24435",
            description:
              "User should able to see the custom category in the payout approval screen",
          },
          {
            type: "Description",
            description:
              "User should able to see the custom category in the payout approval screen",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should able to see the custom category in the payout approval screen",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        token = await setupGraphQLRouteInterceptor(page);
        const adjustmentV2 = new AdjustementsV2Page(page);
        const userPage = new UserPage(page);
        const approvalPage = new ApprovalPage(page);
        const payoutScreen = new PayoutPage(page);
        await payoutScreen.navigateToPayouts();
        const resp = await request.post(
          "spm/settlements/update_settlement_comm_freeze_status",
          {
            data: {
              payeeIds: ["<EMAIL>"],
              date: "31-03-2024",
              isLocked: true,
              abort_instance_check: false,
            },
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const response = await resp.json();
        console.log(response);
        expect(response["<EMAIL>"].status).toBe("SUCCESS");

        const resp2 = await request.post("spm/approval_instance/create", {
          data: {
            entity_type: "payout",
            bulk_mode: false,
            instance_params: {
              date: "31-03-2024",
              instance_details: [
                {
                  email_id: "<EMAIL>",
                  currency: "EUR",
                  payout: "1152506.580000",
                },
              ],
            },
            isSelectedAll: false,
            template_id: "8d142bc1-25ca-4c83-9a37-8f3c36499ecf",
          },
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        const response2 = await resp2.json();
        console.log(response2);
        expect(response2.message).toBe("Approval requested successfully.");
        await userPage.navigateToUser();
        await userPage.loginasPayee("<EMAIL>");
        await approvalPage.payouts();
        const isCategoryPresent = await adjustmentV2.verifyCategoryInPayouts(
          "Adj123!@#"
        );
        expect(isCategoryPresent).toBe(true);
        await adjustmentV2.closePayoutApproval();
        await userPage.validateexitImpersonation();
      }
    );
  }
);
