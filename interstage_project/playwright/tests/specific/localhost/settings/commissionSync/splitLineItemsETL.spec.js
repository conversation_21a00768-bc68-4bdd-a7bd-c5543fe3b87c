import { setupGraphQLRouteInterceptor } from "../../../../bearerToken";
import CommissionSync from "../../../../../test-objects/commissionSync-objects";
import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import DatasheetV2Page from "../../../../../test-objects/datasheet-v2-objects";
const CommonUtils = require("../../../../../test-objects/common-utils-objects");

const {
  splitSummationFixtures: { test, expect },
} = require("../../../../fixtures");

const clientId = 10457;
const setLabel = "split_summation_to_li";

async function setSplitSummationFlag(request, token, setLabel, setValue) {
  const requestBody = { client_id: clientId, label: setLabel, value: setValue };
  const response = await request.post(
    "http://localhost:3000/commission_engine/set_client_feature",
    {
      data: requestBody,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
  const jsonResponse = await response.json();
  console.log(jsonResponse);
  expect(response.status()).toBe(200);
  expect(jsonResponse.value).toBe(setValue);
}

async function runCommissionSync(page, date) {
  const commonPage = new CommonUtils(page);
  const commSyncPage = new CommissionSync(page);
  const csPrevPage = new CommissionsSyncPrevPeriod(page);
  console.log("Running comm syn for", date);
  await page.goto("/settings/commissions-and-data-sync", {
    waitUntil: "networkidle",
  });
  await commonPage.expandMenu("Calculate Commissions Run", 7);
  await commSyncPage.selectCriteria("payeee-in-plan");
  await commSyncPage.selectDropdown(["split-summation-li"]);
  await csPrevPage.selectDate(date);
  await commSyncPage.checkRefreshdatabooks("commissions");
  await csPrevPage.runCommissions();
  await csPrevPage.clickSkipandRun();
  await csPrevPage.waitForCalculationMessage();
  await csPrevPage.waitForCommissionsSuccess();
}

async function verifySplitRecordsCount(page, expectedCounts) {
  const dsV2Page = new DatasheetV2Page(page);
  await dsV2Page.goToDatasheet("split-summation-DB", "split-line-items");
  await dsV2Page.letColumnsLoad("Payee");
  for (const tabName in expectedCounts) {
    const actualCount = await dsV2Page.getTabCount(tabName);
    expect(actualCount).toBe(
      expectedCounts[tabName],
      `Mismatch for tab "${tabName}": Expected ${expectedCounts[tabName]}, got ${actualCount}`
    );
  }
}

async function verifySplitRecordValues(page, tabName) {
  const dsV2Page = new DatasheetV2Page(page);
  // split off filter - Period:Jan 2024, Payee:Jane S, Criteria:Quota
  // split on filter - Period:Feb 2024, Payee:Jane S, Criteria:Quota
  const columnChecks = {
    "Split LI off": [
      {
        columnName: "Quota Retirement",
        columnId: "quota_erosion",
        expectedValues: ["1,542,833.64"],
      },
    ],
    "Split LI on": [
      {
        columnName: "Quota Retirement",
        columnId: "quota_erosion",
        expectedValues: ["657,053.12", "657,053.12", "657,053.12"],
      },
    ],
  };

  await dsV2Page.goToTab(tabName);
  await dsV2Page.sortColumn("Payee", "ASC");

  const checks = columnChecks[tabName] || [];
  for (const { columnName, columnId, expectedValues } of checks) {
    const actualValues = await dsV2Page.getSpanColumnValues(
      columnName,
      columnId
    );
    console.log("actual values", actualValues);
    console.log("expectedValues", expectedValues);
    expect(
      actualValues,
      `Values mismatch for ${columnName} in ${tabName}`
    ).toEqual(expectedValues);
  }
}

test.describe(
  "Verify that the split summation on line items functionality is correctly handled during the commission sync process, controlled by the split_summation_to_li flag.",
  { tag: ["@commissionsync", "@regression", "@primelogic-2"] },
  () => {
    test(
      "Verify line items when split_summation_to_li is set to false",
      {
        annotation: [
          {
            type: "Expected Behaviour",
            description:
              "When the flag is false, line items for summation criteria remain unsplit in the commission report. When true, line items are split without impacting months where the flag is false.",
          },
        ],
      },
      async ({ adminPage, request }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 1500000);
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);

        console.log(
          "***** Run comm sync for the Jan 2024 when the flag is off and verify the records are not split *****"
        );
        let token = await setupGraphQLRouteInterceptor(page);
        await setSplitSummationFlag(request, token, setLabel, false);
        await runCommissionSync(page, "01 Jan 2024");

        console.log(
          "***** Run comm sync for the Feb 2024 when the flag is on and verify the records are split *****"
        );
        token = await setupGraphQLRouteInterceptor(page);
        await setSplitSummationFlag(request, token, setLabel, true);
        await runCommissionSync(page, "01 Feb 2024");

        let expectedCounts = {
          "Split LI off": "01",
          "Split LI on": "03",
        };
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await verifySplitRecordsCount(page, expectedCounts);
        await verifySplitRecordValues(page, "Split LI off");
        await verifySplitRecordValues(page, "Split LI on");

        console.log(
          "Run comm sync for Jan 2024 when flag is on to verify if the records are split"
        );
        await runCommissionSync(page, "01 Jan 2024");
        expectedCounts = {
          "Split LI off": "04",
          "Split LI on": "03",
        };
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await verifySplitRecordsCount(page, expectedCounts);
        await dsV2Page.goToTab("Split LI off");
        await dsV2Page.sortColumn("Payee", "ASC");
        const actualValues = await dsV2Page.getSpanColumnValues(
          "Quota Retirement",
          "quota_erosion"
        );
        expect(actualValues).toEqual([
          "385,708.41",
          "385,708.41",
          "385,708.41",
          "385,708.41",
        ]);
      }
    );
  }
);
