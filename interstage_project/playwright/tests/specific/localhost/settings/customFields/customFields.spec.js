const {
  localplaywrightFixtures: { test, expect },
} = require("../../../../fixtures");
let token = "";

test.beforeEach(async ({ adminPage }, testInfo) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    await page.getByRole("button", { name: "Exit" }).click();
    await page
      .getByRole("link")
      .filter({ hasText: "Users" })
      .waitFor({ state: "visible" });
    await page.waitForLoadState("networkidle");
  }
  await page.goto("/settings/custom-fields", { waitUntil: "networkidle" });
  testInfo.setTimeout(testInfo.timeout - 60000);
});

test.describe(
  "Custom Field",
  { tag: ["@customfields", "@regression", "@settings", "@adminchamp-5"] },
  () => {
    test("Create date Field", async ({ adminPage, request }) => {
      const page = adminPage.page;
      await page.reload();
      const response = await page.waitForResponse((response) =>
        response.url().includes("/spm/session-induction")
      );
      const headers = response.request().headers();
      if (headers.authorization) {
        token = headers.authorization.replace("Bearer ", ""); // Remove "Bearer"
      } else {
        console.log("No Access Token found in headers");
      }
      await page.waitForLoadState("networkidle");
      await page.goto("/settings/custom-fields", { waitUntil: "networkidle" });
      const resp = await request.post("spm/custom_field/add", {
        data: {
          display_name: "Date@@@@",
          field_type: "Email",
          is_archived: false,
          display_order: 1,
          options: {},
          is_effective_dated: false,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const responseBody = await resp.json();
      expect(await responseBody).toBe("Custom field created successfully");
      await page.reload({ waitUntil: "networkidle" });
    });

    test("Custom Field", async ({ adminPage }) => {
      const page = adminPage.page;
      // Add String Custom Field
      await page.getByRole("button", { name: "Add Field" }).click();
      await page.getByPlaceholder("Type label name").fill("Type String");
      await page.locator("div.ant-select-selector").click();
      await page.getByTitle("Text").locator("div").first().click();
      await page.getByLabel("Mark as mandatory").check();
      await page.getByLabel("Requires effective start date").check();
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .getByText("Custom field created.")
        .last()
        .waitFor({ state: "visible", timeout: 8000 });

      // Delete Custom Field

      await page.locator("svg.ant-dropdown-trigger").first().click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByRole("button", { name: "OK" }).click();
      await page
        .getByText("Custom field deleted.")
        .last()
        .waitFor({ state: "visible", timeout: 8000 });
    });

    test("Edit Field", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .locator("(//div[@class='ant-row']//*[local-name()='svg'])[last()-1]")
        .click();
      await page.getByPlaceholder("Type label name").clear();
      await page.getByPlaceholder("Type label name").fill("Date 2 @");
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .getByText("Custom field updated.")
        .last()
        .waitFor({ state: "visible", timeout: 8000 });
    });

    test("Create Dropdown custom Field INTER 3188", async ({ adminPage }) => {
      const page = adminPage.page;
      // Add String Custom Field
      await page.getByRole("button", { name: "Add Field" }).click();
      await page.getByPlaceholder("Type label name").fill("Dropdown");
      await page.locator("div.ant-select-selector").click();
      await page.getByTitle("Dropdown").locator("div").first().click();
      await page.getByPlaceholder("Add value").fill("A");
      await page.getByPlaceholder("Add value").nth(1).fill("B");
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .getByText("Custom field created.")
        .last()
        .waitFor({ state: "visible", timeout: 5000 });

      // Archive and Restore Custom Field

      await page.locator("svg.ant-dropdown-trigger").last().click();
      await page.getByRole("menuitem", { name: "Archive" }).click();
      await page.getByRole("button", { name: "OK" }).click();
      await page
        .getByText("Custom field archived.")
        .last()
        .waitFor({ state: "visible", timeout: 20000 });
      await page.getByRole("tab", { name: "Archived" }).click();
      await page
        .getByRole("tabpanel", { name: "Archived" })
        .locator("svg")
        .click();
      await page.getByRole("menuitem", { name: "Restore" }).click();
      await page.getByRole("button", { name: "OK" }).click();
      await page
        .getByText("Custom field restored.")
        .last()
        .waitFor({ state: "visible", timeout: 3000 });
    });

    test("Verify name in Mappayee and Statement Settings", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("settings/statement-settings", {
        waitUntil: "networkidle",
      });
      const userFields = await page.getByRole("button", {
        name: "User fields",
      });
      await userFields.waitFor({ state: "visible", timeout: 20000 });
      await page
        .getByText("Date 2 @")
        .last()
        .waitFor({ state: "visible", timeout: 5000 });
    });
  }
);
