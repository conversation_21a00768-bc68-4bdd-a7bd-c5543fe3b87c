const { expect } = require("@playwright/test");
const CwPage = require("../../../../../test-objects/cw-objects");
const {
  cwFixtures: { test },
} = require("../../../../fixtures");

test.describe(
  "Custom Workflows",
  { tag: ["@customworkflow", "@regression", "@adminchamp-1"] },
  () => {
    test("create a workflow", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16155" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      await cwPage.clickNextButton();
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("a");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("Clone and delete workflow", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16156,INTER-T16157" },
        {
          type: "Description",
          description: "User should be able to Clone and delete a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to Clone and delete a workflow successfully",
        }
      );

      const cwPage = new CwPage(adminPage.page);
      await expect(async () => {
        await cwPage.workflowScreen();
        await cwPage.searchWorkflow("Dummy");
        await cwPage.selectActions("Dummy");
        await cwPage.actionButton("Clone");
        await cwPage.validate("Workflow cloned - Dummy_Copy_1");
        await cwPage.searchWorkflow("Dummy_Copy_1");
        await cwPage.selectActions("Dummy_Copy_1");
        await cwPage.actionButton("Delete");
        await cwPage.deletePopup("Yes, delete");
        await cwPage.validate("workflow Deleted");
      }).toPass({
        timeout: 240000,
        intervals: [10000],
      });
    });

    test("View Details", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16174" },
        {
          type: "Description",
          description: "User should be able to view details",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to view details",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.searchWorkflow("Dummy");
      await cwPage.selectActions("Dummy");
      await cwPage.actionButton("View details");
      await cwPage.validatingName("Dummy");
      await cwPage.validate("Created OnJun 26, 2024, 2:50 PM");
      await cwPage.validate("Created Byeverstage admin");
      await cwPage.validate("Last Updated Byeverstage admin");
      await cwPage.validate("TriggerDatasheet data changes");
      await cwPage.validate("StatusActive");
      await cwPage.closeButton();
    });

    test("Move to active toggle", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16175" },
        {
          type: "Description",
          description: "User should be able enable toggle",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to enable toggle",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.searchWorkflow("Move to active");
      await cwPage.toggle();
      await cwPage.validate("Workflow is active now");
    });

    test("Move to inactive toggle", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16176" },
        {
          type: "Description",
          description: "User should be able disable toggle",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to disable toggle",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.searchWorkflow("Move to Inactive");
      await cwPage.toggle();
      await cwPage.validate("Workflow paused now");
    });

    test("Turn on toggle", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16177" },
        {
          type: "Description",
          description:
            "User should be able turn on toggle inside workflow screen",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able turn on toggle inside workflow screen",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("TON");
      await cwPage.buttonInsideWorkflow("Turn On");
      await cwPage.validate("Workflow is active now");
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("Turn off toggle", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16178" },
        {
          type: "Description",
          description:
            "User should be able turn OFF toggle inside workflow screen",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able turn OFF toggle inside workflow screen",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.workflowScreen();
      await cwPage.selectWf("TOF");
      await cwPage.buttonInsideWorkflow("Turn Off");
      await cwPage.validate("Workflow paused now");
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("search and select for a workflow", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16178" },
        {
          type: "Description",
          description: "search for a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "search for a workflow",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.searchWorkflow("edit Mode");
      await cwPage.selectWf("edit Mode");
      await cwPage.navigateUrl(
        "http://localhost:3000/settings/workflow-builders/efe58cc6-4265-472d-b87c-1eee5f30305a?name=edit%20mode"
      );
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("workflow history", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16179" },
        {
          type: "Description",
          description: "workflow history",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "workflow history",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.link();
      await cwPage.workflowhistorySearch();
      await cwPage.navigateUrl(
        "http://localhost:3000/settings/workflow-builders/workflow-history"
      );
    });

    test("view workflow from history", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16180" },
        {
          type: "Description",
          description: "view workflow from history",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "view workflow from historyy",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.link();
      await cwPage.workflowhistorySearch();
      await cwPage.workflowhistorySearchwf(
        "test",
        "c0f2e71c-82e0-43f7-9080-4bea5872a77a"
      );
      await cwPage.buttonInsideWorkflow("View workflow");
      await cwPage.validate("/Test");
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("Unable to view deleted workflow from history", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16181" },
        {
          type: "Description",
          description: "Unable to view deleted workflow from history",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Unable to view deleted workflow from history",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.link();
      await cwPage.workflowhistorySearch();
      await cwPage.workflowhistorySearchwf(
        "deleted",
        "aaa4c8f6-b849-4f1f-8662-a851a4425cc9"
      );
      await cwPage.buttonVisible("View workflow");
    });

    test("Unable to create trigger without trigger icon", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16182" },
        {
          type: "Description",
          description: "Unable to create trigger without trigger icon",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Unable to create trigger without trigger icon",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.createTrigger();
      await cwPage.selectCategoryExisting("trig");
      await cwPage.triggerDatabook("Custom Workflows Template", "test");
      await cwPage.buttonDisabled("Create");
    });

    test("Create custom trigger", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16183" },
        {
          type: "Description",
          description: "Create custom trigger",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Create custom trigger",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.createTrigger();
      await cwPage.clickCategory();
      await cwPage.createCategory("play6");
      await cwPage.clickCategory();
      await cwPage.categorySelect("play6");
      await cwPage.addTrigger("p1");
      await cwPage.description("play");
      await cwPage.triggerDatabook("Custom Workflows Template", "test");
      await cwPage.buttonInsideWorkflow("Create");
      await cwPage.validate("Custom trigger created successfully");
    });

    test("Edit custom trigger", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16183" },
        {
          type: "Description",
          description: "Edit custom trigger",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Edit custom trigger",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.editTrigger("New");
      await cwPage.validate("Custom trigger name already exists");
    });

    test("workflow should not be created without a trigger", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16185" },
        {
          type: "Description",
          description: "workflow should not be created without a trigger",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "workflow should not be created without a trigger",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.saveDraft();
      await cwPage.validate("Failed to create workflow");
    });

    test("create a workflow with in-app success", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16155" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      await cwPage.clickNextButton();
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("success");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("publish a draft workflow", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16156" },
        {
          type: "Description",
          description: "publish a draft workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "publish a draft workflow",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("edit mode");
      await cwPage.selectTriggerCategory("Event");
      await cwPage.selectTriggerBy("Add");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("workflow history  button", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16156" },
        {
          type: "Description",
          description: "workflow history  button",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "workflow history  button",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.searchWorkflow("Dummy");
      await cwPage.selectActions("Dummy");
      await cwPage.actionButton("Workflow History");
      await cwPage.navigateUrl(
        " http://localhost:3000/settings/workflow-builders/workflow-history/87ab607d-9a6f-4db0-abe8-c25a690daf58?name=Dummy"
      );
    });

    test("Duplicate workflow name", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16156" },
        {
          type: "Description",
          description: "Duplicate workflow name",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Duplicate workflow name",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("Duplicate");
      await cwPage.nameWorkflow("Dummy");
      await cwPage.updateButton();
      await cwPage.validateduplicate("Workflow name already exists");
    });

    test("unable to publish if databook data is not given", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16156" },
        {
          type: "Description",
          description: "unable to publish if databook data is not given",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "unable to publish if databook data is not given",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      await cwPage.buttonInsideWorkflow("Next");
      await cwPage.nameWorkflow("Do not publish");
      await cwPage.buttonInsideWorkflow("Save Draft");
      await cwPage.validate("Workflow created");
      await cwPage.buttonDisabled("Publish");
    });

    test("include only changes-new", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16157" },
        {
          type: "Description",
          description: "include only changes-new",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      await cwPage.clickNextButton();
      // await cwPage.selectDrop();
      // await cwPage.selectWf("Include only changes");
      // await cwPage.selectDropdown(["New"]);
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("new only");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("include only changes-updated", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16158" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Update"
      );
      // await cwPage.selectDrop();
      // await cwPage.selectWf("Include only changes");
      // await cwPage.selectDropdown(["Updated"]);
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("updated only");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("include only changes-deleted", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16159" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Delete"
      );
      // await cwPage.selectDrop();
      // await cwPage.selectWf("Include only changes");
      // await cwPage.selectDropdown(["Deleted"]);
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("deleted only");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    // skipped this as there is no way to select all 3 options in new UI
    test.skip("include only changes-all", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16159" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      await cwPage.selectDrop();
      await cwPage.selectWf("Include only changes");
      await cwPage.selectDropdown(["New", "Updated", "Deleted"]);
      await cwPage.thenAction("Success", "1");
      await cwPage.nameWorkflow("all3");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("Download button not to be displayed", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16161" },
        {
          type: "Description",
          description: "Download button not to be displayed",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Download button not to be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("Duplicate");
      await cwPage.downloadButton();
    });

    test("Editing published workflow moves it to draft mode", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16162" },
        {
          type: "Description",
          description: "Editing published workflow moves it to draft mode",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Editing published workflow moves it to draft mode",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("Edit to Draft");
      await cwPage.editbutton();
      await cwPage.deletePopup("Yes, Proceed");
      await cwPage.buttonVisibllity("Draft");
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("when an workflow is in draft mode, toggle should not be present", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16163" },
        {
          type: "Description",
          description: "Editing published workflow moves it to draft mode",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Editing published workflow moves it to draft mode",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.selectWf("Duplicate");
      await cwPage.buttonVisible("Turn On");
      await cwPage.buttonInsideWorkflow("Exit");
    });

    test("include only changes-multiple then blocks", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16164" },
        {
          type: "Description",
          description: "User should be able to create a workflow",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to create a workflow successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook(
        "Custom Workflows Template",
        "test",
        "Event",
        "Add"
      );
      // await cwPage.selectDrop();
      // await cwPage.selectWf("Include only changes");
      // await cwPage.selectDropdown(["New"]);
      await cwPage.thenAction("Success", "1");
      await cwPage.thenAction("Success", "2");
      await cwPage.nameWorkflow("workflow with multiple blocks");
      await cwPage.publishWorkflow();
      await cwPage.validate("Workflow published");
    });

    test("Validating error message when user tries to delete a datasheet which is included in Custom workflow", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Description",
          description: "User should not be able to delete a datasheet",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Error message should be displayed when tried to delete a datasheet",
        }
      );
      const page = adminPage.page;
      const cwPage = new CwPage(page);
      await cwPage.navigateToDatasheet(
        "3593ba9b-0734-4398-afc7-38e9629004f4",
        "Test1"
      );
      await cwPage.deleteDatasheet("Test1");
      await expect(
        await page.getByText(
          "Error: Cannot delete datasheet as it is part of workflow."
        )
      ).toBeVisible();
      await cwPage.deleteDatasheet("Test2");
      await expect(
        await page.getByText(
          "Error: Cannot delete datasheet as it is part of workflow."
        )
      ).toBeVisible();
    });

    test("Validating error message when change config of datasheet which is included in the custom workflow", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Description",
          description: "User should not be able to delete a datasheet",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Error message should be displayed when tried to delete a datasheet",
        }
      );
      const page = adminPage.page;
      const cwPage = new CwPage(page);
      await cwPage.navigateToDatasheet(
        "3593ba9b-0734-4398-afc7-38e9629004f4",
        "Test1"
      );
      await cwPage.editDatasheet("Test2");
      await cwPage.addTransformation("Test1");
      await expect(
        await page.getByText(
          "Config changed. All the records will be considered as new records in the associated workflows."
        )
      ).toBeVisible();
      await cwPage.closeEditPage();
      await cwPage.editDatasheet("Test1");
      await cwPage.addTransformation("Test2");
      await expect(
        await page.getByText(
          "Config changed. All the records will be considered as new records in the associated workflows."
        )
      ).toBeVisible();
    });

    test("Validating error message on changing datasheet in the existing Custom Workflow", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Description",
          description: "User should not be able to delete a datasheet",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Error message should be displayed when tried to delete a datasheet",
        }
      );
      const page = adminPage.page;
      const cwPage = new CwPage(page);
      await cwPage.workflowScreen();
      await cwPage.openWorkFlow("Playwright QA");
      await cwPage.validateDatasheetErrorMessage();
    });

    test("Validating IF/Else IF condition Formula Box Values", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Description",
          description: "Validating the IF Condition UI and formula values",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "If Condtion formula box should display related functions",
        }
      );
      const page = adminPage.page;
      const cwPage = new CwPage(page);
      const expectedIFBlockFunc = [
        "EmailEmail",
        "DateDate",
        "AmountInteger",
        "SUMInteger",
        "SUMIFInteger",
        "MINInteger",
        "MAXInteger",
        "AVGInteger",
        "COUNTIFInteger",
        "CountNotNullInteger",
        "GetDateInteger",
        "DistinctCountInteger",
        "IsEmptyBoolean",
        "IsNotEmptyBoolean",
        "ContainsBoolean",
        "NotContainsBoolean",
        "DATEDIFFInteger",
        "LowerString",
        "ConcatString",
        "FindInteger",
        "RoundInteger",
        "RoundUpInteger",
        "RoundDownInteger",
        "GetUserProperty",
        "(",
        "Choose dateDate",
      ];
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Playwright QA", "Test1", "Event", "Add");
      await cwPage.clickNextButton();
      await cwPage.clickAdd("1");
      await cwPage.addComponent("If: Add a condition");
      let actualValues = await cwPage.getFormulaBoxValues();
      expect(actualValues).toEqual(expectedIFBlockFunc);
      await cwPage.clickNextButton();
      await cwPage.addElseIf();
      actualValues = await cwPage.getFormulaBoxValues();
      expect(actualValues).toEqual(expectedIFBlockFunc);
    });

    test("Validating IF/Else IF condition box error messages", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Description",
          description:
            "Validating when IF/Else If condition formula is incorrect, Error messages are displaying",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Error message should be displayed when IF/Else If condition formula is incorrect",
        }
      );
      const page = adminPage.page;
      const cwPage = new CwPage(page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Playwright QA", "Test1", "Event", "Add");
      await cwPage.clickNextButton();
      await cwPage.clickAdd("1");
      await cwPage.addComponent("If: Add a condition");
      await cwPage.addInvalidFormula();
      await cwPage.validateErrorMsg();
      await cwPage.addElseIf();
      await cwPage.addInvalidFormula();
      await cwPage.validateErrorMsg();
      await cwPage.nameWorkflow("Test QA");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Action block is missing");
      await cwPage.buttonDisabled("Publish");
    });

    test.skip("Verify all entered values retention when user adds send an email action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19597" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send an email action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description: "All entered values should be retained",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send an email");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        subject: "subject",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        subjectOrTitleValue: "subject",
        contentValue: "Test@!@#$#@",
      });
    });

    test.skip("Verify all entered values retention when user adds send a slack message action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19598" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send a slack message action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description: "All entered values should be retained",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send a Slack message");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify all entered values retention when user adds send in app notification action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19599" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send in app notification action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description: "All entered values should be retained",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send in-app notification");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        notificationStatus: "Success",
        title: "Title",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        notificationStatusValue: "Success",
        subjectOrTitleValue: "Title",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send an email action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19600" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send an email action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send an email");
      await cwPage.fillThenDetails({
        subject: "subject",
        content: "Test@!@#$#@",
      });
      await cwPage.clickAddAction();
      await cwPage.validateToolTip("Then block is incomplete");
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.clickButton("Then: Send an email");
      await cwPage.verifyRetainedValues({
        subjectOrTitleValue: "subject",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send a slack message action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19601" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send a slack message action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send a Slack message");
      await cwPage.fillThenDetails({
        content: "Test@!@#$#@",
      });
      await cwPage.clickAddAction();
      await cwPage.validateToolTip("Then block is incomplete");
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.clickButton("Then: Send a Slack message");
      await cwPage.verifyRetainedValues({
        content: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send in app notification action block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19602" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send in app notification action block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send in-app notification");
      await cwPage.fillThenDetails({
        notificationStatus: "Success",
        title: "Title",
        content: "Test@!@#$#@",
      });
      await cwPage.clickAddAction();
      await cwPage.validateToolTip("Then block is incomplete");
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.clickButton("Then: Send in-app notification");
      await cwPage.verifyRetainedValues({
        notificationStatusValue: "Success",
        subjectOrTitleValue: "Title",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify entered valid formula retention when user adds IF condition block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19603" },
        {
          type: "Description",
          description:
            "Verify entered valid formula retention when user adds IF condition block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description: "Entered valid formula should be retained.",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.fillCondition("If: ConditionNext", [
        "Deal ID",
        ">",
        "Deal Amount",
      ]);
      await cwPage.clickNextButton();
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.verifyRetainedFormula("Deal Amount");
    });

    test("Verify entered valid formula retention when user adds ELSE IF condition block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19604" },
        {
          type: "Description",
          description:
            "Verify entered valid formula retention when user adds ELSE IF condition block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description: "Entered valid formula should be retained.",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.addElseIf();
      await cwPage.fillCondition("Else-if: ConditionNext", [
        "Deal ID",
        ">",
        "Deal Amount",
      ]);
      await cwPage.clickNextButton();
      await cwPage.clickCollapseOrExpandButton();
      await cwPage.verifyRetainedFormula("Deal Amount");
    });

    test("Verify entered invalid formula retention and tooltip when user adds IF condition block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19605" },
        {
          type: "Description",
          description:
            "Verify entered invalid formula retention and tooltip when user adds IF condition block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered invalid formula should be retained and proper tool tip message should be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.fillCondition("If: ConditionNext", ["Deal ID"]);
      await cwPage.clickNextButton();
      await cwPage.verifyRetainedFormula("Deal ID");
      await cwPage.validateErrorMsg();
    });

    test("Verify entered invalid formula retention and tooltip when user adds ELSE IF condition block", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19606" },
        {
          type: "Description",
          description:
            "Verify entered invalid formula retention and tooltip when user adds ELSE IF condition block",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered invalid formula should be retained and proper tool tip message should be displayed",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.addElseIf();
      await cwPage.fillCondition("Else-if: ConditionNext", ["Deal Amount"]);
      await cwPage.clickNextButton();
      await cwPage.verifyRetainedFormula("Deal Amount");
      await cwPage.validateErrorMsg();
    });

    test.skip("Verify all entered values retention when user adds send an email action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19607" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send an email action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send an email");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        subject: "subject",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-1");
      await cwPage.saveDraft();
      await cwPage.clickButton("Then: Send an email");
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        subjectOrTitleValue: "subject",
        contentValue: "Test@!@#$#@",
      });
    });

    test.skip("Verify all entered values retention when user adds send a slack message action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19608" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send a slack message action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send a Slack message");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-2");
      await cwPage.saveDraft();
      await cwPage.clickButton("Then: Send a Slack message");
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify all entered values retention when user adds send in app notification action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19609" },
        {
          type: "Description",
          description:
            "Verify all entered values retention when user adds send in app notification action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send in-app notification");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        notificationStatus: "Success",
        title: "Title",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-3");
      await cwPage.saveDraft();
      await cwPage.clickButton("Then: Send in-app notification");
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        notificationStatusValue: "Success",
        subjectOrTitleValue: "Title",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send an email action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19610" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send an email action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed and then should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send an email");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        content: "Test@!@#$#@",
      });
      await cwPage.nameWorkflow("Test WorkFlow-4");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Subject not present");
      await cwPage.clickButton("Then: Send an email");
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send a slack message action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19611" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send a slack message action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed and then should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send a Slack message");
      await cwPage.fillThenDetails({
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-5");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Recipients not present");
      await cwPage.clickButton("Then: Send a Slack message");
      await cwPage.verifyRetainedValues({
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify partially entered values retention and tooltip when user adds send in app notification action block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19612" },
        {
          type: "Description",
          description:
            "Verify partially entered values retention and tooltip when user adds send in app notification action block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "All entered values should be retained and proper tool tip message should be displayed and then should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction("Send in-app notification");
      await cwPage.fillThenDetails({
        to: "everstage admin",
        title: "Title",
        content: "Test@!@#$#@",
      });
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-6");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Notification status not present");
      await cwPage.clickButton("Then: Send in-app notification");
      await cwPage.verifyRetainedValues({
        toValue: "everstage admin",
        subjectOrTitleValue: "Title",
        contentValue: "Test@!@#$#@",
      });
    });

    test("Verify entered valid formula retention when user adds IF condition block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19613" },
        {
          type: "Description",
          description:
            "Verify entered valid formula retention when user adds IF condition block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered valid formula should be retained and should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.fillCondition("If: ConditionNext", [
        "Deal ID",
        ">",
        "Deal Amount",
      ]);
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-7");
      await cwPage.saveDraft();
      await cwPage.clickButton("If: Condition");
      await cwPage.verifyRetainedFormula("Deal Amount");
    });

    test("Verify entered valid formula retention when user adds ELSE IF condition block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19614" },
        {
          type: "Description",
          description:
            "Verify entered valid formula retention when user adds ELSE IF condition block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered valid formula should be retained and should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.addElseIf();
      await cwPage.fillCondition("Else-if: ConditionNext", [
        "Deal ID",
        ">",
        "Deal Amount",
      ]);
      await cwPage.clickNextButton();
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-9");
      await cwPage.saveDraft();
      await cwPage.clickButton("Else-if: Condition");
      await cwPage.verifyRetainedFormula("Deal Amount");
    });

    test("Verify entered invalid formula retention and tooltip when user adds IF condition block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19615" },
        {
          type: "Description",
          description:
            "Verify entered invalid formula retention and tooltip when user adds IF condition block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered invalid formula should be retained and proper tool tip message should be displayed and then should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.fillCondition("If: ConditionNext", ["Deal ID"]);
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-8");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Action block is missing");
      await cwPage.clickButton("If: Condition");
      await cwPage.verifyRetainedFormula("Deal ID");
      await cwPage.validateErrorMsg();
    });

    test("Verify entered invalid formula retention and tooltip when user adds ELSE IF condition block and then Save Draft", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T19616" },
        {
          type: "Description",
          description:
            "Verify entered invalid formula retention and tooltip when user adds ELSE IF condition block and then Save Draft",
        },
        {
          type: "Precondition",
          description: "Trigger should be created and Available",
        },
        {
          type: "Expected Behaviour",
          description:
            "Entered invalid formula should be retained and proper tool tip message should be displayed and then should be saved successfully",
        }
      );
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("Custom Workflows Template", "test");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickIfAddACondition();
      await cwPage.addElseIf();
      await cwPage.fillCondition("Else-if: ConditionNext", ["Deal Amount"]);
      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("Test WorkFlow-10");
      await cwPage.saveDraft();
      await cwPage.validateToolTip("Action block is missing", 1);
      await cwPage.clickButton("Else-if: Condition");
      await cwPage.verifyRetainedFormula("Deal Amount");
      await cwPage.validateErrorMsg();
    });
  }
);
