const { time } = require("console");
const {
  rolesFixtures: { test, expect },
} = require("../../../../fixtures");
const csv = require("csv-parser");
const fs = require("fs");
const path = require("path");

test.describe(
  "Roles",
  { tag: ["@rbac", "@regression", "@adminchamp-2"] },
  () => {
    test("Create new custom role validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await expect(
        page.getByText("Payee", { exact: true }).first()
      ).toBeVisible();
      await expect(page.getByText("Super Admin").first()).toBeVisible();
      await expect(
        page.getByText("Admin", { exact: true }).first()
      ).toBeVisible();
      await page.getByRole("button", { name: "New Role" }).click();
      const createButton = page.getByRole("button", { name: "Create Role" });

      await expect(createButton).toBeDisabled();
      await page.getByPlaceholder("Enter a role name").click();
      await page.getByPlaceholder("Enter a role name").fill("customPayee");
      await page.getByPlaceholder("Add optional description").click();
      await page
        .getByPlaceholder("Add optional description")
        .fill("This is custom payee");
      await page.getByRole("button", { name: "Create Role" }).click();
      await expect(page.getByText("customPayee").first()).toBeVisible();
    });

    test("copy of custom role and deletion of role validation", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^customPayeeNot assigned to anyone$/ })
        .first()
        .click();
      await page
        .locator("div")
        .filter({
          hasText:
            /^customPayeeThis is custom payeeNot assigned to anyoneEdit$/,
        })
        .locator("svg")
        .nth(1)
        .click();
      await page.getByRole("menuitem", { name: "Clone" }).click();
      await expect(page.getByText("Copy of customPayee")).toBeVisible();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Please select atleast one")).toBeVisible();
      await page.getByLabel("View dashboards page").check();
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .locator("div")
        .filter({
          hasText:
            /^Copy of customPayeeThis is custom payeeNot assigned to anyoneEdit$/,
        })
        .locator("svg")
        .nth(1)
        .click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByText("Are you sure you want to").click();
      await page.getByRole("button", { name: "Yes, Confirm" }).click();
      await expect(
        page.getByText("Copy of customPayee", { exact: true })
      ).toHaveCount(0);
    });

    test("Deletion of user role having assignee validation", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^customAdminAssigned to 1 user$/ })
          .first()
      ).toBeVisible();
      await page
        .locator("div")
        .filter({ hasText: /^customAdminAssigned to 1 user$/ })
        .first()
        .click();

      await page
        .locator("div")
        .filter({
          hasText:
            /^customAdminthis is customadmin roleAssigned to 1 userEdit$/,
        })
        .locator("svg")
        .nth(1)
        .click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await expect(
        page.getByText("There are 1 user assigned to")
      ).toBeVisible();
      await expect(page.getByText("Please move them to a new")).toBeVisible();
      await expect(page.getByText("You can't undo this action")).toBeVisible();
      const moveButton = page.getByRole("button", { name: "Move & Delete" });
      await expect(moveButton).toBeDisabled();
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select new role" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Admin$/ })
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Move & Delete" }).click();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^AdminAssigned to 3 users$/ })
          .first()
      ).toBeVisible();
    });

    test("Updation of create/update user role list validation", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/users", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "New User" }).click();
      await page
        .getByLabel("Add User")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Role" })
        .click();
      await expect(page.locator("//div[@title='Payee']").first()).toBeVisible();
      await expect(page.locator("//div[@title='Admin']").first()).toBeVisible();
      await expect(
        page.locator("//div[@title='Super Admin']").first()
      ).toBeVisible();
      await page
        .getByLabel("Add User")
        .getByRole("button", { name: "Cancel" })
        .click();
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "New Role" }).click();
      await page.getByPlaceholder("Enter a role name").click();
      await page.getByPlaceholder("Enter a role name").fill("newRoleCreation");
      await page.getByPlaceholder("Add optional description").click();
      await page
        .getByPlaceholder("Add optional description")
        .fill("This is new role creation");
      await page.getByRole("button", { name: "Create Role" }).click();
      await page.goto("/users", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "New User" }).click();
      await page
        .getByLabel("Add User")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Role" })
        .click();
      await expect(page.getByText("newRoleCreation")).toBeVisible();
      await page
        .getByLabel("Add User")
        .getByRole("button", { name: "Cancel" })
        .click();
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^newRoleCreationNot assigned to anyone$/ })
        .first()
        .click();
      await page
        .locator("div")
        .filter({
          hasText:
            /^newRoleCreationThis is new role creationNot assigned to anyoneEdit$/,
        })
        .locator("svg")
        .nth(1)
        .click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByText("Are you sure you want to").click();
      await page.getByRole("button", { name: "Yes, Confirm" }).click();
      await page.goto("/users", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "New User" }).click();
      await page.getByLabel("Role*").click();
      await expect(page.getByText("newRoleCreation")).toHaveCount(0);
      await page
        .getByLabel("Add User")
        .getByRole("button", { name: "Cancel" })
        .click();
    });

    test("Roles disable for newly created role", async ({ payeePage }) => {
      const page = payeePage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "New Role" }).click();
      await page.getByPlaceholder("Enter a role name").click();
      await page.getByPlaceholder("Enter a role name").fill("newPoweradmin");
      await page.getByPlaceholder("Add optional description").click();
      await page
        .getByPlaceholder("Add optional description")
        .fill("this is power admin role");
      await page.getByRole("button", { name: "Create Role" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^newPoweradminNot assigned to anyone$/ })
        .first()
        .click();
      await expect(page.locator(".text-ever-success")).toHaveCount(0);
      await page.getByLabel("View dashboards page").check();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.locator(".text-ever-success")).toHaveCount(1);
      await page
        .locator(
          "//div[@class='ant-space-item']//span[text()='Not assigned to anyone']"
        )
        .click();
      await page.getByText("No users found").click();
      await page.locator("button").filter({ hasText: "Close" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^PayeeAssigned to 1 user$/ })
        .first()
        .click();
      const payeeButton = page.getByRole("button", { name: "Edit" });
      await expect(payeeButton).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^AdminAssigned to 3 users$/ })
        .first()
        .click();
      const adminButton = page.getByRole("button", { name: "Edit" });
      await expect(adminButton).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^Super AdminAssigned to 2 users$/ })
        .first()
        .click();

      const superAdminButton = page.getByRole("button", { name: "Edit" });
      await expect(superAdminButton).toBeEnabled();
    });

    test("Edit button disable for default roles validation", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^PayeeAssigned to 1 user$/ })
        .first()
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Payee$/ })
        .nth(1)
        .click();
      await expect(page.getByRole("button", { name: "Edit" })).toHaveCount(0);
      await page
        .locator("div")
        .filter({ hasText: /^Super AdminAssigned to 2 users$/ })
        .first()
        .click();
      await expect(page.getByRole("button", { name: "Edit" })).toHaveCount(0);
      await page
        .locator("div")
        .filter({ hasText: /^AdminAssigned to 3 users$/ })
        .first()
        .click();
      await expect(page.getByRole("button", { name: "Edit" })).toHaveCount(0);
      await page
        .locator("div")
        .filter({ hasText: /^Super AdminAssigned to 2 users$/ })
        .first()
        .click();
      await page.getByText("Assigned to 2 user").nth(1).click();
      await page.getByText("<EMAIL>").click();
      await page.getByText("<EMAIL>").click();
      await page.locator("button").filter({ hasText: "Close" }).click();
    });

    test("Validation to delete default role by power admin", async ({
      payeePage,
    }) => {
      const page = payeePage.page;
      await page.goto("/settings/user-roles", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^PayeeAssigned to 1 user$/ })
        .first()
        .click();
      await page.locator(".ant-dropdown-trigger").click();
      await expect(page.getByRole("menuitem", { name: "Delete" })).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Super AdminAssigned to 2 users$/ })
        .first()
        .click();
      await page.locator(".ant-dropdown-trigger").click();
      await expect(page.getByRole("menuitem", { name: "Delete" })).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^AdminAssigned to 3 users$/ })
        .first()
        .click();
      await page.locator(".ant-dropdown-trigger").click();
      await expect(page.getByRole("menuitem", { name: "Delete" })).toBeHidden();
    });

    test("Export user role validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/users", { waitUntil: "networkidle" });
      await page
        .locator(
          '(//span[.//input[@placeholder="Search by name or email"]])[1]/following-sibling::button'
        )
        .first()
        .click();
      await page.locator("//div[@data-testid='role']").first().click();
      await expect(page.locator("//span[@title='Payee']").nth(-2)).toBeVisible({
        timeout: 10000,
      });
      await page.locator("//span[@title='Payee']").nth(-2).click();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page
        .getByRole("button", { name: "Export users You can export" })
        .click();

      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("button", { name: "Proceed" }).click();

      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);
      const results = [];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => {
          const trimmedData = {};
          for (const key in data) {
            trimmedData[key.trim()] = data[key];
          }
          results.push(trimmedData);
        })
        .on("end", () => {
          expect(results.length).toBe(1);
          expect(Object.keys(results[0]).length).toBe(18);
          expect(results[0].Email).toBe("<EMAIL>");
          expect(results[0].Role).toBe("Payee");
        })
        .on("error", (err) => {
          throw err;
        });
    });
  }
);
