import ConnectorsPage from "../../../../test-objects/connectors-objects";
import CustomObjectsV3 from "../../../../test-objects/customObjectsV3";
import { setupGraphQLRouteInterceptor } from "../../../bearerToken";
import connectorCreds from "../../../../testData/ICM/connectorCreds.json";

const {
  automateQA1Fixtures: { test, expect },
} = require("../../../fixtures");

test.beforeAll(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 600000);
  const page = adminPage.page;
  const connectorsPage = new ConnectorsPage(page);
  const customObjectPage = new CustomObjectsV3(page);
  const connectionsList = [
    "Hubspot_Connection_2",
    "Salesforce_Connection_2",
    "Netsuite_Connection_2",
    "MS_SQL_Server_Connection_2",
    "PostgreSQL_Connection_2",
    "Snowflake_Connection_2",
    "Zoho_Connection_2",
  ];

  await connectorsPage.navigateToConnectors();
  for (const connection of connectionsList) {
    if (await connectorsPage.isConnectionPresent(connection)) {
      await connectorsPage.deleteConnection(connection, "");
    } else {
      console.log(connection, "connection not found");
    }
  }

  await customObjectPage.navigateToObjectsPage();
  for (const connection of connectionsList) {
    const obj = connection + "_Obj";
    await customObjectPage.deleteObject(obj);
  }
});

test.describe(
  "Self Serve Integrations - Verify user is able to connect to a new self serve integration, create an object with the connection and delete the object/connection",
  { tag: ["@regression", "@connectors", "@launchpad-1"] },
  () => {
    test("Hubspot Self Serve", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Hubspot";
      const connectionName = "Hubspot_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors();
      await connectorsPage.conenctorsHub();
      await page.waitForURL("**/settings/connectors/new", { timeout: 5000 });
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.accessToken(connectorCreds.hubspot.accessToken);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject("Companies", "companies");
      await customObjectPage.searchFieldsConnection("Recent Deal Close Date");
      await customObjectPage.selectChecbox();
      await customObjectPage.searchFieldsConnection("HubSpot Owner Email");
      await customObjectPage.selectChecbox();
      await customObjectPage.selectStringDataType();
      await customObjectPage.setPrimaryKey();
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test(
      "Create a Hubspot Self Serve and delte the objects created through it and check Hubspot connection is still present",
      "Performing all basic operations in the object",
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const connectorsPage = new ConnectorsPage(page);
        const customObjectPage = new CustomObjectsV3(page);
        const Integration = "Hubspot";
        const connectionName = "Hubspot_Connection_2";
        const objName = connectionName + "_Obj";
        const VariableName = "Test";
        await connectorsPage.navigateToConnectors();
        await connectorsPage.conenctorsHub();
        await page.waitForURL("**/settings/connectors/new", { timeout: 5000 });
        await connectorsPage.createConenction(
          true,
          Integration,
          connectionName
        );
        await connectorsPage.accessToken(connectorCreds.hubspot.accessToken);
        await connectorsPage.clickBtn("Validate & Connect");
        await connectorsPage.verifyConnectionPopup(Integration);
        await connectorsPage.clickBtn("Create Object");
        await page.waitForURL("**/objects", { timeout: 5000 });
        await customObjectPage.clickCreateObjectFromConnection();
        await customObjectPage.pickConnection(connectionName);
        await customObjectPage.pickSourceObject("Companies", "companies");
        const fields = [
          { name: "Recent Deal Close Date", type: "Date" },
          { name: "HubSpot Owner Email", type: "String" },
          { name: "Record ID", type: "Number" },
        ];
        for (const field of fields) {
          await customObjectPage.searchFieldsConnection(field.name);
          await customObjectPage.selectChecbox();
          await customObjectPage.selectDataType(field.name, field.type);
        }
        await customObjectPage.setPrimaryKey();
        await customObjectPage.enterObjectName(objName);
        await customObjectPage.save();
        await customObjectPage.assertConnectionObjectSuccessMsg();
        await customObjectPage.searchObject(objName);
        await customObjectPage.clickObject(objName);
        await customObjectPage.pickMoreFieldsButton();
        await customObjectPage.searchFieldsConnection("Ann");
        await customObjectPage.selectChecbox();
        await customObjectPage.selectStringDataType();
        await customObjectPage.save();
        await customObjectPage.AddMoreFieldsButton();
        await customObjectPage.AddSingleFields();
        await customObjectPage.AddNameToSingleFields(VariableName);
        await customObjectPage.save();
        await customObjectPage.pickMoreFieldsButton();
        await customObjectPage.searchFieldsConnection("add");
        await customObjectPage.selectChecbox();
        await customObjectPage.selectDropDown();
        await customObjectPage.selectAlreadyCreatedVariable(VariableName);
        await customObjectPage.save();
        await customObjectPage.removeSuccessMessage();
        await customObjectPage.ManagePermissons();
        await customObjectPage.SelectUserInManage();
        await customObjectPage.save();
        await customObjectPage.searchObject(objName);
        await customObjectPage.clickObject(objName);
        await customObjectPage.removesMappingIcon();
        await customObjectPage.deletesVariableIcon();
        await customObjectPage.cancel();
        await customObjectPage.deleteObject(objName);
        await connectorsPage.navigateToConnectors();
        await connectorsPage.deleteConnection(connectionName, "");
      }
    );

    test("Salesforce Self Serve", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Salesforce";
      const connectionName = "Salesforce_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.clickBtn("Credentials");
      await connectorsPage.salesforceCreds(connectorCreds.salesforce);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject(
        "AI Application",
        "AIApplication"
      );
      await customObjectPage.selectDataType("AI Application ID", "Number");
      await customObjectPage.setPrimaryKey();
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test("Error notification for integration", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Salesforce";
      const connectionName = "Salesforce_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.clickBtn("Credentials");
      await connectorsPage.salesforceCreds(connectorCreds.salesforce);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.pickSourceObject("Opportunity", "Opportunity");
      await customObjectPage.selectDataType("Deleted", "Boolean");
      await customObjectPage.selectDataType("Last Modified Date", "Date");
      await customObjectPage.setPrimaryKey();
      const Datas = ["Number", "Date", "Percentage"];
      while (Datas.length > 0) {
        await customObjectPage.selectDataType("Opportunity ID", Datas.pop());
        await customObjectPage.save();}
      await customObjectPage.cancel();
      await customObjectPage.clickDiscardButton();
    });

    test("Netsuite Self Serve", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Netsuite";
      const connectionName = "Netsuite_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.netsuiteCreds(connectorCreds.netsuite);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject("salesOrdered", "salesOrdered");
      const fields = [
        { name: "trandate", type: "Date" },
        { name: "account", type: "Number" },
        { name: "amountnet", type: "Number" },
        { name: "type", type: "String" },
        { name: "uniquekey", type: "Number" },
      ];

      for (const field of fields) {
        await customObjectPage.searchFieldsConnection(field.name);
        await customObjectPage.selectChecbox();
        await customObjectPage.selectDataType(field.name, field.type);
      }
      await customObjectPage.setPrimaryKey();
      await expect(await page.getByText("5 fields selected")).toBeVisible({
        timeout: 5000,
      });
      await customObjectPage.searchFieldsConnection("trandate");
      await customObjectPage.changesSyncField("trandate");
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test.skip("MsSQL Self Serve", async ({ adminPage }, testInfo) => {
      // skipped - MsSQL connection cannot be tested without local sql setup config
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "MS SQL Server";
      const connectionName = "MS_SQL_Server_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.sqlServerCreds(connectorCreds.mssql);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject("salesOrdered", "salesOrdered");
      const fields = [
        { name: "trandate", type: "Date" },
        { name: "account", type: "Number" },
        { name: "amountnet", type: "Number" },
        { name: "type", type: "String" },
        { name: "uniquekey", type: "Number" },
      ];

      for (const field of fields) {
        await customObjectPage.searchFieldsConnection(field.name);
        await customObjectPage.selectChecbox();
        await customObjectPage.selectDataType(field.name, field.type);
      }
      await customObjectPage.setPrimaryKey();
      await expect(await page.getByText("5 fields selected")).toBeVisible({
        timeout: 5000,
      });
      await customObjectPage.searchFieldsConnection(fields[0].name);
      await customObjectPage.changesSyncField(fields[0].name);
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test.skip("PostgreSQL Self Serve", async ({ adminPage }, testInfo) => {
      // Need QA DB configuration to make it run on this client.
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "PostgreSQL";
      const connectionName = "PostgreSQL_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.postgreSQLCreds(connectorCreds.postgress);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject(
        "client_3022__snapshot_data",
        "airbyte.client_3022__snapshot_data"
      );
      await customObjectPage.selectAllSourceFields();
      await expect(await page.getByText("5 fields selected")).toBeVisible({
        timeout: 5000,
      });

      const fields = [
        { name: "amount", type: "Number" },
        { name: "created_date", type: "Date" },
        { name: "id", type: "Number" },
        { name: "is_deleted", type: "Boolean" },
        { name: "name", type: "String" },
      ];
      for (const field of fields) {
        await customObjectPage.searchFieldsConnection(field.name);
        await customObjectPage.selectDataType(field.name, field.type);
      }
      await customObjectPage.searchFieldsConnection(fields[1].name);
      await customObjectPage.changesSyncField(fields[1].name);
      await customObjectPage.searchFieldsConnection(fields[2].name);
      await customObjectPage.setPrimaryKey();
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test("Snowflake Self Serve", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Snowflake";
      const connectionName = "Snowflake_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.snowflakeCreds(connectorCreds.snowflake);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject("DEALS", "QA_SELF_SERVE.DEALS");
      await customObjectPage.selectAllSourceFields();
      await expect(await page.getByText("6 fields selected")).toBeVisible({
        timeout: 5000,
      });

      const fields = [
        { name: "AMOUNT", type: "Number" },
        { name: "CLOSE_DATE", type: "Date" },
        { name: "IS_DELETED", type: "Boolean" },
        { name: "NAME", type: "String" },
        { name: "UPDATED_ON", type: "Date" },
        { name: "ID", type: "Number" },
      ];

      for (const field of fields) {
        await customObjectPage.searchFieldsConnection(field.name);
        await customObjectPage.selectDataType(field.name, field.type);
      }
      await customObjectPage.setPrimaryKey();
      await customObjectPage.searchFieldsConnection(fields[1].name);
      await customObjectPage.changesSyncField(fields[1].name);
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test("Zoho Self Serve", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const customObjectPage = new CustomObjectsV3(page);

      const Integration = "Zoho";
      const connectionName = "Zoho_Connection_2";
      const objName = connectionName + "_Obj";

      await connectorsPage.navigateToConnectors(true);
      await connectorsPage.createConenction(true, Integration, connectionName);
      await connectorsPage.zohoCreds(connectorCreds.zoho);
      await connectorsPage.clickBtn("Validate & Connect");
      await connectorsPage.verifyConnectionPopup(Integration);
      await connectorsPage.clickBtn("Create Object");
      await page.waitForURL("**/objects", { timeout: 5000 });

      await customObjectPage.clickCreateObjectFromConnection();
      await customObjectPage.pickConnection(connectionName);
      await customObjectPage.pickSourceObject("Leads", "Leads");
      const fields = [
        { name: "Email", type: "Email" },
        { name: "Mobile", type: "Number" },
        { name: "Record Id", type: "String" },
      ];

      for (const field of fields) {
        await customObjectPage.searchFieldsConnection(field.name);
        await customObjectPage.selectChecbox();
        await customObjectPage.selectDataType(field.name, field.type);
      }
      await customObjectPage.setPrimaryKey();
      await customObjectPage.enterObjectName(objName);
      await customObjectPage.save();
      await customObjectPage.assertConnectionObjectSuccessMsg();
      await connectorsPage.navigateToConnectors();
      await connectorsPage.deleteConnection(
        connectionName,
        "There are 1 objects linked to this connection and they will get unlinked if you proceed."
      );
      // await connectorsPage.clickBtn("Go to Objects");
      await page.goto("/objects", {
        waitUntil: "networkidle",
      });
      await customObjectPage.deleteObject(objName);
    });

    test("All integrations  - Verify user is able to request for a new integration setup", async ({
      adminPage,
      request,
    }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);
      const IntegrationName = "AWS S3";
      const requirement = "I want to integrate AWS S3 connection.";
      await connectorsPage.navigateToConnectors(true);
      const token = await setupGraphQLRouteInterceptor(page);
      await connectorsPage.search(IntegrationName);
      await connectorsPage.createConenction(false, IntegrationName, "");
      await connectorsPage.fillRequirements(requirement);
      await connectorsPage.clickBtn("Submit");

      await connectorsPage.verifyEmailIsSent(
        request,
        token,
        IntegrationName,
        requirement
      );
      // Email API verification is possible only from QA ENV, not localhost.
      await connectorsPage.verifyToast("We've received your requirement");
    });

    test("Verify search integration and filter by is working as expected", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const connectorsPage = new ConnectorsPage(page);

      await connectorsPage.navigateToConnectors();
      await connectorsPage.clickBtn("Add new connection");
      await page.waitForURL("**/settings/connectors/new", { timeout: 5000 });
      await connectorsPage.clickBtn("Exit");
      await page.waitForURL("**/settings/connectors", { timeout: 5000 });
      await connectorsPage.navigateToConnectors(true);

      console.log("Filtered By All, Seach for H");
      await connectorsPage.search("H");
      let results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(11);
      expect(results.IntegrationNames).toEqual([
        "Bamboo HR",
        "Bullhorn",
        "Chargebee",
        "Employment Hero",
        "Freshsales",
        "Google Sheets",
        "Hubspot",
        "Redshift",
        "SharePoint",
        "Shopify",
        "Zoho",
      ]);
      expect(results.IntegrationTypes).toEqual([
        "HRIS",
        "HRIS",
        "Accounting and ERP",
        "HRIS",
        "CRM",
        "Database",
        "CRM",
        "Database",
        "Database",
        "Accounting and ERP",
        "CRM",
      ]);

      console.log("Filtered By CRM, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("CRM")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(3);
      expect(results.IntegrationNames).toEqual([
        "Freshsales",
        "Hubspot",
        "Zoho",
      ]);
      expect(results.IntegrationTypes).toEqual(["CRM", "CRM", "CRM"]);

      console.log("Filtered By Accounting and ERP, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("Accounting and ERP")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(2);
      expect(results.IntegrationNames).toEqual(["Chargebee", "Shopify"]);
      expect(results.IntegrationTypes).toEqual([
        "Accounting and ERP",
        "Accounting and ERP",
      ]);

      console.log("Filtered By Database, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("Database")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(3);
      expect(results.IntegrationNames).toEqual([
        "Google Sheets",
        "Redshift",
        "SharePoint",
      ]);
      expect(results.IntegrationTypes).toEqual([
        "Database",
        "Database",
        "Database",
      ]);

      console.log("Filtered By HRIS, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("HRIS")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(3);
      expect(results.IntegrationNames).toEqual([
        "Bamboo HR",
        "Bullhorn",
        "Employment Hero",
      ]);
      expect(results.IntegrationTypes).toEqual(["HRIS", "HRIS", "HRIS"]);

      console.log("Filtered By Contract Management, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("Contract Management")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(0);
      expect(results.IntegrationNames).toEqual([]);
      expect(results.IntegrationTypes).toEqual([]);

      console.log("Filtered By Collaboration, Seach for H");
      await connectorsPage.search("H");
      await page.locator('button:has-text("Collaboration")').click();
      results = await connectorsPage.searchIntegrationsList();
      expect(results.totalCount).toBe(0);
      expect(results.IntegrationNames).toEqual([]);
      expect(results.IntegrationTypes).toEqual([]);

      console.log("search an integration that doesn't exists");
      await connectorsPage.search("no connector");
      await page.locator('button:has-text("All")').click();
      await expect(page.getByText("No connectors found")).toBeVisible();
      await expect(
        page.getByText(
          "Don't see the integrations you need? Try adjusting your search or contact us"
        )
      ).toBeVisible();
      await connectorsPage.clickBtn("Contact us");

      const expectedText =
        "Contact us Tell us about the integration you're looking for. Please include the tool or service name and any specific requirements.";
      await connectorsPage.verifyPageText(expectedText, ".ant-modal-body");
      await connectorsPage.clickBtn("Submit");
      await expect(
        page.getByText("Please enter your requirements")
      ).toBeVisible();
      await connectorsPage.fillRequirements("Test contact us");
      await connectorsPage.clickBtn("Submit");
      await connectorsPage.verifyToast("Request sent successfully");

      console.log("Verify no connectors found prompt is shown");
      await connectorsPage.search("");
      await page.locator('button:has-text("All")').click();
      await expect(
        page.locator(`//span[text()="Don't see the integration you need?"]`)
      ).toBeVisible();
      await connectorsPage.clickBtn("Contact us");
      await connectorsPage.fillRequirements("Test contact us");
      await connectorsPage.clickBtn("Submit");
      await connectorsPage.verifyToast("Request sent successfully");

      await connectorsPage.clickBtn("connectors setup");
      const [newPage] = await Promise.all([
        page.context().waitForEvent("page"), // Waits for the new tab
        console.log("Waiting for a new tab to open..."),
      ]);
      await newPage.waitForLoadState();
      const newPageUrl = newPage.url();
      console.log("New page URL:", newPageUrl);
      expect(newPageUrl).toBe("https://support.everstage.com/support/login");
    });
  }
);
