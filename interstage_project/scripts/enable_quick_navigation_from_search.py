# ruff: noqa
from os import environ
from traceback import format_exc

from django import setup
from django.utils import timezone

environ.setdefault("DJANGO_SETTINGS_MODULE", "interstage_project.settings")
setup()

from commission_engine.models.client_models import Client
from everstage_ddd.global_search.collections import KeywordIndexCollection
from everstage_ddd.global_search.constants import AdminOperation, ConfigOption, Module
from everstage_ddd.global_search.services.proxies import ModFactory
from everstage_ddd.global_search.services.utils import toggle_feature
from spm.accessors.localization_accessor import LocalizationAccessor

if __name__ == "__main__":
    tag = "Enabling Quick Navigation from Global Search-{}".format(
        timezone.now().strftime("%Y%m%d")
    )
    clients = list(Client.objects.filter(is_deleted=False).order_by("client_id"))
    failed_clients = []
    indexed = False

    try:
        keywords_collection = KeywordIndexCollection()
        keywords_collection.drop()
        keywords_collection.create()
        keywords_collection.index()
        indexed = True

        for client in clients:
            print("-" * 50)
            id, name = client.client_id, client.name
            config = (client.client_features or {}).get("global_search", {})
            print("Client: {} | ID: {}".format(name, id))
            try:
                if not config.get("enabled", False):
                    success = toggle_feature(id, ConfigOption.ENABLED)
                    if success:
                        print("Enabled global search successfully")
                    else:
                        print("Failed to enable global search")
                        continue
                else:
                    print("Global search already enabled")

                module = ModFactory(client.client_id).get_module(Module.KEYWORDS.value)
                localization_accessor = LocalizationAccessor(client_id=id)
                if localization_accessor.client_kd_aware().exists():
                    print(
                        "Disabling Quick Navigation for Client As Localization is used: {} | ID: {}".format(
                            name, id
                        )
                    )
                    module.perform(AdminOperation.DEACTIVATION.value)
                else:
                    module.perform(AdminOperation.ACTIVATION.value)

            except Exception as e:
                failed_clients.append(id)
                print("Error in Activating Quick Navigation {}".format(str(e)))
                print(format_exc())
            else:
                print("Quick Navigation Activated Successfully")
    except Exception as e:
        print("Error Indexing Navigation Links {}".format(str(e)))
        print(format_exc())
    finally:
        if indexed:
            print("-" * 50)
            print("{} Completed".format(tag))
            print("-" * 50)
            if failed_clients:
                print(
                    "Failed to Activate Quick Navigation for Clients: {}".format(
                        ", ".join(str(client_id) for client_id in failed_clients)
                    )
                )
            else:
                print("Activated for all clients successfully")
