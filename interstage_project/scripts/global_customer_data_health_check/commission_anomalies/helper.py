from enum import Enum
from typing import Any, Dict, TypedDict

import pandas as pd
from django.db import connection


class AnomalyType(Enum):
    """
    Enum for the different types of anomalies.
    """

    PAYOUT_STATUS_MISSING = {
        "alert_key": "payout_status_missing",
        "description": "Payout status is missing",
    }

    PAYOUT_VALUE_MISMATCH = {
        "alert_key": "payout_value_mismatch",
        "description": "Payout status and statements value mismatch",
    }

    PAYOUT_SNAPSHOT_MISSING = {
        "alert_key": "payout_snapshot_missing",
        "description": "Payout snapshot is missing",
    }

    COMMISSION_REPORT_MISSING = {
        "alert_key": "commission_report_missing",
        "description": "Commission report is missing",
    }

    SETTLEMENT_SNAPSHOT_MISSING = {
        "alert_key": "settlement_snapshot_missing",
        "description": "Settlement snapshot is missing",
    }

    SETTLEMENT_REPORT_MISSING = {
        "alert_key": "settlement_report_missing",
        "description": "Settlement report is missing",
    }

    POSTGRES_COMMISSION_DUPLICATES = {
        "alert_key": "postgres_commission_duplicates",
        "description": "Commission duplicates found in Postgres",
    }

    POSTGRES_SETTLEMENT_DUPLICATES = {
        "alert_key": "postgres_settlement_duplicates",
        "description": "Settlement duplicates found in Postgres",
    }

    SNOWFLAKE_COMMISSION_DUPLICATES = {
        "alert_key": "snowflake_commission_duplicates",
        "description": "Commission duplicates found in Snowflake",
    }

    SNOWFLAKE_SETTLEMENT_DUPLICATES = {
        "alert_key": "snowflake_settlement_duplicates",
        "description": "Settlement duplicates found in Snowflake",
    }

    SNOWFLAKE_PAYOUT_SNAPSHOT_DUPLICATES = {
        "alert_key": "snowflake_payout_snapshot_duplicates",
        "description": "Payout snapshot duplicates found in Snowflake",
    }

    SNOWFLAKE_SETTLEMENT_SNAPSHOT_DUPLICATES = {
        "alert_key": "snowflake_settlement_snapshot_duplicates",
        "description": "Settlement snapshot duplicates found in Snowflake",
    }

    FX_RATE_MISSING = {
        "alert_key": "fx_rate_mapping_missing",
        "description": "FX Rate mapping is missing",
    }

    LINE_ITEM_MISSING = {
        "alert_key": "line_item_missing",
        "description": "Line item is missing",
    }

    QUOTA_FREQUENCY_MISMATCH = {
        "alert_key": "quota_frequency_mismatch",
        "description": "Quota frequency mismatch",
    }

    QUOTA_DATA_ZERO = {
        "alert_key": "quota_data_zero",
        "description": "Quota data is zero",
    }
    POSTGRES_SNOWFLAKE_MISMATCH = {
        "alert_key": "postgres_snowflake_mismatch",
        "description": "Postgres and Snowflake data mismatch",
    }

    BASE_TABLE_SNAPSHOT_MISMATCH = {
        "alert_key": "base_table_snapshot_mismatch",
        "description": "Base table and snapshot table data mismatch",
    }

    SNAPSHOT_REPORT_MISMATCH = {
        "alert_key": "snapshot_report_mismatch",
        "description": "Snapshot table and report table data mismatch",
    }

    REPORT_NON_EFF_DATA_DUPLICATE = {
        "alert_key": "non_effective_data_duplicates",
        "description": "Non Effective data is duplicated",
    }


PRIME_LOGIC_EMAIL_ID = "<EMAIL>"


class AlertData(TypedDict):
    """Type definition for alert data structure"""

    alert_name: str
    description: str
    alert_key: str
    metadata: Dict[str, Any]
    check_type: str


def create_alert_data(
    anomaly_type: AnomalyType,
    alert_key: str,
    check_type: str,
    metadata: Any,
) -> AlertData:
    """Creates alert data for a given entry and anomaly type."""
    alert_data: AlertData = {
        "alert_name": f"{anomaly_type.value['alert_key'].replace('_', ' ').title()}",
        "description": anomaly_type.value["description"],
        "alert_key": alert_key,
        "check_type": check_type,
        "metadata": metadata,
    }
    return alert_data


def fetch_postgres_data(query, params):
    with connection.cursor() as cursor:
        cursor.execute(query, params)
        columns = [col[0] for col in cursor.description]
        result = cursor.fetchall()
    return pd.DataFrame(result, columns=columns)


def fetch_snowflake_data(client_id, query, params=None):
    from spm.services.stormbreaker.snowflake import SnowflakeDataStore

    recs = SnowflakeDataStore.create_connection_and_execute_query(
        client_id, query, params
    )
    result_df = recs.fetch_pandas_all()
    result_df = result_df.rename(columns=str.lower)
    return result_df
