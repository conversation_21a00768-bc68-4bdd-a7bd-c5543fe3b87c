{"LogEntry": {"action_time": "DateTimeField", "user": "ForeignKey", "content_type": "ForeignKey", "object_id": "TextField", "object_repr": "CharField", "action_flag": "PositiveSmallIntegerField", "change_message": "TextField"}, "Permission": {"name": "CharField", "content_type": "ForeignKey", "codename": "CharField"}, "Group": {"name": "CharField", "permissions": "ManyToManyField"}, "User": {"password": "CharField", "last_login": "DateTimeField", "is_superuser": "BooleanField", "username": "CharField", "first_name": "CharField", "last_name": "CharField", "email": "EmailField", "is_staff": "BooleanField", "is_active": "BooleanField", "date_joined": "DateTimeField", "groups": "ManyToManyField", "user_permissions": "ManyToManyField"}, "ContentType": {"app_label": "CharField", "model": "CharField"}, "Session": {"session_key": "CharField", "session_data": "TextField", "expire_date": "DateTimeField"}, "Client": {"client_id": "ESAutoField", "name": "ESCharField", "domain": "ESCharField", "logo_url": "ESCharField", "auth_connection_name": "ESCharField", "connection_type": "ESCharField", "base_currency": "ESCharField", "fiscal_start_month": "ESIntegerField", "secondary_calculator": "ESCharField", "client_notification": "ESBooleanField", "payee_notification": "ESBooleanField", "meta_info": "ESJSONField", "logo_s3_path": "ESFileField", "statement_logo_url": "ESCharField", "statement_logo_s3_path": "ESFileField", "time_zone": "ESCharField", "client_features": "ESJSONField", "is_deleted": "ESBooleanField"}, "ClientSettings": {"client": "ESForeignKey", "icm_settings": "ESJSONField", "cpq_settings": "ESJSONField"}, "EverObjectVariable": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "data_type_id": "ESIntegerField", "system_name": "ESCharField", "display_name": "ESCharField", "ever_object_id": "ESCharField"}, "EverObject": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "ever_object_id": "ESCharField", "name": "ESCharField", "data_origin": "ESCharField", "source_table": "ESCharField", "primary_key": "ESJSONField", "table_name": "ESCharField"}, "CommissionReportEnrichment": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "system_name": "ESCharField", "report_system_name": "ESCharField", "display_name": "ESCharField", "report_type": "ESCharField", "data_type": "ESForeignKey", "meta_data": "ESJSONField"}, "Databook": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "databook_id": "ESUUIDField", "name": "ESCharField", "is_draft": "ESBooleanField", "is_archived": "ESBooleanField", "created_at": "ESDateTimeField", "created_by": "ESEmailField", "datasheet_order": "ESJSONField"}, "Datasheet": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "name": "ESCharField", "description": "ESTextField", "source_type": "ESCharField", "source_id": "ESCharField", "source_databook_id": "ESUUIDField", "order": "ESIntegerField", "primary_key": "ESJSONField", "additional_details": "ESJSONField", "transformation_spec": "ESJSONField", "ordered_columns": "ESArrayField", "hidden_columns": "ESArrayField", "tags": "ESJSONField", "is_pk_modified": "ESBooleanField", "is_datasheet_generated": "ESBooleanField", "is_config_changed": "ESBooleanField", "is_calc_field_changed": "ESBooleanField", "data_origin": "ESCharField", "data_last_updated_at": "ESDateTimeField"}, "DSFilterOperators": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "id": "ESUUIDField", "name": "ESCharField", "alt_name": "ESCharField", "category": "ESCharField", "operand_type_ids": "ESJSONField", "output_type_ids": "ESJSONField", "needs_operand": "ESBooleanField", "multi_valued": "ESBooleanField"}, "DatasheetAdjustments": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_number": "ESIntegerField", "sub_adjustment_number": "ESCharField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "original_row_key": "ESCharField", "row_key": "ESCharField", "operation": "ESCharField", "data": "ESJSONField", "comments": "ESCharField", "is_global": "ESBooleanField"}, "DatasheetPermissions": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "filter_list": "ESJSONField", "permission_set_id": "ESUUIDField", "permission_set_name": "ESCharField", "columns_to_be_hidden": "ESArrayField"}, "DSPermissionsTarget": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "target_type": "ESCharField", "target": "ESCharField", "permission_set_id": "ESUUIDField"}, "DSSnapshotInfoLocal": {"client": "ESForeignKey", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "generated_time": "ESDateTimeField", "path": "ESCharField", "checksum": "ESCharField"}, "DbkdPkdMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "primary_kd": "ESDateTimeField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField"}, "DatasheetVariable": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "system_name": "ESCharField", "display_name": "ESCharField", "description": "ESTextField", "data_type": "ESForeignKey", "source_cf_meta_data": "ESJSONField", "tags": "ESJSONField", "field_order": "ESIntegerField", "meta_data": "ESJSONField", "source_variable_id": "ESCharField", "source_id": "ESCharField", "variable_id": "ESUUIDField", "is_selected": "ESBooleanField", "source_type": "ESCharField", "is_primary": "ESBooleanField", "warning": "ESTextField"}, "DatasheetFilter": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "filter_id": "ESUUIDField", "filter_name": "ESCharField", "filter_type": "ESCharField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "last_updated_by": "ESCharField", "filter_list": "ESJSONField", "pivot_object": "ESJSONField", "config_type": "ESCharField"}, "SkdPkdMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "primary_kd": "ESDateTimeField"}, "CustomObjectPkdMap": {"temporal_id": "ESBigAutoField", "client": "ESForeignKey", "custom_object_id": "ESIntegerField", "primary_kd": "ESDateTimeField", "e2e_sync_run_id": "ESUUIDField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField"}, "ReportObjectPkdMap": {"temporal_id": "ESBigAutoField", "client": "ESForeignKey", "report_object_id": "ESCharField", "primary_kd": "ESDateTimeField", "report_type": "ESCharField", "e2e_sync_run_id": "ESUUIDField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField"}, "CustomObject": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "custom_object_id": "ESIntegerField", "name": "ESCharField", "primary_key": "ESJSONField", "tags": "ESJSONField", "snapshot_key": "ESJSONField", "created_at": "ESDateTimeField", "created_by": "ESCharField", "ordered_columns": "ESArrayField", "csv_header_map": "ESJSONField"}, "CustomObjectVariable": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "custom_object_id": "ESIntegerField", "system_name": "ESCharField", "display_name": "ESCharField", "data_type": "ESForeignKey", "tags": "ESJSONField"}, "CustomObjectData": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "custom_object_id": "ESIntegerField", "row_key": "ESCharField", "data": "ESJSONField", "snapshot_value": "ESCharField"}, "COPermissions": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "object_id": "ESCharField", "object_type": "ESCharField", "is_include": "ESBooleanField", "target_type": "ESCharField", "target": "ESCharField"}, "COSnapshotInfoLocal": {"client": "ESForeignKey", "object_id": "ESIntegerField", "generated_time": "ESDateTimeField", "path": "ESCharField"}, "Commission": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "amount": "ESDecimalField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField", "show_do_nothing": "ESBooleanField", "commission_date": "ESDateTimeField"}, "QuotaErosion": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "quota_category_name": "ESCharField", "qv": "ESDecimalField", "cumulative_qe": "ESDecimalField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "quota_erosion": "ESDecimalField", "is_team": "ESBooleanField", "team_id": "ESCharField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField"}, "CommissionLock": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "locked_knowledge_date": "ESDateTimeField", "is_locked": "ESBooleanField", "lock_id": "ESUUIDField", "commission_plan_ids": "ESJSONField", "commission_snapshot_id": "ESUUIDField"}, "CommLockDetail": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "line_item_type": "ESCharField", "line_item_ids": "ESJSONField", "amount": "ESDecimalField", "lock_id": "ESUUIDField"}, "InterCommission": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "amount": "ESDecimalField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField", "show_do_nothing": "ESBooleanField", "commission_date": "ESDateTimeField"}, "InterQuotaErosion": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "quota_category_name": "ESCharField", "qv": "ESDecimalField", "cumulative_qe": "ESDecimalField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "quota_erosion": "ESDecimalField", "is_team": "ESBooleanField", "team_id": "ESCharField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField"}, "CommissionSecondaryKd": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "sec_kd": "ESDateTimeField"}, "ApiAccessConfig": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "source_object_id": "ESCharField", "request_url": "ESCharField", "request_type": "ESCharField", "request_body": "ESJSONField", "request_header": "ESJSONField", "access_token_config_id": "ESIntegerField", "integration": "ESCharField", "response_key": "ESCharField", "additional_data": "ESJSONField", "integration_id": "ESUUIDField"}, "AccessTokenConfig": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "access_token_config_id": "ESAutoField", "access_type": "ESCharField", "payload_type": "ESCharField", "api_access_key": "ESCharField", "access_token_url": "ESCharField", "access_request_body": "ESJSONField", "jwt_data": "ESJSONField", "service_name": "ESCharField", "domain": "ESCharField", "connection_name": "ESCharField", "connection_status": "ESCharField", "connection_type": "ESCharField", "created_on": "ESDateTimeField", "additional_data": "ESJSONField"}, "ExtractionConfig": {"client_id": "ESIntegerField", "priority": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_disabled": "ESBooleanField", "task_group": "ESCharField", "task": "ESCharField", "source_object_id": "ESCharField", "destination_object_id": "ESCharField", "sync_type": "ESCharField", "destination_object_type": "ESCharField", "integration_id": "ESUUIDField", "access_token_config_id": "ESIntegerField", "additional_data": "ESJSONField"}, "TransformationConfig": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "source_object_id": "ESCharField", "destination_object_id": "ESCharField", "source_field": "ESCharField", "destination_field": "ESCharField", "field_type": "ESCharField", "transformation_logic_id": "ESIntegerField", "integration_id": "ESUUIDField", "additional_config": "ESJSONField"}, "TransformationLogic": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "source_object_id": "ESCharField", "logic": "ESTextField"}, "EnrichmentConfig": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "source_object_id": "ESCharField", "ref_object_key": "ESCharField", "enrichment_resp_key": "ESCharField", "enrichment_id_key": "ESCharField", "source_new_key": "ESCharField", "enrichment_type": "ESCharField", "ref_api_config_obj": "ESCharField", "integration_id": "ESUUIDField", "additional_data": "ESJSONField"}, "Integration": {"client_id": "ESIntegerField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "integration_id": "ESUUIDField", "name": "ESCharField", "service_name": "ESCharField", "desc": "ESCharField", "logo_url": "ESCharField", "source_object_id": "ESCharField", "properties": "ESJSONField", "additional_data": "ESJSONField", "is_api": "ESBooleanField", "destination_object_id": "ESIntegerField", "hyperlink_info": "ESJSONField", "batch_etl_page_size": "ESIntegerField", "preprocessing_metadata": "ESJSONField", "transformation_logic": "ESTextField"}, "EtlGlobalSyncStatus": {"temporal_id": "ESBigAutoField", "global_sync_disabled_at": "ESDateTimeField", "global_sync_enabled_at": "ESDateTimeField", "enabled_by": "ESEmailField", "disabled_by": "ESEmailField", "comments": "ESTextField"}, "UpstreamETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_id": "ESCharField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "sync_mode": "ESCharField", "changes_start_time": "ESDateTimeField", "changes_end_time": "ESDateTimeField", "delete_start_time": "ESDateTimeField", "delete_end_time": "ESDateTimeField", "extraction_start_time": "ESDateTimeField", "extraction_status": "ESCharField", "extraction_end_time": "ESDateTimeField", "transformation_start_time": "ESDateTimeField", "transformation_status": "ESCharField", "transformation_end_time": "ESDateTimeField", "load_start_time": "ESDateTimeField", "load_status": "ESCharField", "load_end_time": "ESDateTimeField", "filter_query": "ESJSONField", "source": "ESCharField", "audit": "ESCharField", "sync_run_log": "ESJSONField", "integration_id": "ESUUIDField", "extracted_records_count": "ESIntegerField", "deleted_records_count": "ESIntegerField"}, "UpstreamCsvETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_id": "ESCharField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "sync_mode": "ESCharField", "changes_start_time": "ESDateTimeField", "changes_end_time": "ESDateTimeField", "extraction_start_time": "ESDateTimeField", "extraction_status": "ESCharField", "extraction_end_time": "ESDateTimeField", "filter_query": "ESJSONField", "source": "ESCharField", "audit": "ESCharField", "sync_run_log": "ESJSONField"}, "ETLLock": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "lock_id": "ESBigAutoField", "client_id": "ESIntegerField", "object_id": "ESCharField", "sync_mode": "ESCharField", "lock_acquired_time": "ESDateTimeField", "lock_released_time": "ESDateTimeField", "is_active": "ESBooleanField", "lock_name": "ESCharField"}, "ExtractionSyncLog": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_name": "ESCharField", "object_id": "ESCharField", "extracted_objects": "ESJSONField", "operation": "ESCharField", "updated_at": "ESDateTimeField"}, "TransformationSyncLog": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_name": "ESCharField", "object_id": "ESCharField", "transformed_objects": "ESJSONField", "updated_at": "ESDateTimeField"}, "LoadSyncLog": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_name": "ESCharField", "object_id": "ESCharField", "loaded_objects": "ESJSONField", "operation": "ESCharField", "updated_at": "ESDateTimeField"}, "CommissionETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESCharField", "job_count": "ESIntegerField", "sync_run_log": "ESJSONField"}, "ForecastETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESCharField", "job_count": "ESIntegerField", "sync_run_log": "ESJSONField"}, "DatabookETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "task": "ESCharField", "primary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "sync_type": "ESCharField", "audit": "ESCharField", "sync_run_log": "ESJSONField"}, "ReportETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "object_id": "ESCharField", "task": "ESCharField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "sync_mode": "ESCharField", "changes_start_time": "ESDateTimeField", "changes_end_time": "ESDateTimeField", "audit": "ESCharField", "sync_run_log": "ESJSONField"}, "SettlementETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "primary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESCharField", "datasheet_id": "ESUUIDField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "sync_run_log": "ESJSONField", "job_count": "ESIntegerField"}, "PayoutSnapshotETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "primary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESCharField", "payees": "ESJSONField", "sync_run_log": "ESJSONField", "job_count": "ESIntegerField"}, "SettlementSnapshotETLStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "primary_kd": "ESDateTimeField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESCharField", "payees": "ESJSONField", "sync_run_log": "ESJSONField", "job_count": "ESIntegerField"}, "ETLSyncStatus": {"e2e_sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "sync_status": "ESCharField", "sync_start_time": "ESDateTimeField", "sync_end_time": "ESDateTimeField", "params": "ESJSONField", "audit": "ESJSONField"}, "PlanModificationSyncStatus": {"e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "task": "ESCharField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "audit": "ESJSONField", "sync_run_log": "ESJSONField"}, "ForecastCommission": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "amount": "ESDecimalField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField", "show_do_nothing": "ESBooleanField", "commission_date": "ESDateTimeField"}, "ForecastQuotaErosion": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "quota_category_name": "ESCharField", "qv": "ESDecimalField", "cumulative_qe": "ESDecimalField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "quota_erosion": "ESDecimalField", "is_team": "ESBooleanField", "team_id": "ESCharField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField"}, "InterForecastCommission": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "amount": "ESDecimalField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField", "show_do_nothing": "ESBooleanField", "commission_date": "ESDateTimeField"}, "InterForecastQuotaErosion": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "quota_category_name": "ESCharField", "qv": "ESDecimalField", "cumulative_qe": "ESDecimalField", "line_item_type": "ESCharField", "line_item_id": "ESTextField", "tier_id": "ESCharField", "original_tier_id": "ESCharField", "quota_erosion": "ESDecimalField", "is_team": "ESBooleanField", "team_id": "ESCharField", "primary_kd": "ESDateTimeField", "secondary_kd": "ESDateTimeField", "secondary_snapshot_id": "ESUUIDField", "commission_snapshot_id": "ESUUIDField", "context_ids": "ESJSONField"}, "ForecastSecondaryKd": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "sec_kd": "ESDateTimeField"}, "HrisConfig": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "datasheet_id": "ESUUIDField", "databook_id": "ESUUIDField", "user_field": "ESCharField", "source_field": "ESCharField", "field_context": "ESCharField"}, "Hyperlink": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "hyperlink_field": "ESCharField", "url": "ESURLField", "custom_object_id": "ESIntegerField"}, "PayoutStatus": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "payment_status": "ESCharField", "total_payout": "ESDecimalField", "paid_amount": "ESDecimalField", "processed_amount": "ESDecimalField", "ignored_amount": "ESDecimalField", "pending_amount": "ESDecimalField", "commission_percentage": "ESDecimalField", "commission": "ESDecimalField", "comm_calc_status": "ESCharField", "settlement_calc_status": "ESCharField", "comm_calc_details": "ESJSONField", "settlement_calc_details": "ESJSONField", "payout_split_up": "ESJSONField", "fx_rate": "ESDecimalField", "variable_pay_as_per_period": "ESDecimalField", "payout_frequency": "ESCharField"}, "PayoutStatusChanges": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESEmailField", "created_at": "ESDateTimeField", "change_type": "ESCharField"}, "NotificationTask": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESCharField", "task": "ESCharField", "frequency": "ESCharField", "cron_id": "ESIntegerField", "is_event_based": "ESBooleanField"}, "MilestoneNotificationStatus": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESCharField", "task": "ESCharField", "milestone": "ESFloatField", "time_period_label": "ESCharField"}, "ETLTask": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "task": "ESCharField", "periodic_task_id": "ESIntegerField", "arguments": "ESJSONField"}, "SettlementRule": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "settlement_rule_id": "ESUUIDField", "name": "ESCharField", "description": "ESCharField", "settlement_flag_expr": "ESJSONField", "amount_expr": "ESJSONField", "settlement_type": "ESCharField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "date_field": "ESCharField", "settlement_join_keys": "ESJSONField", "commission_join_keys": "ESJSONField", "criteria_column": "ESJSONField", "is_valid": "ESBooleanField"}, "CommissionSettlementMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "commission_plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "settlement_rule_id": "ESUUIDField"}, "Settlement": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "settlement_date": "ESDateTimeField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "commission_date": "ESDateTimeField", "comm_period_start_date": "ESDateTimeField", "comm_period_end_date": "ESDateTimeField", "commission_row_key": "ESTextField", "settlement_rule_id": "ESUUIDField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "payee_email_id": "ESCharField", "line_item_id": "ESTextField", "amount": "ESDecimalField", "settlement_flag": "ESBooleanField"}, "SettlementLock": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "locked_knowledge_date": "ESDateTimeField", "is_locked": "ESBooleanField", "lock_id": "ESUUIDField", "plan_ids": "ESJSONField"}, "SettlementLockDetail": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "payee_email_id": "ESCharField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "settlement_rule_id": "ESUUIDField", "amount": "ESDecimalField", "lock_id": "ESUUIDField", "line_item_id": "ESJSONField"}, "SettlementKdMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "payee_email_id": "ESCharField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "settlement_rule_id": "ESUUIDField", "primary_kd": "ESDateTimeField"}, "TagCategory": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "id": "ESUUIDField", "system_name": "ESCharField", "display_name": "ESCharField", "constraints": "ESJSONField", "type": "ESJSONField", "options": "ESJSONField", "description": "ESCharField"}, "TagValue": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "tag_category_id": "ESUUIDField", "system_name": "ESCharField", "display_name": "ESCharField"}, "ReportObjectData": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "object_id": "ESCharField", "object_type": "ESCharField", "row_key": "ESCharField", "snapshot_key": "ESCharField", "data": "ESJSONField"}, "FrozenPayrollData": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "fiscal_year": "ESCharField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESCharField", "variable_pay_in_base_currency": "ESDecimalField", "variable_pay_in_payee_currency": "ESDecimalField"}, "ReportSnapshotInfoLocal": {"client": "ESForeignKey", "object_id": "ESCharField", "object_type": "ESCharField", "generated_time": "ESDateTimeField", "path": "ESCharField"}, "DatasheetDatasetMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "superset_dataset_id": "ESIntegerField", "datasheet_id": "ESUUIDField", "databook_id": "ESUUIDField", "table_name": "ESTextField", "created_by": "ESEmailField"}, "Employee": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "first_name": "ESCharField", "last_name": "ESCharField", "user_role": "ESJSONField", "user_source": "ESCharField", "time_zone": "ESCharField", "email_alias": "ESCharField", "created_date": "ESDateTimeField", "created_by": "ESEmailField", "profile_picture": "ESURLField", "login_mode": "ESCharField", "send_notification": "ESBooleanField", "employee_config": "ESJSONField", "exit_date": "ESDateTimeField", "deactivation_date": "ESDateTimeField", "last_commission_date": "ESDateTimeField", "status": "ESCharField"}, "EmployeePayroll": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "employee_id": "ESCharField", "joining_date": "ESDateTimeField", "exit_date": "ESDateTimeField", "designation": "ESCharField", "level": "ESCharField", "employee_status": "ESCharField", "employment_country": "ESCharField", "fixed_pay": "ESDecimalField", "variable_pay": "ESDecimalField", "pay_currency": "ESCharField", "payout_frequency": "ESCharField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "payee_role": "ESCharField"}, "Hierarchy": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "reporting_manager_email_id": "ESEmailField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField"}, "PlanDetails": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "plan_id": "ESUUIDField", "plan_name": "ESCharField", "plan_type": "ESCharField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "settlement_end_date": "ESDateTimeField", "plan_exclusion_start_date": "ESDateTimeField", "plan_exclusion_end_date": "ESDateTimeField"}, "TeamConfig": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "team_id": "ESCharField", "team_name": "ESCharField", "team_created_by": "ESEmailField", "team_created_time": "ESDateTimeField", "team_updated_by": "ESEmailField", "team_updated_time": "ESDateTimeField", "team_type": "ESCharField", "pod_owner_name": "ESCharField"}, "Membership": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "team_id": "ESCharField", "group_member_email_id": "ESEmailField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField"}, "ApprovalTemplate": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "template_id": "ESUUIDField", "entity_type": "ESCharField", "template_name": "ESCharField", "template_description": "ESCharField", "stage_order": "ESJSONField", "is_active": "ESBooleanField", "created_by": "ESEmailField", "updated_by": "ESEmailField", "notify_on_reject": "ESJSONField", "notify_on_approve": "ESJSONField"}, "ApprovalTemplateStage": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "stage_template_id": "ESUUIDField", "template_id": "ESUUIDField", "stage_name": "ESCharField", "approvers": "ESJSONField", "approval_strategy": "ESCharField", "due_period": "ESIntegerField", "approval_trigger": "ESCharField", "cool_off_period": "ESIntegerField", "is_auto_approve": "ESBooleanField", "notes": "ESCharField", "stage_order": "ESIntegerField"}, "ApprovalInstance": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "approval_wf_instance_id": "ESUUIDField", "template_id": "ESUUIDField", "entity_type": "ESCharField", "entity_key": "ESCharField", "status": "ESCharField", "requested_time": "ESDateTimeField", "completion_time": "ESDateTimeField", "notify_on_reject": "ESJSONField", "notify_on_approve": "ESJSONField", "notify_all_approvers": "ESBooleanField", "notify_all_approvers_on_approve": "ESBooleanField", "created_by": "ESEmailField", "is_system_action": "ESBooleanField", "instance_data": "ESJSONField", "is_active": "ESBooleanField"}, "ApprovalInstanceStage": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "stage_instance_id": "ESUUIDField", "approval_wf_instance_id": "ESUUIDField", "stage_template_id": "ESUUIDField", "stage_name": "ESCharField", "stage_order": "ESIntegerField", "approvers": "ESJSONField", "approval_strategy": "ESCharField", "due_date": "ESDateTimeField", "approval_trigger": "ESCharField", "cool_off_date": "ESDateTimeField", "is_auto_approve": "ESBooleanField", "is_system_action": "ESBooleanField", "status": "ESCharField", "initiated_time": "ESDateTimeField", "requested_time": "ESDateTimeField", "completed_time": "ESDateTimeField", "stage_template_data": "ESJSONField", "notes": "ESCharField"}, "ApprovalRequests": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "approval_request_id": "ESUUIDField", "approval_wf_instance_id": "ESUUIDField", "stage_instance_id": "ESUUIDField", "entity_type": "ESCharField", "entity_key": "ESCharField", "approver": "ESEmailField", "status": "ESCharField", "is_system_action": "ESBooleanField", "requested_time": "ESDateTimeField", "completed_time": "ESDateTimeField", "comments": "ESCharField", "approve_on_request": "ESBooleanField", "request_data": "ESJSONField"}, "SubApprovalRequests": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "sub_request_id": "ESUUIDField", "request_id": "ESUUIDField", "stage_instance_id": "ESUUIDField", "approval_wf_instance_id": "ESUUIDField", "sub_entity_id": "ESTextField", "sub_request_data": "ESJSONField", "status": "ESCharField", "is_system_action": "ESBooleanField", "approver": "ESEmailField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "completed_time": "ESDateTimeField", "comments": "ESTextField", "requested_time": "ESDateTimeField"}, "AuditTrail": {"client": "ESForeignKey", "audit_trail_id": "ESBigAutoField", "event_type_code": "ESCharField", "event_key": "ESCharField", "updated_at": "ESDateTimeField", "summary": "ESCharField", "details": "ESJSONField", "details_diff": "ESJSONField", "updated_by_masked": "ESEmailField", "updated_by_unmasked": "ESEmailField", "associated_support_membership": "ESUUIDField"}, "CommissionPlan": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "plan_name": "ESCharField", "plan_type": "ESCharField", "plan_start_date": "ESDateTimeField", "plan_end_date": "ESDateTimeField", "additional_config": "ESJSONField", "is_draft": "ESBooleanField", "created_by": "ESEmailField", "created_on": "ESDateTimeField", "approved_by": "ESEmailField", "approved_on": "ESDateTimeField", "plan_display_order": "ESIntegerField", "is_settlement_end_date": "ESBooleanField", "settlement_end_date": "ESDateTimeField", "payout_frequency": "ESCharField"}, "PlanCriteria": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "criteria_name": "ESCharField", "criteria_type": "ESCharField", "criteria_description": "ESCharField", "criteria_display_order": "ESIntegerField", "criteria_data": "ESJSONField", "criteria_column": "ESJSONField", "criteria_config": "ESJSONField", "criteria_level": "ESCharField", "simulate_column": "ESJSONField"}, "PlanPayee": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "plan_id": "ESUUIDField", "plan_type": "ESCharField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "settlement_end_date": "ESDateTimeField", "plan_exclusion_start_date": "ESDateTimeField", "plan_exclusion_end_date": "ESDateTimeField"}, "PlanDocs": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "file_name": "ESCharField", "doc": "ESFileField", "employee_email_id": "ESEmailField"}, "TempPlanDocs": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "file_name": "ESCharField", "doc": "ESFileField", "employee_email_id": "ESEmailField"}, "PlanModificationChanges": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "plan_id": "ESUUIDField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "change_type": "ESCharField", "payee_email_id": "ESEmailField", "created_at": "ESDateTimeField"}, "CommissionChanges": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESEmailField", "created_at": "ESDateTimeField", "change_type": "ESCharField"}, "SettlementChanges": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESEmailField", "created_at": "ESDateTimeField", "change_type": "ESCharField"}, "Countries": {"id": "ESAutoField", "country_code": "ESCharField", "country_name": "ESCharField", "currency_code": "ESCharField", "currency_symbol": "ESCharField", "is_active": "ESBooleanField", "locale_id": "ESCharField", "is_client_specific": "ESBooleanField", "client_ids": "ESJSONField"}, "ContractFields": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "template_id": "ESCharField", "selected_role": "ESCharField", "template_fields": "ESJSONField"}, "CustomCalendar": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "calendar_id": "ESUUIDField", "name": "ESCharField", "variable_pay_type": "ESCharField", "calendar_type": "ESCharField", "week_start_day": "ESCharField"}, "CustomPeriods": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "calendar_id": "ESUUIDField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "period_label": "ESCharField"}, "CustomCategory": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "custom_category_id": "ESUUIDField", "custom_category_name": "ESCharField", "created_by": "ESEmailField", "created_at": "ESDateTimeField", "is_default": "ESBooleanField", "is_active": "ESBooleanField", "display_order": "ESIntegerField", "module_name": "ESCharField"}, "VariableDataType": {"id": "ESIntegerField", "data_type": "ESCharField", "data_type_meta": "ESJSONField", "compatible_type_ids": "ESJSONField"}, "Variable": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "id": "ESUUIDField", "name": "ESCharField", "system_name": "ESCharField", "category": "ESCharField", "data_type": "ESForeignKey", "model": "ESCharField", "tags": "ESJSONField", "applicable_primary_objects": "ESJSONField", "variable_type": "ESCharField", "options": "ESJSONField"}, "ClientVariable": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "variable_id": "ESUUIDField", "display_name": "ESCharField", "tags": "ESJSONField", "options": "ESJSONField"}, "Operators": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "id": "ESUUIDField", "name": "ESCharField", "alt_name": "ESCharField", "category": "ESCharField", "operand_type_ids": "ESJSONField", "output_type_ids": "ESJSONField", "needs_operand": "ESBooleanField", "multi_valued": "ESBooleanField"}, "CustomFields": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "display_name": "ESCharField", "system_name": "ESCharField", "field_type": "ESCharField", "options": "ESJSONField", "is_archived": "ESBooleanField", "display_order": "ESIntegerField", "data_type": "ESForeignKey", "is_mandatory": "ESBooleanField", "is_effective_dated": "ESBooleanField"}, "CustomFieldData": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "email": "ESCharField", "data": "ESJSONField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField"}, "Dashboard": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "dashboard_id": "ESUUIDField", "dashboard_type": "ESCharField", "name": "ESCharField", "description": "ESCharField", "created_at": "ESDateTimeField", "created_by": "ESEmailField", "last_modified": "ESDateTimeField", "is_favorite": "ESBooleanField", "is_default": "ESBooleanField", "superset_dashboard_id": "ESIntegerField", "superset_dataset_ids": "ESArrayField"}, "DashboardUserMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "dashboard_id": "ESUUIDField", "employee_email_id": "ESEmailField"}, "DashboardUserGroupMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "dashboard_id": "ESUUIDField", "user_group_id": "ESUUIDField"}, "Widget": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "widget_id": "ESUUIDField", "name": "ESCharField", "dashboard_id": "ESUUIDField", "position": "ESJSONField", "dimension": "ESJSONField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "widget_data": "ESJSONField", "bi_tool_datasheet_id": "ESCharField"}, "Docusign": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "email_id": "ESEmailField", "login_email_id": "ESEmailField", "account_id": "ESCharField", "refresh_token": "ESCharField", "refresh_token_expires_at": "ESDateTimeField", "base_uri": "ESCharField"}, "TemplateDetails": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "account_id": "ESCharField", "template_id": "ESCharField", "plan_id": "ESCharField", "primary_recipient_role": "ESCharField", "fiscal_year": "ESIntegerField", "is_archived": "ESBooleanField"}, "DocusignDocuments": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "template_id": "ESCharField", "envelope_id": "ESCharField", "employee_email_id": "ESEmailField", "fiscal_year": "ESIntegerField", "file_name": "ESCharField", "s3_url": "ESURLField", "is_archived": "ESBooleanField"}, "Draws": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "draw_year": "ESCharField", "draws": "ESJSONField", "draw_updated_by": "ESEmailField"}, "DRS": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "drs_id": "ESCharField", "logger": "ESEmailField", "assignee": "ESEmailField", "subject": "ESCharField", "status": "ESCharField", "category": "ESCharField", "logged_time": "ESDateTimeField", "involved_users": "ESJSONField", "sequence_number": "ESIntegerField"}, "DRSUpdates": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "drs_id": "ESCharField", "meta": "ESJSONField", "message": "ESTextField", "message_markdown": "ESTextField", "updated_by": "ESEmailField", "updated_time": "ESDateTimeField"}, "EmailTemplateDetails": {"template_id": "ESCharField", "template_name": "ESCharField", "email_event_code": "ESCharField", "client_ids": "ESArrayField"}, "ForecastPlan": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "plan_name": "ESCharField", "plan_type": "ESCharField", "plan_start_date": "ESDateTimeField", "plan_end_date": "ESDateTimeField", "additional_config": "ESJSONField", "is_draft": "ESBooleanField", "created_by": "ESEmailField", "created_on": "ESDateTimeField", "approved_by": "ESEmailField", "approved_on": "ESDateTimeField", "plan_display_order": "ESIntegerField", "is_settlement_end_date": "ESBooleanField", "settlement_end_date": "ESDateTimeField", "payout_frequency": "ESCharField"}, "ForecastPlanCriteria": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "criteria_id": "ESUUIDField", "criteria_name": "ESCharField", "criteria_type": "ESCharField", "criteria_description": "ESCharField", "criteria_display_order": "ESIntegerField", "criteria_data": "ESJSONField", "criteria_column": "ESJSONField", "criteria_config": "ESJSONField", "criteria_level": "ESCharField", "simulate_column": "ESJSONField"}, "ForecastPlanPayee": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "plan_id": "ESUUIDField", "plan_type": "ESCharField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "settlement_end_date": "ESDateTimeField", "plan_exclusion_start_date": "ESDateTimeField", "plan_exclusion_end_date": "ESDateTimeField"}, "ForecastPlanDocs": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "file_name": "ESCharField", "doc": "ESFileField", "employee_email_id": "ESEmailField"}, "ForecastPlanDetails": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "plan_id": "ESUUIDField", "plan_name": "ESCharField", "plan_type": "ESCharField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "settlement_end_date": "ESDateTimeField", "plan_exclusion_start_date": "ESDateTimeField", "plan_exclusion_end_date": "ESDateTimeField"}, "TempForecastPlanDocs": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESUUIDField", "file_name": "ESCharField", "doc": "ESFileField", "employee_email_id": "ESEmailField"}, "GenieContext": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "context_id": "ESUUIDField", "name": "ESCharField", "sources": "ESJSONField", "privacy": "ESCharField", "shared_users": "ESJSONField", "index_last_updated_at": "ESDateTimeField", "updated_by": "ESCharField"}, "GenieSourceFile": {"file_id": "ESUUIDField", "path_to_file": "ESCharField", "file": "ESFileField", "context_id": "ESUUIDField", "llama_doc_ids": "ESJSONField", "created_at": "ESDateTimeField", "created_by": "ESEmailField", "deleted_at": "ESDateTimeField", "deleted_by": "ESEmailField"}, "CommissionAdj": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_type": "ESCharField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_id": "ESEmailField", "currency": "ESCharField", "amount": "ESDecimalField", "is_reverse": "ESBooleanField", "plan_id": "ESCharField", "criteria_id": "ESCharField", "line_item_id": "ESCharField", "reason_category_id": "ESUUIDField", "reason_category": "ESCharField", "reason": "ESCharField", "drs_id": "ESCharField", "posted_by": "ESEmailField"}, "Split": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_type": "ESCharField", "deal_owner_id": "ESEmailField", "deal_id": "ESCharField", "synthetic_deal_id": "ESCharField", "split_payee_id": "ESEmailField", "split_percent": "ESIntegerField", "reason_category": "ESCharField", "reason": "ESCharField", "drs_id": "ESCharField", "posted_by": "ESCharField"}, "DrawRecover": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_type": "ESCharField", "payee_id": "ESEmailField", "fiscal_year": "ESCharField", "period": "ESCharField", "recoverable_balance": "ESDecimalField", "amount": "ESDecimalField", "comments": "ESCharField", "drs_id": "ESCharField", "posted_by": "ESCharField"}, "Ignore": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_type": "ESCharField", "line_item_type": "ESCharField", "line_item_id": "ESCharField", "is_ignored": "ESBooleanField", "reason_category": "ESCharField", "reason": "ESCharField", "drs_id": "ESCharField", "posted_by": "ESCharField"}, "DealAmount": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "adjustment_id": "ESUUIDField", "adjustment_type": "ESCharField", "deal_id": "ESCharField", "new_amount": "ESDecimalField", "reason_category": "ESCharField", "reason": "ESCharField", "drs_id": "ESCharField", "posted_by": "ESCharField"}, "MFAStatus": {"id": "ESAutoField", "email_id": "ESEmailField", "session_id": "ESCharField", "status": "ESCharField", "otp": "ESCharField", "generated_at": "ESDateTimeField", "channel": "ESCharField", "source": "ESCharField", "is_valid": "ESBooleanField"}, "MFAAttempts": {"id": "ESAutoField", "email_id": "ESEmailField", "session_id": "ESCharField", "attempted_otp": "ESCharField", "is_success": "ESBooleanField", "attempted_at": "ESDateTimeField", "is_valid": "ESBooleanField"}, "MFARemember": {"email_id": "ESEmailField", "series_identifier": "ESCharField", "token": "ESCharField", "expires_at": "ESDateTimeField", "is_valid": "ESBooleanField"}, "ClientNotification": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "notification_name": "ESCharField", "can_payee_opt_out": "ESBooleanField", "is_admin_notification": "ESBooleanField", "frequency": "ESCharField", "status": "ESJSONField"}, "PayoutFilters": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "filter_id": "ESUUIDField", "filter_name": "ESTextField", "filters": "ESJSONField", "selected_columns": "ESJSONField", "read_only": "ESBooleanField"}, "PayoutModel": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "payout_id": "ESUUIDField", "transaction_date": "ESDateTimeField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_id": "ESEmailField", "payout_knowledge_date": "ESDateTimeField", "payout_amount": "ESDecimalField", "payout_adjustments": "ESJSONField", "is_paid": "ESBooleanField", "comment": "ESTextField"}, "PayoutArrear": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payout_period_start_date": "ESDateTimeField", "payout_period_end_date": "ESDateTimeField", "payee_id": "ESEmailField", "pending_amount": "ESDecimalField", "processed_amount": "ESDecimalField", "ignored_amount": "ESDecimalField", "arrear_adjustments": "ESJSONField", "is_processed": "ESBooleanField", "processed_at": "ESDateTimeField", "comment": "ESTextField"}, "PeriodInfo": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "period_label": "ESCharField"}, "Permissions": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "permission_id": "ESCharField", "permission_name": "ESCharField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField", "is_active": "ESBooleanField", "show_to_user": "ESBooleanField", "show_data_permissions": "ESBooleanField", "component_system_name": "ESCharField", "component_display_name": "ESCharField", "parent_id": "ESCharField", "component_order": "ESIntegerField", "permission_description": "ESCharField"}, "RolePermissions": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "role_permission_id": "ESUUIDField", "display_name": "ESCharField", "description": "ESCharField", "show_to_user": "ESBooleanField", "permissions": "ESJSONField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField", "is_editable": "ESBooleanField"}, "CommissionPlanSharedDetails": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESCharField", "permission_type": "ESCharField", "target_type": "ESCharField", "target_id": "ESCharField"}, "ForecastPlanSharedDetails": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "plan_id": "ESCharField", "permission_type": "ESCharField", "target_type": "ESCharField", "target_id": "ESCharField"}, "Auth0Session": {"temporal_id": "ESBigAutoField", "created_date": "ESDateTimeField", "creation_additional_details": "ESJSONField", "is_blacklisted": "ESBooleanField", "blacklisted_date": "ESDateTimeField", "blacklisting_additional_details": "ESJSONField", "session_id": "ESCharField"}, "Auth0AccessToken": {"temporal_id": "ESBigAutoField", "created_date": "ESDateTimeField", "creation_additional_details": "ESJSONField", "is_blacklisted": "ESBooleanField", "blacklisted_date": "ESDateTimeField", "blacklisting_additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "session_id": "ESCharField", "token_id": "ESUUIDField"}, "SessionClient": {"session_id": "ESCharField", "email_id": "ESEmailField", "client": "ESForeignKey", "membership_id": "ESUUIDField"}, "TokenSession": {"token_id": "ESUUIDField", "session_id": "ESCharField"}, "SupportNotification": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_ids": "ESArrayField", "email_event_code": "ESCharField"}, "UserGroup": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "user_group_id": "ESUUIDField", "user_group_name": "ESCharField", "user_group_config": "ESJSONField", "created_by": "ESCharField", "created_at": "ESDateTimeField"}, "UserGroupMember": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "user_group_id": "ESUUIDField", "employee_email_id": "ESCharField"}, "EmployeeDetailsView": {"client_id": "ESIntegerField", "employee_email_id": "ESEmailField", "first_name": "ESCharField", "last_name": "ESCharField", "full_name": "ESCharField", "user_role": "ESCharField", "created_date": "ESDateTimeField", "created_by": "ESEmailField", "profile_picture": "ESURLField", "exit_date": "ESDateTimeField", "status": "ESCharField", "joining_date": "ESDateTimeField", "employee_id": "ESCharField", "designation": "ESCharField", "fixed_pay": "ESDecimalField", "variable_pay": "ESDecimalField", "pay_currency": "ESCharField", "employment_country": "ESCharField", "payout_frequency": "ESCharField", "payroll_effective_start_date": "ESDateTimeField", "payroll_effective_end_date": "ESDateTimeField", "reporting_manager_email_id": "ESEmailField", "hierarchy_effective_start_date": "ESDateTimeField", "hierarchy_effective_end_date": "ESDateTimeField", "plan_id": "ESUUIDField", "plan_name": "ESCharField", "plan_type": "ESCharField", "plan_effective_start_date": "ESDateTimeField", "plan_effective_end_date": "ESDateTimeField", "cfd_employee_email": "ESCharField", "custom_field_data": "ESJSONField", "employee_tid": "ESIntegerField", "payroll_tid": "ESIntegerField", "plan_tid": "ESIntegerField", "hier_tid": "ESIntegerField", "cfd_tid": "ESIntegerField"}, "ClientConfig": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField", "is_ed_enabled": "ESBooleanField", "type": "ESCharField", "value": "ESJSONField"}, "LocalizationModel": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "domain_specific_attributes": "ESJSONField", "theme_specific_attributes": "ESJSONField"}, "Quota": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "quota_category_name": "ESCharField", "display_name": "ESCharField", "quota_currency_type": "ESCharField"}, "EmployeeQuota": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "quota_category_name": "ESCharField", "quota_type": "ESCharField", "quota_schedule_type": "ESCharField", "quota_year": "ESCharField", "is_team_quota": "ESBooleanField", "team_type": "ESCharField", "schedule_quota": "ESJSONField", "payout_quota": "ESJSONField", "effective_start_date": "ESDateTimeField", "effective_end_date": "ESDateTimeField"}, "IntegrationConfig": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "employee_email_id": "ESEmailField", "integration_type": "ESCharField", "config": "ESJSONField"}, "CommissionAdjustmentStatus": {"client_id": "ESIntegerField", "adjustment_id": "ESUUIDField", "period_start_date": "ESDateTimeField", "period_end_date": "ESDateTimeField", "payee_email_id": "ESEmailField", "currency": "ESCharField", "amount": "ESDecimalField", "plan_id": "ESCharField", "criteria_id": "ESCharField", "line_item_id": "ESCharField", "reason_category_id": "ESUUIDField", "reason_category": "ESCharField", "reason": "ESCharField", "approval_status": "ESCharField", "is_approval_skipped": "ESBooleanField", "skip_approval_reason": "ESCharField", "is_auto_approved": "ESBooleanField", "additional_details": "ESJSONField"}, "MetricDefinition": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "metric_id": "ESUUIDField", "metric_name": "ESCharField", "metric_description": "ESCharField", "metric_category": "ESCharField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "payee_field": "ESCharField", "date_field": "ESCharField", "formula": "ESJSONField", "valid_dimensions": "ESJSONField"}, "ClientAuditTrail": {"trail_id": "ESBigAutoField", "client": "ESForeignKey", "changed_by": "ESCharField", "changed_at": "ESDateTimeField", "changes_diff": "ESJSONField", "change_type": "ESCharField"}, "SolarSchedule": {"event": "CharField", "latitude": "DecimalField", "longitude": "DecimalField"}, "IntervalSchedule": {"every": "IntegerField", "period": "CharField"}, "ClockedSchedule": {"clocked_time": "DateTimeField"}, "CrontabSchedule": {"minute": "CharField", "hour": "CharField", "day_of_week": "CharField", "day_of_month": "CharField", "month_of_year": "CharField", "timezone": "TimeZoneField"}, "PeriodicTasks": {"ident": "SmallIntegerField", "last_update": "DateTimeField"}, "PeriodicTask": {"name": "CharField", "task": "CharField", "interval": "ForeignKey", "crontab": "ForeignKey", "solar": "ForeignKey", "clocked": "ForeignKey", "args": "TextField", "kwargs": "TextField", "queue": "CharField", "exchange": "CharField", "routing_key": "CharField", "headers": "TextField", "priority": "PositiveIntegerField", "expires": "DateTimeField", "expire_seconds": "PositiveIntegerField", "one_off": "BooleanField", "start_time": "DateTimeField", "enabled": "BooleanField", "last_run_at": "DateTimeField", "total_run_count": "PositiveIntegerField", "date_changed": "DateTimeField", "description": "TextField"}, "SlackBot": {"client_id": "ESCharField", "app_id": "ESCharField", "enterprise_id": "ESCharField", "enterprise_name": "ESTextField", "team_id": "ESCharField", "team_name": "ESTextField", "bot_token": "ESTextField", "bot_refresh_token": "ESTextField", "bot_token_expires_at": "ESDateTimeField", "bot_id": "ESCharField", "bot_user_id": "ESCharField", "bot_scopes": "ESTextField", "is_enterprise_install": "ESBooleanField", "installed_at": "ESDateTimeField"}, "SlackInstallation": {"client_id": "ESCharField", "app_id": "ESCharField", "enterprise_id": "ESCharField", "enterprise_name": "ESTextField", "enterprise_url": "ESTextField", "team_id": "ESCharField", "team_name": "ESTextField", "bot_token": "ESTextField", "bot_refresh_token": "ESTextField", "bot_token_expires_at": "ESDateTimeField", "bot_id": "ESCharField", "bot_user_id": "ESTextField", "bot_scopes": "ESTextField", "user_id": "ESCharField", "user_token": "ESTextField", "user_refresh_token": "ESTextField", "user_token_expires_at": "ESDateTimeField", "user_scopes": "ESTextField", "incoming_webhook_url": "ESTextField", "incoming_webhook_channel": "ESTextField", "incoming_webhook_channel_id": "ESTextField", "incoming_webhook_configuration_url": "ESTextField", "is_enterprise_install": "ESBooleanField", "token_type": "ESCharField", "installed_at": "ESDateTimeField"}, "SlackClientDetails": {"client_id": "ESIntegerField", "bot_token": "ESTextField", "bot_refresh_token": "ESTextField", "bot_token_expires_at": "ESDateTimeField", "team_id": "ESCharField", "user_id": "ESCharField", "updated_at": "ESDateTimeField"}, "SlackOAuthState": {"state": "ESCharField", "expire_at": "ESDateTimeField"}, "MsteamsTenantDetails": {"client_id": "ESIntegerField", "app_version": "ESCharField", "app_catalog_id": "ESCharField", "app_external_id": "ESCharField", "app_package_s3_url": "ESTextField", "tenant_id": "ESCharField", "msteams_installed_by": "ESTextField", "msteams_updated_by": "ESTextField", "installed_by": "ESTextField", "updated_by": "ESTextField", "installed_at": "ESDateTimeField", "updated_at": "ESDateTimeField"}, "MsteamsUserServiceUrl": {"user_id": "ESCharField", "email_id": "ESCharField", "service_url": "ESTextField", "tenant_id": "ESCharField", "updated_at": "ESDateTimeField"}, "MsteamsAdminTokenDetails": {"client_id": "ESCharField", "tenant_id": "ESCharField", "admin_refresh_token": "ESCharField", "admin_access_token": "ESCharField", "updated_at": "ESDateTimeField"}, "CrystalViewModel": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "crystal_view_id": "ESUUIDField", "crystal_view_name": "ESCharField", "crystal_view_description": "ESCharField", "status": "ESCharField", "settings_data": "ESJSONField", "source_view_id": "ESUUIDField", "last_modified_at": "ESDateTimeField", "last_modified_by": "ESCharField"}, "CrystalPayee": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "payee": "ESEmailField", "crystal_view_id": "ESUUIDField"}, "PayeeTableOverrides": {"client_id": "ESIntegerField", "view_id": "ESUUIDField", "table_id": "ESUUIDField", "payee_email": "ESEmailField", "data_overrides": "ESJSONField", "commissions_with_overrides": "ESJSONField", "created_at": "ESDateTimeField"}, "AsyncTask": {"client": "ESForeignKey", "created_by": "ESCharField", "task_name": "ESCharField", "created_at": "ESDateTimeField", "params": "ESJSONField", "params_key": "ESCharField", "status": "ESCharField", "result": "ESJSONField", "read_at": "ESDateTimeField", "completed_at": "ESDateTimeField"}, "DatasheetExecutionContextDB": {"client": "ESForeignKey", "e2e_sync_run_id": "ESCharField", "sync_type": "ESCharField", "datasheet_details": "ESJSONField", "stale_datasheets": "ESJSONField", "total_datasheets_count": "ESIntegerField", "root_stale_datasheets": "ESJSONField", "queue_name": "ESCharField", "is_polling_threshold_breached": "ESBooleanField", "created_at": "ESDateTimeField"}, "ETLJobQueueStatus": {"id": "ESBigAutoField", "emr_id": "ESCharField", "session_id": "ESCharField", "statement_id": "ESCharField", "e2e_sync_run_id": "ESUUIDField", "ds_sync_run_id": "ESCharField", "wrapper_sync_run_id": "ESCharField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "status": "ESCharField", "payload": "ESTextField", "job_submitted_ts": "ESDateTimeField", "job_picked_ts": "ESDateTimeField", "job_completed_ts": "ESDateTimeField"}, "LivySession": {"id": "ESBigAutoField", "emr_id": "ESCharField", "session_id": "ESCharField", "status": "ESCharField", "last_job_submitted_ts": "ESDateTimeField"}, "Agent": {"client_id": "ESIntegerField", "agent_id": "ESUUIDField", "version_id": "ESUUIDField", "version": "ESCharField", "model_name": "ESCharField", "name": "ESCharField", "description": "ESTextField", "core_prompt_prefix": "ESTextField", "core_prompt": "ESTextField", "tool_ids": "ESJSONField", "custom_api_tool_ids": "ESJSONField", "created_date": "ESDateTimeField", "last_updated_date": "ESDateTimeField", "latest_version": "ESBooleanField", "is_published": "ESBooleanField", "is_production": "ESBooleanField", "is_tools_enabled": "ESBooleanField", "tag": "ESCharField"}, "Helper": {"helper_id": "ESUUIDField", "agent_id": "ESUUIDField", "agent_version_id": "ESUUIDField", "model_name": "ESCharField", "name": "ESCharField", "description": "ESTextField", "system_prompt": "ESTextField", "rag_modules": "ESJSONField"}, "CustomAPITool": {"tool_id": "ESUUIDField", "tool_name": "ESCharField", "client_id": "ESIntegerField", "agent_id": "ESUUIDField", "request_url": "ESTextField", "request_method": "ESCharField", "tool_type": "ESCharField", "description": "ESTextField", "api_schema": "ESJSONField"}, "CommonPrompts": {"prompt_key": "ESCharField", "prompt_value": "ESTextField"}, "LLMAgentUserSession": {"client_id": "ESIntegerField", "session_id": "ESUUIDField", "version_id": "ESUUIDField", "agent_id": "ESUUIDField", "session_name": "ESCharField", "description": "ESTextField", "created_date": "ESDateTimeField", "last_interaction_date": "ESDateTimeField", "employee_email_id": "ESEmailField", "is_active": "ESBooleanField", "files": "ESJSONField", "share_chat": "ESBooleanField"}, "LLMAgentUserSessionMessages": {"message_id": "ESUUIDField", "thread_id": "ESUUIDField", "session_id": "ESUUIDField", "sender": "ESCharField", "sent_time": "ESDateTimeField", "message_content": "ESJSONField", "chain_of_thought": "ESJSONField", "parent_message_id": "ESUUIDField", "approval_required": "ESBooleanField", "approval_status": "ESCharField", "internal_tracking_message": "ESBooleanField", "like": "ESIntegerField", "feedback": "ESCharField"}, "DatasheetTransformation": {"client": "ESForeignKey", "datasheet_id": "ESUUIDField", "transformation_id": "ESUUIDField", "spec": "ESJSONField", "is_saved": "ESBooleanField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField", "order": "ESIntegerField", "type": "ESCharField", "version": "AutoIncVersionField"}, "DatasheetPin": {"temporal_id": "ESAutoField", "datasheet_id": "ESUUIDField", "client": "ESForeignKey", "is_deleted": "ESBooleanField", "created_by": "ESCharField", "updated_by": "ESCharField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField"}, "DatasheetTag": {"temporal_id": "ESAutoField", "client": "ESForeignKey", "tag_id": "ESUUIDField", "name": "ESCharField", "type": "ESCharField", "is_deleted": "ESBooleanField", "description": "ESCharField", "additional_details": "ESJSONField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField", "version": "AutoIncVersionField"}, "DatasheetTagMap": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "datasheet_id": "ESUUIDField", "tag_id": "ESUUIDField", "version": "AutoIncVersionField"}, "DatasheetVariableTemp": {"client_id": "ESIntegerField", "databook_id": "ESUUIDField", "datasheet_id": "ESUUIDField", "system_name": "ESCharField", "display_name": "ESCharField", "description": "ESTextField", "data_type_id": "ESIntegerField", "source_cf_meta_data": "ESJSONField", "tags": "ESJSONField", "field_order": "ESIntegerField", "meta_data": "ESJSONField", "source_variable_id": "ESCharField", "source_id": "ESCharField", "variable_id": "ESUUIDField", "is_selected": "ESBooleanField", "source_type": "ESCharField", "is_primary": "ESBooleanField", "warning": "ESTextField"}, "DatasheetView": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "view_id": "ESUUIDField", "name": "ESCharField", "datasheet_id": "ESUUIDField", "filter_id": "ESUUIDField", "pivot_id": "ESUUIDField", "last_updated_by": "ESCharField", "created_at": "ESDateTimeField", "ordered_columns": "ESArrayField", "hidden_columns": "ESArrayField", "version": "AutoIncVersionField"}, "DatasheetViewFilter": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "filter_id": "ESUUIDField", "filter_data": "ESJSONField"}, "DatasheetViewPivot": {"temporal_id": "ESBigAutoField", "client": "ESForeignKey", "is_deleted": "ESBooleanField", "pivot_id": "ESUUIDField", "pivot_data": "ESJSONField", "created_at": "ESDateTimeField", "updated_at": "ESDateTimeField"}, "GmailAccessToken": {"gmail_id": "ESEmailField", "access_token": "ESCharField", "refresh_token": "ESCharField", "last_modified_at": "ESDateTimeField"}, "GmailEverstageUserMap": {"gmail_id": "ESEmailField", "everstage_email_id": "ESEmailField"}, "ApprovalRuleGroup": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "rule_group_id": "ESUUIDField", "group_name": "ESCharField", "stage_count": "ESIntegerField", "form_builder_id": "ESUUIDField", "created_by": "ESEmailField", "group_description": "ESCharField", "notify_on_reject": "ESJSONField", "notify_on_approve": "ESJSONField"}, "ApprovalRules": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "rule_id": "ESUUIDField", "rule_group_id": "ESUUIDField", "form_builder_id": "ESUUIDField", "stage_order": "ESIntegerField", "stage_template_id": "ESUUIDField", "condition": "ESJSONField", "approvers": "ESJSONField", "rule_name": "ESCharField"}, "AutoSaveForm": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "form_id": "ESUUIDField", "form_builder_id": "ESUUIDField", "form_data": "ESJSONField"}, "EverstageForm": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "form_id": "ESUUIDField", "form_builder_id": "ESUUIDField", "form_spec": "ESJSONField", "form_data": "ESJSONField", "status": "ESCharField", "created_by": "ESCharField", "updated_by": "ESCharField", "created_at": "ESDateTimeField"}, "FormSpecChange": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "form_id": "ESUUIDField", "form_builder_id": "ESUUIDField", "form_spec": "ESJSONField"}, "QuoteLineItem": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "quote_id": "ESCharField", "quote_line_item_id": "ESCharField", "start_date": "ESDateTimeField", "end_date": "ESDateTimeField", "phase_id": "ESCharField", "phase_name": "ESCharField", "product_name": "ESCharField", "sku": "ESCharField", "billing_frequency": "ESCharField", "billing_type": "ESCharField", "prorate_multiplier": "ESDecimalField", "quantity": "ESIntegerField", "discount_percent": "ESDecimalField", "list_unit_price": "ESDecimalField", "net_unit_price": "ESDecimalField", "list_total": "ESDecimalField", "net_total": "ESDecimalField", "prorated_list_total": "ESDecimalField", "prorated_net_total": "ESDecimalField", "custom_data": "ESJSONField", "pricepoint_data": "ESJSONField", "order": "ESIntegerField", "unit_of_measure": "ESCharField"}, "Quote": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "quote_id": "ESUUIDField", "quote_display_id": "ESIntegerField", "quote_name": "ESCharField", "quote_currency": "ESCharField", "form_id": "ESUUIDField", "valid_till": "ESDateTimeField", "is_primary": "ESBooleanField", "start_date": "ESDateTimeField", "end_date": "ESDateTimeField", "duration_value": "ESIntegerField", "duration_type": "ESCharField", "opportunity_id": "ESCharField", "account_id": "ESCharField", "contact_id": "ESCharField", "bill_to": "ESCharField", "ship_to": "ESCharField", "list_quote_total": "ESFloatField", "net_quote_total": "ESFloatField", "discount_amount": "ESFloatField", "closed_date": "ESDateTimeField", "custom_data": "ESJSONField", "owner_id": "ESCharField", "created_at": "ESDateTimeField", "created_by": "ESCharField"}, "QuoteStatus": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "quote_status_id": "ESUUIDField", "quote_id": "ESUUIDField", "status": "ESCharField", "tags": "ESJSONField", "created_at": "ESDateTimeField", "updated_by": "ESCharField", "comment": "ESTextField"}, "BillingFrequency": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "billing_frequency_id": "ESUUIDField", "name": "ESCharField", "description": "ESTextField", "multiplier_value": "ESFloatField", "multiplier_type": "ESCharField", "created_at": "ESDateTimeField", "created_by": "ESCharField"}, "PriceFactor": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "price_factor_id": "ESUUIDField", "name": "ESCharField", "description": "ESTextField", "values": "ESJSONField", "created_at": "ESDateTimeField", "created_by": "ESCharField"}, "PriceBook": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "price_book_id": "ESUUIDField", "name": "ESCharField", "description": "ESTextField", "status": "ESCharField", "price_factors": "ESJSONField", "created_by": "ESCharField", "created_at": "ESDateTimeField"}, "PriceBookProduct": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "price_book_product_id": "ESUUIDField", "price_book_id": "ESUUIDField", "sku": "ESCharField", "created_at": "ESDateTimeField", "created_by": "ESCharField"}, "CustomTrigger": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "custom_trigger_id": "ESUUIDField", "name": "ESCharField", "description": "ESCharField", "created_by": "ESCharField", "category": "ESCharField", "trigger_type": "ESCharField", "trigger_params": "ESJSONField", "trigger_icon": "ESCharField", "created_at": "ESDateTimeField", "is_draft": "ESBooleanField"}, "TriggerCategory": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "category_id": "ESUUIDField", "name": "ESCharField", "description": "ESCharField", "created_by": "ESCharField", "created_at": "ESDateTimeField", "is_draft": "ESBooleanField"}, "NotificationTracker": {"client": "ESForeignKey", "notification_id": "ESBigAutoField", "created_at": "ESDateTimeField", "employee_email_id": "ESCharField", "channel": "ESCharField", "content": "ESJSONField", "one_time_key": "ESCharField", "one_time_key_hash": "ESCharField", "additional_details": "ESJSONField"}, "WorkflowStatus": {"wf_id": "ESUUIDField", "client_id": "ESIntegerField", "status": "ESCharField", "start_time": "ESDateTimeField", "end_time": "ESDateTimeField", "instance_id": "ESUUIDField", "trigger_id": "ESUUIDField", "trigger": "ESCharField", "status_log": "ESJSONField", "audit": "ESCharField"}, "WorkflowComponentStatus": {"wf_id": "ESUUIDField", "client_id": "ESIntegerField", "status": "ESCharField", "start_time": "ESDateTimeField", "end_time": "ESDateTimeField", "instance_id": "ESUUIDField", "component_id": "ESCharField", "component_type": "ESCharField", "component_log": "ESJSONField"}, "Workflow": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "wf_id": "ESUUIDField", "name": "ESCharField", "description": "ESCharField", "created_by": "ESCharField", "trigger": "ESCharField", "components": "ESJSONField", "logical_structure": "ESJSONField", "created_at": "ESDateTimeField", "is_draft": "ESBooleanField", "is_active": "ESBooleanField"}, "UpstreamTimestamps": {"id": "ESAutoField", "client": "ESForeignKey", "integration_id": "ESUUIDField", "api_changes_synced_till": "ESDateTimeField", "api_deletes_synced_till": "ESDateTimeField", "upstream_source_synced_till": "ESDateTimeField", "last_extracted_by": "ESCharField"}, "UpstreamExtractionStatus": {"id": "ESAutoField", "client": "ESForeignKey", "integration_id": "ESUUIDField", "enabled": "ESBooleanField", "technique": "ESCharField", "meta_data": "ESJSONField", "updated_at": "ESDateTimeField", "is_running": "ESBooleanField", "last_run_at": "ESDateTimeField", "backoff_info": "ESJSONField", "event_logs": "ESJSONField"}, "FivetranSyncLog": {"id": "ESBigAutoField", "e2e_sync_run_id": "ESUUIDField", "sync_run_id": "ESUUIDField", "client_id": "ESIntegerField", "sync_start_time": "ESDateTimeField", "sync_completion_time": "ESDateTimeField", "sync_status": "ESCharField", "connector_id": "ESCharField", "integration_ids": "ESJSONField", "sync_params": "ESJSONField", "skipped_connectors": "ESJSONField", "webhook_log": "ESJSONField", "sync_run_log": "ESJSONField", "additional_details": "ESJSONField"}, "ConnectorDefinition": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "connector_name": "ESCharField", "connector_id": "ESCharField", "has_object_url": "ESBooleanField", "has_fields_url": "ESBooleanField", "object_url": "ESCharField", "fields_url": "ESCharField", "connector_frontend_data": "ESJSONField"}, "ConnectorObject": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "connector_id": "ESCharField", "object_id": "ESCharField"}, "ConnectorObjectvariable": {"temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "connector_id": "ESCharField", "object_id": "ESCharField", "system_name": "ESCharField", "display_name": "ESCharField", "data_type_id": "ESIntegerField"}, "EverTableTag": {"client": "ESForeignKey", "temporal_id": "ESBigAutoField", "knowledge_begin_date": "ESDateTimeField", "knowledge_end_date": "ESDateTimeField", "is_deleted": "ESBooleanField", "additional_details": "ESJSONField", "system_name": "ESCharField", "display_name": "ESCharField", "component": "ESCharField", "type": "ESCharField", "meta_data": "ESJSONField", "description": "ESTextField"}}