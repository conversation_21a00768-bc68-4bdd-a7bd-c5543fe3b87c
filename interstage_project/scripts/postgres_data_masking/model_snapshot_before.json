{"LogEntry": {"action_time": "DateTimeField", "user": "ForeignKey", "content_type": "ForeignKey", "object_id": "TextField", "object_repr": "CharField", "action_flag": "PositiveSmallIntegerField", "change_message": "TextField"}, "Permission": {"name": "CharField", "content_type": "ForeignKey", "codename": "CharField"}, "Group": {"name": "CharField", "permissions": "ManyToManyField"}, "User": {"password": "CharField", "last_login": "DateTimeField", "is_superuser": "BooleanField", "username": "CharField", "first_name": "CharField", "last_name": "CharField", "email": "EmailField", "is_staff": "BooleanField", "is_active": "BooleanField", "date_joined": "DateTimeField", "groups": "ManyToManyField", "user_permissions": "ManyToManyField"}, "ContentType": {"app_label": "CharField", "model": "CharField"}, "Session": {"session_key": "CharField", "session_data": "TextField", "expire_date": "DateTimeField"}, "Client": {"client_id": "AutoField", "name": "CharField", "domain": "CharField", "logo_url": "CharField", "auth_connection_name": "CharField", "connection_type": "CharField", "base_currency": "CharField", "fiscal_start_month": "IntegerField", "secondary_calculator": "CharField", "client_notification": "BooleanField", "payee_notification": "BooleanField", "meta_info": "SensitiveJSONField", "logo_s3_path": "FileField", "statement_logo_url": "CharField", "statement_logo_s3_path": "FileField", "time_zone": "CharField", "client_features": "JSONField", "is_deleted": "BooleanField"}, "ClientSettings": {"client": "ForeignKey", "icm_settings": "JSONField", "cpq_settings": "JSONField"}, "EverObjectVariable": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "data_type_id": "IntegerField", "system_name": "CharField", "display_name": "CharField", "ever_object_id": "CharField"}, "EverObject": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "ever_object_id": "CharField", "name": "CharField", "data_origin": "CharField", "source_table": "CharField", "primary_key": "JSONField", "table_name": "CharField"}, "CommissionReportEnrichment": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "system_name": "CharField", "report_system_name": "CharField", "display_name": "CharField", "report_type": "CharField", "data_type": "ForeignKey", "meta_data": "JSONField"}, "Databook": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "databook_id": "UUIDField", "name": "CharField", "is_draft": "BooleanField", "is_archived": "BooleanField", "created_at": "DateTimeField", "created_by": "EmailField", "datasheet_order": "JSONField"}, "Datasheet": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "name": "CharField", "description": "TextField", "source_type": "CharField", "source_id": "CharField", "source_databook_id": "UUIDField", "order": "IntegerField", "primary_key": "JSONField", "additional_details": "JSONField", "transformation_spec": "SensitiveJSONField", "ordered_columns": "ArrayField", "hidden_columns": "ArrayField", "tags": "JSONField", "is_pk_modified": "BooleanField", "is_datasheet_generated": "BooleanField", "is_config_changed": "BooleanField", "is_calc_field_changed": "BooleanField", "data_origin": "CharField", "data_last_updated_at": "DateTimeField"}, "DSFilterOperators": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "id": "UUIDField", "name": "CharField", "alt_name": "CharField", "category": "CharField", "operand_type_ids": "JSONField", "output_type_ids": "JSONField", "needs_operand": "BooleanField", "multi_valued": "BooleanField"}, "DatasheetAdjustments": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_number": "IntegerField", "sub_adjustment_number": "CharField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "original_row_key": "CharField", "row_key": "CharField", "operation": "CharField", "data": "SensitiveJSONField", "comments": "SensitiveCharField", "is_global": "BooleanField"}, "DatasheetPermissions": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "filter_list": "SensitiveJSONField", "permission_set_id": "UUIDField", "permission_set_name": "CharField", "columns_to_be_hidden": "ArrayField"}, "DSPermissionsTarget": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "target_type": "CharField", "target": "CharField", "permission_set_id": "UUIDField"}, "DSSnapshotInfoLocal": {"client": "ForeignKey", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "generated_time": "DateTimeField", "path": "CharField", "checksum": "CharField"}, "DbkdPkdMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "primary_kd": "DateTimeField", "databook_id": "UUIDField", "datasheet_id": "UUIDField"}, "DatasheetVariable": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "system_name": "CharField", "display_name": "CharField", "description": "TextField", "data_type": "ForeignKey", "source_cf_meta_data": "JSONField", "tags": "JSONField", "field_order": "IntegerField", "meta_data": "SensitiveJSONField", "source_variable_id": "CharField", "source_id": "CharField", "variable_id": "UUIDField", "is_selected": "BooleanField", "source_type": "CharField", "is_primary": "BooleanField", "warning": "TextField"}, "DatasheetFilter": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "filter_id": "UUIDField", "filter_name": "CharField", "filter_type": "CharField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "last_updated_by": "CharField", "filter_list": "SensitiveJSONField", "pivot_object": "JSONField", "config_type": "CharField"}, "SkdPkdMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "secondary_snapshot_id": "UUIDField", "primary_kd": "DateTimeField"}, "CustomObjectPkdMap": {"temporal_id": "BigAutoField", "client": "ForeignKey", "custom_object_id": "IntegerField", "primary_kd": "DateTimeField", "e2e_sync_run_id": "UUIDField", "created_at": "DateTimeField", "updated_at": "DateTimeField"}, "ReportObjectPkdMap": {"temporal_id": "BigAutoField", "client": "ForeignKey", "report_object_id": "CharField", "primary_kd": "DateTimeField", "report_type": "CharField", "e2e_sync_run_id": "UUIDField", "created_at": "DateTimeField", "updated_at": "DateTimeField"}, "CustomObject": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "custom_object_id": "IntegerField", "name": "CharField", "primary_key": "JSONField", "tags": "JSONField", "snapshot_key": "JSONField", "created_at": "DateTimeField", "created_by": "CharField", "ordered_columns": "ArrayField", "csv_header_map": "JSONField"}, "CustomObjectVariable": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "custom_object_id": "IntegerField", "system_name": "CharField", "display_name": "CharField", "data_type": "ForeignKey", "tags": "JSONField"}, "CustomObjectData": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "custom_object_id": "IntegerField", "row_key": "CharField", "data": "JSONField", "snapshot_value": "CharField"}, "COPermissions": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "object_id": "CharField", "object_type": "CharField", "is_include": "BooleanField", "target_type": "CharField", "target": "CharField"}, "COSnapshotInfoLocal": {"client": "ForeignKey", "object_id": "IntegerField", "generated_time": "DateTimeField", "path": "CharField"}, "Commission": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "amount": "SensitiveDecimalField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField", "show_do_nothing": "BooleanField", "commission_date": "DateTimeField"}, "QuotaErosion": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "quota_category_name": "CharField", "qv": "SensitiveDecimalField", "cumulative_qe": "SensitiveDecimalField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "quota_erosion": "SensitiveDecimalField", "is_team": "BooleanField", "team_id": "CharField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField"}, "CommissionLock": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "locked_knowledge_date": "DateTimeField", "is_locked": "BooleanField", "lock_id": "UUIDField", "commission_plan_ids": "JSONField", "commission_snapshot_id": "UUIDField"}, "CommLockDetail": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "line_item_type": "CharField", "line_item_ids": "SensitiveJSONField", "amount": "SensitiveDecimalField", "lock_id": "UUIDField"}, "InterCommission": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "amount": "SensitiveDecimalField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField", "show_do_nothing": "BooleanField", "commission_date": "DateTimeField"}, "InterQuotaErosion": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "quota_category_name": "CharField", "qv": "SensitiveDecimalField", "cumulative_qe": "SensitiveDecimalField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "quota_erosion": "SensitiveDecimalField", "is_team": "BooleanField", "team_id": "CharField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField"}, "CommissionSecondaryKd": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "sec_kd": "DateTimeField"}, "ApiAccessConfig": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "source_object_id": "CharField", "request_url": "CharField", "request_type": "CharField", "request_body": "JSONField", "request_header": "JSONField", "access_token_config_id": "IntegerField", "integration": "CharField", "response_key": "CharField", "additional_data": "JSONField", "integration_id": "UUIDField"}, "AccessTokenConfig": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "access_token_config_id": "AutoField", "access_type": "CharField", "payload_type": "CharField", "api_access_key": "SensitiveCharField", "access_token_url": "CharField", "access_request_body": "SensitiveJSONField", "jwt_data": "JSONField", "service_name": "CharField", "domain": "CharField", "connection_name": "CharField", "connection_status": "CharField", "connection_type": "CharField", "created_on": "DateTimeField", "additional_data": "SensitiveJSONField"}, "ExtractionConfig": {"client_id": "IntegerField", "priority": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_disabled": "BooleanField", "task_group": "CharField", "task": "CharField", "source_object_id": "SensitiveCharField", "destination_object_id": "CharField", "sync_type": "CharField", "destination_object_type": "CharField", "integration_id": "UUIDField", "access_token_config_id": "IntegerField", "additional_data": "JSONField"}, "TransformationConfig": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "source_object_id": "CharField", "destination_object_id": "CharField", "source_field": "CharField", "destination_field": "CharField", "field_type": "CharField", "transformation_logic_id": "IntegerField", "integration_id": "UUIDField", "additional_config": "SensitiveJSONField"}, "TransformationLogic": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "source_object_id": "CharField", "logic": "SensitiveTextField"}, "EnrichmentConfig": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "source_object_id": "CharField", "ref_object_key": "CharField", "enrichment_resp_key": "CharField", "enrichment_id_key": "CharField", "source_new_key": "CharField", "enrichment_type": "CharField", "ref_api_config_obj": "CharField", "integration_id": "UUIDField", "additional_data": "JSONField"}, "Integration": {"client_id": "IntegerField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "integration_id": "UUIDField", "name": "CharField", "service_name": "CharField", "desc": "CharField", "logo_url": "CharField", "source_object_id": "CharField", "properties": "JSONField", "additional_data": "JSONField", "is_api": "BooleanField", "destination_object_id": "IntegerField", "hyperlink_info": "SensitiveJSONField", "batch_etl_page_size": "IntegerField", "preprocessing_metadata": "JSONField", "transformation_logic": "TextField"}, "EtlGlobalSyncStatus": {"temporal_id": "BigAutoField", "global_sync_disabled_at": "DateTimeField", "global_sync_enabled_at": "DateTimeField", "enabled_by": "EmailField", "disabled_by": "EmailField", "comments": "TextField"}, "UpstreamETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_id": "CharField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "sync_mode": "CharField", "changes_start_time": "DateTimeField", "changes_end_time": "DateTimeField", "delete_start_time": "DateTimeField", "delete_end_time": "DateTimeField", "extraction_start_time": "DateTimeField", "extraction_status": "CharField", "extraction_end_time": "DateTimeField", "transformation_start_time": "DateTimeField", "transformation_status": "CharField", "transformation_end_time": "DateTimeField", "load_start_time": "DateTimeField", "load_status": "CharField", "load_end_time": "DateTimeField", "filter_query": "JSONField", "source": "CharField", "audit": "CharField", "sync_run_log": "JSONField", "integration_id": "UUIDField", "extracted_records_count": "IntegerField", "deleted_records_count": "IntegerField"}, "UpstreamCsvETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_id": "CharField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "sync_mode": "CharField", "changes_start_time": "DateTimeField", "changes_end_time": "DateTimeField", "extraction_start_time": "DateTimeField", "extraction_status": "CharField", "extraction_end_time": "DateTimeField", "filter_query": "JSONField", "source": "CharField", "audit": "CharField", "sync_run_log": "JSONField"}, "ETLLock": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "lock_id": "BigAutoField", "client_id": "IntegerField", "object_id": "CharField", "sync_mode": "CharField", "lock_acquired_time": "DateTimeField", "lock_released_time": "DateTimeField", "is_active": "BooleanField", "lock_name": "CharField"}, "ExtractionSyncLog": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_name": "CharField", "object_id": "CharField", "extracted_objects": "SensitiveJSONField", "operation": "CharField", "updated_at": "DateTimeField"}, "TransformationSyncLog": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_name": "CharField", "object_id": "CharField", "transformed_objects": "SensitiveJSONField", "updated_at": "DateTimeField"}, "LoadSyncLog": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_name": "CharField", "object_id": "CharField", "loaded_objects": "SensitiveJSONField", "operation": "CharField", "updated_at": "DateTimeField"}, "CommissionETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "CharField", "job_count": "IntegerField", "sync_run_log": "JSONField"}, "ForecastETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "CharField", "job_count": "IntegerField", "sync_run_log": "JSONField"}, "DatabookETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "task": "CharField", "primary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "sync_type": "CharField", "audit": "CharField", "sync_run_log": "JSONField"}, "ReportETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "object_id": "CharField", "task": "CharField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "sync_mode": "CharField", "changes_start_time": "DateTimeField", "changes_end_time": "DateTimeField", "audit": "CharField", "sync_run_log": "JSONField"}, "SettlementETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "primary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "CharField", "datasheet_id": "UUIDField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "sync_run_log": "JSONField", "job_count": "IntegerField"}, "PayoutSnapshotETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "primary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "CharField", "payees": "JSONField", "sync_run_log": "JSONField", "job_count": "IntegerField"}, "SettlementSnapshotETLStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "primary_kd": "DateTimeField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "CharField", "payees": "JSONField", "sync_run_log": "JSONField", "job_count": "IntegerField"}, "ETLSyncStatus": {"e2e_sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "sync_status": "CharField", "sync_start_time": "DateTimeField", "sync_end_time": "DateTimeField", "params": "JSONField", "audit": "JSONField"}, "PlanModificationSyncStatus": {"e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "task": "CharField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "audit": "JSONField", "sync_run_log": "JSONField"}, "ForecastCommission": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "amount": "SensitiveDecimalField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField", "show_do_nothing": "BooleanField", "commission_date": "DateTimeField"}, "ForecastQuotaErosion": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "quota_category_name": "CharField", "qv": "SensitiveDecimalField", "cumulative_qe": "SensitiveDecimalField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "quota_erosion": "SensitiveDecimalField", "is_team": "BooleanField", "team_id": "CharField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField"}, "InterForecastCommission": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "amount": "SensitiveDecimalField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField", "show_do_nothing": "BooleanField", "commission_date": "DateTimeField"}, "InterForecastQuotaErosion": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "quota_category_name": "CharField", "qv": "SensitiveDecimalField", "cumulative_qe": "SensitiveDecimalField", "line_item_type": "CharField", "line_item_id": "TextField", "tier_id": "CharField", "original_tier_id": "CharField", "quota_erosion": "SensitiveDecimalField", "is_team": "BooleanField", "team_id": "CharField", "primary_kd": "DateTimeField", "secondary_kd": "DateTimeField", "secondary_snapshot_id": "UUIDField", "commission_snapshot_id": "UUIDField", "context_ids": "JSONField"}, "ForecastSecondaryKd": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "sec_kd": "DateTimeField"}, "HrisConfig": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "datasheet_id": "UUIDField", "databook_id": "UUIDField", "user_field": "CharField", "source_field": "CharField", "field_context": "CharField"}, "Hyperlink": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "hyperlink_field": "CharField", "url": "SensitiveURLField", "custom_object_id": "IntegerField"}, "PayoutStatus": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "payment_status": "SensitiveCharField", "total_payout": "SensitiveDecimalField", "paid_amount": "SensitiveDecimalField", "processed_amount": "SensitiveDecimalField", "ignored_amount": "SensitiveDecimalField", "pending_amount": "SensitiveDecimalField", "commission_percentage": "SensitiveDecimalField", "commission": "SensitiveDecimalField", "comm_calc_status": "CharField", "settlement_calc_status": "CharField", "comm_calc_details": "JSONField", "settlement_calc_details": "JSONField", "payout_split_up": "SensitiveJSONField", "fx_rate": "DecimalField", "variable_pay_as_per_period": "SensitiveDecimalField", "payout_frequency": "CharField"}, "PayoutStatusChanges": {"client": "ForeignKey", "temporal_id": "BigAutoField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "EmailField", "created_at": "DateTimeField", "change_type": "CharField"}, "NotificationTask": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "CharField", "task": "CharField", "frequency": "CharField", "cron_id": "IntegerField", "is_event_based": "BooleanField"}, "MilestoneNotificationStatus": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "CharField", "task": "CharField", "milestone": "SensitiveFloatField", "time_period_label": "CharField"}, "ETLTask": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "task": "CharField", "periodic_task_id": "IntegerField", "arguments": "JSONField"}, "SettlementRule": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "settlement_rule_id": "UUIDField", "name": "CharField", "description": "CharField", "settlement_flag_expr": "SensitiveJSONField", "amount_expr": "SensitiveJSONField", "settlement_type": "CharField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "date_field": "CharField", "settlement_join_keys": "JSONField", "commission_join_keys": "JSONField", "criteria_column": "JSONField", "is_valid": "BooleanField"}, "CommissionSettlementMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "commission_plan_id": "UUIDField", "criteria_id": "UUIDField", "settlement_rule_id": "UUIDField"}, "Settlement": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "settlement_date": "DateTimeField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "commission_date": "DateTimeField", "comm_period_start_date": "DateTimeField", "comm_period_end_date": "DateTimeField", "commission_row_key": "TextField", "settlement_rule_id": "UUIDField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "payee_email_id": "CharField", "line_item_id": "TextField", "amount": "SensitiveDecimalField", "settlement_flag": "BooleanField"}, "SettlementLock": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "locked_knowledge_date": "DateTimeField", "is_locked": "BooleanField", "lock_id": "UUIDField", "plan_ids": "JSONField"}, "SettlementLockDetail": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "payee_email_id": "CharField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "settlement_rule_id": "UUIDField", "amount": "SensitiveDecimalField", "lock_id": "UUIDField", "line_item_id": "JSONField"}, "SettlementKdMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "payee_email_id": "CharField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "settlement_rule_id": "UUIDField", "primary_kd": "DateTimeField"}, "TagCategory": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "id": "UUIDField", "system_name": "CharField", "display_name": "CharField", "constraints": "JSONField", "type": "JSONField", "options": "JSONField", "description": "CharField"}, "TagValue": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "tag_category_id": "UUIDField", "system_name": "CharField", "display_name": "CharField"}, "ReportObjectData": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "object_id": "CharField", "object_type": "CharField", "row_key": "CharField", "snapshot_key": "CharField", "data": "JSONField"}, "FrozenPayrollData": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "fiscal_year": "CharField", "period_end_date": "DateTimeField", "payee_email_id": "CharField", "variable_pay_in_base_currency": "SensitiveDecimalField", "variable_pay_in_payee_currency": "SensitiveDecimalField"}, "ReportSnapshotInfoLocal": {"client": "ForeignKey", "object_id": "CharField", "object_type": "CharField", "generated_time": "DateTimeField", "path": "SensitiveCharField"}, "DatasheetDatasetMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "superset_dataset_id": "IntegerField", "datasheet_id": "UUIDField", "databook_id": "UUIDField", "table_name": "TextField", "created_by": "EmailField"}, "Employee": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "first_name": "CharField", "last_name": "CharField", "user_role": "JSONField", "user_source": "CharField", "time_zone": "CharField", "email_alias": "CharField", "created_date": "DateTimeField", "created_by": "EmailField", "profile_picture": "SensitiveURLField", "login_mode": "SensitiveCharField", "send_notification": "BooleanField", "employee_config": "SensitiveJSONField", "exit_date": "DateTimeField", "deactivation_date": "DateTimeField", "last_commission_date": "DateTimeField", "status": "CharField"}, "EmployeePayroll": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "employee_id": "CharField", "joining_date": "DateTimeField", "exit_date": "DateTimeField", "designation": "CharField", "level": "CharField", "employee_status": "CharField", "employment_country": "CharField", "fixed_pay": "SensitiveDecimalField", "variable_pay": "SensitiveDecimalField", "pay_currency": "CharField", "payout_frequency": "CharField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "payee_role": "CharField"}, "Hierarchy": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "reporting_manager_email_id": "EmailField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField"}, "PlanDetails": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "plan_id": "UUIDField", "plan_name": "CharField", "plan_type": "CharField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "settlement_end_date": "DateTimeField", "plan_exclusion_start_date": "DateTimeField", "plan_exclusion_end_date": "DateTimeField"}, "TeamConfig": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "team_id": "CharField", "team_name": "CharField", "team_created_by": "EmailField", "team_created_time": "DateTimeField", "team_updated_by": "EmailField", "team_updated_time": "DateTimeField", "team_type": "CharField", "pod_owner_name": "CharField"}, "Membership": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "team_id": "CharField", "group_member_email_id": "EmailField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField"}, "ApprovalTemplate": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "template_id": "UUIDField", "entity_type": "CharField", "template_name": "CharField", "template_description": "CharField", "stage_order": "JSONField", "is_active": "BooleanField", "created_by": "EmailField", "updated_by": "EmailField", "notify_on_reject": "JSONField", "notify_on_approve": "JSONField"}, "ApprovalTemplateStage": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "stage_template_id": "UUIDField", "template_id": "UUIDField", "stage_name": "CharField", "approvers": "JSONField", "approval_strategy": "CharField", "due_period": "IntegerField", "approval_trigger": "CharField", "cool_off_period": "IntegerField", "is_auto_approve": "BooleanField", "notes": "SensitiveCharField", "stage_order": "IntegerField"}, "ApprovalInstance": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "approval_wf_instance_id": "UUIDField", "template_id": "UUIDField", "entity_type": "CharField", "entity_key": "CharField", "status": "CharField", "requested_time": "DateTimeField", "completion_time": "DateTimeField", "notify_on_reject": "JSONField", "notify_on_approve": "JSONField", "notify_all_approvers": "BooleanField", "notify_all_approvers_on_approve": "BooleanField", "created_by": "EmailField", "is_system_action": "BooleanField", "instance_data": "SensitiveJSONField", "is_active": "BooleanField"}, "ApprovalInstanceStage": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "stage_instance_id": "UUIDField", "approval_wf_instance_id": "UUIDField", "stage_template_id": "UUIDField", "stage_name": "CharField", "stage_order": "IntegerField", "approvers": "JSONField", "approval_strategy": "CharField", "due_date": "DateTimeField", "approval_trigger": "CharField", "cool_off_date": "DateTimeField", "is_auto_approve": "BooleanField", "is_system_action": "BooleanField", "status": "CharField", "initiated_time": "DateTimeField", "requested_time": "DateTimeField", "completed_time": "DateTimeField", "stage_template_data": "JSONField", "notes": "CharField"}, "ApprovalRequests": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "approval_request_id": "UUIDField", "approval_wf_instance_id": "UUIDField", "stage_instance_id": "UUIDField", "entity_type": "CharField", "entity_key": "CharField", "approver": "EmailField", "status": "CharField", "is_system_action": "BooleanField", "requested_time": "DateTimeField", "completed_time": "DateTimeField", "comments": "SensitiveCharField", "approve_on_request": "BooleanField", "request_data": "JSONField"}, "SubApprovalRequests": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "sub_request_id": "UUIDField", "request_id": "UUIDField", "stage_instance_id": "UUIDField", "approval_wf_instance_id": "UUIDField", "sub_entity_id": "TextField", "sub_request_data": "SensitiveJSONField", "status": "CharField", "is_system_action": "BooleanField", "approver": "EmailField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "completed_time": "DateTimeField", "comments": "SensitiveTextField", "requested_time": "DateTimeField"}, "AuditTrail": {"client": "ForeignKey", "audit_trail_id": "BigAutoField", "event_type_code": "CharField", "event_key": "CharField", "updated_at": "DateTimeField", "summary": "CharField", "details": "SensitiveJSONField", "details_diff": "SensitiveJSONField", "updated_by_masked": "EmailField", "updated_by_unmasked": "EmailField", "associated_support_membership": "UUIDField"}, "CommissionPlan": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "plan_name": "CharField", "plan_type": "CharField", "plan_start_date": "DateTimeField", "plan_end_date": "DateTimeField", "additional_config": "SensitiveJSONField", "is_draft": "BooleanField", "created_by": "EmailField", "created_on": "DateTimeField", "approved_by": "EmailField", "approved_on": "DateTimeField", "plan_display_order": "IntegerField", "is_settlement_end_date": "BooleanField", "settlement_end_date": "DateTimeField", "payout_frequency": "CharField"}, "PlanCriteria": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "criteria_name": "CharField", "criteria_type": "CharField", "criteria_description": "CharField", "criteria_display_order": "IntegerField", "criteria_data": "SensitiveJSONField", "criteria_column": "JSONField", "criteria_config": "JSONField", "criteria_level": "CharField", "simulate_column": "JSONField"}, "PlanPayee": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "plan_id": "UUIDField", "plan_type": "CharField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "settlement_end_date": "DateTimeField", "plan_exclusion_start_date": "DateTimeField", "plan_exclusion_end_date": "DateTimeField"}, "PlanDocs": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "file_name": "SensitiveCharField", "doc": "SensitiveFileField", "employee_email_id": "EmailField"}, "TempPlanDocs": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "file_name": "SensitiveCharField", "doc": "SensitiveFileField", "employee_email_id": "EmailField"}, "PlanModificationChanges": {"client": "ForeignKey", "temporal_id": "BigAutoField", "plan_id": "UUIDField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "change_type": "CharField", "payee_email_id": "EmailField", "created_at": "DateTimeField"}, "CommissionChanges": {"client": "ForeignKey", "temporal_id": "BigAutoField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "EmailField", "created_at": "DateTimeField", "change_type": "CharField"}, "SettlementChanges": {"client": "ForeignKey", "temporal_id": "BigAutoField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "EmailField", "created_at": "DateTimeField", "change_type": "CharField"}, "Countries": {"id": "AutoField", "country_code": "CharField", "country_name": "CharField", "currency_code": "CharField", "currency_symbol": "CharField", "is_active": "BooleanField", "locale_id": "CharField", "is_client_specific": "BooleanField", "client_ids": "JSONField"}, "ContractFields": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "template_id": "CharField", "selected_role": "CharField", "template_fields": "SensitiveJSONField"}, "CustomCalendar": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "calendar_id": "UUIDField", "name": "CharField", "variable_pay_type": "CharField", "calendar_type": "CharField", "week_start_day": "CharField"}, "CustomPeriods": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "calendar_id": "UUIDField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "period_label": "CharField"}, "CustomCategory": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "custom_category_id": "UUIDField", "custom_category_name": "CharField", "created_by": "EmailField", "created_at": "DateTimeField", "is_default": "BooleanField", "is_active": "BooleanField", "display_order": "IntegerField", "module_name": "CharField"}, "VariableDataType": {"id": "IntegerField", "data_type": "CharField", "data_type_meta": "JSONField", "compatible_type_ids": "JSONField"}, "Variable": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "id": "UUIDField", "name": "CharField", "system_name": "CharField", "category": "CharField", "data_type": "ForeignKey", "model": "CharField", "tags": "JSONField", "applicable_primary_objects": "JSONField", "variable_type": "CharField", "options": "JSONField"}, "ClientVariable": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "variable_id": "UUIDField", "display_name": "CharField", "tags": "JSONField", "options": "JSONField"}, "Operators": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "id": "UUIDField", "name": "CharField", "alt_name": "CharField", "category": "CharField", "operand_type_ids": "JSONField", "output_type_ids": "JSONField", "needs_operand": "BooleanField", "multi_valued": "BooleanField"}, "CustomFields": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "display_name": "CharField", "system_name": "CharField", "field_type": "CharField", "options": "JSONField", "is_archived": "BooleanField", "display_order": "IntegerField", "data_type": "ForeignKey", "is_mandatory": "BooleanField", "is_effective_dated": "BooleanField"}, "CustomFieldData": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "email": "CharField", "data": "SensitiveJSONField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField"}, "Dashboard": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "dashboard_id": "UUIDField", "dashboard_type": "CharField", "name": "CharField", "description": "CharField", "created_at": "DateTimeField", "created_by": "EmailField", "last_modified": "DateTimeField", "is_favorite": "BooleanField", "is_default": "BooleanField", "superset_dashboard_id": "IntegerField", "superset_dataset_ids": "ArrayField"}, "DashboardUserMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "dashboard_id": "UUIDField", "employee_email_id": "EmailField"}, "DashboardUserGroupMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "dashboard_id": "UUIDField", "user_group_id": "UUIDField"}, "Widget": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "widget_id": "UUIDField", "name": "CharField", "dashboard_id": "UUIDField", "position": "JSONField", "dimension": "JSONField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "widget_data": "JSONField", "bi_tool_datasheet_id": "CharField"}, "Docusign": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "email_id": "EmailField", "login_email_id": "EmailField", "account_id": "CharField", "refresh_token": "SensitiveCharField", "refresh_token_expires_at": "DateTimeField", "base_uri": "SensitiveCharField"}, "TemplateDetails": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "account_id": "CharField", "template_id": "CharField", "plan_id": "CharField", "primary_recipient_role": "CharField", "fiscal_year": "IntegerField", "is_archived": "BooleanField"}, "DocusignDocuments": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "template_id": "CharField", "envelope_id": "CharField", "employee_email_id": "EmailField", "fiscal_year": "IntegerField", "file_name": "SensitiveCharField", "s3_url": "SensitiveURLField", "is_archived": "BooleanField"}, "Draws": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "draw_year": "CharField", "draws": "SensitiveJSONField", "draw_updated_by": "EmailField"}, "DRS": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "drs_id": "CharField", "logger": "EmailField", "assignee": "EmailField", "subject": "SensitiveCharField", "status": "CharField", "category": "CharField", "logged_time": "DateTimeField", "involved_users": "JSONField", "sequence_number": "IntegerField"}, "DRSUpdates": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "drs_id": "CharField", "meta": "JSONField", "message": "SensitiveTextField", "message_markdown": "TextField", "updated_by": "EmailField", "updated_time": "DateTimeField"}, "EmailTemplateDetails": {"template_id": "CharField", "template_name": "CharField", "email_event_code": "CharField", "client_ids": "ArrayField"}, "ForecastPlan": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "plan_name": "CharField", "plan_type": "CharField", "plan_start_date": "DateTimeField", "plan_end_date": "DateTimeField", "additional_config": "SensitiveJSONField", "is_draft": "BooleanField", "created_by": "EmailField", "created_on": "DateTimeField", "approved_by": "EmailField", "approved_on": "DateTimeField", "plan_display_order": "IntegerField", "is_settlement_end_date": "BooleanField", "settlement_end_date": "DateTimeField", "payout_frequency": "CharField"}, "ForecastPlanCriteria": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "criteria_id": "UUIDField", "criteria_name": "CharField", "criteria_type": "CharField", "criteria_description": "CharField", "criteria_display_order": "IntegerField", "criteria_data": "SensitiveJSONField", "criteria_column": "JSONField", "criteria_config": "JSONField", "criteria_level": "CharField", "simulate_column": "JSONField"}, "ForecastPlanPayee": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "plan_id": "UUIDField", "plan_type": "CharField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "settlement_end_date": "DateTimeField", "plan_exclusion_start_date": "DateTimeField", "plan_exclusion_end_date": "DateTimeField"}, "ForecastPlanDocs": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "file_name": "SensitiveCharField", "doc": "SensitiveFileField", "employee_email_id": "EmailField"}, "ForecastPlanDetails": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "plan_id": "UUIDField", "plan_name": "CharField", "plan_type": "CharField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "settlement_end_date": "DateTimeField", "plan_exclusion_start_date": "DateTimeField", "plan_exclusion_end_date": "DateTimeField"}, "TempForecastPlanDocs": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "UUIDField", "file_name": "SensitiveCharField", "doc": "SensitiveFileField", "employee_email_id": "EmailField"}, "GenieContext": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "context_id": "UUIDField", "name": "CharField", "sources": "JSONField", "privacy": "CharField", "shared_users": "JSONField", "index_last_updated_at": "DateTimeField", "updated_by": "CharField"}, "GenieSourceFile": {"file_id": "UUIDField", "path_to_file": "CharField", "file": "FileField", "context_id": "UUIDField", "llama_doc_ids": "JSONField", "created_at": "DateTimeField", "created_by": "EmailField", "deleted_at": "DateTimeField", "deleted_by": "EmailField"}, "CommissionAdj": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_type": "CharField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_id": "EmailField", "currency": "CharField", "amount": "SensitiveDecimalField", "is_reverse": "BooleanField", "plan_id": "CharField", "criteria_id": "CharField", "line_item_id": "SensitiveCharField", "reason_category_id": "UUIDField", "reason_category": "CharField", "reason": "SensitiveCharField", "drs_id": "CharField", "posted_by": "EmailField"}, "Split": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_type": "CharField", "deal_owner_id": "EmailField", "deal_id": "CharField", "synthetic_deal_id": "CharField", "split_payee_id": "EmailField", "split_percent": "SensitiveIntegerField", "reason_category": "CharField", "reason": "SensitiveCharField", "drs_id": "CharField", "posted_by": "CharField"}, "DrawRecover": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_type": "CharField", "payee_id": "EmailField", "fiscal_year": "CharField", "period": "CharField", "recoverable_balance": "SensitiveDecimalField", "amount": "SensitiveDecimalField", "comments": "SensitiveCharField", "drs_id": "CharField", "posted_by": "CharField"}, "Ignore": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_type": "CharField", "line_item_type": "CharField", "line_item_id": "CharField", "is_ignored": "BooleanField", "reason_category": "CharField", "reason": "SensitiveCharField", "drs_id": "CharField", "posted_by": "CharField"}, "DealAmount": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "adjustment_id": "UUIDField", "adjustment_type": "CharField", "deal_id": "CharField", "new_amount": "SensitiveDecimalField", "reason_category": "CharField", "reason": "SensitiveCharField", "drs_id": "CharField", "posted_by": "CharField"}, "MFAStatus": {"id": "AutoField", "email_id": "EmailField", "session_id": "CharField", "status": "CharField", "otp": "CharField", "generated_at": "DateTimeField", "channel": "CharField", "source": "CharField", "is_valid": "BooleanField"}, "MFAAttempts": {"id": "AutoField", "email_id": "EmailField", "session_id": "CharField", "attempted_otp": "CharField", "is_success": "BooleanField", "attempted_at": "DateTimeField", "is_valid": "BooleanField"}, "MFARemember": {"email_id": "EmailField", "series_identifier": "CharField", "token": "CharField", "expires_at": "DateTimeField", "is_valid": "BooleanField"}, "ClientNotification": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "notification_name": "CharField", "can_payee_opt_out": "BooleanField", "is_admin_notification": "BooleanField", "frequency": "CharField", "status": "JSONField"}, "PayoutFilters": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "filter_id": "UUIDField", "filter_name": "TextField", "filters": "SensitiveJSONField", "selected_columns": "JSONField", "read_only": "BooleanField"}, "PayoutModel": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "payout_id": "UUIDField", "transaction_date": "SensitiveDateTimeField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_id": "EmailField", "payout_knowledge_date": "DateTimeField", "payout_amount": "SensitiveDecimalField", "payout_adjustments": "SensitiveJSONField", "is_paid": "BooleanField", "comment": "SensitiveTextField"}, "PayoutArrear": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payout_period_start_date": "DateTimeField", "payout_period_end_date": "DateTimeField", "payee_id": "EmailField", "pending_amount": "SensitiveDecimalField", "processed_amount": "SensitiveDecimalField", "ignored_amount": "SensitiveDecimalField", "arrear_adjustments": "SensitiveJSONField", "is_processed": "BooleanField", "processed_at": "DateTimeField", "comment": "SensitiveTextField"}, "PeriodInfo": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "period_label": "CharField"}, "Permissions": {"client": "ForeignKey", "temporal_id": "BigAutoField", "permission_id": "CharField", "permission_name": "CharField", "created_at": "DateTimeField", "updated_at": "DateTimeField", "is_active": "BooleanField", "show_to_user": "BooleanField", "show_data_permissions": "BooleanField", "component_system_name": "CharField", "component_display_name": "CharField", "parent_id": "CharField", "component_order": "IntegerField", "permission_description": "CharField"}, "RolePermissions": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "role_permission_id": "UUIDField", "display_name": "CharField", "description": "CharField", "show_to_user": "BooleanField", "permissions": "JSONField", "created_at": "DateTimeField", "updated_at": "DateTimeField", "is_editable": "BooleanField"}, "CommissionPlanSharedDetails": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "CharField", "permission_type": "CharField", "target_type": "CharField", "target_id": "CharField"}, "ForecastPlanSharedDetails": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "plan_id": "CharField", "permission_type": "CharField", "target_type": "CharField", "target_id": "CharField"}, "Auth0Session": {"temporal_id": "BigAutoField", "created_date": "DateTimeField", "creation_additional_details": "JSONField", "is_blacklisted": "BooleanField", "blacklisted_date": "DateTimeField", "blacklisting_additional_details": "JSONField", "session_id": "CharField"}, "Auth0AccessToken": {"temporal_id": "BigAutoField", "created_date": "DateTimeField", "creation_additional_details": "JSONField", "is_blacklisted": "BooleanField", "blacklisted_date": "DateTimeField", "blacklisting_additional_details": "JSONField", "employee_email_id": "EmailField", "session_id": "CharField", "token_id": "UUIDField"}, "SessionClient": {"session_id": "CharField", "email_id": "EmailField", "client": "ForeignKey", "membership_id": "UUIDField"}, "TokenSession": {"token_id": "UUIDField", "session_id": "CharField"}, "SupportNotification": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_ids": "ArrayField", "email_event_code": "CharField"}, "UserGroup": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "user_group_id": "UUIDField", "user_group_name": "CharField", "user_group_config": "JSONField", "created_by": "CharField", "created_at": "DateTimeField"}, "UserGroupMember": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "user_group_id": "UUIDField", "employee_email_id": "CharField"}, "EmployeeDetailsView": {"client_id": "IntegerField", "employee_email_id": "EmailField", "first_name": "CharField", "last_name": "CharField", "full_name": "CharField", "user_role": "CharField", "created_date": "DateTimeField", "created_by": "EmailField", "profile_picture": "URLField", "exit_date": "DateTimeField", "status": "CharField", "joining_date": "DateTimeField", "employee_id": "CharField", "designation": "CharField", "fixed_pay": "DecimalField", "variable_pay": "DecimalField", "pay_currency": "CharField", "employment_country": "CharField", "payout_frequency": "CharField", "payroll_effective_start_date": "DateTimeField", "payroll_effective_end_date": "DateTimeField", "reporting_manager_email_id": "EmailField", "hierarchy_effective_start_date": "DateTimeField", "hierarchy_effective_end_date": "DateTimeField", "plan_id": "UUIDField", "plan_name": "CharField", "plan_type": "CharField", "plan_effective_start_date": "DateTimeField", "plan_effective_end_date": "DateTimeField", "cfd_employee_email": "CharField", "custom_field_data": "JSONField", "employee_tid": "IntegerField", "payroll_tid": "IntegerField", "plan_tid": "IntegerField", "hier_tid": "IntegerField", "cfd_tid": "IntegerField"}, "ClientConfig": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField", "is_ed_enabled": "BooleanField", "type": "CharField", "value": "SensitiveJSONField"}, "LocalizationModel": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "domain_specific_attributes": "JSONField", "theme_specific_attributes": "JSONField"}, "Quota": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "quota_category_name": "CharField", "display_name": "CharField", "quota_currency_type": "CharField"}, "EmployeeQuota": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "quota_category_name": "CharField", "quota_type": "CharField", "quota_schedule_type": "CharField", "quota_year": "CharField", "is_team_quota": "BooleanField", "team_type": "CharField", "schedule_quota": "SensitiveJSONField", "payout_quota": "SensitiveJSONField", "effective_start_date": "DateTimeField", "effective_end_date": "DateTimeField"}, "IntegrationConfig": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "employee_email_id": "EmailField", "integration_type": "CharField", "config": "SensitiveJSONField"}, "CommissionAdjustmentStatus": {"client_id": "IntegerField", "adjustment_id": "UUIDField", "period_start_date": "DateTimeField", "period_end_date": "DateTimeField", "payee_email_id": "EmailField", "currency": "CharField", "amount": "SensitiveDecimalField", "plan_id": "CharField", "criteria_id": "CharField", "line_item_id": "CharField", "reason_category_id": "UUIDField", "reason_category": "CharField", "reason": "SensitiveCharField", "approval_status": "CharField", "is_approval_skipped": "BooleanField", "skip_approval_reason": "CharField", "is_auto_approved": "BooleanField", "additional_details": "JSONField"}, "MetricDefinition": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "metric_id": "UUIDField", "metric_name": "CharField", "metric_description": "CharField", "metric_category": "CharField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "payee_field": "CharField", "date_field": "CharField", "formula": "JSONField", "valid_dimensions": "JSONField"}, "ClientAuditTrail": {"trail_id": "BigAutoField", "client": "ForeignKey", "changed_by": "CharField", "changed_at": "DateTimeField", "changes_diff": "SensitiveJSONField", "change_type": "CharField"}, "SolarSchedule": {"event": "CharField", "latitude": "DecimalField", "longitude": "DecimalField"}, "IntervalSchedule": {"every": "IntegerField", "period": "CharField"}, "ClockedSchedule": {"clocked_time": "DateTimeField"}, "CrontabSchedule": {"minute": "CharField", "hour": "CharField", "day_of_week": "CharField", "day_of_month": "CharField", "month_of_year": "CharField", "timezone": "TimeZoneField"}, "PeriodicTasks": {"ident": "SmallIntegerField", "last_update": "DateTimeField"}, "PeriodicTask": {"name": "CharField", "task": "CharField", "interval": "ForeignKey", "crontab": "ForeignKey", "solar": "ForeignKey", "clocked": "ForeignKey", "args": "TextField", "kwargs": "TextField", "queue": "CharField", "exchange": "CharField", "routing_key": "CharField", "headers": "TextField", "priority": "PositiveIntegerField", "expires": "DateTimeField", "expire_seconds": "PositiveIntegerField", "one_off": "BooleanField", "start_time": "DateTimeField", "enabled": "BooleanField", "last_run_at": "DateTimeField", "total_run_count": "PositiveIntegerField", "date_changed": "DateTimeField", "description": "TextField"}, "SlackBot": {"client_id": "CharField", "app_id": "CharField", "enterprise_id": "CharField", "enterprise_name": "TextField", "team_id": "CharField", "team_name": "TextField", "bot_token": "SensitiveTextField", "bot_refresh_token": "SensitiveTextField", "bot_token_expires_at": "DateTimeField", "bot_id": "CharField", "bot_user_id": "CharField", "bot_scopes": "TextField", "is_enterprise_install": "BooleanField", "installed_at": "DateTimeField"}, "SlackInstallation": {"client_id": "CharField", "app_id": "CharField", "enterprise_id": "CharField", "enterprise_name": "TextField", "enterprise_url": "TextField", "team_id": "CharField", "team_name": "TextField", "bot_token": "SensitiveTextField", "bot_refresh_token": "SensitiveTextField", "bot_token_expires_at": "DateTimeField", "bot_id": "CharField", "bot_user_id": "TextField", "bot_scopes": "TextField", "user_id": "CharField", "user_token": "SensitiveTextField", "user_refresh_token": "SensitiveTextField", "user_token_expires_at": "DateTimeField", "user_scopes": "TextField", "incoming_webhook_url": "TextField", "incoming_webhook_channel": "TextField", "incoming_webhook_channel_id": "TextField", "incoming_webhook_configuration_url": "TextField", "is_enterprise_install": "BooleanField", "token_type": "CharField", "installed_at": "DateTimeField"}, "SlackClientDetails": {"client_id": "IntegerField", "bot_token": "SensitiveTextField", "bot_refresh_token": "SensitiveTextField", "bot_token_expires_at": "DateTimeField", "team_id": "CharField", "user_id": "CharField", "updated_at": "DateTimeField"}, "SlackOAuthState": {"state": "SensitiveCharField", "expire_at": "DateTimeField"}, "MsteamsTenantDetails": {"client_id": "IntegerField", "app_version": "CharField", "app_catalog_id": "SensitiveCharField", "app_external_id": "SensitiveCharField", "app_package_s3_url": "SensitiveTextField", "tenant_id": "SensitiveCharField", "msteams_installed_by": "TextField", "msteams_updated_by": "TextField", "installed_by": "TextField", "updated_by": "TextField", "installed_at": "DateTimeField", "updated_at": "DateTimeField"}, "MsteamsUserServiceUrl": {"user_id": "SensitiveCharField", "email_id": "CharField", "service_url": "SensitiveTextField", "tenant_id": "SensitiveCharField", "updated_at": "DateTimeField"}, "MsteamsAdminTokenDetails": {"client_id": "CharField", "tenant_id": "CharField", "admin_refresh_token": "SensitiveCharField", "admin_access_token": "SensitiveCharField", "updated_at": "DateTimeField"}, "CrystalViewModel": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "crystal_view_id": "UUIDField", "crystal_view_name": "CharField", "crystal_view_description": "CharField", "status": "CharField", "settings_data": "SensitiveJSONField", "source_view_id": "UUIDField", "last_modified_at": "DateTimeField", "last_modified_by": "CharField"}, "CrystalPayee": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "payee": "EmailField", "crystal_view_id": "UUIDField"}, "PayeeTableOverrides": {"client_id": "IntegerField", "view_id": "UUIDField", "table_id": "UUIDField", "payee_email": "EmailField", "data_overrides": "SensitiveJSONField", "commissions_with_overrides": "SensitiveJSONField", "created_at": "DateTimeField"}, "AsyncTask": {"client": "ForeignKey", "created_by": "CharField", "task_name": "CharField", "created_at": "DateTimeField", "params": "SensitiveJSONField", "params_key": "SensitiveCharField", "status": "CharField", "result": "SensitiveJSONField", "read_at": "DateTimeField", "completed_at": "DateTimeField"}, "DatasheetExecutionContextDB": {"client": "ForeignKey", "e2e_sync_run_id": "CharField", "sync_type": "CharField", "datasheet_details": "JSONField", "stale_datasheets": "JSONField", "total_datasheets_count": "IntegerField", "root_stale_datasheets": "JSONField", "queue_name": "CharField", "is_polling_threshold_breached": "BooleanField", "created_at": "DateTimeField"}, "ETLJobQueueStatus": {"id": "BigAutoField", "emr_id": "CharField", "session_id": "CharField", "statement_id": "CharField", "e2e_sync_run_id": "UUIDField", "ds_sync_run_id": "CharField", "wrapper_sync_run_id": "CharField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "status": "CharField", "payload": "TextField", "job_submitted_ts": "DateTimeField", "job_picked_ts": "DateTimeField", "job_completed_ts": "DateTimeField"}, "LivySession": {"id": "BigAutoField", "emr_id": "CharField", "session_id": "CharField", "status": "CharField", "last_job_submitted_ts": "DateTimeField"}, "Agent": {"client_id": "IntegerField", "agent_id": "UUIDField", "version_id": "UUIDField", "version": "CharField", "model_name": "CharField", "name": "CharField", "description": "TextField", "core_prompt_prefix": "TextField", "core_prompt": "TextField", "tool_ids": "JSONField", "custom_api_tool_ids": "JSONField", "created_date": "DateTimeField", "last_updated_date": "DateTimeField", "latest_version": "BooleanField", "is_published": "BooleanField", "is_production": "BooleanField", "is_tools_enabled": "BooleanField", "tag": "CharField"}, "Helper": {"helper_id": "UUIDField", "agent_id": "UUIDField", "agent_version_id": "UUIDField", "model_name": "CharField", "name": "CharField", "description": "TextField", "system_prompt": "TextField", "rag_modules": "JSONField"}, "CustomAPITool": {"tool_id": "UUIDField", "tool_name": "CharField", "client_id": "IntegerField", "agent_id": "UUIDField", "request_url": "TextField", "request_method": "CharField", "tool_type": "CharField", "description": "TextField", "api_schema": "JSONField"}, "CommonPrompts": {"prompt_key": "CharField", "prompt_value": "TextField"}, "LLMAgentUserSession": {"client_id": "IntegerField", "session_id": "UUIDField", "version_id": "UUIDField", "agent_id": "UUIDField", "session_name": "CharField", "description": "TextField", "created_date": "DateTimeField", "last_interaction_date": "DateTimeField", "employee_email_id": "EmailField", "is_active": "BooleanField", "files": "JSONField", "share_chat": "BooleanField"}, "LLMAgentUserSessionMessages": {"message_id": "UUIDField", "thread_id": "UUIDField", "session_id": "UUIDField", "sender": "CharField", "sent_time": "DateTimeField", "message_content": "SensitiveJSONField", "chain_of_thought": "SensitiveJSONField", "parent_message_id": "UUIDField", "approval_required": "BooleanField", "approval_status": "CharField", "internal_tracking_message": "BooleanField", "like": "IntegerField", "feedback": "CharField"}, "DatasheetTransformation": {"client": "ForeignKey", "datasheet_id": "UUIDField", "transformation_id": "UUIDField", "spec": "JSONField", "is_saved": "BooleanField", "created_at": "DateTimeField", "updated_at": "DateTimeField", "order": "IntegerField", "type": "CharField", "version": "AutoIncVersionField"}, "DatasheetPin": {"temporal_id": "AutoField", "datasheet_id": "UUIDField", "client": "ForeignKey", "is_deleted": "BooleanField", "created_by": "CharField", "updated_by": "CharField", "created_at": "DateTimeField", "updated_at": "DateTimeField"}, "DatasheetTag": {"temporal_id": "AutoField", "client": "ForeignKey", "tag_id": "UUIDField", "name": "CharField", "type": "CharField", "is_deleted": "BooleanField", "description": "CharField", "additional_details": "JSONField", "created_at": "DateTimeField", "updated_at": "DateTimeField", "version": "AutoIncVersionField"}, "DatasheetTagMap": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "datasheet_id": "UUIDField", "tag_id": "UUIDField", "version": "AutoIncVersionField"}, "DatasheetVariableTemp": {"client_id": "IntegerField", "databook_id": "UUIDField", "datasheet_id": "UUIDField", "system_name": "CharField", "display_name": "CharField", "description": "TextField", "data_type_id": "IntegerField", "source_cf_meta_data": "JSONField", "tags": "JSONField", "field_order": "IntegerField", "meta_data": "JSONField", "source_variable_id": "CharField", "source_id": "CharField", "variable_id": "UUIDField", "is_selected": "BooleanField", "source_type": "CharField", "is_primary": "BooleanField", "warning": "TextField"}, "DatasheetView": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "view_id": "UUIDField", "name": "CharField", "datasheet_id": "UUIDField", "filter_id": "UUIDField", "pivot_id": "UUIDField", "last_updated_by": "CharField", "created_at": "DateTimeField", "ordered_columns": "ArrayField", "hidden_columns": "ArrayField", "version": "AutoIncVersionField"}, "DatasheetViewFilter": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "filter_id": "UUIDField", "filter_data": "JSONField"}, "DatasheetViewPivot": {"temporal_id": "BigAutoField", "client": "ForeignKey", "is_deleted": "BooleanField", "pivot_id": "UUIDField", "pivot_data": "JSONField", "created_at": "DateTimeField", "updated_at": "DateTimeField"}, "GmailAccessToken": {"gmail_id": "EmailField", "access_token": "SensitiveCharField", "refresh_token": "SensitiveCharField", "last_modified_at": "DateTimeField"}, "GmailEverstageUserMap": {"gmail_id": "EmailField", "everstage_email_id": "EmailField"}, "ApprovalRuleGroup": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "rule_group_id": "UUIDField", "group_name": "CharField", "stage_count": "IntegerField", "form_builder_id": "UUIDField", "created_by": "EmailField", "group_description": "CharField", "notify_on_reject": "JSONField", "notify_on_approve": "JSONField"}, "ApprovalRules": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "rule_id": "UUIDField", "rule_group_id": "UUIDField", "form_builder_id": "UUIDField", "stage_order": "IntegerField", "stage_template_id": "UUIDField", "condition": "JSONField", "approvers": "JSONField", "rule_name": "CharField"}, "AutoSaveForm": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "form_id": "UUIDField", "form_builder_id": "UUIDField", "form_data": "JSONField"}, "EverstageForm": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "form_id": "UUIDField", "form_builder_id": "UUIDField", "form_spec": "JSONField", "form_data": "JSONField", "status": "CharField", "created_by": "CharField", "updated_by": "CharField", "created_at": "DateTimeField"}, "FormSpecChange": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "form_id": "UUIDField", "form_builder_id": "UUIDField", "form_spec": "JSONField"}, "QuoteLineItem": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "quote_id": "CharField", "quote_line_item_id": "CharField", "start_date": "DateTimeField", "end_date": "DateTimeField", "phase_id": "CharField", "phase_name": "CharField", "product_name": "CharField", "sku": "CharField", "billing_frequency": "CharField", "billing_type": "CharField", "prorate_multiplier": "DecimalField", "quantity": "IntegerField", "discount_percent": "DecimalField", "list_unit_price": "DecimalField", "net_unit_price": "DecimalField", "list_total": "DecimalField", "net_total": "DecimalField", "prorated_list_total": "DecimalField", "prorated_net_total": "DecimalField", "custom_data": "JSONField", "pricepoint_data": "JSONField", "order": "IntegerField", "unit_of_measure": "CharField"}, "Quote": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "quote_id": "UUIDField", "quote_display_id": "IntegerField", "quote_name": "CharField", "quote_currency": "CharField", "form_id": "UUIDField", "valid_till": "DateTimeField", "is_primary": "BooleanField", "start_date": "DateTimeField", "end_date": "DateTimeField", "duration_value": "IntegerField", "duration_type": "CharField", "opportunity_id": "CharField", "account_id": "CharField", "contact_id": "CharField", "bill_to": "CharField", "ship_to": "CharField", "list_quote_total": "FloatField", "net_quote_total": "FloatField", "discount_amount": "FloatField", "closed_date": "DateTimeField", "custom_data": "JSONField", "owner_id": "CharField", "created_at": "DateTimeField", "created_by": "CharField"}, "QuoteStatus": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "quote_status_id": "UUIDField", "quote_id": "UUIDField", "status": "CharField", "tags": "JSONField", "created_at": "DateTimeField", "updated_by": "CharField", "comment": "TextField"}, "BillingFrequency": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "billing_frequency_id": "UUIDField", "name": "CharField", "description": "TextField", "multiplier_value": "FloatField", "multiplier_type": "CharField", "created_at": "DateTimeField", "created_by": "CharField"}, "PriceFactor": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "price_factor_id": "UUIDField", "name": "CharField", "description": "TextField", "values": "JSONField", "created_at": "DateTimeField", "created_by": "CharField"}, "PriceBook": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "price_book_id": "UUIDField", "name": "CharField", "description": "TextField", "status": "CharField", "price_factors": "JSONField", "created_by": "CharField", "created_at": "DateTimeField"}, "PriceBookProduct": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "price_book_product_id": "UUIDField", "price_book_id": "UUIDField", "sku": "CharField", "created_at": "DateTimeField", "created_by": "CharField"}, "CustomTrigger": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "custom_trigger_id": "UUIDField", "name": "CharField", "description": "CharField", "created_by": "CharField", "category": "CharField", "trigger_type": "CharField", "trigger_params": "JSONField", "trigger_icon": "CharField", "created_at": "DateTimeField", "is_draft": "BooleanField"}, "TriggerCategory": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "category_id": "UUIDField", "name": "CharField", "description": "CharField", "created_by": "CharField", "created_at": "DateTimeField", "is_draft": "BooleanField"}, "NotificationTracker": {"client": "ForeignKey", "notification_id": "BigAutoField", "created_at": "DateTimeField", "employee_email_id": "CharField", "channel": "CharField", "content": "JSONField", "one_time_key": "CharField", "one_time_key_hash": "CharField", "additional_details": "JSONField"}, "WorkflowStatus": {"wf_id": "UUIDField", "client_id": "IntegerField", "status": "CharField", "start_time": "DateTimeField", "end_time": "DateTimeField", "instance_id": "UUIDField", "trigger_id": "UUIDField", "trigger": "CharField", "status_log": "JSONField", "audit": "CharField"}, "WorkflowComponentStatus": {"wf_id": "UUIDField", "client_id": "IntegerField", "status": "CharField", "start_time": "DateTimeField", "end_time": "DateTimeField", "instance_id": "UUIDField", "component_id": "CharField", "component_type": "CharField", "component_log": "JSONField"}, "Workflow": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "wf_id": "UUIDField", "name": "CharField", "description": "CharField", "created_by": "CharField", "trigger": "CharField", "components": "JSONField", "logical_structure": "JSONField", "created_at": "DateTimeField", "is_draft": "BooleanField", "is_active": "BooleanField"}, "UpstreamTimestamps": {"id": "AutoField", "client": "ForeignKey", "integration_id": "UUIDField", "api_changes_synced_till": "DateTimeField", "api_deletes_synced_till": "DateTimeField", "upstream_source_synced_till": "DateTimeField", "last_extracted_by": "CharField"}, "UpstreamExtractionStatus": {"id": "AutoField", "client": "ForeignKey", "integration_id": "UUIDField", "enabled": "BooleanField", "technique": "CharField", "meta_data": "JSONField", "updated_at": "DateTimeField", "is_running": "BooleanField", "last_run_at": "DateTimeField", "backoff_info": "JSONField", "event_logs": "JSONField"}, "FivetranSyncLog": {"id": "BigAutoField", "e2e_sync_run_id": "UUIDField", "sync_run_id": "UUIDField", "client_id": "IntegerField", "sync_start_time": "DateTimeField", "sync_completion_time": "DateTimeField", "sync_status": "CharField", "connector_id": "CharField", "integration_ids": "JSONField", "sync_params": "JSONField", "skipped_connectors": "JSONField", "webhook_log": "JSONField", "sync_run_log": "JSONField", "additional_details": "JSONField"}, "ConnectorDefinition": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "connector_name": "CharField", "connector_id": "CharField", "has_object_url": "BooleanField", "has_fields_url": "BooleanField", "object_url": "CharField", "fields_url": "CharField", "connector_frontend_data": "JSONField"}, "ConnectorObject": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "connector_id": "CharField", "object_id": "CharField"}, "ConnectorObjectvariable": {"temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "connector_id": "CharField", "object_id": "CharField", "system_name": "CharField", "display_name": "CharField", "data_type_id": "IntegerField"}, "EverTableTag": {"client": "ForeignKey", "temporal_id": "BigAutoField", "knowledge_begin_date": "DateTimeField", "knowledge_end_date": "DateTimeField", "is_deleted": "BooleanField", "additional_details": "JSONField", "system_name": "CharField", "display_name": "CharField", "component": "CharField", "type": "CharField", "meta_data": "JSONField", "description": "TextField"}}