from slack_everstage.models import SlackClientDetails


class SlackClientDetailsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return SlackClientDetails.objects.filter(client_id=self.client_id)

    def get(self):
        query_set = self.client_aware()
        if query_set.exists():
            return query_set.first()
        return None

    def save(self, data):
        data.save()
