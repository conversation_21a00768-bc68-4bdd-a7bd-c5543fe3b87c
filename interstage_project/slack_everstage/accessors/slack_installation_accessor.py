from slack_everstage.models import SlackInstallation


class SlackAccessor:
    def __init__(self, user_id, team_id):
        self.user_id = user_id
        self.team_id = team_id

    def slack_user_aware(self):
        return SlackInstallation.objects.filter(user_id=self.user_id)

    def slack_user_team_aware(self):
        return SlackInstallation.objects.filter(
            user_id=self.user_id, team_id=self.team_id
        )

    def get_bot_token(self):
        return (
            self.slack_user_aware()
            .order_by("-installed_at")
            .values("bot_token")
            .first()
            .get("bot_token")
        )

    def get_slack_installation(self):
        return self.slack_user_team_aware().order_by("-installed_at").first()
