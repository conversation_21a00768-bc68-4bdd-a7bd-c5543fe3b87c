# ----------------------
# Database tables
# ----------------------

from django.db import models

from interstage_project.db.models import (
    EsBooleanField,
    EsCharField,
    EsDateTimeField,
    EsIntegerField,
    EsTextField,
)


class SlackBot(models.Model):
    client_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    app_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    enterprise_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    enterprise_name = EsTextField(null=True, is_sensitive=False)
    team_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    team_name = EsTextField(null=True, is_sensitive=False)
    bot_token = EsTextField(null=True)
    bot_refresh_token = EsTextField(null=True)
    bot_token_expires_at = EsDateTimeField(null=True, is_sensitive=False)
    bot_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    bot_user_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    bot_scopes = EsTextField(null=True, is_sensitive=False)
    is_enterprise_install = EsBooleanField(null=True, is_sensitive=False)
    installed_at = EsDateTimeField(null=False, is_sensitive=False)

    class Meta:
        db_table = "slack_bot"
        indexes = [
            models.Index(
                fields=["client_id", "enterprise_id", "team_id", "installed_at"]
            ),
        ]


class SlackInstallation(models.Model):
    client_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    app_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    enterprise_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    enterprise_name = EsTextField(null=True, is_sensitive=False)
    enterprise_url = EsTextField(null=True, is_sensitive=False)
    team_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    team_name = EsTextField(null=True, is_sensitive=False)
    bot_token = EsTextField(null=True)
    bot_refresh_token = EsTextField(null=True)
    bot_token_expires_at = EsDateTimeField(null=True, is_sensitive=False)
    bot_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    bot_user_id = EsTextField(null=True, is_sensitive=False)
    bot_scopes = EsTextField(null=True, is_sensitive=False)
    user_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    user_token = EsTextField(null=True)
    user_refresh_token = EsTextField(null=True)
    user_token_expires_at = EsDateTimeField(null=True, is_sensitive=False)
    user_scopes = EsTextField(null=True, is_sensitive=False)
    incoming_webhook_url = EsTextField(null=True, is_sensitive=False)
    incoming_webhook_channel = EsTextField(null=True, is_sensitive=False)
    incoming_webhook_channel_id = EsTextField(null=True, is_sensitive=False)
    incoming_webhook_configuration_url = EsTextField(null=True, is_sensitive=False)
    is_enterprise_install = EsBooleanField(null=True, is_sensitive=False)
    token_type = EsCharField(null=True, max_length=32, is_sensitive=False)
    installed_at = EsDateTimeField(null=False, is_sensitive=False)

    class Meta:
        db_table = "slack_installation"
        indexes = [
            models.Index(
                fields=[
                    "client_id",
                    "enterprise_id",
                    "team_id",
                    "user_id",
                    "installed_at",
                ]
            ),
        ]


class SlackClientDetails(models.Model):
    client_id = EsIntegerField(null=False, primary_key=True, is_sensitive=False)
    bot_token = EsTextField(null=True)
    bot_refresh_token = EsTextField(null=True)
    bot_token_expires_at = EsDateTimeField(null=True, is_sensitive=False)
    team_id = EsCharField(null=True, max_length=32, is_sensitive=False)
    user_id = EsCharField(null=False, max_length=32, is_sensitive=False)
    updated_at = EsDateTimeField(null=False, is_sensitive=False)

    class Meta:
        db_table = "slack_client_details"
        indexes = [
            models.Index(
                fields=[
                    "client_id",
                    "bot_token",
                    "team_id",
                    "user_id",
                    "updated_at",
                ]
            ),
        ]


class SlackOAuthState(models.Model):
    state = EsCharField(null=False, max_length=64)
    expire_at = EsDateTimeField(null=False, is_sensitive=False)

    class Meta:
        db_table = "slack_oauth_state"
