import datetime
import json
import logging
import traceback

from celery import shared_task
from django.utils import timezone
from slack_sdk import WebClient

import spm.services.commission_data_services as cs
from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_features,
    get_client_hidden_categories,
    is_slack_connected,
)
from commission_engine.accessors.schedule_accessor import (
    MilestoneNotificationStatusAccessor,
)
from commission_engine.utils.date_utils import (
    get_fiscal_year,
    get_period_start_and_end_date,
)
from commission_engine.utils.general_data import (
    SLACK_MESSAGES,
    Freq,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.general_utils import get_statements_url_for_payee
from common.celery.celery_base_task import EverCeleryBaseTask
from interstage_project.threadlocal_log_context import set_threadlocal_context
from slack_everstage.services.slack_app_service import app, get_valid_token
from slack_everstage.utils.block_kit import (
    build_action_block,
    build_button_element,
    build_image_block,
    build_section_block,
)
from slack_everstage.utils.emoji import Emoji
from spm.accessors.approval_workflow_accessors import ApprovalRequestsAccessor
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.accessors.quota_acessors import QuotaAccessor
from spm.constants.approval_workflow_constants import APPROVAL_ENTITY_TYPES
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.approval_line_items.line_item_services import (
    approve_request as approve_request_line_items,
)
from spm.services.approval_line_items.line_item_services import (
    reject_request as reject_request_line_items,
)
from spm.services.approval_workflow_services.approval_instance_services import (
    approve_request,
    reject_request,
)
from spm.services.config_services.slack_config_services import (
    get_employee_in_slack_from_email_id,
)
from spm.services.dashboard_services.payee_commission_services import (
    get_commission_buddy_data,
    get_commission_payout_for_year_for_payee,
    get_payee_commission_payout_for_curr_period,
    get_period_name_for_date,
)
from spm.services.dashboard_services.payee_quota_services import (
    calculate_payee_quota_rank,
)
from spm.services.localization_services import get_localized_message_service
from spm.services.settlement_actions_service.settlement_total_service import (
    SettlementTotalService,
)

from ...utils.slack_utils import get_site_url, set_request_context
from .utils import get_current_period_label, get_new_line_section

URL = get_site_url()

logger = logging.getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def post_pending_approval(client_id, email_id, template, is_bulk=False):
    """
    Posts pending approval request to slack
    """
    try:
        set_threadlocal_context({"client_id": client_id})
        logger.info("Posting pending approval notification for the payee %s", email_id)

        # get user info
        employee = get_employee_in_slack_from_email_id(
            client_id, email_id, "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION"
        )

        if employee is None:
            logger.info(
                "Skipping pending approval notification on Slack for user %s",
                email_id,
            )
            return

        is_slack_connected_for_client = is_slack_connected(employee.client_id)

        if not is_slack_connected_for_client:
            logger.info(
                "Slack not connected for client %s, Skipping slack notification for: %s",
                employee.client_id,
                email_id,
            )
            return "slack not connected"

        set_request_context(employee)
        config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
            client_id, email_id
        )
        if config_record is None:
            logger.info(
                "Error posting pending approval notification on slack for %s",
                email_id,
            )
            return

        slack_config = (
            config_record.config if config_record and config_record.config else {}
        )
        user_id = slack_config.get("slack_user_id")
        team_id = slack_config.get("slack_team_id")

        if not user_id:
            logger.info(
                "User not in slack workspace for client id %s, Skipping slack notification for: %s",
                employee.client_id,
                email_id,
            )
            return "user not in slack"

        # create slack api client
        tokens = get_valid_token(user_id, team_id)
        client = WebClient(token=tokens.get("bot_token"))

        blocks = get_pending_approval(template, is_bulk, email_id)
        client.chat_postMessage(
            text="Approval Request",
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )
        return blocks

    except Exception as o:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", o)


@shared_task(base=EverCeleryBaseTask)
def post_approval_request(client_id, email_id, template):
    """
    Posts new approval request to slack
    """
    try:
        set_threadlocal_context({"client_id": client_id})
        logger.info("Posting new approval notification for the payee %s", email_id)

        # get user info
        employee = get_employee_in_slack_from_email_id(
            client_id, email_id, "NEW_APPROVAL_REQUEST_NOTIFICATION"
        )

        if employee is None:
            logger.info(
                "Skipping new approval notification on Slack for user %s",
                email_id,
            )
            return

        is_slack_connected_for_client = is_slack_connected(employee.client_id)

        if not is_slack_connected_for_client:
            logger.info(
                "Slack not connected for client %s, Skipping slack notification for: %s",
                employee.client_id,
                email_id,
            )
            return "slack not connected"

        set_request_context(employee)
        config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
            client_id, email_id
        )
        if config_record is None:
            logger.info(
                "Error posting new approval notification on slack for %s",
                email_id,
            )
            return

        slack_config = (
            config_record.config if config_record and config_record.config else {}
        )
        user_id = slack_config.get("slack_user_id")
        team_id = slack_config.get("slack_team_id")

        if not user_id:
            logger.info(
                "User not in slack workspace for client id %s, Skipping slack notification for: %s",
                employee.client_id,
                email_id,
            )
            return "user not in slack"

        # create slack api client
        tokens = get_valid_token(user_id, team_id)
        client = WebClient(token=tokens.get("bot_token"))

        blocks = get_approval_notification(client_id, template, email_id)
        client.chat_postMessage(
            text="Approval Request",
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )
        return blocks

    except Exception as o:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", o)


def get_approval_notification(client_id, template_data, email_id):
    """
    Builds slack blocks for new approval notification
    """
    blocks = []
    emp_name = (
        template_data["payee_name"][0]
        if type(template_data["payee_name"]) == tuple
        else template_data["payee_name"]
    )
    approver_name = template_data["name"].split(" ")[0]
    # 0. Header
    blocks.append(
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"{Emoji.WAVE.value} Hey {approver_name}! There's a new payout approval request for you.",
            },
        }
    )

    # 1. payee
    blocks.append(
        build_section_block(
            text=f"*Payee :* {emp_name}",
            text_type="mrkdwn",
        )
    )

    # 2. Period
    period = template_data["payout_period"]
    blocks.append(
        build_section_block(
            text=f"*Period {Emoji.SPIRAL_CALENDAR_PAD.value}:* {period} ",
            text_type="mrkdwn",
        )
    )

    # 3. Payout
    payout = template_data["payout_amount"]
    blocks.append(
        build_section_block(
            text=f"*Payout {Emoji.MONEYBAG.value}:* {payout} ",
            text_type="mrkdwn",
        )
    )

    # 4. Approve by
    due = template_data["approval_due_on"]
    if due:
        blocks.append(
            build_section_block(
                text=f"*Approve by {Emoji.HOURGLASS.value}:* {due} ",
                text_type="mrkdwn",
            )
        )

    # 5. buttons
    request_id = str(template_data["approval_request_id"])
    statement_url_tem = template_data["statement_url"]
    statement_url = (
        statement_url_tem[0] if type(statement_url_tem) == tuple else statement_url_tem
    )
    approve_workflow_args = {
        "payee_name": emp_name,
        "request_id": request_id,
        "client_id": client_id,
        "period": period,
        "statement_url": statement_url,
    }

    reject_workflow_args = {
        "payee_name": emp_name,
        "request_id": request_id,
        "client_id": client_id,
        "period": period,
        "statement_url": statement_url,
    }

    view_approval_args = {
        "payee_name": emp_name,
        "email_id": email_id,
    }
    buttons = [
        {
            "type": "plain_text",
            "text": "View Statement",
            "action_id": "view_approval_statements",
            "value": json.dumps(view_approval_args),
            "url": statement_url,
        },
        {
            "type": "plain_text",
            "text": "Approve",
            "action_id": "approve_workflow",
            "value": json.dumps(approve_workflow_args),
            "style": "primary",
        },
        {
            "type": "plain_text",
            "text": "Reject",
            "action_id": "reject_workflow",
            "value": json.dumps(reject_workflow_args),
            "style": "danger",
        },
    ]

    button_elements = []
    for button in buttons:
        button_elements.append(
            build_button_element(
                action_id=button.get("action_id"),
                value=button.get("value"),
                text_type=button.get("type"),
                text=button.get("text"),
                url=button.get("url"),
                style=button.get("style", "default"),
            )
        )

    blocks.append(build_action_block(elements=button_elements))

    return blocks


def get_pending_approval(template_data, is_bulk, email_id):
    """
    Builds slack blocks for pending approval request notification
    """
    blocks = []
    emp_name = template_data["name"]
    first_name = emp_name.split(" ")[0]
    pending_request_count = template_data["pending_request_count"]
    if is_bulk:
        period = template_data["payout_period"]
        header_text = f"You have *{pending_request_count} approval requests* for the {period} period."
    else:
        header_text = f"You have *{pending_request_count} approval requests* waiting. Please review and approve them."

    # 0. Header
    blocks.append(
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"{Emoji.WAVE.value} Hey {first_name}! {header_text}",
            },
        }
    )

    # 1. button
    view_approval_args = {
        "payee_name": emp_name,
        "email_id": email_id,
    }
    page_url = template_data["approvals_page_url"]
    buttons = [
        {
            "type": "plain_text",
            "text": "View Approval Requests",
            "action_id": "view_approvals",
            "value": json.dumps(view_approval_args),
            "url": page_url,
        },
    ]

    button_elements = []
    for button in buttons:
        button_elements.append(
            build_button_element(
                action_id=button.get("action_id"),
                value=button.get("value"),
                text_type=button.get("type"),
                text=button.get("text"),
                url=button.get("url"),
            )
        )

    blocks.append(build_action_block(elements=button_elements))

    return blocks


def get_quota_map(client_id, commission_date, email_id, payroll):
    commission_data = get_commission(client_id, email_id, commission_date, payroll)
    quota_and_rank = create_quota_category_map(client_id, email_id, commission_data)
    return quota_and_rank["quota_map"]


def get_commission(client_id, email_id, commission_date, payroll):
    client_data = get_client(client_id)
    period = get_period_start_and_end_date(
        commission_date,
        client_data.fiscal_start_month,
        payroll.payout_frequency.lower(),
        client_id=client_id,
    )
    psd = period.get("start_date").strftime("%d/%m/%Y")
    ped = period.get("end_date").strftime("%d/%m/%Y")
    commission_data = cs.get_commission_data(client_id, psd, ped, email_id)

    return commission_data


def get_commission_v2(client_id, email_id, commission_date, payroll):
    client_data = get_client(client_id)
    period = get_period_start_and_end_date(
        commission_date,
        client_data.fiscal_start_month,
        payroll.payout_frequency.lower(),
        client_id=client_id,
    )

    sts = SettlementTotalService(client_id, period.get("end_date"), [email_id])
    commission_data = sts.get_statements_data()

    return commission_data


def get_quota_sub_block(
    blocks, quota_category, quota_localized, quota_det, has_reportees, quota_type
):
    text_map = {
        "as_individual": "`As Individual:`",
        "as_manager": "`As Manager:`",
    }
    if has_reportees and len(blocks) == 0:
        blocks.append(get_new_line_section())
        blocks.append(
            build_section_block(
                text=text_map[quota_type],
                text_type="mrkdwn",
            )
        )
    else:
        blocks.append(get_new_line_section())

    blocks.append(
        build_section_block(
            text=f"*{quota_localized}:* {quota_category}",
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=f"*{quota_localized} Attainment {Emoji.DART.value} %:* {round(quota_det.get(quota_type).get('quota_attainment'), 2)}",
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=f"*{quota_localized} value:* {'{:,}'.format(quota_det.get(quota_type).get('quota_value'))}",
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=f"*Actuals:* {'{:,}'.format(round(quota_det.get(quota_type).get('quota_erosion'), 2))}",
            text_type="mrkdwn",
        )
    )
    return blocks


def get_quota_block(
    quota_category_map,
    _currency_symbol,
    has_reportees,
    client_id,
    quota_localized="Quota",
):
    individual_blocks = []
    manager_blocks = []

    quota_categories = list(quota_category_map.keys())
    qs = QuotaAccessor(client_id).get_quota_category_display_name_map(quota_categories)
    display_name_map = {
        record["quota_category_name"]: record["display_name"] for record in qs
    }
    if "Primary" in quota_categories:
        display_name_map["Primary"] = (
            f"Primary {get_localized_message_service(SLACK_MESSAGES.QUOTA.value, client_id)}"
        )

    for quota_category, quota_det in quota_category_map.items():
        if quota_det.get("as_individual"):
            get_quota_sub_block(
                individual_blocks,
                display_name_map.get(quota_category),
                quota_localized,
                quota_det,
                has_reportees,
                "as_individual",
            )
        if has_reportees and quota_det.get("as_manager"):
            get_quota_sub_block(
                manager_blocks,
                display_name_map.get(quota_category),
                quota_localized,
                quota_det,
                has_reportees,
                "as_manager",
            )

    return individual_blocks + manager_blocks


@shared_task(base=EverCeleryBaseTask)
def post_commissions_monthly(client_id, email_id, commission_date=None):
    """

    Args:
        client_id: es_client
        email_id: slack user email
        commission_date: the date for which the commission period falls if None show curr period commission
        body:

    Returns:

    """
    set_threadlocal_context({"client_id": client_id})
    logger.info("Posting commission for the payee %s", email_id)
    if commission_date is None:
        commission_date = timezone.now() - datetime.timedelta(days=1)

    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "MONTHLY_COMMISSION_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping monthly commissions notification on Slack for user %s",
            email_id,
        )
        return

    set_request_context(employee)

    is_slack_connected_for_client = is_slack_connected(employee.client_id)

    if not is_slack_connected_for_client:
        logger.info(
            "Slack not connected for client %s, Skipping slack notification for: %s",
            employee.client_id,
            email_id,
        )
        return

    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting commission on slack config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))

    try:
        # get payroll info for the employee
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        payout_frequency = payroll.payout_frequency.lower()
        variable_pay = payroll.variable_pay
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        client_data = get_client(client_id)
        start_month = client_data.fiscal_start_month
        fiscal_year = get_fiscal_year(start_month)
        period = get_period_start_and_end_date(
            commission_date,
            client_data.fiscal_start_month,
            payout_frequency,
            client_id=client_id,
        )

        days_left_for_payout = period.get("end_date") - commission_date

        # Building UI layout for commissions

        ## Header
        header_block = []
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi {emp_name}! {Emoji.WAVE.value}",
                },
            }
        )
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Here's how you did last month ({(commission_date.strftime('%b %Y'))})!",
                },
            },
        )

        ## YTD payout block
        ytd_payout_block = []

        ytd_payout = 0
        ytd_percent = 0
        payouts_for_year = get_commission_payout_for_year_for_payee(
            client_id, fiscal_year, email_id
        )
        if payouts_for_year and payouts_for_year.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts_for_year.get("error"))

        for _, payout in payouts_for_year.items():
            if isinstance(payout, dict):
                ytd_payout += float(payout.get("commission_amount", 0))
            ytd_percent = (
                round((ytd_payout / float(variable_pay)) * 100, 2)
                if variable_pay
                else 0
            )
        if payout_frequency != Freq.MONTHLY.value:
            text = get_localized_message_service(
                SLACK_MESSAGES.PAYOUT_YTD_PERIOD_CLOSE.value, client_id
            ).format(
                currency_symbol=currency_symbol,
                ytd_payout="{:,}".format(round(ytd_payout, 2)),
                days_left_for_payout=days_left_for_payout.days,
            )
        else:
            text = get_localized_message_service(
                SLACK_MESSAGES.PAYOUT_YTD.value, client_id
            ).format(
                currency_symbol=currency_symbol,
                ytd_payout="{:,}".format(round(ytd_payout, 2)),
            )

        # payout YTD
        ytd_payout_block.append(
            build_section_block(
                text=text,
                text_type="mrkdwn",
            )
        )

        # payout percentage YTD
        ytd_payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT_YTD.value, client_id
                ).format(ytd_percent=f"{ytd_percent:.2f}"),
                text_type="mrkdwn",
            )
        )

        ## get payouts for the curr_period and create payout block
        payouts = get_payee_commission_payout_for_curr_period(client_id, email_id)
        if payouts and payouts.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts.get("error"))
            return

        curr_period = get_period_name_for_date(
            payout_frequency,
            period.get("start_date"),
            client_data.fiscal_start_month,
        )
        curr_payout = payouts.get(curr_period, {})
        commission_amount = curr_payout.get("commission_amount", 0)
        variable_pay = float(curr_payout.get("variable_pay", 0))
        payout_per = (
            (float(commission_amount) / float(variable_pay)) * 100
            if variable_pay
            else 0
        )

        payout_block = []
        # payout
        comm_amount = "{:,}".format(round(float(commission_amount), 2))
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_AMOUNT.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    comm_amount=comm_amount,
                ),
                text_type="mrkdwn",
            )
        )
        #  payout %
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT.value, client_id
                ).format(payout_percent=round(payout_per, 2)),
                text_type="mrkdwn",
            )
        )

        from spm.services.rbac_services import get_ui_permissions

        ## get commission data for the commission_date and create quota block
        commission_data = get_commission(client_id, email_id, commission_date, payroll)
        quota_and_rank = create_quota_category_map(client_id, email_id, commission_data)
        quota_map = quota_and_rank.get("quota_map")
        quota_localized = get_localized_message_service(
            SLACK_MESSAGES.QUOTA.value, client_id
        )
        has_reportees = commission_data.get("has_reportees")
        user_permissions = get_ui_permissions(client_id, email_id)
        has_quota_permission = "view:quotas" in user_permissions
        quota_blocks = (
            get_quota_block(
                quota_map, currency_symbol, has_reportees, client_id, quota_localized
            )
            if has_quota_permission
            else []
        )

        ## if payout period is other than monthly, show commission to get to attain the target in a period.
        target_block = []

        if payout_frequency != Freq.MONTHLY.value:
            period_label = get_current_period_label(
                commission_date, payout_frequency.lower()
            )
            target_block = get_target_block(
                client_id, email_id, period_label, fiscal_year, currency_symbol
            )

        # quota rank
        # rank_map = quota_and_rank.get("rank_map")
        # quota_rank_blocks = get_rank_block(rank_map, client_id)

        # footer
        footer_block = get_footer_block(payroll.payee_role)

        blocks = (
            header_block
            + ytd_payout_block
            + payout_block
            + quota_blocks
            + target_block
            + footer_block
        )
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )
        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )

        analytics_data = {
            "user_id": email_id,
            "event_name": SegmentEvents.SLACK_NOTIFICATION.value,
            "event_properties": {
                SegmentProperties.PAYEE_NAME.value: emp_name,
                SegmentProperties.PAYEE_EMP_ID.value: email_id,
                SegmentProperties.NOTIFICATION_TYPE.value: "Monthly Commission Notification",
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return blocks
    except Exception:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: EXCEPTION TB: POST COMMISSION MONTHLY traceback- %s",
            traceback.print_exc(),
        )
        client.chat_postMessage(
            text=get_localized_message_service(
                SLACK_MESSAGES.UNABLE_TO_GET_COMMISSION.value, client_id
            ),
            channel=user_id,
        )


@shared_task(base=EverCeleryBaseTask)
def post_commissions_frozen_for_payee_notification_on_slack(
    client_id: int, email_id: str, commission_date=None
):
    """

    Args:
        client_id: es_client
        email_id: slack user email
        commission_date: the date for which the commission period falls if None show curr period commission
        body:

    Returns:

    """
    set_threadlocal_context({"client_id": client_id})
    logger.info("Posting commission locked for the payee %s on slack", email_id)

    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "STATEMENTS_LOCKED_NOTIFICATION"
    )
    if employee is None:
        # Slack is disabled for the client
        logger.info(
            "Slack is disabled for the client - %s, skipping notification for: %s",
            client_id,
            email_id,
        )
        return

    if commission_date is None:
        commission_date = timezone.now() - datetime.timedelta(days=1)

    set_request_context(employee)

    is_slack_connected_for_client = is_slack_connected(employee.client_id)

    if not is_slack_connected_for_client:
        logger.info(
            "Slack not connected for client %s, Skipping slack notification for: %s",
            client_id,
            email_id,
        )
        return

    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting commission on slack config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))

    try:
        # get payroll info for the employee
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        payout_frequency = payroll.payout_frequency.lower()
        variable_pay = payroll.variable_pay
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        client_data = get_client(client_id)
        start_month = client_data.fiscal_start_month
        fiscal_year = get_fiscal_year(start_month)
        period = get_period_start_and_end_date(
            commission_date,
            client_data.fiscal_start_month,
            payout_frequency,
            client_id=client_id,
        )
        statement_url = get_statements_url_for_payee(
            email_id,
            period["start_date"].strftime("%Y-%m-%d"),
            period["end_date"].strftime("%Y-%m-%d"),
        )

        days_left_for_payout = period.get("end_date") - commission_date

        # Building UI layout for commissions

        ## Header
        header_block = []
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi {emp_name}! {Emoji.WAVE.value}",
                },
            }
        )
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Your statement for {(commission_date.strftime('%b %Y'))} has been locked {Emoji.LOCKED.value}",
                },
            },
        )

        ## YTD payout block
        ytd_payout_block = []

        ytd_payout = 0
        ytd_percent = 0
        payouts_for_year = get_commission_payout_for_year_for_payee(
            client_id, fiscal_year, email_id
        )
        if payouts_for_year and payouts_for_year.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts_for_year.get("error"))

        for _, payout in payouts_for_year.items():
            if isinstance(payout, dict):
                ytd_payout += float(payout.get("commission_amount", 0))
            ytd_percent = (
                round((ytd_payout / float(variable_pay)) * 100, 2)
                if variable_pay
                else 0
            )
        if payout_frequency != Freq.MONTHLY.value:
            text = get_localized_message_service(
                SLACK_MESSAGES.PAYOUT_YTD_PERIOD_CLOSE.value, client_id
            ).format(
                currency_symbol=currency_symbol,
                ytd_payout="{:,}".format(round(ytd_payout, 2)),
                days_left_for_payout=days_left_for_payout.days,
            )
        else:
            text = get_localized_message_service(
                SLACK_MESSAGES.PAYOUT_YTD.value, client_id
            ).format(
                currency_symbol=currency_symbol,
                ytd_payout="{:,}".format(round(ytd_payout, 2)),
            )

        # payout YTD
        ytd_payout_block.append(
            build_section_block(
                text=text,
                text_type="mrkdwn",
            )
        )

        # payout percentage YTD
        ytd_payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT_YTD.value, client_id
                ).format(ytd_percent=f"{ytd_percent:.2f}"),
                text_type="mrkdwn",
            )
        )

        ## get payouts for the curr_period and create payout block
        payouts = get_payee_commission_payout_for_curr_period(client_id, email_id)
        if payouts and payouts.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts.get("error"))
            return

        curr_period = get_period_name_for_date(
            payout_frequency,
            period.get("start_date"),
            client_data.fiscal_start_month,
        )
        curr_payout = payouts.get(curr_period, {})
        commission_amount = curr_payout.get("commission_amount", 0)
        variable_pay = float(curr_payout.get("variable_pay", 0))
        payout_per = (
            (float(commission_amount) / float(variable_pay)) * 100
            if variable_pay
            else 0
        )

        payout_block = []
        # payout
        comm_amount = "{:,}".format(round(float(commission_amount), 2))
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_AMOUNT.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    comm_amount=comm_amount,
                ),
                text_type="mrkdwn",
            )
        )
        #  payout %
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT.value, client_id
                ).format(payout_percent=round(payout_per, 2)),
                text_type="mrkdwn",
            )
        )

        from spm.services.rbac_services import get_ui_permissions

        ## get commission data for the commission_date and create quota block
        commission_data = get_commission(client_id, email_id, commission_date, payroll)
        quota_and_rank = create_quota_category_map(client_id, email_id, commission_data)
        quota_map = quota_and_rank.get("quota_map")
        quota_localized = get_localized_message_service(
            SLACK_MESSAGES.QUOTA.value, client_id
        )
        has_reportees = commission_data.get("has_reportees")
        user_permissions = get_ui_permissions(client_id, email_id)
        has_quota_permission = "view:quotas" in user_permissions
        quota_blocks = (
            get_quota_block(
                quota_map, currency_symbol, has_reportees, client_id, quota_localized
            )
            if has_quota_permission
            else []
        )

        ## if payout period is other than monthly, show commission to get to attain the target in a period.
        target_block = []

        if payout_frequency != Freq.MONTHLY.value:
            period_label = get_current_period_label(
                commission_date, payout_frequency.lower()
            )
            target_block = get_target_block(
                client_id, email_id, period_label, fiscal_year, currency_symbol
            )

        # quota rank
        # rank_map = quota_and_rank.get("rank_map")
        # quota_rank_blocks = get_rank_block(rank_map, client_id)

        # footer which redirects to statements page
        footer_block = get_footer_block(payroll.payee_role, True, statement_url)

        blocks = (
            header_block
            + ytd_payout_block
            + payout_block
            + quota_blocks
            + target_block
            + footer_block
        )
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )
        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )

        analytics_data = {
            "user_id": email_id,
            "event_name": SegmentEvents.SLACK_NOTIFICATION.value,
            "event_properties": {
                SegmentProperties.PAYEE_NAME.value: emp_name,
                SegmentProperties.PAYEE_EMP_ID.value: email_id,
                SegmentProperties.NOTIFICATION_TYPE.value: "Commission Locked Notification",
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return blocks
    except Exception:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: EXCEPTION TB: POST COMMISSION MONTHLY traceback- %s",
            traceback.print_exc(),
        )
        client.chat_postMessage(
            text=get_localized_message_service(
                SLACK_MESSAGES.UNABLE_TO_GET_COMMISSION.value, client_id
            ),
            channel=user_id,
        )


@shared_task(base=EverCeleryBaseTask)
def post_commissions_daily_or_weekly(client_id, email_id, commission_date=None):
    """

    Args:
        client_id: es_client
        email_id: slack user email
        commission_date: the date for which the commission period falls if None show curr period commission
        update: if True update the chat message
        body:

    Returns:

    """
    from spm.services.rbac_services import get_ui_permissions

    set_threadlocal_context({"client_id": client_id})
    logger.info("Posting commission for the payee %s", email_id)
    if commission_date is None:
        commission_date = timezone.now()

    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "COMMISSION_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping daily or weekly commissions notification on Slack for user %s",
            email_id,
        )
        return

    is_slack_connected_for_client = is_slack_connected(employee.client_id)

    if not is_slack_connected_for_client:
        logger.info(
            "Slack not connected for client %s, Skipping slack notification for: %s",
            employee.client_id,
            email_id,
        )
        return

    set_request_context(employee)
    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting commission on slack config not found for %s",
            email_id,
        )
        return

    # get permission info for the user
    user_permissions = get_ui_permissions(client_id, email_id)
    if RbacPermissions.VIEW_STATEMENTS.value not in user_permissions:
        logger.info(
            "Permissions not present - Skipping Slack Commission Notification for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))

    try:
        # get payroll info for the employee
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        payout_frequency = payroll.payout_frequency.lower()
        variable_pay = payroll.variable_pay
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        client_data = get_client(client_id)
        start_month = client_data.fiscal_start_month
        fiscal_year = get_fiscal_year(start_month)
        period = get_period_start_and_end_date(
            commission_date,
            client_data.fiscal_start_month,
            payout_frequency,
            client_id=client_id,
        )

        days_left_for_payout = period.get("end_date") - commission_date

        # Building UI layout for commissions

        ## Header
        header_block = []
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi {emp_name}! {Emoji.WAVE.value}",
                },
            }
        )
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Here's how you are doing for the current month ({(commission_date.strftime('%b %Y'))})",
                },
            }
        )

        ## get payouts for the curr_period and create payout block
        payouts = get_payee_commission_payout_for_curr_period(client_id, email_id)

        if payouts and payouts.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts.get("error"))
            return

        curr_period = get_period_name_for_date(
            payout_frequency,
            period.get("start_date"),
            client_data.fiscal_start_month,
        )
        curr_payout = payouts.get(curr_period, {})
        commission_amount = float(curr_payout.get("commission_amount", 0))
        variable_pay = float(curr_payout.get("variable_pay", 0))
        payout_per = (
            (float(commission_amount) / float(variable_pay)) * 100
            if variable_pay
            else 0
        )
        payout_block = []
        # payout
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.DAYS_FOR_PERIOD_CLOSE.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    commission_amount="{:,}".format(round(commission_amount, 2)),
                    days_left=days_left_for_payout.days,
                ),
                text_type="mrkdwn",
            )
        )

        #  payout %
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT.value, client_id
                ).format(payout_percent=round(payout_per, 2)),
                text_type="mrkdwn",
            )
        )

        ## get commission data for the commission_date and create quota block
        commission_data = get_commission(client_id, email_id, commission_date, payroll)
        quota_and_rank = create_quota_category_map(client_id, email_id, commission_data)
        quota_map = quota_and_rank.get("quota_map")
        quota_localized = get_localized_message_service(
            SLACK_MESSAGES.QUOTA.value, client_id
        )
        has_reportees = commission_data.get("has_reportees")
        has_quota_permission = "view:quotas" in user_permissions
        quota_blocks = (
            get_quota_block(
                quota_map, currency_symbol, has_reportees, client_id, quota_localized
            )
            if has_quota_permission
            else []
        )

        ## target block
        period_label = get_current_period_label(
            commission_date, payout_frequency.lower()
        )
        target_block = get_target_block(
            client_id, email_id, period_label, fiscal_year, currency_symbol
        )

        # quota rank
        # rank_map = quota_and_rank.get("rank_map")
        # quota_rank_blocks = get_rank_block(rank_map, client_id)

        # footer
        footer_block = get_footer_block(payroll.payee_role)

        blocks = (
            header_block + payout_block + quota_blocks + target_block + footer_block
        )
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )
        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )

        analytics_data = {
            "user_id": email_id,
            "event_name": SegmentEvents.SLACK_NOTIFICATION.value,
            "event_properties": {
                SegmentProperties.PAYEE_NAME.value: emp_name,
                SegmentProperties.PAYEE_EMP_ID.value: email_id,
                SegmentProperties.NOTIFICATION_TYPE.value: "Daily/Weekly Commission Notification",
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return blocks
    except Exception:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: EXCEPTION TB: POST COMMISSION WEEKLY or DAILY traceback- %s",
            traceback.print_exc(),
        )
        client.chat_postMessage(
            channel=str(user_id),
            text=get_localized_message_service(
                SLACK_MESSAGES.UNABLE_TO_GET_COMMISSION.value, client_id
            ),
        )


@shared_task(base=EverCeleryBaseTask)
def post_commission_reminder(
    client_id, email_id, commission_date=None, before_end_date=False
):
    """
    Args:
        client_id: es_client
        email_id: slack user email
        commission_date: the date for which the commission period falls if None show curr period commission
        before_end_date:
    Returns:

    """
    set_threadlocal_context({"client_id": client_id})
    logger.info("Posting commission Reminder for the payee %s", email_id)
    if commission_date is None:
        commission_date = timezone.now() - datetime.timedelta(days=1)

    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "COMMISSION_REMINDER_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping commission reminder notification on Slack for user %s",
            email_id,
        )
        return

    is_slack_connected_for_client = is_slack_connected(employee.client_id)

    if not is_slack_connected_for_client:
        logger.info(
            "Slack not connected for client %s, Skipping slack notification for: %s",
            employee.client_id,
            email_id,
        )
        return

    set_request_context(employee)
    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting commission reminder on slack config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))
    try:
        # get payroll info for the employee
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        payout_frequency = payroll.payout_frequency.lower()
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        client_data = get_client(client_id)
        period = get_period_start_and_end_date(
            commission_date,
            client_data.fiscal_start_month,
            payout_frequency,
            prev_required=True,
            client_id=client_id,
        )
        client_features = get_client_features(client_id)
        freeze_date = client_features["freeze_date"]
        ## Header
        header_block = []
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi {emp_name}! {Emoji.WAVE.value}",
                },
            }
        )
        if before_end_date:
            header_block.append(
                {
                    "type": "section",
                    "text": {"type": "mrkdwn", "text": "Final Reminder."},
                }
            )
        header_block.append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": get_localized_message_service(
                        SLACK_MESSAGES.LAST_MONTH_COMM_CALC.value, client_id
                    ).format(freeze_date=freeze_date)
                    + "Please review and get your queries sorted before that.",
                },
            },
        )

        ## get payouts for the curr_period and create payout block
        payouts = get_payee_commission_payout_for_curr_period(client_id, email_id)

        if payouts and payouts.get("error"):
            logger.error("APP_INT_EXCEPTION: %s", payouts.get("error"))
            return

        curr_period = get_period_name_for_date(
            payout_frequency,
            period.get("prev_start_date"),
            client_data.fiscal_start_month,
        )
        curr_payout = payouts.get(curr_period, {})
        commission_amount = float(curr_payout.get("commission_amount", 0))

        payout_block = []
        # payout
        payout_block.append(
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_AMOUNT.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    comm_amount="{:,}".format(round(commission_amount, 2)),
                ),
                text_type="mrkdwn",
            )
        )

        # footer todo: show only statements button
        footer_block = get_footer_block(payroll.payee_role)

        blocks = header_block + payout_block + footer_block
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )

        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )

        analytics_data = {
            "user_id": email_id,
            "event_name": SegmentEvents.SLACK_NOTIFICATION.value,
            "event_properties": {
                SegmentProperties.PAYEE_NAME.value: emp_name,
                SegmentProperties.PAYEE_EMP_ID.value: email_id,
                SegmentProperties.NOTIFICATION_TYPE.value: "Commission Reminder Notification",
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return blocks

    except Exception as o:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", o)


def post_payment_initiated_notification(client_id, email_id, payout):
    """
    Args:
        client_id: es_client
        email_id: slack user email
        payout: The payout details of the payee with email_id
    Returns:

    """

    logger.info("Posting Payment Initiated Notification for the payee %s", email_id)
    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "PAYOUT_INITIATED_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping payout initiated notification on Slack for user %s",
            email_id,
        )
        return

    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )

    if config_record is None:
        logger.info(
            "Error posting commission reminder on slack config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")

    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))
    try:
        commission_date = timezone.now()
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        blocks = [
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.CURRENT_MONTH_PAYOUT_INITIATED.value, client_id
                ).format(
                    emoji=Emoji.WAVE.value,
                    emp_name=emp_name,
                ),
                text_type="mrkdwn",
            ),
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_AMOUNT.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    comm_amount="{:,}".format(round(payout["paid_amount"], 2)),
                ),
                text_type="mrkdwn",
            ),
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "emoji": True,
                            "text": "My statements",
                        },
                        "value": "my_statements",
                        "action_id": "my_statements",
                        "url": f"{URL}/statements",
                    }
                ],
            },
        ]
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )
        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )
        return blocks
    except Exception as e:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", e)


def post_commission_attained_notification(client_id, payee_commission_details):
    """
    Args:
        client_id: es_client
        payee_commission_details: The details of a payee's commission attained
    Returns:

    """

    email_id = payee_commission_details["payee_email_id"]
    logger.info(
        "Posting Commission attained notification for payee email_id=%s payee_commission_details=%s",
        email_id,
        payee_commission_details,
    )
    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "COMMISSION_MILESTONE_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping commission attained notification on Slack for user %s",
            email_id,
        )
        return

    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting commission attained notification: config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))
    try:
        commission_date = timezone.now()
        payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
            commission_date, email_id, as_dicts=False
        )
        if payroll is None or len(payroll) == 0:
            logger.error("APP_INT_EXCEPTION: Payroll data not found for %s", email_id)
            return
        else:
            payroll = payroll[0]

        pay_currency = payroll.pay_currency
        countries = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
        currency_symbol = countries.currency_symbol
        commission_percentage_string = payee_commission_details[
            "commission_percentage"
        ][:-1]
        commission_percentage = (
            0
            if commission_percentage_string == "-"
            else float(payee_commission_details["commission_percentage"][:-1])
        )
        if commission_percentage < 25:
            logger.info(
                "Skipping Commission attained notification for payee %s with commission percentage %s",
                email_id,
                commission_percentage,
            )
            return

        milestone = 0

        if 25 <= commission_percentage < 50:
            payout_message = (
                f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou have warmed up nicely."
            )
            payout_indicator = (
                ":large_blue_circle: :white_circle: :white_circle: :white_circle:"
            )
            milestone = 25
        elif 50 <= commission_percentage < 75:
            payout_message = f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou're half way there! March on."
            payout_indicator = (
                ":large_blue_circle: :large_blue_circle: :white_circle: :white_circle:"
            )
            milestone = 50
        elif 75 <= commission_percentage < 100:
            payout_message = f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou're close to completion! March on."
            payout_indicator = ":large_blue_circle: :large_blue_circle: :large_blue_circle: :white_circle:"
            milestone = 75
        else:
            payout_message = f"Woohooo! You're killing it {emp_name}. Time to maximize!"
            payout_indicator = ":large_blue_circle: :large_blue_circle: :large_blue_circle: :large_blue_circle:"
            milestone = 100

        end_date = datetime.datetime.strptime(
            payee_commission_details["period_end_date"], "%Y-%m-%d"
        )
        time_period = (
            f"{get_current_period_label(end_date, payroll.payout_frequency.lower())}"
        )

        from commission_engine.utils.general_data import Task

        is_milestone_attainment_already_notified = MilestoneNotificationStatusAccessor(
            client_id
        ).is_commission_attainment_notified_for_milestone(
            email_id,
            Task.SLACK_COMMISSION_ATTAINED_NOTIFICATION.name,
            time_period,
            milestone,
        )

        if is_milestone_attainment_already_notified:
            logger.info(
                "Skipping Commission attained notification for payee %s with milestone %s for %s as its already notified",
                email_id,
                milestone,
                time_period,
            )
            return
        blocks = [
            build_section_block(
                text=f"{payout_message}",
                text_type="mrkdwn",
            ),
            build_section_block(
                text=f"*{time_period}*",
                text_type="mrkdwn",
            ),
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_AMOUNT.value, client_id
                ).format(
                    emoji=Emoji.MONEYBAG.value,
                    currency_symbol=currency_symbol,
                    comm_amount="{:,}".format(
                        round(
                            float(payee_commission_details["calculated_commission"]), 2
                        )
                    ),
                ),
                text_type="mrkdwn",
            ),
            build_section_block(
                text=get_localized_message_service(
                    SLACK_MESSAGES.PAYOUT_PERCENT.value, client_id
                ).format(
                    payout_percent=payee_commission_details["commission_percentage"]
                ),
                text_type="mrkdwn",
            ),
            build_section_block(
                text=payout_indicator,
                text_type="mrkdwn",
            ),
        ]

        if commission_percentage >= 100:
            blocks.append(
                build_image_block(
                    image_url="https://everstage-public-assets.s3.us-west-1.amazonaws.com/Slack/congratulations.gif",
                    alt_text="Congratulations",
                )
            )
        blocks.append(
            build_action_block(
                elements=[
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "emoji": True,
                            "text": "My statements",
                        },
                        "value": "my_statements",
                        "action_id": "my_statements",
                        "url": f"{URL}/statements",
                    }
                ]
            )
        )
        from spm.services.commission_actions_service.commission_slack_services import (
            persist_commission_attainment_notification_details,
        )

        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        )

        client.chat_postMessage(
            text=commissions_localized,
            channel=user_id,
            as_user=True,
            blocks=blocks,
        )
        persist_commission_attainment_notification_details(
            client_id,
            email_id,
            Task.SLACK_COMMISSION_ATTAINED_NOTIFICATION.name,
            time_period,
            milestone,
            logger,
        )
        return blocks

    except Exception as e:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", e)


def post_quota_milestone_notification(
    client_id,
    email_id,
    emp_name,
    payroll,
    category,
    category_details,
    has_reportees,
    key,
    client,
    user_id,
    quota_currency_type,
):
    from commission_engine.utils.general_data import Task
    from spm.services.commission_actions_service.commission_slack_services import (
        persist_quota_attainment_notification_details,
    )

    blocks = []
    quota_attainment = category_details[key]["quota_attainment"]
    quota_erosion = category_details[key]["quota_erosion"]
    quota_value = category_details[key]["quota_value"]
    quota_time_period = f"{get_current_period_label(category_details[key]['ped'], payroll.payout_frequency.lower())}"
    additional_details = {
        "quota_category": category,
        "quota_type": key,
    }

    if quota_attainment < 25:
        logger.info(
            "Skipping Quota attained notification for payee %s with quota category: %s - %s, percentage: %s%%",
            email_id,
            category,
            key,
            quota_attainment,
        )
        return

    milestone = 0
    if 25 <= quota_attainment < 50:
        payout_message = (
            f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou have warmed up nicely."
        )
        milestone = 25
    elif 50 <= quota_attainment < 75:
        payout_message = (
            f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou're half way there! March on."
        )
        milestone = 50
    elif 75 <= quota_attainment < 100:
        payout_message = f"Hey {emp_name}! {Emoji.WAVE.value}\n\nYou're close to completion! March on."
        milestone = 75
    else:
        payout_message = get_localized_message_service(
            SLACK_MESSAGES.ACHIEVED_QUOTA.value, client_id
        ).format(emoji=Emoji.CONFETTIBALL.value, emp_name=emp_name)
        milestone = 100

    is_milestone_attainment_already_notified = MilestoneNotificationStatusAccessor(
        client_id
    ).is_quota_attainment_notified_for_milestone(
        email_id,
        Task.SLACK_QUOTA_ATTAINED_NOTIFICATION.name,
        additional_details,
        quota_time_period,
        milestone,
    )

    if is_milestone_attainment_already_notified:
        logger.info(
            "Skipping Quota attained notification for payee %s with quota category: %s - %s, milestone: %s as its already notified",
            email_id,
            category,
            key,
            milestone,
        )
        return

    quota_type = (
        ""
        if not has_reportees
        else "`As Individual:`\n\n" if key == "as_individual" else "`As Manager:`\n\n"
    )
    blocks.append(
        build_section_block(
            text=f"{payout_message}",
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=get_localized_message_service(
                SLACK_MESSAGES.QUOTA_CATEGORY.value, client_id
            ).format(category=category, quota_type=quota_type),
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=get_localized_message_service(
                SLACK_MESSAGES.QUOTA_PERIOD.value, client_id
            ).format(quota_time_period=quota_time_period),
            text_type="mrkdwn",
        )
    )
    blocks.append(
        build_section_block(
            text=get_localized_message_service(
                SLACK_MESSAGES.QUOTA_ATTAINMENT.value, client_id
            ).format(
                emoji=Emoji.DART.value,
                quota_attainment=round(quota_attainment, 2),
            ),
            text_type="mrkdwn",
        )
    )
    # Conditionally include quota_currency_type
    quota_value_text = (
        f"{quota_currency_type} {'{:,}'.format(round(quota_value, 2))}"
        if quota_currency_type
        else f"{'{:,}'.format(round(quota_value, 2))}"
    )
    blocks.append(
        build_section_block(
            text=get_localized_message_service(
                SLACK_MESSAGES.QUOTA_VALUE.value, client_id
            ).format(quota_value=quota_value_text),
            text_type="mrkdwn",
        )
    )
    quota_erosion_text = (
        f"{quota_currency_type} {'{:,}'.format(round(quota_erosion, 2))}"
        if quota_currency_type
        else f"{'{:,}'.format(round(quota_erosion, 2))}"
    )
    blocks.append(
        build_section_block(
            text=f"*Actuals:*  {quota_erosion_text}",
            text_type="mrkdwn",
        )
    )

    if quota_attainment >= 100:
        blocks.append(
            build_image_block(
                image_url="https://everstage-public-assets.s3.us-west-1.amazonaws.com/Slack/congratulations-quota.gif",
                alt_text="Congratulations",
            )
        )
    blocks.append(
        build_action_block(
            elements=[
                {
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "emoji": True,
                        "text": "My statements",
                    },
                    "value": "my_statements",
                    "action_id": "my_statements",
                    "url": f"{URL}/statements",
                }
            ]
        )
    )

    quota_localized = get_localized_message_service(
        SLACK_MESSAGES.QUOTA.value, client_id
    )

    client.chat_postMessage(
        text=quota_localized,
        channel=user_id,
        as_user=True,
        blocks=blocks,
    )

    persist_quota_attainment_notification_details(
        client_id,
        email_id,
        Task.SLACK_QUOTA_ATTAINED_NOTIFICATION.name,
        additional_details,
        quota_time_period,
        milestone,
        logger,
    )

    return blocks


def post_quota_attained_notification(client_id, email_id, commission_date, payroll):
    """
    Args:
        client_id: es_client
        email_id: Payee email id
    Returns:

    """

    quota_map = get_quota_map(client_id, commission_date, email_id, payroll)

    _blocks = []
    logger.info(
        "Posting Quota attained notification for payee email_id=%s quota_map=%s",
        email_id,
        quota_map,
    )
    # get user info
    employee = get_employee_in_slack_from_email_id(
        client_id, email_id, "QUOTA_MILESTONE_NOTIFICATION"
    )

    if employee is None:
        logger.info(
            "Skipping quota attained notification on Slack for user %s",
            email_id,
        )
        return

    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email_id
    )
    if config_record is None:
        logger.info(
            "Error posting quota attained notification on slack, config not found for %s",
            email_id,
        )
        return

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")
    emp_name = f"{employee.first_name}"
    has_reportees = len(
        HierarchyAccessor(client_id).get_reportees(commission_date, email_id)
    )

    # create slack api client
    tokens = get_valid_token(user_id, team_id)
    client = WebClient(token=tokens.get("bot_token"))

    if not len(quota_map.items()):
        return
    try:
        quota_categories = list(quota_map.keys())
        qs = QuotaAccessor(client_id).get_all_quota_categories_given_names(
            quota_category_names=quota_categories,
            projection=["quota_category_name", "display_name", "quota_currency_type"],
        )
        country_currency_map = CountriesAccessor(
            client_id
        ).get_currency_code_and_symbol_map()
        display_name_map = {
            record["quota_category_name"]: {
                "display_name": record["display_name"],
                "quota_currency_type": record["quota_currency_type"],
            }
            for record in qs
        }
        for category in quota_categories:
            if category not in display_name_map:
                display_name_map[category] = {
                    "display_name": None,
                    "quota_currency_type": None,
                }

        if "Primary" in quota_categories:
            display_name_map["Primary"][
                "display_name"
            ] = f"Primary {get_localized_message_service(SLACK_MESSAGES.QUOTA.value, client_id)}"

        for category, category_details in quota_map.items():
            display_info = display_name_map.get(category, {})
            display_name = display_info.get("display_name")
            quota_currency_type = display_info.get("quota_currency_type")
            if category_details.get("as_individual"):
                post_quota_milestone_notification(
                    client_id,
                    email_id,
                    emp_name,
                    payroll,
                    display_name,
                    category_details,
                    has_reportees,
                    "as_individual",
                    client,
                    user_id,
                    country_currency_map.get(quota_currency_type),
                )

            if has_reportees and category_details.get("as_manager"):
                post_quota_milestone_notification(
                    client_id,
                    email_id,
                    emp_name,
                    payroll,
                    display_name,
                    category_details,
                    has_reportees,
                    "as_manager",
                    client,
                    user_id,
                    country_currency_map.get(quota_currency_type),
                )

    except Exception as e:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", e)


def get_target_block(client_id, email_id, period_label, _fiscal_year, currency_symbol):
    stats_block = []
    commission_stats = get_commission_buddy_data(client_id, email_id)
    target = float(commission_stats.get("target"))
    payouts_localized = get_localized_message_service(
        SLACK_MESSAGES.PAYOUTS_LC.value, client_id
    )

    stats_block.append(get_new_line_section())
    stats_block.append(
        build_section_block(
            text=f"You need to be at {currency_symbol}{'{:,}'.format(round(target, 2))} {Emoji.CHART_WITH_UPWARDS_TREND.value} to "
            f"achieve 100% {payouts_localized} for {period_label}",
            text_type="mrkdwn",
        )
    )

    return stats_block


# Unused as quota rank is inconsistent - https://interstage.atlassian.net/browse/INTER-7794
def get_rank_block(quota_category_map, client_id):
    blocks = []

    quota_categories = list(quota_category_map.keys())
    qs = QuotaAccessor(client_id).get_quota_category_display_name_map(quota_categories)
    display_name_map = {
        record["quota_category_name"]: record["display_name"] for record in qs
    }
    if "Primary" in quota_categories:
        display_name_map["Primary"] = (
            f"Primary {get_localized_message_service(SLACK_MESSAGES.QUOTA.value, client_id)}"
        )

    for quota_category, quota_det in quota_category_map.items():
        if quota_det.get("rank") and quota_det.get("rank") != -1:
            blocks.append(
                build_section_block(
                    text=f"*{display_name_map.get(quota_category)}:* You are ranked at  {quota_det.get('rank')}/{quota_det.get('total_members')} {Emoji.PAGE.value}  among your peers",
                    text_type="mrkdwn",
                )
            )

    return blocks


def update_quota_category_map(quota_category_map, quota_category, key):
    quota_det = quota_category_map.get(quota_category).get(key)
    quota_attainment = (
        (
            (quota_det.get("cumulative_qe") + quota_det.get("quota_erosion"))
            / quota_det.get("quota_value")
            * 100
        )
        if quota_det.get("quota_value")
        else 0
    )
    quota_category_map[quota_category][key].update(
        {
            "quota_attainment": round(quota_attainment, 2),
        }
    )


def create_quota_category_map(client_id, email_id, commission_data):
    quota_category_map = {}
    quota_rank_map = {}
    plan_data = commission_data.get("plan_data")
    has_reportees = commission_data.get("has_reportees")
    quota_schedule = commission_data.get("quota_schedule")
    quota_categories = quota_schedule.keys()
    hidden_categories = get_client_hidden_categories(client_id)
    logger.info("Hidden quotas categories for client are: %s", hidden_categories)
    available_quota_categories = set(quota_categories) - set(hidden_categories)
    for quota_category in available_quota_categories:
        quota_category_map[quota_category] = {
            "as_manager": None,
            "as_individual": None,
        }
    logger.info(
        "Available quotas categories for client are: %s", available_quota_categories
    )
    for _plan_id, plan_object in plan_data.items():
        quota_data = plan_object.get("quota_data")
        if quota_data:
            for quota in quota_data:
                quota_category_name = quota.get("quota_category_name")
                if quota_category_name in available_quota_categories:
                    key = "as_manager" if quota.get("is_team") else "as_individual"
                    if quota_category_map[quota_category_name][key] is None:
                        quota_category_map[quota_category_name][key] = {
                            "quota_value": quota.get("qv"),
                            "quota_erosion": quota.get("quota_erosion"),
                            "cumulative_qe": quota.get("cumulative_qe"),
                            "ped": quota.get("period_end_date"),
                        }
                    else:
                        quota_object = quota_category_map[quota_category_name][key]
                        quota_object["quota_erosion"] = quota_object.get(
                            "quota_erosion"
                        ) + quota.get("quota_erosion")

                    if not has_reportees or quota.get(
                        "is_team"
                    ):  # if payee is manager, consider his team quota for ranking
                        if quota_rank_map.get(quota_category_name) is None:
                            quota_rank_map[quota_category_name] = {
                                "quota_value": quota.get("qv"),
                                "quota_erosion": quota.get("quota_erosion"),
                                "cumulative_qe": quota.get("cumulative_qe"),
                                "ped": quota.get("period_end_date"),
                            }
                        else:
                            quota_object = quota_rank_map.get(quota_category_name)
                            quota_object["quota_erosion"] = quota_object.get(
                                "quota_erosion"
                            ) + quota.get("quota_erosion")
                else:
                    logger.info(
                        "Skipping quota category %s for payee: %s as the quota category is not unavailable for payee.",
                        quota_category_name,
                        email_id,
                    )

            quota_categories = quota_category_map.keys()
            for quota_category in quota_category_map.keys():
                if quota_category_map.get(quota_category).get("as_individual"):
                    update_quota_category_map(
                        quota_category_map, quota_category, "as_individual"
                    )
                if quota_category_map.get(quota_category).get("as_manager"):
                    update_quota_category_map(
                        quota_category_map, quota_category, "as_manager"
                    )

            for quota_category in quota_rank_map.keys():
                quota_det = quota_rank_map.get(quota_category)
                quota_attainment = (
                    (
                        (
                            quota_det.get("cumulative_qe")
                            + quota_det.get("quota_erosion")
                        )
                        / quota_det.get("quota_value")
                        * 100
                    )
                    if quota_det.get("quota_value")
                    else 0
                )
                quota_sch = quota_schedule.get(quota_category)
                quota_rank_map[quota_category].update(
                    {
                        "quota_attainment": round(quota_attainment, 2),
                    }
                )
                rank_data = calculate_payee_quota_rank(
                    client_id,
                    email_id,
                    quota_attainment,
                    quota_det.get("ped"),
                    quota_sch,
                    quota_category,
                )
                total_members = len(rank_data.get("desc_ordered_qa_list"))
                quota_rank_map[quota_category].update(
                    {
                        "rank": rank_data.get("rank"),
                        "total_members": total_members,
                    }
                )

    return {"rank_map": quota_rank_map, "quota_map": quota_category_map}


def get_footer_block(payee_role, is_statement=False, statement_url="statements"):
    url = get_site_url()
    dashboard_button = {
        "type": "button",
        "text": {"type": "plain_text", "text": "My dashboard", "emoji": True},
        "value": "click_me_123",
        "action_id": "dashboard",
        "url": f"{url}/dashboards",
    }

    what_if_button = {
        "type": "button",
        "text": {
            "type": "plain_text",
            "text": "Crystal",
            "emoji": True,
        },
        "value": "click_me_123",
        "action_id": "what_if",
        "url": f"{url}/crystal",
    }
    statement_button = {
        "type": "button",
        "text": {"type": "plain_text", "text": "My Statement", "emoji": True},
        "value": "click_me_123",
        "action_id": "statements",
        "url": f"{url}/{statement_url}",
    }
    footer_elements = [dashboard_button]
    if payee_role == "Revenue":
        footer_elements.append(what_if_button)

    if is_statement:
        return [{"type": "actions", "elements": [statement_button]}]

    return [{"type": "actions", "elements": footer_elements}]


@app.action("dashboard")
def handle_dashboard(ack):
    ack()


@app.action("my_statements")
def handle_statements(ack):
    ack()


@app.action("what_if")
def handle_what_if(ack):
    ack()


@app.action("view_approvals")
def handle_view_approvals(ack, body):
    ack()
    logger.info("Running view approvals action")

    actions = body.get("actions")
    reject_workflow_action = {}
    for action in actions:
        if action.get("action_id") == "view_approvals":
            reject_workflow_action = action
            break

    if reject_workflow_action is None:
        raise Exception("View approvals action arguments not found")

    action_value = json.loads(reject_workflow_action.get("value"))
    payee_name = action_value.get("payee_name")
    email_id = action_value.get("email_id")

    analytics_data = {
        "user_id": email_id,
        "event_name": SegmentEvents.SLACK_COMMANDS.value,
        "event_properties": {
            SegmentProperties.PAYEE_NAME.value: payee_name,
            SegmentProperties.PAYEE_EMP_ID.value: email_id,
            SegmentProperties.CMD_TYPE.value: "Commissions",
            SegmentProperties.QUERY_ACTION.value: "My Statements",
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)


@app.action("view_approval_statements")
def handle_view_approval_statements(ack, body):
    ack()
    logger.info("View approval statements action")

    actions = body.get("actions")
    reject_workflow_action = {}
    for action in actions:
        if action.get("action_id") == "view_approval_statements":
            reject_workflow_action = action
            break

    if reject_workflow_action is None:
        raise Exception("view statements action arguments not found")

    action_value = json.loads(reject_workflow_action.get("value"))
    payee_name = action_value.get("payee_name")
    email_id = action_value.get("email_id")

    analytics_data = {
        "user_id": email_id,
        "event_name": SegmentEvents.SLACK_COMMANDS.value,
        "event_properties": {
            SegmentProperties.PAYEE_NAME.value: payee_name,
            SegmentProperties.PAYEE_EMP_ID.value: email_id,
            SegmentProperties.CMD_TYPE.value: "Commissions",
            SegmentProperties.QUERY_ACTION.value: "My Statements",
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)


@app.action("approve_workflow")
def handle_approve_workflow(ack, body, client):
    """
    Handles approve action from slack notification
    """
    ack()
    logger.info("Running approve workflow action")

    actions = body.get("actions")
    approve_workflow_action = {}
    for action in actions:
        if action.get("action_id") == "approve_workflow":
            approve_workflow_action = action
            break

    if approve_workflow_action is None:
        raise Exception("approve workflow action arguments not found")

    action_value = json.loads(approve_workflow_action.get("value"))
    payee_name = action_value.get("payee_name")
    request_id = action_value.get("request_id")
    client_id = action_value.get("client_id")
    period = action_value.get("period")
    statement_url = action_value.get("statement_url")

    request_accessor = ApprovalRequestsAccessor(client_id)
    request = request_accessor.get_request_by_id(request_id)
    status = request.status
    container = body.get("container")

    if status != "requested":
        client.chat_update(
            channel=container.get("channel_id"),
            ts=container.get("message_ts"),
            text=f"Thank you for the response. This request has already been {status}.",
            blocks=[],
        )
    else:
        audit = {"updated_by": payee_name}
        if request.entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            approve_request(client_id, [request_id], audit, logger)
        elif request.entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
            approve_request_line_items(client_id, [request_id], audit=audit)
        client.chat_update(
            channel=container.get("channel_id"),
            ts=container.get("message_ts"),
            text=f"Thank you for the response. You've approved {payee_name}'s payout for <{statement_url}|{period}>.",
            blocks=[],
        )


@app.action("reject_workflow")
def handle_reject_workflow(ack, body, client):
    """
    Sends rejection pop up modal to slack
    """
    ack()

    logger.info("Running reject workflow action")

    actions = body.get("actions")
    reject_workflow_action = {}
    for action in actions:
        if action.get("action_id") == "reject_workflow":
            reject_workflow_action = action
            break

    if reject_workflow_action is None:
        raise Exception("reject workflow action arguments not found")

    action_value = json.loads(reject_workflow_action.get("value"))
    action_value["container"] = body.get("container")
    request_id = action_value.get("request_id")
    client_id = action_value.get("client_id")
    container = action_value["container"]

    request_accessor = ApprovalRequestsAccessor(client_id)
    request = request_accessor.get_request_by_id(request_id)
    status = request.status

    if status != "requested":
        client.chat_update(
            channel=container.get("channel_id"),
            ts=container.get("message_ts"),
            text=f"Thank you for the response. This request has already been {status}.",
            blocks=[],
        )

    else:
        # Pop up modal
        client.views_open(
            trigger_id=body["trigger_id"],
            view={
                "title": {
                    "type": "plain_text",
                    "text": "Reject this request",
                },
                "submit": {"type": "plain_text", "text": "Submit"},
                "private_metadata": json.dumps(action_value),
                "blocks": [
                    {
                        "type": "input",
                        "element": {
                            "type": "plain_text_input",
                            "multiline": True,
                            "action_id": "plain_text_input-action",
                            "max_length": 500,
                        },
                        "label": {
                            "type": "plain_text",
                            "text": "Comments",
                            "emoji": True,
                        },
                    }
                ],
                "type": "modal",
            },
        )


@app.view("")
def handle_view_events(ack, client, body):
    """
    Rejects approval request from slack notification
    """
    ack()

    try:
        view = body.get("view")
        state = view.get("state")
        block = view.get("blocks")[0]
        block_id = block.get("block_id")
        values = state.get("values")
        block_data = values.get(block_id)
        plain_text_input = block_data.get("plain_text_input-action")
        msg = plain_text_input.get("value")

        reject_workflow_action = view.get("private_metadata")
        action_value = json.loads(reject_workflow_action)
        payee_name = action_value.get("payee_name")
        request_id = action_value.get("request_id")
        client_id = action_value.get("client_id")
        period = action_value.get("period")
        statement_url = action_value.get("statement_url")

        audit = {"updated_by": payee_name}
        request_data = {"request_id": [request_id], "comments": msg}

        all_requests = ApprovalRequestsAccessor(
            client_id
        ).get_bulk_request_by_ids_queryset([request_id])
        approval_request = all_requests[0]
        if approval_request.entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            reject_request(client_id, request_data, audit, logger)
        elif (
            approval_request.entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value
        ):
            reject_request_line_items(client_id, [request_id], msg, audit)

        container = action_value.get("container")

        client.chat_update(
            channel=container.get("channel_id"),
            ts=container.get("message_ts"),
            text=f"Thank you for the response. You've rejected {payee_name}'s payout for <{statement_url}|{period}>.",
            blocks=[],
        )

    except Exception as o:  # pylint: disable=broad-except
        logger.error("APP_INT_EXCEPTION: %s", o)
