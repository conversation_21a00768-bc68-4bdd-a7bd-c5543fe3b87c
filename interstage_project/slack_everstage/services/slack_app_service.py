# pylint: disable=redefined-outer-name
# pylint: disable=unused-argument
# pylint: disable=redefined-builtin

import logging
import os
from datetime import datetime

from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from rest_framework.response import Response
from slack_bolt import App
from slack_bolt.adapter.django import <PERSON>lack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from slack_bolt.error import BoltError
from slack_sdk import WebClient
from slack_sdk.errors import SlackA<PERSON><PERSON><PERSON>r, SlackTokenRotationError
from slack_sdk.oauth.installation_store import Installation
from slack_sdk.oauth.token_rotation import TokenRotator

from commission_engine.accessors.client_accessor import is_slack_connected
from commission_engine.utils.general_data import SLACK_MESSAGES
from slack_everstage.accessors.slack_client_details_accessor import (
    SlackClientDetailsAccessor,
)
from slack_everstage.accessors.slack_installation_accessor import SlackAccessor
from slack_everstage.services.custom_oauth_settings import OAuthSettings
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.services.config_services.slack_config_services import (
    get_employee_in_slack,
    update_employee_config,
)
from spm.services.localization_services import get_localized_message_service

from ..utils.slack_utils import get_slash_commands_info, set_request_context
from .slack_datastores import DjangoInstallationStore, DjangoOAuthStateStore

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
SLACK_CLIENT_ID, SLACK_CLIENT_SECRET, signing_secret = (
    os.environ["SLACK_CLIENT_ID"],
    os.environ["SLACK_CLIENT_SECRET"],
    os.environ["SLACK_SIGNING_SECRET"],
)

# def success(args: SuccessArgs) -> BoltResponse:
#     installation = args.installation
#     client = args.request.context.client
#     try:
#         client.chat_postMessage(
#             token=installation.bot_token, # Use the token you just got from oauth.v2.access API response
#             channel=installation.user_id,  # Only with chat.postMessage API, you can use user_id here
#             text="Thanks for installing this app!"
#         )
#         return args.default.success(args)
#     except SlackApiError as e:
#         pass

app = App(
    signing_secret=signing_secret,
    process_before_response=True,
    oauth_settings=OAuthSettings(
        client_id=SLACK_CLIENT_ID,
        client_secret=SLACK_CLIENT_SECRET,
        install_page_rendering_enabled=False,
        # minutes_before_expiration (by default token expires every 12 hrs, so here for every req new token generated)
        # token_rotation_expiration_minutes=720,
        # files:write is to upload a file under a message
        scopes=[
            "commands",
            "chat:write",
            "users:read",
            "users:read.email",
            "files:write",
            "channels:read",
            "groups:read",
        ],
        installation_store=DjangoInstallationStore(
            client_id=SLACK_CLIENT_ID,
            logger=logger,
        ),
        state_store=DjangoOAuthStateStore(
            expiration_seconds=120,
            logger=logger,
        ),
    ),
)

handler = SlackRequestHandler(app=app)

logger = logging.getLogger(__name__)


# Slack global middleware
@app.use
# pylint: disable=redefined-outer-name
# pylint: disable=unused-argument
# pylint: disable=redefined-builtin
def global_slack_middleware(client, context, logger, payload, next):
    user_id = context.get("user_id")
    team_id = context.get("team_id")
    integration_config_record = None
    config_record = (
        IntegrationConfigAccessor().get_slack_config_record_with_team_id_user_id(
            user_id, team_id
        )
    )
    for config in config_record:
        integration_config_record = config

    if not integration_config_record:
        logger.error(
            "APP_INT_EXCEPTION: Employee with slack user id %s and team id %s not found",
            user_id,
            team_id,
        )
        next()
    else:
        try:
            # Set the client context in request object
            employee = EmployeeAccessor(
                integration_config_record.client_id
            ).get_employee(integration_config_record.employee_email_id)
            curr_time = timezone.now()
            if employee.deactivation_date and employee.deactivation_date < curr_time:
                logger.info(
                    "Invalidating integration config for deactivated employee %s",
                    employee.employee_email_id,
                )
                IntegrationConfigAccessor().invalidate_config_for_user_id_and_team_id(
                    user_id, team_id, curr_time
                )
            set_request_context(employee)
            next()
        except Exception as e:
            logger.error("APP_INT_EXCEPTION: Error with slack middleware: %s", e)
            raise Exception from e


def get_valid_token(user_id, team_id):
    """
        Performs token rotation if the underlying tokens (bot / user) are expired / expiring.
    Args:
        user_id: the slack user id
        team_id: the slack team id
    Returns:
        None
    """
    logger.info("Performing token validation... %s, %s", user_id, team_id)
    try:
        slack_install_data = SlackAccessor(user_id, team_id).get_slack_installation()
        if not slack_install_data:
            raise Exception(
                f"Invalid user : Slack Install data not present for user_id {user_id}, {team_id=}"
            )

        installation_store = DjangoInstallationStore(
            client_id=SLACK_CLIENT_ID,
            logger=logger,
        )
        latest_bot_details_for_team = installation_store.find_bot(
            enterprise_id=slack_install_data.enterprise_id,
            team_id=team_id,
        )

        if not (
            latest_bot_details_for_team
            and latest_bot_details_for_team.bot_token
            and latest_bot_details_for_team.bot_refresh_token
        ):
            raise Exception(
                f"Invalid team_id: Slack bot data not present for {team_id=}"
            )

        logger.info("Bot token available for %s", team_id)
        bot_token = latest_bot_details_for_team.bot_token

        token_rotator = TokenRotator(
            client_id=SLACK_CLIENT_ID, client_secret=SLACK_CLIENT_SECRET
        )

        if token_rotator is None:
            raise BoltError(
                "InstallationStore with client_id/client_secret are required for token rotation"
            )

        installation = Installation(
            user_id=user_id,
            bot_id=latest_bot_details_for_team.bot_id,
            bot_refresh_token=latest_bot_details_for_team.bot_refresh_token,
            bot_token_expires_at=latest_bot_details_for_team.bot_token_expires_at,
            installed_at=datetime.fromtimestamp(
                latest_bot_details_for_team.installed_at
            ),
            app_id=latest_bot_details_for_team.app_id,
            enterprise_id=latest_bot_details_for_team.enterprise_id,
        )
        refreshed = token_rotator.perform_token_rotation(
            installation=installation,
        )
        if refreshed is not None:
            logger.info(
                "************ Bot Token refreshed for %s****************", team_id
            )
            installation_store.update_bot_token_details_for_team(team_id, refreshed)
            bot_token = refreshed.bot_token

        return {"bot_token": bot_token}
    except SlackTokenRotationError as slack_token_error:
        logger.error(
            "APP_INT_EXCEPTION: Error in getting bot token for user %s %s %s",
            user_id,
            team_id,
            slack_token_error,
        )
        if slack_token_error.api_error.response.get("error") == "invalid_refresh_token":
            logger.error(
                "APP_INT_EXCEPTION: The bot refresh token for the %s has expired; Please disconnect and reconnect from slack",
                team_id,
            )
        raise Exception from slack_token_error
    except Exception as error:
        logger.error(
            "APP_INT_EXCEPTION: Error in getting bot token for user %s %s %s",
            user_id,
            team_id,
            error,
        )
        raise Exception from error


@app.event("team_join")
# pylint: disable=unused-argument
# pylint: disable=redefined-outer-name
def user_added_to_team(event, client, logger, context):
    try:
        user_id = context["user_id"]
        team_id = context["team_id"]
        user_details = event.get("user", {})
        email = user_details.get("profile", {}).get("email", None)
        if email:
            logger.info(f"New user added to team: {email} {user_id} {team_id}")
            is_member_deleted_from_workspace = user_details.get("deleted", False)
            update_employee_config(
                email, user_id, team_id, is_member_deleted_from_workspace
            )
        return Response(status=200)
    except Exception as exception:
        logger.error(
            f"APP_INT_EXCEPTION: Error handling user changed event for: {context['user_id']}, {context['team_id']} - {exception}"
        )
        return Response(status=500)


@app.event("user_change")
# pylint: disable=unused-argument
# pylint: disable=redefined-outer-name
def user_data_changed(event, client, logger, context):
    try:
        user_id = context["user_id"]
        team_id = context["team_id"]
        user_details = event.get("user", {})
        email = user_details.get("profile", {}).get("email", None)
        if email:
            logger.info(f"User data changed: {email} {user_id} {team_id}")
            is_member_deleted_from_workspace = user_details.get("deleted", False)
            if is_member_deleted_from_workspace:
                update_employee_config(
                    email, user_id, team_id, is_member_deleted_from_workspace
                )
        return Response(status=200)
    except Exception as exception:
        logger.error(
            f"APP_INT_EXCEPTION: Error handling user changed event for: {context['user_id']}, {context['team_id']} - {exception}"
        )
        return Response(status=500)


@app.event("app_home_opened")
def app_home_opened(event, client, logger, context):
    user_id = context["user_id"]
    team_id = context["team_id"]
    logger.info("Running app home event")
    try:
        logger.info(f"Verifying user {user_id} {team_id}")
        try:
            config_record = get_employee_in_slack(
                slack_user_id=user_id, slack_team_id=team_id
            )
            client_id = config_record.client_id
            email_id = config_record.employee_email_id
            is_slack_connected_for_client = is_slack_connected(client_id)

            if not is_slack_connected_for_client:
                client.views_publish(
                    user_id=user_id,
                    view={"type": "home", "blocks": connect_prompt_block(user_id)},
                )
                return

            commissions_localized = get_localized_message_service(
                SLACK_MESSAGES.COMMISSIONS.value, client_id
            ).lower()
            quota_localized = get_localized_message_service(
                SLACK_MESSAGES.QUOTA.value, client_id
            ).lower()

            client.views_publish(
                user_id=user_id,
                view={
                    "type": "home",
                    "blocks": [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"Hi <@{user_id}>! :wave:, Welcome to the Everstage App!",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"Get instant insights into your {quota_localized} and {commissions_localized} using the following commands:",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"{get_slash_commands_info(client_id, email_id, commissions_localized=commissions_localized, quota_localized=quota_localized)}",
                            },
                        },
                    ],
                },
            )
            return Response(status=200)
        except ObjectDoesNotExist:
            client.views_publish(
                user_id=user_id,
                view={"type": "home", "blocks": connect_prompt_block(user_id)},
            )
    except SlackApiError as e:
        logger.error("APP_INT_EXCEPTION: Error fetching conversations: {}".format(e))
        return Response(status=500)


def connect_prompt_block(user_id):
    connect_block = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"Hi <@{user_id}>! :wave:",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "Welcome to the Everstage app for Slack!",
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": "Contact your Admin and connect your account to get started.",
            },
        },
    ]

    return connect_block


@app.action("connect")
def handle_connect_btn(ack):
    ack()


@app.command("/hello")
def handle_hello(ack, say, command, body):
    # pylint: disable=unused-argument
    ack("Hello world")
    print(body)


def uninstall_app(user_id, team_id):
    try:
        bot_token = get_valid_token(user_id, team_id)["bot_token"]
        client = WebClient(token=bot_token)
        client.apps_uninstall(
            client_id=os.environ["SLACK_CLIENT_ID"],
            client_secret=os.environ["SLACK_CLIENT_SECRET"],
        )
        logger.info("Slack app removed from workspace by %s %s", user_id, team_id)
    except Exception:
        logger.info("Exception in removing slack app  %s %s", user_id, team_id)


def get_slack_client_detail(client_id):
    return SlackClientDetailsAccessor(client_id).get()


def get_slack_bot_token_for_client(client_id):
    """
    Retrieves the slack bot token for a given client id
    """
    is_slack_connected_for_client = is_slack_connected(client_id)

    if not is_slack_connected_for_client:
        logger.info(
            "Slack not connected for client %s, returning empty token",
            client_id,
        )
        raise Exception(f"Slack not connected for client - {client_id}")

    slack_client_detail_object = SlackClientDetailsAccessor(client_id).get()

    if slack_client_detail_object:
        user_id = slack_client_detail_object.user_id
        team_id = slack_client_detail_object.team_id

        token = get_valid_token(user_id, team_id)
        slack_bot_token = token.get("bot_token")
        return slack_bot_token

    logger.info(
        "Slack client object not found for client id %s",
        client_id,
    )
    raise Exception(f"Slack client object not found for client id - {client_id}")


def get_slack_bot_token_and_user_id_for_email(client_id, email):
    """
    Retrieves the slack user id for a given client id and email
    """
    config_record = IntegrationConfigAccessor().get_slack_config_record_by_mail_id(
        client_id, email
    )
    if config_record is None:
        raise Exception(
            f"Integration config record not found for - {client_id}, {email}"
        )

    slack_config = (
        config_record.config if config_record and config_record.config else {}
    )
    user_id = slack_config.get("slack_user_id")
    team_id = slack_config.get("slack_team_id")

    if not user_id:
        logger.info(
            "User not in slack workspace for client id %s, Skipping slack notification for: %s",
            client_id,
            email,
        )
        raise Exception(
            f"User not in slack workspace for client id - {client_id} - {email}"
        )

    token = get_valid_token(user_id, team_id)
    slack_bot_token = token.get("bot_token")
    return slack_bot_token, user_id
