# ----------------------
# Bolt store implementations
# ----------------------

from datetime import datetime
from logging import Logger
from typing import Optional
from uuid import uuid4

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import F
from django.utils import timezone
from django.utils.timezone import is_naive, make_aware
from slack_bolt.error import BoltError
from slack_sdk import WebClient
from slack_sdk.oauth import InstallationStore, OAuthStateStore
from slack_sdk.oauth.installation_store import Bot, Installation

from commission_engine.accessors.client_accessor import set_client_feature
from commission_engine.utils.general_data import (
    NotificationChannelConnectionStatus,
    SegmentEvents,
    SegmentProperties,
)
from slack_everstage.accessors.slack_client_details_accessor import (
    SlackClientDetailsAccessor,
)
from slack_everstage.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    SlackClientDetails,
    SlackInstallation,
    SlackOAuthState,
)
from slack_everstage.serializers.slack_client_details_serializer import (
    SlackClientDetailsSerializer,
)
from slack_everstage.utils.slack_utils import set_request_context
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.models.config_models.employee_models import Employee
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.slack_config_services import (
    add_default_slack_notifications,
    get_employee_in_slack,
    save_slack_details,
    update_slack_details_for_client_members,
)
from spm.services.notification_channel_connection_status_services import (
    update_slack_notification_channel_connection_status,
)


class DjangoInstallationStore(InstallationStore):
    client_id: str

    def __init__(
        self,
        client_id: str,
        logger: Logger,
    ):
        self.client_id = client_id
        self._logger = logger
        self.es_client_id = None

    @property
    def logger(self) -> Logger:
        return self._logger

    def delete_bot(self, *args, **kwargs):
        pass

    def delete_installation(self, *args, **kwargs):
        pass

    def verify_and_add_user(self, user_id, team_id, bot_token):
        time = timezone.now()
        client = WebClient(token=bot_token)
        if not client:
            raise Exception(
                f"Installation Error: Error in creating web client for user {user_id}"
            )

        # get user_info ..containing email_id and other data
        user_info = client.users_info(user=user_id)
        user_info_dict = user_info.__dict__
        user_email = (
            user_info_dict.get("data", {})
            .get("user", {})
            .get("profile", {})
            .get("email")
        )
        if user_email is None:
            self.logger.error(
                f"APP_INT_EXCEPTION: Installation Error: Error in getting email_id for slack_id {user_id}"
            )
            raise Exception(
                f"Installation Error: Error in getting email_id for slack_id {user_id}"
            )

        try:
            current_employee = None
            employee = (
                Employee.objects.filter(knowledge_end_date__isnull=True)
                .filter(is_deleted=False)
                .filter(employee_email_id=user_email)
            )
            for emp in employee:
                current_employee = emp
                break
            if current_employee:
                set_request_context(current_employee)
                self.logger.info(f"Saving user_id in Employee for {user_email}")
                save_slack_details(
                    client_id=current_employee.client_id,
                    email_id=user_email,
                    slack_user_id=user_id,
                    slack_team_id=team_id,
                    knowledge_date=time,
                )
                add_default_slack_notifications(user_email)
        except ObjectDoesNotExist as o:
            self.logger.error(
                f"APP_INT_EXCEPTION: Employee data not found for {user_email}"
            )
            raise ObjectDoesNotExist(f"Employee data not found for {user_email}") from o
        except Exception as e:
            self.logger.error("APP_INT_EXCEPTION: %s", e)
            raise ObjectDoesNotExist(f"Error in slack oauth flow {user_email}") from e

    def update_bot_token_details_for_team(self, team_id, refreshed):
        i = refreshed.to_dict()

        if (
            "bot_token_expires_at" in i
            and i["bot_token_expires_at"]
            and is_naive(i["bot_token_expires_at"])
        ):
            i["bot_token_expires_at"] = make_aware(i["bot_token_expires_at"])

        SlackInstallation.objects.filter(
            client_id=self.client_id, team_id=team_id
        ).update(
            bot_refresh_token=i["bot_refresh_token"],
            bot_token=i["bot_token"],
            bot_token_expires_at=i["bot_token_expires_at"],
        )
        SlackBot.objects.filter(client_id=self.client_id, team_id=team_id).update(
            bot_refresh_token=i["bot_refresh_token"],
            bot_token=i["bot_token"],
            bot_token_expires_at=i["bot_token_expires_at"],
        )
        self.persist_slack_client_details(
            client_id=None,
            bot_token=i["bot_token"],
            bot_refresh_token=i["bot_refresh_token"],
            bot_token_expires_at=i["bot_token_expires_at"],
            team_id=team_id,
            user_id=i["user_id"],
        )

    def save(self, installation: Installation, performed_token_rotation=False):
        i = installation.to_dict()

        try:
            with transaction.atomic():
                user_id = i.get("user_id")
                team_id = i.get("team_id")
                self.logger.info(
                    "Updating installation data for %s %s", user_id, team_id
                )
                # convert naive date to zone_aware date
                if is_naive(i["installed_at"]):
                    i["installed_at"] = make_aware(i["installed_at"])
                if (
                    "bot_token_expires_at" in i
                    and i["bot_token_expires_at"]
                    and is_naive(i["bot_token_expires_at"])
                ):
                    i["bot_token_expires_at"] = make_aware(i["bot_token_expires_at"])
                if (
                    "user_token_expires_at" in i
                    and i["user_token_expires_at"]
                    and is_naive(i["user_token_expires_at"])
                ):
                    i["user_token_expires_at"] = make_aware(i["user_token_expires_at"])
                i["client_id"] = self.client_id

                row_to_update = (
                    SlackInstallation.objects.filter(client_id=self.client_id)
                    .filter(enterprise_id=installation.enterprise_id)
                    .filter(team_id=installation.team_id)
                    .filter(user_id=user_id)
                    .order_by("-installed_at")
                    .first()
                )
                if row_to_update is not None:
                    for key, value in i.items():
                        setattr(row_to_update, key, value)
                    row_to_update.save()
                else:
                    SlackInstallation(**i).save()
                self.save_bot(installation.to_bot())

                if performed_token_rotation is False:
                    self.logger.info(
                        "Begin installation flow for %s %s", user_id, team_id
                    )
                    existing_users = list(
                        IntegrationConfigAccessor().get_slack_config_record_with_team_id_user_id(
                            user_id, team_id
                        )
                    )
                    existing_user = None
                    for user in existing_users:
                        existing_user = user
                        break

                    if existing_user:
                        self.logger.info(
                            "Begin slack re-installation/update for %s %s",
                            user_id,
                            team_id,
                        )
                    else:
                        # if new user, verify and add the default notification tasks
                        self.logger.info(
                            "Begin slack-installation for new user for %s %s",
                            user_id,
                            team_id,
                        )
                        bot_token = i.get("bot_token")
                        self.verify_and_add_user(user_id, team_id, bot_token)

                    config_record = get_employee_in_slack(
                        slack_user_id=user_id, slack_team_id=team_id
                    )
                    if config_record:
                        client_id = config_record.client_id
                        employee = EmployeeAccessor(client_id).get_employee(
                            config_record.employee_email_id
                        )
                        set_request_context(employee)
                        update_slack_details_for_client_members(
                            client_id,
                            i["bot_token"],
                            i["team_id"],
                            i,
                            exclude_employee=config_record.employee_email_id,
                        )
                        self.persist_slack_client_details(
                            client_id=client_id,
                            bot_token=i.get("bot_token"),
                            bot_refresh_token=i.get("bot_refresh_token"),
                            bot_token_expires_at=i.get("bot_token_expires_at"),
                            team_id=team_id,
                            user_id=user_id,
                        )
                        set_client_feature(client_id, "slack_connected", True)
                        update_slack_notification_channel_connection_status(
                            client_id,
                            NotificationChannelConnectionStatus.CONNECTED.value,
                            config_record.employee_email_id,
                        )

                        # Segment events

                        analytics_data = {
                            "user_id": config_record.employee_email_id,
                            "event_name": SegmentEvents.SLACK_CONNECTED.value,
                            "event_properties": {
                                SegmentProperties.SLACK_EMAIL.value: config_record.employee_email_id,
                                SegmentProperties.CONNECT_SLACK_DATE.value: datetime.now().strftime(
                                    "%d-%m-%Y"
                                ),
                            },
                        }
                        analytics = CoreAnalytics(analyser_type="segment")
                        analytics.send_analytics(analytics_data)

                        user_data = {
                            "user_id": config_record.employee_email_id,
                            "user_properties": {
                                SegmentProperties.SLACK_CONNECTED.value: True
                            },
                        }
                        analytics.register_user(user_data)
                        self.logger.info(f"END: Installation Success for {user_id}")

                    else:
                        self.logger.info(
                            "Slack not enabled in any client/User not found for %s %s",
                            user_id,
                            team_id,
                        )

                else:
                    self.logger.info(
                        "Token rotation performed for %s %s", user_id, team_id
                    )

        except ObjectDoesNotExist as o:
            self.logger.info(
                "Slack user not found or the corresponding client is not enabled for slack"
            )
            raise BoltError from o
        except Exception as e:
            print(e)
            raise Exception from e

    def update_slack_client_details(
        self,
        client_id,
        bot_token,
        bot_refresh_token,
        bot_token_expires_at,
        team_id,
        user_id,
    ):
        if not client_id:
            existing_record = SlackClientDetails.objects.filter(team_id=team_id).first()
        else:
            existing_record = SlackClientDetailsAccessor(client_id).get()
        if existing_record:
            existing_record.bot_refresh_token = bot_refresh_token
            existing_record.bot_token_expires_at = bot_token_expires_at
            existing_record.team_id = team_id
            existing_record.user_id = user_id
            existing_record.bot_token = bot_token
            existing_record.updated_at = timezone.now()
            existing_record.save()
            self.logger.info(
                f"Updated existing slack client details record for team_id: {team_id}"
            )

    def persist_slack_client_details(
        self,
        client_id,
        bot_token,
        bot_refresh_token,
        bot_token_expires_at,
        team_id,
        user_id,
    ):
        self.logger.info(
            f"BEGIN: Persisting slack client details for team_id: {team_id}"
        )
        if not client_id:
            existing_record = SlackClientDetails.objects.filter(team_id=team_id).first()
        else:
            existing_record = SlackClientDetailsAccessor(client_id).get()
        if existing_record:
            self.logger.info(
                f"Found existing slack client details record for team_id: {team_id}"
            )
            self.update_slack_client_details(
                client_id,
                bot_token,
                bot_refresh_token,
                bot_token_expires_at,
                team_id,
                user_id,
            )
        else:
            data = {
                "client_id": client_id,
                "bot_token": bot_token,
                "bot_refresh_token": bot_refresh_token,
                "bot_token_expires_at": bot_token_expires_at,
                "team_id": team_id,
                "user_id": user_id,
                "updated_at": timezone.now(),
            }
            ser = SlackClientDetailsSerializer(data=data)
            if ser.is_valid():
                SlackClientDetailsAccessor(client_id).save(ser)
                self.logger.info(
                    f"END: Persisting slack client details for team_id: {team_id}"
                )
            else:
                raise Exception(
                    f"Error in persisting slack client details {ser.errors}"
                )

    def save_bot(self, bot: Bot):
        b = bot.to_dict()
        if is_naive(b["installed_at"]):
            b["installed_at"] = make_aware(b["installed_at"])
        if (
            b
            and "bot_token_expires_at" in b
            and b["bot_token_expires_at"]
            and is_naive(b["bot_token_expires_at"])
        ):
            b["bot_token_expires_at"] = make_aware(b["bot_token_expires_at"])
        b["client_id"] = self.client_id

        row_to_update = (
            SlackBot.objects.filter(client_id=self.client_id)
            .filter(enterprise_id=bot.enterprise_id)
            .filter(team_id=bot.team_id)
            .order_by("-installed_at")
            .first()
        )
        if row_to_update is not None:
            for key, value in b.items():
                setattr(row_to_update, key, value)
            row_to_update.save()
        else:
            SlackBot(**b).save()

    def find_bot(
        self,
        *,
        enterprise_id: Optional[str],
        team_id: Optional[str],
        is_enterprise_install: Optional[bool] = False,
    ) -> Optional[Bot]:
        e_id = enterprise_id or None
        t_id = team_id or None
        if is_enterprise_install:
            t_id = None
        rows = (
            SlackBot.objects.filter(client_id=self.client_id)
            .filter(enterprise_id=e_id)
            .filter(team_id=t_id)
            .order_by(F("installed_at").desc())[:1]
        )
        if len(rows) > 0:
            b = rows[0]
            return Bot(
                app_id=b.app_id,
                enterprise_id=b.enterprise_id,
                team_id=b.team_id,
                bot_token=b.bot_token,
                bot_refresh_token=b.bot_refresh_token,
                bot_token_expires_at=b.bot_token_expires_at,
                bot_id=b.bot_id,
                bot_user_id=b.bot_user_id,
                bot_scopes=b.bot_scopes,
                installed_at=b.installed_at,
            )
        return None

    def find_installation(
        self,
        *,
        enterprise_id: Optional[str],
        team_id: Optional[str],
        user_id: Optional[str] = None,
        is_enterprise_install: Optional[bool] = False,
    ) -> Optional[Installation]:
        e_id = enterprise_id or None
        t_id = team_id or None
        if is_enterprise_install:
            t_id = None
        rows = (
            SlackInstallation.objects.filter(client_id=self.client_id)
            .filter(enterprise_id=e_id)
            .filter(team_id=t_id)
            .order_by(F("installed_at").desc())[:1]
        )

        if len(rows) > 0:
            i = rows[0]
            return Installation(
                app_id=i.app_id,
                enterprise_id=i.enterprise_id,
                team_id=i.team_id,
                bot_token=i.bot_token,
                bot_refresh_token=i.bot_refresh_token,
                bot_token_expires_at=i.bot_token_expires_at,
                bot_id=i.bot_id,
                bot_user_id=i.bot_user_id,
                bot_scopes=i.bot_scopes,
                user_id=i.user_id,
                user_scopes=i.user_scopes,
                incoming_webhook_url=i.incoming_webhook_url,
                incoming_webhook_channel_id=i.incoming_webhook_channel_id,
                incoming_webhook_configuration_url=i.incoming_webhook_configuration_url,
                installed_at=i.installed_at,
            )
        return None


class DjangoOAuthStateStore(OAuthStateStore):
    expiration_seconds: int

    def __init__(
        self,
        expiration_seconds: int,
        logger: Logger,
    ):
        self.expiration_seconds = expiration_seconds
        self._logger = logger

    @property
    def logger(self) -> Logger:
        return self._logger

    def issue(self, *args, **kwargs) -> str:
        state: str = str(uuid4())
        expire_at = timezone.now() + timezone.timedelta(seconds=self.expiration_seconds)
        row = SlackOAuthState(state=state, expire_at=expire_at)
        row.save()
        return state

    def consume(self, state: str) -> bool:
        rows = SlackOAuthState.objects.filter(state=state).filter(
            expire_at__gte=timezone.now()
        )
        if len(rows) > 0:
            for row in rows:
                row.delete()
            return True
        return False
