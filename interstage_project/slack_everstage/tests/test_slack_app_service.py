import datetime
import logging
from unittest.mock import MagicMock, patch

import pytest
from slack_sdk import WebClient
from slack_sdk.errors import Slack<PERSON><PERSON><PERSON>rror, SlackTokenRotationError
from slack_sdk.oauth.installation_store import Bo<PERSON>

from commission_engine.accessors.client_accessor import set_client_feature
from commission_engine.models import Client
from commission_engine.utils.general_data import SLACK_MESSAGES, IntegrationType
from slack_everstage.models import SlackInstallation
from slack_everstage.services import slack_app_service
from slack_everstage.services.slack_app_service import (
    get_valid_token,
    global_slack_middleware,
)
from slack_everstage.services.slack_datastores import DjangoInstallationStore
from slack_everstage.utils.slack_utils import get_slash_commands_info
from spm.models import Employee
from spm.services.localization_services import get_localized_message_service
from spm.tests.models import create_employee_objects, create_integration_config_object

logger = logging.getLogger(__name__)


@pytest.fixture()
def resource():
    SlackInstallation.objects.create(
        client_id="2943567919989.3599016742054",
        app_id="A03HM0GMU1L",
        enterprise_id=None,
        enterprise_name=None,
        enterprise_url=None,
        team_id="T02TRGPT1V3",
        team_name=None,
        bot_token="MS0yLTI5NDM1Njc5MTk5ODktMzY4NzY2MjM1MDg2NC0zNjQ5Mjk4NTE1OTc1LTM3MTY1NDYzO",
        bot_refresh_token="MS0yLTI5NDM1Njc5MTk5ODktMzY4NzY2MjM1MDg2NC0zNjQ5Mjk4NTE1OTc1LTM3MTY1NDYzO",
        bot_token_expires_at=datetime.datetime.now(),
        bot_id="B03KB8PFZEJ",
        bot_user_id="B03KB8PFZEJ",
        bot_scopes="commands,chat:write",
        user_id="U02TMPSM7SS",
        user_token="U02TMPSM7SS",
        user_refresh_token="gxYzk1N2ZkY2RlYTdiN2Q2NWQyYjIxMWFhYWQ3NTE2NzdlMmNmZjZmZT",
        user_token_expires_at=datetime.datetime.now() + datetime.timedelta(days=5),
        user_scopes="users:read,users:read.email",
        incoming_webhook_url=None,
        incoming_webhook_channel=None,
        incoming_webhook_channel_id=None,
        incoming_webhook_configuration_url=None,
        is_enterprise_install=False,
        token_type="bot",
        installed_at=datetime.datetime.now(),
    )

    slack_app_service.DjangoInstallationStore = MagicMock(
        return_value=DjangoInstallationStore(
            client_id="2943567919989.3599016742054",
            logger=logger,
        )
    )
    set_client_feature(1, "slack_connected", True)


@pytest.mark.django_db
@pytest.mark.slack_services1
class TestSlackAppService:
    def test_connect_prompt_block(self):
        connect_block = slack_app_service.connect_prompt_block("1234")
        assert connect_block == [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi <@1234>! :wave:",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "Welcome to the Everstage app for Slack!",
                },
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "Contact your Admin and connect your account to get started.",
                },
            },
        ]

    @patch(
        "spm.services.rbac_services.get_ui_permissions",
        return_value=["view:quotas", "view:payouts"],
    )
    def test_app_home_opened(self, mock_get_ui_permissions):
        set_client_feature(1, "slack_connected", True)
        create_employee_objects(
            "<EMAIL>",
            "Ashiq",
            "Mohamed",
            False,
            employee_config={},
        )
        create_integration_config_object(
            client_id=1,
            integration_type=IntegrationType.SLACK.value,
            config={
                "slack_team_id": "T02TRGPT1V3",
                "slack_user_id": "U02TMPSM7SS",
            },
            employee_email_id="<EMAIL>",
        )
        client = MagicMock(return_value=WebClient)
        slack_app_service.app_home_opened(
            "", client, logger, {"team_id": "T02TRGPT1V3", "user_id": "U02TMPSM7SS"}
        )
        commissions_localized = get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, 1
        ).lower()
        quota_localized = get_localized_message_service(
            SLACK_MESSAGES.QUOTA.value, 1
        ).lower()
        client.views_publish.assert_called_with(
            user_id="U02TMPSM7SS",
            view={
                "type": "home",
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "Hi <@U02TMPSM7SS>! :wave:, Welcome to the Everstage App!",
                        },
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"Get instant insights into your {quota_localized} and {commissions_localized} using the following commands:",
                        },
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": get_slash_commands_info(1, "<EMAIL>"),
                        },
                    },
                ],
            },
        )

    def test_app_home_opened_exc(self):
        with pytest.raises(Exception):
            slack_app_service.app_home_opened(
                "",
                "client",
                logger,
                {"team_id": "T02TRGPT1V3", "user_id": "U02TMPSM7SS"},
            )
        with pytest.raises(Exception):
            get_valid_token("U02TMPSM7SS", "T02TRGPT1V3")

    @pytest.mark.get_valid_token
    def test_get_valid_token_invalid_refresh_token(self, resource):
        DjangoInstallationStore.save = MagicMock()
        DjangoInstallationStore.find_bot = MagicMock(
            retrun_value=Bot(
                bot_id="bot-id-123",
                bot_user_id="bot-user-123",
                bot_refresh_token="bot-refresh-123",
                bot_token="bot-access-123",
                installed_at=datetime.datetime.now().timestamp(),
                enterprise_id="everstage-123",
            )
        )
        slack_app_service.TokenRotator.perform_bot_token_rotation = MagicMock(
            side_effect=SlackTokenRotationError(
                api_error=SlackApiError(
                    response={"ok": False, "error": "invalid_refresh_token"},
                    message="Testing",
                )
            )
        )
        with pytest.raises(Exception):
            get_valid_token("U02TMPSM7SS", "T02TRGPT1V3")

    @pytest.mark.get_valid_token
    def test_get_valid_token(self, resource):
        DjangoInstallationStore.save = MagicMock()
        DjangoInstallationStore.find_bot = MagicMock(
            retrun_value=Bot(
                bot_id="bot-id-123",
                bot_user_id="bot-user-123",
                bot_refresh_token="bot-refresh-123",
                bot_token="bot-access-123",
                installed_at=datetime.datetime.now().timestamp(),
                enterprise_id="everstage-123",
            )
        )
        DjangoInstallationStore.update_bot_token_details_for_team = MagicMock()
        slack_app_service.TokenRotator.perform_token_rotation = MagicMock()
        get_valid_token("U02TMPSM7SS", "T02TRGPT1V3")
        slack_app_service.TokenRotator.perform_token_rotation.assert_called_once()
        # pylint: disable=E1101
        DjangoInstallationStore.find_bot.assert_called_once()

    @pytest.mark.get_valid_token
    def test_get_valid_token_exp(self, resource):
        DjangoInstallationStore.save = MagicMock()
        slack_app_service.TokenRotator = MagicMock(return_value=None)
        with pytest.raises(Exception):
            get_valid_token("U02TMPSM7SS", "T02TRGPT1V3")

    @pytest.mark.slack_global_middleware
    def test_global_slack_middleware_with_exiting_slack_user(self, resource):
        next_mock = MagicMock()
        Client.objects.create(
            client_id=100,
            name="test",
            domain="test.com",
            logo_url=None,
            auth_connection_name=None,
            connection_type=None,
            base_currency=None,
            fiscal_start_month=1,
            secondary_calculator=None,
            client_notification=True,
            payee_notification=False,
            meta_info={},
            logo_s3_path=None,
            time_zone=None,
            client_features={
                "show_reports": False,
                "freeze_date": 15,
            },
            is_deleted=False,
        )
        Employee.objects.create(
            employee_email_id="<EMAIL>",
            first_name="QW",
            last_name="ER",
            user_role="Payee",
            created_date=datetime.datetime.now(),
            created_by="<EMAIL>",
            profile_picture=None,
            send_notification=False,
            client_id=100,
            knowledge_begin_date=datetime.datetime.now(),
            time_zone="UTC",
            employee_config={},
        )
        create_integration_config_object(
            client_id=1,
            integration_type=IntegrationType.SLACK.value,
            config={"user_id": "user-123", "team_id": "team-123"},
            employee_email_id="<EMAIL>",
        )
        global_slack_middleware(
            MagicMock(),
            {"user_id": "user-123", "team_id": "team-123"},
            MagicMock(),
            MagicMock(),
            next_mock,
        )
        next_mock.assert_called_once()

    @pytest.mark.slack_global_middleware
    def test_global_slack_middleware_without_exiting_slack_user(self, resource):
        next_mock = MagicMock()
        global_slack_middleware(
            MagicMock(),
            {"user_id": "user-123", "team_id": "team-123"},
            MagicMock(),
            MagicMock(),
            next_mock,
        )
        next_mock.assert_called_once()
