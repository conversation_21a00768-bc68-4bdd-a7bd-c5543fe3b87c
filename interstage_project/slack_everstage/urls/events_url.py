from django.http import HttpRequest
from django.urls import path
from django.views.decorators.csrf import csrf_exempt

from slack_everstage.services.commission_services.slash_commission_service import (
    handler as slash_commission_commands_handler,
)
from slack_everstage.services.commission_services.slash_crystal_service import (
    handler as slash_crystal_handler,
)
from slack_everstage.services.commission_services.slash_quota_attainment_service import (
    handler as slash_quota_attainment_handler,
)
from slack_everstage.services.commission_services.slash_quota_service import (
    handler as slash_quota_handler,
)
from slack_everstage.services.slack_app_service import handler


@csrf_exempt
def slack_events_handler(request: HttpRequest):
    return handler.handle(request)


@csrf_exempt
def slack_commission_commands_handler(request: HttpRequest):
    return slash_commission_commands_handler.handle(request)


@csrf_exempt
def slack_quota_attainment_handler(request: HttpRequest):
    return slash_quota_attainment_handler.handle(request)


@csrf_exempt
def slack_quota_handler(request: HttpRequest):
    return slash_quota_handler.handle(request)


@csrf_exempt
def slack_crystal_handler(request: HttpRequest):
    return slash_crystal_handler.handle(request)


event_url = [
    path("events", slack_events_handler, name="slack_events"),
    path(
        "events/commissions",
        slack_commission_commands_handler,
        name="slack_events_commissions",
    ),
    path(
        "events/quota_attainment",
        slack_quota_attainment_handler,
        name="slack_events_quota_attainment",
    ),
    path(
        "events/quota",
        slack_quota_handler,
        name="slack_events_quota",
    ),
    path(
        "events/crystal",
        slack_crystal_handler,
        name="slack_events_crystal",
    ),
    # NOTE:
    # Any new end-point here which is handled by `SlackRequestHandler` should also be added to :-
    # interstage_project/interstage_project/applications/slack.py -> `Slack.identify_request` method.
]
