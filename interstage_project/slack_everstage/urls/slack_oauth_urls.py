from django.http import HttpRequest
from django.urls import path

from slack_everstage.services.slack_app_service import handler


def slack_oauth_handler(request: HttpRequest):
    print(f"{request.headers=}")
    return handler.handle(request)


def slack_install(request: HttpRequest):
    return handler.handle(request)


oauth_url = [
    path("install", slack_install, name="install"),
    path("oauth_redirect", slack_oauth_handler, name="oauth_redirect"),
    # NOTE:
    # Any new end-point here which is handled by `SlackRequestHandler` should also be added to :-
    # interstage_project/interstage_project/applications/slack.py -> `Slack.identify_request` method.
]
