import logging
import os
import uuid

from crum import get_current_request
from slack_sdk.errors import SlackApiError

from commission_engine.accessors.client_accessor import get_client
from everstage_infra.aws_infra.constants.environments import ENVIRONMENT
from spm.models.config_models.employee_models import Employee

logger = logging.getLogger(__name__)

SLACK_MESSAGE_LIMIT = 3000


def set_request_context(employee):
    # Set the client context in request object
    client_id = employee.client_id
    request = get_current_request()
    if request:
        logger.info(
            "Setting client context in the request for employee %s with client id %s",
            employee.employee_email_id,
            client_id,
        )
        client = get_client(client_id)
        request.client_id = client.client_id
        request.domain = client.domain
        request.request_id = uuid.uuid4()


def get_user_email(client, user_id, token):
    try:
        result = client.users_info(user=user_id, token=token)
        user_email = result.data.get("user", {}).get("profile", {}).get("email")
        return user_email
    except SlackApiError:
        print("Error getting email for the user %s", user_id)


def get_client_id_from_email(user_email):
    employees = Employee.objects.filter(
        employee_email_id=user_email,
        knowledge_end_date__isnull=True,
        is_deleted=False,
    )

    for emp in employees:
        return emp.client_id

    return None


def get_site_url():
    if os.environ.get("ENV") == ENVIRONMENT["PRODUCTION"]:
        return "https://app.everstage.com"
    elif os.environ.get("ENV") == ENVIRONMENT["PRODUCTION-EU"]:
        return "https://app.eu.everstage.com"
    else:
        return os.environ.get("URL")


def get_slash_commands_info(client_id, email_id, **kwargs):
    from commission_engine.utils.general_data import SLACK_MESSAGES, Command
    from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
    from spm.services.localization_services import get_localized_message_service
    from spm.services.rbac_services import get_ui_permissions

    commissions_localized = (
        kwargs.get("commissions_localized")
        if kwargs.get("commissions_localized")
        else get_localized_message_service(
            SLACK_MESSAGES.COMMISSIONS.value, client_id
        ).lower()
    )
    quota_localized = (
        kwargs.get("quota_localized")
        if kwargs.get("quota_localized")
        else get_localized_message_service(
            SLACK_MESSAGES.QUOTA.value, client_id
        ).lower()
    )
    description = {
        "SLASH_COMMISSION_COMMAND": f"See your {commissions_localized} for a selected period",
        "SLASH_QUOTA_ATTAINMENT_COMMAND": f"See your {quota_localized} attainment for a given period",
        "SLASH_QUOTA_COMMAND": f"See your {quota_localized} details for a given {quota_localized} category",
        "SLASH_CRYSTAL_COMMAND": f"Simulate and project your future {commissions_localized}",
    }

    user_permissions = get_ui_permissions(client_id, email_id)
    command_info = ""

    for command in Command:
        required_permissions = command.value.get("permissions", [])
        can_show = False or len(required_permissions) == 0

        # At least one of the required permissions must be present
        for permission in required_permissions:
            if user_permissions and permission in user_permissions:
                can_show = True
                break

        if command.value.get("name") == "SLASH_CRYSTAL_COMMAND":
            can_show = (
                can_show
                or EmployeePayrollAccessor(client_id).get_payee_role(email_id)
                == "Revenue"
            )

        if can_show:
            cmd = command.value.get("command")
            des = description[command.value.get("name")]
            command_info += f"`{cmd}` {des}\n"

    return command_info


def slash_help_block(client_id, email_id):
    commands = get_slash_commands_info(client_id, email_id)
    if commands:
        help_block = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"Hi! You can run these commands.\n{commands}",
                },
            },
        ]
    else:
        help_block = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "Sorry, you don't have access to this command",
                },
            },
        ]
    return help_block


def get_slack_message_character_limit():
    """
    Slack message blocks text field has a limit of 3000 characters.
    """
    return SLACK_MESSAGE_LIMIT
