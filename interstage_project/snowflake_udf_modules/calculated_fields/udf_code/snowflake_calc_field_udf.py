"""
Any change made to the calculated fields code under commission_engine/services/commission_calculation_service
should also be made to this file
Guidelines to replicate the code :-
1. Any new third party modules used here should be added to the Packages constant in udf_function.sql
2. Add any new operators to operators.py and new function and its implementation to functions.py unless it will result in
a circular import, in which case, add it here.
"""

# pylint: disable=import-error, global-statement

import decimal
import json

from .function_fields import (
    DATATYPES_DEFAULT_VALUE_MAP,
    EMPTY_VALUES,
    _get_repr_val_from_value,
    const__,
    date_add__,
    date_diff__,
    get_date_period__,
    get_nth_level_node__,
    is_empty__,
    is_not_empty__,
    last_date__,
    len__,
    lower__,
    round__,
    round_down__,
    round_up__,
    start_date__,
    timezone__,
    trim__,
)
from .operators_fields import operations
from .utils import get_var, set_accurate_column_types, to_numeric

#####################################################################
# function operator related fields, this was not added to function_fields.py
# since it would result in a circular import between this driver file and function_fields.py

date_output_functions = ["StartDate", "LastDate", "DateAdd"]
DEFAULT_TIMEZONE = "UTC"


def handle_variable(token, data, ever_comparison=False, case_insensitive=False):
    if "function_name" in token:
        function = token["function_name"]

        if function in (
            "IF",
            "Contains",
            "NotContains",
            "Concat",
            "Left",
            "Right",
            "Mid",
        ):
            res = functions[function](
                *token["args"], data, ever_comparison=ever_comparison
            )
            # make sure to add case insensitve flag for all string related functions
            # Contains , Not Contains are default case insensitive
        elif function == "Find":
            res = functions[function](
                *token["args"],
                data,
                ever_comparison=ever_comparison,
                case_insensitive=case_insensitive,
            )
        elif function == "Coalesce":
            res = functions[function](
                token["args"], data, ever_comparison=ever_comparison
            )
        else:
            res = functions[function](*token["args"], data)
    else:
        variable = token["meta"]["system_name"]
        if token["meta"]["category"] == "CONFIG":
            schedule = data["schedule"] if "schedule" in data else None
            if (
                "context" in data
                and "payee" in data["context"]
                and data["context"]["payee"]
            ):
                payee_email = data["payee_email"]
                if (
                    "team_params" in data
                    and data["team_params"]
                    and "team_owner_email_id" in data["team_params"]
                ):
                    payee_email = data["team_params"]["team_owner_email_id"]
                line_item = data["context"]["payee"][0]
                for payee_dat in data["context"]["payee"]:
                    if payee_dat["email"] == payee_email:
                        line_item = payee_dat
                        break
                res = get_var(variable, {"line_item": line_item, "schedule": schedule})
            elif variable in ("commission", "settled_amount"):
                res = get_var(variable, data)
            else:
                res = 0
        else:
            res = get_var(variable, data)

        if res is None:
            res = DATATYPES_DEFAULT_VALUE_MAP.get(token["data_type"].lower())
            ##Returns default value when token is a variable/constant

        if token["data_type"].lower() == "integer":
            res = to_numeric(res)
        if token["data_type"].lower() == "percentage":
            res = res / 100
            if not isinstance(res, decimal.Decimal):
                res = decimal.Decimal(res)
    if res is None:
        if "function_name" in token and token["function_name"] in date_output_functions:
            return res
        res = DATATYPES_DEFAULT_VALUE_MAP.get(token["data_type"].lower())
        ##Returns default value when token is a function
    return res


def concat__(delimiter, tokens, data=None, ever_comparison=False):
    delimiter = "" if not delimiter else delimiter
    tokens = [] if not tokens else tokens
    data = {} if not data else data
    token_values = []
    for token in tokens:
        res = ""
        if "function_name" in token:
            res = handle_variable(token, data, ever_comparison=ever_comparison)
        elif "meta" in token and "system_name" in token["meta"]:
            res = data.get("line_item", {}).get(token["meta"]["system_name"])
            if res == None:
                res = ""
        token_values.append(str(res))
    return delimiter.join(token_values)


def coalesce__(tokens, data=None, ever_comparison=False):
    tokens = [] if not tokens else tokens
    data = {} if not data else data
    token_values = []
    for token in tokens:
        res = ""
        if "function_name" in token:
            res = handle_variable(token, data, ever_comparison=ever_comparison)
        elif "meta" in token and "system_name" in token["meta"]:
            res = data.get("line_item", {}).get(token["meta"]["system_name"])
        token_values.append(res)
    return next((x for x in token_values if x != "" and x is not None), None)


def contains__(token, string_to_check, data, ever_comparison=False):
    """
    Check if the given string contains the other string, case insensitive.
    `string_to_check` can take on some query string or other datasheet field.
    """
    data = {} if not data else data
    key = token.get("meta", {}).get("system_name")
    data_type = token.get("data_type", "").lower()
    value = data.get("line_item", {}).get(key)

    # handling Hierarchy data type
    if data_type == "hierarchy" and value:
        value = _get_repr_val_from_value(value)

    # handling when string_to_check is a token (might be a const string ßor
    # other datasheet field)
    if not isinstance(string_to_check, str):
        string_to_check = handle_variable(
            string_to_check, data, ever_comparison=ever_comparison
        )

    return (
        value not in EMPTY_VALUES
        and string_to_check not in EMPTY_VALUES
        and string_to_check.lower() in value.lower()
    )


def not_contains__(token, string_to_check, data, ever_comparison=False):
    """
    Check if the given string not-contains the other string, case insensitive.
    `string_to_check` can take on some query string or other datasheet field.
    Return false if any of token or string_to_check is empty.
    """
    data = {} if not data else data
    key = token.get("meta", {}).get("system_name")
    data_type = token.get("data_type", "").lower()
    value = data.get("line_item", {}).get(key)

    # handling Hierarchy data type
    if data_type == "hierarchy" and value:
        value = _get_repr_val_from_value(value)

    # handling when string_to_check is a token (might be a const string ßor
    # other datasheet field)
    if not isinstance(string_to_check, str):
        string_to_check = handle_variable(
            string_to_check, data, ever_comparison=ever_comparison
        )

    if value in EMPTY_VALUES or string_to_check in EMPTY_VALUES:
        return False

    return string_to_check.lower() not in value.lower()


def left__(token, num_chars, data=None, ever_comparison=False):
    """
    Get left substring of a text column

    Args:
    -----
        `token` - token that has the datasheet field to apply function.
        `num_chars` - token that can either be a datasheet field or an integer constant.
        `data` - a line_item.

    Returns:
    --------
        Returns left substring of `data` if `data` is nonempty and num_chars is positive.
        `None` otherwise.
        NOTE: If num_chars exceeds the length of the string, the entire string will be returned.
    """
    data = {} if not data else data

    # get number of characters to index from num_chars token
    n_chars = handle_variable(num_chars, data, ever_comparison=ever_comparison)

    key = token.get("meta", {}).get("system_name")
    value = data.get("line_item", {}).get(key)

    if value not in EMPTY_VALUES and n_chars not in EMPTY_VALUES:
        n_chars = int(n_chars)
        # handling negative values
        if n_chars <= 0:
            return None
        return substring__(value, "Left", n_chars)
    else:
        return None


def right__(token, num_chars, data=None, ever_comparison=False):
    """
    Get right substring of a text column. For args and return values refer `left__`.
    """
    data = {} if not data else data

    # get number of characters to index from num_chars token
    n_chars = handle_variable(num_chars, data, ever_comparison=ever_comparison)

    key = token.get("meta", {}).get("system_name")
    value = data.get("line_item", {}).get(key)

    if value not in EMPTY_VALUES and n_chars not in EMPTY_VALUES:
        n_chars = int(n_chars)
        # handling negative values
        if n_chars <= 0:
            return None
        return substring__(value, "Right", n_chars)
    else:
        return None


def mid__(token, start_index, num_chars, data=None, ever_comparison=False):
    """
    Get mid substring of a text column

    Args:
    -----
        `token` - token that has the datasheet field to apply function.
        `start_index` - token that has a datasheet field. The value is index 1 based.
        `num_chars` - token that can either be a datasheet field or an integer constant.
        `data` - a line_item.

    Returns:
    --------
        Returns mid substring of `data` if `data` is nonempty, num_chars and start_index is positive.
        `None` otherwise.
        NOTE: If num_chars exceeds the length of the string, the entire string will be returned.
    """
    data = {} if not data else data

    # get number of characters to index from num_chars token
    n_chars = handle_variable(num_chars, data, ever_comparison=ever_comparison)

    # get starting index from start_index token
    start_ind = handle_variable(start_index, data, ever_comparison=ever_comparison)

    key = token.get("meta", {}).get("system_name")
    value = data.get("line_item", {}).get(key)

    if (
        value not in EMPTY_VALUES
        and n_chars not in EMPTY_VALUES
        and start_ind not in EMPTY_VALUES
    ):
        # handling negative values
        n_chars = int(n_chars)
        # adapting to 1 based indexing
        start_ind = int(start_ind)
        start_ind -= 1
        if start_ind < 0 or n_chars <= 0:
            return None
        return substring__(value, "Mid", n_chars, start_ind)
    else:
        return None


def substring__(string, direction, n_chars, start_index=None):
    if direction == "Left":
        return string[:n_chars]
    elif direction == "Right":
        return string[-n_chars:]
    elif direction == "Mid":
        return string[start_index : start_index + n_chars]
    else:
        return ""


def find__(token, query, data=None, ever_comparison=False, case_insensitive=False):
    """
    Get index of first occurance of a query string.

    Args:
    -----
        `token` - the datasheet field on which the function to be applied.
        `query` - a constant/other datasheet field.
        `data` - a line_item.

    Returns:
    --------
        The index of first occurance of the query string. Indexing starts from 1.
        Returns 0 if query is not present in data.
    """
    data = {} if not data else data

    query_str = ""
    query_str = handle_variable(query, data, ever_comparison=ever_comparison)

    key = token.get("meta", {}).get("system_name")
    value = data.get("line_item", {}).get(key)

    if value not in EMPTY_VALUES and query_str not in EMPTY_VALUES:
        query_str = str(query_str)
        value = str(value)
        if case_insensitive:
            query_str = query_str.lower()
            value = value.lower()
        pos = value.find(query_str)

        # if query not found in given string
        if pos < 0:
            return 0
        return pos + 1

    else:
        return 0


def if__(condition, thenClause, elseClause, data, ever_comparison=False):
    if evaluate_ast(condition, data, ever_comparison=ever_comparison):
        return evaluate_ast(thenClause, data, ever_comparison=ever_comparison)
    else:
        if elseClause:
            return evaluate_ast(elseClause, data, ever_comparison=ever_comparison)
        else:
            return float("-inf")


functions = {
    "DATEDIFF": date_diff__,
    "StartDate": start_date__,
    "LastDate": last_date__,
    "DateAdd": date_add__,
    "GetDate": get_date_period__,
    "Timezone": timezone__,
    "Round": round__,
    "RoundUp": round_up__,
    "RoundDown": round_down__,
    "IsEmpty": is_empty__,
    "IsNotEmpty": is_not_empty__,
    "Contains": contains__,
    "NotContains": not_contains__,
    "Trim": trim__,
    "Lower": lower__,
    "Concat": concat__,
    "Coalesce": coalesce__,
    "Len": len__,
    "Left": left__,
    "Right": right__,
    "Mid": mid__,
    "Find": find__,
    "IF": if__,  #
    "CONSTANT": const__,
    "GetNthLevelNode": get_nth_level_node__,
}


#####################################################################
def evaluate_ast(ast, data=None, ever_comparison=False, case_insensitive=False):
    try:
        if isinstance(ast, dict) and "type" in ast and ast["type"] == "VARIABLE":
            return handle_variable(
                ast,
                data,
                ever_comparison=ever_comparison,
                case_insensitive=case_insensitive,
            )

        if ast is None or not isinstance(ast, dict):
            return ast

        data = data if data else {}
        operator = list(ast.keys())[0]
        values = ast[operator]
        if not isinstance(values, list) and not isinstance(values, tuple):
            values = [values]

        values = [
            evaluate_ast(val, data, ever_comparison=ever_comparison) for val in values
        ]
        if operator not in operations:
            raise ValueError("Unrecognized operation %s" % operator)

        if operator in ("==", "===", "!=", "!==", ">", ">=", "<", "<="):
            res = operations[operator](*values, ever_comparison=ever_comparison)
        elif operator in ("IN", "NOTIN"):
            res = operations[operator](*values, case_insensitive=case_insensitive)
        else:
            res = operations[operator](*values)

        return res
    except ZeroDivisionError:
        return 0
    except Exception as e:
        raise e


def evaluate_per_variable(
    line_item=None,
    variable_name=None,
    fiscal_start_month=None,
    data_type_id=None,
    data_type_map=None,
    variable_ast_map=None,
    ever_comparison=False,
    case_insensitive=False,
    custom_periods={},
):
    ast = variable_ast_map[variable_name]

    if isinstance(ast, str):
        # Returning 'No expression!'
        return ast

    # Otherwise evaluate the ast tree
    res = evaluate_ast(
        ast,
        data={
            "line_item": line_item,
            "start_month": fiscal_start_month,
            "custom_periods": custom_periods,
        },
        ever_comparison=ever_comparison,
        case_insensitive=case_insensitive,
    )
    if res == float("-inf"):
        # If "NULL"/"Do Nothing" is enabled, then result could be -Infinity. Resetting it back to 0.
        res = 0
    if (
        data_type_id != data_type_map["INTEGER"]
        and data_type_id != data_type_map["PERCENTAGE"]
        and not isinstance(res, bool)
        and res == 0
    ):
        res = None
    if isinstance(res, decimal.Decimal):
        res = float(res)
    return res


def evaluate_ast_wrapper_variables_wrapped(
    data: dict,
    data_type_map: dict,
    variables: list,
    variable_ast_map: dict,
    fiscal_start_month: int,
    var_type_map: dict,
    timezone: str,
    ever_comparison: bool = False,
    custom_periods: dict = {},
) -> dict:
    """
    Applies all calculated fields on a single row of data.
    Args :-
        data :- row on which calc field is evaluated
        data_type_map :- id to data type name map
        variables :- list of variables to evaluate calc field for
        variable_ast_map :- calc field system name to its ast map
        var_type_map :- variable and their data type map
        timezone :- default timezone from django
    """
    global DEFAULT_TIMEZONE
    DEFAULT_TIMEZONE = timezone

    datatype_id_name_map = dict()
    for key, value in data_type_map.items():
        datatype_id_name_map[value] = key

    # apply calculated fieds computation on the preprocessed data
    preprocessed_data = set_accurate_column_types(
        var_type_map, data, datatype_id_name_map
    )

    for variable in variables:
        variable_name = variable["system_name"]
        data_type_id = variable["data_type_id"]
        case_insensitive = variable.get("meta_data", {}).get("case_insensitive", False)
        val = evaluate_per_variable(
            line_item=preprocessed_data,
            variable_name=variable_name,
            data_type_id=data_type_id,
            data_type_map=data_type_map,
            fiscal_start_month=fiscal_start_month,
            variable_ast_map=variable_ast_map,
            ever_comparison=ever_comparison,
            custom_periods=custom_periods,
            case_insensitive=case_insensitive,
        )

        # Removing this as it replaces microseconds to zero,
        # which cause issues in date comparison (while joining)

        preprocessed_data[variable_name] = val
        # add the computed calc fields to data
        data[variable_name] = val

    return data
