-- This is the snowflake udf function that will be mapped to
-- the python implemenation of calculated fields

-- snowflake_calc_fields is the name of the snowflake udf function
--     data arg is the data column in datasheet_data table
--     data_type_map contains the mapping between everstage data type id and its name
--     variable_list contains list of calculated feilds(each field is a dictionary)
--     variable_ast_map contains mapping of variable system name to its ast
--     fiscal_start_month is the client's fiscal_start_month
--     var_type_map contains the mapping of variable name and it's data type
--     default_timezone contains the default timezone for the client(utc if not set)
--     ever_comparison flag that tells whether to use custom logical comparison for float values

-- the calculated_fields_bundle.zip file will have all the third party modules as well as the python code for calculated fields.
-- it's directory structure looks like this.
-- calculated_fields_bundle/
--      pydash/
--      pytz/
--      dateutil/
--      udf_code/
--          function_fields.py
--          operator_fields.py
--          snowflake_calc_fields.py
--          utils.py


create or replace function snowflake_calc_fields(
	data object,
    data_type_map object,
    variable_list array,
    variable_ast_map object,
    fiscal_start_month int,
    var_type_map object,
    default_timezone string,
    ever_comparison boolean,
    custom_periods object
)
returns object
language python
runtime_version = '3.10'
handler = 'compute_calc_fields'
imports = ('@SNOWFLAKE_CALC_FIELDS/calculated_fields_udf_bundle.zip')
PACKAGES = ('pandas', 'numpy')
as
$$
import fcntl
import os
import sys
import threading
import zipfile
import traceback

# File lock class for synchronizing write access to /tmp
class FileLock:
   def __enter__(self):
      self._lock = threading.Lock()
      self._lock.acquire()
      self._fd = open('/tmp/lockfile.LOCK', 'w+')
      fcntl.lockf(self._fd, fcntl.LOCK_EX)

   def __exit__(self, type, value, traceback):
      self._fd.close()
      self._lock.release()

# Get the location of the import directory. Snowflake sets the import
# directory location so code can retrieve the location via sys._xoptions.
IMPORT_DIRECTORY_NAME = "snowflake_import_directory"
import_dir = sys._xoptions[IMPORT_DIRECTORY_NAME]

# Get the path to the ZIP file and set the location to extract to.
zip_file_path = import_dir + "calculated_fields_udf_bundle.zip"
extracted = '/tmp/modules_dir/calculated_fields'

# Extract the contents of the ZIP. This is done under the file lock
# to ensure that only one worker process unzips the contents.
with FileLock():
   if not os.path.isdir(extracted):
      with zipfile.ZipFile(zip_file_path, 'r') as myzip:
         myzip.extractall(extracted)

# The code is present in calculated_fields_udf_bundle/udf_code
sys.path.append(extracted + "/calculated_fields_udf_bundle")
sys.path.append(extracted + "/calculated_fields_udf_bundle/udf_code")

from udf_code.snowflake_calc_field_udf import (
	evaluate_ast_wrapper_variables_wrapped as evaluate_ast_wrapper_variables_wrapped
)

def compute_calc_fields(
    data, 
    data_type_map, 
    variable_list, 
    variable_ast_map, 
    fiscal_start_month, 
    var_type_map,
    default_timezone,
    ever_comparison,
    custom_periods,
):

    return evaluate_ast_wrapper_variables_wrapped(
    	data,
        data_type_map,
        variable_list,
        variable_ast_map,
        fiscal_start_month,
        var_type_map,
        default_timezone,
        ever_comparison,
        custom_periods,
    )
$$;
