"""Module providing access to Approval Workflow related models."""

from datetime import datetime

from django.db import connection
from django.db.models import <PERSON>, Ex<PERSON>, <PERSON>, OuterRef, Q, Subquery

from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_WORKFLOW_STATUS,
)
from spm.models.approval_workflow_models import (
    ApprovalInstance,
    ApprovalInstanceStage,
    ApprovalRequests,
    ApprovalTemplate,
    ApprovalTemplateStage,
    SubApprovalRequests,
)


class ApprovalTemplateAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApprovalTemplate.objects.filter(client=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            knowledge_end_date__isnull=True, is_deleted=False
        )

    def persist_approval_template(self, data):
        return data.save()

    def get_all_templates(
        self,
        exclude_inactive=True,
        as_dict=False,
        projection=None,
        order_by=None,
        entity_types_to_exclude: list | None = None,
    ):
        qs = self.client_kd_aware()
        if exclude_inactive:
            qs = qs.exclude(is_active=False)
        if entity_types_to_exclude:
            qs = qs.exclude(entity_type__in=entity_types_to_exclude)
        if order_by:
            qs = qs.order_by(*order_by)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def get_templates_by_entity_type(
        self,
        entity_type,
        exclude_inactive=True,
        as_dict=False,
        projection=None,
        order_by=None,
    ):
        if not isinstance(entity_type, list):
            entity_type = [entity_type]
        qs = self.client_kd_aware().filter(entity_type__in=entity_type)
        if exclude_inactive:
            qs = qs.exclude(is_active=False)
        if order_by:
            qs = qs.order_by(*order_by)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def does_template_id_exist(self, template_id):
        return self.client_kd_aware().filter(template_id=template_id).exists()

    def get_template_by_id(self, template_id, as_dict=False, projection=None):
        qs = self.client_kd_aware().filter(template_id=template_id)
        if projection:
            return qs.values(*projection).first()
        return qs.values().first() if as_dict else qs.first()

    def get_template_names_by_ids(self, template_ids):
        query = (
            self.client_kd_aware()
            .filter(template_id__in=template_ids)
            .values_list("template_name", flat=True)
        )

        return list(query)

    def invalidate_template_by_id(self, template_id, ked):
        return (
            self.client_kd_aware()
            .filter(template_id=template_id)
            .update(knowledge_end_date=ked)
        )

    def update_template(self, template_id, updated_template_data, kd):
        self.invalidate_template_by_id(template_id, kd)
        return self.persist_approval_template(updated_template_data)

    def existing_template_names(self):
        return list(self.client_kd_aware().values_list("template_name", flat=True))

    def does_notify_on_reject_group_exist(self, group_id):
        return (
            self.client_kd_aware()
            .filter(notify_on_reject__groups__contains=group_id)
            .exists()
        )

    def does_notify_on_reject_user_exist(self, email_id):
        return (
            self.client_kd_aware()
            .filter(notify_on_reject__users__contains=email_id)
            .exists()
        )

    def does_notify_on_approve_user_exist(self, email_id):
        return (
            self.client_kd_aware()
            .filter(notify_on_approve__users__contains=email_id)
            .exists()
        )

    def get_template_names_for_notifier(self, email_id):
        qs = (
            self.client_kd_aware()
            .filter(
                Q(notify_on_reject__users__contains=email_id)
                | Q(notify_on_approve__users__contains=email_id)
            )
            .values_list("template_name", flat=True)
        )
        return list(qs)

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return (
            self.client_aware()
            .filter(knowledge_begin_date__gte=last_sync_time)
            .exists()
        )


class ApprovalTemplateStageAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApprovalTemplateStage.objects.filter(client=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            knowledge_end_date__isnull=True, is_deleted=False
        )

    def persist_approval_template(self, data):
        return data.save()

    def bulk_persist(self, stages):
        return ApprovalTemplateStage.objects.bulk_create(stages)

    def bulk_create_template_stages(self, stages_list):
        stages_object_list = [ApprovalTemplateStage(**stage) for stage in stages_list]
        return self.bulk_persist(stages_object_list)

    def get_stages_by_stage_ids(
        self, stage_template_ids, projection=None, order_by=None
    ):
        if not projection:
            projection = []
        qs = self.client_kd_aware().filter(stage_template_id__in=stage_template_ids)
        if order_by:
            qs = qs.order_by(*order_by)
        return qs.values(*projection)

    def get_stages_by_template_id(
        self, template_id, projection=None, as_dict=False, order_by=None
    ):
        qs = self.client_kd_aware().filter(template_id=template_id)
        if order_by:
            qs = qs.order_by(*order_by)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def invalidate_stages(self, template_id, kd):
        return (
            self.client_kd_aware()
            .filter(template_id=template_id)
            .update(knowledge_end_date=kd)
        )

    def bulk_update_stages(self, template_id, stages_list, kd):
        self.invalidate_stages(template_id, kd)
        return self.bulk_create_template_stages(stages_list)

    def does_approvers_group_exist(self, group_id):
        return (
            self.client_kd_aware().filter(approvers__groups__contains=group_id).exists()
        )

    def does_approvers_user_exist(self, email_id):
        return (
            self.client_kd_aware().filter(approvers__users__contains=email_id).exists()
        )

    def get_stages_by_template_ids(self, template_ids):
        qs = self.client_kd_aware().filter(template_id__in=template_ids)
        return list(qs.values())

    def get_all_template_stages(
        self, filters=None, projection=None, order_by=None, as_dict=True
    ):
        if not projection:
            projection = []
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if order_by:
            qs = qs.order_by(*order_by)
        return list(qs.values(*projection)) if as_dict else list(qs)

    def get_template_id_for_approver(self, email_id):
        qs = (
            self.client_kd_aware()
            .filter(approvers__users__contains=email_id)
            .values_list("template_id", flat=True)
        )
        return list(qs)


class ApprovalInstanceAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApprovalInstance.objects.filter(client=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_active_kd_aware(self):
        return self.client_kd_aware().filter(is_active=True)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_instance_by_entity_key(self, entity_key, projection=None):
        if not projection:
            projection = []
        return list(
            self.client_kd_aware()
            .filter(entity_key=entity_key)
            .order_by("knowledge_begin_date")
            .values(*projection)
        )

    def get_instance_by_partial_entity_key(
        self, entity_key, entity_type, projection=None
    ):
        if not projection:
            projection = []
        return list(
            self.client_kd_aware()
            .filter(entity_key__contains=entity_key, entity_type=entity_type)
            .order_by("knowledge_begin_date")
            .values(*projection)
        )

    def persist_instance(self, data):
        return data.save()

    def bulk_persist(self, instances):
        return ApprovalInstance.objects.bulk_create(instances, batch_size=1000)

    def set_is_active_false_for_records_by_entity_key(self, entity_key):
        if isinstance(entity_key, list):
            return (
                self.client_kd_aware()
                .filter(entity_key__in=entity_key, is_active=True)
                .update(is_active=False)
            )
        return (
            self.client_kd_aware()
            .filter(entity_key=entity_key, is_active=True)
            .update(is_active=False)
        )

    def invalidate_instance(self, instance_id, knowledge_date):
        if isinstance(instance_id, list):
            qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_id)
        else:
            qs = self.client_kd_aware().filter(approval_wf_instance_id=instance_id)

        unique_entity_keys = list(qs.values_list("entity_key", flat=True).distinct())
        self.set_is_active_false_for_records_by_entity_key(unique_entity_keys)

        return qs.update(knowledge_end_date=knowledge_date)

    def get_instance_by_id(self, instance_id, projection=None):
        if not projection:
            projection = []
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id=instance_id)
            .values(*projection)
            .first()
        )

    def get_instance_by_id_object(self, instance_id):
        return (
            self.client_kd_aware().filter(approval_wf_instance_id=instance_id).first()
        )

    def get_bulk_instance_by_ids(self, instance_id, projection=None):
        if not projection:
            projection = []
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_id)
            .values(*projection)
        )

    def get_bulk_instance_by_ids_queryset(self, instance_ids):
        if not isinstance(instance_ids, list):
            return list(
                self.client_kd_aware().filter(approval_wf_instance_id=instance_ids)
            )
        return list(
            self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        )

    def update_instance_status(
        self, instance_id, status, knowledge_date, update_completion_time=False
    ):
        record = self.get_instance_by_id_object(instance_id)
        if record:
            self.invalidate_instance(instance_id, knowledge_date)
            record.pk = None
            record.knowledge_begin_date = knowledge_date
            record.status = status
            record.is_active = True
            if update_completion_time:
                record.completion_time = knowledge_date
            self.persist_instance(record)

    def bulk_update_instance_status(
        self,
        instance_ids,
        status,
        knowledge_date,
        is_system_action=False,
        update_completion_time=False,
        additional_details=None,
    ):
        all_record = self.get_bulk_instance_by_ids_queryset(instance_ids)
        if all_record:
            self.invalidate_instance(instance_ids, knowledge_date)
            for record in all_record:
                record.pk = None
                record.knowledge_begin_date = knowledge_date
                record.additional_details = additional_details
                record.status = status
                record.is_system_action = is_system_action
                record.is_active = True
                if update_completion_time:
                    record.completion_time = knowledge_date
            return self.bulk_persist(all_record)

    def get_unique_entity_keys_for_instance_ids(self, instance_ids):
        return list(
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .values_list("entity_key", flat=True)
            .distinct()
        )

    def does_active_instance_exist_for_entity_keys_entity_type(
        self, entity_keys, status=None, entity_type=None
    ):
        qs = self.client_kd_aware().filter(entity_key__in=entity_keys)
        if status:
            qs = qs.filter(status__in=status)
        if entity_type:
            qs = qs.filter(entity_type=entity_type)
        return qs.exists()

    def get_approval_instance_by_ids(self, instance_ids, projection=None, as_dict=True):
        qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        if projection:
            list(qs.values(projection))
        return list(qs.values()) if as_dict else list(qs)

    def get_instances_by_entity_keys_entity_type(
        self, entity_keys, entity_type=None, projection=None, as_dict=True
    ):
        qs = self.client_kd_aware().filter(entity_key__in=entity_keys)
        if entity_type:
            qs = qs.filter(entity_type=entity_type)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def get_active_instances_by_entity_keys_entity_type(self, entity_keys, entity_type):
        return list(
            self.client_active_kd_aware()
            .filter(entity_key__in=entity_keys)
            .filter(entity_type=entity_type)
            .values()
        )

    def get_instance_ids_by_entity_keys(self, entity_keys):
        return list(
            self.client_kd_aware()
            .filter(entity_key__in=entity_keys)
            .values_list("approval_wf_instance_id", flat=True)
        )

    def bulk_create_instances(self, instances_list):
        instances_object_list = [ApprovalInstance(**stage) for stage in instances_list]
        return self.bulk_persist(instances_object_list)

    def bulk_invalidate_by_instance_ids(self, instance_ids, ked):
        qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        unique_entity_keys = list(qs.values_list("entity_key", flat=True).distinct())
        self.set_is_active_false_for_records_by_entity_key(unique_entity_keys)
        return qs.update(knowledge_end_date=ked)

    def bulk_delete_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked, is_deleted=True, is_active=False)
        )

    def bulk_invalidate_by_entity_keys(self, entity_keys, ked):
        return (
            self.client_active_kd_aware()
            .filter(entity_key__in=entity_keys)
            .update(knowledge_end_date=ked)
        )

    def get_instances_by_entity_keys_and_status_and_entity_type(
        self, entity_keys, status, entity_type, as_dict=True
    ):
        subquery = (
            ApprovalInstance.objects.filter(
                client_id=self.client_id,
                entity_key__in=entity_keys,
                status__in=status,
                entity_type=entity_type,
                knowledge_end_date__isnull=True,
            )
            .values("entity_key")
            .annotate(max_kbd=Max("knowledge_begin_date"))
            .filter(
                entity_key=OuterRef("entity_key"),
                max_kbd=OuterRef("knowledge_begin_date"),
            )
        )

        qs = ApprovalInstance.objects.filter(Exists(subquery))
        return list(qs) if not as_dict else list(qs.values())

    def get_latest_instances_by_entity_keys(self, entity_keys):
        subquery = (
            self.client_kd_aware()
            .filter(entity_key__in=entity_keys)
            .values("entity_key")
            .annotate(max_kbd=Max("knowledge_begin_date"))
            .filter(
                entity_key=OuterRef("entity_key"),
                max_kbd=OuterRef("knowledge_begin_date"),
            )
        )
        return list(
            ApprovalInstance.objects.filter(Exists(subquery)).values(
                "entity_key",
                "status",
                "entity_type",
                "approval_wf_instance_id",
                "additional_details",
                "completion_time",
                "requested_time",
            )
        )

    def get_latest_status_by_period_and_status(self, ped):
        subquery = (
            self.client_kd_aware()
            .filter(entity_key__contains=ped)
            .values("entity_key")
            .annotate(max_kbd=Max("knowledge_begin_date"))
            .filter(
                entity_key=OuterRef("entity_key"),
                max_kbd=OuterRef("knowledge_begin_date"),
            )
        )

        return list(
            ApprovalInstance.objects.filter(Exists(subquery)).values(
                "entity_key",
                "status",
                "entity_type",
                "approval_wf_instance_id",
                "additional_details",
            )
        )

    def get_all_workflow_ids_list(self, filters=None):
        qs = self.client_kd_aware()
        if filters:
            # Check if "entity_type" is provided in the filters and is a list
            entity_types = filters.get("entity_types", [])
            if entity_types and isinstance(entity_types, list):
                # Use __in operator for filtering with a list of entity types
                qs = qs.filter(entity_type__in=entity_types)
                del filters[
                    "entity_types"
                ]  # Remove the "entity_type" from filters to avoid duplicate filtering
            # Apply other filters
            qs = qs.filter(**filters)
        return list(qs.values_list("approval_wf_instance_id", flat=True))

    def get_adjustment_ids_by_instance_ids_for_comm_adj(self, instance_ids):
        return list(
            self.client_kd_aware()
            .filter(
                approval_wf_instance_id__in=instance_ids,
                entity_type=APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value,
            )
            .values_list("entity_key", flat=True)
        )

    def check_adj_instance_exists_for_comm_adj(self, entity_key):
        return (
            self.client_kd_aware()
            .filter(
                entity_key=entity_key,
                entity_type=APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value,
            )
            .exists()
        )

    def get_all_wf_instances(
        self, filters=None, qs_filter=None, projection=None, order_by=None, as_dict=True
    ):
        if not projection:
            projection = []
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if qs_filter:
            qs = qs.filter(qs_filter)
        if order_by:
            qs = qs.order_by(*order_by)
        return list(qs.values(*projection)) if as_dict else list(qs)

    def get_approval_instance_with_entity_keys_and_status(
        self, entity_keys, status, entity_type
    ):
        return list(
            self.client_kd_aware()
            .filter(
                entity_key__in=entity_keys, status__in=status, entity_type=entity_type
            )
            .values("entity_key")
        )

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return (
            self.client_aware()
            .filter(
                Q(knowledge_begin_date__gte=last_sync_time)
                | Q(knowledge_end_date__gte=last_sync_time)
            )
            .exists()
        )


class ApprovalInstanceStageAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApprovalInstanceStage.objects.filter(client=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def get_approval_stage_instance_by_stage_instance_id(
        self, stage_instance_id, projection=None, as_dict=True
    ):
        qs = self.client_kd_aware().filter(stage_instance_id=stage_instance_id)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def get_approval_stage_instances_by_wf_instance_id(
        self, wf_instance_id, projection=None, order_by=None, as_dict=None
    ):
        qs = self.client_kd_aware().filter(approval_wf_instance_id=wf_instance_id)
        if order_by:
            qs = qs.order_by(*order_by)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def bulk_persist(self, instance_stages):
        return ApprovalInstanceStage.objects.bulk_create(instance_stages)

    def bulk_create_instance_stages(self, instances_list):
        instances_object_list = [
            ApprovalInstanceStage(**stage) for stage in instances_list
        ]
        return self.bulk_persist(instances_object_list)

    def get_stage_instances_by_instance_id_status(
        self, instance_ids, status, projection=None, as_dict=None
    ):
        qs = self.client_kd_aware().filter(
            approval_wf_instance_id__in=instance_ids, status__in=status
        )
        if projection:
            return list(qs.values(projection))
        return list(qs.values()) if as_dict else list(qs)

    def bulk_invalidate_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked)
        )

    def bulk_delete_stages_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked, is_deleted=True)
        )

    def bulk_invalidate_by_stage_instance_ids(self, stage_instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_instance_ids)
            .update(knowledge_end_date=ked)
        )

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def persist_instance_stage(self, data):
        return data.save()

    def invalidate_stage(self, stage_id, knowledge_date):
        if isinstance(stage_id, list):
            return (
                self.client_kd_aware()
                .filter(stage_instance_id__in=stage_id)
                .update(knowledge_end_date=knowledge_date)
            )
        return (
            self.client_kd_aware()
            .filter(stage_instance_id=stage_id)
            .update(knowledge_end_date=knowledge_date)
        )

    def bulk_update_stage(self, stage_id, updated_stage_data, kd):
        self.invalidate_stage(stage_id, kd)
        return self.bulk_persist(updated_stage_data)

    def update_stage(self, stage_id, updated_stage_data, kd):
        self.invalidate_stage(stage_id, kd)
        return self.persist_instance_stage(updated_stage_data)

    def get_instance_stage_by_id(self, stage_instance_id, projection=None):
        if not projection:
            projection = []
        return (
            self.client_kd_aware()
            .filter(stage_instance_id=stage_instance_id)
            .values(*projection)
            .first()
        )

    def get_instance_stage_by_id_object(self, stage_instance_id):
        return (
            self.client_kd_aware().filter(stage_instance_id=stage_instance_id).first()
        )

    def get_bulk_instance_stage_by_ids(self, stage_instance_ids, projection=None):
        if not projection:
            projection = []
        return list(
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_instance_ids)
            .values(*projection)
        )

    def get_bulk_instance_stage_by_ids_queryset(self, stage_instance_ids):
        return list(
            self.client_kd_aware().filter(stage_instance_id__in=stage_instance_ids)
        )

    def get_instance_stage_by_instance_id(self, instance_ids, projection=None):
        if not projection:
            projection = []
        qs = self.client_kd_aware()
        if isinstance(instance_ids, list):
            qs = qs.filter(approval_wf_instance_id__in=instance_ids)
        else:
            qs = qs.filter(approval_wf_instance_id=instance_ids)
        return list(qs.order_by("stage_order").values(*projection))

    def get_instance_stage_by_instance_id_queryobject(self, instance_ids, status=None):
        qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        if status:
            qs = qs.filter(status=status)
        return list(qs)

    def get_max_stage_order(self, instance_id):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id=instance_id)
            .aggregate(Max("stage_order"))["stage_order__max"]
        )

    def get_max_stage_order_group_by_instances(self, instance_ids):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .values("approval_wf_instance_id")
            .annotate(max_order=Max("stage_order"))
        )

    def get_next_stage(self, instance_id):
        return (
            self.client_kd_aware()
            .filter(
                approval_wf_instance_id=instance_id,
                status=APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
            )
            .order_by("stage_order")
            .first()
        )

    def get_next_stage_group_by_instance(self, instance_id):
        return (
            self.client_kd_aware()
            .filter(
                approval_wf_instance_id__in=instance_id,
                status=APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
            )
            .order_by("approval_wf_instance_id", "stage_order")
            .distinct("approval_wf_instance_id")
        )

    def get_previous_stage_by_instance_id_and_order(
        self, instance_id, stage_order, projection=None
    ):
        if not projection:
            projection = []
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id=instance_id, stage_order=stage_order)
            .values(*projection)
            .first()
        )

    def get_auto_approve_due_date_breached_instance_stages(self, ked):
        return list(
            self.client_kd_aware().filter(
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                is_auto_approve=True,
                due_date__lte=ked,
            )
        )

    def get_stage_count_by_instance_id_and_status(self, instance_ids, status=None):
        if not isinstance(instance_ids, list):
            instance_ids = [instance_ids]
        qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        if status:
            qs = qs.filter(status=status)
        return qs.values("approval_wf_instance_id").annotate(
            count=Count("approval_wf_instance_id")
        )

    # function to get stage id and status by instance ids
    def get_stage_instances_by_instance_ids(self, instance_ids, projection=None):
        if not projection:
            projection = []
        if not isinstance(instance_ids, list):
            instance_ids = [instance_ids]
        qs = self.client_kd_aware().filter(approval_wf_instance_id__in=instance_ids)
        return qs.values(*projection)

    def get_all_stage_ids_list(self, filters=None, projection=None):
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if not projection:
            return list(qs.values_list("stage_instance_id", flat=True))

        return list(qs.values(*projection))

    def get_approvers_by_instance_id(self, instance_id):
        return list(
            self.client_kd_aware()
            .filter(approval_wf_instance_id=instance_id)
            .values_list("approvers", flat=True)
        )

    def get_stage_by_entity_keys(self, entity_keys, projection=None):
        if not projection:
            projection = []
        # Build the Q object for entity_key contains for each item in the list
        entity_key_filter = Q()
        for key in entity_keys:
            entity_key_filter |= Q(entity_key__icontains=key)

        # Subquery to get `approval_wf_instance_id` from `ApprovalWfInstance`
        subquery = (
            ApprovalInstance.objects.filter(client_id=self.client_id)
            .filter(entity_key_filter)
            .values("approval_wf_instance_id")
        )

        # Query to get `stage_instance_id`, `approvers`, and `status`
        results = (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=Subquery(subquery))
            .values(*projection)
            .order_by("stage_order")
        )
        return list(results)

    def get_all_stage_instances(
        self, filters=None, projection=None, order_by=None, as_dict=True
    ):
        if not projection:
            projection = []
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if order_by:
            qs = qs.order_by(*order_by)
        return list(qs.values(*projection)) if as_dict else list(qs)

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return (
            self.client_aware()
            .filter(
                Q(knowledge_begin_date__gte=last_sync_time)
                | Q(knowledge_end_date__gte=last_sync_time)
            )
            .exists()
        )


class ApprovalRequestsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApprovalRequests.objects.filter(client=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def persist_approval_request(self, data):
        return data.save()

    def invalidate_request(self, request_ids, knowledge_date):
        if not isinstance(request_ids, list):
            return (
                self.client_kd_aware()
                .filter(approval_request_id=request_ids)
                .update(knowledge_end_date=knowledge_date)
            )
        return (
            self.client_kd_aware()
            .filter(approval_request_id__in=request_ids)
            .update(knowledge_end_date=knowledge_date)
        )

    def bulk_delete_requests_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked, is_deleted=True)
        )

    def invalidate_request_by_stage_and_status(
        self, stage_id, knowledge_date, status=None
    ):
        if not isinstance(stage_id, list):
            stage_id = [stage_id]
        qs = self.client_kd_aware().filter(stage_instance_id__in=stage_id)
        if status:
            qs = qs.filter(status=status)
        return qs.update(knowledge_end_date=knowledge_date)

    def get_request_by_id(self, request_id):
        return self.client_kd_aware().filter(approval_request_id=request_id).first()

    def get_requests_by_approver_and_stage(
        self, stage_instance_ids, approvers, projection=None
    ):
        if not projection:
            projection = []
        if not isinstance(approvers, list):
            approvers = [approvers]
        if not isinstance(stage_instance_ids, list):
            stage_instance_ids = [stage_instance_ids]
        return list(
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_instance_ids, approver__in=approvers)
            .order_by("completed_time")
            .values(*projection)
        )

    def update_request(self, request_id, updated_request_data, kd):
        self.invalidate_request(request_id, kd)
        return self.persist_approval_request(updated_request_data)

    def bulk_update_request(self, request_id, updated_request_data, kd):
        self.invalidate_request(request_id, kd)
        return self.bulk_persist(updated_request_data)

    def bulk_persist(self, request_list):
        return ApprovalRequests.objects.bulk_create(request_list)

    def get_request_by_stage_id(self, stage_id):
        return self.client_kd_aware().filter(stage_instance_id=stage_id).first()

    def get_request_by_stage_id_status_and_req_id(
        self, stage_id, status=None, request_ids=None, projection=None
    ):
        if not projection:
            projection = []
        if isinstance(stage_id, list):
            qs = self.client_kd_aware().filter(stage_instance_id__in=stage_id)
        else:
            qs = self.client_kd_aware().filter(stage_instance_id=stage_id)
        if status:
            qs = qs.filter(status=status)
        if request_ids:
            # We need to fetch all requests both of entity_type - payout and payout_line_item entity_type
            # For payout entity_type, status should be requested, this condition is enough to filter out payout requests
            # For payout_line_item entity_type, we need to check if any one of the record in sub_approval_request table is in requested status
            # So we query sub_approval_request and get line_item_request_ids
            qs = qs.filter(
                Q(approval_request_id__in=request_ids) | Q(entity_type="payout")
            )
        else:
            qs = qs.filter(entity_type="payout")
        return list(qs.values(*projection))

    def get_request_by_stage_id_and_status_object(
        self, stage_id, status=None, projection=None
    ):
        if isinstance(stage_id, list):
            qs = self.client_kd_aware().filter(stage_instance_id__in=stage_id)
        else:
            qs = self.client_kd_aware().filter(stage_instance_id=stage_id)
        if status:
            qs = qs.filter(status=status)
        if projection:
            return list(qs.values(*projection))
        return list(qs)

    def get_request_count_by_stage_id_and_status(self, stage_id, status=None):
        qs = self.client_kd_aware().filter(stage_instance_id=stage_id)
        if status:
            qs = qs.filter(status=status)
        return qs.count()

    def check_if_requests_exists_with_stage_id_status(self, stage_id, status=None):
        qs = self.client_kd_aware().filter(stage_instance_id=stage_id)
        if status:
            qs = qs.filter(status=status)
        return qs.exists()

    def get_all_requests_count(
        self, approver_email=None, status=None, entity_type=None
    ):
        qs = self.client_kd_aware()
        if approver_email:
            qs = qs.filter(approver=approver_email)
        if status:
            qs = qs.filter(status=status)
        if entity_type:
            qs = qs.filter(entity_type=entity_type)
        return qs.count()

    def get_request_by_entity_key(self, entity_key, approver, status=None):
        qs = self.client_kd_aware()
        if status:
            qs = qs.filter(status=status)
        return qs.filter(entity_key=entity_key, approver=approver).first()

    def withdraw_stage_requests(
        self, stage_id, knowledge_date, is_system_action=False, audit=None
    ):
        all_requests = self.get_request_by_stage_id_and_status_object(
            stage_id, APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        )
        if all_requests:
            self.invalidate_request_by_stage_and_status(
                stage_id,
                knowledge_date,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            )
            for request in all_requests:
                request.pk = None
                request.status = APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value
                request.is_system_action = is_system_action
                request.knowledge_begin_date = knowledge_date
                request.completed_time = knowledge_date
                request.additional_details = audit
            self.bulk_persist(all_requests)

    def bulk_create_approval_requests(self, approval_requests_list):
        request_objects_list = [
            ApprovalRequests(**request) for request in approval_requests_list
        ]
        return self.bulk_persist(request_objects_list)

    def get_requests_by_instance_id_status(
        self, instance_ids, status, projection=None, as_dict=None
    ):
        qs = self.client_kd_aware().filter(
            approval_wf_instance_id__in=instance_ids, status__in=status
        )
        if projection:
            return list(qs.values(*projection))
        return list(qs.values()) if as_dict else list(qs)

    def bulk_invalidate_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked)
        )

    def bulk_invalidate_by_request_ids(self, request_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_request_id__in=request_ids)
            .update(knowledge_end_date=ked)
        )

    def get_bulk_request_by_ids(self, request_ids, status=None, projection=None):
        if not projection:
            projection = []
        qs = self.client_kd_aware().filter(approval_request_id__in=request_ids)
        if status:
            qs = qs.filter(status=status)
        return qs.values(*projection)

    def get_bulk_request_by_ids_queryset(self, request_ids):
        return list(self.client_kd_aware().filter(approval_request_id__in=request_ids))

    def get_request_by_stage_ids_queryobject(self, stage_ids, request_ids, status=None):
        qs = (
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_ids)
            .exclude(approval_request_id__in=request_ids)
        )
        if status:
            qs = qs.filter(status=status)
        return list(qs)

    def get_request_by_instance_ids_queryobject(
        self, instance_ids, request_ids, status=None
    ):
        qs = (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .exclude(approval_request_id__in=request_ids)
        )
        if status:
            qs = qs.filter(status=status)
        return list(qs)

    def get_request_count_group_by_stage_id_and_status(self, stage_ids, status=None):
        qs = self.client_kd_aware().filter(stage_instance_id__in=stage_ids)
        if status:
            qs = qs.filter(status=status)
        return qs.values("stage_instance_id").annotate(total=Count("stage_instance_id"))

    def get_request_ids_by_status_and_stage_instance_ids(self, stage_ids, status):
        return list(
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_ids, status__in=status)
            .values_list("approval_request_id", flat=True)
        )

    def get_pending_requests_count_for_approvers(self, entity_type):
        return list(
            self.client_kd_aware()
            .filter(
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                entity_type=entity_type,
            )
            .values("approver")
            .annotate(count=Count("approver"))
        )

    def get_pending_requests_count_for_approvers_by_entity_type(self, entity_type):
        return list(
            self.client_kd_aware()
            .filter(
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                entity_type=entity_type,
            )
            .values("approver")
            .annotate(count=Count("approver"))
        )

    def get_distinct_approver_by_entity_type(self, entity_type):
        return list(
            self.client_kd_aware()
            .filter(
                entity_type=entity_type,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            )
            .values_list("approver", flat=True)
            .distinct()
        )

    def get_all_approvers_by_entity_key(self, entity_key):
        return list(
            self.client_kd_aware()
            .filter(entity_key=entity_key)
            .values_list("approver", flat=True)
            .distinct()
        )

    def get_request_entity_type_by_partial_entity_key(self, entity_key):
        qs = self.client_kd_aware()
        return (
            qs.filter(entity_key__contains=entity_key)
            .order_by("-knowledge_begin_date")
            .values_list("entity_type", flat=True)
            .first()
        )

    def get_instance_by_partial_entity_key(
        self, entity_key, entity_type, projection=None
    ):
        if not projection:
            projection = []
        return list(
            self.client_kd_aware()
            .filter(entity_key__contains=entity_key, entity_type=entity_type)
            .order_by("-knowledge_begin_date")
            .values(*projection)
        )

    def get_valid_request_ids_for_approver(self, approver, request_ids):
        qs = self.client_kd_aware().filter(
            approval_request_id__in=request_ids, approver=approver
        )
        return list(qs.values_list("approval_request_id", flat=True))

    def get_approval_requests_by_entity_keys(self, entity_key, status=None):
        qs = self.client_kd_aware()
        if status:
            qs = qs.filter(status__in=status)
        return qs.filter(entity_key__in=entity_key).values(
            "approval_request_id",
            "approval_wf_instance_id",
            "stage_instance_id",
            "approver",
            "entity_key",
            "status",
        )

    def get_approval_request_ids_by_entity_keys_status(self, entity_key, status=None):
        qs = self.client_kd_aware()
        if status:
            qs = qs.filter(status__in=status)
        return list(
            qs.filter(entity_key__in=entity_key).values_list(
                "approval_request_id", flat=True
            )
        )

    # function to get request ids and status by stage ids
    def get_request_ids_and_status_by_stage_ids(self, stage_ids, projection=None):
        if not projection:
            projection = []
        return list(
            self.client_kd_aware()
            .filter(stage_instance_id__in=stage_ids)
            .values(*projection)
        )

    def get_request_ids_by_stage_and_approver(self, stage_id, approver):
        return list(
            self.client_kd_aware()
            .filter(stage_instance_id=stage_id, approver=approver)
            .values_list("approval_request_id", flat=True)
        )

    def get_all_request_ids_list(self, filters=None, projection=None):
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if projection:
            return list(qs.values(*projection))
        return list(qs.values_list("approval_request_id", flat=True))

    def get_approved_requests_count_by_instance_ids(self, instance_ids, as_dict=False):
        """
        Get count of approved requests grouped by instance ID.
        """
        counts = (
            self.client_kd_aware()
            .filter(
                approval_wf_instance_id__in=instance_ids,
                status=APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            )
            .values("approval_wf_instance_id")
            .annotate(count=Count("approval_wf_instance_id"))
        )
        if as_dict:
            return {item["approval_wf_instance_id"]: item["count"] for item in counts}
        return counts

    def check_if_request_id_exists_by_status(self, request_ids: list, status: str):
        return (
            self.client_kd_aware()
            .filter(status=status)
            .filter(approval_request_id__in=request_ids)
            .exists()
        )

    def get_adj_aprrovals_count_for_approver(
        self, approver_email, entity_type, status=None
    ):
        qs = (
            self.client_kd_aware()
            .filter(approver=approver_email)
            .filter(entity_type=entity_type)
        )
        if status:
            qs = qs.filter(status=status)
        return qs.count()

    def get_entity_key_by_request_id(self, request_id):
        return (
            self.client_kd_aware()
            .filter(approval_request_id=request_id)
            .values()
            .first()
        )

    def get_request_ids_by_entity_key(self, quote_id, approver):
        return list(
            self.client_kd_aware()
            .filter(
                entity_key__contains=quote_id,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                approver=approver,
            )
            .values_list("approval_request_id", flat=True)
        )

    def get_all_requests(
        self, filters=None, projection=None, order_by=None, as_dict=True
    ):
        if not projection:
            projection = []
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        if order_by:
            qs = qs.order_by(*order_by)
        return list(qs.values(*projection)) if as_dict else list(qs)

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return (
            self.client_aware()
            .filter(
                Q(knowledge_begin_date__gte=last_sync_time)
                | Q(knowledge_end_date__gte=last_sync_time)
            )
            .exists()
        )


class SubApprovalRequestsAccessor:
    def __init__(self, client_id) -> None:
        self.client_id = client_id

    def client_aware(self):
        return SubApprovalRequests.objects.filter(client=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def persist_sub_approval_request(self, data):
        return data.save()

    def invalidate_request_by_sub_request_id(self, sub_request_ids, knowledge_date):
        if not isinstance(sub_request_ids, list):
            return (
                self.client_kd_aware()
                .filter(sub_request_id=sub_request_ids)
                .update(knowledge_end_date=knowledge_date)
            )
        return (
            self.client_kd_aware()
            .filter(sub_request_id__in=sub_request_ids)
            .update(knowledge_end_date=knowledge_date)
        )

    def bulk_delete_requests_by_instance_ids(self, instance_ids, ked):
        return (
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .update(knowledge_end_date=ked, is_deleted=True)
        )

    def invalidate_request_by_approval_request_id(self, request_ids, knowledge_date):
        if not isinstance(request_ids, list):
            return (
                self.client_kd_aware()
                .filter(request_id=request_ids)
                .update(knowledge_end_date=knowledge_date)
            )
        return (
            self.client_kd_aware()
            .filter(request_id__in=request_ids)
            .update(knowledge_end_date=knowledge_date)
        )

    def get_sub_approval_request_by_id(self, sub_request_id):
        return self.client_kd_aware().filter(sub_request_id=sub_request_id).first()

    def get_sub_requests_by_approval_request_id(self, request_id):
        return self.client_kd_aware().filter(request_id=request_id)

    def get_sub_rquest_by_instance_ids(self, instance_ids):
        return list(
            self.client_kd_aware()
            .filter(approval_wf_instance_id__in=instance_ids)
            .values()
        )

    def get_pending_requests_count_for_approvers(self):
        return list(
            self.client_kd_aware()
            .filter(
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            )
            .values("approver")
            .annotate(count=Count("approver"))
        )

    def update_is_system_action(self, sub_request_id, is_system_action):
        return (
            self.client_kd_aware()
            .filter(sub_request_id=sub_request_id)
            .update(is_system_action=is_system_action)
        )

    def update_request_by_sub_request_id(self, request_id, updated_request_data, kd):
        self.invalidate_request_by_sub_request_id(request_id, kd)
        return self.persist_sub_approval_request(updated_request_data)

    def update_request_by_approval_request_id(
        self, request_id, updated_request_data, kd
    ):
        self.invalidate_request_by_approval_request_id(request_id, kd)
        return self.persist_sub_approval_request(updated_request_data)

    def bulk_persist(self, instances):
        return SubApprovalRequests.objects.bulk_create(instances)

    def bulk_create_sub_approval_requests(self, sub_approval_requests_list):
        request_objects_list = [
            SubApprovalRequests(**request) for request in sub_approval_requests_list
        ]
        return self.bulk_persist(request_objects_list)

    # function to get sub requests and their status by request ids
    def get_sub_requests_by_request_ids(self, request_ids, status=None):
        qs = self.client_kd_aware().filter(request_id__in=request_ids)
        if status:
            qs = qs.filter(status=status)
        return list(qs.values())

    def get_sub_requests_by_sub_request_ids(self, request_ids):
        return list(self.client_kd_aware().filter(sub_request_id__in=request_ids))

    def get_sub_approval_requests_by_stage_ids(self, stage_ids):
        return list(self.client_kd_aware().filter(stage_instance_id__in=stage_ids))

    def get_sub_entity_ids_by_approver_and_period(
        self, ped, approver=None, stage_id=None, status=None, payees=None
    ):
        filter_clauses = Q()
        qs = self.client_kd_aware().filter(period_end_date=ped)
        if approver:
            qs = qs.filter(approver__in=approver)
        if stage_id is not None:
            qs = qs.filter(
                stage_instance_id=stage_id,
            )
        if status:
            qs = qs.filter(status=status)
        if payees:
            for payee in payees:
                filter_clauses |= Q(sub_entity_id__startswith=payee)
        qs = qs.filter(filter_clauses)
        return list(qs.values_list("sub_entity_id", flat=True))

    def get_approval_payees_by_approver_period_and_search_term(
        self,
        period,
        approver=None,
        search_term=None,
        status=None,
        limit=None,
        offset=None,
    ):
        """
        This accessor joins sub_approval_request table with employee table to get the payee name
        for whom the approver need to approve the line item level payouts

        Args:
            approver(str) - email_id of the login_user
            period(datetime) - payout period
        """
        base_query = """
            SELECT distinct e.employee_email_id as value,concat(e.first_name,' ',e.last_name) as label
            FROM sub_approval_request sar
            JOIN employee e ON e.employee_email_id  = split_part(sar.sub_entity_id, '::', 1)
            WHERE e.client_id = %(client_id)s and sar.client_id = %(client_id)s
            and e.knowledge_end_date is null
            and sar.knowledge_end_date is null 
            and sar.period_end_date = %(period)s
        """
        params = {"client_id": self.client_id, "period": period}
        if approver:
            if len(approver) == 1:
                base_query += " and sar.approver = %(approver)s "
                params["approver"] = approver[0]
            else:
                base_query += " and sar.approver in %(approver)s "
                params["approver"] = approver
        if search_term:
            search_term = search_term.lower() if search_term else None
            base_query += " and (lower(e.first_name) like %(search_term)s or lower(e.last_name) like %(search_term)s or e.employee_email_id like %(search_term)s) "
            params["search_term"] = search_term + "%"
        if status:
            base_query += " and sar.status = %(status)s "
            params["status"] = status
        base_query += " order by e.employee_email_id asc"
        if limit:
            offset = offset if offset else 0
            base_query += " limit %(limit)s offset %(offset)s"
            params["limit"] = limit
            params["offset"] = offset

        with connection.cursor() as cursor:
            cursor.execute(base_query, params)
            payee_details = cursor.fetchall()
            fields = [desc[0] for desc in cursor.description]
            result = [dict(zip(fields, payees)) for payees in payee_details]
            return result

    def get_all_requests_count_by_approver_and_status(
        self, approver_email=None, status=None
    ):
        qs = self.client_kd_aware()
        if approver_email:
            qs = qs.filter(approver=approver_email)
        if status:
            qs = qs.filter(status=status)
        return qs.count()

    def get_sub_entity_id_and_status(
        self,
        period_end_date,
        plan_id,
        criteria_id,
        approver=None,
        payees=None,
        stage_id=None,
        status=None,
        limit=None,
        offset=None,
        get_count=False,
    ):
        qs = self.client_kd_aware().filter(period_end_date=period_end_date)
        if approver:
            qs = qs.filter(approver__in=approver)
        if stage_id is not None:
            qs = qs.filter(stage_instance_id=stage_id)
        email_filters = Q()
        if payees is not None and len(payees) > 0:
            for email_id in payees:
                email_filters |= Q(
                    sub_entity_id__startswith=email_id
                    + "::"
                    + plan_id
                    + "::"
                    + criteria_id
                )
        else:
            email_filters = Q(sub_entity_id__contains=plan_id + "::" + criteria_id)
        qs = qs.filter(email_filters)
        if status:
            qs = qs.filter(status__in=status)
        else:
            # If status is None, return all records except NOT_STARTED status
            qs = qs.exclude(status=APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value)
        qs = qs.order_by("sub_entity_id")
        if limit:
            offset = offset if offset else 0
            qs = qs[offset : offset + limit]
        if get_count:
            return qs.count()
        return list(
            qs.values(
                "sub_entity_id",
                "status",
                "sub_request_id",
                "sub_request_data",
                "comments",
                "approver",
                "stage_instance_id",
            )
        )

    def get_approval_request_ids_by_stage_and_status(self, stage_id, status):
        qs = self.client_kd_aware().filter(stage_instance_id=stage_id)
        if status:
            qs = qs.filter(status=status)
        return list(qs.values_list("request_id", flat=True).distinct())

    def get_approval_details(self, sub_request_ids):
        query = """
            select awi.approval_wf_instance_id,awi.status as approval_instance_status,
            ais.stage_instance_id,ais.status as approval_stage_status, ais.approval_strategy,
            ar.approval_request_id,ar.status as approval_request_status, 
            sar.sub_request_id,sar.status as sub_approval_request_status        
            from  sub_approval_request sar 
            inner join approval_request ar on sar.request_id = ar.approval_request_id 
            inner join approval_instance_stage ais on ar.stage_instance_id = ais.stage_instance_id 
            inner join approval_wf_instance awi on ais.approval_wf_instance_id = awi.approval_wf_instance_id
            where awi.client_id = %(client_id)s and awi.knowledge_end_date is null and awi.is_deleted = false
            and ais.client_id = %(client_id)s and ais.knowledge_end_date is null and ais.is_deleted = false
            and ar.client_id = %(client_id)s and ar.knowledge_end_date is null and ar.is_deleted = false
            and sar.client_id = %(client_id)s and sar.knowledge_end_date is null and sar.is_deleted = false
        """
        params = {
            "client_id": self.client_id,
        }
        if len(sub_request_ids) == 1:
            query += " and sar.sub_request_id = %(sub_request_ids)s ;"
            params["sub_request_ids"] = sub_request_ids[0]
        else:
            query += " and sar.sub_request_id in %(sub_request_ids)s ;"
            params["sub_request_ids"] = sub_request_ids
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            result: list = cursor.fetchall()
        if cursor.description and len(result) > 0:
            cols = [desc[0] for desc in cursor.description]
            return result, cols
        return [], []

    def get_all_sub_request_ids_list(self, filters=None):
        qs = self.client_kd_aware()
        if filters:
            qs = qs.filter(**filters)
        return list(qs.values_list("sub_request_id", flat=True))

    def check_sub_request_exists_for_plan_id(self, plan_id):
        return (
            self.client_kd_aware()
            .filter(sub_entity_id__contains=plan_id)
            .distinct()
            .values_list("request_id", flat=True)
        )

    def get_valid_request_ids_for_approver_by_request_ids(
        self, approver, sub_request_ids
    ):
        qs = self.client_kd_aware().filter(
            sub_request_id__in=sub_request_ids, approver=approver
        )
        return list(qs.values_list("sub_request_id", flat=True))

    def get_distinct_status_by_approver_period_payee(
        self, approver=None, period=None, payee=None
    ):
        qs = self.client_kd_aware()
        if approver:
            qs = qs.filter(approver__in=approver)
        if period:
            qs = qs.filter(period_end_date=period)
        if payee:
            qs = qs.filter(sub_entity_id__startswith=payee)
        return list(qs.values_list("status", flat=True).distinct())

    def get_distinct_approvers_by_status_period_payee(
        self,
        status=None,
        period=None,
        payee=None,
        search_term=None,
        approver=None,
        limit=None,
        offset=None,
    ):
        """
        This accessor joins sub_approval_request table with employee table to get the payee name
        for whom the approver need to approve the line item level payouts

        Args:
            approver(str) - email_id of the login_user
            period(datetime) - payout period
        """
        base_query = """
            SELECT distinct e.employee_email_id as value,concat(e.first_name,' ',e.last_name) as label
            FROM sub_approval_request sar
            JOIN employee e ON e.employee_email_id  = sar.approver
            WHERE e.client_id = %(client_id)s and sar.client_id = %(client_id)s
            and e.knowledge_end_date is null
            and sar.knowledge_end_date is null 
            and sar.period_end_date = %(period)s
        """
        params = {"client_id": self.client_id, "period": period}
        if status:
            base_query += " and sar.status = %(status)s "
            params["status"] = status
        if search_term:
            search_term = search_term.lower() if search_term else None
            base_query += " and (lower(e.first_name) like %(search_term)s or lower(e.last_name) like %(search_term)s or e.employee_email_id like %(search_term)s) "
            params["search_term"] = search_term + "%"
        if payee:
            base_query += " and sar.sub_entity_id like %(payee)s "
            params["payee"] = payee + "%"
        if approver:
            if len(approver) == 1:
                base_query += " and sar.approver = %(approver)s "
                params["approver"] = approver[0]
            else:
                base_query += " and sar.approver in %(approver)s "
                params["approver"] = approver
        base_query += " order by e.employee_email_id asc"
        if limit:
            offset = offset if offset else 0
            base_query += " limit %(limit)s offset %(offset)s"
            params["limit"] = limit
            params["offset"] = offset

        with connection.cursor() as cursor:
            cursor.execute(base_query, params)
            payee_details = cursor.fetchall()
            fields = [desc[0] for desc in cursor.description]
            result = [dict(zip(fields, payees)) for payees in payee_details]
            return result

    def get_req_ids_by_stage_id_and_status(self, stage_instance_ids, status):
        qs = self.client_kd_aware().filter(
            stage_instance_id__in=stage_instance_ids, status=status
        )
        return list(qs.values_list("approval_wf_instance_id", flat=True))

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return (
            self.client_aware()
            .filter(
                Q(knowledge_begin_date__gte=last_sync_time)
                | Q(knowledge_end_date__gte=last_sync_time)
            )
            .exists()
        )
