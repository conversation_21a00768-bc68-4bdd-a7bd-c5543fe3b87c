from datetime import datetime
from functools import reduce
from typing import Optional

from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import connection, transaction
from django.db.models import Q, QuerySet
from django.db.models.functions import Cast
from django.utils import timezone
from psycopg2.extras import execute_values

from commission_engine.utils.general_data import PlanModificationTypes
from interstage_project.db.models import EsTextField
from interstage_project.utils import log_me
from spm.accessors.accessor_templates.commission_plan_accessor_template import (
    CommissionPlanAccessorTemplate,
)
from spm.accessors.accessor_templates.plan_criteria_accessor_template import (
    PlanCriteriaAccessorTemplate,
)
from spm.accessors.accessor_templates.plan_docs_accessor_template import (
    PlanSupportingDocsAccessorTemplate,
)
from spm.accessors.accessor_templates.plan_payee_accessor_template import (
    PlanPayeeAccessorTemplate,
)
from spm.accessors.accessor_templates.temp_plan_docs_accessor_template import (
    TempPlanSupportingDocsAccessorTemplate,
)
from spm.models.commission_plan_models import (
    CommissionChanges,
    CommissionPlan,
    PlanCriteria,
    PlanDocs,
    PlanModificationChanges,
    PlanPayee,
    SettlementChanges,
    TempPlanDocs,
)


class CommissionPlanAccessor(CommissionPlanAccessorTemplate):
    def client_aware(self):
        if self.shared_plan_ids is None:
            return CommissionPlan.objects.filter(client=self.client_id)
        else:
            return CommissionPlan.objects.filter(
                client=self.client_id, plan_id__in=self.shared_plan_ids
            )

    def invalidate_plan_entries(self, plan_id, time):
        existing_records = self.client_aware().filter(
            knowledge_end_date__isnull=True, plan_id=plan_id
        )

        existing_records_copy = list(existing_records.values())

        for er in existing_records_copy:
            er["temporal_id"] = None
            er["knowledge_begin_date"] = time
            er["is_deleted"] = True

        existing_records.update(knowledge_end_date=time)

        CommissionPlan.objects.bulk_create(
            [CommissionPlan(**q) for q in existing_records_copy]
        )

    def update_commission_plan(self, ser, kd):
        latest_record = self.get_latest_commission_plan_by_id(
            ser.validated_data["plan_id"]
        )
        if latest_record:
            self.invalidate_latest(ser.validated_data["plan_id"], kd)
            latest_record.pk = None
            latest_record.knowledge_begin_date = kd
            latest_record.plan_name = ser.validated_data["plan_name"]
            latest_record.plan_start_date = ser.validated_data["plan_start_date"]
            latest_record.plan_end_date = ser.validated_data["plan_end_date"]
            latest_record.plan_type = ser.validated_data["plan_type"]
            latest_record.is_settlement_end_date = ser.validated_data[
                "is_settlement_end_date"
            ]
            latest_record.settlement_end_date = ser.validated_data[
                "settlement_end_date"
            ]
            latest_record.payout_frequency = ser.validated_data["payout_frequency"]
            self.persist_commission_plan(latest_record)

    def get_plan_criteria_names(self, criteria_ids):
        """
        Returns the plan_ids,plan_names,criteria_ids,criteria names for the given plan_ids and criteria_ids

        Args:
            criteria_ids - tuple of strings
        """
        base_query = """
            select cp.plan_id ,cp.plan_name ,cpc.criteria_id ,cpc.criteria_name,bool(cpc.criteria_data->>'is_line_item_level') as is_line_item_level  from commission_plan cp join commission_plan_criteria cpc on cp.plan_id = cpc.plan_id 
            where cp.client_id = %(client_id)s and cpc.client_id = %(client_id)s and cp.knowledge_end_date is null and cpc.knowledge_end_date is null 
            and cp.is_deleted = false and cpc.is_deleted = false
        """
        params = {"client_id": self.client_id}
        if len(criteria_ids) == 1:
            base_query += "and cpc.criteria_id = %(criteria_ids)s "
            params["criteria_ids"] = criteria_ids[0]
        else:
            base_query += "and cpc.criteria_id in %(criteria_ids)s "
            params["criteria_ids"] = criteria_ids
        base_query += " order by cp.plan_name;"
        with connection.cursor() as cursor:
            cursor.execute(base_query, params)
            records = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            result = [dict(zip(columns, row)) for row in records]
            return result


class PlanPayeeAccessor(PlanPayeeAccessorTemplate):
    def client_aware(self):
        return PlanPayee.objects.filter(client=self.client_id)

    def insert_spiff_plans(self, spiff_plans):
        try:
            PlanPayee.objects.bulk_create(spiff_plans)
        except Exception as e:
            raise e

    def bulk_create_plan_payees(self, plan_payees):
        PlanPayee.objects.bulk_create(plan_payees)


class PlanCriteriaAccessor(PlanCriteriaAccessorTemplate):
    def client_aware(self):
        return PlanCriteria.objects.filter(client=self.client_id)

    def get_names_by_ids(self, criteria_ids):
        return list(
            self.client_aware()
            .filter(criteria_id__in=criteria_ids)
            .values_list("criteria_id", "criteria_name")
        )

    @transaction.atomic
    def invalidate_plan_entries(self, plan_id, time):
        existing_records = self.client_aware().filter(
            knowledge_end_date__isnull=True, plan_id=plan_id
        )

        existing_records_copy = list(existing_records.values())

        for er in existing_records_copy:
            er["temporal_id"] = None
            er["knowledge_begin_date"] = time
            er["is_deleted"] = True

        existing_records.update(knowledge_end_date=time)

        PlanCriteria.objects.bulk_create(
            [PlanCriteria(**q) for q in existing_records_copy]
        )


class PlanSupportingDocsAccessor(PlanSupportingDocsAccessorTemplate):
    def client_aware(self):
        return PlanDocs.objects.filter(client=self.client_id)

    def upload_docs(self, audit, plan_id, file, employee_email_id):
        kd = timezone.now()
        file_name = file._name  # pylint: disable=protected-access
        current_entry = self.get_current_entry(plan_id, file_name, employee_email_id)
        if current_entry:
            self.invalidate(current_entry, kd)
        try:
            PlanDocs.objects.create(
                client_id=self.client_id,
                knowledge_begin_date=kd,
                additional_details=audit,
                plan_id=plan_id,
                doc=file,
                file_name=file_name,
                employee_email_id=employee_email_id,
            )
        except Exception as e:
            log_me(e)


class TempPlanSupportingDocsAccessor(TempPlanSupportingDocsAccessorTemplate):
    def client_aware(self) -> QuerySet[TempPlanDocs]:
        return TempPlanDocs.objects.filter(client=self.client_id)

    def upload_doc(
        self,
        plan_id: str,
        employee_email_id: str,
        plan_doc: InMemoryUploadedFile,
        knowledge_date: Optional[datetime] = None,
        audit: Optional[dict] = None,
    ) -> TempPlanDocs:
        knowledge_date = knowledge_date or timezone.now()
        return TempPlanDocs.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=knowledge_date,
            additional_details=audit,
            plan_id=plan_id,
            employee_email_id=employee_email_id,
            doc=plan_doc,
            file_name=plan_doc.name,
        )


class PlanModificationChangesAccessor:
    def __init__(self, client_id: int):
        self.client_id = client_id

    def client_aware(self):
        return PlanModificationChanges.objects.filter(client=self.client_id)

    def bulk_create_payee_periods(self, payee_periods):
        PlanModificationChanges.objects.bulk_create(payee_periods)

    def get_plan_changes_by_last_sync(
        self, last_sync: datetime, sync_type: PlanModificationTypes
    ):
        return PlanModificationChanges.objects.filter(
            client_id=self.client_id,
            created_at__gte=last_sync,
            change_type=sync_type.value,
        )

    def get_plan_changes_by_last_sync_and_type(
        self, last_sync: datetime, sync_type: PlanModificationTypes
    ) -> list:

        if sync_type == PlanModificationTypes.PLAN_DELETE:
            qs = (
                self.get_plan_changes_by_last_sync(last_sync, sync_type)
                .annotate(plan_id_str=Cast("plan_id", output_field=EsTextField()))
                .values("plan_id_str")
                .distinct()
            )
        elif sync_type == PlanModificationTypes.REMOVE_PAYEE:
            qs = (
                self.get_plan_changes_by_last_sync(last_sync, sync_type)
                .annotate(plan_id_str=Cast("plan_id", output_field=EsTextField()))
                .values("plan_id_str", "payee_email_id")
                .distinct()
            )
        elif (
            sync_type == PlanModificationTypes.EDIT_PERIOD
            or sync_type == PlanModificationTypes.SETTLEMENT_END_DATE
        ):
            qs = (
                self.get_plan_changes_by_last_sync(last_sync, sync_type)
                .annotate(plan_id_str=Cast("plan_id", output_field=EsTextField()))
                .values("plan_id_str", "payee_email_id", "period_end_date")
                .distinct()
            )
        else:
            return []

        return list(qs)

    def get_removed_payees_in_plan(self, payee_email: list[str], plan_id: str):
        return list(
            self.client_aware()
            .filter(
                change_type=PlanModificationTypes.REMOVE_PAYEE.value,
                payee_email_id__in=payee_email,
                plan_id=plan_id,
            )
            .values()
        )

    def update_removed_payees_in_plan(self, payee_email: list[str], plan_id: str):
        self.client_aware().filter(
            change_type=PlanModificationTypes.REMOVE_PAYEE.value,
            payee_email_id__in=payee_email,
            plan_id=plan_id,
        ).update(change_type=PlanModificationTypes.EDIT_PERIOD.value)

    def check_if_already_edited(self, plan_id: str, payee_details: list) -> bool:
        """
        The query would be,
        Eg: Payee (Jan-Dec) is modified to Jul-Dec, then immediately modified to Apr-Dec.

        While modifying for the 1st time, there would be records from Jan-Jun.
        While modifying for the 2nd time (Apr as eff-start, Dec as eff-end),
        The psd and ped are respective to the plan_modification_changes table (Jan-Jun).
        The query would be (psd < 31-12-2024 and ped > 01-04-2024.
        The result would be Apr, May, June.
        """
        query_set = self.client_aware()
        filter_conditions = []

        for payee in payee_details:
            filter_conditions.append(
                Q(
                    payee_email_id=payee["employee_email_id"],
                    period_start_date__lt=payee["effective_end_date"],
                    period_end_date__gt=payee["effective_start_date"],
                    plan_id=plan_id,
                    change_type=PlanModificationTypes.EDIT_PERIOD.value,
                )
            )
        if filter_conditions:
            query_set = query_set.filter(reduce(lambda x, y: x | y, filter_conditions))
            return query_set.exists()
        return False

    def delete_if_already_edited(self, plan_id: str, payee_details: list):
        query_set = self.client_aware()
        filter_conditions = []

        for payee in payee_details:
            filter_conditions.append(
                Q(
                    payee_email_id=payee["employee_email_id"],
                    period_start_date__lt=payee["effective_end_date"],
                    period_end_date__gt=payee["effective_start_date"],
                    plan_id=plan_id,
                    change_type=PlanModificationTypes.EDIT_PERIOD.value,
                )
            )
        if filter_conditions:
            query_set = query_set.filter(reduce(lambda x, y: x | y, filter_conditions))
            query_set.delete()

    def get_distinc_payees_with_pending_changes(self, payee_details: list[dict]):
        """
        This function is used to insert the payee details into a temporary table.
        The temporary table is used to get the distinct payees on join with the plan_modification_changes table.
        """
        values_to_insert = [
            (
                payee["payee_email_id"],
                payee["period_start_date"],
                payee["period_end_date"],
                payee["sec_kd"],
            )
            for payee in payee_details
        ]
        temp_table_name = f"temp_sec_kd_{self.client_id}"
        with connection.cursor() as cursor:
            cursor.execute(
                f"""
                CREATE TEMP TABLE IF NOT EXISTS {temp_table_name} (
                    payee_email_id varchar(255),
                    period_start_date timestamp with time zone,
                    period_end_date timestamp with time zone,
                    sec_kd timestamp with time zone
                )
            """
            )
            execute_values(
                cursor,
                f"""
                INSERT INTO {temp_table_name} (
                    payee_email_id,
                    period_start_date,
                    period_end_date,
                    sec_kd
                ) VALUES %s
                """,
                values_to_insert,
            )
            sql_query = f"""
            select
                distinct plan_modification_changes.payee_email_id
            from
                {temp_table_name}
            join plan_modification_changes on
                {temp_table_name}.period_start_date = plan_modification_changes.period_start_date
                and {temp_table_name}.period_end_date = plan_modification_changes.period_end_date
                and {temp_table_name}.payee_email_id = plan_modification_changes.payee_email_id
                and plan_modification_changes.client_id = {self.client_id}
                and plan_modification_changes.created_at > {temp_table_name}.sec_kd;
            """

            cursor.execute(sql_query)
            result = cursor.fetchall()
            cursor.execute(f"DROP TABLE IF EXISTS {temp_table_name}")

            return [row[0] for row in result]


class CommissionChangesAccessor:
    def __init__(self, client_id: int):
        self.client_id = client_id

    def client_aware(self):
        return CommissionChanges.objects.filter(client=self.client_id)

    def bulk_create_commission_changes(self, commission_changes):
        CommissionChanges.objects.bulk_create(commission_changes, batch_size=3000)

    def get_commission_changes_after_last_sync(self, last_sync_time: datetime):
        return list(
            self.client_aware()
            .filter(created_at__gte=last_sync_time)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )

    def get_changes_by_types_after_last_sync(
        self, last_sync_time: datetime, change_types: list
    ):
        qs = (
            self.client_aware()
            .filter(created_at__gte=last_sync_time, change_type__in=change_types)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )
        return list(qs)

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return self.client_aware().filter(created_at__gte=last_sync_time).exists()


class SettlementChangesAccessor:
    def __init__(self, client_id: int):
        self.client_id = client_id

    def client_aware(self):
        return SettlementChanges.objects.filter(client=self.client_id)

    def bulk_create_settlement_changes(self, settlement_changes):
        SettlementChanges.objects.bulk_create(settlement_changes, batch_size=3000)

    def get_settlement_changes_after_last_sync(self, last_sync_time: datetime):
        return list(
            self.client_aware()
            .filter(created_at__gte=last_sync_time)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )

    def does_record_exist_after_date(self, last_sync_time: datetime):
        return self.client_aware().filter(created_at__gte=last_sync_time).exists()

    def get_settlement_changes_payee_period_after_kd(self, knowledge_date: datetime):
        return list(
            self.client_aware()
            .filter(created_at__gte=knowledge_date)
            .values("payee_email_id", "period_end_date")
            .distinct()
        )

    def get_changes_by_types_after_last_sync(
        self, last_sync_time: datetime, change_types: list
    ):
        qs = (
            self.client_aware()
            .filter(created_at__gte=last_sync_time, change_type__in=change_types)
            .values("payee_email_id", "period_start_date", "period_end_date")
            .distinct()
        )
        return list(qs)
