from datetime import datetime

from commission_engine.utils.general_data import IntegrationType
from spm.models.config_models.integration_config_models import IntegrationConfig
from spm.serializers.config_serializers.integration_config_serializers import (
    IntegrationConfigSerializer,
)


class IntegrationConfigAccessor:
    """
    This class is used to access the IntegrationConfig model
    """

    def __init__(self):
        pass

    def kd_aware(self):
        return IntegrationConfig.objects.filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def get_slack_config_record_with_team_id_user_id(self, user_id: str, team_id: str):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param user_id: user_id of the slack user
        :param team_id: team_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return self.kd_aware().filter(
            config__slack_user_id=user_id,
            config__slack_team_id=team_id,
            integration_type=IntegrationType.SLACK.value,
        )

    def get_active_or_invalidated_slack_config_record_with_team_id_user_id(
        self, user_id: str, team_id: str
    ):
        """
        This method is used to get all the IntegrationConfig objects without kd_aware for the given client_id, employee_email_id and integration_type
        :param user_id: user_id of the slack user
        :param team_id: team_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return IntegrationConfig.objects.filter(
            config__slack_user_id=user_id,
            config__slack_team_id=team_id,
            integration_type=IntegrationType.SLACK.value,
        )

    def get_slack_config_emails_with_user_ids(self, user_ids):
        return (
            self.kd_aware()
            .filter(
                config__slack_user_id=user_ids,
                integration_type=IntegrationType.SLACK.value,
            )
            .values_list("employee_email_id", flat=True)
        )

    def get_msteams_config_record_with_user_id(self, user_id: str):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param user_id: user_id of the msteams user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return self.kd_aware().filter(
            config__msteams_user_id=user_id,
            integration_type=IntegrationType.MS_TEAMS.value,
        )

    def get_msteams_config_record_by_mail_id(self, client_id: int, mail_id: str):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param mail_id: mail_id of the msteams user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return (
            self.kd_aware()
            .filter(
                client_id=client_id,
                employee_email_id=mail_id,
                integration_type=IntegrationType.MS_TEAMS.value,
            )
            .first()
        )

    def get_slack_config_record_by_mail_id(self, client_id: int, mail_id: str):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param mail_id: mail_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return (
            self.kd_aware()
            .filter(
                client_id=client_id,
                employee_email_id=mail_id,
                integration_type=IntegrationType.SLACK.value,
            )
            .first()
        )

    def get_slack_config_record_for_mail_ids(self, client_id, mail_ids: list):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param mail_id: mail_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return self.kd_aware().filter(
            client_id=client_id,
            employee_email_id__in=mail_ids,
            integration_type=IntegrationType.SLACK.value,
        )

    def get_msteams_config_record_for_mail_ids(self, client_id, mail_ids: list):
        """
        This method is used to get the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param mail_id: mail_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return self.kd_aware().filter(
            client_id=client_id,
            employee_email_id__in=mail_ids,
            integration_type=IntegrationType.MS_TEAMS.value,
        )

    def get_config_records_for_mail_id(self, client_id: int, mail_id: str):
        """
        This method is used to get the IntegrationConfig objects for the given client_id, employee_email_id and integration_type
        :param mail_id: mail_id of the slack user
        :param integration_type: integration_type of the integration

        :return: IntegrationConfig object
        """
        return self.kd_aware().filter(client_id=client_id, employee_email_id=mail_id)

    def create_config_for_emp_id(
        self,
        client_id: int,
        knowledge_begin_date: datetime,
        employee_email_id: str,
        integration_type: str,
        config: dict,
    ):
        """
        This method is used to create an IntegrationConfig object for the given client_id, employee_email_id, integration_type and config
        :param employee_email_id: employee_email_id of the employee
        :param integration_type: integration_type of the integration
        :param config: config of the integration

        :return: IntegrationConfig object
        """
        return IntegrationConfig.objects.create(
            client_id=client_id,
            knowledge_begin_date=knowledge_begin_date,
            employee_email_id=employee_email_id,
            integration_type=integration_type,
            config=config,
        )

    def bulk_create_config_records(self, fields_map: list):
        """
        This method is used to create an IntegrationConfig object for the given client_id, employee_email_id, integration_type, knowledge_begin_date and config
        :param fields: list of fields to be inserted (client_id, knowledge_begin_date, config, employee_email_id, integration_type)
        """

        serializer = IntegrationConfigSerializer(data=fields_map, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

    def invalidate_config_record_for_emp_id(
        self,
        client_id: int,
        employee_email_id: str,
        integration_type: str,
        end_time: datetime,
    ):
        """
        This method is used to invalidate the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param employee_email_id: employee_email_id of the employee
        :param integration_type: integration_type of the integration

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            employee_email_id=employee_email_id,
            integration_type=integration_type,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_slack_config_for_email_ids(
        self,
        client_id: int,
        email_ids: list,
        end_time: datetime,
    ):
        """
        This method is used to invalidate the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param employee_email_id: employee_email_id of the employee
        :param integration_type: integration_type of the integration
        :param end_time: end_time

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            employee_email_id__in=email_ids,
            integration_type=IntegrationType.SLACK.value,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_msteams_config_for_email_ids(
        self,
        client_id: int,
        email_ids: list,
        end_time: datetime,
    ):
        """
        This method is used to invalidate the IntegrationConfig object for the given client_id, employee_email_id and integration_type
        :param employee_email_id: employee_email_id of the employee
        :param integration_type: integration_type of the integration
        :param end_time: end_time

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            employee_email_id__in=email_ids,
            integration_type=IntegrationType.MS_TEAMS.value,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_all_employees_in_slack_team(
        self,
        client_id: int,
        team_id: str,
        end_time: datetime,
    ):
        """
        This method is used to invalidate all the IntegrationConfig objects for the given client_id and integration_type
        :param integration_type: integration_type of the integration

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            integration_type=IntegrationType.SLACK.value,
            config__slack_team_id=team_id,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_all_employees_in_msteams_team(
        self, client_id: int, end_time: datetime
    ):
        """
        This method is used to invalidate all the IntegrationConfig objects for the given client_id and integration_type
        :param integration_type: integration_type of the integration

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            integration_type=IntegrationType.MS_TEAMS.value,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_all_config_records_of_integration_type(
        self,
        integration_type: str,
        client_id: int,
        team_id: str,
        end_time: datetime,
    ):
        """
        This method is used to invalidate all the IntegrationConfig objects for the given client_id and integration_type
        :param integration_type: integration_type of the integration

        :return: None
        """
        self.kd_aware().filter(
            client_id=client_id,
            integration_type=integration_type,
            config__slack_team_id=team_id,
        ).update(
            knowledge_end_date=end_time,
        )

    def invalidate_config_for_user_id_and_team_id(
        self,
        user_id: str,
        team_id: str,
        end_time: datetime,
    ):
        """
        This method is used to invalidate the IntegrationConfig object for the given user_id and team_id
        :param user_id: user_id of the slack user
        :param team_id: team_id of the slack user

        :return: None
        """
        self.kd_aware().filter(
            config__slack_user_id=user_id,
            config__slack_team_id=team_id,
            integration_type=IntegrationType.SLACK.value,
        ).update(
            knowledge_end_date=end_time,
        )
