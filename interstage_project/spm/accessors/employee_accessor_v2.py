# ruff: noqa: PLR0913,TRY003,FBT002,PLW2901,TRY201
import datetime
import uuid
from typing import Any, List, Set, Union

from django.db import connection
from django.db.models import Count, Q, Subquery, Value
from django.db.models.functions import Concat, Lower
from django.db.models.query import QuerySet
from django.utils import timezone

from commission_engine.utils.general_data import NotificationMode
from interstage_project.db.models import <PERSON>s<PERSON>har<PERSON><PERSON>
from interstage_project.utils import log_me
from spm.constants.approval_workflow_constants import email_for_notify
from spm.models.config_models.employee_models import Employee
from spm.serializers.config_serializers.employee_serializers import EmployeeSerializer


class EmployeeReadAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return Employee.objects.filter(client=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def user_role_contains_query(self, role_permission_ids):
        role_filter_query = Q()
        if isinstance(role_permission_ids, (list, set, tuple)):
            for role in role_permission_ids:
                role_filter_query |= Q(user_role__contains=[str(role)])
        if isinstance(role_permission_ids, (str, uuid.UUID)):
            role_filter_query |= Q(user_role__contains=[str(role_permission_ids)])
        return role_filter_query

    def get_all_employee_name_map(self):
        return {
            employee["employee_email_id"]: {
                "full_name": f"{employee['first_name']} {employee['last_name']}",
                "first_name": employee["first_name"],
                "last_name": employee["last_name"],
            }
            for employee in self.get_all_employees(
                projection=["employee_email_id", "first_name", "last_name"],
            )
        }

    def get_payees_paginated_with_search(
        self,
        limit,
        emails_list=None,
        full_name=None,
        email=None,
        search_term=None,
        exclude_roles=None,
    ):
        if limit is not None:
            query_set = self.client_kd_aware()
            if emails_list:
                query_set = query_set.filter(employee_email_id__in=emails_list)
            if exclude_roles:
                exclude_role_query = self.user_role_contains_query(exclude_roles)
                query_set = query_set.exclude(exclude_role_query)
            query_set = query_set.annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            )
            query_set = query_set.annotate(
                lower_case_full_name=Lower(
                    Concat(
                        "first_name",
                        Value(" "),
                        "last_name",
                        output_field=EsCharField(),
                    )
                )
            )
            if search_term is not None:
                search_term_query = Q(full_name__istartswith=search_term) | (
                    Q(last_name__istartswith=search_term)
                    | Q(employee_email_id__istartswith=search_term)
                )
                query_set = query_set.filter(search_term_query)
            if full_name is not None and email is not None:
                query_set = query_set.filter(
                    Q(lower_case_full_name__gt=full_name.lower())
                    | Q(
                        lower_case_full_name=full_name.lower(),
                        employee_email_id__gt=email,
                    )
                )
            return list(
                query_set.order_by(Lower("full_name"), "employee_email_id")
                .distinct()[:limit]
                .values("full_name", "employee_email_id")
            )
        else:
            raise Exception("Limit cannot be Empty")

    def get_payees_details_paginated(
        self,
        limit,
        emails_list=None,
        full_name=None,
        email=None,
    ):
        if limit is not None:
            query_set = self.client_kd_aware()
            if emails_list:
                query_set = query_set.filter(employee_email_id__in=emails_list)
            query_set = query_set.annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            )
            query_set = query_set.annotate(
                lower_case_full_name=Lower(
                    Concat(
                        "first_name",
                        Value(" "),
                        "last_name",
                        output_field=EsCharField(),
                    )
                )
            )
            if full_name is not None and email is not None:
                query_set = query_set.filter(
                    Q(lower_case_full_name__gt=full_name.lower())
                    | Q(
                        lower_case_full_name=full_name.lower(),
                        employee_email_id__gt=email,
                    )
                )
            return list(
                query_set.order_by(Lower("full_name"), "employee_email_id")
                .distinct()[:limit]
                .values_list(
                    "full_name",
                    "employee_email_id",
                    "first_name",
                    "last_name",
                    "exit_date",
                )
            )
        else:
            raise Exception("Limit cannot be Empty")

    def exit_date_aware(self, as_of_date=None, user_status=None):
        """
        if user status is
            None or "Active", then get current active users(Note: as_of_date set to curr time here).
            "Exited", then get only exited.(as_of_date set to curr time, so we get as_of_date exited users)
            "All", then get all the users (as_of_date not applied here).
        """
        query_set = self.client_kd_aware()
        if as_of_date is None:
            as_of_date = timezone.now()

        if user_status is None:
            user_status = "Active"

        if user_status == "Active":
            return query_set.filter(
                Q(exit_date__gte=as_of_date) | Q(exit_date__isnull=True)
            )

        if user_status == "Exited":
            return query_set.filter(exit_date__isnull=False, exit_date__lte=as_of_date)

        return query_set

    def get_all_employees(
        self, as_of_date=None, user_status=None, email_list=None, projection=None
    ):
        qs = self.exit_date_aware(as_of_date, user_status)
        if email_list:
            qs = qs.filter(employee_email_id__in=email_list)
        if projection:
            return list(qs.values(*projection))
        return list(qs)

    def get_all_employees_including_exit_payees(self, email_list=None, projection=None):
        qs = self.client_kd_aware()
        if email_list:
            qs = qs.filter(employee_email_id__in=email_list)
        if projection:
            return list(qs.values(*projection))
        return list(qs)

    def get_employee_by_email_id_include_exit_payees(
        self,
        email_id,
        projection=None,
        as_dicts=False,
    ):
        qs = self.client_kd_aware().filter(employee_email_id=email_id)
        if projection:
            return list(qs.values(*projection))

        if as_dicts and projection is None:
            return list(qs.values())

        return list(qs)

    def get_employee_by_email_id(
        self,
        email_id,
        as_of_date=None,
        user_status=None,
        projection=None,
        as_dicts=False,
    ):
        qs = self.exit_date_aware(as_of_date, user_status).filter(
            employee_email_id=email_id
        )
        if projection:
            return list(qs.values(*projection))

        if as_dicts and projection is None:
            return list(qs.values())

        return list(qs)

    def get_all_employees_with_limit(
        self,
        as_of_date=None,
        user_status=None,
        user_role=None,
        search_term=None,
        offset=None,
        limit=None,
        email_list=None,
        only_email=False,
        context=None,
        exclude_payees=None,
        projection=None,
        is_to_fetch_exited_users: bool = False,
    ):
        if is_to_fetch_exited_users:
            qs = self.client_kd_aware()
        else:
            qs = self.exit_date_aware(as_of_date, user_status)
        qs = qs.annotate(
            full_name=Concat(
                "first_name", Value(" "), "last_name", output_field=EsCharField()
            )
        )
        if user_role:
            role_filter_query = self.user_role_contains_query(user_role)
            qs = qs.filter(role_filter_query)
        if exclude_payees:
            qs = qs.exclude(employee_email_id__in=exclude_payees)
        if email_list:
            qs = qs.filter(employee_email_id__in=email_list)
        if search_term:
            qs = qs.filter(
                Q(
                    full_name__istartswith=search_term
                )  # pylint: disable=unsupported-binary-operation
                | Q(first_name__istartswith=search_term)
                | Q(last_name__istartswith=search_term)
                | Q(employee_email_id__istartswith=search_term)
            )

        # Exclude everstage from the list if we are sending notification
        if context == email_for_notify:
            qs = qs.exclude(first_name__iexact="everstage")

        qs = qs.order_by(Lower("full_name"))
        if offset is not None and limit is not None:
            qs = qs[offset : offset + limit]

        if projection:
            qs = qs.values(*projection)
        if only_email:
            return list(qs.values_list("employee_email_id", flat=True))
        return list(qs)

    def get_all_payees_with_status(self, status, email_list, as_of_date=None):
        qs = self.exit_date_aware(as_of_date).filter(
            status__in=status, employee_email_id__in=email_list
        )
        return list(qs)

    def get_employees_status(self, email_list):
        """
        get the status of given email ids if they exist
        """
        qs = (
            self.client_kd_aware()
            .filter(employee_email_id__in=email_list)
            .values("employee_email_id", "status")
        )
        return list(qs)

    def get_all_employees_with_search_limit(
        self,
        as_of_date=None,
        user_status=None,
        search_term=None,
        email_only=False,
        offset=None,
        limit=None,
        payee_emails=None,
    ):
        qs = self.exit_date_aware(as_of_date, user_status)
        qs = qs.annotate(
            full_name=Concat(
                "first_name", Value(" "), "last_name", output_field=EsCharField()
            )
        )
        if payee_emails:
            qs = qs.filter(employee_email_id__in=payee_emails)
        if search_term:
            qs = qs.filter(
                Q(full_name__icontains=search_term)
                | Q(employee_email_id__istartswith=search_term)
            )
        count = qs.count()
        qs = qs.order_by("full_name")
        if email_only:
            return list(qs.values_list("employee_email_id", flat=True))
        if offset is not None and limit is not None:
            qs = qs[(offset * limit) : (offset * limit) + limit]
        employees_object = {"employees": list(qs), "employees_count": count}
        return employees_object

    def get_all_employees_qs(self, as_of_date=None, user_status=None):
        qs = self.exit_date_aware(as_of_date, user_status)
        return qs

    def get_all_employees_by_role(self, roles, exclude_payee=None, projection=None):
        roles_filter_query = self.user_role_contains_query(roles)
        qs = (
            self.client_kd_aware()
            .filter(roles_filter_query)
            .filter(~Q(employee_email_id=exclude_payee))
        )
        if projection:
            qs = qs.values(*projection)
        return list(qs)

    def get_all_non_exited_employees_by_role(
        self, roles, exclude_payee=None, projection=None
    ):
        roles_filter_query = self.user_role_contains_query(roles)
        qs = (
            self.exit_date_aware()
            .filter(roles_filter_query)
            .filter(~Q(employee_email_id=exclude_payee))
        )
        if projection:
            qs = qs.values(*projection)
        return list(qs)

    def get_all_employee_ids_by_role(self, roles):
        roles_filter_query = self.user_role_contains_query(roles)
        return list(
            self.client_kd_aware()
            .filter(roles_filter_query)
            .values("employee_email_id")
        )

    def get_employee_email_ids_by_role(self, roles):
        if len(roles) > 0:
            roles_filter_query = self.user_role_contains_query(roles)
            return list(
                self.client_kd_aware()
                .filter(roles_filter_query)
                .values_list("employee_email_id", flat=True)
            )
        else:
            return []

    def get_taken_emails(self) -> Set[str]:
        return set(self.client_aware().values_list("employee_email_id", flat=True))

    def get_active_support_user_email_ids_by_roles(self, roles):
        if len(roles) > 0:
            roles_filter_query = self.user_role_contains_query(roles)
            return list(
                self.client_kd_aware()
                .filter(is_internal_support_user=True)
                .filter(
                    Q(deactivation_date__gte=timezone.now())
                    | Q(deactivation_date__isnull=True),
                    Q(exit_date__gte=timezone.now()) | Q(exit_date__isnull=True),
                    status="Active",
                )
                .filter(roles_filter_query)
                .values_list("employee_email_id", flat=True)
            )
        else:
            return []

    def get_active_employee_email_ids_by_roles(self, roles):
        if len(roles) > 0:
            roles_filter_query = self.user_role_contains_query(roles)
            return list(
                self.client_kd_aware()
                .filter(
                    Q(deactivation_date__gte=timezone.now())
                    | Q(deactivation_date__isnull=True),
                    Q(exit_date__gte=timezone.now()) | Q(exit_date__isnull=True),
                    status="Active",
                )
                .filter(roles_filter_query)
                .values_list("employee_email_id", flat=True)
            )
        else:
            return []

    def get_employee_email_id_by_role_with_min_client_count(
        self, roles: list[uuid.UUID]
    ) -> tuple[str, int] | None:
        """
        Returns the employee with the given roles associated with the minimum number of clients
        Used during analytics default dashboard creation for PowerAdmin selection
        """
        if len(roles) > 0:
            roles_filter_query = self.user_role_contains_query(roles)
            subquery = (
                self.client_kd_aware()
                .filter(roles_filter_query)
                .values_list("employee_email_id", flat=True)
            )
            return (
                Employee.objects.filter(employee_email_id__in=Subquery(subquery))
                .values_list("employee_email_id")
                .annotate(client_count=Count("client_id", distinct=True))
                .order_by("client_count")
                .first()
            )

        return None

    def get_all_employee_email(self, as_of_date=None, user_status=None):
        return list(
            self.exit_date_aware(as_of_date, user_status).values("employee_email_id")
        )

    def get_all_employee_email_list(
        self, as_of_date=None, user_status=None, email_list=None
    ):
        query_set = self.exit_date_aware(as_of_date, user_status)
        if email_list:
            query_set = query_set.filter(employee_email_id__in=email_list)
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_all_employee_email_list_including_exited_users(self, email_list=None):
        query_set = self.client_kd_aware()
        if email_list:
            query_set = query_set.filter(employee_email_id__in=email_list)
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_all_non_deactivated_users_email_list(self, as_of_date=None):
        as_of_date = as_of_date or timezone.now()
        query_set = self.client_kd_aware().filter(
            Q(deactivation_date__gte=as_of_date) | Q(deactivation_date__isnull=True)
        )
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_all_employee_email_list_with_notifications(
        self, as_of_date=None, user_status=None, email_list=None
    ):
        query_set = self.exit_date_aware(as_of_date, user_status).filter(
            send_notification=True
        )
        if email_list is not None:
            query_set = query_set.filter(employee_email_id__in=email_list)
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_all_non_deactivated_users_email_with_notifications(self, as_of_date=None):
        as_of_date = as_of_date or timezone.now()
        query_set = self.client_kd_aware().filter(
            Q(deactivation_date__gte=as_of_date) | Q(deactivation_date__isnull=True),
            send_notification=True,
        )
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_employees_with_notification_status(
        self,
        email_list: list[str] | None = None,
        send_notification: bool = True,
        projection: list[str] | None = None,
    ):
        """Get employee details with send_notification status"""
        projection = projection or []
        qs = self.exit_date_aware().filter(send_notification=send_notification)
        if email_list:
            qs = qs.filter(employee_email_id__in=email_list)

        return list(qs.values(*projection))

    def get_all_employee_email_list_exclude_user_roles_and_email_ids(
        self, exclude_users, as_of_date=None, user_status=None, email_list=None
    ):
        query_set = self.exit_date_aware(as_of_date, user_status)
        if exclude_users:
            exclude_role_query = self.user_role_contains_query(exclude_users)
            query_set = query_set.exclude(exclude_role_query)
        if email_list:
            query_set = query_set.exclude(employee_email_id__in=email_list)
        return list(query_set.values_list("employee_email_id", flat=True))

    def get_employee(self, email_id, projection=None, as_query_set=False):
        qs = self.client_kd_aware().filter(employee_email_id=email_id)
        if as_query_set:
            return qs
        employee_list = qs.first()
        if projection:
            return qs.values(*projection).first()
        return employee_list

    def get_active_or_invalidated_employee(
        self, email_id, projection=None, as_query_set=False
    ):
        qs = (
            self.client_aware()
            .filter(employee_email_id=email_id)
            .order_by("-temporal_id")
        )
        if as_query_set:
            return qs
        if projection:
            return qs.values(*projection).first()
        return qs.first()

    def get_employees(self, employee_email_ids, as_dicts=True, as_query_set=False):
        qs = self.client_kd_aware().filter(employee_email_id__in=employee_email_ids)
        if as_query_set:
            return qs
        employee_list = list(qs.values()) if as_dicts else list(qs)
        return employee_list

    def get_employees_with_filters(
        self,
        email_ids=None,
        as_dicts=True,
        search_term=None,
        limit=None,
        offset=None,
        projection=None,
        email_only=False,
        order_by=None,
        order_by_full_name=False,
    ):
        qs = self.client_kd_aware()
        if email_ids:
            qs = qs.filter(employee_email_id__in=email_ids)
        if search_term:
            qs = qs.annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            ).filter(
                Q(full_name__icontains=search_term)
                | Q(employee_email_id__istartswith=search_term)
            )
        if as_dicts:
            qs = qs.values()
        if order_by:
            qs = qs.order_by(*order_by)
        if order_by_full_name:
            qs = qs.annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            ).order_by("full_name")
        if projection:
            qs = qs.values(*projection)
        if limit is not None and offset is not None:
            qs = qs[offset : offset + limit]
        if email_only:
            qs = qs.values_list("employee_email_id", flat=True)
        return list(qs)

    def count_employees_with_filters(self, email_ids=None, search_term=None):
        qs = self.client_kd_aware()
        if email_ids:
            qs = qs.filter(employee_email_id__in=email_ids)
        if search_term:
            qs = qs.annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            ).filter(
                Q(full_name__icontains=search_term)
                | Q(employee_email_id__istartswith=search_term)
            )
        return qs.count()

    def get_all_employee_by_status(self, email_ids, status, projection=None):
        qs = self.client_kd_aware().filter(
            employee_email_id__in=email_ids, status=status
        )
        if projection:
            return list(qs.values(*projection))
        return list(qs)

    def get_all_employee_details(self, email_ids, projection=None):
        if projection is None:
            projection = []
        return (
            self.client_kd_aware()
            .filter(employee_email_id__in=email_ids)
            .values(*projection)
        )

    def search_employee(self, email_id):
        qs = self.client_kd_aware().filter(employee_email_id__contains=email_id)
        employee_list = qs.first()
        return employee_list

    def get_employee_role(self, email_id):
        return (
            self.client_kd_aware().filter(employee_email_id=email_id).first().user_role
        )

    def get_employee_full_name(self, email_id) -> str:
        employee_record = (
            self.client_kd_aware()
            .filter(employee_email_id=email_id)
            .annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            )
            .first()
        )
        return employee_record.full_name if employee_record else ""

    @staticmethod
    def get_employees_with_same_email(email_id, fields=None) -> QuerySet:
        if not isinstance(fields, list):
            fields = []
        return Employee.objects.only(*fields).filter(
            knowledge_end_date__isnull=True,
            is_deleted=False,
            employee_email_id=email_id,
        )

    @classmethod
    def get_internal_support_users_with_same_email(
        cls, email_id, fields=None
    ) -> QuerySet:
        return cls.get_employees_with_same_email(email_id, fields).filter(
            is_internal_support_user=True
        )

    def get_send_notification(self, email_id):
        return (
            self.client_kd_aware()
            .filter(employee_email_id=email_id)
            .first()
            .send_notification
        )

    @staticmethod
    def get_invited_or_active_users(email_id, as_of_date=None, fields=None):
        """Get all invited or active users who haven't exited yet,
        with the given email id irrespective of client id"""
        as_of_date = as_of_date or timezone.now()
        if not isinstance(fields, list):
            fields = []
        return Employee.objects.only(*fields).filter(
            Q(status="Active") | Q(status="Invited"),
            Q(exit_date__gte=as_of_date) | Q(exit_date__isnull=True),
            knowledge_end_date__isnull=True,
            is_deleted=False,
            employee_email_id=email_id,
        )

    @staticmethod
    def get_invited_or_active_and_non_deactivated_users(
        email_id, as_of_date=None, fields=None
    ):
        """Get all invited or non-deactivated users who haven't exited yet,
        with the given email id irrespective of client id"""
        as_of_date = as_of_date or timezone.now()
        if not isinstance(fields, list):
            fields = []
        return Employee.objects.only(*fields).filter(
            Q(status="Active") | Q(status="Invited"),
            Q(deactivation_date__gte=as_of_date) | Q(deactivation_date__isnull=True),
            knowledge_end_date__isnull=True,
            is_deleted=False,
            employee_email_id=email_id,
        )

    @staticmethod
    def get_non_exited_users(email_id, as_of_date=None):
        """Get all users who haven't exited yet,
        with the given email id irrespective of client id"""
        as_of_date = as_of_date or timezone.now()
        return EmployeeReadAccessor.get_employees_with_same_email(email_id).filter(
            Q(exit_date__gte=as_of_date) | Q(exit_date__isnull=True)
        )

    @staticmethod
    def get_non_deactivated_users(email_id, as_of_date=None):
        """Get all users who haven't deactivated yet,
        with the given email id irrespective of client id"""
        as_of_date = as_of_date or timezone.now()
        return EmployeeReadAccessor.get_employees_with_same_email(email_id).filter(
            Q(deactivation_date__gte=as_of_date) | Q(deactivation_date__isnull=True)
        )

    def get_latest_employee_records(self, email_id):
        return list(
            self.client_kd_aware()
            .filter(employee_email_id=email_id, knowledge_end_date__isnull=True)
            .values()
        )

    def get_all_slack_using_employees(self):
        return self.client_kd_aware().filter(
            employee_config__slack_team_id__isnull=False,
            employee_config__slack_user_id__isnull=False,
        )

    def get_all_msteams_using_employees(self):
        qs = self.client_kd_aware().filter(
            employee_config__msteams_conversation_id__isnull=False,
            employee_config__msteams_user_id__isnull=False,
        )
        return list(qs)

    def get_employees_name(self, employee_email_ids):
        qs = self.get_employees_name_as_dict(employee_email_ids)
        return list(qs)

    def get_employees_profile_picture_list(self, employee_email_ids):
        qs = self.get_employees_names_profile_picture(employee_email_ids)
        return list(qs)

    def get_employees_name_as_dict(self, employee_email_ids):
        qs = (
            self.client_kd_aware()
            .filter(employee_email_id__in=employee_email_ids)
            .values("employee_email_id", "first_name", "last_name")
        )
        return qs

    def get_employees_name_as_dict_with_full_name(self, employee_email_ids):
        qs = (
            self.client_kd_aware()
            .filter(employee_email_id__in=employee_email_ids)
            .annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            )
            .values("employee_email_id", "full_name")
        )
        return qs

    def get_employees_name_as_dict_with_full_name_excluding_everstage(
        self, employee_email_ids
    ):
        qs = (
            self.exit_date_aware()
            .filter(employee_email_id__in=employee_email_ids)
            .exclude(employee_email_id__istartswith="everstage")
            .annotate(
                full_name=Concat(
                    "first_name", Value(" "), "last_name", output_field=EsCharField()
                )
            )
            .values("employee_email_id", "full_name")
        )
        return qs

    def get_employees_name_exitdate(self, employee_email_ids):
        qs = self.client_kd_aware().filter(employee_email_id__in=employee_email_ids)
        qs = qs.annotate(
            full_name=Concat(
                "first_name", Value(" "), "last_name", output_field=EsCharField()
            )
        )
        qs = qs.order_by(Lower("full_name"))
        qs = qs.values("employee_email_id", "first_name", "last_name", "exit_date")
        return list(qs)

    def get_employees_names_profile_picture(self, employee_email_ids):
        return (
            self.client_kd_aware()
            .filter(employee_email_id__in=employee_email_ids)
            .values("employee_email_id", "first_name", "last_name", "profile_picture")
        )

    def get_employees_basic_detail(self, employee_email_ids):
        qs = self.client_kd_aware().filter(employee_email_id__in=employee_email_ids)
        return qs.only(
            "employee_email_id",
            "first_name",
            "last_name",
            "user_role",
            "profile_picture",
        )

    def get_employees_profile_picture(self, employee_email_ids):
        return (
            self.client_kd_aware()
            .filter(employee_email_id__in=employee_email_ids)
            .values("employee_email_id", "profile_picture")
        )

    def get_employee_profile_picture(self, employee_email_id):
        return (
            self.client_kd_aware()
            .filter(employee_email_id=employee_email_id)
            .values_list("profile_picture", flat=True)
            .first()
        )

    def get_all_employee_names(self):
        qs = self.client_kd_aware().values(
            "employee_email_id", "first_name", "last_name"
        )
        return list(qs)

    def get_all_active_employee_email_ids(self):
        qs = self.exit_date_aware().values("employee_email_id")
        return list(qs)

    def get_modified_records(self, updated_time):
        return self.client_kd_aware().filter(knowledge_begin_date__gte=updated_time)

    def get_last_deleted_employees(self, ked):
        if ked is None:
            return self.client_aware().filter(is_deleted=True).values()
        return self.client_aware().filter(is_deleted=True, knowledge_end_date__gte=ked)

    def get_email_id_of_records_modified(self, updated_time):
        query_set = (
            self.client_kd_aware()
            .filter(knowledge_begin_date__gte=updated_time)
            .values_list("employee_email_id", flat=True)
        )
        return list(query_set)

    def get_deleted_records_by_changes_start_time(self, changes_start_time):
        """Function to get Deleted records after changes start time.

        Args:
            changes_start_time (datetime): Changes start time.
        """
        return list(
            self.client_aware()
            .filter(
                is_deleted=True,
                knowledge_end_date__gte=changes_start_time,
            )
            .values()
        )

    def get_preferred_client(self, employee_email_id):
        employee = self.get_employee(email_id=employee_email_id)
        if employee and employee.employee_config:
            preferred_client_id = employee.employee_config.get("preffered_client_id")

            return preferred_client_id
        return None

    def get_count_all_employee_ids_by_role(self):
        query = """
            SELECT distinct jsonb_array_elements(e.user_role) AS role_id, COUNT(*) AS role_count
            FROM employee e
            WHERE e.client_id = %(client_id)s
                AND e.is_deleted = false
                AND e.knowledge_end_date IS NULL
            GROUP BY role_id;
        """

        params = {"client_id": self.client_id}

        result = []
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            columns = [col[0] for col in cursor.description]
            result = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return result

    def get_user_role_count(self, user_role):
        roles_filter_query = self.user_role_contains_query(user_role)
        return self.client_kd_aware().filter(roles_filter_query).count()

    def does_record_exist_after_date(self, date):
        return self.client_kd_aware().filter(knowledge_begin_date__gt=date).exists()

    def get_exited_employees_from_given_list(self, email_list):
        exitedEmployees = self.exit_date_aware(user_status="Exited").filter(
            employee_email_id__in=email_list
        )
        return list(exitedEmployees.values_list("employee_email_id", flat=True))

    def does_employee_exist(self, email_id):
        """Check whether employee ever existed irrespective of his status (Active/Exited)"""
        return self.client_kd_aware().filter(employee_email_id=email_id).exists()

    def is_employee_active(self, email_id):
        """Check whether employee is active or invited"""
        return (
            self.exit_date_aware()
            .filter(
                Q(status="Active") | Q(status="Invited"), employee_email_id=email_id
            )
            .exists()
        )

    def are_employees_non_exited(self, email_ids: Union[List[str], Set[str]]) -> bool:
        """Check whether all employees are non-exited"""
        return set(
            self.exit_date_aware()
            .filter(employee_email_id__in=email_ids)
            .values_list("employee_email_id", flat=True)
        ) == set(email_ids)

    def email_notfications_count(self):
        enabled = self.exit_date_aware().filter(send_notification=True).count()
        disabled = self.exit_date_aware().filter(send_notification=False).count()
        return enabled, disabled

    def get_exited_or_deactivated_employees(self, changes_date, curr_date):
        return (
            self.client_kd_aware()
            .filter(
                (Q(exit_date__gt=changes_date) & Q(exit_date__lte=curr_date))
                | (
                    Q(deactivation_date__gt=changes_date)
                    & Q(deactivation_date__lte=curr_date)
                ),
            )
            .values_list("employee_email_id", flat=True)
        )


class EmployeeWriteAccessor:
    def __init__(self, client_id):
        self.client_id = client_id
        self.employee_accessor = EmployeeReadAccessor(client_id=self.client_id)

    ####################### Static methods #######################ß

    @staticmethod
    def exclusive_or(left: Any, right: Any) -> bool:
        """
        Test if either left or right is true, but not both
        """
        return (left or right) and not (left and right)

    @staticmethod
    def get_employee_emails(records: List[Employee] | List[dict]) -> List[str]:
        return [
            (
                record["employee_email_id"]
                if isinstance(record, dict)
                else record.employee_email_id
            )
            for record in records
        ]

    ####################### Model Write Methods #######################

    def _persist_record(self, data):
        # Do not use this function directly for any write.
        return data.save()

    def _update_record(self, qs, data):
        # Do not use this function directly for any write.
        return qs.update(**data)

    def _bulk_create_employees(self, employees):
        # Do not use this function directly for any write.
        Employee.objects.bulk_create(employees, batch_size=1000)

    ####################### Base Write Methods #######################

    def _invalidate_employee_record(
        self,
        employee_id: str | List[str] | None = None,
        filter_query: QuerySet[Employee] | None = None,
        knowledge_date: datetime.datetime | None = None,
    ):
        """
        Invalidates the latest record of the employee(s) with the given employee_id(s) or custom filter_query.
        'employee_id' and 'filter_query' mutually exclusive.
        This method should not be directly called from services.

        Parameters:
            -  employee_id (str | List[str])
            -  filter_query (QuerySet[Employee])
            -  knowledge_date (datetime)

        """
        if knowledge_date is None:
            knowledge_date = timezone.now()

        update_data = {"knowledge_end_date": knowledge_date}

        if not self.exclusive_or(employee_id, filter_query):
            raise ValueError("Either 'employee_id' or 'filter_query' must be provided")

        if filter_query:
            return self._update_record(filter_query, update_data)

        if isinstance(employee_id, list):
            qs = self.employee_accessor.get_employees(employee_id, as_query_set=True)
        else:
            qs = self.employee_accessor.get_employee(employee_id, as_query_set=True)

        return self._update_record(qs, update_data)

    def _create_objects(self, records: List[Employee] | List[dict]):
        """
        Bulk create object when passes as list of Employee objects or dict.
        This method should not be directly called from services.

        Parameters:
            -  records (List[Employee])

        """
        employee_records = []
        if isinstance(records, list):
            for record in records:
                if isinstance(record, dict):
                    record = Employee(**record)
                record.pk = None
                employee_records.append(record)
        try:
            return self._bulk_create_employees(employee_records)
        except Exception as e:
            log_me("EMPLOYEE BULK INSERT EXP - {}".format(e))
            raise e

    ####################### Write Methods that will be used to produce events #######################

    def persist_employee(self, employee_data: Employee | EmployeeSerializer):
        """
        Method to persist the Employee or EmployeeSerializer object.
        """
        return self._persist_record(employee_data)

    def create_employee_objects(self, records: List[Employee] | List[dict]):
        """
        Method to create the Employee objects in bulk.
        """
        return self._create_objects(records)

    def update_employee(self, query_set: QuerySet[Employee], employee_data: dict):
        """
        Common method to update the records based on 'query_set' and 'employee_data'.
        'query_set' is a QuerySet of Employee objects to be updated.
        'employee_data' is a dict with the fields to be updated.
        """
        return self._update_record(query_set, employee_data)

    def invalidate_and_create_objects(
        self,
        knowledge_date: datetime.datetime,
        new_records: Employee | QuerySet[Employee] | List[Employee] | List[dict],
        invalidate_employee_email: str | List[str] | None = None,
        invalidate_filter_query: QuerySet[Employee] | None = None,
    ):
        """
        Common method to invalidate the records based on mutually exclusive 'invalidate_employee_email' and 'invalidate_filter_query'
        and bulk create 'new_records'.
        If no new_records provided and no invalidate params provided then return None.
        If no new records provided but any of invalidate param provided, then throws error as then we can just use invalidate function.
        Can be used for all kind operation where we need to invalidate and create new records.
        Parameters:
            -  knowledge_date (datetime)
            -  new_records (Employee | QuerySet[Employee] | List[Employee] | List[dict])
            -  invalidate_employee_email (str | List[str])
            -  invalidate_filter_query (QuerySet[Employee])
        """
        if not (new_records or invalidate_employee_email or invalidate_filter_query):
            # If all the parameters provided are empty, then no need to perform any operation.
            return None

        if not new_records and self.exclusive_or(
            invalidate_employee_email, invalidate_filter_query
        ):
            raise ValueError(
                "Only Invalidate parameters provided, new records should be provided along with invalidate parameters."
            )

        self._invalidate_employee_record(
            invalidate_employee_email, invalidate_filter_query, knowledge_date
        )
        if isinstance(new_records, Employee):
            new_records = [new_records]
        if isinstance(new_records, QuerySet):
            new_records = list(new_records)
        return self._create_objects(new_records)

    ######################## Write Methods to be used in services #######################

    def update_employee_preferred_client(
        self, email_id, preffered_client_id, knowledge_date
    ):
        """
        Updates the preferred client of the employee with the given email_id.
        """
        records_qs = self.employee_accessor.get_employees_with_same_email(email_id)
        for record in records_qs:
            record.pk = None
            if preffered_client_id == -1 and record.employee_config is not None:
                record.employee_config.pop("preffered_client_id")
            else:
                if record.employee_config is None:
                    record.employee_config = {}
                record.employee_config["preffered_client_id"] = preffered_client_id
            record.knowledge_begin_date = knowledge_date

        invalidate_filter_qs = self.employee_accessor.get_employees_with_same_email(
            email_id
        )
        self.invalidate_and_create_objects(
            new_records=records_qs,
            invalidate_filter_query=invalidate_filter_qs,
            knowledge_date=knowledge_date,
        )

    def remove_msteams_config(self, email_id, kd):
        """
        Removes the MS Teams config from the employee with the given email_id.
        If the employee has other MS Teams configs, they will be retained.
        """
        msteams_configs = NotificationMode.MS_TEAMS.value["employee_config_key"]
        employee = self.employee_accessor.get_employee(email_id)
        if employee.employee_config is not None:
            for msteams_config in msteams_configs:
                if msteams_config in employee.employee_config:
                    employee.pk = None
                    employee.knowledge_begin_date = kd
                del employee.employee_config[msteams_config]
            self.invalidate_and_create_objects(
                new_records=employee, invalidate_employee_email=email_id, knowledge_date=kd  # type: ignore
            )

    def save_freshchat_id(self, email, fc_id, kd):
        """
        Save the freshchat id for the employee with the given email id.
        """
        record = self.employee_accessor.get_employee(email)
        record.pk = None
        record.knowledge_begin_date = kd
        if record.employee_config:
            record.employee_config.update({"freshchat_id": fc_id})
        else:
            record.employee_config = {"freshchat_id": fc_id}
        self.invalidate_and_create_objects(
            new_records=record, invalidate_employee_email=email, knowledge_date=kd  # type: ignore
        )

    def update_employee_profile(self, email_id, kd, time_zone, first_name, last_name):
        """
        Updates the employee's first name, last name and time zone for the given email_id.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.first_name = first_name
        record.last_name = last_name
        record.time_zone = time_zone
        record.knowledge_begin_date = kd
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=kd,
        )

    def update_employee_profile_picture(self, email_id, profile_picture_url, kd=None):
        """
        Updates the employee's profile picture for the given email_id.
        """
        kd = timezone.now() if kd is None else kd
        records = self.employee_accessor.get_latest_employee_records(email_id)
        modified_records = []
        for record in records:
            record["profile_picture"] = profile_picture_url
            record["knowledge_begin_date"] = kd
            modified_records.append(record)
        self.invalidate_and_create_objects(
            new_records=modified_records,
            invalidate_employee_email=email_id,
            knowledge_date=kd,
        )

    def update_send_notification(self, email_ids, kd, send_notification):
        """
        Updates the send_notification field of the employees for the given email_ids.
        """
        records = self.employee_accessor.get_employees(email_ids, as_dicts=False)
        for record in records:
            record.pk = None
            record.send_notification = send_notification
            record.knowledge_begin_date = kd
        self.invalidate_and_create_objects(
            new_records=records,
            invalidate_employee_email=email_ids,
            knowledge_date=kd,
        )

    def update_exit_date(
        self, email_id, exit_date, last_commission_date, knowledge_date
    ):
        """
        Updates the exit date and last commission date of the employee with the given email_id.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.knowledge_begin_date = knowledge_date
        record.exit_date = exit_date
        record.last_commission_date = last_commission_date
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=knowledge_date,
        )

    def update_exit_and_deactivation_date(
        self,
        email_id,
        exit_date,
        deactivation_date,
        last_commission_date,
        knowledge_date,
    ):
        """
        Updates the exit date, deactivation date and last commission date of the employee with the given email_id.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.knowledge_begin_date = knowledge_date
        record.exit_date = exit_date
        record.deactivation_date = deactivation_date
        record.last_commission_date = last_commission_date
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=knowledge_date,
        )

    def update_status(self, email_id, status, knowledge_date):
        """
        Invalidate the current latest record and create new record with updated status.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.knowledge_begin_date = knowledge_date
        record.status = status
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=knowledge_date,
        )

    def update_status_and_send_notification(
        self,
        email_id: str,
        status: str,
        send_notification: bool,
        knowledge_date: datetime.datetime,
    ):
        """
        Invalidate the current latest record and create new record with updated status
        and send_notification preference for the given email_id.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.knowledge_begin_date = knowledge_date
        record.status = status
        record.send_notification = send_notification
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=knowledge_date,
        )

    def update_employee_name_and_role(
        self,
        email_id,
        first_name,
        last_name,
        role,
        knowledge_date,
        update_deactivation_date=False,
        deactivation_date=None,
    ):
        """
        Invalidates the current latest record and create new record with updated name and role.
        """
        record = self.employee_accessor.get_employee(email_id)
        record.pk = None
        record.knowledge_begin_date = knowledge_date
        record.first_name = first_name
        record.last_name = last_name
        record.user_role = role
        if update_deactivation_date:
            record.deactivation_date = deactivation_date
        self.invalidate_and_create_objects(
            new_records=record,  # type: ignore
            invalidate_employee_email=email_id,
            knowledge_date=knowledge_date,
        )

    def invalidate_and_update_role(self, user_role, new_user_role, ked, audit):
        """
        Invalidates the records with the given 'user_role' and updates the 'user_role' with 'new_user_role'.
        Invalidating by sending the invalidation query instead of invalidating with email list.
        """
        roles_filter_query = self.employee_accessor.user_role_contains_query(user_role)
        role_exisiting_records_query = self.employee_accessor.client_kd_aware().filter(
            roles_filter_query
        )
        exisiting_records = list(role_exisiting_records_query)
        for record in exisiting_records:
            record.pk = None
            record.knowledge_begin_date = ked
            if user_role in record.user_role:
                record.user_role.remove(user_role)
                record.user_role.append(new_user_role)
            record.additional_details = audit
        self.invalidate_and_create_objects(
            new_records=exisiting_records,
            invalidate_filter_query=role_exisiting_records_query,
            knowledge_date=ked,
        )

    def deleteEmployee(self, email_id, knowledge_date):
        # Retrieve the employee record by email
        employee = self.employee_accessor.get_employee(email_id)

        if not employee:
            raise ValueError(f"No employee found with email: {email_id}")

        # Call _invalidate_employee_record with the employee ID
        self._invalidate_employee_record(email_id, knowledge_date=knowledge_date)
