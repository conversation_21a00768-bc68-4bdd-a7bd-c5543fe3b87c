import datetime

from commission_engine.utils.general_data import Notification
from spm.models.notification_models import ClientNotification

DEFAULT_NOTIFICATION_STATUS = {
    "email": False,
    "slack": False,
    "ms_teams": False,
}


class ClientNotificationAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ClientNotification.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    # ------------- Read accessors -------------

    def get_all_notifications(self):
        return list(self.client_kd_aware())

    def get_notification(self, notification_name: str):
        return (
            self.client_kd_aware().filter(notification_name=notification_name).first()
        )

    def get_notifications_map(self):
        """Get a dictionary of all notifications with notification_name as key."""
        return {
            notification.notification_name: notification
            for notification in self.get_all_notifications()
        }

    def get_notification_status(self, notification: str | Notification):
        """
        Get notification status for a given notification. It is guaranteed that the status
        will have following keys:
        - email: bool
        - slack: bool
        - ms_teams: bool
        """
        if isinstance(notification, Notification):
            notification = notification.name

        notif = self.get_notification(notification)
        return (
            (notif.status or DEFAULT_NOTIFICATION_STATUS)
            if notif
            else DEFAULT_NOTIFICATION_STATUS
        )

    def is_notification_enabled(
        self, notification: str | Notification, mode: str
    ) -> bool:
        """
        Get the status of a notification for a given channel.
        Channel could be email, slack or ms_teams.
        """
        notif_status = self.get_notification_status(notification)
        return notif_status.get(mode, False)

    def get_not_opted_out_payee_notifications(self):
        """
        Get all payee notifications which are not opted out.
        Used to add entries in notification_tasks table on send-invite.
        """
        return list(
            self.client_kd_aware()
            .filter(can_payee_opt_out=False, is_admin_notification=False)
            .values("notification_name", "status", "frequency", "can_payee_opt_out")
        )

    def check_notification_enabled(self, notification_name: str) -> bool:
        task = self.get_notification(notification_name)
        # If the notification is not present or status is not present, then its either a
        # payee notification task or the notification control has been given to payees.
        # Consider such notifications as enabled.
        if not task or not task.status:
            return True

        # If notification is enabled in any one of the channels.
        return any(task.status.values())

    # ------------- Write accessors -------------

    def invalidate_notifications(self, notif_names: list[str], kd: datetime.datetime):
        return (
            self.client_kd_aware()
            .filter(notification_name__in=notif_names)
            .update(knowledge_end_date=kd)
        )

    @staticmethod
    def persist_notifications(notifications):
        return ClientNotification.objects.bulk_create(notifications)
