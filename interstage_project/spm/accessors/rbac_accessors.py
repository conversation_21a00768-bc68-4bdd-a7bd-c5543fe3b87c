from typing import List, Set

from django.db.models import Q, QuerySet

from commission_engine.utils.general_data import RBAC, RbacPermissions
from spm.models.rbac_models import Permissions, RolePermissions


class PermissionsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return Permissions.objects.filter(client=self.client_id)

    def get_active_records(self):
        return self.client_aware().filter(is_active=True, show_to_user=True)

    def get_all_permissions(self):
        return list(
            self.get_active_records().values(
                "permission_id",
                "parent_id",
                "component_system_name",
                "permission_name",
                "permission_description",
            )
        )

    def get_all_distinct_component_details(self):
        return (
            self.get_active_records()
            .values(
                "component_system_name",
                "component_display_name",
                "show_data_permissions",
                "component_order",
            )
            .distinct("component_system_name")
        )


class RolePermissionsAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return RolePermissions.objects.filter(client=self.client_id)

    def client_kd_aware(self) -> QuerySet:
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def show_user_roles(self):
        return list(
            self.client_kd_aware()
            .filter(show_to_user=True)
            .values(
                "role_permission_id",
                "display_name",
                "description",
                "updated_at",
                "is_editable",
                "created_at",
            )
        )

    def get_role_by_role_permission_id(self, role_permission_id, projection=None):
        if projection is None:
            projection = []
        if isinstance(role_permission_id, list):
            return (
                self.client_kd_aware()
                .filter(role_permission_id__in=role_permission_id)
                .values(*projection)
            )
        return (
            self.client_kd_aware()
            .filter(role_permission_id=role_permission_id)
            .values(*projection)
            .first()
        )

    def get_role_by_role_permission_ids(self, role_permission_ids, values=False):
        qs = self.client_kd_aware().filter(role_permission_id__in=role_permission_ids)
        if values:
            return list(qs.values())
        return list(qs)

    def is_name_exist(self, name):
        return self.client_kd_aware().filter(display_name__iexact=name).exists()

    def get_role_display_names(self):
        qs = self.client_kd_aware().values_list("display_name", flat=True)
        return list(qs)

    def get_role_ids_and_display_names(self):
        qs = (
            self.client_kd_aware()
            .filter(show_to_user=True)
            .values("role_permission_id", "display_name")
        )
        return list(qs)

    def invalidate_role(self, role_permission_id, ked):
        self.client_kd_aware().filter(role_permission_id=role_permission_id).update(
            knowledge_end_date=ked
        )

    def invalidate_by_roles(self, role_permission_ids, ked):
        self.client_kd_aware().filter(
            role_permission_id__in=role_permission_ids
        ).update(knowledge_end_date=ked)

    def is_role_permission_id_exists(self, role_permission_id):
        return (
            self.client_kd_aware()
            .filter(role_permission_id=role_permission_id)
            .exists()
        )

    def does_all_given_role_permissions_exist(self, role_permission_ids: List | Set):
        return self.client_kd_aware().filter(
            role_permission_id__in=role_permission_ids
        ).count() == len(set(role_permission_ids))

    def create_objects(self, records):  # each record is a dict
        role_permission_records = []
        if isinstance(records, list):
            for record in records:
                if isinstance(record, dict):
                    record = RolePermissions(**record)
                record.pk = None
                role_permission_records.append(record)
        try:
            RolePermissions.objects.bulk_create(role_permission_records)
        except Exception as e:
            raise e

    def update_objects(self, records, role_permission_ids, ked):
        self.invalidate_by_roles(role_permission_ids, ked)
        self.create_objects(records)

    def invalidate_and_update_role(self, impersonate_user_roles, time, audit):
        for record in impersonate_user_roles:
            record.knowledge_begin_date = time
            record.additional_details = audit
        RolePermissionsAccessor(self.client_id).create_objects(impersonate_user_roles)

    def get_impersonate_users(self, role_permission_id):
        return list(
            self.client_kd_aware().filter(
                **{
                    "permissions__manage_users__impersonated_user_roles__contains": [
                        role_permission_id
                    ]
                }
            )
        )

    def invalidate_impersonate_users(self, role_permission_id, ked):
        return (
            self.client_kd_aware().filter(
                **{
                    "permissions__manage_users__impersonated_user_roles__contains": [
                        role_permission_id
                    ]
                }
            )
        ).update(knowledge_end_date=ked)

    def is_payout_value_others_permission(self, role_permission_ids):
        if isinstance(role_permission_ids, str):
            return (
                self.client_kd_aware()
                .filter(
                    role_permission_id=role_permission_ids,
                    **{
                        "permissions__payouts_statements__permissions__contains": "view:payoutvalueothers"
                    },
                )
                .exists()
            )
        return (
            self.client_kd_aware()
            .filter(
                role_permission_id__in=role_permission_ids,
                **{
                    "permissions__payouts_statements__permissions__contains": "view:payoutvalueothers"
                },
            )
            .exists()
        )

    @staticmethod
    def are_roles_power_admin_in_any_client(role_permission_ids: list[str]):
        """Check whether the given perm IDs has power admin permissions in any client"""
        return RolePermissions.objects.filter(
            permissions__advanced_permissions__permissions__contains=RbacPermissions.MANAGE_ALLADMINS.value,
            role_permission_id__in=role_permission_ids,
        ).exists()

    def get_all_power_admins_role_id(self):
        return list(
            self.client_kd_aware()
            .filter(
                **{
                    "permissions__advanced_permissions__permissions__contains": RbacPermissions.MANAGE_ALLADMINS.value
                }
            )
            .values_list("role_permission_id", flat=True)
        )

    def get_power_admins_roles(self) -> QuerySet:
        return self.client_kd_aware().filter(
            **{
                "permissions__advanced_permissions__permissions__contains": RbacPermissions.MANAGE_ALLADMINS.value
            }
        )

    def is_role_power_admin(self, role_permission_id: str) -> bool:
        return (
            self.client_kd_aware()
            .filter(
                role_permission_id=role_permission_id,
                **{
                    "permissions__advanced_permissions__permissions__contains": RbacPermissions.MANAGE_ALLADMINS.value
                },
            )
            .exists()
        )

    def get_all_non_poweradmin_roles_with_manage_roles_permission(self) -> List:
        return list(
            self.client_kd_aware()
            .filter(
                Q(
                    permissions__advanced_permissions__permissions__contains=RbacPermissions.MANAGE_ROLES.value
                )
                & ~Q(
                    permissions__advanced_permissions__permissions__contains=RbacPermissions.MANAGE_ALLADMINS.value
                )
            )
            .values_list("role_permission_id", flat=True)
        )

    def does_any_non_poweradmin_role_has_manage_roles_permission(
        self, role_permission_ids: List[str]
    ):
        return (
            self.client_kd_aware()
            .filter(role_permission_id__in=role_permission_ids)
            .filter(
                Q(
                    permissions__advanced_permissions__permissions__contains=RbacPermissions.MANAGE_ROLES.value
                )
                & ~Q(
                    permissions__advanced_permissions__permissions__contains=RbacPermissions.MANAGE_ALLADMINS.value
                )
            )
            .exists()
        )

    def get_all_roles_based_on_queries_permission(self, ui_permission):
        return list(
            self.client_kd_aware()
            .filter(**{"permissions__queries__permissions__contains": ui_permission})
            .values_list("role_permission_id", flat=True)
        )

    def get_all_roles_with_all_queries_permission(self):
        return list(
            self.client_kd_aware()
            .filter(
                permissions__queries__permissions__contains=[
                    RbacPermissions.VIEW_QUERIES.value,
                    RbacPermissions.EDIT_QUERIES.value,
                    RbacPermissions.CREATE_QUERIES.value,
                ]
            )
            .filter(
                permissions__queries__data_permission__type__in=[
                    RBAC.ALL_DATA.value,
                    RBAC.INDIVIDUAL_AND_TEAM_DATA.value,
                ]
            )
            .values_list("role_permission_id", flat=True)
        )

    def get_all_roles(self, projection=None):
        if projection is None:
            projection = []
        return list(self.client_kd_aware().values(*projection))

    def is_view_payroll_permission(self, role_permission_ids):
        if isinstance(role_permission_ids, str):
            return (
                self.client_kd_aware()
                .filter(
                    role_permission_id=role_permission_ids,
                    **{
                        "permissions__manage_users__permissions__contains": "view:payroll"
                    },
                )
                .exists()
            )
        return (
            self.client_kd_aware()
            .filter(
                role_permission_id__in=role_permission_ids,
                **{"permissions__manage_users__permissions__contains": "view:payroll"},
            )
            .exists()
        )

    def is_own_data_permission(self, role_permission_ids):
        if isinstance(role_permission_ids, str):
            return (
                self.client_kd_aware()
                .filter(
                    role_permission_id=role_permission_ids,
                    **{
                        "permissions__advanced_permissions__permissions__contains": "manage:owndata"
                    },
                )
                .exists()
            )
        return (
            self.client_kd_aware()
            .filter(
                role_permission_id__in=role_permission_ids,
                **{
                    "permissions__advanced_permissions__permissions__contains": "manage:owndata"
                },
            )
            .exists()
        )

    def get_databook_permissions(self, role_permission_ids):
        if isinstance(role_permission_ids, str):
            return (
                self.client_kd_aware()
                .filter(role_permission_id=role_permission_ids)
                .values_list("permissions__databooks__permissions", flat=True)
                .first()
            )
        return (
            self.client_kd_aware()
            .filter(role_permission_id__in=role_permission_ids)
            .values_list("permissions__databooks__permissions", flat=True)
            .first()
        )

    def get_commission_plan_permission_roles(self):
        return (
            self.client_kd_aware()
            .filter(
                **{
                    "permissions__commission_plans__permissions__contains": RbacPermissions.VIEW_COMMISSIONPLAN.value
                }
            )
            .values("role_permission_id", "permissions__commission_plans")
        )

    def get_roles_based_on_plan_scope(self, permission_type, permission_value):
        return (
            self.client_kd_aware()
            .filter(
                **{
                    f"permissions__commission_plans__plans_scope__{permission_type}": permission_value
                }
            )
            .values_list("role_permission_id", flat=True)
        )

    def get_roles_based_on_plan_permissions(self, permission):
        return (
            self.client_kd_aware()
            .filter(
                **{"permissions__commission_plans__permissions__contains": permission}
            )
            .values_list("role_permission_id", flat=True)
        )

    # not used - RBAC CP
    def get_view_and_edit_commission_plan_roles(self):
        return (
            self.client_kd_aware()
            .filter(
                Q(
                    permissions__commission_plans__permissions__contains=RbacPermissions.VIEW_COMMISSIONPLAN.value
                )
                | Q(
                    permissions__commission_plans__permissions__contains=RbacPermissions.EDIT_COMMISSIONPLAN.value
                )
            )
            .values("role_permission_id", "permissions__commission_plans__permissions")
        )

    def get_all_roles_with_global_search_permission(self):
        return list(
            self.client_kd_aware()
            .filter(
                **{
                    "permissions__global_search__permissions__contains": RbacPermissions.VIEW_GLOBAL_SEARCH.value
                }
            )
            .values_list("role_permission_id", flat=True)
        )
