from enum import Enum


class APPROVAL_WORKFLOW_STATUS(Enum):
    WITHDRAWN = "withdrawn"
    APPROVED = "approved"
    REJECTED = "rejected"
    REQUESTED = "requested"
    NOT_STARTED = "not_started"
    STARTED = "started"
    ABORTED = "aborted"
    COMPLETED = "completed"
    NOT_REQUESTED = "not_requested"
    PENDING = "pending"
    CANCELLED = "cancelled"
    DECLINED = "declined"
    NEEDS_ATTENTION = "needs_attention"
    REVOKED = "revoked"
    REVOKED_TITLE = "revoked_title"


APPROVAL_WORKFLOW_STATUS_EXPORT = {
    APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value: "Withdrawn",
    APPROVAL_WORKFLOW_STATUS.APPROVED.value: "Approved",
    APPROVAL_WORKFLOW_STATUS.REJECTED.value: "Rejected",
    APPROVAL_WORKFLOW_STATUS.REQUESTED.value: "Requested",
    APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value: "Not Started",
    APPROVAL_WORKFLOW_STATUS.STARTED.value: "Started",
    APPROVAL_WORKFLOW_STATUS.ABORTED.value: "Aborted",
    APPROVAL_WORKFLOW_STATUS.COMPLETED.value: "Completed",
    APPROVAL_WORKFLOW_STATUS.NOT_REQUESTED.value: "Not Requested",
    APPROVAL_WORKFLOW_STATUS.PENDING.value: "Pending",
    APPROVAL_WORKFLOW_STATUS.CANCELLED.value: "Cancelled",
    APPROVAL_WORKFLOW_STATUS.DECLINED.value: "Declined",
    APPROVAL_WORKFLOW_STATUS.NEEDS_ATTENTION.value: "Needs Attention",
    APPROVAL_WORKFLOW_STATUS.REVOKED.value: "Revoked",
    APPROVAL_WORKFLOW_STATUS.REVOKED_TITLE.value: "Revoked Title",
}

ENTITY_KEY_DELIMETER = "##::##"


class DYNAMIC_TYPES(Enum):
    PAYEE = "payee"
    MANAGER = "manager"
    PREVIOUS_APPROVER_MANAGER = "previous_approver_manager"
    ALL_APPROVERS = "all_approvers"
    ALL_PREVIOUSLY_APPROVED = "all_previously_approved"
    MANAGER_OF_INITIATOR = "manager_of_initiator"
    INITIATOR = "initiator"
    ALL_PLAN_OWNERS = "all_plan_owners"


class SKIP_NOTIFICATION_DYNAMIC_TYPES(Enum):
    PAYEE = "payee"
    REPORTING_MANAGER = "reporting_manager"


class APPROVAL_ENTITY_TYPES(Enum):
    ALL = "all"
    PAYOUT = "payout"
    COMMISSION_ADJUSTMENT = "commission_adjustment"
    PAYOUT_LINE_ITEM = "payout_line_item"
    QUOTE = "quote"
    COMMISSION_PLAN = "commission_plan"


class APPROVAL_TRIGGER_TYPES(Enum):
    APPROVED = "approved"


class APPROVAL_STAGE_STRATEGY(Enum):
    ANYONE = "anyone"
    EVERYONE = "everyone"


class APPROVAL_EMAIL_NOTIFICATION_TEMPLATE(Enum):
    NEW_REQUEST_SINGLE = "d-732afb44d90d496594f954503f2341ec"
    NEW_REQUEST_BULK = "d-96249b9612654a9ba51c7ea1048ee6a8"
    NEW_REQUEST_BULK_NEXT_STAGE = "d-00d8f77a748c4465bb902913aa246872"
    DAILY_REMINDER = "d-0c3918aa372844e9b4fe8898894ee4b0"
    SEND_REMINDER_EMAIL = "d-5701676fa0cb4b328711e768b44502f3"
    REQUEST_REJECTION_EMAIL = "d-3ffa2819026e48faa61dc839f05459ac"
    REQUEST_APPROVAL_EMAIL = "d-f19cbcd86e3e48469d123017284aa967"
    REQUEST_APPROVE_REJECT_FAILURE = "d-1ebcaf58a2d24480a66f0baf9d9e4d50"
    FAILURE_ATTACHMENT_EMAIL = "d-8595e6d1d45d4c53a1ef40262624f7eb"
    NOTIFY_CORRECTIONS = "d-a5764579788b440cbc9429df1da8ed3b"


class COMMISSION_ADJUSTMENT_APPROVAL_EMAIL_TEMPLATE(Enum):
    NEW_REQUEST = "d-7628589c1b0e44fb9b9bef4865fea205"
    SEND_REMINDER_EMAIL = "d-7c74e45484a340b09377baa32b5e1ec1"
    REJECTED = "d-2dc43d01a7ce4914b0db747c80181e82"
    APPROVED = "d-86ee764b5a0945e5938fb058946e2902"
    NOTIFY_WHEN_SKIPPED = "d-0de8974c86354619ae1442cc45959692"
    BULK_NEW_REQUESTS = "d-72e3048299a94b80bcc5277151e75894"


class QUOTE_APPROVAL_EMAIL_TEMPLATE(Enum):
    NEW_REQUEST = "d-f300b74d915842a69c8fbbc54e446179"
    DAILY_REMINDER = "d-8c815d4d32214f97ac2da5d7f0569e83"
    APPROVED = "d-df620aadc1db4e21a971587a6ef88905"
    REJECTED = "d-d75c0cdb32f941a2b80ae5c3ef05789a"
    PERSIST_APPROVAL = "d-b98670f917764be69992204daf2aa3be"


class COMMISSION_PLAN_APPROVAL_EMAIL_TEMPLATE(Enum):
    REJECTED = "d-539cf00d87044ad98b4f2a76fa887275"
    APPROVED = "d-9d6d63e7322c407ab9eba0e9f59bc13f"
    REVOKED = "d-258d39f668614a969b6b0b63ecf7c3c4"


ENTITY_KEY_DELIMETER = "##::##"

SUB_ENTITY_KEY_DELIMETER = "::"


class APPROVAL_NOTIFICATION_TYPES(Enum):
    NEW_APPROVAL_REQUEST_NOTIFICATION = "NEW_APPROVAL_REQUEST_NOTIFICATION"
    EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION = (
        "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION"
    )


# This class is used for the purpose of using email in frontend
# If the purpose is notify we need not notify everstage.support users
email_for_notify = "notify"


class APPROVAL_STAGE_STATUS(Enum):
    ON_TRACK = "on_track"
    OVERDUE = "overdue"
