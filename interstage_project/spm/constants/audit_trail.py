AUDIT_TRAIL_EVENT_TYPE = {
    "DELETE_DATA-SOURCE": {
        "code": "DELETE_DATA-SOURCE",
        "display_name": "Delete Custom Object",
    },
    "INVALIDATE_DATA-SOURCE-DATA": {
        "code": "INVALIDATE_DATA-SOURCE-DATA",
        "display_name": "Invalidate Custom Object Data",
    },
    "DELETE_DATA-SOURCE-VARIABLE": {
        "code": "DELETE_DATA-SOURCE-VARIABLE",
        "display_name": "Delete Custom Object Variable",
    },
    "CREATE_TEAM": {"code": "CREATE_TEAM", "display_name": "Created Team"},
    "RENAME_TEAM": {"code": "RENAME_TEAM", "display_name": "Renamed Team"},
    "DELETE_TEAM": {"code": "DELETE_TEAM", "display_name": "Deleted Team"},
    "CREATE_USER": {"code": "CREATE_USER", "display_name": "User Created"},
    "UPDATE_USER": {
        "code": "UPDATE_USER",
        "display_name": "Updated User Details",
    },
    "EDIT_USER": {"code": "EDIT_USER", "display_name": "Edited User Details"},
    "DELETE_USER-DETAILS": {
        "code": "DELETE_USER-DETAILS",
        "display_name": "Deleted User Details",
    },
    "UPDATE_MANAGER": {
        "code": "UPDATE_MANAGER",
        "display_name": "Updated Reporting Manager",
    },
    "EDIT_MANAGER": {
        "code": "EDIT_MANAGER",
        "display_name": "Edited Reporting Manager",
    },
    "DELETE_MANAGER": {
        "code": "DELETE_MANAGER",
        "display_name": "Deleted Reporting Manager",
    },
    "CREATE_QUERY_CATEGORY": {
        "code": "CREATE_QUERY_CATEGORY",
        "display_name": "Query Category Created",
    },
    "UPDATE_QUERY_CATEGORY": {
        "code": "UPDATE_QUERY_CATEGORY",
        "display_name": "Query Category Updated",
    },
    "ARCHIVE_QUERY_CATEGORY": {
        "code": "ARCHIVE_QUERY_CATEGORY",
        "display_name": "Query Category Archived",
    },
    "UNARCHIVE_QUERY_CATEGORY": {
        "code": "UNARCHIVE_QUERY_CATEGORY",
        "display_name": "Query Category Unarchived",
    },
    "CREATE_QUERY": {"code": "CREATE_QUERY", "display_name": "Query Created"},
    "UPDATE_QUERY": {"code": "UPDATE_QUERY", "display_name": "Query Updated"},
    "CREATE_DRAWS": {
        "code": "CREATE_DRAWS",
        "display_name": "Added Draw Schedule",
    },
    "UPDATE_DRAWS": {
        "code": "UPDATE_DRAWS",
        "display_name": "Updated Draw Schedule",
    },
    "CREATE_QUOTA": {"code": "CREATE_QUOTA", "display_name": "Added $QUOTA"},
    "UPDATE_QUOTA": {"code": "UPDATE_QUOTA", "display_name": "Updated $QUOTA"},
    "BULK_MANAGE-DATA": {
        "code": "BULK_MANAGE-DATA",
        "display_name": "Bulk Data Upload",
    },
    "BULK_CREATE_QUOTA": {
        "code": "BULK_CREATE_QUOTA",
        "display_name": "Added $QUOTA in Bulk",
    },
    "BULK_UPDATE_QUOTA": {
        "code": "BULK_UPDATE_QUOTA",
        "display_name": "Updated $QUOTA in Bulk",
    },
    "BULK_EFFECTIVE_QUOTA": {
        "code": "BULK_EFFECTIVE_QUOTA",
        "display_name": "Updated Effective $QUOTA in Bulk",
    },
    "CREATE_DATA-FEED": {
        "code": "CREATE_DATA-FEED",
        "display_name": "Created Data Feed",
    },
    "UPDATE_DATA-FEED": {
        "code": "UPDATE_DATA-FEED",
        "display_name": "Updated Data Feed",
    },
    "DELETE_DATA-FEED": {
        "code": "DELETE_DATA-FEED",
        "display_name": "Deleted Data Feed",
    },
    "PUBLISH_PLAN": {
        "code": "PUBLISH_PLAN",
        "display_name": "Published $COMMISSION Plan",
    },
    "UPDATE_PLAN": {
        "code": "UPDATE_PLAN",
        "display_name": "Updated $COMMISSION Plan",
    },
    "CREATE_VARIABLE": {
        "code": "CREATE_VARIABLE",
        "display_name": "Added Variable",
    },
    "RENAME_VARIABLE": {
        "code": "RENAME_VARIABLE",
        "display_name": "Renamed Variable",
    },
    "CREATE_COMMISSION-FEED": {
        "code": "CREATE_COMMISSION-FEED",
        "display_name": "Created $COMMISSION Feed",
    },
    "UPDATE_COMMISSION-FEED": {
        "code": "UPDATE_COMMISSION-FEED",
        "display_name": "Modified $COMMISSION Feed",
    },
    "RENAME_COMMISSION-FEED": {
        "code": "RENAME_COMMISSION-FEED",
        "display_name": "Renamed $COMMISSION Feed",
    },
    "DELETE_COMMISSION-FEED": {
        "code": "DELETE_COMMISSION-FEED",
        "display_name": "Deleted $COMMISSION Feed",
    },
    "CREATE_CALCULATED-FEED": {
        "code": "CREATE_CALCULATED-FEED",
        "display_name": "Created Calculated  Feed",
    },
    "UPDATE_CALCULATED-FEED": {
        "code": "UPDATE_CALCULATED-FEED",
        "display_name": "Modified Calculated  Feed",
    },
    "RENAME_CALCULATED-FEED": {
        "code": "RENAME_CALCULATED-FEED",
        "display_name": "Renamed Calculated  Feed",
    },
    "DELETE_CALCULATED-FEED": {
        "code": "DELETE_CALCULATED-FEED",
        "display_name": "Deleted Calculated  Feed",
    },
    "LOGIN": {"code": "LOGIN", "display_name": "User Logged In"},
    "LOGOUT": {"code": "LOGOUT", "display_name": "User Logged Out"},
    "LOGIN_AS": {"code": "LOGIN_AS", "display_name": "User Impersonation"},
    "LOGOUT_AS": {"code": "LOGOUT_AS", "display_name": "User Impersonation"},
    "FREEZE_FREEZE-COMMISSION": {
        "code": "FREEZE_FREEZE-COMMISSION",
        "display_name": "Locked $COMMISSIONS",
    },
    "UNFREEZE_FREEZE-COMMISSION": {
        "code": "UNFREEZE_FREEZE-COMMISSION",
        "display_name": "Unlocked $COMMISSIONS",
    },
    "PAID_PAID-COMMISSION": {
        "code": "PAID_PAID-COMMISSION",
        "display_name": "Marked as Paid",
    },
    "UNPAID_PAID-COMMISSION": {
        "code": "UNPAID_PAID-COMMISSION",
        "display_name": "Marked as Unpaid",
    },
    "ADD_ADJUSTMENTS-COMMISSION": {
        "code": "ADD_ADJUSTMENTS-COMMISSION",
        "display_name": "Adjusted $COMMISSION",
    },
    "ADD_ADJUSTMENTS-SPLIT": {
        "code": "ADD_ADJUSTMENTS-SPLIT",
        "display_name": "Adjusted Split Deal",
    },
    "ADD_ADJUSTMENTS-DEAL": {
        "code": "ADD_ADJUSTMENTS-DEAL",
        "display_name": "Adjusted Deal Amount",
    },
    "ADD_ADJUSTMENTS-DRAWS": {
        "code": "ADD_ADJUSTMENTS-DRAWS",
        "display_name": "Adjusted Recover Draw",
    },
    "ADD_ADJUSTMENTS-LINE-ITEM": {
        "code": "ADD_ADJUSTMENTS-LINE-ITEM",
        "display_name": "Adjusted Ignore Line Item",
    },
    "CREATE_ADJUSTMENTS_CATEGORY": {
        "code": "CREATE_ADJUSTMENTS_CATEGORY",
        "display_name": "Adjustments Category Created",
    },
    "UPDATE_ADJUSTMENTS_CATEGORY": {
        "code": "UPDATE_ADJUSTMENTS_CATEGORY",
        "display_name": "Adjustments Category Updated",
    },
    "ARCHIVE_ADJUSTMENTS_CATEGORY": {
        "code": "ARCHIVE_ADJUSTMENTS_CATEGORY",
        "display_name": "Adjustments Category Archived",
    },
    "UNARCHIVE_ADJUSTMENTS_CATEGORY": {
        "code": "UNARCHIVE_ADJUSTMENTS_CATEGORY",
        "display_name": "Adjustments Category Unarchived",
    },
    "UPDATE_USERS-PLAN": {
        "code": "UPDATE_USERS-PLAN",
        "display_name": "Updated User's $COMMISSION Plan",
    },
    "EDIT_USERS-PLAN": {
        "code": "EDIT_USERS-PLAN",
        "display_name": "Edited User's $COMMISSION Plan",
    },
    "UPDATE_USERS-SPIFF-PLAN": {
        "code": "UPDATE_USERS-SPIFF-PLAN",
        "display_name": "Updated SPIFF Plans",
    },
    "UPDATE_SETTINGS": {
        "code": "UPDATE_SETTINGS",
        "display_name": "Settings Updated",
    },
    "UPLOAD_DATA": {"code": "UPLOAD_DATA", "display_name": "Data Uploaded"},
    "CREATE_CUSTOM-OBJECT": {
        "code": "CREATE_CUSTOM-OBJECT",
        "display_name": "Created Custom object",
    },
    "ADD_CUSTOM-OBJECT-VARIABLE": {
        "code": "ADD_CUSTOM-OBJECT-VARIABLE",
        "display_name": "Added a custom object variable",
    },
    "RENAME_CUSTOM-OBJECT-VARIABLE": {
        "code": "RENAME_CUSTOM-OBJECT-VARIABLE",
        "display_name": "Renamed a custom object variable",
    },
    "CREATE_HYPERLINK-DATA": {
        "code": "CREATE_HYPERLINK-DATA",
        "display_name": "Created Hyperlink Data",
    },
    "UPDATE_HYPERLINK-DATA": {
        "code": "UPDATE_HYPERLINK-DATA",
        "display_name": "Updated Hyperlink Data",
    },
    "CREATE_PRICE_BOOK": {
        "code": "CREATE_PRICE_BOOK",
        "display_name": "Created Price Book",
    },
    "UPDATE_PRICE_BOOK": {
        "code": "UPDATE_PRICE_BOOK",
        "display_name": "Updated Price Book",
    },
    "DELETE_PRICE_BOOK": {
        "code": "DELETE_PRICE_BOOK",
        "display_name": "Deleted Price Book",
    },
    "LIST_PRICE_BOOKS": {
        "code": "LIST_PRICE_BOOKS",
        "display_name": "List Price Books",
    },
    "CLONE_PRICE_BOOK": {
        "code": "CLONE_PRICE_BOOK",
        "display_name": "Cloned Price Book",
    },
    "CREATE_DATABOOK": {
        "code": "CREATE_DATABOOK",
        "display_name": "Created Databook",
    },
    "CLONE_DATABOOK": {
        "code": "CLONE_DATABOOK",
        "display_name": "Cloned Databook",
    },
    "RENAME_DATABOOK": {
        "code": "RENAME_DATABOOK",
        "display_name": "Renamed Databook",
    },
    "UPDATE_DATABOOK": {
        "code": "UPDATE_DATABOOK",
        "display_name": "Update Databook Meta",
    },
    "DELETE_DATABOOK": {
        "code": "DELETE_DATABOOK",
        "display_name": "Deleted Databook",
    },
    "CREATE_DATASHEET": {
        "code": "CREATE_DATASHEET",
        "display_name": "Created Datasheet",
    },
    "CLONE_DATASHEET": {
        "code": "CLONE_DATASHEET",
        "display_name": "Cloned Datasheet",
    },
    "EDIT_DATASHEET": {
        "code": "EDIT_DATASHEET",
        "display_name": "Edited Datasheet",
    },
    "DELETE_DATASHEET": {
        "code": "DELETE_DATASHEET",
        "display_name": "Deleted Datasheet",
    },
    "EXPORT_DATASHEET": {
        "code": "EXPORT_DATASHEET",
        "display_name": "Export Datasheet",
    },
    "EMPLOYEE_EXIT": {
        "code": "EMPLOYEE_EXIT",
        "display_name": "Initiated exit (Employee eff.end date modified)",
    },
    "EMPLOYEE_EXIT-COMM": {
        "code": "EMPLOYEE_EXIT-COMM",
        "display_name": "Initiated exit (Comm.plan eff.end date modified)",
    },
    "CREATE_DATASHEET-ADJUSTMENT": {
        "code": "CREATE_DATASHEET-ADJUSTMENT",
        "display_name": "Created Datasheet $ADJUSTMENT",
    },
    "UPDATE_DATASHEET-ADJUSTMENT": {
        "code": "UPDATE_DATASHEET-ADJUSTMENT",
        "display_name": "Updated Datasheet $ADJUSTMENT",
    },
    "REVERT_DATASHEET-ADJUSTMENT": {
        "code": "REVERT_DATASHEET-ADJUSTMENT",
        "display_name": "Reverted Datasheet $ADJUSTMENT",
    },
    "CREATE_DATASHEET-FILTER": {
        "code": "CREATE_DATASHEET-FILTER",
        "display_name": "Created Filter",
    },
    "UPDATE_DATASHEET-FILTER": {
        "code": "UPDATE_DATASHEET-FILTER",
        "display_name": "Updated Filter",
    },
    "DELETE_DATASHEET-FILTER": {
        "code": "DELETE_DATASHEET-FILTER",
        "display_name": "Deleted Filter",
    },
    "CREATE_DATASHEET-PIVOT": {
        "code": "CREATE_DATASHEET-PIVOT",
        "display_name": "Created Pivot",
    },
    "UPDATE_DATASHEET-PIVOT": {
        "code": "UPDATE_DATASHEET-PIVOT",
        "display_name": "Updated Pivot",
    },
    "DELETE_DATASHEET-PIVOT": {
        "code": "DELETE_DATASHEET-PIVOT",
        "display_name": "Deleted Pivot",
    },
    "SAVE_CONTRACT-FIELD": {
        "code": "SAVE_CONTRACT-FIELD",
        "display_name": "Saved Contract Fields",
    },
    "CONNECT_DOCUSIGN": {
        "code": "CONNECT_DOCUSIGN",
        "display_name": "Docusign Connected",
    },
    "DISCONNECT_DOCUSIGN": {
        "code": "DISCONNECT_DOCUSIGN",
        "display_name": "Docusign Disconnected",
    },
    "CREATE_CONTRACT": {
        "code": "CREATE_CONTRACT",
        "display_name": "Created Contract",
    },
    "CREATE_CUSTOM-FIELD": {
        "code": "CREATE_CUSTOM-FIELD",
        "display_name": "Created Custom Field",
    },
    "UPDATE_CUSTOM-FIELD": {
        "code": "UPDATE_CUSTOM-FIELD",
        "display_name": "Updated Custom Field",
    },
    "DELETE_CUSTOM-FIELD": {
        "code": "DELETE_CUSTOM-FIELD",
        "display_name": "Deleted Custom Field",
    },
    "UPDATE_CUSTOM-FIELD-DATA": {
        "code": "UPDATE_CUSTOM-FIELD-DATA",
        "display_name": "Updated Custom Field data",
    },
    "DELETE_CUSTOM-FIELD-DATA": {
        "code": "DELETE_CUSTOM-FIELD-DATA",
        "display_name": "Deleted Custom Field data",
    },
    "DELETE_USER": {
        "code": "DELETE_USER",
        "display_name": "Delete User(s)",
    },
    "CREATE_DASHBOARD": {
        "code": "CREATE_DASHBOARD",
        "display_name": "Created Dashboard",
    },
    "UPDATE_DASHBOARD": {
        "code": "UPDATE_DASHBOARD",
        "display_name": "Updated Dashboard",
    },
    "DELETE_DASHBOARD": {
        "code": "DELETE_DASHBOARD",
        "display_name": "Deleted Dashboard",
    },
    "RENAME_DASHBOARD": {
        "code": "RENAME_DASHBOARD",
        "display_name": "Renamed Dashboard",
    },
    "SHARE_DASHBOARD": {
        "code": "SHARE_DASHBOARD",
        "display_name": "Shared Dashboard",
    },
    "CREATE_WIDGET": {
        "code": "CREATE_WIDGET",
        "display_name": "Created Widget",
    },
    "DELETE_WIDGET": {
        "code": "DELETE_WIDGET",
        "display_name": "Deleted Widget",
    },
    "PROCESS_ARREAR": {
        "code": "PROCESS_ARREAR",
        "display_name": "Process $ARREAR",
    },
    "IGNORE_ARREAR": {
        "code": "IGNORE_ARREAR",
        "display_name": "Ignore $ARREAR",
    },
    "INITIATE_PAYOUT": {
        "code": "INITIATE_PAYOUT",
        "display_name": "Register Payment",
    },
    "INVALIDATE_PAYOUT": {
        "code": "INVALIDATE_PAYOUT",
        "display_name": "Invalidate $PAYOUT",
    },
    "FREEZE_FREEZE-SETTLEMENT": {
        "code": "FREEZE_FREEZE-SETTLEMENT",
        "display_name": "Locked $PAYOUT",
    },
    "UNFREEZE_FREEZE-SETTLEMENT": {
        "code": "UNFREEZE_FREEZE-SETTLEMENT",
        "display_name": "Unlocked $PAYOUT",
    },
    "CREATE_USER-GROUP": {
        "code": "CREATE_USER-GROUP",
        "display_name": "Created Group",
    },
    "UPDATE_USER-GROUP": {
        "code": "UPDATE_USER-GROUP",
        "display_name": "Updated Group",
    },
    "DELETE_USER-GROUP": {
        "code": "DELETE_USER-GROUP",
        "display_name": " Deleted Group",
    },
    "CLONE_USER-GROUP": {
        "code": "CLONE_USER-GROUP",
        "display_name": " Clone Group",
    },
    "CREATE_APPROVAL-TEMPLATE": {
        "code": "CREATE_APPROVAL-TEMPLATE",
        "display_name": "Created Approval template",
    },
    "UPDATE_APPROVAL-TEMPLATE": {
        "code": "UPDATE_APPROVAL-TEMPLATE",
        "display_name": "Updated Approval template",
    },
    "DELETE_APPROVAL-TEMPLATE": {
        "code": "DELETE_APPROVAL-TEMPLATE",
        "display_name": "Deleted Approval template",
    },
    "CLONE_APPROVAL-TEMPLATE": {
        "code": "CLONE_APPROVAL-TEMPLATE",
        "display_name": "Cloned Approval template",
    },
    "CREATE_APPROVAL-INSTANCE": {
        "code": "CREATE_APPROVAL-INSTANCE",
        "display_name": "Create Approval Instance",
    },
    "UPDATE_APPROVAL-CONFIG": {
        "code": "UPDATE_APPROVAL-CONFIG",
        "display_name": "Updated Approval Config",
    },
    "DELETE_APPROVAL-INSTANCES": {
        "code": "DELETE_APPROVAL-INSTANCES",
        "display_name": "Deleted Approval Instance",
    },
    "UPDATE_REEVALUATE-APPROVALS": {
        "code": "UPDATE_REEVALUATE-APPROVALS",
        "display_name": "Re-evaluate Approvals",
    },
    "APPROVE_REQUEST-APPROVALS": {
        "code": "APPROVE_REQUEST-APPROVALS",
        "display_name": "Approval Request Approved",
    },
    "REJECT_REQUEST-APPROVALS": {
        "code": "REJECT_REQUEST-APPROVALS",
        "display_name": "Approval Request Rejected",
    },
    "WITHDRAW_STAGE-APPROVALS": {
        "code": "WITHDRAW_STAGE-APPROVALS",
        "display_name": "Withdraw Stage Requests",
    },
    "REVOKE_REQUEST-APPROVALS": {
        "code": "REVOKE_REQUEST-APPROVALS",
        "display_name": "Revoke Approval Instance",
    },
    "V2_DATASHEET-EXPORT": {
        "code": "V2_DATASHEET-EXPORT",
        "display_name": "Export Datasheet",
    },
    "ADD_APPROVER_STAGE_ADD-APPROVERS": {
        "code": "ADD_APPROVER_STAGE_ADD-APPROVERS",
        "display_name": "Added Stage Approvers",
    },
    "CHANGE_DUE_DATE_STAGE-APPROVALS": {
        "code": "CHANGE_DUE_DATE_STAGE-APPROVALS",
        "display_name": "Changed Stage Due Date",
    },
    "EXPORT_STATEMENTS": {
        "code": "EXPORT_STATEMENTS",
        "display_name": "Export Statement",
    },
    "CREATE_ROLE-RBAC": {
        "code": "CREATE_ROLE-RBAC",
        "display_name": "Created Role",
    },
    "DELETE_ROLE-RBAC": {
        "code": "DELETE_ROLE-RBAC",
        "display_name": "Deleted Role",
    },
    "EDIT_ROLE-RBAC": {
        "code": "EDIT_ROLE-RBAC",
        "display_name": "Edited Role",
    },
    "CLONE_ROLE-RBAC": {
        "code": "CLONE_ROLE-RBAC",
        "display_name": "Cloned Role",
    },
    "STATEMENTS_EMAIL-STATEMENTS": {
        "code": "STATEMENTS_EMAIL-STATEMENTS",
        "display_name": "Email Statements Export",
    },
    "CREATE_CUSTOM-CALENDAR": {
        "code": "CREATE_CUSTOM-CALENDAR",
        "display_name": "Created Custom Calendar",
    },
    "UPDATE_CUSTOM-CALENDAR": {
        "code": "UPDATE_CUSTOM-CALENDAR",
        "display_name": "Updated Custom Calendar",
    },
    "DELETE_CUSTOM-CALENDAR": {
        "code": "DELETE_CUSTOM-CALENDAR",
        "display_name": "Deleted Custom Calendar",
    },
    "CLONE_CUSTOM-CALENDAR": {
        "code": "CLONE_CUSTOM-CALENDAR",
        "display_name": "Cloned Custom Calendar",
    },
    "SAVE_HRIS-CONFIG": {
        "code": "SAVE_HRIS-CONFIG",
        "display_name": "Saved HRIS Config",
    },
    "PROCESS_HRIS-UPDATES": {
        "code": "PROCESS_HRIS-UPDATES",
        "display_name": "Process HRIS Updates",
    },
    "REJECT_LINE_ITEM_REQUEST-APPROVALS": {
        "code": "REJECT_LINE_ITEM_REQUEST-APPROVALS",
        "display_name": "Reject approval request",
    },
    "APPROVE_LINE_ITEM_REQUEST-APPROVALS": {
        "code": "APPROVE_LINE_ITEM_REQUEST-APPROVALS",
        "display_name": "Approve approval request",
    },
    "ACCEPT_LINE_ITEM_REQUEST-APPROVALS": {
        "code": "ACCEPT_LINE_ITEM_REQUEST-APPROVALS",
        "display_name": "Approve line item level approval request",
    },
    "DECLINE_LINE_ITEM_REQUEST-APPROVALS": {
        "code": "DECLINE_LINE_ITEM_REQUEST-APPROVALS",
        "display_name": "Reject line item level approval request",
    },
    "CREATE_DATASHEET-TAG": {
        "code": "CREATE_DATASHEET-TAG",
        "display_name": "Created Datasheet Tag",
    },
    "UPDATE_DATASHEET-TAG": {
        "code": "UPDATE_DATASHEET-TAG",
        "display_name": "Updated Datasheet Tag",
    },
    "DELETE_DATASHEET-TAG": {
        "code": "DELETE_DATASHEET-TAG",
        "display_name": "Deleted Datasheet Tag",
    },
    "CREATE_DATASHEET-PIN": {
        "code": "CREATE_DATASHEET-PIN",
        "display_name": "Created Datasheet Pin",
    },
    "DELETE_DATASHEET-PIN": {
        "code": "DELETE_DATASHEET-PIN",
        "display_name": "Deleted Datasheet Pin",
    },
    "CREATE_DATASHEET-VIEW": {
        "code": "CREATE_DATASHEET-VIEW",
        "display_name": "Created Datasheet View",
    },
    "UPDATE_DATASHEET-VIEW": {
        "code": "UPDATE_DATASHEET-VIEW",
        "display_name": "Updated Datasheet View",
    },
    "DELETE_DATASHEET-VIEW": {
        "code": "DELETE_DATASHEET-VIEW",
        "display_name": "Deleted Datasheet View",
    },
    "CREATE_DATASHEET-VIEW-FILTER": {
        "code": "CREATE_DATASHEET-VIEW-FILTER",
        "display_name": "Created Filter For Datasheet View",
    },
    "RBAC_SHARED-PLAN-MEMBERS": {
        "code": "RBAC_SHARED-PLAN-MEMBERS",
        "display_name": "Shared Plan Member",
    },
    "CREATE_DATASHEET-VIEW-PIVOT": {
        "code": "CREATE_DATASHEET-VIEW-PIVOT",
        "display_name": "Created Pivot For Datasheet View",
    },
    "CREATE_APPROVALS-QUOTE-APPROVALS": {
        "code": "CREATE_APPROVALS-QUOTE-APPROVALS",
        "display_name": "Quote Approval Creation",
    },
    "EDIT_APPROVALS-QUOTE-APPROVALS": {
        "code": "EDIT_APPROVALS-QUOTE-APPROVALS",
        "display_name": "Quote Approval Edited",
    },
}

AUDIT_KEYS = {
    "DATA-SOURCE": [],
    "DATA-SOURCE-VARIABLE": [],
    "DATA-SOURCE-DATA": [],
    "QUERY": [
        "assignee",
        "updated_by",
        "subject",
        "status",
        "category",
        "involved_users",
    ],
    "CATEGORY": [],
    "DRAWS": [
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
        "Jan",
        "Feb",
        "Mar",
        "draw_year",
    ],
    "TEAM": ["team_name", "team_members"],
    "DATA-FEED": [
        "data_feed_tab_name",
        "data_feed_among_option",
        "data_feed_columns",
    ],
    "QUOTA": [
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
        "Jan",
        "Feb",
        "Mar",
        "quota_category_name",
        "quota_year",
        "quota_type",
        "quota_schedule_type",
    ],
    "PLAN": ["plan_name", "payees", "plan_start_date", "plan_end_date"],
    "VARIABLE": ["display_name", "model", "data_type"],
    "COMMISSION-FEED": [
        "tab_name",
        "plan_name",
        "period_start_date",
        "period_end_date",
        "payees",
    ],
    "CALCULATED-FEED": [
        "tab_name",
        "period_start_date",
        "period_end_date",
        "payees",
        "payout_frequency",
        "columns",
        "among_option",
    ],
    "FREEZE-COMMISSION": [
        "payee_name",
        "payee_email_id",
        "calculated_commission",
        "calculation_status",
        "employee_id",
        "is_locked",
        "payment_status",
    ],
    "FREEZE-SETTLEMENT": [
        "calculated_payout",
        "calculation_status",
        "payment_status",
        "pending_amt",
        "payee_ids",
        "month",
        "year",
        "is_locked",
    ],
    "PAID-COMMISSION": [
        "payee_name",
        "payee_email_id",
        "calculated_commission",
        "calculation_status",
        "employee_id",
        "payment_status",
        "paid_amount",
    ],
    "USER": [
        "employee_id",
        "user_role",
        "employee_email_id",
        "effective_start_date",
        "effective_end_date",
        "first_name",
        "last_name",
        "variable_pay",
        "joining_date",
        "payout_frequency",
        "pay_currency",
        "designation",
        "level",
        "employment_country",
        "exit_date",
        "last_commission_date",
    ],
    "USER-DETAILS": [],
    "MANAGER": [
        "reporting_manager_email_id",
        "manager_effective_start_date",
        "manager_effective_end_date",
        "effective_start_date",
        "effective_end_date",
    ],
    "ADJUSTMENTS-COMMISSION": [
        "adjustment_type",
        "criteria_id",
        "amount",
        "currency",
        "line_item_id",
        "period_start_date",
        "period_end_date",
        "plan_id",
        "reason_category",
    ],
    "ADJUSTMENTS-SPLIT": [
        "adjustment_type",
        "opportunity_id",
        "owner_id",
        "reason",
        "reason_category",
        "split_details",
    ],
    "ADJUSTMENTS-DEAL": [
        "adjustment_type",
        "deal_id",
        "new_amount",
        "reason",
        "reason_category",
    ],
    "ADJUSTMENTS-DRAWS": [
        "adjustment_type",
        "payee_id",
        "amount",
        "recoverable_balance",
        "fiscal_year",
        "period",
        "comments",
    ],
    "ADJUSTMENTS-LINE-ITEM": [
        "adjustment_type",
        "line_item_id",
        "line_item_type",
        "reason",
        "reason_category",
    ],
    "USERS-PLAN": [
        "plan_type",
        "effective_start_date",
        "effective_end_date",
        "plan_name",
    ],
    "USERS-SPIFF-PLAN": ["spiff_plans"],
    "SETTINGS": [
        "type",
        "value",
        "effective_start_date",
        "effective_end_date",
        "start_month",
        "end_month",
        "currency_type",
        "currency_value",
    ],
    "DATA": ["record_list"],
    "CUSTOM-OBJECT": ["variable"],
    "MANAGE-DATA": ["object_name", "file_name"],
    "CUSTOM-OBJECT-VARIABLE": ["variable"],
    "DATABOOK": ["is_draft", "is_archived", "name"],
    "DATASHEET": ["source_type", "name"],
    "EXIT": [
        "employee_id",
        "user_role",
        "employee_email_id",
        "effective_start_date",
        "effective_end_date",
        "first_name",
        "last_name",
        "variable_pay",
        "joining_date",
        "payout_frequency",
        "pay_currency",
        "designation",
        "level",
        "employment_country",
        "exit_date",
        "last_commission_date",
        "deactivation_date",
    ],
    "EXIT-COMM": [
        "plan_type",
        "effective_start_date",
        "effective_end_date",
        "plan_name",
    ],
    "DATASHEET-ADJUSTMENT": [],
    "DATASHEET-FILTER": [],
    "DATASHEET-PIVOT": [],
    "CONTRACT": [],
    "CONTRACT-FIELD": [],
    "DOCUSIGN": [],
    "CUSTOM-FIELD": [
        "display_name",
        "field_type",
        "options",
        "is_archived",
        "display_order",
        "is_mandatory",
    ],
    "CUSTOM-FIELD-DATA": ["data"],
    "DASHBOARD": ["name"],
    "WIDGET": [],
    "ARREAR": ["data"],
    "PAYOUT": ["payee_ids", "month", "year", "settlement_details", "payout_id"],
    "USER-GROUP": [
        "user_group_name",
    ],
    "STATEMENTS": [],
    "APPROVAL-TEMPLATE": ["template_name", "template_description"],
    "APPROVAL-INSTANCE": [],
    "REQUEST-APPROVALS": ["approval_type", "entity_keys"],
    "APPROVAL-INSTANCES": ["entity_keys", "request_ids"],
    "APPROVALS-QUOTE-APPROVALS": ["rule_groups"],
    "REEVALUATE-APPROVALS": ["entity_keys"],
    "STAGE-APPROVALS": [],
    "ADD-APPROVERS": ["approvers"],
    "ROLE-RBAC": [],
    "EMAIL-STATEMENTS": ["payee_email_id", "recipient_email_id", "period_end_date"],
    "CUSTOM-CALENDAR": ["calendar_id", "name", "variable_pay_type", "calendar_type"],
    "HRIS-CONFIG": ["mapped_field"],
    "HRIS-UPDATES": ["databook_id", "datasheet_id", "row_key", "datasheet_data_id"],
    "HYPERLINK-DATA": ["hyperlink_field", "hyperlink_url"],
    "APPROVAL-CONFIG": [
        "payout_approvals",
        "datasheet_approvals",
        "commission_adjustment_approvals",
    ],
    "DATASHEET-TAG": ["name", "description"],
    "DATASHEET-PIN": ["updated_at", "updated_by"],
    "DATASHEET-VIEW": ["name", "last_updated_by"],
    "DATASHEET-VIEW-FILTER": ["filter_data"],
    "SHARED-PLAN-MEMBERS": [
        "permission_type",
        "target_id",
        "target_type",
    ],
    "DATASHEET-VIEW-PIVOT": ["pivot_data"],
    "DATASHEET-EXPORT": [
        "export_type",
        "datasheet_id",
        "databook_id",
        "datasheet_name",
        "databook_name",
        "apply_adjustments",
    ],
}

EVENT_MAP = {
    "RENAME_TEAM": ["CREATE_TEAM", "RENAME_TEAM", "DELETE_TEAM"],
    "DELETE_TEAM": ["CREATE_TEAM", "RENAME_TEAM", "DELETE_TEAM"],
    "UPDATE_USER": ["UPDATE_USER", "EDIT_USER"],
    "UPDATE_DRAWS": ["UPDATE_DRAWS", "CREATE_DRAWS"],
    "EDIT_USER": ["CREATE_USER", "UPDATE_USER", "EDIT_USER"],
    "EDIT_MANAGER": ["UPDATE_MANAGER", "EDIT_MANAGER"],
    "UPDATE_MANAGER": [],
    "UPDATE_QUERY": ["CREATE_QUERY", "UPDATE_QUERY"],
    "UPDATE_QUOTA": ["CREATE_QUOTA", "UPDATE_QUOTA"],
    "UPDATE_DATA-FEED": [
        "CREATE_DATA-FEED",
        "UPDATE_DATA-FEED",
        "DELETE_DATA-FEED",
    ],
    "DELETE_DATA-FEED": [
        "CREATE_DATA-FEED",
        "UPDATE_DATA-FEED",
        "DELETE_DATA-FEED",
    ],
    "PUBLISH_PLAN": ["PUBLISH_PLAN", "UPDATE_PLAN"],
    "UPDATE_PLAN": ["PUBLISH_PLAN", "UPDATE_PLAN"],
    "RENAME_VARIABLE": ["CREATE_VARIABLE", "RENAME_VARIABLE"],
    "RENAME_COMMISSION-FEED": [
        "CREATE_COMMISSION-FEED",
        "UPDATE_COMMISSION-FEED",
        "RENAME_COMMISSION-FEED",
        "DELETE_COMMISSION-FEED",
    ],
    "UPDATE_COMMISSION-FEED": [
        "CREATE_COMMISSION-FEED",
        "UPDATE_COMMISSION-FEED",
        "RENAME_COMMISSION-FEED",
        "DELETE_COMMISSION-FEED",
    ],
    "DELETE_COMMISSION-FEED": [
        "CREATE_COMMISSION-FEED",
        "UPDATE_COMMISSION-FEED",
        "RENAME_COMMISSION-FEED",
        "DELETE_COMMISSION-FEED",
    ],
    "UPDATE_CALCULATED-FEED": [
        "CREATE_CALCULATED-FEED",
        "UPDATE_CALCULATED-FEED",
        "RENAME_CALCULATED-FEED",
        "DELETE_CALCULATED-FEED",
    ],
    "RENAME_CALCULATED-FEED": [
        "CREATE_CALCULATED-FEED",
        "UPDATE_CALCULATED-FEED",
        "RENAME_CALCULATED-FEED",
        "DELETE_CALCULATED-FEED",
    ],
    "DELETE_CALCULATED-FEED": [
        "CREATE_CALCULATED-FEED",
        "UPDATE_CALCULATED-FEED",
        "RENAME_CALCULATED-FEED",
        "DELETE_CALCULATED-FEED",
    ],
    "LOGIN": ["LOGIN", "LOGOUT"],
    "LOGOUT": ["LOGIN", "LOGOUT"],
    "FREEZE_FREEZE-COMMISSION": [
        "PAID_PAID-COMMISSION",
        "UNPAID_PAID-COMMISSION",
        "FREEZE_FREEZE-COMMISSION",
        "UNFREEZE_FREEZE-COMMISSION",
    ],
    "UNFREEZE_FREEZE-COMMISSION": [
        "PAID_PAID-COMMISSION",
        "UNPAID_PAID-COMMISSION",
        "FREEZE_FREEZE-COMMISSION",
        "UNFREEZE_FREEZE-COMMISSION",
    ],
    "PAID_PAID-COMMISSION": [
        "PAID_PAID-COMMISSION",
        "UNPAID_PAID-COMMISSION",
        "FREEZE_FREEZE-COMMISSION",
        "UNFREEZE_FREEZE-COMMISSION",
    ],
    "UNPAID_PAID-COMMISSION": [
        "PAID_PAID-COMMISSION",
        "UNPAID_PAID-COMMISSION",
        "FREEZE_FREEZE-COMMISSION",
        "UNFREEZE_FREEZE-COMMISSION",
    ],
    "ADD_ADJUSTMENTS-COMMISSION": ["ADD_ADJUSTMENTS-COMMISSION"],
    "ADD_ADJUSTMENTS-SPLIT": ["ADD_ADJUSTMENTS-SPLIT"],
    "ADD_ADJUSTMENTS-DEAL": ["ADD_ADJUSTMENTS-DEAL"],
    "ADD_ADJUSTMENTS-DRAWS": ["ADD_ADJUSTMENTS-DRAWS"],
    "ADD_ADJUSTMENTS-LINE-ITEM": ["ADD_ADJUSTMENTS-LINE-ITEM"],
    "EDIT_USERS-PLAN": ["EDIT_USERS-PLAN", "UPDATE_USERS-PLAN"],
    "UPDATE_USERS-PLAN": [],
    "UPDATE_SETTINGS": ["UPDATE_SETTINGS"],
    "UPDATE_USERS-SPIFF-PLAN": ["UPDATE_USERS-SPIFF-PLAN"],
    "RENAME_CUSTOM-OBJECT-VARIABLE": [
        "CREATE_CUSTOM-OBJECT",
        "ADD_CUSTOM-OBJECT-VARIABLE",
        "RENAME_CUSTOM-OBJECT-VARIABLE",
    ],
    "CLONE_DATABOOK": [
        "CREATE_DATABOOK",
        "CLONE_DATABOOK",
        "RENAME_DATABOOK",
        "PUBLISH_DATABOOK",
    ],
    "RENAME_DATABOOK": [
        "CREATE_DATABOOK",
        "CLONE_DATABOOK",
        "RENAME_DATABOOK",
        "PUBLISH_DATABOOK",
    ],
    "UPDATE_DATABOOK": [
        "CREATE_DATABOOK",
        "CLONE_DATABOOK",
        "UPDATE_DATABOOK",
        "PUBLISH_DATABOOK",
    ],
    "PUBLISH_DATABOOK": [
        "CREATE_DATABOOK",
        "CLONE_DATABOOK",
        "RENAME_DATABOOK",
    ],
    "CLONE_DATASHEET": [
        "CREATE_DATASHEET",
        "CLONE_DATASHEET",
        "EDIT_DATASHEET",
    ],
    "EDIT_DATASHEET": [
        "CREATE_DATASHEET",
        "CLONE_DATASHEET",
        "EDIT_DATASHEET",
    ],
    "EMPLOYEE_EXIT": ["EMPLOYEE_EXIT"],
    "EMPLOYEE_EXIT-COMM": ["EMPLOYEE_EXIT-COMM"],
    "DATASHEET-ADJUSTMENT": [],
    "DATASHEET-FILTER": [],
    "DATASHEET-PIVOT": [],
    "CONTRACT": [],
    "CONTRACT-FIELD": [],
    "DOCUSIGN": [],
    "UPDATE_CUSTOM-FIELD": [
        "CREATE_CUSTOM-FIELD",
        "UPDATE_CUSTOM-FIELD",
        "DELETE_CUSTOM-FIELD",
    ],
    "UPDATE_CUSTOM-FIELD-DATA": ["UPDATE_CUSTOM-FIELD-DATA"],
    "DELETE_USER": ["DELETE_USER"],
    "UPDATE_USER-GROUP": [
        "CREATE_USER-GROUP",
        "UPDATE_USER-GROUP",
    ],
    "UPDATE_APPROVAL-INSTANCE": [
        "CREATE_APPROVAL-INSTANCE",
    ],
    "UPDATE_APPROVAL-TEMPLATE": [
        "CREATE_APPROVAL-TEMPLATE",
        "UPDATE_APPROVAL-TEMPLATE",
        "DELETE_APPROVAL-TEMPLATE",
    ],
    "STATEMENTS": [],
    "STATEMENTS_EMAIL-STATEMENTS": [],
    # "REQUEST-APPROVALS": []
    "UPDATE_CUSTOM-CALENDAR": [
        "CREATE_CUSTOM-CALENDAR",
        "UPDATE_CUSTOM-CALENDAR",
    ],
    "SAVE_HRIS-CONFIG": [
        "SAVE_HRIS-CONFIG",
    ],
    "PROCESS_HRIS-CONFIG": [
        "PROCESS_HRIS-CONFIG",
    ],
    "CREATE_HYPERLINK-DATA": ["CREATE_HYPERLINK-DATA", "UPDATE_HYPERLINK-DATA"],
    "UPDATE_HYPERLINK-DATA": ["CREATE_HYPERLINK-DATA", "UPDATE_HYPERLINK-DATA"],
    "CREATE_DATASHEET-TAG": ["CREATE_DATASHEET-TAG"],
    "UPDATE_DATASHEET-TAG": ["UPDATE_DATASHEET-TAG"],
    "DELETE_DATASHEET-TAG": ["DELETE_DATASHEET-TAG"],
    "CREATE_DATASHEET-PIN": ["CREATE_DATASHEET-PIN"],
    "DELETE_DATASHEET-PIN": ["DELETE_DATASHEET-PIN"],
    "CREATE_DATASHEET-VIEW": ["CREATE_DATASHEET-VIEW"],
    "UPDATE_DATASHEET-VIEW": ["UPDATE_DATASHEET-VIEW"],
    "DELETE_DATASHEET-VIEW": ["DELETE_DATASHEET-VIEW"],
    "CREATE_DATASHEET-VIEW-FILTER": ["CREATE_DATASHEET-VIEW-FILTER"],
    "RBAC_SHARED-PLAN-MEMBERS": ["RBAC_SHARED-PLAN-MEMBERS"],
    "CREATE_DATASHEET-VIEW-PIVOT": ["CREATE_DATASHEET-VIEW-PIVOT"],
}
