import datetime

from django.http import JsonResponse
from django.utils import timezone
from django.utils.timezone import make_aware
from graphql import GraphQLError

from commission_engine.utils.general_data import RBAC, RBACComponent
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    authorize_for_profile_lookup,
)
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import Employee<PERSON>ccessor
from spm.accessors.team_accessors.membership_accessor import MembershipAccessor
from spm.accessors.team_accessors.team_accessor import TeamAccessor
from spm.services.config_services.employee_services import (
    generate_query_for_user_status,
    get_payees_paginated_with_search,
    get_power_admin_users_for_client,
)
from spm.services.config_services.hierarchy_services import (
    get_reportee_by_manager_email_search,
)
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails

logger = LogWithContext()


class CommonEmployeeConfigResolver:
    def resolve_common_employee(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id, login_user_id, [email_id], info.context.component
        )
        if is_authorized:
            return EmployeeAccessor(info.context.client_id).get_employee(email_id)
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    def resolve_common_payee_search(self, info, **kwargs):
        logger.info("Payee search query is getting called")
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        limit = kwargs.get("limit_value")
        full_name = kwargs.get("full_name")
        email = kwargs.get("email")
        search_term = kwargs.get("search_term")
        payroll_and_designation_details = kwargs.get("payroll_and_designation_details")
        data_permission = get_data_permission(
            client_id, login_user_id, info.context.component
        )

        if data_permission and data_permission["type"] == RBAC.ALL_DATA.value:
            return get_payees_paginated_with_search(
                info.context.client_id,
                limit,
                full_name,
                email,
                search_term,
                payroll_and_designation_details,
            )
        if (
            data_permission
            and data_permission["type"] == RBAC.INDIVIDUAL_AND_TEAM_DATA.value
        ):
            return get_reportee_by_manager_email_search(
                info.context.client_id,
                login_user_id,
                limit,
                search_term,
                full_name,
                email,
                payroll_and_designation_details,
            )
        return []

    def resolve_common_all_teams_of_member(self, info, **kwargs):
        logger.info("All teams of member query is getting called")
        team_type = kwargs.get("team_type")
        member_email_id = kwargs.get("member_email_id")
        effective_date = kwargs.get("effective_date")
        if effective_date:
            effective_date = make_aware(
                datetime.datetime.strptime(effective_date, "%Y-%m-%d")
            )
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, member_email_id, info.context.component
        )
        if is_authorized:
            info.context.member_email_id = member_email_id
            if effective_date:
                ids = MembershipAccessor(
                    info.context.client_id
                ).get_all_teams_of_whole_team(effective_date, [member_email_id])
            else:
                ids = MembershipAccessor(
                    info.context.client_id
                ).get_all_teams_of_member(member_email_id)
            team_ids = []
            for team_id in ids:
                team_ids.append(team_id["team_id"])
            return TeamAccessor(info.context.client_id).get_all_teams_of_type_team_id(
                team_ids, team_type, as_dicts=False
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("ALL TEAMS OF MEMBER - PERMISSION REQUIRED")
            return response

    def resolve_common_employee_with_limit(self, info, **kwargs):
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        user_status = kwargs.get("user_status")
        as_of_date = kwargs.get("as_of_date")
        offset_value = kwargs.get("offset_value")
        limit_value = kwargs.get("limit_value")
        search_term = kwargs.get("search_term")
        user_role = kwargs.get("user_role")
        context = kwargs.get("context")
        employee_list = []
        selected_options = kwargs.get("selected_options")
        is_to_fetch_exited_users = kwargs.get("is_to_fetch_exited_users")
        component = info.context.component
        # Check if the component requires data permission check
        if component and component in [
            RBACComponent.PAYOUTS_STATEMENTS.value,
            RBACComponent.MANAGE_USERS.value,
            RBACComponent.QUERIES.value,
            RBACComponent.QUOTAS_DRAWS.value,
        ]:
            data_permission = get_data_permission(client_id, login_user_id, component)

            if not data_permission:
                raise GraphQLError("Permission denied")

            if data_permission["type"] != RBAC.ALL_DATA.value:
                employee_list = get_valid_payee_emails(
                    client_id, login_user_id, data_permission
                )
        if not is_to_fetch_exited_users:
            is_to_fetch_exited_users = False
        if is_to_fetch_exited_users:
            if not selected_options:
                return []
            power_admins_list = get_power_admin_users_for_client(client_id)
            users_result_list = EmployeeAccessor(
                info.context.client_id
            ).get_all_employees_with_limit(
                as_of_date=as_of_date,
                user_status=user_status,
                search_term=search_term,
                offset=offset_value,
                limit=limit_value,
                email_list=employee_list,
                user_role=user_role,
                exclude_payees=power_admins_list,
                context=context,
                only_email=True,
                is_to_fetch_exited_users=is_to_fetch_exited_users,
            )
            as_of_date = timezone.now()
            user_status_result = generate_query_for_user_status(
                selected_options, as_of_date, client_id, users_result_list
            )
            if user_status_result:
                sorted_employees = sorted(
                    user_status_result,
                    key=lambda emp: f"{emp.first_name.lower()} {emp.last_name.lower()}",
                )
                return sorted_employees
            else:
                return []
        else:
            users_result_list = EmployeeAccessor(
                info.context.client_id
            ).get_all_employees_with_limit(
                as_of_date=as_of_date,
                user_status=user_status,
                search_term=search_term,
                offset=offset_value,
                limit=limit_value,
                email_list=employee_list,
                user_role=user_role,
                context=context,
                is_to_fetch_exited_users=is_to_fetch_exited_users,
            )
            return users_result_list
