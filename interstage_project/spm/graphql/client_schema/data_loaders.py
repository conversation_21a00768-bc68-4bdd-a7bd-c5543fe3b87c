from promise import Promise

from spm.accessors.quota_acessors import <PERSON>uota<PERSON>ccessor
from spm.graphql.commons import DataLoaderWithContext


class QuotaCategoryDisplayNameMapLoader(DataLoaderWithContext):
    def batch_load_fn(self, keys):
        quota_accessor = QuotaAccessor(self.ctx.client_id)
        quota_display_name_map = quota_accessor.get_quota_category_display_name_map(
            keys
        )
        display_name_map = {
            record["quota_category_name"]: record["display_name"]
            for record in quota_display_name_map
        }
        return Promise.resolve([display_name_map.get(name, name) for name in keys])
