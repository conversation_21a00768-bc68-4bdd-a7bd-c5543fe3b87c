# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
import json
import traceback

import graphene
from dateutil.parser import parse
from django.http import JsonResponse
from django.utils.timezone import make_aware
from graphene_django.types import DjangoObjectType, ObjectType
from graphql import GraphQLError
from pydash import camel_case

import commission_engine.utils.date_utils as date_utils
from commission_engine.accessors.commission_accessor import CommissionAccessor
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.accessors.payout_status_accessor import PayoutStatusAccessor

# from stringcase import camelcase
from commission_engine.models.commission_models import Commission
from commission_engine.services.payout_status_service import (
    correct_periods_of_payees_for_a_manager,
)
from commission_engine.utils.general_data import RBAC, RBACComponent, RbacPermissions
from interstage_project.auth_utils import (
    authorize_for_profile_lookup,
    authorize_for_profile_lookup_v2,
    permission_required,
)
from interstage_project.utils import Log<PERSON>ith<PERSON>ontext, log_me
from spm.accessors.commission_plan_accessor import PlanCriteriaAccessor
from spm.services.commission_data_services import (
    get_commission_data,
    get_payout_details_for_criteria,
    get_payout_records_count_for_criteria,
)
from spm.services.commission_plan_services import (
    get_datasheet_id_from_plan_and_criteria_id,
)
from spm.services.commission_statement_service import CommissionsOverview
from spm.services.config_services.hierarchy_reportee_services import (
    get_reportee_in_hierarchy,
)
from spm.services.datasheet_permission_services import get_all_hidden_columns_for_user
from spm.services.rbac_services import (
    does_user_have_databook_manage_permission,
    get_data_permission,
    is_payout_value_permission,
    is_view_payroll_permission,
)
from spm.services.settlement_actions_service.settlement_total_service import (
    SettlementTotalService,
)
from spm.services.user_group_service import UserGroupMemberService


class CommissionType(DjangoObjectType):
    class Meta:
        model = Commission


class CommissionPeriodType(ObjectType):
    period_start_date = graphene.Field(graphene.DateTime)
    period_end_date = graphene.Field(graphene.DateTime)
    commission_locked = graphene.Boolean()
    period_label = graphene.String()

    def resolve_period_start_date(self, info):
        return self.get("period_start_date") if isinstance(self, dict) else None

    def resolve_period_end_date(self, info):
        return self.get("period_end_date") if isinstance(self, dict) else None

    def resolve_period_label(self, info):
        psd = self.get("period_start_date")
        ped = self.get("period_end_date")
        payout_frequency = str(self.get("payout_frequency"))
        return (
            info.context.custom_period_label_loader.load((payout_frequency, psd, ped))
            if isinstance(self, dict)
            else None
        )


class AllCommissionPeriodsType(ObjectType):
    period_start_date = graphene.Field(graphene.DateTime)
    period_end_date = graphene.Field(graphene.DateTime)
    period_label = graphene.String()

    def resolve_period_start_date(self, info):
        return self.get("period_start_date") if isinstance(self, dict) else None

    def resolve_period_end_date(self, info):
        return self.get("period_end_date") if isinstance(self, dict) else None

    def resolve_period_label(self, info):
        psd = self.get("period_start_date")
        ped = self.get("period_end_date")
        payout_frequency = str(self.get("payout_frequency"))
        return (
            info.context.custom_period_label_loader.load((payout_frequency, psd, ped))
            if isinstance(self, dict)
            else None
        )


class PayoutStatusPeriodType(ObjectType):
    period_start_date = graphene.Field(graphene.DateTime)
    period_end_date = graphene.Field(graphene.DateTime)
    period_label = graphene.String()

    def resolve_period_start_date(self, info):
        return self.get("period_start_date") if isinstance(self, dict) else None

    def resolve_period_end_date(self, info):
        return self.get("period_end_date") if isinstance(self, dict) else None

    def resolve_period_label(self, info):
        psd = self.get("period_start_date")
        ped = self.get("period_end_date")
        payout_frequency = str(self.get("payout_frequency"))
        return (
            info.context.custom_period_label_loader.load((payout_frequency, psd, ped))
            if isinstance(self, dict)
            else None
        )


class DataPermissions(graphene.ObjectType):
    component = graphene.String()
    is_authorized = graphene.Boolean()


class CommissionDataConfigQuery(object):
    commission_data = graphene.Field(
        graphene.String,
        payee_email=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        component=graphene.String(),
    )
    payee_commissions = graphene.List(
        PayoutStatusPeriodType,
        payee_email=graphene.String(required=True),
        component=graphene.String(),
    )
    payees_and_period_for_logged_in_payee = graphene.Field(graphene.String)
    open_deals_for_payee = graphene.Field(
        graphene.String,
        payee_email=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        primary_kd=graphene.String(),
    )
    plans_and_commission_periods = graphene.Field(graphene.String)
    commission_overview = graphene.Field(
        graphene.String,
        payee_email=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        currency=graphene.String(),
    )
    payout_details = graphene.Field(
        graphene.String,
        payee_email=graphene.String(),
        comm_period=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        criteria_id=graphene.String(),
        plan_id=graphene.String(),
        commission_type=graphene.String(),
        currency=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        sort_fields=graphene.List(graphene.String),
        sort_orders=graphene.List(graphene.String),
    )
    payout_records_count = graphene.Field(
        graphene.Int,
        payee_email=graphene.String(),
        comm_period=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        criteria_id=graphene.String(),
        plan_id=graphene.String(),
        commission_type=graphene.String(),
        currency=graphene.String(),
    )

    quota_attainment_details = graphene.Field(
        graphene.String,
        payee_email=graphene.String(),
        psd=graphene.String(),
        ped=graphene.String(),
        currency=graphene.String(),
        check_quota_exists=graphene.Boolean(),
    )
    payee_commission_periods = graphene.List(
        CommissionPeriodType,
        payee_email=graphene.String(),
    )
    all_payees_commission_periods = graphene.List(
        AllCommissionPeriodsType,
    )
    has_data_permission_for_payee = graphene.Field(
        graphene.List(DataPermissions),
        payee_email=graphene.String(),
        components=graphene.List(graphene.String),
    )
    payee_commissions_v2 = graphene.List(
        PayoutStatusPeriodType,
        payee_email=graphene.String(required=True),
        component=graphene.String(),
        periods=graphene.List(graphene.String),
    )

    def resolve_has_data_permission_for_payee(self, info, **kwargs):
        # return : [{ component : String, is_authorized: String }]
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        payee_email = kwargs.get("payee_email")
        components = kwargs.get("components", [])
        res = {}
        ret_list = []
        for component in components:
            is_authorized = authorize_for_profile_lookup_v2(
                client_id, login_user_id, payee_email, component
            )
            res["component"] = component
            res["is_authorized"] = is_authorized
            ret_list.append(res)
        return ret_list

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_commission_data(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        psd = kwargs.get("psd")
        ped = kwargs.get("ped")
        component = kwargs.get("component")
        client_id = info.context.client_id
        logger = LogWithContext(
            {
                "client_id": client_id,
                "payee_email": payee_email,
                "psd": psd,
                "ped": ped,
            }
        )
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, payee_email, component
        )
        # is_authorized = True
        if is_authorized:
            try:
                logger.info("BEGIN: GET COMMISSION DATA CONFIG QUERY")
                data = get_commission_data(
                    client_id,
                    psd,
                    ped,
                    payee_email,
                    login_user_id=login_user_id,
                    apply_datasheet_permissions=True,
                )
                data = change_keys(data, camel_case)
                res = json.dumps(data, default=str)
                logger.info("END: GET COMMISSION DATA CONFIG QUERY")
                # log_me(f"JSON string of comm data {res}")
                # res = json.loads(res)
                # return Response(res, status=status.HTTP_200_OK)
                return res
            except Exception as e:
                log_me(f"Exception: {e}")
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_payee_commissions(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        component = kwargs.get("component")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id, login_user_id, payee_email, component
        )
        periods = []
        if is_authorized:
            data_permission = get_data_permission(client_id, login_user_id, component)
            if not data_permission or data_permission["type"] != RBAC.ALL_DATA.value:
                reportees = get_reportee_in_hierarchy(
                    client_id, login_user_id, payee_email
                )
                periods = reportees[0].get("periods", []) if len(reportees) > 0 else []
            return correct_periods_of_payees_for_a_manager(
                client_id, payee_email, periods
            )
        raise GraphQLError("Permission denied")
        # response = JsonResponse(
        #     {"message": "You don't have permission to access this data"}
        # )
        # response.status_code = 403
        # return response

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_payee_commissions_v2(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        component = kwargs.get("component")
        # Currently the endpoint is used in statement page, adding payout_statements componnent
        if component not in ["payouts_statements"]:
            raise GraphQLError("Permission denied")
        raw_periods = kwargs.get("periods")
        periods = []
        for period in raw_periods:
            period = json.loads(period)
            periods.append(
                {
                    "effective_start_date": (
                        period["effectiveStartDate"]
                        if period["effectiveStartDate"] is not None
                        else None
                    ),
                    "effective_end_date": (
                        period["effectiveEndDate"]
                        if period["effectiveEndDate"] is not None
                        else None
                    ),
                }
            )
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id, login_user_id, payee_email, component
        )
        if is_authorized:
            data_permission = get_data_permission(client_id, login_user_id, component)

            if login_user_id != payee_email and (
                not data_permission or data_permission["type"] != RBAC.ALL_DATA.value
            ):
                is_exists = False
                if data_permission.get("is_user_groups_selected", False):
                    selected_usergroups = data_permission.get(
                        "selected_user_groups", []
                    )
                    for user_group in selected_usergroups:
                        is_exists |= UserGroupMemberService(
                            client_id
                        ).check_if_user_exists_in_user_group(user_group, payee_email)
                        if is_exists:
                            periods = []
                            break
                if not is_exists and data_permission.get(
                    "is_reporting_team_selected", False
                ):
                    reportees = get_reportee_in_hierarchy(
                        client_id, login_user_id, payee_email
                    )
                    periods = (
                        reportees[0].get("periods", []) if len(reportees) > 0 else []
                    )
                if not is_exists and not periods:
                    return []

            return correct_periods_of_payees_for_a_manager(
                client_id, payee_email, periods
            )
        raise GraphQLError("Permission denied")
        # else:
        #     response = JsonResponse(
        #         {"message": "You don't have permission to access this data"}
        #     )
        #     response.status_code = 403
        # return response

    @permission_required(
        [
            RbacPermissions.MANAGE_CONTRACTS.value,
        ]
    )
    def resolve_plans_and_commission_periods(self, info, **kwargs):
        client_id = info.context.client_id
        data = {}
        all_data = CommissionAccessor(
            client_id
        ).get_all_commission_data_for_plan_period()
        for plan in all_data:
            plan_id = str(plan["commission_plan_id"])
            if plan_id in data:
                data[plan_id].append(plan)
            else:
                data[plan_id] = [plan]
        res = json.dumps(data, default=str)
        return res

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_commission_overview(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        psd = kwargs.get("psd")
        ped = kwargs.get("ped")
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
        currency = kwargs.get("currency")
        client_id = info.context.client_id
        logger = LogWithContext(
            {
                "client_id": client_id,
                "payee_email": payee_email,
                "psd": psd,
                "ped": ped,
            }
        )

        logged_in_user = info.context.user.username
        is_payout_value = is_payout_value_permission(client_id, logged_in_user)
        is_payroll = is_view_payroll_permission(client_id, logged_in_user)
        is_authorized = authorize_for_profile_lookup_v2(
            client_id,
            logged_in_user,
            payee_email,
            RBACComponent.PAYOUTS_STATEMENTS.value,
        )
        if is_authorized:
            try:
                logger.info("BEGIN: GET COMMISSION DATA OVERVIEW")
                data = SettlementTotalService(
                    client_id, ped, [payee_email]
                ).get_statements_data(currency)
                if not is_payout_value and logged_in_user != payee_email:
                    data.update(
                        {
                            "earned_commission": 0,
                            "earned_commission_details": {},
                            "deferred_commission": 0,
                            "deferred_commission_details": {},
                            "previous_commission_deferred": 0,
                            "previous_commission_deferred_details": {},
                            "adjustments": 0,
                            "adjustments_details": [],
                            "draws": 0,
                            "draws_details": [],
                            "payout_arrears": 0,
                            "payout_arrears_details": {},
                            "total_payout": 0,
                            "total_paid_amount": 0,
                            "paid_amount": 0,
                            "pending_amount": 0,
                            "processed_amount": 0,
                            "ignored_amount": 0,
                            "payout_split_up": {},
                            "current_payout": 0,
                            "current_payout_details": {},
                            "commission_percentage": 0,
                        }
                    )

                if not is_payroll and logged_in_user != payee_email:
                    data.update(
                        {
                            "variable_pay": "-",
                            "variable_pay_as_per_period": "-",
                        }
                    )
                data = change_keys(data, camel_case)
                res = json.dumps(data, default=str)
                logger.info("END: GET COMMISSION DATA OVERVIEW")
                return res
            except Exception as e:
                logger.error(f"Exception: {e}", extra_context=traceback.print_exc())
        else:
            raise GraphQLError("Permission denied")
            # response = JsonResponse(
            #     {"message": "You don't have permission to access this data"}
            # )
            # response.status_code = 403
            # return response

    @permission_required(
        [RbacPermissions.VIEW_PAYOUTS.value, RbacPermissions.VIEW_STATEMENTS.value]
    )
    def resolve_payout_details(self, info, **kwargs):
        client_id = info.context.client_id
        payee_email = kwargs.get("payee_email")
        comm_ped = kwargs.get("comm_period", None)
        psd = kwargs.get("psd")
        ped = kwargs.get("ped")
        criteria_id = kwargs.get("criteria_id")
        plan_id = kwargs.get("plan_id")
        commission_type = kwargs.get("commission_type")
        currency = kwargs.get("currency")
        limit_value = kwargs.get("limit_value")
        offset_value = kwargs.get("offset_value")

        sort_fields = kwargs.get("sort_fields", [])
        sort_orders = kwargs.get("sort_orders", [])
        orderby_fields = [
            {
                "column": field,
                "order": order,
            }
            for field, order in zip(sort_fields, sort_orders)
        ]
        if not orderby_fields:
            criteria_details = PlanCriteriaAccessor(client_id).get_criteria(
                criteria_id, ["criteria_config", "criteria_data"]
            )
            criteria_data = criteria_details.get("criteria_data", {})
            criteria_config = criteria_details.get("criteria_config", {})
            sort_cols = criteria_config.get("sort_cols", [])
            date_field = criteria_data.get("date_field")

            orderby_fields = (
                [{"column": col[0], "order": col[1]} for col in sort_cols]
                if sort_cols
                else [{"column": date_field, "order": "asc"}]
            )

            if criteria_data["type"] == "Tier" or criteria_data["type"] == "Quota":
                orderby_fields.extend(
                    [
                        {"column": "row_key", "order": "asc"},
                        {"column": "tierName", "order": "asc"},
                    ]
                )

        if limit_value is None or offset_value is None:
            limit_value = offset_value = None
        logger = LogWithContext(
            {
                "client_id": client_id,
                "payee_email": payee_email,
                "psd": psd,
                "ped": ped,
                "criteria_id": criteria_id,
                "plan_id": plan_id,
                "offset": offset_value,
                "limit": limit_value,
                "orderby_fields": orderby_fields,
            }
        )
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id,
            login_user_id,
            payee_email,
            RBACComponent.PAYOUTS_STATEMENTS.value,
        )
        if is_authorized:
            try:
                logger.info("BEGIN: GET PAYOUT DETAILS")
                data = get_payout_details_for_criteria(
                    client_id=client_id,
                    psd=psd,
                    ped=ped,
                    payee_email=payee_email,
                    plan_id=plan_id,
                    criteria_id=criteria_id,
                    requested_data=commission_type,
                    currency=currency,
                    offset=offset_value,
                    limit=limit_value,
                    login_user_id=login_user_id,
                    comm_ped=comm_ped,
                    orderby_fields=orderby_fields,  # type: ignore
                )

                manage_permissions_enabled = does_user_have_databook_manage_permission(
                    client_id, login_user_id
                )

                datasheet_id = get_datasheet_id_from_plan_and_criteria_id(
                    client_id, plan_id, criteria_id
                )

                if manage_permissions_enabled is False:
                    hidden_columns_for_given_source_id = (
                        get_all_hidden_columns_for_user(
                            client_id, [datasheet_id], login_user_id
                        )[datasheet_id]
                    )
                    visible_column_list = []
                    for column in data["columns"]:
                        if column not in hidden_columns_for_given_source_id:
                            visible_column_list.append(column)

                    data["columns"] = visible_column_list

                # data = change_keys(data, camel_case)
                res = json.dumps(data, default=str)
                logger.info("END: GET PAYOUT DETAILS")
                return res
            except Exception as e:
                logger.error(f"Exception: {e}", extra_context=traceback.print_exc())
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [RbacPermissions.VIEW_PAYOUTS.value, RbacPermissions.VIEW_STATEMENTS.value]
    )
    def resolve_payout_records_count(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        comm_ped = kwargs.get("comm_period", None)
        psd = kwargs.get("psd")
        ped = kwargs.get("ped")
        criteria_id = kwargs.get("criteria_id")
        plan_id = kwargs.get("plan_id")
        commission_type = kwargs.get("commission_type")
        client_id = info.context.client_id
        logger = LogWithContext(
            {
                "client_id": client_id,
                "payee_email": payee_email,
                "psd": psd,
                "ped": ped,
                "criteria_id": criteria_id,
                "plan_id": plan_id,
            }
        )
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id,
            login_user_id,
            payee_email,
            RBACComponent.PAYOUTS_STATEMENTS.value,
        )
        if is_authorized:
            try:
                logger.info("BEGIN: GET PAYOUT RECORDS COUNT")
                count = get_payout_records_count_for_criteria(
                    client_id=client_id,
                    psd=psd,
                    ped=ped,
                    payee_email=payee_email,
                    plan_id=plan_id,
                    criteria_id=criteria_id,
                    requested_data=commission_type,
                    login_user_id=login_user_id,
                    comm_ped=comm_ped,
                )
                logger.info(f"END: GET PAYOUT RECORDS COUNT : {count} records")
                return count
            except Exception as e:
                logger.error(f"Exception: {e}", extra_context=traceback.print_exc())
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_quota_attainment_details(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        psd = kwargs.get("psd")
        ped = kwargs.get("ped")
        check_quota_exists = kwargs.get("check_quota_exists")
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))

        client_id = info.context.client_id
        logger = LogWithContext(
            {
                "client_id": client_id,
                "payee_email": payee_email,
                "psd": psd,
                "ped": ped,
            }
        )
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id, login_user_id, payee_email, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            try:
                logger.info("BEGIN: GET QUOTA ATTAINMENT DETAILS")
                data = CommissionsOverview(
                    client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
                ).get_quota_attainment_details()
                data = change_keys(data, camel_case)
                res = json.dumps(data, default=str)
                logger.info("END: GET QUOTA ATTAINMENT DETAILS QUERY")
                return res
            except Exception as e:
                logger.error(f"Exception: {e}", extra_context=traceback.print_exc())
        else:
            if check_quota_exists:
                data = CommissionsOverview(
                    client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
                ).get_quota_attainment_details()
                quota_len = len(data.get("quota_data"))
                return str(quota_len > 0)

            raise GraphQLError("Permission denied")
            # response = JsonResponse(
            #     {"message": "You don't have permission to access this data"}
            # )
            # response.status_code = 403
            # return response

    @permission_required(RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value)
    def resolve_payee_commission_periods(self, info, **kwargs):
        payee_email = kwargs.get("payee_email")
        client_id = info.context.client_id
        locked_periods = set(
            CommissionLockAccessor(client_id).get_locked_period_end_dates_of_payee(
                payee_email
            )
        )
        commission_periods = PayoutStatusAccessor(client_id).get_periods_for_a_payee(
            payee_email
        )

        for period in commission_periods:
            if period["period_end_date"] in locked_periods:
                period["commission_locked"] = True
            else:
                period["commission_locked"] = False

        return commission_periods

    @permission_required(RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value)
    def resolve_all_payees_commission_periods(self, info, **kwargs):
        client_id = info.context.client_id

        # Fetch commission periods data
        commission_periods = PayoutStatusAccessor(client_id).get_periods_for_a_payees()
        seen = set()
        commission_periods_map = []
        for item in commission_periods:
            period_start_date = item["period_start_date"]
            period_end_date = item["period_end_date"]
            payout_frequency = item["payout_frequency"]
            # Check for duplicates based on period_start_date, period_end_date, and payout_frequency
            if (period_start_date, period_end_date, payout_frequency) not in seen:
                seen.add((period_start_date, period_end_date, payout_frequency))
                # Set up period details with locked status
                period_details = {
                    "period_start_date": period_start_date,
                    "period_end_date": period_end_date,
                    "payout_frequency": payout_frequency,
                }

                commission_periods_map.append(period_details)
        return commission_periods_map


def change_keys(obj, convert):
    """
    Recursively goes through the dictionary obj and replaces keys with the convert function.
    """
    if isinstance(obj, (str, int, float)):
        return obj
    if isinstance(obj, dict):
        new = obj.__class__()
        for k, v in obj.items():
            new[convert(k)] = change_keys(v, convert)
    elif isinstance(obj, (list, set, tuple)):
        new = obj.__class__(change_keys(v, convert) for v in obj)
    else:
        return obj
    return new
