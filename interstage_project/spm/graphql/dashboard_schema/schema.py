# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test


import graphene
from django.http import JsonResponse
from graphene_django.types import ObjectType

from commission_engine.accessors.client_accessor import get_client
from commission_engine.services.commission_summary_service import (
    get_commission_payout_for_curr_and_prev_months,
    get_commission_summary_payout_for_year,
    get_payee_commission_summary_payout_for_year,
)
from commission_engine.utils.general_data import RBACComponent, RbacPermissions
from crystal.utils.crystal_utils import PRIMARY_QUOTA
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    authorize_for_profile_lookup,
    permission_required,
)
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.drs_accessor import Drs<PERSON>ccessor
from spm.accessors.quota_acessors import Employee<PERSON><PERSON><PERSON><PERSON>ccessor, QuotaAccessor
from spm.services.dashboard_services.commission_services import (
    get_commission_payout_for_year,
)
from spm.services.dashboard_services.dashboard_services import (
    get_active_payees_count,
    get_direct_and_indirect_employees_list_for_quota,
    get_employees_list_for_quota,
    get_opportunities_won_count_custom,
    get_payout_locked_count,
    get_payroll_sent_count,
    get_unmapped_count,
)
from spm.services.dashboard_services.payee_commission_services import (
    get_commission_buddy_data,
    get_commission_payout_for_year_for_payee_periods,
    get_commission_structure,
    get_drs_payout_status,
    get_payee_commission_payout_for_curr_period,
    get_payee_currency_symbol,
)
from spm.services.dashboard_services.payee_quota_services import (
    get_own_leader_team_and_members,
    get_own_leader_team_and_members_for_category,
    get_payee_quota_attainment_for_current_period,
    get_payee_quota_burn_down,
    get_payee_quota_structure_current_period,
    get_quota_attainment_for_payee_year,
    get_user_role,
)
from spm.services.dashboard_services.quota_dashboard_services import (
    get_leader_team_and_members,
    get_quota_attainment_for_category_current_period,
    get_quota_attainment_for_category_team_year,
    get_quota_attainment_for_current_period,
    get_quota_attainment_for_team_and_payees,
    get_quota_attainment_for_team_year,
    get_quota_distribution_for_payees,
    get_quota_run_rate_for_team,
)
from spm.services.localization_services import get_localized_message_service
from spm.services.rbac_services import get_ui_permissions
from superset.services import dashboard_services

logger = LogWithContext()


class GraphQLPayeesForQuotaObjectType(graphene.ObjectType):
    headers = graphene.List(graphene.String)
    data = graphene.List(graphene.List(graphene.String))


class GraphQLQuotaCategoryMapType(graphene.ObjectType):
    label = graphene.String()
    value = graphene.String()

    def resolve_label(self, info):
        return self["label"]

    def resolve_value(self, info):
        return self["value"]


class GraphQLQuotaAttainmentsObjectType(graphene.ObjectType):
    quotas = graphene.JSONString()
    payees_info = graphene.JSONString()
    display_names = graphene.List(GraphQLQuotaCategoryMapType)

    def resolve_display_names(self, info):
        if not self.get("quotas") or len(self.get("quotas")) == 0:
            return []
        qs = QuotaAccessor(info.context.client_id).get_quota_category_display_name_map(
            list(self.get("quotas").keys())
        )
        quotas_list = []
        for record in qs:
            if record["display_name"] and record["quota_category_name"] != "Primary":
                quotas_list.append(
                    {
                        "label": record["display_name"],
                        "value": record["quota_category_name"],
                    }
                )
            elif record["quota_category_name"] == "Primary":
                quotas_list.append(
                    {
                        "label": get_localized_message_service(
                            PRIMARY_QUOTA, info.context.client_id
                        ),
                        "value": "Primary",
                    }
                )
        return quotas_list


class GraphQLQuotaAttainmentsCategoryObjectType(graphene.ObjectType):  #
    quotas = graphene.JSONString()
    payees_info = graphene.JSONString()
    quota_category_names = graphene.List(graphene.String)
    display_names = graphene.List(GraphQLQuotaCategoryMapType)

    def resolve_display_names(self, info):
        if not self.get("quota_category_names"):
            return []
        qs = QuotaAccessor(info.context.client_id).get_quota_category_display_name_map(
            self.get("quota_category_names")
        )
        quotas_list = []
        for record in qs:
            if record["display_name"] and record["quota_category_name"] != "Primary":
                quotas_list.append(
                    {
                        "label": record["display_name"],
                        "value": record["quota_category_name"],
                    }
                )

        if "Primary" in self.get("quota_category_names"):
            quotas_list.append(
                {
                    "label": get_localized_message_service(
                        PRIMARY_QUOTA, info.context.client_id
                    ),
                    "value": "Primary",
                }
            )
        return quotas_list


class GraphQLCurrentQuotaAttainmentsCategoryObjectType(graphene.ObjectType):  #
    quotas = graphene.JSONString()
    quota_category_names = graphene.List(graphene.String)
    display_names = graphene.List(GraphQLQuotaCategoryMapType)

    def resolve_display_names(self, info):
        if not self.get("quota_category_names"):
            return []
        qs = QuotaAccessor(info.context.client_id).get_quota_category_display_name_map(
            self.get("quota_category_names")
        )
        quotas_list = []
        for record in qs:
            if record["display_name"] and record["quota_category_name"] != "Primary":
                quotas_list.append(
                    {
                        "label": record["display_name"],
                        "value": record["quota_category_name"],
                    }
                )

        if "Primary" in self.get("quota_category_names"):
            quotas_list.append(
                {
                    "label": get_localized_message_service(
                        PRIMARY_QUOTA, info.context.client_id
                    ),
                    "value": "Primary",
                }
            )
        return quotas_list


class AdminDashBoardQuery(object):
    drs_counts = graphene.Field(graphene.JSONString, email=graphene.String())
    payee_status_count = graphene.Field(graphene.JSONString)
    commission_payouts_count = graphene.Field(
        graphene.JSONString,
        plan_ids=graphene.List(graphene.String),
        year=graphene.String(),
    )
    current_commission_payouts = graphene.Field(
        graphene.JSONString, plan_ids=graphene.List(graphene.String)
    )
    opportunities_count = graphene.Field(graphene.JSONString)
    quota_attainments_for_team = graphene.Field(
        GraphQLQuotaAttainmentsObjectType,
        team_owner_id=graphene.String(),
        year=graphene.String(),
    )
    current_quota_attainment = graphene.Field(
        graphene.JSONString, team_owner_id=graphene.String()
    )
    quota_attainment_for_payee_and_teams = graphene.Field(
        GraphQLQuotaAttainmentsObjectType,
        team_owner_ids=graphene.List(graphene.String),
        payee_ids=graphene.List(graphene.String),
        year=graphene.String(),
        quota_category=graphene.String(),
    )
    quota_attainment_distribution = graphene.Field(
        graphene.JSONString,
        payee_ids=graphene.List(graphene.String),
        year=graphene.String(),
        quota_category=graphene.String(),
    )
    quota_run_rate = graphene.Field(
        graphene.JSONString,
        team_owner_id=graphene.String(),
        year=graphene.String(),
    )
    dynamic_team_direct_members = graphene.Field(
        graphene.JSONString,
        quota_year=graphene.String(),
        quota_category=graphene.String(),
        quota_schedule=graphene.String(),
        search_text=graphene.String(),
        manager_emails=graphene.List(graphene.String),
        limit_value=graphene.Int(),
        offset_value=graphene.Int(),
    )
    all_quota_category_types = graphene.List(
        GraphQLQuotaCategoryMapType, year=graphene.Int()
    )
    commission_summary_payouts_count = graphene.Field(
        graphene.JSONString,
        plan_ids=graphene.List(graphene.String),
        year=graphene.Int(),
    )
    payees_for_quota = graphene.Field(
        GraphQLPayeesForQuotaObjectType,
        quota_year=graphene.String(),
        quota_category=graphene.String(),
        quota_schedule=graphene.String(),
        search_text=graphene.String(),
        limit_value=graphene.Int(),
        offset_value=graphene.Int(),
    )

    direct_and_indirect_payees_for_quota = graphene.Field(
        GraphQLPayeesForQuotaObjectType,
        quota_year=graphene.String(),
        quota_category=graphene.String(),
        quota_schedule=graphene.String(),
        search_text=graphene.String(),
        limit_value=graphene.Int(),
        offset_value=graphene.Int(),
    )

    quota_attainments_category_for_team = graphene.Field(
        GraphQLQuotaAttainmentsCategoryObjectType,
        team_owner_id=graphene.String(),
        year=graphene.Int(),
        category=graphene.String(),
    )

    current_category_quota_attainment = graphene.Field(
        GraphQLCurrentQuotaAttainmentsCategoryObjectType,
        team_owner_id=graphene.String(),
        category=graphene.String(),
    )

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_drs_counts(self, info, **kwargs):
        logger.info("BEGIN: DRS COUNT QUERY")
        email = kwargs.get("email")
        assigned_to_me = DrsAccessor(info.context.client_id).get_assigned_to_user_count(
            email
        )
        assigned_to_others = DrsAccessor(
            info.context.client_id
        ).get_assigned_to_others_count(email)
        unassigned = DrsAccessor(info.context.client_id).get_unassigned_count()
        data = {
            "assigned_to_me": assigned_to_me,
            "assigned_to_others": assigned_to_others,
            "unassigned": unassigned,
        }
        logger.info("END: DRS COUNT QUERY")
        return data

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_payee_status_count(self, info, **kwargs):
        logger.info("BEGIN: PAYEE STATUS COUNT QUERY")
        locked_count = get_payout_locked_count(info.context.client_id)
        active_count = get_active_payees_count(info.context.client_id)
        payroll_sent_count = get_payroll_sent_count(info.context.client_id)
        data = {
            "unmapped_payees_count": get_unmapped_count(info.context.client_id),
            "active_payees_count": {
                "current_month": active_count[0],
                "previous_month": active_count[1],
            },
            "locked_payees_count": {
                "current_month": locked_count[0],
                "previous_month": locked_count[1],
            },
            "payroll_sent_count": {
                "current_month": payroll_sent_count[0],
                "previous_month": payroll_sent_count[1],
            },
        }
        logger.info("END: PAYEE STATUS COUNT QUERY")
        return data

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_commission_payouts_count(self, info, **kwargs):
        logger.info("BEGIN: COMMISSION PAYOUTS COUNT QUERY")
        plan_ids = kwargs.get("plan_ids")
        year = kwargs.get("year")
        return get_commission_payout_for_year(info.context.client_id, plan_ids, year)

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_commission_summary_payouts_count(self, info, **kwargs):
        plan_ids = kwargs.get("plan_ids")
        year = kwargs.get("year")
        if year:
            year = str(year)
            # Dependant method logics are expecting year to be str, adding this as a quick solution
        client_id = info.context.client_id
        request_id = info.context.request_id
        return get_commission_summary_payout_for_year(
            plan_ids, client_id, year, request_id=request_id
        )

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_current_commission_payouts(self, info, **kwargs):
        logger.info("BEGIN: CURRENT COMMISSION PAYOUTS QUERY")
        plan_ids = kwargs.get("plan_ids")

        request_id = info.context.request_id
        logger.info("USING FROZEN PAYROLL DATA FOR PAYOUT")
        return get_commission_payout_for_curr_and_prev_months(
            info.context.client_id, plan_ids, request_id=request_id
        )

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_opportunities_count(self, info, **kwargs):
        logger.info("BEGIN: OPPORTUNITIES COUNT QUERY")
        return get_opportunities_won_count_custom(info.context.client_id)

    @permission_required(
        [
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
        ]
    )
    def resolve_quota_attainments_for_team(self, info, **kwargs):
        logger.info("BEGIN: QUOTA ATTAINMENTS FOR TEAM QUERY")
        team_owner_id = kwargs.get("team_owner_id")
        year = str(
            kwargs.get("year")
        )  # Casting it as the flow involved using year as string before django 4 upgrade
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, team_owner_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_quota_attainment_for_team_year(
                info.context.client_id, year, team_owner_id
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("END: QUOTA ATTAINMENTS FOR TEAM QUERY - PERMISSION REQUIRED")
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
        ]
    )
    def resolve_quota_attainments_category_for_team(self, info, **kwargs):
        """This API is used to generate data for the Quota Attainment Tracker widget based on team_owner_id, category and fiscal year"""
        logger.info("BEGIN: QUOTA ATTAINMENTS FOR TEAM QUERY BY CATEGORY")
        team_owner_id = kwargs.get("team_owner_id")
        year = kwargs.get("year")
        category = kwargs.get("category")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, team_owner_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            quota_attainment = get_quota_attainment_for_category_team_year(
                info.context.client_id, year, team_owner_id, category, login_user_id
            )
            logger.info("END: QUOTA ATTAINMENTS FOR TEAM QUERY BY CATEGORY")
            return quota_attainment
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("END: QUOTA ATTAINMENTS FOR TEAM QUERY - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_current_quota_attainment(self, info, **kwargs):
        logger.info("BEGIN: CURRENT QUOTA ATTAINMENT QUERY")
        team_owner_id = kwargs.get("team_owner_id")
        return get_quota_attainment_for_current_period(
            info.context.client_id, team_owner_id
        )

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_current_category_quota_attainment(self, info, **kwargs):
        """This API is used to generate data for the Quota Attainment widget based on team_owner_id and category for the current period"""
        logger.info("BEGIN: CURRENT QUOTA ATTAINMENT QUERY BY CATEGORY")
        team_owner_id = kwargs.get("team_owner_id")
        category = kwargs.get("category")
        quota_attainment = get_quota_attainment_for_category_current_period(
            info.context.client_id, team_owner_id, category, info.context.user.username
        )
        logger.info("END: CURRENT QUOTA ATTAINMENT QUERY BY CATEGORY")
        return quota_attainment

    @permission_required(
        [
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
        ]
    )
    def resolve_quota_attainment_for_payee_and_teams(self, info, **kwargs):
        logger.info("BEGIN: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY")
        team_owner_ids = kwargs.get("team_owner_ids")
        payee_ids = kwargs.get("payee_ids")
        year = kwargs.get("year")
        quota_category = kwargs.get("quota_category")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            client_id,
            login_user_id,
            team_owner_ids + payee_ids,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            return get_quota_attainment_for_team_and_payees(
                client_id, team_owner_ids, payee_ids, year, quota_category
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error(
                "END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY - PERMISSION REQUIRED"
            )
            return response

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_quota_attainment_distribution(self, info, **kwargs):
        logger.info("BEGIN: QUOTA ATTAINMENT DISTRIBUTION QUERY")
        payee_ids = kwargs.get("payee_ids")
        year = kwargs.get("year")
        quota_category = kwargs.get("quota_category")
        data = get_quota_distribution_for_payees(
            info.context.client_id, payee_ids, year, quota_category
        )
        logger.info("END: QUOTA ATTAINMENT DISTRIBUTION QUERY")
        return data

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_quota_run_rate(self, info, **kwargs):
        logger.info("BEGIN: QUOTA RUN RATE QUERY")
        team_owner_id = kwargs.get("team_owner_id")
        year = kwargs.get("year")
        return get_quota_run_rate_for_team(info.context.client_id, year, team_owner_id)

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_dynamic_team_direct_members(self, info, **kwargs):
        logger.info("BEGIN: DYNAMIC TEAM DIRECT MEMBERS QUERY")
        return get_leader_team_and_members(info.context.client_id, kwargs)

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_payees_for_quota(self, info, **kwargs):
        logger.info("BEGIN: Individuals for Quota Category")
        result = get_employees_list_for_quota(info.context.client_id, kwargs)
        if (
            isinstance(result, dict)
            and "headers" in result
            and isinstance(result["headers"], dict)
        ):
            headers_dict = result["headers"]
            header_keys = [
                key for key, _ in sorted(headers_dict.items(), key=lambda item: item[1])
            ]  # Solving type check in gql (being extra cautious on order)
            result["headers"] = header_keys
        return result

    @permission_required(RbacPermissions.VIEW_PAYEEDASHBOARD.value)
    def resolve_direct_and_indirect_payees_for_quota(self, info, **kwargs):
        logger.info("BEGIN: Direct and Indirect Individuals for Quota Category")
        result = get_direct_and_indirect_employees_list_for_quota(
            info.context.client_id, info.context.user.username, kwargs
        )
        if (
            isinstance(result, dict)
            and "headers" in result
            and isinstance(result["headers"], dict)
        ):
            headers_dict = result["headers"]
            header_keys = [
                key for key, _ in sorted(headers_dict.items(), key=lambda item: item[1])
            ]  # Solving type check in gql (being extra cautious on order)
            result["headers"] = header_keys
        logger.info("END: Direct and Indirect Individuals for Quota Category")
        return result

    @permission_required(
        [
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
        ]
    )
    def resolve_all_quota_category_types(self, info, **kwargs):
        logger.info("BEGIN: ALL QUOTA CATEGORY TYPES QUERY")
        client_id = info.context.client_id
        logged_in_user = info.context.user.username
        year = kwargs.get("year")
        if year:
            year = str(
                year
            )  # Dependant method logics are expecting year to be str, adding this as a quick solution

        # Users without view:quotas shouldn't be able to view quota categories
        permissions = get_ui_permissions(client_id, logged_in_user)
        if RbacPermissions.VIEW_QUOTAS.value not in permissions:
            logger.info(
                f"User: {logged_in_user} doesn't have view:quotas permission to view quota categories"
            )
            logger.info("END: ALL QUOTA CATEGORY TYPES QUERY")
            return []

        quotas = EmployeeQuotaAccessor(
            client_id
        ).get_all_distinct_quota_categories_for_year(year)
        quota_category_names = [quota["quota_category_name"] for quota in quotas]
        qs = QuotaAccessor(client_id).get_quota_category_display_name_map(
            quota_category_names
        )
        quotas_list = []
        for record in qs:
            if record["display_name"] and record["quota_category_name"] != "Primary":
                quotas_list.append(
                    {
                        "label": record["display_name"],
                        "value": record["quota_category_name"],
                    }
                )

        if "Primary" in quota_category_names:
            quotas_list.append(
                {
                    "label": get_localized_message_service(
                        PRIMARY_QUOTA, info.context.client_id
                    ),
                    "value": "Primary",
                }
            )

        logger.info("END: ALL QUOTA CATEGORY TYPES QUERY")
        return quotas_list


class PayeeDashBoardQuery(object):
    payee_commission_tracker = graphene.Field(
        graphene.JSONString, year=graphene.String(), payee_id=graphene.String()
    )
    payee_commission_current = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )
    commission_buddy = graphene.Field(graphene.JSONString, payee_id=graphene.String())
    commission_structure = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )
    drs_payout_status = graphene.Field(graphene.JSONString, payee_id=graphene.String())
    payee_quota_attainment_tracker = graphene.Field(
        graphene.JSONString,
        payee_id=graphene.String(),
        year=graphene.String(),
    )
    payee_current_quota_attainment = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )
    own_team_direct_members = graphene.Field(
        graphene.JSONString, year=graphene.String(), payee_id=graphene.String()
    )
    payee_quota_burn_down = graphene.Field(
        graphene.JSONString, payee_id=graphene.String(), year=graphene.String()
    )
    user_role = graphene.Field(
        graphene.String, year=graphene.String(), payee_id=graphene.String()
    )
    payee_currency_symbol = graphene.Field(
        graphene.String, year=graphene.String(), payee_id=graphene.String()
    )
    base_currency_symbol = graphene.Field(graphene.String)
    own_team_direct_members_quota_category = graphene.Field(
        graphene.JSONString,
        year=graphene.String(),
        payee_id=graphene.String(),
        quota_category=graphene.String(),
    )
    payee_current_quota_structure = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )
    payee_commission_summary_tracker = graphene.Field(
        graphene.JSONString, year=graphene.String(), payee_id=graphene.String()
    )
    payee_referral_data = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )
    referral_leader_board_data = graphene.Field(graphene.JSONString)
    payee_commission_buddy_data = graphene.Field(
        graphene.JSONString, payee_id=graphene.String()
    )

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_commission_tracker(self, info, **kwargs):
        year = kwargs.get("year")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        logger.info("BEGIN: Payee commission tracker query")
        if payee_id == login_user_id:
            return get_commission_payout_for_year_for_payee_periods(
                info.context.client_id, year, payee_id
            )
        else:
            logger.info("END: Payee commission tracker query - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_commission_summary_tracker(self, info, **kwargs):
        year = kwargs.get("year")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        request_id = info.context.request_id
        logger.info("BEGIN: Payee commission summary tracker query")
        if payee_id == login_user_id:
            return get_payee_commission_summary_payout_for_year(
                info.context.client_id, year, payee_id, request_id
            )
        else:
            logger.info(
                "END: Payee commission summary tracker query - PERMISSION REQUIRED"
            )
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_commission_current(self, info, **kwargs):
        logger.info("BEGIN: Payee commission current")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_payee_commission_payout_for_curr_period(
                info.context.client_id, payee_id
            )
        else:
            logger.error("END: Payee commission current - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_commission_buddy(self, info, **kwargs):
        logger.info("BEGIN: Commission buddy query")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_commission_buddy_data(info.context.client_id, payee_id)
        else:
            logger.error("END: Commission buddy query - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_commission_structure(self, info, **kwargs):
        logger.info("BEGIN: Commission structure query")
        login_user_id = info.context.user.username
        return get_commission_structure(info.context.client_id, login_user_id)

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_drs_payout_status(self, info, **kwargs):
        logger.info("BEGIN: DRS payout status")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_drs_payout_status(info.context.client_id, payee_id)
        else:
            logger.error("DRS payout status - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_quota_attainment_tracker(self, info, **kwargs):
        logger.info("BEGIN: Payee quota attainment tracker query")
        payee_id = kwargs.get("payee_id")
        year = kwargs.get("year")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        request_id = info.context.request_id
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, payee_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_quota_attainment_for_payee_year(
                client_id, year, payee_id, request_id
            )
        else:
            logger.error(
                "END: Payee quota attainment tracker query - PERMISSION REQUIRED"
            )
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_current_quota_attainment(self, info, **kwargs):
        logger.info("BEGIN: Payee current quota attainment query")
        payee_id = kwargs.get("payee_id")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, payee_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_payee_quota_attainment_for_current_period(client_id, payee_id)
        else:
            logger.error(
                "END: Payee quota attainment for current period query - PERMISSION REQUIRED"
            )
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_own_team_direct_members(self, info, **kwargs):
        logger.info("BEGIN: Own team direct and members query")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_own_leader_team_and_members(info.context.client_id, payee_id)
        else:
            logger.error("END: Own team direct and members query - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_user_role(self, info, **kwargs):
        logger.info("BEGIN: User role query")
        year = kwargs.get("year")
        payee_id = kwargs.get("payee_id")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_user_role(info.context.client_id, payee_id, year)
        else:
            logger.error("END: User role query - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_quota_burn_down(self, info, **kwargs):
        logger.info("BEGIN: Payee quota burn down query")
        payee_id = kwargs.get("payee_id")
        year = kwargs.get("year")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, payee_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_payee_quota_burn_down(client_id, payee_id, year)
        else:
            logger.error("END: Payee quota burn down query - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_currency_symbol(self, info, **kwargs):
        logger.info("BEGIN: Payee currency symbol")
        payee_id = kwargs.get("payee_id")
        year = kwargs.get("year")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_payee_currency_symbol(info.context.client_id, payee_id, year)
        else:
            logger.error("END: Payee currency symbol - PERMISSION REQUIRED")
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_base_currency_symbol(self, info, **kwargs):
        logger.info("BEGIN: Base currency symbol query")
        client_id = info.context.client_id
        client = get_client(client_id)
        country = CountriesAccessor(client_id).get_currency_symbol(client.base_currency)
        symbol = country.currency_symbol if country else "$"
        logger.info("END: Base currency symbol query")
        return symbol

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_own_team_direct_members_quota_category(self, info, **kwargs):
        logger.info("BEGIN: Own team direct members quota category")
        year = kwargs.get("year")
        payee_id = kwargs.get("payee_id")
        quota_category = kwargs.get("quota_category")
        login_user_id = info.context.user.username
        if payee_id == login_user_id:
            return get_own_leader_team_and_members_for_category(
                info.context.client_id, payee_id, year, quota_category
            )
        else:
            logger.error(
                "END: Own team direct members quota category - PERMISSION REQUIRED"
            )
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
        ]
    )
    def resolve_payee_current_quota_structure(self, info, **kwargs):
        logger.info("BEGIN: Payee current quota structure query")
        payee_id = kwargs.get("payee_id")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id, login_user_id, payee_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_payee_quota_structure_current_period(client_id, payee_id)
        else:
            logger.error(
                "END: Payee current quota structure query - PERMISSION REQUIRED"
            )
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response


# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
class UserType(ObjectType):
    first_name = graphene.Field(graphene.String)
    last_name = graphene.Field(graphene.String)
    employee_email_id = graphene.Field(graphene.String)


class DashboardQuery(object):
    valid_users = graphene.List(UserType, dashboard_type=graphene.String())

    @permission_required(RbacPermissions.VIEW_DASHBOARD.value)
    def resolve_valid_users(self, info, **kwargs):
        client_id = info.context.client_id
        dashboard_type = kwargs.get("dashboard_type")
        filtered_employee_email_ids = (
            dashboard_services.get_shareable_users_for_dashboard(
                client_id, dashboard_type
            )
        )
        filtered_employees = EmployeeAccessor(client_id).get_employees_name(
            filtered_employee_email_ids
        )
        return filtered_employees
