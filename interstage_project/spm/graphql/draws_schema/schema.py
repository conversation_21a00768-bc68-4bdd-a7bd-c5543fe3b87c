# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import graphene
from django.http import J<PERSON>R<PERSON>ponse
from graphene import ObjectType
from graphene_django.types import DjangoObjectType

from commission_engine.utils.general_data import RBACComponent, RbacPermissions
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    permission_required,
)
from interstage_project.utils import LogWithContext
from spm.graphql.employee_config_schema.schema import EmployeePayrollWithoutPay
from spm.models import Draws

logger = LogWithContext()


class Draw(ObjectType):
    draw_amount = graphene.Field(graphene.String)
    draw_period = graphene.Field(graphene.String)
    draw_type_name = graphene.Field(graphene.String)

    def resolve_draw_amount(self, info):
        return self["draw_amount"] if self else None

    def resolve_draw_period(self, info):
        return self["draw_period"] if self else None

    def resolve_draw_type_name(self, info):
        return self["draw_type_name"] if self else None


class DrawsType(DjangoObjectType):
    draws = graphene.List(graphene.List(Draw))
    employee = graphene.Field(EmployeePayrollWithoutPay)

    def resolve_draws(self, info):
        return self.draws

    def resolve_employee(self, info):
        return (
            info.context.employee_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Draws


class DrawsQuery(object):
    employee_draws = graphene.List(
        DrawsType,
        employee_email_id=graphene.String(),
        year=graphene.Int(),
    )

    @permission_required(RbacPermissions.VIEW_DRAWS.value)
    def resolve_employee_draws(self, info, **kwargs):
        logger.info("Employee draws query is getting called")
        employee_email_id = kwargs.get("employee_email_id")
        year = kwargs.get("year")
        component = RBACComponent.QUOTAS_DRAWS.value
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id, login_user_id, [employee_email_id], component
        )
        if is_authorized:
            from spm.services.draws_services import get_payee_draw, get_payee_draw_year

            if year:
                return get_payee_draw_year(
                    client_id=info.context.client_id,
                    payee_email=employee_email_id,
                    year=year,
                )
            else:
                return get_payee_draw(
                    client_id=info.context.client_id, payee_email=employee_email_id
                )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("EMPLOYEE DRAWS - PERMISSION REQUIRED")
            return response
