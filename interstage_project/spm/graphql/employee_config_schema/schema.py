# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
import datetime
import logging

import dateutil.parser
import graphene
from django.http import JsonResponse
from django.utils import timezone
from django.utils.timezone import make_aware
from graphene_django.types import DjangoObjectType
from graphql import GraphQLError
from pydash import camel_case

# from commission_engine.models import SecondaryData
from commission_engine.accessors.client_accessor import get_client_features
from commission_engine.accessors.hris_integration_accessor import HrisConfigAccessor
from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils import get_variable_pay_per_period, last_day_of_month
from commission_engine.utils.date_utils import end_of_day, start_of_day
from commission_engine.utils.general_data import (
    GRAPHQL__PERMISSION_DENIED,
    RBAC,
    RBACComponent,
    RbacPermissions,
)
from everstage_ddd.custom_metrics import get_bcr_metrics_for_payee_period
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    authorize_for_profile_lookup,
    authorize_for_profile_lookup_v2,
    permission_required,
)
from spm.accessors.approval_workflow_accessors import (
    ApprovalRequestsAccessor,
    SubApprovalRequestsAccessor,
)
from spm.accessors.commission_plan_accessor import PlanPayeeAccessor
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
    PlanDetailsAccessor,
)
from spm.accessors.custom_field_accessor import CustomFieldDataAccessor
from spm.accessors.draws_accessor import DrawsAccessor
from spm.accessors.quota_acessors import EmployeeQuotaAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_WORKFLOW_STATUS,
)
from spm.graphql.base_schemas.common_employee_config_resolver import (
    CommonEmployeeConfigResolver,
)
from spm.models import CustomFieldData, Draws, PlanCriteria, RolePermissions
from spm.models.config_models.employee_models import (
    Employee,
    EmployeePayroll,
    Hierarchy,
    PlanDetails,
)
from spm.services.approval_line_items.line_item_services import (
    get_all_approvers,
    get_payees_from_approvals_for_approver,
)
from spm.services.commission_data_services import get_payee_payrolls_secondary_kd_aware
from spm.services.config_services.client_config_services import get_query_setting_config
from spm.services.config_services.employee_services import (
    employee_details_based_on_permission,
    get_all_filters_count,
    get_matching_lead_employees,
    get_payee_by_payout_frequency,
)
from spm.services.config_services.hierarchy_reportee_services import (
    get_reporting_managers,
)
from spm.services.custom_field_services import fetch_historic_custom_field_data
from spm.services.rbac_services import (
    get_data_permission,
    get_ui_permissions,
    get_user_permissions,
    get_valid_payee_emails,
)
from spm.services.team_services.membership_services import (
    get_own_dynamic_flat_team_email_ids,
)

logger = logging.getLogger(__name__)


class EmployeeObjectType(DjangoObjectType):
    class Meta:
        model = Employee
        fields = ["employee_email_id", "first_name", "last_name"]


class RolePermissionsType(DjangoObjectType):
    class Meta:
        model = RolePermissions
        fields = ["role_permission_id", "display_name"]


class BaseEmployeeType(DjangoObjectType):
    full_name = graphene.Field(graphene.String)

    def resolve_full_name(self, info):
        return f"{self.first_name} {self.last_name}"

    class Meta:
        model = Employee
        fields = [
            "employee_email_id",
            "first_name",
            "last_name",
        ]


class EmployeeRoleDetailsType(BaseEmployeeType):
    can_user_manage_admins = graphene.Field(graphene.Boolean)

    def resolve_can_user_manage_admins(self, info):
        non_empty_user_role = [role for role in self.user_role if role]
        user_role_key = ":".join(non_empty_user_role)
        return (
            info.context.can_user_manage_admins_loader.load(user_role_key)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = list(BaseEmployeeType._meta.fields) + [
            "user_role",
        ]


class EmployeeNameDetailsType(EmployeeRoleDetailsType):
    user_role_details = graphene.List(RolePermissionsType)

    def resolve_user_role_details(self, info):
        non_empty_user_role = [role for role in self.user_role if role]
        user_role_key = ":".join(non_empty_user_role)
        return (
            info.context.employee_user_role_details_loader.load(user_role_key)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = list(BaseEmployeeType._meta.fields) + ["time_zone"]


class EmployeeBasicDetailsType(EmployeeRoleDetailsType):
    class Meta:
        model = Employee
        fields = list(EmployeeRoleDetailsType._meta.fields) + [
            "exit_date",
            "send_notification",
        ]


class EmployeeProfileDetailsType(BaseEmployeeType):
    class Meta:
        model = Employee
        fields = list(BaseEmployeeType._meta.fields) + ["profile_picture"]


class EmployeeBasicPayrollDetailsType(BaseEmployeeType):
    payout_frequency = graphene.Field(graphene.String)

    def resolve_payout_frequency(self, info):
        return (
            info.context.employee_payout_frequency_loader.load(self.payout_frequency)
            if self.payout_frequency is not None
            else None
        )

    class Meta:
        model = EmployeePayroll
        fields = [
            "employee_email_id",
            "payout_frequency",
            "joining_date",
            "effective_start_date",
            "effective_end_date",
            "designation",
        ]


class EmployeePayrollType(DjangoObjectType):
    payout_frequency = graphene.Field(graphene.String)

    def resolve_payout_frequency(self, info):
        return (
            info.context.employee_payout_frequency_loader.load(self.payout_frequency)
            if self.payout_frequency is not None
            else None
        )

    class Meta:
        model = EmployeePayroll
        exclude = ["fixed_pay", "variable_pay"]


class EmployeePayrollWithoutPay(DjangoObjectType):
    employee_payroll = graphene.List(EmployeePayrollType)

    def resolve_employee_payroll(self, info):
        return (
            info.context.employee_payroll_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = ["employee_email_id", "first_name", "last_name", "exit_date"]


class BCRMapItem(graphene.ObjectType):
    key = graphene.String()
    value = graphene.String()


class EmployeePayrollTypeWithPay(DjangoObjectType):
    variable_pay = graphene.Field(graphene.String)
    fixed_pay = graphene.Field(graphene.String)
    on_target_variable_pay = graphene.Field(graphene.String)
    payout_frequency = graphene.Field(graphene.String)
    bcr = graphene.List(BCRMapItem, psd=graphene.String(), ped=graphene.String())

    def resolve_variable_pay(self, info):
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.MANAGE_USERS.value
        )
        ui_permission = get_user_permissions(client_id, login_user_id)
        variable_pay = True
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            if self.employee_email_id not in payee_emails:
                variable_pay = False
        if (
            variable_pay
            and "permissions" in ui_permission
            and "view:payroll" in ui_permission["permissions"]
        ):
            return self.variable_pay if self.variable_pay is not None else None
        return None

    def resolve_fixed_pay(self, info):
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.MANAGE_USERS.value
        )
        ui_permission = get_user_permissions(client_id, login_user_id)
        fixed_pay = True
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            if self.employee_email_id not in payee_emails:
                fixed_pay = False
        if (
            fixed_pay
            and "permissions" in ui_permission
            and "view:payroll" in ui_permission["permissions"]
        ):
            return self.fixed_pay if self.fixed_pay is not None else None
        return None

    def resolve_on_target_variable_pay(self, info):
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.MANAGE_USERS.value
        )
        ui_permission = get_user_permissions(client_id, login_user_id)
        on_target_variable_pay = True
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            if self.employee_email_id not in payee_emails:
                on_target_variable_pay = False
        if (
            on_target_variable_pay
            and "permissions" in ui_permission
            and "view:payroll" in ui_permission["permissions"]
        ):
            is_effective_date_based = (
                hasattr(info.context, "is_effective_date_based")
                and info.context.is_effective_date_based
            )
            effective_date = (
                info.context.effective_date
                if is_effective_date_based
                and hasattr(info.context, "effective_date")
                and info.context.effective_date
                else make_aware(datetime.datetime.now())
            )

            variable_pay_per_period = get_variable_pay_per_period(
                client_id,
                self.variable_pay,
                self.payout_frequency,
                effective_date,
            )
            return variable_pay_per_period if self.variable_pay is not None else None
        return None

    def resolve_payout_frequency(self, info):
        return (
            info.context.employee_payout_frequency_loader.load(self.payout_frequency)
            if self.payout_frequency is not None
            else None
        )

    def resolve_bcr(self, info, **kwargs):
        client_id = info.context.client_id

        # Create period start and end date
        psd = (
            make_aware(
                start_of_day(dateutil.parser.parse(kwargs["psd"], dayfirst=False))
            )
            if kwargs["psd"]
            else None
        )
        ped = (
            make_aware(end_of_day(dateutil.parser.parse(kwargs["ped"], dayfirst=True)))
            if kwargs["ped"]
            else None
        )

        bcr_list = get_bcr_metrics_for_payee_period(
            client_id, self.employee_email_id, psd, ped
        )

        return [
            BCRMapItem(key=str(k), value=str(v)) for d in bcr_list for k, v in d.items()
        ]

    class Meta:
        model = EmployeePayroll


class EmployeeHierarchyType(DjangoObjectType):
    manager_details = graphene.Field(EmployeeProfileDetailsType)

    def resolve_manager_details(self, info):
        return (
            info.context.manager_details_loader.load(self.reporting_manager_email_id)
            if self.reporting_manager_email_id is not None
            else None
        )

    class Meta:
        model = Hierarchy


class CriteriaType(DjangoObjectType):
    class Meta:
        model = PlanCriteria
        fields = ["criteria_id", "criteria_name"]


class EmployeePlanType(DjangoObjectType):
    plan_name = graphene.Field(graphene.String)
    plan_criterias = graphene.List(CriteriaType)

    def resolve_plan_name(self, info):
        return (
            info.context.employee_plan_name_loader.load(self.plan_id)
            if self.plan_id is not None
            else None
        )

    def resolve_plan_criterias(self, info):
        return (
            info.context.employee_plan_criteria_loader.load(self.plan_id)
            if self.plan_id is not None
            else None
        )

    class Meta:
        model = PlanDetails
        fields = [
            "employee_email_id",
            "plan_id",
            "plan_name",
            "effective_start_date",
            "effective_end_date",
            "settlement_end_date",
        ]


class EmployeeSpiffPlanType(DjangoObjectType):
    plan_name = graphene.Field(graphene.String)
    plan_criterias = graphene.List(CriteriaType)

    def resolve_plan_name(self, info):
        return (
            info.context.employee_plan_name_loader.load(self.plan_id)
            if self.plan_id is not None
            else None
        )

    def resolve_plan_criterias(self, info):
        return (
            info.context.employee_plan_criteria_loader.load(self.plan_id)
            if self.plan_id is not None
            else None
        )

    class Meta:
        model = PlanDetails
        fields = [
            "employee_email_id",
            "plan_id",
            "plan_name",
            "effective_start_date",
            "effective_end_date",
        ]


class DrawType(DjangoObjectType):
    class Meta:
        model = Draws
        fields = ["draws", "draw_year"]


class HrisFieldsMapType(graphene.ObjectType):
    user_field = graphene.String()
    field_context = graphene.String()


class CustomFieldDataHistoryType(DjangoObjectType):
    data = graphene.Field(graphene.JSONString)
    effective_start_date = graphene.Field(graphene.DateTime)
    effective_end_date = graphene.Field(graphene.DateTime)

    def resolve_data(self, info):
        return self.data

    def resolve_effective_start_date(self, info):
        return self.effective_start_date

    def resolve_effective_end_date(self, info):
        return self.effective_end_date

    class Meta:
        model = CustomFieldData
        fields = ["data", "effective_start_date", "effective_end_date"]


class EmployeeCustomFieldDataType(DjangoObjectType):
    custom_field_data = graphene.Field(graphene.JSONString)
    latest_custom_field_data = graphene.Field(CustomFieldDataHistoryType)
    custom_fields_history = graphene.List(CustomFieldDataHistoryType)

    @permission_required(
        [
            RbacPermissions.VIEW_USERS.value,
            RbacPermissions.MANAGE_USERCUSTOMFIELD.value,
        ]
    )
    def resolve_custom_field_data(self, info):
        return self.data

    @permission_required(
        [
            RbacPermissions.VIEW_USERS.value,
            RbacPermissions.MANAGE_USERCUSTOMFIELD.value,
        ]
    )
    def resolve_latest_custom_field_data(self, info):
        client_id = info.context.client_id
        email = self.email
        return CustomFieldDataAccessor(client_id).get_latest_custom_field_data(email)

    @permission_required(
        [
            RbacPermissions.MANAGE_USERS.value,
            RbacPermissions.MANAGE_USERCUSTOMFIELD.value,
        ]
    )
    def resolve_custom_fields_history(self, info):
        client_id = info.context.client_id
        email = self.email
        return fetch_historic_custom_field_data(client_id, email)

    class Meta:
        model = CustomFieldData


class EmployeePayrollPlanDetailsType(EmployeeRoleDetailsType):
    employee_payroll = graphene.List(EmployeeBasicPayrollDetailsType)
    employee_plan_details = graphene.List(EmployeePlanType)

    def resolve_employee_payroll(self, info):
        return (
            info.context.employee_payroll_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_plan_details(self, info):
        return (
            info.context.employee_plan_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = list(EmployeeRoleDetailsType._meta.fields) + ["profile_picture"]


class AllEmployeesWithLimit(EmployeeRoleDetailsType):
    employee_payroll = graphene.List(EmployeeBasicPayrollDetailsType)
    employee_plan_details = graphene.List(EmployeePlanType)
    employee_draw = graphene.List(DrawType)
    employee_spiff_plan_details = graphene.List(EmployeeSpiffPlanType)
    employee_all_plan_details = graphene.List(EmployeePlanType)

    def resolve_employee_plan_details(self, info):
        return (
            info.context.employee_plan_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_spiff_plan_details(self, info):
        return (
            info.context.employee_spiff_plan_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_draw(self, info):
        return (
            info.context.employee_draw_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_all_plan_details(self, info):
        return (
            info.context.employee_main_and_spiff_plan_loader.load(
                self.employee_email_id
            )
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_payroll(self, info):
        return (
            info.context.employee_payroll_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = list(EmployeeRoleDetailsType._meta.fields)


class EmployeeHierarchyBasicType(DjangoObjectType):
    employee_hierarchy = graphene.List(EmployeeHierarchyType)

    def resolve_employee_hierarchy(self, info):
        return (
            info.context.employee_hierarchy_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = ["employee_email_id", "first_name", "last_name"]


class BaseEmployeeForPlanType(DjangoObjectType):
    reportees = graphene.List(EmployeeHierarchyType)

    def resolve_reportees(self, info):
        return (
            info.context.employee_reportees_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee
        fields = ["employee_email_id", "first_name", "last_name", "profile_picture"]


class EmployeePayType(DjangoObjectType):
    has_payroll = graphene.Field(graphene.Boolean)
    has_hierarchy = graphene.Field(graphene.Boolean)
    employee_payroll = graphene.List(EmployeePayrollTypeWithPay)
    employee_hierarchy = graphene.List(EmployeeHierarchyType)
    employee_plan_details = graphene.List(EmployeePlanType)
    hris_fields = graphene.List(HrisFieldsMapType)
    employee_spiff_plan_details = graphene.List(EmployeeSpiffPlanType)
    employee_custom_field_data = graphene.Field(EmployeeCustomFieldDataType)
    user_role_details = graphene.List(RolePermissionsType)
    can_user_manage_admins = graphene.Field(graphene.Boolean)
    employee_all_plan_details = graphene.List(EmployeePlanType)

    def resolve_hris_fields(self, info):
        hris_config = HrisConfigAccessor(info.context.client_id).client_kd_aware()
        hris_configs = []
        for config in hris_config:
            hris_configs.append(
                {
                    "user_field": (
                        config.user_field
                        if config.field_context == "custom_fields"
                        else camel_case(config.user_field)
                    ),
                    "field_context": config.field_context,
                }
            )
        return hris_configs

    def resolve_has_payroll(self, info):
        return (
            EmployeePayrollAccessor(info.context.client_id).payroll_exists_for_email_id(
                self.employee_email_id
            )
            if self.employee_email_id is not None
            else False
        )

    def resolve_has_hierarchy(self, info):
        return (
            HierarchyAccessor(info.context.client_id).hierarchy_exists_for_email_id(
                self.employee_email_id
            )
            if self.employee_email_id is not None
            else False
        )

    def resolve_user_role_details(self, info):
        non_empty_user_role = [role for role in self.user_role if role]
        user_role_key = ":".join(non_empty_user_role)
        return (
            info.context.employee_user_role_details_loader.load(user_role_key)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_payroll(self, info):
        return (
            info.context.employee_payroll_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_hierarchy(self, info):
        return (
            info.context.employee_hierarchy_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_plan_details(self, info):
        return (
            info.context.employee_plan_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_spiff_plan_details(self, info):
        return (
            info.context.employee_spiff_plan_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_employee_all_plan_details(self, info):
        return (
            info.context.employee_main_and_spiff_plan_loader.load(
                self.employee_email_id
            )
            if self.employee_email_id is not None
            else None
        )

    @permission_required(
        [
            RbacPermissions.VIEW_USERS.value,
            RbacPermissions.MANAGE_USERCUSTOMFIELD.value,
        ]
    )
    def resolve_employee_custom_field_data(self, info):
        return (
            info.context.custom_field_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_can_user_manage_admins(self, info):
        non_empty_user_role = [role for role in self.user_role if role]
        user_role_key = ":".join(non_empty_user_role)
        return (
            info.context.can_user_manage_admins_loader.load(user_role_key)
            if self.employee_email_id is not None
            else None
        )

    class Meta:
        model = Employee


class EmployeeDataAndCount(graphene.ObjectType):
    employees = graphene.List(EmployeePayType)
    employees_count = graphene.Int()


class GraphQLManagersObjectType(graphene.ObjectType):
    headers = graphene.List(graphene.String)
    data = graphene.List(graphene.List(graphene.String))


class EmployeePaginatedType(graphene.ObjectType):
    full_name = graphene.String()
    employee_email_id = graphene.String()


class EmployeeApprovalType(graphene.ObjectType):
    label = graphene.String()
    value = graphene.String()


class EmployeeConfigQuery(object):
    all_employees_with_limit = graphene.Field(
        graphene.List(AllEmployeesWithLimit),
        user_status=graphene.String(),
        user_role=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        component=graphene.String(),
    )

    all_employees_with_pay = graphene.Field(
        EmployeeDataAndCount,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
    )

    all_employee_payroll = graphene.List(EmployeePayrollType)
    all_employee_plan = graphene.List(EmployeePlanType)
    employee_pay = graphene.Field(EmployeePayType, email_id=graphene.String())
    employee_pay_time_slice = graphene.Field(
        graphene.JSONString, email_id=graphene.String(), term_end_date=graphene.String()
    )
    employee_plan_details = graphene.Field(
        graphene.List(EmployeePlanType), email_id=graphene.String()
    )
    employee_spiff_plan_details = graphene.Field(
        graphene.List(EmployeeSpiffPlanType), email_id=graphene.String()
    )
    employee_pay_for_all = graphene.Field(
        EmployeePayType,
        email_id=graphene.String(),
        effective_date=graphene.String(),
        secondary_kd_aware=graphene.Boolean(),
        component=graphene.String(),
    )
    employee_basic_hierarchy_details = graphene.Field(
        EmployeeHierarchyBasicType,
    )
    has_reportees_given_component = graphene.Field(
        graphene.Boolean, email_id=graphene.String(), component=graphene.String()
    )
    payee_role = graphene.Field(graphene.String, email_id=graphene.String())
    has_reportee = graphene.Field(graphene.Boolean, email_id=graphene.String())
    has_reportee_quota = graphene.Field(graphene.Boolean, email_id=graphene.String())
    has_reportee_draws = graphene.Field(graphene.Boolean, email_id=graphene.String())
    has_own_quota = graphene.Field(graphene.Boolean, email_id=graphene.String())
    has_own_draws = graphene.Field(graphene.Boolean, email_id=graphene.String())
    get_employee_config = graphene.Field(
        graphene.JSONString, email_id=graphene.String()
    )
    all_employee_names = graphene.List(
        EmployeeRoleDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        config_users_only=graphene.Boolean(),
    )
    employee_name_details = graphene.List(
        BaseEmployeeType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        component=graphene.String(),
        user_role=graphene.String(),
    )
    employee = graphene.Field(
        EmployeePayType,
        email_id=graphene.String(),
        component=graphene.String(required=True),
    )
    employee_quota_draw_module = graphene.Field(
        EmployeePayrollWithoutPay,
        email_id=graphene.String(),
    )
    employee_user_module = graphene.Field(
        EmployeePayType,
        email_id=graphene.String(),
    )
    employee_payout_module = graphene.Field(
        EmployeePayrollWithoutPay,
        email_id=graphene.String(),
    )

    employee_role_details = graphene.List(
        EmployeeRoleDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        user_role=graphene.String(),
    )
    employee_role_details_user_module = graphene.List(
        EmployeeRoleDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        user_role=graphene.String(),
    )
    employee_basic_details = graphene.List(
        EmployeeBasicDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        component=graphene.String(),
        user_role=graphene.String(),
    )
    employee_profile_details = graphene.List(
        EmployeeProfileDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        user_role=graphene.String(),
        context=graphene.String(),
    )
    employee_profile_details_user_module = graphene.List(
        EmployeeProfileDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        user_role=graphene.String(),
        context=graphene.String(),
        selected_options=graphene.List(graphene.String),
        is_to_fetch_exited_users=graphene.Boolean(),
    )
    employee_profile_details_payout_module = graphene.List(
        EmployeeProfileDetailsType,
        user_status=graphene.String(),
        as_of_date=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
        user_role=graphene.String(),
        context=graphene.String(),
    )
    employee_basic_detail = graphene.Field(
        EmployeeNameDetailsType,
        email_id=graphene.String(),
    )
    employee_name_detail = graphene.Field(
        BaseEmployeeType,
        email_id=graphene.String(),
    )
    employee_profile_detail = graphene.Field(
        EmployeeProfileDetailsType,
        email_id=graphene.String(),
    )

    employee_by_client = graphene.Field(
        graphene.List(EmployeeBasicDetailsType),
        client_id=graphene.Int(required=True),
        user_status=graphene.String(),
    )
    employee_by_approver = graphene.List(
        EmployeeApprovalType,
        period=graphene.String(required=True),
        search_term=graphene.String(),
        limit_value=graphene.Int(),
        offset_value=graphene.Int(),
        approver=graphene.String(),
        status=graphene.String(),
    )
    all_approvers_by_period = graphene.List(
        EmployeeApprovalType,
        period=graphene.String(required=True),
        search_term=graphene.String(),
        payee=graphene.String(),
        status=graphene.String(),
        limit_value=graphene.Int(),
        offset_value=graphene.Int(),
    )
    ever_part_of_plan = graphene.Field(graphene.Boolean, email_id=graphene.String())
    all_managers = graphene.List(EmployeeHierarchyType)
    all_managers_with_limit = graphene.Field(
        GraphQLManagersObjectType,
        limit_value=graphene.Int(),
        first_name=graphene.String(),
        email=graphene.String(),
        search_term=graphene.String(),
    )
    managers_with_limit = graphene.Field(
        GraphQLManagersObjectType,
        limit_value=graphene.Int(),
        offset_full_name=graphene.String(),
        offset_email=graphene.String(),
        search_term=graphene.String(),
    )
    quick_filters_count = graphene.Field(graphene.JSONString)
    all_employee_email = graphene.Field(
        graphene.JSONString,
        search_term=graphene.String(),
    )
    pending_approval_request_count = graphene.Field(
        graphene.Int, email_id=graphene.String()
    )
    pending_plan_approval_request_count = graphene.Field(
        graphene.Int, email_id=graphene.String()
    )
    all_employees_by_payout_frequency_paginated = graphene.List(
        EmployeePaginatedType,
        payout_frequency=graphene.String(),
        limit_value=graphene.Int(),
        full_name=graphene.String(),
        email=graphene.String(),
        search_term=graphene.String(),
    )
    pending_comm_adj_request_count = graphene.Field(
        graphene.Int, email_id=graphene.String()
    )

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_all_employees_by_payout_frequency_paginated(self, info, **kwargs):
        payout_frequency = kwargs.get("payout_frequency")
        limit = kwargs.get("limit_value")
        full_name = kwargs.get("full_name")
        email = kwargs.get("email")
        search_term = kwargs.get("search_term")
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.QUOTAS_DRAWS.value
        )
        payee_email = None
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_email = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            user_permissions = get_ui_permissions(client_id, login_user_id)
            own_data_permission = "manage:owndata" in user_permissions
            if not own_data_permission:
                if payee_email and login_user_id in payee_email:
                    payee_email.remove(login_user_id)
        return get_payee_by_payout_frequency(
            info.context.client_id,
            payout_frequency,
            limit,
            search_term,
            full_name,
            email,
            payee_email=payee_email,
        )

    @permission_required(
        [
            RbacPermissions.VIEW_USERS.value,
            RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
        ]
    )
    def resolve_all_employees_with_limit(self, info, **kwargs):
        user_status = kwargs.get("user_status")
        user_role = kwargs.get("user_role")
        as_of_date = kwargs.get("as_of_date")
        offset = kwargs.get("offset_value")
        limit = kwargs.get("limit_value")
        search_term = kwargs.get("search_term")
        component = kwargs.get("component")
        login_user_id = info.context.user.username
        client_id = info.context.client_id
        if not component:
            payee_emails = [login_user_id]
        else:
            payee_emails = None
            data_permission = get_data_permission(client_id, login_user_id, component)
            if not data_permission:
                raise GraphQLError("Permission denied")
            if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
                payee_emails = get_valid_payee_emails(
                    client_id, login_user_id, data_permission
                )
        return EmployeeAccessor(info.context.client_id).get_all_employees_with_limit(
            as_of_date=as_of_date,
            user_status=user_status,
            user_role=user_role,
            search_term=search_term,
            offset=offset,
            limit=limit,
            email_list=payee_emails,
        )

    @permission_required(
        [RbacPermissions.VIEW_PAYROLL.value, RbacPermissions.VIEW_USERS.value]
    )
    def resolve_all_employees_with_pay(self, info, **kwargs):
        user_status = kwargs.get("user_status")
        as_of_date = kwargs.get("as_of_date")
        offset = kwargs.get("offset_value")
        limit = kwargs.get("limit_value")
        search_term = kwargs.get("search_term")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        logged_email = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.MANAGE_USERS.value
        )
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission
            )
            return EmployeeAccessor(
                info.context.client_id
            ).get_all_employees_with_search_limit(
                as_of_date=as_of_date,
                user_status=user_status,
                search_term=search_term,
                offset=offset,
                limit=limit,
                payee_emails=payee_emails,
            )
        else:
            return EmployeeAccessor(
                info.context.client_id
            ).get_all_employees_with_search_limit(
                as_of_date=as_of_date,
                user_status=user_status,
                search_term=search_term,
                offset=offset,
                limit=limit,
            )

    @permission_required(RbacPermissions.VIEW_PAYROLL.value)
    def resolve_all_employee_payroll(self, info, **kwargs):
        return EmployeePayrollAccessor(info.context.client_id).get_all_employees()

    @permission_required(RbacPermissions.VIEW_USERS.value)
    def resolve_all_employee_plan(self, info, **kwargs):
        return PlanDetailsAccessor(
            info.context.client_id
        ).get_all_employee_plan_details()

    @permission_required(RbacPermissions.VIEW_PAYROLL.value)
    def resolve_employee_pay(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        logged_user = info.context.user.username

        # Check for data permission of logged in user: manage users
        client_id = info.context.client_id
        is_authorized = authorize_for_profile_lookup(
            client_id=client_id,
            login_user_id=logged_user,
            email_id=email_id,
            component=RBACComponent.MANAGE_USERS.value,
        )
        if not is_authorized:
            logger.info(
                "User: %s is not authorized to view employee pay details of %s",
                logged_user,
                email_id,
            )
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        return EmployeeAccessor(client_id).get_employee(email_id)

    @permission_required(
        [
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
        ]
    )
    def resolve_employee_pay_time_slice(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        term_end_date = kwargs.get("term_end_date")
        esd = make_aware(
            end_of_day(dateutil.parser.parse(term_end_date, dayfirst=True))
        )

        # Check for data permission of logged in user: payouts statements
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            client_id=client_id,
            login_user_id=login_user_id,
            email_id=email_id,
            component=RBACComponent.PAYOUTS_STATEMENTS.value,
        )
        if not is_authorized:
            logger.info(
                "User: %s don't have permission to view payroll details of %s",
                login_user_id,
                email_id,
            )
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        payee_payroll = get_payee_payrolls_secondary_kd_aware(
            info.context.client_id, esd, [email_id]
        )
        pay_currency = payee_payroll[0]["pay_currency"]
        currency_symbol = (
            CountriesAccessor(client_id)
            .get_currency_symbol(pay_currency)
            .currency_symbol
        )
        return {"currency": pay_currency, "currency_symbol": currency_symbol}

    @permission_required(
        [
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
        ]
    )
    def resolve_employee_plan_details(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        return (
            info.context.employee_plan_loader.load(email_id)
            if email_id is not None
            else None
        )

    @permission_required(
        [
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_PAYOUTS.value,
            RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
        ]
    )
    def resolve_employee_spiff_plan_details(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        return (
            info.context.employee_spiff_plan_loader.load(email_id)
            if email_id is not None
            else None
        )

    @permission_required(
        [
            RbacPermissions.VIEW_ADMINDASHBOARD.value,
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
        ]
    )
    def resolve_employee_basic_hierarchy_details(self, info, **kwargs):
        login_user_id = info.context.user.username
        return EmployeeAccessor(info.context.client_id).get_employee(login_user_id)

    @permission_required(
        [
            RbacPermissions.VIEW_PAYEEDASHBOARD.value,
            RbacPermissions.VIEW_STATEMENTS.value,
            RbacPermissions.VIEW_PAYOUTS.value,
        ]
    )
    def resolve_employee_pay_for_all(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        effective_date = kwargs.get("effective_date")
        secondary_kd_aware = kwargs.get("secondary_kd_aware", False)
        component = kwargs.get("component")
        if effective_date:
            if has_feature(info.context.client_id, "custom_calendar"):
                effective_date = make_aware(
                    end_of_day(datetime.datetime.strptime(effective_date, "%Y-%m-%d"))
                )
            else:
                effective_date = make_aware(
                    last_day_of_month(
                        datetime.datetime.strptime(effective_date, "%Y-%m-%d")
                    )
                )
            if secondary_kd_aware:
                info.context.secondary_kd_aware = True
            info.context.is_effective_date_based = True
            info.context.effective_date = effective_date
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup_v2(
            info.context.client_id,
            login_user_id,
            email_id,
            component,
        )
        if is_authorized:
            return EmployeeAccessor(info.context.client_id).get_employee(email_id)
        raise GraphQLError("Permission denied")

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_has_reportees_given_component(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        component = RBACComponent.QUOTAS_DRAWS.value

        # Check for data permission of logged in user
        login_user_id = info.context.user.username
        is_authorized = authorize_for_profile_lookup(
            client_id=info.context.client_id,
            login_user_id=login_user_id,
            email_id=email_id,
            component=component,
        )
        if not is_authorized:
            logger.info("User: %s don't have permission to view hierarchy", email_id)
            raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

        return HierarchyAccessor(info.context.client_id).reportees_exist_for_email(
            email_id, timezone.now()
        )

    @permission_required(
        [
            RbacPermissions.VIEW_DASHBOARD.value,
        ]
    )
    def resolve_employee(self, info, **kwargs):
        email_id = kwargs.get("email_id")
        component = kwargs.get("component")
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id, login_user_id, [email_id], component
        )
        if is_authorized:
            return EmployeeAccessor(info.context.client_id).get_employee(email_id)
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            return response

    @permission_required(RbacPermissions.VIEW_USERS.value)
    def resolve_employee_user_module(self, info, **kwargs):
        info.context.component = RBACComponent.MANAGE_USERS.value
        return CommonEmployeeConfigResolver().resolve_common_employee(info, **kwargs)

    @permission_required([RbacPermissions.VIEW_STATEMENTS.value])
    def resolve_employee_payout_module(self, info, **kwargs):
        info.context.component = RBACComponent.MANAGE_USERS.value
        return CommonEmployeeConfigResolver().resolve_common_employee(info, **kwargs)

    @permission_required(
        [RbacPermissions.VIEW_DRAWS.value, RbacPermissions.VIEW_QUOTAS.value]
    )
    def resolve_employee_quota_draw_module(self, info, **kwargs):
        info.context.component = RBACComponent.QUOTAS_DRAWS.value
        return CommonEmployeeConfigResolver().resolve_common_employee(info, **kwargs)

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_payee_role(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        date = make_aware(datetime.datetime.now())
        payroll = EmployeePayrollAccessor(client_id).get_current_valid_employee_payroll(
            date, [email_id]
        )
        if len(payroll) > 0:
            return payroll[0]["payee_role"]
        else:
            return None

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_has_reportee(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        has_reportees = HierarchyAccessor(client_id).reportees_exist_for_email(
            email_id, timezone.now()
        )
        return has_reportees

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_has_reportee_quota(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        reportee_emails = get_own_dynamic_flat_team_email_ids(client_id, email_id)
        # removing logged in user from the reportee list
        if email_id in reportee_emails:
            reportee_emails.remove(email_id)
        reportee_quotas = EmployeeQuotaAccessor(client_id).employees_quota_exists(
            reportee_emails
        )
        return reportee_quotas

    @permission_required(
        [RbacPermissions.VIEW_DRAWS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_has_reportee_draws(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        reportee_emails = get_own_dynamic_flat_team_email_ids(client_id, email_id)
        # removing logged in user from the reportee list
        if email_id in reportee_emails:
            reportee_emails.remove(email_id)
        reportee_draws = DrawsAccessor(client_id).draws_exists(reportee_emails)
        return reportee_draws

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_has_own_quota(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        has_own_quota = EmployeeQuotaAccessor(client_id).employees_quota_exists(
            [email_id]
        )
        return has_own_quota

    @permission_required(
        [RbacPermissions.VIEW_DRAWS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_has_own_draws(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        has_draws = DrawsAccessor(client_id).draws_exists([email_id])
        return has_draws

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_get_employee_config(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        emp = EmployeeAccessor(client_id).get_employee(email_id)
        return emp.employee_config

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_DATASHEETPERMISSIONS.value,
            RbacPermissions.VIEW_TEAMS.value,
            RbacPermissions.MANAGE_USERS.value,
            RbacPermissions.MANAGE_CONFIG.value,
        ]
    )
    def resolve_employee_name_details(self, info, **kwargs):
        return employee_details_based_on_permission(info, **kwargs)

    @permission_required(
        [RbacPermissions.MANAGE_CONFIG.value, RbacPermissions.MANAGE_DATASETTINGS.value]
    )
    def resolve_employee_role_details(self, info, **kwargs):
        # Returns All Employees Data - Should be used only for Modules with No Data Permission
        info.context.component = None  # Pass Component as None to get all employees
        return CommonEmployeeConfigResolver().resolve_common_employee_with_limit(
            info, **kwargs
        )

    @permission_required(RbacPermissions.VIEW_TEAMS.value)
    def resolve_employee_role_details_user_module(self, info, **kwargs):
        # Returns Employees Based on RBAC User Data Permission
        info.context.component = RBACComponent.MANAGE_USERS.value
        return CommonEmployeeConfigResolver().resolve_common_employee_with_limit(
            info, **kwargs
        )

    @permission_required(RbacPermissions.MANAGE_USERS.value)
    def resolve_employee_basic_details(self, info, **kwargs):
        return employee_details_based_on_permission(info, **kwargs)

    @permission_required(RbacPermissions.MANAGE_ROLES.value)
    def resolve_employee_profile_details(self, info, **kwargs):
        # Returns All Employees Data - Should be used only for Modules with No Data Permission
        info.context.component = None  # Pass Component as None to get all employees
        return CommonEmployeeConfigResolver().resolve_common_employee_with_limit(
            info, **kwargs
        )

    @permission_required(RbacPermissions.MANAGE_USERGROUPS.value)
    def resolve_employee_profile_details_user_module(self, info, **kwargs):
        # Returns Employees Based on RBAC User Data Permission
        info.context.component = RBACComponent.MANAGE_USERS.value
        return CommonEmployeeConfigResolver().resolve_common_employee_with_limit(
            info, **kwargs
        )

    @permission_required(RbacPermissions.MANAGE_CONFIG.value)
    def resolve_employee_profile_details_payout_module(self, info, **kwargs):
        # Returns Employees Based on RBAC Payout Data Permission
        info.context.component = RBACComponent.PAYOUTS_STATEMENTS.value
        return CommonEmployeeConfigResolver().resolve_common_employee_with_limit(
            info, **kwargs
        )

    @permission_required(
        [RbacPermissions.MANAGE_USERS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_employee_basic_detail(self, info, **kwargs):
        client_id = info.context.client_id
        email_id = info.context.user.username
        return EmployeeAccessor(client_id).get_employee(email_id)

    @permission_required(
        [RbacPermissions.VIEW_DASHBOARD.value, RbacPermissions.VIEW_QUOTAS.value]
    )
    def resolve_employee_name_detail(self, info, **kwargs):
        client_id = info.context.client_id
        email_id = kwargs.get("email_id")
        return EmployeeAccessor(client_id).get_employee(email_id)

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_employee_profile_detail(self, info, **kwargs):
        client_id = info.context.client_id
        email_id = info.context.user.username
        return EmployeeAccessor(client_id).get_employee(email_id)

    @permission_required(
        [RbacPermissions.VIEW_QUERIES.value, RbacPermissions.MANAGE_CONFIG.value]
    )
    def resolve_all_employee_names(self, info, **kwargs):
        client_id = info.context.client_id
        user_status = kwargs.get("user_status")
        as_of_date = kwargs.get("as_of_date")
        offset = kwargs.get("offset_value")
        limit = kwargs.get("limit_value")
        search_term = kwargs.get("search_term")
        config_users_only = kwargs.get("config_users_only")
        view_queries_roles = RolePermissionsAccessor(
            client_id
        ).get_all_roles_based_on_queries_permission(RbacPermissions.VIEW_QUERIES.value)
        employee_list = []
        if config_users_only:
            client_quer_config = get_query_setting_config(client_id)
            employee_list = (
                client_quer_config.get("selected_user", [])
                if client_quer_config
                else []
            )

        return EmployeeAccessor(info.context.client_id).get_all_employees_with_limit(
            as_of_date=as_of_date,
            user_status=user_status,
            search_term=search_term,
            offset=offset,
            limit=limit,
            email_list=employee_list,
            user_role=view_queries_roles,
        )

    @permission_required(RbacPermissions.MANAGE_ADMINUI.value)
    def resolve_employee_by_client(self, info, client_id, user_status):
        return EmployeeAccessor(client_id).get_all_employees(user_status=user_status)

    @permission_required(
        [RbacPermissions.VIEW_STATEMENTS.value, RbacPermissions.VIEW_EVERSTAGE.value]
    )
    def resolve_ever_part_of_plan(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        part_of_plan = PlanPayeeAccessor(client_id).employees_plan_exists(email_id)
        return part_of_plan

    @permission_required(
        [RbacPermissions.VIEW_USERS.value, RbacPermissions.VIEW_PAYOUTS.value]
    )
    def resolve_all_managers(self, info):
        logged_email = info.context.user.username
        client_id = info.context.client_id
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.MANAGE_USERS.value
        )
        all_managers = HierarchyAccessor(info.context.client_id).get_all_managers()
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission
            )
            return list(set(payee_emails) & set(all_managers))
        else:
            return all_managers

    @permission_required(RbacPermissions.VIEW_ADMINDASHBOARD.value)
    def resolve_all_managers_with_limit(self, info, **kwargs):
        limit = kwargs.get("limit_value")
        first_name = kwargs.get("first_name")
        email = kwargs.get("email")
        search_term = kwargs.get("search_term")
        return get_matching_lead_employees(
            info.context.client_id,
            limit=limit,
            first_name=first_name,
            email=email,
            search_term=search_term,
        )

    @permission_required(RbacPermissions.VIEW_PAYEEDASHBOARD.value)
    def resolve_managers_with_limit(self, info, **kwargs):
        limit = kwargs.get("limit_value")
        offset_full_name = kwargs.get("offset_full_name")
        offset_email = kwargs.get("offset_email")
        search_term = kwargs.get("search_term")

        result = get_reporting_managers(
            info.context.client_id,
            info.context.user.username,
            limit=limit,
            search=search_term,
            full_name_offset=offset_full_name,
            email_offset=offset_email,
        )
        if (
            isinstance(result, dict)
            and "headers" in result
            and isinstance(result["headers"], dict)
        ):
            headers_dict = result["headers"]
            header_keys = [
                key for key, _ in sorted(headers_dict.items(), key=lambda item: item[1])
            ]  # Solving type check in gql (being extra cautious on order)
            result["headers"] = header_keys
        return result

    @permission_required(RbacPermissions.VIEW_USERS.value)
    def resolve_quick_filters_count(self, info):
        result = get_all_filters_count(
            info.context.client_id, info.context.user.username
        )
        return result

    @permission_required(RbacPermissions.VIEW_USERS.value)
    def resolve_all_employee_email(self, info, **kwargs):
        search_term = kwargs.get("search_term")
        return EmployeeAccessor(
            info.context.client_id
        ).get_all_employees_with_search_limit(
            search_term=search_term, email_only=True, user_status="All"
        )

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_pending_approval_request_count(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        client_features = get_client_features(client_id)
        is_entity_line_item_level = (
            client_features.get("approval_config", {})
            .get("payout_approvals", {})
            .get("line_item_level_approval", False)
        )
        if is_entity_line_item_level:
            return SubApprovalRequestsAccessor(
                client_id
            ).get_all_requests_count_by_approver_and_status(
                approver_email=email_id, status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value
            )
        return ApprovalRequestsAccessor(client_id).get_all_requests_count(
            approver_email=email_id,
            status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            entity_type=APPROVAL_ENTITY_TYPES.PAYOUT.value,
        )

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_pending_plan_approval_request_count(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id

        return ApprovalRequestsAccessor(client_id).get_all_requests_count(
            approver_email=email_id,
            status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            entity_type=APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
        )

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_employee_by_approver(self, info, period, **kwargs):
        search_term = kwargs.get("search_term")
        approver = kwargs.get("approver")
        status = kwargs.get("status")
        limit = kwargs.get("limit_value")
        offset = kwargs.get("offset_value")
        return get_payees_from_approvals_for_approver(
            client_id=info.context.client_id,
            period=period,
            login_user_id=info.context.user.username,
            search_term=search_term,
            approver=approver,
            status=status,
            limit=limit,
            offset=offset,
        )

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_all_approvers_by_period(self, info, period, **kwargs):
        search_term = kwargs.get("search_term")
        payee = kwargs.get("payee")
        status = kwargs.get("status")
        limit = kwargs.get("limit_value")
        offset = kwargs.get("offset_value")
        return get_all_approvers(
            client_id=info.context.client_id,
            period=period,
            login_user_id=info.context.user.username,
            search_term=search_term,
            payee=payee,
            status=status,
            limit=limit,
            offset=offset,
        )

    @permission_required(RbacPermissions.VIEW_EVERSTAGE.value)
    def resolve_pending_comm_adj_request_count(self, info, **kwargs):
        email_id = info.context.user.username
        client_id = info.context.client_id
        return ApprovalRequestsAccessor(client_id).get_adj_aprrovals_count_for_approver(
            approver_email=email_id,
            entity_type=APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value,
            status="requested",
        )
