"""
Mutation config for createAccessTokenConfig query.
"""

import graphene
from django.db import transaction

from commission_engine.accessors.etl_config_accessor import AccessTokenConfigAccessor
from commission_engine.services.etl_config_validation_service import (
    InvalidCredentialsError,
    test_connection_returning_domain,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation


class CreateAccessTokenConfig(graphene.Mutation):
    """
    GQL mutation to create a single AccessTokenConfig config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        service_name = graphene.String(required=True)
        access_request_body = graphene.JSONString(required=False)
        api_access_key = graphene.String(required=False)
        connection_name = graphene.String(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        connection_name: str,
        service_name: str,
        **kwargs,
    ):
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id

        format_service_name = service_name.lower()

        fields = {
            "connection_name": connection_name,
            "service_name": format_service_name,
        }

        try:
            if format_service_name == "salesforce":
                salesforce_env = kwargs.get("access_request_body", {}).get(
                    "salesforce_env"
                )
                access_request_body = kwargs.get("access_request_body", {}).copy()
                access_request_body.pop("salesforce_env", None)
                domain = test_connection_returning_domain(
                    service_name=format_service_name,
                    access_request_body=access_request_body,
                    salesforce_env=salesforce_env,
                )
                fields["domain"] = domain
                fields["access_request_body"] = access_request_body
                fields["additional_data"] = {
                    "salesforce_env": salesforce_env,
                }

            elif format_service_name == "hubspot":
                domain = test_connection_returning_domain(
                    service_name=format_service_name,
                    api_access_key=kwargs.get("api_access_key"),
                )
                fields["domain"] = domain
                fields["api_access_key"] = kwargs.get("api_access_key")

            AccessTokenConfigAccessor(
                client_id=accessor_client_id
            ).create_and_persist_record(fields=fields)
            return CreateAccessTokenConfig(result="Success")
        except InvalidCredentialsError:
            return CreateAccessTokenConfig(result="Invalid credentials")
