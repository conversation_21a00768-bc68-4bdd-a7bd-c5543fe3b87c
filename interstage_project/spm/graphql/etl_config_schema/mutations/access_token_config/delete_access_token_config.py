"""
Mutation config for DeleteAccessTokenConfig query.
"""

import graphene
from django.db import transaction
from django.utils import timezone

from commission_engine.accessors.etl_config_accessor import AccessTokenConfigAccessor
from commission_engine.self_service_integrations.self_service_factory import (
    SelfServiceFactory,
)
from commission_engine.services.etl_config_service import invalidate_connection_records
from commission_engine.services.hyperlink_service import HyperlinkService
from commission_engine.utils.general_data import RbacPermissions, Services
from interstage_project.auth_utils import permission_required_mutation


class DeleteAccessTokenConfig(graphene.Mutation):
    """
    GQL mutation to delete a single AccessTokenConfig config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        access_token_config_id = graphene.Int(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        access_token_config_id: int,
        **kwargs,
    ):
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id

        curr_time = timezone.now()

        atc_record = (
            AccessTokenConfigAccessor(client_id=accessor_client_id)
            .get_record_by_id(access_token_config_id=access_token_config_id)
            .last()
        )

        if not atc_record:
            raise Exception("Record not found")

        HyperlinkService(accessor_client_id).invalidate_hyperlink_records(
            access_token_id=access_token_config_id
        )

        if (atc_record.service_name or "").upper() in Services.__members__:
            SelfServiceFactory(
                service=atc_record.service_name,  # type: ignore
                client_id=accessor_client_id,
                access_token_config_id=access_token_config_id,
            ).delete()
        else:
            invalidate_connection_records(
                client_id=accessor_client_id,
                access_token_config_id=access_token_config_id,
            )

        AccessTokenConfigAccessor(client_id=accessor_client_id).invalidate_record(
            access_token_config_id=access_token_config_id, curr_time=curr_time
        )

        return DeleteAccessTokenConfig(result="Success")
