"""
Mutation config for UpdateAccessTokenConfig query.
"""

import logging

import graphene
from django.db import transaction

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ApiAccessConfigAccessor,
    ExtractionConfigAccessor,
)
from commission_engine.services.etl_config_validation_service import (
    InvalidCredentialsError,
    test_connection_returning_domain,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation

logger = logging.getLogger(__name__)


class UpdateAccessTokenConfig(graphene.Mutation):
    """
    GQL mutation to update a single AccessTokenConfig config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        access_token_config_id = graphene.Int(required=True)
        service_name = graphene.String(required=True)
        access_request_body = graphene.JSONString(required=False)
        api_access_key = graphene.String(required=False)
        connection_name = graphene.String(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        access_token_config_id: int,
        service_name: str,
        **kwargs,
    ):
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id
        fields: dict = {}

        try:
            format_service_name = service_name.lower()
            if format_service_name == "hubspot":
                test_connection_returning_domain(
                    service_name=format_service_name,
                    api_access_key=kwargs.get("api_access_key"),
                )

                fields = {
                    "api_access_key": kwargs.get("api_access_key"),
                } | kwargs

            elif format_service_name == "salesforce":
                test_connection_returning_domain(
                    service_name=format_service_name,
                    access_request_body=kwargs.get("access_request_body"),
                )

                fields = {
                    "access_request_body": kwargs.get("access_request_body"),
                } | kwargs
        except InvalidCredentialsError as err:
            logger.exception("Error while testing connection", exc_info=err)
            return UpdateAccessTokenConfig(result="Invalid credentials")

        new_access_token_config_id = AccessTokenConfigAccessor(
            client_id=accessor_client_id
        ).update_record_returning_id(
            access_token_config_id=access_token_config_id, updated_fields=fields
        )

        ApiAccessConfigAccessor(
            client_id=accessor_client_id
        ).update_access_token_config_id(
            old_access_token_config_id=access_token_config_id,
            new_access_token_config_id=new_access_token_config_id,
        )
        ExtractionConfigAccessor(
            client_id=accessor_client_id
        ).update_access_token_config_id(
            old_access_token_config_id=access_token_config_id,
            new_access_token_config_id=new_access_token_config_id,
        )

        return UpdateAccessTokenConfig(result="Success")
