"""
Mutation config for BulkCreateTransformationConfig query.
"""

import logging
from typing import Optional, Union
from uuid import UUID, uuid4

import graphene
from django.db import transaction

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ExtractionConfigAccessor,
    TransformationConfigAccessor,
)
from commission_engine.custom_types.self_service_integration_types import (
    AssociatedObjectsType,
)
from commission_engine.models.custom_object_models import CustomObject
from commission_engine.self_service_integrations.self_service_factory import (
    SelfServiceFactory,
)
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from commission_engine.utils.general_data import (
    RbacPermissions,
    Services,
    UpstreamSyncModes,
)
from everstage_ddd.upstream.extraction.accessors import UpstreamTimestampsAccessor
from interstage_project.auth_utils import permission_required_mutation
from interstage_project.utils import LogWithContext
from spm.services.custom_object_services.custom_object_service import (
    add_multiple_variable,
)

logger = logging.getLogger(__name__)


class GQLMappingType(graphene.InputObjectType):
    source_field = graphene.String(required=True)
    destination_field = graphene.String(required=True)
    field_type = graphene.String(required=True)
    destination_field_type = graphene.Int(required=True)
    is_association = graphene.Boolean(required=True)
    associated_object_id = graphene.String(required=False)


class BulkCreateTransformationConfig(graphene.Mutation):
    """
    GQL mutation to BulkCreate a single transformation config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        integration_id = graphene.String(required=True)
        source_object_id = graphene.String(required=True)
        destination_object_id = graphene.Int(required=True)
        mappings = graphene.List(GQLMappingType, required=True)
        integration_id = graphene.String(required=True)
        access_token_config_id = graphene.Int(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def mutate(
        cls,
        _,
        info,
        mappings,
        integration_id: str,
        source_object_id: str,
        destination_object_id: int,
        access_token_config_id: int,
        **kwargs,
    ):
        try:
            if destination_object_id:
                destination_object_id = str(destination_object_id)
            with transaction.atomic():
                client_id = kwargs.get("client_id")
                accessor_client_id = client_id if client_id else info.context.client_id
                request = info.context.request
                request.logger = LogWithContext()
                source_object_id = (
                    source_object_id.lower() if source_object_id else source_object_id
                )

                variables = []
                ins_mappings = []
                destination_object: Union[CustomObject, None] = (
                    CustomObjectAccessor(client_id=accessor_client_id)
                    .get_object_by_id(custom_object_id=destination_object_id)
                    .last()
                )

                if not destination_object:
                    raise Exception("Invalid CO")

                ordered_columns: Optional[list[str]] = (
                    destination_object.ordered_columns
                )

                if ordered_columns is None:
                    ordered_columns = CustomObjectVariableAccessor(
                        client_id=accessor_client_id
                    ).get_system_names_by_object_id(
                        object_id=int(destination_object_id)
                    )
                all_system_names = CustomObjectVariableAccessor(
                    client_id=accessor_client_id
                ).get_system_names_by_object_id(object_id=int(destination_object_id))
                for mp in mappings:
                    ins_obj = {}
                    # In case we are trying to map a unmapped field, dont create a variable for it
                    if (
                        mp["destination_field"].startswith(
                            f"co_{str(destination_object_id)}_"
                        )
                        and mp["destination_field"] in all_system_names
                    ):
                        continue

                    ins_obj["display_name"] = mp["destination_field"]
                    ins_obj["id"] = mp["destination_field_type"]

                    variables.append(ins_obj)

                request.data = {
                    "object_id": destination_object_id,
                    "variables": variables,
                }

                if len(variables) > 0:
                    variable_response = add_multiple_variable(
                        client_id=accessor_client_id, request=request
                    )

                    dis_sys_map = {}

                    for obj in variable_response["variable_response"]:
                        ordered_columns.append(obj["system_name"])
                        dis_sys_map[obj["display_name"]] = obj["system_name"]

                access_token_record = (
                    AccessTokenConfigAccessor(client_id=accessor_client_id)
                    .get_record_by_id(access_token_config_id=access_token_config_id)
                    .last()
                )
                service = access_token_record.service_name  # type: ignore
                sf_datetime_fields = set()
                if service == Services.SALESFORCE.value:
                    sf_fields = SelfServiceFactory(
                        service=Services.SALESFORCE.value,
                        client_id=accessor_client_id,
                        access_token_config_id=access_token_config_id,
                    ).get_all_fields_in_object(object_id=source_object_id)
                    sf_datetime_fields = set(
                        field["name"]
                        for field in sf_fields
                        if field["type"] == "datetime"
                    )

                for mp in mappings:
                    ins_map = mp
                    if (
                        ins_map["destination_field"].startswith(
                            f"co_{str(destination_object_id)}_"
                        )
                        and ins_map["destination_field"] in all_system_names
                    ):
                        ins_map["destination_field"] = mp["destination_field"]
                    else:
                        ins_map["destination_field"] = dis_sys_map[
                            mp["destination_field"]
                        ]

                    ins_map["integration_id"] = integration_id
                    ins_map["source_object_id"] = source_object_id.lower()
                    ins_map["destination_object_id"] = destination_object_id
                    ins_map["is_association"] = mp["is_association"]
                    ins_map["associated_object_id"] = (
                        mp["associated_object_id"] if mp["is_association"] else None
                    )

                    if mp["is_association"]:
                        associated_object = AssociatedObjectsType(
                            source_field=mp["source_field"],
                            object_id=mp["associated_object_id"],
                        )
                        SelfServiceFactory(
                            service="hubspot",
                            client_id=accessor_client_id,
                            access_token_config_id=access_token_config_id,
                        ).create_enrichment_config(
                            object_id=source_object_id,
                            integration_id=integration_id,
                            associated_object=associated_object,
                        )
                        SelfServiceFactory(
                            service="hubspot",
                            client_id=accessor_client_id,
                            access_token_config_id=access_token_config_id,
                        ).create_api_access_associations_config(
                            object_id=source_object_id,
                            integration_id=integration_id,
                            associated_object=associated_object,
                        )

                    elif (
                        service == Services.SALESFORCE.value
                        and mp["source_field"] in sf_datetime_fields
                    ):
                        ins_map["additional_config"] = {
                            "date_format": ["YYYY-MM-DDTHH24:MI:SS.FF3TZHTZM"]
                        }

                    ins_mappings.append(ins_map)

                CustomObjectAccessor(client_id=accessor_client_id).update_record(
                    custom_object_id=destination_object_id,
                    updated_fields={"ordered_columns": ordered_columns},
                )
                TransformationConfigAccessor(
                    client_id=accessor_client_id
                ).bulk_create_and_persist_record(mappings=ins_mappings)

                e2e_sync_run_id = uuid4()
                sync_run_id = uuid4()

                etl_config_validation(
                    client_id=accessor_client_id,
                    integration_id=UUID(integration_id),
                    e2e_sync_run_id=e2e_sync_run_id,
                    sync_run_id=sync_run_id,
                )

                ext_record = ExtractionConfigAccessor(
                    client_id=accessor_client_id
                ).get_record_by_integration_id(integration_id=UUID(integration_id))[0]

                if ext_record.sync_type == UpstreamSyncModes.CHANGES.value:
                    logger.info(
                        "Setting extraction date to historic sync date as transformation config is updated."
                    )
                    UpstreamTimestampsAccessor(
                        client_id=accessor_client_id,
                        integration_id=ext_record.integration_id,
                    ).set_extraction_date_to_historic_date()

                return BulkCreateTransformationConfig(result="Success")
        except Exception as err:
            logger.exception("error", exc_info=err)
            return BulkCreateTransformationConfig(result=f"Error: {str(err)}")
