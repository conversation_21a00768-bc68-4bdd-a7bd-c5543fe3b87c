"""
Mutation config for createTransformationConfig query.
"""

from uuid import UUID, uuid4

import graphene
from django.db import transaction

from commission_engine.accessors.etl_config_accessor import TransformationConfigAccessor
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation


class CreateTransformationConfig(graphene.Mutation):
    """
    GQL mutation to create a single transformation config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        integration_id = graphene.String(required=True)
        source_object_id = graphene.String(required=True)
        destination_object_id = graphene.Int(required=True)
        source_field = graphene.String(required=True)
        destination_field = graphene.String(required=True)
        field_type = graphene.String(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        **kwargs,
    ):
        try:
            client_id = kwargs.get("client_id")
            accessor_client_id = client_id if client_id else info.context.client_id

            TransformationConfigAccessor(
                client_id=accessor_client_id
            ).create_and_persist_record(fields=kwargs)

            e2e_sync_run_id = uuid4()
            sync_run_id = uuid4()

            etl_config_validation(
                client_id=accessor_client_id,
                integration_id=UUID(kwargs.get("integration_id")),
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
            )

            return CreateTransformationConfig(result="Success")
        except Exception as e:
            return CreateTransformationConfig(result=f"Error: {str(e)}")
