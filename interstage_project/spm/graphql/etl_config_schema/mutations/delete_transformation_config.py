"""
Mutation config for deleteTransformationConfig query.
"""

from uuid import UUID

import graphene
from django.db import transaction
from django.utils import timezone

from commission_engine.accessors.etl_config_accessor import TransformationConfigAccessor
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation


class DeleteTransformationConfig(graphene.Mutation):
    """
    GQL mutation to Delete a single transformation config by ID.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        integration_id = graphene.String(required=True)
        source_field = graphene.String(required=True)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        integration_id: str,
        source_field: str,
        **kwargs,
    ):
        try:
            client_id = kwargs.get("client_id")
            accessor_client_id = client_id if client_id else info.context.client_id
            current_time = timezone.now()

            TransformationConfigAccessor(
                client_id=accessor_client_id
            ).invalidate_record(
                integration_id=UUID(integration_id),
                source_field=source_field,
                curr_time=current_time,
            )

            return DeleteTransformationConfig(result="Success")
        except Exception as e:
            return DeleteTransformationConfig(result=f"Error: {str(e)}")
