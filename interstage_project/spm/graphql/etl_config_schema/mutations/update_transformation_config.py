"""
Mutation config for updateTransformationConfig query.
"""

from uuid import UUID, uuid4

import graphene
from django.db import transaction

from commission_engine.accessors.etl_config_accessor import TransformationConfigAccessor
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation


class UpdateTransformationConfig(graphene.Mutation):
    """
    GQL mutation to update a single transformation config by ID.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=False)
        integration_id = graphene.String(required=True)
        destination_object_id = graphene.Int()
        source_field = graphene.String(required=True)
        destination_field = graphene.String()
        field_type = graphene.String()

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(cls, _, info, source_field: str, integration_id: str, **kwargs):
        try:
            client_id = kwargs.get("client_id")
            accessor_client_id = client_id if client_id else info.context.client_id

            TransformationConfigAccessor(client_id=accessor_client_id).update_record(
                integration_id=UUID(integration_id),
                source_field=source_field,
                updated_fields=kwargs,
            )

            e2e_sync_run_id = uuid4()
            sync_run_id = uuid4()

            etl_config_validation(
                client_id=accessor_client_id,
                integration_id=UUID(integration_id),
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
            )

            return UpdateTransformationConfig(result="Success")
        except Exception as e:
            return UpdateTransformationConfig(result=f"Error: {str(e)}")
