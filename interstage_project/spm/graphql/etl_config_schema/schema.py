# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import json
from typing import Any, Dict, List, Optional
from uuid import UUID

import graphene
from graphene_django.types import DjangoObjectType, ObjectType

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ApiAccessConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.accessors.hyperlink_accessor import HyperlinkAccessor
from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
)
from commission_engine.utils.general_data import RbacPermissions
from commission_engine.utils.general_utils import fetch_sql_results
from everstage_ddd.self_service_integration.constants import (
    MiddlewareConnectors,
    SelfServiceConnectors,
)
from interstage_project.auth_utils import permission_required
from spm.graphql.etl_config_schema.mutations import (
    BulkCreateTransformationConfig,
    CreateAccessTokenConfig,
    CreateTransformationConfig,
    DeleteAccessTokenConfig,
    DeleteTransformationConfig,
    UpdateAccessTokenConfig,
    UpdateTransformationConfig,
)


class AccessTokenConfigTypeGraphQL(graphene.ObjectType):
    access_token_config_id = graphene.String(required=False)
    access_type = graphene.String(required=False)  # UN-PWD/PUB-PRI
    service_name = graphene.String(required=False)
    connection_name = graphene.String(required=False)
    connection_type = graphene.String(required=False)  # hris/upstream
    access_request_body = graphene.JSONString(required=False)
    api_access_key = graphene.String(required=False)
    connection_status = graphene.String(required=False)
    created_on = graphene.DateTime(required=False)
    knowledge_begin_date = graphene.DateTime(required=False)
    client_id = graphene.Int(required=False)
    knowledge_end_date = graphene.DateTime(required=False)
    payload_type = graphene.String(required=False)  # PARAMS/BODY
    access_token_url = graphene.String(required=False)
    jwt_data = graphene.JSONString(required=False)
    domain = graphene.String(required=False)
    additional_data = graphene.JSONString(required=False)
    objects_created = graphene.Int(required=True)


class AccessTokenConfigType(DjangoObjectType):
    class Meta:
        model = AccessTokenConfig


class ExtractionConfigType(DjangoObjectType):
    class Meta:
        model = ExtractionConfig


class ApiAccessConfigType(DjangoObjectType):
    class Meta:
        model = ApiAccessConfig


class TransformationConfigType(DjangoObjectType):
    class Meta:
        model = TransformationConfig


class IntegrationType(DjangoObjectType):
    class Meta:
        model = Integration


class ConnectedObjectType(ObjectType):
    integration_id = graphene.String()
    name = graphene.String()
    service_name = graphene.String()
    logo_url = graphene.String()
    destination_object_id = graphene.Int()
    extraction_end_time = graphene.DateTime()
    is_disabled = graphene.Boolean()
    source_object_id = graphene.Int()
    object_id = graphene.String()
    has_hard_delete_sync = graphene.Boolean()
    is_fivetran_sync = graphene.Boolean()


class ETLConfigQuery(object):
    """
    GQL queries for ETL Config.
    """

    # AccessTokenConfig queries
    access_token_configs = graphene.List(
        AccessTokenConfigTypeGraphQL,
        client_id=graphene.Int(required=False),
    )

    # Integration queries
    integration = graphene.Field(
        IntegrationType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )
    extraction_config = graphene.Field(
        ExtractionConfigType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )
    integrations = graphene.List(IntegrationType, client_id=graphene.Int(required=True))
    integration_for_custom_object_id = graphene.Field(
        IntegrationType,
        client_id=graphene.Int(required=False),
        custom_object_id=graphene.Int(required=True),
    )

    # TransformationConfig queries
    transformation_configs_for_integration = graphene.List(
        TransformationConfigType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )
    transformation_configs_for_custom_object_id = graphene.List(
        TransformationConfigType,
        client_id=graphene.Int(required=False),
        custom_object_id=graphene.Int(required=True),
    )

    api_access_config = graphene.List(
        ApiAccessConfigType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )

    hyperlinked_field_for_custom_object = graphene.String(
        custom_object_id=graphene.Int(required=True)
    )

    # Connected objects
    connected_objects = graphene.List(
        ConnectedObjectType,
        client_id=graphene.Int(required=False),
        should_include_disabled_objects=graphene.Boolean(required=False),
        service_name=graphene.String(required=False),
    )

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def resolve_access_token_configs(
        self, info, **kwargs
    ) -> List[AccessTokenConfigTypeGraphQL]:
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id

        query = """
        SELECT 
            t1.client_id, 
            t1.knowledge_begin_date, 
            t1.knowledge_end_date, 
            t1.access_token_config_id, 
            t1.access_type, 
            t1.payload_type, 
            t1.api_access_key, 
            t1.access_token_url, 
            t1.access_request_body::jsonb, 
            t1.jwt_data::jsonb, 
            t1.service_name, 
            t1."domain", 
            t1.connection_name, 
            t1.connection_status, 
            t1.created_on, 
            t1.connection_type, 
            t1.additional_data::jsonb,
            COUNT(t2.destination_object_id) AS objects_created
        FROM 
            access_token_config t1
        LEFT JOIN 
            extraction_config t2
        ON 
            t1.client_id = t2.client_id  and
            t1.access_token_config_id = t2.access_token_config_id and
            t2.knowledge_end_date is null
        where 
            t1.client_id=%(client_id)s and
            t1.knowledge_end_date is null
        GROUP BY 
            t1.client_id, 
            t1.knowledge_begin_date, 
            t1.knowledge_end_date, 
            t1.access_token_config_id, 
            t1.access_type, 
            t1.payload_type, 
            t1.api_access_key, 
            t1.access_token_url, 
            t1.access_request_body, 
            t1.jwt_data, 
            t1.service_name, 
            t1."domain", 
            t1.connection_name, 
            t1.connection_status, 
            t1.created_on, 
            t1.connection_type, 
            t1.additional_data;
        """
        params = {"client_id": accessor_client_id}
        records = fetch_sql_results(query, params)

        result = []
        for record in records:
            request_body = record["access_request_body"]
            if isinstance(request_body, str):
                request_body = json.loads(request_body)
            record_dict = AccessTokenConfigTypeGraphQL(
                access_token_config_id=str(record["access_token_config_id"]),
                access_type=record["access_type"],
                connection_name=record["connection_name"],
                connection_type=record["connection_type"],
                access_request_body=request_body,
                api_access_key=record["api_access_key"],
                connection_status=record["connection_status"],
                created_on=record["created_on"],
                knowledge_begin_date=record["knowledge_begin_date"],
                client_id=record["client_id"],
                knowledge_end_date=record["knowledge_end_date"],
                payload_type=record["payload_type"],
                access_token_url=record["access_token_url"],
                jwt_data=record["jwt_data"],
                service_name=(
                    (request_body or {}).get("db_type")
                    if record["service_name"] == "sql"
                    else record["service_name"]
                ),
                domain=record["domain"],
                additional_data=record["additional_data"],
                objects_created=record["objects_created"],
            )

            result.append(record_dict)

        return result

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def resolve_integration(self, info, integration_id: str, **kwargs) -> Integration:
        client_id = kwargs.get("client_id")

        record = (
            IntegrationAccessor(client_id)
            .get_record_by_integration_id(integration_id=UUID(integration_id))
            .last()
        )

        if not record:
            raise Exception("Record not found")

        return record

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def resolve_extraction_config(
        self, info, integration_id: str, **kwargs
    ) -> ExtractionConfig:
        client_id = kwargs.get("client_id")

        record = (
            ExtractionConfigAccessor(client_id)
            .get_record_by_integration_id(integration_id=UUID(integration_id))
            .last()
        )

        if not record:
            raise Exception("Record not found")

        return record

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def resolve_api_access_config(
        self, info, integration_id: str, **kwargs
    ) -> ApiAccessConfig:
        client_id = kwargs.get("client_id")

        record = list(
            (
                ApiAccessConfigAccessor(client_id).get_record_by_integration_id(
                    integration_id=UUID(integration_id)
                )
            )
        )

        if not record:
            raise Exception("Record not found")

        return record

    @permission_required(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    def resolve_integrations(self, info, **kwargs) -> list[Integration]:
        client_id = kwargs.get("client_id")

        return IntegrationAccessor(client_id).get_all_records()

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_integration_for_custom_object_id(
        self, info, custom_object_id: int, **kwargs
    ) -> Integration:
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id

        extraction_record = (
            ExtractionConfigAccessor(client_id=accessor_client_id)
            .get_records_by_custom_object_id(custom_object_id=custom_object_id)
            .last()
        )

        if not extraction_record:
            raise Exception("Record not found")

        record = (
            IntegrationAccessor(client_id=accessor_client_id)
            .get_record_by_integration_id(
                integration_id=extraction_record.integration_id
            )
            .last()
        )

        if not record:
            raise Exception("Record not found")

        return record

    @permission_required(RbacPermissions.MANAGE_ADMINUI.value)
    def resolve_transformation_configs_for_integration(
        self, info, integration_id: str, **kwargs
    ) -> list[TransformationConfig]:
        client_id = kwargs.get("client_id")

        return list(
            TransformationConfigAccessor(client_id).get_records_by_integration_id(
                integration_id=UUID(integration_id)
            )
        )

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_transformation_configs_for_custom_object_id(
        self, info, custom_object_id: int, **kwargs
    ) -> list[TransformationConfig]:
        client_id = kwargs.get("client_id")
        accessor_client_id = client_id if client_id else info.context.client_id

        return list(
            TransformationConfigAccessor(
                client_id=accessor_client_id
            ).get_records_by_custom_object_id(custom_object_id=custom_object_id)
        )

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_hyperlinked_field_for_custom_object(
        self, info, custom_object_id: int
    ) -> Optional[str]:
        try:
            return HyperlinkAccessor(
                client_id=info.context.client_id
            ).get_hyperlinked_field_for_custom_object(custom_object_id=custom_object_id)
        except AttributeError:
            return None

    @permission_required(RbacPermissions.MANAGE_DATASETTINGS.value)
    def resolve_connected_objects(self, info, **kwargs) -> list[ConnectedObjectType]:
        client_id = kwargs.get("client_id")
        should_include_disabled_objects = kwargs.get("should_include_disabled_objects")
        service_name = kwargs.get("service_name")
        accessor_client_id = client_id if client_id else info.context.client_id
        integration_ids = None
        if service_name is not None:
            integration_data: List[Dict[str, Any]] = IntegrationAccessor(
                client_id=accessor_client_id
            ).get_integration_by_service_name(
                service_name=service_name, projection=["integration_id"]
            )
            if len(integration_data) > 0:
                integration_ids = [
                    integration_id.get("integration_id")
                    for integration_id in integration_data
                ]

        ec_records = ExtractionConfigAccessor(
            client_id=accessor_client_id
        ).get_all_extraction_configs(
            should_include_disabled_objects=should_include_disabled_objects,
            integration_ids=integration_ids,
        )
        connected_objects: list[ConnectedObjectType] = []

        task_names_list = [ec_record.task for ec_record in ec_records]
        last_extracted_time_for_tasks = UpstreamETLStatusReaderAccessor(
            client_id=accessor_client_id
        ).get_all_last_success_sync_for_tasks(tasks=task_names_list)
        data_extraction_map = {}

        for extracted_record in last_extracted_time_for_tasks:
            data_extraction_map[extracted_record["object_id"]] = extracted_record[
                "changes_end_time"
            ]

        fivetran_access_token_config_ids = []
        sql_access_token_records = AccessTokenConfigAccessor(
            accessor_client_id
        ).get_records_by_service_name(SelfServiceConnectors.SQL.value)
        for config in sql_access_token_records:
            if (
                isinstance(config.access_request_body, dict)
                and config.access_request_body.get("middleware_name")
                == MiddlewareConnectors.FIVETRAN.value
            ):
                fivetran_access_token_config_ids.append(config.access_token_config_id)

        for ec_record in ec_records:
            integration_id = ec_record.integration_id
            int_record = (
                IntegrationAccessor(client_id=accessor_client_id)
                .get_record_by_integration_id(integration_id=integration_id)
                .last()
            )

            is_fivetran_sync = (
                True
                if ec_record.access_token_config_id in fivetran_access_token_config_ids
                else False
            )

            has_hard_delete_sync = False
            hard_delete_sync_object = ApiAccessConfigAccessor(
                accessor_client_id
            ).get_obj_by_integration_id_and_source_id(
                integration_id, int_record.source_object_id.lower() + "_hard_delete"
            )
            if hard_delete_sync_object:
                has_hard_delete_sync = True

            connected_objects.append(
                ConnectedObjectType(
                    integration_id=integration_id,
                    name=int_record.name,
                    destination_object_id=ec_record.destination_object_id,
                    extraction_end_time=data_extraction_map[ec_record.task],
                    is_disabled=ec_record.is_disabled,
                    service_name=int_record.service_name,
                    logo_url=int_record.logo_url,
                    source_object_id=ec_record.source_object_id,
                    object_id=ec_record.source_object_id,
                    has_hard_delete_sync=has_hard_delete_sync,
                    is_fivetran_sync=is_fivetran_sync,
                )
            )

        return connected_objects


class ETLConfigMutation(graphene.ObjectType):
    """
    GQL mutations for ETL Config.
    """

    # AccessTokenConfig mutations
    create_access_token_config = CreateAccessTokenConfig.Field()
    update_access_token_config = UpdateAccessTokenConfig.Field()
    delete_access_token_config = DeleteAccessTokenConfig.Field()

    # TransformationConfig mutations
    bulk_create_transformation_config = BulkCreateTransformationConfig.Field()
    create_transformation_config = CreateTransformationConfig.Field()
    update_transformation_config = UpdateTransformationConfig.Field()
    delete_transformation_config = DeleteTransformationConfig.Field()
