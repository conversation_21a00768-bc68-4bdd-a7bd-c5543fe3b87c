from uuid import UUID

import graphene
from django.db import transaction

from commission_engine.services import integration_settings_service
from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import permission_required_mutation


class UpdateUpstreamEtlStatus(graphene.Mutation):
    class Arguments:
        client_id = graphene.Int(required=False)
        integration_id = graphene.String(required=True)
        changes_end_time = graphene.String(required=False)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(
        [
            RbacPermissions.MANAGE_DATASETTINGS.value,
            RbacPermissions.MANAGE_ADMINUI.value,
        ]
    )
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        integration_id: UUID,
        **kwargs,
    ):
        try:
            client_id = kwargs.get("client_id")
            accessor_client_id = client_id if client_id else info.context.client_id
            changes_end_time = (
                kwargs.get("changes_end_time")
                if kwargs.get("changes_end_time")
                else None
            )
            if changes_end_time is not None:
                integration_settings_service.change_data_extraction_time_for_integration_id(
                    accessor_client_id, integration_id, changes_end_time
                )
            return UpdateUpstreamEtlStatus(result="Success")
        except Exception as e:
            return UpdateUpstreamEtlStatus(result=f"Error: {str(e)}")
