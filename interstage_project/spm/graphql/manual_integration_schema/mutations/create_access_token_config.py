"""
Mutation config for manualCreateAccessTokenConfig query.
"""

import graphene
from django.db import transaction

from everstage_admin_backend.manual_integrations.manual_access_config import (
    ManualAccessConfig,
)
from interstage_project.auth_utils import permission_required_mutation


class ManualCreateAccessTokenConfig(graphene.Mutation):
    """
    GQL mutation to create a single AccessTokenConfig config.
    """

    class Arguments:
        """
        Mutation arguments.
        """

        client_id = graphene.Int(required=True)
        access_type = graphene.String(required=True)
        payload_type = graphene.String(required=True)
        api_access_key = graphene.String(required=False)
        access_token_url = graphene.String(required=False)
        access_request_body = graphene.String(required=False)
        domain = graphene.String(required=False)
        service_name = graphene.String(required=True)
        connection_name = graphene.String(required=True)
        validation_url = graphene.String(required=False)
        additional_data = graphene.String(required=False)

    result = graphene.String()

    @classmethod
    @permission_required_mutation(["edit:settings", "write:everstageadmin"])
    @transaction.atomic
    def mutate(
        cls,
        _,
        info,
        client_id: int,
        access_type: str,
        payload_type: str,
        access_token_url: str,
        service_name: str,
        domain: str,
        connection_name: str,
        **kwargs,
    ):
        try:
            with transaction.atomic():
                manual_access_config = ManualAccessConfig()
                result = manual_access_config.create(
                    client_id=client_id,
                    access_type=access_type,
                    payload_type=payload_type,
                    access_token_url=access_token_url,
                    domain=domain,
                    service_name=service_name,
                    connection_name=connection_name,
                    **kwargs,
                )

            return ManualCreateAccessTokenConfig(result=result)
        except Exception as e:
            return ManualCreateAccessTokenConfig(result=f"Error: {str(e)}")
