# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test
from inspect import getmembers, isabstract, isclass

import graphene

import commission_engine.third_party_connections as third_party_connections
from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.etl_config_accessor import ExtractionConfigAccessor
from interstage_project.auth_utils import permission_required
from spm.graphql.custom_object_schema.schema import CustomObjectType
from spm.graphql.manual_integration_schema.mutations import (
    CreateManualIntegration,
    ManualCreateAccessTokenConfig,
    UpdateManualIntegration,
)


class JSONDict(graphene.ObjectType):
    """
    Custom object type representing a dictionary.
    """

    label = graphene.String(required=True)
    value = graphene.String(required=True)

    def resolve_label(self, info):
        return self["label"]

    def resolve_value(self, info):
        return self["value"]


class ManualIntegrationConfigQuery(object):
    """
    GQL queries for Manual Integration Config.
    """

    unmapped_custom_object_id = graphene.List(
        CustomObjectType, client_id=graphene.Int(required=True)
    )

    mapped_and_unmapped_custom_object_id = graphene.List(
        CustomObjectType,
        client_id=graphene.Int(required=True),
        integration_id=graphene.String(required=True),
    )

    supported_service_names = graphene.List(
        JSONDict, client_id=graphene.Int(required=False)
    )

    @permission_required(["edit:settings", "write:everstageadmin"])
    def resolve_unmapped_custom_object_id(self, info, client_id):
        destination_object_ids = (
            ExtractionConfigAccessor(client_id=client_id)
            .client_kd_aware()
            .values_list("destination_object_id", flat=True)
        )

        unmapped_custom_objects = (
            CustomObjectAccessor(client_id=client_id)
            .client_kd_aware()
            .exclude(
                custom_object_id__in=[
                    int(x) if x.isdigit() else -1 for x in destination_object_ids
                ]
            )
        )

        return list(unmapped_custom_objects)

    @permission_required(["edit:settings", "write:everstageadmin"])
    def resolve_mapped_and_unmapped_custom_object_id(
        self, info, client_id, integration_id
    ):
        destination_object_ids = (
            ExtractionConfigAccessor(client_id=client_id)
            .client_kd_aware()
            .values_list("destination_object_id", flat=True)
        )

        unmapped_custom_objects = (
            CustomObjectAccessor(client_id=client_id)
            .client_kd_aware()
            .exclude(
                custom_object_id__in=[
                    int(x) if x.isdigit() else -1 for x in destination_object_ids
                ]
            )
        )

        record = ExtractionConfigAccessor(
            client_id=client_id
        ).get_record_by_integration_id(integration_id)
        selected_object_id = record[0].destination_object_id
        co_record = CustomObjectAccessor(client_id=client_id).get_object_by_id(
            int(selected_object_id)
        )

        return list(unmapped_custom_objects) + list(co_record)

    @permission_required(["edit:settings", "write:everstageadmin"])
    def resolve_supported_service_names(self, info, **kwargs):
        """
        getmembers return the members/class used in a module as (name, type)
        here third_party_connections is a module which holds all the ConcreteClasses which we imported in the init.py file
        and we are fetching only the concrete classes using the lambda predicate and creating a map
        as {'class_name': class}
        for ex: {'Hubspot': <class> Hubspot}
        """
        imported_modules = getmembers(
            third_party_connections, lambda m: isclass(m) and not isabstract(m)
        )

        result = [
            {"label": module_name[0].lower(), "value": module_name[0].lower()}
            for module_name in imported_modules
        ]

        result.append({"label": "csv", "value": "csv"})
        result.remove({"label": "thirdpartyapi", "value": "thirdpartyapi"})

        return sorted(result, key=lambda x: x["label"])


class ManualIntegrationMutation(graphene.ObjectType):
    """
    GQL mutations for Manual Integration Config.
    """

    # Manual-Integration
    manual_create_access_token_config = ManualCreateAccessTokenConfig.Field()
    create_manual_integration = CreateManualIntegration.Field()
    update_manual_integration = UpdateManualIntegration.Field()
