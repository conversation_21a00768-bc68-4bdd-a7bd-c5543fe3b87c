# pylint: disable=unsubscriptable-object, no-member, unsupported-membership-test

import datetime
from decimal import Decimal
from typing import Optional

import graphene
from django.http import JsonResponse
from django.utils.timezone import make_aware
from graphene_django.types import DjangoObjectType, ObjectType
from graphql import GraphQL<PERSON>rror

from commission_engine.utils.general_data import (
    GRAPHQL__PERMISSION_DENIED,
    RBAC,
    RBACComponent,
    RbacPermissions,
)
from crystal.utils.crystal_utils import PRIMARY_QUOTA
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    permission_required,
)
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import HierarchyAccessor
from spm.accessors.quota_acessors import Employee<PERSON>uotaAccessor, QuotaAccessor
from spm.graphql.employee_config_schema.schema import BaseEmployeeType
from spm.models.quota_models import Employee<PERSON>uota, Quota
from spm.services.localization_services import get_localized_message_service
from spm.services.quota_services import (
    get_max_locked_date_for_payee,
    get_paginated_payees_with_quota_based_on_data_permission,
    get_payee_quotas_paginated,
    get_payees_count_with_quota_based_on_data_permission,
)
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails

logger = LogWithContext()


class QuotaType(DjangoObjectType):
    display_name = graphene.String()

    class Meta:
        model = Quota

    def resolve_display_name(self, info):
        return info.context.quota_category_display_name_map_loader.load(
            self.quota_category_name
        )


# class PayoutQuotaType(ObjectType):
#     january = graphene.Field(lambda: QuotaLine)
#     february = graphene.Field(lambda: QuotaLine)
#
#     def resolve_january(self, info):
#         return self['Jan']
#
#     def resolve_february(self, info):
#         return self['Feb']


class PayoutQuotaType(ObjectType):
    month = graphene.Field(graphene.String)
    quota_line = graphene.Field(lambda: QuotaLine)

    def resolve_month(self, info):
        return self[0]

    def resolve_quota_line(self, info):
        return self[1]


class QuotaLine(ObjectType):
    ramp = graphene.Field(graphene.Float)
    quota = graphene.Field(graphene.Float)
    ramped_quota = graphene.Field(graphene.Float)

    def resolve_ramp(self, info):
        return self["ramp"]

    def resolve_quota(self, info):
        return self["quota"]

    def resolve_ramped_quota(self, info):
        return self["ramped_quota"]


class EmployeeQuotaType(DjangoObjectType):
    schedule_quota = graphene.List(QuotaLine)
    payout_quota = graphene.List(PayoutQuotaType)
    employee = graphene.Field(BaseEmployeeType)
    display_name = graphene.String()
    quota_currency_symbol = graphene.String()

    def resolve_schedule(self, info):
        return self.schedule_quota

    def resolve_payout_quota(self, info):
        return self.payout_quota.items()

    def resolve_employee(self, info):
        return (
            info.context.employee_loader.load(self.employee_email_id)
            if self.employee_email_id is not None
            else None
        )

    def resolve_display_name(self, info):
        return info.context.quota_category_display_name_map_loader.load(
            self.quota_category_name
        )

    def resolve_quota_currency_symbol(self, info):
        return info.context.quota_currency_symbol_loader.load(self.quota_category_name)

    class Meta:
        model = EmployeeQuota


class EmployeeQuotaReportees(graphene.ObjectType):
    quota_category_name = graphene.String()
    quota_year = graphene.String()
    result = graphene.JSONString()


class EmployeeQuotaPaginatedType(graphene.ObjectType):
    full_name = graphene.String()
    employee_email_id = graphene.String()


class EmployeQuotaCategoryNameType(graphene.ObjectType):
    quota_category_name = graphene.String()
    fiscal_year = graphene.String()
    employee_email_id = graphene.String()
    full_name = graphene.String()
    first_name = graphene.String()
    last_name = graphene.String()
    profile_picture = graphene.String()


class QuotaCategoryMapType(graphene.ObjectType):
    label = graphene.String()
    value = graphene.String()

    def resolve_label(self, info):
        return self["label"]

    def resolve_value(self, info):
        return self["value"]


class QuotaCategorySettingsType(graphene.ObjectType):
    quota_category_name = graphene.String()
    display_name = graphene.String()
    quota_currency_type = graphene.String()

    def resolve_quota_category_name(self, info):
        return self.get("quotaCategoryName")

    def resolve_display_name(self, info):
        return self.get("displayName")

    def resolve_quota_currency_type(self, info):
        return self.get("quotaCurrencyType")


class QuotaConfigQuery(object):
    all_quota_categories = graphene.List(QuotaType)
    all_quota_categories_list = graphene.List(QuotaCategoryMapType)
    all_quota_categories_with_primary = graphene.List(QuotaCategorySettingsType)
    all_quota_categories_for_client = graphene.List(
        QuotaType, client_id=graphene.String()
    )
    all_quota_categories_for_email = graphene.List(
        QuotaCategoryMapType, email_id=graphene.String()
    )
    all_employee_quota = graphene.List(EmployeeQuotaType)
    employees_quota = graphene.List(
        EmployeeQuotaType, email_ids=graphene.List(graphene.String)
    )
    employees_quota_without_effective = graphene.JSONString(
        email_ids=graphene.List(graphene.String)
    )
    reportees_quota = graphene.List(EmployeeQuotaReportees, email_id=graphene.String())
    latest_locked_date_for_payee = graphene.Field(
        graphene.String, email_id=graphene.String(), date=graphene.String()
    )
    employee_quota = graphene.List(
        EmployeeQuotaType, email_id=graphene.String(), year=graphene.Int()
    )
    employee_quota_by_email_list = graphene.List(
        EmployeeQuotaType,
        email_ids=graphene.List(graphene.String),
        year=graphene.Int(),
        quota_category=graphene.String(),
        is_team=graphene.Boolean(),
    )

    employee_quota_paginated = graphene.List(
        EmployeeQuotaPaginatedType,
        payout_frequency=graphene.String(),
        limit_value=graphene.Int(),
        full_name=graphene.String(),
        email=graphene.String(),
        search_term=graphene.String(),
    )

    employees_by_quota_paginated = graphene.List(
        EmployeQuotaCategoryNameType,
        quota_category_name=graphene.String(),
        fiscal_year=graphene.String(),
        search_term=graphene.String(),
        offset_value=graphene.Int(),
        limit_value=graphene.Int(),
    )
    count_employees_quota_category = graphene.Field(
        graphene.Int,
        quota_category_name=graphene.String(),
        fiscal_year=graphene.String(),
    )

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.MANAGE_ADMINUI.value]
    )
    def resolve_all_quota_categories(self, info, **kwargs):
        logger.info("All quota categories query is getting called")
        return QuotaAccessor(info.context.client_id).get_all_quota_categories()

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.MANAGE_ADMINUI.value]
    )
    def resolve_all_quota_categories_list(self, info, **kwargs):
        logger.info("All quota categories query is getting called")

        records = QuotaAccessor(info.context.client_id).get_all_quota_categories()
        cat_list = []
        for record in records:
            cat_list.append(
                {"label": record.display_name, "value": record.quota_category_name}
            )
        localized_primary_quota_name = get_localized_message_service(
            PRIMARY_QUOTA, info.context.client_id
        )
        cat_list.append(
            {
                "label": localized_primary_quota_name,
                "value": localized_primary_quota_name,
            }
        )
        return cat_list

    @permission_required([RbacPermissions.MANAGE_QUOTA_SETTINGS.value])
    def resolve_all_quota_categories_with_primary(self, info, **kwargs):
        logger.info("All quota categories query is getting called")

        records = QuotaAccessor(info.context.client_id).get_all_quota_categories()
        cat_list = []
        for record in records:
            cat_list.append(
                {
                    "displayName": record.display_name,
                    "quotaCategoryName": record.quota_category_name,
                    "quotaCurrencyType": record.quota_currency_type,
                }
            )
        localized_primary_quota_name = get_localized_message_service(
            PRIMARY_QUOTA, info.context.client_id
        )
        cat_list.append(
            {
                "displayName": localized_primary_quota_name,
                "quotaCategoryName": "Primary",
                "quotaCurrencyType": None,
            }
        )
        return cat_list

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.MANAGE_ADMINUI.value]
    )
    def resolve_all_quota_categories_for_email(self, info, **kwargs):
        logger.info("All quota categories for email called")
        email_id = kwargs.get("email_id")
        quota_categories_for_payee = EmployeeQuotaAccessor(
            info.context.client_id
        ).get_employee_quota(email_id=email_id)
        if not quota_categories_for_payee:
            return []
        quota_categories_names = [
            quota.quota_category_name for quota in quota_categories_for_payee
        ]
        quota_category_list = QuotaAccessor(
            info.context.client_id
        ).get_quota_category_display_name_map(
            quota_category_name_list=quota_categories_names
        )
        quota_category_dict = [
            {"label": item["display_name"], "value": item["quota_category_name"]}
            for item in quota_category_list
        ]
        return quota_category_dict

    @permission_required(
        [RbacPermissions.VIEW_QUOTAS.value, RbacPermissions.MANAGE_ADMINUI.value]
    )
    def resolve_all_quota_categories_for_client(self, info, **kwargs):
        logger.info("All quota categories for client query is getting called")
        client_id = kwargs.get("client_id")
        info.context.client_id = client_id
        return QuotaAccessor(client_id).get_all_quota_categories()

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_all_employee_quota(self, info, **kwargs):
        logger.info("All employee quota query is getting called")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        return EmployeeQuotaAccessor(info.context.client_id).get_all_employee_quota()

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_employee_quota_by_email_list(self, info, **kwargs):
        email_ids = kwargs.get("email_ids")
        logged_email = info.context.user.username
        year = kwargs.get("year")
        quota_category = kwargs.get("quota_category")
        is_team = kwargs.get("is_team", False)
        is_authorized = authorize_for_email_lookup(
            info.context.client_id,
            logged_email,
            email_ids,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            return EmployeeQuotaAccessor(
                info.context.client_id
            ).get_latest_team_members_quota(
                email_list=email_ids,
                quota_cat=quota_category,
                year=year,
                is_team=is_team,
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("EMPLOYEES QUOTA - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_employee_quota_paginated(self, info, **kwargs):
        logger.info("All employee quota filtered by payout frequency and paginated")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        payout_frequency = kwargs.get("payout_frequency")
        limit = kwargs.get("limit_value")
        full_name = kwargs.get("full_name")
        email = kwargs.get("email")
        search_term = kwargs.get("search_term")
        if not payout_frequency:
            return []
        logged_email = info.context.user.username
        client_id = info.context.client_id
        payee_emails = None
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.QUOTAS_DRAWS.value
        )
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission
            )
        return get_payee_quotas_paginated(
            info.context.client_id,
            payout_frequency,
            limit,
            search_term,
            full_name,
            email,
            payee_emails,
        )

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_employee_quota(self, info, **kwargs):
        logger.info("Employee quota query is getting called")
        email_id = kwargs.get("email_id")
        year = kwargs.get("year")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id,
            login_user_id,
            [email_id],
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            if year:
                employee_quota = list(
                    EmployeeQuotaAccessor(
                        info.context.client_id
                    ).get_employee_quota_by_year(email_id, year, orderby_esd=True)
                )
            else:
                employee_quota = list(
                    EmployeeQuotaAccessor(info.context.client_id).get_employee_quota(
                        email_id, orderby_esd=True
                    )
                )
            return employee_quota
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("EMPLOYEE QUOTA - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_employees_quota(self, info, **kwargs):
        logger.info("Employees quota query is getting called")
        email_ids = kwargs.get("email_ids")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            info.context.client_id,
            login_user_id,
            email_ids,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            return EmployeeQuotaAccessor(
                info.context.client_id
            ).get_employees_quota_by_effective_dated(
                email_ids, info.context.effective_date
            )
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("EMPLOYEES QUOTA - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_employees_quota_without_effective(self, info, **kwargs):
        """
        Used in duplicate quota from payee drop down.

        Returns:
            List: A list of non-effective quotas for authorized users.
        """
        logger.info("Employees quota query is getting called")
        client_id = info.context.client_id
        email_ids = kwargs.get("email_ids")
        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            client_id,
            login_user_id,
            email_ids,
            RBACComponent.QUOTAS_DRAWS.value,
        )
        if is_authorized:
            non_effective_quotas = []

            # Retrieve all employee quotas without effective date
            all_employees_quota = EmployeeQuotaAccessor(
                client_id
            ).get_employees_quota_without_effective_dated(email_ids)

            # Retrieve quota category display names
            all_quota_category_display_name = QuotaAccessor(
                client_id
            ).get_all_quota_category_display_name_map()
            quota_category_display_name_map = {
                quota_record["quota_category_name"]: quota_record["display_name"]
                for quota_record in all_quota_category_display_name
            }

            for employee_quota in all_employees_quota:
                # If the count is equal to one, it means it's not an effective dated quota
                if employee_quota["count"] == 1:
                    # Assign display name based on quota category name
                    if employee_quota["quota_category_name"] == "Primary":
                        employee_quota["display_name"] = get_localized_message_service(
                            PRIMARY_QUOTA, client_id
                        )
                    else:
                        employee_quota["display_name"] = (
                            quota_category_display_name_map[
                                employee_quota["quota_category_name"]
                            ]
                            if quota_category_display_name_map[
                                employee_quota["quota_category_name"]
                            ]
                            else employee_quota["quota_category_name"]
                        )
                    non_effective_quotas.append(employee_quota)
            return non_effective_quotas
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("EMPLOYEES QUOTA - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.VIEW_QUOTAS.value)
    def resolve_reportees_quota(self, info, **kwargs):
        """
        Returns a list of dictionaries with the structure:
        [
            {
                'quota_category_name': "qc_everstage",
                'quota_year': "2023",
                'result': {"Annual": [{'quota': 4, 'ramped_quota': '4.00'}]}
            },
            ...
        ]
        """
        logger.info("Reportees quota query is getting called")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        email_id = kwargs.get("email_id")

        info.context.is_effective_date_based = True
        info.context.effective_date = make_aware(datetime.datetime.now())
        reportees = HierarchyAccessor(client_id).get_reportees(
            info.context.effective_date, email_id
        )
        reportees_email_ids = [rep.employee_email_id for rep in reportees]

        # Check for data permission of logged in user: quotas draws
        # NOTE: When the logged in user and the email id are same, the logged in user can
        # access the quota values of their reportees irrespective of their data permission.
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.QUOTAS_DRAWS.value
        )
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            valid_payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            if email_id not in valid_payee_emails:
                logger.info(
                    f"User: {login_user_id} don't have permission to view quotas of {email_id}"
                )
                raise GraphQLError(GRAPHQL__PERMISSION_DENIED)

            # Filtering out only valid email ids according to data permission
            reportees_email_ids = list(
                set(reportees_email_ids).intersection(valid_payee_emails)
            )

        employee_quota = EmployeeQuotaAccessor(
            info.context.client_id
        ).get_employees_quota_by_effective_dated(
            reportees_email_ids, info.context.effective_date, include_team=False
        )

        # Initialize a dictionary to store summed quotas
        summed_quotas = {}

        # Iterate through employee quotas to sum them
        for record in employee_quota:
            quota_category_name = record.quota_category_name
            quota_year = record.quota_year
            schedule_type = record.quota_schedule_type
            schedule_quotas = record.schedule_quota

            # Create a unique key based on quota category, year, and schedule type
            key = (quota_category_name, quota_year, schedule_type)

            if key not in summed_quotas:
                # Initialize summed_quotas for the unique key
                summed_quotas[key] = [
                    {"quota": 0, "ramped_quota": Decimal("0.00")}
                ] * len(schedule_quotas)

            new_records = []

            for summed_quota, record_b in zip(summed_quotas[key], schedule_quotas):
                combined_quota = summed_quota.get("quota", 0) + record_b.get("quota", 0)
                combined_ramped_quota = Decimal(
                    summed_quota.get("ramped_quota", "0.00")
                ) + Decimal(record_b.get("ramped_quota", "0.00"))

                new_record = {
                    "quota": combined_quota,
                    "ramped_quota": f"{combined_ramped_quota:.2f}",
                }
                new_records.append(new_record)

            summed_quotas[key] = new_records

        # Initialize a dictionary to store the result
        summed_reportees_quota = {}

        for key, value in summed_quotas.items():
            quota_category_name, quota_year, schedule_type = key
            if (quota_category_name, quota_year) not in summed_reportees_quota:
                summed_reportees_quota[(quota_category_name, quota_year)] = {
                    "quota_category_name": quota_category_name,
                    "quota_year": quota_year,
                    "result": {schedule_type: value},
                }
            else:
                if (
                    schedule_type
                    not in summed_reportees_quota[(quota_category_name, quota_year)][
                        "result"
                    ]
                ):
                    summed_reportees_quota[(quota_category_name, quota_year)][
                        "result"
                    ].update({schedule_type: value})
        return list(summed_reportees_quota.values())

    @permission_required(RbacPermissions.VIEW_PAYOUTS.value)
    def resolve_latest_locked_date_for_payee(self, info, **kwargs):
        logger.info("Latest locked date for payee query is getting called")
        email_id = kwargs.get("email_id")
        date = kwargs.get("date")
        client_id = info.context.client_id
        login_user_id = info.context.user.username
        is_authorized = authorize_for_email_lookup(
            client_id, login_user_id, [email_id], RBACComponent.QUOTAS_DRAWS.value
        )
        if is_authorized:
            return get_max_locked_date_for_payee(client_id, email_id, date)
        else:
            response = JsonResponse(
                {"message": "You don't have permission to access this data"}
            )
            response.status_code = 403
            logger.error("LATEST LOCKED DATE FOR PAYEE - PERMISSION REQUIRED")
            return response

    @permission_required(RbacPermissions.MANAGE_QUOTA_SETTINGS.value)
    def resolve_count_employees_quota_category(self, info, **kwargs):
        client_id: int = info.context.client_id
        logged_email: str = info.context.user.username

        quota_category_name: str = kwargs.get("quota_category_name", None)
        fiscal_year: str = kwargs.get("fiscal_year", None)

        return get_payees_count_with_quota_based_on_data_permission(
            client_id=client_id,
            logged_email=logged_email,
            quota_category_name=quota_category_name,
            fiscal_year=fiscal_year,
        )

    @permission_required(RbacPermissions.MANAGE_QUOTA_SETTINGS.value)
    def resolve_employees_by_quota_paginated(self, info, **kwargs):
        client_id: int = info.context.client_id
        logged_email: str = info.context.user.username

        offset: int = kwargs.get("offset_value", 0)
        limit: int = kwargs.get("limit_value", 20)
        search_term: Optional[str] = kwargs.get("search_term", None)

        quota_category_name: str = kwargs.get("quota_category_name", None)
        fiscal_year: str = kwargs.get("fiscal_year", None)

        return get_paginated_payees_with_quota_based_on_data_permission(
            client_id=client_id,
            logged_email=logged_email,
            quota_category_name=quota_category_name,
            fiscal_year=fiscal_year,
            offset=offset,
            limit=limit,
            search_term=search_term,
        )
