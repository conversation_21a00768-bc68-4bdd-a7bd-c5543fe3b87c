import uuid

from django.db.models import DO_NOTHING, Index, Model, Q, UniqueConstraint

from commission_engine.models.client_models import Client
from interstage_project.db.models import (
    EsBigAutoField,
    EsCharField,
    EsDateTimeField,
    EsEmailField,
    EsForeignKey,
    EsUUIDField,
)
from spm.models.base_models.base_plan_models import (
    BaseCommissionPlan,
    BasePlanCriteria,
    BasePlanDocs,
    BasePlanPayee,
    BaseTempPlanDocs,
)
from spm.models.utils.model_utils import (  # pylint: disable=W0611
    path_within_bucket,
    temp_plan_doc_file_path,
)


class CommissionPlan(BaseCommissionPlan):
    class Meta(BaseCommissionPlan.Meta):
        db_table = "commission_plan"
        indexes = BaseCommissionPlan.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                ],
                name="cp_ked_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "is_draft",
                    "plan_type",
                ],
                name="cp_ked_pt_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_start_date",
                    "plan_end_date",
                ],
                name="cp_ked_psd_ped_idx",
            ),
        ]


class PlanCriteria(BasePlanCriteria):
    class Meta(BasePlanCriteria.Meta):
        db_table = "commission_plan_criteria"
        constraints = [
            UniqueConstraint(
                fields=["client_id", "criteria_id"],
                name="unique_criteria",
                condition=Q(is_deleted=False) & Q(knowledge_end_date__isnull=True),
            ),
        ]
        indexes = BasePlanCriteria.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                ],
                name="pc_ked_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "criteria_id",
                ],
                name="pc_ked_ci_idx",
            ),
        ]


class PlanPayee(BasePlanPayee):
    class Meta(BasePlanPayee.Meta):
        db_table = "plan_payee"
        indexes = BasePlanPayee.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                ],
                name="pp_ked_pi_eei_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "employee_email_id",
                ],
                name="pp_ked_ee_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                name="pp_ked_pi_esd_eed_idx",
            ),
        ]


class PlanDocs(BasePlanDocs):
    class Meta(BasePlanDocs.Meta):
        db_table = "plan_supporting_docs"
        indexes = BasePlanDocs.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                    "doc",
                ],
                name="pd_ked_pi_eei_idx",
            )
        ]


class TempPlanDocs(BaseTempPlanDocs):
    class Meta(BaseTempPlanDocs.Meta):
        db_table = "temp_plan_supporting_docs"
        indexes = BaseTempPlanDocs.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                    "doc",
                ],
                name="tpd_ked_pi_eei_idx",
            )
        ]


class PlanModificationChanges(Model):
    client = EsForeignKey(
        Client, on_delete=DO_NOTHING, db_index=False, is_sensitive=False
    )
    temporal_id = EsBigAutoField(primary_key=True, is_sensitive=False)
    plan_id = EsUUIDField(default=uuid.uuid4, null=False, is_sensitive=False)
    period_start_date = EsDateTimeField(null=False, is_sensitive=False)
    period_end_date = EsDateTimeField(null=False, is_sensitive=False)
    change_type = EsCharField(max_length=100, null=False, is_sensitive=False)
    payee_email_id = EsEmailField(null=False, is_sensitive=False)
    created_at = EsDateTimeField(auto_now_add=True, is_sensitive=False)

    class Meta:
        db_table = "plan_modification_changes"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-created_at",
                    "period_end_date",
                    "period_start_date",
                ],
                name="ci_cat_ped_psd_pln_modif_idx",
            )
        ]


class CommissionChanges(Model):
    client = EsForeignKey(
        Client, on_delete=DO_NOTHING, db_index=False, is_sensitive=False
    )
    temporal_id = EsBigAutoField(primary_key=True, is_sensitive=False)
    period_start_date = EsDateTimeField(null=False, is_sensitive=False)
    period_end_date = EsDateTimeField(null=False, is_sensitive=False)
    payee_email_id = EsEmailField(null=False, is_sensitive=False)
    created_at = EsDateTimeField(auto_now_add=True, is_sensitive=False)
    change_type = EsCharField(max_length=100, null=False, is_sensitive=False)

    class Meta:
        db_table = "commission_changes"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-created_at",
                    "period_end_date",
                    "period_start_date",
                ],
                name="ci_cat_ped_psd_comm_chg_idx",
            )
        ]


class SettlementChanges(Model):
    client = EsForeignKey(
        Client, on_delete=DO_NOTHING, db_index=False, is_sensitive=False
    )
    temporal_id = EsBigAutoField(primary_key=True, is_sensitive=False)
    period_start_date = EsDateTimeField(null=False, is_sensitive=False)
    period_end_date = EsDateTimeField(null=False, is_sensitive=False)
    payee_email_id = EsEmailField(null=False, is_sensitive=False)
    created_at = EsDateTimeField(auto_now_add=True, is_sensitive=False)
    change_type = EsCharField(max_length=100, null=False, is_sensitive=False)

    class Meta:
        db_table = "settlement_changes"
        indexes = [
            Index(
                fields=[
                    "client_id",
                    "-created_at",
                ],
                name="sc_ci_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "-created_at",
                    "period_end_date",
                    "period_start_date",
                ],
                name="sc_ci_cat_ped_psd_idx",
            ),
        ]
