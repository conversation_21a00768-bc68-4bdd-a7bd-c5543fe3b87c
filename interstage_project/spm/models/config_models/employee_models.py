from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db.models import Index, Q, UniqueConstraint

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import (
    EsBooleanField,
    EsCharField,
    EsDateTimeField,
    EsDecimalField,
    EsEmailField,
    EsJSONField,
    EsURLField,
)
from spm.models.base_models.base_plan_models import BasePlanDetails


class Employee(MultiTenantTemporal):
    employee_email_id = EsEmailField(null=False, is_sensitive=False)
    first_name = EsCharField(max_length=100, null=False, is_sensitive=False)
    last_name = <PERSON>sCharField(max_length=100, null=False, is_sensitive=False)
    user_role = EsJSONField(null=False, is_sensitive=False)
    user_source = EsCharField(max_length=100, null=True, blank=True, is_sensitive=False)
    time_zone = EsCharField(max_length=100, null=True, blank=True, is_sensitive=False)
    email_alias = EsCharField(max_length=254, null=True, blank=True, is_sensitive=False)
    created_date = EsDateTimeField(null=False, is_sensitive=False)
    created_by = EsEmailField(null=True, blank=True, is_sensitive=False)
    profile_picture = EsURLField(null=True)
    login_mode = EsCharField(max_length=254, null=True, blank=True)
    send_notification = EsBooleanField(default=False, is_sensitive=False)
    employee_config = EsJSONField(encoder=DjangoJSONEncoder, null=True, blank=True)
    exit_date = EsDateTimeField(null=True, blank=True, is_sensitive=False)
    deactivation_date = EsDateTimeField(null=True, blank=True, is_sensitive=False)
    last_commission_date = EsDateTimeField(null=True, blank=True, is_sensitive=False)
    status = EsCharField(max_length=100, null=True, blank=True, is_sensitive=False)
    is_internal_support_user = EsBooleanField(default=False, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "employee"
        constraints = [
            UniqueConstraint(
                fields=[
                    "client_id",
                    "employee_email_id",
                ],
                name="unique_latest_employee",
                condition=(Q(is_deleted=False) & Q(knowledge_end_date__isnull=True)),
            ),
        ]
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "employee_email_id",
                ],
                name="emp_ked_eei_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "user_role",
                ],
                name="emp_ked_ur_idx",
            ),
        ]


class EmployeePayroll(MultiTenantTemporal):
    employee_email_id = EsEmailField(null=False, is_sensitive=False)
    employee_id = EsCharField(max_length=254, null=True, blank=True, is_sensitive=False)
    joining_date = EsDateTimeField(null=False, is_sensitive=False)
    exit_date = EsDateTimeField(null=True, blank=True, is_sensitive=False)
    designation = EsCharField(max_length=100, null=True, blank=True, is_sensitive=False)
    level = EsCharField(
        max_length=100, null=True, blank=True, default=None, is_sensitive=False
    )
    employee_status = EsCharField(
        max_length=100, null=True, default="Live", is_sensitive=False
    )
    employment_country = EsCharField(max_length=100, null=False, is_sensitive=False)
    fixed_pay = EsDecimalField(null=True, decimal_places=2, max_digits=20, blank=True)
    variable_pay = EsDecimalField(
        null=False, decimal_places=2, max_digits=20, default=0
    )
    pay_currency = EsCharField(max_length=50, null=False, is_sensitive=False)
    payout_frequency = EsCharField(max_length=100, null=False, is_sensitive=False)
    effective_start_date = EsDateTimeField(null=False, is_sensitive=False)
    effective_end_date = EsDateTimeField(null=True, is_sensitive=False)
    payee_role = EsCharField(max_length=100, null=False, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "employee_payroll_details"
        constraints = [
            UniqueConstraint(
                fields=[
                    "client_id",
                    "employee_email_id",
                ],
                name="unique_latest_payroll",
                condition=(
                    Q(is_deleted=False)
                    & Q(knowledge_end_date__isnull=True)
                    & Q(effective_end_date__isnull=True)
                ),
            ),
            UniqueConstraint(
                fields=[
                    "client_id",
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                name="unique_payroll_dates",
                condition=(Q(is_deleted=False) & Q(knowledge_end_date__isnull=True)),
            ),
        ]
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "effective_start_date",
                    "effective_end_date",
                    "employee_email_id",
                ],
                name="ep_ked_esd_eed_eei_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "employee_email_id",
                    "effective_end_date",
                ],
                name="ep_ked_eei_eed_idx",
            ),
        ]


class Hierarchy(MultiTenantTemporal):
    employee_email_id = EsEmailField(null=False, is_sensitive=False)
    reporting_manager_email_id = EsEmailField(null=False, is_sensitive=False)
    effective_start_date = EsDateTimeField(null=False, is_sensitive=False)
    effective_end_date = EsDateTimeField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "hierarchy"
        constraints = [
            UniqueConstraint(
                fields=[
                    "client_id",
                    "employee_email_id",
                ],
                name="unique_latest_hierarchy",
                condition=(
                    Q(is_deleted=False)
                    & Q(knowledge_end_date__isnull=True)
                    & Q(effective_end_date__isnull=True)
                ),
            ),
            UniqueConstraint(
                fields=[
                    "client_id",
                    "employee_email_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                name="unique_hierarchy_dates",
                condition=(Q(is_deleted=False) & Q(knowledge_end_date__isnull=True)),
            ),
        ]
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "employee_email_id",
                    "effective_end_date",
                ],
                name="hie_ked_eei_eed_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "effective_start_date",
                    "effective_end_date",
                    "employee_email_id",
                ],
                name="hie_ked_esd_eed_eei_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "effective_end_date",
                    "reporting_manager_email_id",
                ],
                name="hie_ked_eed_rme_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "effective_start_date",
                    "effective_end_date",
                    "reporting_manager_email_id",
                ],
                name="hie_ked_esd_eed_rme_idx",
            ),
        ]


class PlanDetails(BasePlanDetails):
    class Meta(BasePlanDetails.Meta):
        db_table = "plan_details"
        indexes = BasePlanDetails.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_type",
                    "employee_email_id",
                    "plan_id",
                ],
                name="pd_ked_pt_eei_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_type",
                    "effective_start_date",
                    "employee_email_id",
                ],
                name="pd_ked_pt_esd_eei_idx",
            ),
        ]
