from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Index

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import EsCharField, EsEmailField, EsJSONField


class IntegrationConfig(MultiTenantTemporal):
    """
    This model is used to store the configuration of the integration
    """

    employee_email_id = EsEmailField(null=False, is_sensitive=False)
    integration_type = EsCharField(null=False, max_length=250, is_sensitive=False)
    config = EsJSONField(encoder=DjangoJSONEncoder, null=True, blank=True)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "integration_config"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=["client_id", "employee_email_id", "integration_type"],
                name="ic_c_ei_it_idx",
            ),
        ]
