from django.db.models import Index, Q, UniqueConstraint

from spm.models.base_models.base_plan_models import (
    BaseCommissionPlan,
    BasePlanCriteria,
    BasePlanDetails,
    BasePlanDocs,
    BasePlanPayee,
    BaseTempPlanDocs,
)
from spm.models.utils.model_utils import (  # pylint: disable=W0611
    path_within_bucket,
    temp_plan_doc_file_path,
)


class ForecastPlan(BaseCommissionPlan):
    class Meta(BaseCommissionPlan.Meta):
        db_table = "forecast_plan"
        indexes = BaseCommissionPlan.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                ],
                name="fp_ked_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "is_draft",
                    "plan_type",
                ],
                name="fp_ked_pt_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_start_date",
                    "plan_end_date",
                ],
                name="fp_ked_psd_ped_idx",
            ),
        ]


class ForecastPlanCriteria(BasePlanCriteria):
    class Meta(BasePlanCriteria.Meta):
        db_table = "forecast_plan_criteria"
        constraints = [
            UniqueConstraint(
                fields=["client_id", "criteria_id"],
                name="unique_forecast_criteria",
                condition=Q(is_deleted=False) & Q(knowledge_end_date__isnull=True),
            ),
        ]
        indexes = BasePlanCriteria.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                ],
                name="f_pc_ked_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "criteria_id",
                ],
                name="f_pc_ked_ci_idx",
            ),
        ]


class ForecastPlanPayee(BasePlanPayee):
    class Meta(BasePlanPayee.Meta):
        db_table = "forecast_plan_payee"
        indexes = BasePlanPayee.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                ],
                name="f_pp_ked_pi_eei_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "employee_email_id",
                ],
                name="f_pp_ked_ee_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "effective_start_date",
                    "effective_end_date",
                ],
                name="f_pp_ked_pi_esd_eed_idx",
            ),
        ]


class ForecastPlanDocs(BasePlanDocs):
    class Meta(BasePlanDocs.Meta):
        db_table = "forecast_plan_supporting_docs"
        indexes = BasePlanDocs.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                    "doc",
                ],
                name="f_pd_ked_pi_eei_idx",
            )
        ]


class ForecastPlanDetails(BasePlanDetails):
    class Meta(BasePlanDetails.Meta):
        db_table = "forecast_plan_details"
        indexes = BasePlanDetails.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_type",
                    "employee_email_id",
                    "plan_id",
                ],
                name="fpd_ked_pt_eei_pi_idx",
            ),
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_type",
                    "effective_start_date",
                    "employee_email_id",
                ],
                name="fpd_ked_pt_esd_eei_idx",
            ),
        ]


class TempForecastPlanDocs(BaseTempPlanDocs):
    class Meta(BaseTempPlanDocs.Meta):
        db_table = "temp_forecast_plan_supporting_docs"
        indexes = BaseTempPlanDocs.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "plan_id",
                    "employee_email_id",
                    "doc",
                ],
                name="tfpd_ked_pi_eei_idx",
            )
        ]
