from django.db.models import Index

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import EsBooleanField, EsCharField, EsJSONField


class ClientNotification(MultiTenantTemporal):
    notification_name = EsCharField(max_length=100, null=False, is_sensitive=False)
    can_payee_opt_out = EsBooleanField(null=False, default=False, is_sensitive=False)
    is_admin_notification = EsBooleanField(
        null=False, default=False, is_sensitive=False
    )
    # frequency will be None when can_payee_opt_out is True
    frequency = EsCharField(max_length=50, null=True, is_sensitive=False)
    # status will be None only when is_admin_notification is False and can_payee_opt_out is True
    # otherwise it'll be like the follwing:
    # {
    #     "email": True,
    #     "slack": False,
    #     "msteams": False,
    # }
    status = EsJSONField(null=True, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "client_notification"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "is_deleted",
                    "-knowledge_end_date",
                    "notification_name",
                ],
                name="cn_ked_nn_idx",
            ),
        ]
