dashboard_permission_objects = [
    {
        "permission_id": "view:dashboard",
        "permission_name": "View dashboards page",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "show_data_permissions": False,
        "component_order": 1,
        "permission_description": "Users can only see the dashboards shared with them.",
    },
    {
        "permission_id": "manage:dashboard",
        "permission_name": "Create & Edit custom dashboards",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "parent_id": "view:dashboard",
        "show_data_permissions": False,
        "component_order": 1,
    },
    {
        "permission_id": "delete:dashboard",
        "permission_name": "Delete custom dashboards",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "parent_id": "view:dashboard",
        "show_data_permissions": False,
        "component_order": 1,
    },
    {
        "permission_id": "view:admindashboard",
        "permission_name": " View default admin dashboard",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "parent_id": "view:dashboard",
        "show_data_permissions": False,
        "component_order": 1,
    },
    {
        "permission_id": "view:payeedashboard",
        "permission_name": "View default payee dashboard ",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "parent_id": "view:dashboard",
        "show_data_permissions": False,
        "component_order": 1,
        "permission_description": "Admins who are part of commission plans can see the payee dashboard even without this permission.",
    },
    {
        "permission_id": "manage:analytics",
        "permission_name": "Manage analytics",
        "component_system_name": "dashboard",
        "component_display_name": "Dashboard",
        "parent_id": "view:dashboard",
        "show_data_permissions": False,
        "component_order": 1,
        "permission_description": "Users can access analytics portal to create and manage dashboards.",
    },
]

datasheet_permission_objects = [
    {
        "permission_id": "view:databook",
        "permission_name": "View databooks page",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "show_data_permissions": False,
        "component_order": 2,
        "permission_description": "Users will get read-only access to datasheets.",
    },
    {
        "permission_id": "manage:databook",
        "permission_name": "Create & Edit databooks",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "parent_id": "view:databook",
        "show_data_permissions": False,
        "component_order": 2,
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    {
        "permission_id": "manage:datasheetpermissions",
        "permission_name": "Manage datasheet permissions",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "parent_id": "view:databook",
        "show_data_permissions": False,
        "component_order": 2,
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    {
        "permission_id": "manage:datasheetadjustments",
        "permission_name": "Adjust records in datasheet",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "parent_id": "view:databook",
        "show_data_permissions": False,
        "component_order": 2,
        "permission_description": "Datasheet level row and column restrictions won't apply to users with this permission.",
    },
    {
        "permission_id": "export:datasheet",
        "permission_name": "Export datasheet",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "parent_id": "view:databook",
        "show_data_permissions": False,
        "component_order": 2,
    },
    {
        "permission_id": "delete:datasheet",
        "permission_name": "Delete databooks",
        "component_system_name": "databooks",
        "component_display_name": "Databooks",
        "parent_id": "view:databook",
        "show_data_permissions": False,
        "component_order": 2,
    },
]

commission_plans_permission_objects = [
    {
        "permission_id": "view:commissionplan",
        "permission_name": "View Commission Plan",
        "component_system_name": "commission_plans",
        "component_display_name": "Commission Plans",
        "show_data_permissions": False,
        "component_order": 3,
    },
    {
        "permission_id": "edit:commissionplan",
        "permission_name": "Edit Commission Plan",
        "component_system_name": "commission_plans",
        "component_display_name": "Commission Plans",
        "parent_id": "view:commissionplan",
        "show_data_permissions": False,
        "component_order": 3,
    },
    {
        "permission_id": "create:commissionplan",
        "permission_name": "Create Commission Plan",
        "component_system_name": "commission_plans",
        "component_display_name": "Commission Plans",
        "parent_id": "edit:commissionplan",
        "show_data_permissions": False,
        "component_order": 3,
    },
    {
        "permission_id": "delete:commissionplan",
        "permission_name": "Delete published plans",
        "component_system_name": "commission_plans",
        "component_display_name": "Commission Plans",
        "parent_id": "edit:commissionplan",
        "show_data_permissions": False,
        "component_order": 3,
        "permission_description": "Plans in draft state can be deleted by users with 'edit commission plans' permission.",
    },
    {
        "permission_id": "publish:commissionplan",
        "permission_name": "Publish published plans",
        "component_system_name": "commission_plans",
        "component_display_name": "Commission Plans",
        "parent_id": "edit:commissionplan",
        "show_data_permissions": False,
        "component_order": 3,
        "permission_description": "Plans in draft state can be directly publish without approval",
    },
]

commission_payout_permission_objects = [
    {
        "permission_id": "view:payouts",
        "permission_name": "View payouts page",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "component_order": 5,
    },
    {
        "permission_id": "manage:payouts",
        "permission_name": "Manage payouts & arrears",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "view:payouts",
        "component_order": 5,
        "permission_description": "Users can lock payouts, email statements, manage approval stages.",
    },
    {
        "permission_id": "register:payouts",
        "permission_name": "Register payouts & arrears",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "view:payouts",
        "component_order": 5,
    },
    {
        "permission_id": "invalidate:payouts",
        "permission_name": "Invalidate payouts & arrears",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "register:payouts",
        "component_order": 5,
    },
    {
        "permission_id": "view:requestapprovals",
        "permission_name": "Request approvals",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "view:payouts",
        "component_order": 5,
    },
    {
        "permission_id": "export:payouts",
        "permission_name": "Export payouts",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "view:payouts",
        "component_order": 5,
    },
    {
        "permission_id": "view:statements",
        "permission_name": "View Statement tab on navigation bar",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "component_order": 5,
    },
    {
        "permission_id": "export:statement",
        "permission_name": "Export statements",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "parent_id": "view:statements",
        "component_order": 5,
    },
    {
        "permission_id": "view:hiddencriteria",
        "permission_name": "View hidden criteria in statements",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "component_order": 5,
        "permission_description": "Users can see hidden criteria configured in commission plans on the statement.",
    },
    {
        "permission_id": "manage:commissionadjustment",
        "permission_name": "Manage commission adjustments",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "component_order": 5,
    },
    {
        "permission_id": "view:payoutvalueothers",
        "permission_name": "View the payout value of others",
        "component_system_name": "payouts_statements",
        "component_display_name": "Payouts & Statements",
        "component_order": 5,
        "permission_description": "Users can see their own payouts by default. This permission lets them see payout values of others based on their data permissions.",
    },
]

crystal_permission_objects = [
    {
        "permission_id": "manage:crystal",
        "permission_name": "Manage crystal views",
        "component_system_name": "crystal",
        "component_display_name": "Crystal",
        "show_data_permissions": False,
        "component_order": 6,
    },
]


quotas_permission_objects = [
    {
        "permission_id": "view:quotas",
        "permission_name": "View quotas page",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "component_order": 7,
    },
    {
        "permission_id": "manage:quotas",
        "permission_name": "Manages quotas",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "parent_id": "view:quotas",
        "component_order": 7,
    },
    {
        "permission_id": "manage:quotasettings",
        "permission_name": "Manages quota settings",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "parent_id": "view:quotas",
        "component_order": 7,
    },
    {
        "permission_id": "view:hiddenquotas",
        "permission_name": "View hidden quota categories",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "component_order": 7,
    },
    {
        "permission_id": "view:draws",
        "permission_name": "View draws page",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "component_order": 7,
    },
    {
        "permission_id": "manage:draws",
        "permission_name": "Manage draws",
        "component_system_name": "quotas_draws",
        "component_display_name": "Quotas & Draws",
        "parent_id": "view:draws",
        "component_order": 7,
    },
]


manage_users_permission_objects = [
    {
        "permission_id": "view:users",
        "permission_name": "View users page",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
    },
    {
        "permission_id": "manage:users",
        "permission_name": "Create & Edit Users",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "parent_id": "view:users",
        "component_order": 8,
    },
    {
        "permission_id": "view:payroll",
        "permission_name": "View Base Pay & Variable Pay",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
        "permission_description": "Users can see their base pay and variable pay numbers by default. This permission lets them see payroll information of others based on their data permissions.",
    },
    {
        "permission_id": "edit:payroll",
        "permission_name": "Edit Base Pay & Variable Pay",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "parent_id": "view:payroll",
        "component_order": 8,
    },
    {
        "permission_id": "delete:users",
        "permission_name": "Delete Users",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "parent_id": "view:users",
        "component_order": 8,
    },
    {
        "permission_id": "export:users",
        "permission_name": "Export users",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "parent_id": "view:users",
        "component_order": 8,
    },
    {
        "permission_id": "allow:impersonation",
        "permission_name": "Can login as others",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
        "parent_id": "view:users",
        "permission_description": "Users with current role can login as users who belong to these roles",
    },
    {
        "permission_id": "view:teams",
        "permission_name": "View teams & pods page",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
    },
    {
        "permission_id": "manage:usergroups",
        "permission_name": "Manage user groups",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
    },
    {
        "permission_id": "manage:usercustomfield",
        "permission_name": "Manage user custom fields",
        "component_system_name": "manage_users",
        "component_display_name": "Teams, Groups & Users",
        "component_order": 8,
    },
]

queries_permission_objects = [
    {
        "permission_id": "view:queries",
        "permission_name": "View queries page",
        "component_system_name": "queries",
        "component_display_name": "Queries",
        "component_order": 9,
    },
    {
        "permission_id": "create:queries",
        "permission_name": "Create queries",
        "component_system_name": "queries",
        "component_display_name": "Queries",
        "parent_id": "view:queries",
        "component_order": 9,
    },
    {
        "permission_id": "edit:queries",
        "permission_name": "Edit queries",
        "component_system_name": "queries",
        "component_display_name": "Queries",
        "parent_id": "view:queries",
        "component_order": 9,
    },
    {
        "permission_id": "delete:queries",
        "permission_name": "Delete queries",
        "component_system_name": "queries",
        "component_display_name": "Queries",
        "parent_id": "view:queries",
        "is_active": False,
        "component_order": 9,
    },
]

settings_permission_objects = [
    {
        "permission_id": "manage:config",
        "permission_name": "Manage general configurations",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "Users can modify basic account settings, notifications, customise statements and approval workflows.",
    },
    {
        "permission_id": "manage:contracts",
        "permission_name": "Manage contracts",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "Users can setup integrations with contract management systems like DocuSign and manage contracts within Everstage.",
    },
    {
        "permission_id": "manage:datasettings",
        "permission_name": "Manage data & data integrations",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "Users can set up integrations, custom objects, upload data to objects, view audit logs, access commissions & data sync page.",
    },
    {
        "permission_id": "manage:accountnotifications",
        "permission_name": "Manage account notifications",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "This permission allows users to manage notifications at an account level.",
    },
    {
        "permission_id": "manage:approvalworkflows",
        "permission_name": "Manage approval workflows",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "This permission allows users to manage approval workflows at an account level.",
    },
    {
        "permission_id": "access:helpcenter",
        "permission_name": "Allow help center access",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "component_order": 11,
        "permission_description": "Users can access help center articles and raise support tickets.",
    },
    {
        "permission_id": "manage:multiperiodsync",
        "permission_name": "Manage multi period commission sync",
        "component_system_name": "settings",
        "component_display_name": "Settings",
        "show_data_permissions": False,
        "show_to_user": False,
        "component_order": 11,
        "permission_description": "This permission allows users to enable and work with multi period commission sync.",
    },
]

advanced_permission_objects = [
    {
        "permission_id": "manage:roles",
        "permission_name": "Manage roles",
        "component_system_name": "advanced_permissions",
        "component_display_name": "Advanced Admin Controls",
        "show_data_permissions": False,
        "component_order": 12,
        "permission_description": "Users cannot modify their own role.",
    },
    {
        "permission_id": "manage:owndata",
        "permission_name": "Modify their own data across Everstage",
        "component_system_name": "advanced_permissions",
        "component_display_name": "Advanced Admin Controls",
        "show_data_permissions": False,
        "component_order": 12,
        "permission_description": "Users can modify plans they belong to & their own quotas, draws, commission/draw adjustment, user information.",
    },
    {
        "permission_id": "manage:reportenrich",
        "permission_name": "Manage tags & report enrichment",
        "component_system_name": "advanced_permissions",
        "component_display_name": "Advanced Admin Controls",
        "show_data_permissions": False,
        "component_order": 12,
        "show_to_user": False,
    },
    {
        "permission_id": "manage:alladmins",
        "permission_name": "Manage All Admins",
        "component_system_name": "advanced_permissions",
        "component_display_name": "Advanced Admin Controls",
        "show_data_permissions": False,
        "component_order": 12,
        "show_to_user": False,
        "permission_description": "Users with this permission are considered as Power Admins in Everstage (for internal use).",
    },
]

everstage_objects = [
    {
        "permission_id": "view:everstage",
        "permission_name": "General permission for common api's",
        "component_system_name": "everstage",
        "component_display_name": "Everstage",
        "show_data_permissions": False,
        "component_order": 13,
        "show_to_user": False,
    },
    {
        "permission_id": "view:approvals",
        "permission_name": "General permission for approval workflow APIs",
        "component_system_name": "everstage",
        "component_display_name": "Everstage",
        "show_data_permissions": False,
        "component_order": 13,
        "show_to_user": False,
    },
    {
        "permission_id": "manage:adminui",
        "permission_name": "Admin UI permission",
        "component_system_name": "everstage",
        "component_display_name": "Everstage",
        "show_data_permissions": False,
        "component_order": 13,
        "show_to_user": False,
    },
]

everai_objects = [
    {
        "permission_id": "manage:agentworkbench",
        "permission_name": "Access agent workbench",
        "component_system_name": "everai",
        "component_display_name": "EverAI Admin Controls",
        "show_data_permissions": False,
        "component_order": 14,
        "permission_description": "Users can access the chat interface to interact with AI Agents.",
    },
    {
        "permission_id": "manage:autogendescription",
        "permission_name": "Manage autogenerated descriptions",
        "component_system_name": "everai",
        "component_display_name": "EverAI Admin Controls",
        "show_data_permissions": False,
        "component_order": 14,
        "permission_description": "Enables datasheet descriptions and provides option to generate descriptions for datasheets.",
    },
    {
        "permission_id": "manage:commissionformulageneration",
        "permission_name": "Manage commission formula generation",
        "component_system_name": "everai",
        "component_display_name": "EverAI Admin Controls",
        "show_data_permissions": False,
        "component_order": 14,
        "permission_description": "Users can access the commission formula generation AI to create and manage formulas.",
    },
    {
        "permission_id": "manage:datasheetaigeneration",
        "permission_name": "Manage datasheet AI generation",
        "component_system_name": "everai",
        "component_display_name": "EverAI Admin Controls",
        "show_data_permissions": False,
        "component_order": 14,
        "permission_description": "Users can access the datasheet AI generation to create and manage AI generated datasheets.",
    },
]

global_search_permission_objects = [
    {
        "permission_id": "view:globalsearch",
        "permission_name": "View global search",
        "component_system_name": "global_search",
        "component_display_name": "Global Search",
        "show_data_permissions": False,
        "component_order": 15,
        "permission_description": "Users can search across users, statements and other modules in Everstage.",
        "show_to_user": False,
    }
]


territory_plans_permission_objects = [
    {
        "permission_id": "view:territoryplans",
        "permission_name": "View territory plans",
        "component_system_name": "territory_plans",
        "component_display_name": "Territory Plans",
        "show_data_permissions": False,
        "component_order": 10,
        "permission_description": "Users can view territory plans",
    },
    {
        "permission_id": "explore:territoryplans",
        "permission_name": "Explore territory plans",
        "component_system_name": "territory_plans",
        "component_display_name": "Territory Plans",
        "parent_id": "view:territoryplans",
        "component_order": 10,
        "permission_description": "Users can explore territory plans",
    },
]

permission_data_lists = (
    dashboard_permission_objects
    + datasheet_permission_objects
    + commission_plans_permission_objects
    + commission_payout_permission_objects
    + crystal_permission_objects
    + quotas_permission_objects
    + territory_plans_permission_objects
    + manage_users_permission_objects
    + queries_permission_objects
    + settings_permission_objects
    + advanced_permission_objects
    + everstage_objects
    + everai_objects
    + global_search_permission_objects
)

default_role_permission_list = [
    {
        "knowledge_end_date": None,
        "display_name": "Payee",
        "description": "Provides limited Everstage access for users to view shared dashboards, their own or team-related statements and $QUOTAS_lc, and submit queries.",
        "show_to_user": True,
        "permissions": {
            "queries": {
                "permissions": ["view:queries", "create:queries", "edit:queries"],
                "data_permission": {"type": "INDIVIDUAL_DATA"},
            },
            "dashboard": {
                "permissions": ["view:dashboard", "view:payeedashboard"],
                "data_permission": None,
            },
            "everstage": {
                "permissions": ["view:everstage", "view:approvals"],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": ["view:payroll"],
                "data_permission": {"type": "INDIVIDUAL_DATA"},
            },
            "quotas_draws": {
                "permissions": ["view:quotas", "view:draws"],
                "data_permission": {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": [],
                    "is_user_groups_selected": False,
                    "is_reporting_team_selected": True,
                },
            },
            "payouts_statements": {
                "permissions": [
                    "view:statements",
                    "export:statement",
                    "view:payoutvalueothers",
                ],
                "data_permission": {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": [],
                    "is_user_groups_selected": False,
                    "is_reporting_team_selected": True,
                },
            },
            "territory_plans": {
                "permissions": ["view:territoryplans", "explore:territoryplans"],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        "is_editable": False,
    },
    {
        "knowledge_end_date": None,
        "display_name": "Super Admin",
        "description": "Provides complete admin access to Everstage, including the ability for users to modify their own payroll information, $QUOTA_lc, draws, $COMMISSION_lc $ADJUSTMENTS_lc, etc.",
        "show_to_user": True,
        "permissions": {
            "everstage": {"permissions": ["view:everstage", "view:approvals"]},
            "crystal": {
                "permissions": ["manage:crystal"],
                "data_permission": None,
            },
            "queries": {
                "permissions": [
                    "view:queries",
                    "create:queries",
                    "edit:queries",
                    "delete:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "settings": {
                "permissions": [
                    "manage:config",
                    "manage:contracts",
                    "manage:datasettings",
                    "manage:accountnotifications",
                    "manage:approvalworkflows",
                    "access:helpcenter",
                ],
                "data_permission": None,
            },
            "dashboard": {
                "permissions": [
                    "view:dashboard",
                    "manage:dashboard",
                    "delete:dashboard",
                    "view:admindashboard",
                    "manage:analytics",
                ],
                "data_permission": None,
            },
            "databooks": {
                "permissions": [
                    "view:databook",
                    "manage:databook",
                    "manage:datasheetpermissions",
                    "manage:datasheetadjustments",
                    "export:datasheet",
                    "delete:datasheet",
                ],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": [
                    "view:users",
                    "manage:users",
                    "view:payroll",
                    "edit:payroll",
                    "export:users",
                    "allow:impersonation",
                    "view:teams",
                    "manage:teams",
                    "manage:usergroups",
                    "manage:usercustomfield",
                ],
                "data_permission": {"type": "ALL_DATA"},
                "impersonated_user_roles": ["ALL"],
            },
            "quotas_draws": {
                "permissions": [
                    "view:quotas",
                    "manage:quotas",
                    "manage:quotasettings",
                    "view:hiddenquotas",
                    "view:draws",
                    "manage:draws",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                    "publish:commissionplan",
                ],
                "plans_scope": {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": None,
                },
                "data_permission": None,
            },
            "payouts_statements": {
                "permissions": [
                    "view:payouts",
                    "manage:payouts",
                    "register:payouts",
                    "invalidate:payouts",
                    "view:requestapprovals",
                    "export:payouts",
                    "view:hiddencriteria",
                    "manage:commissionadjustment",
                    "view:payoutvalueothers",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "global_search": {
                "permissions": ["view:globalsearch"],
                "data_permission": None,
            },
            "advanced_permissions": {
                "permissions": ["manage:roles", "manage:owndata"],
                "data_permission": None,
            },
            "territory_plans": {
                "permissions": ["view:territoryplans", "explore:territoryplans"],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        "is_editable": False,
    },
    {
        "knowledge_end_date": None,
        "display_name": "Power Admin",
        "description": "",
        "show_to_user": False,
        "permissions": {
            "everstage": {"permissions": ["view:everstage", "view:approvals"]},
            "crystal": {
                "permissions": ["manage:crystal"],
                "data_permission": None,
            },
            "queries": {
                "permissions": [
                    "view:queries",
                    "create:queries",
                    "edit:queries",
                    "delete:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "settings": {
                "permissions": [
                    "manage:config",
                    "manage:contracts",
                    "manage:datasettings",
                    "manage:accountnotifications",
                    "manage:approvalworkflows",
                    "access:helpcenter",
                ],
                "data_permission": None,
            },
            "dashboard": {
                "permissions": [
                    "view:dashboard",
                    "manage:dashboard",
                    "delete:dashboard",
                    "view:admindashboard",
                    "manage:analytics",
                ],
                "data_permission": None,
            },
            "databooks": {
                "permissions": [
                    "view:databook",
                    "manage:databook",
                    "manage:datasheetpermissions",
                    "manage:datasheetadjustments",
                    "export:datasheet",
                    "delete:datasheet",
                ],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": [
                    "view:users",
                    "manage:users",
                    "delete:users",
                    "view:payroll",
                    "edit:payroll",
                    "export:users",
                    "allow:impersonation",
                    "view:teams",
                    "manage:teams",
                    "manage:usergroups",
                    "manage:usercustomfield",
                ],
                "data_permission": {"type": "ALL_DATA"},
                "impersonated_user_roles": ["ALL"],
            },
            "quotas_draws": {
                "permissions": [
                    "view:quotas",
                    "manage:quotas",
                    "manage:quotasettings",
                    "view:hiddenquotas",
                    "view:draws",
                    "manage:draws",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                    "delete:commissionplan",
                    "publish:commissionplan",
                ],
                "plans_scope": {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": "ALL_PLANS",
                },
                "data_permission": None,
            },
            "payouts_statements": {
                "permissions": [
                    "view:payouts",
                    "manage:payouts",
                    "register:payouts",
                    "invalidate:payouts",
                    "view:requestapprovals",
                    "export:payouts",
                    "view:hiddencriteria",
                    "manage:commissionadjustment",
                    "view:payoutvalueothers",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "global_search": {
                "permissions": ["view:globalsearch"],
                "data_permission": None,
            },
            "advanced_permissions": {
                "permissions": [
                    "manage:roles",
                    "manage:owndata",
                    "manage:reportenrich",
                    "manage:alladmins",
                ],
                "data_permission": None,
            },
            "everai": {
                "permissions": [
                    "manage:agentworkbench",
                    "manage:autogendescription",
                ],
                "data_permission": None,
            },
            "territory_plans": {
                "permissions": ["view:territoryplans", "explore:territoryplans"],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        "is_editable": False,
    },
    {
        "knowledge_end_date": None,
        "display_name": "Admin",
        "description": "Provides admin access with some limitations. Users cannot modify their own payroll details, $QUOTA_lc, draws, $COMMISSION_lc $ADJUSTMENTS_lc, etc., but can access other admin capabilities.",
        "show_to_user": True,
        "permissions": {
            "everstage": {"permissions": ["view:everstage", "view:approvals"]},
            "crystal": {
                "permissions": ["manage:crystal"],
                "data_permission": None,
            },
            "queries": {
                "permissions": [
                    "view:queries",
                    "create:queries",
                    "edit:queries",
                    "delete:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "settings": {
                "permissions": [
                    "manage:config",
                    "manage:contracts",
                    "manage:datasettings",
                    "manage:accountnotifications",
                    "manage:approvalworkflows",
                    "access:helpcenter",
                ],
                "data_permission": None,
            },
            "dashboard": {
                "permissions": [
                    "view:dashboard",
                    "manage:dashboard",
                    "delete:dashboard",
                    "view:admindashboard",
                    "manage:analytics",
                ],
                "data_permission": None,
            },
            "databooks": {
                "permissions": [
                    "view:databook",
                    "manage:databook",
                    "manage:datasheetpermissions",
                    "manage:datasheetadjustments",
                    "export:datasheet",
                    "delete:datasheet",
                ],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": [
                    "view:users",
                    "manage:users",
                    "view:payroll",
                    "edit:payroll",
                    "export:users",
                    "allow:impersonation",
                    "view:teams",
                    "manage:teams",
                    "manage:usergroups",
                    "manage:usercustomfield",
                ],
                "data_permission": {"type": "ALL_DATA"},
                "impersonated_user_roles": ["ALL"],
            },
            "quotas_draws": {
                "permissions": [
                    "view:quotas",
                    "manage:quotas",
                    "manage:quotasettings",
                    "view:hiddenquotas",
                    "view:draws",
                    "manage:draws",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                    "publish:commissionplan",
                ],
                "plans_scope": {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": None,
                },
                "data_permission": None,
            },
            "payouts_statements": {
                "permissions": [
                    "view:payouts",
                    "manage:payouts",
                    "register:payouts",
                    "invalidate:payouts",
                    "view:requestapprovals",
                    "export:payouts",
                    "view:hiddencriteria",
                    "manage:commissionadjustment",
                    "view:payoutvalueothers",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "global_search": {
                "permissions": ["view:globalsearch"],
                "data_permission": None,
            },
            "territory_plans": {
                "permissions": ["view:territoryplans", "explore:territoryplans"],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        "is_editable": False,
    },
    {
        "knowledge_end_date": None,
        "display_name": "Plan Designer",
        "description": "",
        "show_to_user": True,
        "permissions": {
            "databooks": {
                "permissions": ["view:databook", "manage:databook"],
                "data_permission": {"type": None},
            },
            "everstage": {
                "permissions": ["view:everstage", "view:approvals"],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": ["manage:usergroups", "view:users"],
                "data_permission": {"type": "ALL_DATA"},
            },
            "quotas_draws": {
                "permissions": ["view:quotas", "manage:quotas"],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                    "publish:commissionplan",
                ],
                "plans_scope": {
                    "can_edit": "SHARED_PLANS",
                    "can_view": "SHARED_PLANS",
                    "can_delete": None,
                },
                "data_permission": {"type": None},
            },
            "settings": {
                "permissions": ["access:helpcenter"],
                "data_permission": None,
            },
        },
        "is_editable": False,
    },
]
