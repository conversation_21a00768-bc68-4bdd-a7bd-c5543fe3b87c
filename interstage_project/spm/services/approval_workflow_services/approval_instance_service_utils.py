from datetime import datetime
from uuid import uuid4

from django.utils import timezone

from commission_engine.utils.date_utils import add_days_to_date_in_time_zone
from interstage_project.utils import LogWithContext
from spm.accessors.approval_workflow_accessors import (
    ApprovalInstanceAccessor,
    ApprovalInstanceStageAccessor,
    ApprovalRequestsAccessor,
    ApprovalTemplateStageAccessor,
)
from spm.constants.approval_workflow_constants import (
    APPROVAL_TRIGGER_TYPES,
    APPROVAL_WORKFLOW_STATUS,
    ENTITY_KEY_DELIMETER,
)
from spm.custom_exceptions.approval_workflow_exceptions import (
    StageInstanceSerializerException,
)
from spm.serializers.approval_wokflow_serializers import (
    ApprovalInstanceStageSerializer,
    ApprovalRequestsBulkSerializer,
)


def get_all_stage_status_count(client_id, instance_ids, status):
    if not isinstance(instance_ids, list):
        instance_ids = [instance_ids]
    # Getting the count of all the stages for a particular instance
    all_instance_stage_count = ApprovalInstanceStageAccessor(
        client_id
    ).get_stage_count_by_instance_id_and_status(instance_ids)
    # Getting the count of all the passed status stage for a particular instance
    all_instance_stage_status_count = ApprovalInstanceStageAccessor(
        client_id
    ).get_stage_count_by_instance_id_and_status(instance_ids, status=status)

    all_instance_id_stage_count_map = {
        str(st["approval_wf_instance_id"]): st["count"]
        for st in all_instance_stage_count
    }
    all_instance_id_stage_status_count_map = {
        str(st["approval_wf_instance_id"]): st["count"]
        for st in all_instance_stage_status_count
    }

    all_stages_in_provided_status_map = {}
    for instance_id in instance_ids:
        if (
            instance_id in all_instance_id_stage_count_map
            and instance_id in all_instance_id_stage_status_count_map
            and all_instance_id_stage_count_map[instance_id]
            == all_instance_id_stage_status_count_map[instance_id]
        ):
            all_stages_in_provided_status_map[instance_id] = True
        else:
            all_stages_in_provided_status_map[instance_id] = False

    return {"all_instance_id_stage_status_count_map": all_stages_in_provided_status_map}


def update_stage_model_objects_status(
    client_id,
    all_stages,
    kd,
    status=None,
    due_period_map=None,
    is_system_action=False,
    cool_off_period_map=None,
    set_initiated_time=False,
    set_requested_time=False,
    set_completion_time=False,
    client_time_zone=None,
    approvers=None,
    additional_details=None,
):
    logger = LogWithContext()
    all_stage_ids = [st.stage_instance_id for st in all_stages]
    ApprovalInstanceStageAccessor(client_id).invalidate_stage(all_stage_ids, kd)
    logger.info("BEGIN: Updating stages instances")
    for record in all_stages:
        record = clone_object(record, kd, additional_details)
        record.is_system_action = is_system_action
        if status:
            record.status = status
        if set_requested_time:
            record.requested_time = kd
        if set_initiated_time:
            record.initiated_time = kd
        if set_completion_time:
            record.completed_time = kd
        if approvers:
            record.approvers = approvers
        if due_period_map:
            record.due_date = (
                add_days_to_date_in_time_zone(
                    due_period_map[str(record.stage_instance_id)], client_time_zone, kd
                )
                if record.stage_instance_id
                and str(record.stage_instance_id) in due_period_map
                and due_period_map[str(record.stage_instance_id)]
                else None
            )
        if cool_off_period_map:
            record.cool_off_date = (
                add_days_to_date_in_time_zone(
                    cool_off_period_map[str(record.stage_instance_id)],
                    client_time_zone,
                    kd,
                )
                if record.stage_instance_id
                and str(record.stage_instance_id) in cool_off_period_map
                and cool_off_period_map[str(record.stage_instance_id)]
                else None
            )
        stage_instance_dict = record.__dict__
        stage_instance_dict["client"] = client_id
        stage_instance_ser = ApprovalInstanceStageSerializer(data=stage_instance_dict)
        if not stage_instance_ser.is_valid():
            logger.error(
                "Stage updation failed for instance - {} and stage_template_id - {} at stage - {}, errors - {}".format(
                    record.approval_wf_instance_id,
                    record.stage_instance_id,
                    record.stage_name,
                    stage_instance_ser.errors,
                )
            )
            approval_instance = ApprovalInstanceAccessor(client_id).get_instance_by_id(
                instance_id=record.approval_wf_instance_id, projection=["entity_key"]
            )
            payee = (
                approval_instance["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
                if approval_instance
                else None
            )
            period = (
                get_period(
                    approval_instance["entity_key"].split(ENTITY_KEY_DELIMETER)[1]
                )
                if approval_instance
                else None
            )
            raise StageInstanceSerializerException(payee, period)
    stage_instances = ApprovalInstanceStageAccessor(client_id).bulk_persist(all_stages)
    logger.info(
        "END: Updating stages instances. Total stage instances updated - {} ".format(
            len(all_stages)
        )
    )
    return stage_instances


def create_requests_for_approvers(
    client_id,
    approvers_list,
    stage_id,
    instance_id,
    entity_key,
    entity_type,
    kd,
    logger=None,
    audit=None,
):
    logger = LogWithContext() if logger is None else logger
    # # Check if requests for given details are already present
    # existing_requests = ApprovalRequestsAccessor(client_id).get_requests_by_approver_and_stage(stage_id, approvers_list, projection=['approval_request_id', 'approver'])
    # approvers_to_skip = set(ar["approver"] for ar in existing_requests)

    logger.info(f"Creating approval requests for approvers: {approvers_list}")
    all_new_stage_requests = []
    for approver in approvers_list:
        # if approver not in approvers_to_skip:
        request_object = construct_request_data(
            client_id,
            instance_id,
            stage_id,
            entity_key,
            entity_type,
            approver,
            kd,
            audit,
        )
        all_new_stage_requests.append(request_object)
    all_requesr_ser = ApprovalRequestsBulkSerializer(
        data=all_new_stage_requests, many=True
    )
    if all_requesr_ser.is_valid():
        return ApprovalRequestsAccessor(client_id).persist_approval_request(
            all_requesr_ser
        )
    else:
        logger.error(
            "Exception while creating new stage requests - {}".format(
                all_requesr_ser.errors
            )
        )
        raise Exception("Exception while creating new stage requests")


def get_cool_off_stages_map(
    all_stage_ids, stage_cool_off_date_map, knowledge_date=None, logger=None
):
    logger = LogWithContext({}) if logger is None else logger
    knowledge_date = knowledge_date if knowledge_date else timezone.now()
    logger.info(
        f"Creating StageID to cool-off date condition met map for Stages: {all_stage_ids}"
    )

    stage_id_cool_off_status_map = {}
    for stage_id in all_stage_ids:
        if (
            stage_id in stage_cool_off_date_map
            and stage_cool_off_date_map[stage_id]
            and knowledge_date < stage_cool_off_date_map[stage_id]
        ):
            stage_id_cool_off_status_map[stage_id] = False
        else:
            stage_id_cool_off_status_map[stage_id] = True

    return {
        "stage_id_cool_off_status_map": stage_id_cool_off_status_map,
    }


def get_approval_trigger_passed_stage_map(
    all_stage_ids, next_stage_approval_trigger_map, knowledge_date=None, logger=None
):
    logger = LogWithContext({}) if logger is None else logger
    knowledge_date = knowledge_date if knowledge_date else timezone.now()

    logger.info(
        f"Creating StageID to approval strategy condition met map for Stages: {all_stage_ids}"
    )

    stage_id_trigger_status_map = {}

    for stage_id in all_stage_ids:
        if (
            stage_id in next_stage_approval_trigger_map
            and next_stage_approval_trigger_map[stage_id]
            and next_stage_approval_trigger_map[stage_id]
            == APPROVAL_TRIGGER_TYPES.APPROVED.value
        ):
            stage_id_trigger_status_map[stage_id] = True
        else:
            stage_id_trigger_status_map[stage_id] = False

    return {
        "stage_id_trigger_status_map": stage_id_trigger_status_map,
    }


def construct_request_data(
    client_id,
    instance_id,
    stage_id,
    entity_key,
    entity_type,
    approver,
    kd,
    additional_details=None,
):
    return {
        "client": client_id,
        "knowledge_begin_date": kd,
        "approval_request_id": uuid4(),
        "approval_wf_instance_id": instance_id,
        "stage_instance_id": stage_id,
        "entity_type": entity_type,
        "entity_key": entity_key,
        "approver": approver,
        "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
        "requested_time": kd,
        "additional_details": additional_details,
    }


def filter_stage_objects_by_stage_ids(stage_objects, stage_ids):
    stage_id_object_map = {str(st.stage_instance_id): st for st in stage_objects}
    filtered_objects = []
    for stage_id in stage_ids:
        if stage_id in stage_id_object_map:
            filtered_objects.append(stage_id_object_map[stage_id])

    return filtered_objects


def filter_objects_by_key(all_objects, required_object_key_values, key):
    key_record_map = {str(getattr(ob, key)): ob for ob in all_objects}
    filtered_objects = []
    for required_value in required_object_key_values:
        if required_value in key_record_map:
            filtered_objects.append(key_record_map[required_value])

    return filtered_objects


def clone_object(query_object, curr_time, request_audit=None):
    query_object.pk = None
    query_object.knowledge_begin_date = curr_time
    query_object.additional_details = request_audit
    return query_object


def get_period(date_string):
    date = datetime.strptime(date_string, "%Y-%m-%d")
    return date.strftime("%b") + " " + date.strftime("%Y")


def get_approval_wf_instances(
    client_id,
    filters=None,
    qs_filter=None,
    projection=None,
    order_by=None,
    *,
    as_dict=True,
):
    approval_requests = ApprovalInstanceAccessor(client_id).get_all_wf_instances(
        filters, qs_filter, projection, order_by, as_dict
    )
    return approval_requests


def get_approval_stage_instances(
    client_id, filters=None, projection=None, order_by=None, *, as_dict=True
):
    approval_requests = ApprovalInstanceStageAccessor(
        client_id
    ).get_all_stage_instances(filters, projection, order_by, as_dict)
    return approval_requests


def get_approval_requests(
    client_id, filters=None, projection=None, order_by=None, *, as_dict=True
):
    approval_requests = ApprovalRequestsAccessor(client_id).get_all_requests(
        filters, projection, order_by, as_dict
    )
    return approval_requests


def get_approval_template_stages(
    client_id, filters=None, projection=None, order_by=None, *, as_dict=True
):
    approval_template_stages = ApprovalTemplateStageAccessor(
        client_id
    ).get_all_template_stages(filters, projection, order_by, as_dict)
    return approval_template_stages


def create_approval_template_stage(client_id, validated_data):
    return ApprovalTemplateStageAccessor(client_id).bulk_create_template_stages(
        validated_data
    )


def get_formatted_amount(
    amount: float | str, currency_symbol: str = "", digits: int = 2
):
    """Get formatted currency value gien amount, currency symbol and digits"""
    return f"{currency_symbol}{float(amount):,.{digits}f}"
