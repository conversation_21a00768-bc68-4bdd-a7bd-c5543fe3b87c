import decimal
import json
import logging
import traceback
from collections import Counter, defaultdict
from datetime import datetime
from typing import Any, Dict, List
from uuid import UUID, uuid4

import pytz
from celery import shared_task
from dateutil import tz
from dateutil.parser import parse
from django.db import connection, transaction
from django.utils import timezone
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response

from async_tasks.accessor import AsyncTaskAccessor
from async_tasks.config import AsyncTaskConfig
from async_tasks.models import TaskStatus
from async_tasks.service import AsyncTaskService
from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_features,
    get_line_item_approval_flag,
)
from commission_engine.services.payout_status_changes_service import (
    add_approval_details_for_payout_status_changes,
)
from commission_engine.utils.date_utils import (
    add_days_to_date_in_time_zone,
    date_string_to_time_zone_aware_eod_utc,
    datetime_to_timezone_utc,
    end_of_day,
    get_period_start_and_end_date,
    make_aware_wrapper,
)
from commission_engine.utils.general_data import (
    RBAC,
    PayoutStatusChangesTypes,
    RBACComponent,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.general_utils import get_statements_url_for_payee
from common.celery.celery_base_task import EverCeleryBaseTask
from common.everstage_supabase.services import get_supabase_client
from interstage_project.utils import LogWithContext
from slack_everstage.utils.slack_utils import get_site_url
from spm.accessors.approval_workflow_accessors import (
    ApprovalInstanceAccessor,
    ApprovalInstanceStageAccessor,
    ApprovalRequestsAccessor,
    ApprovalTemplateAccessor,
    ApprovalTemplateStageAccessor,
    SubApprovalRequestsAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.user_group_accessor import UserGroupAccessor
from spm.commission_adjustment_approvals.accessors import (
    CommissionAdjustmentStatusAccessor,
)
from spm.commission_adjustment_approvals.models import CommissionAdjustmentStatus
from spm.commission_adjustment_approvals.services.email_notification_services import (
    send_comm_adj_approval_reminder_email,
    send_commission_adj_notification_to_approvers,
)
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_STAGE_STRATEGY,
    APPROVAL_TRIGGER_TYPES,
    APPROVAL_WORKFLOW_STATUS,
    DYNAMIC_TYPES,
    ENTITY_KEY_DELIMETER,
    SUB_ENTITY_KEY_DELIMETER,
)
from spm.constants.localization_constants import LocalizationTerms
from spm.custom_exceptions.approval_workflow_exceptions import (
    ApprovalRequestSerializerException,
    DueDatePassedException,
    InstanceStageStatusException,
    RequestNotInRequestedStageException,
    StageInstanceSerializerException,
)
from spm.serializers.approval_wokflow_serializers import (
    ApprovalInstanceSerializer,
    ApprovalInstanceStageSerializer,
    ApprovalRequestsSerializer,
)
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.approval_line_items import ApprovalSubRequestsStatus
from spm.services.approval_line_items.approval_instance import ApprovalInstanceStatus
from spm.services.approval_line_items.approval_request import ApprovalRequestStatus
from spm.services.approval_line_items.approval_stage_instance import ApprovalStageStatus
from spm.services.approval_workflow_services.approval_email_services import (
    get_email_statement_url,
    send_approve_request_email,
    send_failure_attachment_mail,
    send_notification_to_approvers_next_stages,
    send_reject_request_email,
    send_reminder_email,
)
from spm.services.approval_workflow_services.approval_instance_service_utils import (
    clone_object,
    construct_request_data,
    create_requests_for_approvers,
    filter_stage_objects_by_stage_ids,
    get_all_stage_status_count,
    get_approval_trigger_passed_stage_map,
    get_cool_off_stages_map,
    get_formatted_amount,
    get_period,
    update_stage_model_objects_status,
)
from spm.services.approval_workflow_services.approval_sub_request_services import (
    create_sub_approval_requests_data,
)
from spm.services.approval_workflow_services.approval_workflow_template_services import (
    get_audit_log_data,
    validate_approvers,
    validate_auto_approve,
    validate_notify_on_reject,
    validate_stage,
)
from spm.services.commission_plan_approvals.commission_plan_approval_notifications import (
    send_plan_approval_completion_notification,
    send_plan_approval_rejection_notification,
    send_plan_revoke_notification,
)
from spm.services.commission_plan_approvals.commission_plan_approvals_services import (
    get_plan_owners,
    handle_required_permissions,
    publish_plan_on_approval,
    remove_temp_shared_plan_access,
)
from spm.services.localization_services import get_localized_words_service
from spm.services.period_label_services import get_period_label_with_ped
from spm.services.query_builder.approval_workflow import ApprovalRequestQueryBuilder
from spm.services.rbac_services import (
    get_data_permission,
    get_invalid_emails_for_given_perms_and_emails,
    get_ui_permissions,
    get_valid_payee_emails,
    is_view_payroll_permission,
)
from spm.services.user_group_service import UserGroupMemberService

module_logger = logging.getLogger(__name__)


def resolve_reporting_manager_of_employees(employee_email_ids, employee_manager_map):
    """
    Resolves the reporting managers of a list of employees based on the provided employee-manager map.

    Parameters:
        - employee_email_ids (List[str]): List of employee email ids.
        - employee_manager_map (Dict[str, str]): A dictionary that maps employee emails to their manager email ids.

    Returns:
        List[int]: List of resolved manager email ids
    """
    resolved_managers = []
    for employee_email_id in employee_email_ids:
        if employee_email_id in employee_manager_map:
            resolved_managers.append(employee_manager_map[employee_email_id])
    return resolved_managers


def get_resolved_employees(
    client_id,
    employees_details_object,
    employee_email,
    employee_manager_map,
    previous_stage_approvers=None,
    entity_key=None,
):
    """
    Resolves the list of employees based on the provided employees_details_object and employee-manager map.

    Parameters:
        - client_id (int): The ID of the client.
        - employees_details_object (Dict): Dictionary containing the employees details based on type.
            (e.g. :
            {
            "users": [
                "<EMAIL>"
            ],
            "groups": ["f1f1a18c-9fa0-475a-a1ed-cd5f1ab97926"],
            "dynamic": ["payee", "manager", "previous_approver_manager"]
            })
        - employee_email (str): Email of the employee.
        - employee_manager_map (Dict[str, str]): A dictionary that maps employee emails to their manager email ids.
        - previous_stage_approvers (List[str], optional): List of employee email ids who were approvers in the previous stage.

    Returns:
        List[str]: List of resolved employee emails
    """
    resolved_employees = []
    if employees_details_object.get("users"):
        resolved_employees.extend(employees_details_object["users"])
    if employees_details_object.get("groups"):
        for group in employees_details_object["groups"]:
            resolved_employees.extend(
                UserGroupMemberService(client_id).get_user_group_members_emails(group)
            )
    if employees_details_object.get("dynamic"):
        for dynamic_type in employees_details_object["dynamic"]:
            if dynamic_type == DYNAMIC_TYPES.PAYEE.value:
                resolved_employees.append(employee_email)
            if dynamic_type == DYNAMIC_TYPES.MANAGER.value:
                if employee_email in employee_manager_map:
                    resolved_employees.append(employee_manager_map[employee_email])
            if dynamic_type == DYNAMIC_TYPES.PREVIOUS_APPROVER_MANAGER.value:
                managers = resolve_reporting_manager_of_employees(
                    previous_stage_approvers, employee_manager_map
                )
                resolved_employees.extend(managers)
            if dynamic_type == DYNAMIC_TYPES.INITIATOR.value:
                resolved_employees.append(employee_email)
            if dynamic_type == DYNAMIC_TYPES.MANAGER_OF_INITIATOR.value:
                if employee_email in employee_manager_map:
                    resolved_employees.append(employee_manager_map[employee_email])
            if dynamic_type == DYNAMIC_TYPES.ALL_PLAN_OWNERS.value:
                plan_owners = get_plan_owners(client_id, employee_email, entity_key)
                resolved_employees.extend(plan_owners)
    return list(set(resolved_employees))


def is_valid_instance_data(
    client_id,
    template_id,
    entity_type,
    instance_params,
    is_bulk_mode,
    logger=None,
    template_data=None,
):
    """
    Validates the instance data for the provided client, template and entity type.

    Parameters:
        - client_id (int): The ID of the client.
        - template_id (int): The ID of the template.
        - entity_type (str): The type of the entity. (e.g. "payout")
        - instance_params (Dict): Dictionary containing the instance details.
        - logger (logging.Logger, optional): A logger object to log messages.
        - template_data (Dict, optional): A dictionary containing the template data.

    Returns:
        Dict: A dictionary containing the validation result.
    """
    logger = LogWithContext() if logger is None else logger
    logger.info("BEGIN: Instance validation.")
    entity_type_required_fields_map = {
        APPROVAL_ENTITY_TYPES.PAYOUT.value: {"email_id", "currency", "payout"},
        APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value: {
            "email_id",
            "currency",
            "adjustment_id",
            "adjustment_amount",
            "reason",
            "reason_category",
            "period",
        },
        # Eg: {"adjusment_amount": 1000, "currency": "USD", "Reason Category": "CRM Issue", "Reason": "CRM Failed", "period": ""}
        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value: {
            "email_id",
            "currency",
            "payout",
        },
        APPROVAL_ENTITY_TYPES.QUOTE.value: {
            "email_id",
            "form_builder_id",
            "quote_id",
            "rule_group_id",
            "rule_group_name",
            "all_fields",
            "quote_total",
            "currency",
            "comments",
        },
        APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value: {
            "email_id",
            "plan_id",
        },
    }
    if template_id:
        if not ApprovalTemplateAccessor(client_id).does_template_id_exist(template_id):
            logger.info(f"Template id - {template_id} does not exist.")
            return {
                "is_valid": False,
                "message": "Workflow does not exist.",
                "entity_keys": [],
            }
    else:
        existing_user_groups = UserGroupAccessor(client_id).get_user_groups(
            projection=["user_group_id"]
        )
        existing_user_groups = [
            str(existing_user_group["user_group_id"])
            for existing_user_group in existing_user_groups
        ]
        existing_users = EmployeeAccessor(client_id).get_all_employee_email()
        existing_users = [
            existing_user["employee_email_id"] for existing_user in existing_users
        ]

        logger.info("Validate stage.")
        validate_stage_result = validate_stage(template_data, logger)
        if not validate_stage_result["is_valid"]:
            return validate_stage_result

        logger.info("Validate notify on reject.")
        validate_notify_on_reject_result = validate_notify_on_reject(
            template_data, existing_users, existing_user_groups, logger
        )
        if not validate_notify_on_reject_result["is_valid"]:
            return validate_notify_on_reject_result

        logger.info("Validate approvers.")
        validate_approvers_result = validate_approvers(
            template_data, existing_users, existing_user_groups, logger
        )
        if not validate_approvers_result["is_valid"]:
            return validate_approvers_result

        logger.info("Validate auto approve.")
        validate_auto_approve_result = validate_auto_approve(template_data, logger)
        if not validate_auto_approve_result["is_valid"]:
            return validate_auto_approve_result

    if not all(
        set(instance_detail.keys()) == entity_type_required_fields_map[entity_type]
        for instance_detail in instance_params["instance_details"]
    ):
        logger.info(
            f"Invalid input. All required fields for {entity_type} not present."
        )
        return {
            "is_valid": False,
            "message": "Invalid input, all required fields for request not provided.",
        }

    entity_keys = get_entity_keys(entity_type, instance_params)
    # If entity_type is payout_line_item, then check if there are no request in requested status for the same entity_keys
    if (
        entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value
        and not is_bulk_mode
        and ApprovalInstanceAccessor(
            client_id
        ).does_active_instance_exist_for_entity_keys_entity_type(
            entity_keys, [APPROVAL_WORKFLOW_STATUS.REQUESTED.value], entity_type
        )
    ):
        logger.info("Instance already requested.")
        return {
            "is_valid": False,
            "message": "Cannot request again when there's an ongoing approval.",
            "entity_keys": [],
        }
    # If entity_type is payout, then check if there are no request in requested/approved status for the same entity_keys
    if (
        entity_type
        in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
        ]
        and not is_bulk_mode
        and ApprovalInstanceAccessor(
            client_id
        ).does_active_instance_exist_for_entity_keys_entity_type(
            entity_keys,
            [
                APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            ],
            entity_type,
        )
    ):
        logger.info("Instance already requested/approved.")
        return {
            "is_valid": False,
            "message": "Approval is already requested/approved. Cannot request again until it's approved or rejected.",
            "entity_keys": [],
        }

    logger.info("END: Instance Validation.")
    return {
        "is_valid": True,
        "message": "Instance data is valid.",
        "entity_keys": entity_keys,
    }


def get_entity_keys(entity_type, instance_params):
    """
    Generates a list of entity keys based on the provided entity type and instance parameters.

    Parameters:
        - entity_type (str): The type of the entity. (e.g. "payout")
        - instance_params (Dict): Dictionary containing the instance details.

    Returns:
        List[str]: List of entity keys
    """
    if entity_type in [
        APPROVAL_ENTITY_TYPES.PAYOUT.value,
        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
    ]:
        return [
            employee_detail["email_id"]
            + ENTITY_KEY_DELIMETER
            + parse(instance_params["date"], dayfirst=True).strftime("%Y-%m-%d")
            for employee_detail in instance_params["instance_details"]
        ]

    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
        return [
            f'{adjustment_detail["adjustment_id"]}'
            for adjustment_detail in instance_params["instance_details"]
        ]

    if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
        return [
            instance["email_id"]
            + ENTITY_KEY_DELIMETER
            + str(instance["quote_id"])
            + ENTITY_KEY_DELIMETER
            + str(instance["rule_group_id"])
            for instance in instance_params["instance_details"]
        ]

    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
        return [instance["plan_id"] for instance in instance_params["instance_details"]]


def get_latest_entity_type(client_id, email_id, ped):
    entity_key = [
        email_id + ENTITY_KEY_DELIMETER + parse(ped, dayfirst=True).strftime("%Y-%m-%d")
    ]
    instances = ApprovalInstanceAccessor(client_id).get_latest_instances_by_entity_keys(
        entity_key
    )
    if instances:
        return instances[0]["entity_type"]
    return None


def get_sub_entity_id(payee_email, plan_id, criteria_id, line_item_id):
    """
    Generates a sub entity id based on the provided payee email, plan id, criteria id and line item id.

    Parameters:
        - payee_email (str): The email of the payee.
        - plan_id (int): The ID of the plan.
        - criteria_id (int): The ID of the criteria.
        - line_item_id (int): The ID of the line item.

    Returns:
        str: sub entity id

    EXAMPLE:
        RESPONSE: '<EMAIL>::71d470aa-88d2-4744-bbd9-f5ed2724844d::a685d2a5-408f-4658-b441-9da3cc33044d::16'
    """
    return (
        payee_email
        + SUB_ENTITY_KEY_DELIMETER
        + str(plan_id)
        + SUB_ENTITY_KEY_DELIMETER
        + str(criteria_id)
        + SUB_ENTITY_KEY_DELIMETER
        + str(line_item_id)
    )


def get_entity_key_email_map(entity_type, instance_params):
    if entity_type in [
        APPROVAL_ENTITY_TYPES.PAYOUT.value,
        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
    ]:
        return {
            employee_detail["email_id"]
            + ENTITY_KEY_DELIMETER
            + parse(instance_params["date"], dayfirst=True).strftime(
                "%Y-%m-%d"
            ): employee_detail["email_id"]
            for employee_detail in instance_params["instance_details"]
        }
    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
        return {
            str(adjustment_detail["adjustment_id"]): adjustment_detail["email_id"]
            for adjustment_detail in instance_params["instance_details"]
        }


def get_all_employee_manager_map(client_id):
    """
    Returns a map of all employees and their managers.

    Parameters:
        - client_id (int): The ID of the client.

    Returns:
        Dict[str, str]: A dictionary that maps employee emails to their manager emails.
    """
    hierarchy_data = HierarchyAccessor(client_id).get_current_hierarchy_list()
    return {
        employee_data["employee_email_id"]: employee_data["reporting_manager_email_id"]
        for employee_data in hierarchy_data
    }


@transaction.atomic
def create_instances_data(
    client_id,
    template_id,
    notify_on_reject,
    notify_on_approve,
    notify_on_revoke,
    entity_type,
    entity_keys,
    instance_details,
    employee_manager_map,
    created_by,
    audit_data,
    logger=None,
    is_bulk_mode=False,
):
    """
    Create instances data for a given template and entity_type.

    Parameters:
        - client_id (int): The ID of the client.
        - template_id (int): The ID of the template.
        - notify_on_reject (List[Dict]): List of notify when reject config
        - entity_type (str): The type of the entity. (e.g. "payout")
        - entity_keys (List[str]): List of entity_keys.
        - email_instance_data_map (Dict): Dictionary containing the instance details.
        - employee_manager_map (Dict[str, str]): A dictionary that maps employee emails to their manager emails.
        - created_by (str): The user who created the instance.
        - audit_data (Dict): Additional details for the instance.
        - logger (object): A logger object to log messages.

    Returns:
        List[object]: List of created instances data and errors if found.
    """
    logger = LogWithContext() if logger is None else logger
    logger.info("BEGIN: Instances creation for template - {}.".format(template_id))
    current_date_time = timezone.now()
    email_serialized_instance_data_map = {}
    error_instances = {}
    for instance_details_idx, instance_data in enumerate(instance_details):
        entity_key = (
            f'{instance_data["adjustment_id"]}'
            if (
                is_bulk_mode
                and entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
            )
            else entity_keys[instance_details_idx]
        )
        notify_all_approvers_on_reject = (
            DYNAMIC_TYPES.ALL_APPROVERS.value in notify_on_reject["dynamic"]
        )
        notify_approved_approvers_on_reject = (
            DYNAMIC_TYPES.ALL_PREVIOUSLY_APPROVED.value in notify_on_reject["dynamic"]
        )
        notify_all_approvers_on_approve = (
            DYNAMIC_TYPES.ALL_APPROVERS.value in notify_on_approve["dynamic"]
            if notify_on_approve
            else False
        )
        notify_approved_approvers_on_approve = (
            DYNAMIC_TYPES.ALL_PREVIOUSLY_APPROVED.value in notify_on_approve["dynamic"]
            if notify_on_approve
            else False
        )

        notify_all_approvers_when_revoked = (
            DYNAMIC_TYPES.ALL_APPROVERS.value in notify_on_revoke["dynamic"]
            if notify_on_revoke
            else False
        )
        notify_approved_approvers_when_revoked = (
            DYNAMIC_TYPES.ALL_PREVIOUSLY_APPROVED.value in notify_on_revoke["dynamic"]
            if notify_on_revoke
            else False
        )

        notify_when_rejected = get_resolved_employees(
            client_id,
            notify_on_reject,
            instance_data["email_id"],
            employee_manager_map,
            entity_key=entity_key,
        )
        notify_when_approved = get_resolved_employees(
            client_id,
            notify_on_approve,
            instance_data["email_id"],
            employee_manager_map,
            entity_key=entity_key,
        )
        notify_when_revoked = get_resolved_employees(
            client_id,
            notify_on_revoke,
            instance_data["email_id"],
            employee_manager_map,
            entity_key=entity_key,
        )
        if (
            not notify_when_rejected
            and notify_on_reject != [DYNAMIC_TYPES.ALL_PREVIOUSLY_APPROVED]
            and notify_on_approve != [DYNAMIC_TYPES.ALL_PREVIOUSLY_APPROVED]
            and not notify_all_approvers_on_reject
            and not notify_all_approvers_on_approve
        ):
            logger.info(
                "Notify when rejected resolved to empty for request - {}".format(
                    instance_data["email_id"]
                )
            )
            error_instances[instance_data["email_id"]] = (
                "Notify when rejected resolved to empty for the requested workflow."
            )
        notify_settings = {
            "notify_on_reject": {
                "all_approvers": notify_all_approvers_on_reject,
                "previously_approved": notify_approved_approvers_on_reject,
            },
            "notify_on_approve": {
                "all_approvers": notify_all_approvers_on_approve,
                "previously_approved": notify_approved_approvers_on_approve,
            },
            "notify_on_revoke": {
                "all_approvers": notify_all_approvers_when_revoked,
                "previously_approved": notify_approved_approvers_when_revoked,
            },
        }
        approval_instance_data = {
            "approval_wf_instance_id": uuid4(),
            "knowledge_begin_date": current_date_time,
            "additional_details": audit_data,
            "template_id": template_id,
            "entity_type": entity_type,
            "entity_key": entity_key,
            "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            "requested_time": current_date_time,
            "notify_on_reject": notify_when_rejected,
            "notify_on_approve": notify_when_approved,
            "notify_on_revoke": notify_when_revoked,
            "created_by": created_by,
            "client": client_id,
            "notify_all_approvers_on_approve": notify_all_approvers_on_approve,
            "notify_all_approvers": notify_all_approvers_on_reject,
            "notify_settings": notify_settings,
            "is_active": True,
        }
        # Populate Instance data based on entity type
        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
        ]:
            approval_instance_data["instance_data"] = {
                "currency": instance_data["currency"],
                "payout": instance_data["payout"],
            }
        if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            approval_instance_data["instance_data"] = {
                "currency": instance_data["currency"],
                "adjustment_value": instance_data["adjustment_amount"],
                "reason_category": instance_data["reason_category"],
                "reason": instance_data["reason"],
            }
        if entity_type in [
            APPROVAL_ENTITY_TYPES.QUOTE.value,
            APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
        ]:
            approval_instance_data["instance_data"] = instance_data
        approval_instance_ser = ApprovalInstanceSerializer(data=approval_instance_data)
        if not approval_instance_ser.is_valid():
            logger.error(
                "Instance creation failed for employee - {}, errors - {}".format(
                    instance_data["email_id"], approval_instance_ser.errors
                )
            )
            error_instances[instance_data["email_id"]] = (
                "Invalid data, something went wrong during serialization."
            )
        if (
            is_bulk_mode
            and entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
        ):
            if instance_data["email_id"] not in error_instances:
                if instance_data["email_id"] not in email_serialized_instance_data_map:

                    email_serialized_instance_data_map[instance_data["email_id"]] = [
                        approval_instance_ser.validated_data
                    ]
                else:
                    email_serialized_instance_data_map[
                        instance_data["email_id"]
                    ].append(approval_instance_ser.validated_data)
        else:
            if instance_data["email_id"] not in error_instances:
                email_serialized_instance_data_map[instance_data["email_id"]] = (
                    approval_instance_ser.validated_data
                )

    logger.info(
        "END: Instance Creation.{}".format(
            {
                "instances_data": email_serialized_instance_data_map,
                "error_emails_and_messages_map": error_instances,
            }
        )
    )
    return {
        "instances_data": email_serialized_instance_data_map,
        "error_emails_and_messages_map": error_instances,
    }


def create_stages_and_requests_data_for_bulk_adj(
    client_id,
    client_time_zone,
    stages_data,
    entity_type,
    approval_instances_data,
    employee_manager_map,
    instance_emails,
    audit_data=None,
    logger=None,
):
    logger = LogWithContext() if logger is None else logger
    logger.info("BEGIN: Instance stages and request creation.")
    current_date_time = timezone.now()
    email_serialized_stage_data_map = {}
    email_serialized_approval_requests_data_map = {}
    error_emails_and_messages = {}
    for instance_idx, instance_list in enumerate(approval_instances_data):
        for instance in instance_list:
            stage_approvers_resolved = []
            for stage_order, stage_data in enumerate(stages_data, 1):
                approvers = (
                    get_resolved_employees(
                        client_id,
                        stage_data["approvers"],
                        instance_emails[instance_idx],
                        employee_manager_map,
                        stage_approvers_resolved[stage_order - 2],
                    )
                    if stage_order > 1
                    else get_resolved_employees(
                        client_id,
                        stage_data["approvers"],
                        instance_emails[instance_idx],
                        employee_manager_map,
                    )
                )
                if not approvers:
                    logger.error(
                        "Approvers resolved to [] for request {}, stage {} and instance {}".format(
                            instance_emails[instance_idx],
                            stage_data["stage_name"],
                            instance["approval_wf_instance_id"],
                        )
                    )
                    error_emails_and_messages[instance_emails[instance_idx]] = (
                        "Approvers resolved to empty for stage - {} for the requested workflow.".format(
                            stage_data["stage_name"]
                        )
                    )
                    break
                stage_approvers_resolved.append(approvers)
                stage_instance_id = uuid4()
                stage_instance_data = {
                    "stage_instance_id": stage_instance_id,
                    "approval_wf_instance_id": instance["approval_wf_instance_id"],
                    "stage_template_id": stage_data["stage_template_id"],
                    "stage_name": stage_data["stage_name"],
                    "stage_order": stage_order,
                    "approvers": approvers,
                    "approval_strategy": stage_data["approval_strategy"],
                    "due_date": (
                        add_days_to_date_in_time_zone(
                            stage_data["due_period"], client_time_zone
                        )
                        if stage_data["due_period"] != None and stage_order == 1
                        else None
                    ),
                    "approval_trigger": APPROVAL_TRIGGER_TYPES.APPROVED.value,
                    "is_auto_approve": stage_data["is_auto_approve"],
                    "status": (
                        APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                        if stage_order == 1
                        else APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                    ),
                    "initiated_time": current_date_time if stage_order == 1 else None,
                    "requested_time": current_date_time if stage_order == 1 else None,
                    "stage_template_data": {
                        "due_period": stage_data["due_period"],
                        "cool_off_period": 0,
                    },  # When cool off is introduced, this part must be changed
                    "notes": stage_data["notes"],
                    "additional_details": audit_data,
                    "knowledge_begin_date": current_date_time,
                    "client": client_id,
                }
                stage_instance_ser = ApprovalInstanceStageSerializer(
                    data=stage_instance_data
                )
                if not stage_instance_ser.is_valid():
                    logger.error(
                        "Instances creation failed for instance - {} at stage = {}, errors - {}".format(
                            instance["approval_wf_instance_id"],
                            stage_data["stage_name"],
                            stage_instance_ser.errors,
                        )
                    )
                    error_emails_and_messages[instance_emails[instance_idx]] = (
                        "Invalid data for stage {}, something went wrong during serialization.".format(
                            stage_data["stage_name"]
                        )
                    )
                    break
                if instance_emails[instance_idx] not in error_emails_and_messages:
                    if (
                        instance_emails[instance_idx]
                        not in email_serialized_stage_data_map
                    ):
                        email_serialized_stage_data_map[
                            instance_emails[instance_idx]
                        ] = []
                    email_serialized_stage_data_map[
                        instance_emails[instance_idx]
                    ].append(stage_instance_ser.validated_data)
                if stage_order == 1:
                    for approver in approvers:
                        approval_request_data = {
                            "approval_wf_instance_id": instance[
                                "approval_wf_instance_id"
                            ],
                            "stage_instance_id": stage_instance_id,
                            "entity_type": entity_type,
                            "entity_key": instance["entity_key"],
                            "approver": approver,
                            "status": (
                                APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                                if stage_order == 1
                                else APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                            ),
                            "requested_time": (
                                current_date_time if stage_order == 1 else None
                            ),
                            "knowledge_begin_date": current_date_time,
                            "client": client_id,
                            "additional_details": audit_data,
                        }

                        approval_request_ser = ApprovalRequestsSerializer(
                            data=approval_request_data
                        )
                        if not approval_request_ser.is_valid():
                            logger.error(
                                "Approval Request invalid for instance - {}, stage - {} , approver - {}, errors - {}".format(
                                    instance["approval_wf_instance_id"],
                                    stage_data["stage_name"],
                                    approver,
                                    approval_request_ser.errors,
                                )
                            )
                            error_emails_and_messages[instance_emails[instance_idx]] = (
                                "Invalid data for stage {} for approver {}, something went wrong during serialization.".format(
                                    stage_data["stage_name"], approver
                                )
                            )
                            break
                        if (
                            instance_emails[instance_idx]
                            not in email_serialized_approval_requests_data_map
                        ):
                            email_serialized_approval_requests_data_map[
                                instance_emails[instance_idx]
                            ] = []
                        email_serialized_approval_requests_data_map[
                            instance_emails[instance_idx]
                        ].append(approval_request_ser.validated_data)
    logger.info(
        "END: Instance stages and requests creation - {}".format(
            {
                "stage_instances_data": email_serialized_stage_data_map,
                "approval_requests_data": email_serialized_approval_requests_data_map,
                "error_emails_and_messages_map": error_emails_and_messages,
            }
        )
    )
    return {
        "stage_instances_data": email_serialized_stage_data_map,
        "approval_requests_data": email_serialized_approval_requests_data_map,
        "error_emails_and_messages_map": error_emails_and_messages,
    }


def create_stages_and_requests_data(
    client_id,
    client_time_zone,
    stages_data,
    entity_type,
    entity_keys,
    approval_instances_data,
    employee_manager_map,
    instance_emails,
    audit_data=None,
    logger=None,
):
    logger = LogWithContext() if logger is None else logger
    logger.info("BEGIN: Instance stages and request creation.")
    current_date_time = timezone.now()
    email_serialized_stage_data_map = {}
    email_serialized_approval_requests_data_map = {}
    error_emails_and_messages = {}

    for instance_idx, instance in enumerate(approval_instances_data):
        stage_approvers_resolved = []
        for stage_order, stage_data in enumerate(stages_data, 1):
            approvers = (
                get_resolved_employees(
                    client_id,
                    stage_data["approvers"],
                    instance_emails[instance_idx],
                    employee_manager_map,
                    stage_approvers_resolved[stage_order - 2],
                )
                if stage_order > 1
                else get_resolved_employees(
                    client_id,
                    stage_data["approvers"],
                    instance_emails[instance_idx],
                    employee_manager_map,
                )
            )
            if not approvers:
                logger.error(
                    "Approvers resolved to [] for request {}, stage {} and instance {}".format(
                        instance_emails[instance_idx],
                        stage_data["stage_name"],
                        instance["approval_wf_instance_id"],
                    )
                )
                error_emails_and_messages[instance_emails[instance_idx]] = (
                    "Approvers resolved to empty for stage - {} for the requested workflow.".format(
                        stage_data["stage_name"]
                    )
                )
                break
            stage_approvers_resolved.append(approvers)
            stage_instance_id = uuid4()
            stage_instance_data = {
                "stage_instance_id": stage_instance_id,
                "approval_wf_instance_id": instance["approval_wf_instance_id"],
                "stage_template_id": stage_data["stage_template_id"],
                "stage_name": stage_data["stage_name"],
                "stage_order": stage_order,
                "approvers": approvers,
                "approval_strategy": stage_data["approval_strategy"],
                "due_date": (
                    add_days_to_date_in_time_zone(
                        stage_data["due_period"], client_time_zone
                    )
                    if stage_data["due_period"] != None and stage_order == 1
                    else None
                ),
                "approval_trigger": APPROVAL_TRIGGER_TYPES.APPROVED.value,
                "is_auto_approve": stage_data["is_auto_approve"],
                "status": (
                    APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                    if stage_order == 1
                    else APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                ),
                "initiated_time": current_date_time if stage_order == 1 else None,
                "requested_time": current_date_time if stage_order == 1 else None,
                "stage_template_data": {
                    "due_period": stage_data["due_period"],
                    "cool_off_period": 0,
                },  # When cool off is introduced, this part must be changed
                "notes": stage_data["notes"],
                "additional_details": audit_data,
                "knowledge_begin_date": current_date_time,
                "client": client_id,
            }
            stage_instance_ser = ApprovalInstanceStageSerializer(
                data=stage_instance_data
            )
            if not stage_instance_ser.is_valid():
                logger.error(
                    "Instances creation failed for instance - {} at stage = {}, errors - {}".format(
                        instance["approval_wf_instance_id"],
                        stage_data["stage_name"],
                        stage_instance_ser.errors,
                    )
                )
                error_emails_and_messages[instance_emails[instance_idx]] = (
                    "Invalid data for stage {}, something went wrong during serialization.".format(
                        stage_data["stage_name"]
                    )
                )
                break
            if instance_emails[instance_idx] not in error_emails_and_messages:
                if stage_order == 1:
                    email_serialized_stage_data_map[instance_emails[instance_idx]] = []
                email_serialized_stage_data_map[instance_emails[instance_idx]].append(
                    stage_instance_ser.validated_data
                )
            if (
                entity_type
                in [
                    APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
                    APPROVAL_ENTITY_TYPES.QUOTE.value,
                    APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
                ]
                or stage_order == 1
            ):
                for approver in approvers:
                    approval_request_data = {
                        "approval_wf_instance_id": instance["approval_wf_instance_id"],
                        "stage_instance_id": stage_instance_id,
                        "entity_type": entity_type,
                        "entity_key": entity_keys[instance_idx],
                        "approver": approver,
                        "status": (
                            APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                            if stage_order == 1
                            else APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                        ),
                        "requested_time": (
                            current_date_time if stage_order == 1 else None
                        ),
                        "knowledge_begin_date": current_date_time,
                        "client": client_id,
                        "additional_details": audit_data,
                    }
                    if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
                        if approver in stage_data.get("approve_on_request", []):
                            approval_request_data["approve_on_request"] = True
                        if approver in stage_data.get("approver_details", {}):
                            approval_request_data["request_data"] = stage_data[
                                "approver_details"
                            ][approver]
                    approval_request_ser = ApprovalRequestsSerializer(
                        data=approval_request_data
                    )
                    if not approval_request_ser.is_valid():
                        logger.error(
                            "Approval Request invalid for instance - {}, stage - {} , approver - {}, errors - {}".format(
                                instance["approval_wf_instance_id"],
                                stage_data["stage_name"],
                                approver,
                                approval_request_ser.errors,
                            )
                        )
                        error_emails_and_messages[instance_emails[instance_idx]] = (
                            "Invalid data for stage {} for approver {}, something went wrong during serialization.".format(
                                stage_data["stage_name"], approver
                            )
                        )
                        break
                    if (
                        instance_emails[instance_idx]
                        not in email_serialized_approval_requests_data_map
                    ):
                        email_serialized_approval_requests_data_map[
                            instance_emails[instance_idx]
                        ] = []
                    email_serialized_approval_requests_data_map[
                        instance_emails[instance_idx]
                    ].append(approval_request_ser.validated_data)
    logger.info(
        "END: Instance stages and requests creation - {}".format(
            {
                "stage_instances_data": email_serialized_stage_data_map,
                "approval_requests_data": email_serialized_approval_requests_data_map,
                "error_emails_and_messages_map": error_emails_and_messages,
            }
        )
    )
    return {
        "stage_instances_data": email_serialized_stage_data_map,
        "approval_requests_data": email_serialized_approval_requests_data_map,
        "error_emails_and_messages_map": error_emails_and_messages,
    }


def get_valid_inst_stages_reqs_attachment_data(
    requested_instance_emails,
    instances_data,
    invalid_instance_email_message_map,
    stage_instances_data,
    requests_data,
    invalid_request_email_message_map,
    logger=None,
):
    logger = LogWithContext() if logger is None else logger
    failed_email_message_list = []
    logger.info(
        "Invalid instance email and messages: {}".format(
            invalid_instance_email_message_map
        )
    )
    logger.info(
        "Invalid request email and messages: {}".format(
            invalid_request_email_message_map
        )
    )
    for email in requested_instance_emails:
        if (
            email in invalid_instance_email_message_map
            or email in invalid_request_email_message_map
        ):
            logger.info("Invalid data for email - {}. Removing..".format(email))
            if email in invalid_request_email_message_map:
                failed_email_message_list.append(
                    {
                        "email_id": email,
                        "message": invalid_request_email_message_map[email],
                    }
                )
            if email in invalid_instance_email_message_map:
                failed_email_message_list.append(
                    {
                        "email_id": email,
                        "message": invalid_instance_email_message_map[email],
                    }
                )
            instances_data.pop(email, None)
            stage_instances_data.pop(email, None)
            requests_data.pop(email, None)

    return failed_email_message_list


def approval_summary_query_for_payout_report(client_id):
    """
    Generates the SQL query to fetch the approval summary for the payout report.

    This query retrieves the approval workflow instance details, including the approval status, completion time, entity key, and the count of declined sub-approval requests for both 'payout' and 'payout_line_item' entity types.

    The query considers both 'payout' and 'payout_line_item' entity types because an entity key can have active approvals of either type. For example:
        - A client may currently have an payout approval enabled, while previously it could have had payout_line_item approval.
        - If a new approval request for a payee for a specific period is raised now, it will be a payout approval.
        - The same payee for a previous period might have had a payout_line_item approval, and that status will still be visible in the payouts page if a new request is not raised again for the previous period.

    The query is divided into two parts:
        1. The first part fetches the latest approval status and declined sub-approval request count for 'payout_line_item' entity types.
        2. The second part fetches the latest approval status for 'payout' entity types without considering sub-approval requests.

    Args:
        client_id (int): The client ID for which the approval summary is to be fetched.

    Returns:
        str: The SQL query string to fetch the approval summary for the payout report.
    """

    module_logger.info("BEGIN: Getting approval summary query for payout report")
    approval_query = f"""
        SELECT
            awi.approval_wf_instance_id,
            MAX(awi.temporal_id) AS latest_temporal_id,
            MAX(awi.status) AS latest_status,
            MAX(awi.completion_time) AS latest_completion_time,
            MAX(awi.entity_key) AS entity_key,
            COUNT(sar.approval_wf_instance_id) FILTER (
            WHERE sar.status = 'declined'
            ) AS declined_count
        FROM
            approval_wf_instance awi
        LEFT JOIN sub_approval_request sar ON
            sar.approval_wf_instance_id = awi.approval_wf_instance_id
            AND sar.knowledge_end_date IS NULL
            AND NOT sar.is_deleted
        WHERE
            awi.client_id = {client_id}
            AND awi.entity_type = 'payout_line_item'
            AND awi.knowledge_end_date IS NULL
            AND NOT awi.is_deleted
            AND awi.is_active
        GROUP BY
            awi.approval_wf_instance_id

        UNION

        SELECT
            awi.approval_wf_instance_id,
            awi.temporal_id AS latest_temporal_id,
            awi.status AS latest_status,
            awi.completion_time AS latest_completion_time,
            awi.entity_key,
            0 AS declined_count
        FROM
            approval_wf_instance awi
        WHERE
            awi.client_id = {client_id}
            AND awi.entity_type = 'payout'
            AND awi.knowledge_end_date IS NULL
            AND NOT awi.is_deleted
            AND awi.is_active
    """
    module_logger.info(
        "END: Successfully fetched approval summary query for the payout report"
    )
    return approval_query


def create_payout_status_changes_entries_for_approvals_actions(
    client_id: int,
    entity_key_list: list,
    event_type: str = PayoutStatusChangesTypes.APPROVAL_CREATE.value,
):
    """
    Function to create payout status changes entries for approvals
    """
    module_logger.info("BEGIN: Creating payout status changes entries for approvals")
    if not entity_key_list:
        module_logger.info("No entity keys found to record payout status changes")

    grouped_emails_by_ped = defaultdict(set)
    for entity_key in entity_key_list:
        email, ped = entity_key.split(ENTITY_KEY_DELIMETER)
        grouped_emails_by_ped[ped].add(email)

    for ped, emails in grouped_emails_by_ped.items():
        add_approval_details_for_payout_status_changes(
            client_id=client_id,
            ped=end_of_day(make_aware_wrapper(datetime.strptime(ped, "%Y-%m-%d"))),
            payees=list(emails),
            event_type=event_type,
        )

    module_logger.info("END: Successfully created payout status changes entries")


@transaction.atomic
def create_approval_instance_and_stages(
    client_id,
    template_id,
    entity_type,
    instance_params,
    created_by,
    template_data=None,
    audit_data=None,
    is_bulk_mode=False,
    logger=None,
    existing_entity_keys=None,
):
    """
    This function creates approval instances with the given template and stages.
    It also creates the approval requests for the instances.

    @transaction.atomic - This decorator ensures that the function is executed in a single transaction,
    so that if an exception occurs, all the operations inside the function are rolled back.

    Parameters:
        - client_id (int) - The ID of the client for which the approval instances are being created.
        - template_id (int) - The ID of the approval template to be used.
        - entity_type (str) - The type of entity for which the approval instances are being created.
        - instance_params (dict) - The parameters for creating the approval instances.
          Eg:{
                "period_end": "Jan",
                "instance_details": [
                {
                    "email_id": "<EMAIL>",
                    "currency": "USD",
                    "payout": "31508.000000"
                },
                {
                    "email_id": "<EMAIL>",
                    "currency": "INR",
                    "payout": "31507.000000"
                },
                ],
                "year": "2023"
            }
        - created_by (int) - The email ID of the user who is creating the approval instances.
        - template_data (dict) - The data for the approval template, if template_id is not provided.
          Eg:
        - audit_data (dict) - The audit data to be recorded for the approval instances.
        - logger (logging.Logger) - The logger to be used for logging the function's execution.

    Returns:
        - (django.http.response.Response) - If the validation fails, returns a response with status code 400
    and the error message. If successfull then returns a status code 201 and success message.
    """
    from everstage_ddd.cpq.approvals import send_quote_approval_creation_notification

    logger = LogWithContext() if logger is None else logger
    logger.info(
        "BEGIN: Create approval instances with template - {}".format(template_id)
    )
    entity_key_email_map = get_entity_key_email_map(entity_type, instance_params)
    email_instance_data_map = {}
    failure_attachment_content = []

    client_email_notification_flag = get_client(client_id).client_notification

    try:
        is_comm_bulk_mode = (
            is_bulk_mode
            and entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
        )
        validation_result = is_valid_instance_data(
            client_id,
            template_id,
            entity_type,
            instance_params,
            is_bulk_mode,
            logger,
            template_data,
        )

        # Validation
        if not validation_result["is_valid"]:
            if not is_bulk_mode:
                logger.info("Not bulk mode. Returning validation result to frontend.")
            else:
                # send failure email
                failure_attachment_content = [
                    {
                        "email_id": requested_email["email_id"],
                        "message": "Invalid workflow data. "
                        + validation_result["message"],
                    }
                    for requested_email in instance_params["instance_details"]
                ]

                logger.info(
                    f"Failure attachment Content - {failure_attachment_content}"
                )
                if client_email_notification_flag:
                    send_failure_attachment_mail(
                        client_id,
                        failure_attachment_content,
                        to_email=created_by,
                        logger=logger,
                    )
            if is_comm_bulk_mode:
                return Response(
                    {"status": "failed", "message": []},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:

                return Response(
                    {"status": "failed", "message": validation_result["message"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        entity_keys = validation_result["entity_keys"]
        if is_comm_bulk_mode:
            for instance_param in instance_params["instance_details"]:
                email_id = instance_param["email_id"]
                if email_id not in email_instance_data_map:
                    # If email_id is not in the dictionary, create a new list
                    email_instance_data_map[email_id] = [instance_param]
                else:
                    # If email_id exists, append the instance to the existing list
                    email_instance_data_map[email_id].append(instance_param)
        else:
            email_instance_data_map = {
                instance_param["email_id"]: instance_param
                for instance_param in instance_params["instance_details"]
            }

        # In bulk mode skip already requested and approved instances
        if is_bulk_mode:
            logger.info(
                "Bulk mode detected, requested for - {} ignoring instances already in requested state".format(
                    instance_params
                )
            )
            process_bulk_instances(
                client_id, email_instance_data_map, entity_keys, entity_key_email_map
            )

            logger.info(
                "Emails after removing instances in requested state {}".format(
                    list(email_instance_data_map.keys())
                )
            )
        if is_comm_bulk_mode:
            instance_details = [
                item for sublist in email_instance_data_map.values() for item in sublist
            ]
        else:
            instance_details = list(email_instance_data_map.values())

        # GET Client Timezone
        client_time_zone = get_client(client_id).time_zone

        ### AUDIT LOG ####
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
            audit_entity_type = "Payout Line Item"
        elif entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            audit_entity_type = "Payout"
        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            audit_entity_type = "Commission Adjustment"
        else:
            audit_entity_type = entity_type
        audit_summary = "Create " + audit_entity_type + " approval instance(s)"
        audit_meta_data = get_audit_log_data(
            EVENT["CREATE_APPROVAL-INSTANCE"]["code"],
            audit_summary,
            created_by,
            instance_params,
            "create_instance",
        )

        notify_on_reject = notify_on_approve = []
        if template_id:
            template_data = ApprovalTemplateAccessor(client_id).get_template_by_id(
                template_id
            )
            notify_on_reject = template_data.notify_on_reject
            notify_on_approve = template_data.notify_on_approve
            notify_on_revoke = template_data.notify_on_revoke

        else:
            notify_on_reject = template_data["notify_on_reject"]
            notify_on_approve = template_data["notify_on_approve"]
            notify_on_revoke = template_data["notify_on_revoke"]

        employee_manager_map = get_all_employee_manager_map(client_id)
        instances_data = create_instances_data(
            client_id,
            template_id,
            notify_on_reject,
            notify_on_approve,
            notify_on_revoke,
            entity_type,
            entity_keys,
            instance_details,
            employee_manager_map,
            created_by,
            audit_data,
            logger,
            is_bulk_mode,
        )
        logger.info("Instances data created and errors-  {}".format(instances_data))

        stages_data = []
        if template_id:
            stages_template_data = ApprovalTemplateStageAccessor(
                client_id
            ).get_stages_by_template_id(template_id, as_dict=True)
            stage_template_id_data_map = {
                stage_template_data["stage_template_id"]: stage_template_data
                for stage_template_data in stages_template_data
            }
            for stage_template_id in template_data.stage_order:
                stages_data.append(stage_template_id_data_map[UUID(stage_template_id)])
        else:
            stages_data = template_data["stages"]
            for stage_data in stages_data:
                stage_data["stage_template_id"] = None
        if is_comm_bulk_mode:
            stages_requests_data = create_stages_and_requests_data_for_bulk_adj(
                client_id,
                client_time_zone,
                stages_data,
                entity_type,
                list(instances_data["instances_data"].values()),  # Instances data
                employee_manager_map,
                list(instances_data["instances_data"].keys()),  # Instance emails
            )
        else:
            stages_requests_data = create_stages_and_requests_data(
                client_id,
                client_time_zone,
                stages_data,
                entity_type,
                entity_keys,
                list(instances_data["instances_data"].values()),  # Instances data
                employee_manager_map,
                list(instances_data["instances_data"].keys()),  # Instance emails
            )

        failed_email_message_list = get_valid_inst_stages_reqs_attachment_data(
            list(email_instance_data_map.keys()),  # emails in not requested state
            instances_data["instances_data"],
            instances_data["error_emails_and_messages_map"],
            stages_requests_data["stage_instances_data"],
            stages_requests_data["approval_requests_data"],
            stages_requests_data["error_emails_and_messages_map"],
        )

        logger.info(f"Failed email and messages list - {failed_email_message_list}")
        # Create instances, stages and requests
        logger.info("Begin Instances and stages creation.")
        approval_requests_data = []
        stage_instances_data = []
        for requests in stages_requests_data["approval_requests_data"].values():
            approval_requests_data.extend(requests)
        for stage_instances in stages_requests_data["stage_instances_data"].values():
            stage_instances_data.extend(stage_instances)

        # Set is_active to False for existing instances
        ApprovalInstanceAccessor(
            client_id
        ).set_is_active_false_for_records_by_entity_key(entity_key=entity_keys)
        if (
            is_bulk_mode
            and entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
        ):
            all_instance_details = [
                item
                for sublist in instances_data["instances_data"].values()
                for item in sublist
            ]
            instances = ApprovalInstanceAccessor(client_id).bulk_create_instances(
                all_instance_details
            )
        else:
            instances = ApprovalInstanceAccessor(client_id).bulk_create_instances(
                list(instances_data["instances_data"].values())
            )

        stage_instances = ApprovalInstanceStageAccessor(
            client_id
        ).bulk_create_instance_stages(stage_instances_data)
        approval_requests = ApprovalRequestsAccessor(
            client_id
        ).bulk_create_approval_requests(approval_requests_data)
        invalidated_entity_keys = None
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
            logger.info(f"Line item level approval is enabled for client {client_id}")

            payee_emails = []
            ped = end_of_day(
                make_aware(datetime.strptime(instance_params["date"], "%d-%m-%Y"))
            )
            for payee in instance_params["instance_details"]:
                payee_emails.append(payee["email_id"])

            logger.info("BEGIN Sub Approvals creation.")

            sub_approval_requests_data_dict = create_sub_approval_requests_data(
                client_id=client_id,
                period_end_date=ped,
                payee_emails=payee_emails,
                entity_keys=entity_keys,
                audit_data=audit_data,
            )

            sub_approval_request_data = sub_approval_requests_data_dict["data"]
            invalidated_entity_keys = sub_approval_requests_data_dict[
                "invalidated_entity_keys"
            ]

            if len(sub_approval_request_data) > 0:
                SubApprovalRequestsAccessor(
                    client_id
                ).bulk_create_sub_approval_requests(sub_approval_request_data)

            logger.info("END Sub Approvals creation.")

        if len(failed_email_message_list) > 0 and client_email_notification_flag:
            # TODO: Are we sending mail only when bulk mode??
            send_failure_attachment_mail(
                client_id, failed_email_message_list, to_email=created_by, logger=logger
            )

        if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
            logger.info(
                f"Add required view commisison plan permission for client {client_id}"
            )
            plan_id = entity_keys[0]
            active_approvers = []
            all_approvers = []
            for _request in approval_requests:
                if _request.status == APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
                    active_approvers.append(_request.approver)
                all_approvers.append(_request.approver)
            permission = ["view:commissionplan"]
            invalid_approvers = get_invalid_emails_for_given_perms_and_emails(
                client_id, all_approvers, permission
            )
            if invalid_approvers:
                raise Exception("Invalid approvers found.")

            handle_required_permissions(
                client_id,
                created_by,
                plan_id,
                active_approvers,
                audit_data,
            )

        audit_services.log(
            client_id,
            audit_meta_data.event_type_code,
            audit_meta_data.event_key,
            audit_meta_data.summary,
            audit_meta_data.updated_by,
            audit_meta_data.updated_at,
            audit_meta_data.audit_data,
        )
        logger.info("Send email notifications to approvers.")
        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
        ]:
            logger.info(f"Invalidated entity keys - {invalidated_entity_keys}")
            updated_stage_instances = []
            updated_requests = []
            updated_wf_instances = []
            wf_ids_to_remove = []
            # invalidated_entity_keys can be empty set also, so check for None
            if invalidated_entity_keys is not None:
                for request in approval_requests:
                    # Remove the requests which are invalidated (zero payout)
                    if request.entity_key not in list(invalidated_entity_keys):
                        # Remove the requests which are in not_started status (mail need not be sent at this status)
                        if request.status != ApprovalRequestStatus.Not_Started().name:
                            updated_requests.append(request)
                    else:
                        wf_ids_to_remove.append(request.approval_wf_instance_id)

                updated_stage_instances = [
                    stage_instance
                    for stage_instance in stage_instances
                    if stage_instance.approval_wf_instance_id not in wf_ids_to_remove
                ]
                updated_wf_instances = [
                    instance
                    for instance in instances
                    if instance.approval_wf_instance_id not in wf_ids_to_remove
                ]

                instances = updated_wf_instances
                stage_instances = updated_stage_instances
                approval_requests = updated_requests
            logger.info(f"Updated approval requests count - {len(approval_requests)}")

            if (
                len(instances) > 0
                and len(stage_instances) > 0
                and len(approval_requests) > 0
            ):
                logger.info(
                    f"Sending email notifications to approvers for {len(approval_requests)} approval requests in client {client_id}"
                )
                send_notification_to_approvers_next_stages(
                    client_id,
                    instances,
                    stage_instances,
                    approval_requests,
                    logger=logger,
                )
        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            send_commission_adj_notification_to_approvers(
                client_id,
                stage_instances,
                approval_requests,
                instance_params,
                is_bulk_mode,
                all_instance_details=[
                    item
                    for sublist in instances_data["instances_data"].values()
                    for item in sublist
                ],
            )

        elif entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
            send_quote_approval_creation_notification(
                client_id,
                instances,
                stage_instances,
                approval_requests,
                audit_data=audit_data,
                existing_entity_keys=existing_entity_keys,
            )

        analytics_data = {
            "user_id": created_by,
            "event_name": SegmentEvents.CREATE_APPROVAL_INSTANCE.value,
            "event_properties": {
                SegmentProperties.APPROVAL_INSTANCE_TYPE.value: entity_type,
                SegmentProperties.NUM_OF_INSTANCES.value: len(instances),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info("END: Create approval instances.")
        if len(failed_email_message_list) > 0:
            message = (
                [
                    item["entity_key"]
                    for sublist in instances_data["instances_data"].values()
                    for item in sublist
                ]
                if is_bulk_mode
                and entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
                else failed_email_message_list[0]["message"]
            )
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if is_comm_bulk_mode:
            entity_keys = [
                item["entity_key"]
                for sublist in instances_data["instances_data"].values()
                for item in sublist
            ]
            return Response(
                {"status": "success", "message": entity_keys},
                status=status.HTTP_201_CREATED,
            )

        if (
            entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value
            and invalidated_entity_keys
        ):
            if not is_bulk_mode and len(invalidated_entity_keys) > 0:
                return Response(
                    {
                        "status": "success",
                        "message": "Approval request not submitted as it has no line items to approve.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
        ]:
            if invalidated_entity_keys:
                payees_to_be_tracked = list(
                    set(entity_keys) - set(invalidated_entity_keys)
                )
            else:
                payees_to_be_tracked = entity_keys
            if payees_to_be_tracked:
                create_payout_status_changes_entries_for_approvals_actions(
                    client_id, payees_to_be_tracked
                )
        return Response(
            {"status": "success", "message": "Approval requested successfully."},
            status=status.HTTP_201_CREATED,
        )

    except Exception as exc:
        raise Exception("Approval requistion failed, please try later.") from exc


def process_bulk_instances(
    client_id, email_instance_data_map, entity_keys, entity_key_email_map
):
    instances = ApprovalInstanceAccessor(client_id).get_latest_instances_by_entity_keys(
        entity_keys
    )
    for instance in instances:
        # If status is requested or approved then ignore.
        if instance["status"] in [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            APPROVAL_WORKFLOW_STATUS.APPROVED.value,
        ]:
            entity_keys.remove(instance["entity_key"])
            email_instance_data_map.pop(entity_key_email_map[instance["entity_key"]])


@transaction.atomic
def abort_instances(client_id, entity_type, instance_params, audit_data, logger=None):
    """
    This function aborts the approval instances that match the given parameters.
    It also aborts the corresponding stages and requests of the instances which are in progress to be started.

    @transaction.atomic - This decorator ensures that the function is executed in a single transaction,
    so that if an exception occurs, all the operations inside the function are rolled back.

    Parameters:
        -  client_id (int) - The ID of the client for which the approval instances are being aborted.
        -  entity_type (str) - The type of entity for which the approval instances are being aborted.
        -  instance_params (dict) - The parameters for identifying the approval instances to be aborted.
        -  audit_data (dict) - The audit data to be recorded for the aborted instances.
        -  logger (logging.Logger) - The logger to be used for logging the function's execution.

    @return None
    """
    logger = LogWithContext() if logger is None else logger
    logger.info("BEGIN: Abort instances for params - {}".format(instance_params))
    try:
        current_datetime = timezone.now()
        entity_keys = get_entity_keys(entity_type, instance_params)
        entity_status_to_abort = [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            APPROVAL_WORKFLOW_STATUS.APPROVED.value,
        ]
        if (
            entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value
        ):  # Rejected approvals can be retriggered for comm_adj usecase
            entity_status_to_abort.extend(
                [
                    APPROVAL_WORKFLOW_STATUS.REJECTED.value,
                    APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                ]
            )
        logger.info("Entity keys = {}".format(entity_keys))
        instances = ApprovalInstanceAccessor(
            client_id
        ).get_instances_by_entity_keys_and_status_and_entity_type(
            entity_keys,
            entity_status_to_abort,
            entity_type,
            as_dict=False,
        )
        logger.info("Ongoing instances {}".format(instances))
        if instances:
            requested_status_instance_ids = []
            for instance in instances:
                if instance.status in entity_status_to_abort:
                    requested_status_instance_ids.append(
                        instance.approval_wf_instance_id
                    )
                instance.pk = None
                instance.status = APPROVAL_WORKFLOW_STATUS.ABORTED.value
                instance.knowledge_begin_date = current_datetime
                instance.is_system_action = True
                instance.additional_details = audit_data
            ApprovalInstanceAccessor(client_id).bulk_invalidate_by_instance_ids(
                [instance.approval_wf_instance_id for instance in instances],
                current_datetime,
            )
            ApprovalInstanceAccessor(client_id).bulk_persist(instances)
            if requested_status_instance_ids:
                ongoing_instance_stages = ApprovalInstanceStageAccessor(
                    client_id
                ).get_stage_instances_by_instance_id_status(
                    requested_status_instance_ids,
                    [
                        APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
                        APPROVAL_WORKFLOW_STATUS.STARTED.value,
                        APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                        APPROVAL_WORKFLOW_STATUS.COMPLETED.value,
                        # APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                    ],
                )
                logger.info(
                    "Ongoing instance stages {}".format(ongoing_instance_stages)
                )
                ongoing_requests = ApprovalRequestsAccessor(
                    client_id
                ).get_requests_by_instance_id_status(
                    requested_status_instance_ids,
                    [
                        APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                        APPROVAL_WORKFLOW_STATUS.APPROVED.value,
                        # APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                    ],
                )
                logger.info("Ongoing requests {}".format(ongoing_requests))
                for ongoing_instance_stage in ongoing_instance_stages:
                    ongoing_instance_stage.pk = None
                    ongoing_instance_stage.status = (
                        APPROVAL_WORKFLOW_STATUS.ABORTED.value
                    )
                    ongoing_instance_stage.knowledge_begin_date = current_datetime
                    ongoing_instance_stage.is_system_action = True
                    ongoing_instance_stage.additional_details = audit_data
                for ongoing_request in ongoing_requests:
                    ongoing_request.pk = None
                    ongoing_request.status = APPROVAL_WORKFLOW_STATUS.ABORTED.value
                    ongoing_request.knowledge_begin_date = current_datetime
                    ongoing_request.is_system_action = True
                    ongoing_request.additional_details = audit_data

                ApprovalInstanceStageAccessor(
                    client_id
                ).bulk_invalidate_by_stage_instance_ids(
                    [
                        ongoing_instance_stage.stage_instance_id
                        for ongoing_instance_stage in ongoing_instance_stages
                    ],
                    current_datetime,
                )
                ApprovalRequestsAccessor(client_id).bulk_invalidate_by_request_ids(
                    [
                        ongoing_request.approval_request_id
                        for ongoing_request in ongoing_requests
                    ],
                    current_datetime,
                )
                ApprovalInstanceStageAccessor(client_id).bulk_persist(
                    ongoing_instance_stages
                )
                ApprovalRequestsAccessor(client_id).bulk_persist(ongoing_requests)
        logger.info("END: Abort Instances.")
    except Exception as e:
        logger.error("Exception while aborting approvals.")
        raise Exception("Failed. ") from e


def get_timeline_data_entity_key(
    client_id,
    logged_in_user,
    entity_key,
    entity_type=APPROVAL_ENTITY_TYPES.PAYOUT.value,
    stage_instance_id=None,
    work_flow_instance_id=None,
    can_view_payout=True,
):
    """Get Approval Timeline data for an entity_key

    For a provided entity_key, this function returns the timeline data with all the datetime in loggedin users's timezone

    Parameters:
        -  client_id (int)
        -  logged_in_user (str)
        -  entity_key (str)

    @return {"data": []}


    """
    from spm.services.approval_line_items.line_item_services import (
        get_sub_request_count_for_approval_request_id,
        get_valid_payee_emails_for_logged_in_user,
    )

    DATE_TIME_FORMAT = "%d %b %Y, %H:%M"
    DUE_DATE_TIME_FORMAT = "%d %b %Y"
    log_context = {
        "client_id": client_id,
        "entity_key": entity_key,
    }
    logger = LogWithContext(log_context)
    timeline_data = []

    if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
        all_aproval_instances = ApprovalInstanceAccessor(
            client_id
        ).get_instance_by_partial_entity_key(
            entity_key,
            entity_type,
            projection=[
                "requested_time",
                "approval_wf_instance_id",
                "instance_data",
                "additional_details",
                "status",
                "knowledge_begin_date",
                "entity_type",
            ],
        )
    else:
        all_aproval_instances = ApprovalInstanceAccessor(
            client_id
        ).get_instance_by_entity_key(
            entity_key,
            projection=[
                "requested_time",
                "approval_wf_instance_id",
                "instance_data",
                "additional_details",
                "status",
                "knowledge_begin_date",
                "entity_type",
            ],
        )
    all_instance_ids = []
    all_instance_updated_by_user = set()
    if work_flow_instance_id is not None:
        for inst in all_aproval_instances:
            if str(inst.get("approval_wf_instance_id")) == work_flow_instance_id:
                all_instance_ids.append(str(inst.get("approval_wf_instance_id")))
                if inst.get("additional_details"):
                    all_instance_updated_by_user.add(
                        inst.get("additional_details", {}).get("updated_by")
                    )
    else:
        all_instance_ids = [
            str(inst.get("approval_wf_instance_id")) for inst in all_aproval_instances
        ]
        for inst in all_aproval_instances:
            all_instance_ids.append(str(inst.get("approval_wf_instance_id")))
            if inst.get("additional_details"):
                all_instance_updated_by_user.add(
                    inst.get("additional_details", {}).get("updated_by")
                )

    logger.info(
        f"Fetched all Approval Instances: {all_instance_ids} for entity_key: {entity_key} "
    )
    all_instance_stage_data = ApprovalInstanceStageAccessor(
        client_id
    ).get_instance_stage_by_instance_id(
        all_instance_ids,
        projection=[
            "approval_wf_instance_id",
            "stage_instance_id",
            "approvers",
            "status",
            "stage_name",
            "due_date",
            "stage_order",
            "notes",
        ],
    )

    instance_id_stage_map = {}
    all_stage_ids = []
    all_employees = []
    all_approvers = []
    all_employees.append(logged_in_user)
    if stage_instance_id is not None:
        for stage in all_instance_stage_data:
            if stage_instance_id == str(stage["stage_instance_id"]):
                inst_id = str(stage["approval_wf_instance_id"])
                if inst_id not in instance_id_stage_map:
                    instance_id_stage_map[inst_id] = []
                instance_id_stage_map[inst_id].append(stage)

                if (
                    stage["status"] != APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                    and stage["status"] != APPROVAL_WORKFLOW_STATUS.STARTED.value
                ):
                    all_stage_ids.append(stage["stage_instance_id"])
                all_approvers += stage["approvers"]
    else:
        for stage in all_instance_stage_data:
            inst_id = str(stage["approval_wf_instance_id"])
            if inst_id not in instance_id_stage_map:
                instance_id_stage_map[inst_id] = []
            instance_id_stage_map[inst_id].append(stage)

            if (
                stage["status"] != APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                and stage["status"] != APPROVAL_WORKFLOW_STATUS.STARTED.value
            ):
                all_stage_ids.append(stage["stage_instance_id"])
            all_approvers += stage["approvers"]

    all_approvers = list(set(all_approvers))
    all_stage_ids = list(set(all_stage_ids))

    all_employees_requests = ApprovalRequestsAccessor(
        client_id
    ).get_requests_by_approver_and_stage(
        all_stage_ids,
        all_approvers,
        projection=[
            "stage_instance_id",
            "approval_wf_instance_id",
            "completed_time",
            "status",
            "approver",
            "comments",
            "approval_request_id",
            "is_system_action",
            "additional_details",
            "knowledge_begin_date",
        ],
    )

    stage_id_all_requests_map = {}
    all_request_updated_by_user = set()
    for appr_req in all_employees_requests:
        stage_id = str(appr_req["stage_instance_id"])
        if stage_id not in stage_id_all_requests_map:
            stage_id_all_requests_map[stage_id] = []
        stage_id_all_requests_map[stage_id].append(appr_req)

        if appr_req.get("additional_details"):
            all_request_updated_by_user.add(
                appr_req.get("additional_details", {}).get("updated_by")
            )

    all_employees = (
        all_employees
        + all_approvers
        + list(all_instance_updated_by_user)
        + list(all_request_updated_by_user)
    )
    all_employees_data = EmployeeAccessor(client_id).get_all_employee_details(
        all_employees,
        projection=[
            "employee_email_id",
            "first_name",
            "last_name",
            "profile_picture",
            "time_zone",
        ],
    )
    employee_email_id_record_map = {
        employee.get("employee_email_id"): employee for employee in all_employees_data
    }

    user_timezone = employee_email_id_record_map.get(logged_in_user, {}).get(
        "time_zone", None
    )
    if user_timezone:
        user_timezone = user_timezone.split(" ")
        if len(user_timezone) > 1:
            user_timezone = tz.gettz(user_timezone[1])
        else:
            user_timezone = pytz.timezone(user_timezone[0])

    today = datetime.now(user_timezone if user_timezone else pytz.UTC)
    is_line_item_level_enabled = get_line_item_approval_flag(client_id)
    is_quote_approval = entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value
    multiple_approval_instances = {}
    for ap_intance in all_aproval_instances:
        if work_flow_instance_id is not None and work_flow_instance_id != str(
            ap_intance.get("approval_wf_instance_id")
        ):
            continue
        if is_quote_approval:
            timeline_data = []
        if ap_intance.get("instance_data"):
            instance_data = ap_intance.get("instance_data")
            if not can_view_payout:
                if "payout" in instance_data:
                    instance_data["payout"] = "-"

                if "adjustmentValue" in instance_data:
                    instance_data["adjustmentValue"] = "-"

                if "quote_total" in instance_data:
                    instance_data["quote_total"] = "-"
            ap_intance["instance_data"] = instance_data
        instance_id = str(ap_intance.get("approval_wf_instance_id"))
        updated_by = (
            ap_intance.get("additional_details", {}).get("updated_by", None)
            if ap_intance.get("additional_details")
            else None
        )
        instance_updated_by_user = employee_email_id_record_map.get(updated_by, {})
        timeline_data.append(
            {
                "status": APPROVAL_WORKFLOW_STATUS.COMPLETED.value,
                "title": "Approval Initiated",
                "actionTime": (
                    ap_intance["requested_time"]
                    .astimezone(user_timezone)
                    .strftime(DATE_TIME_FORMAT)
                    if ap_intance.get("requested_time")
                    else None
                ),
                "updatedBy": (
                    instance_updated_by_user.get("first_name", "")
                    + " "
                    + instance_updated_by_user.get("last_name", "")
                    if updated_by
                    else ""
                ),
                "approverData": [],
                "entityType": ap_intance.get("entity_type"),
            }
        )

        stage_data = instance_id_stage_map.get(instance_id, [])
        last_stage_id = (
            str(stage_data[-1].get("stage_instance_id"))
            if len(stage_data) > 0
            else None
        )

        previous_stage_status = None
        for stage in stage_data:
            instance_stage_id = str(stage.get("stage_instance_id"))
            update_last_stage = (
                last_stage_id is not None and last_stage_id == instance_stage_id
            )
            due_date = (
                stage["due_date"].astimezone(user_timezone)
                if user_timezone and stage["due_date"]
                else stage["due_date"]
            )
            if due_date:
                number_of_days_for_due_date = (due_date - today).days
            else:
                number_of_days_for_due_date = None
            stage_object = {
                "status": stage.get("status"),
                "title": stage.get("stage_name"),
                "instanceData": ap_intance.get("instance_data"),
                "stageId": stage.get("stage_instance_id"),
                "stageOrder": stage.get("stage_order"),
                "dueDate": (
                    stage["due_date"]
                    .astimezone(user_timezone)
                    .strftime(DUE_DATE_TIME_FORMAT)
                    if stage.get("due_date")
                    else None
                ),
                "instanceCreationTimeUTC": (
                    ap_intance["requested_time"]
                    .astimezone(user_timezone)
                    .strftime("%Y-%m-%d")
                    if ap_intance.get("requested_time")
                    else None
                ),
                "notes": stage.get("notes"),
                "approverData": [],
                "isLastStage": update_last_stage,
                "previousStageStatus": previous_stage_status,
                "entityType": ap_intance.get("entity_type"),
                "numberOfDaysForDueDate": number_of_days_for_due_date,
            }

            if (
                previous_stage_status is None
                or previous_stage_status != APPROVAL_WORKFLOW_STATUS.REJECTED.value
            ):
                # if previous_stage_status is REJECTED, then skip this if condition and carry on the `rejected` status for all the remaining stages.
                previous_stage_status = stage.get("status")

            approval_request_data = stage_id_all_requests_map.get(instance_stage_id, [])

            aborted_action_time = aborted_updated_by = aborted_status = None
            update_aborted_stage = False

            if (
                stage["status"] == APPROVAL_WORKFLOW_STATUS.ABORTED.value
                and len(approval_request_data) == 0
            ):
                # if aborted, then get action_time, updated_by from approval instance table.
                aborted_action_time = (
                    ap_intance.get("knowledge_begin_date")
                    .astimezone(user_timezone)
                    .strftime(DATE_TIME_FORMAT)
                )
                aborted_updated_by = (
                    instance_updated_by_user.get("first_name", "")
                    + " "
                    + instance_updated_by_user.get("last_name", ""),
                )
                aborted_status = APPROVAL_WORKFLOW_STATUS.ABORTED.value
                update_aborted_stage = True
            # If stage is not started then getting approver data from stage table itself
            if (
                stage["status"] == APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
                or stage["status"] == APPROVAL_WORKFLOW_STATUS.STARTED.value
                or update_aborted_stage
            ):
                approvers_email_list = stage["approvers"]
                for approver_email in approvers_email_list:
                    approver_details = employee_email_id_record_map.get(
                        approver_email, {}
                    )
                    stage_object["approverData"].append(
                        {
                            "url": (
                                approver_details.get("profile_picture")
                                if approver_details.get("profile_picture")
                                else ""
                            ),
                            "firstName": approver_details.get("first_name"),
                            "lastName": approver_details.get("last_name"),
                            "email": approver_email,
                            "status": (
                                aborted_status
                                if aborted_status
                                else APPROVAL_WORKFLOW_STATUS.NOT_REQUESTED.value
                            ),
                            "actionTime": (
                                aborted_action_time if aborted_action_time else ""
                            ),
                            "updatedBy": (
                                aborted_updated_by if aborted_updated_by else ""
                            ),
                        }
                    )
            else:
                # else fetching approver data from approval request table
                payee_request_count_map = None
                valid_timeline_approvers = []
                if (
                    is_line_item_level_enabled
                    and stage["status"] == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                ):
                    payee_request_count_map = (
                        get_sub_request_count_for_approval_request_id(
                            client_id, approval_request_data
                        )
                    )
                    valid_timeline_approvers = []
                    if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                        ped = make_aware(
                            end_of_day(
                                parse(
                                    entity_key.split(ENTITY_KEY_DELIMETER)[1],
                                    dayfirst=True,
                                )
                            )
                        )
                        logged_in_user_permission = get_ui_permissions(
                            client_id, logged_in_user
                        )
                        has_request_approval_access = (
                            RbacPermissions.VIEW_REQUESTAPPROVALS.value
                            in logged_in_user_permission
                        )
                        # if user has no access to request approvals, then only logged_in_user details will be shown in timeline
                        if not has_request_approval_access:
                            valid_timeline_approvers = [logged_in_user]
                        else:
                            valid_timeline_approvers = (
                                get_valid_payee_emails_for_logged_in_user(
                                    client_id, logged_in_user, ped
                                )
                            )

                for app_req in approval_request_data:
                    approver_email = app_req.get("approver")
                    approver_details = employee_email_id_record_map.get(
                        approver_email, {}
                    )
                    updated_by = (
                        app_req.get("additional_details", {}).get("updated_by", None)
                        if app_req.get("additional_details")
                        else None
                    )
                    updated_by = (
                        employee_email_id_record_map.get(updated_by, {}).get(
                            "first_name"
                        )
                        if updated_by
                        else ""
                    )

                    action_time = None
                    if (
                        app_req.get("status")
                        != APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                    ):
                        if (
                            app_req.get("status")
                            == APPROVAL_WORKFLOW_STATUS.ABORTED.value
                        ):
                            # if aborted, then get action_time, updated_by from approval instance table.
                            action_time = ap_intance.get("knowledge_begin_date")
                            updated_by = (
                                instance_updated_by_user.get("first_name", "")
                                + " "
                                + instance_updated_by_user.get("last_name", "")
                            )
                        else:
                            action_time = app_req.get("completed_time")

                    approver_data_dict = {
                        "url": (
                            approver_details.get("profile_picture")
                            if approver_details.get("profile_picture")
                            else ""
                        ),
                        "firstName": approver_details.get("first_name"),
                        "lastName": approver_details.get("last_name"),
                        "email": app_req.get("approver"),
                        "status": app_req.get("status"),
                        "comments": app_req.get("comments"),
                        "isSystemAction": app_req.get("is_system_action"),
                        "actionTime": (
                            action_time.astimezone(user_timezone).strftime(
                                DATE_TIME_FORMAT
                            )
                            if action_time
                            else None
                        ),
                        "updatedBy": updated_by,
                    }
                    if (
                        payee_request_count_map is not None
                        and app_req.get("approver") in valid_timeline_approvers
                    ):
                        approver_data_dict["count"] = payee_request_count_map.get(
                            app_req.get("approval_request_id"), {}
                        )
                    stage_object["approverData"].append(approver_data_dict)
            timeline_data.append(stage_object)
            if stage_object["status"] == APPROVAL_WORKFLOW_STATUS.REVOKED.value:
                timeline_data.append(
                    {
                        "status": APPROVAL_WORKFLOW_STATUS.REVOKED_TITLE.value,
                        "title": "Approval request revoked",
                        "actionTime": (
                            ap_intance["knowledge_begin_date"]
                            .astimezone(user_timezone)
                            .strftime(DATE_TIME_FORMAT)
                            if ap_intance.get("knowledge_begin_date")
                            else None
                        ),
                        "updatedBy": (
                            instance_updated_by_user.get("first_name", "")
                            + " "
                            + instance_updated_by_user.get("last_name", "")
                            if updated_by
                            else ""
                        ),
                        "approverData": [],
                        "entityType": ap_intance.get("entity_type"),
                    }
                )
        multiple_approval_instances[instance_id] = timeline_data
    # This will be used for cpq approvals -> when a quote has multiple approval instances
    # if is_quote_approval:
    #     return {"data": multiple_approval_instances}
    return {"data": timeline_data}


@transaction.atomic
def update_all_request_status(
    client_id,
    request_ids,
    kd,
    request_status,
    is_system_action=False,
    logger=None,
    audit=None,
):
    logger = LogWithContext() if logger is None else logger
    logger.info(f"Updating request status to {request_status}")
    all_approval_request = ApprovalRequestsAccessor(
        client_id
    ).get_bulk_request_by_ids_queryset(request_ids)
    if all_approval_request:
        ApprovalRequestsAccessor(client_id).invalidate_request(request_ids, kd)
        for request in all_approval_request:
            if request.status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
                raise RequestNotInRequestedStageException(
                    request.approver, str(request.approval_request_id), request.status
                )
            approval_request = clone_object(request, kd, audit)
            approval_request.status = request_status
            approval_request.completed_time = kd
            approval_request.is_system_action = is_system_action

    return ApprovalRequestsAccessor(client_id).bulk_persist(all_approval_request)


@transaction.atomic
def update_all_request_object_status(
    client_id,
    original_request_ids,
    all_request_objects,
    kd,
    comments=None,
    logger=None,
    audit=None,
):
    logger = LogWithContext() if logger is None else logger
    logger.info(f"BEGIN: Updating request status to {status}")
    all_request_ids_invalidate = []
    for request in all_request_objects:
        entity_type = request.entity_type
        request_id = str(request.approval_request_id)
        if (
            entity_type != APPROVAL_ENTITY_TYPES.QUOTE.value
            and request.status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        ):
            raise RequestNotInRequestedStageException(
                request.approver, str(request.approval_request_id), request.status
            )
        all_request_ids_invalidate.append(request_id)
        request = clone_object(request, kd, audit)
        if request_id in original_request_ids:
            request.comments = comments
            request.status = APPROVAL_WORKFLOW_STATUS.REJECTED.value
        else:
            request.comments = None
            request.is_system_action = True
            if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
                request.status = APPROVAL_WORKFLOW_STATUS.ABORTED.value
            else:
                request.status = APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value
        request.completed_time = kd

        request_dict = request.__dict__
        request_dict["client"] = client_id
        approval_request_ser = ApprovalRequestsSerializer(data=request_dict)
        if not approval_request_ser.is_valid():
            logger.error(
                "Approval Request invalid for instance - {}, stage - {}, request-{} , approver - {}, errors - {}".format(
                    request.approval_wf_instance_id,
                    request.stage_instance_id,
                    request_id,
                    request.approver,
                    approval_request_ser.errors,
                )
            )
            raise ApprovalRequestSerializerException(
                request.approver, request.stage_instance_id
            )
    logger.info(
        "END: Updating request. Total requests updates - {}".format(
            len(all_request_objects)
        )
    )
    return ApprovalRequestsAccessor(client_id).bulk_update_request(
        all_request_ids_invalidate, all_request_objects, kd
    )


@transaction.atomic
def update_all_stages_object_status(
    client_id,
    current_stage_ids,
    all_stage_objects,
    kd,
    logger=None,
    audit=None,
):
    logger = LogWithContext() if logger is None else logger
    all_stage_id_invalidate = []
    for stage in all_stage_objects:
        stage_id = str(stage.stage_instance_id)
        all_stage_id_invalidate.append(stage_id)
        stage = clone_object(stage, kd, audit)
        if stage_id in current_stage_ids:
            stage.status = APPROVAL_WORKFLOW_STATUS.REJECTED.value
        else:
            stage.status = APPROVAL_WORKFLOW_STATUS.ABORTED.value
        stage.completed_time = kd

    return ApprovalInstanceStageAccessor(client_id).bulk_update_stage(
        all_stage_id_invalidate, all_stage_objects, kd
    )


def get_completed_and_not_completed_stage_map(
    client_id, all_stage_objects, logger=None
):
    """Get Stage completion status

    This function returns the stage_ids for all completed & incompleted stages of all the stage provided by checking the stage strategy and the number of requests approved for a stage.

    Parameters:
        -  client_id (int)
        -  all_stage_objects (ApprovalInstanceStage object)
        -  logger (logging.Logger)

    @return {"complete_stages": [], "incomplete_stages": []}
    """
    logger = LogWithContext({}) if logger is None else logger
    stage_ids = [str(st.stage_instance_id) for st in list(all_stage_objects)]

    # Getting the count of all the requests for a particular stage
    all_stage_requests_count = ApprovalRequestsAccessor(
        client_id
    ).get_request_count_group_by_stage_id_and_status(stage_ids)
    # Getting the count of all the approved requests for a particular stage
    all_stage_approved_requests_count = ApprovalRequestsAccessor(
        client_id
    ).get_request_count_group_by_stage_id_and_status(
        stage_ids, APPROVAL_WORKFLOW_STATUS.APPROVED.value
    )

    all_stage_requests_count_map = {
        str(st["stage_instance_id"]): st["total"] for st in all_stage_requests_count
    }
    all_stage_approved_requests_count_map = {
        str(st["stage_instance_id"]): st["total"]
        for st in all_stage_approved_requests_count
    }

    complete_stages = []
    incomplete_stages = []
    for stage in all_stage_objects:
        stage_id = str(stage.stage_instance_id)
        # Checking if strategy is anyone and there is even a single approved requests
        if (
            stage.approval_strategy == APPROVAL_STAGE_STRATEGY.ANYONE.value
            and stage_id in all_stage_approved_requests_count_map
            and all_stage_approved_requests_count_map[stage_id] > 0
        ):
            complete_stages.append(stage_id)
        # Checking if strategy is everyone then total requests count and approved requests count should be same
        elif (
            stage.approval_strategy == APPROVAL_STAGE_STRATEGY.EVERYONE.value
            and stage_id in all_stage_requests_count_map
            and stage_id in all_stage_approved_requests_count_map
            and all_stage_requests_count_map[stage_id]
            == all_stage_approved_requests_count_map[stage_id]
        ):
            complete_stages.append(stage_id)
        # In all other conditions, stage is incomplete
        else:
            incomplete_stages.append(stage_id)

    return {"complete_stages": complete_stages, "incomplete_stages": incomplete_stages}


def get_last_stage_and_going_on_stage_map(
    client_id, instance_ids, stage_objects, logger=None
):
    """Get last stages and Ongoing stages

    This function check if the provided instance is at the last stage or new stages are present for that. It receives current stage dict and creates the map of current instacesID to stage order.
    Another map is created for instace ID to max stage order and checks.

    Parameters:
        -  client_id (int)
        -  instance_ids (str)
        -  stage_objects (Dict)
        -  logger (logging.Logger)

    @return {"all_last_stage_instances": [<>], "all_in_progress_stage_instances": []}
    """
    logger = LogWithContext({}) if logger is None else logger

    # Creating instance max order map and current stage order map to check if current stage is last stage of instance
    instance_max_orders = ApprovalInstanceStageAccessor(
        client_id
    ).get_max_stage_order_group_by_instances(instance_ids)
    instance_max_order_map = {
        str(mx_ins["approval_wf_instance_id"]): mx_ins["max_order"]
        for mx_ins in list(instance_max_orders)
    }
    current_instance_max_order_map = {
        str(st["approval_wf_instance_id"]): st["stage_order"]
        for st in list(stage_objects)
    }

    # Separating the instances that are last stages and in-between stages
    all_last_stage_instances = []
    all_in_progress_stage_instances = []

    for inst_id in instance_ids:
        if (
            inst_id in instance_max_order_map
            and inst_id in current_instance_max_order_map
            and current_instance_max_order_map[inst_id]
            == instance_max_order_map[inst_id]
        ):
            all_last_stage_instances.append(inst_id)
        else:
            all_in_progress_stage_instances.append(inst_id)

    return {
        "all_last_stage_instances": all_last_stage_instances,
        "all_in_progress_stage_instances": all_in_progress_stage_instances,
    }


def create_requests_for_all_stage(
    client_id, stage_objects, kd, logger=None, audit=None
):
    logger = LogWithContext({}) if logger is None else logger
    stage_instance_ids = [str(st.stage_instance_id) for st in list(stage_objects)]
    logger.info(f"BEGIN: Creating new requests for stages:{stage_instance_ids}")

    stage_id_approvers_map = {}
    all_instance_ids = []
    for st in stage_objects:
        all_instance_ids.append(str(st.approval_wf_instance_id))
        stage_id_approvers_map[
            (str(st.stage_instance_id), str(st.approval_wf_instance_id))
        ] = st.approvers

    all_instance_ids = list(set(all_instance_ids))

    all_instances = ApprovalInstanceAccessor(client_id).get_bulk_instance_by_ids(
        all_instance_ids,
        projection=["approval_wf_instance_id", "entity_key", "entity_type"],
    )

    all_instances_entity_key_map = {
        str(inst["approval_wf_instance_id"]): inst["entity_key"]
        for inst in all_instances
    }
    all_instances_entity_type_map = {
        str(inst["approval_wf_instance_id"]): inst["entity_type"]
        for inst in all_instances
    }
    serialized_approval_requests = []
    for stage_instance_tuple, approvers in stage_id_approvers_map.items():
        for approver in approvers:
            request_object = construct_request_data(
                client_id,
                stage_instance_tuple[1],
                stage_instance_tuple[0],
                all_instances_entity_key_map[stage_instance_tuple[1]],
                all_instances_entity_type_map[
                    stage_instance_tuple[1]
                ],  # Using entity_type of the instance
                approver,
                kd,
                audit,
            )
            approval_request_ser = ApprovalRequestsSerializer(data=request_object)
            if not approval_request_ser.is_valid():
                logger.error(
                    "Approval Request invalid for instance - {}, stage - {} , approver - {}, errors - {}".format(
                        stage_instance_tuple[1],
                        stage_instance_tuple[0],
                        approver,
                        approval_request_ser.errors,
                    )
                )
                raise ApprovalRequestSerializerException(
                    approver, stage_instance_tuple[0]
                )
            serialized_approval_requests.append(approval_request_ser.validated_data)
    approval_requests = ApprovalRequestsAccessor(
        client_id
    ).bulk_create_approval_requests(serialized_approval_requests)
    logger.info(
        "END: Stage requests creation. Total requests_created - {}".format(
            len(approval_requests)
        )
    )
    return approval_requests


def get_next_stages_and_trigger(
    client_id,
    all_instance_ids,
    kd,
    is_system_action=False,
    client_time_zone=None,
    logger=None,
    audit=None,
    entity_type=APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
):
    # Importing here to avoid circular import

    from everstage_ddd.cpq.approvals import send_quote_approval_creation_notification
    from spm.services.approval_line_items.line_item_services import (
        update_next_stage_requests,
    )

    logger = LogWithContext({}) if logger is None else logger
    kd = kd if kd else timezone.now()

    logger.info(f"Fetching next stages for Instance IDs: {all_instance_ids}")
    all_next_stages = ApprovalInstanceStageAccessor(
        client_id
    ).get_next_stage_group_by_instance(all_instance_ids)
    all_next_stage_ids = []
    next_stage_due_period_map = {}
    next_stage_cool_off_period_map = {}
    logger.info("Creating Due Period & Cool-Off period map")
    for nxt_st in all_next_stages:
        all_next_stage_ids.append(str(nxt_st.stage_instance_id))
        next_stage_due_period_map[str(nxt_st.stage_instance_id)] = (
            nxt_st.stage_template_data.get("due_period", None)
            if nxt_st.stage_template_data
            else None
        )
        next_stage_cool_off_period_map[str(nxt_st.stage_instance_id)] = (
            nxt_st.stage_template_data.get("cool_off_period", None)
            if nxt_st.stage_template_data
            else None
        )

    logger.info(f"Fetched next stages: {all_next_stage_ids}")

    # Resolving due_date and cool_off_date for next stages and setting status to started
    logger.info("Updating all stages to resolve due-date & cool-off date")
    all_new_next_stages = update_stage_model_objects_status(
        client_id,
        all_next_stages,
        kd,
        status=APPROVAL_WORKFLOW_STATUS.STARTED.value,
        due_period_map=next_stage_due_period_map,
        cool_off_period_map=next_stage_cool_off_period_map,
        is_system_action=is_system_action,
        set_initiated_time=True,
        set_requested_time=False,
        client_time_zone=client_time_zone,
        additional_details=audit,
    )

    next_stage_cool_off_date_map = {}
    next_stage_approval_trigger_map = {}
    for nxt_st in all_new_next_stages:
        stage_id = str(nxt_st.stage_instance_id)
        next_stage_cool_off_date_map[stage_id] = nxt_st.cool_off_date
        next_stage_approval_trigger_map[stage_id] = nxt_st.approval_trigger

    cool_off_map = get_cool_off_stages_map(
        all_next_stage_ids, next_stage_cool_off_date_map, kd
    )
    stage_id_cool_off_status_map = cool_off_map["stage_id_cool_off_status_map"]

    approval_trigger_map = get_approval_trigger_passed_stage_map(
        all_next_stage_ids, next_stage_approval_trigger_map, kd
    )
    stage_id_trigger_status_map = approval_trigger_map["stage_id_trigger_status_map"]
    # Checking for all stage that have passed cool-off date and approval-trigger conditions
    next_requested_stages = []
    for next_stage_id in all_next_stage_ids:
        if (
            next_stage_id in stage_id_cool_off_status_map
            and next_stage_id in stage_id_trigger_status_map
            and stage_id_cool_off_status_map[next_stage_id]
            and stage_id_trigger_status_map[next_stage_id]
        ):
            next_requested_stages.append(next_stage_id)

    if next_requested_stages:
        logger.info(f"Ready to Request Stages: {next_requested_stages}")
        next_requested_stage_objects = filter_stage_objects_by_stage_ids(
            all_new_next_stages, next_requested_stages
        )
        stage_instances = update_stage_model_objects_status(
            client_id,
            next_requested_stage_objects,
            kd,
            status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            is_system_action=is_system_action,
            set_initiated_time=False,
            set_requested_time=True,
            additional_details=audit,
        )

        if entity_type not in [
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
            APPROVAL_ENTITY_TYPES.QUOTE.value,
            APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
        ]:
            approval_requests = create_requests_for_all_stage(
                client_id, next_requested_stage_objects, kd, logger, audit
            )
            workflow_instances = ApprovalInstanceAccessor(
                client_id
            ).get_bulk_instance_by_ids_queryset(all_instance_ids)
        else:
            stage_ids = [
                str(st.stage_instance_id) for st in next_requested_stage_objects
            ]
            (workflow_instances, approval_requests) = update_next_stage_requests(
                client_id,
                all_instance_ids,
                stage_ids,
                is_system_action,
                entity_type,
                audit,
            )

        logger.info("Send email notifications to approvers.")
        app_req_entity_type_map = {}
        # approval requests can be empty if the approver has no permission
        # to any of the line items (in case of line item approvals)
        if approval_requests:
            for approval_request in approval_requests:
                if approval_request.entity_type in app_req_entity_type_map:
                    app_req_entity_type_map[approval_request.entity_type].append(
                        approval_request
                    )
                else:
                    app_req_entity_type_map[approval_request.entity_type] = [
                        approval_request
                    ]

            for entity_type, appr_requests in app_req_entity_type_map.items():
                if entity_type in [
                    APPROVAL_ENTITY_TYPES.PAYOUT.value,
                    APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
                ]:
                    send_notification_to_approvers_next_stages(
                        client_id,
                        workflow_instances,
                        stage_instances,
                        appr_requests,
                        next_stage_notification=True,
                        logger=logger,
                    )
                elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                    # send_commission_adj_notification_to_approvers(
                    #     client_id,
                    #     workflow_instances,
                    #     stage_instances,
                    #     approval_requests,
                    # )
                    pass
                elif entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
                    send_quote_approval_creation_notification(
                        client_id,
                        workflow_instances,
                        stage_instances,
                        appr_requests,
                        audit_data=audit,
                    )


def complete_stages_and_trigger_next(
    client_id,
    stage_objects,
    kd,
    is_system_action=False,
    check_strategy=True,
    logger=None,
    audit=None,
    entity_type=APPROVAL_ENTITY_TYPES.PAYOUT.value,
):
    from everstage_ddd.cpq.approvals import send_quote_completion_notification
    from everstage_ddd.cpq.quote import resolve_quote_status

    logger = LogWithContext({}) if logger is None else logger
    stage_objects_dict = [st.__dict__ for st in stage_objects]

    # GET Client Timezone
    client_time_zone = get_client(client_id).time_zone
    logger.info(f"Client Time Zone: {client_time_zone}")

    # Getting all distinct instance ids from all current stages
    all_instance_ids = [
        str(st["approval_wf_instance_id"]) for st in list(stage_objects_dict)
    ]
    all_instance_ids = list(set(all_instance_ids))
    client_email_notification_flag = get_client(client_id).client_notification
    from spm.commission_adjustment_approvals.services.approval_workflow_services import (
        resolve_commission_adjustment_status,
    )

    if check_strategy:
        # Getting all stages with strategy as anyone to withdraw other requests
        all_anyone_strategy_stages = []
        for st in list(stage_objects_dict):
            if st["approval_strategy"] == APPROVAL_STAGE_STRATEGY.ANYONE.value:
                all_anyone_strategy_stages.append(str(st["stage_instance_id"]))

        # Withdrawn approval requests for stage with anyone strategy
        logger.info(
            f"Withdrawing approval requests for stage_id: {all_anyone_strategy_stages}"
        )
        ApprovalRequestsAccessor(client_id).withdraw_stage_requests(
            all_anyone_strategy_stages, kd, is_system_action=True, audit=audit
        )
        if (
            entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value
            and all_anyone_strategy_stages
        ):
            requests = ApprovalRequestsAccessor(
                client_id
            ).get_request_by_stage_id_and_status_object(all_anyone_strategy_stages[0])
            if requests:
                entity_key = requests[0].entity_key
                remove_temp_shared_plan_access(
                    client_id,
                    audit.get("approved_by"),
                    entity_key,
                    requests=requests,
                )

    # Updating status for all stages to completed
    logger.info("Updating status for all stages to completed")
    update_stage_model_objects_status(
        client_id,
        stage_objects,
        kd,
        status=APPROVAL_WORKFLOW_STATUS.COMPLETED.value,
        set_completion_time=True,
        additional_details=audit,
        is_system_action=is_system_action,
    )

    # Checkinf if current stage is last stage of instance or next stage is present
    last_stage_and_going_on_stage_map = get_last_stage_and_going_on_stage_map(
        client_id, all_instance_ids, stage_objects_dict
    )

    all_last_stage_instances = last_stage_and_going_on_stage_map[
        "all_last_stage_instances"
    ]
    all_in_progress_stage_instances = last_stage_and_going_on_stage_map[
        "all_in_progress_stage_instances"
    ]

    if all_last_stage_instances:
        logger.info(
            f"Completing Approval Instances for ids: {all_last_stage_instances}"
        )
        ai_acc = ApprovalInstanceAccessor(client_id)

        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
        ]:
            unique_entity_keys = ai_acc.get_unique_entity_keys_for_instance_ids(
                all_last_stage_instances
            )
            create_payout_status_changes_entries_for_approvals_actions(
                client_id=client_id,
                entity_key_list=unique_entity_keys,
                event_type=PayoutStatusChangesTypes.APPROVAL_APPROVE.value,
            )

        workflow_instances = ai_acc.bulk_update_instance_status(
            all_last_stage_instances,
            APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            kd,
            is_system_action=is_system_action,
            update_completion_time=True,
            additional_details=audit,
        )
        if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
            entity_keys = [instance.entity_key for instance in workflow_instances]
            publish_plan_on_approval(client_id, entity_keys)
        resolve_commission_adjustment_status(
            client_id,
            instance_ids=all_last_stage_instances,
            approval_status=APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            audit_data=audit,
        )
        # update quote status to approved for cpq
        resolve_quote_status(client_id, all_last_stage_instances)

        if client_email_notification_flag:
            if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
                logger.info(
                    "BEGIN: Sending notification to users for completion of instance"
                )
                send_quote_completion_notification(client_id, workflow_instances)
                logger.info(
                    "END: Sending notification to users for completion of instance"
                )
            elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                logger.info(
                    f"BEGIN: Send commission plan approval notification to users when instance is approved for {client_id}"
                )

                send_plan_approval_completion_notification(
                    client_id, workflow_instances
                )
                logger.info(
                    f"END: Send commission plan approval notification to users when instance is approved for {client_id}"
                )
            else:
                logger.info(
                    "BEGIN: Send notification to users when instance is approved"
                )
                send_approve_request_email(
                    client_id,
                    workflow_instances,
                    logger=logger,
                )
                logger.info("END: Send notification to users when instance is approved")

    if all_in_progress_stage_instances:
        get_next_stages_and_trigger(
            client_id,
            all_in_progress_stage_instances,
            kd,
            client_time_zone=client_time_zone,
            entity_type=entity_type,
            logger=logger,
            audit=audit,
        )


@transaction.atomic
def approve_all_requests(
    client_id,
    request_ids,
    knowledge_date=None,
    logger=None,
    audit=None,
    approve_on_request=False,
):
    """Approves Request ID

    This function approves and trigger the next stage for completed stages and  complete the instance for all last stages.

    Parameters:
        -  client_id (int)
        -  request_ids (List[str])
        -  knowledge_date (datetime)
        -  logger (logging.Logger)
        -  audit (Dict)

    @return {"audit_data": {}}
    """
    logger = LogWithContext({}) if logger is None else logger
    knowledge_date = knowledge_date if knowledge_date else timezone.now()
    if request_ids:
        request = ApprovalRequestsAccessor(client_id).get_entity_key_by_request_id(
            request_ids[0]
        )

        entity_type = request["entity_type"]
        entity_key = request["entity_key"]
        if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value and not approve_on_request:
            approver = audit.get("approved_by")
            if approver:
                quote_id = entity_key.split(ENTITY_KEY_DELIMETER)[1]
                req_ids = ApprovalRequestsAccessor(
                    client_id
                ).get_request_ids_by_entity_key(quote_id, approver)
                request_ids = [str(req_id) for req_id in req_ids]

    all_requests = update_all_request_status(
        client_id,
        request_ids,
        knowledge_date,
        request_status=APPROVAL_WORKFLOW_STATUS.APPROVED.value,
        logger=logger,
        audit=audit,
    )
    if all_requests:
        entity_type = all_requests[0].entity_type
        entity_key = all_requests[0].entity_key
        if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
            remove_temp_shared_plan_access(
                client_id, audit.get("approved_by"), entity_key, requests=all_requests
            )

    if all_requests:
        all_stages_id = [
            str(request.stage_instance_id) for request in list(all_requests)
        ]
        all_stages_id = list(set(all_stages_id))
        logger.info(f"All Distinct Stage ID: {all_stages_id}")
        all_stage_objects = ApprovalInstanceStageAccessor(
            client_id
        ).get_bulk_instance_stage_by_ids_queryset(all_stages_id)

        stage_completion_map = get_completed_and_not_completed_stage_map(
            client_id, all_stage_objects, logger
        )
        logger.info(f"All Stages Completion Map: {stage_completion_map}")
        if (
            "complete_stages" in stage_completion_map
            and stage_completion_map["complete_stages"]
        ):
            completed_stage_objects = filter_stage_objects_by_stage_ids(
                all_stage_objects, stage_completion_map["complete_stages"]
            )
            complete_stages_and_trigger_next(
                client_id,
                completed_stage_objects,
                knowledge_date,
                logger=logger,
                audit=audit,
                entity_type=all_requests[0].entity_type,
            )

        if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
            return audit

        audit_data = {}
        if audit:
            audit_data = {
                "approval_type": all_requests[0].entity_type,
                "payees": [
                    rq.entity_key.split(ENTITY_KEY_DELIMETER)[0] for rq in all_requests
                ],
            }
        return {"audit_data": audit_data}
    else:
        logger.error("Exception while fetching current requests")
        raise Exception("Error while fetching current requests")


@transaction.atomic
def reject_all_requests(
    client_id, request_ids, knowledge_date=None, comments=None, logger=None, audit=None
):
    """Reject Request ID

    This function accepts all request IDs, reject current requests, stage & instance and abort the future stages.

    Parameters:
        -  client_id (int)
        -  request_ids (List[str])
        -  comments ()
        -  knowledge_date (datetime)
        -  logger (logging.Logger)
        -  audit (Dict)

    @return {"audit_data": {}}
    """
    logger = (
        LogWithContext({"approval_request_id": request_ids})
        if logger is None
        else logger
    )
    knowledge_date = knowledge_date if knowledge_date else timezone.now()

    logger.info("Updating all requests status to rejected")

    all_requests = ApprovalRequestsAccessor(client_id).get_bulk_request_by_ids_queryset(
        request_ids
    )

    client_email_notification_flag = get_client(client_id).client_notification
    from everstage_ddd.cpq.approvals import send_quote_reject_notification
    from everstage_ddd.cpq.quote import resolve_quote_status
    from spm.commission_adjustment_approvals.services.approval_workflow_services import (
        resolve_commission_adjustment_status,
    )

    if all_requests:
        all_stages_ids = []
        all_instance_ids = []
        instances_reject_users_map = {}
        entity_type = all_requests[0].entity_type
        for request in list(all_requests):
            all_stages_ids.append(str(request.stage_instance_id))
            all_instance_ids.append(str(request.approval_wf_instance_id))
            instances_reject_users_map.update(
                {str(request.approval_wf_instance_id): str(request.approver)}
            )

        all_stages_ids = list(set(all_stages_ids))
        all_instance_ids = list(set(all_instance_ids))

        all_auto_reject_requests = ApprovalRequestsAccessor(
            client_id
        ).get_request_by_stage_ids_queryobject(
            all_stages_ids, request_ids, status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        )

        update_all_request_object_status(
            client_id,
            request_ids,
            all_auto_reject_requests + all_requests,
            knowledge_date,
            comments=comments,
            logger=logger,
            audit=audit,
        )

        if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
            entity_key = all_requests[0].entity_key
            remove_temp_shared_plan_access(
                client_id,
                audit.get("updated_by"),
                entity_key,
                requests=all_auto_reject_requests + all_requests,
            )

        if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
            eagerly_created_requests = ApprovalRequestsAccessor(
                client_id
            ).get_request_by_instance_ids_queryobject(
                all_instance_ids,
                request_ids,
                status=APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
            )
            update_all_request_object_status(
                client_id,
                request_ids,
                eagerly_created_requests,
                knowledge_date,
                comments=comments,
                logger=logger,
                audit=audit,
            )
        all_current_stages = ApprovalInstanceStageAccessor(
            client_id
        ).get_bulk_instance_stage_by_ids_queryset(all_stages_ids)
        all_not_started_stages = ApprovalInstanceStageAccessor(
            client_id
        ).get_instance_stage_by_instance_id_queryobject(
            all_instance_ids, APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value
        )

        logger.info(f"Rejecting Approval Stages for ids: {all_stages_ids}")
        update_all_stages_object_status(
            client_id,
            all_stages_ids,
            all_current_stages + all_not_started_stages,
            knowledge_date,
            logger=logger,
            audit=audit,
        )

        logger.info(f"Rejecting Approval Instances for ids: {all_instance_ids}")
        ai_acc = ApprovalInstanceAccessor(client_id)

        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
        ]:
            unique_entity_keys = ai_acc.get_unique_entity_keys_for_instance_ids(
                all_instance_ids
            )
            create_payout_status_changes_entries_for_approvals_actions(
                client_id=client_id,
                entity_key_list=unique_entity_keys,
                event_type=PayoutStatusChangesTypes.APPROVAL_REJECT.value,
            )

        workflow_instances = ai_acc.bulk_update_instance_status(
            all_instance_ids,
            APPROVAL_WORKFLOW_STATUS.REJECTED.value,
            knowledge_date,
            update_completion_time=True,
            additional_details=audit,
        )
        resolve_commission_adjustment_status(
            client_id,
            instance_ids=all_instance_ids,
            approval_status=APPROVAL_WORKFLOW_STATUS.REJECTED.value,
            audit_data=audit,
        )
        # update quote status to approved for cpq
        resolve_quote_status(client_id, all_instance_ids)

        if client_email_notification_flag:
            logger.info("Send email notifications for rejected requests.")
            # rejected_stage_instances = filter_objects_by_key(stage_instances, all_stages_ids, "stage_instance_id")
            entity_type_instance_map = {}
            for instance in workflow_instances:
                if instance.entity_type in entity_type_instance_map:
                    entity_type_instance_map[instance.entity_type].append(instance)
                else:
                    entity_type_instance_map[instance.entity_type] = [instance]
            for entity_type, instances in entity_type_instance_map.items():
                if entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
                    send_quote_reject_notification(
                        client_id, instances, comments, instances_reject_users_map
                    )
                elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                    send_plan_approval_rejection_notification(
                        client_id, instances, comments, instances_reject_users_map
                    )
                else:
                    send_reject_request_email(
                        client_id,
                        entity_type,
                        instances,
                        comments,
                        instances_reject_users_map=instances_reject_users_map,
                        logger=logger,
                    )

        audit_data = {}
        if audit:
            audit_data = {
                "approval_type": all_requests[0].entity_type,
                "payees": [
                    rq.entity_key.split(ENTITY_KEY_DELIMETER)[0] for rq in all_requests
                ],
            }
        return {"audit_data": audit_data}
    else:
        logger.error("Exception while fetching current requests")
        raise Exception("Error while fetching current requests")


def approve_request(
    client_id, all_request_ids, audit, logger, approve_on_request=False
):
    logger.info(f"BEGIN: Approving request, id - {all_request_ids}")
    try:
        time = timezone.now()

        res = approve_all_requests(
            client_id, all_request_ids, time, logger, audit, approve_on_request
        )
        ###################### audit log #####################
        event_type_code = EVENT["APPROVE_REQUEST-APPROVALS"]["code"]
        event_key = uuid4()
        summary = "Request Approved"
        updated_by = audit["updated_by"]
        updated_at = time
        ######################################################
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            res.get("audit_data", []),
        )

        analytics_data = {
            "user_id": updated_by,
            "event_name": SegmentEvents.APPROVE_REQUEST.value,
            "event_properties": {
                SegmentProperties.APPROVAL_REQUEST_ID.value: str(all_request_ids),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)

        logger.info("END: Approving request")
    except StageInstanceSerializerException as stage_instance_excep:
        raise Exception(
            "Error while approving request for payee - {} & period - {}".format(
                stage_instance_excep.instance_email, stage_instance_excep.stage_name
            )
        ) from stage_instance_excep
    except ApprovalRequestSerializerException as req_excep:
        raise Exception(
            "Error while creating request for approver -  {} and StageID - {}".format(
                req_excep.instance_email, req_excep.stage_name
            )
        ) from req_excep
    except RequestNotInRequestedStageException as req_not_excep:
        logger.error(f"RequestID {req_not_excep.request_id} is not in requested status")
        raise Exception(
            "This request has been already {}.".format(req_not_excep.current_status)
        ) from req_not_excep
    except Exception as exc:
        raise Exception(
            "An error occurred while approve this request. Please try again."
        ) from exc


def initiate_bulk_approve_task(client_id, request_data, audit, created_by, entity_type):
    is_approve_all = request_data.get("select_all", False)
    select_all_filters = request_data.get("filters", {})
    from spm.commission_adjustment_approvals.services.approval_workflow_services import (
        get_comm_adj_approval_requests,
    )

    if is_approve_all and select_all_filters:
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            all_request_data = get_approval_request_data(
                client_id=client_id,
                approver_email=created_by,
                period=select_all_filters.get("period"),
                req_status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                search_term=select_all_filters.get("search_term", ""),
                only_request_ids=True,
            )
            all_request_ids = [
                str(ar["approval_request_id"]) for ar in all_request_data["requests"]
            ]
        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            all_request_data = get_comm_adj_approval_requests(
                client_id=client_id,
                approver_email=created_by,
                period=select_all_filters.get("period"),
                req_status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                search_term=select_all_filters.get("search_term", ""),
                limit=None,
                offset=None,
            )
            all_request_ids = [
                str(request_data["approval_request_id"])
                for request_data in all_request_data["adjustments_data"]
            ]

    else:
        all_request_ids = request_data["request_id"]

    task_id = AsyncTaskService(
        client_id=client_id,
        created_by=created_by,
        task=AsyncTaskConfig.BULK_APPROVE_REQUEST,
    ).run_task(
        params={
            "client_id": client_id,
            "all_request_ids": all_request_ids,
            "created_by": created_by,
            "audit": audit,
        },
        force_run=True,
    )

    analytics_data = {
        "user_id": created_by,
        "event_name": SegmentEvents.BULK_APPROVE_REQUEST.value,
        "event_properties": {
            SegmentProperties.NUM_OF_APPROVAL_REQUEST.value: len(all_request_ids)
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)

    return task_id


def reject_request(client_id, request_data, audit, logger):
    try:
        logger.info(f"BEGIN: Rejecting request, id - {request_data['request_id']}")
        time = timezone.now()
        request_id = request_data["request_id"]
        comments = request_data.get("comments", None)
        logger.info(f"BEGIN: Rejecting request, id - {request_id}")

        if not comments:
            raise Exception("Please provide comment!")
        res = reject_all_requests(client_id, request_id, time, comments, logger, audit)

        ###################### audit log #####################
        event_type_code = EVENT["REJECT_REQUEST-APPROVALS"]["code"]
        event_key = uuid4()
        summary = "Request Rejected"
        updated_by = audit["updated_by"]
        updated_at = time
        ######################################################

        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            res.get("audit_data", []),
        )

        analytics_data = {
            "user_id": updated_by,
            "event_name": SegmentEvents.REJECT_APPROVAL_REQUEST.value,
            "event_properties": {
                SegmentProperties.APPROVAL_REQUEST_ID.value: request_id,
                SegmentProperties.COMMENTS.value: str(comments),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)

        logger.info(f"END: Rejecting request, id - {request_id}")
    except ApprovalRequestSerializerException as req_excep:
        raise Exception(
            "Error while rejecting request for approver -  {} and StageID - {}".format(
                req_excep.instance_email, req_excep.stage_name
            )
        ) from req_excep
    except RequestNotInRequestedStageException as req_not_excep:
        logger.error(f"RequestID {req_not_excep.request_id} is not in requested status")
        raise Exception(
            "This request has been already {}.".format(req_not_excep.current_status)
        ) from req_not_excep
    except Exception as exc:
        raise Exception(
            "An error occurred while rejecting this request. Please try again."
        ) from exc


def initiate_bulk_reject_task(
    client_id,
    request_data,
    audit,
    created_by,
    entity_type=APPROVAL_ENTITY_TYPES.PAYOUT.value,
):
    is_approve_all = request_data.get("select_all", False)
    select_all_filters = request_data.get("filters", {})
    from spm.commission_adjustment_approvals.services.approval_workflow_services import (
        get_comm_adj_approval_requests,
    )

    if is_approve_all and select_all_filters:
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            all_request_data = get_approval_request_data(
                client_id=client_id,
                approver_email=created_by,
                period=select_all_filters.get("period"),
                req_status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                search_term=select_all_filters.get("search_term", ""),
                only_request_ids=True,
            )
            all_request_ids = [
                str(ar["approval_request_id"]) for ar in all_request_data["requests"]
            ]
        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            all_request_data = get_comm_adj_approval_requests(
                client_id=client_id,
                approver_email=created_by,
                period=select_all_filters.get("period"),
                req_status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                search_term=select_all_filters.get("search_term", ""),
                limit=None,
                offset=None,
            )
            all_request_ids = [
                str(request_data["approval_request_id"])
                for request_data in all_request_data["adjustments_data"]
            ]

    else:
        all_request_ids = request_data["request_id"]

    request_data["request_id"] = all_request_ids
    task_id = AsyncTaskService(
        client_id=client_id,
        created_by=created_by,
        task=AsyncTaskConfig.BULK_REJECT_REQUEST,
    ).run_task(
        params={
            "client_id": client_id,
            "request_data": request_data,
            "created_by": created_by,
            "audit": audit,
        },
        force_run=True,
    )

    analytics_data = {
        "user_id": created_by,
        "event_name": SegmentEvents.BULK_REJECT_REQUEST.value,
        "event_properties": {
            SegmentProperties.NUM_OF_APPROVAL_REQUEST.value: len(all_request_ids)
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)

    return task_id


@transaction.atomic
def withdraw_stage_and_move_to_next(
    client_id, request_data, audit, logger=None, knowledge_date=None
):
    """Withdraw Stage Instance

    This function withdraws the current stage and trigger the next if new stage is present.
    Instance Update Conditions id current stage is the last stage of instance:
    1: If all stages are withdrawn, set Instance status to withdrawn
    2: If any of the stage is approved, set Instance status to approved.

    Parameters:
        -  client_id (int)
        -  request_data (Dict)
        -  audit (Dict)
        -  logger (logging.Logger)
        -  knowledge_date (datetime)

    @return None
    """
    try:
        from spm.commission_adjustment_approvals.services.approval_workflow_services import (
            resolve_commission_adjustment_status,
        )
        from spm.services.approval_line_items.line_item_services import (
            withdraw_sub_requests_for_stage_ids,
        )

        knowledge_date = knowledge_date if knowledge_date else timezone.now()
        logger = LogWithContext() if logger is None else logger
        stage_id = request_data.get("stage_id")
        # GET Client Timezone
        client_time_zone = get_client(client_id).time_zone
        current_stage = ApprovalInstanceStageAccessor(
            client_id
        ).get_bulk_instance_stage_by_ids_queryset(stage_instance_ids=[stage_id])
        if current_stage[0].status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
            raise InstanceStageStatusException(current_stage[0].stage_name)

        logger.info(f"Withdrawing request for Stage ID: {stage_id}")

        ApprovalRequestsAccessor(client_id).withdraw_stage_requests(
            stage_id, knowledge_date, is_system_action=False, audit=audit
        )
        entity_type = request_data.get("entity_type", None)
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
            withdraw_sub_requests_for_stage_ids(client_id, [stage_id], audit, logger)

        logger.info(f"Withdrawing current stage for Stage ID: {stage_id}")
        current_stage = update_stage_model_objects_status(
            client_id,
            current_stage,
            knowledge_date,
            status=APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
            set_completion_time=True,
            additional_details=audit,
        )

        if current_stage:
            current_stage_dict = current_stage[0].__dict__

            current_instance_id = str(current_stage_dict["approval_wf_instance_id"])
            last_stage_and_going_on_stage_map = get_last_stage_and_going_on_stage_map(
                client_id, [current_instance_id], [current_stage_dict]
            )

            all_last_stage_instances = last_stage_and_going_on_stage_map[
                "all_last_stage_instances"
            ]
            all_in_progress_stage_instances = last_stage_and_going_on_stage_map[
                "all_in_progress_stage_instances"
            ]

            if all_last_stage_instances:
                # if all stages are withdrawn, withdraw current instance also
                all_stage_status = get_all_stage_status_count(
                    client_id,
                    current_instance_id,
                    status=APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                )
                all_instance_id_stage_status_count_map = all_stage_status[
                    "all_instance_id_stage_status_count_map"
                ]
                logger.info(
                    "all_instance_id_stage_status_count_map",
                    all_instance_id_stage_status_count_map,
                )
                if all_instance_id_stage_status_count_map[current_instance_id]:
                    # All Stages are in withdrawn status, withdrawing instance
                    logger.info(
                        f"Withdrawing Approval Instances for Instance ID: {current_instance_id}"
                    )
                    ai_acc = ApprovalInstanceAccessor(client_id)

                    if entity_type in [
                        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
                        APPROVAL_ENTITY_TYPES.PAYOUT.value,
                    ]:
                        unique_entity_keys = (
                            ai_acc.get_unique_entity_keys_for_instance_ids(
                                all_last_stage_instances
                            )
                        )
                        create_payout_status_changes_entries_for_approvals_actions(
                            client_id=client_id,
                            entity_key_list=unique_entity_keys,
                            event_type=PayoutStatusChangesTypes.APPROVAL_WITHDRAW.value,
                        )

                    ai_acc.bulk_update_instance_status(
                        all_last_stage_instances,
                        APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                        knowledge_date,
                        update_completion_time=True,
                        additional_details=audit,
                    )
                    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                        resolve_commission_adjustment_status(
                            client_id,
                            instance_ids=all_last_stage_instances,
                            approval_status=APPROVAL_WORKFLOW_STATUS.WITHDRAWN.value,
                            audit_data=audit,
                        )
                else:
                    # All Stages are not in withdrawn status, compelteing instance
                    logger.info(
                        f"Approving Approval Instances for Instance ID: {current_instance_id}"
                    )
                    ai_acc = ApprovalInstanceAccessor(client_id)

                    if entity_type in [
                        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
                        APPROVAL_ENTITY_TYPES.PAYOUT.value,
                    ]:
                        unique_entity_keys = (
                            ai_acc.get_unique_entity_keys_for_instance_ids(
                                all_last_stage_instances
                            )
                        )
                        create_payout_status_changes_entries_for_approvals_actions(
                            client_id=client_id,
                            entity_key_list=unique_entity_keys,
                            event_type=PayoutStatusChangesTypes.APPROVAL_APPROVE.value,
                        )

                    ai_acc.bulk_update_instance_status(
                        all_last_stage_instances,
                        APPROVAL_WORKFLOW_STATUS.APPROVED.value,
                        knowledge_date,
                        update_completion_time=True,
                        additional_details=audit,
                    )
                    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                        resolve_commission_adjustment_status(
                            client_id,
                            instance_ids=all_last_stage_instances,
                            approval_status=APPROVAL_WORKFLOW_STATUS.APPROVED.value,
                            audit_data=audit,
                        )
                    elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                        entity_keys = ApprovalInstanceAccessor(
                            client_id
                        ).get_unique_entity_keys_for_instance_ids(
                            all_last_stage_instances
                        )
                        publish_plan_on_approval(client_id, entity_keys)
            if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                requests = ApprovalRequestsAccessor(
                    client_id
                ).get_request_by_stage_id_and_status_object(stage_id)
                if requests:
                    entity_key = requests[0].entity_key
                    approvers = [request.approver for request in requests]
                    remove_temp_shared_plan_access(
                        client_id,
                        audit.get("updated_by"),
                        entity_key,
                        approvers=approvers,
                    )
            if all_in_progress_stage_instances:
                logger.info(
                    f"Triggering next stage for Instance ID: {current_instance_id}"
                )
                get_next_stages_and_trigger(
                    client_id,
                    all_in_progress_stage_instances,
                    knowledge_date,
                    client_time_zone=client_time_zone,
                    logger=logger,
                    audit=audit,
                    entity_type=entity_type,
                )

            ###################### audit log #####################
            event_type_code = EVENT["WITHDRAW_STAGE-APPROVALS"]["code"]
            event_key = stage_id
            summary = f"Withdraw stage: {current_stage_dict.get('stage_name')}"
            updated_by = audit["updated_by"]
            updated_at = knowledge_date
            ######################################################

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                [],
            )

            analytics_data = {
                "user_id": updated_by,
                "event_name": SegmentEvents.WITHDRAW_STAGE_APPROVAL.value,
                "event_properties": {
                    SegmentProperties.APPROVAL_STAGE_NAME.value: current_stage_dict.get(
                        "stage_name"
                    ),
                    SegmentProperties.APPROVAL_STAGE_ID.value: stage_id,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
        else:
            raise Exception("Error while fetching current stage")
    except InstanceStageStatusException as stage_instance_excep:
        raise Exception(
            f"{stage_instance_excep.stage_name} is not in requested state!"
        ) from stage_instance_excep
    except Exception as exc:
        raise Exception(
            "An error occurred while withdrawing the stage. Please try again."
        ) from exc


@transaction.atomic
def add_approver_to_stage(
    client_id, request_data, audit, logger=None, knowledge_date=None
):
    """Add Approvers to Stage

    This function resolves the approver from employee_object, add approvers to stage and create new Approval Requests for newly added approvers.

    Parameters:
        -  client_id (int)
        -  request_data (Dict)
        -  audit (Dict)
        -  logger (logging.Logger)
        -  knowledge_date (datetime)

    @return None
    """
    from spm.services.approval_line_items.line_item_services import (
        create_sub_requests_data_for_approvers,
    )

    try:
        knowledge_date = knowledge_date if knowledge_date else timezone.now()
        logger = LogWithContext() if logger is None else logger
        employees_object = request_data.get("employees_object")
        stage_id = request_data.get("stage_id")

        current_stage = ApprovalInstanceStageAccessor(
            client_id
        ).get_bulk_instance_stage_by_ids_queryset(stage_instance_ids=[stage_id])
        if current_stage:
            current_stage = current_stage[0]

            if current_stage.status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
                raise InstanceStageStatusException(current_stage.stage_name)

            current_instance = ApprovalInstanceAccessor(client_id).get_instance_by_id(
                current_stage.approval_wf_instance_id,
                projection=["entity_key", "entity_type"],
            )
            entity_key = current_instance.get("entity_key", "")
            entity_type = current_instance.get(
                "entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value
            )
            if entity_type in [
                APPROVAL_ENTITY_TYPES.PAYOUT.value,
                APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
            ]:
                payee_email = entity_key.split(ENTITY_KEY_DELIMETER)[0]
            else:
                adjustment_entry: List[CommissionAdjustmentStatus] = (
                    CommissionAdjustmentStatusAccessor(
                        client_id
                    ).get_adjustments_by_adjustment_ids([entity_key])
                )
                payee_email: str = (
                    adjustment_entry[0].payee_email_id if adjustment_entry else ""
                )

            previous_approvers = []

            if current_stage.stage_order > 1:
                # checking if current stage is not the first stage then getting previous approvers
                logger.info(
                    f"Getting previous Stage for Approval Instance: {current_stage.approval_wf_instance_id} and Stage Order: {current_stage.stage_order-1}"
                )
                previous_stage = ApprovalInstanceStageAccessor(
                    client_id
                ).get_previous_stage_by_instance_id_and_order(
                    instance_id=current_stage.approval_wf_instance_id,
                    stage_order=current_stage.stage_order - 1,
                    projection=["approvers"],
                )
                previous_approvers = (
                    previous_stage["approvers"] if previous_stage else []
                )

            employee_manager_map = get_all_employee_manager_map(client_id)
            current_approvers = current_stage.approvers
            resolved_approvers = get_resolved_employees(
                client_id,
                employees_object,
                payee_email,
                employee_manager_map,
                previous_approvers,
            )
            new_approvers = list(set(resolved_approvers) - set(current_approvers))
            all_approvers = list(set(resolved_approvers + current_approvers))
            logger.info(f"New approvers: {new_approvers}")
            current_stage = update_stage_model_objects_status(
                client_id,
                [current_stage],
                knowledge_date,
                approvers=all_approvers,
                additional_details=audit,
            )
            if new_approvers:
                current_stage = current_stage[0]
                approval_requests = create_requests_for_approvers(
                    client_id,
                    new_approvers,
                    current_stage.stage_instance_id,
                    current_stage.approval_wf_instance_id,
                    entity_key,
                    entity_type,
                    knowledge_date,
                    logger,
                    audit,
                )
                if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                    create_sub_requests_data_for_approvers(
                        client_id,
                        approval_requests,
                        audit=audit,
                    )
                workflow_instances = ApprovalInstanceAccessor(
                    client_id
                ).get_bulk_instance_by_ids_queryset(
                    current_stage.approval_wf_instance_id
                )

                if (
                    entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value
                    and approval_requests
                ):
                    entity_key = approval_requests[0].entity_key
                    approvers = [request.approver for request in approval_requests]
                    handle_required_permissions(
                        client_id,
                        audit.get("updated_by"),
                        entity_key,
                        approvers,
                        audit,
                    )
                logger.info("Send email notifications to approvers.")
                if not entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                    send_notification_to_approvers_next_stages(
                        client_id,
                        workflow_instances,
                        [current_stage],
                        approval_requests,
                        next_stage_notification=True,
                        logger=logger,
                    )

                ###################### audit log #####################
                event_type_code = EVENT["ADD_APPROVER_STAGE_ADD-APPROVERS"]["code"]
                event_key = stage_id
                summary = f"Add Approvers to Stage: {current_stage.stage_name}"
                updated_by = audit["updated_by"]
                updated_at = knowledge_date
                audit_data = {"approvers": new_approvers}
                ######################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )

                analytics_data = {
                    "user_id": updated_by,
                    "event_name": SegmentEvents.ADD_APPROVER_TO_STAGE.value,
                    "event_properties": {
                        SegmentProperties.APPROVAL_STAGE_NAME.value: current_stage.stage_name,
                        SegmentProperties.APPROVAL_STAGE_ID.value: stage_id,
                        SegmentProperties.APPROVERS.value: str(new_approvers),
                        SegmentProperties.APPROVAL_INSTANCE_TYPE.value: entity_type,
                    },
                }
                analytics = CoreAnalytics(analyser_type="segment")
                analytics.send_analytics(analytics_data)
            else:
                raise Exception("Approver already present.")
        else:
            raise Exception("Error while fetching current stage")
    except InstanceStageStatusException as stage_instance_excep:
        raise Exception(
            f"{stage_instance_excep.stage_name} is not in requested state!"
        ) from stage_instance_excep
    except Exception as exc:
        raise Exception(
            "An error occurred while adding approver to the stage, please try again."
        ) from exc


@transaction.atomic
def change_stage_due_date(client_id, request_data, user_email, audit, logger=None):
    """Change Stage Due Date

    This function accepts the new due date and check if that new due date in employee's timezone is already passed in client's timezone.
    If not passed then updates the new due date for stage.

    Parameters:
        -  client_id (int)
        -  request_data (Dict)
        -  user_email(str)
        -  audit (Dict)
        -  logger (logging.Logger)

    @return None
    """
    try:
        logger = LogWithContext() if logger is None else logger
        DATE_TIME_FORMAT = "%d %b %Y, %H:%M"
        knowledge_date = timezone.now()
        stage_id = request_data.get("stage_id")
        new_due_date = request_data.get("due_date")

        current_stage = ApprovalInstanceStageAccessor(
            client_id
        ).get_bulk_instance_stage_by_ids_queryset(stage_instance_ids=[stage_id])
        if current_stage:
            if current_stage[0].status != APPROVAL_WORKFLOW_STATUS.REQUESTED.value:
                raise InstanceStageStatusException(current_stage[0].stage_name)

            client_details = get_client(client_id)
            client_time_zone = (
                client_details.time_zone if client_details.time_zone else "UTC"
            )
            employee_details = EmployeeAccessor(client_id).get_employee(
                user_email, projection=["time_zone"]
            )
            employee_time_zone = (
                employee_details["time_zone"] if employee_details else "UTC"
            )
            employee_time_zone = employee_time_zone if employee_time_zone else "UTC"

            user_due_date_eod_utc = date_string_to_time_zone_aware_eod_utc(
                new_due_date, employee_time_zone
            )
            current_time_client_timezone_utc = datetime_to_timezone_utc(
                knowledge_date, client_time_zone
            )
            logger.info(
                f"Converted current time in Client's Timezone: {current_time_client_timezone_utc} and Employee's Timezone: {user_due_date_eod_utc}"
            )
            if user_due_date_eod_utc >= current_time_client_timezone_utc:
                new_stage_object = clone_object(current_stage[0], knowledge_date, audit)
                new_stage_object.due_date = user_due_date_eod_utc
                ApprovalInstanceStageAccessor(client_id).update_stage(
                    stage_id, new_stage_object, knowledge_date
                )
            else:
                raise DueDatePassedException(user_due_date_eod_utc)

            ###################### audit log #####################
            event_type_code = EVENT["CHANGE_DUE_DATE_STAGE-APPROVALS"]["code"]
            event_key = stage_id
            summary = f"Changed Due Date for Stage: {current_stage[0].stage_name} to {user_due_date_eod_utc.strftime(DATE_TIME_FORMAT)} UTC"
            updated_by = audit["updated_by"]
            updated_at = knowledge_date
            audit_data = []
            ######################################################
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

            analytics_data = {
                "user_id": updated_by,
                "event_name": SegmentEvents.CHANGE_STAGE_DUE_DATE.value,
                "event_properties": {
                    SegmentProperties.APPROVAL_STAGE_NAME.value: current_stage[
                        0
                    ].stage_name,
                    SegmentProperties.APPROVAL_STAGE_ID.value: stage_id,
                    SegmentProperties.STAGE_DUE_DATE.value: str(
                        user_due_date_eod_utc.strftime(DATE_TIME_FORMAT)
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
        else:
            raise Exception("Error while fetching current stage")
    except InstanceStageStatusException as stage_instance_excep:
        raise Exception(
            f"{stage_instance_excep.stage_name} is not in requested state!"
        ) from stage_instance_excep
    except DueDatePassedException as due_exc:
        raise Exception(
            "Due date is already passed in Organization's Timezone"
        ) from due_exc
    except Exception as exc:
        raise Exception(
            "An error occurred while changing the due date. Please try again."
        ) from exc


def parse_amount(amount):
    return str(round(decimal.Decimal(str(amount)), 2))


def send_reminder_to_stage_approvers(client_id, request_data, logger):
    """Send Reminder to Approvers

    This function sends the approve reminder to all the approvers in a stage that are in requested status.

    Parameters:
        -  client_id (int)
        -  request_data (Dict)
        -  user_email(str)
        -  audit (Dict)
        -  logger (logging.Logger)

    @return False if no approvers to remind else True
    """
    from everstage_ddd.cpq.approvals import send_quote_approval_reminder_notification

    reminder_sent = True
    try:
        commission_date = timezone.now()
        stage_id = request_data.get("stage_id")
        logger.info(f"BEGIN: Sending reminder to approvers in Stage ID: {stage_id}")
        stage_instance = ApprovalInstanceStageAccessor(
            client_id
        ).get_instance_stage_by_id(
            stage_instance_id=stage_id,
            projection=[
                "due_date",
                "notes",
                "approval_wf_instance_id",
                "status",
                "stage_name",
            ],
        )
        if (
            stage_instance
            and stage_instance["status"] != APPROVAL_WORKFLOW_STATUS.REQUESTED.value
        ):
            raise InstanceStageStatusException(stage_instance["stage_name"])
        approval_instance_id = (
            stage_instance.get("approval_wf_instance_id") if stage_instance else None
        )
        approval_instance = ApprovalInstanceAccessor(client_id).get_instance_by_id(
            instance_id=approval_instance_id,
            projection=["instance_data", "entity_key", "entity_type"],
        )
        entity_type = approval_instance.get(
            "entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value
        )

        if entity_type in [
            APPROVAL_ENTITY_TYPES.PAYOUT.value,
            APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value,
        ]:
            # this call gets all distinct approval request ids for the given stage_id and status(requested)
            # For payout_line_item entity_type, we need to check if any one of the record in sub_approval_request table is in requested status
            line_item_approval_request_ids = []
            if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                line_item_approval_request_ids = SubApprovalRequestsAccessor(
                    client_id
                ).get_approval_request_ids_by_stage_and_status(
                    stage_id=stage_id,
                    status=ApprovalSubRequestsStatus.Requested().name,
                )
            approval_requests = ApprovalRequestsAccessor(
                client_id
            ).get_request_by_stage_id_status_and_req_id(
                stage_id=stage_id,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                request_ids=line_item_approval_request_ids,
                projection=[
                    "approver",
                    "approval_request_id",
                    "status",
                    "entity_type",
                ],
            )
            sub_requests_count_map = {}
            if not approval_requests:
                reminder_sent = False
                logger.info("END: No approvers to remind.")
            elif stage_instance and approval_instance:
                if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                    approval_req_ids = [
                        req["approval_request_id"] for req in approval_requests
                    ]
                    sub_requests = SubApprovalRequestsAccessor(
                        client_id
                    ).get_sub_requests_by_request_ids(
                        approval_req_ids,
                        status=ApprovalSubRequestsStatus.Requested().name,
                    )
                    sub_requests_count_map = Counter(
                        [sub_request["request_id"] for sub_request in sub_requests]
                    )
                currency_code_symbol_map = {
                    country.currency_code: country.currency_symbol
                    for country in list(
                        CountriesAccessor(client_id).get_all_countries()
                    )
                }
                payee_email = approval_instance["entity_key"].split(
                    ENTITY_KEY_DELIMETER
                )[0]
                all_approvers_to_remind = {
                    rq.get("approver"): rq.get("approval_request_id")
                    for rq in approval_requests
                }
                all_employees = []
                all_employees.append(payee_email)
                all_employees = all_employees + list(all_approvers_to_remind.keys())
                all_employees_details = EmployeeAccessor(
                    client_id
                ).get_all_employee_details(
                    all_employees,
                    projection=[
                        "employee_email_id",
                        "first_name",
                        "last_name",
                        "time_zone",
                        "user_role",
                    ],
                )
                active_employee_emails: List[str] = EmployeeAccessor(
                    client_id
                ).get_all_non_deactivated_users_email_list()
                all_payroll = list(
                    EmployeePayrollAccessor(
                        client_id
                    ).get_all_employees_payroll_for_date_values(
                        commission_date,
                        all_employees,
                        projection=["payout_frequency", "employee_email_id"],
                    )
                )
                employee_email_id_record_map = {
                    employee.get("employee_email_id"): employee
                    for employee in all_employees_details
                }
                employee_email_payroll_map = {
                    payroll.get("employee_email_id"): payroll for payroll in all_payroll
                }

                logger.info(
                    f"No. of approvers to remind: {len(all_approvers_to_remind)}"
                )
                payee_template_data_map = {}

                keywords_to_localize = {"payout": LocalizationTerms.PAYOUT_LC.value}
                localized_keywords = get_localized_words_service(
                    keywords_to_localize, client_id
                )

                for approver in all_approvers_to_remind:
                    if approver not in active_employee_emails:
                        logger.info(
                            f"Approver {approver} is not an active user. Skipping sending reminder email."
                        )
                        continue
                    approval_request_id = (
                        all_approvers_to_remind.get(approver)
                        if all_approvers_to_remind
                        else None
                    )
                    payee_details = employee_email_id_record_map.get(payee_email, {})
                    approver_details = employee_email_id_record_map.get(approver, {})
                    user_timezone = approver_details.get("time_zone", "")
                    user_timezone = (
                        pytz.timezone(user_timezone) if user_timezone else None
                    )
                    instance_data = approval_instance["instance_data"]
                    payout_period = approval_instance["entity_key"].split(
                        ENTITY_KEY_DELIMETER
                    )[1]
                    email_variable_object = {
                        "name": approver_details.get("first_name", "")
                        + " "
                        + approver_details.get("last_name", ""),
                        "payee_name": payee_details.get("first_name", "")
                        + " "
                        + payee_details.get("last_name", ""),
                        "approver_name": approver_details.get("first_name", ""),
                        "payout_period": get_period(payout_period),
                        "payout_amount": get_formatted_amount(
                            str(instance_data["payout"]),
                            currency_code_symbol_map[instance_data["currency"]],
                        ),
                        "has_due_date": True if stage_instance["due_date"] else False,
                        "approval_due_on": (
                            stage_instance["due_date"]
                            .astimezone(user_timezone)
                            .strftime("%b %d, %Y")
                            if stage_instance["due_date"]
                            else ""
                        ),
                        "statement_url": f"{get_site_url()}/{get_email_statement_url(client_id, approver_details, payee_details, employee_email_payroll_map, payout_period)}",
                        "has_additional_message": (
                            True if stage_instance["notes"] else False
                        ),
                        "additional_message": (
                            stage_instance["notes"] if stage_instance["notes"] else ""
                        ),
                        "payout_localized": localized_keywords["payout"],
                        "line_item_approvals_enabled": (
                            True
                            if entity_type
                            == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value
                            else False
                        ),
                    }
                    if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                        email_variable_object["payee_line_item_url"] = (
                            f"{get_site_url()}/approvals/payouts?status=requested&period={payout_period}&email={payee_email}"
                        )
                        email_variable_object["line_item_count"] = (
                            sub_requests_count_map.get(approval_request_id, 0)
                        )

                    payee_template_data_map[approver] = email_variable_object
                    email_variable_object["approval_request_id"] = str(
                        approval_request_id
                    )
                    email_variable_object["client_id"] = approver_details.get(
                        "client_id"
                    )

                    if entity_type != APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                        from slack_everstage.services.commission_services.notification_service import (
                            post_approval_request,
                        )

                        post_approval_request(
                            client_id, approver, email_variable_object
                        )

                    if entity_type != APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                        from ms_teams_everstage.services.commission_services.notification_service import (
                            post_new_approval_on_msteams,
                        )

                        post_new_approval_on_msteams(
                            client_id, approver, email_variable_object
                        )

                if entity_type != APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
                    send_reminder_email(client_id, payee_template_data_map)
                logger.info("END: Sending reminder to approvers.")
            else:
                raise Exception("Exception while fetching approval data")

        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            approval_requests = ApprovalRequestsAccessor(
                client_id
            ).get_request_by_stage_id_and_status_object(
                stage_id=stage_id,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                projection=[
                    "approver",
                    "approval_request_id",
                    "status",
                    "entity_type",
                ],
            )
            send_comm_adj_approval_reminder_email(
                client_id, stage_instance, approval_instance, approval_requests
            )

        elif entity_type == APPROVAL_ENTITY_TYPES.QUOTE.value:
            approval_requests = ApprovalRequestsAccessor(
                client_id
            ).get_request_by_stage_id_and_status_object(
                stage_id=stage_id,
                status=APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                projection=[
                    "approver",
                    "approval_request_id",
                    "status",
                    "entity_type",
                    "stage_instance_id",
                    "request_data",
                ],
            )
            send_quote_approval_reminder_notification(
                client_id, approval_instance, stage_instance, approval_requests
            )

    except InstanceStageStatusException as stage_instance_excep:
        raise Exception(
            f"{stage_instance_excep.stage_name} is not in requested state!"
        ) from stage_instance_excep
    except Exception as exc:
        raise Exception(
            "An error occurred while the sending reminder. Please try again."
        ) from exc

    return reminder_sent


def get_instance_details_for_selected_all(
    client_id, ped, logged_in_user, search_term, filters, logger
):
    """
    Get approval instance data for selected all. The result will be filtered based on
    the data permission of the logged in user (payouts_statements).
    """
    # imported here to avoid circular dependency with tasks.py
    from spm.services.payout_view_services import PayoutsViewData

    data_permission = get_data_permission(
        client_id, logged_in_user, RBACComponent.PAYOUTS_STATEMENTS.value
    )
    if not data_permission:
        raise Exception("You are not authorized to perform this action.")

    if data_permission["type"] != RBAC.ALL_DATA.value:
        valid_emails = get_valid_payee_emails(
            client_id, logged_in_user, data_permission
        )
        filters["email_id_in"] = valid_emails

    instance_details = PayoutsViewData(
        client_id, ped=ped, logger=logger
    ).get_payout_data(
        search_term=search_term,
        filters=filters,
        is_bulk_action="BULK_REQUEST_APPROVALS",
        logged_in_user=logged_in_user,
    )

    return instance_details["data"]


def initiate_create_approval_instances(
    client_id,
    template_id,
    entity_type,
    instance_params,
    created_by,
    template_data=None,
    audit_data=None,
    logger=None,
    is_selected_all=False,
    search_term=None,
    filters=None,
    is_auto_mode: bool = False,
):
    try:
        logger = LogWithContext() if logger is None else logger
        logger.info("BEGIN: Initiate create approval instance.")
        realtime_task_id = str(uuid4())
        try:
            supabase = get_supabase_client()
            supabase.insert(
                {
                    "task_name": AsyncTaskConfig.BULK_CREATE_APPROVAL_INSTANCES.name,
                    "client_id": client_id,
                    "task_id": realtime_task_id,
                    "data": {"status": TaskStatus.PENDING},
                }
            ).execute()
        except Exception:  # pylint: disable=broad-except
            traceback.print_exc()
            logger.error(
                "Cannot add task for BULK_CREATE_APPROVAL_INSTANCES in supabase table"
            )

        # Getting all instance details when is selected all is true
        if is_selected_all:
            filters = filters or {}
            ped = end_of_day(
                make_aware(datetime.strptime(instance_params["date"], "%d-%m-%Y"))
            )
            instance_details = get_instance_details_for_selected_all(
                client_id,
                ped=ped,
                logged_in_user=created_by,
                search_term=search_term,
                filters=filters,
                logger=logger,
            )
            instance_params["instance_details"] = instance_details

        template_id_params_map: Dict[str, Dict[str, Any]] = {}
        if is_auto_mode:
            # Forming template_id_params_map for auto mode after instance_details is fetched
            # To refer about the use and structure of template_id_params_map, refer to the docstring of CreateApprovalInstanceView
            template_id_params_map = resolve_templates_for_payees(instance_params)

        task_id = AsyncTaskService(
            client_id=client_id,
            created_by=created_by,
            task=AsyncTaskConfig.BULK_CREATE_APPROVAL_INSTANCES,
        ).run_task(
            params={
                "client_id": client_id,
                "template_id": template_id,
                "entity_type": entity_type,
                "instance_params": instance_params,
                "created_by": created_by,
                "template_data": template_data,
                "audit_data": audit_data,
                "realtime_id": realtime_task_id,
                "template_id_params_map": template_id_params_map,
            },
            force_run=True,
        )

        analytics_data = {
            "user_id": created_by,
            "event_name": SegmentEvents.BULK_CREATE_APPROVAL_INSTANCE.value,
            "event_properties": {
                SegmentProperties.APPROVAL_INSTANCE_TYPE.value: entity_type
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info("END: Initiate create approval instance.")
        return task_id
    except Exception as exc:
        raise Exception("Bulk instance creation failed.") from exc


def resolve_templates_for_payees(instance_params) -> Dict[str, Dict[str, Any]]:
    """
    Maps template_id to instance_params for auto mode. Mocker for now.

    example:
        MODULO = 3
        template_id_payee_map = {}
        template_ids = ["template_1", "template_2", "template_3"]
        for i in range(len(instance_params["instance_details"])):
            template = template_ids[i % MODULO]
            if template not in template_id_payee_map:
                template_id_payee_map[template] = {
                    "date": instance_params["date"],
                    "instance_details": [instance_params["instance_details"][i]],
                }
            else:
                template_id_payee_map[template]["instance_details"].append(
                    instance_params["instance_details"][i]
                )
        return template_id_payee_map
    """
    return {"template_1": instance_params}


def get_approval_request_data(
    client_id,
    approver_email=None,
    req_status=None,
    period=None,
    search_term=None,
    offset=None,
    limit=None,
    only_request_ids=False,
    entity_type=APPROVAL_ENTITY_TYPES.PAYOUT.value,
):
    """Get Approval Requests Table Data

    Joins Employee, ApprovalInstaceStage & ApprovalInstance table to ApprovalRequest table to get the all the requests data.
    Use only_request_ids parameter to get only the request ids otherwise it returns all data of approval_request

    Parameters:
        -  client_id (int)
        -  approver_email (str)
        -  req_status(str)
        -  period (str)
        -  search_term (str)
        -  offset (int)
        -  limit (int)
        -  only_request_ids (bool)

    @return {"requests": [], "requests_count": Int}
    """

    log_context = {
        "client_id": client_id,
        "approver_email": approver_email,
        "search_term": search_term,
        "request_status": req_status,
        "period": period,
    }
    logger = LogWithContext(log_context)
    logger.info("BEGIN: Get All Request data")
    base_query = (
        ApprovalRequestQueryBuilder(client_id)
        .join_with_employee()
        .join_with_employee_payroll_details()
        .join_with_instance_stage()
        .join_with_approval_instance()
        .join_with_hierarchy()
        .join_with_custom_field_data()
        .approval_request_client_kd_aware()
        .search_with_entity_type(entity_type)
    )

    query_filters = {
        "approver_email": str(approver_email) if approver_email else None,
        "request_status": req_status if req_status != "all" else None,
        "request_period": period if period != "all" else None,
        "search_term": search_term,
    }

    query_with_filters = base_query.apply_filters(**query_filters)

    has_custom_calendar = get_client_features(client_id).get("custom_calendar", False)
    if has_custom_calendar:
        base_query = base_query.join_custom_calendar_data()

    final_query = (
        (
            query_with_filters.clone_deep()
            .join_reporting_manager_details()
            .join_with_payout_status()
            .join_approver_details(use_alias=True)
            .select_request_period()
            .select_approval_request_details()
            .select_basic_details_in_employee()
            .select_all_custom_field_data()
            .select_payroll_details_payout()
            .select_stage_field_for_request()
            .select_instance_data()
            .select_payout_start_end_date()
            .approvals_tab_order()
        )
        if not only_request_ids
        else (query_with_filters.clone_deep().select_request_ids())
    )

    if has_custom_calendar:
        final_query = final_query.select_custom_calendar_details()

    if limit != None and offset != None:
        offset = offset * limit
        final_query = final_query.limit_and_offset(limit, offset)

    get_all_requests_sql = final_query.get_sql()
    logger.info(
        f"(get_approval_request_data) : Built final 'get all requests' query : {get_all_requests_sql}"
    )

    with connection.cursor() as cursor:
        logger.info(
            "(get_approval_request_data) : 'get requests' raw query db call : BEGIN"
        )
        cursor.execute(get_all_requests_sql)
        columns = [col[0] for col in cursor.description]
        all_request_data = [dict(zip(columns, row)) for row in cursor.fetchall()]

        if only_request_ids:
            return {"requests": all_request_data}

        all_request_data = add_additional_details_to_requests(
            client_id, all_request_data, logged_in_user=approver_email
        )
        logger.info(
            "(get_approval_request_data) : 'get requests' raw query db call : END"
        )

    logger.info("Fetch plan details...")
    # Plan details is fetched separately because for every payee there can be
    # multiple records returned from plan details as there could be many SPIFF plans and a primary plan
    # this would affect the limit if joined together
    fetch_plan_details_query = (
        ApprovalRequestQueryBuilder(client_id)
        .join_with_employee()
        .join_with_plan_details()
        .join_additional_plan_info_from_plan_details()
        .approval_request_client_kd_aware()
        .search_with_entity_type(entity_type)
        .apply_filters(**query_filters)
        .select_request_period()
        .select_approval_request_details()
        .select_basic_details_in_employee()
        .select_plan_details_payout()
    )
    with connection.cursor() as cursor:
        logger.info("(get_plan_details_data) query : BEGIN")
        cursor.execute(fetch_plan_details_query.get_sql())
        columns = [col[0] for col in cursor.description]
        plan_details_data = [dict(zip(columns, row)) for row in cursor.fetchall()]

    entity_key_plan_details_map = {}

    count_requests_sql = query_with_filters.count_approval_request().get_sql()
    logger.info(
        f"(get_approval_request_data) : Built final 'count requests' query : {count_requests_sql}"
    )

    with connection.cursor() as cursor:
        logger.info(
            "(get_approval_request_data) : 'count requests' raw query db call : BEGIN"
        )
        cursor.execute(count_requests_sql)
        count_res = cursor.fetchone()
        logger.info(
            "(get_approval_request_data) : 'count requests' raw query db call : END"
        )
        emp_count = count_res[0] if count_res else 0

    logger.info("Create enity-key: plan details map..")

    ### FORMAT
    # {
    # "entity_key_1":{
    # "spiff_plans":[],
    # "primary_plans":[]
    # },
    # "entity_key_2":{
    # "spiff_plans":[],
    # "primary_plans":[]
    # }
    # }
    for plan_detail_record in plan_details_data:
        if not plan_detail_record["entity_key"] in entity_key_plan_details_map:
            entity_key_plan_details_map[plan_detail_record["entity_key"]] = {}
        if plan_detail_record["plan_type"] == "MAIN":
            if (
                "primary_plan"
                not in entity_key_plan_details_map[plan_detail_record["entity_key"]]
            ):
                entity_key_plan_details_map[plan_detail_record["entity_key"]][
                    "primary_plan"
                ] = plan_detail_record["plan_name"]
        elif plan_detail_record["plan_type"] == "SPIFF":
            if (
                "spiff_plans"
                in entity_key_plan_details_map[plan_detail_record["entity_key"]]
            ):
                entity_key_plan_details_map[plan_detail_record["entity_key"]][
                    "spiff_plans"
                ].add(plan_detail_record["plan_name"])
            else:
                entity_key_plan_details_map[plan_detail_record["entity_key"]][
                    "spiff_plans"
                ] = {plan_detail_record["plan_name"]}

    logger.info("Add plan details to the final data")

    all_period_labels_ped_map = {}

    # Format plan names as string before return
    for record in all_request_data:
        # If record's period is not in all_period_labels_ped_map, then get the period label using that record's period
        if record["period"] not in all_period_labels_ped_map:
            all_period_labels_ped_map[record["period"]] = get_period_label_with_ped(
                client_id=client_id,
                period_end_date=datetime.strptime(record["period"], "%Y-%m-%d"),
                default_date_format="%b %Y",
            )
        record["period_label"] = all_period_labels_ped_map[record["period"]]
        record["primary_plan"] = (
            entity_key_plan_details_map[record["entity_key"]]["primary_plan"]
            if record["entity_key"] in entity_key_plan_details_map
            and "primary_plan" in entity_key_plan_details_map[record["entity_key"]]
            else ""
        )
        if (
            record["entity_key"] in entity_key_plan_details_map
            and "spiff_plans" in entity_key_plan_details_map[record["entity_key"]]
        ):
            record["spiff_plans"] = ", ".join(
                entity_key_plan_details_map[record["entity_key"]]["spiff_plans"]
            )
        else:
            record["spiff_plans"] = ""
            # If payee's freqeuency is based on custom calendar
        if record.get("custom_calendar_name", None):
            record["payout_frequency"] = record["custom_calendar_name"]

    logger.info("END: Get All Request data")
    return {"requests": all_request_data, "requests_count": emp_count}


def add_additional_details_to_requests(
    client_id, all_request_data, logged_in_user, logger=None
):
    """
    Adds additional details to the requests data like:
    -   statements_url
    -   payout_details
    -   custom_field_data
    -   plan_details
    """
    from commission_engine.utils.payout_utils import (
        can_user_view_payout_numbers,
        can_user_view_payroll_numbers,
    )

    # from commission_engine.utils.payroll_utils import can_user_view_payroll_details
    from spm.services.custom_field_services import (
        get_custom_fields_details,
        resolve_custom_field_data_for_user_filters,
    )

    logger = LogWithContext() if logger is None else logger

    custom_fields_data = get_custom_fields_details(client_id)
    all_request_data = add_statement_url_for_all_requests(client_id, all_request_data)

    is_view_payroll = (
        is_view_payroll_permission(client_id, logged_in_user)
        if logged_in_user
        else False
    )

    modified_request_data = {}
    for request_data in all_request_data:
        employee_email_id = request_data["employee_email_id"]
        period = request_data["period"]
        request_status = request_data["status"]
        frequency = str(request_data["payout_frequency"]).lower()
        approval_wf_instance_id = str(request_data["approval_wf_instance_id"])
        stage_instance_id = str(request_data["stage_instance_id"])

        key = (
            str(employee_email_id)
            + "#"
            + frequency
            + "#"
            + str(period)
            + "#"
            + request_status
            + "#"
            + approval_wf_instance_id
            + "#"
            + stage_instance_id
        )
        can_view_payout = can_user_view_payout_numbers(
            client_id, str(logged_in_user), employee_email_id
        )
        can_view_payroll = can_user_view_payroll_numbers(
            client_id, str(logged_in_user), employee_email_id
        )

        if key not in modified_request_data:
            custom_field_data_resolved = resolve_custom_field_data_for_user_filters(
                request_data["custom_field_data"],
                system_name_default_values_map=custom_fields_data[
                    "system_name_default_values_map"
                ],
                types_map=custom_fields_data["types_map"],
                dropdown_options_map=custom_fields_data["dropdown_options_map"],
                as_dicts=True,
            )
            if not can_view_payout:
                if "payout" in request_data:
                    request_data["payout"] = "-"
                if "instance_data" in request_data:
                    instance_data = json.loads(request_data["instance_data"])
                    instance_data["payout"] = "-"
                    request_data["instance_data"] = json.dumps(instance_data)

            if not can_view_payroll:
                request_data["fixed_pay"] = "-"
                request_data["variable_pay"] = "-"

            modified_request_data[key] = {
                **custom_field_data_resolved,
                **{
                    k: v
                    for k, v in request_data.items()
                    if k
                    not in [
                        "plan_id",
                        "custom_field_data_tid",
                        "custom_field_data",
                        "plan_type",
                        "plan_name",
                    ]
                },
            }

            if not (employee_email_id == logged_in_user or is_view_payroll):
                modified_request_data[key].update(
                    {"fixed_pay": "-", "variable_pay": "-"}
                )
    return list(modified_request_data.values())


def add_statement_url_for_all_requests(client_id, all_request_data, logger=None):
    """
    Adds {"statements_url" : <base64 encoded URL>} to all the approval requests.

    Some of the approval requests may contain same email_id, payout_frequency and kd, in that cases
    `known_statements_urls` will be used as a cache.
    """
    logger = LogWithContext() if logger is None else logger

    logger.info("BEGIN : Populating all_request_data with statements URl.")
    start_month = get_client(client_id).fiscal_start_month
    known_statements_urls = {}
    for request_data in all_request_data:
        employee_email_id = request_data["employee_email_id"]
        knowledge_date = request_data["period"]
        frequency = str(request_data["payout_frequency"]).lower()
        key = str(employee_email_id) + "#" + str(frequency) + "#" + str(knowledge_date)
        if key not in known_statements_urls:
            knowledge_date = make_aware(
                end_of_day(datetime.strptime(knowledge_date, "%Y-%m-%d"))
            )
            data = get_period_start_and_end_date(
                knowledge_date, start_month, frequency, client_id=client_id
            )
            psd = data["start_date"]
            ped = data["end_date"]
            statements_url = get_statements_url_for_payee(
                employee_email_id, psd.strftime("%Y-%m-%d"), ped.strftime("%Y-%m-%d")
            )
            request_data["statements_url"] = statements_url
            known_statements_urls[key] = statements_url
        else:
            request_data["statements_url"] = known_statements_urls[key]

    logger.info(f"known_statements_urls keys- {list(known_statements_urls.keys())}")
    logger.info("END : Populating all_request_data with statements URl.")
    return all_request_data


def get_valid_request_ids_for_approver_email(client_id, approver_email, request_ids):
    if not isinstance(request_ids, list):
        request_ids = [request_ids]
    request_ids = set(request_ids)
    return ApprovalRequestsAccessor(client_id).get_valid_request_ids_for_approver(
        approver_email, request_ids
    )


@transaction.atomic
def abort_all_instances(
    client_id: int,
    audit: dict | None = None,
    handle_sub_requests: bool = False,
    entity_types: List[str] | None = None,
    logger=None,
):
    """
    This function aborts all the instances for the given client_id in the following tables, whenever there is a
    switch between the payouts approval config
    1. approval_workflow_instance
    2. approval_instance_stage
    3. approval_requests
    4. sub_approval_requests (if line item level approval is switched to payout level)
    """
    # TODO : Move common methods outside line_ierm_services
    from spm.services.approval_line_items.line_item_services import (  # To solve circular import
        bulk_update_instances_status,
        bulk_update_requests_status,
        bulk_update_stages_status,
        bulk_update_sub_requests_status,
    )

    logger = LogWithContext() if logger is None else logger
    logger.info(
        f"BEGIN: Abort all instances for client {client_id} as approval config is switched"
    )

    logger.info(f"Fetching all instances for client {client_id}")

    approval_instance_ids = ApprovalInstanceAccessor(
        client_id
    ).get_all_workflow_ids_list(
        filters={
            "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            "entity_types": entity_types,
        }
    )
    logger.info(
        f"Fetching all stages for client {client_id} with instances {approval_instance_ids}"
    )
    stage_ids = ApprovalInstanceStageAccessor(client_id).get_all_stage_ids_list(
        filters={
            "approval_wf_instance_id__in": approval_instance_ids,
            "status__in": [
                APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
                APPROVAL_WORKFLOW_STATUS.STARTED.value,
            ],
        }
    )
    logger.info(
        f"Fetching all requests for client {client_id} with total stages {len(stage_ids)}"
    )
    approval_request_ids = ApprovalRequestsAccessor(client_id).get_all_request_ids_list(
        filters={
            "stage_instance_id__in": stage_ids,
            "status__in": [
                APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
            ],
        }
    )
    logger.info(f"Total requests fetched - {len(approval_request_ids)}")
    if handle_sub_requests:
        logger.info(
            f"Fetching all sub requests for client {client_id} with total requests {len(approval_request_ids)}"
        )
        sub_request_ids = SubApprovalRequestsAccessor(
            client_id
        ).get_all_sub_request_ids_list(
            filters={
                "request_id__in": approval_request_ids,
                "status__in": [
                    APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                    APPROVAL_WORKFLOW_STATUS.DECLINED.value,
                    APPROVAL_WORKFLOW_STATUS.NOT_STARTED.value,
                ],
            }
        )
        logger.info(f"Aborting all sub requests, total - {len(sub_request_ids)}")
        bulk_update_sub_requests_status(
            client_id,
            sub_request_ids,
            ApprovalSubRequestsStatus.Aborted().name,
            is_system_action=True,
            audit_data=audit,
        )
    logger.info(f"Aborting all requests, total - {len(approval_request_ids)}")
    bulk_update_requests_status(
        client_id,
        approval_request_ids,
        ApprovalRequestStatus.Aborted().name,
        is_system_action=True,
        audit_data=audit,
    )
    logger.info(f"Aborting all stages, total - {len(stage_ids)}")
    bulk_update_stages_status(
        client_id,
        stage_ids,
        ApprovalStageStatus.Aborted().name,
        audit_data=audit,
    )
    logger.info(f"Aborting all instances, total - {len(approval_instance_ids)}")
    bulk_update_instances_status(
        client_id,
        approval_instance_ids,
        ApprovalInstanceStatus.Aborted().name,
        audit_data=audit,
    )
    logger.info(
        f"END: Abort all instances for client {client_id} as approval config is switched"
    )


@transaction.atomic
def delete_approval_requests(client_id, logged_in_user, **kwargs):
    """
    This function deletes the approval requests for the given entity keys and entity type

    Args:
        client_id (int)
        entity_keys (list)
        entity_type (str)

    Returns:
        None
    """
    entity_keys = kwargs.get("entity_keys")
    entity_type = kwargs.get("entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value)
    module_logger.info("BEGIN: delete_approval_requests for entity_key %s", entity_keys)
    if not entity_keys:
        module_logger.info("No entity keys found to invalidate")
        return {"status": "Failed", "message": "No approval requests to delete"}
    knowledge_date = timezone.now()
    instances = ApprovalInstanceAccessor(
        client_id
    ).get_instances_by_entity_keys_entity_type(
        entity_keys,
        as_dict=True,
    )

    if not instances:
        module_logger.info("No instances found to delete")
        return {"status": "Failed", "message": "No approval requests to delete"}
    instance_ids = [instance["approval_wf_instance_id"] for instance in instances]
    module_logger.info("Invalidating instances - %s", instance_ids)

    ApprovalInstanceAccessor(client_id).bulk_delete_by_instance_ids(
        instance_ids,
        knowledge_date,
    )

    ApprovalInstanceStageAccessor(client_id).bulk_delete_stages_by_instance_ids(
        instance_ids,
        knowledge_date,
    )

    ApprovalRequestsAccessor(client_id).bulk_delete_requests_by_instance_ids(
        instance_ids,
        knowledge_date,
    )

    SubApprovalRequestsAccessor(client_id).bulk_delete_requests_by_instance_ids(
        instance_ids, knowledge_date
    )

    if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value and entity_keys:
        remove_temp_shared_plan_access(
            client_id,
            logged_in_user,
            entity_keys[0],
        )
    ###################### audit log #####################
    event_type_code = EVENT["DELETE_APPROVAL-INSTANCES"]["code"]
    event_key = str(uuid4())
    summary = "Approval requests deleted successfully"
    updated_by = logged_in_user
    updated_at = timezone.now()

    ######################################################
    data = {
        "entity_keys": entity_keys,
        "request_ids": instance_ids,
    }
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by,
        updated_at,
        data,
    )
    ######################################################
    # Analytics
    module_logger.info(
        "Sending analytics for delete approval requests for client_id %s", client_id
    )
    analytics = CoreAnalytics(analyser_type="segment")
    analytics_data = {
        "user_id": logged_in_user,
        "event_name": SegmentEvents.DELETE_INSTANCE.value,
        "event_properties": {
            SegmentProperties.INSTANCE_ID.value: [
                str(instance_id) for instance_id in instance_ids
            ],
            SegmentProperties.ENTITY_KEYS.value: entity_keys,
        },
    }
    analytics.send_analytics(analytics_data)
    ######################################################

    module_logger.info("END: delete_approval_requests for entity_key %s", entity_keys)
    return {"status": "SUCCESS", "message": "Approval requests deleted successfully"}


def create_payout_status_changes_entries_for_approvals_revoke(
    client_id: int, payee_emails: list, ped: str
):
    """
    Function to create payout status changes entries for approval revoke
    """
    module_logger.info(
        "BEGIN: Creating payout status changes entries - approval revoke"
    )
    add_approval_details_for_payout_status_changes(
        client_id=client_id,
        ped=end_of_day(make_aware_wrapper(datetime.strptime(ped, "%Y-%m-%d"))),
        payees=list(payee_emails),
        event_type=PayoutStatusChangesTypes.APPROVAL_REVOKE.value,
    )

    module_logger.info("END: Successfully created payout status changes entries")


@transaction.atomic
def revoke_instances(client_id, entity_type, instance_params, comments, audit_data):
    """
    This function is same as abort_instances, except the status is set to REVOKED instead of ABORTED

    @return None
    """
    from spm.services.approval_line_items.line_item_services import (  # To solve circular import
        bulk_update_sub_requests_status,
    )

    module_logger.info("BEGIN: Revoke instances for params - %s", instance_params)
    try:
        current_datetime = timezone.now()
        entity_keys = get_entity_keys(entity_type, instance_params)

        # Only requested instances can be revoked
        entity_status_to_revoke = [
            APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
        ]
        module_logger.info("Entity keys = %s", entity_keys)

        # Fetch all the instances that need to be revoked
        instances = ApprovalInstanceAccessor(
            client_id
        ).get_instances_by_entity_keys_and_status_and_entity_type(
            entity_keys,
            entity_status_to_revoke,
            entity_type,
            as_dict=False,
        )
        module_logger.info("Ongoing instances to revoke %s", instances)
        if instances:
            requested_status_instance_ids = []
            for instance in instances:
                if instance.status in entity_status_to_revoke:
                    requested_status_instance_ids.append(
                        instance.approval_wf_instance_id
                    )
                instance.pk = None
                instance.status = APPROVAL_WORKFLOW_STATUS.REVOKED.value
                instance.knowledge_begin_date = current_datetime
                instance.is_system_action = False
                instance.additional_details = {
                    "updated_by": audit_data["logged_in_user"]
                }
                instance.completion_time = current_datetime
            ApprovalInstanceAccessor(client_id).bulk_invalidate_by_instance_ids(
                [instance.approval_wf_instance_id for instance in instances],
                current_datetime,
            )
            ApprovalInstanceAccessor(client_id).bulk_persist(instances)

            if (
                entity_type != APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value
                and entity_keys
            ):
                # Create payout status changes entries for approval revoke
                payees = [
                    entity_key.split(ENTITY_KEY_DELIMETER)[0]
                    for entity_key in entity_keys
                ]
                ped = entity_keys[0].split(ENTITY_KEY_DELIMETER)[1]
                create_payout_status_changes_entries_for_approvals_revoke(
                    client_id, payees, ped
                )

            # Fetch all the stages and requests of the instances that need to be revoked
            # We will be revoking stages in started, requested and not started status
            if requested_status_instance_ids:
                ongoing_instance_stages = ApprovalInstanceStageAccessor(
                    client_id
                ).get_stage_instances_by_instance_id_status(
                    requested_status_instance_ids,
                    [
                        APPROVAL_WORKFLOW_STATUS.STARTED.value,
                        APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                    ],
                )
                module_logger.info(
                    "Ongoing instance stages for revoking %s", ongoing_instance_stages
                )

                # We will be revoking requests which are in requested status only.
                ongoing_requests = ApprovalRequestsAccessor(
                    client_id
                ).get_requests_by_instance_id_status(
                    requested_status_instance_ids,
                    [
                        APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                    ],
                )
                module_logger.info("Ongoing requests for revoking %s", ongoing_requests)
                for ongoing_instance_stage in ongoing_instance_stages:
                    ongoing_instance_stage.pk = None
                    ongoing_instance_stage.status = (
                        APPROVAL_WORKFLOW_STATUS.REVOKED.value
                    )
                    ongoing_instance_stage.knowledge_begin_date = current_datetime
                    ongoing_instance_stage.is_system_action = False
                    ongoing_instance_stage.additional_details = {
                        "updated_by": audit_data["logged_in_user"]
                    }
                    ongoing_instance_stage.completed_time = current_datetime
                for ongoing_request in ongoing_requests:
                    ongoing_request.pk = None
                    ongoing_request.status = APPROVAL_WORKFLOW_STATUS.REVOKED.value
                    ongoing_request.knowledge_begin_date = current_datetime
                    ongoing_request.is_system_action = False
                    ongoing_request.additional_details = {
                        "updated_by": audit_data["logged_in_user"]
                    }
                    ongoing_request.completed_time = current_datetime
                    ongoing_request.comments = comments

                ApprovalInstanceStageAccessor(
                    client_id
                ).bulk_invalidate_by_stage_instance_ids(
                    [
                        ongoing_instance_stage.stage_instance_id
                        for ongoing_instance_stage in ongoing_instance_stages
                    ],
                    current_datetime,
                )
                ApprovalRequestsAccessor(client_id).bulk_invalidate_by_request_ids(
                    [
                        ongoing_request.approval_request_id
                        for ongoing_request in ongoing_requests
                    ],
                    current_datetime,
                )
                ApprovalInstanceStageAccessor(client_id).bulk_persist(
                    ongoing_instance_stages
                )
                ApprovalRequestsAccessor(client_id).bulk_persist(ongoing_requests)

                if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                    approval_request_ids = [
                        ongoing_request.approval_request_id
                        for ongoing_request in ongoing_requests
                    ]
                    sub_request_ids = SubApprovalRequestsAccessor(
                        client_id
                    ).get_all_sub_request_ids_list(
                        filters={
                            "request_id__in": approval_request_ids,
                            "status__in": [
                                APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                            ],
                        }
                    )
                    module_logger.info(
                        "Aborting all sub requests, total - %s", len(sub_request_ids)
                    )
                    bulk_update_sub_requests_status(
                        client_id,
                        sub_request_ids,
                        ApprovalSubRequestsStatus.Revoked().name,
                        is_system_action=False,
                        audit_data={"updated_by": audit_data["logged_in_user"]},
                    )

                if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                    approvers = [
                        ongoing_request.approver for ongoing_request in ongoing_requests
                    ]
                    entity_key = entity_keys[0]
                    remove_temp_shared_plan_access(
                        client_id,
                        audit_data.get("approved_by"),
                        entity_key,
                        approvers=approvers,
                    )
            if entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value:
                send_plan_revoke_notification(client_id, instances, comments)
        ###################### Audit log #####################
        if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
            audit_entity_type = "Payout Line Item"
        elif entity_type == APPROVAL_ENTITY_TYPES.PAYOUT.value:
            audit_entity_type = "Payout"
        elif entity_type == APPROVAL_ENTITY_TYPES.COMMISSION_ADJUSTMENT.value:
            audit_entity_type = "Commission Adjustment"
        else:
            audit_entity_type = entity_type
        audit_summary = "Revoke " + audit_entity_type + " approval instance(s)"
        audit_meta_data = get_audit_log_data(
            EVENT["REVOKE_REQUEST-APPROVALS"]["code"],
            audit_summary,
            audit_data["logged_in_user"],
            {"approval_type": entity_type, "entity_keys": entity_keys},
            uuid4(),
        )

        audit_services.log(
            client_id,
            audit_meta_data.event_type_code,
            audit_meta_data.event_key,
            audit_meta_data.summary,
            audit_meta_data.updated_by,
            audit_meta_data.updated_at,
            audit_meta_data.audit_data,
        )
        module_logger.info("END: Revoke Instances.")
    except Exception as e:
        module_logger.exception("Exception while Revoking approvals.")
        # Since it's a transaction atomic block, we need to raise exception to rollback the transaction
        raise Exception("Failed. ") from e


def get_filtered_out_requested_emails(client_id: int, date: str, details: list):
    """
    This function returns the list of emails that are already in requested status
    This is used to filter out data received for bulk revoke with selectAll option
    This output is also important to audit the data for bulk revoke
    """

    entity_key_email_map = {}
    for employee_detail in details:
        entity_key_of_instance = (
            employee_detail["email_id"]
            + ENTITY_KEY_DELIMETER
            + parse(date, dayfirst=True).strftime("%Y-%m-%d")
        )
        entity_key_email_map[entity_key_of_instance] = employee_detail["email_id"]

    entity_keys = entity_key_email_map.keys()
    # Only requested instances can be revoked
    entity_status_to_revoke = [
        APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
    ]
    is_line_item_level_enabled = get_line_item_approval_flag(client_id)
    entity_type = (
        APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value
        if is_line_item_level_enabled
        else APPROVAL_ENTITY_TYPES.PAYOUT.value
    )

    # Fetch all the instances that need to be revoked
    instances = ApprovalInstanceAccessor(
        client_id
    ).get_approval_instance_with_entity_keys_and_status(
        entity_keys,
        entity_status_to_revoke,
        entity_type,
    )
    requested_emails = []
    for instance in instances:
        requested_emails.append(entity_key_email_map[instance["entity_key"]])

    return requested_emails


@shared_task(base=EverCeleryBaseTask)
def bulk_revoke_approval_instances(client_id: int, **kwargs):
    """
    A shared task function to run revoke instances in engine.

    @return None
    """
    module_logger.info("BEGIN: Bulk revoke approval instances.")
    realtime_task_id = str(uuid4())
    entity_type = kwargs.get("entity_type")
    instance_params = kwargs.get("instance_params", {})
    audit_data = kwargs.get("audit_data", {})
    comments = kwargs.get("comments", None)

    if kwargs.get("is_selected_all"):
        filters = kwargs.get("filters") or {}
        ped = end_of_day(
            make_aware(datetime.strptime(instance_params["date"], "%d-%m-%Y"))
        )
        instance_details = get_instance_details_for_selected_all(
            client_id,
            ped=ped,
            logged_in_user=audit_data["logged_in_user"],
            search_term=kwargs.get("search_term"),
            filters=filters,
            logger=module_logger,
        )
        payee_email_ids = get_filtered_out_requested_emails(
            client_id, audit_data["date"], instance_details
        )

        if not payee_email_ids:
            module_logger.info("No payees found to revoke.")
            return
        instance_params["instance_details"] = [
            {"email_id": payee_email} for payee_email in payee_email_ids
        ]
        audit_data["payees"] = payee_email_ids

    async_params = {
        "details": [
            {
                "payee_email_id": payee_email,
                "ped": audit_data["date"],
            }
            for payee_email in audit_data["payees"]
        ],
        "created_by": audit_data["logged_in_user"],
        "approval_type": entity_type,
        "total_approval_instances": len(audit_data["payees"]),
    }

    # Add task to async task table
    async_task = AsyncTaskAccessor(client_id).add_task(
        audit_data["logged_in_user"],
        AsyncTaskConfig.BULK_REVOKE_APPROVAL_INSTANCES.value["name"],
        async_params,
    )
    async_task_id = async_task.id
    AsyncTaskAccessor(client_id).set_processing(async_task_id)

    # Initialize supabase for the client
    try:
        supabase = get_supabase_client()
        supabase.insert(
            {
                "task_name": AsyncTaskConfig.BULK_REVOKE_APPROVAL_INSTANCES.name,
                "client_id": client_id,
                "task_id": realtime_task_id,
                "data": {"status": TaskStatus.PENDING.value},
            }
        ).execute()
    except Exception:
        module_logger.exception(
            "Cannot initialize supabase for client: %s, task: %s",
            client_id,
            async_task_id,
        )

    # Revoke instances
    revoke_instances(client_id, entity_type, instance_params, comments, audit_data)

    try:
        supabase = get_supabase_client()
        supabase.update({"data": {"status": TaskStatus.DONE.value}}).eq(
            "task_id", realtime_task_id
        ).execute()

    except Exception:
        module_logger.exception("Cannot update status to processing in supabase table")

    AsyncTaskAccessor(client_id).set_done(
        async_task_id, {"status": TaskStatus.DONE.value}
    )

    module_logger.info("END: Bulk revoke approval instances.")
