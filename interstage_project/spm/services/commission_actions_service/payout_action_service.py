import csv
import time as t1
import traceback
import uuid
from io import BytesIO, <PERSON><PERSON>
from typing import Any, Dict, List

import xlsxwriter
from dateutil.parser import parse
from django.utils import timezone
from django.utils.timezone import make_aware
from pydash import nest
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

import interstage_project.utils as iputils
from commission_engine.accessors.accessor_factories.commission_sec_kd_accessor_factory import (
    CommissionSecondaryKdAccessorFactory,
)
from commission_engine.accessors.client_accessor import (
    can_run_payout_snapshot_etl,
    can_run_sf_payout_snapshot,
    get_client,
    get_client_subscription_plan,
)
from commission_engine.accessors.commission_accessor import CommissionAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    ETLSyncStatusReaderAccessor,
)
from commission_engine.accessors.lock_accessor import (
    CommissionLockAccessor,
    CommissionLockDetailsAccessor,
)
from commission_engine.serializers.commission_serializers import (
    CommissionLockSerializer,
    CommissionLockUpdateSerializer,
    CommLockDetailUpdateSerializer,
)
from commission_engine.services.payout_status_changes_service import (
    add_freeze_unfreeze_details_for_payout_status_changes,
)
from commission_engine.services.payout_status_service import (
    update_comm_payout_freeze_status,
    update_payout_details,
)
from commission_engine.utils import (
    COMMISSION_TYPE,
    delete_commission_cache,
    delete_payee_commission_cache,
    get_payout_details,
)
from commission_engine.utils.date_utils import (
    convert_str_to_date,
    end_of_day,
    get_period_start_and_end_date,
    get_start_date_by_freq_and_end_date,
    last_day_of_month_from_month_year,
)
from commission_engine.utils.general_data import (
    ETL_ACTIVITY,
    CommissionChangesTypes,
    PayoutStatusChangesTypes,
    SegmentEvents,
    SegmentProperties,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.accessors.commission_plan_accessor import PlanModificationChangesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.commission_adjustment_approvals.services.approval_config_services import (
    can_abort_comm_adj_request,
)
from spm.commission_adjustment_approvals.services.approval_workflow_services import (
    abort_comm_adj_instances,
)
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.approval_workflow_services.approval_instance_services import (
    APPROVAL_ENTITY_TYPES,
    abort_instances,
)
from spm.services.commission_actions_service.commission_actions_service import (
    get_all_commissions_per_month,
)
from spm.services.commission_actions_service.commission_slack_services import (
    notify_lock_status_to_admin,
)
from spm.services.plan_modification_changes_service import (
    add_freeze_unfreeze_period_payees_details,
)

# def update_paid_status(client_id, request):
#     time = timezone.now()
#     data = {}
#     ###################### audit log #####################
#     event_type_code = EVENT["PAID_PAID-COMMISSION"]["code"]
#     updated_by = request.audit["updated_by"]
#     updated_at = time
#     ######################################################
#     logger = request.logger
#     if (
#         request.data["payee_ids"]
#         and request.data["month"]
#         and request.data["year"]
#     ):
#         month = request.data["month"]
#         year = int(request.data["year"])
#         time_period = str(month) + " " + str(year)
#         commission_details = request.data["commission_details"]
#         modified_commission_details_map = {}
#         for key, value in commission_details.items():
#             modified_commission_details_map[value["payee_email_id"]] = value
#         commission_details = modified_commission_details_map
#         end_date = last_day_of_month_from_month_year(month, year)
#         logger.update_context(
#             {
#                 "month": month,
#                 "year": year,
#                 "commission_details": commission_details,
#                 "end_date": end_date,
#             }
#         )
#         if len(request.data["payee_ids"]) > 1:
#             bulk_action = True
#         else:
#             bulk_action = False
#         for payee in request.data["payee_ids"]:
#             payout = PayoutAccessor(client_id).get_payout_for_payee_and_date(payee, end_date)
#             commission_lock = CommissionLockAccessor(client_id).get_payee_lock_in_end_date(end_date, payee)
#             settlement_lock = SettlementLockAccessor(client_id).get_payee_lock_in_end_date(end_date, payee)
#             event_key = payee + "#" + month + request.data["year"]
#             summary = payee
#             audit_data = {**commission_details[payee]}
#             start_date = convert_str_to_date(audit_data["period_start_date"])
#             try:
#                 if request.data["is_paid"]:
#                     if commission_lock is None or not commission_lock.is_locked:
#                         add_lock_entry(
#                             client_id,
#                             payee,
#                             end_date,
#                             time,
#                             request.audit,
#                             commission_lock,
#                         )
#                         if settlement_lock is None or not settlement_lock.is_locked:
#                             sas.add_settlement_lock_entry(
#                                 client_id,
#                                 payee,
#                                 end_date,
#                                 time,
#                                 request.audit,
#                                 settlement_lock,
#                             )
#                         # deleting cache only if lock is not present because
#                         # paid status does not affect the cached commission data
#                         delete_commission_cache(
#                             client_id=client_id,
#                             psd=start_date,
#                             ped=end_date,
#                             email_id=payee,
#                         )
#                     if payout is None or not payout.is_paid:
#                         audit_data["payment_status"] = "Paid"
#                         audit_data["paid_amount"] = audit_data[
#                             "calculated_commission"
#                         ]
#                         audit_data["calculation_status"] = "Frozen"
#                         payout_details = add_payout_entry(
#                             client_id,
#                             payee,
#                             end_date,
#                             time,
#                             request.audit,
#                             commission_details,
#                             payout,
#                         )
#                         audit_services.log(
#                             client_id,
#                             event_type_code,
#                             event_key,
#                             summary,
#                             updated_by,
#                             updated_at,
#                             audit_data,
#                         )
#                         logger.info(
#                             "Paid Commission for payee with email {}".format(
#                                 payee
#                             )
#                         )
#                         notify_payment_initiated_to_payee(
#                             client_id, logger, payee, payout_details
#                         )
#                         segment.Analytics(user_id=request.audit["updated_by"]).track(
#                             event_name=SegmentEvents.MARKED_AS_PAID.value,
#                             track_properties={
#                                 SegmentProperties.TIME_PERIOD.value: time_period,
#                                 SegmentProperties.PAYEE.value: payee,
#                                 SegmentProperties.BULK_ACTION.value: bulk_action,
#                             },
#                         )
#                 else:
#                     event_type_code = EVENT["UNPAID_PAID-COMMISSION"]["code"]
#                     audit_data["payment_status"] = "UnPaid"
#                     audit_data["paid_amount"] = audit_data[
#                         "calculated_commission"
#                     ]
#                     if payout:
#                         PayoutAccessor(client_id).mark_as_unpaid(
#                             payee, end_date, time, request.audit
#                         )
#                         audit_services.log(
#                             client_id,
#                             event_type_code,
#                             event_key,
#                             summary,
#                             updated_by,
#                             updated_at,
#                             audit_data,
#                         )
#                         logger.info(
#                             "Marked as UnPaid Commission for payee with email {}".format(
#                                 payee
#                             )
#                         )
#                         segment.Analytics(user_id=request.audit["updated_by"]).track(
#                             event_name=SegmentEvents.MARKED_AS_UNPAID.value,
#                             track_properties={
#                                 SegmentProperties.TIME_PERIOD.value: time_period,
#                                 SegmentProperties.PAYEE.value: payee,
#                                 SegmentProperties.BULK_ACTION.value: bulk_action,
#                             },
#                         )
#             except:
#                 error_dict = {"trace_back": traceback.print_exc()}
#                 logger.error("Excep in Marking Payee Paid/Unpaid", error_dict)
#                 raise SQLParseError()
#             logger.update_context({"payee_id": payee})
#             logger.info(
#                 "marked commission status successfully for payee with email {}".format(
#                     payee
#                 )
#             )
#             data[payee] = {"status": "SUCCESS", "payee_id": payee}
#         notify_paid_status_to_admin(client_id, logger, request, updated_by)
#         return Response(data, status=status.HTTP_201_CREATED)
#     else:
#         logger.error(
#             "Payee ID's or Month or Year is None to UPDATE PAID STATUS"
#         )
#         return Response(
#             {"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST
#         )


def update_lock_status(
    client_id, request, abort_approvals=False, both_comm_and_sett_report_etl=False
):
    import everstage_etl.tasks.report_etl as report_etl

    time = timezone.now()
    data = {}
    ###################### audit log #####################
    event_type_code = EVENT["FREEZE_FREEZE-COMMISSION"]["code"]
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    logger = request.logger

    if request.data["payee_ids"] and request.data["month"] and request.data["year"]:
        month = request.data["month"]
        year = int(request.data["year"])
        time_period = str(month) + " " + str(year)
        end_date = last_day_of_month_from_month_year(month, year)
        commission_details = get_all_commissions_per_month(
            client_id, month, request.data["year"]
        )
        logger.update_context(
            {
                "month": month,
                "year": year,
                "end_date": end_date,
            }
        )
        if len(request.data["payee_ids"]) > 1:
            bulk_action = True
        else:
            bulk_action = False
        for payee in request.data["payee_ids"]:
            event_key = payee + "#" + month + request.data["year"]
            summary = payee
            payee_index = next(
                (
                    index
                    for index, item in enumerate(commission_details)
                    if item["payee_email_id"] == payee
                ),
                None,
            )
            if payee_index is not None:
                audit_data = commission_details[payee_index]
                lock = CommissionLockAccessor(client_id).get_payee_lock_in_end_date(
                    end_date, payee
                )
                start_date = convert_str_to_date(audit_data["period_start_date"])
                try:
                    if request.data["is_locked"]:
                        if lock is None or not lock.is_locked:
                            audit_data["calculation_status"] = "Locked"
                            audit_data["payment_status"] = "UnPaid"
                            add_lock_entry(
                                client_id,
                                payee,
                                end_date,
                                time,
                                request.audit,
                                lock,
                            )
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )
                            logger.info(
                                "Updated lock status for payee with email {}".format(
                                    payee
                                )
                            )

                            analytics_data = {
                                "user_id": request.audit["updated_by"],
                                "event_name": SegmentEvents.FREEZE_COMMISSIONS.value,
                                "event_properties": {
                                    SegmentProperties.TIME_PERIOD.value: time_period,
                                    SegmentProperties.PAYEE.value: payee,
                                    SegmentProperties.BULK_ACTION.value: bulk_action,
                                },
                            }
                            analytics = CoreAnalytics(analyser_type="segment")
                            analytics.send_analytics(analytics_data)
                    else:
                        if lock and lock.is_locked:
                            audit_data["calculation_status"] = "Not Locked"
                            audit_data["payment_status"] = "UnPaid"
                            event_type_code = EVENT["UNFREEZE_FREEZE-COMMISSION"][
                                "code"
                            ]
                            # payout = PayoutAccessor(client_id).get_payout_for_payee_and_date(payee, end_date)
                            # if payout and payout.is_paid:
                            #     PayoutAccessor(client_id).mark_as_unpaid(payee, end_date, time, request.audit)
                            CommissionLockAccessor(client_id).mark_as_unlocked(
                                payee, end_date, time, request.audit
                            )
                            CommissionLockDetailsAccessor(
                                client_id
                            ).invalidate_lock_detail_with_lock_id(lock.lock_id, time)
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )
                            logger.info(
                                "updated lock status for payee with email {}".format(
                                    payee
                                )
                            )

                            analytics_data = {
                                "user_id": request.audit["updated_by"],
                                "event_name": SegmentEvents.UNFREEZE_COMMISSIONS.value,
                                "event_properties": {
                                    SegmentProperties.TIME_PERIOD.value: time_period,
                                    SegmentProperties.PAYEE.value: payee,
                                    SegmentProperties.BULK_ACTION.value: bulk_action,
                                },
                            }
                            analytics = CoreAnalytics(analyser_type="segment")
                            analytics.send_analytics(analytics_data)
                    # delete cached commission data for payee
                    delete_commission_cache(
                        client_id=client_id,
                        psd=start_date,
                        ped=end_date,
                        email_id=payee,
                    )
                except Exception as e:
                    error_dict = {"trace_back": traceback.print_exc()}
                    logger.error("Excep in Updating Lock Status", error_dict)
                    raise SQLParseError() from e
                logger.update_context({"payee_id": payee})
                logger.info(
                    "Updated lock status successfully for payee with email {}".format(
                        payee
                    )
                )
                data[payee] = {"status": "SUCCESS", "payee_id": payee}
        if request.data.get("is_locked") is False:
            if not both_comm_and_sett_report_etl:
                # when unfreeze, trigger the report_etl for the commission period.
                logger.info("Triggered Celery Task for REPORT ETL due to Unfreeze")
                subscription_plan = get_client_subscription_plan(client_id)
                report_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.REPORT.value
                )
                report_etl.report_object_etl_by_period_wrapper.si(
                    client_id=client_id,
                    ped=end_date,
                    sped=None,
                    payee_email_id=request.data["payee_ids"],
                    is_unfreeze_mode=True,
                    is_custom_mode=False,
                    log_context=logger.get_context(),
                ).set(queue=report_queue_name).apply_async()
            if abort_approvals:
                # Abort approval instances if active
                abort_payout_instances(
                    client_id,
                    end_date.strftime("%d-%m-%Y"),
                    request.data["payee_ids"],
                    request.audit,
                )
        update_payout_details(
            client_id, end_date, request.data["payee_ids"], audit=request.audit
        )
        notify_lock_status_to_admin(
            client_id,
            request.data["payee_ids"],
            updated_by,
            request.data["is_locked"],
        )
        return Response(data, status=status.HTTP_201_CREATED)
    else:
        logger.error("Payee ID's or Month or Year is None to UPDATE LOCK STATUS")
        return Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST)


def update_sec_kd_for_period_payees_in_list(
    client_id, period_end_date, payee_email_id_sec_kd_map, curr_time, log_context
):
    logger = iputils.LogWithContext(log_context)
    cska = CommissionSecondaryKdAccessorFactory(
        client_id, COMMISSION_TYPE.COMMISSION
    ).get_accessor()

    records_to_save = []

    payee_records = cska.get_records_for_payees(
        period_end_date, payee_email_id_sec_kd_map.keys()
    )

    # Gather records to update
    for record in payee_records:
        record.pk = None
        record.knowledge_begin_date = curr_time
        record.sec_kd = payee_email_id_sec_kd_map[record.payee_email_id]
        records_to_save.append(record)

    logger.info(
        f"Updating {len(records_to_save)} records for period_end_date: {period_end_date}"
    )

    cska.invalidate_and_create_records(
        period_end_date, payee_email_id_sec_kd_map, curr_time, records_to_save
    )


def update_commission_secondary_kd(client_id, payee_email_ids, date, log_context):
    """
    Function to update the secondary kd for the payees on unlocking commission
    """
    logger = iputils.LogWithContext(log_context)
    logger.info("Begin updating secondary kd for payees on unlocking commission")

    curr_time = timezone.now()
    date = make_aware(end_of_day(parse(date, dayfirst=True)))

    payee_emails_and_comm_sec_kds = CommissionAccessor(
        client_id
    ).get_commission_secondary_kd_for_payees(date, payee_email_ids)

    payee_emails_and_comm_sec_kds_map = {
        x["payee_email_id"]: x["secondary_kd"] for x in payee_emails_and_comm_sec_kds
    }

    # Invalidate and update records
    update_sec_kd_for_period_payees_in_list(
        client_id, date, payee_emails_and_comm_sec_kds_map, curr_time, log_context
    )

    logger.info("End updating secondary kd for payees on unlocking commission")


def update_lock_status_v2(
    client_id,
    batch_payee_ids,
    is_locked,
    date,
    audit,
    logger,
    abort_approvals=False,
    both_comm_and_sett_report_etl=False,
    e2e_sync_run_id=None,
):
    """optimised version of update_lock_status, does most of the operation in bulk mode, lock and unlock commission, payout status"""
    import everstage_etl.tasks.report_etl as report_etl
    from everstage_etl.tasks import snapshot_sync_by_period

    time = timezone.now()
    ###################### audit log #####################
    updated_at = time
    updated_by = audit["updated_by"]
    ######################################################
    output_data = {}
    func_start_millisecond = int(round(t1.time() * 1000))
    payout_snapshot_tasks_to_return = None
    if batch_payee_ids and date:
        end_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        event_type_code = EVENT["FREEZE_FREEZE-COMMISSION"]["code"]
        logger.update_context(
            {
                "date": date,
                "end_date": end_date,
            }
        )
        payee_ids = set(batch_payee_ids)
        lock_list = CommissionLockAccessor(client_id).get_payees_lock_in_end_date(
            end_date, payee_ids
        )
        updated_payees = []
        payout_status_objs = []
        if is_locked:
            lock_ids = []
            for lock in lock_list:
                if not lock or not lock.is_locked:
                    lock_ids.append(lock.lock_id)
                else:
                    payee_ids.remove(lock.payee_email_id)
            if len(payee_ids) > 0:
                start_millisecond = int(round(t1.time() * 1000))
                lock_commission_for_payees(
                    client_id, payee_ids, lock_ids, end_date, time, audit
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status lock_commission_for_payees {}".format(
                        end_millisecond - start_millisecond
                    )
                )
                start_millisecond = int(round(t1.time() * 1000))
                payout_status_objs = update_comm_payout_freeze_status(
                    client_id, end_date, payee_ids, True, False, updated_by
                )
                if can_abort_comm_adj_request(client_id):
                    # if comm_adj approval is enabled, abort all the comm_adj approval instances
                    logger.info(
                        "Aborting Comm Adj Approval Instances for payees {}".format(
                            payee_ids
                        )
                    )
                    abort_comm_adj_instances(
                        client_id,
                        end_date,
                        list(payee_ids),
                        audit,
                    )

                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status update_comm_payout_freeze_status {}".format(
                        end_millisecond - start_millisecond
                    )
                )
                # Todo : Check for abort comm_adj and use abort_payout_instances with entity type as comm_adj
            updated_payees = payee_ids
            add_freeze_unfreeze_period_payees_details(
                client_id,
                end_date,
                payees=list(payee_ids),
                freeze_type=CommissionChangesTypes.FREEZE.value,
            )
            # Add payout status changes entry for freeze / lock
            add_freeze_unfreeze_details_for_payout_status_changes(
                client_id=client_id,
                ped=end_date,
                payees=list(payee_ids),
                freeze_type=PayoutStatusChangesTypes.FREEZE.value,
            )

        else:
            unlock_ids = []
            unlock_payees = []
            event_type_code = EVENT["UNFREEZE_FREEZE-COMMISSION"]["code"]
            for lock in lock_list:
                if lock and lock.is_locked:
                    unlock_ids.append(lock.lock_id)
                    unlock_payees.append(lock.payee_email_id)

            if len(unlock_ids) > 0:
                start_millisecond = int(round(t1.time() * 1000))
                unlock_comm_objs = unlock_commission_for_payees(
                    client_id, unlock_payees, unlock_ids, end_date, time, audit
                )
                logger.info("Unlock for payees {}".format(str(unlock_ids)))
                subscription_plan = get_client_subscription_plan(client_id)
                report_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.REPORT.value
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status unlock_commission_for_payees {}".format(
                        end_millisecond - start_millisecond
                    )
                )

                if not both_comm_and_sett_report_etl:
                    start_millisecond = int(round(t1.time() * 1000))
                    report_etl.report_object_etl_by_period_wrapper.si(
                        client_id=client_id,
                        ped=end_date,
                        sped=None,
                        payee_email_id=batch_payee_ids,
                        is_unfreeze_mode=True,
                        is_custom_mode=False,
                        log_context=logger.get_context(),
                    ).set(queue=report_queue_name).apply_async()
                    end_millisecond = int(round(t1.time() * 1000))
                    logger.info(
                        "update_lock_status report_object_etl_by_commission_period_wrapper {}".format(
                            end_millisecond - start_millisecond
                        )
                    )

                start_millisecond = int(round(t1.time() * 1000))
                payout_status_objs = update_payout_details(
                    client_id, end_date, batch_payee_ids, audit=audit
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status update_payout_details {}".format(
                        end_millisecond - start_millisecond
                    )
                )
                start_millisecond = int(round(t1.time() * 1000))
                if abort_approvals:
                    # Abort approval instances if active
                    abort_payout_instances(client_id, date, batch_payee_ids, audit)
                # When payout_snapshot is enabled, trigger snapshot sync task

                add_freeze_unfreeze_period_payees_details(
                    client_id,
                    end_date,
                    payees=list(payee_ids),
                    freeze_type=CommissionChangesTypes.UNFREEZE.value,
                )
                # Add payout status changes entry for unfreeze / unlock
                add_freeze_unfreeze_details_for_payout_status_changes(
                    client_id=client_id,
                    ped=end_date,
                    payees=list(payee_ids),
                    freeze_type=PayoutStatusChangesTypes.UNFREEZE.value,
                )

                if can_run_payout_snapshot_etl(client_id):
                    snapshot_etl_queue_name = get_queue_name_respect_to_task_group(
                        client_id, subscription_plan, TaskGroupEnum.SNAPSHOT.value
                    )
                    # if snowflake payout snapshot enabled, return tasks
                    # these tasks will be chained with Report ETL tasks
                    if can_run_sf_payout_snapshot(client_id):
                        logger.info(
                            "update_lock_status_v2: Returning payout and inter_comm snapshot tasks"
                        )
                        payout_snapshot_tasks_to_return = snapshot_sync_by_period(
                            client_id=client_id,
                            payee_list=list(payee_ids),
                            curr_date=end_date,
                            unlocked_objs=unlock_comm_objs,
                            return_tasks=True,
                            e2e_sync_run_id=e2e_sync_run_id,
                        )
                    else:
                        snapshot_sync_by_period.si(
                            client_id=client_id,
                            payee_list=list(payee_ids),
                            curr_date=end_date,
                            unlocked_objs=unlock_comm_objs,
                        ).set(queue=snapshot_etl_queue_name).apply_async()
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status abort_payout_instances {}".format(
                        end_millisecond - start_millisecond
                    )
                )
            updated_payees = unlock_payees

        payee_payrolls = EmployeePayrollAccessor(client_id).get_employee_payroll(
            end_date, batch_payee_ids
        )
        payroll_dict = (
            {x["employee_email_id"]: x for x in payee_payrolls}
            if payee_payrolls
            else {}
        )

        client = get_client(client_id)
        start_millisecond = int(round(t1.time() * 1000))
        for payee in updated_payees:
            period = get_period_start_and_end_date(
                end_date,
                client.fiscal_start_month,
                payroll_dict[payee]["payout_frequency"].lower(),
                client_id=client_id,
            )
            delete_payee_commission_cache(
                client_id=client_id,
                psd=period["start_date"],
                ped=period["end_date"],
                email_id=payee,
            )
            output_data[payee] = {"status": "SUCCESS", "payee_id": payee}
        end_millisecond = int(round(t1.time() * 1000))
        logger.info(
            "update_lock_status delete_commission_cache {}".format(
                end_millisecond - start_millisecond
            )
        )
        start_millisecond = int(round(t1.time() * 1000))
        track_commission_lock_data(
            client_id,
            is_locked,
            event_type_code,
            updated_by,
            updated_at,
            payee_ids,
            date,
            payout_status_objs,
        )
        end_millisecond = int(round(t1.time() * 1000))
        logger.info(
            "update_lock_status track_commission_lock_data {}".format(
                end_millisecond - start_millisecond
            )
        )

        func_end_millisecond = int(round(t1.time() * 1000))
        logger.info(
            "update_lock_status v2 {}".format(
                func_end_millisecond - func_start_millisecond
            )
        )
        return (
            Response(output_data, status=status.HTTP_201_CREATED),
            payout_snapshot_tasks_to_return,
        )
    else:
        logger.error("Payee ID's or Month or Year is None to UPDATE LOCK STATUS")
        return (
            Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST),
            payout_snapshot_tasks_to_return,
        )


def track_commission_lock_data(
    client_id,
    is_locked,
    event_type_code,
    updated_by,
    updated_at,
    payee_ids,
    date,
    payout_status_objs,
):
    """commission lock, unlock bulk insertion into audit_trail and Segment"""

    time_period = str(date)
    audit_data_list = []
    bulk_action = False
    if len(payee_ids) > 1:
        bulk_action = True
    for payout_status in payout_status_objs:
        payee = payout_status.payee_email_id
        audit_data = {}
        audit_data["calculated_commission"] = payout_status.commission
        audit_data["payment_status"] = payout_status.payment_status
        if is_locked:
            audit_data["calculation_status"] = "Locked"
            event_name = SegmentEvents.FREEZE_COMMISSIONS.value
        else:
            audit_data["calculation_status"] = "Not Locked"
            event_name = SegmentEvents.UNFREEZE_COMMISSIONS.value

        event_key = payee + "#" + date
        summary = payee
        audit_data["event_key"] = event_key
        audit_data["summary"] = summary
        audit_data_list.append(audit_data)

        analytics_data = {
            "user_id": updated_by,
            "event_name": event_name,
            "event_properties": {
                SegmentProperties.TIME_PERIOD.value: time_period,
                SegmentProperties.PAYEE.value: payee,
                SegmentProperties.BULK_ACTION.value: bulk_action,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
    audit_services.bulk_log(
        client_id, event_type_code, updated_by, updated_at, audit_data_list
    )


def unlock_commission_for_payees(client_id, payees, lock_ids, end_date, time, audit):
    """unlock commission_lock and commission_lock_details for a list of payees"""
    comm_lock_objs = CommissionLockAccessor(client_id).mark_as_unlocked_for_payees(
        payees, end_date, time, audit
    )
    CommissionLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_ids(
        lock_ids, time
    )
    return comm_lock_objs


def lock_commission_for_payees(client_id, payee_ids, lock_ids, ped, time, audit):
    """creates commission_lock and commission_lock_details for a list of payees"""
    payees_commission_data = CommissionAccessor(
        client_id
    ).get_kd_commission_data_for_end_date_payees(
        kd=time, ped=ped, payee_emails=payee_ids, as_dicts=True
    )
    CommissionLockAccessor(client_id).invalidate_lock_payees(payee_ids, ped, time)
    CommissionLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_ids(
        lock_ids, time
    )

    payee_commission_dict = {}
    for payee_commission in payees_commission_data:
        if payee_commission["payee_email_id"] in payee_commission_dict:
            payee_commission_list = payee_commission_dict[
                payee_commission["payee_email_id"]
            ]
            payee_commission_list.append(payee_commission)
        else:
            payee_commission_list = []
            payee_commission_list.append(payee_commission)
            payee_commission_dict[payee_commission["payee_email_id"]] = (
                payee_commission_list
            )
    skipped_payees = set(payee_ids) - set(payee_commission_dict.keys())
    if skipped_payees:
        skipped_payee_payout_freq = get_payout_details(
            client_id=client_id, period_end_date=ped, emails=list(skipped_payees)
        )
        lock_data_list = []
        for payee in skipped_payees:
            psd = get_start_date_by_freq_and_end_date(
                ped, skipped_payee_payout_freq[payee]
            )
            lock_data_list.append(
                {
                    "client": client_id,
                    "lock_id": uuid.uuid4(),
                    "additional_details": audit,
                    "knowledge_begin_date": time,
                    "period_start_date": psd,
                    "period_end_date": ped,
                    "is_locked": True,
                    "locked_knowledge_date": time,
                    "commission_snapshot_id": None,
                    "commission_plan_ids": None,
                    "payee_email_id": payee,
                }
            )

        lock_ser = CommissionLockUpdateSerializer(data=lock_data_list, many=True)
        if lock_ser.is_valid():
            CommissionLockAccessor(client_id).persist_commission_lock(lock_ser)

    if payee_commission_dict is not None:
        bulk_create_commission_locks_for_payees(
            client_id, payee_commission_dict, audit, time
        )


def bulk_create_commission_locks_for_payees(
    client_id, payee_commission_dict, audit, time
):
    """creates new record in commission_lock and commission_lock_details for a list of payees"""
    lock_data_list = []
    lock_details = []

    for payee, payee_commission in payee_commission_dict.items():
        grouped_by_plan = nest(payee_commission, "commission_plan_id", "criteria_id")
        comm_plans = list(grouped_by_plan.keys())  # type: ignore
        plan_list = []
        for plan in comm_plans:
            plan_list.append(str(plan))

        lock_id = uuid.uuid4()
        lock_data = {
            "client": client_id,
            "lock_id": lock_id,
            "additional_details": audit,
            "knowledge_begin_date": time,
            "period_start_date": payee_commission[0]["period_start_date"],  # type: ignore
            "period_end_date": payee_commission[0]["period_end_date"],  # type: ignore
            "is_locked": True,
            "locked_knowledge_date": time,
            "commission_snapshot_id": payee_commission[0]["commission_snapshot_id"],  # type: ignore
            "commission_plan_ids": plan_list,
            "payee_email_id": payee,
        }
        lock_data_list.append(lock_data)

        for _plan_id, plan_data in grouped_by_plan.items():  # type: ignore
            for _criteria_id, criteria_comm_data in plan_data.items():  # type: ignore
                lock_detail = {
                    "client": client_id,
                    "additional_details": audit,
                    "knowledge_begin_date": time,
                    "payee_email_id": criteria_comm_data[0]["payee_email_id"],
                    "commission_plan_id": criteria_comm_data[0]["commission_plan_id"],
                    "criteria_id": criteria_comm_data[0]["criteria_id"],
                    "line_item_type": criteria_comm_data[0]["line_item_type"],
                    "lock_id": lock_id,
                }
                line_items = []
                criteria_amount = 0
                for criteria_data in criteria_comm_data:
                    li = criteria_data["line_item_id"]
                    if not li:
                        if not criteria_data["show_do_nothing"]:
                            line_items = criteria_data["context_ids"]
                    else:
                        if not criteria_data["show_do_nothing"]:
                            line_items.append(criteria_data["line_item_id"])
                            criteria_amount = criteria_amount + criteria_data["amount"]

                lock_detail["line_item_ids"] = line_items
                lock_detail["amount"] = criteria_amount
                lock_details.append(lock_detail)

    lock_ser = CommissionLockUpdateSerializer(data=lock_data_list, many=True)
    if lock_ser.is_valid():
        CommissionLockAccessor(client_id).persist_commission_lock(lock_ser)
    lock_det_ser = CommLockDetailUpdateSerializer(data=lock_details, many=True)
    if lock_det_ser.is_valid():
        CommissionLockDetailsAccessor(client_id).persist_commission_lock_detail(
            lock_det_ser
        )


def abort_payout_instances(client_id, date, payee_ids, audit_data):
    instance_params = {
        "date": date,
        "instance_details": [{"email_id": payee_id} for payee_id in payee_ids],
    }
    abort_instances(
        client_id, APPROVAL_ENTITY_TYPES.PAYOUT.value, instance_params, audit_data
    )


def add_lock_entry(client_id, payee, end_date, time, audit, lock):
    if lock:
        CommissionLockAccessor(client_id).invalidate_lock(payee, end_date, time)
        CommissionLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_id(
            lock.lock_id, time
        )
    lock_id = uuid.uuid4()
    all_commission_plan_data = CommissionAccessor(
        client_id
    ).get_kd_commission_data_for_end_date(
        kd=time, ped=end_date, payee_email=payee, as_dicts=True
    )
    if len(all_commission_plan_data) > 0:
        grouped_by_plan = nest(
            all_commission_plan_data, "commission_plan_id", "criteria_id"
        )
        comm_plans = list(grouped_by_plan.keys())  # type: ignore
        plan_list = []
        for plan in comm_plans:
            plan_list.append(str(plan))
        lock_data = {
            "client": client_id,
            "lock_id": lock_id,
            "additional_details": audit,
            "knowledge_begin_date": time,
            "period_start_date": all_commission_plan_data[0]["period_start_date"],  # type: ignore
            "period_end_date": all_commission_plan_data[0]["period_end_date"],  # type: ignore
            "is_locked": True,
            "locked_knowledge_date": time,
            "commission_snapshot_id": all_commission_plan_data[0]["commission_snapshot_id"],  # type: ignore
            "commission_plan_ids": plan_list,
            "payee_email_id": payee,
        }
        # construct commission lock detail data
        lock_details = []
        for _plan_id, plan_data in grouped_by_plan.items():  # type: ignore
            for _criteria_id, criteria_comm_data in plan_data.items():  # type: ignore
                lock_detail = {
                    "client": client_id,
                    "additional_details": audit,
                    "knowledge_begin_date": time,
                    "payee_email_id": criteria_comm_data[0]["payee_email_id"],
                    "commission_plan_id": criteria_comm_data[0]["commission_plan_id"],
                    "criteria_id": criteria_comm_data[0]["criteria_id"],
                    "line_item_type": criteria_comm_data[0]["line_item_type"],
                    "lock_id": lock_id,
                }
                line_items = []
                criteria_amount = 0
                for criteria_data in criteria_comm_data:
                    li = criteria_data["line_item_id"]
                    if not li:
                        if not criteria_data["show_do_nothing"]:
                            line_items = criteria_data["context_ids"]
                    else:
                        if not criteria_data["show_do_nothing"]:
                            line_items.append(criteria_data["line_item_id"])
                            criteria_amount = criteria_amount + criteria_data["amount"]

                lock_detail["line_item_ids"] = line_items
                lock_detail["amount"] = criteria_amount
                lock_details.append(lock_detail)
        lock_ser = CommissionLockSerializer(data=lock_data)  # type: ignore
        lock_det_ser = CommLockDetailUpdateSerializer(data=lock_details, many=True)  # type: ignore

        try:
            if lock_ser.is_valid() and lock_det_ser.is_valid():
                CommissionLockAccessor(client_id).persist_commission_lock(lock_ser)
                CommissionLockDetailsAccessor(client_id).persist_commission_lock_detail(
                    lock_det_ser
                )
            else:
                print("No commission")
        except Exception as e:
            raise SQLParseError() from e


def is_commission_changed(client_id, end_date, payee_emails):
    period_end_date = make_aware(end_of_day(parse(end_date, dayfirst=True)))
    before_commission_lock, after_commission_lock = CommissionAccessor(
        client_id
    ).get_commissions_before_and_after_lock(period_end_date, payee_emails)
    return compare_payout_changes(before_commission_lock, after_commission_lock)


def compare_payout_changes(before_lock: list[dict], after_lock: list[dict]) -> set[str]:
    """
    Compare commissions before and after the lock, looking for missing line items or changed amounts.

    Args:
        before_lock (list[dict]): List of commission entries before the lock.
        after_lock (list[dict]): List of commission entries after the lock.

    Returns:
        list[dict]: A list of changes, each entry contains payee_email, line_item, and amount.
    """
    before_dict = {}
    for entry in before_lock:
        if entry["line_item_id"]:
            key = (entry["payee_email_id"], entry["line_item_id"])
            before_dict[key] = entry["amount"]

    after_dict = {}
    for entry in after_lock:
        if entry["line_item_id"]:
            key = (entry["payee_email_id"], entry["line_item_id"])
            after_dict[key] = entry["amount"]

    # List to store differences for detailed warning - Not used for now.
    differences = []
    payout_changed_payees = set()

    # Compare each line item in before_lock with after_lock
    for (payee_email, line_item), before_amount in before_dict.items():
        if (payee_email, line_item) not in after_dict:
            # Line item is missing in after_lock, so we append it
            differences.append(
                {
                    "payee_email_id": payee_email,
                    "line_item": line_item,
                    "amount": before_amount,
                    "status": "line item deleted",
                }
            )
            payout_changed_payees.add(payee_email)
        elif after_dict[(payee_email, line_item)] != before_amount:
            # Line item exists, but amount has changed
            after_amount = after_dict[(payee_email, line_item)]
            differences.append(
                {
                    "payee_email_id": payee_email,
                    "line_item": line_item,
                    "amount_before": before_amount,
                    "amount_after": after_amount,
                    "status": "amount changed",
                }
            )
            payout_changed_payees.add(payee_email)

    # Compare each line item in after_lock to see if there are any new line items
    for (payee_email, line_item), after_amount in after_dict.items():
        if (payee_email, line_item) not in before_dict:
            differences.append(
                {
                    "payee_email_id": payee_email,
                    "line_item": line_item,
                    "amount": after_amount,
                    "status": "new line item",
                }
            )
            payout_changed_payees.add(payee_email)

    return payout_changed_payees


def export_changed_payout_details(client_id: int, email_list: List[str]) -> BytesIO:
    """
    Generates an Excel file with the user data (email and full name) and returns the file as BytesIO.

    Args:
        client_id (int): Client ID.
        email_list (List[str]): List of user emails.

    Returns:
        BytesIO: The generated Excel file in memory.
    """
    output = BytesIO()

    workbook = xlsxwriter.Workbook(output, {"remove_timezone": True})

    worksheet = workbook.add_worksheet("User Data")

    bold = workbook.add_format({"bold": True, "font_size": 9})

    worksheet.write("A1", "S.No", bold)
    worksheet.write("B1", "Email", bold)
    worksheet.write("C1", "Name", bold)

    employee_metadata: List[Dict[str, Any]] = list(
        EmployeeAccessor(client_id).get_employees_name_as_dict_with_full_name(
            email_list
        )
    )

    for index, employee in enumerate(employee_metadata, start=1):
        worksheet.write(index, 0, index)  # S.No
        worksheet.write(index, 1, employee["employee_email_id"])
        worksheet.write(index, 2, employee["full_name"])

    workbook.close()

    output.seek(0)

    return output


def check_sync_running_status(client_id: int) -> bool:
    """
    Returns True if any of the sync task is running for the client.
    """
    activities = [
        ETL_ACTIVITY.COMMISSION_CALCULATION.value,
        ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
        ETL_ACTIVITY.SETTLEMENT_CALCULATION.value,
    ]
    return ETLSyncStatusReaderAccessor(client_id).is_any_sync_running(
        activity=activities
    )


def get_payees_with_pending_changes(client_id: int, payee_emails: list[str], ped: str):
    end_date = make_aware(end_of_day(parse(ped, dayfirst=True)))
    sec_kd_accessor = CommissionSecondaryKdAccessorFactory(
        client_id, COMMISSION_TYPE.COMMISSION
    ).get_accessor()
    sec_kd_data = sec_kd_accessor.get_sec_kd_for_payees_in_period_during_lock(
        payee_emails, end_date
    )
    payees_with_pending_changes = PlanModificationChangesAccessor(
        client_id
    ).get_distinc_payees_with_pending_changes(sec_kd_data)
    if payees_with_pending_changes:
        # Send the list of payees as a csv file to the user
        csv_file = StringIO()
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(["Payee Email"])
        for payee in payees_with_pending_changes:
            csv_writer.writerow([payee])
        csv_file.seek(0)
        return True, set(payees_with_pending_changes), csv_file.getvalue()
    return False, set(), None


# def add_payout_entry(
#     client_id, payee, end_date, time, audit, commission_details, payout
# ):
#     if payout:
#         PayoutAccessor(client_id).invalidate_payout(payee, end_date, time)
#
#     all_commission_plan_data = CommissionAccessor(
#         client_id
#     ).get_kd_commission_data_for_end_date(
#         kd=time, ped=end_date, payee_email=payee, as_dicts=True
#     )
#     if len(all_commission_plan_data) > 0:
#         payout_data = {
#             "client": client_id,
#             "additional_details": audit,
#             "knowledge_begin_date": time,
#             "period_start_date": all_commission_plan_data[0][
#                 "period_start_date"
#             ],
#             "period_end_date": all_commission_plan_data[0]["period_end_date"],
#             "payee_id": payee,
#             "payout_knowledge_date": time,
#             "payout_amount": Decimal(
#                 commission_details[payee]["calculated_adjustments"][
#                     "commission"
#                 ]
#             )
#             + Decimal(
#                 commission_details[payee]["calculated_adjustments"][
#                     "commission_adj"
#                 ]
#             )
#             + Decimal(
#                 commission_details[payee]["calculated_adjustments"]["draw_adj"]
#             ),
#             "is_paid": True,
#             "payout_adjustments": commission_details[payee][
#                 "calculated_adjustments"
#             ],
#         }
#         payout_ser = PayoutSerializer(data=payout_data)
#         try:
#             if payout_ser.is_valid():
#                 PayoutAccessor(client_id).persist_payout(payout_ser)
#                 if get_client(client_id).client_notification and (
#                     os.environ.get("ENV") == "STAGING"
#                     or os.environ.get("ENV") == "PRODUCTION"
#                 ):
#                     payout_paid_mails(
#                         client_id,
#                         payee,
#                         payout_data["period_end_date"],
#                         payout_data["payout_amount"],
#                     )
#             return payout_data
#         except:
#             raise SQLParseError()
#     else:
#         print("No commission")
