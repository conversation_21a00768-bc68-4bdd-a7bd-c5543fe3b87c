from pydash import nest

import commission_engine.utils.date_utils as date_utils
import interstage_project.utils as iputils
import spm.services.commission_data_services as commission_data_services
from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.commission_accessor import QuotaErosionAccessor
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import HierarchyAccessor
from spm.accessors.quota_acessors import EmployeeQuotaAccessor, QuotaAccessor


class CommissionsOverview:
    """Commissions overview for a payee in a period (returns values in payee_currency)"""

    def __init__(self, client_id, psd, ped, payee_email):
        self.client_id = client_id
        self.psd = psd
        self.ped = ped
        self.payee_email = payee_email

    def get_quota_attainment_details(self):
        """ """
        log_context = {
            "client_id": self.client_id,
            "period_start_date": self.psd,
            "period_end_date": self.ped,
            "payee_email": self.payee_email,
        }
        logger = iputils.LogWithContext(log_context)
        paid_kd = commission_data_services.get_latest_or_kd_if_paid(
            self.client_id, self.psd, self.ped, self.payee_email
        )
        if paid_kd:
            locked_kd = CommissionLockAccessor(
                self.client_id
            ).get_kd_locked_kd_for_locked_comm_in_period(
                psd=self.psd, ped=self.ped, payee_email=self.payee_email, kd=paid_kd
            )
        else:
            locked_kd = CommissionLockAccessor(
                self.client_id
            ).get_locked_kd_for_locked_comm_in_period(
                psd=self.psd, ped=self.ped, payee_email=self.payee_email
            )
        if locked_kd:
            # is_team is passed as None to retrieve all quotas. showing &Team quota for manager is done in frontend
            quota_data = QuotaErosionAccessor(
                self.client_id
            ).get_payee_snapshot_quota_data(
                payee_email=self.payee_email,
                locked_kd=locked_kd,
                psd=self.psd,
                ped=self.ped,
                is_team=None,
                fields=[],
            )
        else:
            quota_data = QuotaErosionAccessor(self.client_id).get_quota_data(
                psd=self.psd,
                ped=self.ped,
                payee_email=self.payee_email,
                is_team=None,
                fields=[],
            )
        logger.info(
            "Quota data Fetched for payee: {} end date: {}".format(
                self.payee_email, self.ped
            )
        )

        # Get quota schedule
        client = get_client(self.client_id)
        fiscal_year = date_utils.get_fiscal_year(client.fiscal_start_month, self.ped)
        quotas = EmployeeQuotaAccessor(
            self.client_id
        ).get_all_latest_employee_quota_using_effective_date(
            self.payee_email, fiscal_year, None, self.ped
        )

        quota_schedule = {
            (quota.quota_category_name, quota.is_team_quota): quota.quota_schedule_type
            for quota in quotas
        }

        grouped_data = nest(quota_data, "quota_category_name", "is_team")
        criteria_grouped_data = nest(quota_data, "criteria_id")
        logger.info("Grouped quota data by category and is_team flag")

        quota_category_names = list(grouped_data.keys())
        qs = QuotaAccessor(self.client_id).get_all_quota_categories_given_names(
            quota_category_names,
            projection=["quota_category_name", "display_name", "quota_currency_type"],
        )
        display_name_map = {
            record["quota_category_name"]: record["display_name"] for record in qs
        }
        quota_currency_map = {
            record["quota_category_name"]: record["quota_currency_type"]
            for record in qs
        }

        country_currency_map = CountriesAccessor(
            self.client_id
        ).get_currency_code_and_symbol_map()

        currency_locale_map = CountriesAccessor(
            self.client_id
        ).get_currency_code_and_locale_map()

        if "Primary" in quota_category_names:
            display_name_map["Primary"] = "Primary"

        result = []
        for category in grouped_data:
            for is_team in grouped_data[category]:
                if len(grouped_data[category][is_team]):
                    quota_value = grouped_data[category][is_team][0]["qv"]
                    quota_erosion = 0
                    record = (
                        grouped_data[category][is_team][0]
                        if grouped_data[category][is_team]
                        else None
                    )
                    cum_qe = 0
                    if record and "cumulative_qe" in record:
                        cum_qe = record["cumulative_qe"]
                    for each_item in grouped_data[category][is_team]:
                        quota_erosion += each_item["quota_erosion"]
                    quota_attainment = (
                        ((quota_erosion + cum_qe) / quota_value) * 100
                        if quota_value
                        else 0
                    )
                    attained_quota = (quota_value * quota_attainment) / 100
                    quota_currency_code = quota_currency_map.get(category)
                    data = {
                        "quota_category_name": category,
                        "display_name": display_name_map.get(category),
                        "quota_currency_symbol": country_currency_map.get(
                            quota_currency_code
                        ),
                        "quota_locale_id": currency_locale_map.get(quota_currency_code),
                        "is_team": is_team,
                        "quota_erosion": quota_erosion,
                        "quota_value": quota_value,
                        "quota_attainment": quota_attainment,
                        "attained_quota": attained_quota,
                        "quota_schedule": quota_schedule.get(
                            (str(category), is_team), None
                        ),
                    }
                    result.append(data)

        criteria_quota_erosion_map = {}
        for criteria in criteria_grouped_data:
            criteria_quota_erosion_map[str(criteria)] = 0
            for item in criteria_grouped_data[criteria]:
                criteria_quota_erosion_map[str(criteria)] += item["quota_erosion"]
            if criteria_grouped_data[criteria]:
                quota_currency_code = quota_currency_map.get(
                    criteria_grouped_data[criteria][0]["quota_category_name"], ""
                )
                currency = country_currency_map.get(quota_currency_code, "")
                criteria_quota_erosion_map[str(criteria)] = (
                    currency,
                    criteria_quota_erosion_map[str(criteria)],
                )
        # check if payee has reportees
        reportees = HierarchyAccessor(self.client_id).get_reportees(
            self.ped, self.payee_email
        )
        has_reportees = False
        if reportees and len(reportees) > 0:
            has_reportees = True

        logger.info("End of get_quota_attainment_details")

        return {
            "has_reportees": has_reportees,
            "quota_data": result,
            "criteria_quota_erosion_map": [
                {
                    "criteria_id": key,
                    "currency_symbol": value[0],
                    "quota_erosion": value[1],
                }
                for key, value in criteria_quota_erosion_map.items()
            ],
        }
