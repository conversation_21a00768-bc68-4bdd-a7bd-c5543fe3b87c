from _datetime import datetime
from django.utils import timezone
from django.utils.timezone import make_aware

from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.custom_calendar_accessors import (
    CustomCalendarAccessor,
    CustomPeriodsAccessor,
)
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.constants.localization_constants import LocalizationEngMessages as LocMsgs
from spm.services.config_services.employee_services_utils import (
    PayoutFreqMsgs,
    custom_field_date_conversion_util,
)
from spm.services.localization_services import get_localized_message_service
from spm.utils import is_valid_uuid


def construct_employee_dict(client_id, audit, employee_serializer, request, time):
    if "first_name" in request:
        emp_dict = {
            "client": client_id,
            "additional_details": audit,
            "employee_email_id": request["employee_email_id"],
            "first_name": request["first_name"],
            "last_name": request["last_name"],
            "user_role": request["user_role"],
            "knowledge_begin_date": time,
            "created_date": time,
            "user_source": "manual",
            "created_by": audit.get("updated_by"),
            "send_notification": False,
            "employee_config": (
                request["employee_config"] if "employee_config" in request else None
            ),
            "status": "Added",
        }
        return employee_serializer(data=emp_dict)


def construct_employee_payroll_dict(
    client_id, audit, payroll_serializer, request, time
):
    if "employee_payroll" in request:
        payroll_dict = request["employee_payroll"].copy()
        payroll_dict["client"] = client_id
        payroll_dict["additional_details"] = audit
        payroll_dict["employee_email_id"] = request["employee_email_id"]
        payroll_dict["knowledge_begin_date"] = time
        if type(payroll_dict["effective_start_date"]) is str:
            payroll_dict["effective_start_date"] = datetime.strptime(
                payroll_dict["effective_start_date"], "%d-%b-%Y"
            )
        if type(payroll_dict["joining_date"]) is str:
            payroll_dict["joining_date"] = datetime.strptime(
                payroll_dict["joining_date"], "%d-%b-%Y"
            )
        payroll_dict["variable_pay"] = (
            payroll_dict["variable_pay"] if "variable_pay" in payroll_dict else 0
        )
        return payroll_serializer(data=payroll_dict)


def construct_hierarchy_dict(client_id, audit, hierarchy_serializer, request, time):
    if "employee_hierarchy" in request:
        hier_dict = request["employee_hierarchy"].copy()
        hier_dict["client"] = client_id
        hier_dict["additional_details"] = audit
        hier_dict["employee_email_id"] = request["employee_email_id"]
        hier_dict["knowledge_begin_date"] = time
        if type(hier_dict["effective_start_date"]) is str:
            hier_dict["effective_start_date"] = datetime.strptime(
                hier_dict["effective_start_date"], "%d-%b-%Y"
            )
        return hierarchy_serializer(data=hier_dict)


def construct_custom_field_dict(
    client_id,
    audit,
    custom_field_serializer,
    request,
    time,
    custom_effective_start_date=None,
):
    if "custom_field" in request:
        custom_field_dict = dict()
        custom_field_dict["data"] = request["custom_field"]["data"]
        custom_field_dict["client"] = client_id
        custom_field_dict["additional_details"] = audit
        custom_field_dict["email"] = request["employee_email_id"]
        custom_field_dict["knowledge_begin_date"] = time
        custom_field_dict["effective_start_date"] = (
            custom_effective_start_date
            if custom_effective_start_date
            else make_aware(datetime.strptime("01-JAN-2000", "%d-%b-%Y"))
        )
        return custom_field_serializer(data=custom_field_dict)


def new_construct_custom_field_dict(
    client_id, request, custom_effective_start_date, is_overwrite=False
):
    custom_field_list = []
    effective_dated_fields = CustomFieldsAccessor(
        client_id
    ).get_custom_fields_list_with_filters(
        {"is_archived": False, "is_effective_dated": True}
    )
    if "custom_field" in request:
        for name, value in request["custom_field"]["data"].items():
            custom_dict = {
                "name": name,
                "value": value,
                "effective_start_date": (
                    custom_effective_start_date
                    if name in effective_dated_fields and not is_overwrite
                    else None
                ),
            }
            custom_field_list.append(custom_dict)
    return custom_field_list


def is_email_exists(client_id, email):
    emails = EmployeeAccessor(client_id).get_all_employee_email(user_status="All")
    for e in emails:
        if email == e["employee_email_id"]:
            return True
    return False


def modify_bulk_add_custom_field_data(c_ser, client_id):
    checkbox_custom_fields = CustomFieldsAccessor(
        client_id
    ).get_active_fields_by_field_type("Checkbox")
    date_fields = CustomFieldsAccessor(client_id).get_active_fields_by_field_type(
        "Date"
    )
    drop_down_fields = CustomFieldsAccessor(client_id).get_active_fields_by_field_type(
        "Dropdown"
    )
    c_ser = custom_field_date_conversion_util(c_ser, date_fields)
    for key, value in c_ser.validated_data["data"].items():
        # Save checkbox data as boolean
        if key in [x["system_name"] for x in checkbox_custom_fields]:
            if type(c_ser.validated_data["data"][key]) is str:
                c_ser.validated_data["data"][key] = True if value == "True" else False

        # checking and converting dropdown display name to system name
        if key in [x["system_name"] for x in drop_down_fields]:
            options = next(
                item for item in drop_down_fields if item["system_name"] == key
            )["options"]
            if c_ser.validated_data["data"][key] and not any(
                x["system_name"] == c_ser.validated_data["data"][key] for x in options
            ):
                display_name_sys_name_map = {
                    x["display_name"]: x["system_name"] for x in options
                }
                if c_ser.validated_data["data"][key] in display_name_sys_name_map:
                    c_ser.validated_data["data"][key] = display_name_sys_name_map[
                        c_ser.validated_data["data"][key]
                    ]

    return c_ser


def moidfy_bulk_custom_field_data(client_id, custom_fields):
    checkbox_custom_fields = CustomFieldsAccessor(
        client_id
    ).get_active_fields_by_field_type("Checkbox")
    drop_down_fields = CustomFieldsAccessor(client_id).get_active_fields_by_field_type(
        "Dropdown"
    )
    for custom_data in custom_fields:
        # Save checkbox data as boolean
        if custom_data["name"] in [x["system_name"] for x in checkbox_custom_fields]:
            if type(custom_data["value"]) is str:
                custom_data["value"] = True if custom_data["value"] == "True" else False

        # checking and converting dropdown display name to system name
        if custom_data["name"] in [x["system_name"] for x in drop_down_fields]:
            options = next(
                item
                for item in drop_down_fields
                if item["system_name"] == custom_data["name"]
            )["options"]
            if custom_data["value"] and not any(
                x["system_name"] == custom_data["value"] for x in options
            ):
                display_name_sys_name_map = {
                    x["display_name"]: x["system_name"] for x in options
                }
                if custom_data["value"] in display_name_sys_name_map:
                    custom_data["value"] = display_name_sys_name_map[
                        custom_data["value"]
                    ]

    return custom_fields


def handle_payout_frequency_change_bulk(
    client_id: int,
    new_effective_start_date: timezone.datetime,
    new_payout_freq: str,
    all_locks: list[dict],
    all_plans: list[dict],
    all_forecast_plans: list[dict],
):
    """
    Handles Frequency Change by validating a existense of future commission lock, active commission plan
    or quota, for a given effective start date.
    """

    is_custom_freq = is_valid_uuid(new_payout_freq) and (
        CustomCalendarAccessor(client_id).does_custom_calendar_exist(new_payout_freq)
    )
    if is_custom_freq:
        record = CustomPeriodsAccessor(client_id).get_custom_period_by_date(
            new_payout_freq, new_effective_start_date
        )
        if not record:
            message = get_localized_message_service(
                LocMsgs.PAYOUT_FREQ_CHANGE__NOT_CONFIGURED.value,
                client_id,
            )
            return PayoutFreqMsgs.NOT_CONFIGURED, message

    # TODO: Localization: Change the way payout_frequency displayed in error view
    # of bulk upload should be localized.

    for plan in all_plans:
        # check if any commission plan would be active for the given effective date
        if new_effective_start_date <= plan["effective_end_date"]:
            message = get_localized_message_service(
                LocMsgs.PAYOUT_FREQ_CHANGE__PLAN.value, client_id
            )

            return PayoutFreqMsgs.PLAN.value, message

    for lock in all_locks:
        # check if the commission is locked for any future date for the new effective
        # start date
        if lock["period_end_date"] >= new_effective_start_date:
            message = get_localized_message_service(
                LocMsgs.PAYOUT_FREQ_CHANGE__COMMISSION_LOCK.value, client_id
            )
            return PayoutFreqMsgs.COMMISSION_LOCK.value, message

    for plan in all_forecast_plans:
        # check if any forecast plan would be active for the given effective date
        if new_effective_start_date <= plan["effective_end_date"]:
            message = get_localized_message_service(
                LocMsgs.PAYOUT_FREQ_CHANGE__FORECAST_PLAN.value, client_id
            )

            return PayoutFreqMsgs.FORECAST_PLAN.value, message

    return None, None


def handle_payroll_esd_change_bulk(
    client_id: int,
    new_effective_start_date: timezone.datetime,
    all_locks: list[dict],
    all_plans: list[dict],
    all_forecast_plans: list[dict],
    is_perv_payroll: bool = False,
):
    """
    Handles payroll effective start date by validating a existense of future commission lock, active commission plan.
    """

    for plan in all_plans:
        # check if any commission plan would be active for the given effective date
        if (
            not is_perv_payroll
            and new_effective_start_date > plan["effective_start_date"]
        ) or (
            is_perv_payroll and new_effective_start_date <= plan["effective_end_date"]
        ):
            message = get_localized_message_service(
                LocMsgs.ESD_CHANGE_PLAN.value, client_id
            )

            return PayoutFreqMsgs.PLAN.value, message

    for lock in all_locks:

        # check if the commission is locked for any future date for the new effective
        # start date
        if (
            not is_perv_payroll and new_effective_start_date > lock["period_start_date"]
        ) or (is_perv_payroll and new_effective_start_date <= lock["period_end_date"]):
            message = get_localized_message_service(
                LocMsgs.ESD_CHANGE_COMMISSION_LOCK.value, client_id
            )
            return PayoutFreqMsgs.COMMISSION_LOCK.value, message

    for plan in all_forecast_plans:
        # check if any forecast plan would be active for the given effective date
        if (
            not is_perv_payroll
            and new_effective_start_date > plan["effective_start_date"]
        ) or (
            is_perv_payroll and new_effective_start_date <= plan["effective_end_date"]
        ):
            message = get_localized_message_service(
                LocMsgs.ESD_CHANGE_FORECAST_PLAN.value, client_id
            )

            return PayoutFreqMsgs.FORECAST_PLAN.value, message

    return None, None
