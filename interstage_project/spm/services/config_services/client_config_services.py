import traceback
from datetime import datetime

from _datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import get_client_features
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.utils.date_utils import (
    end_of_day,
    first_day_of_month,
    last_day_of_month,
    start_of_day,
)
from commission_engine.utils.general_data import (
    DATABOOK_SYNC_STRATEGY,
    PROFILE_CARD_DEFAULT_FIELDS_MAP,
    SegmentEvents,
    SegmentProperties,
)
from spm.accessors.config_accessors.client_config_accessor import ClientConfigAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.models.config_models.client_config_models import ClientConfig
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.localization_services import get_localized_message_service

UNTAG_OPTIONS = {
    "OBJECT": "OBJECT",
    "OBJECT_VARIABLE": "OBJECT_VARIABLE",
    "DATASHEET_VARIABLE": "DATASHEET_VARIABLE",
}


def revenue_leader_update(client_id, serializer, request):
    time = timezone.now()
    audit_data = {}
    request.data["knowledge_begin_date"] = time
    request.data["effective_start_date"] = make_aware(
        start_of_day(
            datetime.strptime(request.data["effective_start_date"], "%d-%b-%Y")
        )
    )
    eed = request.data["effective_start_date"] - timedelta(days=1)
    effective_end_date = end_of_day(eed)
    request.data["client"] = client_id
    request.data["is_ed_enabled"] = True
    request.data["additional_details"] = request.audit
    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_SETTINGS"]["code"]
    event_key = request.data["type"]
    summary = request.data["type"]
    audit_data["effective_start_date"] = request.data["effective_start_date"].strftime(
        "%Y-%m-%d %H:%M"
    )
    audit_data["effective_end_date"] = eed.strftime("%Y-%m-%d %H:%M")
    audit_data["value"] = request.data["value"]
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    time_period = (
        str(request.data["effective_start_date"].strftime("%d-%m-%Y"))
        + " to "
        + eed.strftime("%d-%m-%Y")
    )

    analytics_data = {
        "user_id": request.audit["updated_by"],
        "event_name": SegmentEvents.UPDATE_REVENUE_LEADER.value,
        "event_properties": {
            SegmentProperties.EFFECTIVE_TIME_PERIOD.value: time_period,
            SegmentProperties.REVENUE_LEADER.value: request.data["value"],
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)
    logger = request.logger
    logger.info(
        "Inserting/Updating revenue leader with email and start date as - {0} - {1}".format(
            request.data["value"], request.data["effective_start_date"]
        )
    )
    prev = ClientConfigAccessor(client_id).latest_rev_leader()
    try:
        if prev:
            if prev.effective_start_date < request.data["effective_start_date"]:
                prev.effective_end_date = effective_end_date
                prev.knowledge_begin_date = time
                prev.pk = None
                ClientConfigAccessor(client_id).invalidate_latest_rl(time)
                ClientConfigAccessor(client_id).persist_client_config(prev)
            else:
                logger.update_context(
                    {
                        "email_id": request.data["value"],
                        "error": "INVALID DATE",
                        "date": prev.effective_start_date,
                    }
                )
                logger.info("INVALID date in updating revenue leader")
                return Response(
                    {
                        "error": "INVALID DATE",
                        "date": prev.effective_start_date,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        ser = serializer(data=request.data)
        if ser.is_valid():
            ClientConfigAccessor(client_id).persist_client_config(ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.update_context(
                {
                    "email_id": request.data["value"],
                    "start_date": request.data["effective_start_date"],
                }
            )
            logger.info("Inserted/Updated revenue leader with email and start date")
            return Response("SUCCESS", status=status.HTTP_201_CREATED)
        error_dict = {
            "ser_errors": ser.errors,
            "trace_back": traceback.print_exc(),
        }
        logger.error("Error in inserting/updating revenue leader", error_dict)
        return Response(ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Excep in inserting/updating revenue leader", error_dict)
        raise SQLParseError() from e


def revenue_leader_edit(client_id, serializer, request):
    time = timezone.now()
    audit_data = {}
    prev_to_latest = ClientConfigAccessor(client_id).get_prev_to_latest_for_rev_leader()
    request.data["knowledge_begin_date"] = time
    request.data["effective_start_date"] = make_aware(
        start_of_day(
            datetime.strptime(request.data["effective_start_date"], "%d-%b-%Y")
        )
    )
    eed = request.data["effective_start_date"] - timedelta(days=1)
    effective_end_date = end_of_day(eed)
    request.data["client"] = client_id
    request.data["type"] = "RevenueLeader"
    request.data["is_ed_enabled"] = True
    request.data["additional_details"] = request.audit

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_SETTINGS"]["code"]
    event_key = request.data["type"]
    summary = request.data["type"]
    audit_data["effective_start_date"] = request.data["effective_start_date"].strftime(
        "%Y-%m-%d %H:%M"
    )
    audit_data["effective_end_date"] = eed.strftime("%Y-%m-%d %H:%M")
    audit_data["value"] = request.data["value"]
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    logger = request.logger
    logger.info(
        "Editing revenue leader with email and start date as - {0} - {1}".format(
            request.data["value"], request.data["effective_start_date"]
        )
    )

    try:
        if prev_to_latest:
            prev_to_latest.knowledge_begin_date = time
            print(prev_to_latest.effective_start_date, prev_to_latest.value)
            if (
                prev_to_latest.effective_start_date
                < request.data["effective_start_date"]
            ):
                ClientConfigAccessor(client_id).invalidate_for_rl(
                    prev_to_latest.temporal_id, time
                )
                prev_to_latest.effective_end_date = effective_end_date
                prev_to_latest.pk = None
                ClientConfigAccessor(client_id).persist_client_config(prev_to_latest)
            else:
                logger.update_context(
                    {
                        "email_id": request.data["value"],
                        "error": "INVALID DATE",
                        "date": prev_to_latest.effective_start_date,
                    }
                )
                logger.info("INVALID date in updating revenue leader")
                return Response(
                    {
                        "error": "INVALID DATE",
                        "date": prev_to_latest.effective_start_date,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        ClientConfigAccessor(client_id).invalidate_latest_rl(time)
        ser = serializer(data=request.data)
        if ser.is_valid():
            ClientConfigAccessor(client_id).persist_client_config(ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.update_context(
                {
                    "email_id": request.data["value"],
                    "date": request.data["effective_start_date"],
                }
            )
            logger.info("Edited revenue leader with email and start date")
            return Response("SUCCESS", status=status.HTTP_201_CREATED)
        else:
            error_dict = {
                "ser_errors": ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error("Error in editing revenue leader", error_dict)
            Response(ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Excep in inserting/updating revenue leader", error_dict)
        return SQLParseError()


def fx_rate_insert(client_id, request):
    time = timezone.now()
    request.data["client"] = client_id
    request.data["knowledge_begin_date"] = time
    request.data["is_ed_enabled"] = False
    request.data["additional_details"] = request.audit
    fx_start_month = int(
        request.data["start_month"].split("-")[1]
    )  # need to parse month   01-01-2020
    fx_end_month = int(
        request.data["end_month"].split("-")[1]
    )  # need to parse end month
    fx_start_month_year = int(
        request.data["start_month"].split("-")[2]
    )  # need to parse start month year
    fx_end_month_year = int(
        request.data["end_month"].split("-")[2]
    )  # need to parse end month year
    latest_record = ClientConfigAccessor(client_id).client_latest_for_fxrate()

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_SETTINGS"]["code"]
    event_key = request.data["type"]
    summary = request.data["type"]
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    logger = request.logger

    if not latest_record:
        latest_record = ClientConfig(
            client_id=request.client_id,
            knowledge_begin_date=time,
            additional_details=request.audit,
            value=[],
            type="FxRate",
        )

    start_month_end_date_datetime = last_day_of_month(
        datetime(fx_start_month_year, fx_start_month, 1)
    )
    end_month_end_date_datetime = last_day_of_month(
        datetime(fx_end_month_year, fx_end_month, 1)
    )
    audit_data["start_month"] = (
        first_day_of_month(datetime(fx_start_month_year, fx_start_month, 1))
    ).strftime("%d-%m-%Y")
    audit_data["end_month"] = end_month_end_date_datetime.strftime("%d-%m-%Y")
    val = latest_record.value
    index = 0
    for ind in range(len(val)):
        if val[ind]["currency"] == request.data["currency_type"]:
            index = ind
            break
    else:
        val.append({"currency": request.data["currency_type"], "fx_rates": dict()})
        index = -1

    while start_month_end_date_datetime <= end_month_end_date_datetime:
        key = start_month_end_date_datetime.strftime("%Y%m%d")
        val[index]["fx_rates"][key] = request.data["currency_value"]
        start_month_end_date_datetime = last_day_of_month(
            (start_month_end_date_datetime + relativedelta(months=1))
        )
    latest_record.value = val
    latest_record.knowledge_begin_date = time
    latest_record.pk = None
    try:
        ClientConfigAccessor(client_id).persist_for_fxrate(latest_record, time)
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        logger.update_context(
            {
                "currency": request.data["currency_type"],
                "value": request.data["currency_value"],
                "start_date": start_month_end_date_datetime,
                "end_date": end_month_end_date_datetime,
            }
        )
        logger.info("FxRate Updated successfully")
        time_period = (
            str(start_month_end_date_datetime.strftime("%d-%m-%Y"))
            + " to "
            + end_month_end_date_datetime.strftime("%d-%m-%Y")
        )
        analytics_data = {
            "user_id": request.audit["updated_by"],
            "event_name": SegmentEvents.UPDATE_FX_RATES.value,
            "event_properties": {
                SegmentProperties.EFFECTIVE_TIME_PERIOD.value: time_period,
                SegmentProperties.CURRENCY.value: request.data["currency_type"],
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Excep in inserting/updating FxRate", error_dict)
        raise SQLParseError() from e


def fx_rate_by_date(client_id, end_date, currency, knowledge_date=None):
    if knowledge_date:
        fx_rate = ClientConfigAccessor(client_id).get_ked_fx_rates(knowledge_date)
    else:
        fx_rate = ClientConfigAccessor(client_id).get_fx_rates()
    fx_rate = fx_rate.value if fx_rate else None

    end_date = last_day_of_month(end_date)
    date_key = end_date.strftime("%Y%m%d")
    if fx_rate:
        for value in fx_rate:
            if value["currency"] == currency:
                return (
                    value["fx_rates"][date_key]
                    if date_key in value["fx_rates"]
                    else None
                )
    return None


def add_new_and_update_existing_tags(existing_tags, new_tags):
    for new_tag in new_tags:
        for idx in range(len(existing_tags)):
            if existing_tags[idx]["category"] == new_tag["category"]:
                existing_tags[idx] = new_tag
                break
        else:
            existing_tags.append(new_tag)
    return existing_tags


def save_tags(request):
    client_id = request.client_id
    data = request.data
    # what to tag is either object or datasheet
    what_to_tag = data["what_to_tag"]
    # object tagging
    if what_to_tag == "object":
        cust_obj = data["custom_object"] if "custom_object" in data else None
        cust_obj_vars = (
            data["custom_obj_variable"] if "custom_obj_variable" in data else None
        )
        # tagging custom object
        for obj in cust_obj:
            new_tag = {"category": obj["tags"][0], "value": obj["tags"][1]}
            co_rec = CustomObjectAccessor(client_id).get_object_by_id(
                obj["custom_obj_id"]
            )
            existing_tag = []
            if co_rec and co_rec[0].tags:
                existing_tag = co_rec[0].tags
            add_new_and_update_existing_tags(existing_tag, [new_tag])
            co_rec.update(tags=existing_tag)
        # tagging custom object variable
        for var in cust_obj_vars:
            tag_category = var["tags"][0]
            tag_value = var["tags"][1]
            is_deal_status = (
                True
                if tag_category == "field_label" and tag_value == "deal_status"
                else False
            )
            new_tags = [{"category": tag_category, "value": tag_value}]
            if is_deal_status:
                new_tags.append(
                    {
                        "category": "deal_status_won",
                        "value": var["closed_won_str"],
                    }
                )
                new_tags.append(
                    {
                        "category": "deal_status_lost",
                        "value": var["closed_lost_str"],
                    }
                )
            var_rec = CustomObjectVariableAccessor(
                client_id
            ).get_variable_by_system_name_and_id(var["custom_obj_id"], var["variable"])
            existing_tag = []
            if var_rec:
                existing_tag = var_rec[0].tags if var_rec[0].tags else []
            add_new_and_update_existing_tags(existing_tag, new_tags)
            var_rec.update(tags=existing_tag)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)

    # Datasheet tagging
    elif what_to_tag == "datasheet":
        datasheet_data = data["datasheet_variable"]
        # iterate the request datasheet tagging data
        for ds_tag in datasheet_data:
            databook_id = ds_tag["db"]
            datasheet_id = ds_tag["ds"]
            datasheet_var = ds_tag["ds_var"]
            new_tag = [
                {
                    "category": ds_tag["tags"][0],
                    "value": ds_tag["tags"][1],
                }
            ]
            # existing datasheet variable record
            dsv_rec = DatasheetVariableAccessor(
                client_id
            ).get_datasheet_var_for_db_ds_sys_name(
                databook_id, datasheet_id, datasheet_var
            )
            # update the tags column
            dsv_rec.update(tags=new_tag)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    return Response("FAILED", status=status.HTTP_400_BAD_REQUEST)


def remove_tag(request):
    client_id = request.client_id
    data = request.data
    untag_type = data["type"]
    if untag_type == UNTAG_OPTIONS["OBJECT"]:
        co_rec = CustomObjectAccessor(client_id).get_object_by_id(
            data["custom_object_id"]
        )
        if co_rec:
            co_rec.update(tags=None)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    elif untag_type == UNTAG_OPTIONS["OBJECT_VARIABLE"]:
        cov_rec = CustomObjectVariableAccessor(
            client_id
        ).get_variable_by_system_name_and_id(data["custom_object_id"], data["variable"])
        if cov_rec:
            cov_rec.update(tags=None)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    elif untag_type == UNTAG_OPTIONS["DATASHEET_VARIABLE"]:
        dsv_rec = DatasheetVariableAccessor(
            client_id
        ).get_datasheet_var_for_db_ds_sys_name(
            data["databook_id"], data["datasheet_id"], data["variable"]
        )
        if dsv_rec:
            dsv_rec.update(tags=None)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    return Response("FAILED", status=status.HTTP_400_BAD_REQUEST)


def get_all_fx_rates(client_id):
    fx_rate = ClientConfigAccessor(client_id).get_all_fx_rates()
    fx_rate_map = {}
    latest_record = None
    for item in fx_rate:
        fx_rate_map[(item["knowledge_begin_date"], item["knowledge_end_date"])] = item
        if item["knowledge_end_date"] is None:
            latest_record = item
    return fx_rate_map, latest_record


def get_ked_fx_rates(client_id, knowledge_date):
    return ClientConfigAccessor(client_id).get_ked_fx_rates(kd=knowledge_date)


def get_fx_rates(client_id):
    return ClientConfigAccessor(client_id).get_fx_rates()


def get_databook_sync_strategy(client_id) -> DATABOOK_SYNC_STRATEGY:
    """
    Get Databook book sync strategy.

    Supported ETL strategy's include "snowflake"/"SNOWFLAKE", "memory"/"MEMORY". Defaults to "memory"/"MEMORY.
    """
    client_features = get_client_features(client_id=client_id)
    databook_sync_strategy = client_features.get(
        "databook_sync_strategy", DATABOOK_SYNC_STRATEGY.SNOWFLAKE.value
    )

    try:
        return DATABOOK_SYNC_STRATEGY(databook_sync_strategy.lower())
    except ValueError:
        return DATABOOK_SYNC_STRATEGY.MEMORY


def get_client_fx_rates_for_kd(client_id, payee_id, end_date):
    from commission_engine.accessors.client_accessor import get_client
    from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
    from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
    from spm.services.dashboard_services.payee_commission_services import (
        get_payee_payroll_date,
    )

    client = get_client(client_id)
    kd = get_payee_payroll_date(client_id=client_id, payee=payee_id, end_date=end_date)
    payroll = EmployeePayrollAccessor(client_id).get_variable_pay_for_given_date(
        kd, end_date, [payee_id]
    )
    fx_rate = get_ked_fx_rates(client_id=client_id, knowledge_date=kd)
    pay_currency = payroll[0]["pay_currency"] if payroll else None
    fx_rate = fx_rate.value if fx_rate else None
    date_key = end_date.strftime("%Y%m%d")
    fx_rate_value = None
    if fx_rate:
        for value in fx_rate:
            if value["currency"] == pay_currency:
                fx_rate_value = (
                    value["fx_rates"][date_key]
                    if date_key in value["fx_rates"]
                    else None
                )
    country = CountriesAccessor(client_id).get_currency_symbol(pay_currency)
    symbol = country.currency_symbol if country else None
    data = {
        "base_currency": client.base_currency,
        "pay_currency": pay_currency,
        "fx_rate": fx_rate_value,
        "currency_symbol": symbol,
    }

    return data


def get_default_options_and_fields():
    options = {
        "user_fields": True,
        "teams": False,
        "pod_details": False,
    }
    selected_fields = [
        "reporting_manager",
        "employment_country",
        "pay_currency",
        "variable_pay",
    ]
    return options, selected_fields


def get_profile_card_config(client_id):
    data = {
        "default_fields": [],
        "selected_fields": [],
        "options": {},
    }
    try:
        config_record = ClientConfigAccessor(client_id).get_profile_fields()
        default_fields = {}
        for field, display_name in PROFILE_CARD_DEFAULT_FIELDS_MAP.items():
            default_fields[field] = get_localized_message_service(
                display_name, client_id
            )
        data["default_fields"] = default_fields
        if config_record:
            configs = config_record.value
            data["selected_fields"] = configs["selected_fields"]
            data["options"] = configs["options"]
        else:
            data["options"], data["selected_fields"] = get_default_options_and_fields()

        client_features = get_client_features(client_id)

        if not client_features.get("can_use_custom_metrics", False):
            data["default_fields"].pop("bcr", None)
            data["selected_fields"].remove("bcr")

    except Exception as e:
        print("Exception {}".format(e))
    return data


def get_query_setting_config(client_id):
    data = {"query_assignee_type": "any_user", "selected_user": [], "cc_others": True}

    try:
        config_record = ClientConfigAccessor(client_id).get_query_setting()
        if config_record:
            return config_record.value
        return data
    except Exception as e:
        print("Exception {}".format(e))
    return data


def update_profile_card_config(client_id, data, audit, logger):
    time = timezone.now()
    data["client"] = client_id
    data["knowledge_begin_date"] = time
    data["is_ed_enabled"] = False
    data["additional_details"] = audit

    latest_record = ClientConfigAccessor(client_id).get_profile_fields()
    if not latest_record:
        latest_record = ClientConfig(
            client_id=client_id,
            knowledge_begin_date=time,
            additional_details=audit,
            value=[],
            type="ProfileFields",
        )
    latest_record.value = data["value"]
    latest_record.knowledge_begin_date = time
    latest_record.pk = None
    try:
        ClientConfigAccessor(client_id).persist_for_profile_fields(latest_record, time)
        ###################### audit log #####################
        event_type_code = EVENT["UPDATE_SETTINGS"]["code"]
        event_key = data["type"]
        summary = data["type"]
        audit_data = data
        updated_by = audit["updated_by"]
        updated_at = time
        ######################################################
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        logger.info("Profile Fields Updated successfully")

        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.UPDATE_PROFILE_FIELDS.value,
            "event_properties": {
                SegmentProperties.CHANGED_PROFILE_FIELDS.value: data["value"]
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Exception in inserting/updating Profile Fields", error_dict)
        raise SQLParseError() from e


def handle_custom_field_delete(client_id, custom_field):
    updated_at = timezone.now()
    record = ClientConfigAccessor(client_id).get_profile_fields()
    if record and record.value:
        data = record.value
        selected_fields = data["selected_fields"]
        if custom_field in selected_fields:
            selected_fields.remove(custom_field)
            data["selected_fields"] = selected_fields
            record.value = data
            record.pk = None
            ClientConfigAccessor(client_id).persist_for_profile_fields(
                record, updated_at
            )


def update_query_setting_config(client_id, data, audit, logger):
    time = timezone.now()
    data["client"] = client_id
    data["knowledge_begin_date"] = time
    data["is_ed_enabled"] = False
    data["additional_details"] = audit

    latest_record = ClientConfigAccessor(client_id).get_query_setting()
    if not latest_record:
        latest_record = ClientConfig(
            client_id=client_id,
            knowledge_begin_date=time,
            additional_details=audit,
            value=[],
            type="QuerySetting",
        )
    latest_record.value = data["value"]
    latest_record.knowledge_begin_date = time
    latest_record.pk = None
    try:
        ClientConfigAccessor(client_id).persist_for_query_setting(latest_record, time)
        ###################### audit log #####################
        event_type_code = EVENT["UPDATE_SETTINGS"]["code"]
        event_key = data["type"]
        summary = data["type"]
        audit_data = data
        updated_by = audit["updated_by"]
        updated_at = time
        ######################################################
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        logger.info("Query Setting Updated successfully")

        analytics_data = {
            "user_id": audit["updated_by"],
            "event_name": SegmentEvents.UPDATE_PROFILE_FIELDS.value,
            "event_properties": {
                SegmentProperties.CHANGED_PROFILE_FIELDS.value: data["value"]
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response("SUCCESS", status=status.HTTP_201_CREATED)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error("Exception in inserting/updating Query Setting", error_dict)
        raise SQLParseError() from e
