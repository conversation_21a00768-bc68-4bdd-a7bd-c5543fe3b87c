import copy
import datetime
import json
import logging
import traceback
from ast import literal_eval
from datetime import datetime as _date
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import List
from uuid import UUID

import pandas as pd
import pydash
from celery import chain
from dateutil.parser import parse
from django.core.exceptions import ObjectDoesNotExist
from django.db import connection, transaction
from django.db.models import Q
from django.db.utils import IntegrityError
from django.utils import timezone
from django.utils.timezone import make_aware
from graphql import GraphQLError
from rest_framework import status
from rest_framework.response import Response
from sendgrid import Email, Mail
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_subscription_plan,
)
from commission_engine.services.payout_status_changes_service import (
    add_payee_status_details_for_payout_status_changes,
)
from commission_engine.utils import STATUS_CODE
from commission_engine.utils.cache_utils import (
    delete_hierarchy_cache,
    update_emp_scope_info_cache,
)
from commission_engine.utils.date_utils import (
    end_of_day,
    make_aware_wrapper,
    start_of_day,
)
from commission_engine.utils.general_data import (
    GRAPHQL__PERMISSION_DENIED,
    RBAC,
    RESPONSE__PERMISSION_DENIED,
    IntegrationType,
    PayoutStatusChangesTypes,
    RBACComponent,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.general_utils import replace_nan_nat_for_df
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.auth_management_api import (
    create_user_in_auth,
    get_password_reset_link,
)
from interstage_project.auth_utils import (
    authorize_for_email_lookup,
    authorize_for_profile_lookup,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group, log_me
from ms_teams_everstage.services.tenant_services import (
    integrate_msteams_for_newly_added_users,
)
from slack_everstage.utils.slack_utils import get_site_url
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanPayeeAccessor,
)
from spm.accessors.config_accessors.employee_accessor import (
    CrClEmployeeAccessor,
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
    PlanDetailsAccessor,
    PlanDetailsAllAccessor,
    PlanDetailsSpiffAccessor,
)
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.accessors.custom_field_accessor import (
    CustomFieldDataAccessor,
    CustomFieldsAccessor,
)
from spm.accessors.email_template_accessor import EmailTemplateDetailsAccessor
from spm.accessors.employee_accessor_v2 import (
    EmployeeReadAccessor,
    EmployeeWriteAccessor,
)
from spm.accessors.employee_view_accesor import EmployeeDetailsViewAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.localization_constants import LocalizationEngMessages as LocMsgs
from spm.constants.localization_constants import UserExportTerms
from spm.models.commission_plan_models import PlanPayee
from spm.models.config_models.employee_models import Employee, PlanDetails
from spm.serializers.commission_plan_serializers import PlanPayeeSerializer
from spm.serializers.custom_fields_serializer import CustomFieldDataSerializer
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.commission_plan_services import validate_plan_and_payee
from spm.services.config_services import employee_services_utils
from spm.services.config_services.bulk_add_utils import (
    construct_custom_field_dict,
    construct_employee_dict,
    construct_employee_payroll_dict,
    construct_hierarchy_dict,
    is_email_exists,
    modify_bulk_add_custom_field_data,
)
from spm.services.config_services.employee_services_utils import (
    PayoutFreqMsgs,
    custom_field_date_conversion_utils,
    handle_custom_payout_frequency,
    handle_payout_frequency_change,
    validate_custom_field_data,
    validate_custom_field_datas,
)
from spm.services.config_services.slack_config_services import (
    add_default_slack_and_ms_teams_notification_for_users,
    integrate_slack_for_newly_added_users,
)
from spm.services.email_services.email_services import send_email
from spm.services.localization_services import (
    get_localized_message_service,
    get_localized_words_service,
)
from spm.services.notification_settings_services.notification_settings_service import (
    add_compulsory_notifications_for_payees,
)
from spm.services.query_builder.base import BaseQueryBuilder
from spm.services.query_builder.employees import (
    EmployeeQueryBuilder,
    HierarchyQueryBuilder,
)
from spm.services.rbac_services import (
    get_data_permission,
    get_role_display_name,
    get_ui_permission_by_role,
    get_ui_permissions,
    get_valid_payee_emails,
    is_own_data_permission,
)
from spm.sort_utils import SortInfoType, SortUtility, remove_ignore_columns
from spm.tasks import invalidate_and_cache_all_user_groups, process_bulk_add_task
from spm.utils import ConnectionType, ConnectionTypeText, clone_object

from ..config_services.config_changes_notification_service import notify_plan_change
from ..custom_field_services import (
    get_custom_fields_details,
    resolve_custom_field_data_for_user_export,
    resolve_custom_field_data_for_user_filters,
)

logger = logging.getLogger(__name__)


def check_whether_sup_user_is_valid(email):
    """
    If email is active in everstage
    """
    return CrClEmployeeAccessor().get_employee(email=email)


def get_all_employees(client_id, as_of_date=None, user_status=None, email_list=None):
    """
    Get all employees query set based on user_status, as_of_date.

    Args:
        user_status (_string_, optional): ["Active", "Exited", "All"]. Defaults to "Active".
    """
    return EmployeeAccessor(client_id).get_all_employees(
        as_of_date, user_status, email_list
    )


def get_employees_name(client_id, payee_list):
    return EmployeeAccessor(client_id).get_employees_name(payee_list)


def get_employee_name_map(client_id: int, email_ids: list[str]):
    """
    Return employee name map for given email ids. Eg.
    {
        "<EMAIL>": {
            "first_name": "John",
            "last_name": "Doe",
            "full_name": "John Doe"
        }, ...
    }
    """
    emp_names = EmployeeReadAccessor(client_id).get_employees_name(email_ids)
    emp_names_with_full_name = []
    for emp in emp_names:
        emp["full_name"] = f"{emp['first_name']} {emp['last_name']}"
        emp_names_with_full_name.append(emp)

    return {emp["employee_email_id"]: emp for emp in emp_names_with_full_name}


def get_non_exited_notif_enabled_users(client_id: int, email_ids: list[str]):
    """
    Get non exited users who have notification enabled
    """
    return EmployeeReadAccessor(
        client_id
    ).get_all_employee_email_list_with_notifications(email_list=email_ids)


def get_employees_name_profile_picture(client_id, payee_list):
    return EmployeeAccessor(client_id).get_employees_profile_picture_list(payee_list)


def get_employee_role(client_id, email_id):
    return EmployeeAccessor(client_id).get_employee_role(email_id)


def get_all_employee_plans(client_id, employee_email_ids, as_dicts=True):
    return PlanDetailsAllAccessor(client_id).get_all_employee_plans(
        employee_email_ids, as_dicts=as_dicts
    )


def check_if_employee_exists(client_id, employe_email_id) -> bool:
    """
    checks if the employee exists in everstage
    """
    return EmployeeAccessor(client_id).does_employee_exist(employe_email_id)


def get_response(serializer):
    return Response(serializer.data)


def is_superadmin(client_id, user_email):
    user_role = EmployeeAccessor(client_id).get_employee_role(user_email)
    ui_permission = get_ui_permission_by_role(client_id, user_role)
    return True if RbacPermissions.MANAGE_CRYSTAL.value in ui_permission else False


def get_employees_details(client_id, emails):
    return EmployeeAccessor(client_id=client_id).get_employees_names_profile_picture(
        emails
    )


def get_employee_status(client_id: int, emails: str) -> list[dict[str, str]]:
    """
    given a list of emails, checks the status of these emails in everstage (active/added).
    Returns a list of dictionary [{"email_id" : "email", "status" : "active/added"}].
    No value is returned for an email, that does not exist in the db
    """
    return EmployeeAccessor(client_id=client_id).get_employees_status(email_list=emails)


def get_employee_designation(client_id: int, payee_email: str) -> str | None:
    """
    get employee designation
    """
    employee_designation = EmployeePayrollAccessor(
        client_id=client_id
    ).get_employee_designation(
        employee_email_id=payee_email, date=datetime.datetime.now()
    )
    if len(employee_designation) > 0:
        employee_designation = employee_designation.first()["designation"]
    else:
        employee_designation = None

    return employee_designation


def get_employee_full_name(client_id: int, payee_email: str) -> str:
    """
    get employee full name
    """
    employee_full_name = EmployeeAccessor(client_id=client_id).get_employee_full_name(
        payee_email
    )
    return employee_full_name


def get_employee_payroll(client_id, payee_email, effective_date):
    employee_payroll_acc = EmployeePayrollAccessor(client_id)
    emp_payroll = employee_payroll_acc.get_employee_payroll(
        effective_date, [payee_email], as_dicts=True, projection=["payout_frequency"]
    )
    return emp_payroll


def is_authorized_for_profile_lookup(client_id, login_user=None, for_user=None) -> bool:
    """
    Access ueer is typically deciphered from the user's access token - this happens when the user is logged in
    """
    is_payout_authorized = authorize_for_profile_lookup(
        client_id, login_user, for_user, RBACComponent.PAYOUTS_STATEMENTS.value
    )
    is_quota_authorized = authorize_for_profile_lookup(
        client_id, login_user, for_user, RBACComponent.QUOTAS_DRAWS.value
    )
    return is_payout_authorized or is_quota_authorized


def persist_employee(client_id, serializer, request):
    time = timezone.now()
    client = get_client(client_id)
    request.data["knowledge_begin_date"] = time
    request.data["created_date"] = time
    request.data["client"] = client_id
    request.data["additional_details"] = request.audit
    request.data["send_notification"] = False
    request.data["status"] = "Added"
    request.data["created_by"] = request.audit["updated_by"]
    if "user_source" not in request.data:
        request.data["user_source"] = "manual"
    email_id = request.data["employee_email_id"]

    ###################### audit log #####################
    event_type_code = EVENT["CREATE_USER"]["code"]
    event_key = email_id
    summary = request.data["first_name"] + request.data["last_name"]
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################

    req_logger = request.logger
    req_logger.update_context({"payee_email_id": request.data["employee_email_id"]})
    employee_slack_config = {}
    employee_ms_teams_config = {}
    ##### Integerate slack and ms-teams for the user #####
    try:
        employee_slack_config = integrate_slack_for_newly_added_users(
            client_id, [email_id]
        )
        employee_ms_teams_config = integrate_msteams_for_newly_added_users(
            client_id, [email_id]
        )
        emp_config = {}
        installed_slack_for_employees = []
        installed_msteams_for_employees = []
        # Integration config map - list of dicts with keys - client_id, employee_email_id, integration_type, config, knowledge_begin_date
        integration_config_map_list = []
        if employee_slack_config and email_id in employee_slack_config.keys():
            emp_config = {}
            installed_slack_for_employees.append(email_id)
            for key, value in employee_slack_config[email_id].items():
                emp_config[key] = value
            integration_config_map_list.append(
                {
                    "integration_type": IntegrationType.SLACK.value,
                    "employee_email_id": email_id,
                    "client": client_id,
                    "knowledge_begin_date": time,
                    "config": emp_config,
                }
            )
        if employee_ms_teams_config and email_id in employee_ms_teams_config.keys():
            emp_config = {}
            installed_msteams_for_employees.append(email_id)
            for key, value in employee_ms_teams_config[email_id].items():
                emp_config[key] = value
            integration_config_map_list.append(
                {
                    "integration_type": IntegrationType.MS_TEAMS.value,
                    "employee_email_id": email_id,
                    "client": client_id,
                    "knowledge_begin_date": time,
                    "config": emp_config,
                }
            )
        # Bulk create integration config records
        if integration_config_map_list:
            if installed_slack_for_employees:
                IntegrationConfigAccessor().invalidate_slack_config_for_email_ids(
                    client_id=client_id,
                    email_ids=installed_slack_for_employees,
                    end_time=time,
                )
            if installed_msteams_for_employees:
                IntegrationConfigAccessor().invalidate_msteams_config_for_email_ids(
                    client_id=client_id,
                    email_ids=installed_msteams_for_employees,
                    end_time=time,
                )
            IntegrationConfigAccessor().bulk_create_config_records(
                fields_map=integration_config_map_list
            )

    except Exception as e:
        req_logger.error(
            f"Exception - Error integrating slack/msteams for employees - {email_id} - {e}"
        )
    ######################################################

    # Segment user analytics info
    req_logger = request.logger
    req_logger.update_context({"payee_email_id": request.data["employee_email_id"]})
    req_logger.info(
        "Adding new user with email id {}".format(request.data["employee_email_id"])
    )
    ser = serializer(data=request.data)
    try:
        if ser.is_valid():
            if not is_email_exists(client_id, ser.validated_data["employee_email_id"]):
                create_user_in_auth(
                    client_id,
                    ser.validated_data,
                )
                EmployeeWriteAccessor(client_id).persist_employee(ser)
                ######## Set slack and ms-teams notifcation #########
                try:
                    add_default_slack_and_ms_teams_notification_for_users(
                        client_id=client_id,
                        newly_added_users=[ser.validated_data["employee_email_id"]],
                        users_slack_map=employee_slack_config,
                        users_ms_teams_map=employee_ms_teams_config,
                    )
                except Exception as e:
                    logger.error(
                        "Exception - Error handling default msteams/slack tasks for emps in bulk - %s",
                        e,
                    )
                ######################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )
                req_logger.info(
                    "Added new user successfully with email id {}".format(
                        request.data["employee_email_id"]
                    )
                )
                role_display_name = get_role_display_name(
                    client_id, request.data.get("user_role")
                )
                analyser = CoreAnalytics(analyser_type="segment")
                analytics_data = {
                    "user_id": request.audit["updated_by"],
                    "event_name": SegmentEvents.ADD_USER.value,
                    "event_properties": {
                        SegmentProperties.FIRST_NAME.value: request.data["first_name"],
                        SegmentProperties.LAST_NAME.value: request.data["last_name"],
                        SegmentProperties.EMAIL.value: request.data[
                            "employee_email_id"
                        ],
                        SegmentProperties.ROLE.value: role_display_name,
                    },
                }
                analyser.send_analytics(analytics_data)
                analytics_data = {
                    "user_id": email_id,
                    "user_properties": {
                        SegmentProperties.NAME.value: f'{request.data["first_name"]} {request.data["last_name"]}',
                        SegmentProperties.EMAIL.value: email_id,
                        SegmentProperties.CLIENT_ID.value: client_id,
                        SegmentProperties.CLIENT.value: client.name,
                        SegmentProperties.IS_MANAGER.value: False,
                        SegmentProperties.ROLE.value: role_display_name,
                        SegmentProperties.CRYSTAL_ACCESS.value: "No",
                        SegmentProperties.STATUS.value: "Added",
                        SegmentProperties.SLACK_CONNECTED.value: False,
                        SegmentProperties.SLACK_NOTIFICATION_FREQ.value: None,
                    },
                }
                analyser.register_user(analytics_data)
                subscription_plan = get_client_subscription_plan(client_id)
                misc_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.MISC.value
                )
                invalidate_and_cache_all_user_groups.si(client_id).set(
                    queue=misc_queue_name
                ).apply_async()
                # pylint: disable=unused-variable
                emp = EmployeeAccessor(client_id).get_employee(
                    ser.validated_data["employee_email_id"]
                )
                return Response("SUCCESS", status=status.HTTP_201_CREATED)
            req_logger.info(
                "Employee email Id already exists {}".format(
                    request.data["employee_email_id"]
                )
            )
            return Response("EMAIL ALREADY EXISTS", status=status.HTTP_400_BAD_REQUEST)
        else:
            error_dict = {
                "ser_erros": ser.errors,
                "trace_back": traceback.print_exc(),
            }
            req_logger.error(
                "Error in creating new employee with email id {}".format(
                    request.data["employee_email_id"]
                ),
                error_dict,
            )
            return Response(ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        req_logger.error(
            "Excep in creating new employee with email id {}".format(
                request.data["employee_email_id"]
            ),
            error_dict,
        )
        raise SQLParseError() from e


@transaction.atomic
def send_auth_invite(
    client_id,
    email_ids,
    login_user_id,
    all_users_selected=None,
    filter_mode=None,
    search_term=None,
    audit: dict | None = None,
):
    client = get_client(client_id)
    members = {}
    failed_emails = []
    succeeded_emails = []
    is_filter_applied = filter_mode["value"] if filter_mode else False
    if all_users_selected:
        # if all users flag is selected
        if is_filter_applied == False:
            # if no filters applied, get all employee email list wrt search_term
            email_ids = EmployeeAccessor(client_id).get_all_employees_with_search_limit(
                search_term=search_term, email_only=True
            )
            members = {
                employee["employee_email_id"]: employee
                for employee in EmployeeAccessor(client_id).get_all_employees(
                    email_list=[login_user_id, *email_ids],
                    projection=[
                        "employee_email_id",
                        "first_name",
                        "last_name",
                        "user_role",
                    ],
                )
            }
        elif is_filter_applied == True:
            # if filters applied, get filtered employee email list wrt search_term
            filters = filter_mode["constraints"]

            email_ids = get_filtered_emails_list_query_builder(
                client_id, filters, search_term
            )
            members = {
                employee["employee_email_id"]: employee
                for employee in EmployeeAccessor(client_id).get_all_employees(
                    email_list=[login_user_id, *email_ids],
                    projection=[
                        "employee_email_id",
                        "first_name",
                        "last_name",
                        "user_role",
                    ],
                )
            }
    else:
        # if all user flag is false
        members = {
            employee["employee_email_id"]: employee
            for employee in EmployeeAccessor(client_id).get_all_employees(
                email_list=[login_user_id, *email_ids],
                projection=[
                    "employee_email_id",
                    "first_name",
                    "last_name",
                    "user_role",
                ],
            )
        }

    # Check for data permission of logged in user: manage users
    is_authorized = authorize_for_email_lookup(
        client_id=client_id,
        login_user_id=login_user_id,
        email_ids=email_ids,
        component=RBACComponent.MANAGE_USERS.value,
    )
    if not is_authorized:
        logger.info(
            "User: %s don't have permission to send invite of users %s",
            login_user_id,
            email_ids,
            extra={
                "login_user_id": login_user_id,
                "emails_to_sendinvite": email_ids,
            },
        )
        return {
            "is_success": False,
            "response": RESPONSE__PERMISSION_DENIED,
        }

    # Check for the own data permission of logged in user
    has_owndata_perm = is_own_data_permission(client_id, login_user_id)
    if not has_owndata_perm and login_user_id in email_ids:
        logger.info(
            "User: %s don't have permission send invite to themself", login_user_id
        )
        return {
            "is_success": False,
            "response": RESPONSE__PERMISSION_DENIED,
        }

    if client.auth_connection_name != "email-password":
        if client.connection_type == ConnectionType.GSUITE.value:
            login_text = ConnectionTypeText.GSUITE.value
        elif client.connection_type == ConnectionType.SALESFORCE.value:
            login_text = ConnectionTypeText.SALESFORCE.value
        elif client.connection_type == ConnectionType.OKTA.value:
            login_text = ConnectionTypeText.OKTA.value
        else:
            # It has to be str value (For Sendgrid to parse)
            login_text = "null"
        for email in email_ids:
            if email not in members:
                # inactive users
                failed_emails.append(email)
                continue
            succeeded_emails.append(email)

            message = Mail(
                from_email=Email(email="<EMAIL>", name="Everstage"),
                to_emails=email if is_prod_env() else "<EMAIL>",
            )
            template_id = (
                EmailTemplateDetailsAccessor().get_invite_template_id_for_client(
                    client_id
                )
            )
            message.template_id = template_id
            message.dynamic_template_data = {
                "name": f'{members[email]["first_name"]} {members[email]["last_name"]}',  # type: ignore
                "Org_SSO": login_text,
                "admin_name": f'{members[login_user_id]["first_name"]} {members[login_user_id]["last_name"]}',  # type: ignore
                "app_url": get_site_url(),
            }
            log_me("sending invite user email to {0}".format(email))
            subscription_plan = get_client_subscription_plan(client_id)
            send_email_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
            )
            send_email.si(message=message, type="invitation").set(
                queue=send_email_queue_name
            ).apply_async()
    else:
        for email in email_ids:
            if email not in members:
                # inactive users
                failed_emails.append(email)
                continue

            message = Mail(
                from_email=Email(email="<EMAIL>", name="Everstage"),
                to_emails=email if is_prod_env() else "<EMAIL>",
            )
            template_id = (
                EmailTemplateDetailsAccessor().get_invite_template_id_for_client(
                    client_id
                )
            )
            message.template_id = template_id
            reset_link = get_password_reset_link(email)

            if not reset_link:
                # If user is not present in Auth with email-password connection, create user in Auth with email-password connection
                create_user_in_auth(
                    client_id,
                    {
                        "employee_email_id": email,
                        "first_name": members[email]["first_name"],
                        "last_name": members[email]["last_name"],
                        "user_role": members[email]["user_role"],
                    },
                )
                reset_link = get_password_reset_link(email)

            if reset_link:
                reset_link += "&type=invite"  # Added to set appropriate title in Auth: Set/Change Password
                succeeded_emails.append(email)

                message.dynamic_template_data = {
                    "name": members[email]["first_name"]
                    + " "
                    + members[email]["last_name"],
                    "admin_name": members[login_user_id]["first_name"]
                    + " "
                    + members[login_user_id]["last_name"],
                    "reset_url": reset_link,
                }
                log_me("sending invite user email to {0}".format(email))
                subscription_plan = get_client_subscription_plan(client_id)
                send_email_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
                )
                send_email.si(message=message, type="invitation").set(
                    queue=send_email_queue_name
                ).apply_async()

                analytics_data = {
                    "user_id": email,
                    "user_properties": {
                        SegmentProperties.STATUS.value: "Invited",
                    },
                }
                analytics = CoreAnalytics(analyser_type="segment")
                analytics.register_user(analytics_data)
            else:
                failed_emails.append(email)
                log_me("Error sending invite user email to {0}".format(email))
    # Bulk updating the send invite status of succeeded_emails employees
    knowledge_date = timezone.now()
    all_employees_to_update = EmployeeAccessor(client_id).get_all_employee_by_status(
        succeeded_emails, "Added"
    )
    emails_with_added_status = []
    for emp in all_employees_to_update:
        emp.pk = None
        emp.knowledge_begin_date = knowledge_date
        emp.status = "Invited"

        # Enabling notification for the user when the client notification has enabled
        emp.send_notification = client.client_notification  # type: ignore

        emails_with_added_status.append(emp.employee_email_id)

    EmployeeWriteAccessor(client_id).invalidate_and_create_objects(
        new_records=all_employees_to_update,
        invalidate_employee_email=emails_with_added_status,
        knowledge_date=knowledge_date,
    )

    # Add payout status changes details for invite payees
    add_payee_status_details_for_payout_status_changes(
        client_id=client_id,
        payee_emails=emails_with_added_status,
        event_type=PayoutStatusChangesTypes.INVITE_PAYEE.value,
    )

    if client.client_notification:
        # Adding non-opted out / compulsory notifications for the payees
        add_compulsory_notifications_for_payees(
            client_id,
            emails_with_added_status,
            knowledge_date=knowledge_date,
            audit=audit,
        )

    return {
        "is_success": True,
        "response": {
            "succeeded_emails": succeeded_emails,
            "failed_emails": failed_emails,
        },
    }


def get_user_status(emp_status, exit_date, deactivation_date, effective_date=None):
    """
    This function returns the status of the employee based on the effective date, exit date and deactivation date
    """
    effective_date = effective_date if effective_date else timezone.now()
    if exit_date:
        if effective_date <= exit_date:
            return "Marked for exit"
        else:
            return "Inactive"
    elif deactivation_date:
        if effective_date <= deactivation_date:
            return "Marked for deactivation"
        else:
            return "Pending exit"
    else:
        return emp_status


def update_user_source(
    client_id,
    email_ids,
    all_users_selected,
    filter_mode,
    search_term,
    updated_user_source,
    login_user_id,
):
    is_filter_applied = filter_mode["value"] if filter_mode else False
    if all_users_selected:
        # if all users flag is selected
        if is_filter_applied == False:
            # if no filters applied, get all employee email list wrt search_term
            email_ids = EmployeeAccessor(client_id).get_all_employees_with_search_limit(
                search_term=search_term, email_only=True
            )
        elif is_filter_applied == True:
            # if filters applied, get filtered employee email list wrt search_term
            filters = filter_mode["constraints"]

            email_ids = get_filtered_emails_list_query_builder(
                client_id, filters, search_term
            )
    has_owndata_perm = is_own_data_permission(client_id, login_user_id)
    if not has_owndata_perm and login_user_id in email_ids:
        logger.info(
            "User: %s don't have permission to change user source for themself",
            login_user_id,
        )
        return {
            "is_success": False,
            "response": "You don't have permission to modify your own user source",
        }
    knowledge_date = timezone.now()
    all_employees_to_update = EmployeeAccessor(client_id).get_employees(
        email_ids, as_dicts=False
    )
    for emp in all_employees_to_update:
        emp.pk = None
        emp.knowledge_begin_date = knowledge_date
        emp.user_source = updated_user_source

    EmployeeWriteAccessor(client_id).invalidate_and_create_objects(
        new_records=all_employees_to_update,
        invalidate_employee_email=email_ids,  # type: ignore
        knowledge_date=knowledge_date,
    )

    return {
        "is_success": True,
        "response": {
            "status": "success",
        },
    }


def exit_employee(client_id, email_id, exit_date, last_commission_date, audit_info):
    exit_date = make_aware(end_of_day(_date.strptime(exit_date, "%d/%m/%Y")))
    knowledge_date = timezone.now()
    if last_commission_date:
        last_commission_date = make_aware(
            end_of_day(_date.strptime(last_commission_date, "%d/%m/%Y"))
        )
    try:
        employee_record = EmployeeAccessor(client_id).get_employee(email_id)
        deactivation_date = employee_record.deactivation_date

        if not deactivation_date or exit_date < deactivation_date:
            EmployeeWriteAccessor(client_id).update_exit_and_deactivation_date(
                email_id=email_id,
                deactivation_date=exit_date,
                exit_date=exit_date,
                last_commission_date=last_commission_date,
                knowledge_date=knowledge_date,
            )
        else:
            EmployeeWriteAccessor(client_id).update_exit_date(
                email_id, exit_date, last_commission_date, knowledge_date
            )
        employee = EmployeeAccessor(client_id).get_employee(email_id)

        # Update exit date and user role in cache
        update_emp_scope_info_cache(client_id, email_id, employee_obj=employee)

        ###################### audit log #####################

        event_type_code = EVENT["EMPLOYEE_EXIT"]["code"]
        event_key = email_id
        audit_data = employee.__dict__
        summary = employee.first_name + employee.last_name
        updated_by = audit_info.get("updated_by")
        updated_at = knowledge_date
        #####################################################

        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        # Add payout status changes details for exit payee
        add_payee_status_details_for_payout_status_changes(
            client_id=client_id,
            payee_emails=[email_id],
            event_type=PayoutStatusChangesTypes.EXIT_PAYEE.value,
        )
    except Exception as e:
        log_me(f"Error updating exit date for  {email_id} : {e}")
        raise Exception(f"Error updating exit date for  {email_id}") from e


def get_all_employee_payroll_details(client_id, as_dicts=False):
    return EmployeePayrollAccessor(client_id).get_all_employees(as_dicts=as_dicts)


def get_current_valid_employee_payroll(client_id, emails, as_dicts=True):
    return EmployeePayrollAccessor(client_id).get_current_valid_employee_payroll(
        timezone.now(), emails, as_dicts=as_dicts
    )


def get_all_valid_employees_for_date(client_id, date=None, as_dicts=True):
    """
    Get all valid effective date aware employees
    """
    if date is None:
        date = timezone.now()
    return EmployeePayrollAccessor(client_id).get_all_valid_employees_for_date(
        date=date, as_dicts=as_dicts
    )


def construct_employee_payroll_records(
    client_id,
    email_id,
    input_data,
    existing_record,
    audit,
    time,
):
    """
    Construct and return employee payroll records based on the input data.

    Parameters:
    - client_id (int): The ID of the client.
    - email_id (str): The email ID of the employee.
    - input_data (List[Dict]): The input data containing payroll fields and their effective start dates.
    - existing_record (List[Dict]): The list of existing payroll records.
    - audit (Dict): The audit details for the modification.
    - time (datetime): The timestamp of the modification.

    Returns:
    - List[Dict]: The list of updated payroll records.
    """
    from commission_engine.services.data_sources import UserReportStrategy

    modified_records = []

    # Create a copy of the existing record
    modified_records.extend(existing_record)

    # Sort the input data by effective start date in ascending order, placing None at the end
    input_data.sort(
        key=lambda x: (
            datetime.datetime.strptime(x["effective_start_date"], "%d-%b-%Y")
            if isinstance(x["effective_start_date"], str)
            else datetime.datetime.max
        )
    )

    # Group the input data by effective start date
    grouped_data = {}
    for record in input_data:
        effective_start_date = record["effective_start_date"]
        if effective_start_date not in grouped_data:
            grouped_data[effective_start_date] = []
        grouped_data[effective_start_date].append(record)

    # Update the existing record for each group
    for effective_start_date, group_records in grouped_data.items():
        modified_record = modified_records[-1].copy() if modified_records else {}
        for record in group_records:
            if record["name"] == "joining_date":
                record["value"] = make_aware(
                    datetime.datetime.strptime(record["value"], "%d-%b-%Y")
                )
            modified_record[record["name"]] = record["value"]
        modified_record["employee_email_id"] = email_id
        modified_record["effective_start_date"] = (
            make_aware(datetime.datetime.strptime(effective_start_date, "%d-%b-%Y"))
            if effective_start_date
            else modified_record["effective_start_date"]
        )
        modified_record["knowledge_begin_date"] = time
        modified_record["additional_details"] = audit
        modified_record["client_id"] = client_id
        # Append the modified record to the list
        modified_records = UserReportStrategy(client_id).split_data_by_date_intervals(
            modified_records, [modified_record]
        )

    if None in grouped_data:
        none_group_records = grouped_data[None]
        for none_record in none_group_records:
            field_name = none_record["name"]
            field_value = none_record["value"]
            if field_name == "joining_date":
                for record in modified_records:
                    record[field_name] = field_value
                    record["knowledge_begin_date"] = time
                    record["additional_details"] = audit
    for record in modified_records:
        record["effective_end_date"] = (
            end_of_day(record["effective_end_date"])
            if "effective_end_date" in record and record["effective_end_date"]
            else None
        )
        record["knowledge_begin_date"] = time
        record["additional_details"] = audit

    return modified_records


def construct_custom_field_records(
    client_id,
    email_id,
    input_data,
    existing_record,
    audit,
    time,
    effective_dated_fields,
):
    """
    Construct and return custom field records based on the input data.

    Parameters:
    - client_id (int): The ID of the client.
    - email_id (str): The email ID of the employee.
    - input_data (List[Dict]): The input data containing custom field values and their effective start dates.
    - existing_record (List[Dict]): The list of existing custom field records.
    - audit (Dict): The audit details for the modification.
    - time (datetime.datetime): The timestamp of the modification.
    - effective_dated_fields (List[str]): The list of custom fields that have is_effective_dated.

    Returns:
    - List[Dict]: The list of updated custom field records.
    """

    from commission_engine.services.data_sources import UserReportStrategy

    modified_records = []

    # Create a copy of the existing record
    modified_records.extend(existing_record)
    # Sort the input data by effective start date in ascending order, placing None at the end
    input_data.sort(
        key=lambda x: (
            datetime.datetime.strptime(x["effective_start_date"], "%d-%b-%Y")
            if isinstance(x["effective_start_date"], str)
            else datetime.datetime.max
        )
    )
    # Group the input data by effective start date
    grouped_data = {}
    for record in input_data:
        effective_start_date = record["effective_start_date"]
        if effective_start_date not in grouped_data:
            grouped_data[effective_start_date] = []
        grouped_data[effective_start_date].append(record)

    # Update the existing record for each group
    for effective_start_date, group_records in grouped_data.items():
        if effective_start_date is not None:
            modified_record = (
                modified_records[-1].copy() if modified_records else {"data": {}}
            )
            modified_record["data"] = (
                modified_record["data"].copy() if modified_record else {}
            )

            for record in group_records:
                modified_record["data"][record["name"]] = record["value"]
            modified_record["email"] = email_id

            # Update the effective start date
            modified_record["effective_start_date"] = (
                make_aware(datetime.datetime.strptime(effective_start_date, "%d-%b-%Y"))
                if isinstance(effective_start_date, str)
                else effective_start_date
            )
            modified_record["knowledge_begin_date"] = time
            modified_record["additional_details"] = audit
            modified_record["client_id"] = client_id
            # Append the modified record to the list
            modified_records = UserReportStrategy(
                client_id
            ).split_data_by_date_intervals(modified_records, [modified_record])

    # Update the data in all modified records for None effective start date
    if None in grouped_data:
        none_group_records = grouped_data[None]
        for none_record in none_group_records:
            field_name = none_record["name"]
            field_value = none_record["value"]
            if field_name not in effective_dated_fields:
                for record in modified_records:
                    record["data"][field_name] = field_value
                    record["knowledge_begin_date"] = time
                    record["additional_details"] = audit

            else:
                record = modified_records[-1]
                record["data"][field_name] = field_value
                record["knowledge_begin_date"] = time
                record["additional_details"] = audit

    for record in modified_records:
        record["effective_end_date"] = (
            end_of_day(record["effective_end_date"])
            if "effective_end_date" in record and record["effective_end_date"]
            else None
        )
        record["knowledge_begin_date"] = time
        record["additional_details"] = audit

    return modified_records


def persist_employee_custom_field_data(
    client_id, employee_email_id, custom_fields, audit, time, user_joining_date
):
    employee_joining_date = EmployeePayrollAccessor(client_id).get_joining_date(
        employee_email_id
    )
    if employee_joining_date:
        employee_joining_date = employee_joining_date.strftime("%d-%b-%Y")
    joining_date = user_joining_date if user_joining_date else employee_joining_date

    payee_all_custom_field_data = CustomFieldDataAccessor(
        client_id
    ).get_payee_all_custom_field_data(employee_email_id)
    effective_dated_fields = CustomFieldsAccessor(
        client_id
    ).get_custom_fields_list_with_filters(
        {"is_archived": False, "is_effective_dated": True}
    )
    non_effective_field_exist = False
    for field in custom_fields:
        if field["name"] not in effective_dated_fields:
            non_effective_field_exist = True
            break

    if not non_effective_field_exist and payee_all_custom_field_data:
        payee_all_custom_field_data = [payee_all_custom_field_data[-1]]
    if not payee_all_custom_field_data:
        for field in custom_fields:
            if not field["effective_start_date"]:
                field["effective_start_date"] = (
                    joining_date if joining_date else "01-JAN-2000"
                )

    invalidate_all_custom_data = copy.deepcopy(payee_all_custom_field_data)
    latest_custom_field_data = construct_custom_field_records(
        client_id,
        employee_email_id,
        custom_fields,
        payee_all_custom_field_data,
        audit,
        time,
        effective_dated_fields,
    )
    date_fields = CustomFieldsAccessor(client_id).get_active_fields_by_field_type(
        "Date"
    )
    latest_custom_field_data = custom_field_date_conversion_utils(
        latest_custom_field_data, date_fields
    )

    validated_result = validate_custom_field_datas(client_id, custom_fields)
    if validated_result["status"] == "Error":
        for error in validated_result["message"]:
            logger.error(
                f"{error} while updating Custom Field data for {employee_email_id}"
            )
        return {
            "status": STATUS_CODE.FAILED,
            "error_message": "Invalid custom field data",
        }
    # Records are invalidated first to avoid unique constraint violation
    CustomFieldDataAccessor(client_id).invalidate_by_records(
        time, invalidate_all_custom_data
    )
    CustomFieldDataAccessor(client_id).create_objects(latest_custom_field_data)
    employee_details = (
        EmployeeAccessor(client_id).get_employee(employee_email_id).__dict__
    )
    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_CUSTOM-FIELD-DATA"]["code"]
    event_key = employee_email_id
    audit_data = latest_custom_field_data[0]
    summary = employee_details["first_name"] + " " + employee_details["last_name"]
    updated_by = audit["updated_by"]
    updated_at = time
    ######################################################
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by,
        updated_at,
        audit_data,
    )
    return {"status": STATUS_CODE.SUCCESS}


def update_employee_payroll(
    client_id, employee_email_id, payroll_fields, audit, time
) -> dict:
    employee_details = (
        EmployeeAccessor(client_id).get_employee(employee_email_id).__dict__
    )
    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_USER"]["code"]
    event_key = employee_email_id
    summary = employee_details["first_name"] + employee_details["last_name"]
    updated_by = audit["updated_by"]
    updated_at = time
    #####################################################
    ui_permission = get_ui_permissions(client_id, updated_by)
    latest_payroll_data = EmployeePayrollAccessor(client_id).get_all_employee_payroll(
        employee_email_id, as_dict=True
    )
    keys_to_check = ["fixed_pay", "variable_pay"]
    for input_data in payroll_fields:
        name = input_data["name"]
        if latest_payroll_data and name != "joining_date":
            latest_payroll_data = [latest_payroll_data[0]]
        if (
            name in keys_to_check
            and ui_permission
            and RbacPermissions.EDIT_PAYROLL.value not in ui_permission
        ):
            return {
                "status": STATUS_CODE.FAILED,
                "error_message": "Dont have permission to edit fixed pay or variable pay",
            }
        if name == "payout_frequency":
            curr_eff_start_date = (
                latest_payroll_data[0].get("effective_start_date")
                if latest_payroll_data
                else None
            )

            new_eff_start_date = (
                make_aware(
                    datetime.datetime.strptime(
                        input_data["effective_start_date"], "%d-%b-%Y"
                    )
                )
                if input_data["effective_start_date"]
                else curr_eff_start_date
            )
            error_type, error_message = None, None
            # Handle custom frequency change during initial save
            if not latest_payroll_data:
                error_type, error_message = handle_custom_payout_frequency(
                    client_id, input_data["value"], new_eff_start_date
                )

            # Checking for active plans, commission locks and quotas for the employee
            # when the payout freq has changed
            elif (
                latest_payroll_data[0].get("payout_frequency", None)
                != input_data["value"]
            ):
                error_type, error_message = handle_payout_frequency_change(
                    client_id=client_id,
                    employee_email_id=employee_email_id,
                    new_effective_start_date=new_eff_start_date,
                    modify_effective_start_date=False,
                )

                if error_type == PayoutFreqMsgs.PLAN:
                    logger.info(
                        "Cannot edit the payout frequency for a period when the user: '%s' has active commission plans.",
                        employee_email_id,
                    )
                    error_message = get_localized_message_service(
                        LocMsgs.PAYOUT_FREQ_CHANGE__PLAN.value,
                        client_id,
                    )
                elif error_type == PayoutFreqMsgs.FORECAST_PLAN:
                    logger.info(
                        "Cannot edit the payout frequency for a period when the user: '%s' has active forecast plans.",
                        employee_email_id,
                    )
                    error_message = get_localized_message_service(
                        LocMsgs.PAYOUT_FREQ_CHANGE__FORECAST_PLAN.value,
                        client_id,
                    )
                elif error_type == PayoutFreqMsgs.COMMISSION_LOCK:
                    logger.info(
                        "Cannot edit the payout frequency for a period where commissions are locked for the user: %s.",
                        employee_email_id,
                    )
                    error_message = get_localized_message_service(
                        LocMsgs.PAYOUT_FREQ_CHANGE__COMMISSION_LOCK.value,
                        client_id,
                    )

            if error_type is not None:
                logger.info(error_message)
                return {
                    "status": STATUS_CODE.FAILED,
                    "error_message": error_message,
                }

    invalidate_payroll_details = copy.deepcopy(latest_payroll_data)
    merged_data = construct_employee_payroll_records(
        client_id,
        employee_email_id,
        payroll_fields,
        latest_payroll_data,
        audit,
        time,
    )
    # Records are invalidated first to avoid unique constraint violation
    EmployeePayrollAccessor(client_id).invalidate_by_records(
        time, invalidate_payroll_details
    )
    EmployeePayrollAccessor(client_id).create_objects(merged_data)
    audit_data = merged_data[len(merged_data) - 1]
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by,
        updated_at,
        audit_data,
    )
    logger.info(
        "Updated employee payroll successfully for user with email id %s",
        employee_email_id,
    )
    return {"status": STATUS_CODE.SUCCESS}


@transaction.atomic
def modify_employee_payroll(
    client_id, employee_email_id, custom_fields, payroll_fields, audit
):
    time = timezone.now()
    user_joining_date = None
    is_error = False
    error_message = None
    try:
        if payroll_fields:
            user_joining_date = next(
                (
                    field["value"]
                    for field in payroll_fields
                    if field["name"] == "joining_date"
                ),
                None,
            )
            payroll_field_result = update_employee_payroll(
                client_id,
                employee_email_id,
                payroll_fields,
                audit,
                time,
            )
            if payroll_field_result["status"] == STATUS_CODE.FAILED:
                error_message = payroll_field_result["error_message"]
                logger.info(
                    "Failed to update payroll data for employee %s for data %s with following error %s",
                    employee_email_id,
                    payroll_fields,
                    error_message,
                )
                is_error = True

        if custom_fields and not is_error:
            persist_custom_field_result = persist_employee_custom_field_data(
                client_id,
                employee_email_id,
                custom_fields,
                audit,
                time,
                user_joining_date,
            )
            if persist_custom_field_result["status"] == STATUS_CODE.FAILED:
                error_message = persist_custom_field_result["error_message"]
                logger.info(
                    "Failed to update custom fields for employee %s for data %s with following error %s",
                    employee_email_id,
                    custom_fields,
                    error_message,
                )
                is_error = True

        if is_error:
            return {"status": status.HTTP_400_BAD_REQUEST, "message": error_message}

        subscription_plan = get_client_subscription_plan(client_id)
        misc_queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        invalidate_and_cache_all_user_groups.si(client_id).set(
            queue=misc_queue_name
        ).apply_async()

        return {
            "message": "Requested Payroll Changes Updated Successfully",
            "status": status.HTTP_201_CREATED,
        }
    except IntegrityError as ex:
        logger.exception("Integrity Error while saving employee payroll")
        raise ex
    except Exception as ex:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            f"Excep in saving employee payroll for user with email id {employee_email_id}",
            extra=error_dict,
        )
        raise SQLParseError() from ex


def validate_payee_fields(client_id, email_id, employee_details, custom_fields):
    """
    Validates the payee fields for a client's employee based on the provided information.

    Args:
        client_id (int): The ID of the client.
        email_id (str): The email ID of the employee.
        employee_details (dict): The details of the employee, including designation, employee ID, and effective start date.
        custom_fields (dict): The custom fields and their values for the employee.

    Returns:
        Response: The response object containing the validation results and other relevant information.

    Raises:
        SQLParseError: If there is an error in parsing SQL statements.
        ValidationError: If there is a validation error in the input data.
    """
    try:
        payroll_mandatory_fields = {
            "employment_country": "Employment Country",
            "pay_currency": "Payout Currency",
            "payout_frequency": "Payout Frequency",
            "payee_role": "Crystal Access",
        }
        # check if employee exists
        if not EmployeeAccessor(client_id).get_employee(email_id):
            return Response(
                {"error_message": "Employee not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        prev_employee_details = EmployeePayrollAccessor(
            client_id
        ).get_latest_employee_payroll(
            email_id,
            [
                "designation",
                "employee_id",
                "effective_start_date",
                "joining_date",
                "fixed_pay",
                "variable_pay",
                "employment_country",
                "payout_frequency",
                "pay_currency",
                "payee_role",
            ],
        )
        if not prev_employee_details and not employee_details.get("joining_date"):
            return Response(
                {"error_message": "Joining Date is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not prev_employee_details:
            missing_mandatory_fields = [
                value
                for key, value in payroll_mandatory_fields.items()
                if key not in employee_details
            ]
            if len(missing_mandatory_fields) > 0:
                return Response(
                    {
                        "error_message": f"Mandatory fields are missing - {', '.join(missing_mandatory_fields)}"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        prev_custom_fields = CustomFieldDataAccessor(
            client_id
        ).get_latest_custom_field_data(email_id, ["data", "effective_start_date"])
        effective_dated_fields = CustomFieldsAccessor(
            client_id
        ).get_custom_fields_list_with_filters(
            {"is_archived": False, "is_effective_dated": True}
        )
        date_fields = CustomFieldsAccessor(
            client_id
        ).get_custom_fields_list_with_filters(
            {"is_archived": False, "field_type": "Date"}
        )
        checkbox_fields = CustomFieldsAccessor(
            client_id
        ).get_custom_fields_list_with_filters(
            {"is_archived": False, "field_type": "Checkbox"}
        )

        modified_employee_fields = {}
        for key, value in employee_details.items():
            if key == "joining_date":
                modified_employee_fields[key] = (
                    make_aware(datetime.datetime.strptime(value, "%d-%b-%Y"))
                    if value
                    else None
                )
            elif key in ("variable_pay", "fixed_pay"):
                modified_employee_fields[key] = Decimal(value) if value else None
            else:
                modified_employee_fields[key] = value

        modified_custom_fields = {}
        for key, value in custom_fields.items():
            if key in date_fields:
                modified_custom_fields[key] = (
                    datetime.datetime.strptime(value, "%d-%b-%Y").strftime("%Y-%m-%d")
                    if value
                    else None
                )
            elif (
                key in checkbox_fields
                and prev_custom_fields
                and "data" in prev_custom_fields
                and key not in prev_custom_fields["data"]
            ):
                prev_custom_fields["data"][key] = False
                modified_custom_fields[key] = value
            else:
                modified_custom_fields[key] = value

        joining_date = (
            prev_employee_details["joining_date"]
            if prev_employee_details and "joining_date" in prev_employee_details
            else employee_details.get("joining_date")
        )
        date_obj = (
            datetime.datetime.strptime(joining_date, "%d-%b-%Y")
            if isinstance(joining_date, str)
            else joining_date
        )
        new_joining_date = date_obj - timedelta(days=1)
        new_joining_date_str = new_joining_date.strftime("%d-%b-%Y")

        # Identify modified employee `fields`
        employee_fields_changed = [
            key
            for key, value in modified_employee_fields.items()
            if (not prev_employee_details and value not in (None, ""))
            or (
                prev_employee_details
                and key in prev_employee_details
                and prev_employee_details[key] != value
            )
        ]

        # Identify modified custom fields
        custom_field_changed = [
            key
            for key, value in modified_custom_fields.items()
            if (not prev_custom_fields and value is not None and value is not False)
            # Previously we checked if 'value not in (None, False)',
            # but now we explicitly check if 'value is not None and value is not False'
            # because we want to allow 0 as a valid value. It was previously ignored
            # because 0 evaluates to False in a boolean context.
            or (
                prev_custom_fields
                and key in prev_custom_fields["data"]
                and prev_custom_fields["data"][key] != value
            )
            or (
                prev_custom_fields
                and key not in prev_custom_fields["data"]
                and value is not None
                and value is not False
            )
        ]

        result = {
            "modified_payroll_fields": employee_fields_changed,
            "modified_custom_fields": custom_field_changed,
            "effective_dated_fields": effective_dated_fields,
            "latest_payroll_effective_date": (
                prev_employee_details.get("effective_start_date")
                if prev_employee_details
                and "effective_start_date" in prev_employee_details
                else new_joining_date_str
            ),
            "latest_custom_field_effective_date": (
                prev_custom_fields.get("effective_start_date")
                if prev_custom_fields and "effective_start_date" in prev_custom_fields
                else new_joining_date_str
            ),
        }

        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        raise SQLParseError() from e


def modify_effective_start_date(client_id, email, effective_start_date, field_type):
    try:
        kd = timezone.now()
        start_date = make_aware(_date.strptime(effective_start_date, "%d-%b-%Y"))
        end_date = make_aware(end_of_day(parse(effective_start_date, dayfirst=True)))
        effective_end_date = end_date - timedelta(days=1)
        if field_type == "payroll":
            error_type, error_message = handle_payout_frequency_change(
                client_id=client_id,
                employee_email_id=email,
                new_effective_start_date=start_date,
                modify_effective_start_date=True,
            )
            if error_type == PayoutFreqMsgs.PLAN:
                logger.info(
                    "Cannot edit the effective start date for a period when the user: '%s' has active commission plans.",
                    email,
                )
                error_message = get_localized_message_service(
                    LocMsgs.ESD_CHANGE_PLAN.value,
                    client_id,
                )
            elif error_type == PayoutFreqMsgs.COMMISSION_LOCK:
                logger.info(
                    "Cannot edit the effective start date for a period where commissions are locked for the user: %s.",
                    email,
                )
                error_message = get_localized_message_service(
                    LocMsgs.ESD_CHANGE_COMMISSION_LOCK.value,
                    client_id,
                )
            elif error_type == PayoutFreqMsgs.FORECAST_PLAN:
                logger.info(
                    "Cannot edit the effective start date for a period when the user: '%s' has active forecast plans.",
                    email,
                )
                error_message = get_localized_message_service(
                    LocMsgs.ESD_CHANGE_FORECAST_PLAN.value,
                    client_id,
                )

            if error_type is not None:
                logger.info(error_message)
                return Response(error_message, status=status.HTTP_400_BAD_REQUEST)

            EmployeePayrollAccessor(client_id).update_start_date_for_latest(
                email, kd, start_date
            )
            EmployeePayrollAccessor(
                client_id
            ).update_effective_end_date_for_prev_to_latest(
                email, kd, effective_end_date
            )
        else:
            error_message = None
            # Check if new effective start date is before joining date
            joining_date = EmployeePayrollAccessor(client_id).get_joining_date(email)
            if joining_date and start_date < joining_date:
                error_message = f"New effective start date should be greater than or equal to {joining_date.strftime('%d-%b-%Y')} which is the joining date of payee"
            # Check if new effective start date is on or before previous data's start date
            prev_data = CustomFieldDataAccessor(
                client_id
            ).get_prev_to_latest_custom_field_data(email)
            if prev_data is not None:
                accepted_start_date = end_of_day(prev_data.effective_start_date)
                if start_date <= accepted_start_date:
                    error_message = f"New effective start date should be greater than {accepted_start_date.strftime('%d-%b-%Y')} which is the effective start date of the previous custom field data"
            if error_message:
                logger.info(error_message)
                return Response(error_message, status=status.HTTP_400_BAD_REQUEST)

            CustomFieldDataAccessor(client_id).update_start_date_for_latest(
                email, kd, start_date
            )
            CustomFieldDataAccessor(
                client_id
            ).update_effective_end_date_for_prev_to_latest(
                email, kd, effective_end_date
            )

        return Response("SUCCESS", status=status.HTTP_200_OK)
    except Exception as e:
        error_message = f"Error occurred while modifying effective start date: {str(e)}"
        return Response(error_message, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_all_payees_plan_data(client_id):
    return PlanPayeeAccessor(client_id=client_id).client_kd_aware()


# ********* Employee Plan details


def persist_employee_plan(client_id, plan_serializer, request, kbd):
    log_context = {
        "client_id": client_id,
        "kbd": kbd,
        "payee_email_id": pydash.get(request.data, ["employee_email_id"]),
        "effective_start_date": pydash.get(
            request.data, ["plan_details", "effective_start_date"]
        ),
        "plan_id": pydash.get(request.data, ["plan_details", "plan_id"]),
    }
    time = kbd
    if time is None:
        time = timezone.now()
    plan_id = request.data["plan_details"]["plan_id"]
    employee_details = (
        EmployeeAccessor(client_id)
        .get_employee(request.data["employee_email_id"])
        .__dict__
    )
    cp = CommissionPlanAccessor(client_id).get_latest_commission_plan_by_id(plan_id)
    #################### validations #####################
    if PlanPayeeAccessor(client_id).employee_exists_in_plan(
        request.data["employee_email_id"], plan_id
    ):
        msg = f"User '{request.data['employee_email_id']}' is already part of the plan '{cp.plan_name}'!"
        logger.error(msg, extra=log_context)
        return Response({"errors": [msg]}, status=status.HTTP_400_BAD_REQUEST)
    validations_res = validate_plan_and_payee(
        client_id,
        {
            "plan_id": plan_id,
            "plan_name": cp.plan_name,
            "plan_type": cp.plan_type,
            "plan_start_date": cp.plan_start_date.strftime("%d-%b-%Y"),
            "plan_end_date": cp.plan_end_date.strftime("%d-%b-%Y"),
            "added_plan_payees": [
                {
                    "employee_email_id": request.data["employee_email_id"],
                    "effective_start_date": pydash.get(
                        request.data, ["plan_details", "effective_start_date"]
                    ),
                    "effective_end_date": pydash.get(
                        request.data, ["plan_details", "effective_end_date"]
                    ),
                    "settlement_end_date": (
                        cp.settlement_end_date.strftime("%d-%b-%Y")
                        if cp.settlement_end_date
                        else None
                    ),
                }
            ],
            "is_settlement_end_date": cp.is_settlement_end_date,
            "settlement_end_date": (
                cp.settlement_end_date.strftime("%d-%b-%Y")
                if cp.settlement_end_date
                else None
            ),
            "modified_plan_payees": [],
            "removed_plan_payees": [],
        },
    )
    res = {"errors": []}
    if pydash.get(validations_res, ["plan_payees", "error"]):
        res["errors"].append(validations_res["plan_payees"]["error"])
    if pydash.get(
        validations_res,
        ["plan_payees", "payees", request.data["employee_email_id"], "errors"],
    ):
        res["errors"].extend(
            validations_res["plan_payees"]["payees"][request.data["employee_email_id"]][
                "errors"
            ]
        )
    if res["errors"]:
        logger.error(
            "Identified %s validation errors for adding '%s' to the plan '%s'",
            len(res["errors"]),
            request.data["employee_email_id"],
            cp.plan_name,
            extra=log_context,
        )
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    ######################################################

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_USERS-PLAN"]["code"]
    event_key = request.data["employee_email_id"]
    summary = employee_details["first_name"] + employee_details["last_name"]
    audit_data = copy.deepcopy(request.data["plan_details"])
    audit_data["plan_name"] = cp.plan_name
    audit_data.pop("plan_id", None)
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################

    plan_dict = request.data["plan_details"]
    plan_dict["client"] = client_id
    plan_dict["additional_details"] = request.audit
    plan_dict["knowledge_begin_date"] = time
    plan_dict["employee_email_id"] = request.data["employee_email_id"]
    plan_dict["effective_start_date"] = make_aware(
        start_of_day(_date.strptime(plan_dict["effective_start_date"], "%d-%b-%Y"))
    )
    plan_dict["effective_end_date"] = make_aware(
        end_of_day(_date.strptime(plan_dict["effective_end_date"], "%d-%b-%Y"))
    )
    if cp.settlement_end_date:
        plan_dict["settlement_end_date"] = cp.settlement_end_date
    p_ser = plan_serializer(data=plan_dict)
    pp_ser = PlanPayeeSerializer(data=plan_dict)
    try:
        if p_ser.is_valid() and pp_ser.is_valid():
            PlanDetailsAccessor(client_id).persist_employee_plan_details(p_ser)
            PlanPayeeAccessor(client_id).persist_payee_plan(pp_ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Saved employee plan successfully for user with email id %s",
                request.data["employee_email_id"],
                extra=log_context,
            )
            notify_plan_change(
                client_id,
                request.data["employee_email_id"],
                plan_dict["plan_id"],
                plan_dict["effective_start_date"],
                plan_dict["effective_end_date"],
            )

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.MAP_PAYEE.value,
                "event_properties": {
                    SegmentProperties.PAYEE_NAME.value: summary,
                    SegmentProperties.PAYEE_EMP_ID.value: request.data[
                        "employee_email_id"
                    ],
                    SegmentProperties.ENTRY_POINT.value: request.data.get(
                        "entry_point"
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            invalidate_and_cache_all_user_groups.si(client_id).set(
                queue=misc_queue_name
            ).apply_async()
            return Response("SUCCESS", status=status.HTTP_201_CREATED)
        else:
            error_dict = {
                "ser_erros": p_ser.errors,
                "pp_ser_erros": pp_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in Saving employee plan for user with email id %s",
                request.data["employee_email_id"],
                extra={
                    **log_context,
                    **error_dict,
                },
            )
            return Response(p_ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Excep in Saving employee plan for user with email id %s",
            request.data["employee_email_id"],
            extra={
                **log_context,
                **error_dict,
            },
        )
        raise SQLParseError() from e


def modify_employee_plan(client_id, plan_serializer, request):
    time = timezone.now()
    is_edit = request.data["is_edit"]
    if is_edit:
        return edit_employee_plan(client_id, plan_serializer, request, time)
    else:
        return update_employee_plan(client_id, plan_serializer, request, time)


def edit_employee_plan(client_id, plan_serializer, request, kd):
    log_context = {
        "client_id": client_id,
        "kd": kd,
        "payee_email_id": pydash.get(request.data, ["employee_email_id"]),
        "effective_start_date": pydash.get(
            request.data, ["plan_details", "effective_start_date"]
        ),
        "plan_id": pydash.get(request.data, ["plan_details", "plan_id"]),
    }
    plan_id = request.data["plan_details"]["plan_id"]
    employee_details = (
        EmployeeAccessor(client_id)
        .get_employee(request.data["employee_email_id"])
        .__dict__
    )
    cp = CommissionPlanAccessor(client_id).get_latest_commission_plan_by_id(plan_id)
    plan_detail = PlanDetailsAllAccessor(client_id).get_latest_payee_plan(
        plan_id, request.data["employee_email_id"]
    )
    existing_plan_id = PlanDetailsAccessor(client_id).get_latest_employee_plan_id(
        request.data["employee_email_id"]
    )
    #################### validations #####################
    validations_res = validate_plan_and_payee(
        client_id,
        {
            "plan_id": plan_id,
            "plan_name": cp.plan_name,
            "plan_type": cp.plan_type,
            "plan_start_date": cp.plan_start_date.strftime("%d-%b-%Y"),
            "plan_end_date": cp.plan_end_date.strftime("%d-%b-%Y"),
            "added_plan_payees": [],
            "modified_plan_payees": [
                {
                    "employee_email_id": request.data["employee_email_id"],
                    "effective_start_date": request.data["plan_details"][
                        "effective_start_date"
                    ],
                    "effective_end_date": request.data["plan_details"][
                        "effective_end_date"
                    ],
                    "settlement_end_date": (
                        plan_detail.settlement_end_date.strftime("%d-%b-%Y")
                        if plan_detail and plan_detail.settlement_end_date
                        else None
                    ),
                }
            ],
            "is_settlement_end_date": cp.is_settlement_end_date,
            "settlement_end_date": (
                cp.settlement_end_date.strftime("%d-%b-%Y")
                if cp.settlement_end_date
                else None
            ),
            "removed_plan_payees": [],
            "is_edit": True,
            "existing_plan_id": existing_plan_id,
        },
    )
    res = {"errors": []}
    if pydash.get(validations_res, ["plan_payees", "error"]):
        res["errors"].append(validations_res["plan_payees"]["error"])
    if pydash.get(
        validations_res,
        ["plan_payees", "payees", request.data["employee_email_id"], "errors"],
    ):
        res["errors"].extend(
            validations_res["plan_payees"]["payees"][request.data["employee_email_id"]][
                "errors"
            ]
        )
    if res["errors"]:
        logger.error(
            "Identified %s validation errors for updating '%s' in the plan '%s'",
            len(res["errors"]),
            request.data["employee_email_id"],
            cp.plan_name,
            extra=log_context,
        )
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    ######################################################

    ###################### audit log #####################
    event_type_code = EVENT["EDIT_USERS-PLAN"]["code"]
    event_key = request.data["employee_email_id"]
    summary = employee_details["first_name"] + employee_details["last_name"]
    audit_data = copy.deepcopy(request.data["plan_details"])
    audit_data["plan_name"] = cp.plan_name
    audit_data.pop("plan_id", None)
    updated_by = request.audit["updated_by"]
    updated_at = kd
    ######################################################

    plan_dict = request.data["plan_details"]
    plan_dict["client"] = client_id
    plan_dict["additional_details"] = request.audit
    plan_dict["knowledge_begin_date"] = kd
    plan_dict["employee_email_id"] = request.data["employee_email_id"]
    plan_dict["effective_start_date"] = make_aware(
        start_of_day(_date.strptime(plan_dict["effective_start_date"], "%d-%b-%Y"))
    )
    plan_dict["effective_end_date"] = make_aware(
        end_of_day(_date.strptime(plan_dict["effective_end_date"], "%d-%b-%Y"))
    )
    if plan_detail and plan_detail.settlement_end_date:
        plan_dict["settlement_end_date"] = plan_detail.settlement_end_date
    p_ser = plan_serializer(data=plan_dict)
    pp_ser = PlanPayeeSerializer(data=plan_dict)
    try:
        if p_ser.is_valid() and pp_ser.is_valid():
            PlanDetailsAccessor(client_id).invalidate_latest(
                request.data["employee_email_id"], kd
            )
            PlanDetailsAccessor(client_id).persist_employee_plan_details(p_ser)
            PlanPayeeAccessor(client_id).invalidate_latest(
                existing_plan_id, plan_dict["employee_email_id"], kd
            )
            PlanPayeeAccessor(client_id).persist_payee_plan(pp_ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Edited employee plan successfully for user with email id %s",
                request.data["employee_email_id"],
                extra=log_context,
            )
            notify_plan_change(
                client_id,
                request.data["employee_email_id"],
                plan_dict["plan_id"],
                plan_dict["effective_start_date"],
                plan_dict["effective_end_date"],
            )
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            invalidate_and_cache_all_user_groups.si(client_id).set(
                queue=misc_queue_name
            ).apply_async()
            return Response("SUCCESS", status=status.HTTP_201_CREATED)
        else:
            error_dict = {
                "ser_erros": p_ser.errors,
                "pp_ser_erros": pp_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in Editing employee plan for user with email id %s",
                request.data["employee_email_id"],
                extra={
                    **log_context,
                    **error_dict,
                },
            )
            return Response(p_ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Excep in Editing employee plan for user with email id %s",
            request.data["employee_email_id"],
            extra={
                **log_context,
                **error_dict,
            },
        )
        raise SQLParseError() from e


def update_employee_plan(client_id, plan_serializer, request, kd):
    log_context = {
        "client_id": client_id,
        "kd": kd,
        "payee_email_id": pydash.get(request.data, ["employee_email_id"]),
        "effective_start_date": pydash.get(
            request.data, ["plan_details", "effective_start_date"]
        ),
        "plan_id": pydash.get(request.data, ["plan_details", "plan_id"]),
    }
    plan_id = request.data["plan_details"]["plan_id"]
    employee_details = (
        EmployeeAccessor(client_id)
        .get_employee(request.data["employee_email_id"])
        .__dict__
    )
    cp = CommissionPlanAccessor(client_id).get_latest_commission_plan_by_id(plan_id)
    plan_detail = PlanDetailsAllAccessor(client_id).get_latest_payee_plan(
        plan_id, request.data["employee_email_id"]
    )
    #################### validations #####################
    if PlanPayeeAccessor(client_id).employee_exists_in_plan(
        request.data["employee_email_id"], plan_id
    ):
        msg = f"User '{request.data['employee_email_id']}' is already part of the plan '{cp.plan_name}'!"
        logger.error(msg, extra=log_context)
        return Response({"errors": [msg]}, status=status.HTTP_400_BAD_REQUEST)
    validations_res = validate_plan_and_payee(
        client_id,
        {
            "plan_id": plan_id,
            "plan_name": cp.plan_name,
            "plan_type": cp.plan_type,
            "plan_start_date": cp.plan_start_date.strftime("%d-%b-%Y"),
            "plan_end_date": cp.plan_end_date.strftime("%d-%b-%Y"),
            "added_plan_payees": [],
            "modified_plan_payees": [
                {
                    "employee_email_id": request.data["employee_email_id"],
                    "effective_start_date": request.data["plan_details"][
                        "effective_start_date"
                    ],
                    "effective_end_date": request.data["plan_details"][
                        "effective_end_date"
                    ],
                    "settlement_end_date": (
                        plan_detail.settlement_end_date.strftime("%d-%b-%Y")
                        if plan_detail and plan_detail.settlement_end_date
                        else None
                    ),
                }
            ],
            "is_settlement_end_date": cp.is_settlement_end_date,
            "settlement_end_date": (
                cp.settlement_end_date.strftime("%d-%b-%Y")
                if cp.settlement_end_date
                else None
            ),
            "removed_plan_payees": [],
        },
    )
    res = {"errors": []}
    if pydash.get(validations_res, ["plan_payees", "error"]):
        res["errors"].append(validations_res["plan_payees"]["error"])
    if pydash.get(
        validations_res,
        ["plan_payees", "payees", request.data["employee_email_id"], "errors"],
    ):
        res["errors"].extend(
            validations_res["plan_payees"]["payees"][request.data["employee_email_id"]][
                "errors"
            ]
        )
    if res["errors"]:
        logger.error(
            "Identified %s validation errors for updating '%s' in the plan '%s'",
            len(res["errors"]),
            request.data["employee_email_id"],
            cp.plan_name,
            extra=log_context,
        )
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    ######################################################

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_USERS-PLAN"]["code"]
    event_key = request.data["employee_email_id"]
    summary = employee_details["first_name"] + employee_details["last_name"]
    audit_data = copy.deepcopy(request.data["plan_details"])
    audit_data["plan_name"] = cp.plan_name
    audit_data.pop("plan_id", None)
    updated_by = request.audit["updated_by"]
    updated_at = kd
    ######################################################

    plan_dict = request.data["plan_details"]
    plan_dict["client"] = client_id
    plan_dict["additional_details"] = request.audit
    plan_dict["knowledge_begin_date"] = kd
    plan_dict["employee_email_id"] = request.data["employee_email_id"]
    plan_dict["effective_start_date"] = make_aware(
        start_of_day(_date.strptime(plan_dict["effective_start_date"], "%d-%b-%Y"))
    )
    plan_dict["effective_end_date"] = make_aware(
        end_of_day(_date.strptime(plan_dict["effective_end_date"], "%d-%b-%Y"))
    )
    if plan_detail and plan_detail.settlement_end_date:
        plan_dict["settlement_end_date"] = plan_detail.settlement_end_date
    p_ser = plan_serializer(data=plan_dict)
    pp_ser = PlanPayeeSerializer(data=plan_dict)
    try:
        if p_ser.is_valid() and pp_ser.is_valid():
            PlanDetailsAccessor(client_id).persist_employee_plan_details(p_ser)
            PlanPayeeAccessor(client_id).persist_payee_plan(pp_ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Updated employee plan successfully for user with email id %s",
                request.data["employee_email_id"],
                extra=log_context,
            )
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            invalidate_and_cache_all_user_groups.si(client_id).set(
                queue=misc_queue_name
            ).apply_async()
            return Response("SUCCESS", status=status.HTTP_201_CREATED)
        else:
            error_dict = {
                "ser_erros": p_ser.errors,
                "pp_ser_erros": pp_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in Updating employee plan for user with email id %s",
                request.data["employee_email_id"],
                extra={
                    **log_context,
                    **error_dict,
                },
            )
            return Response(p_ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Excep in Updating employee plan for user with email id %s",
            request.data["employee_email_id"],
            extra={
                **log_context,
                **error_dict,
            },
        )
        raise SQLParseError() from e


def bulk_add(
    client_id,
    employee_serializer,
    payroll_serializer,
    hierarchy_serializer,
    request,
    audit,
):
    req_logger = request.logger
    try:
        time = timezone.now()
        response = {}
        validate_response = validate_bulk_add(
            client_id,
            employee_serializer,
            payroll_serializer,
            hierarchy_serializer,
            request,
            audit,
        )
        validate_resp_data = validate_response.data
        validate_new_users = validate_resp_data["nusers"]
        req_logger.info("BEGIN: BULK ADD FOR {} USERS".format(len(request.data)))
        if validate_new_users > 50:
            task_arg_tuples = []
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            for record in request.data:
                record_id = record["id"]
                vres = validate_resp_data[record_id]
                task_arg_tuples.append(
                    process_bulk_add_task.si(client_id, audit, vres, record, time).set(
                        queue=misc_queue_name
                    )
                )
            bulk_add_chain = chain(
                *task_arg_tuples,
                invalidate_and_cache_all_user_groups.si(client_id).set(
                    queue=misc_queue_name
                ),
            )
            bulk_add_chain.apply_async()
            logger.info("BULK ADD FOR %s USERS SENT TO CELERY TASK", len(request.data))

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.USER_UPLOAD.value,
                "event_properties": {
                    SegmentProperties.NUM_OF_USERS_IMPORTED.value: len(request.data),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            return Response(
                {"nusers": validate_new_users}, status=status.HTTP_202_ACCEPTED
            )
        else:
            failed_users = 0
            for req in request.data:
                record_id = req["id"]
                req_email = req["employee_email_id"]
                if (
                    validate_resp_data[record_id]["status"] == "Validation Passed"
                    or validate_resp_data[record_id]["status"] == "Validation Warning"
                ):
                    # converting user_role string to list of single role as a part of Multi User role support phase 1.
                    if "user_role" in req:
                        req["user_role"] = [req["user_role"]]
                    e_ser = construct_employee_dict(
                        client_id, audit, employee_serializer, req, time
                    )
                    p_ser = construct_employee_payroll_dict(
                        client_id, audit, payroll_serializer, req, time
                    )
                    h_ser = construct_hierarchy_dict(
                        client_id, audit, hierarchy_serializer, req, time
                    )
                    c_ser = construct_custom_field_dict(
                        client_id,
                        audit,
                        CustomFieldDataSerializer,
                        req,
                        time,
                    )
                    if (
                        (not e_ser or e_ser.is_valid())
                        and (not p_ser or p_ser.is_valid())
                        and (not h_ser or h_ser.is_valid())
                        and (not c_ser or c_ser.is_valid())
                    ):
                        if e_ser and not is_email_exists(client_id, req_email):
                            create_user_in_auth(client_id, e_ser.validated_data)
                            EmployeeWriteAccessor(client_id).persist_employee(e_ser)
                            ###################### audit log #####################
                            event_type_code = EVENT["CREATE_USER"]["code"]
                            event_key = req_email
                            summary = req["first_name"] + req["last_name"]
                            audit_data = copy.deepcopy(req)
                            updated_by = audit["updated_by"]
                            updated_at = time
                            ######################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )

                        if p_ser:
                            effective_end_date = make_aware_wrapper(
                                end_of_day(p_ser.validated_data["effective_start_date"])
                            ) - timedelta(days=1)
                            EmployeePayrollAccessor(
                                client_id
                            ).add_or_update_employee_payroll(
                                p_ser, time, effective_end_date
                            )
                            employee_details = (
                                EmployeeAccessor(client_id)
                                .get_employee(req_email)
                                .__dict__
                            )
                            ###################### audit log #####################
                            event_type_code = EVENT["UPDATE_USER"]["code"]
                            event_key = req_email
                            audit_data = {
                                **copy.deepcopy(req["employee_payroll"]),
                                **employee_details,
                            }
                            summary = audit_data["first_name"] + audit_data["last_name"]
                            updated_by = request.audit["updated_by"]
                            updated_at = time
                            #####################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )
                        if h_ser:
                            effective_end_date = make_aware_wrapper(
                                end_of_day(h_ser.validated_data["effective_start_date"])
                            ) - timedelta(days=1)
                            HierarchyAccessor(
                                client_id
                            ).add_or_update_employee_hierarchy(
                                h_ser, time, effective_end_date
                            )
                            employee_details = (
                                EmployeeAccessor(client_id)
                                .get_employee(req_email)
                                .__dict__
                            )
                            ###################### audit log #####################
                            event_type_code = EVENT["UPDATE_MANAGER"]["code"]
                            event_key = req_email
                            audit_data = {
                                **employee_details,
                                "reporting_manager_email_id": req["employee_hierarchy"][
                                    "reporting_manager_email_id"
                                ],
                                "manager_effective_start_date": req[
                                    "employee_hierarchy"
                                ]["effective_start_date"],
                            }
                            summary = audit_data["first_name"] + audit_data["last_name"]
                            updated_by = request.audit["updated_by"]
                            updated_at = time
                            ######################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )

                        if c_ser:
                            c_ser = modify_bulk_add_custom_field_data(c_ser, client_id)
                            CustomFieldDataAccessor(
                                client_id
                            ).add_or_update_custom_field_data(c_ser)
                            employee_details = (
                                EmployeeAccessor(client_id)
                                .get_employee(req_email)
                                .__dict__
                            )
                            ###################### audit log #####################
                            event_type_code = EVENT["UPDATE_CUSTOM-FIELD-DATA"]["code"]
                            event_key = req_email
                            audit_data = {
                                **employee_details,
                                "Email": req_email,
                            }
                            summary = audit_data["first_name"] + audit_data["last_name"]
                            updated_by = request.audit["updated_by"]
                            updated_at = time
                            ######################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )
                        response[record_id] = {
                            "status": "Save Successful",
                            "errors": None,
                            "warnings": None,
                        }
                    else:
                        failed_users += 1
                        logger.error(
                            "Error occured while adding user with email id %s",
                            req_email,
                        )
                        response[record_id] = {
                            "status": "ERROR",
                            "errors": [
                                e_ser.errors if e_ser else None,
                                p_ser.errors if p_ser else None,
                                h_ser.errors if h_ser else None,
                            ],
                            "warnings": [],
                        }
                else:
                    response[record_id] = validate_resp_data[record_id]

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.USER_UPLOAD.value,
                "event_properties": {
                    SegmentProperties.NUM_OF_USERS_IMPORTED.value: validate_new_users,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            if failed_users > 0:
                logger.info(
                    "END: BULK ADD FOR %s USERS FAILED WHILE ADDING %s USERS",
                    failed_users,
                    validate_new_users,
                )
            else:
                logger.info(
                    "END: BULK ADD FOR %s USERS MADE SUCCESSFULLY", validate_new_users
                )
            delete_hierarchy_cache(client_id)
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            invalidate_and_cache_all_user_groups.si(client_id).set(
                queue=misc_queue_name
            ).apply_async()
        response["nusers"] = validate_new_users
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        req_logger.error(
            "Exception raised while bulk adding user",
            error_dict,
        )
        return Response(
            f"Error occured in the server {e}",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    return Response(response, status=status.HTTP_201_CREATED)


def validate_bulk_add(
    client_id,
    employee_serializer,
    payroll_serializer,
    hierarchy_serializer,
    request,
    audit,
):
    time = timezone.now()
    response = {}
    new_users = 0
    seen_emails = {}
    logged_user = request.user.username
    for req in request.data:
        record_id = req["id"]
        req_email = req["employee_email_id"]
        e_ser = construct_employee_dict(
            client_id, audit, employee_serializer, req, time
        )
        p_ser = construct_employee_payroll_dict(
            client_id, audit, payroll_serializer, req, time
        )
        h_ser = construct_hierarchy_dict(
            client_id, audit, hierarchy_serializer, req, time
        )
        c_ser = construct_custom_field_dict(
            client_id, audit, CustomFieldDataSerializer, req, time
        )
        if (
            (not e_ser or e_ser.is_valid())
            and (not p_ser or p_ser.is_valid())
            and (not h_ser or h_ser.is_valid())
            and (not c_ser or c_ser.is_valid())
        ):
            errors = []
            warnings = []
            if req_email in seen_emails:
                errors.append("Duplicate record found for Email Id in the table")
            else:
                seen_emails[req_email] = 1

            # check domain of users in bulk add
            # if req_email.split("@")[1] != current_user_domain:
            #    errors.append(
            #        "You can only add/update payees who belong to your domain"
            #    )

            # manager email validation
            if h_ser:
                if not is_email_exists(
                    client_id,
                    h_ser.validated_data["reporting_manager_email_id"],
                ):
                    errors.append("Manager email id not present")
                if not (
                    req_email != h_ser.validated_data["reporting_manager_email_id"]
                ):
                    errors.append("Payee and Manager cannot be the same person")
            if not is_email_exists(client_id, req_email):
                if not e_ser:
                    errors.append("Email not present")
                else:
                    new_users = new_users + 1
                    if e_ser:
                        user_role = e_ser.validated_data["user_role"]
                        if not check_user_has_permission(logged_user, client_id):
                            errors.append(
                                "You do not have permission to update "
                                + user_role
                                + " user role"
                            )
                    if p_ser:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            >= p_ser.validated_data["joining_date"]
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than or equal to joining date"
                            )
                    if h_ser:
                        if p_ser:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= p_ser.validated_data["joining_date"]
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        p_ser.validated_data["joining_date"].strftime(
                                            "%d-%b-%Y"
                                        )
                                    )
                                )
                        else:
                            errors.append(
                                "Need employee joining date to set the hierarchy effective start date"
                            )
            else:
                if e_ser:
                    if p_ser or h_ser:
                        warnings.append("Email exists")
                    else:
                        errors.append("Email exists")
                # user role validation
                user_role = EmployeeAccessor(client_id).get_employee_role(req_email)
                if not check_user_has_permission(logged_user, client_id):
                    errors.append(
                        "You do not have permission to update "
                        + ", ".join(user_role)
                        + " user role"
                    )
                if p_ser:
                    latest_record = EmployeePayrollAccessor(
                        client_id
                    ).get_latest_employee_payroll(req_email)
                    if latest_record:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            > latest_record.effective_start_date
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than "
                                + str(
                                    latest_record.effective_start_date.strftime(
                                        "%d-%b-%Y"
                                    )
                                )
                            )
                    else:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            >= p_ser.validated_data["joining_date"]
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than or equal to joining date"
                            )
                if h_ser:
                    hier_record = HierarchyAccessor(
                        client_id
                    ).get_latest_employee_hierarchy(req_email)
                    if hier_record:
                        if not (
                            h_ser.validated_data["effective_start_date"]
                            > hier_record.effective_start_date
                        ):
                            errors.append(
                                "Hierarchy effective start date should be greater than "
                                + str(
                                    hier_record.effective_start_date.strftime(
                                        "%d-%b-%Y"
                                    )
                                )
                            )
                    else:
                        payroll_record = EmployeePayrollAccessor(
                            client_id
                        ).get_latest_employee_payroll(req_email)
                        if payroll_record:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= payroll_record.joining_date
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        payroll_record.joining_date.strftime("%d-%b-%Y")
                                    )
                                )
                        elif p_ser:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= p_ser.validated_data["joining_date"]
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        p_ser.validated_data["joining_date"].strftime(
                                            "%d-%b-%Y"
                                        )
                                    )
                                )
                        else:
                            errors.append(
                                "Need employee joining date to set the hierarchy effective start date"
                            )
                if c_ser:
                    validated_result = validate_custom_field_data(
                        request.client_id, c_ser
                    )
                    if validated_result["status"] == "Error":
                        for error in validated_result["message"]:
                            errors.append(error)
            validation_status = "Validation Passed"
            if errors:
                validation_status = "Validation Failed"
            elif warnings:
                validation_status = "Validation Warning"
            response[record_id] = {
                "status": validation_status,
                "errors": errors,
                "warnings": warnings,
            }
        else:
            response[record_id] = {
                "status": "ERROR",
                "errors": [
                    e_ser.errors if e_ser else None,
                    p_ser.errors if p_ser else None,
                    h_ser.errors if h_ser else None,
                    c_ser.errors if c_ser else None,
                ],
                "warnings": [],
            }
    response["nusers"] = new_users
    return Response(response, status=status.HTTP_201_CREATED)


def check_user_has_permission(email_id, client_id):
    ui_permission = get_ui_permissions(client_id, email_id)
    if RbacPermissions.MANAGE_USERS.value in ui_permission:
        return True
    return False


def persist_employee_spiff_plan(request):
    time = timezone.now()
    client_id = request.client_id
    data = request.data
    spiff_plans = data["spiff_plans"]
    employee_email_id = data["employee_email_id"]
    audit = request.audit
    log_context = {"payee_email_id": employee_email_id}
    plan_ids = pydash.chain(spiff_plans).map_("plan_id").compact().uniq().value()
    plans = CommissionPlanAccessor(client_id).get_plans_by_ids(plan_ids)
    plans_map = {str(plan.plan_id): plan for plan in plans}
    ##################### validations ####################
    res = {"plans": {}}
    for plan in spiff_plans:
        cp = plans_map[plan["plan_id"]]
        validations_res = validate_plan_and_payee(
            client_id,
            {
                "plan_id": plan["plan_id"],
                "plan_name": cp.plan_name,
                "plan_type": cp.plan_type,
                "plan_start_date": cp.plan_start_date.strftime("%d-%b-%Y"),
                "plan_end_date": cp.plan_end_date.strftime("%d-%b-%Y"),
                "added_plan_payees": [],
                "modified_plan_payees": [
                    {
                        "employee_email_id": employee_email_id,
                        "effective_start_date": plan["spiff_period"][0],
                        "effective_end_date": plan["spiff_period"][1],
                        "settlement_end_date": (
                            cp.settlement_end_date.strftime("%d-%b-%Y")
                            if cp.settlement_end_date
                            else None
                        ),
                    }
                ],
                "is_settlement_end_date": cp.is_settlement_end_date,
                "settlement_end_date": (
                    cp.settlement_end_date.strftime("%d-%b-%Y")
                    if cp.settlement_end_date
                    else None
                ),
                "removed_plan_payees": [],
            },
        )
        errors = []
        if pydash.get(validations_res, ["plan_payees", "error"]):
            errors.append(validations_res["plan_payees"]["error"])
        if pydash.get(
            validations_res, ["plan_payees", "payees", employee_email_id, "errors"]
        ):
            errors.extend(
                validations_res["plan_payees"]["payees"][employee_email_id]["errors"]
            )
        if errors:
            logger.error(
                "Identified %s validation errors for adding/updating '%s' in the SPIFF plan '%s'",
                len(errors),
                employee_email_id,
                cp.plan_name,
                extra=log_context,
            )
            res["plans"][plan["plan_id"]] = {"errors": errors}
    if len(res["plans"].keys()) > 0:
        return res
    ######################################################

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_USERS-SPIFF-PLAN"]["code"]
    event_key = request.data["employee_email_id"]
    summary = request.data["employee_email_id"]
    audit_data = copy.deepcopy(request.data)
    audit_data["spiff_plan_names"] = []
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    # invalidate entries in planpayee
    # insert in plan payee
    existing_payee_plans = {}

    try:
        plan_ids = []
        plan_details = []
        payee_plans = []
        # invalidate entries in plan details
        # insert in pland details
        for index, plan in enumerate(spiff_plans):
            cp = plans_map[plan["plan_id"]]
            plan_ids.append(plan["plan_id"])
            esd = make_aware(
                start_of_day(_date.strptime(plan["spiff_period"][0], "%d-%b-%Y"))
            )
            eed = make_aware(
                end_of_day(_date.strptime(plan["spiff_period"][1], "%d-%b-%Y"))
            )
            sed = None
            if cp.settlement_end_date:
                sed = cp.settlement_end_date
            plan_details.append(
                PlanDetails(
                    client_id=client_id,
                    knowledge_begin_date=time,
                    additional_details=audit,
                    employee_email_id=employee_email_id,
                    plan_id=plan["plan_id"],
                    plan_type="SPIFF",
                    effective_start_date=esd,
                    effective_end_date=eed,
                    settlement_end_date=sed,
                )
            )
            payee_plans.append(
                PlanPayee(
                    client_id=client_id,
                    knowledge_begin_date=time,
                    additional_details=audit,
                    employee_email_id=employee_email_id,
                    plan_id=plan["plan_id"],
                    plan_type="SPIFF",
                    effective_start_date=esd,
                    effective_end_date=eed,
                    settlement_end_date=sed,
                )
            )
            cp = plans_map[plan["plan_id"]]
            audit_data["spiff_plans"][index]["spiff_name"] = cp.plan_name
            existing_payee_plans[plan["plan_id"]] = PlanPayeeAccessor(
                client_id
            ).get_latest_payee_plan(plan_id=plan["plan_id"], email_id=employee_email_id)

        # invalidate all entries
        PlanDetailsSpiffAccessor(client_id).invalidate_all_spiff_plans_in_payee(
            employee_email_id, time
        )
        PlanPayeeAccessor(client_id).invalidate_all_spiff_plans_in_payee(
            employee_email_id, time
        )
        # insert new entries if any
        if spiff_plans and len(spiff_plans) > 0:
            PlanDetailsSpiffAccessor(client_id).insert_spiff_plans(plan_details)
            PlanPayeeAccessor(client_id).insert_spiff_plans(payee_plans)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

        for plan in payee_plans:
            if not existing_payee_plans[plan.plan_id]:
                logger.info(
                    "Notifying the new payee plan %s for payee %s on slack",
                    plan.plan_id,
                    employee_email_id,
                    extra=log_context,
                )
                notify_plan_change(
                    client_id,
                    employee_email_id,
                    plan.plan_id,
                    plan.effective_start_date,
                    plan.effective_end_date,
                )
            else:
                existing_payee_plan = existing_payee_plans[plan.plan_id]
                if plan.effective_start_date.strftime(
                    "%Y-%m-%d"
                ) != existing_payee_plan.effective_start_date.strftime(
                    "%Y-%m-%d"
                ) or plan.effective_end_date.strftime(
                    "%Y-%m-%d"
                ) != existing_payee_plan.effective_end_date.strftime(
                    "%Y-%m-%d"
                ):
                    logger.info(
                        "Notifying the updated payee plan %s for payee %s on slack",
                        plan.plan_id,
                        employee_email_id,
                        extra=log_context,
                    )
                    notify_plan_change(
                        client_id,
                        employee_email_id,
                        plan.plan_id,
                        plan.effective_start_date,
                        plan.effective_end_date,
                    )

        subscription_plan = get_client_subscription_plan(client_id)
        misc_queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        invalidate_and_cache_all_user_groups.si(client_id).set(
            queue=misc_queue_name
        ).apply_async()

    except Exception as e:
        raise e


def save_freshchat_id(request):
    time = timezone.now()
    client_id = request.client_id
    data = request.data
    data["additional_details"] = request.audit
    email = data["email"]
    fc_id = data["freshchat_id"]
    if fc_id:
        try:
            EmployeeWriteAccessor(client_id).save_freshchat_id(email, fc_id, time)
        except Exception as e:
            raise SQLParseError() from e
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    return Response("Error saving Freshchat id", status=status.HTTP_400_BAD_REQUEST)


def save_slack_details(
    client_id, email_id, slack_user_id, slack_team_id, knowledge_date
):
    try:
        employee_accessor = EmployeeAccessor(client_id)
        employee = employee_accessor.get_employee(email_id)
        emp_config = (
            employee.employee_config if employee and employee.employee_config else {}
        )
        emp_config.update(
            {"slack_user_id": slack_user_id, "slack_team_id": slack_team_id}
        )
        employee = clone_object(employee, knowledge_date, employee.additional_details)
        employee.employee_config = emp_config
        EmployeeWriteAccessor(client_id).invalidate_and_create_objects(
            new_records=employee,  # type: ignore
            knowledge_date=knowledge_date,
            invalidate_employee_email=email_id,
        )
    except ObjectDoesNotExist as e:
        traceback.print_exc(e)
        raise e


def bulk_add_old(
    client_id,
    employee_serializer,
    payroll_serializer,
    hierarchy_serializer,
    request,
    audit,
):
    req_logger = request.logger
    try:
        time = timezone.now()
        response = {}
        validate_response = validate_bulk_add_old(
            client_id,
            employee_serializer,
            payroll_serializer,
            hierarchy_serializer,
            request,
            audit,
        )
        validate_resp_data = validate_response.data
        validate_new_users = validate_resp_data["nusers"]
        req_logger.info("BEGIN: BULK ADD FOR {} USERS".format(len(request.data)))
        if validate_new_users > 50:
            task_arg_tuples = []
            for record in request.data:
                req_email = record["employee_email_id"]
                vres = validate_resp_data[req_email]
                task_arg_tuples.append((client_id, audit, vres, record, time))
            bulk_add_chain = chain(
                process_bulk_add_task.starmap(task_arg_tuples),
                invalidate_and_cache_all_user_groups.si(client_id),
            )
            bulk_add_chain.apply_async()

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.USER_UPLOAD.value,
                "event_properties": {
                    SegmentProperties.NUM_OF_USERS_IMPORTED.value: len(request.data),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            logger.info("BULK ADD FOR %s USERS SENT TO CELERY TASK", len(request.data))

            return Response(
                {"nusers": validate_new_users}, status=status.HTTP_202_ACCEPTED
            )
        else:
            failed_users = 0
            for req in request.data:
                req_email = req["employee_email_id"]
                if (
                    validate_resp_data[req_email]["status"] == "Validation Passed"
                    or validate_resp_data[req_email]["status"] == "Validation Warning"
                ):
                    e_ser = construct_employee_dict(
                        client_id, audit, employee_serializer, req, time
                    )
                    p_ser = construct_employee_payroll_dict(
                        client_id, audit, payroll_serializer, req, time
                    )
                    h_ser = construct_hierarchy_dict(
                        client_id, audit, hierarchy_serializer, req, time
                    )
                    if (
                        (not e_ser or e_ser.is_valid())
                        and (not p_ser or p_ser.is_valid())
                        and (not h_ser or h_ser.is_valid())
                    ):
                        if e_ser and not is_email_exists(client_id, req_email):
                            create_user_in_auth(client_id, e_ser.validated_data)
                            EmployeeWriteAccessor(client_id).persist_employee(e_ser)

                            ###################### audit log #####################
                            event_type_code = EVENT["CREATE_USER"]["code"]
                            event_key = req_email
                            summary = req["first_name"] + req["last_name"]
                            audit_data = copy.deepcopy(req)
                            updated_by = audit["updated_by"]
                            updated_at = time
                            ######################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )

                        if p_ser:
                            effective_end_date = make_aware_wrapper(
                                end_of_day(p_ser.validated_data["effective_start_date"])
                            ) - timedelta(days=1)
                            EmployeePayrollAccessor(
                                client_id
                            ).add_or_update_employee_payroll(
                                p_ser, time, effective_end_date
                            )
                            employee_details = (
                                EmployeeAccessor(client_id)
                                .get_employee(req_email)
                                .__dict__
                            )
                            ###################### audit log #####################
                            event_type_code = EVENT["UPDATE_USER"]["code"]
                            event_key = req_email
                            audit_data = {
                                **copy.deepcopy(req["employee_payroll"]),
                                **employee_details,
                            }
                            summary = audit_data["first_name"] + audit_data["last_name"]
                            updated_by = request.audit["updated_by"]
                            updated_at = time
                            #####################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )

                        if h_ser:
                            effective_end_date = make_aware_wrapper(
                                end_of_day(h_ser.validated_data["effective_start_date"])
                            ) - timedelta(days=1)
                            HierarchyAccessor(
                                client_id
                            ).add_or_update_employee_hierarchy(
                                h_ser, time, effective_end_date
                            )
                            employee_details = (
                                EmployeeAccessor(client_id)
                                .get_employee(req_email)
                                .__dict__
                            )
                            ###################### audit log #####################
                            event_type_code = EVENT["UPDATE_MANAGER"]["code"]
                            event_key = req_email
                            audit_data = {
                                **employee_details,
                                "reporting_manager_email_id": req["employee_hierarchy"][
                                    "reporting_manager_email_id"
                                ],
                                "manager_effective_start_date": req[
                                    "employee_hierarchy"
                                ]["effective_start_date"],
                            }
                            summary = audit_data["first_name"] + audit_data["last_name"]
                            updated_by = request.audit["updated_by"]
                            updated_at = time
                            ######################################################
                            audit_services.log(
                                client_id,
                                event_type_code,
                                event_key,
                                summary,
                                updated_by,
                                updated_at,
                                audit_data,
                            )

                        response[req_email] = {
                            "status": "Save Successful",
                            "errors": None,
                            "warnings": None,
                        }
                    else:
                        failed_users += 1
                        logger.error(
                            "Error occured while adding user with email id %s",
                            req_email,
                        )
                        response[req_email] = {
                            "status": "ERROR",
                            "errors": [
                                e_ser.errors if e_ser else None,
                                p_ser.errors if p_ser else None,
                                h_ser.errors if h_ser else None,
                            ],
                            "warnings": [],
                        }
                else:
                    response[req_email] = validate_resp_data[req_email]

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.USER_UPLOAD.value,
                "event_properties": {
                    SegmentProperties.NUM_OF_USERS_IMPORTED.value: validate_new_users,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            if failed_users > 0:
                logger.info(
                    "END: BULK ADD FOR %s USERS FAILED WHILE ADDING %s USERS",
                    failed_users,
                    validate_new_users,
                )
            else:
                logger.info(
                    "END: BULK ADD FOR %s USERS MADE SUCCESSFULLY", validate_new_users
                )
            delete_hierarchy_cache(client_id)
            subscription_plan = get_client_subscription_plan(client_id)
            misc_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.MISC.value
            )
            invalidate_and_cache_all_user_groups.si(client_id).set(
                queue=misc_queue_name
            ).apply_async()
        response["nusers"] = validate_new_users
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        req_logger.error(
            "Exception raised while bulk adding user",
            error_dict,
        )
        return Response(
            f"Error occured in the server {e}",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    return Response(response, status=status.HTTP_201_CREATED)


def validate_bulk_add_old(
    client_id,
    employee_serializer,
    payroll_serializer,
    hierarchy_serializer,
    request,
    audit,
):
    time = timezone.now()
    response = {}
    new_users = 0
    seen_emails = {}
    logged_user = request.user.username
    for req in request.data:
        req_email = req["employee_email_id"]
        e_ser = construct_employee_dict(
            client_id, audit, employee_serializer, req, time
        )
        p_ser = construct_employee_payroll_dict(
            client_id, audit, payroll_serializer, req, time
        )
        h_ser = construct_hierarchy_dict(
            client_id, audit, hierarchy_serializer, req, time
        )
        if (
            (not e_ser or e_ser.is_valid())
            and (not p_ser or p_ser.is_valid())
            and (not h_ser or h_ser.is_valid())
        ):
            errors = []
            warnings = []
            if req_email in seen_emails:
                errors.append("Duplicate record found for Email Id in the table")
            else:
                seen_emails[req_email] = 1

            # check domain of users in bulk add
            # if req_email.split("@")[1] != current_user_domain:
            #    errors.append(
            #        "You can only add/update payees who belong to your domain"
            #    )

            # manager email validation
            if h_ser:
                if not is_email_exists(
                    client_id,
                    h_ser.validated_data["reporting_manager_email_id"],
                ):
                    errors.append("Manager email id not present")
                if not (
                    req_email != h_ser.validated_data["reporting_manager_email_id"]
                ):
                    errors.append("Payee and Manager cannot be the same person")
            if not is_email_exists(client_id, req_email):
                if not e_ser:
                    errors.append("Email not present")
                else:
                    new_users = new_users + 1
                    if e_ser:
                        user_role = e_ser.validated_data["user_role"]

                        if not check_user_has_permission(logged_user, client_id):
                            errors.append(
                                "You do not have permission to update "
                                + user_role
                                + " user role"
                            )
                    if p_ser:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            >= p_ser.validated_data["joining_date"]
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than or equal to joining date"
                            )
                    if h_ser:
                        if p_ser:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= p_ser.validated_data["joining_date"]
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        p_ser.validated_data["joining_date"].strftime(
                                            "%d-%b-%Y"
                                        )
                                    )
                                )
                        else:
                            errors.append(
                                "Need employee joining date to set the hierarchy effective start date"
                            )
            else:
                if e_ser:
                    if p_ser or h_ser:
                        warnings.append("Email exists")
                    else:
                        errors.append("Email exists")
                # user role validation
                user_role = EmployeeAccessor(client_id).get_employee_role(req_email)
                if not check_user_has_permission(logged_user, client_id):
                    errors.append(
                        "You do not have permission to update "
                        + ", ".join(user_role)
                        + " user role"
                    )
                if p_ser:
                    latest_record = EmployeePayrollAccessor(
                        client_id
                    ).get_latest_employee_payroll(req_email)
                    if latest_record:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            > latest_record.effective_start_date
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than "
                                + str(
                                    latest_record.effective_start_date.strftime(
                                        "%d-%b-%Y"
                                    )
                                )
                            )
                    else:
                        if not (
                            p_ser.validated_data["effective_start_date"]
                            >= p_ser.validated_data["joining_date"]
                        ):
                            errors.append(
                                "Payroll effective start date should be greater than or equal to joining date"
                            )
                if h_ser:
                    hier_record = HierarchyAccessor(
                        client_id
                    ).get_latest_employee_hierarchy(req_email)
                    if hier_record:
                        if (
                            not h_ser.validated_data["effective_start_date"]
                            > hier_record.effective_start_date
                        ):
                            errors.append(
                                "Hierarchy effective start date should be greater than "
                                + str(
                                    hier_record.effective_start_date.strftime(
                                        "%d-%b-%Y"
                                    )
                                )
                            )
                    else:
                        payroll_record = EmployeePayrollAccessor(
                            client_id
                        ).get_latest_employee_payroll(req_email)
                        if payroll_record:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= payroll_record.joining_date
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        payroll_record.joining_date.strftime("%d-%b-%Y")
                                    )
                                )
                        elif p_ser:
                            if not (
                                h_ser.validated_data["effective_start_date"]
                                >= p_ser.validated_data["joining_date"]
                            ):
                                errors.append(
                                    "Hierarchy effective start date should be greater than or equal to joining date "
                                    + str(
                                        p_ser.validated_data["joining_date"].strftime(
                                            "%d-%b-%Y"
                                        )
                                    )
                                )
                        else:
                            errors.append(
                                "Need employee joining date to set the hierarchy effective start date"
                            )
            validation_status = "Validation Passed"
            if errors:
                validation_status = "Validation Failed"
            elif warnings:
                validation_status = "Validation Warning"
            response[req_email] = {
                "status": validation_status,
                "errors": errors,
                "warnings": warnings,
            }
        else:
            response[req_email] = {
                "status": "ERROR",
                "errors": [
                    e_ser.errors if e_ser else None,
                    p_ser.errors if p_ser else None,
                    h_ser.errors if h_ser else None,
                ],
                "warnings": [],
            }
    response["nusers"] = new_users
    return Response(response, status=status.HTTP_201_CREATED)


def get_filtered_users_raw(
    client_id,
    filters,
    search_term=None,
    offset=None,
    limit=None,
    data_for_export=False,
    only_count=False,
    effective_date=None,
    payee_emails=None,
    orderby_fields: list[SortInfoType] | None = None,
):
    """Returns users along with all their details (payroll, hierarchy, custom field data etc). Supports search,
    multiple filters & pagination.

    This function generates a raw sql (with multiple joins) (with filters if needed) to fetch all users along
    with details.

    If not paginated, then there will be one query which will have all the applied filters
    Ex:
        SELECT ...
        FROM Employee
        LEFT JOIN ...
        WHERE {ALL_THE_FILTERS}
        ORDER BY full_name

    If paginated, then there will be a base-query and a sub-query. The sub-query will have all the applied
    filters along with limit & offset.
    NOTE: (Here, we can't directly apply limit, offset on the base-query as there could be multiple rows with
    the same email since we are joining with plan_details table)
    Ex:
        SELECT ...
        FROM Employee
        LEFT JOIN ...
        WHERE
            email IN (
                SELECT DISTINCT(email), full_name
                FROM (
                    SELECT DISTINCT(email), full_name
                    FROM Employee
                    LEFT JOIN ...
                    WHERE {ALL_THE_FILTERS}
                    ORDER BY full_name
                    LIMIT X OFFSET X
                )
            )
    """
    log_context = {
        "client_id": client_id,
        "filters": filters,
        "search_term": search_term,
        "offset": offset,
        "limit": limit,
        "data_for_export": data_for_export,
        "only_count": only_count,
        "effective_date": effective_date,
    }
    logger.info("(get_filtered_users_raw) : BEGIN", extra=log_context)
    effective_date = timezone.now() if effective_date is None else effective_date
    custom_fields_data = get_custom_fields_details(client_id)
    # pylint: disable=import-outside-toplevel; circular import
    from spm.services.custom_calendar_services import get_payout_cycles_label_map

    payout_cycle_map = get_payout_cycles_label_map(client_id)
    logger.info(
        "(get_filtered_users_raw) : Fetched active custom fields", extra=log_context
    )
    base_query = (
        EmployeeQueryBuilder(client_id)
        .kd_aware()
        .left_join_employee_payroll_details(effective_date=effective_date)
        .join_hierarchy(effective_date=effective_date)
        .join_plan_details(effective_date=effective_date)
        .join_custom_field_data(effective_date=effective_date)
    )
    logger.info(
        "(get_filtered_users_raw) : Built base query : %s",
        base_query.clone_deep().select_field("*").get_sql(),
        extra=log_context,
    )
    query_with_filters = base_query.clone_deep()

    def apply_filters(qb):
        query_filters = {
            "search_term": search_term,
            **pydash.pick(
                filters,
                [
                    "status",
                    "mapping_status",
                    "role",
                    "user_source",
                    "exit_date",
                    "payout_frequency",
                    "employment_country",
                    "pay_currency",
                    "joining_date",
                    "reporting_manager",
                    "commission_plan",
                    "designation",
                    "employee_id",
                ],
            ),
            "custom_field_data": filters.get("custom_field") if filters else None,
            "custom_field_types_map": custom_fields_data["types_map"],
            "effective_date": effective_date,
            "email_id_in": payee_emails,
        }
        qb = qb.apply_filters(**query_filters)

    logger.info(
        "(get_filtered_users_raw) : Applying filters to base query", extra=log_context
    )
    apply_filters(query_with_filters)
    logger.info(
        "(get_filtered_users_raw) : Built base query with filters : %s",
        query_with_filters.clone_deep().select_field("*").get_sql(),
        extra=log_context,
    )
    is_paginated = (
        not data_for_export
        and limit is not None
        and offset is not None
        # Do not apply limit, offset when sort is enabled. Pagination will happen after
        # sorting of columns.
        and not orderby_fields
    )
    final_query = (
        (base_query if is_paginated else query_with_filters.clone_deep())
        .join_reporting_manager_details()
        .join_additional_plan_info_from_plan_details()
        .select_all_employee()
        .select_all_payroll_details()
        .select_all_hierarchy()
        .select_all_plan_details()
        .select_all_custom_field_data()
        .select_employee_full_name()
        .order_by_employee_full_name_base_temporal_id()
    )
    if is_paginated:
        offset = offset * limit
        final_query = final_query.where_employee_email_id_in(
            (
                BaseQueryBuilder(client_id, None)
                .from_(
                    query_with_filters.clone_deep()
                    .select_distinct_employee_email_id()
                    .select_employee_full_name()
                    .limit_and_offset(limit, offset)
                    .order_by_employee_full_name()
                    .query
                )
                .select_field("employee_email_id")
                .query
            )
        )
    get_employees_sql = final_query.get_sql()
    logger.info(
        "(get_filtered_users_raw) : Built final 'get employeees' query : %s",
        get_employees_sql,
        extra=log_context,
    )
    count_employees_sql = (
        query_with_filters.clone_deep().count_distinct_employee_email_id().get_sql()
    )
    logger.info(
        "(get_filtered_users_raw) : Built final 'count employees' query : %s",
        count_employees_sql,
        extra=log_context,
    )
    columns = []
    employees = []
    emp_count = 0
    with connection.cursor() as cursor:
        logger.info(
            "(get_filtered_users_raw) : 'count employeees' raw query db call : BEGIN",
            extra=log_context,
        )
        cursor.execute(count_employees_sql)
        count_res = cursor.fetchone()
        logger.info(
            "(get_filtered_users_raw) : 'count employeees' raw query db call : END",
            extra=log_context,
        )
        emp_count = count_res[0]
        if only_count:
            logger.info("(get_filtered_users_raw) : END", extra=log_context)
            return emp_count
        logger.info(
            "(get_filtered_users_raw) : 'get employeees' raw query db call : BEGIN",
            extra=log_context,
        )
        cursor.execute(get_employees_sql)
        columns = [col[0] for col in cursor.description]
        employees = cursor.fetchall()
        logger.info(
            "(get_filtered_users_raw) : 'get employeees' raw query db call : END",
            extra=log_context,
        )
    columns_index = dict(zip(columns, range(len(columns))))
    grouped_employees = pydash.group_by(
        employees, lambda row: row[columns_index["employee_email_id"]]
    )
    res = []

    all_power_admins = RolePermissionsAccessor(client_id).get_all_power_admins_role_id()
    all_power_admins = {str(x) for x in all_power_admins}

    role_permissions = RolePermissionsAccessor(client_id).get_all_roles()
    role_permissions_map = {}
    for role_permission in role_permissions:
        role_permissions_map[str(role_permission["role_permission_id"])] = (
            role_permission["display_name"]
        )

    if not data_for_export:
        res = get_users_data(
            grouped_employees=grouped_employees,
            columns_index=columns_index,
            effective_date=effective_date,
            all_power_admins=all_power_admins,
            custom_fields_data=custom_fields_data,
            payout_cycle_map=payout_cycle_map,
            role_permissions_map=role_permissions_map,
        )

    def get_plan_names_for_export(emp, plan_type):
        names = (
            pydash.chain(emp)
            .filter_(lambda item: item[columns_index["plan_type"]] == plan_type)
            .map_(lambda item: item[columns_index["plan_name"]])
            .sort()
            .value()
        )
        return ", ".join(names)

    def construct_export_response():
        terms_to_localize = {
            "primary_comm_plan": UserExportTerms.PRIMARY_COMM_PLAN.value,
            "payout_frequency": UserExportTerms.PAYOUT_FREQUENCY.value,
            "payout_currency": UserExportTerms.PAYOUT_CURRENCY.value,
        }
        localized_terms = get_localized_words_service(terms_to_localize, client_id)
        for email, employee_data in grouped_employees.items():
            current_user_roles_set = set(
                literal_eval(employee_data[0][columns_index["user_role"]])
            )
            if not set(all_power_admins).isdisjoint(current_user_roles_set):
                # If any user_role is power admin, then don't show him in the list
                continue

            item = {
                "Name": employee_data[0][columns_index["full_name"]],
                "Email": email,
                "Designation": employee_data[0][columns_index["designation"]],
                localized_terms["primary_comm_plan"]: get_plan_names_for_export(
                    employee_data, "MAIN"
                ),
                "SPIFF Plans": get_plan_names_for_export(employee_data, "SPIFF"),
                "Base Pay": employee_data[0][columns_index["fixed_pay"]],
                "Variable Pay": employee_data[0][columns_index["variable_pay"]],
                localized_terms["payout_frequency"]: payout_cycle_map.get(
                    str(employee_data[0][columns_index["payout_frequency"]]), None
                ),
                localized_terms["payout_currency"]: employee_data[0][
                    columns_index["pay_currency"]
                ],
                "Employee ID": employee_data[0][columns_index["employee_id"]],
                "Employment Country": employee_data[0][
                    columns_index["employment_country"]
                ],
                "Reporting Manager": employee_data[0][
                    columns_index["reporting_manager_email_id"]
                ],
                "Reporting Manager Name": (
                    employee_data[0][columns_index["reporting_manager_full_name"]]
                    if employee_data[0][columns_index["reporting_manager_email_id"]]
                    else None
                ),
                "Joining Date": employee_data[0][columns_index["joining_date"]],
                "Exit Date": employee_data[0][columns_index["exit_date"]],
                "Status": get_user_status(
                    employee_data[0][columns_index["status"]],
                    employee_data[0][columns_index["exit_date"]],
                    employee_data[0][columns_index["deactivation_date"]],
                    effective_date=effective_date,
                ),
                "Role": ", ".join(
                    [
                        role_permissions_map.get(role_id, "-")
                        for role_id in literal_eval(
                            employee_data[0][columns_index["user_role"]]
                        )
                    ]
                ),
                **resolve_custom_field_data_for_user_export(
                    employee_data[0][columns_index["custom_field_data"]],
                    display_name_default_values_map=custom_fields_data[
                        "display_name_default_values_map"
                    ],
                    system_name_display_name_map=custom_fields_data[
                        "system_name_display_name_map"
                    ],
                    types_map=custom_fields_data["types_map"],
                    dropdown_options_map=custom_fields_data["dropdown_options_map"],
                ),
                "Created Date": employee_data[0][columns_index["created_date"]],
            }
            res.append(item)  # type: ignore

    logger.info("(get_filtered_users_raw) : Constucting response", extra=log_context)

    if data_for_export:
        construct_export_response()

    if orderby_fields and res:
        res = sort_users_data(
            res, orderby_fields, custom_fields_data["types_map"], limit, offset
        )

    logger.info("(get_filtered_users_raw) : END", extra=log_context)
    return {"data": res, "count": emp_count}


def get_users_data(
    grouped_employees: dict,
    columns_index: dict,
    effective_date: datetime.datetime | None,
    all_power_admins: set[str],
    custom_fields_data: dict[str, dict],
    payout_cycle_map: dict[str, str],
    role_permissions_map: dict[str, str],
) -> list[dict]:
    res = []
    for email, employee_data in grouped_employees.items():
        current_user_roles_set = set(
            literal_eval(employee_data[0][columns_index["user_role"]])
        )

        payroll_details = {
            "joining_date": None,
            "employee_id": None,
            "designation": None,
            "fixed_pay": None,
            "variable_pay": None,
            "payout_frequency": None,
            "employment_country": None,
            "pay_currency": None,
        }
        if employee_data[0][columns_index["joining_date"]]:
            payroll_details = {
                "joining_date": employee_data[0][columns_index["joining_date"]],
                "employee_id": employee_data[0][columns_index["employee_id"]],
                "designation": employee_data[0][columns_index["designation"]],
                "fixed_pay": employee_data[0][columns_index["fixed_pay"]],
                "variable_pay": employee_data[0][columns_index["variable_pay"]],
                "payout_frequency": payout_cycle_map.get(
                    str(employee_data[0][columns_index["payout_frequency"]]), None
                ),
                "employment_country": employee_data[0][
                    columns_index["employment_country"]
                ],
                "pay_currency": employee_data[0][columns_index["pay_currency"]],
            }

        hierarchy_details = {
            "reporting_manager_email_id": None,
            "reporting_manager_full_name": None,
        }
        if employee_data[0][columns_index["reporting_manager_email_id"]]:
            hierarchy_details = {
                "reporting_manager_email_id": employee_data[0][
                    columns_index["reporting_manager_email_id"]
                ],
                "reporting_manager_full_name": employee_data[0][
                    columns_index["reporting_manager_full_name"]
                ],
            }

        all_user_roles = ", ".join(
            [
                role_permissions_map.get(role_id, "-")
                for role_id in literal_eval(
                    employee_data[0][columns_index["user_role"]]
                )
            ]
        )

        custom_fields_data_dict = resolve_custom_field_data_for_user_filters(
            employee_data[0][columns_index["custom_field_data"]],
            system_name_default_values_map=custom_fields_data[
                "system_name_default_values_map"
            ],
            types_map=custom_fields_data["types_map"],
            dropdown_options_map=custom_fields_data["dropdown_options_map"],
            as_dicts=True,
        )

        item = {
            "client_id": employee_data[0][columns_index["client_id"]],
            "employee_email_id": email,
            "can_user_manage_admins": not set(all_power_admins).isdisjoint(
                current_user_roles_set
            ),
            "full_name": employee_data[0][columns_index["full_name"]],
            "first_name": employee_data[0][columns_index["first_name"]],
            "last_name": employee_data[0][columns_index["last_name"]],
            "user_role": employee_data[0][columns_index["user_role"]],
            "user_source": employee_data[0][columns_index["user_source"]],
            "profile_picture": employee_data[0][columns_index["profile_picture"]],
            "created_by": employee_data[0][columns_index["created_by"]],
            "created_date": employee_data[0][columns_index["created_date"]],
            "exit_date": employee_data[0][columns_index["exit_date"]],
            "last_commission_date": employee_data[0][
                columns_index["last_commission_date"]
            ],
            "deactivation_date": employee_data[0][columns_index["deactivation_date"]],
            "status": get_user_status(
                employee_data[0][columns_index["status"]],
                employee_data[0][columns_index["exit_date"]],
                employee_data[0][columns_index["deactivation_date"]],
                effective_date=effective_date,
            ),
            "employee_plan_details": [],
            "employee_spiff_plan_details": [],
            "user_role_name": all_user_roles,
            **payroll_details,
            **hierarchy_details,
            **custom_fields_data_dict,
        }

        for each_data in employee_data:
            plan_details = (
                {
                    "plan_id": each_data[columns_index["plan_id"]],
                    "plan_name": each_data[columns_index["plan_name"]],
                    "plan_type": each_data[columns_index["plan_type"]],
                }
                if each_data[columns_index["plan_id"]]
                else None
            )
            if each_data[columns_index["plan_type"]] == "MAIN":
                item["employee_plan_details"].append(plan_details)
            if each_data[columns_index["plan_type"]] == "SPIFF":
                item["employee_spiff_plan_details"].append(plan_details)

        if not item["employee_plan_details"]:
            item["employee_plan_details"] = None
        else:
            item["employee_plan_details"] = pydash.sort_by(
                item["employee_plan_details"], "plan_name"
            )

        if not item["employee_spiff_plan_details"]:
            item["employee_spiff_plan_details"] = None
        else:
            item["employee_spiff_plan_details"] = pydash.sort_by(
                item["employee_spiff_plan_details"], "plan_name"
            )

        res.append(item)

    return res


def sort_users_data(
    records: list[dict],
    orderby_fields: list[SortInfoType],
    custom_fields_type_map: dict[str, str],
    limit: int | None = None,
    offset: int | None = None,
):
    ignore_cols = [
        "client_id",
        "employee_email_id",
        "can_user_manage_admins",
        "user_role",
        "profile_picture",
        "created_by",
        "deactivation_date",
    ]

    # All column names from frontend are in camelcase, converting them to snakecase.
    for col in orderby_fields:
        col["column"] = pydash.snake_case(col["column"])

    orderby_fields = remove_ignore_columns(orderby_fields, ignore_cols)
    col_ids = [col["column"] for col in orderby_fields]

    custom_date_fields = []
    for col_id, col_type in custom_fields_type_map.items():
        if col_type == "Date" and col_id in col_ids:
            custom_date_fields.append(col_id)

    # Pre-process the records:
    # Change format of custom date fields to make it sortable.
    # Format stored in DB: %Y-%m-%d
    df = pd.DataFrame(records)
    for col in custom_date_fields:
        df[col] = pd.to_datetime(df[col], errors="coerce")

    # Plan details are stored as list of dictionaries. Converting them to string for sorting.
    def get_plan_names(rec):
        if not rec:
            return None
        return ", ".join([plan["plan_name"] for plan in rec])

    df = (
        SortUtility(df, orderby_fields)
        .sort_records(
            column_replacements=[
                {
                    "column": "employee_plan_details",
                    "callback": lambda df, col: df[col].apply(get_plan_names),
                },
                {
                    "column": "employee_spiff_plan_details",
                    "callback": lambda df, col: df[col].apply(get_plan_names),
                },
                {
                    "column": "fixed_pay",
                    "callback": lambda df, col: df[col].replace({0: None}),
                },
                {
                    "column": "variable_pay",
                    "callback": lambda df, col: df[col].replace({0: None}),
                },
            ]
        )
        .apply_limit_offset(
            limit, offset * limit if limit is not None and offset is not None else None
        )
        .as_dataframe()
    )

    for col in custom_date_fields:
        df[col] = pd.to_datetime(df[col], errors="coerce").dt.strftime("%d-%b-%Y")

    clean_df = replace_nan_nat_for_df(df)

    return clean_df.to_dict("records")


def get_all_filters_count(client_id, logged_email, effective_date=None):
    effective_date = timezone.now() if effective_date is None else effective_date
    filters = {
        "Unmapped": {"mapping_status": "Unmapped"},
        "Mapped": {"mapping_status": "Mapped"},
        "Added": {"status": ["Added"]},
        "Inactive": {"status": ["Inactive"]},
    }
    data_permission = get_data_permission(
        client_id, logged_email, RBACComponent.MANAGE_USERS.value
    )
    payee_emails = None
    if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
        payee_emails = get_valid_payee_emails(client_id, logged_email, data_permission)
    filter_count_map = {}
    for filter_name, filter_s in filters.items():
        filter_count_map[filter_name] = get_filtered_users_raw(
            client_id=client_id,
            filters=filter_s,
            only_count=True,
            effective_date=effective_date,
            payee_emails=payee_emails,
        )
    return filter_count_map


def get_matching_lead_employees(
    client_id, limit, first_name=None, email=None, search_term=None
):
    base_query = (
        HierarchyQueryBuilder(client_id)
        .kd_ed_aware()
        .kd_aware()
        .join_reporting_manager_details()
    )
    if first_name != None and email != None:
        base_query.skip_by_fn_email(first_name, email)
    if search_term != None:
        base_query.like_full_name(search_term)
    base_query.limit_and_offset(limit)
    base_query.select_distinct()
    base_query.order_by_employee_first_name()
    base_query.order_by_employee_email()
    with connection.cursor() as cursor:
        cursor.execute(base_query.get_sql())
        employees = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
        headers = dict(zip(columns, range(len(columns))))
        data = employees
        headers_dict = employee_services_utils.change_headers_to_camel_case(headers)
        # Sort the dictionary items by values and extract the keys
        header_keys = [
            key for key, _ in sorted(headers_dict.items(), key=lambda item: item[1])
        ]  # Solving type check in gql (being extra cautious on order)
        output = {
            "headers": header_keys,
            "data": data,
        }
        return output


def get_payees_paginated_with_search(
    client_id,
    limit=50,
    full_name=None,
    email=None,
    search_term=None,
    payroll_and_designation_details=False,
):
    log_context = {
        "client_id": client_id,
        "limit": limit,
        "full_name": full_name,
        "email": email,
        "search_term": search_term,
        "payroll_and_designation_details": payroll_and_designation_details,
    }
    logger.info("BEGIN : get_payees_paginated_with_search", extra=log_context)
    output = []
    non_hierarchy_users_flag = True
    if payroll_and_designation_details:
        non_hierarchy_users_flag = False
    query_set = HierarchyQueryBuilder(
        client_id
    ).get_reportee_by_manager_email_paginated(
        None, limit, search_term, full_name, email, non_hierarchy_users_flag
    )
    with connection.cursor() as cursor:
        cursor.execute(query_set.get_sql())
        employees = cursor.fetchall()

    if payroll_and_designation_details and employees:
        emails = list(map(lambda emp_data: emp_data[1], employees))
        result = get_payroll_plan_and_designation_details_with_manager_of_employee(
            client_id, emails
        )
        output = (
            construct_payroll_plan_and_designation_details_with_manager_of_employee(
                result
            )
        )
    else:
        for employee in employees:
            employee_dict = {"employee_email_id": employee[1], "full_name": employee[0]}
            output.append(employee_dict)

    output_log_context = {
        "output_employee_count": len(employees),
        "sql": query_set.get_sql(),
    }
    logger.info("END : get_payees_paginated_with_search", extra=output_log_context)
    return output


def construct_payroll_plan_and_designation_details_with_manager_of_employee(
    employee_payroll_designation,
):
    columns_index = employee_payroll_designation["columns_index"]
    employee_result = employee_payroll_designation["data"]
    grouped_employees = pydash.group_by(
        employee_result, lambda row: row[columns_index["employee_email_id"]]
    )
    output = []
    from spm.services.config_services.hierarchy_services import get_plan_names_str

    for email, employee_data in grouped_employees.items():
        plan_names_str = get_plan_names_str(employee_data, columns_index)
        output.append(
            {
                "employee_email_id": email,
                "full_name": employee_data[0][columns_index["full_name"]],
                "designation": employee_data[0][columns_index["designation"]],
                "profile_picture": employee_data[0][columns_index["profile_picture"]],
                "has_payroll": employee_data[0][columns_index["has_payroll"]],
                "plan_name": plan_names_str if plan_names_str else None,
                "reporting_manager_email_id": employee_data[0][
                    columns_index["reporting_manager_email_id"]
                ],
                "exit_date": employee_data[0][columns_index["exit_date"]],
            }
        )
    return output


def get_active_custom_field_for_filters(client_id):
    # Active custom fields
    active_custom_fields = CustomFieldsAccessor(
        client_id
    ).get_active_custom_fields_by_client()
    active_drop_down_custom_fields = {}
    active_date_custom_fields = {}
    for active_field in active_custom_fields:
        if active_field.field_type == "Dropdown":
            active_drop_down_custom_fields[active_field.system_name] = {
                options["system_name"]: options["display_name"]
                for options in active_field.options
            }
        elif active_field.field_type == "Date":
            active_date_custom_fields[active_field.system_name] = active_field
    default_data = {}
    for active_field in active_custom_fields:
        if active_field.field_type == "Checkbox":
            default_data[active_field.system_name] = False
        else:
            default_data[active_field.system_name] = None
    return (
        active_custom_fields,
        active_drop_down_custom_fields,
        active_date_custom_fields,
        default_data,
    )


def get_filtered_emails_list(client_id, filters, search_term):
    try:
        current_date = timezone.now()
        current_data_qs = EmployeeDetailsViewAccessor(
            client_id
        ).get_all_employees_from_views(current_date)
        cur_emp_list = list(
            current_data_qs.values("employee_email_id", "full_name").distinct()
        )
        cur_emp_email_list = [x["employee_email_id"] for x in cur_emp_list]
        future_data_qs = EmployeeDetailsViewAccessor(
            client_id
        ).get_all_employee_with_future_records(current_date, cur_emp_email_list)

        active_date_custom_fields = get_active_custom_field_for_filters(client_id)[2]

        # Apply filters
        current_data_qs = apply_filters_on_qs(
            current_data_qs,
            filters,
            search_term,
            current_date,
            active_date_custom_fields,
        )
        future_records_qs = apply_filters_on_qs(
            future_data_qs,
            filters,
            search_term,
            current_date,
            active_date_custom_fields,
        )
        all_data_qs = current_data_qs.union(future_records_qs)
        email_list = list(
            all_data_qs.values_list("employee_email_id", flat=True).distinct(
                "employee_email_id"
            )
        )
        return email_list
    except Exception as e:
        print(f"Exception: {str(e)}")
        print(traceback.print_exc())
        return None


def get_filtered_emails_list_query_builder(client_id, filters, search_term):
    try:
        log_context = {
            "client_id": client_id,
            "filters": filters,
            "search_term": search_term,
        }
        logger.info(
            "(get_filtered_emails_list_query_builder) : BEGIN", extra=log_context
        )
        effective_date = timezone.now()
        custom_fields_data = get_custom_fields_details(client_id)
        logger.info(
            "(get_filtered_emails_list_query_builder) : Fetched active custom fields",
            extra=log_context,
        )
        base_query = (
            EmployeeQueryBuilder(client_id)
            .kd_aware()
            .left_join_employee_payroll_details(effective_date=effective_date)
            .join_hierarchy(effective_date=effective_date)
            .join_plan_details(effective_date=effective_date)
            .join_custom_field_data()
        )
        query_with_filters = base_query.clone_deep()

        def apply_filters(qb):
            query_filters = {
                "search_term": search_term,
                **pydash.pick(
                    filters,
                    [
                        "status",
                        "mapping_status",
                        "role",
                        "exit_date",
                        "user_source",
                        "payout_frequency",
                        "employment_country",
                        "pay_currency",
                        "joining_date",
                        "reporting_manager",
                    ],
                ),
                "custom_field_data": filters.get("custom_field") if filters else None,
                "custom_field_types_map": custom_fields_data["types_map"],
                "effective_date": effective_date,
            }
            qb = qb.apply_filters(**query_filters)

        logger.info(
            "(get_filtered_emails_list_query_builder) : Applying filters to base query",
            extra=log_context,
        )
        apply_filters(query_with_filters)
        final_query = (
            query_with_filters.clone_deep()
            .join_reporting_manager_details()
            .join_additional_plan_info_from_plan_details()
            .select_employee_email_id()
        )
        get_employees_sql = final_query.get_sql()
        logger.info(
            "(get_filtered_emails_list_query_builder) : Built final query : %s",
            get_employees_sql,
        )
        columns = []
        employees = []
        with connection.cursor() as cursor:
            logger.info(
                "(get_filtered_emails_list_query_builder) : raw query db call : BEGIN",
                extra=log_context,
            )
            cursor.execute(get_employees_sql)
            columns = [col[0] for col in cursor.description]
            employees = cursor.fetchall()
            logger.info(
                "(get_filtered_emails_list_query_builder) : raw query db call : END",
                extra=log_context,
            )
        columns_index = dict(zip(columns, range(len(columns))))
        grouped_employees = pydash.group_by(
            employees, lambda row: row[columns_index["employee_email_id"]]
        )
        email_list = list(grouped_employees.keys())
        return email_list
    except Exception as e:
        print(f"Exception: {str(e)}")
        print(traceback.print_exc())
        return None


def get_filtered_users_from_views(
    client_id,
    filters,
    search_term=None,
    offset=0,
    limit=10,
    data_for_export=False,
    _only_emails_list=False,
    only_count=False,
):
    log_context = {
        "client_id": client_id,
        "filters": filters,
        "search_term": search_term,
        "offset": offset,
        "limit": limit,
        "data_for_export": data_for_export,
        "only_count": only_count,
    }
    logger.info("(get_filtered_users_from_views) : BEGIN", extra=log_context)
    try:
        current_date = timezone.now()
        current_data_qs = EmployeeDetailsViewAccessor(
            client_id
        ).get_all_employees_from_views(current_date)
        cur_emp_list = list(
            current_data_qs.values("employee_email_id", "full_name").distinct()
        )
        cur_emp_email_list = [x["employee_email_id"] for x in cur_emp_list]
        future_data_qs = EmployeeDetailsViewAccessor(
            client_id
        ).get_all_employee_with_future_records(current_date, cur_emp_email_list)
        fr_emp_list = list(
            future_data_qs.values("employee_email_id", "full_name").distinct()
        )
        all_emp_list = fr_emp_list + cur_emp_list
        emp_dict = (
            {x["employee_email_id"]: x for x in all_emp_list} if all_emp_list else {}
        )

        (
            active_custom_fields,
            active_drop_down_custom_fields,
            active_date_custom_fields,
            default_data,
        ) = get_active_custom_field_for_filters(client_id)

        if data_for_export and filters == None:
            all_data_qs = current_data_qs.union(future_data_qs)
        else:
            # Apply filters
            current_data_qs = apply_filters_on_qs(
                current_data_qs,
                filters,
                search_term,
                current_date,
                active_date_custom_fields,
            )

            future_records_qs = apply_filters_on_qs(
                future_data_qs,
                filters,
                search_term,
                current_date,
                active_date_custom_fields,
            )

            all_data_qs = current_data_qs.union(future_records_qs)

        # Resolve data from view
        lis = (
            [
                "client_id",
                "employee_email_id",
                "full_name",
                "first_name",
                "last_name",
                "user_role",
                "profile_picture",
                "created_by",
                "created_date",
                "exit_date",
                "status",
                "joining_date",
                "employee_id",
                "designation",
                "fixed_pay",
                "variable_pay",
                "pay_currency",
                "employment_country",
                "payout_frequency",
                "payroll_effective_start_date",
                "payroll_effective_end_date",
                "reporting_manager_email_id",
                "hierarchy_effective_start_date",
                "hierarchy_effective_end_date",
                "custom_field_data",
            ]
            if not only_count
            else ["employee_email_id"]
        )
        emp_view_data = all_data_qs.values(*lis).distinct("employee_email_id")
        emp_view_data = emp_view_data.order_by("full_name")
        logger.info(
            "(get_filtered_users_from_views) : Count employeees : BEGIN",
            extra=log_context,
        )
        emp_count = emp_view_data.count()
        logger.info(
            "(get_filtered_users_from_views) : Count employeees : END",
            extra=log_context,
        )

        if only_count:
            logger.info("(get_filtered_users_from_views) : END", extra=log_context)
            return emp_count

        logger.info(
            "(get_filtered_users_from_views) : get employeees : BEGIN",
            extra=log_context,
        )
        pg_data_list = (
            list(emp_view_data)
            if data_for_export or (limit is None or offset is None)
            else list(emp_view_data[(offset * limit) : (offset * limit) + limit])
        )
        logger.info(
            "(get_filtered_users_from_views) : get employeees : END", extra=log_context
        )

        # Get plan details for these employee email ids
        email_ids_from_view = [i["employee_email_id"] for i in list(pg_data_list)]
        main_plan_dict, spiff_plan_dict = get_plan_details_of_emp_list(
            client_id, current_date, email_ids_from_view
        )

        if data_for_export:
            final_list = construct_data_for_user_export(
                pg_data_list,
                active_custom_fields,
                main_plan_dict,
                spiff_plan_dict,
                current_date,
                emp_dict,
            )
        else:
            final_list = construct_data_for_user_filters(
                pg_data_list,
                active_custom_fields,
                active_date_custom_fields,
                active_drop_down_custom_fields,
                default_data,
                main_plan_dict,
                spiff_plan_dict,
                current_date,
                emp_dict,
            )

        logger.info("(get_filtered_users_from_views) : END", extra=log_context)
        return {"data": final_list, "count": emp_count}
    except Exception as e:
        print(f"Exception: {str(e)}")
        print(traceback.print_exc())


def construct_data_for_user_filters(
    pg_data_list,
    active_custom_fields,
    active_date_custom_fields,
    active_drop_down_custom_fields,
    default_data,
    main_plan_dict,
    spiff_plan_dict,
    current_date,
    emp_dict,
):
    # construct final data
    final_list = []
    for emp in pg_data_list:
        cus_data = get_custom_data_for_emp(
            emp["custom_field_data"],
            active_custom_fields,
            active_date_custom_fields,
            active_drop_down_custom_fields,
            default_data,
        )
        final_status = get_user_status(
            emp_status=emp["status"],
            exit_date=emp["exit_date"],
            deactivation_date=emp["deactivation_date"],
        )
        final_list.append(
            {
                "client_id": emp["client_id"],
                "employee_email_id": emp["employee_email_id"],
                "full_name": emp["full_name"],
                "first_name": emp["first_name"],
                "last_name": emp["last_name"],
                "user_role": emp["user_role"],
                "profile_picture": emp["profile_picture"],
                "created_by": emp["created_by"],
                "created_date": emp["created_date"],
                "exit_date": emp["exit_date"],
                "status": final_status,
                "employee_payroll": (
                    [
                        {
                            "joining_date": emp["joining_date"],
                            "employee_email_id": emp["employee_email_id"],
                            "employee_id": emp["employee_id"],
                            "designation": emp["designation"],
                            "fixed_pay": emp["fixed_pay"],
                            "variable_pay": emp["variable_pay"],
                            "payout_frequency": emp["payout_frequency"],
                            "employment_country": emp["employment_country"],
                            "pay_currency": emp["pay_currency"],
                        }
                    ]
                    if emp["payroll_effective_start_date"]
                    and emp["payroll_effective_start_date"] <= current_date
                    and (
                        emp["payroll_effective_end_date"] is None
                        or emp["payroll_effective_end_date"] >= current_date
                    )
                    else None
                ),
                "employee_hierarchy": (
                    [
                        {
                            "employee_email_id": emp["employee_email_id"],
                            "reporting_manager_email_id": emp[
                                "reporting_manager_email_id"
                            ],
                            "manager_details": {
                                "full_name": (
                                    emp_dict[emp["reporting_manager_email_id"]][
                                        "full_name"
                                    ]
                                    if emp["reporting_manager_email_id"] in emp_dict
                                    else None
                                ),
                            },
                        }
                    ]
                    if emp["hierarchy_effective_start_date"]
                    and emp["hierarchy_effective_start_date"] <= current_date
                    and (
                        emp["hierarchy_effective_end_date"] is None
                        or emp["hierarchy_effective_end_date"] >= current_date
                    )
                    else None
                ),
                "employee_plan_details": (
                    main_plan_dict[emp["employee_email_id"]]
                    if emp["employee_email_id"] in main_plan_dict
                    else None
                ),
                "employee_spiff_plan_details": (
                    spiff_plan_dict[emp["employee_email_id"]]
                    if emp["employee_email_id"] in spiff_plan_dict
                    else None
                ),
                "employee_custom_field_data": {
                    "custom_field_data": json.dumps(cus_data, default=str)
                },
            }
        )
    return final_list


def apply_filters_on_qs(
    view_qs, filters, search_term, current_date, active_date_custom_fields
):
    local_status = []
    exit_status = []
    if search_term:
        view_qs = view_qs.filter(
            Q(full_name__icontains=search_term)
            | Q(employee_email_id__istartswith=search_term)
        )

    if filters and filters.get("status") and len(filters.get("status")) > 0:
        for stat in filters["status"]:
            if stat in ["Marked for exit", "Inactive"]:
                exit_status.append(stat)
            else:
                local_status.append(stat)

    if len(local_status) == 0 and len(exit_status) > 0:
        if len(exit_status) == 2:
            view_qs = view_qs.filter(
                Q(exit_date__gt=current_date) | Q(exit_date__lt=current_date)
            )
        elif "Marked for exit" in exit_status:
            view_qs = view_qs.filter(exit_date__gt=current_date)
        elif "Inactive" in exit_status:
            view_qs = view_qs.filter(exit_date__lt=current_date)
    elif len(local_status) > 0 and len(exit_status) == 0:
        view_qs = view_qs.filter(Q(status__in=local_status) & Q(exit_date__isnull=True))
    elif len(local_status) > 0 and len(exit_status) > 0:
        if len(exit_status) == 2:
            view_qs = view_qs.filter(
                Q(status__in=status)  # pylint: disable=unsupported-binary-operation
                | Q(exit_date__gt=current_date)
                | Q(exit_date__lt=current_date)
            )
        elif "Marked for exit" in exit_status:
            view_qs = view_qs.filter(
                Q(status__in=status) | Q(exit_date__gt=current_date)
            )
        elif "Inactive" in exit_status:
            view_qs = view_qs.filter(
                Q(status__in=status) | Q(exit_date__lt=current_date)
            )

    if (
        filters
        and filters.get("mapping_status")
        and filters.get("mapping_status") == "Mapped"
    ):
        view_qs = (
            view_qs.filter(
                payout_frequency__isnull=False, reporting_manager_email_id__isnull=False
            )
            .filter(plan_effective_start_date__lte=current_date)
            .filter(
                Q(plan_effective_end_date__gte=current_date)
                | Q(plan_effective_end_date__isnull=True)
            )
        )
    elif (
        filters
        and filters.get("mapping_status")
        and filters.get("mapping_status") == "Unmapped"
    ):
        mapped_view_emps = (
            view_qs.filter(
                payout_frequency__isnull=False, reporting_manager_email_id__isnull=False
            )
            .filter(plan_effective_start_date__lte=current_date)
            .filter(
                Q(plan_effective_end_date__gte=current_date)
                | Q(plan_effective_end_date__isnull=True)
            )
        ).values("employee_email_id")

        view_qs = view_qs.filter(~Q(employee_email_id__in=mapped_view_emps))

    if filters and filters.get("role") and len(filters.get("role")) > 0:
        view_qs = view_qs.filter(user_role__in=filters.get("role"))
    if filters and filters.get("exit_date"):
        if filters["exit_date"]["type"] == "before":
            view_qs = view_qs.filter(exit_date__lt=filters["exit_date"]["date1"])
        if filters["exit_date"]["type"] == "after":
            view_qs = view_qs.filter(exit_date__gt=filters["exit_date"]["date1"])
        if filters["exit_date"]["type"] == "between":
            view_qs = view_qs.filter(
                exit_date__gte=filters["exit_date"]["date1"],
                exit_date__lte=filters["exit_date"]["date2"],
            )
    # Payroll filters
    if (
        filters
        and filters.get("payout_frequency")
        and len(filters.get("payout_frequency")) > 0
    ):
        view_qs = view_qs.filter(payout_frequency__in=filters.get("payout_frequency"))

    if (
        filters
        and filters.get("employment_country")
        and len(filters.get("employment_country")) > 0
    ):
        view_qs = view_qs.filter(
            employment_country__in=filters.get("employment_country")
        )

    if filters and filters.get("pay_currency") and len(filters.get("pay_currency")) > 0:
        view_qs = view_qs.filter(pay_currency__in=filters.get("pay_currency"))

    if filters and filters.get("joining_date"):
        if filters["joining_date"]["type"] == "before":
            view_qs = view_qs.filter(joining_date__lt=filters["joining_date"]["date1"])
        if filters["joining_date"]["type"] == "after":
            view_qs = view_qs.filter(joining_date__gt=filters["joining_date"]["date1"])
        if filters["joining_date"]["type"] == "between":
            view_qs = view_qs.filter(
                joining_date__gte=filters["joining_date"]["date1"],
                joining_date__lte=filters["joining_date"]["date2"],
            )
    # Hierarchy filters
    if (
        filters
        and filters.get("reporting_manager")
        and len(filters.get("reporting_manager")) > 0
    ):
        view_qs = view_qs.filter(
            reporting_manager_email_id__in=filters.get("reporting_manager")
        )

    # Custom field filters
    if filters and filters.get("custom_field") and filters["custom_field"]:
        for key, value in filters["custom_field"].items():
            if value:
                param = {}
                if key in active_date_custom_fields:
                    if value["type"] == "after":
                        param = {f"custom_field_data__{key}__gt": value["date1"]}
                    elif value["type"] == "before":
                        param = {f"custom_field_data__{key}__lt": value["date1"]}
                    elif value["type"] == "between":
                        param = {
                            f"custom_field_data__{key}__gt": value["date1"],
                            f"custom_field_data__{key}__lt": value["date2"],
                        }
                else:
                    param = {f"custom_field_data__{key}__in": value}
                view_qs = view_qs.filter(**param)

    return view_qs


def get_plan_details_of_emp_list(client_id, current_date, email_ids_from_view):
    emp_plan_data = EmployeeDetailsViewAccessor(client_id).get_employee_plan_details(
        current_date, email_ids_from_view
    )
    commission_plan_details = CommissionPlanAccessor(
        client_id
    ).get_all_published_plans()
    plan_names = (
        {x.plan_id: x.plan_name for x in commission_plan_details}
        if commission_plan_details
        else {}
    )
    main_plan_dict = {}
    spiff_plan_dict = {}
    already_appended_plan = set()
    for plan_data in emp_plan_data:
        plan_id = plan_data["plan_id"]
        plan_name = (
            plan_names[plan_data["plan_id"]]
            if plan_data["plan_id"] in plan_names
            else None
        )
        plan_type = plan_data["plan_type"]
        employe_email_id = plan_data["employee_email_id"]
        plan_dict = {"plan_id": plan_id, "plan_name": plan_name, "plan_type": plan_type}
        checker_key = (employe_email_id, plan_id)
        if plan_data["plan_type"] == "MAIN":
            if (
                checker_key not in already_appended_plan
                and plan_data["employee_email_id"] not in main_plan_dict
            ):
                main_plan_dict[plan_data["employee_email_id"]] = [plan_dict]
            elif (
                plan_data["employee_email_id"] in main_plan_dict
                and checker_key not in already_appended_plan
            ):
                main_plan_dict[plan_data["employee_email_id"]].append(plan_dict)

        elif plan_data["plan_type"] == "SPIFF":
            if (
                checker_key not in already_appended_plan
                and plan_data["employee_email_id"] not in spiff_plan_dict
            ):
                spiff_plan_dict[plan_data["employee_email_id"]] = [plan_dict]
            elif (
                plan_data["employee_email_id"] in spiff_plan_dict
                and checker_key not in already_appended_plan
            ):
                spiff_plan_dict[plan_data["employee_email_id"]].append(plan_dict)
        already_appended_plan.add(checker_key)
    return main_plan_dict, spiff_plan_dict


def get_custom_data_for_emp(
    emp_data,
    active_custom_fields,
    active_date_custom_fields,
    active_drop_down_custom_fields,
    default_data,
):
    cus_data = emp_data
    if cus_data:
        for active_field in active_custom_fields:
            if active_field.system_name not in cus_data:
                if active_field.field_type == "Checkbox":
                    cus_data[active_field.system_name] = False
                else:
                    cus_data[active_field.system_name] = None

        for key, value in cus_data.items():
            if key in active_date_custom_fields:
                date_var = (
                    _date.strptime(
                        value,
                        "%Y-%m-%d",
                    )
                    if value
                    else None
                )
                cus_data[key] = date_var.strftime("%d-%b-%Y") if date_var else None
            if key in active_drop_down_custom_fields and value is not None:
                options_map = active_drop_down_custom_fields.get(key, {})
                cus_data[key] = options_map.get(value, "")
    else:
        cus_data = default_data
    return cus_data


def get_employee_by_ms_teams_user_id(user_id: str):
    """
    Function to get an employee record by their MS Teams' User ID.

    Args:
        user_id (str): MS Teams' User ID.

    Returns:
        Optional[Employee]: Returns Employee if valid User ID is supplied. None otherwise.
    """
    config_record = IntegrationConfigAccessor().get_msteams_config_record_with_user_id(
        user_id=user_id
    )
    if len(config_record) == 0:
        return None
    for config in config_record:
        return config
    return None


def get_custom_data_for_export(emp_data, active_custom_fields, default_data):
    cus_data = emp_data
    temp = {}
    if cus_data:
        for active_field in active_custom_fields:
            if active_field.system_name not in cus_data:
                if active_field.field_type == "Checkbox":
                    temp[active_field.display_name] = False
                else:
                    temp[active_field.display_name] = None
            else:
                if active_field.field_type == "Dropdown":
                    for item in active_field.options:
                        if item["system_name"] == cus_data[active_field.system_name]:
                            temp[active_field.display_name] = item["display_name"]
                            break
                elif active_field.field_type == "Date":
                    date_value = None
                    if cus_data[active_field.system_name] is not None:
                        date_value = _date.strptime(
                            cus_data[active_field.system_name],
                            "%Y-%m-%d",
                        )
                    temp[active_field.display_name] = date_value
                else:
                    temp[active_field.display_name] = cus_data[active_field.system_name]
    else:
        temp = default_data
    return temp


def get_exportable_users_data(client_id, filters, view_payroll, payee_emails=None):
    # data = get_filtered_users_from_views(
    #     client_id, filters=filters, search_term=None, offset=0, limit=10, data_for_export=True
    # )
    data = get_filtered_users_raw(
        client_id,
        filters,
        limit=None,
        offset=None,
        data_for_export=True,
        payee_emails=payee_emails,
    )
    df = pd.DataFrame(data["data"])
    if not view_payroll:
        # Drop variable pay and base pay columns
        df = df.drop(columns=["Base Pay", "Variable Pay"], axis=1, errors="ignore")
    csv_data = df.to_csv(index=False, date_format="%d %b %Y")
    return csv_data


def construct_data_for_user_export(
    pg_data_list,
    active_custom_fields,
    main_plan_dict,
    spiff_plan_dict,
    current_date,
    emp_dict,
):
    # construct final data
    final_list = []
    default_data = {}
    for active_field in active_custom_fields:
        if active_field.field_type == "Checkbox":
            default_data[active_field.display_name] = False
        else:
            default_data[active_field.display_name] = None
    for emp in pg_data_list:
        cus_data = get_custom_data_for_export(
            emp["custom_field_data"], active_custom_fields, default_data
        )
        final_status = get_user_status(
            emp_status=emp["status"],
            exit_date=emp["exit_date"],
            deactivation_date=emp["deactivation_date"],
        )

        is_payroll_exists = (
            emp["payroll_effective_start_date"]
            and emp["payroll_effective_start_date"] <= current_date
            and (
                emp["payroll_effective_end_date"] is None
                or emp["payroll_effective_end_date"] >= current_date
            )
        )
        is_hierarchy_exists = (
            emp["hierarchy_effective_start_date"]
            and emp["hierarchy_effective_start_date"] <= current_date
            and (
                emp["hierarchy_effective_end_date"] is None
                or emp["hierarchy_effective_end_date"] >= current_date
            )
        )

        basic = {
            "Name": emp["full_name"],
            "Email": emp["employee_email_id"],
            "Designation": emp["designation"] if is_payroll_exists else None,
            "Primary Commission Plan": formatPlan(
                main_plan_dict[emp["employee_email_id"]]
                if emp["employee_email_id"] in main_plan_dict
                else None
            ),
            "SPIFF Plans": formatPlan(
                spiff_plan_dict[emp["employee_email_id"]]
                if emp["employee_email_id"] in spiff_plan_dict
                else None
            ),
            "Base Pay": emp["fixed_pay"] if is_payroll_exists else None,
            "Variable Pay": emp["variable_pay"] if is_payroll_exists else None,
            "Payout Frequency": emp["payout_frequency"] if is_payroll_exists else None,
            "Payout Currency": emp["pay_currency"] if is_payroll_exists else None,
            "Employee ID": emp["employee_id"] if is_payroll_exists else None,
            "Employment Country": (
                emp["employment_country"] if is_payroll_exists else None
            ),
            "Reporting Manager": (
                emp["reporting_manager_email_id"] if is_hierarchy_exists else None
            ),
            "Reporting Manager Name": (
                (
                    emp_dict[emp["reporting_manager_email_id"]]["full_name"]
                    if emp["reporting_manager_email_id"] in emp_dict
                    else None
                )
                if is_hierarchy_exists
                else None
            ),
            "Joining Date": emp["joining_date"] if is_payroll_exists else None,
            "Exit Date": emp["exit_date"],
            "Status": final_status,
            "Role": emp["user_role"],
        }

        final_list.append(
            {
                **basic,
                **cus_data,
                "Created Date": emp["created_date"],
            }
        )
    return final_list


def formatPlan(data):
    res = ""
    if data:
        for datum in data:
            res += datum["plan_name"] + ", "
    return res[:-2] if len(res) > 0 else res


def get_employee_details_with_filters(client_id, query_params):
    query = construct_query_for_employee_with_quota_filters(client_id, query_params)
    logger.info("Employee Details for Quota Filters Query: %s", query)
    result = execute_query_and_parse_result(query)
    return result


def construct_query_for_employee_with_quota_filters(client_id, query_params):
    query = EmployeeQueryBuilder(client_id).join_employee_quota()
    if query_params.get("quota_filters"):
        query.construct_quota_filters(**query_params["quota_filters"])

    if query_params.get("search_text"):
        query.starts_with_name_or_email(query_params["search_text"])

    if query_params.get("order_by_full_name"):
        query.order_by_employee_full_name()

    if query_params.get("limit"):
        query.limit_and_offset(
            limit=query_params["limit"],
            offset=query_params.get("offset") or 0,
        )

    query.select_distinct_employee_email_id().select_employee_full_name()
    return query.get_sql()


def execute_query_and_parse_result(query):
    with connection.cursor() as cursor:
        cursor.execute(query)
        data = cursor.fetchall()
        columns = [field_description[0] for field_description in cursor.description]
        headers = dict(zip(columns, range(len(columns))))
    return {
        "data": data,
        "headers": employee_services_utils.change_headers_to_camel_case(headers),
    }


def get_payroll_plan_and_designation_details_with_manager_of_employee(
    client_id, emails, effective_date=timezone.now()
):
    base_query = (
        EmployeeQueryBuilder(client_id)
        .kd_aware()
        .left_join_employee_payroll_details(effective_date=effective_date)
        .join_main_plan_details(effective_date=effective_date)
        .join_additional_plan_info_from_plan_details()
        .join_hierarchy(effective_date=effective_date, inner_join=False)
        .select_basic_details_in_employee()
        .select_exit_date()
        .get_has_payroll()
        .select_reporting_manager_email_id()
        .where_employee_email_id_in(emails)
        .order_by_employee_full_name()
    )
    with connection.cursor() as cursor:
        cursor.execute(base_query.get_sql())
        employees = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
    columns_index = dict(zip(columns, range(len(columns))))
    return {"data": employees, "columns_index": columns_index}


def get_payee_by_payout_frequency(
    client_id,
    payout_frequency,
    limit=10,
    search_term=None,
    full_name=None,
    email=None,
    payee_email=None,
):
    """this function will get you all the active payees with a specific payout frequency"""
    log_context = {
        "client_id": client_id,
        "payout_frequency": payout_frequency,
        "limit": limit,
        "full_name": full_name,
        "email": email,
        "search_term": search_term,
    }
    logger.info("BEGIN : get_payee_by_payout_frequency", extra=log_context)
    query_set = EmployeeQueryBuilder(
        client_id
    ).join_with_employee_payroll_where_payout_frequency(
        payout_frequency, limit, search_term, full_name, email, payee_email
    )
    output = []
    with connection.cursor() as cursor:
        cursor.execute(query_set.get_sql())
        employees = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
        columns_index = dict(zip(columns, range(len(columns))))
        output = construct_output_employee_payout_frequency(employees, columns_index)
        output_log_context = {"output_employee_count": len(employees)}

    logger.info("END : get_payee_by_payout_frequency", extra=output_log_context)
    return output


def construct_output_employee_payout_frequency(employees, columns_index):
    """constructs the output as an array of object with payee email and full name [{"employee_email_id": "<EMAIL>", "full_name": "kishore rajkumar"}]"""
    output = []
    for employee in employees:
        emp_email_id = employee[columns_index["employee_email_id"]]
        full_name = employee[columns_index["full_name"]]
        output.append({"employee_email_id": emp_email_id, "full_name": full_name})
    return output


def get_payroll_plan_and_designation_details_of_employee(
    client_id, emails, effective_date=timezone.now()
):
    base_query = (
        EmployeeQueryBuilder(client_id)
        .kd_aware()
        .left_join_employee_payroll_details(effective_date=effective_date)
        .join_main_plan_details(effective_date=effective_date)
        .join_additional_plan_info_from_plan_details()
        .select_basic_details_in_employee()
        .get_has_payroll()
        .where_employee_email_id_in(emails)
        .order_by_employee_full_name_employee_email()
        .select_exit_date()
    )
    with connection.cursor() as cursor:
        cursor.execute(base_query.get_sql())
        employees = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
    columns_index = dict(zip(columns, range(len(columns))))
    return {"data": employees, "columns_index": columns_index}


def get_valid_everstage_users_from_emails(client_id, emails):
    """
    This function will return all the valid everstage users from the given list of emails
    """
    return EmployeeAccessor(client_id).get_all_employees_with_search_limit(
        email_only=True, payee_emails=emails
    )


def validate_joining_date(client_id: str, email_id: str) -> Response:
    """
    Validates the joining date for a client's employee based on the provided information.

    Args:
        client_id (int): The ID of the client.
        email_id (str or list): The email ID of the employee.

    Returns:
        Response: The response object containing the minimum valid_effective_start_date from Hirerachy,Payroll and CustomObject table for the employee
    """
    try:
        result = {}
        payroll_record = EmployeePayrollAccessor(
            client_id
        ).get_payees_minimum_effective_start_date(email_id)
        if payroll_record and payroll_record[0]:
            result = {"joining_date": payroll_record[0].minimium_start_date}
        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        error_dict = {"trace_back": traceback.format_exc()}
        raise SQLParseError(error_dict) from e


def get_users_designations(
    client_id: int, limit: int = None, offset: int = None, search_text: str = None
):
    """
    Retrieves all the users' designations

    Args:
        client_id (int): The client ID for filtering employee data.
        limit (int): The number of records to be returned.
        offset (int): The offset from which the records should be returned.
        search_text (str): The search text to filter the results.

    Returns:
        dict: A dictionary containing the filtered list of designations.

    Sample Return Value:
        {
            "designations": [
                "Intern",
                "Software Engineer I",
                "Software Engineer II"
            ]
        }
    """
    log_context = {
        "client_id": client_id,
        "limit": limit,
        "offset": offset,
        "search_text": search_text,
    }
    logger.info("BEGIN : get_users_designations", extra=log_context)
    res = {"designations": []}
    designations = EmployeePayrollAccessor(client_id).get_designations(
        limit=limit, offset=offset, search_text=search_text
    )
    designations_sorted = sorted(designations, key=lambda x: x.lower())
    logger.info("Designations retrieved: %s", len(designations), extra=log_context)
    res["designations"] = designations_sorted
    logger.info("END : get_users_designations", extra=log_context)
    return res


def get_valid_employees_for_sync(client_id, curr_date):
    employee_payroll_acc = EmployeePayrollAccessor(client_id)

    # Get valid payees payroll data for the current sync
    emp_payroll = (
        employee_payroll_acc.get_valid_employee_payroll_details_for_commission_sync(
            curr_date
        )
    )

    valid_employee_email_list = [entry.employee_email_id for entry in emp_payroll]

    # Get exit payees email ids
    # if they have payouts or are in plans with ped post their last commission dates
    exit_payee_email_ids = employee_payroll_acc.get_exit_employees_email_ids()
    distinct_exit_email_ids = []

    # Collect distinct exit payees email ids which are not already included in valid payees list
    for email_id in exit_payee_email_ids:
        if email_id not in valid_employee_email_list:
            distinct_exit_email_ids.append(email_id)

    # Get distinct exit payees payroll data, if any
    if distinct_exit_email_ids:
        exit_payee_details = list(
            employee_payroll_acc.get_employee_payroll(
                date=curr_date,
                employee_email_ids=distinct_exit_email_ids,
                as_dicts=False,
            )
        )

        emp_payroll += exit_payee_details

    return emp_payroll


def employee_details_based_on_permission(info, **kwargs):
    client_id = info.context.client_id
    login_user_id = info.context.user.username
    user_status = kwargs.get("user_status")
    as_of_date = kwargs.get("as_of_date")
    offset_value = kwargs.get("offset_value")
    limit_value = kwargs.get("limit_value")
    search_term = kwargs.get("search_term")
    component = kwargs.get("component")
    user_role = kwargs.get("user_role")
    context = kwargs.get("context")
    selected_options = kwargs.get("selected_options")
    is_to_fetch_exited_users = kwargs.get("is_to_fetch_exited_users")
    employee_list = []
    # Check if the component requires data permission check
    if component in [
        RBACComponent.PAYOUTS_STATEMENTS.value,
        RBACComponent.MANAGE_USERS.value,
    ]:
        data_permission = get_data_permission(client_id, login_user_id, component)

        if not data_permission:
            raise GraphQLError("Permission denied")

        if data_permission["type"] != RBAC.ALL_DATA.value:
            employee_list = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )

    is_authorized = _does_user_have_access_for_employee_details(
        client_id=client_id, login_user_id=login_user_id, component=component
    )

    if not is_authorized:
        logger.info(
            f"User: {login_user_id} don't have permission to access employee details"
        )
        raise GraphQLError(GRAPHQL__PERMISSION_DENIED)
    if not is_to_fetch_exited_users:
        is_to_fetch_exited_users = False
    if is_to_fetch_exited_users:
        if not selected_options:
            return []
        users_result_list = EmployeeAccessor(
            info.context.client_id
        ).get_all_employees_with_limit(
            as_of_date=as_of_date,
            user_status=user_status,
            search_term=search_term,
            offset=offset_value,
            limit=limit_value,
            email_list=employee_list,
            user_role=user_role,
            context=context,
            only_email=True,
            is_to_fetch_exited_users=is_to_fetch_exited_users,
        )
        as_of_date = timezone.now()
        user_status_result = generate_query_for_user_status(
            selected_options, as_of_date, client_id, users_result_list
        )
        if user_status_result:
            sorted_employees = sorted(
                user_status_result,
                key=lambda emp: f"{emp.first_name.lower()} {emp.last_name.lower()}",
            )
            return sorted_employees
        else:
            return []
    else:
        users_result_list = EmployeeAccessor(
            info.context.client_id
        ).get_all_employees_with_limit(
            as_of_date=as_of_date,
            user_status=user_status,
            search_term=search_term,
            offset=offset_value,
            limit=limit_value,
            email_list=employee_list,
            user_role=user_role,
            context=context,
            is_to_fetch_exited_users=is_to_fetch_exited_users,
        )
        return users_result_list


def generate_query_for_user_status(
    selected_options: List[str],
    as_of_date: datetime.datetime,
    client_id: int,
    employee_email_list: List[str],
):
    """Generate a query based on the selected status."""

    # Ensure employee_list contains only strings
    if not employee_email_list:
        employee_email_list = [""]

    email_ids = tuple(employee_email_list)

    base_query = """
        SELECT e.*
        FROM employee e
        LEFT JOIN employee_payroll_details ped
        ON ped.employee_email_id = e.employee_email_id
        WHERE e.client_id = %(client_id)s
        AND ped.client_id = %(client_id)s
        AND e.knowledge_end_date IS NULL
        AND ped.knowledge_end_date IS NULL
        AND e.is_deleted IS FALSE
        AND ped.is_deleted IS FALSE
        AND ped.effective_end_date IS NULL
        AND e.employee_email_id IN %(email_ids)s
    """

    base_query2 = """SELECT e.*
            FROM employee e
            WHERE e.employee_email_id NOT IN (
                SELECT epd.employee_email_id
                FROM employee_payroll_details epd
                WHERE epd.client_id = %(client_id)s
                AND epd.knowledge_end_date IS NULL 
                AND epd.is_deleted IS FALSE
            )
            AND e.client_id = %(client_id)s 
            AND e.knowledge_end_date IS NULL 
            AND e.is_deleted IS FALSE
            AND e.employee_email_id IN %(email_ids)s"""

    query_parts = []

    if "ACTIVE" in selected_options:
        query1 = f"""
            {base_query}
            AND ped.joining_date <= %(as_of_date)s
            AND (e.exit_date > %(as_of_date)s OR e.exit_date IS NULL)
        """
        query2 = f"""
            {base_query2}
            AND (e.exit_date > %(as_of_date)s OR e.exit_date IS NULL)
        """
        query_parts.append(f"({query1}) UNION ({query2})")

    if "YET_TO_JOIN" in selected_options:
        # query1 is used to fetch users whose joining date is greater than current date
        query_parts.append(
            f"""
            {base_query}
            AND ped.joining_date > %(as_of_date)s
        """
        )

    if "EXITED" in selected_options:
        query1 = f"""{base_query}
            AND e.exit_date <= %(as_of_date)s
        """
        query2 = f"""{base_query2}
            AND e.exit_date <= %(as_of_date)s
        """
        query_parts.append(f"({query1}) UNION ({query2})")
    if not query_parts:
        return []

    combined_query = " UNION ".join(query_parts)

    try:
        exit_payees_obj_list = list(
            Employee.objects.raw(
                combined_query,
                params={
                    "client_id": client_id,
                    "email_ids": email_ids,
                    "as_of_date": as_of_date,
                },
            )
        )
        logger.info(
            "combined query and query params: %s %s",
            combined_query,
            {
                "client_id": client_id,
                "email_ids": email_ids,
                "as_of_date": as_of_date,
            },
        )

    except Exception as e:
        logger.error("Error executing raw SQL query %s:", e)
        return []

    return exit_payees_obj_list


def get_all_employee_details_based_on_permission(
    *, client_id, login_user_id, component, params
):
    employee_list = []
    # Check if the component requires data permission check
    if component in [
        RBACComponent.PAYOUTS_STATEMENTS.value,
        RBACComponent.MANAGE_USERS.value,
    ]:
        data_permission = get_data_permission(client_id, login_user_id, component)

        if not data_permission:
            raise ValueError("Permission denied")

        if data_permission["type"] != RBAC.ALL_DATA.value:
            employee_list = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )

    is_authorized = _does_user_have_access_for_employee_details(
        client_id=client_id, login_user_id=login_user_id, component=component
    )

    if not is_authorized:
        logger.error(
            f"User: {login_user_id} don't have permission to access employee details"
        )
        raise ValueError("Permission denied")

    result = EmployeeAccessor(client_id).get_all_employees_with_limit(
        email_list=employee_list,
        # Typecasting to int since we will receive string from the django REST query params
        limit=int(params["limit_value"]) if "limit_value" in params else None,
        offset=int(params["offset_value"]) if "offset_value" in params else None,
        search_term=params.get("search_term"),
    )
    from spm.pydantic_serializers.user_group import EmployeeListResponse

    return EmployeeListResponse(result)


def _does_user_have_access_for_employee_details(*, client_id, login_user_id, component):
    ui_permissions = get_ui_permissions(client_id, login_user_id)
    is_authorized = False
    if component == RBACComponent.MANAGE_USERS.value:
        is_authorized = any(
            perm in ui_permissions
            for perm in [
                RbacPermissions.MANAGE_USERGROUPS.value,
                RbacPermissions.VIEW_TEAMS.value,
                RbacPermissions.MANAGE_USERS.value,
            ]
        )
    elif component == RBACComponent.PAYOUTS_STATEMENTS.value:
        is_authorized = RbacPermissions.MANAGE_CONFIG.value in ui_permissions
    elif component == RBACComponent.SETTINGS.value:
        is_authorized = any(
            perm in ui_permissions
            for perm in [
                RbacPermissions.MANAGE_CONFIG.value,
                RbacPermissions.MANAGE_DATASETTINGS.value,
            ]
        )
    elif component == RBACComponent.ADVANCED_PERMISSION.value:
        is_authorized = RbacPermissions.MANAGE_ROLES.value in ui_permissions
    elif component == RBACComponent.DATABOOKS.value:
        is_authorized = (
            RbacPermissions.MANAGE_DATASHEETPERMISSIONS.value in ui_permissions
        )
    return is_authorized


def get_power_admin_users_for_client(client_id: int) -> List[str]:
    """
    Get all the power admin user emails for a client

    Args:
        client_id (int): The client ID

    Returns:
        List[str]: List of power admin user email ids

    """
    roles: List[UUID] = RolePermissionsAccessor(
        client_id
    ).get_all_power_admins_role_id()
    return EmployeeReadAccessor(client_id).get_active_employee_email_ids_by_roles(roles)
