import datetime
import logging

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.utils import timezone
from django.utils.timezone import make_aware
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from commission_engine.accessors.schedule_accessor import NotificationTaskAccessor
from commission_engine.serializers.payee_notification_serializer import (
    PayeeNotificationSerializer,
)
from commission_engine.utils.general_data import (
    Command,
    IntegrationType,
    Notification,
    NotificationMode,
)
from slack_everstage.accessors.slack_client_details_accessor import (
    SlackClientDetailsAccessor,
)
from slack_everstage.accessors.slack_installation_accessor import SlackAccessor
from slack_everstage.models import SlackInstallation
from slack_everstage.utils.slack_utils import (
    get_client_id_from_email,
    set_request_context,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.models.config_models.employee_models import Employee
from spm.serializers.config_serializers.integration_config_serializers import (
    IntegrationConfigSerializer,
)

logger = logging.getLogger(__name__)


@transaction.atomic
def update_slack_details_for_client_members(
    client_id,
    bot_token,
    team_id,
    installation_details,
    include_employees=None,
    exclude_employee=None,
    should_update_employee_config=True,
):
    if include_employees is None:
        include_employees = []
    logger.info(
        "BEGIN: Update employee config for all employees with client_id: %s", client_id
    )
    slack_client = WebClient(token=bot_token)
    next_cursor = ""
    employee_slack_details = {}
    payees = []
    employee_config_map = {}
    slack_user_ids = []
    is_user_not_found = False
    # use users_lookupByEmail api if only one user is added
    if len(include_employees) == 1:
        try:
            users_list_response = slack_client.users_lookupByEmail(
                email=include_employees[0]
            )
        except SlackApiError as slack_exception:
            if slack_exception.response.get("error") == "users_not_found":
                is_user_not_found = True
            else:
                raise Exception from slack_exception
    else:
        users_list_response = slack_client.users_list(team_id=team_id, limit=1000)

    try:
        while True and not is_user_not_found:
            if users_list_response.get("ok"):
                if len(include_employees) == 1:
                    workspace_members = [users_list_response.get("user", {})]
                else:
                    workspace_members = users_list_response.get("members", [])
            for member in workspace_members:
                member_email = member.get("profile", {}).get("email", None)
                if member_email and (member_email != exclude_employee):
                    if include_employees and member_email not in include_employees:
                        continue
                    employee_slack_details[member_email] = {
                        "member_team_id": member["team_id"],
                        "member_user_id": member["id"],
                        "is_member_deleted_from_workspace": member.get(
                            "deleted", False
                        ),
                    }
                    if not should_update_employee_config:
                        slack_user_ids.append(member["id"])
                        employee_config_map[member_email] = {
                            "slack_user_id": member["id"],
                            "slack_team_id": member["team_id"],
                        }
            if len(include_employees) == 1:
                break
            next_cursor = users_list_response["response_metadata"].get(
                "next_cursor", ""
            )
            if len(str(next_cursor)):
                logger.info("Fetching more employees with cursor: %s", next_cursor)
                users_list_response = slack_client.users_list(
                    team_id=team_id, cursor=next_cursor, limit=1000
                )
            else:
                break
        logger.info(
            "Fetched all employees from slack team: %s", installation_details["team_id"]
        )

        employees = EmployeeAccessor(client_id).get_employees(
            employee_slack_details.keys()
        )
        employees_in_slack = []

        # if not employees:
        #     return employee_config_map if not should_update_employee_config else None

        knowledge_date = timezone.now()
        active_slack_employees = []

        for employee in employees:
            _employee_slack_details = employee_slack_details.get(
                employee["employee_email_id"], None
            )

            if (
                _employee_slack_details
                and not _employee_slack_details["is_member_deleted_from_workspace"]
            ):
                slack_user_id = _employee_slack_details["member_user_id"]
                slack_team_id = _employee_slack_details["member_team_id"]
                emp_config = (
                    employee["employee_config"]
                    if employee and employee["employee_config"]
                    else {}
                )
                emp_config.update(
                    {"slack_user_id": slack_user_id, "slack_team_id": slack_team_id}
                )
                slack_user_ids.append(slack_user_id)

                if should_update_employee_config:
                    config_record = {
                        "integration_type": IntegrationType.SLACK.value,
                        "employee_email_id": employee["employee_email_id"],
                        "client": employee["client_id"],
                        "knowledge_begin_date": knowledge_date,
                        "config": {
                            "slack_user_id": slack_user_id,
                            "slack_team_id": slack_team_id,
                        },
                    }
                    employee["knowledge_begin_date"] = knowledge_date
                    employee["employee_config"] = emp_config
                    employee["client"] = employee.pop("client_id")

                    employees_in_slack.append(employee["employee_email_id"])
                    active_slack_employees.append(config_record)

                    # Since the task creation logic is nested, avoiding bulk update/create for notification task
                    payees.append(employee["employee_email_id"])
        # if payees:
        #     invalidate_and_add_slack_notifications_for_users(
        #         client_id=client_id, payee_email_list=payees
        #     )
        if should_update_employee_config and len(active_slack_employees) > 0:
            IntegrationConfigAccessor().invalidate_slack_config_for_email_ids(
                client_id=client_id,
                email_ids=employees_in_slack,
                end_time=knowledge_date,
            )
            logging.info(
                "Invalidated latest config records for: %s", employees_in_slack
            )
            config_ser = IntegrationConfigSerializer(
                data=active_slack_employees, many=True
            )
            # emp_ser = EmployeeSerializer(data=active_slack_employees, many=True)
            if config_ser.is_valid():
                config_ser.save()
                logging.info(
                    "Created config records with slack config for: %s",
                    employees_in_slack,
                )
            else:
                logging.error("Error saving the employee config: %s", config_ser.errors)
                raise Exception(
                    f"Error saving the employee config: {config_ser.errors}"
                )
        slack_installations = (
            SlackInstallation.objects.filter(
                user_id__in=slack_user_ids, team_id=installation_details["team_id"]
            )
            .order_by("-installed_at")
            .all()
        )

        slack_installations_update = []
        slack_installations_create = []
        slack_user_ids_update = []
        slack_user_ids_create = []

        for slack_installation in slack_installations:
            installation_details["user_id"] = slack_installation.user_id
            if (
                slack_installation.user_id in slack_user_ids
                and slack_installation.user_id not in slack_user_ids_update
            ):
                slack_user_ids_update.append(slack_installation.user_id)
                for key, value in installation_details.items():
                    setattr(slack_installation, key, value)
                slack_installations_update.append(slack_installation)

        slack_user_ids_create = set(slack_user_ids) - set(slack_user_ids_update)
        for slack_user_id in slack_user_ids_create:
            installation_details["user_id"] = slack_user_id
            slack_installations_create.append(SlackInstallation(**installation_details))
        if slack_user_ids_update:
            SlackInstallation.objects.bulk_update(
                slack_installations_update, installation_details.keys()
            )
            logger.info(
                "Updated slack installation data for user ids: %s",
                slack_user_ids_update,
            )
        if slack_user_ids_create:
            SlackInstallation.objects.bulk_create(slack_installations_create)
            logger.info(
                "Created slack installation data for user ids: %s",
                slack_user_ids_create,
            )

        logger.info(
            "END: Updated employee config for all employees with client_id: %s",
            client_id,
        )
        return employee_config_map if not should_update_employee_config else None
    except Exception as e:
        logger.error(
            "Error updating slack details for: %s members for client: %s - %s",
            team_id,
            client_id,
            e,
        )
        raise Exception from e


@transaction.atomic
def integrate_slack_for_newly_added_users(client_id, employee_list):
    from commission_engine.services.payee_notification_service import is_slack_connected
    from slack_everstage.services.slack_app_service import get_valid_token

    slack_client_details = SlackClientDetailsAccessor(client_id).get()
    if not is_slack_connected(client_id) or not slack_client_details:
        return
    bot_token = get_valid_token(
        slack_client_details.user_id, slack_client_details.team_id
    )["bot_token"]
    installation_details = (
        SlackInstallation.objects.filter(
            user_id=slack_client_details.user_id, team_id=slack_client_details.team_id
        )
        .order_by("-installed_at")
        .values()[0]
    )
    installation_details.pop("id")
    logger.info("BEGIN: Slack installation for newly added users")
    employee_config_map = update_slack_details_for_client_members(
        client_id,
        bot_token,
        slack_client_details.team_id,
        installation_details,
        include_employees=employee_list,
        should_update_employee_config=False,
    )
    logger.info("END: Slack installation for newly added users")
    return employee_config_map


def save_slack_details(
    client_id,
    email_id,
    slack_user_id,
    slack_team_id,
    knowledge_date,
):
    try:
        from commission_engine.services.payee_notification_service import (
            is_slack_integrated,
        )

        integration_config_accessor = IntegrationConfigAccessor()
        if not slack_user_id and not slack_team_id:
            if is_slack_integrated(client_id, email_id):
                integration_config_accessor.invalidate_config_record_for_emp_id(
                    client_id=client_id,
                    employee_email_id=email_id,
                    integration_type=IntegrationType.SLACK.value,
                    end_time=knowledge_date,
                )
        else:
            slack_config = {
                "slack_user_id": slack_user_id,
                "slack_team_id": slack_team_id,
            }
            integration_config_accessor.invalidate_config_record_for_emp_id(
                client_id=client_id,
                employee_email_id=email_id,
                integration_type=IntegrationType.SLACK.value,
                end_time=knowledge_date,
            )
            integration_config_accessor.create_config_for_emp_id(
                client_id=client_id,
                employee_email_id=email_id,
                integration_type=IntegrationType.SLACK.value,
                knowledge_begin_date=knowledge_date,
                config=slack_config,
            )
    except Exception as exception:
        logging.error("Error saving slack details for %s: %s", email_id, exception)
        raise Exception from exception


def add_default_slack_notifications(email_id):
    """
    returns list of default tasks for the payee
        {
            "email_id": "<EMAIL>",
            "tasks": [
                {
                    "name": "COMMISSION_NOTIFICATION",
                    "frequency": "DAILY",
                    "slack": {
                        "enabled": "true"
                    }
                }
            ]
        }

    """
    from commission_engine.services.payee_notification_service import (
        add_tasks_for_payee,
    )
    from spm.services.rbac_services import get_ui_permissions

    default_slack_tasks = []
    client_id = get_client_id_from_email(email_id)
    user_permissions = get_ui_permissions(client_id, email_id)

    for notification in Notification:
        required_permissions = notification.value.get("permissions", [])
        can_show = False
        for permission in required_permissions:
            if user_permissions and permission in user_permissions:
                can_show = True
                break

        if (
            can_show
            and notification.value["managed_by_payee"]
            and notification.value.get("enabled_on_connect", False)
        ):
            default_slack_tasks.append(
                {
                    "name": notification.name,
                    "frequency": notification.value["frequency"],
                    "slack": {"enabled": True},
                }
            )
    add_tasks_for_payee(client_id, default_slack_tasks, email_id)
    logger.info("Added default tasks for payee %s on Slack", email_id)


def add_default_slack_and_ms_teams_notification_for_users(
    client_id, newly_added_users, users_slack_map, users_ms_teams_map
):
    if users_slack_map is None:
        users_slack_map = {}
    if users_ms_teams_map is None:
        users_ms_teams_map = {}
    teams_notications_to_add = []
    slack_noticiations_to_add = []
    payees_list = []
    try:
        employees = EmployeeAccessor(client_id).get_employees(newly_added_users)
        for employee in employees:
            payees_list.append(employee["employee_email_id"])
        for employee_email_id in newly_added_users:
            if (
                employee_email_id in users_ms_teams_map
                and employee_email_id in payees_list
            ):
                teams_notications_to_add.append(employee_email_id)
            if (
                employee_email_id in users_slack_map
                and employee_email_id in payees_list
            ):
                slack_noticiations_to_add.append(employee_email_id)
        if teams_notications_to_add:
            from ms_teams_everstage.services.tenant_services import (
                add_default_msteams_notifications,
            )

            add_default_msteams_notifications(client_id, teams_notications_to_add)
        if slack_noticiations_to_add:
            invalidate_and_add_slack_notifications_for_users(
                client_id, slack_noticiations_to_add
            )
    except Exception as err:
        logger.info(
            "Failed to add ms-teams/slack notifications for the newly added users - %s",
            err,
        )


def invalidate_and_add_slack_notifications_for_users(client_id, payee_email_list):
    """
    This method is used to invalidate the existing slack notifications for the payees
    and add the default slack notifications for the payees
    """
    employee_task_map = {}
    tasks_to_invalidate = []
    from spm.services.rbac_services import get_ui_permissions

    logger.info(
        "BEGIN: Invalidate and add slack notifications for client id %s and for the following employees %s",
        client_id,
        payee_email_list,
    )

    for payee_email in payee_email_list:
        client_id = get_client_id_from_email(payee_email)
        notifications_to_add = []
        user_permissions = get_ui_permissions(client_id, payee_email)
        for notification in Notification:
            required_permissions = notification.value.get("permissions", [])
            can_show = False
            for permission in required_permissions:
                if user_permissions and permission in user_permissions:
                    can_show = True
                    break
            if (
                can_show
                and notification.value["managed_by_payee"]
                and notification.value.get("enabled_on_connect", False)
            ):
                slack_notification = {
                    "name": notification.name,
                    "frequency": notification.value["frequency"],
                    "slack": {"enabled": True},
                }
                notifications_to_add.append(slack_notification)
                for mode in [mode.value["name"] for mode in NotificationMode]:
                    if mode in slack_notification:
                        notification_name = slack_notification["name"]
                        notification = Notification[notification_name].value
                        task_name = notification[mode]["task"]
                        if task_name not in tasks_to_invalidate:
                            tasks_to_invalidate.append(task_name)
        employee_task_map[payee_email] = notifications_to_add
    # invalidate the existing slack notifications for the payees in bulk
    kd = make_aware(datetime.datetime.now())
    NotificationTaskAccessor(client_id).invalidate_specified_task_for_payees(
        email_ids=payee_email_list, tasks=tasks_to_invalidate, kd=kd
    )
    from commission_engine.services.payee_notification_service import (
        add_notification_task_in_bulk_for_employees,
    )

    add_notification_task_in_bulk_for_employees(
        client_id=client_id, employee_task_map=employee_task_map
    )
    logger.info(
        "END: Invalidate and add slack notifications for client id %s and for the following employees %s",
        client_id,
        payee_email_list,
    )


def add_slack_notifications_for_newly_added_employees(client_id, payees):
    payee_email = payees[0]
    add_default_slack_notifications(payee_email)
    notification_tasks = NotificationTaskAccessor(client_id).get_tasks_for_payee(
        payee_email, as_dicts=True
    )
    notification_tasks_to_be_saved = []
    if notification_tasks:
        for payee in payees:
            if payee_email == payee:
                continue
            for task in notification_tasks:
                if task["task"].startswith("SLACK_"):
                    task["employee_email_id"] = payee
                    task["client"] = task["client_id"]
                    notification_tasks_to_be_saved.append(task)
        if notification_tasks_to_be_saved:
            notification_tasks_serializer = PayeeNotificationSerializer(
                data=notification_tasks_to_be_saved, many=True
            )

            if notification_tasks_serializer.is_valid():
                notification_tasks_serializer.save()
                logger.info("Saved default notification tasks for payee")
            else:
                logger.error(
                    "Error saving default notification tasks for payees: %s",
                    notification_tasks_serializer.errors,
                )
                raise Exception(
                    f"Error saving default notification tasks for payees: {notification_tasks_serializer.errors}"
                )


def update_slack_installation_details_for_employee(
    client_id, member_details, installation_details
):
    member_team_id = member_details["team_id"]
    member_user_id = member_details["id"]
    member_email = member_details.get("profile", {}).get("email", None)
    employee = EmployeeAccessor(client_id).get_employee(member_email)

    if employee:
        logger.info(
            "Update slack installation details for config for all employee: %s",
            member_email,
        )
        slack_installation = SlackAccessor(
            member_user_id, member_team_id
        ).get_slack_installation()
        installation_details["user_id"] = member_user_id
        if slack_installation is not None:
            for key, value in installation_details.items():
                setattr(slack_installation, key, value)
            slack_installation.save()
        else:
            SlackInstallation(**installation_details).save()


def save_slack_installation_details(user_id, slack_client_details):
    admin_installation_details = (
        SlackInstallation.objects.filter(team_id=slack_client_details.team_id)
        .filter(user_id=slack_client_details.user_id)
        .order_by("-installed_at")
        .first()
    )

    admin_installation_details_dict = admin_installation_details.__dict__

    row_to_update = (
        SlackInstallation.objects.filter(client_id=admin_installation_details.client_id)
        .filter(enterprise_id=admin_installation_details.enterprise_id)
        .filter(team_id=admin_installation_details.team_id)
        .filter(user_id=user_id)
        .order_by("-installed_at")
        .first()
    )
    if row_to_update is not None:
        for key, value in admin_installation_details_dict.items():
            setattr(row_to_update, key, value)
        row_to_update.save()
    else:
        admin_installation_details_dict["user_id"] = user_id
        del admin_installation_details_dict["_state"]
        del admin_installation_details_dict["id"]
        SlackInstallation(**admin_installation_details_dict).save()


def add_slack_details_for_employee(employee):
    try:
        logger.error(
            "Adding slack details for newly added employee: %s",
            employee.employee_email_id,
        )
        slack_installation = SlackInstallation.objects.order_by("-installed_at").first()
        from slack_everstage.services.slack_app_service import get_valid_token

        bot_token = get_valid_token(
            slack_installation.user_id, slack_installation.team_id
        )["bot_token"]
        slack_client = WebClient(token=bot_token)
        slack_user_details_response = slack_client.users_lookupByEmail(
            email=employee.employee_email_id
        )
        if slack_user_details_response.get("ok"):
            slack_user_details = slack_user_details_response.get("user")
            member_user_id = slack_user_details.get("id", None)
            member_team_id = slack_user_details.get("team_id", None)
            is_member_deleted_from_workspace = slack_user_details.get("deleted", False)
            update_employee_config(
                employee.employee_email_id,
                member_user_id,
                member_team_id,
                is_member_deleted_from_workspace,
                employee,
            )
            add_default_slack_notifications(employee.employee_email_id)
    except SlackApiError as slack_exception:
        if slack_exception.response.get("error") == "users_not_found":
            logger.info(
                "%s not present in team %s in slack, Skipping slack integration",
                employee.employee_email_id,
                slack_installation.team_id,
            )
        else:
            raise Exception from slack_exception
    except Exception as exception:
        logger.error(
            "Error in adding slack details for employee %s: %s",
            employee.employee_email_id,
            exception,
        )
        raise Exception from exception


def update_employee_config(
    email,
    user_id,
    team_id,
    is_member_deleted_from_workspace,
    employee=None,
    slack_logger=None,
):
    if not employee:
        employees = Employee.objects.filter(
            is_deleted=False,
            knowledge_end_date__isnull=True,
            employee_email_id=email,
        )
        for emp in employees:
            employee = emp
    if employee:
        set_request_context(employee)
        time = timezone.now()

        from commission_engine.services.payee_notification_service import (
            is_slack_connected,
        )

        slack_client_details = SlackClientDetailsAccessor(employee.client_id).get()
        if not is_slack_connected(employee.client_id) or not slack_client_details:
            logger.info(
                "Client %s not connected to slack..Skipping slack install/update for employee: %s",
                employee.client_id,
                employee.employee_email_id,
            )
            return
        if is_member_deleted_from_workspace:
            logger.info("User deleted from workspace: %s", employee.employee_email_id)
            save_slack_details(
                client_id=employee.client_id,
                email_id=employee.employee_email_id,
                slack_user_id=None,
                slack_team_id=None,
                knowledge_date=time,
            )
        else:
            logger.info("User added to workspace: %s", employee.employee_email_id)
            save_slack_details(
                client_id=employee.client_id,
                email_id=employee.employee_email_id,
                slack_user_id=user_id,
                slack_team_id=team_id,
                knowledge_date=time,
            )

            save_slack_installation_details(user_id, slack_client_details)

            add_default_slack_notifications(employee.employee_email_id)
        logger.info(
            "Updated slack config for employee: %s with client id: %s",
            email,
            employee.client_id,
        )
    else:
        if slack_logger:
            slack_logger.info(
                "Employee with email id: %s not found, Skipping slack config update",
                email,
            )
        else:
            logger.info(
                "Employee with email id: %s not found, Skipping slack config update",
                email,
            )


def get_employee_in_slack_from_email_id(client_id, email_id, notification_type):
    employee_obj = (
        Employee.objects.filter(knowledge_end_date__isnull=True)
        .filter(is_deleted=False)
        .filter(employee_email_id=email_id)
        .filter(client_id=client_id)
    ).first()

    if not employee_obj:
        raise ObjectDoesNotExist(f"Email id {email_id} not found in Everstage.")

    if not is_notification_allowed_for_user(client_id, email_id, notification_type):
        logger.info(
            "User - %s is not allowed to receive notification - %s",
            email_id,
            notification_type,
        )
        return

    employee_deactivation_date = employee_obj.deactivation_date
    if employee_deactivation_date and employee_deactivation_date <= timezone.now():
        logger.info(
            "User - %s got deactivated on %s", email_id, employee_deactivation_date
        )
        return

    return employee_obj


def get_employee_in_slack(slack_user_id, slack_team_id):
    config_record = (
        IntegrationConfigAccessor().get_slack_config_record_with_team_id_user_id(
            slack_user_id, slack_team_id
        )
    )
    # Employee.objects.filter(knowledge_end_date__isnull=True)
    # .filter(is_deleted=False)
    # .filter(employee_config__slack_team_id=slack_team_id)
    # .filter(employee_config__slack_user_id=slack_user_id)
    if len(config_record) == 0:
        raise ObjectDoesNotExist(
            f"Slack user_id {slack_user_id} not found in Everstage."
        )
    for emp in config_record:
        return emp
    raise ObjectDoesNotExist(f"Slack user_id {slack_user_id} not found in Everstage.")


def get_active_or_invalidated_employee_in_slack(slack_user_id, slack_team_id):
    config_record = IntegrationConfigAccessor().get_active_or_invalidated_slack_config_record_with_team_id_user_id(
        slack_user_id, slack_team_id
    )
    if len(config_record) == 0:
        raise ObjectDoesNotExist(
            f"Slack user_id {slack_user_id} not found in Everstage."
        )
    for emp in config_record:
        return emp
    raise ObjectDoesNotExist(f"Slack user_id {slack_user_id} not found in Everstage.")


def is_notification_allowed_for_user(client_id, email_id, notification_type):
    from spm.services.rbac_services import get_ui_permissions

    # If the notification type is WORKFLOW_NOTIFICATION, then allow the notification
    if notification_type == "WORKFLOW_NOTIFICATION":
        return True

    if notification_type not in Notification.__members__:
        return False

    notification = Notification[notification_type]
    user_permissions = get_ui_permissions(client_id, email_id) or []
    required_permissions = notification.value.get("permissions", [])

    for permission in required_permissions:
        if user_permissions and permission in user_permissions:
            return True

    return len(required_permissions) == 0


def is_command_allowed_for_user(client_id, email_id, command_type):
    from spm.services.rbac_services import get_ui_permissions

    if command_type not in Command.__members__:
        return False

    command = Command[command_type]
    user_permissions = get_ui_permissions(client_id, email_id) or []
    required_permissions = command.value.get("permissions", [])

    for permission in required_permissions:
        if user_permissions and permission in user_permissions:
            return True

    return len(required_permissions) == 0
