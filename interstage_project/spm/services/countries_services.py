import json
from ast import literal_eval

from django.db import transaction
from django.db.models import Max
from rest_framework import status
from rest_framework.response import Response

from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.models.config_models.countries_models import Countries


@transaction.atomic
def add_countries(request):
    logger = request.logger

    query = {"country_code": request.data["country_code"]}
    ids = list(Countries.objects.filter(**query))
    if len(ids) > 0:
        return Response(
            {
                "status": "ERROR",
                "errors": {"country_code": "Country code already present"},
            },
            status=status.HTTP_409_CONFLICT,
        )

    max_id = Countries.objects.filter().aggregate(max_id=Max("pk"))["max_id"]
    country_id = max_id + 1 if max_id else 1
    try:
        Countries.objects.create(
            id=country_id,
            country_code=request.data["country_code"],
            country_name=request.data["country_name"],
            currency_code=request.data["currency_code"],
            currency_symbol=request.data["currency_symbol"],
            is_client_specific=literal_eval(
                request.data["is_client_specific"].capitalize()
            ),
            client_ids=json.loads(request.data["client_ids"]),
            is_active=True,
        )
        return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)

    except Exception as ex:
        logger.error("Error while adding country: {}".format(str(ex)))
        return Response(
            {"error": str(ex)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@transaction.atomic
def update_countries(request):
    logger = request.logger
    query = {"id": request.data["id"]}
    country = Countries.objects.filter(**query)
    try:
        country_name = request.data["country_name"]
        currency_symbol = request.data["currency_symbol"]
        is_client_specific = literal_eval(
            request.data["is_client_specific"].capitalize()
        )
        client_ids = json.loads(request.data["client_ids"])

        if not is_client_specific:
            updates = {
                "country_name": country_name,
                "currency_symbol": currency_symbol,
                "is_client_specific": is_client_specific,
                "client_ids": [],
            }
        else:
            updates = {
                "country_name": country_name,
                "currency_symbol": currency_symbol,
                "is_client_specific": is_client_specific,
                "client_ids": client_ids,
            }

        country.update(**updates)

        return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)

    except Exception as ex:
        logger.error("Error while updating country: {}".format(str(ex)))
        return Response(
            {"error": str(ex)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def get_currency_symbol(client_id, currency):
    """
    given a currency, returns the currency symbol
    """
    currency_symbol = (
        CountriesAccessor(client_id).get_currency_symbol(currency).currency_symbol
    )
    return currency_symbol
