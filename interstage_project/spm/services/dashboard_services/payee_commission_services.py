import calendar
import datetime
from datetime import date, datetime, timedelta
from decimal import Decimal

from dateutil.parser import parse
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.commission_accessor import CommissionAccessor
from commission_engine.accessors.lock_accessor import Commission<PERSON>ockAccessor
from commission_engine.accessors.payout_status_accessor import PayoutStatusAccessor
from commission_engine.utils import get_variable_pay_per_period
from commission_engine.utils.date_utils import (
    end_of_day,
    get_period_start_and_end_date,
    get_previous_different_payroll_periods,
    last_day_of_month,
)
from commission_engine.utils.fx_utils import change_to_payee_currency_by_fx_rate
from commission_engine.utils.general_data import Freq
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.accessors.drs_accessor import Dr<PERSON><PERSON>ccessor
from spm.accessors.payout_accessor import PayoutAccessor
from spm.constants.localization_constants import DashboardMessages
from spm.services.dashboard_services.commission_services import (
    calculate_qtd_values,
    get_payee_payroll_date,
)
from spm.services.dashboard_services.dashboard_services import (
    get_end_dates_list,
    get_period_name_for_date,
)
from spm.services.dashboard_services.quota_dashboard_services import get_period
from spm.services.localization_services import get_localized_message_service
from spm.utils import is_valid_uuid

logger = LogWithContext()


def get_commission_payout_for_year_for_payee_periods(client_id, year, payee_id):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    end_dates_list = get_end_dates_list(int(year), start_month)
    logger.info("Getting comission payouts for payee")
    com_data = get_commission_payouts_for_payee(client_id, end_dates_list, payee_id)
    if "error" in com_data:
        logger.error(
            "END: Payee commission tracker query - Commission not available for user"
        )
        return {"error": "Commission not available for user"}
    index = 1
    result_monthly = {}
    for data in com_data.values():
        result_monthly[index] = data
        index += 1
    logger.info("Calculating qtd values")
    qtd_com = calculate_qtd_values(com_data)
    logger.info("END: Payee commission tracker query")
    return {"commission_payouts": result_monthly, "commission_payouts_qtd": qtd_com}


def get_commission_payout_for_year_for_payee(client_id, year, payee_id):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    end_dates_list = get_end_dates_list(int(year), start_month)
    return get_commission_payouts_for_payee(client_id, end_dates_list, payee_id)


def get_payout_status_for_payee_in_periods(
    client_id: int, payee_email: str, period_end_dates: list[datetime]
) -> list:
    """
    Given a payee email and list of period end dates, returns the payout status for those periods
    """
    payout_status_list = PayoutStatusAccessor(
        client_id
    ).get_payout_status_for_payee_in_periods(payee_email, period_end_dates)
    # TODO: [2023-03-01] @krishna - fix the return type - there should be a one to one correspondence
    # between the period_end_dates and the status' returned.  Something like - {"2023-01-01": "Active", "2023-02-01": "Unknown"}
    # Also, the status can be an enum
    return payout_status_list


def get_commission_payouts_for_payee(client_id, end_dates_list, payee_id):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    commission_data = CommissionAccessor(client_id).commissions_for_payee(payee_id)
    if len(commission_data) == 0:
        return {"error": "Commission not available for user"}

    data = {}
    payout_status_list = PayoutStatusAccessor(
        client_id
    ).get_payout_status_for_payee_in_periods(payee_id, end_dates_list)
    payout_status_map = {}
    for payout_status in payout_status_list:
        payout_status_map[payout_status.period_end_date] = payout_status
    for i in range(len(end_dates_list)):
        commission_per_month = 0
        payout_status = payout_status_map.get(end_dates_list[i])
        if payout_status:
            vp_per_month = payout_status.variable_pay_as_per_period
        else:
            vp_per_month, _ = get_variable_pay(client_id, payee_id, end_dates_list[i])
        if payout_status:
            payout_split_up = payout_status.payout_split_up
            fx_rate = payout_status.fx_rate
            if payout_split_up:
                commission_per_month = change_to_payee_currency_by_fx_rate(
                    payout_split_up.get("commission", 0),
                    fx_rate,
                )
                commission_per_month += Decimal(
                    payout_split_up.get("commission_adj", 0)
                )
                commission_per_month += Decimal(payout_split_up.get("draw_adj", 0))
        period = get_period(Freq.MONTHLY.value, i, start_month)
        data[period] = {
            "commission_amount": str(commission_per_month),
            "variable_pay": str(vp_per_month),
        }

    return data


def get_payee_commission_payout_for_curr_period(client_id, payee_id):
    client = get_client(client_id)
    curr_year = date.today().year
    curr_datetime = date(int(curr_year), date.today().month, 1)
    current_datetime = make_aware(datetime.now())
    all_payroll = EmployeePayrollAccessor(
        client_id
    ).get_all_payroll_till_current_payroll(current_datetime, payee_id)
    if len(all_payroll) == 0:
        logger.error("END: Payee commission current - Payroll details not present")
        return {"error": "Payroll details not present"}
    current_payroll = all_payroll[0]

    if not CommissionAccessor(client_id).does_commission_exist_for_payee(payee_id):
        logger.error(
            "END: Payee commission current - Commission not available for user"
        )
        return {"error": "Commission not available for user"}

    curr_freq = (
        current_payroll.payout_frequency.lower()
        if current_payroll and current_payroll.payout_frequency
        else None
    )
    curr_period = get_period_start_and_end_date(
        curr_time=curr_datetime,
        start_month=client.fiscal_start_month,
        time_prd=curr_freq,
        client_id=client_id,
    )
    curr_start_date = curr_period.get("start_date")
    prev_periods = get_previous_different_payroll_periods(
        current_psd=curr_start_date, no_of_prev_periods=2, all_payroll=all_payroll
    )
    curr_period["payout_frequency"] = curr_freq
    start_end_dates = [curr_period] + prev_periods
    data = {}
    end_dates = [date.get("end_date") for date in start_end_dates]
    payout_status_list = PayoutStatusAccessor(
        client_id
    ).get_payout_status_for_payee_in_periods(payee_id, end_dates)
    payout_status_map = {}

    for payout_status in payout_status_list:
        payout_status_map[payout_status.period_end_date] = payout_status
    for dates in start_end_dates:
        start_date = dates.get("start_date")
        end_date = dates.get("end_date")
        payout_status = payout_status_map.get(end_date)

        vp_per_month = 0
        commission_per_month = 0
        commission_amount = 0
        draw_adj = 0
        commission_adj = 0
        fx_rate = payout_status.fx_rate if payout_status else 1
        if payout_status and payout_status.payout_split_up:
            payout_split_up = payout_status.payout_split_up
            commission_amount = Decimal(payout_split_up.get("commission", 0))
            draw_adj = Decimal(payout_split_up.get("draw_adj", 0))
            commission_adj = Decimal(payout_split_up.get("commission_adj", 0))
            vp_per_month = payout_status.variable_pay_as_per_period
        else:
            vp_per_month, _ = get_variable_pay(client_id, payee_id, end_date, False)

        period = get_period_name_for_date(
            dates.get("payout_frequency"), end_date, client.fiscal_start_month
        )
        commission_per_month = change_to_payee_currency_by_fx_rate(
            commission_amount, fx_rate
        )
        commission_per_month += draw_adj
        commission_per_month += commission_adj
        data[period] = {
            "commission_amount": str(commission_per_month),
            "variable_pay": str(vp_per_month),
            "psd": start_date.strftime("%Y-%m-%d"),
            "ped": end_date.strftime("%Y-%m-%d"),
        }
    logger.info("END: Payee commission current")

    return data


def get_variable_pay(client_id, payee_id, cal_date, is_monthly=True):
    end_date = get_payee_payroll_date(client_id, payee_id, cal_date)
    payroll = (
        EmployeePayrollAccessor(client_id).get_variable_pay_for_given_date(
            end_date, cal_date, [payee_id]
        )
        if end_date
        else None
    )
    vp_per_month = 0
    variable_pay_per_period = 0
    payee_currency = None
    if payroll and len(payroll) > 0:
        payee_payroll = payroll[0]
        if payee_payroll:
            variable_pay_per_period = get_variable_pay_per_period(
                client_id,
                payee_payroll["variable_pay"],
                payee_payroll["payout_frequency"],
                end_date,
            )
            payee_currency = payee_payroll["pay_currency"]
            if is_valid_uuid(payee_payroll["payout_frequency"]) or not is_monthly:
                variable_pay_per_period = get_variable_pay_per_period(
                    client_id,
                    payee_payroll["variable_pay"],
                    payee_payroll["payout_frequency"],
                    end_date,
                )
                return round(variable_pay_per_period, 2), payee_currency

            # TODO: Remove this code once the default dashboard is decommissioned
            payout_frequency = payee_payroll["payout_frequency"]
            payout_frequency = payout_frequency.lower()
            vp_per_month = 0
            actual_variable_pay = payee_payroll["variable_pay"]

            if payout_frequency == Freq.MONTHLY.value:
                variable_pay = actual_variable_pay / 12
                vp_per_month = variable_pay

            if payout_frequency == Freq.QUARTERLY.value:
                variable_pay = actual_variable_pay / 4
                vp_per_month = (variable_pay / 3) if is_monthly else variable_pay

            if payout_frequency == Freq.HALFYEARLY.value:
                variable_pay = actual_variable_pay / 2
                vp_per_month = (variable_pay / 6) if is_monthly else variable_pay

            if payout_frequency == Freq.ANNUAL.value:
                variable_pay = actual_variable_pay
                vp_per_month = (variable_pay / 12) if is_monthly else variable_pay

    return round(vp_per_month, 2), payee_currency


def get_commission_buddy_data(client_id, payee_id):
    curr_month = date.today().month
    curr_year = date.today().year
    curr_date = date.today().day
    month_dates = calendar.monthrange(curr_year, curr_month)
    end_date = date(curr_year, curr_month, month_dates[1]).strftime("%Y-%m-%d")
    end_date = make_aware(end_of_day(parse(end_date, dayfirst=False)))
    vp = 0
    payroll = EmployeePayrollAccessor(client_id).get_variable_pay_for_given_date(
        end_date, end_date, [payee_id]
    )
    diff = None
    if payroll and len(payroll) > 0:
        payee_payroll = payroll[0]
        if payee_payroll:
            payout_frequency = payee_payroll["payout_frequency"]
            payout_frequency = payout_frequency.lower()
            actual_variable_pay = payee_payroll["variable_pay"]
            if payout_frequency == Freq.MONTHLY.value:
                variable_pay = actual_variable_pay / 12
                vp = variable_pay

            if payout_frequency == Freq.QUARTERLY.value:
                variable_pay = actual_variable_pay / 4
                vp = variable_pay / 3

            if payout_frequency == Freq.HALFYEARLY.value:
                variable_pay = actual_variable_pay / 2
                vp = variable_pay / 6

            if payout_frequency == Freq.ANNUAL.value:
                variable_pay = actual_variable_pay
                vp = variable_pay / 12

            start_end_dates = get_period_start_and_end_date(
                date.today(),
                get_client(client_id).fiscal_start_month,
                payout_frequency.lower(),
                client_id=client_id,
            )
            end_date = start_end_dates["end_date"]
            diff = end_date - datetime.now(timezone.utc)
    else:
        logger.error("END: Commission buddy query - Payroll not available")
        return {"error": "Payroll not available"}

    target = (vp / month_dates[1]) * curr_date
    logger.info("END: Commission buddy query")
    return {
        "target": str(target),
        "days_away": diff.days if diff else 0,
        "variable_pay": str(vp),
    }


def get_commission_structure(client_id, payee_id):
    variable_pay = 0
    payout_frequency = None
    curr_month = date.today().month
    curr_year = date.today().year
    month_dates = calendar.monthrange(curr_year, curr_month)
    end_date = date(curr_year, curr_month, month_dates[1]).strftime("%Y-%m-%d")
    end_date = make_aware(end_of_day(parse(end_date, dayfirst=False)))
    payroll = EmployeePayrollAccessor(client_id).get_variable_pay_for_given_ed_date(
        end_date, [payee_id]
    )
    if payroll and len(payroll) > 0:
        payout_frequency = payroll[0]["payout_frequency"]
        payout_frequency = payout_frequency.lower()
        actual_variable_pay = payroll[0]["variable_pay"]

        if payout_frequency == Freq.MONTHLY.value:
            variable_pay = actual_variable_pay / 12
        elif payout_frequency == Freq.QUARTERLY.value:
            variable_pay = actual_variable_pay / 4
        elif payout_frequency == Freq.HALFYEARLY.value:
            variable_pay = actual_variable_pay / 2
        elif payout_frequency == Freq.ANNUAL.value:
            variable_pay = actual_variable_pay
    else:
        logger.error("END: Commission structure query - Payroll not available")
        return {"error": "Payroll not available"}

    logger.info("END: Commission structure query")
    return {
        "variable_pay": str(variable_pay),
        "payout_frequency": payout_frequency.capitalize() if payout_frequency else None,
        "quota_period": None,
        "quota_amount": None,
    }


def get_drs_payout_status(client_id, payee_id):
    raised_by_me_open_assigned = DrsAccessor(client_id).get_assigned_to_user_count(
        payee_id
    )
    today = date.today()
    first = today.replace(day=1)
    last_month = first - timedelta(days=1)
    end_date = make_aware(
        end_of_day(parse(last_month.strftime("%Y-%m-%d"), dayfirst=False))
    )
    payout = PayoutAccessor(client_id).get_payout_for_payee_and_date(payee_id, end_date)
    period = end_date.strftime("%B")[0:3] + " - " + str(end_date.year)

    if payout and payout.is_paid:
        payout_status = get_localized_message_service(
            DashboardMessages.PAYOUT_PROCESSED.value, client_id
        )
    else:
        lock = CommissionLockAccessor(client_id).get_payee_lock_in_end_date_lock_aware(
            end_date, payee_id
        )
        if lock and lock.is_locked:
            payout_status = get_localized_message_service(
                DashboardMessages.PAYOUT_LOCKED.value, client_id
            )
        else:
            payout_status = get_localized_message_service(
                DashboardMessages.PAYOUT_OPEN.value, client_id
            )

    logger.info("END: DRS payout status")
    return {
        "tickets": raised_by_me_open_assigned,
        "payout_status": payout_status,
        "period": period,
    }


def get_current_end_date_list():
    end_dates_list = []
    curr_month = date.today().month
    curr_year = date.today().year
    if curr_month == 1:
        last_month = 12
        last_month_year = curr_year - 1
    else:
        last_month = curr_month - 1
        last_month_year = curr_year
    month_dates = calendar.monthrange(curr_year, curr_month)
    end_date = date(curr_year, curr_month, month_dates[1]).strftime("%Y-%m-%d")
    end_dates_list.append(make_aware(end_of_day(parse(end_date, dayfirst=False))))
    month_dates = calendar.monthrange(last_month_year, last_month)
    end_date = date(last_month_year, last_month, month_dates[1]).strftime("%Y-%m-%d")
    end_dates_list.append(make_aware(end_of_day(parse(end_date, dayfirst=False))))
    return end_dates_list


def get_payee_currency_symbol(client_id, payee_id, year, term_end_date=None):
    year_end_date = date(int(year), 12, 31).strftime("%Y-%m-%d")
    search_term = (
        last_day_of_month(term_end_date)
        if term_end_date
        else make_aware(end_of_day(parse(year_end_date)))
    )
    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
        search_term, [payee_id]
    )
    country = (
        CountriesAccessor(client_id).get_currency_symbol(payroll[0]["pay_currency"])
        if len(payroll) > 0
        else None
    )
    symbol = country.currency_symbol if country else "$"
    logger.info("END: Payee currency symbol")
    return symbol


def get_variable_pay_for_given_date(
    client_id, knowledge_date, end_date, employee_email_ids
):
    return EmployeePayrollAccessor(client_id).get_variable_pay_for_given_date(
        kd=knowledge_date, ed=end_date, employee_email_ids=employee_email_ids
    )


def get_employee_payroll(client_id, end_date, employee_email_ids, as_dicts=True):
    return EmployeePayrollAccessor(client_id).get_employee_payroll(
        date=end_date, employee_email_ids=employee_email_ids, as_dicts=as_dicts
    )
