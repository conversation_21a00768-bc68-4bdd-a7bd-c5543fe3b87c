from datetime import date, datetime

import pandas as pd
from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.commission_accessor import QuotaErosionAccessor
from commission_engine.services.commission_calculation_service.quota_calculator import (
    get_prev_prds,
)
from commission_engine.utils import (
    find_period,
    get_first_month_of_fiscal_year,
    get_fiscal_start_end_dates,
    get_fiscal_year,
    get_period_start_and_end_date,
    make_aware_wrapper,
)
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import (
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.quota_acessors import EmployeeQuotaAccessor
from spm.services.dashboard_services.dashboard_services import get_period_name_for_date
from spm.services.dashboard_services.quota_dashboard_services import (
    calculate_ytd_cum_qe_for_payee,
    calculate_ytd_values_for_cum_qe,
    get_end_dates_list,
    get_quota_attainment_details_all,
    get_run_rate_dict,
    get_start_end_for_year,
)
from spm.services.team_services.membership_services import flatten_hierarchy_team

logger = LogWithContext()


def get_quota_attainment_for_payee_year(client_id, year, payee_id, request_id):
    client = get_client(client_id)
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    start_month = client.fiscal_start_month - 1
    role = get_user_role(client_id, payee_id, year)
    logger.update_context(
        {
            "request_id": request_id,
            "client_id": client_id,
            "payee_id": payee_id,
            "year": year,
        }
    )

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:

        # For manager payee
        if role == "Manager":
            logger.info("Getting latest team quota for manager")
            team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
                payee_id, year, True, "Leader"
            )
            all_quotas = {}
            if len(team_quotas) > 0:
                logger.info(
                    "Getting {} quota attainment and rank for payee (manager) year for {}".format(
                        len(team_quotas), payee_id
                    )
                )

                for team_quota in team_quotas:
                    team_qa = {}
                    ytd_cum_qe = 0
                    ytd_qv = 0
                    quota_freq = team_quota.quota_type
                    category = team_quota.quota_category_name
                    schedule = team_quota.quota_schedule_type

                    end_date_list = get_end_dates_list(int(year), start_month, schedule)
                    period = get_period_start_and_end_date(
                        datetime.now(),
                        client.fiscal_start_month,
                        schedule.lower(),
                    )
                    current_period_index = (
                        end_date_list.index(period["end_date"])
                        if period["end_date"] in end_date_list
                        else len(end_date_list) - 1
                    )

                    for i in range(0, len(end_date_list)):
                        if i > current_period_index:
                            team_qa[str(i + 1)] = {
                                "team_qa": "0",
                                "cum_qe": "0",
                                "qv": "0",
                                "reportee_list": [],
                            }
                            continue
                        payroll = EmployeePayrollAccessor(
                            client_id
                        ).get_employee_payroll(end_date_list[i], [payee_id])
                        payout_frequency = (
                            payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                        )
                        prev_dates = (
                            get_prev_prds(
                                client.fiscal_start_month,
                                end_date_list[i],
                                payout_frequency.lower(),
                                schedule.lower(),
                            )
                            if payout_frequency
                            else None
                        )
                        quota_dates = [end_date_list[i]]
                        if prev_dates and len(prev_dates) > 1:
                            quota_dates.extend(prev_dates[1])

                        qv = float(team_quota.schedule_quota[i]["quota"])

                        quota_erosions = QuotaErosionAccessor(
                            client_id
                        ).get_quota_data_for_team_end_dates(
                            quota_dates,
                            payee_id,
                            category,
                        )
                        cum_qe = 0
                        qv = float(team_quota.schedule_quota[i]["quota"])
                        if len(quota_erosions) > 0:
                            for erosion in quota_erosions:
                                cum_qe += float(erosion.quota_erosion)
                            qv = float(quota_erosions[0].qv)
                            qa = (cum_qe / qv) * 100 if qv else 0
                            ytd_cum_qe += cum_qe
                        else:
                            qa = 0

                        ytd_qv += qv

                        team_qa[str(i + 1)] = {
                            "qa": str(qa),
                            "cum_qe": str(cum_qe),
                            "qv": str(qv),
                        }

                    curr_period = find_period(
                        datetime.today().month, start_month + 1, schedule.lower()
                    )
                    team_qa_qtd = {}

                    if schedule.lower() == "monthly" and quota_freq.lower() in [
                        "quarterly",
                        "monthly",
                    ]:
                        team_qa_qtd = calculate_ytd_values_for_cum_qe(team_qa)

                    qat = {
                        "qa": team_qa,
                        "curr_period": curr_period,
                        "ytd": str(
                            round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)
                        ),
                        "ytd_cum_qe": str(ytd_cum_qe),
                        "qa_qtd": team_qa_qtd,
                    }
                    all_quotas[category] = qat
                logger.info("END: Payee quota attainment tracker query")
                return all_quotas

            logger.error(
                "END: Payee quota attainment tracker query - Quota not available"
            )
            return {"error": "Quota not available"}

        # For employee payee

        logger.info("Getting latest employees or team quota")
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota(
            [payee_id],
            year,
            False,
            None,
            projection=[
                "quota_category_name",
                "quota_schedule_type",
                "schedule_quota",
                "quota_type",
            ],
        )
        all_quotas = {}
        if len(emp_quotas) > 0:
            logger.info(
                "Getting {} quota attainment and rank for payee year for {}".format(
                    len(emp_quotas), payee_id
                )
            )
            for emp_quota in emp_quotas:
                category = emp_quota["quota_category_name"]
                schedule = emp_quota["quota_schedule_type"]
                quota_freq = emp_quota["quota_type"]
                emp_qa = {}
                ytd_cum_qe = 0
                ytd_qv = 0
                end_date_list = get_end_dates_list(int(year), start_month, schedule)

                for i in range(0, len(end_date_list)):
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date_list[i],
                        [payee_id],
                        projection=["payout_frequency"],
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date_list[i],
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    quota_dates = [end_date_list[i]]
                    if prev_dates and len(prev_dates) > 1:
                        quota_dates.extend(prev_dates[1])

                    qv = float(emp_quota["schedule_quota"][i]["quota"])

                    quota_erosions = QuotaErosionAccessor(
                        client_id
                    ).get_quota_data_for_payee_end_dates(
                        quota_dates,
                        payee_id,
                        category,
                        projection=["quota_erosion", "qv"],
                    )
                    cum_qe = 0
                    qv = float(emp_quota["schedule_quota"][i]["quota"])
                    if len(quota_erosions) > 0:
                        for erosion in quota_erosions:
                            cum_qe += float(erosion["quota_erosion"])
                        qv = float(quota_erosions[0]["qv"])
                        qa = (cum_qe / qv) * 100 if qv else 0
                        ytd_cum_qe += cum_qe
                    else:
                        qa = 0

                    ytd_qv += qv

                    emp_qa[str(i + 1)] = {
                        "qa": str(qa),
                        "cum_qe": str(cum_qe),
                        "qv": str(qv),
                    }

                emp_qa_qtd = {}
                if schedule.lower() == "monthly" and quota_freq.lower() in [
                    "quarterly",
                    "monthly",
                ]:
                    emp_qa_qtd = calculate_ytd_values_for_cum_qe(emp_qa)

                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )
                qat = {
                    "qa": emp_qa,
                    "curr_period": curr_period,
                    "qa_qtd": emp_qa_qtd,
                    "ytd": str(round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)),
                    "ytd_cum_qe": str(ytd_cum_qe),
                }
                all_quotas[category] = qat
            return all_quotas

        logger.error("END: Payee quota attainment tracker query - Quota not available")
        return {"error": "Quota not available"}

    # Dashboard using Quota Attainment report for manager
    elif quota_dashboard_using_report and role == "Manager":
        logger.info("Getting latest team quota for manager")
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            payee_id, year, True, "Leader"
        )
        all_quotas = {}
        if len(team_quotas) > 0:
            logger.info(
                "Getting {} quota attainment and rank for payee (manager) year for {}".format(
                    len(team_quotas), payee_id
                )
            )

            start_end_date = get_fiscal_start_end_dates(
                fiscal_start_month=client.fiscal_start_month, year=int(year)
            )

            qa_details = get_quota_attainment_details_all(
                client_id=client_id,
                payee_id_list=[payee_id],
                type_of_quota="Team",
                start_date=start_end_date["start_date"].isoformat(),
                end_date=start_end_date["end_date"].isoformat(),
            )

            for team_quota in team_quotas:
                team_qa = {}
                ytd_cum_qe = 0
                ytd_qv = 0
                team_qa_for_qtd = {}
                quota_freq = team_quota.quota_type
                category = team_quota.quota_category_name
                schedule = team_quota.quota_schedule_type

                # If the schedule is monthly and the quota frequency is quarterly, we track the last run sync data for quarter
                last_run_sync_index = 1

                end_date_list = get_end_dates_list(int(year), start_month, schedule)
                period = get_period_start_and_end_date(
                    datetime.now(),
                    client.fiscal_start_month,
                    schedule.lower(),
                )
                current_period_index = (
                    end_date_list.index(period["end_date"])
                    if period["end_date"] in end_date_list
                    else len(end_date_list) - 1
                )

                for i in range(0, len(end_date_list)):
                    if i > current_period_index:
                        team_qa[str(i + 1)] = {
                            "team_qa": "0",
                            "cum_qe": "0",
                            "qv": "0",
                            "reportee_list": [],
                        }
                        team_qa_for_qtd[str(i + 1)] = {
                            "qa": "0",
                            "cum_qe": "0",
                            "qv": "0",
                        }
                        continue
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date_list[i], [payee_id]
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date_list[i],
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    quota_dates = [end_date_list[i]]
                    if prev_dates and len(prev_dates) > 1:
                        quota_dates.extend(prev_dates[1])

                    qv = float(team_quota.schedule_quota[i]["quota"])

                    quota_dates = [date.strftime("%Y-%m-%d") for date in quota_dates]
                    filtered_df = qa_details[
                        (qa_details["CATEGORY_NAME"] == category)
                        & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                    ]

                    if (
                        payout_frequency
                        and payout_frequency.lower() == "monthly"
                        and quota_freq.lower() == "quarterly"
                        and schedule.lower() == "monthly"
                    ):
                        # Check if it is the last month of the quarter or the current period
                        if (i + 1) % 3 == 0 or i == current_period_index:
                            # If sync was not run for that quarter end, then take the last run sync data
                            if filtered_df.empty:
                                cum_qe = float(
                                    team_qa[str(last_run_sync_index)]["cum_qe"]
                                )
                                qa = float(team_qa[str(last_run_sync_index)]["qa"])
                                cum_qe_qtd = cum_qe
                                qa_qtd = qa
                                ytd_cum_qe += cum_qe

                            # If sync was run for that quarter end, then take the latest data
                            else:
                                filtered_df["PED_TEMP"] = pd.to_datetime(
                                    filtered_df["PERIOD_END_DATE"]
                                )
                                filtered_df = filtered_df.sort_values(
                                    by="PED_TEMP", ascending=True
                                )
                                req_data = filtered_df.iloc[-1]
                                cum_qe = req_data["QUOTA_EROSION"]
                                qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                qv = req_data["QUOTA_VALUE"]
                                cum_qe_qtd = cum_qe
                                qa_qtd = qa
                                ytd_cum_qe += cum_qe

                            # Update the last run sync index to start of next quarter
                            last_run_sync_index = i + 2

                        # If it is not the last month of the quarter
                        elif filtered_df.empty:
                            cum_qe = 0
                            qa = 0
                            cum_qe_qtd = 0
                            qa_qtd = 0

                        else:
                            last_run_sync_index = i + 1
                            filtered_df["PED_TEMP"] = pd.to_datetime(
                                filtered_df["PERIOD_END_DATE"]
                            )
                            filtered_df = filtered_df.sort_values(
                                by="PED_TEMP", ascending=True
                            )
                            req_data = filtered_df.iloc[-1]
                            cum_qe = req_data["QUOTA_EROSION"]
                            qv = req_data["QUOTA_VALUE"]
                            qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                            cum_qe_qtd = 0
                            qa_qtd = 0

                    elif filtered_df.empty:
                        cum_qe = 0
                        qa = 0
                        cum_qe_qtd = 0
                        qa_qtd = 0

                    else:
                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        cum_qe = req_data["QUOTA_EROSION"]
                        qv = req_data["QUOTA_VALUE"]
                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                        ytd_cum_qe += cum_qe
                        cum_qe_qtd = cum_qe
                        qa_qtd = qa

                    team_qa_for_qtd[str(i + 1)] = {
                        "qa": str(qa_qtd),
                        "cum_qe": str(cum_qe_qtd),
                        "qv": str(qv),
                    }

                    ytd_qv += qv

                    team_qa[str(i + 1)] = {
                        "qa": str(qa),
                        "cum_qe": str(cum_qe),
                        "qv": str(qv),
                    }

                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )
                team_qa_qtd = {}

                # Calculate YTD Cum QE if the quota frequency is different from the schedule
                if (quota_freq.lower() != schedule.lower()) and not (
                    quota_freq.lower() == "quarterly" and schedule.lower() == "monthly"
                ):
                    quota_end_date_list = get_end_dates_list(
                        int(year), start_month, quota_freq
                    )
                    qa_period = get_period_start_and_end_date(
                        datetime.now(),
                        client.fiscal_start_month,
                        quota_freq.lower(),
                    )
                    qa_current_period_index = (
                        quota_end_date_list.index(qa_period["end_date"])
                        if qa_period["end_date"] in quota_end_date_list
                        else len(quota_end_date_list) - 1
                    )
                    quota_end_date_list = quota_end_date_list[:qa_current_period_index]
                    quota_end_date_list.append(period["end_date"])
                    ytd_cum_qe = calculate_ytd_cum_qe_for_payee(
                        quota_end_date_list=quota_end_date_list,
                        qa_details=qa_details,
                        category=category,
                    )

                if schedule.lower() == "monthly" and quota_freq.lower() in [
                    "quarterly",
                    "monthly",
                ]:
                    team_qa_qtd = calculate_ytd_values_for_cum_qe(team_qa_for_qtd)

                qat = {
                    "qa": team_qa,
                    "curr_period": curr_period,
                    "ytd": str(round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)),
                    "ytd_cum_qe": str(ytd_cum_qe),
                    "qa_qtd": team_qa_qtd,
                    "quota_category": quota_freq,
                }
                all_quotas[category] = qat
            logger.info("END: Payee quota attainment tracker query")
            return all_quotas

        logger.error("END: Payee quota attainment tracker query - Quota not available")
        return {"error": "Quota not available"}

    # Dashboard using Quota Attainment report for employee payee
    else:
        logger.info("Getting latest employees or team quota")
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota(
            [payee_id],
            year,
            False,
            None,
            projection=[
                "quota_category_name",
                "quota_schedule_type",
                "schedule_quota",
                "quota_type",
            ],
        )
        all_quotas = {}
        if len(emp_quotas) > 0:
            logger.info(
                "Getting {} quota attainment and rank for payee year for {}".format(
                    len(emp_quotas), payee_id
                )
            )
            start_end_date = get_fiscal_start_end_dates(
                fiscal_start_month=client.fiscal_start_month, year=int(year)
            )

            qa_details = get_quota_attainment_details_all(
                client_id=client_id,
                payee_id_list=[payee_id],
                type_of_quota="Individual",
                start_date=start_end_date["start_date"].isoformat(),
                end_date=start_end_date["end_date"].isoformat(),
            )

            for emp_quota in emp_quotas:
                category = emp_quota["quota_category_name"]
                schedule = emp_quota["quota_schedule_type"]
                quota_freq = emp_quota["quota_type"]
                emp_qa = {}
                emp_qa_for_qtd = {}
                ytd_cum_qe = 0
                ytd_qv = 0
                end_date_list = get_end_dates_list(int(year), start_month, schedule)

                # If the schedule is monthly and the quota frequency is quarterly, we track the last run sync data for quarter
                last_run_sync_index = 1

                for i in range(0, len(end_date_list)):
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date_list[i],
                        [payee_id],
                        projection=["payout_frequency"],
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date_list[i],
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    quota_dates = [end_date_list[i]]
                    if prev_dates and len(prev_dates) > 1:
                        quota_dates.extend(prev_dates[1])

                    qv = float(emp_quota["schedule_quota"][i]["quota"])

                    quota_dates = [date.strftime("%Y-%m-%d") for date in quota_dates]

                    filtered_df = qa_details[
                        (qa_details["CATEGORY_NAME"] == category)
                        & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                    ]

                    if (
                        payout_frequency
                        and payout_frequency.lower() == "monthly"
                        and quota_freq.lower() == "quarterly"
                        and schedule.lower() == "monthly"
                    ):
                        # Check if it is the last month of the quarter
                        if (i + 1) % 3 == 0:
                            # If sync was not run for that quarter end, then take the last run sync data
                            if filtered_df.empty:
                                cum_qe = float(
                                    emp_qa[str(last_run_sync_index)]["cum_qe"]
                                )
                                qa = float(emp_qa[str(last_run_sync_index)]["qa"])
                                cum_qe_qtd = cum_qe
                                qa_qtd = qa
                                ytd_cum_qe += cum_qe
                            # If sync was run for that quarter end, then take the latest data
                            else:
                                filtered_df["PED_TEMP"] = pd.to_datetime(
                                    filtered_df["PERIOD_END_DATE"]
                                )
                                filtered_df = filtered_df.sort_values(
                                    by="PED_TEMP", ascending=True
                                )
                                req_data = filtered_df.iloc[-1]
                                cum_qe = req_data["QUOTA_EROSION"]
                                qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                qv = req_data["QUOTA_VALUE"]
                                cum_qe_qtd = cum_qe
                                qa_qtd = qa
                                ytd_cum_qe += cum_qe

                            # Update the last run sync index to start of next quarter
                            last_run_sync_index = i + 2

                        # If it is not the last month of the quarter
                        elif filtered_df.empty:
                            cum_qe = 0
                            qa = 0
                            cum_qe_qtd = 0
                            qa_qtd = 0
                        else:
                            last_run_sync_index = i + 1
                            filtered_df["PED_TEMP"] = pd.to_datetime(
                                filtered_df["PERIOD_END_DATE"]
                            )
                            filtered_df = filtered_df.sort_values(
                                by="PED_TEMP", ascending=True
                            )
                            req_data = filtered_df.iloc[-1]
                            cum_qe = req_data["QUOTA_EROSION"]
                            qv = req_data["QUOTA_VALUE"]
                            qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                            cum_qe_qtd = 0
                            qa_qtd = 0

                    elif filtered_df.empty:
                        cum_qe = 0
                        qa = 0
                        cum_qe_qtd = 0
                        qa_qtd = 0

                    else:
                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        cum_qe = req_data["QUOTA_EROSION"]
                        qv = req_data["QUOTA_VALUE"]
                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                        cum_qe_qtd = cum_qe
                        qa_qtd = qa
                        ytd_cum_qe += cum_qe

                    emp_qa_for_qtd[str(i + 1)] = {
                        "qa": str(qa_qtd),
                        "cum_qe": str(cum_qe_qtd),
                        "qv": str(qv),
                    }

                    ytd_qv += qv

                    emp_qa[str(i + 1)] = {
                        "qa": str(qa),
                        "cum_qe": str(cum_qe),
                        "qv": str(qv),
                    }

                emp_qa_qtd = {}

                if schedule.lower() == "monthly" and quota_freq.lower() in [
                    "quarterly",
                    "monthly",
                ]:
                    emp_qa_qtd = calculate_ytd_values_for_cum_qe(emp_qa_for_qtd)

                if (quota_freq.lower() != schedule.lower()) and not (
                    quota_freq.lower() == "quarterly" and schedule.lower() == "monthly"
                ):
                    quota_end_date_list = get_end_dates_list(
                        int(year), start_month, quota_freq
                    )
                    ytd_cum_qe = calculate_ytd_cum_qe_for_payee(
                        quota_end_date_list=quota_end_date_list,
                        qa_details=qa_details,
                        category=category,
                    )

                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )
                qat = {
                    "qa": emp_qa,
                    "curr_period": curr_period,
                    "qa_qtd": emp_qa_qtd,
                    "ytd": str(round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)),
                    "ytd_cum_qe": str(ytd_cum_qe),
                }
                all_quotas[category] = qat
            return all_quotas

        logger.error("END: Payee quota attainment tracker query - Quota not available")
        return {"error": "Quota not available"}


def calculate_payee_quota_rank(
    client_id, payee_id, payee_qa, qa_date, schedule, category
):
    client = get_client(client_id)
    rank = -1
    hierarchy = HierarchyAccessor(client_id).get_employee_hierarchy(qa_date, [payee_id])
    reportee_qa_list = [payee_qa]
    if hierarchy and len(hierarchy) > 0:
        manager_id = hierarchy[0]["reporting_manager_email_id"]
        dir_reportees = HierarchyAccessor(client_id).get_reportees_email_id(
            qa_date, manager_id
        )
        for reportee in dir_reportees:
            if reportee["employee_email_id"] != payee_id:
                payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                    qa_date, [payee_id]
                )
                payout_frequency = (
                    payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                )
                prev_dates = (
                    get_prev_prds(
                        client.fiscal_start_month,
                        qa_date,
                        payout_frequency.lower(),
                        schedule.lower(),
                    )
                    if payout_frequency
                    else None
                )
                quota_dates = [qa_date]
                if prev_dates and len(prev_dates) > 1:
                    quota_dates.extend(prev_dates[1])
                quota_erosions_for_employees = QuotaErosionAccessor(
                    client_id
                ).get_quota_data_for_payee_end_dates(
                    quota_dates, reportee["employee_email_id"], category
                )
                if len(quota_erosions_for_employees) > 0:
                    cum_qe = 0
                    for erosion in quota_erosions_for_employees:
                        cum_qe = cum_qe + erosion.quota_erosion
                    emp_qa = (
                        cum_qe / quota_erosions_for_employees[0].qv
                        if quota_erosions_for_employees[0].qv
                        else 0
                    )
                    emp_qa = emp_qa * 100
                    reportee_qa_list.append(emp_qa)

        reportee_qa_list.sort(reverse=True)
        rank = reportee_qa_list.index(payee_qa) + 1
    return {"rank": rank, "desc_ordered_qa_list": reportee_qa_list}


def get_employee_quotas_by_role(client_id, payee_id, role=None):
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month)
    if role is None:
        role = get_user_role(client_id, payee_id, fiscal_year)
    if role == "Manager":
        quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            payee_id, fiscal_year, True, "Leader"
        )
    else:
        quotas = EmployeeQuotaAccessor(client_id).get_latest_employees_or_teams_quota(
            [payee_id], fiscal_year, False, None
        )
    return quotas


def get_employee_quotas(client_id, payee_id):
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month)

    team_quotas = EmployeeQuotaAccessor(client_id).get_latest_employees_or_teams_quota(
        [payee_id], fiscal_year, True, "Leader"
    )

    ind_quotas = EmployeeQuotaAccessor(client_id).get_latest_employees_or_teams_quota(
        [payee_id], fiscal_year, False, None
    )

    return team_quotas + ind_quotas


def get_payee_quota_attainment_for_current_period(client_id, payee_id, kd=None):
    client = get_client(client_id)
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    fiscal_year = get_fiscal_year(client.fiscal_start_month)
    if kd is None:
        curr_year = date.today().year
        curr_month = date.today().month
    else:
        curr_year = kd.year
        curr_month = kd.month
    role = get_user_role(client_id, payee_id, fiscal_year)

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:

        # For manager payee
        if role == "Manager":
            team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
                payee_id, fiscal_year, True, "Leader"
            )
            all_quotas = {}
            if len(team_quotas) > 0:
                logger.info(
                    "Getting {} payee quota attainment for current period for {}".format(
                        len(team_quotas), payee_id
                    )
                )
                for team_quota in team_quotas:
                    category = team_quota.quota_category_name
                    schedule = team_quota.quota_schedule_type
                    d = date(int(curr_year), curr_month, 1)
                    start_end_dates = get_period_start_and_end_date(
                        d,
                        client.fiscal_start_month,
                        schedule.lower(),
                        prev_required=True,
                    )
                    team_qa = {}
                    for i in range(0, 2):
                        if i == 0:
                            end_date = start_end_dates["prev_end_date"]
                        else:
                            end_date = start_end_dates["end_date"]
                        payroll = EmployeePayrollAccessor(
                            client_id
                        ).get_employee_payroll(end_date, [payee_id])
                        payout_frequency = (
                            payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                        )
                        prev_dates = (
                            get_prev_prds(
                                client.fiscal_start_month,
                                end_date,
                                payout_frequency.lower(),
                                schedule.lower(),
                            )
                            if payout_frequency
                            else None
                        )
                        end_dates = [end_date]
                        if prev_dates and len(prev_dates) > 1:
                            end_dates.extend(prev_dates[1])

                        quota_erosions = QuotaErosionAccessor(
                            client_id
                        ).get_quota_data_for_team_end_dates(
                            end_dates,
                            payee_id,
                            category,
                        )
                        if len(quota_erosions) > 0:
                            cum_qe = 0
                            for erosion in quota_erosions:
                                cum_qe = cum_qe + erosion.quota_erosion
                            qa = (
                                (cum_qe / quota_erosions[0].qv) * 100
                                if quota_erosions[0].qv
                                else 0
                            )
                        else:
                            qa = 0

                        period = get_period_name_for_date(
                            schedule.lower(), end_date, client.fiscal_start_month
                        )
                        team_qa[period] = str(qa)
                    all_quotas[category] = team_qa
                logger.info("END: Payee quota attainment for current period query")
                return all_quotas

            logger.error(
                "END: Payee quota attainment for current period query - Quota not available"
            )
            return {"error": "Quota not available"}

        # For employee payee
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota([payee_id], fiscal_year, False, None)
        if len(emp_quotas) > 0:
            logger.info(
                "Getting {} payee quota attainment for current period for {}".format(
                    len(emp_quotas), payee_id
                )
            )
            all_quotas = {}
            for emp_quota in emp_quotas:
                category = emp_quota.quota_category_name
                schedule = emp_quota.quota_schedule_type
                emp_qa = {}
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d,
                    client.fiscal_start_month,
                    schedule.lower(),
                    prev_required=True,
                )
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date, [payee_id]
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date,
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    end_dates = [end_date]
                    if prev_dates and len(prev_dates) > 1:
                        end_dates.extend(prev_dates[1])

                    quota_erosions = QuotaErosionAccessor(
                        client_id
                    ).get_quota_data_for_payee_end_dates(end_dates, payee_id, category)
                    if len(quota_erosions) > 0:
                        cum_qe = 0
                        for erosion in quota_erosions:
                            cum_qe = cum_qe + erosion.quota_erosion
                        qa = (
                            (cum_qe / quota_erosions[0].qv) * 100
                            if quota_erosions[0].qv
                            else 0
                        )
                    else:
                        qa = 0

                    period = get_period_name_for_date(
                        schedule.lower(), end_date, client.fiscal_start_month
                    )
                    emp_qa[period] = str(qa)
                all_quotas[category] = emp_qa
            logger.info("END: Payee quota attainment for current period query")
            return all_quotas

        logger.error(
            "END: Payee quota attainment for current period query - Quota not available"
        )
        return {"error": "Quota not available"}

    # Dashboard using Quota Attainment report for manager
    elif quota_dashboard_using_report and role == "Manager":
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            payee_id, fiscal_year, True, "Leader"
        )
        all_quotas = {}
        if len(team_quotas) > 0:
            logger.info(
                "Getting {} payee quota attainment for current period for {}".format(
                    len(team_quotas), payee_id
                )
            )
            start_end_date = get_fiscal_start_end_dates(client.fiscal_start_month)

            qa_details = get_quota_attainment_details_all(
                client_id=client_id,
                payee_id_list=[payee_id],
                type_of_quota="Team",
                start_date=start_end_date["start_date"].isoformat(),
                end_date=start_end_date["end_date"].isoformat(),
            )

            for team_quota in team_quotas:
                category = team_quota.quota_category_name
                schedule = team_quota.quota_schedule_type
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d, client.fiscal_start_month, schedule.lower(), prev_required=True
                )
                team_qa = {}
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date, [payee_id]
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date,
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    end_dates = [end_date]
                    if prev_dates and len(prev_dates) > 1:
                        end_dates.extend(prev_dates[1])

                    end_dates = [date.strftime("%Y-%m-%d") for date in end_dates]
                    filtered_df = qa_details[
                        (qa_details["CATEGORY_NAME"] == category)
                        & (qa_details["PERIOD_END_DATE"].isin(end_dates))
                    ]
                    if filtered_df.empty:
                        cum_qe = 0
                        qa = 0

                    else:
                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        cum_qe = req_data["QUOTA_EROSION"]
                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]

                    period = get_period_name_for_date(
                        schedule.lower(), end_date, client.fiscal_start_month
                    )
                    team_qa[period] = str(qa)
                all_quotas[category] = team_qa
            logger.info("END: Payee quota attainment for current period query")
            return all_quotas

        logger.error(
            "END: Payee quota attainment for current period query - Quota not available"
        )
        return {"error": "Quota not available"}

    # Dashboard using Quota Attainment report for regular payee
    else:
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota([payee_id], fiscal_year, False, None)
        if len(emp_quotas) > 0:
            logger.info(
                "Getting {} payee quota attainment for current period for {}".format(
                    len(emp_quotas), payee_id
                )
            )
            start_end_date = get_fiscal_start_end_dates(client.fiscal_start_month)

            qa_details = get_quota_attainment_details_all(
                client_id=client_id,
                payee_id_list=[payee_id],
                type_of_quota="Individual",
                start_date=start_end_date["start_date"].isoformat(),
                end_date=start_end_date["end_date"].isoformat(),
            )

            all_quotas = {}
            for emp_quota in emp_quotas:
                category = emp_quota.quota_category_name
                schedule = emp_quota.quota_schedule_type
                emp_qa = {}
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d, client.fiscal_start_month, schedule.lower(), prev_required=True
                )
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date, [payee_id]
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date,
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    end_dates = [end_date]
                    if prev_dates and len(prev_dates) > 1:
                        end_dates.extend(prev_dates[1])

                    end_dates = [date.strftime("%Y-%m-%d") for date in end_dates]

                    filtered_df = qa_details[
                        (qa_details["CATEGORY_NAME"] == category)
                        & (qa_details["PERIOD_END_DATE"].isin(end_dates))
                    ]
                    if filtered_df.empty:
                        cum_qe = 0
                        qa = 0

                    else:
                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        cum_qe = req_data["QUOTA_EROSION"]
                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]

                    period = get_period_name_for_date(
                        schedule.lower(), end_date, client.fiscal_start_month
                    )
                    emp_qa[period] = str(qa)
                all_quotas[category] = emp_qa
            logger.info("END: Payee quota attainment for current period query")
            return all_quotas

        logger.error(
            "END: Payee quota attainment for current period query - Quota not available"
        )
        return {"error": "Quota not available"}


def get_own_leader_team_and_members(client_id, payee_id):
    flat_team = flatten_hierarchy_team(client_id, None, payee_id)
    # If the payee is not in any team, return empty. It might occur when the payee is
    # neither reporting to any employee nor any employee is reporting to the payee.
    if payee_id not in [team["team_owner_email_id"] for team in flat_team]:
        return {}

    data = {}
    logger.info("Getting own team and members for {} flat team".format(len(flat_team)))
    for team in flat_team:
        reportee_list = []
        time = timezone.now()
        dir_reportees = HierarchyAccessor(client_id).get_reportees_email_id(
            time, team["team_owner_email_id"]
        )
        for reportee in dir_reportees:
            reportee_list.append({"employee_email_id": reportee["employee_email_id"]})
        data[team["team_owner_email_id"]] = reportee_list
    logger.info("END: Own team direct and members query")
    return data


def get_own_leader_team_and_members_for_category(
    client_id, payee_id, year, quota_category
):
    time = timezone.now()
    emp_quotas = EmployeeQuotaAccessor(client_id).get_employee_quota_for_year(
        quota_category, time, year, False
    )
    emp_quota_dict = {x["employee_email_id"]: x for x in emp_quotas}
    flat_team = flatten_hierarchy_team(client_id, None, payee_id)
    data = {}
    logger.info(
        "Getting own leader team and members for category for {} flat teams".format(
            len(flat_team)
        )
    )
    for team in flat_team:
        reportee_list = []
        dir_reportees = HierarchyAccessor(client_id).get_reportees_email_id(
            time, team["team_owner_email_id"]
        )
        for reportee in dir_reportees:
            emp_quota = (
                emp_quota_dict[reportee["employee_email_id"]]
                if reportee["employee_email_id"] in emp_quota_dict
                else None
            )
            period = emp_quota["quota_schedule_type"] if emp_quota else None
            reportee_list.append(
                {
                    "employee_email_id": reportee["employee_email_id"],
                    "period": period,
                }
            )
        data[team["team_owner_email_id"]] = reportee_list
    logger.info("END: Own team direct members quota category")
    return data


def get_payee_quota_burn_down(client_id, payee_id, year):
    role = get_user_role(client_id, payee_id, year)
    if role == "Manager":
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            payee_id, year, True, "Leader"
        )
        logger.info(
            "Getting payee quota burn down for {} quotas".format(len(team_quotas))
        )
        if len(team_quotas) > 0:
            all_quotas = {}
            for team_quota in team_quotas:
                category = team_quota.quota_category_name
                schedule = team_quota.quota_schedule_type
                team_run_rate = get_quota_burn_down(
                    client_id,
                    payee_id,
                    year,
                    schedule.lower(),
                    category,
                    True,
                )
                all_quotas[category] = team_run_rate
            logger.info("END: Payee quota burn down query")
            return all_quotas
        else:
            logger.error("END: Payee quota burn down query - Quota not available")
            return {"error": "Quota not available"}
    else:
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota([payee_id], year, False, None)
        if len(emp_quotas) > 0:
            all_quotas = {}
            logger.info(
                "Getting payee quota burn down for {} quotas".format(len(emp_quotas))
            )
            for emp_quota in emp_quotas:
                category = emp_quota.quota_category_name
                schedule = emp_quotas[0].quota_schedule_type
                team_run_rate = get_quota_burn_down(
                    client_id,
                    payee_id,
                    year,
                    schedule.lower(),
                    category,
                    False,
                )
                all_quotas[category] = team_run_rate
            logger.info("END: Payee quota burn down query")
            return all_quotas
        else:
            logger.error("END: Payee quota burn down query - Quota not available")
            return {"error": "Quota not available"}


def get_quota_burn_down(client_id, payee_id, year, schedule, category, is_team):
    client = get_client(client_id)
    d = date(int(year), client.fiscal_start_month, 1)
    year_start_end_dates = get_period_start_and_end_date(
        d, client.fiscal_start_month, "annual"
    )
    qbd = get_run_rate_dict(schedule)
    quota_erosion = QuotaErosionAccessor(
        client_id
    ).get_all_quota_erosion_for_payee_or_team(
        payee_id,
        is_team,
        year_start_end_dates["start_date"],
        year_start_end_dates["end_date"],
        category,
        True,
    )
    data_frame_qe = pd.DataFrame(quota_erosion)
    if not data_frame_qe.empty:
        period_date_group = data_frame_qe.groupby(
            ["period_start_date", "period_end_date"]
        )
        for key, _value in period_date_group:
            sub_data_frame = period_date_group.get_group(key)
            if len(sub_data_frame) > 0:
                ped = sub_data_frame.iloc[0]["period_end_date"]
                period_details = find_period(
                    ped.month, client.fiscal_start_month, schedule.lower()
                )
                period = period_details["index"] + 1
                kd_group = sub_data_frame.groupby(
                    sub_data_frame["knowledge_begin_date"].dt.date
                )
                for kd, group in kd_group:
                    max_date = group.agg({"knowledge_begin_date": "max"})
                    records = group.loc[
                        group["knowledge_begin_date"]
                        == max_date["knowledge_begin_date"]
                    ]
                    qe_sum = 0
                    cum_qe = 0
                    qv = 0
                    for ind in records.index:
                        qe_sum = qe_sum + records["quota_erosion"][ind]
                        cum_qe = records["cumulative_qe"][ind]
                        qv = records["qv"][ind]
                    qa = ((qe_sum + cum_qe) / qv) * 100 if qv else 0
                    qa = 100 - qa
                    qbd[str(period)][str(kd)] = str(qa)
        curr_period = find_period(
            datetime.today().month, client.fiscal_start_month, schedule.lower()
        )
        burn_down = {"qbd": qbd, "curr_period": curr_period}
        return burn_down
    else:
        return {"error": "Quota not available"}


def get_user_role(client_id, payee_id, year):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    year_dates = get_start_end_for_year(int(year), start_month)
    hierarchy = HierarchyAccessor(client_id).get_employee_hierarchy_for_period(
        year_dates[0], year_dates[1], payee_id
    )
    if len(hierarchy) > 0:
        logger.info("END: User role query")
        return "Manager"
    else:
        logger.info("END: User role query")
        return "Payee"


def get_payee_quota_structure_current_period(client_id, payee_id):
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month)
    curr_year = date.today().year
    curr_month = date.today().month
    role = get_user_role(client_id, payee_id, fiscal_year)
    current_fiscal_yr_start_date = make_aware_wrapper(
        datetime(
            get_first_month_of_fiscal_year(client.fiscal_start_month, int(fiscal_year)),
            client.fiscal_start_month,
            1,
        )
    )
    if role == "Manager":
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            payee_id, fiscal_year, True, "Leader"
        )
        all_quotas = {}
        logger.info(
            "Getting payee quota structure of current period for {} quotas".format(
                len(team_quotas)
            )
        )
        if len(team_quotas) > 0:
            for team_quota in team_quotas:
                category = team_quota.quota_category_name
                schedule = team_quota.quota_schedule_type
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d, client.fiscal_start_month, schedule.lower(), prev_required=True
                )
                team_qa = {}
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    if end_date > current_fiscal_yr_start_date:
                        act_period = find_period(
                            end_date.month,
                            client.fiscal_start_month,
                            schedule.lower(),
                        )
                        index = act_period["index"] if "index" in act_period else 0
                        period = get_period_name_for_date(
                            schedule.lower(),
                            end_date,
                            client.fiscal_start_month,
                        )
                        quota_amount = team_quota.schedule_quota
                        team_qa[period] = {
                            "period": schedule,
                            "amount": quota_amount[index],
                        }
                all_quotas[category] = team_qa
            logger.info("END: Payee current quota structure query")
            return all_quotas
        else:
            logger.error(
                "END: Payee current quota structure query - Quota not available"
            )
            team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
                payee_id, int(fiscal_year) - 1, True, "Leader"
            )
            if len(team_quotas) > 0:
                logger.error(
                    "END: Payee current quota structure query - Quota available for previous year"
                )
                return {"error": "Quota available for previous year"}
            return {"error": "Quota not available"}
    else:
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota([payee_id], fiscal_year, False, None)
        logger.info(
            "Getting payee quota structure of current period for {} quotas".format(
                len(emp_quotas)
            )
        )
        if len(emp_quotas) > 0:
            all_quotas = {}
            for emp_quota in emp_quotas:
                category = emp_quota.quota_category_name
                schedule = emp_quota.quota_schedule_type
                emp_qa = {}
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d, client.fiscal_start_month, schedule.lower(), prev_required=True
                )
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    if end_date > current_fiscal_yr_start_date:
                        if i != 0 or schedule.lower() != "annual":
                            act_period = find_period(
                                end_date.month,
                                client.fiscal_start_month,
                                schedule.lower(),
                            )
                            index = act_period["index"] if "index" in act_period else 0
                            period = get_period_name_for_date(
                                schedule.lower(),
                                end_date,
                                client.fiscal_start_month,
                            )
                            quota_amount = emp_quota.schedule_quota
                            emp_qa[period] = {
                                "period": schedule,
                                "amount": quota_amount[index],
                            }

                all_quotas[category] = emp_qa
            logger.info("END: Payee current quota structure query")
            return all_quotas
        else:
            logger.error(
                "END: Payee current quota structure query - Quota not available"
            )
            emp_quotas = EmployeeQuotaAccessor(
                client_id
            ).get_latest_employees_or_teams_quota(
                [payee_id], int(fiscal_year) - 1, False, None
            )
            if len(emp_quotas) > 0:
                logger.error(
                    "END: Payee current quota structure query - Quota available for previous year"
                )
                return {"error": "Quota available for previous year"}
            return {"error": "Quota not available"}
