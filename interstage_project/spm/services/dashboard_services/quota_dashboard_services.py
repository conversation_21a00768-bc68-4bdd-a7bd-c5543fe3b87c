import decimal
import logging
from copy import deepcopy
from datetime import date, datetime, timedelta

import pandas as pd
from django.utils import timezone
from pandas import date_range

from commission_engine.accessors import client_accessor
from commission_engine.accessors.client_accessor import *
from commission_engine.accessors.commission_accessor import QuotaErosionAccessor
from commission_engine.database.snowflake_query_utils import snowflake_writer_handler
from commission_engine.services.commission_calculation_service.quota_calculator import (
    get_prev_prds,
    get_prev_prds_diff_payout_freq,
)
from commission_engine.snowflake_accessors.utils import escape_single_quotes
from commission_engine.utils import (
    end_of_day,
    find_period,
    get_fiscal_start_end_dates,
    get_fiscal_year,
    get_period_start_and_end_date,
    start_of_day,
)
from commission_engine.utils.general_data import Freq, RbacPermissions, TimePeriodWt
from commission_engine.utils.report_utils import get_report_object_data_table_name
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.quota_acessors import EmployeeQuotaAccessor
from spm.services.dashboard_services.dashboard_services import (
    calendar,
    get_end_date_of_month,
    get_period_name_for_date,
    make_aware,
    parse,
)
from spm.services.rbac_services import get_ui_permissions
from spm.services.team_services.membership_services import (
    get_manager_details_with_filters,
    get_reportee_details_with_quota,
)

logger = LogWithContext()

logger_new = logging.getLogger(__name__)


def get_quota_attainment_details_all(
    client_id, payee_id_list, start_date, end_date, type_of_quota
):
    """
    This function is used to get the quota attainment details for a given payee_id and type_of_quota of a client within that fiscal year

    client_id: Client ID
    payee_id_list: List of payee email id
    start_date: Start date of the fiscal year
    end_date: End date of the fiscal year
    type_of_quota: Type of quota (Individual or Team or Both)

    """

    single_quote_escaped_values = [escape_single_quotes(val) for val in payee_id_list]
    payee_email_values_str = "'" + "','".join(single_quote_escaped_values) + "'"
    table_name = get_report_object_data_table_name(client_id, "quota_attainment")

    if type_of_quota == "Both":
        query_variables = [
            "client_id",
            "end_date",
            "start_date",
        ]
        query_params = [
            client_id,
            end_date,
            start_date,
        ]
        query = f"""
            select to_date(data:period_end_date::timestamp)::string as period_end_date, 
                data:attained as quota_erosion, 
                data:target as quota_value, 
                data:quota_category_name::string as category_name,
                data:quota_attainment_percentage as quota_attainment_percentage,
                data:payee_email_id::string as payee_email_id,
                data:individual_or_team_quota::string as individual_or_team_quota
            from {table_name} 
            where client_id = ?
            and object_id = 'quota_attainment'
            and knowledge_end_date is null
            and is_deleted = false
            and data:payee_email_id in ({payee_email_values_str})
            and data:period_end_date::datetime <= ?::datetime
            and data:period_start_date::datetime >= ?::datetime
        """
    else:
        query_variables = [
            "client_id",
            "type_of_quota",
            "end_date",
            "start_date",
        ]
        query_params = [
            client_id,
            type_of_quota,
            end_date,
            start_date,
        ]
        query = f"""
            select to_date(data:period_end_date::timestamp)::string as period_end_date, 
                data:attained as quota_erosion, 
                data:target as quota_value, 
                data:quota_category_name::string as category_name,
                data:quota_attainment_percentage as quota_attainment_percentage,
                data:payee_email_id::string as payee_email_id,
                data:individual_or_team_quota::string as individual_or_team_quota
            from {table_name}
            where client_id = ?
            and object_id = 'quota_attainment'
            and knowledge_end_date is null
            and is_deleted = false
            and data:individual_or_team_quota = ?::string
            and data:payee_email_id in ({payee_email_values_str})
            and data:period_end_date::datetime <= ?::datetime
            and data:period_start_date::datetime >= ?::datetime
        """

    logger.info(f"Query variables for the query are {query_variables}")
    logger.info(f"Constructed qa details query -> {query}")

    with snowflake_writer_handler(client_id=client_id) as snowpark_session:
        qa_report_data = snowpark_session.sql(query, tuple(query_params)).to_pandas()

    qa_report_data["QUOTA_EROSION"] = qa_report_data["QUOTA_EROSION"].astype(float)
    qa_report_data["QUOTA_VALUE"] = qa_report_data["QUOTA_VALUE"].astype(float)
    qa_report_data["QUOTA_ATTAINMENT_PERCENTAGE"] = qa_report_data[
        "QUOTA_ATTAINMENT_PERCENTAGE"
    ].astype(float)
    return qa_report_data


# Function to calculate the YTD values for Cumulative Quota Erosion when quota and schedule frequency are different
def calculate_ytd_cum_qe_for_payee(
    quota_end_date_list, qa_details, category, payee_id=None, type_of_quota=None
):
    quota_end_date_list = [date.strftime("%Y-%m-%d") for date in quota_end_date_list]
    ytd_cum_qe = 0

    for i in range(0, len(quota_end_date_list)):
        if i == 0:
            filtered_df = qa_details[
                (qa_details["CATEGORY_NAME"] == category)
                & (qa_details["PERIOD_END_DATE"] <= quota_end_date_list[i])
            ]

        else:
            filtered_df = qa_details[
                (qa_details["CATEGORY_NAME"] == category)
                & (qa_details["PERIOD_END_DATE"] > quota_end_date_list[i - 1])
                & (qa_details["PERIOD_END_DATE"] <= quota_end_date_list[i])
            ]

        if payee_id:
            filtered_df = filtered_df[(filtered_df["PAYEE_EMAIL_ID"] == payee_id)]

        if type_of_quota:
            filtered_df = filtered_df[
                (filtered_df["INDIVIDUAL_OR_TEAM_QUOTA"] == type_of_quota)
            ]

        if filtered_df.empty:
            continue

        filtered_df = filtered_df.sort_values(by="PERIOD_END_DATE", ascending=True)
        req_data = filtered_df.iloc[-1]
        cum_qe = req_data["QUOTA_EROSION"]
        ytd_cum_qe += cum_qe

    return ytd_cum_qe


def get_quota_attainment_for_team_year(
    client_id, year, team_owner_id, quota_category=None
):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    team_qa = {}
    curr_period = None
    team_quotas = None
    if quota_category is None:
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            team_owner_id, year, True, "Leader"
        )
    else:
        team_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota_category(
            [team_owner_id], quota_category, year, True, "Leader"
        )
    all_quotas = {}
    logger.info("Getting quota attainment for {} teams".format(len(team_quotas)))
    all_email_ids = set()

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:
        if len(team_quotas) > 0:
            payroll = EmployeePayrollAccessor(client_id).get_all_employee_payroll(
                [team_owner_id]
            )
            for team_quota in team_quotas:
                category = team_quota.quota_category_name
                schedule = team_quota.quota_schedule_type
                quota = team_quota.schedule_quota
                team_qa = {}

                end_date_list = get_end_dates_list(int(year), start_month, schedule)

                period = get_period_start_and_end_date(
                    datetime.now(),
                    client.fiscal_start_month,
                    schedule.lower(),
                )
                current_period_index = (
                    end_date_list.index(period["end_date"])
                    if period["end_date"] in end_date_list
                    else len(end_date_list) - 1
                )

                ytd_cum_qe = 0
                ytd_qv = 0
                for i in range(0, len(end_date_list)):
                    if i > current_period_index:
                        team_qa[str(i + 1)] = {
                            "team_qa": "0",
                            "cum_qe": "0",
                            "qv": "0",
                            "reportee_list": [],
                        }
                        continue
                    prev_dates = (
                        get_prev_prds_diff_payout_freq(
                            end_date_list[i],
                            payroll,
                            schedule.lower(),
                        )
                        if len(payroll) > 0
                        else None
                    )
                    quota_dates = []
                    if prev_dates and len(prev_dates[1]) > 0:
                        quota_dates.extend(prev_dates[1])
                    else:
                        quota_dates = [end_date_list[i]]
                    quota_erosions = QuotaErosionAccessor(
                        client_id
                    ).get_quota_data_for_team_end_dates(
                        quota_dates,
                        team_owner_id,
                        category,
                    )
                    cum_qe = 0
                    qv = float(quota[i]["quota"])
                    if len(quota_erosions) > 0:
                        for erosion in quota_erosions:
                            qv = float(quota_erosions[0].qv)
                            cum_qe += float(erosion.quota_erosion)
                        qa = (cum_qe / qv) * 100 if qv else 0
                        ytd_cum_qe += cum_qe
                    else:
                        qa = 0

                    ytd_qv += qv

                    dir_reportees = HierarchyAccessor(client_id).get_reportees_email_id(
                        end_date_list[i], team_owner_id
                    )
                    dir_reportees.append({"employee_email_id": team_owner_id})
                    # get a list of all reportees email ids
                    all_reporting_email_ids = [
                        reportee["employee_email_id"] for reportee in dir_reportees
                    ]
                    reportee_map = EmployeeQuotaAccessor(
                        client_id
                    ).get_quota_schedule_type_for_payees(
                        all_reporting_email_ids, category, year, False
                    )
                    reportee_list = []
                    reportee_email_ids = []
                    for reportee in reportee_map:
                        if reportee.get("quota_schedule_type") == schedule:
                            reportee_email_ids.append(reportee["employee_email_id"])
                        else:
                            reportee_list.append(
                                {
                                    "employee_email_id": reportee["employee_email_id"],
                                    "qa": 0,
                                    "cum_qe": 0,
                                }
                            )
                        all_email_ids.add(reportee["employee_email_id"])

                    if reportee_email_ids:
                        quota_erosions_dict = {}
                        reportee_quotas = {}
                        quota_data = []
                        dir_reportees_payroll = EmployeePayrollAccessor(
                            client_id
                        ).get_all_employee_payroll(reportee_email_ids)
                        latest_reportee_quotas = EmployeeQuotaAccessor(
                            client_id
                        ).get_latest_employees_quota_year(
                            reportee_email_ids, category, year, False
                        )
                        for latest_reportee_quota in latest_reportee_quotas:
                            reportee_quotas[latest_reportee_quota.employee_email_id] = (
                                latest_reportee_quota
                            )

                        for reportee_payroll in dir_reportees_payroll:
                            quota_dates = []
                            prev_dates = (
                                get_prev_prds_diff_payout_freq(
                                    end_date_list[i],
                                    [reportee_payroll],
                                    schedule.lower(),
                                )
                                if len([reportee_payroll]) > 0
                                else None
                            )

                            if prev_dates and len(prev_dates[1]) > 0:
                                quota_dates.extend(prev_dates[1])
                            else:
                                quota_dates = [end_date_list[i]]
                            quota_data.append(
                                {
                                    "end_dates": quota_dates,
                                    "payee_email": reportee_payroll.employee_email_id,
                                }
                            )

                        quota_erosions = QuotaErosionAccessor(
                            client_id
                        ).get_quota_data_for_category_payee_end_dates(
                            quota_data,
                            category,
                        )

                        for value in quota_erosions:
                            if value.payee_email_id in quota_erosions_dict.keys():
                                quota_erosions_dict[value.payee_email_id].append(value)
                            else:
                                quota_erosions_dict[value.payee_email_id] = [value]

                        def sort_quota_erosions(erosion):
                            return erosion.period_end_date

                        for (
                            key,
                            quota_erosions_for_employees,
                        ) in quota_erosions_dict.items():
                            if len(quota_erosions_for_employees) > 0:
                                emp_cum_qe = 0
                                all_qv = {}
                                quota_erosions_for_employees.sort(
                                    key=sort_quota_erosions
                                )
                                for erosion in quota_erosions_for_employees:
                                    emp_cum_qe = emp_cum_qe + erosion.quota_erosion
                                    if erosion.period_end_date not in all_qv:
                                        all_qv[erosion.period_end_date] = erosion.qv

                                total_emp_cum_qe = (
                                    emp_cum_qe
                                    + quota_erosions_for_employees[0].cumulative_qe
                                )
                                if (
                                    reportee_quotas
                                    and key in reportee_quotas.keys()
                                    and reportee_quotas[key].quota_schedule_type
                                    != schedule
                                ):
                                    emp_qv = sum(all_qv.values())
                                else:
                                    emp_qv = quota_erosions_for_employees[0].qv
                                emp_qa = total_emp_cum_qe / emp_qv if emp_qv else 0
                                emp_qa = emp_qa * 100
                                reportee_list.append(
                                    {
                                        "employee_email_id": key,
                                        "qa": str(emp_qa),
                                        "cum_qe": str(total_emp_cum_qe),
                                    }
                                )

                    curr_period = find_period(
                        datetime.today().month, start_month + 1, schedule.lower()
                    )
                    team_qa[str(i + 1)] = {
                        "team_qa": str(qa),
                        "cum_qe": str(cum_qe),
                        "qv": str(qv),
                        "reportee_list": reportee_list,
                    }
                team_qa_qtd = {}
                if schedule.lower() == "monthly" and team_quota.quota_type.lower() in [
                    "quarterly",
                    "monthly",
                ]:
                    team_qa_qtd = calculate_ytd_values_for_cum_qe(team_qa)

                qat = {
                    "team_qa": team_qa,
                    "curr_period": curr_period,
                    "ytd": str(round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)),
                    "ytd_cum_qe": str(ytd_cum_qe),
                    "team_qa_qtd": team_qa_qtd,
                    "quota_category": team_quota.quota_type,
                }
                all_quotas[category] = qat
        else:
            logger.error("END: QUOTA ATTAINMENTS FOR TEAM QUERY - QUOTA NOT AVAILABLE")
            return {"quotas": {"error": "Quota not available"}}

    # Dashboard using Quota Attainment report
    elif quota_dashboard_using_report and len(team_quotas) > 0:
        start_end_date = get_fiscal_start_end_dates(
            fiscal_start_month=client.fiscal_start_month, year=int(year)
        )

        payroll = EmployeePayrollAccessor(client_id).get_all_employee_payroll(
            [team_owner_id]
        )
        payout_frequency = payroll[0].payout_frequency if len(payroll) > 0 else None
        reportee_map_end_date_based_details = {}

        # Collect all payee emails for whom data is required
        for team_quota in team_quotas:
            category = team_quota.quota_category_name
            schedule = team_quota.quota_schedule_type
            quota = team_quota.schedule_quota
            team_qa = {}

            end_date_list = get_end_dates_list(int(year), start_month, schedule)
            period = get_period_start_and_end_date(
                datetime.now(),
                client.fiscal_start_month,
                schedule.lower(),
            )
            current_period_index = (
                end_date_list.index(period["end_date"])
                if period["end_date"] in end_date_list
                else len(end_date_list) - 1
            )
            for i in range(0, len(end_date_list)):
                if i > current_period_index:
                    reportee_map_end_date_based_details[end_date_list[i]] = {
                        "reportee_email_ids": [],
                        "reportee_list": [],
                    }
                    continue

                dir_reportees = HierarchyAccessor(client_id).get_reportees_email_id(
                    end_date_list[i], team_owner_id
                )
                dir_reportees.append({"employee_email_id": team_owner_id})
                # get a list of all reportees email ids
                all_reporting_email_ids = [
                    reportee["employee_email_id"] for reportee in dir_reportees
                ]
                reportee_map = EmployeeQuotaAccessor(
                    client_id
                ).get_quota_schedule_type_for_payees(
                    all_reporting_email_ids, category, year, False
                )
                reportee_list = []
                reportee_email_ids = []
                for reportee in reportee_map:
                    if reportee.get("quota_schedule_type") == schedule:
                        reportee_email_ids.append(reportee["employee_email_id"])
                    else:
                        reportee_list.append(
                            {
                                "employee_email_id": reportee["employee_email_id"],
                                "qa": 0,
                                "cum_qe": 0,
                            }
                        )
                    all_email_ids.add(reportee["employee_email_id"])

                reportee_map_end_date_based_details[end_date_list[i]] = {
                    "reportee_email_ids": reportee_email_ids,
                    "reportee_list": reportee_list,
                }

        qa_details = get_quota_attainment_details_all(
            client_id=client_id,
            payee_id_list=all_email_ids,
            type_of_quota="Both",
            start_date=start_end_date["start_date"].isoformat(),
            end_date=start_end_date["end_date"].isoformat(),
        )

        for team_quota in team_quotas:
            category = team_quota.quota_category_name
            schedule = team_quota.quota_schedule_type
            quota = team_quota.schedule_quota
            quota_freq = team_quota.quota_type
            team_qa_for_qtd = {}
            team_qa = {}

            # If the schedule is monthly and the quota frequency is quarterly, we track the last run sync data for quarter
            last_run_sync_index = 1

            end_date_list = get_end_dates_list(int(year), start_month, schedule)

            period = get_period_start_and_end_date(
                datetime.now(),
                client.fiscal_start_month,
                schedule.lower(),
            )
            current_period_index = (
                end_date_list.index(period["end_date"])
                if period["end_date"] in end_date_list
                else len(end_date_list) - 1
            )

            ytd_cum_qe = 0
            ytd_qv = 0
            for i in range(0, len(end_date_list)):
                if i > current_period_index:
                    team_qa[str(i + 1)] = {
                        "team_qa": "0",
                        "cum_qe": "0",
                        "qv": "0",
                        "reportee_list": [],
                    }
                    team_qa_for_qtd[str(i + 1)] = {
                        "team_qa": "0",
                        "cum_qe": "0",
                        "qv": "0",
                        "reportee_list": [],
                    }
                    continue
                prev_dates = (
                    get_prev_prds_diff_payout_freq(
                        end_date_list[i],
                        payroll,
                        schedule.lower(),
                    )
                    if len(payroll) > 0
                    else None
                )
                quota_dates = []
                if prev_dates and len(prev_dates[1]) > 0:
                    quota_dates.extend(prev_dates[1])
                else:
                    quota_dates = [end_date_list[i]]

                quota_dates = [date.strftime("%Y-%m-%d") for date in quota_dates]
                qv = float(quota[i]["quota"])
                filtered_df = qa_details[
                    (qa_details["CATEGORY_NAME"] == category)
                    & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                    & (qa_details["PAYEE_EMAIL_ID"] == team_owner_id)
                    & (qa_details["INDIVIDUAL_OR_TEAM_QUOTA"] == "Team")
                ]

                if quota_freq.lower() == "quarterly" and schedule.lower() == "monthly":
                    # Check if it is the last month of the quarter
                    if (i + 1) % 3 == 0 or i == current_period_index:
                        # If sync was not run for that quarter end, then take the last run sync data
                        if filtered_df.empty:
                            cum_qe = float(team_qa[str(last_run_sync_index)]["cum_qe"])
                            qa = float(team_qa[str(last_run_sync_index)]["team_qa"])
                            cum_qe_qtd = cum_qe
                            qa_qtd = qa
                            ytd_cum_qe += cum_qe
                        # If sync was run for that quarter end, then take the latest data
                        else:
                            filtered_df["PED_TEMP"] = pd.to_datetime(
                                filtered_df["PERIOD_END_DATE"]
                            )
                            filtered_df = filtered_df.sort_values(
                                by="PED_TEMP", ascending=True
                            )
                            req_data = filtered_df.iloc[-1]
                            cum_qe = req_data["QUOTA_EROSION"]
                            qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                            qv = req_data["QUOTA_VALUE"]
                            cum_qe_qtd = cum_qe
                            qa_qtd = qa
                            ytd_cum_qe += cum_qe

                        # Update the last run sync index to start of next quarter
                        last_run_sync_index = i + 2

                    # If it is not the last month of the quarter
                    elif filtered_df.empty:
                        cum_qe = 0
                        qa = 0
                        cum_qe_qtd = 0
                        qa_qtd = 0

                    else:
                        last_run_sync_index = i + 1
                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        cum_qe = req_data["QUOTA_EROSION"]
                        qv = req_data["QUOTA_VALUE"]
                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                        cum_qe_qtd = 0
                        qa_qtd = 0

                elif filtered_df.empty:
                    cum_qe = 0
                    qa = 0
                    cum_qe_qtd = 0
                    qa_qtd = 0

                else:
                    filtered_df["PED_TEMP"] = pd.to_datetime(
                        filtered_df["PERIOD_END_DATE"]
                    )
                    filtered_df = filtered_df.sort_values(by="PED_TEMP", ascending=True)
                    req_data = filtered_df.iloc[-1]
                    cum_qe = req_data["QUOTA_EROSION"]
                    qv = req_data["QUOTA_VALUE"]
                    qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                    ytd_cum_qe += cum_qe
                    cum_qe_qtd = cum_qe
                    qa_qtd = qa

                ytd_qv += qv

                reportee_email_ids = reportee_map_end_date_based_details[
                    end_date_list[i]
                ]["reportee_email_ids"]
                reportee_list = reportee_map_end_date_based_details[end_date_list[i]][
                    "reportee_list"
                ]

                if reportee_email_ids:
                    quota_erosions_dict = {}
                    reportee_quotas = {}
                    dir_reportees_payroll = EmployeePayrollAccessor(
                        client_id
                    ).get_all_employee_payroll(reportee_email_ids)
                    latest_reportee_quotas = EmployeeQuotaAccessor(
                        client_id
                    ).get_latest_employees_quota_year(
                        reportee_email_ids, category, year, False
                    )
                    for latest_reportee_quota in latest_reportee_quotas:
                        reportee_quotas[latest_reportee_quota.employee_email_id] = (
                            latest_reportee_quota
                        )

                    for reportee_payroll in dir_reportees_payroll:
                        quota_dates = []
                        prev_dates = (
                            get_prev_prds_diff_payout_freq(
                                end_date_list[i],
                                [reportee_payroll],
                                schedule.lower(),
                            )
                            if len([reportee_payroll]) > 0
                            else None
                        )

                        if prev_dates and len(prev_dates[1]) > 0:
                            quota_dates.extend(prev_dates[1])
                        else:
                            quota_dates = [end_date_list[i]]

                        quota_dates = [
                            date.strftime("%Y-%m-%d") for date in quota_dates
                        ]
                        filtered_df = qa_details[
                            (qa_details["CATEGORY_NAME"] == category)
                            & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                            & (
                                qa_details["PAYEE_EMAIL_ID"]
                                == reportee_payroll.employee_email_id
                            )
                            & (qa_details["INDIVIDUAL_OR_TEAM_QUOTA"] == "Individual")
                        ]
                        if filtered_df.empty:
                            continue

                        filtered_df["PED_TEMP"] = pd.to_datetime(
                            filtered_df["PERIOD_END_DATE"]
                        )
                        filtered_df = filtered_df.sort_values(
                            by="PED_TEMP", ascending=True
                        )
                        req_data = filtered_df.iloc[-1]
                        total_emp_cum_qe = req_data["QUOTA_EROSION"]
                        emp_qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]

                        reportee_list.append(
                            {
                                "employee_email_id": reportee_payroll.employee_email_id,
                                "qa": str(emp_qa),
                                "cum_qe": str(total_emp_cum_qe),
                            }
                        )

                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )

                team_qa_for_qtd[str(i + 1)] = {
                    "team_qa": str(qa_qtd),
                    "cum_qe": str(cum_qe_qtd),
                    "qv": str(qv),
                    "reportee_list": reportee_list,
                }

                team_qa[str(i + 1)] = {
                    "team_qa": str(qa),
                    "cum_qe": str(cum_qe),
                    "qv": str(qv),
                    "reportee_list": reportee_list,
                }

            team_qa_qtd = {}

            if (quota_freq.lower() != schedule.lower()) and not (
                quota_freq.lower() == "quarterly" and schedule.lower() == "monthly"
            ):
                quota_end_date_list = get_end_dates_list(
                    int(year), start_month, quota_freq
                )
                qa_period = get_period_start_and_end_date(
                    datetime.now(),
                    client.fiscal_start_month,
                    quota_freq.lower(),
                )
                qa_current_period_index = (
                    quota_end_date_list.index(qa_period["end_date"])
                    if qa_period["end_date"] in quota_end_date_list
                    else len(quota_end_date_list) - 1
                )
                quota_end_date_list = quota_end_date_list[:qa_current_period_index]
                quota_end_date_list.append(period["end_date"])
                ytd_cum_qe = calculate_ytd_cum_qe_for_payee(
                    quota_end_date_list=quota_end_date_list,
                    qa_details=qa_details,
                    category=category,
                    payee_id=team_owner_id,
                    type_of_quota="Team",
                )

            if schedule.lower() == "monthly" and quota_freq.lower() in [
                "quarterly",
                "monthly",
            ]:
                team_qa_qtd = calculate_ytd_values_for_cum_qe(team_qa_for_qtd)

            qat = {
                "team_qa": team_qa,
                "curr_period": curr_period,
                "ytd": str(round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)),
                "ytd_cum_qe": str(ytd_cum_qe),
                "team_qa_qtd": team_qa_qtd,
            }
            all_quotas[category] = qat

    else:
        logger.error("END: QUOTA ATTAINMENTS FOR TEAM QUERY - QUOTA NOT AVAILABLE")
        return {"quotas": {"error": "Quota not available"}}

    payee_names = get_payee_name_dict(client_id, all_email_ids)
    logger.info("END: QUOTA ATTAINMENTS FOR TEAM QUERY")
    return {"quotas": all_quotas, "payees_info": payee_names}


def get_quota_attainment_for_category_team_year(
    client_id, year, team_owner_id, quota_category=None, login_user_id=None
):
    """This function is used to generate data for the Quota Attainment Tracker widget based on team_owner_id, category and fiscal year"""
    log_context = {
        "client_id": client_id,
        "fiscal_year": year,
        "team_owner_id": team_owner_id,
        "quota_category": quota_category,
    }
    logger_new.info(
        "BEGIN : get_quota_attainment_for_category_team_year", extra=log_context
    )
    choosen_quota_to_display = quota_category
    quota_list = []
    if quota_category is None:
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            team_owner_id, year, True, "Leader"
        )
        quota_list = __rearrange_quota_list(client_id, team_quotas, login_user_id)
        if len(quota_list) > 0:
            choosen_quota_to_display = quota_list[0]
    if choosen_quota_to_display is not None:
        result = get_quota_attainment_for_team_year(
            client_id, year, team_owner_id, choosen_quota_to_display
        )
        if quota_category is None and len(quota_list) > 0:
            result["quota_category_names"] = quota_list
        logger_new.info(
            "END : get_quota_attainment_for_category_team_year",
            extra={
                "client_id": client_id,
                "team_owner_id": team_owner_id,
                "quota_category": quota_category,
            },
        )
        return result
    else:
        logger_new.info("END : get_quota_attainment_for_category_team_year")
        return {"quotas": {"error": "Quota not available"}}


def __rearrange_quota_list(client_id, team_quotas, login_user_id):
    quota_name_list = []
    ui_permissions = get_ui_permissions(client_id, login_user_id)
    if ui_permissions and RbacPermissions.VIEW_HIDDENQUOTAS.value in ui_permissions:
        hidden_quotas = []
    else:
        hidden_quotas = set(
            (client_accessor.get_client_hidden_quota_categories(client_id) or [])
        )
    is_primary_present = False
    for quota in team_quotas:
        if quota.quota_category_name in hidden_quotas:
            continue
        elif quota.quota_category_name == "Primary":
            is_primary_present = True
        else:
            quota_name_list.append(quota.quota_category_name)
    quota_name_list.sort()
    if is_primary_present:
        quota_name_list.insert(0, "Primary")

    return quota_name_list


def get_payee_name_dict(client_id, email_ids):
    output = {}
    if email_ids is not None:
        payees = EmployeeAccessor(client_id).get_employees_name(email_ids)
        for payee in payees:
            output[payee["employee_email_id"]] = (
                f"{payee['first_name']} {payee['last_name']}"
            )
    return output


def get_quota_attainment_for_category_current_period(
    client_id, team_owner_id, quota_category=None, login_user_id=None
):
    """This function is used to generate data for the Quota Attainment widget based on team_owner_id and category for the current period"""
    log_context = {
        "client_id": client_id,
        "team_owner_id": team_owner_id,
        "quota_category": quota_category,
    }
    logger_new.info(
        "BEGIN : get_quota_attainment_for_category_current_period", extra=log_context
    )
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month)
    choosen_quota_to_display = quota_category
    quota_list = []
    if quota_category is None:
        team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            team_owner_id, fiscal_year, True, "Leader"
        )
        quota_list = __rearrange_quota_list(client_id, team_quotas, login_user_id)
        if len(quota_list) > 0:
            choosen_quota_to_display = quota_list[0]
    if choosen_quota_to_display is not None:
        result = {}
        result["quotas"] = get_quota_attainment_for_current_period(
            client_id, team_owner_id, choosen_quota_to_display
        )
        if quota_category is None and len(quota_list) > 0:
            result["quota_category_names"] = quota_list
        logger_new.info(
            "END : get_quota_attainment_for_category_current_period",
            extra={
                "client_id": client_id,
                "team_owner_id": team_owner_id,
                "quota_category": quota_category,
            },
        )
        return result
    else:
        logger_new.info("END : get_quota_attainment_for_category_current_period")
        return {"quotas": {"error": "Quota not available"}}


def get_quota_attainment_for_current_period(
    client_id, team_owner_id, quota_category=None
):
    client = get_client(client_id)
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    curr_year = date.today().year
    curr_month = date.today().month
    fiscal_year = get_fiscal_year(client.fiscal_start_month)
    if quota_category is None:
        emp_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
            team_owner_id, str(fiscal_year), True, "Leader"
        )
    else:
        emp_quotas = EmployeeQuotaAccessor(
            client_id
        ).get_latest_employees_or_teams_quota_category(
            [team_owner_id], quota_category, fiscal_year, True, "Leader"
        )
    all_quotas = {}
    logger.info(
        "Getting quota attainment for current period for {} employees".format(
            len(emp_quotas)
        )
    )

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:
        if len(emp_quotas) > 0:
            for emp_quota in emp_quotas:
                category = emp_quota.quota_category_name
                schedule = emp_quota.quota_schedule_type
                d = date(int(curr_year), curr_month, 1)
                start_end_dates = get_period_start_and_end_date(
                    d, client.fiscal_start_month, schedule.lower(), prev_required=True
                )
                team_qa = {}
                for i in range(0, 2):
                    if i == 0:
                        end_date = start_end_dates["prev_end_date"]
                    else:
                        end_date = start_end_dates["end_date"]
                    payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                        end_date, [team_owner_id]
                    )
                    payout_frequency = (
                        payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                    )
                    prev_dates = (
                        get_prev_prds(
                            client.fiscal_start_month,
                            end_date,
                            payout_frequency.lower(),
                            schedule.lower(),
                        )
                        if payout_frequency
                        else None
                    )
                    end_dates = [end_date]
                    if prev_dates and len(prev_dates) > 1:
                        end_dates.extend(prev_dates[1])

                    quota_erosions = QuotaErosionAccessor(
                        client_id
                    ).get_quota_data_for_team_end_dates(
                        end_dates,
                        team_owner_id,
                        category,
                    )
                    if len(quota_erosions) > 0:
                        cum_qe = 0
                        for erosion in quota_erosions:
                            cum_qe = cum_qe + erosion.quota_erosion
                        qa = (
                            (cum_qe / quota_erosions[0].qv) * 100
                            if quota_erosions[0].qv
                            else 0
                        )
                    else:
                        qa = 0

                    period = get_period_name_for_date(
                        schedule.lower(), end_date, client.fiscal_start_month
                    )
                    team_qa[period] = str(qa)
                    all_quotas[category] = team_qa

        else:
            logger.error("END: CURRENT QUOTA ATTAINMENT QUERY - QUOTA NOT AVAILABLE")
            return {"error": "Quota not available"}

    # Dashboard using Quota Attainment report
    elif quota_dashboard_using_report and len(emp_quotas) > 0:
        start_end_date = get_fiscal_start_end_dates(client.fiscal_start_month)

        qa_details = get_quota_attainment_details_all(
            client_id=client_id,
            payee_id_list=[team_owner_id],
            type_of_quota="Team",
            start_date=start_end_date["start_date"].isoformat(),
            end_date=start_end_date["end_date"].isoformat(),
        )

        for emp_quota in emp_quotas:
            category = emp_quota.quota_category_name
            schedule = emp_quota.quota_schedule_type
            d = date(int(curr_year), curr_month, 1)
            start_end_dates = get_period_start_and_end_date(
                d, client.fiscal_start_month, schedule.lower(), prev_required=True
            )
            team_qa = {}
            for i in range(0, 2):
                if i == 0:
                    end_date = start_end_dates["prev_end_date"]
                else:
                    end_date = start_end_dates["end_date"]
                payroll = EmployeePayrollAccessor(client_id).get_employee_payroll(
                    end_date, [team_owner_id]
                )
                payout_frequency = (
                    payroll[0]["payout_frequency"] if len(payroll) > 0 else None
                )
                prev_dates = (
                    get_prev_prds(
                        client.fiscal_start_month,
                        end_date,
                        payout_frequency.lower(),
                        schedule.lower(),
                    )
                    if payout_frequency
                    else None
                )
                end_dates = [end_date]
                if prev_dates and len(prev_dates) > 1:
                    end_dates.extend(prev_dates[1])

                end_dates = [date.strftime("%Y-%m-%d") for date in end_dates]
                filtered_df = qa_details[
                    (qa_details["CATEGORY_NAME"] == category)
                    & (qa_details["PERIOD_END_DATE"].isin(end_dates))
                ]
                if filtered_df.empty:
                    cum_qe = 0
                    qa = 0

                else:
                    filtered_df["PED_TEMP"] = pd.to_datetime(
                        filtered_df["PERIOD_END_DATE"]
                    )
                    filtered_df = filtered_df.sort_values(by="PED_TEMP", ascending=True)
                    req_data = filtered_df.iloc[-1]
                    cum_qe = req_data["QUOTA_EROSION"]
                    qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]

                period = get_period_name_for_date(
                    schedule.lower(), end_date, client.fiscal_start_month
                )
                team_qa[period] = str(qa)
                all_quotas[category] = team_qa

    else:
        logger.error("END: CURRENT QUOTA ATTAINMENT QUERY - QUOTA NOT AVAILABLE")
        return {"error": "Quota not available"}

    logger.info("END: CURRENT QUOTA ATTAINMENT QUERY")
    return all_quotas


def get_quota_attainment_for_team_and_payees(
    client_id, team_owner_ids, payee_ids, year, quota_category
):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    teams_quotas = EmployeeQuotaAccessor(
        client_id
    ).get_latest_employees_or_teams_quota_category(
        team_owner_ids, quota_category, year, True, "Leader"
    )
    emp_quotas = EmployeeQuotaAccessor(
        client_id
    ).get_latest_employees_or_teams_quota_category(
        payee_ids, quota_category, year, False, None
    )
    all_payee_info = get_payee_name_dict(client_id, payee_ids)
    team_quota_dict = {x.employee_email_id: x for x in teams_quotas}
    emp_quota_dict = {x.employee_email_id: x for x in emp_quotas}

    all_quotas = teams_quotas + emp_quotas
    schedule_error = False
    logger.info(
        "Getting quota attainment for team and payees - {} team quota and {} payee quota".format(
            len(teams_quotas), len(emp_quotas)
        )
    )

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:
        if len(all_quotas) > 0:
            schedule = all_quotas[0].quota_schedule_type
            quota_freq = all_quotas[0].quota_type
            for quota in all_quotas:
                if quota.quota_schedule_type != schedule:
                    schedule_error = True
                    break
            if not schedule_error:
                team_qa = {}
                payee_qa = {}
                team_qa_qtd = {}
                payee_qa_qtd = {}
                payee_cum_qe = {}
                payee_cum_qe_qtd = {}

                end_date_list = get_end_dates_list(int(year), start_month, schedule)
                ytd_cum_qe = 0
                ytd_qv = 0
                if len(team_owner_ids) > 0:
                    for owner in team_owner_ids:
                        period_qa = {}
                        qa_list = {}

                        for i in range(0, len(end_date_list)):
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [owner])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            quota_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                quota_dates.extend(prev_dates[1])

                            quota_erosions = QuotaErosionAccessor(
                                client_id
                            ).get_quota_data_for_team_end_dates(
                                quota_dates,
                                owner,
                                quota_category,
                            )
                            cum_qe = 0
                            qv = 0
                            team_quota_data = team_quota_dict[owner]
                            if team_quota_data:
                                qv = float(team_quota_data.schedule_quota[i]["quota"])

                            if len(quota_erosions) > 0:
                                for erosion in quota_erosions:
                                    cum_qe += float(erosion.quota_erosion)
                                qv = float(quota_erosions[0].qv)
                                period_qa[str(i + 1)] = (
                                    str((cum_qe / qv) * 100) if qv else 0
                                )
                                ytd_cum_qe += cum_qe
                            else:
                                period_qa[str(i + 1)] = 0

                            ytd_qv += qv
                            qa_list[str(i + 1)] = {"cum_qe": cum_qe, "qv": qv}

                        if schedule.lower() == "monthly" and quota_freq.lower() in [
                            "quarterly",
                            "monthly",
                        ]:
                            team_qa_qtd[owner] = calculate_ytd_values(qa_list)["qa_qtd"]
                        team_qa[owner] = period_qa

                if len(payee_ids) > 0:
                    for payee in payee_ids:
                        period_qa = {}
                        period_cum_qe = {}
                        qa_list = {}

                        for i in range(0, len(end_date_list)):
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [payee])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            quota_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                quota_dates.extend(prev_dates[1])

                            quota_erosions_for_employee = QuotaErosionAccessor(
                                client_id
                            ).get_quota_data_for_payee_end_dates(
                                quota_dates, payee, quota_category
                            )
                            cum_qe = 0
                            qv = 0
                            emp_quota_data = emp_quota_dict[payee]
                            if emp_quota_data:
                                qv = float(emp_quota_data.schedule_quota[i]["quota"])
                            if len(quota_erosions_for_employee) > 0:
                                for erosion in quota_erosions_for_employee:
                                    cum_qe += float(erosion.quota_erosion)
                                total_cum_qe = cum_qe + float(
                                    quota_erosions_for_employee[0].cumulative_qe
                                )

                                qv = float(quota_erosions_for_employee[0].qv)
                                period_qa[str(i + 1)] = (
                                    str((total_cum_qe / qv) * 100) if qv else 0
                                )
                                period_cum_qe[str(i + 1)] = (
                                    str(total_cum_qe) if total_cum_qe else 0
                                )
                                ytd_cum_qe += cum_qe
                            else:
                                period_qa[str(i + 1)] = 0
                                period_cum_qe[str(i + 1)] = 0

                            ytd_qv += qv
                            qa_list[str(i + 1)] = {"cum_qe": cum_qe, "qv": qv}

                        if schedule.lower() == "monthly" and quota_freq.lower() in [
                            "quarterly",
                            "monthly",
                        ]:
                            payee_qa_qtd[payee] = calculate_ytd_values(qa_list)[
                                "qa_qtd"
                            ]
                            payee_cum_qe_qtd[payee] = calculate_ytd_values(qa_list)[
                                "cum_qe_qtd"
                            ]
                        payee_qa[payee] = period_qa
                        payee_cum_qe[payee] = period_cum_qe

                logger.info("END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY")
                return {
                    "quotas": {
                        quota_category: {
                            "team_qa": team_qa,
                            "payee_qa": payee_qa,
                            "payee_cum_qe": payee_cum_qe,
                            "ytd": str(
                                round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)
                            ),
                            "ytd_cum_qe": str(ytd_cum_qe),
                            "team_qa_qtd": team_qa_qtd,
                            "payee_qa_qtd": payee_qa_qtd,
                            "payee_cum_qe_qtd": payee_cum_qe_qtd,
                        }
                    },
                    "payees_info": all_payee_info,
                }

            logger.error(
                "END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY - SELECTED PAYEES OR TEAMS HAVE DIFFERENT SCHEDULE"
            )
            return {
                "quotas": {"error": "Selected payees or teams have different schedule"}
            }

        else:
            logger.error(
                "END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY - QUOTA NOT AVAILABLE FOR PAYEES OR TEAMS"
            )
            return {"quotas": {"error": "Quota not available for payees or teams"}}

    # Dashboard using Quota Attainment Report
    else:
        start_end_date = get_fiscal_start_end_dates(
            fiscal_start_month=client.fiscal_start_month, year=int(year)
        )

        qa_details = get_quota_attainment_details_all(
            client_id=client_id,
            payee_id_list=team_owner_ids + payee_ids,
            type_of_quota="Both",
            start_date=start_end_date["start_date"].isoformat(),
            end_date=start_end_date["end_date"].isoformat(),
        )

        if len(all_quotas) > 0:
            schedule = all_quotas[0].quota_schedule_type
            quota_freq = all_quotas[0].quota_type
            for quota in all_quotas:
                if quota.quota_schedule_type != schedule:
                    schedule_error = True
                    break
            if not schedule_error:
                team_qa = {}
                payee_qa = {}
                team_qa_qtd = {}
                payee_qa_qtd = {}
                payee_cum_qe = {}
                payee_cum_qe_qtd = {}

                end_date_list = get_end_dates_list(int(year), start_month, schedule)
                ytd_cum_qe = 0
                # Track the correct ytd_cum_qe value
                # Helps in case when quota frequency is not same as schedule frequency
                temp_ytd_cum_qe = 0
                ytd_qv = 0
                if len(team_owner_ids) > 0:
                    for owner in team_owner_ids:
                        period_qa = {}
                        qa_list = {}

                        team_qa_for_qtd = {}

                        # If the schedule is monthly and the quota frequency is quarterly, we track the last run sync data for quarter
                        last_run_sync_index = 1

                        for i in range(0, len(end_date_list)):
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [owner])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            quota_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                quota_dates.extend(prev_dates[1])

                            quota_dates = [
                                date.strftime("%Y-%m-%d") for date in quota_dates
                            ]
                            filtered_df = qa_details[
                                (qa_details["CATEGORY_NAME"] == quota_category)
                                & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                                & (qa_details["PAYEE_EMAIL_ID"] == owner)
                                & (qa_details["INDIVIDUAL_OR_TEAM_QUOTA"] == "Team")
                            ]
                            team_quota_data = team_quota_dict[owner]
                            if team_quota_data:
                                qv = float(team_quota_data.schedule_quota[i]["quota"])

                            if (
                                payout_frequency
                                and payout_frequency.lower() == "monthly"
                                and quota_freq.lower() == "quarterly"
                                and schedule.lower() == "monthly"
                            ):
                                # Check if it is the last month of the quarter
                                if (i + 1) % 3 == 0:
                                    # If sync was not run for that quarter end, then take the last run sync data
                                    if filtered_df.empty:
                                        cum_qe = float(
                                            qa_list[str(last_run_sync_index)]["cum_qe"]
                                        )
                                        period_qa[str(i + 1)] = str(
                                            period_qa[str(last_run_sync_index)]
                                        )
                                        cum_qe_qtd = cum_qe
                                        ytd_cum_qe += cum_qe
                                    # If sync was run for that quarter end, then take the latest data
                                    else:
                                        filtered_df["PED_TEMP"] = pd.to_datetime(
                                            filtered_df["PERIOD_END_DATE"]
                                        )
                                        # Might not require sorting, but just to be sure
                                        filtered_df = filtered_df.sort_values(
                                            by="PED_TEMP", ascending=True
                                        )
                                        req_data = filtered_df.iloc[-1]
                                        cum_qe = req_data["QUOTA_EROSION"]
                                        qv = req_data["QUOTA_VALUE"]
                                        period_qa[str(i + 1)] = str(
                                            req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                        )
                                        cum_qe_qtd = cum_qe
                                        ytd_cum_qe += cum_qe

                                    # Update the last run sync index to start of next quarter
                                    last_run_sync_index = i + 2

                                # If it is not the last month of the quarter
                                elif filtered_df.empty:
                                    cum_qe = 0
                                    cum_qe_qtd = 0
                                    period_qa[str(i + 1)] = 0

                                else:
                                    last_run_sync_index = i + 1
                                    filtered_df["PED_TEMP"] = pd.to_datetime(
                                        filtered_df["PERIOD_END_DATE"]
                                    )
                                    filtered_df = filtered_df.sort_values(
                                        by="PED_TEMP", ascending=True
                                    )
                                    req_data = filtered_df.iloc[-1]
                                    cum_qe = req_data["QUOTA_EROSION"]
                                    qv = req_data["QUOTA_VALUE"]
                                    period_qa[str(i + 1)] = str(
                                        req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                    )
                                    cum_qe_qtd = 0

                            elif filtered_df.empty:
                                cum_qe = 0
                                period_qa[str(i + 1)] = 0
                                cum_qe_qtd = 0

                            else:
                                filtered_df["PED_TEMP"] = pd.to_datetime(
                                    filtered_df["PERIOD_END_DATE"]
                                )
                                filtered_df = filtered_df.sort_values(
                                    by="PED_TEMP", ascending=True
                                )
                                req_data = filtered_df.iloc[-1]
                                cum_qe = req_data["QUOTA_EROSION"]
                                qv = req_data["QUOTA_VALUE"]
                                period_qa[str(i + 1)] = str(
                                    req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                )
                                ytd_cum_qe += cum_qe
                                cum_qe_qtd = cum_qe

                            team_qa_for_qtd[str(i + 1)] = {
                                "cum_qe": cum_qe_qtd,
                                "qv": qv,
                            }

                            ytd_qv += qv
                            qa_list[str(i + 1)] = {"cum_qe": cum_qe, "qv": qv}

                        if schedule.lower() == "monthly" and quota_freq.lower() in [
                            "quarterly",
                            "monthly",
                        ]:
                            team_qa_qtd[owner] = calculate_ytd_values(team_qa_for_qtd)[
                                "qa_qtd"
                            ]
                        team_qa[owner] = period_qa

                        if (quota_freq.lower() != schedule.lower()) and not (
                            quota_freq.lower() == "quarterly"
                            and schedule.lower() == "monthly"
                        ):
                            quota_end_date_list = get_end_dates_list(
                                int(year), start_month, quota_freq
                            )
                            ytd_cum_qe_for_payee = calculate_ytd_cum_qe_for_payee(
                                quota_end_date_list=quota_end_date_list,
                                qa_details=qa_details,
                                category=quota_category,
                                payee_id=owner,
                                type_of_quota="Team",
                            )
                            ytd_cum_qe = ytd_cum_qe_for_payee + temp_ytd_cum_qe
                            temp_ytd_cum_qe = ytd_cum_qe
                        else:
                            temp_ytd_cum_qe = ytd_cum_qe

                if len(payee_ids) > 0:
                    for payee in payee_ids:
                        period_qa = {}
                        period_cum_qe = {}
                        qa_list = {}

                        emp_qa_for_qtd = {}

                        # If the schedule is monthly and the quota frequency is quarterly, we track the last run sync data for quarter
                        last_run_sync_index = 1

                        for i in range(0, len(end_date_list)):
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [payee])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            quota_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                quota_dates.extend(prev_dates[1])

                            quota_dates = [
                                date.strftime("%Y-%m-%d") for date in quota_dates
                            ]
                            filtered_df = qa_details[
                                (qa_details["CATEGORY_NAME"] == quota_category)
                                & (qa_details["PERIOD_END_DATE"].isin(quota_dates))
                                & (qa_details["PAYEE_EMAIL_ID"] == payee)
                                & (
                                    qa_details["INDIVIDUAL_OR_TEAM_QUOTA"]
                                    == "Individual"
                                )
                            ]
                            emp_quota_data = emp_quota_dict[payee]
                            if emp_quota_data:
                                qv = float(emp_quota_data.schedule_quota[i]["quota"])

                            if (
                                payout_frequency
                                and payout_frequency.lower() == "monthly"
                                and quota_freq.lower() == "quarterly"
                                and schedule.lower() == "monthly"
                            ):
                                # Check if it is the last month of the quarter
                                if (i + 1) % 3 == 0:
                                    # If sync was not run for that quarter end, then take the last run sync data
                                    if filtered_df.empty:
                                        cum_qe = float(
                                            qa_list[str(last_run_sync_index)]["cum_qe"]
                                        )
                                        qa = float(period_qa[str(last_run_sync_index)])
                                        cum_qe_qtd = cum_qe
                                        ytd_cum_qe += cum_qe
                                        period_qa[str(i + 1)] = str(qa)
                                        period_cum_qe[str(i + 1)] = str(cum_qe)
                                    # If sync was run for that quarter end, then take the latest data
                                    else:
                                        filtered_df["PED_TEMP"] = pd.to_datetime(
                                            filtered_df["PERIOD_END_DATE"]
                                        )
                                        # Might not require sorting, but just to be sure
                                        filtered_df = filtered_df.sort_values(
                                            by="PED_TEMP", ascending=True
                                        )
                                        req_data = filtered_df.iloc[-1]
                                        cum_qe = req_data["QUOTA_EROSION"]
                                        qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                        qv = req_data["QUOTA_VALUE"]
                                        period_qa[str(i + 1)] = str(qa)
                                        period_cum_qe[str(i + 1)] = str(cum_qe)
                                        cum_qe_qtd = cum_qe
                                        ytd_cum_qe += cum_qe

                                    # Update the last run sync index to start of next quarter
                                    last_run_sync_index = i + 2

                                # If it is not the last month of the quarter
                                elif filtered_df.empty:
                                    cum_qe = 0
                                    qa = 0
                                    cum_qe_qtd = 0
                                    period_qa[str(i + 1)] = 0
                                    period_cum_qe[str(i + 1)] = 0

                                else:
                                    last_run_sync_index = i + 1
                                    filtered_df["PED_TEMP"] = pd.to_datetime(
                                        filtered_df["PERIOD_END_DATE"]
                                    )
                                    filtered_df = filtered_df.sort_values(
                                        by="PED_TEMP", ascending=True
                                    )
                                    req_data = filtered_df.iloc[-1]
                                    cum_qe = req_data["QUOTA_EROSION"]
                                    qv = req_data["QUOTA_VALUE"]
                                    qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                    period_qa[str(i + 1)] = str(qa)
                                    period_cum_qe[str(i + 1)] = (
                                        str(cum_qe) if cum_qe else 0
                                    )
                                    cum_qe_qtd = 0

                            elif filtered_df.empty:
                                cum_qe = 0
                                period_qa[str(i + 1)] = 0
                                period_cum_qe[str(i + 1)] = 0
                                cum_qe_qtd = 0

                            else:
                                filtered_df["PED_TEMP"] = pd.to_datetime(
                                    filtered_df["PERIOD_END_DATE"]
                                )
                                filtered_df = filtered_df.sort_values(
                                    by="PED_TEMP", ascending=True
                                )
                                req_data = filtered_df.iloc[-1]
                                cum_qe = req_data["QUOTA_EROSION"]
                                qv = req_data["QUOTA_VALUE"]
                                period_qa[str(i + 1)] = str(
                                    req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                )
                                period_cum_qe[str(i + 1)] = str(cum_qe) if cum_qe else 0
                                cum_qe_qtd = cum_qe
                                ytd_cum_qe += cum_qe

                            emp_qa_for_qtd[str(i + 1)] = {
                                "cum_qe": cum_qe_qtd,
                                "qv": qv,
                            }

                            ytd_qv += qv
                            qa_list[str(i + 1)] = {"cum_qe": cum_qe, "qv": qv}

                        if schedule.lower() == "monthly" and quota_freq.lower() in [
                            "quarterly",
                            "monthly",
                        ]:
                            payee_qa_qtd[payee] = calculate_ytd_values(emp_qa_for_qtd)[
                                "qa_qtd"
                            ]
                            payee_cum_qe_qtd[payee] = calculate_ytd_values(
                                emp_qa_for_qtd
                            )["cum_qe_qtd"]
                        payee_qa[payee] = period_qa
                        payee_cum_qe[payee] = period_cum_qe

                        if (quota_freq.lower() != schedule.lower()) and not (
                            quota_freq.lower() == "quarterly"
                            and schedule.lower() == "monthly"
                        ):
                            quota_end_date_list = get_end_dates_list(
                                int(year), start_month, quota_freq
                            )
                            ytd_cum_qe_for_payee = calculate_ytd_cum_qe_for_payee(
                                quota_end_date_list=quota_end_date_list,
                                qa_details=qa_details,
                                category=quota_category,
                                payee_id=payee,
                                type_of_quota="Individual",
                            )
                            ytd_cum_qe = ytd_cum_qe_for_payee + temp_ytd_cum_qe
                            temp_ytd_cum_qe = ytd_cum_qe
                        else:
                            temp_ytd_cum_qe = ytd_cum_qe

                logger.info("END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY")
                return {
                    "quotas": {
                        quota_category: {
                            "team_qa": team_qa,
                            "payee_qa": payee_qa,
                            "payee_cum_qe": payee_cum_qe,
                            "ytd": str(
                                round((ytd_cum_qe / ytd_qv) * 100 if ytd_qv else 0, 2)
                            ),
                            "ytd_cum_qe": str(ytd_cum_qe),
                            "team_qa_qtd": team_qa_qtd,
                            "payee_qa_qtd": payee_qa_qtd,
                            "payee_cum_qe_qtd": payee_cum_qe_qtd,
                        }
                    },
                    "payees_info": all_payee_info,
                }

            logger.error(
                "END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY - SELECTED PAYEES OR TEAMS HAVE DIFFERENT SCHEDULE"
            )
            return {
                "quotas": {"error": "Selected payees or teams have different schedule"}
            }

        else:
            logger.error(
                "END: QUOTA ATTAINMENT FOR PAYEE AND TEAMS QUERY - QUOTA NOT AVAILABLE FOR PAYEES OR TEAMS"
            )
            return {"quotas": {"error": "Quota not available for payees or teams"}}


def get_quota_distribution_for_payees(client_id, payee_ids, year, quota_category):
    client = get_client(client_id)
    start_month = client.fiscal_start_month - 1
    # If feature flag enabled in backend, then get the quota attainment details from quota attainment report
    quota_dashboard_using_report = client.client_features.get(
        "quota_dashboard_using_report", False
    )
    emp_quotas = EmployeeQuotaAccessor(
        client_id
    ).get_latest_employees_or_teams_quota_category(
        payee_ids, quota_category, year, False, None
    )

    # Regular Dashboard using Quota Erosion
    if not quota_dashboard_using_report:
        if len(emp_quotas) > 0:
            schedule_error = False
            schedule = emp_quotas[0].quota_schedule_type
            for quota in emp_quotas:
                if quota.quota_schedule_type != schedule:
                    schedule_error = True
                    break
            if not schedule_error:
                payee_qa = {}
                end_date_list = get_end_dates_list(int(year), start_month, schedule)
                if len(payee_ids) > 0:
                    for payee in payee_ids:
                        logger.info(
                            "Getting Quota distribution for payee {}".format(payee)
                        )
                        for i in range(0, len(end_date_list)):
                            period = get_period(schedule, i, start_month)
                            if period not in payee_qa:
                                payee_qa[period] = []
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [payee])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            end_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                end_dates.extend(prev_dates[1])

                            quota_erosions_for_employee = QuotaErosionAccessor(
                                client_id
                            ).get_quota_data_for_payee_end_dates(
                                end_dates, payee, quota_category
                            )
                            if len(quota_erosions_for_employee) > 0:
                                cum_qe = 0
                                for erosion in quota_erosions_for_employee:
                                    cum_qe = cum_qe + erosion.quota_erosion

                                payee_qa[period].append(
                                    {
                                        "employee_email_id": payee,
                                        "qa": (
                                            str(
                                                (
                                                    (
                                                        quota_erosions_for_employee[
                                                            0
                                                        ].cumulative_qe
                                                        + cum_qe
                                                    )
                                                    / quota_erosions_for_employee[0].qv
                                                )
                                                * 100
                                            )
                                            if quota_erosions_for_employee[0].qv
                                            else 0
                                        ),
                                    }
                                )
                            else:
                                payee_qa[period].append(
                                    {"employee_email_id": payee, "qa": 0}
                                )
                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )
                qd = {"payee_qa": payee_qa, "curr_period": curr_period}
                return {quota_category: qd}
            else:
                return {"error": "Selected payees or teams have different schedule"}
        else:
            return {"error": "Quota not available for payees or teams"}

    # Dashboard using Quota Attainment Report
    else:
        if len(emp_quotas) > 0:
            schedule_error = False
            schedule = emp_quotas[0].quota_schedule_type
            for quota in emp_quotas:
                if quota.quota_schedule_type != schedule:
                    schedule_error = True
                    break
            if not schedule_error:
                payee_qa = {}
                end_date_list = get_end_dates_list(int(year), start_month, schedule)
                if len(payee_ids) > 0:
                    start_end_date = get_fiscal_start_end_dates(
                        fiscal_start_month=client.fiscal_start_month, year=int(year)
                    )

                    qa_details = get_quota_attainment_details_all(
                        client_id=client_id,
                        payee_id_list=payee_ids,
                        type_of_quota="Individual",
                        start_date=start_end_date["start_date"].isoformat(),
                        end_date=start_end_date["end_date"].isoformat(),
                    )
                    for payee in payee_ids:
                        logger.info(
                            "Getting Quota distribution for payee {}".format(payee)
                        )
                        for i in range(0, len(end_date_list)):
                            period = get_period(schedule, i, start_month)
                            if period not in payee_qa:
                                payee_qa[period] = []
                            payroll = EmployeePayrollAccessor(
                                client_id
                            ).get_employee_payroll(end_date_list[i], [payee])
                            payout_frequency = (
                                payroll[0]["payout_frequency"]
                                if len(payroll) > 0
                                else None
                            )
                            prev_dates = (
                                get_prev_prds(
                                    client.fiscal_start_month,
                                    end_date_list[i],
                                    payout_frequency.lower(),
                                    schedule.lower(),
                                )
                                if payout_frequency
                                else None
                            )
                            end_dates = [end_date_list[i]]
                            if prev_dates and len(prev_dates) > 1:
                                end_dates.extend(prev_dates[1])

                            end_dates = [
                                date.strftime("%Y-%m-%d") for date in end_dates
                            ]
                            filtered_df = qa_details[
                                (qa_details["CATEGORY_NAME"] == quota_category)
                                & (qa_details["PERIOD_END_DATE"].isin(end_dates))
                                & (qa_details["PAYEE_EMAIL_ID"] == payee)
                            ]
                            if filtered_df.empty:
                                payee_qa[period].append(
                                    {"employee_email_id": payee, "qa": 0}
                                )

                            else:
                                filtered_df["PED_TEMP"] = pd.to_datetime(
                                    filtered_df["PERIOD_END_DATE"]
                                )
                                filtered_df = filtered_df.sort_values(
                                    by="PED_TEMP", ascending=True
                                )
                                req_data = filtered_df.iloc[-1]
                                cum_qe = req_data["QUOTA_EROSION"]
                                qa = req_data["QUOTA_ATTAINMENT_PERCENTAGE"]
                                payee_qa[period].append(
                                    {"employee_email_id": payee, "qa": str(qa)}
                                )

                curr_period = find_period(
                    datetime.today().month, start_month + 1, schedule.lower()
                )
                qd = {"payee_qa": payee_qa, "curr_period": curr_period}
                return {quota_category: qd}
            else:
                return {"error": "Selected payees or teams have different schedule"}
        else:
            return {"error": "Quota not available for payees or teams"}


def get_quota_run_rate_for_team(client_id, year, team_owner_id):
    client = get_client(client_id)
    team_quotas = EmployeeQuotaAccessor(client_id).get_latest_team_quota(
        team_owner_id, year, True, "Leader"
    )
    start_month = client.fiscal_start_month
    d = date(int(year), 1, 1)
    year_start_end_dates = get_period_start_and_end_date(d, start_month, "annual")
    logger.info("Getting quota run rate for {} teams".format(len(team_quotas)))
    if len(team_quotas) > 0 and year_start_end_dates:
        all_quotas = {}
        for team_quota in team_quotas:
            category = team_quota.quota_category_name
            schedule = team_quota.quota_schedule_type
            team_run_rate = get_run_rate_dict(schedule)
            quota_erosion = QuotaErosionAccessor(
                client_id
            ).get_all_quota_erosion_for_payee_or_team(
                team_owner_id,
                True,
                year_start_end_dates["start_date"],
                year_start_end_dates["end_date"],
                category,
                True,
            )
            data_frame_qe = pd.DataFrame(quota_erosion)

            if not data_frame_qe.empty:
                period_date_group = data_frame_qe.groupby(
                    ["period_start_date", "period_end_date"]
                )
                for key, value in period_date_group:
                    sub_data_frame = period_date_group.get_group(key)
                    if len(sub_data_frame) > 0:
                        ped = sub_data_frame.iloc[0]["period_end_date"]
                        period_details = find_period(
                            ped.month, start_month, schedule.lower()
                        )
                        period = period_details["index"] + 1
                        kd_group = sub_data_frame.groupby(
                            sub_data_frame["knowledge_begin_date"].dt.date
                        )
                        for kd, group in kd_group:
                            max_date = group.agg({"knowledge_begin_date": "max"})
                            records = group.loc[
                                group["knowledge_begin_date"]
                                == max_date["knowledge_begin_date"]
                            ]
                            qe_sum = 0
                            cum_qe = 0
                            qv = 0
                            for ind in records.index:
                                qe_sum = qe_sum + records["quota_erosion"][ind]
                                cum_qe = records["cumulative_qe"][ind]
                                qv = records["qv"][ind]
                            qa = ((qe_sum + cum_qe) / qv) * 100 if qv else 0
                            team_run_rate[str(period)][str(kd)] = str(qa)
                curr_period = find_period(
                    datetime.today().month, start_month, schedule.lower()
                )
                qrr = {
                    "team_run_rate": team_run_rate,
                    "curr_period": curr_period,
                }
                all_quotas[category] = qrr
            else:
                all_quotas[category] = {
                    "error": "Quota not available for payees or teams"
                }
        logger.info("END: QUOTA RUN RATE QUERY")
        return all_quotas
    else:
        logger.info(
            "END: QUOTA RUN RATE QUERY - QUOTA NOT AVAILABLE FOR PAYEES OR TEAMS"
        )
        return {"error": "Quota not available for payees or teams"}


def get_current_prev_end_dates(end_date_list, schedule):
    schedule = schedule.lower()
    curr_period_date = None
    prev_period_date = None
    curr_year = date.today().year
    today = timezone.now()
    for i in range(1, len(end_date_list)):
        if end_date_list[i - 1] < today <= end_date_list[i]:
            curr_period_date = end_date_list[i]
            prev_period_date = end_date_list[i - 1]
        if end_date_list[i - 1] == today:
            curr_period_date = end_date_list[i - 1]
            curr_month = date.today().month
            if schedule == Freq.MONTHLY.value:
                prev_month = curr_month - 1
                prev_year = curr_year
            elif schedule == Freq.QUARTERLY.value:
                prev_month = curr_month - 3
                prev_year = curr_year
            elif schedule == Freq.HALFYEARLY.value:
                prev_month = curr_month - 6
                prev_year = curr_year
            else:
                prev_month = curr_month - 3
                prev_year = curr_year
            if prev_month <= 0:
                prev_month = 12 - prev_month
                prev_year = curr_year - 1
            prev_month_days = calendar.monthrange(prev_year, prev_month)
            prev_month_end_date = date(
                prev_year, prev_month, prev_month_days[1]
            ).strftime("%Y-%m-%d")
            prev_period_date = make_aware(
                end_of_day(parse(prev_month_end_date, dayfirst=False))
            )
    return [curr_period_date, prev_period_date]


def get_end_dates_list(year, start_month, schedule):
    schedule = schedule.lower()
    end_dates_list = []
    is_year_added = False
    if start_month != 0:
        year = year - 1
    if schedule == Freq.MONTHLY.value:
        for i in range(1, 13):
            month = i + start_month
            if month <= 12:
                end_dates_list.append(get_end_date_of_month(month, year))
            else:
                if not is_year_added:
                    year = year + 1
                    is_year_added = True
                month = month - 12
                end_dates_list.append(get_end_date_of_month(month, year))
    if schedule == Freq.QUARTERLY.value:
        for i in range(1, 5):
            month = i * 3 + start_month
            if month <= 12:
                end_dates_list.append(get_end_date_of_month(month, year))
            else:
                if not is_year_added:
                    year = year + 1
                    is_year_added = True
                month = month - 12
                end_dates_list.append(get_end_date_of_month(month, year))
    if schedule == Freq.HALFYEARLY.value:
        for i in range(1, 3):
            month = i * 6 + start_month
            if month <= 12:
                end_dates_list.append(get_end_date_of_month(month, year))
            else:
                if not is_year_added:
                    year = year + 1
                    is_year_added = True
                month = month - 12
                end_dates_list.append(get_end_date_of_month(month, year))
    if schedule == Freq.ANNUAL.value:
        for i in range(1, 2):
            month = i * 12 + start_month
            if month <= 12:
                end_dates_list.append(get_end_date_of_month(month, year))
            else:
                if not is_year_added:
                    year = year + 1
                    is_year_added = True
                month = month - 12
                end_dates_list.append(get_end_date_of_month(month, year))
    return end_dates_list


def get_end_dates_list_for_period(start_month, end_month, schedule):
    schedule = schedule.lower()
    end_dates_list = set()

    if schedule == Freq.MONTHLY.value:
        for _date in date_range(start_month, end_month, freq="MS").tolist():
            year = _date.year
            month = _date.month
            end_dates_list.add(get_end_date_of_month(month, year))

    if schedule == Freq.QUARTERLY.value:
        for _date in date_range(start_month, end_month, freq="MS").tolist():
            year = _date.year
            month = _date.month
            quarter = int(month / 4)
            end_dates_list.add(get_end_date_of_month((quarter + 1) * 3, year))

    if schedule == Freq.HALFYEARLY.value:
        for _date in date_range(start_month, end_month, freq="MS").tolist():
            year = _date.year
            month = _date.month
            quarter = int(month / 7)
            end_dates_list.add(get_end_date_of_month((quarter + 1) * 6, year))

    if schedule == Freq.ANNUAL.value:
        for _date in date_range(start_month, end_month, freq="MS").tolist():
            year = _date.year
            month = _date.month
            quarter = int(month / 13)
            end_dates_list.add(get_end_date_of_month((quarter + 1) * 12, year))

    return sorted(list(end_dates_list))


def get_dates_list_for_run_rate(year, start_month, schedule):
    schedule = schedule.lower()
    dates = {}
    if start_month != 0:
        year = year - 1
    start_month = start_month + 1
    if schedule == Freq.MONTHLY.value:
        count = 1
        for i in range(0, 12):
            month = i + start_month
            if month > 12:
                month = month - 12
                year = year + 1
            date_list = get_all_dates_for_month(month, year)
            dates[count] = date_list
            count = count + 1
    if schedule == Freq.QUARTERLY.value:
        count = 1
        for i in range(start_month, 12 + start_month, 3):
            date_list = []
            month = i
            if i > 12:
                month = month - 12
                year = year + 1
            for j in range(month, month + 3):
                date_list = date_list + get_all_dates_for_month(j, year)
            dates[count] = date_list
            count = count + 1
    if schedule == Freq.HALFYEARLY.value:
        count = 1
        for i in range(start_month, 12 + start_month, 6):
            date_list = []
            month = i
            if month > 12:
                month = month - 12
                year = year + 1
            for j in range(month, month + 6):
                if j > 12:
                    j = j - 12
                if j == 1:
                    year = year + 1
                date_list = date_list + get_all_dates_for_month(j, year)
            dates[count] = date_list
            count = count + 1
    if schedule == Freq.ANNUAL.value:
        date_list = []
        month = start_month
        for j in range(month, month + 12):
            if j > 12:
                j = j - 12
            if j == 1:
                year = year + 1
            date_list = date_list + get_all_dates_for_month(j, year)
        dates[1] = date_list
    return dates


def get_all_dates_for_month(month, year):
    month_dates = calendar.monthrange(year, month)
    d1 = date(year, month, 1)
    d2 = date(year, month, month_dates[1])
    delta = d2 - d1
    date_list = []
    for i in range(delta.days + 1):
        day_date = (d1 + timedelta(days=i)).strftime("%Y-%m-%d")
        date_list.append(day_date)
    return date_list


def get_start_end_for_year(year, start_month):
    if start_month != 0:
        start_date = make_aware(
            start_of_day(parse(date(year - 1, start_month + 1, 1).strftime("%Y-%m-%d")))
        )
        last_day = calendar.monthrange(year, start_month)[1]
        end_date = make_aware(
            end_of_day(
                parse(
                    date(year, start_month, last_day).strftime("%Y-%m-%d"),
                    dayfirst=False,
                )
            )
        )
    else:
        start_date = make_aware(
            start_of_day(parse(date(year, 1, 1).strftime("%Y-%m-%d")))
        )
        end_date = make_aware(
            end_of_day(parse(date(year, 12, 31).strftime("%Y-%m-%d"), dayfirst=False))
        )

    return start_date, end_date


def get_leader_team_and_members(client_id, params):
    logger.info("START: DYNAMIC TEAM DIRECT MEMBERS QUERY")
    effective_date = date.today()
    params["effective_date"] = effective_date
    params["order_by_full_name"] = True

    quota_filters = {
        "quota_category": params.get("quota_category"),
        "quota_schedule": params.get("quota_schedule"),
        "quota_year": params.get("quota_year"),
    }
    params["quota_filters"] = quota_filters
    employee_dict = get_manager_details_with_filters(client_id, params)

    if not employee_dict:
        return []
    filters = {
        "employee_dict": employee_dict,
        "quota_filters": quota_filters,
        "effective_date": effective_date,
    }
    result = get_reportee_details_with_quota(client_id, filters)
    logger.info("END: DYNAMIC TEAM DIRECT MEMBERS QUERY")
    return result


def get_period(schedule, period, start_month):
    schedule = schedule.lower()
    if schedule == Freq.MONTHLY.value:
        period = period + start_month
        if period > 11:
            period = period - 12
    periods = {
        "monthly": [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ],
        "quarterly": ["Q1", "Q2", "Q3", "Q4"],
        "halfyearly": ["H1", "H2"],
        "annual": ["Year"],
    }
    return periods[schedule][period]


def get_run_rate_dict(schedule):
    run_rate = {}
    schedule = schedule.lower()
    if schedule == Freq.MONTHLY.value:
        for i in range(1, 13):
            run_rate[str(i)] = {}
    if schedule == Freq.QUARTERLY.value:
        for i in range(1, 5):
            run_rate[str(i)] = {}
    if schedule == Freq.HALFYEARLY.value:
        for i in range(1, 3):
            run_rate[str(i)] = {}
    if schedule == Freq.ANNUAL.value:
        run_rate[str(1)] = {}
    return run_rate


def calculate_ytd_values(qa_list):
    qa_qtd = {}
    cum_qe_qtd = {}
    j = 1
    for i in range(1, len(qa_list), 3):
        cum_qe = (
            qa_list[str(i)]["cum_qe"]
            + qa_list[str(i + 1)]["cum_qe"]
            + qa_list[str(i + 2)]["cum_qe"]
        )
        qv = (
            qa_list[str(i)]["qv"]
            + qa_list[str(i + 1)]["qv"]
            + qa_list[str(i + 2)]["qv"]
        )
        qa_qtd[str(j)] = str(round((cum_qe / qv) * 100 if qv else 0, 2))
        cum_qe_qtd[str(j)] = str(round(cum_qe, 2))
        j += 1
    return {"qa_qtd": qa_qtd, "cum_qe_qtd": cum_qe_qtd}


def calculate_ytd_values_for_cum_qe(qa_list):
    j = 1
    qa_qtd = {}
    for i in range(1, len(qa_list), 3):
        cum_qe = (
            decimal.Decimal(qa_list[str(i)]["cum_qe"])
            + decimal.Decimal(qa_list[str(i + 1)]["cum_qe"])
            + decimal.Decimal(qa_list[str(i + 2)]["cum_qe"])
        )
        qv = (
            decimal.Decimal(qa_list[str(i)]["qv"])
            + decimal.Decimal(qa_list[str(i + 1)]["qv"])
            + decimal.Decimal(qa_list[str(i + 2)]["qv"])
        )
        if "reportee_list" in qa_list[str(i)]:
            rep_dict = {}
            rep_dict_cum_qe = {}
            rep_list = (
                qa_list[str(i)]["reportee_list"]
                + qa_list[str(i + 1)]["reportee_list"]
                + qa_list[str(i + 2)]["reportee_list"]
            )
            for rep in rep_list:
                if not rep["employee_email_id"] in rep_dict:
                    rep_dict[rep["employee_email_id"]] = decimal.Decimal(rep["qa"])
                    rep_dict_cum_qe[rep["employee_email_id"]] = decimal.Decimal(
                        rep["cum_qe"]
                    )
                else:
                    rep_dict[rep["employee_email_id"]] += decimal.Decimal(rep["qa"])
                    rep_dict_cum_qe[rep["employee_email_id"]] += decimal.Decimal(
                        rep["cum_qe"]
                    )
            final_rep_list = []
            for key, value in rep_dict.items():
                final_rep_list.append(
                    {
                        "employee_email_id": key,
                        "qa": str(value),
                        "cum_qe": str(rep_dict_cum_qe[key]),
                    }
                )

            qa_qtd[str(j)] = {
                "team_qa": str(round((cum_qe / qv) * 100 if qv else 0, 2)),
                "cum_qe": str(cum_qe),
                "qv": str(qv),
                "reportee_list": final_rep_list,
            }
            j += 1
        else:
            qa_qtd[str(j)] = {
                "qa": str(round((cum_qe / qv) * 100 if qv else 0, 2)),
                "cum_qe": str(cum_qe),
                "qv": str(qv),
            }
            j += 1
    return qa_qtd
