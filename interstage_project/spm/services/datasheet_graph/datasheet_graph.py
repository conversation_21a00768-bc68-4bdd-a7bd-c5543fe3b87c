import collections
import logging
import uuid
from dataclasses import dataclass
from datetime import datetime
from functools import reduce
from typing import Optional

import networkx as nx
import pydash
import pytz
from django.db import connection

from commission_engine.services.data_sources.object_factory import (
    create_report_data_context,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    get_datasheets_for_databooks_by_sync_type,
    knowledge_date_map_for_custom_and_report_objects,
)
from commission_engine.types import HierarchyMetaData, HierarchyUtils
from commission_engine.utils.general_data import (
    DATASHEET_SOURCE,
    DATASHEET_TRANSFORMATIONS,
    DatasheetDataRegenerateReason,
    ReportObject,
)
from commission_engine.utils.general_utils import log_time_taken
from spm.services.custom_object_services.custom_object_service import (
    get_custom_object_id_to_name_map,
)
from spm.services.datasheet_services import get_force_skip_enabled_datasheet_ids
from spm.services.datasheet_variable_services import get_hierarchy_variables

from .custom_exceptions import (
    CircularDependencyError,
    IncludeStaleInformationQueryOnDatasheetGraphException,
    InvalidSourceTypeException,
    NodeNotFoundException,
)

logger = logging.getLogger(__name__)


"""

        ..                                                                         ..
      .    .                     ..                                               .  .
     .      .                   .   .                    ..                      .    .
    .         .                .     .                  .  .                    .  C4  .
  .            .              .       .                .    .            +---- .        . ------------+
 .      C1       .           .         .              .      .           |     ...........            |
.                 .         .     C2    .            .   C3   .          |             |              |
....................       .             .          .          .         |             |              |
         |                .................         .............        |             |              |
         |                      |                    |                   |             |              |
         |                      |                    |                   |             |              |
         v                      v                    |                   |             |              |
   +-----------+           +------------+            |                   |             |              |
   |           |           |            |            |                   |             |              |
   |    D1     |           |      D2    +-------+    |                   v             v              v
   |           |           |            |       |    |                +---------+    +--------+  +-------+
   +--+--------+           +----+-------+       |    |                |         |    |        |  |       |
      |                         |               |    |                |    D6   |    |        |  |   D8  |
      |                         |               |    |                |         |    |  D7    |  |       |
      |                         |               |    |                |         |    |        |  |       |
      |                         |               |    |                +---------+    +--------+  +-------+
      |                         |               v    v
      |          +---------+    |             +-----------+
      +--------->|         |<---+             |           |
                 |   D3    |                  |   D4      |
                 |         |                  |           |
                 |         |                  |           |
                 +--+------+                  +-----------+
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |                                |
                    |            +----------+        |
                    |            |          |        |
                    |            |          |<-------+
                    +----------->|     D5   |
                                 |          |
                                 |          |
                                 +----------+


    C1,C2,C3,C4 -> Custom Objects
    D1,D2,D3,D4,D5,D6,D7,D8 -> Datasheets
   

    """


def handle_date_to_str(kd: datetime):
    if kd:
        return kd.isoformat()
    return None


class TransformationSpec:
    def __init__(self, trans_spec: Optional[tuple] = None):
        self.trans_spec = trans_spec

    def to_dict(self) -> dict:
        """
        Returns a dict representation of the TransformationSpec
        """
        return {"trans_spec": self.trans_spec}


# Holds useful information about datasheets that are stale and the order in which they need
# to be executed
StaleDetails = collections.namedtuple(
    "StaleDetails",
    [
        "stale_datasheet_execution_plan",
        "stale_datasheet_list",
        "total_datasheet_count",
        "skipped_stale_datasheets",
    ],
)


@dataclass(frozen=True)
class GraphNode:
    """
    Represents a node in the DatasheetGraph
    """

    client_id: int = None
    node_id: str = None
    last_knowledge_date: datetime = None
    name: str = None


@dataclass(frozen=True)
class DatasheetNode(GraphNode):
    """
    Specialized version of GraphNode that represents a datasheet
    """

    source_id: str = None
    databook_id: str = None
    source_type: str = None
    transformations: TransformationSpec = None
    order: int = None
    primary_key: tuple = None
    is_pk_modified: bool = False
    last_global_adj_time: datetime = None
    last_local_adj_time: datetime = None
    last_reverted_global_time: datetime = None
    last_reverted_local_time: datetime = None
    last_ds_modified_time: datetime = None
    data_origin: str = None
    is_stale: bool = False
    is_calc_field_changed: bool = False
    is_config_changed: bool = False

    def to_dict(self) -> dict:
        """
        Returns a dict representation of the DatasheetNode
        """
        return {
            "client_id": self.client_id,
            "node_id": self.node_id,
            "last_knowledge_date": handle_date_to_str(self.last_knowledge_date),
            "name": self.name,
            "source_id": self.source_id,
            "source_type": self.source_type,
            "transformations": self.transformations.to_dict(),
            "order": self.order,
            "data_origin": self.data_origin,
            "primary_key": self.primary_key,
            "is_pk_modified": self.is_pk_modified,
            "is_stale": self.is_stale,
            "last_global_adj_time": handle_date_to_str(self.last_global_adj_time),
            "last_local_adj_time": handle_date_to_str(self.last_local_adj_time),
            "last_ds_modified_time": handle_date_to_str(self.last_ds_modified_time),
        }


@dataclass(frozen=True)
class CustomObjectNode(GraphNode):
    """
    Specialized version of GraphNode that represents a custom object
    """

    object_type: str = None

    def to_dict(self) -> dict:
        """
        Returns a dict representation of the CustomObjectNode
        """
        return {
            "client_id": self.client_id,
            "node_id": self.node_id,
            "last_knowledge_date": handle_date_to_str(self.last_knowledge_date),
            "object_type": self.object_type,
            "name": self.name,
        }


@dataclass(frozen=True)
class ReportObjectNode(GraphNode):
    """
    Specialized version of GraphNode that represents a report object
    """

    report_type: str = None

    def to_dict(self) -> dict:
        """
        Returns a dict representation of the CustomObjectNode
        """
        return {
            "client_id": self.client_id,
            "node_id": self.node_id,
            "last_knowledge_date": handle_date_to_str(self.last_knowledge_date),
            "object_type": self.report_type,
            "name": self.name,
        }


@log_time_taken()
class DataSheetGraph:
    def __init__(
        self,
        client_id: int,
        databook_id: str | None = None,
        include_stale_information_query: bool = False,
        knowledge_date: datetime | None = None,
    ):
        self.client_id: int = client_id
        self.databook_id: str | None = databook_id
        self._include_stale_information_query: bool = include_stale_information_query
        self.knowledge_date: datetime | None = knowledge_date
        self.graph = nx.DiGraph()
        self.ds_info = {}
        self.co_info = {}
        self.report_info = {}
        self._build_graph(
            include_stale_information_query=include_stale_information_query
        )
        cycles = list(nx.simple_cycles(self.graph))
        if cycles:
            logger.exception(
                "Circular dependency in the graph for client_id %s. The circular dependencies are: %s",
                client_id,
                cycles,
            )
            raise CircularDependencyError()

    def parent(self, datasheet_id: str) -> set[GraphNode]:
        """
        Return a set of the predecessors (or parents) of the datasheet
        corresponding to the provided datasheet_id.

        Arguments:
            datasheet_id {str} -- The id of the datasheet for which to fetch the predecessors
        Returns:
            set -- A set containing the predecessors of the datasheet

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.parent(D5)
            {'D4', 'D3'}
        """
        ds_node = self.ds_info[datasheet_id]
        return set(self.graph.predecessors(ds_node))

    def children(self, datasheet_id: str) -> set[GraphNode]:
        """
        Return a set of the successors (or children) of the datasheet
        corresponding to the provided datasheet_id.

        Arguments:
            datasheet_id {str} -- The id of the datasheet for which to fetch the
            successors

        Returns:
            set -- A set containing the successors of the datasheet

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.children(D1)
            {D3}

        """
        ds_node = self.ds_info[datasheet_id]
        return set(self.graph.successors(ds_node))

    def ancestors(self, datasheet_id: str) -> set[GraphNode]:
        """
        Summary:
            Returns the ancestors of the given datasheet ID.

        Args:
            datasheet_id (str): The identifier for the datasheet whose ancestors are being retrieved.

        Returns:
            set: A set containing the ancestors of the specified datasheet.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.ancestors(D5)
            {D3,D4,D1,C1,C3,D2,C2}
        """
        ds_node = self.ds_info[datasheet_id]
        return set(nx.ancestors(self.graph, ds_node))

    def descendants(self, datasheet_id: str) -> set[GraphNode]:
        """
        Summary:
            Returns the descendants of the given datasheet ID.

        Args:
            datasheet_id (str): The identifier for the datasheet whose descendants are being retrieved.

        Returns:
            set: A set containing the descendants of the specified datasheet.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.descendants(D1)
            {D3,D5}
        """
        ds_node = self.ds_info[datasheet_id]
        return set(nx.descendants(self.graph, ds_node))

    def common_ancestors(
        self, datasheet_id1: str, datasheet_id2: str
    ) -> set[GraphNode]:
        """
        Get the common ancestors of two datasheets.

        Args:
            datasheet_id1 (str): ID of the first datasheet
            datasheet_id2 (str): ID of the second datasheet

        Returns:
            set: The set of shared ancestors between the two datasheets.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.common_ancestors(D4,D5)
            {C2,D2,C3}
        """
        ancestors_of_datasheet_id1 = self.ancestors(datasheet_id1)
        ancestors_of_datasheet_id2 = self.ancestors(datasheet_id2)
        common_ancestors = ancestors_of_datasheet_id1 & ancestors_of_datasheet_id2
        return common_ancestors

    def common_descendants(
        self, datasheet_id1: str, datasheet_id2: str
    ) -> set[GraphNode]:
        """
        Get the common descendants of two datasheets.

        Args:
            datasheet_id1 (str): ID of the first datasheet
            datasheet_id2 (str): ID of the second datasheet

        Returns:
            set: The set of shared descendants between the two datasheets.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.common_descendants(D2,D4)
            {D5}
        """
        descendants_of_datasheet_id1: set = self.descendants(datasheet_id1)
        descendants_of_datasheet_id2: set = self.descendants(datasheet_id2)
        common_descendants: set = (
            descendants_of_datasheet_id1 & descendants_of_datasheet_id2
        )
        return common_descendants

    def all_nodes(self, node_type=None, node_ids=None) -> list:
        """
        Retrieve a list of nodes based on optional filters.

        Args:
            node_type (type or None): The type of nodes to filter by. If None, no type filter is applied.
            node_ids (list or None): A list of node IDs to filter by. If None, no ID filter is applied.

        Returns:
            list: A list of nodes that match the specified filters, or all nodes if no filters are provided.
        """
        nodes = list(self.graph.nodes)

        if node_ids is not None:
            nodes = [node for node in nodes if node.node_id in node_ids]

        if node_type is not None:
            nodes = [node for node in nodes if isinstance(node, node_type)]

        return nodes

    def all_datasheet_ids(self) -> set[str]:
        """
        Gets the set of all datasheet IDs in the graph

        Args:
            self (object): The instance of the class

        Returns:
            set: A set of all datasheet IDs in the graph

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.all_datasheet_ids()
            {D1,D2,D3,D4,D5,D6,D7,D8}
        """
        return set(self.ds_info.keys())

    def independent_subgraphs(self) -> tuple:
        """Returns a tuple of tuples, which contain the ids of the nodes in each weakly connected component. Each tuple represents a subgraph.

        Args:
        self (DataSheetGraph): The Datasheet Graph object for which to find the sets of independent subgraphs.

        Returns:
        Tuple of tuples: tuple of tuple node for each weakly connected component.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.independent_subgraphs()
            (
                (D1,C1,D2,C2,D3,D4,C3,D5),
                (C4,D8,D6,D7)

            )
        """
        subgraph_set = nx.weakly_connected_components(self.graph)
        return tuple(tuple(x) for x in subgraph_set)

    @log_time_taken()
    def ancestors_by_type(self, datasheet_id: str) -> dict:
        """
        Separates dataset and object ancestors of a given datasheet id.

        Args:
            datasheet_id (str): The datasheet id that the parents should
                be found for.

        Returns:
            ret (dict): a dictionary of two sets containing ancestors of the input
                DatasheetNode and either CustomObjectNodes.

                "custom_object": A set of CustomObjectNodes that
                    are ancestors of the input datasheet id
                "datasheet": A set of DatasheetNodes that
                    are ancestors of the input datasheet id

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.separate_ds_object_ancestors(D5)
            {
                "custom_object": {C3,C1,C2},
                "datasheet": {D3,D4,D1,C1,C3,D2,C2}
            }
        """
        ancestors = self.ancestors(datasheet_id)
        ds_ancestor: set = set()
        object_ancestor: set = set()
        report_ancestor: set = set()
        for ancestor in ancestors:
            if isinstance(ancestor, DatasheetNode):
                ds_ancestor.add(ancestor)
            elif isinstance(ancestor, CustomObjectNode):
                object_ancestor.add(ancestor)

            elif isinstance(ancestor, ReportObjectNode):
                report_ancestor.add(ancestor)
        return {
            "custom_object": object_ancestor,
            "report_object": report_ancestor,
            "datasheet": ds_ancestor,
        }

    def descendants_by_type(self, node_id: str) -> dict:
        """
        Separates dataset, report, custom object ancestors of a given datasheet id.
        """
        descendants = self.descendants(datasheet_id=node_id)

        descendants_by_type = {
            "custom_object": set(),
            "report_object": set(),
            "datasheet": set(),
        }

        for descendant in descendants:
            if isinstance(descendant, DatasheetNode):
                descendants_by_type["datasheet"].add(descendant)
            elif isinstance(descendant, CustomObjectNode):
                descendants_by_type["custom_object"].add(descendant)
            elif isinstance(descendant, ReportObjectNode):
                descendants_by_type["report_object"].add(descendant)

        return descendants_by_type

    def _is_report_data_stale(self, datasheet_id: str, ancestors_by_type) -> dict:
        # This function used to check the Report object data is stale or not
        cur_ds_last_gen_time = self.ds_info[datasheet_id].last_knowledge_date
        report_ancestors = ancestors_by_type["report_object"]
        reason = None
        is_report_data_stale = False

        for report_ancestor in report_ancestors:
            # Check 6: Check if datasheet's any of report object ancestor is stale
            if report_ancestor.last_knowledge_date > cur_ds_last_gen_time:
                logger.info(
                    "Check 6: Datasheet is stale because one of its report ancestors is stale."
                )
                is_report_data_stale = True
                if reason is None:
                    reason = (
                        DatasheetDataRegenerateReason.ANCESTOR_REPORT_OBJECT_MODIFIED.value
                    )
                return {
                    "is_report_data_stale": is_report_data_stale,
                    "reason": reason,
                }

        # Check 7: Check if datasheet's any of report object ancestor source data is stale
        unique_report_node_ids = {
            report_ancestor.node_id for report_ancestor in report_ancestors
        }

        for report_node_id in unique_report_node_ids:
            src_data_context = create_report_data_context(
                client_id=self.client_id,
                object_id=report_node_id,
            )
            if src_data_context.is_data_stale():
                logger.info(
                    "Check 7: Datasheet is stale because its report source is stale."
                )
                is_report_data_stale = True
                if reason is None:
                    reason = (
                        DatasheetDataRegenerateReason.SOURCE_REPORT_OBJECT_DATA_MODIFIED.value
                    )
                return {
                    "is_report_data_stale": is_report_data_stale,
                    "reason": reason,
                }

        return {
            "is_report_data_stale": is_report_data_stale,
            "reason": reason,
        }

    def is_datasheet_data_stale(self, datasheet_id: str) -> dict:
        """
        Checks if a datasheet data is stale or not.

        Args:
            datasheet_id: string

        return {
            "is_report_data_stale": Boolean,
            "is_datasheet_stale": Boolean,
            "reason": Enum,
        }

        """
        if self._include_stale_information_query is False:
            raise IncludeStaleInformationQueryOnDatasheetGraphException(
                function_name="is_datasheet_data_stale"
            )

        ancestors_by_type = self.ancestors_by_type(datasheet_id)
        ds_ancestors = ancestors_by_type["datasheet"]
        cur_ds_last_gen_time = self.ds_info[datasheet_id].last_knowledge_date

        is_datasheet_stale = False
        reason = None

        current_datasheet = self.ds_info[datasheet_id]

        # Check 1: Check if current datasheet's configuration is modified, then the datasheet is stale
        if (
            current_datasheet.is_calc_field_changed
            or current_datasheet.is_config_changed
            or current_datasheet.is_pk_modified
        ):
            logger.info(
                f"Check 1: {datasheet_id} Datasheet is stale because configuration of the datasheet has been changed."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.CURRENT_SHEET_CONFIG_MODIFIED.value

        # Check 2: Check if any of the ancestor's datadsheet configuration is modified, then the datasheet is stale
        elif any(
            self.ds_info[ds_ancestor.node_id].is_calc_field_changed
            or self.ds_info[ds_ancestor.node_id].is_config_changed
            or self.ds_info[ds_ancestor.node_id].is_pk_modified
            for ds_ancestor in ds_ancestors
        ):
            logger.info(
                f"Check 2: {datasheet_id} is stale because one of the ancestor datasheet configuration is modified."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.ANCESTOR_SHEET_CONFIG_MODIFIED.value

        # Check 3: Check if any of the ancestor's or current datasheet adjustment time or modified time is greater than the current datasheet's
        # last generate time, then the datasheet is stale
        elif any(
            max(
                ds_ancestor.last_global_adj_time,
                ds_ancestor.last_knowledge_date,
                ds_ancestor.last_reverted_global_time,
            )
            > cur_ds_last_gen_time
            for ds_ancestor in list(ds_ancestors) + [current_datasheet]
        ):
            logger.info(
                f"Check 3: {datasheet_id} Datasheet is stale because one of its ancestors is stale."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.ANCESTOR_MODIFIED.value

        # Check 4: Check if datasheet's last local adjustment time is greater than the current datasheet's last generate time
        elif self.ds_info[datasheet_id].last_local_adj_time > cur_ds_last_gen_time:
            logger.info(
                f"Check 4: {datasheet_id} Datasheet is stale because there is some local adjustment after last generate time."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.LOCAL_ADJUSTMENT_ADDED.value

        # Check 5: Check if datasheet's any of custom object ancestor is stale
        elif any(
            object_ancestor.last_knowledge_date > cur_ds_last_gen_time
            for object_ancestor in ancestors_by_type["custom_object"]
        ):
            logger.info(
                f"Check 5: {datasheet_id} Datasheet is stale because one of its  custom object ancestors is stale."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.ANCESTOR_CUSTOM_OBJECT_MODIFIED.value

        # Check 6: Check if datasheet's last reverted local adjustment time is greater than the current datasheet's last generate time
        elif self.ds_info[datasheet_id].last_reverted_local_time > cur_ds_last_gen_time:
            logger.info(
                f"Check 6: {datasheet_id} Datasheet is stale because there is some local adjustment reverted after last generate time."
            )
            is_datasheet_stale = True
            reason = DatasheetDataRegenerateReason.LOCAL_ADJUSTMENT_REVERTED.value

        # REPORT OBJECT STALENESS CHECKS
        report_data_stale_result = self._is_report_data_stale(
            datasheet_id, ancestors_by_type
        )

        # Combining both the datasheet data stale and report data stale results
        is_report_data_stale = report_data_stale_result["is_report_data_stale"]
        is_datasheet_stale = (
            is_datasheet_stale or report_data_stale_result["is_report_data_stale"]
        )
        if reason is None:
            reason = report_data_stale_result["reason"]

        return {
            "is_report_data_stale": is_report_data_stale,
            "is_datasheet_stale": is_datasheet_stale,
            "reason": reason,
        }

    @staticmethod
    def _dict_exclude_key(dict_: dict, key: str):
        dict_.pop(key)
        return dict_

    def _execute_sql_query(self):
        """
        The below query is used to get the datasheet information for the given client_id and databook_id which we used to bulid node for graph.
        """
        client_id: int = self.client_id
        logger.info("BEGIN: Executing SQL query to get datasheet information")

        params = {"client_id": client_id}
        # If the databook_id is not None means
        # we need to build the graph at databook level.

        # If the databook_id is None means
        # we need to build the graph at client level.
        databook_filter = (
            "AND databook_id = %(databook_id)s" if self.databook_id is not None else ""
        )
        if self.databook_id is not None:
            params["databook_id"] = self.databook_id

        sql_query = """
        SELECT 
            datasheet.datasheet_id, 
            datasheet.knowledge_begin_date AS ds_kbd, 
            dbkd_pkd_map.knowledge_begin_date AS dbkd_pkd_map_kbd, 
            ds_adjustments.local_adj, 
            ds_adjustments.global_adj, 
            ds_adjustments.local_adj_revert, 
            ds_adjustments.global_adj_revert 
            FROM 
            (
                SELECT 
                datasheet_id, 
                knowledge_begin_date 
                FROM 
                datasheet 
                WHERE 
                client_id = %(client_id)s {databook_filter}
                AND is_deleted = false 
                AND knowledge_end_date IS NULL
            ) datasheet  
            LEFT JOIN (
                SELECT 
                datasheet_id, 
                knowledge_begin_date 
                FROM 
                dbkd_pkd_map dpm 
                WHERE 
                client_id = %(client_id)s {databook_filter}
                AND is_deleted = false 
                AND knowledge_end_date IS NULL
            ) dbkd_pkd_map ON datasheet.datasheet_id = dbkd_pkd_map.datasheet_id 
            LEFT JOIN (
                SELECT 
                datasheet_id, 
                Max(
                    CASE WHEN is_global = false AND knowledge_end_date IS null THEN knowledge_begin_date end
                ) AS local_adj, 
                Max(
                    CASE WHEN is_global = true AND knowledge_end_date IS NULL THEN knowledge_begin_date end
                ) AS global_adj,
                Max(
                    CASE WHEN is_global = false AND knowledge_end_date IS NOT NULL THEN knowledge_end_date end
                ) AS local_adj_revert, 
                Max(
                    CASE WHEN is_global = true AND knowledge_end_date IS NOT NULL THEN knowledge_end_date end
                ) AS global_adj_revert 
                FROM 
                datasheet_adjustments 
                WHERE 
                client_id = %(client_id)s {databook_filter}
                AND is_deleted = false 
                AND (
                    is_global = false 
                    OR is_global = true
                )
                GROUP BY 
                datasheet_id
            ) ds_adjustments ON datasheet.datasheet_id = ds_adjustments.datasheet_id;
        """.format(
            databook_filter=databook_filter
        )

        keys = [
            "datasheet_id",
            "datasheet_kbd",
            "dbkd_pkd_kbd",
            "max_local_kd",
            "max_global_kd",
            "last_reverted_local_kd",
            "last_reverted_global_kd",
        ]
        cursor = connection.cursor()
        cursor.execute(sql_query, params)
        logger.info("END: Executing SQL query to get datasheet information")
        result = cursor.fetchall()
        result = [dict(zip(keys, values)) for values in result]
        transformed_data = {
            str(entry["datasheet_id"]): self._dict_exclude_key(entry, "datasheet_id")
            for entry in result
        }

        return transformed_data

    @log_time_taken()
    def _build_nodes(self, ds_list, include_stale_information_query=False):
        results = {}
        custom_report_to_max_kbd = {}
        datetime_min = datetime.min.replace(tzinfo=pytz.UTC)
        if include_stale_information_query:
            results = self._execute_sql_query()

            custom_report_to_max_kbd = knowledge_date_map_for_custom_and_report_objects(
                client_id=self.client_id, datasheet_list=ds_list
            )

            # If any latest_knowledge_date is None, we set datetime_min as the value for that key.
            # This prevents errors when comparing datetime values for calculating stale information.
            for object_id, latest_knowledge_date in custom_report_to_max_kbd.items():
                if latest_knowledge_date is None:
                    custom_report_to_max_kbd[object_id] = datetime_min

        constructed_datasheet_nodes = {}
        constructed_custom_object_nodes = {}
        constructed_report_object_nodes = {}

        # If include_stale_information_query is False, it means we didn't include the datasheet
        # knowledge date metadata's in the graph construction query.
        #
        # If we set datetime_min as the default value to the minimum time of datetime, it may lead to incorrect
        # calculations of the datasheet stale information when include_stale_information_query is False.
        # Therefore, we are intentionally setting datetime_min as None.
        if include_stale_information_query is False:
            datetime_min = None

        co_id_to_name_map = get_custom_object_id_to_name_map(client_id=self.client_id)
        for ds in ds_list:
            datasheet_id = str(ds.datasheet_id)
            trans_spec_obj = TransformationSpec(tuple(ds.transformation_spec))

            # In temporal splice transformation, any source type can be selected inside the transformation dict,
            # so parse the dict and include all the sources in the datasheet node
            for transformation_dict in ds.transformation_spec:
                if (
                    transformation_dict["type"]
                    == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name
                ):
                    for source in transformation_dict["meta"]:
                        if source["source_type"] == DATASHEET_SOURCE.OBJECT.value:
                            node_name = co_id_to_name_map.get(
                                int(source["source_id"]), "Unknown"
                            )

                            object_last_knowledge_date = custom_report_to_max_kbd.get(
                                source["source_id"], datetime_min
                            )
                            if object_last_knowledge_date is None:
                                object_last_knowledge_date = datetime_min
                            object_node_dict = {
                                "client_id": self.client_id,
                                "name": node_name,
                                "node_id": source["source_id"],
                                "object_type": source["source_type"],
                                "last_knowledge_date": object_last_knowledge_date,
                            }

                            object_node = CustomObjectNode(**object_node_dict)
                            constructed_custom_object_nodes[source["source_id"]] = (
                                object_node
                            )

                        elif source["source_type"] == DATASHEET_SOURCE.REPORT.value:
                            node_name = source["source_id"] + " report"

                            object_last_knowledge_date = custom_report_to_max_kbd.get(
                                source["source_id"], datetime_min
                            )
                            if object_last_knowledge_date is None:
                                object_last_knowledge_date = datetime_min
                            report_node_dict = {
                                "client_id": self.client_id,
                                "name": node_name,
                                "node_id": source["source_id"],
                                "report_type": source["source_type"],
                                "last_knowledge_date": object_last_knowledge_date,
                            }

                            report_node = ReportObjectNode(**report_node_dict)
                            constructed_report_object_nodes[source["source_id"]] = (
                                report_node
                            )

                elif (
                    transformation_dict["type"]
                    == DATASHEET_TRANSFORMATIONS.GET_USER_PROPERTIES.name
                ):
                    node_name = "user report"
                    source_id = "user"
                    source_type = "report"
                    object_last_knowledge_date = custom_report_to_max_kbd.get(
                        source_id, datetime_min
                    )
                    report_node_dict = {
                        "client_id": self.client_id,
                        "name": node_name,
                        "node_id": source_id,
                        "report_type": source_type,
                        "last_knowledge_date": object_last_knowledge_date,
                    }

                    report_node = ReportObjectNode(**report_node_dict)
                    constructed_report_object_nodes[source_id] = report_node

            if ds.source_type == DATASHEET_SOURCE.OBJECT.value:
                node_name = co_id_to_name_map.get(int(ds.source_id), "Unknown")

                object_last_knowledge_date = custom_report_to_max_kbd.get(
                    ds.source_id, datetime_min
                )
                if object_last_knowledge_date is None:
                    object_last_knowledge_date = datetime_min
                object_node_dict = {
                    "client_id": self.client_id,
                    "name": node_name,
                    "node_id": ds.source_id,
                    "object_type": ds.source_type,
                    "last_knowledge_date": object_last_knowledge_date,
                }

                object_node = CustomObjectNode(**object_node_dict)
                constructed_custom_object_nodes[ds.source_id] = object_node

            elif ds.source_type == DATASHEET_SOURCE.REPORT.value:
                node_name = ds.source_id + " report"

                object_last_knowledge_date = custom_report_to_max_kbd.get(
                    ds.source_id, datetime_min
                )
                if object_last_knowledge_date is None:
                    object_last_knowledge_date = datetime_min
                report_node_dict = {
                    "client_id": self.client_id,
                    "name": node_name,
                    "node_id": ds.source_id,
                    "report_type": ds.source_type,
                    "last_knowledge_date": object_last_knowledge_date,
                }

                report_node = ReportObjectNode(**report_node_dict)
                constructed_report_object_nodes[ds.source_id] = report_node

            result = results.get(datasheet_id, {})

            last_modified_time: datetime | None = (
                pydash.get(result, "datasheet_kbd") or datetime_min
            )
            last_generated_time: datetime | None = (
                pydash.get(result, "dbkd_pkd_kbd") or datetime_min
            )
            last_local_adj_time_ds: datetime | None = (
                pydash.get(result, "max_local_kd") or datetime_min
            )
            last_global_adj_time_ds: datetime | None = (
                pydash.get(result, "max_global_kd") or datetime_min
            )
            last_reverted_local_time: datetime | None = (
                pydash.get(result, "last_reverted_local_kd") or datetime_min
            )
            last_reverted_global_time: datetime | None = (
                pydash.get(result, "last_reverted_global_kd") or datetime_min
            )

            datasheet_node_dict = {
                "client_id": self.client_id,
                "node_id": datasheet_id,
                "databook_id": str(ds.databook_id),
                "name": ds.name,
                "source_id": ds.source_id,
                "source_type": ds.source_type,
                "transformations": trans_spec_obj,
                "order": ds.order,
                "data_origin": ds.data_origin,
                "primary_key": tuple(ds.primary_key),
                "is_pk_modified": ds.is_pk_modified,
                "last_global_adj_time": last_global_adj_time_ds,
                "last_local_adj_time": last_local_adj_time_ds,
                "last_knowledge_date": last_generated_time,
                "last_ds_modified_time": last_modified_time,
                "last_reverted_global_time": last_reverted_global_time,
                "last_reverted_local_time": last_reverted_local_time,
                "is_calc_field_changed": ds.is_calc_field_changed,
                "is_config_changed": ds.is_config_changed,
            }

            datasheet_node = DatasheetNode(**datasheet_node_dict)
            constructed_datasheet_nodes[datasheet_id] = datasheet_node
        return (
            constructed_datasheet_nodes,
            constructed_custom_object_nodes,
            constructed_report_object_nodes,
        )

    @log_time_taken()
    def _build_graph(self, include_stale_information_query=False):
        """
        Builds the graph staructure based on the datasheet_list.
        """
        ds_list = get_datasheets_for_databooks_by_sync_type(
            client_id=self.client_id,
            databook_ids=self.databook_id,
            knowledge_date=self.knowledge_date,
        )
        (
            ds_id_to_datasheet_nodes,
            co_id_to_customobject_nodes,
            report_id_to_reportobject_nodes,
        ) = self._build_nodes(
            ds_list, include_stale_information_query=include_stale_information_query
        )

        for datasheet_obj in ds_list:
            ds_id = str(datasheet_obj.datasheet_id)
            child_node = ds_id_to_datasheet_nodes[ds_id]
            if datasheet_obj.source_type == DATASHEET_SOURCE.OBJECT.value:
                source_id = datasheet_obj.source_id
                parent_node = co_id_to_customobject_nodes[source_id]

            elif datasheet_obj.source_type == DATASHEET_SOURCE.REPORT.value:
                source_id = datasheet_obj.source_id
                parent_node = report_id_to_reportobject_nodes[source_id]

            elif datasheet_obj.source_type == DATASHEET_SOURCE.DATASHEET.value:
                source_id = str(
                    datasheet_obj.source_id
                )  # converting the source ds uuid to string
                parent_node = ds_id_to_datasheet_nodes[source_id]

            self.graph.add_node(child_node)
            self.graph.add_edge(parent_node, child_node)

            for transformation_dict in datasheet_obj.transformation_spec:
                if (
                    transformation_dict["type"]
                    == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name
                ):
                    for source in transformation_dict["meta"]:
                        if source["source_type"] == DATASHEET_SOURCE.DATASHEET.value:
                            parent_node = ds_id_to_datasheet_nodes[source["source_id"]]
                            self.graph.add_edge(parent_node, child_node)

                        elif source["source_type"] == DATASHEET_SOURCE.OBJECT.value:
                            parent_node = co_id_to_customobject_nodes[
                                source["source_id"]
                            ]
                            self.graph.add_edge(parent_node, child_node)

                        elif source["source_type"] == DATASHEET_SOURCE.REPORT.value:
                            parent_node = report_id_to_reportobject_nodes[
                                source["source_id"]
                            ]
                            self.graph.add_edge(parent_node, child_node)
                elif (
                    transformation_dict["type"]
                    == DATASHEET_TRANSFORMATIONS.GET_USER_PROPERTIES.name
                ):
                    parent_node = report_id_to_reportobject_nodes["user"]
                    self.graph.add_edge(parent_node, child_node)

            # For each datasheet, add an edge from its dependency to it
            for linked_datasheet in datasheet_obj.linked_datasheets:
                parent_ds_id = linked_datasheet.datasheet_id
                parent_node = ds_id_to_datasheet_nodes[parent_ds_id]
                self.graph.add_edge(parent_node, child_node)

        self._add_hierarchy_sheet_as_dependency(ds_id_to_datasheet_nodes)

        self.ds_info = ds_id_to_datasheet_nodes
        self.co_info = co_id_to_customobject_nodes
        self.report_info = report_id_to_reportobject_nodes

    def _add_hierarchy_sheet_as_dependency(self, ds_id_to_datasheet_nodes) -> None:
        """
        Adds the hierarchy datasheet as a dependency to the datasheet graph.
        """
        hierarchy_variables = get_hierarchy_variables(
            client_id=self.client_id, databook_id=self.databook_id
        )

        for hvar in hierarchy_variables:
            hierarchy_meta = HierarchyMetaData(**hvar.meta_data[HierarchyUtils.HIERARCHY_FUNCTION_NAME.value])  # type: ignore
            if (
                hvar.linked_datasheet is not None
                and hierarchy_meta.reference_sheet
                != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value
            ):
                parent_ds_id = hvar.linked_datasheet.datasheet_id
                parent_node = ds_id_to_datasheet_nodes[parent_ds_id]
                child_node = ds_id_to_datasheet_nodes[str(hvar.datasheet_id)]
                self.graph.add_edge(parent_node, child_node)

    def roots(self) -> set[GraphNode]:
        """
        Returns the set of root nodes in the graph.
        A root node is a node with no incoming edges.

        Returns:
            A set of CustomObjectNode objects representing the root nodes.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.roots()
            {C1,C3,C2,C4}
        """
        return {node for node in self.graph.nodes() if self.graph.in_degree(node) == 0}

    def is_root(self, node: GraphNode) -> bool:
        """
        Returns True if the node is a root node, False otherwise.

        Args:
            node: A node of the graph.

        Returns:
            A boolean value.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.is_root(C2)
            True
        """
        return self.graph.in_degree(node) == 0

    def leaves(self) -> set[GraphNode]:
        """
        Returns the set of leaf nodes in the graph.
        A leaf node is a node with no outgoing edges.

        Returns:
            A set representing the leaf nodes.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.roots()
            {D5,D6,D7,D8}
        """
        return {node for node in self.graph.nodes() if self.graph.out_degree(node) == 0}

    def is_leaf(self, node: GraphNode) -> bool:
        """
        Returns True if the node is a leaf node, False otherwise.

        Args:
            node: A node of the graph.

        Returns:
            A boolean value.

        Example:
            >>> graph = DataSheetGraph(Client_ID, DB_ID)
            >>> graph.is_leave(D6)
            True
        """
        return self.graph.out_degree(node) == 0

    def serialize_data(self):
        """
        Returns the graph in a json format.
        """

        graph = nx.node_link_data(self.graph)
        nodes = graph["nodes"]
        edges = graph["links"]

        converted_nodes = []
        for node in nodes:
            node = node["id"]
            converted_node = node.to_dict()
            converted_node["is_leaf"] = self.is_leaf(node)
            converted_node["is_root"] = self.is_root(node)
            converted_nodes.append(converted_node)

        converted_edges = []
        for edge in edges:
            edge["source"] = edge["source"].to_dict()
            edge["target"] = edge["target"].to_dict()
            converted_edges.append(edge)

        graph["nodes"] = converted_nodes
        graph["links"] = converted_edges

        return graph

    def datasheet_details(self, datasheet_ids=None) -> dict:
        """
        Returns the details of the datasheet.
        """
        if datasheet_ids is None:
            datasheet_ids = self.all_datasheet_ids()

        datasheet_detail_dict = {}
        for datasheet_id in datasheet_ids:
            databook_id = self.ds_info[datasheet_id].databook_id
            datasheet_order = self.ds_info[datasheet_id].order
            datasheet_name = self.ds_info[datasheet_id].name
            source_type = self.ds_info[datasheet_id].source_type
            source_id = self.ds_info[datasheet_id].source_id
            transformation_spec = list(
                self.ds_info[datasheet_id].transformations.trans_spec
            )
            childrens = self.datasheet_childrens_list(datasheet_id=datasheet_id)
            immediate_parents = self.immediate_datasheet_parent(
                datasheet_id=datasheet_id
            )
            immediate_childrens = self.immediate_datasheet_children(
                datasheet_id=datasheet_id
            )

            from everstage_etl.databook_etl.datasheet_etl_types import (
                DatasheetExecutionDetails,
                TranformationInputDetails,
            )

            details = {
                "childrens": childrens,
                "databook_id": databook_id,
                "datasheet_order": datasheet_order,
                "datasheet_name": datasheet_name,
                "sync_run_id": uuid.uuid4(),
                "immediate_childrens": immediate_childrens,
                "immediate_parents": immediate_parents,
                "details_for_snowflake_transformation": TranformationInputDetails(
                    **{
                        "datasheet_id": datasheet_id,
                        "source_id": source_id,
                        "source_type": source_type,
                        "transformation_spec": transformation_spec,
                        "datasheet_name": datasheet_name,
                    }
                ),
            }

            datasheet_node = DatasheetExecutionDetails(**details)
            datasheet_detail_dict[datasheet_id] = datasheet_node
        return datasheet_detail_dict

    def get_stale_datasheets_to_generate(
        self,
        datasheet_ids: list[str] | None = None,
        e2e_sync_run_id: str | None = None,
    ) -> StaleDetails:
        """
        Finds all datasheets that are considered 'stale' and returns them in a particular order
        to be executed.

        Args:
        datasheet_ids: list[str] | None = None
        Consider only these datasheets and their ancestors while trying to find stale datasheets.

        If datasheet_ids is None, it means a client-level sync and we need to find stale datasheets
        across all datasheets

        Returns:
        Named tuple has a following entities
        stale_datasheet_execution_plan: set[tuple[set[str]]]
        stale_datasheet_list: set[str]
        total_datasheet_count: int -> has count of dependencies sheet for given datasheet ids

        NamedTuple(stale_datasheet_execution_plan, stale_datasheet_list, total_datasheet_count)

        (
            {
                (                                                             -> WCC_1
                    frozenset({D1, D2, D3, D4, D5}),                          -> WCC_1 order 1 datasheets
                    frozenset({D6}),                                          -> WCC_1 order 2 datasheets
                ),
                (                                                             -> WCC_2
                    frozenset({D7}),                                          -> WCC_2 order 1 datasheets
                )
                (
                    frozenset({D8}),                                          -> WCC_3 order 1 datasheets
                ),
                (
                    frozenset({DS1, DS2, DS3, DS4, DS5}),                     -> WCC_4 order 1 datasheets
                    frozenset({DS6, DS7}),                                    -> WCC_4 order 2 datasheets
                ),
            },
            {
            D1, D2, D3, D4, D5, D6, DS1, DS2, DS3, DS4, DS5, DS6, DS7
            },
            15,
        )
        """
        # Find all weakly connected components in the graph
        datasheet_lists: list[list[str]] = self.independent_datasheet_lists(
            datasheet_ids=datasheet_ids
        )

        force_skip_enabled_datasheet_ids = self.get_force_skip_enabled_datasheet_ids(
            e2e_sync_run_id=e2e_sync_run_id
        )

        # Sum of all independent datasheet lists.
        total_datasheet_count = reduce(
            lambda current_datasheet_count, datasheet_list: current_datasheet_count
            + len(datasheet_list),
            datasheet_lists,
            0,
        )

        stale_datasheet_list = set()
        stale_datasheet_execution_plan = set()
        skipped_stale_datasheets = set()
        for datasheet_list in datasheet_lists:
            execution_plan, stale_datasheet_ids, skipped_datasheet_ids = (
                self._filter_stale_datasheets(
                    datasheet_list=datasheet_list,
                    datasheet_ids_to_skip=force_skip_enabled_datasheet_ids,
                )
            )
            stale_datasheet_list.update(stale_datasheet_ids)
            stale_datasheet_execution_plan.add(execution_plan)
            skipped_stale_datasheets.update(skipped_datasheet_ids)
        logger.info(
            "GRAPH: datasheet_execution_plan, %s", stale_datasheet_execution_plan
        )
        return StaleDetails(
            stale_datasheet_execution_plan,
            stale_datasheet_list,
            total_datasheet_count,
            skipped_stale_datasheets,
        )

    def _filter_stale_datasheets(
        self, datasheet_list, datasheet_ids_to_skip=[]
    ) -> tuple[tuple[frozenset[str]], set[str], set[str]]:
        """
        This function is used to filter the stale datasheets from the given datasheet_list.
        Returns:

        execution_plan_for_single_weakly_connected_component: [tuple[frozenset[str]]]
        stale_datasheet_list: set[str]

        tuple(execution_plan_for_single_WCC, stale_datasheet_list)

        (
            {
                (                                                             -> WCC_1
                    frozenset({D1, D2, D3, D4, D5}),                          -> WCC_1 order 1 datasheets
                    frozenset({D6}),                                          -> WCC_1 order 2 datasheets
                ),
            },
            {
                D1, D2, D3, D4, D5, D6
            }
        )
        """

        is_stale_dict = {datasheet_id: False for datasheet_id in datasheet_list}

        for datasheet_id in datasheet_list:
            is_stale = is_stale_dict[datasheet_id]
            if is_stale is False:
                if self.is_datasheet_data_stale(datasheet_id)["reason"] is not None:
                    is_stale_dict[datasheet_id] = True
                    # If a datasheet is stale, mark all its descendants (children) as stale too
                    objects_to_update = self.descendants(datasheet_id)
                    for obj in objects_to_update:
                        is_stale_dict[obj.node_id] = True

        logger.info("GRAPH: datasheet_dict, %s", is_stale_dict)

        skipped_datasheet_ids = set()
        order_wise_stale_datasheets = tuple()
        stale_datasheet_ids = set()
        order_to_datasheet_ids = collections.defaultdict(list)
        for datasheet_id, is_stale in is_stale_dict.items():
            if is_stale and datasheet_id in datasheet_list:
                if str(datasheet_id) in datasheet_ids_to_skip:
                    skipped_datasheet_ids.add(datasheet_id)
                    continue
                datasheet_order = self.ds_info[datasheet_id].order
                order_to_datasheet_ids[datasheet_order].append(datasheet_id)
                stale_datasheet_ids.add(datasheet_id)

        for datasheet_order, datasheet_ids in sorted(
            order_to_datasheet_ids.items(),
            key=lambda x: x[0],
        ):
            order_wise_stale_datasheets += (frozenset(datasheet_ids),)

        logger.info(
            "GRAPH: order_wise_stale_datasheets, %s", order_wise_stale_datasheets
        )

        return order_wise_stale_datasheets, stale_datasheet_ids, skipped_datasheet_ids

    def independent_datasheet_lists(
        self,
        datasheet_ids: list | None = None,
    ) -> list[list[str]]:
        """
        Get a list of lists of datasheets that are independent of each other.

        returns:
            A list of lists of datasheet ids.
            [
                [ D1 , D2 , D3 , D4 , D5 ],
                [ D6 ],
                [ D7 ],
                [ D8 ]
            ]

        refer the graph which is in the top of this file to understand better.
        """

        # Each inner list represents a weakly connected component of the databook graph.
        # Each weakly connected component consists of a list of datasheets that are independent of each other.

        if datasheet_ids is not None:
            all_dependent_datasheet_ids = set()
            for datasheet_id in datasheet_ids:
                current_datasheet_node = self.ds_info[datasheet_id]
                ancestors = self.ancestors_by_type(datasheet_id=datasheet_id)[
                    "datasheet"
                ]
                dependency_set = {current_datasheet_node} | set(ancestors)
                all_dependent_datasheet_ids.update(dependency_set)

            datasheet_nodes: list[DatasheetNode] = list(all_dependent_datasheet_ids)
        else:
            datasheet_nodes: list[DatasheetNode] = self.all_nodes(
                node_type=DatasheetNode
            )

        subgraphs: list = [
            self.graph.subgraph(list(component))
            for component in nx.weakly_connected_components(
                self.graph.subgraph(datasheet_nodes)
            )
        ]

        independent_datasheet_lists: list = [
            [node.node_id for node in nx.topological_sort(subgraph)]
            for subgraph in subgraphs
        ]
        logger.info(
            "GRAPH: independent_datasheet_lists %s", independent_datasheet_lists
        )
        return independent_datasheet_lists

    def datasheet_childrens_list(self, datasheet_id) -> list[str]:
        """
        Return list of datasheet childrens for the given datasheet_id

        [
            datasheet_id_1,
            datasheet_id_2,
            datasheet_id_3,
        ]
        """

        datasheet_childrens = self.descendants(datasheet_id)
        datasheet_childrens_list = [datasheet_id] + [
            datasheet.node_id for datasheet in datasheet_childrens
        ]
        return datasheet_childrens_list

    def establish_paths(self, source_ids: list, destination_ids: list) -> bool:
        """
        This function will establish a path from the source_id to the destination_ids.
        """
        for source_id in source_ids:
            source_node = self.get_node(node_id=source_id)
            for destination_id in destination_ids:
                dependency_node = self.get_node(node_id=destination_id)
                self.graph.add_edge(source_node, dependency_node)

        # TODO: Throw an exception if the graph becomes cyclic after adding the node
        return nx.is_directed_acyclic_graph(self.graph)

    def create_minimal_node(
        self, node_id, node_type: DATASHEET_SOURCE, node_details=None
    ) -> None:
        """
        Create a node with minimal information and add it to the graph.

        Here the minimal information is the node_id only.
        """
        NodeClass = {
            DATASHEET_SOURCE.OBJECT: CustomObjectNode,
            DATASHEET_SOURCE.REPORT: ReportObjectNode,
            DATASHEET_SOURCE.DATASHEET: DatasheetNode,
        }.get(node_type)

        node_container = {
            DATASHEET_SOURCE.OBJECT: self.co_info,
            DATASHEET_SOURCE.REPORT: self.report_info,
            DATASHEET_SOURCE.DATASHEET: self.ds_info,
        }.get(node_type)

        if NodeClass is None or node_container is None:
            raise InvalidSourceTypeException(
                client_id=self.client_id,
                source_type=node_type,
                datasheet_id=node_id,
                databook_id=None,
            )

        if node_details is None:
            node_details = {"node_id": node_id}

        node = NodeClass(**node_details)

        # Adding node to the graph
        self.graph.add_node(node)
        node_container[node_id] = node

    def _compute_datasheet_order_for_graph(self, root_nodes: list, graph=None) -> dict:
        """
        Used to calculate the datasheet order for the given
        graph in iterative DFS fashion.
        """
        if graph:
            # If a specific graph is provided, use it
            # This is useful when we want to calculate the datasheet order for a subgraph
            self.graph = graph

        # Initialize a stack for iterative DFS
        stack: list[tuple[DatasheetNode, int]] = [(node, 0) for node in root_nodes]

        final_result = {}
        while stack:
            node, order = stack.pop()

            # Update the order of the node if it is not present in the final_result
            if node not in final_result:
                final_result[node] = order

            # If the node_id is present in the final_result then update the order
            # only if the order is greater than the previous order
            else:
                final_result[node] = max(order, final_result[node])

            for neighbor in self.graph.neighbors(node):
                # Push the neighbor node and the order + 1 to the stack
                stack.append((neighbor, order + 1))

        return final_result

    def compute_datasheet_order(
        self, datasheet_id=None, graph=None, filter_datasheet_nodes_only=True
    ) -> dict:
        """
        This function will compute the datasheet order for the weakly connected component
        of the graph which containing the given datasheet_id. If the datasheet_id is None
        then it will compute the datasheet order for the whole graph.

        A datasheet_id must be present in only one weakly connected component
        """
        if graph:
            # If a specific graph is provided, use it
            sub_graph = graph

        elif datasheet_id:
            # If no specific graph is provided, but a datasheet_id is provided, get the subgraph for that datasheet_id
            sub_graph = self.get_subgraph(datasheet_id)

        else:
            # If neither a specific graph nor a datasheet_id is provided, use the entire graph
            sub_graph = self.graph

        # TODO: Find a better way to find the root nodes of the graph

        # We are itertaing over all element in the graph to find the root nodes
        # because there is no inbulit function in networkx to find the
        # root nodes of the graph

        # Get root nodes of the subgraph
        root_nodes = [
            node for node in sub_graph.nodes() if sub_graph.in_degree(node) == 0
        ]

        # Compute the datasheet order
        datasheet_orders = self._compute_datasheet_order_for_graph(
            root_nodes=root_nodes, graph=sub_graph
        )

        # if filter_datasheet_nodes_only is False, the condition not filter_datasheet_nodes_only
        # will be True for all nodes, so all nodes will be included. If filter_datasheet_nodes_only
        # is True, only nodes that are instances of DatasheetNode will be included

        final_result = {
            node.node_id: node_order
            for node, node_order in datasheet_orders.items()
            if not filter_datasheet_nodes_only or isinstance(node, DatasheetNode)
        }

        return final_result

    def get_subgraph(self, datasheet_id: str) -> nx.DiGraph:
        """
        This function will return the subgraph containing the given datasheet_id.

        If no subgraph is containg the given datasheet_id then it will raise an exception.
        """
        weakly_connected_components = list(nx.weakly_connected_components(self.graph))

        # TODO: Find a better way to find the subgraph containing the datasheet_id
        subgraph = next(
            (
                self.graph.subgraph(component)
                for component in weakly_connected_components
                if any(node.node_id == datasheet_id for node in component)
            ),
            None,
        )

        # If there is no subgraph containing the datasheet_id then
        # here we raise an exception
        if subgraph is None:
            raise NodeNotFoundException(client_id=self.client_id, node_id=datasheet_id)
        return subgraph

    def get_node(self, node_id) -> GraphNode:
        """
        This function will return the node for the given node_id.
        """
        datasheet_graph_information = {
            **self.co_info,
            **self.report_info,
            **self.ds_info,
        }

        if node_id in datasheet_graph_information:
            return datasheet_graph_information[node_id]

        raise NodeNotFoundException(client_id=self.client_id, node_id=node_id)

    def datasheets_used_in_transformation_spec(self, datasheet_id: str) -> list[str]:
        """
        This function will return the datasheets used in the transformation spec of the given datasheet_id.
        """
        datasheets_used_in_transformation = []

        if self.ds_info[datasheet_id].transformations is not None:
            transformations = list(
                self.ds_info[datasheet_id].transformations.trans_spec
            )
            for transformation in transformations:
                if transformation["type"] in (
                    DATASHEET_TRANSFORMATIONS.JOIN.name,
                    DATASHEET_TRANSFORMATIONS.UNION.name,
                ):
                    datasheet_id = transformation["with"]
                    datasheets_used_in_transformation.append(datasheet_id)
                if (
                    transformation["type"]
                    == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name
                ):
                    for source in transformation["meta"]:
                        if source["source_type"] == "datasheet":
                            datasheets_used_in_transformation.append(
                                source["source_id"]
                            )

        return datasheets_used_in_transformation

    def remove_path(self, source_ids: list, destination_id: str) -> None:
        """
        This function will remove the path from the source_ids to the destination_id.
        """
        destination_node = self.get_node(node_id=destination_id)
        for source_id in source_ids:
            source_node = self.get_node(node_id=source_id)

            # Check if the edge exists in the graph or not
            # If the edge exists in the graph then remove the edge
            if self.graph.has_edge(source_node, destination_node):
                self.graph.remove_edge(source_node, destination_node)

    def is_node_exists(self, node_id) -> bool:
        """
        This function will check if the node exists in the graph or not.

        If the node exists in the graph then it will return True else False.
        """
        datasheet_graph_information = {
            **self.co_info,
            **self.report_info,
            **self.ds_info,
        }

        if node_id in datasheet_graph_information:
            return True
        return False

    def hierarchy_reference_datasheet_ids(self, datasheet_id: str) -> list[str]:
        """
        This function will return the hierarchy reference sheet id for the given datasheet_id.

        If the hierarchy reference sheet id is present, it will return a list of reference sheet IDs.
        If not present, it will return an empty list.

        TODO: Check if this method needs to be in this class
        """
        h_vars = get_hierarchy_variables(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=datasheet_id,
        )
        reference_sheet_ids = []
        for h_var in h_vars:
            if h_var.linked_datasheet is not None and h_var.linked_datasheet.datasheet_id != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value:  # type: ignore
                reference_sheet_ids.append(h_var.linked_datasheet.datasheet_id)

        return reference_sheet_ids

    def datasheets(self, databook_id: str | None = None) -> set[DatasheetNode]:
        """
        This function will return all the datasheet ids for the given databook_id.
        If databook_id is None, return all datasheet nodes.
        """
        datasheet_nodes = set(self.all_nodes(node_type=DatasheetNode))

        if databook_id:
            datasheet_nodes = {
                node for node in datasheet_nodes if node.databook_id == databook_id
            }

        return datasheet_nodes

    def sub_graphs(self, datasheet_ids) -> list[nx.DiGraph]:
        """
        This function will return the subgraphs for the given datasheet_ids.
        """
        datasheet_nodes = self.all_nodes(node_ids=datasheet_ids)
        subgraphs: list = [
            nx.topological_sort(self.graph.subgraph(list(component)))
            for component in nx.weakly_connected_components(
                self.graph.subgraph(datasheet_nodes)
            )
        ]

        return subgraphs

    def unqiue_databook_ids(self) -> set[str]:
        """
        This function will return the unique databook_ids present in the graph.
        """
        return {
            datasheet_node.databook_id
            for datasheet_node in self.all_nodes(node_type=DatasheetNode)
        }

    def calculate_width_and_height_for_graph(self, node_ids=None) -> tuple[int, int]:
        """
        Calculate the width and height of the graph.
        """
        if node_ids:
            # Convert node_ids to actual node objects
            nodes: list[GraphNode] = [self.get_node(node_id) for node_id in node_ids]

            # Create a subgraph with the nodes of interest
            sub_graph = self.graph.subgraph(nodes)
        else:
            sub_graph = self.graph

        datasheet_order_records = self.compute_datasheet_order(
            graph=sub_graph, filter_datasheet_nodes_only=False
        )

        order_values = collections.Counter(datasheet_order_records.values())

        # Calculate the longest path in the graph (height)
        maximum_height: int = max(order_values.keys()) - min(order_values.keys()) + 1
        logger.info("Maximum height of the graph is %s", maximum_height)

        # Calculate the number of datasheets in the longest width
        level, number_of_occurrences = order_values.most_common(1)[0]

        logger.info(
            "Maximum number of nodes is in level %s and the occurences is %s",
            level,
            number_of_occurrences,
        )

        return maximum_height, number_of_occurrences

    def associated_report_objects(
        self, datasheet_id=None, fetch_only_stale_objects=False
    ) -> set[str]:
        """
        Returns all associated report objects for the given datasheet_id.
        """
        if datasheet_id is None:
            report_object_ids = {report_object.value for report_object in ReportObject}

        else:
            report_ancestors = self.ancestors_by_type(datasheet_id=datasheet_id).get(
                "report_object", []
            )
            report_object_ids = {
                report_ancestor.node_id for report_ancestor in report_ancestors
            }

        if fetch_only_stale_objects:
            report_object_ids = {
                report_node_id
                for report_node_id in report_object_ids
                if create_report_data_context(
                    client_id=self.client_id,
                    object_id=report_node_id,
                ).is_data_stale()
            }

        return report_object_ids

    def immediate_datasheet_parent(self, datasheet_id: str) -> set:
        """
        Returns the immediate parent datasheet_ids for the given datasheet_id
        """
        datasheet_ids = set()
        ds_node = self.ds_info[datasheet_id]
        for parent in self.graph.predecessors(ds_node):
            if isinstance(parent, DatasheetNode):
                datasheet_ids.add(parent.node_id)
        return datasheet_ids

    def immediate_datasheet_children(self, datasheet_id: str) -> set:
        """
        Returns the immediate children datasheet_ids for the given datasheet_id.
        """
        datasheet_ids = set()
        ds_node = self.ds_info[datasheet_id]
        for child in self.graph.successors(ds_node):
            datasheet_ids.add(child.node_id)
        return datasheet_ids

    def root_stale_datasheet_ids(self, stale_datasheet_list: list[str]) -> set[str]:
        """
        Finds and returns the root stale datasheet_ids
        """
        root_stale_ids = set()
        for node in self.graph.nodes:
            if node.node_id in stale_datasheet_list:
                parents = self.immediate_datasheet_parent(node.node_id)
                if not any(parent in stale_datasheet_list for parent in parents):
                    root_stale_ids.add(node.node_id)
        return root_stale_ids

    def all_dependent_datasheet_ids(self, custom_object_id: str) -> set[str]:
        """
        Finds and returns all dependent datasheet_ids for the given custom_object_id.
        """
        co_node = self.co_info[custom_object_id]

        all_dependent_ds_ids = set()
        for ds_id in set(nx.descendants(self.graph, co_node)):
            all_dependent_ds_ids.add(str(ds_id.node_id))
        return all_dependent_ds_ids

    def get_sorted_datasheet_ancestor_ids(self, datasheet_id: str) -> list[str]:
        datasheet_ancestors = filter(
            lambda node: isinstance(node, DatasheetNode), self.ancestors(datasheet_id)
        )
        return [ancestor.node_id for ancestor in datasheet_ancestors]

    def get_force_skip_enabled_datasheet_ids(
        self, e2e_sync_run_id: str | None = None
    ) -> list[str]:
        """
        Function to get force skip enabled datasheet ids.
        """
        logger.info(
            f"BEGIN: Finding force skip enabled datasheet ids for client_id: {self.client_id}"
        )
        from commission_engine.accessors.client_accessor import (
            is_force_datasheet_skip_enabled,
        )

        if not is_force_datasheet_skip_enabled(
            self.client_id
        ) or not self._is_flow_eligible_for_force_datasheet_skip(e2e_sync_run_id):
            return []

        force_skip_enabled_datasheet_ids = get_force_skip_enabled_datasheet_ids(
            self.client_id
        )

        logger.info(
            f"END: Force skip enabled datasheet ids for client_id: {self.client_id}: {force_skip_enabled_datasheet_ids}"
        )
        return list(force_skip_enabled_datasheet_ids)

    def _is_flow_eligible_for_force_datasheet_skip(
        self, e2e_sync_run_id: str | None
    ) -> bool:
        """
        Check if skipping datasheets is applicable for the current ETL run.
        """
        if not e2e_sync_run_id:
            return False
        from commission_engine.services.etl_sync_status_service import (
            is_sync_triggered_from_datasheet_ui,
        )

        if is_sync_triggered_from_datasheet_ui(
            client_id=self.client_id, e2e_sync_run_id=e2e_sync_run_id
        ):
            return False
        return True
