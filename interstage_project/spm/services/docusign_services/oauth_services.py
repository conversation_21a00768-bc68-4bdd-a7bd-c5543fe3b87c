import datetime
import os

from django.core.cache import cache
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from commission_engine.accessors.client_accessor import get_client
from commission_engine.utils import SegmentEvents, SegmentProperties
from spm.accessors.docusign_accessors import Doc<PERSON>ignAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.serializers.docusign_serializers import DocusignSerializer
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.docusign_services.docusign_sync_services import (
    create_docusign_sync_task_entry_for_client,
    remove_docusign_sync_task_entry_for_client,
)

DOCUSIGN_CLIENT_ID, DOCUSIGN_CLIENT_SECRET = (
    os.environ["DOCUSIGN_CLIENT_ID"],
    os.environ["DOCUSIGN_CLIENT_SECRET"],
)


def authenticate_docusign_user(client_id, api_client, code, login_email_id, logger):
    """
         Adding the docusign user to everstage.

    :param client_id: everstage client_id
    :param api_client: docusign client object to access the api's
    :param code: oauth code generated after the user consent
    :param login_email_id: everstage login user id
    :return: None

    """

    time = timezone.now()
    oauth_data = {}
    response = api_client.generate_access_token(
        client_id=DOCUSIGN_CLIENT_ID,
        client_secret=DOCUSIGN_CLIENT_SECRET,
        code=code,
    )
    access_token = response.access_token
    refresh_token = response.refresh_token
    timeout = int(response.expires_in)
    refresh_token_expiry = time + datetime.timedelta(
        days=30
    )  # standard expiry is 30 days for refresh token

    # get user info
    user_info = api_client.get_user_info(access_token=access_token)
    base_uri_response = api_client.sanitize_for_serialization(user_info)
    logger.info(f"DOCUSIGN: get_user_info response {base_uri_response}")

    # docusign data of the user
    oauth_data["client"] = client_id
    oauth_data["refresh_token"] = refresh_token
    oauth_data["refresh_token_expires_at"] = refresh_token_expiry
    oauth_data["knowledge_begin_date"] = timezone.now()
    oauth_data["email_id"] = base_uri_response.get("email")
    oauth_data["user_id"] = base_uri_response.get("sub")
    oauth_data["login_email_id"] = login_email_id
    accounts = base_uri_response.get("accounts", [])
    for account in accounts:
        is_default = account.get("is_default")
        if (
            str(is_default).lower() == "true"
        ):  # converting both bool and string value to string
            oauth_data["account_id"] = account.get("account_id")
            oauth_data["base_uri"] = account.get("base_uri")
            break

    cache_key = (
        f'docusign_token##{oauth_data.get("email_id")}##{oauth_data.get("account_id")}'
    )
    cache.set(cache_key, access_token, timeout)

    ###################### audit log #####################
    event_type_code = EVENT["CONNECT_DOCUSIGN"]["code"]
    event_key = login_email_id
    summary = "Connected to docusign successfully"
    audit_data = oauth_data
    updated_by = login_email_id
    updated_at = time
    ######################################################

    ser = DocusignSerializer(data=oauth_data)
    if ser.is_valid(raise_exception=True):
        DocusignAccessor(client_id).invalidate(
            account_id=oauth_data["account_id"],
            knowledge_date=time,
            docusign_email_id=oauth_data["email_id"],
            login_email_id=login_email_id,
        )
        ser.save()
        # Creating sync entry for the client when connect a docusign user
        client = get_client(client_id)
        create_docusign_sync_task_entry_for_client(client.client_id, client.time_zone)
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        analytics_data = {
            "user_id": login_email_id,
            "event_name": SegmentEvents.CONNECT_DOCUSIGN.value,
            "event_properties": {
                SegmentProperties.DOCUSIGN_EMAIL.value: oauth_data.get("email_id")
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)


def disconnect_docusign_user(
    client_id, login_email_id, action_summary, updated_by, logger
):
    time = timezone.now()

    ###################### audit log #####################
    event_type_code = EVENT["DISCONNECT_DOCUSIGN"]["code"]
    event_key = login_email_id
    summary = action_summary
    audit_data = {}
    updated_by_user = updated_by
    updated_at = time
    ######################################################

    logger.update_context({"email_id": login_email_id})
    prev_record = DocusignAccessor(client_id).get_docusign_details(login_email_id)
    DocusignAccessor(client_id).delete_user_account(
        prev_record=prev_record,
        login_email_id=login_email_id,
        knowledge_date=time,
    )
    # Deleting sync entry for the client if there are no connected accounts left
    if DocusignAccessor(client_id).docusign_connected_account_count() == 0:
        remove_docusign_sync_task_entry_for_client(client_id)
    audit_services.log(
        client_id,
        event_type_code,
        event_key,
        summary,
        updated_by_user,
        updated_at,
        audit_data,
    )
    analytics_data = {
        "user_id": login_email_id,
        "event_name": SegmentEvents.DISCONNECT_DOCUSIGN.value,
        "event_properties": {
            SegmentProperties.DOCUSIGN_EMAIL.value: getattr(prev_record, "email_id")
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)
    logger.info(
        "Docusign disconnected successfully for user - {}".format(login_email_id)
    )
    return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
