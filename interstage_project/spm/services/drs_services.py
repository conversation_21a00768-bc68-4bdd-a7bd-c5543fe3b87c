import base64
import logging
import uuid
from datetime import datetime
from typing import List, Optional, TypedDict

from django.db import transaction
from django.utils import timezone
from pydantic import BaseModel
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import get_client, is_notification_v2
from commission_engine.utils.general_data import SegmentEvents, SegmentProperties
from everstage_ddd.notifications import (
    notify_query_creation_v2,
    notify_query_updation_v2,
)
from everstage_infra.aws_infra.ecs import is_prod_env, is_staging_env
from spm.accessors.config_accessors.employee_accessor import HierarchyAccessor
from spm.accessors.drs_accessor import Dr<PERSON><PERSON><PERSON><PERSON><PERSON>, Drs<PERSON>pdatesAccessor
from spm.accessors.employee_accessor_v2 import EmployeeReadAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.serializers import <PERSON><PERSON><PERSON><PERSON><PERSON>, DrsUpdatesSerializer
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.client_config_services import get_query_setting_config
from spm.services.drs_email_service import query_created_mails, query_update_mails
from spm.services.drs_slack_service import (
    notify_query_created,
    notify_query_update_on_slack,
)

logger = logging.getLogger(__name__)


class DRSParams(BaseModel):
    """Represents the parameters for a DRS request."""

    id: str
    assignee: str
    subject: str
    status: str
    category_id: str
    category: str
    message: str
    message_markdown: str
    involved_users: Optional[list[str]]


class AssigneeUser(TypedDict):
    """Represents the assignee user."""

    employee_email_id: str
    full_name: str


@transaction.atomic
def persist_drs(client_id: int, drs_data: DRSParams, audit: dict) -> dict:
    """
    Persists a DRS ticket.

    Args:
        client_id (int): The ID of the client.
        drs_data (DRSParams): The DRS ticket data.
        audit (dict): The audit log data.

    Returns:
        dict: A dictionary containing the result of the operation.
    """
    logger.info("Creating new ticket with id - %s", drs_data.id)

    time = timezone.now()

    ###################### audit log #####################
    event_type_code = EVENT["CREATE_QUERY"]["code"]
    event_key = drs_data.id
    summary = drs_data.subject
    audit_data = drs_data
    updated_by = audit["updated_by"]
    updated_at = time
    ######################################################

    max_seq_number = DrsAccessor(client_id).get_max_sequence_number()
    drs, drs_update = _prepare_drs_and_update_records(client_id, drs_data, time, audit)
    drs["sequence_number"] = max_seq_number + 1

    ser = DrsSerializer(data=drs)
    update_ser = DrsUpdatesSerializer(data=drs_update)
    try:
        if ser.is_valid() and update_ser.is_valid():
            DrsAccessor(client_id).persist_drs(ser)
            DrsUpdatesAccessor(client_id).persist_drs_updates(update_ser)

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

            logger.info(
                "Created new ticket %s with status: %s, assignee: %s, category: %s",
                drs_data.id,
                drs_data.status,
                drs_data.assignee,
                drs_data.category,
            )

            if is_notification_v2(client_id):
                notify_query_creation_v2(client_id, drs_id=drs_data.id)
            else:
                notify_query_created(client_id, drs)
                if get_client(client_id).client_notification and (
                    is_prod_env() or is_staging_env()
                ):
                    query_created_mails(client_id, drs, drs_update["message"])

            analytics_data = {
                "user_id": audit["updated_by"],
                "event_name": SegmentEvents.CREATE_QUERY.value,
                "event_properties": {
                    SegmentProperties.QUERY_ID.value: drs_data.id,
                    SegmentProperties.QUERY_TITLE.value: drs_data.subject,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

            return {"success": True, "data": {"drs_id": drs_data.id}}
        else:
            error_dict = {
                "drs_error": ser.errors,
                "drs_update_error": update_ser.errors,
            }
            logger.exception(
                "Exception in creating new ticket due to DRS serializer error, id - %s, error - %s",
                drs_data.id,
                error_dict,
            )
            return {"success": False, "error": error_dict}
    except Exception as e:
        logger.exception("Exception in creating new ticket, DRS Accessor error")
        raise SQLParseError() from e


@transaction.atomic
def persist_drs_updates(
    client_id: int, drs_id: uuid.UUID, drs_update_data: DRSParams, audit: dict
) -> dict:
    """
    Persists the updates for a DRS ticket.

    Args:
        client_id (int): The ID of the client.
        drs_id (uuid.UUID): The ID of the DRS ticket.
        drs_update_data (DRSParams): The updated data for the DRS ticket.
        audit (dict): The audit log data.

    Returns:
        dict: A dictionary containing the success status and data (or error).
    """
    logger.info("Updating query ticket with id - %s", drs_id)

    time = timezone.now()
    previous_drs_update = DrsUpdatesAccessor(client_id).get_drs_update_by_id(drs_id)

    ###################### audit log #####################
    event_type_code = EVENT["UPDATE_QUERY"]["code"]
    event_key = drs_update_data.id
    summary = drs_update_data.subject
    audit_data = drs_update_data
    updated_by = audit["updated_by"]
    updated_at = time
    ######################################################

    _, drs_update = _prepare_drs_and_update_records(
        client_id, drs_update_data, time, audit, previous_drs_update
    )
    update_ser = DrsUpdatesSerializer(data=drs_update)
    try:
        if update_ser.is_valid():
            DrsUpdatesAccessor(client_id).invalidate(drs_id, time)
            DrsUpdatesAccessor(client_id).persist_drs_updates(update_ser)

            previous_meta = previous_drs_update["meta"] if previous_drs_update else {}
            if (
                previous_meta["status"]["new_value"] != drs_update_data.status
                or previous_meta["category_id"]["new_value"]
                != drs_update_data.category_id
                or previous_meta["category"]["new_value"] != drs_update_data.category
                or previous_meta["assignee"]["new_value"] != drs_update_data.assignee
                or previous_meta["involved_users"]["new_value"]
                != drs_update_data.involved_users
            ):
                DrsAccessor(client_id).update_drs(
                    drs_id,
                    drs_update_data.status,
                    drs_update_data.assignee,
                    drs_update_data.category_id,
                    drs_update_data.category,
                    drs_update_data.involved_users,
                    time,
                    audit,
                )

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

            logger.info(
                "Updated ticket %s with status: %s, assignee: %s, category: %s",
                drs_id,
                drs_update_data.status,
                drs_update_data.assignee,
                drs_update_data.category,
            )

            if is_notification_v2(client_id):
                notify_query_updation_v2(client_id, drs_id=str(drs_id))
            else:
                notify_query_update_on_slack(client_id, drs_id, drs_update)
                if get_client(client_id).client_notification and (
                    is_prod_env() or is_staging_env()
                ):
                    logger.info(
                        "Sending query update email for update  - %s",
                        drs_update_data.message,
                    )
                    query_update_mails(client_id, drs_update, drs_update["message"])

            analytics_data = {
                "user_id": audit["updated_by"],
                "event_name": (
                    SegmentEvents.CLOSE_QUERY.value
                    if drs_update_data.status == "Closed"
                    else SegmentEvents.UPDATE_QUERY.value
                ),
                "event_properties": {
                    SegmentProperties.QUERY_ID.value: drs_update_data.id,
                    SegmentProperties.QUERY_TITLE.value: drs_update_data.subject,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

            return {"success": True, "data": {"drs_id": drs_id}}
        else:
            logger.exception(
                "Error in updating ticket DRS serializer error, id: %s, error: %s",
                drs_id,
                update_ser.errors,
            )
            return {"success": False, "error": update_ser.errors}
    except Exception as e:
        logger.exception("Exception in updating new ticket, DRS Accessor error")
        raise SQLParseError() from e


def filter_support_user_and_get_email_name_map(
    client_id: int, email_list: List[str]
) -> dict:
    """Filter out support user from the list of assignee users."""
    emp_read_acc = EmployeeReadAccessor(client_id)
    email_name_map = (
        emp_read_acc.get_employees_name_as_dict_with_full_name_excluding_everstage(
            email_list
        )
    )

    return email_name_map


def get_query_admins(client_id: int) -> List[str]:
    """Get the email IDs of all users with the admin role."""
    emp_read_acc = EmployeeReadAccessor(client_id)

    # Get all admin role emails
    admin_roles = RolePermissionsAccessor(
        client_id
    ).get_all_roles_with_all_queries_permission()

    # Get all admin emails
    admin_emails = emp_read_acc.get_employee_email_ids_by_role(admin_roles)

    return admin_emails


def get_assignee_user_list(
    client_id: int, employee_email_id: str
) -> List[AssigneeUser]:
    """
    Retrieves a list of assignee users based on,
        - The employee's current and past managers
        - The email IDs of all users with the admin role.

    Args:
        client_id (int): The ID of the client.
        employee_email_id (str): The email ID of the employee.

    Returns:
        List[AssigneeUser]: A list of dictionaries containing the email IDs and full names of the assignee users.
    """
    logger.info("Fetching assignee user list for employee - %s", employee_email_id)
    logger.info("Client ID - %s", client_id)

    # Get the list of users configured in the client query settings
    client_query_config = get_query_setting_config(client_id)
    if client_query_config.get("query_assignee_type") == "specific_user":
        final_email_list = (
            client_query_config.get("selected_user", []) if client_query_config else []
        )
    else:
        # Get all current and past managers of the employee
        hierarchy_history = HierarchyAccessor(
            client_id
        ).get_all_employee_hierarchy_by_email([employee_email_id])
        managers = set(
            [hierarchy["reporting_manager_email_id"] for hierarchy in hierarchy_history]
        )

        admin_emails = get_query_admins(client_id)

        # Combine the two lists and construct a map with the names of the users
        final_email_list = list(managers.union(set(admin_emails)))

    email_name_map = filter_support_user_and_get_email_name_map(
        client_id, final_email_list
    )

    return email_name_map


def _prepare_drs_and_update_records(
    client_id: int,
    drs_data: DRSParams,
    time: datetime,
    audit: dict,
    previous_drs_update: dict = None,
) -> tuple[dict, dict]:
    # Prepare DRS Record
    drs = {
        "drs_id": drs_data.id,
        "logger": audit["updated_by"],
        "assignee": drs_data.assignee,
        "subject": drs_data.subject,
        "status": drs_data.status,
        "category_id": drs_data.category_id,
        "category": drs_data.category,
        "logged_time": time,
        "involved_users": drs_data.involved_users,
        "client": client_id,
        "knowledge_begin_date": time,
        "additional_details": audit,
    }

    # Prepare DRS Update Record
    def _create_meta_field(old_value, new_value):
        return {"old_value": old_value, "new_value": new_value}

    meta_fields = ["status", "category_id", "category", "assignee", "involved_users"]
    meta = {}
    if previous_drs_update:
        previous_meta = previous_drs_update["meta"]
        for field in meta_fields:
            meta[field] = _create_meta_field(
                previous_meta[field]["new_value"] if previous_meta else None,
                getattr(drs_data, field),
            )
    else:
        for field in meta_fields:
            field_value = getattr(drs_data, field)
            meta[field] = _create_meta_field(field_value, field_value)

    # Decode the markdown message and message field to text
    json_message = (
        base64.b64decode(drs_data.message).decode("utf-8", errors="replace")
        if drs_data.message
        else None
    )
    json_message_markdown = (
        base64.b64decode(drs_data.message_markdown).decode("utf-8", errors="replace")
        if drs_data.message_markdown
        else None
    )
    drs_update = {
        "drs_id": drs_data.id,
        "meta": meta,
        "message": _trim_text(json_message),
        "message_markdown": _trim_text(json_message_markdown),
        "updated_by": audit["updated_by"],
        "updated_time": time,
        "client": client_id,
        "knowledge_begin_date": time,
        "additional_details": audit,
    }

    return drs, drs_update


def _trim_text(text: str | None) -> str | None:
    trimmed_text = text.strip() if text else ""

    if not trimmed_text:
        return None

    return trimmed_text
