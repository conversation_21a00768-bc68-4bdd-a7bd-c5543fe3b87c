from datetime import timedelta

import pandas as pd
from django.utils.timezone import make_aware

from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.utils.date_utils import first_day_of_month, last_day_of_month
from commission_engine.utils.general_data import RbacPermissions, static_frequencies
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import PlanDetailsAllAccessor
from spm.serializers.config_serializers.employee_serializers import (
    EmployeePayrollSerializer,
)
from spm.services.custom_calendar_services import get_custom_calendar_map
from spm.services.hris_post_approval_services.base_validator import HRI<PERSON>aseValidator
from spm.services.rbac_services import get_ui_permissions, is_user_power_admin


class PayrollValidator(HRISBaseValidator):
    def __init__(self, client_id, logged_in_user):
        super().__init__(client_id, logged_in_user)

        self._set_table_fields_from_config("employee_payroll")

        self.valid_country_codes = set()
        self.valid_currency_codes = set()
        countries = CountriesAccessor(client_id).get_all_active_countries()
        for country in countries:
            self.valid_country_codes.add(country.country_code)
            self.valid_currency_codes.add(country.currency_code)
        self._custom_calendar_map = {}

    def _set_table_fields_from_config(self, field_context):
        super()._set_table_fields_from_config(field_context)

        self.table_fields["all"] += [
            self.email_field,
            "effective_start_date",
            "effective_end_date",
        ]
        self.table_fields["db_table"] = "employee_payroll_details"

    def _get_custom_calendar_map(self):
        if not self._custom_calendar_map:
            self._custom_calendar_map = get_custom_calendar_map(self.client_id)
        return self._custom_calendar_map

    def _has_edit_payroll_permission(self, email_id):
        if is_user_power_admin(self.client_id, email_id):
            return True
        ui_permission = get_ui_permissions(
            client_id=self.client_id,
            email_id=email_id,
        )
        if ui_permission and RbacPermissions.EDIT_PAYROLL.value not in ui_permission:
            return False
        return True

    def _validate_pay(self, val):
        if ",," in val:
            raise ValueError("Not a valid number.")
        if "." in val and "," in val:
            if val.rindex(",") > val.index("."):
                raise ValueError("Not a valid number.")
        val = val.replace(",", "")
        return str(round(float(val), 2))

    def validate_values(
        self, email_id, update_rec, joining_date, temporal_id, update_type
    ):
        if not self.check_mandatory_fields(update_rec):
            return self.add_validation_error(
                temporal_id, "Mandatory fields missing in payroll"
            )

        # Check if payout frequency value is supported
        custom_calendars_map = self._get_custom_calendar_map()
        custom_calendars_name = list(custom_calendars_map.values())
        payout_frequency = update_rec["payout_frequency"]
        if (
            payout_frequency not in custom_calendars_name
            and payout_frequency.lower() not in static_frequencies
        ):
            return self.add_validation_error(temporal_id, "Invalid payout frequency")

        # Check for valid pay currency code
        if (
            update_rec["pay_currency"]
            and update_rec["pay_currency"] not in self.valid_currency_codes
        ):
            return self.add_validation_error(temporal_id, "Invalid pay currency")

        # Check for valid country code
        if update_rec["employment_country"] and not (
            update_rec["employment_country"] in self.valid_country_codes
        ):
            return self.add_validation_error(temporal_id, "Invalid employment country")

        update_joining_date = update_rec["joining_date"]

        # Skip if there is an update to joining date or before joining date
        if update_joining_date != joining_date:
            return self.add_validation_error(temporal_id, "Joining date mismatch")

        # check if esd is greater than joining date
        esd = update_rec["effective_start_date"]
        if esd < joining_date:
            return self.add_validation_error(
                temporal_id, "Effective start date lesser than joining date"
            )

        if update_rec["payee_role"].lower() not in ["yes", "no"]:
            return self.add_validation_error(
                temporal_id, "Crystal Access value should be Yes or No"
            )

        if "fixed_pay" in update_rec and update_rec["fixed_pay"]:
            if (
                update_type == "existing_user"
                and not self._has_edit_payroll_permission(self.logged_in_user)
            ):
                return self.add_validation_error(
                    temporal_id,
                    "User does not have permission to edit Base Pay & Variable Pay",
                )
            try:
                update_rec["fixed_pay"] = self._validate_pay(
                    str(update_rec["fixed_pay"])
                )
            except Exception:
                return self.add_validation_error(temporal_id, "Invalid fixed pay value")

        if "variable_pay" in update_rec and update_rec["variable_pay"]:
            if (
                update_type == "existing_user"
                and not self._has_edit_payroll_permission(self.logged_in_user)
            ):
                return self.add_validation_error(
                    temporal_id,
                    "User does not have permission to edit Base Pay & Variable Pay",
                )
            try:
                update_rec["variable_pay"] = self._validate_pay(
                    str(update_rec["variable_pay"])
                )
            except Exception:
                return self.add_validation_error(
                    temporal_id, "Invalid variable pay value"
                )

        return True

    def validate(
        self,
        email_id,
        update_rec,
        joining_date,
        temporal_id,
        emp_payroll_covered_recs,
        update_type,
    ):
        if not self.validate_values(
            email_id, update_rec, joining_date, temporal_id, update_type
        ):
            return False

        if update_type == "new_user":
            return True

        # get distinct payout frequencies
        payout_frequencies = emp_payroll_covered_recs["payout_frequency"].unique()

        custom_calendars_map = self._get_custom_calendar_map()
        # Convert payout frequencies to keys if value matches in mapping
        payout_frequencies = [
            next(
                (
                    k
                    for k, v in custom_calendars_map.items()
                    if v.lower() == freq.lower()
                ),
                freq,
            )
            for freq in payout_frequencies
        ]
        # Convert update_rec payout frequency to key if value matches in mapping
        if "payout_frequency" in update_rec:
            update_rec["payout_frequency"] = next(
                (
                    k
                    for k, v in custom_calendars_map.items()
                    if v.lower() == update_rec["payout_frequency"].lower()
                ),
                update_rec["payout_frequency"],
            )

        # Check if the payee has update to the payout frequencies
        if len(payout_frequencies) > 1 or (
            len(payout_frequencies) == 1
            and update_rec["payout_frequency"].lower() != payout_frequencies[0].lower()
        ):
            new_start_date, new_end_date = (
                make_aware(first_day_of_month(update_rec["effective_start_date"])),
                make_aware(last_day_of_month(update_rec["effective_end_date"])),
            )

            # Check if the payee has any commission plans during the new effective period
            plans = PlanDetailsAllAccessor(
                self.client_id
            ).get_employee_plans_in_a_period(
                email_id, new_start_date, new_end_date, projection=["plan_id"]
            )

            if plans:
                return self.add_validation_error(
                    temporal_id,
                    "Cannot update the payout frequency for a period where the user has active plans.",
                )

            # Check if the payee has any commission locks during the new effective period
            comm_locks = CommissionLockAccessor(
                self.client_id
            ).get_locked_data_for_payee_in_period(
                new_start_date, new_end_date, email_id
            )

            if comm_locks:
                return self.add_validation_error(
                    temporal_id,
                    "Cannot update the payout frequency for a period where incentives are locked for the user.",
                )

        return True

    def check_if_no_update_in_values(self, start_rec, end_rec, update_rec):
        """
        Check if the update is intermediate, bounded and if there is actually update to the values
        """
        update_to_values = []
        for key in self.table_fields["all"]:
            if key not in [
                self.email_field,
                "temporal_id",
                "effective_start_date",
                "effective_end_date",
            ]:
                if key == "payee_role":
                    value = update_rec.get(key).lower() if update_rec.get(key) else None
                    update_val = "Revenue" if value == "yes" else "Non-Revenue"
                else:
                    update_val = update_rec.get(key)
                update_to_values.append(update_val == start_rec.get(key))

        return start_rec == end_rec and all(update_to_values)

    def add_first_record(self, update_records, joining_date):
        """
        Adds first record starting from joining date to the first record in updates
        """
        update_first_record = update_records[0]
        if joining_date < update_first_record["effective_start_date"]:
            first_record = {
                key: val if key in self.table_fields["mandatory"] else None
                for key, val in update_first_record.items()
            }
            eed = update_first_record["effective_start_date"] - timedelta(
                microseconds=1
            )
            first_record.update(
                {
                    self.email_field: update_first_record[self.email_field],
                    "effective_start_date": joining_date,
                    "effective_end_date": eed,
                }
            )
            update_records.insert(0, first_record)

        return update_records

    def get_serializer(self, records, time, audit):
        if "payroll" in records and records["payroll"]:
            emp_payroll_ser = EmployeePayrollSerializer
            payroll_records = records["payroll"]
            for rec in payroll_records:
                rec["client"] = self.client_id
                rec["additional_details"] = audit
                rec["knowledge_begin_date"] = time
                if type(rec["effective_start_date"]) is str:
                    rec["effective_start_date"] = datetime.strptime(
                        rec["effective_start_date"], "%d-%b-%Y"
                    )
                if type(rec["effective_end_date"]) is str:
                    rec["effective_end_date"] = datetime.strptime(
                        rec["effective_end_date"], "%d-%b-%Y"
                    )
                if type(rec["joining_date"]) is str:
                    rec["joining_date"] = datetime.strptime(
                        rec["joining_date"], "%d-%b-%Y"
                    )
                rec["effective_end_date"] = self.format_eff_end_date(
                    rec["effective_end_date"]
                )
                rec["variable_pay"] = (
                    rec["variable_pay"]
                    if "variable_pay" in rec and rec["variable_pay"]
                    else 0
                )

                if rec["payee_role"].lower() == "yes":
                    rec["payee_role"] = "Revenue"
                elif rec["payee_role"].lower() == "no":
                    rec["payee_role"] = "Non-Revenue"

                if rec.get("payout_frequency", "").lower() not in static_frequencies:
                    custom_calendars_id_name_map = get_custom_calendar_map(
                        self.client_id
                    )
                    custom_calendars_name_id_map = {
                        value: key
                        for key, value in custom_calendars_id_name_map.items()
                    }
                    # if rec has custom calendar name, convert it to id
                    if rec["payout_frequency"] not in custom_calendars_id_name_map:
                        rec["payout_frequency"] = custom_calendars_name_id_map[
                            rec["payout_frequency"]
                        ]

            return emp_payroll_ser(data=payroll_records, many=True)
