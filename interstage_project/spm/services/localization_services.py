import logging
from string import Template

from django.core.cache import cache

from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils.general_data import default_localization_map
from spm.accessors.localization_accessor import (
    CustomThemeAccessor,
    LocalizationAccessor,
)
from spm.constants.localization_constants import (
    default_statement_translation_map,
    statement_translation_map,
)

logger = logging.getLogger(__name__)


def get_domain_specific_attributes_from_cache(client_id):
    """
    This function will return the domain_specific_attributes for the client_id from cache.
    """
    cache_key = "client_domain_specific_attributes_{}".format(client_id)
    client_domain_specific_attributes = cache.get(cache_key)
    return client_domain_specific_attributes


def update_domain_specific_attributes_in_cache(
    client_id, client_domain_specific_attributes
):
    """
    This function will update the domain_specific_attributes for the client_id in cache.
    """
    logger.info("BEGIN: update_client_domain_specific_attributes_in_cache")
    cache_key = "client_domain_specific_attributes_{}".format(client_id)

    cache.delete(cache_key)
    # Providing None as timeout will make the cache never expire (write through).
    cache.set(cache_key, client_domain_specific_attributes, None)
    logger.info("END: update_client_domain_specific_attributes_in_cache")


def update_domain_specific_attributes(
    client_id, domain_specific_attributes, remove_empty_keys=False
):
    """
    The domain_specific_attributes is a dictionary of dictionaries. The first level keys are the languages and the second level keys are the domain_specific attributes.
    Example: {"en": {"domain_specific_attribute_1": "value", "domain_specific_attribute_2": "value"}, "fr": {"domain_specific_attribute_1": "value", "domain_specific_attribute_2": "value"}}
    This function will update the domain_specific_attributes for the client_id. If the client_id does not have any domain_specific_attributes, it will create a new one.
    """
    logger.info("BEGIN: update_client_domain_specific_attributes_service")
    if not domain_specific_attributes:
        logger.info("END: update_client_domain_specific_attributes_service")
        return "Please specify attributes. Attributes cannot be empty"

    default_localization_keys = set()
    incomming_keys = set()

    for language, attributes in domain_specific_attributes.items():
        incomming_keys.update(set(attributes.keys()))
        default_localization_keys.update(set(default_localization_map[language].keys()))
    if not incomming_keys.issubset(default_localization_keys):
        logger.info("END: update_client_domain_specific_attributes_service")
        return "Invalid attributes"

    domain_specific_attributes = capitalize_incomming_values(domain_specific_attributes)

    client_domain_specific_attributes = LocalizationAccessor(
        client_id
    ).get_localization_details(projection=["domain_specific_attributes"])
    client_domain_specific_attributes = client_domain_specific_attributes.get(
        "domain_specific_attributes"
    )
    if not client_domain_specific_attributes:
        client_domain_specific_attributes = {}
    if remove_empty_keys:
        (
            domain_specific_attributes,
            client_domain_specific_attributes,
        ) = remove_empty_string_keys(
            domain_specific_attributes, client_domain_specific_attributes
        )
    for language, attributes in domain_specific_attributes.items():
        if not client_domain_specific_attributes.get(language):
            client_domain_specific_attributes[language] = attributes
        else:
            client_domain_specific_attributes[language].update(attributes)

    LocalizationAccessor(client_id).update_domain_specific_attributes(
        client_domain_specific_attributes
    )
    update_domain_specific_attributes_in_cache(
        client_id, client_domain_specific_attributes
    )
    logger.info("END: update_client_domain_specific_attributes_service")

    return "Adding attributes is successful"


def remove_empty_string_keys(
    domain_specific_attributes, client_domain_specific_attributes
):
    """
    This function takes two dictionaries, 'domain_specific_attributes' and 'client_domain_specific_attributes', and processes
    the values within them to remove empty string keys from the domain-specific attributes. It considers the client-specific
    configuration to decide whether to remove keys or not. The modified attributes are returned in a new dictionary.

    Args:
        domain_specific_attributes (dict): A dictionary containing language-specific attributes.
        client_domain_specific_attributes (dict): A dictionary containing client-specific configuration for language attributes.

    Returns:
        tuple: A tuple containing two dictionaries:
            - attributes_to_be_updated (dict): A dictionary with non-empty string keys and values.
            - modified_client_domain_specific_attributes (dict): A modified version of the client-specific attributes
              after removing corresponding empty string keys.

    Note:
        - If a localization key in 'domain_specific_attributes' has an empty string value, and it is present in
          'client_domain_specific_attributes', the key is removed from 'client_domain_specific_attributes'.
        - If a localization key in 'domain_specific_attributes' has a non-empty string value, it is included in
          'attributes_to_be_updated'.
        - If 'client_domain_specific_attributes' is not provided for a language or a language is not present in it,
          all non-empty string keys in 'domain_specific_attributes' for that language are included in
          'attributes_to_be_updated'.
    """
    import copy

    logger.info("BEGIN: remove empty string keys.")
    attributes_to_be_updated = {}
    modified_client_domain_specific_attributes = {}
    for language, attributes in domain_specific_attributes.items():
        attributes_to_be_updated[language] = {}
        if (
            client_domain_specific_attributes
            and language in client_domain_specific_attributes
        ):
            logger.info("Domain specific attributes already set for client.")
            client_configured_attributes = copy.deepcopy(
                client_domain_specific_attributes[language]
            )
            for localization_key, value in attributes.items():
                if isinstance(value, str) and value.strip() == "":
                    if localization_key in client_configured_attributes:
                        del client_configured_attributes[localization_key]
                else:
                    attributes_to_be_updated[language][localization_key] = value
            modified_client_domain_specific_attributes[language] = (
                client_configured_attributes
            )
        else:
            logger.info("No domain specific attributes set for client.")
            for localization_key, value in attributes.items():
                if isinstance(value, str) and value.strip() != "":
                    attributes_to_be_updated[language][localization_key] = value
    logger.info(
        "END: remove_empty_string_keys,  icoming-attributes after removal - %s client-attibutes after removal - %s",
        str(attributes_to_be_updated),
        str(client_domain_specific_attributes),
    )
    return attributes_to_be_updated, modified_client_domain_specific_attributes


def get_localization_map(domain_specific_attributes):
    """This function will return the localization_map with the updated domain_specific_attributes."""
    client_domain_specific_attributes = {}
    if not domain_specific_attributes:
        domain_specific_attributes = {}
    for language, attributes in default_localization_map.items():
        language_attributes = domain_specific_attributes.get(language, {})
        client_domain_specific_attributes[language] = {
            **attributes,
            **language_attributes,
        }
    return client_domain_specific_attributes


def get_domain_specific_attributes(client_id):
    """
    This function will return the domain_specific_attributes for the client_id.
    Example: {"en": {"domain_specific_attribute_1": "value", "domain_specific_attribute_2": "value"}, "fr": {"domain_specific_attribute_1": "value", "domain_specific_attribute_2": "value"}}
    """
    client_domain_specific_attributes = get_domain_specific_attributes_from_cache(
        client_id
    )
    if client_domain_specific_attributes:
        return get_localization_map(client_domain_specific_attributes)

    client_domain_specific_attributes = LocalizationAccessor(
        client_id
    ).get_localization_details(["domain_specific_attributes"])
    client_domain_specific_attributes = client_domain_specific_attributes.get(
        "domain_specific_attributes"
    )

    update_domain_specific_attributes_in_cache(
        client_id, client_domain_specific_attributes
    )

    return get_localization_map(client_domain_specific_attributes)


def delete_domain_specific_attributes(client_id, domain_specific_attributes_to_delete):
    """
    This function will delete the domain_specific_attributes for the client_id.
    """
    logger.info("BEGIN: delete_client_domain_specific_attributes_service")
    client_domain_specific_attributes = LocalizationAccessor(
        client_id
    ).get_localization_details(["domain_specific_attributes"])
    client_domain_specific_attributes = client_domain_specific_attributes.get(
        "domain_specific_attributes"
    )
    if not client_domain_specific_attributes:
        logger.info("END: delete_client_domain_specific_attributes_service")
        return

    for delete_attribute in domain_specific_attributes_to_delete:
        for language in list(client_domain_specific_attributes.keys()):
            if delete_attribute in client_domain_specific_attributes[language]:
                del client_domain_specific_attributes[language][delete_attribute]

    LocalizationAccessor(client_id).update_domain_specific_attributes(
        client_domain_specific_attributes
    )
    update_domain_specific_attributes_in_cache(
        client_id, client_domain_specific_attributes
    )
    logger.info("END: delete_client_domain_specific_attributes_service")


def get_localized_message_service(message, client_id) -> str:
    """
    This function will return the localized message for the client_id and parameterized string using Template.
    The keywords that need to be localized must preceed by a $ Eg: To localize "Commission" in the string "Commission Plan", the string should be "$COMMISSION Plan"
    The keywords can be converted to lower case by adding a suffix "_lc" Eg: To localize "Earned commission", the string should be "Earned $COMMISSION_lc"
    """
    try:
        logger.info("BEGIN: get_localized_message_service")
        client_domain_specific_attributes = get_domain_specific_attributes(client_id)
        language = "en"
        client_domain_specific_attributes = client_domain_specific_attributes[language]
        # s1="$COMMISSION for *$COMMISSION_lc"
        message_words = message.split()
        # dict2={"COMMISSION": "Commission"}
        for word in message_words:
            if "$" in word and "_lc" in word:
                key_to_change = word[word.index("$") + 1 : word.index("_lc")]
                client_domain_specific_attributes.update(
                    {
                        key_to_change
                        + "_lc": client_domain_specific_attributes[
                            key_to_change
                        ].lower()
                    }
                )

        template = Template(message)

        localized_message = template.safe_substitute(client_domain_specific_attributes)
        logger.info("END: get_localized_message_service")
        return localized_message
    except KeyError as e:
        logger.exception("KeyError while getting localized message: %s", e)
        return message


def get_localized_message_for_default_custom_category(custom_category_name, client_id):
    """
    Function to return localized message for default custom category for given client_id
    """

    try:
        logger.info("BEGIN: get_localized_message_for_default_custom_category")
        client_domain_specific_attributes = get_domain_specific_attributes(client_id)
        language = "en"
        client_domain_specific_attributes = client_domain_specific_attributes[language]
        message_words = custom_category_name.split()
        localized_words = []

        for word in message_words:
            if word.upper() in client_domain_specific_attributes:
                localized_words.append(client_domain_specific_attributes[word.upper()])
            else:
                localized_words.append(word)

        logger.info("END: get_localized_message_for_default_custom_category")
        return " ".join(localized_words)

    except KeyError as e:
        logger.exception("KeyError while getting localized message: %s", e)
        return custom_category_name


def get_statements_translated_message_service(
    message, client_id, preferred_language
) -> str:
    """
    This function will return the translated message if the message is available in preferred_language.
    If the message is not available in preferred_language, it will return the message in english after localizing the message.
    For example: If the preferred_language is "de" and the message is "Payout Summary", the function will return "Übersicht der Auszahlung",
    as the message is available in preferred_language.
    For example: If the preferred_language is "de" and the message is "Commission Summary", the function will return "Commission Summary",
    as the message is not available in preferred_language. The function will return the message in english after localizing the message.

    Args:
        message (str): The message to be translated.
        client_id (str): client_id of user
        preferred_language (str): The language in which the message should be translated.

    Returns:
        translated_message (str): The translated message.
    """
    try:
        logger.info(
            "BEGIN: get_statements_localized_message_service for client: %s and preferred language %s",
            client_id,
            preferred_language,
        )
        translated_message = statement_translation_map.get(preferred_language, {}).get(
            message, ""
        )
        if translated_message:
            logger.info("%s translated to %s", message, translated_message)
            logger.info(
                "END: get_statements_localized_message_service for client: %s",
                client_id,
            )
            return translated_message
        en_translated_message = default_statement_translation_map.get("en", {}).get(
            message, ""
        )
        en_localized_message = get_localized_message_service(
            en_translated_message, client_id
        )
        logger.info(
            "Preferred language not available for message: %s, translated to english %s",
            message,
            en_translated_message,
        )
        logger.info(
            "END: get_statements_localized_message_service for client: %s", client_id
        )
        return en_localized_message

    except KeyError as e:
        logger.exception("KeyError while getting translated message: %s", e)
        return message


def capitalize_incomming_values(domain_specific_attributes):
    """
    This function will capitalize the incomming values for the domain_specific_attributes.
    """
    for language, attributes in domain_specific_attributes.items():
        for key, value in attributes.items():
            domain_specific_attributes[language][key] = value.title()
    return domain_specific_attributes


def get_localized_words_service(keywords_to_localize, client_id) -> dict:
    """
    This function will return the localized words for the keywords to localize.
    """
    localized_words = {}
    for keyword, terminology in keywords_to_localize.items():
        localized_words[keyword] = get_localized_message_service(terminology, client_id)
    return localized_words


def get_theme_specific_attributes_from_cache(client_id):
    """
    This function will return the client_theme_specific_attributes for the client_id from cache.
    """
    cache_key = "client_theme_specific_attributes_{}".format(client_id)
    client_theme_specific_attributes = cache.get(cache_key)
    return client_theme_specific_attributes


def update_theme_specific_attributes_in_cache(
    client_id, client_theme_specific_attributes
):
    """
    This function will update the theme_specific_attributes for the client_id in cache.
    """
    logger.info("BEGIN: update_client_theme_specific_attributes_in_cache")
    cache_key = "client_theme_specific_attributes_{}".format(client_id)
    # Providing None as timeout will make the cache never expire (write through).
    cache.set(cache_key, client_theme_specific_attributes, None)
    logger.info("END: update_client_theme_specific_attributes_in_cache")


def get_theme_specific_attributes(client_id):
    """
    This function will return the theme_specific_attributes for the client_id.
    """
    logger.info("BEGIN: get_theme_specific_attributes")
    if has_feature(client_id, "enable_custom_theme"):
        client_theme_specific_attributes = get_theme_specific_attributes_from_cache(
            client_id
        )
        if client_theme_specific_attributes:
            logger.info("Cache hit: Getting theme_specific_attributes from cache")
            logger.info("END: get_theme_specific_attributes")
            return client_theme_specific_attributes

        logger.info("Cache miss: Getting theme_specific_attributes from database")
        client_theme_specific_attributes = CustomThemeAccessor(
            client_id
        ).get_custom_theme_details(["theme_specific_attributes"])
        client_theme_specific_attributes = client_theme_specific_attributes.get(
            "theme_specific_attributes"
        )

        update_theme_specific_attributes_in_cache(
            client_id, client_theme_specific_attributes
        )

        logger.info("END: get_theme_specific_attributes")

        return client_theme_specific_attributes
    else:
        logger.info("Custom theme feature is not enabled for client: %s", client_id)
        logger.info("END: get_theme_specific_attributes")
        return {}


def update_theme_specific_attributes(client_id, theme_specific_attributes):
    """
    The theme_specific_attributes is a dictionary with theme color values.
    """
    logger.info("BEGIN: update_client_theme_specific_attributes")
    if has_feature(client_id, "enable_custom_theme"):
        if not theme_specific_attributes:
            logger.info("Please specify attributes. Attributes cannot be empty")
            logger.info("END: update_client_theme_specific_attributes")
            return "Please specify attributes. Attributes cannot be empty"

        CustomThemeAccessor(client_id).update_theme_specific_attributes(
            theme_specific_attributes
        )
        update_theme_specific_attributes_in_cache(client_id, theme_specific_attributes)
        logger.info("END: update_client_theme_specific_attributes")

        return "Adding attributes is successful"
    else:
        logger.info("Custom theme feature is not enabled for client: %s", client_id)
        logger.info("END: update_client_theme_specific_attributes")
        return "Custom theme feature is not enabled for this client"
