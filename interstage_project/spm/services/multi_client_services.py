import logging
from typing import Dict, List, Optional
from uuid import UUID

from django.core.cache import cache
from django.db.models import F
from django.utils import timezone
from rest_framework.exceptions import NotFound

from commission_engine.accessors.client_accessor import get_clients_by_ids
from commission_engine.utils.cache_utils import (
    get_impersonate_client_id_cache_key,
    get_selected_client_in_cache_for_employee,
)
from everstage_admin_backend.tsar import get_live_memberships_for_member
from interstage_project.auth_management_api import logout_as_user
from interstage_project.auth_utils import get_token_auth_header
from interstage_project.jwt_handler import jwt_hard_decode_token
from interstage_project.session_utils import get_tsar_membership
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.accessors.session_management_accessors import (
    Auth0AccessTokenAccessor,
    SessionClientAccessor,
)
from spm.services.session_management_services import SessionClientMappingServices

logger = logging.getLogger(__name__)


def get_selected_client_for_employee(employee_email_id, client_id, request):
    token = get_token_auth_header(request)
    if token:
        decoded = jwt_hard_decode_token(token)
        user_metadata = decoded.get("https://everstage.com/user_metadata")
        if user_metadata is not None:
            # TODO: Must include client_id in the cache key as well because there might
            # be same admin email in two clients trying to impersonate different user
            # with same email id in those two clients.
            cache_key = get_impersonate_client_id_cache_key(
                user_metadata["admin_email"], user_metadata["email"]
            )
            if cache.get(cache_key) is not None:
                return int(cache.get(cache_key))
            employee_email_id = user_metadata["admin_email"]
        selected_client_cache_key = get_selected_client_in_cache_for_employee(
            employee_email_id
        )
        selected_client = (
            int(cache.get(selected_client_cache_key))
            if cache.get(selected_client_cache_key)
            else None
        )
        if not selected_client:
            selected_client = EmployeeAccessor(
                client_id=client_id
            ).get_preferred_client(employee_email_id=employee_email_id)

        # For multi client user, if the user has exited / inactive in the selected client,
        # then set the selected client to None to prompt the user for valid client
        # IDs while logging in.
        if selected_client is not None:
            is_active = EmployeeAccessor(selected_client).is_employee_active(
                employee_email_id
            )
            if not is_active:
                selected_client = None

        return selected_client
    return None


def get_all_clients_and_selected_client_for_employee(
    client_id, employee_email_id, request, is_login_as
):
    logger.info("Fetching selected client id for employee %s", employee_email_id)
    client_list = []

    # The user need not be "Invited" or "Active" status when impersonated by any other user.
    if is_login_as:
        employees_client_id_list = (
            EmployeeAccessor(client_id)
            .get_employees_with_same_email(employee_email_id)
            .values("client_id")
        )
    else:
        employees_client_id_list = (
            EmployeeAccessor.get_invited_or_active_and_non_deactivated_users(
                employee_email_id
            ).values("client_id")
        )

    for client in employees_client_id_list:
        client_list.append(client["client_id"])

    if len(employees_client_id_list) > 0:
        clients_qs = get_clients_by_ids(client_list)
        multi_clients = list(
            clients_qs.annotate(
                clientId=F("client_id"),
                clientName=F("name"),
                src=F("logo_url"),
            ).values("clientId", "clientName", "src")
        )
        selected_client = get_selected_client_for_employee(
            employee_email_id=employee_email_id, client_id=client_id, request=request
        )

        return (multi_clients, selected_client)

    return ([], None)


def _get_clients_for_employee(user: str) -> List[Dict]:
    """
    Fetch a list of clients associated with an employee.

    Args:
        user (str): The username of the employee.

    Returns:
        List[Dict]: A list of dictionaries containing client details such as client ID, name, and logo URL.
    """
    clients = list(
        EmployeeAccessor.get_invited_or_active_and_non_deactivated_users(user)
        .annotate(client_name=F("client__name"), src=F("client__logo_url"))
        .values("client_id", "client_name", "src")
        .order_by("client_name")
    )
    return clients


def _identify_client_id_for_employee(
    clients: List[Dict], email: str, is_impersonation: bool
) -> Optional[int]:
    """
    Identify the client ID for an employee based on their email and impersonation status.

    Args:
        clients (List[Dict]): List of clients associated with the employee.
        email (str): The email of the employee.
        is_impersonation (bool): Flag indicating if the user is impersonating.

    Returns:
        Optional[int]: The identified client ID, or None if no valid client ID is found.
    """
    if not clients:
        # Reaching here, means user is either not (active/invited) or is deactivated.

        # Check if the employee exists in our application.
        employees = EmployeeAccessor.get_employees_with_same_email(email)
        if not employees.exists():
            logger.error("Employee %s not found", email)
            raise NotFound("Employee not Found")

        # Check if the employee is deactivated.
        employees = EmployeeAccessor.get_non_deactivated_users(email)
        if not employees.exists():
            logger.error("Employee %s deactivated", email)
            raise NotFound("Employee Deactivated")

        # The employee is not deactivated, but is not invited or active.
        raise NotFound("Employee not Invited")

    client_id: Optional[int] = None

    # check if user is a single client user
    if len(clients) == 1:
        client_id = clients[0].get("client_id")
    # reaching here means user is a multi-client user
    # now, we'll deal with impersonation and non-impersonation cases separately
    elif is_impersonation:
        # lookup `session_client` table for the login_user's latest record and refer to the client_id from there
        client_id = SessionClientAccessor(None, email).get_latest_client_id_for_email()

        ###### TEMPORARY CODE BLOCK ######
        # TODO: This is just for smooth transition from old to new flow. To be removed in any release after its deployment.
        if client_id is None:
            client_id = Auth0AccessTokenAccessor().get_latest_client_id_for_email(email)
        ##################################
    # reaching here means multi-client and non-impersonation case
    # now, get prefered client id from employee table
    else:
        employees = EmployeeAccessor.get_employees_with_same_email(email)
        client_id = (employees[0].employee_config or {}).get("preffered_client_id")

    # By this point, we have identified the client_id for the user; if available.
    # If client_id is not None, check if the user is active in the client. If not, return None.
    client_id = int(client_id) if client_id is not None else None
    return client_id if EmployeeAccessor(client_id).is_employee_active(email) else None


def get_clients(
    session_id: str,
    login_email: str,
    email: str,
    is_impersonation: bool,
    client_id: Optional[int],
    con_provider: str,
    auth0_user_id: str,
):
    """
    Retrieve and process client information for a user session.

    Args:
        session_id (str): The session ID.
        login_email (str): The login email of the user.
        email (str): The email of the employee.
        is_impersonation (bool): Flag indicating if the user is impersonating.
        client_id (Optional[int]): The client ID retreived from request.
        con_provider (str): The connection provider.
        auth0_user_id (str): The Auth0 user ID.

    Returns:
        dict: A dictionary containing client information and UI action instructions.
    """
    ui_action: Optional[str] = None  # For triggering tab reload if required

    # Fetch accessible clients list for the login user
    clients: List[Dict] = _get_clients_for_employee(login_email)

    # Fetch live support memberships for the staff member, if any
    live_memberships: List[Dict] = get_live_memberships_for_member(
        login_email, con_provider
    )

    selected_client_id: Optional[int] = None
    selected_support_membership_id: Optional[UUID] = None

    # if live support memberships found, then lookup for selected `support_membership_id` in threadlocal and `client_id` in request; which may have been injected by the application middleware (by referring session_client table).
    if live_memberships:
        # if selected_support_membership_id and selected_client_id both are None, then this is a fresh login; and we need to let `selected_client_id` be None. Here, we won't invoke regular client finding strategies; because we want the user to select a regular client/ support membership to login into.
        # if selected_support_membership_id is None, but selected_client_id is NOT None, then user has already selected a regular client and this is not a support-user membership login.
        # if selected_support_membership_id and selected_client_id both are NOT None, then this is a support-user membership login; as the user has already selected a support membership for this session.
        # if selected_support_membership_id is not None, but selected_client_id is None, this case can't happen.
        selected_support_membership_id = get_tsar_membership()
        selected_client_id = client_id
    else:
        # if live support memberships are not available, then we will invoke regular client finding strategies.
        selected_client_id = (
            client_id
            if client_id is not None
            and client_id in [client["client_id"] for client in clients]
            else _identify_client_id_for_employee(
                clients,
                login_email,
                is_impersonation,
            )
        )

    # if selected_client_id is found for regular login use-cases (not support-user login), then save/update it in the session_client table for the login_user & current session.
    if selected_client_id is not None:
        if selected_support_membership_id is None:
            # Update session-client mapping for regular login use-cases
            SessionClientMappingServices(
                session_id=session_id,
                email_id=login_email,
                client_id=selected_client_id,
            ).create_or_update_session_client_mapping()

            # [TODO]########### T E M P O R A R Y   C O D E   B L O C K ##################
            # Create email<>client mapping in cache; to be used by Chrome Extension flow.
            # Modify chrome extension flow to get rid off this dependency.
            cache_key: str = get_selected_client_in_cache_for_employee(login_email)
            cache.set(cache_key, int(selected_client_id))
            ##############################################################################

        ################ I M P E R S O N A T I O N   H A N D L I N G ################
        # While being logged-in into >=2 clients, handling exit from impersonation:
        # 1. If support-user has logged-in as support-user into 2 clients; client1 and client2.
        # 2. Now, on impersonating in client1, if the user reloads the page in client2's browser tab, will remove the impersonation in client1.
        # 3. Now, user will see exited impersonation in client1's tab only on page-reload. Until then, user will experience the impersonation.
        if is_impersonation:
            impersonated_user_belong_to_same_client: bool = EmployeeAccessor(
                selected_client_id
            ).does_employee_exist(email)
            if not impersonated_user_belong_to_same_client:
                logout_as_user(auth0_user_id)
                ui_action = "reload"

    return {
        "clients": clients,
        "selected_client_id": selected_client_id,
        "client_support_memberships": live_memberships,
        "selected_support_membership_id": selected_support_membership_id,
        "ui_action": ui_action,
    }


def set_client(
    session_id: str,
    login_email: str,
    email: str,
    is_impersonation: bool,
    auth0_user_id: str,
    selected_client_id: int,
    selected_support_membership_id: Optional[UUID],
    is_selection_default: bool,
):
    """
    Set the client for a user session and update relevant mappings.

    Args:
        session_id (str): The session ID.
        login_email (str): The login email of the user.
        email (str): The email of the employee.
        is_impersonation (bool): Flag indicating if the user is impersonating.
        auth0_user_id (str): The Auth0 user ID.
        selected_client_id (int): The selected client ID.
        selected_support_membership_id (Optional[UUID]): The selected support membership ID.
        is_selection_default (bool): Flag indicating if the selection should be set as default.

    Returns:
        dict: A dictionary containing UI action instructions.
    """
    ui_action: Optional[str] = None  # For triggering tab reload if required

    if selected_support_membership_id is not None:
        # if current session entry is already present in the session_client table, then raise an error; else create a new entry.
        SessionClientMappingServices(
            session_id=session_id,
            email_id=login_email,
            client_id=selected_client_id,
            membership_id=selected_support_membership_id,
        ).create_session_membership_mapping_only_if_not_present()

    else:
        # create or update entry in session_client table
        SessionClientMappingServices(
            session_id=session_id,
            email_id=login_email,
            client_id=selected_client_id,
        ).create_or_update_session_client_mapping()

        # set preferred client id in employee table if is_selection_default is True
        if is_selection_default:
            EmployeeWriteAccessor(selected_client_id).update_employee_preferred_client(
                email_id=login_email,
                preffered_client_id=selected_client_id,
                knowledge_date=timezone.now(),
            )

    ################ I M P E R S O N A T I O N   H A N D L I N G ################
    # While logging-in afresh, if the user is impersonated into any other user from another client, remove the impersonation.
    if is_impersonation:
        impersonated_user_belong_to_same_client: bool = EmployeeAccessor(
            selected_client_id
        ).does_employee_exist(email)
        if not impersonated_user_belong_to_same_client:
            logout_as_user(auth0_user_id)
            ui_action = "reload"

    # [TODO]########### T E M P O R A R Y   C O D E   B L O C K ##################
    # Create email<>client mapping in cache; to be used by Chrome Extension flow.
    # Modify chrome extension flow to get rid off this dependency.
    cache_key: str = get_selected_client_in_cache_for_employee(login_email)
    cache.set(cache_key, selected_client_id)
    ##############################################################################

    return {"ui_action": ui_action}
