import datetime
import json
from ast import literal_eval
from decimal import Decimal

import numpy as np
import pandas as pd
import pydash
import pytz
from dateutil import tz
from django.db import connection
from django.utils.timezone import make_aware

import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import (
    get_client_features,
    get_client_fiscal_start_month,
    get_line_item_approval_flag,
)
from commission_engine.accessors.settlement_accessor import (
    CommissionSettlementMapAccessor,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.utils.date_utils import get_period_label_email
from commission_engine.utils.general_data import RBAC, RBACComponent
from commission_engine.utils.general_utils import (
    get_statements_url_for_payee,
    replace_nan_nat_for_df,
)
from slack_everstage.utils.slack_utils import get_site_url
from spm.accessors.approval_workflow_accessors import (
    ApprovalInstanceAccessor,
    ApprovalInstanceStageAccessor,
    ApprovalRequestsAccessor,
    SubApprovalRequestsAccessor,
)
from spm.accessors.commission_plan_accessor import CommissionPlanAccessor
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    PlanDetailsAllAccessor,
)
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.commission_adjustment_approvals.accessors import (
    CommissionAdjustmentStatusAccessor,
)
from spm.commission_adjustment_approvals.services.approval_config_services import (
    get_commission_adjustment_approvals_flag,
)
from spm.constants.approval_workflow_constants import (
    APPROVAL_ENTITY_TYPES,
    APPROVAL_WORKFLOW_STATUS,
    APPROVAL_WORKFLOW_STATUS_EXPORT,
    ENTITY_KEY_DELIMETER,
)
from spm.constants.localization_constants import PayoutExportTerms
from spm.services.approval_line_items import ApprovalSubRequestsStatus
from spm.services.approval_line_items.line_item_services import (
    get_sub_request_count_for_approval_request_id,
)
from spm.services.config_services.employee_services import get_user_status
from spm.services.custom_field_services import (
    get_custom_fields_details,
    resolve_custom_field_data_for_user_filters,
)
from spm.services.localization_services import get_localized_words_service
from spm.services.period_label_services import get_custom_period_label
from spm.services.query_builder.payouts import PayoutStatusQueryBuilder
from spm.services.rbac_services import (
    get_data_permission,
    get_valid_payee_emails,
    is_payout_value_permission,
    is_view_payroll_permission,
)
from spm.services.settlement_actions_service.settlement_total_service import (
    SettlementTotalService,
)
from spm.sort_utils import SortInfoType, SortUtility, remove_ignore_columns


def get_currency_symbol(client_id):
    return {
        country.currency_code: country.currency_symbol
        for country in list(CountriesAccessor(client_id).get_all_countries())
    }


def get_active_countries(client_id):
    return {
        country.country_code: country.country_name
        for country in list(CountriesAccessor(client_id).get_all_active_countries())
    }


class PayoutsViewData:
    def __init__(self, client_id, ped, logger=None):
        self.client_id = client_id
        self.ped = ped
        if not logger:
            log_context = {
                "client_id": client_id,
                "ped": ped,
            }
            self.logger = iputils.LogWithContext(log_context)
        else:
            self.logger = logger

        self.is_export = False
        self.currency_map = {}
        self.active_countries = {}
        self.custom_fields_data = {}
        self.role_permission_id_name_map = {}
        self.sort_ignore_columns = [
            "profile_picture",
            "key",
            "payee_email_id",
            "comm_calc_status",
            "settlement_calc_status",
            "comm_freezed_at",
            "comm_freezed_by",
            "settlement_freezed_at",
            "settlement_freezed_by",
            "payout_transactions",
            "payout_split_up",
            "entity_type",
            "is_comm_adj_requested",
        ]
        self.payout_view_default_columns = [
            "profile_picture",
            "key",
            "payee_email_id",
            "comm_calc_status",
            "settlement_calc_status",
            "comm_freezed_at",
            "comm_freezed_by",
            "settlement_freezed_at",
            "settlement_freezed_by",
            "payout_transactions",
            "payout_split_up",
            "entity_type",
            "is_comm_adj_requested",
            "period",
            "payee_name",
            "payee_currency_symbol",
            "approval_status",
            "period_start_date",
            "period_end_date",
            "total_payout",
            "paid_amount",
            "processed_amount",
            "ignored_amount",
            "entity_type",
            "payee_currency",
            "payment_status",
        ]

    def get_payout_data_for_email_statement(
        self,
        logged_in_user: str,
        search_term: str,
        filters: dict,
        is_selected_all: bool,
        payee_email_ids: list[str],
    ):
        """
        Get payout related for sending email with statement attachment.

        Parameters
        ----------
        logged_in_user : str
            The Logged in user for whom the data permissions would be accounted

        search_term : str
            The search term used in the payouts page

        filters : dict
            Filters used in the payouts page

        is_selected_all : bool
            Flag to mention if all the payees are selected. Because of pagination in
            payouts page, all email ids won't be there in frontend at any point of time.

        payee_email_ids : list[str]
            The selected payee email ids. Note that this array would be empty if the
            is_selected_all flag is enabled, and it doesn't contain the filtered emails.

        Returns
        -------
        payout_details : dict[str, dict] or Empty dict
            Each dict has payee email id as key and the value with the following fields
            period_start_date, period_end_date, is_settlement_view, payee_name,
            commission_paid (total payout), commission_period. Returns an empty dict
            in case of any errors.

        Raises
        ------
        ValueError
            When the logged in user don't have permission access data of the given payees.

        """
        # Getting valid emails for the logged in user - considering their data permission,
        # payout filters, search term and selected payees
        data_permission = get_data_permission(
            self.client_id, logged_in_user, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        has_period_label_feature = has_feature(self.client_id, "payout_period_label")
        if not data_permission:
            self.logger.info(
                "No payout statements data permission found for the logged in user"
            )
            raise ValueError("Permission denied")

        if data_permission["type"] != RBAC.ALL_DATA.value:
            valid_email_ids = get_valid_payee_emails(
                self.client_id, logged_in_user, data_permission, self.ped
            )
            if is_selected_all:
                filtered_email_ids = valid_email_ids
            else:
                filtered_email_ids = list(
                    set(payee_email_ids).intersection(valid_email_ids)
                )

            if not filtered_email_ids:
                raise ValueError("Permission denied")

            filters["email_id_in"] = filtered_email_ids
        else:
            if not is_selected_all:
                if payee_email_ids:
                    filters["email_id_in"] = payee_email_ids
                else:
                    return {}

        payouts_data = self.get_payout_data(
            filters=filters,
            search_term=search_term,
            is_export=False,
            is_bulk_action="BULK_EMAIL_STATEMENTS",
            logged_in_user=logged_in_user,
            selected_columns=[
                "payeee_email_id",
                "period_start_date",
                "payout_frequency",
                "total_payout",
                "payee_currency_symbol",
                "full_name",
            ],
        )["data"]

        employee_email_ids = [payout["payee_email_id"] for payout in payouts_data]

        # Getting settlemnent view data
        is_settlement_view_data = CommissionSettlementMapAccessor(
            self.client_id
        ).get_settlement_view_flags_given_email_ids(employee_email_ids)

        fiscal_start_month = get_client_fiscal_start_month(self.client_id) or 1

        result = {}
        for payout in payouts_data:
            email_id = payout["payee_email_id"]
            psd = payout["period_start_date"]
            psd = make_aware(datetime.datetime.strptime(psd, "%Y-%m-%d"))
            period = ""
            if has_period_label_feature:
                # Get custom period label, if present
                period_tuple = [(psd, self.ped)]
                period = get_custom_period_label(self.client_id, period_tuple)

            if not period:
                period = get_period_label_email(
                    psd=psd,
                    ped=self.ped,
                    fiscal_start_month=fiscal_start_month,
                    payout_freq=payout["payout_frequency"],
                    client_id=self.client_id,
                )

            total_payout = payout["total_payout"]
            # Payout value might be "-" in case of absence of view:payoutvalueothers permission
            if total_payout == "-":
                self.logger.info(
                    f"Skipping {email_id} because of absence of view:payoutvalueothers permission"
                )
                continue

            amount = Decimal(str(total_payout)).quantize(Decimal("0.01"))
            currency_symbol = payout["payee_currency_symbol"] or ""
            commission_paid = f"{currency_symbol} {amount:,}"

            statement_url = get_statements_url_for_payee(
                email_id,
                psd.strftime("%Y-%m-%d"),
                self.ped.strftime("%Y-%m-%d"),
            )

            result[email_id] = {
                "period_start_date": psd,
                "period_end_date": self.ped,
                "is_settlement_view": is_settlement_view_data[email_id],
                # template fields
                "name": payout["full_name"],
                "commission_paid": commission_paid,
                "commission_period": period,
                "action_url": f"{get_site_url()}/{statement_url}",
                "file_name": f"Statements-{self.ped.strftime('%b-%Y')}.xlsx",
            }
        return result

    def get_email_id_approval_details_map(
        self, logged_in_user, instance_records: list[dict], active_stages: list[dict]
    ):
        """
        Get email id to stage status map.
        """
        self.logger.info("BEGIN: Fetch_email_id_stage_status_map")
        email_id_approval_extra_details = self.get_email_id_approval_extra_details(
            instance_records
        )

        all_updated_by_email_ids = list(
            set(
                email_id_approval_extra_details[
                    "email_id_approvals_updated_by_map"
                ].values()
            )
        )

        approved_requests_count_by_instance_ids = ApprovalRequestsAccessor(
            self.client_id
        ).get_approved_requests_count_by_instance_ids(
            [
                instance_record["approval_wf_instance_id"]
                for instance_record in instance_records
            ],
            as_dict=True,
        )

        active_stage_ids = [stage["stage_instance_id"] for stage in active_stages]

        instance_id_stage_map = {
            stage["approval_wf_instance_id"]: stage["stage_instance_id"]
            for stage in active_stages
        }

        approval_requests = ApprovalRequestsAccessor(
            self.client_id
        ).get_all_request_ids_list(
            filters={
                "stage_instance_id__in": active_stage_ids,
                "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
            },
            projection=[
                "stage_instance_id",
                "approver",
                "entity_type",
                "approval_request_id",
            ],
        )

        all_approver_email_ids = [
            approval_request["approver"] for approval_request in approval_requests
        ]

        all_employees = set(
            all_approver_email_ids + [logged_in_user] + all_updated_by_email_ids
        )
        all_approver_details = EmployeeAccessor(
            self.client_id
        ).get_all_employee_details(
            email_ids=list(all_employees),
            projection=[
                "first_name",
                "last_name",
                "profile_picture",
                "employee_email_id",
                "time_zone",
            ],
        )

        employee_details_map = {}
        for approver in all_approver_details:
            approver_email_id = approver["employee_email_id"]
            employee_details_map[approver_email_id] = {
                "first_name": approver.get("first_name", ""),
                "last_name": approver.get("last_name", ""),
                "email_id": approver_email_id,
                "image": approver.get("profile_picture", None),
                "time_zone": approver.get("time_zone", None),
            }

        user_timezone = employee_details_map.get(logged_in_user, {}).get(
            "time_zone", None
        )
        if user_timezone:
            user_timezone = user_timezone.split(" ")
            if len(user_timezone) > 1:
                user_timezone = tz.gettz(user_timezone[1])
            else:
                user_timezone = pytz.timezone(user_timezone[0])

        stage_id_name_due_date_map = {}
        stage_id_requested_time_map = {}
        today = datetime.datetime.now(user_timezone if user_timezone else pytz.UTC)
        for stage in active_stages:
            due_date = stage["due_date"]
            stage_instance_id = stage["stage_instance_id"]
            requested_time = stage["requested_time"]

            if requested_time:
                stage_requested_on = requested_time.astimezone(
                    user_timezone if user_timezone else pytz.UTC
                ).strftime("%b %d, %Y")
                stage_id_requested_time_map[stage_instance_id] = stage_requested_on

            if due_date:
                # Convert due_date to user timezone if it exists, otherwise use UTC
                if user_timezone:
                    due_date = due_date.astimezone(user_timezone)
                else:
                    due_date = due_date.astimezone(pytz.UTC)

                number_of_days_for_due_date = (due_date - today).days
                stage_id_name_due_date_map[stage_instance_id] = (
                    stage["stage_name"],
                    number_of_days_for_due_date,
                )
            else:
                stage_id_name_due_date_map[stage_instance_id] = (
                    stage["stage_name"],
                    None,
                )

        stage_pending_approvers_map = {}
        is_line_item_level_enabled = get_line_item_approval_flag(self.client_id)
        payee_request_count_map = {}
        if is_line_item_level_enabled:
            payee_request_count_map = get_sub_request_count_for_approval_request_id(
                self.client_id, approval_requests
            )

        for approval_request in approval_requests:
            if approval_request["stage_instance_id"] not in stage_pending_approvers_map:
                stage_pending_approvers_map[approval_request["stage_instance_id"]] = []

            entity_type = approval_request["entity_type"]

            if entity_type == APPROVAL_ENTITY_TYPES.PAYOUT_LINE_ITEM.value:
                pending_count = payee_request_count_map[
                    approval_request["approval_request_id"]
                ]["pending"]

                if pending_count > 0:
                    stage_pending_approvers_map[
                        approval_request["stage_instance_id"]
                    ].append(employee_details_map[approval_request["approver"]])
            else:
                stage_pending_approvers_map[
                    approval_request["stage_instance_id"]
                ].append(employee_details_map[approval_request["approver"]])

        email_id_stage_status_map = {
            instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]: {
                "stage_name": stage_id_name_due_date_map[
                    instance_id_stage_map[instance_record["approval_wf_instance_id"]]
                ][0],
                "due_date": stage_id_name_due_date_map[
                    instance_id_stage_map[instance_record["approval_wf_instance_id"]]
                ][1],
                "pending_approvers": stage_pending_approvers_map[
                    instance_id_stage_map[instance_record["approval_wf_instance_id"]]
                ],
                "approved_requests_count": approved_requests_count_by_instance_ids.get(
                    instance_record["approval_wf_instance_id"],
                    0,
                ),
                "stage_requested_on": stage_id_requested_time_map.get(
                    instance_id_stage_map[instance_record["approval_wf_instance_id"]],
                    None,
                ),
            }
            for instance_record in instance_records
            if instance_record["approval_wf_instance_id"] in instance_id_stage_map
        }

        email_id_approvals_updated_by_map = email_id_approval_extra_details[
            "email_id_approvals_updated_by_map"
        ]
        email_id_approvals_updated_by_map_with_details = {}

        for updated_by_email_id in email_id_approvals_updated_by_map:
            updated_by = email_id_approvals_updated_by_map[updated_by_email_id]
            email_id_approvals_updated_by_map_with_details[updated_by_email_id] = (
                employee_details_map.get(updated_by, None)
            )

        email_id_approval_extra_details["email_id_approvals_updated_by_map"] = (
            email_id_approvals_updated_by_map_with_details
        )

        self.logger.info("END: Fetch_email_id_stage_status_map")
        return {
            "email_id_stage_status_map": email_id_stage_status_map,
            **email_id_approval_extra_details,
        }

    def get_email_id_approval_extra_details(self, instance_records: list[dict]):
        self.logger.info("BEGIN: Fetch_email_id_approval_extra_details")
        email_id_approvals_updated_by_map = {}

        instance_id_updated_by_map = {
            instance_record["approval_wf_instance_id"]: (
                instance_record["additional_details"].get("updated_by", None)
                if (
                    instance_record["additional_details"]
                    and isinstance(instance_record["additional_details"], dict)
                )
                else None
            )
            for instance_record in instance_records
        }

        email_id_approvals_updated_by_map = {
            instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]: (
                (instance_id_updated_by_map[instance_record["approval_wf_instance_id"]])
                if instance_record["approval_wf_instance_id"]
                in instance_id_updated_by_map
                else None
            )
            for instance_record in instance_records
        }

        email_id_rejected_details_map = {}
        is_line_item_level_enabled = get_line_item_approval_flag(self.client_id)
        if not is_line_item_level_enabled:
            rejected_instance_ids = [
                instance_record["approval_wf_instance_id"]
                for instance_record in instance_records
                if instance_record["status"] == APPROVAL_WORKFLOW_STATUS.REJECTED.value
            ]
            if rejected_instance_ids:
                rejected_requests = ApprovalRequestsAccessor(
                    self.client_id
                ).get_requests_by_instance_id_status(
                    rejected_instance_ids,
                    [APPROVAL_WORKFLOW_STATUS.REJECTED.value],
                    as_dict=True,
                    projection=["approval_wf_instance_id", "comments", "approver"],
                )
                request_instance_id_comments_map = {
                    request["approval_wf_instance_id"]: request["comments"]
                    for request in rejected_requests
                }

                for request in rejected_requests:
                    approver_who_rejected = request["approver"]
                    approval_wf_instance_id = request["approval_wf_instance_id"]
                    instance_id_updated_by_map[approval_wf_instance_id] = (
                        approver_who_rejected
                    )

                email_id_approvals_updated_by_map = {
                    instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]: (
                        instance_id_updated_by_map[
                            instance_record["approval_wf_instance_id"]
                        ]
                        if instance_record["approval_wf_instance_id"]
                        in instance_id_updated_by_map
                        else None
                    )
                    for instance_record in instance_records
                }

                email_id_rejected_details_map = {
                    instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]: (
                        request_instance_id_comments_map.get(
                            instance_record["approval_wf_instance_id"], None
                        )
                    )
                    for instance_record in instance_records
                }

        self.logger.info("END: Fetch_email_id_approval_extra_details")
        return {
            "email_id_approvals_updated_by_map": email_id_approvals_updated_by_map,
            "email_id_rejected_details_map": email_id_rejected_details_map,
        }

    def get_payout_data(
        self,
        filters=None,
        search_term=None,
        offset=0,
        limit=20,
        is_export=False,
        is_bulk_action=False,
        is_approval_on=False,
        logged_in_user=None,
        orderby_fields: list[SortInfoType] | None = None,
        selected_columns=None,
    ):
        self.is_export = is_export

        if filters is None:
            filters = {}

        if selected_columns is None:
            selected_columns = []

        if not is_export:
            selected_columns += self.payout_view_default_columns

        self.logger.info("BEGIN: Payout data for period-{} ".format(self.ped))

        self.currency_map = get_currency_symbol(self.client_id)
        self.logger.info("Got currency map")

        self.active_countries = get_active_countries(self.client_id)
        self.logger.info("Got active countries data")

        self.custom_fields_data = get_custom_fields_details(self.client_id)
        self.logger.info("Got custom field details")

        is_payout_value = False
        is_view_payroll = False
        if logged_in_user:
            is_payout_value = is_payout_value_permission(self.client_id, logged_in_user)
            is_view_payroll = is_view_payroll_permission(self.client_id, logged_in_user)
        effective_date = self.ped
        email_id_approval_status_map = {}
        email_id_entity_type_map = {}
        email_id_stage_status_map = {}
        email_id_approval_details_map = {}

        # If approval_status filter provided in filters then fetch approval status based on filters
        if filters.get("approval_status"):
            filter_type = filters.get("approval_status").get("type", "IN")
            filter_value = filters.get("approval_status").get("value")
            self.logger.info(
                "Approval status filter provided - {}".format(filter_value)
            )
            period_string = self.ped.strftime("%Y-%m-%d")

            is_not_requested_required = (
                APPROVAL_WORKFLOW_STATUS.NOT_REQUESTED.value in filter_value
            )

            if APPROVAL_WORKFLOW_STATUS.CANCELLED.value in filter_value:
                filter_value = [
                    (
                        APPROVAL_WORKFLOW_STATUS.ABORTED.value
                        if approval_filter == APPROVAL_WORKFLOW_STATUS.CANCELLED.value
                        else approval_filter
                    )
                    for approval_filter in filter_value
                ]

            instance_records = ApprovalInstanceAccessor(
                self.client_id
            ).get_latest_status_by_period_and_status(period_string)
            # fetch all active stages for the fetched instances
            active_stages = ApprovalInstanceStageAccessor(
                self.client_id
            ).get_all_stage_ids_list(
                filters={
                    "approval_wf_instance_id__in": [
                        instance_record["approval_wf_instance_id"]
                        for instance_record in instance_records
                    ],
                    "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                },
                projection=[
                    "approval_wf_instance_id",
                    "stage_instance_id",
                    "stage_name",
                    "due_date",
                    "requested_time",
                ],
            )

            email_id_approval_details_map = self.get_email_id_approval_details_map(
                logged_in_user, instance_records, active_stages
            )

            email_id_stage_status_map = email_id_approval_details_map[
                "email_id_stage_status_map"
            ]

            active_stage_ids = [stage["stage_instance_id"] for stage in active_stages]

            # if any one of the sub requests are rejected, fetch that instance id
            partially_rejected_wf_ids = SubApprovalRequestsAccessor(
                self.client_id
            ).get_req_ids_by_stage_id_and_status(
                active_stage_ids,
                ApprovalSubRequestsStatus.Declined().name,
            )
            emails_not_in = []
            emails_in = []

            for instance_record in instance_records:
                email = instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
                # Atleast 1 sub request is declined, in this case.
                # In DB we have reuested status for this instance, but in UI wee need to show
                # needs_attention status. So, we are setting needs_attention status here.
                if (
                    instance_record["approval_wf_instance_id"]
                    in partially_rejected_wf_ids
                    and instance_record["status"]
                    == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                ):
                    email_id_approval_status_map[email] = (
                        APPROVAL_WORKFLOW_STATUS.NEEDS_ATTENTION.value
                    )
                else:
                    email_id_approval_status_map[email] = instance_record["status"]
                email_id_entity_type_map[email] = instance_record["entity_type"]
                # If approval_wf_instance_id is in partially_rejected_wf_ids then one of the sub requests
                # is declined.
                if (
                    instance_record["approval_wf_instance_id"]
                    in partially_rejected_wf_ids
                    and instance_record["status"]
                    == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                ):
                    # If needs_attention is selected in filter, then include the email in emails_in
                    if APPROVAL_WORKFLOW_STATUS.NEEDS_ATTENTION.value in filter_value:
                        if filter_type == "NOTIN":
                            emails_not_in.append(email)
                        else:
                            emails_in.append(email)
                    # If needs_attention is not selected in filter, then include the email in emails_not_in
                    else:
                        if filter_type == "NOTIN":
                            emails_in.append(email)
                        else:
                            emails_not_in.append(email)
                else:
                    if is_not_requested_required:
                        if filter_type == "IN":
                            if instance_record["status"] not in filter_value:
                                emails_not_in.append(email)
                        else:
                            if instance_record["status"] not in filter_value:
                                emails_in.append(email)
                    else:
                        if filter_type == "IN":
                            if instance_record["status"] in filter_value:
                                emails_in.append(email)
                        else:
                            if instance_record["status"] in filter_value:
                                emails_not_in.append(email)

            self.logger.info(
                f"Approval status fetched for period: {email_id_approval_status_map}"
            )

            self.logger.info(
                f"Entity type email map for period: {email_id_entity_type_map}"
            )
            if is_not_requested_required:
                if filter_type == "IN":
                    if emails_not_in:
                        self.logger.info(
                            f"Approval status filter for emails not in - {emails_not_in}"
                        )
                        filters["email_id_not_in"] = emails_not_in
                else:
                    if emails_in:
                        self.logger.info(
                            f"Approval status filter for emails in - {emails_in}"
                        )
                        filters["email_id_in"] = emails_in
                    else:
                        return {"data": [], "count": 0}
            else:
                if filter_type == "IN":
                    if emails_in:
                        self.logger.info(
                            f"Approval status filter for emails in - {emails_in}"
                        )
                        filters["email_id_in"] = emails_in
                    else:
                        return {"data": [], "count": 0}
                else:
                    if emails_not_in:
                        self.logger.info(
                            f"Approval status filter for emails not in - {emails_not_in}"
                        )
                        filters["email_id_not_in"] = emails_not_in

        if filters.get("stage_status"):
            if not email_id_stage_status_map:
                period_string = self.ped.strftime("%Y-%m-%d")
                instance_records = ApprovalInstanceAccessor(
                    self.client_id
                ).get_latest_status_by_period_and_status(period_string)
                # fetch all active stages for the fetched instances
                active_stages = ApprovalInstanceStageAccessor(
                    self.client_id
                ).get_all_stage_ids_list(
                    filters={
                        "approval_wf_instance_id__in": [
                            instance_record["approval_wf_instance_id"]
                            for instance_record in instance_records
                        ],
                        "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                    },
                    projection=[
                        "approval_wf_instance_id",
                        "stage_instance_id",
                        "stage_name",
                        "due_date",
                        "requested_time",
                    ],
                )

                email_id_approval_details_map = self.get_email_id_approval_details_map(
                    logged_in_user, instance_records, active_stages
                )
                email_id_stage_status_map = email_id_approval_details_map[
                    "email_id_stage_status_map"
                ]

            filter_type = filters.get("stage_status").get("type", "IN")
            filter_value = filters.get("stage_status").get("value")

            email_ids_in = []
            email_ids_not_in = []
            if filter_type == "IN":
                if "on_track" in filter_value:
                    on_track_mail_ids = [
                        email_id
                        for email_id, stage_status in email_id_stage_status_map.items()
                        if stage_status["due_date"] is None
                        or stage_status["due_date"] >= 0
                    ]
                    email_ids_in.extend(on_track_mail_ids)
                if "overdue" in filter_value:
                    overdue_mail_ids = [
                        email_id
                        for email_id, stage_status in email_id_stage_status_map.items()
                        if stage_status["due_date"] is not None
                        and stage_status["due_date"] < 0
                    ]
                    email_ids_in.extend(overdue_mail_ids)

                if not email_ids_in:
                    return {"data": [], "count": 0}

                if "email_id_in" in filters:
                    email_ids_in = list(set(filters["email_id_in"]) & set(email_ids_in))

                if not email_ids_in:
                    return {"data": [], "count": 0}

                filters["email_id_in"] = email_ids_in
            elif filter_type == "NOTIN":
                if "on_track" in filter_value:
                    on_track_mail_ids = [
                        email_id
                        for email_id, stage_status in email_id_stage_status_map.items()
                        if stage_status["due_date"] is None
                        or stage_status["due_date"] >= 0
                    ]
                    email_ids_not_in.extend(on_track_mail_ids)
                if "overdue" in filter_value:
                    overdue_mail_ids = [
                        email_id
                        for email_id, stage_status in email_id_stage_status_map.items()
                        if stage_status["due_date"] is not None
                        and stage_status["due_date"] < 0
                    ]
                    email_ids_not_in.extend(overdue_mail_ids)

                if not email_ids_not_in:
                    return {"data": [], "count": 0}

                if "email_id_not_in" in filters:
                    email_ids_not_in = list(
                        set(filters["email_id_not_in"]) & set(email_ids_not_in)
                    )

                if not email_ids_not_in:
                    return {"data": [], "count": 0}

                filters["email_id_not_in"] = email_ids_not_in

        if filters.get("awaiting_response_from"):
            if not email_id_stage_status_map:
                period_string = self.ped.strftime("%Y-%m-%d")
                instance_records = ApprovalInstanceAccessor(
                    self.client_id
                ).get_latest_status_by_period_and_status(period_string)
                # fetch all active stages for the fetched instances
                active_stages = ApprovalInstanceStageAccessor(
                    self.client_id
                ).get_all_stage_ids_list(
                    filters={
                        "approval_wf_instance_id__in": [
                            instance_record["approval_wf_instance_id"]
                            for instance_record in instance_records
                        ],
                        "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                    },
                    projection=[
                        "approval_wf_instance_id",
                        "stage_instance_id",
                        "stage_name",
                        "due_date",
                        "requested_time",
                    ],
                )

                email_id_approval_details_map = self.get_email_id_approval_details_map(
                    logged_in_user, instance_records, active_stages
                )
                email_id_stage_status_map = email_id_approval_details_map[
                    "email_id_stage_status_map"
                ]

            filter_type = filters.get("awaiting_response_from").get("type", "IN")
            filter_value = filters.get("awaiting_response_from").get("value", [])
            filter_value = [
                filter_value_item["value"] for filter_value_item in filter_value
            ]

            email_ids_in = []
            email_ids_not_in = []
            if filter_value:
                for email_id, pending_on in email_id_stage_status_map.items():
                    pending_approvers = pending_on.get("pending_approvers", [])
                    pending_approvers_email_ids = [
                        pending_approver["email_id"]
                        for pending_approver in pending_approvers
                    ]
                    for pending_on_email_filter in filter_value:
                        if pending_on_email_filter in pending_approvers_email_ids:
                            if filter_type == "IN":
                                email_ids_in.append(email_id)
                            elif filter_type == "NOTIN":
                                email_ids_not_in.append(email_id)

            if filter_type == "IN":
                if not email_ids_in:
                    return {"data": [], "count": 0}
            elif filter_type == "NOTIN":
                if not email_ids_not_in:
                    return {"data": [], "count": 0}

            if email_ids_in:
                if "email_id_in" in filters:
                    email_ids_in_new = set(filters["email_id_in"]).intersection(
                        set(email_ids_in)
                    )
                    if not email_ids_in_new:
                        return {"data": [], "count": 0}
                    filters["email_id_in"] = list(email_ids_in_new)
                else:
                    filters["email_id_in"] = email_ids_in

            if email_ids_not_in:
                if "email_id_not_in" in filters:
                    email_ids_not_in_new = set(filters["email_id_not_in"]).intersection(
                        set(email_ids_not_in)
                    )
                    if not email_ids_not_in_new:
                        return {"data": [], "count": 0}
                    filters["email_id_not_in"] = list(email_ids_not_in_new)
                else:
                    filters["email_id_not_in"] = email_ids_not_in

            del filters["awaiting_response_from"]

        if filters.get("user_status"):
            filters["status"] = filters["user_status"]
            del filters["user_status"]

        base_query = (
            PayoutStatusQueryBuilder(self.client_id)
            .payouts_kd_aware()
            .filter_period_data(ped=self.ped)
            .join_employee()
            .join_commission_sec_kd_details()
            .join_employee_payroll_details(
                effective_date=effective_date, sec_kd_aware=True
            )
            .join_hierarchy(effective_date=effective_date)
            .join_plan_details(effective_date=effective_date)
            .join_custom_field_data(effective_date=effective_date, sec_kd_aware=True)
        )
        has_custom_calendar = get_client_features(self.client_id).get(
            "custom_calendar", False
        )
        if has_custom_calendar:
            base_query = base_query.join_custom_calendar_data()

        def apply_filters(qb):
            query_filters = {
                "search_term": search_term,
                **filters,
                "custom_field_data": filters.get("custom_field"),
                "custom_field_types_map": self.custom_fields_data["types_map"],
                "effective_date": effective_date,
                "is_payout_view": True,
            }
            qb = qb.apply_filters(**query_filters)

        apply_filters(base_query)
        final_query = (
            base_query.clone_deep()
            .join_reporting_manager_details()
            .select_employees_details_payout()
            .select_payroll_details_payout()
            .select_hierarchy_details_payout()
            .select_all_custom_field_data()
            .select_employee_full_name()
            .select_employee_profile_picture()
            .select_all_payout_status()
            .select_distinct_employee_email_id_payouts()
            .order_by_payouts_payee_email()
        )
        if has_custom_calendar:
            final_query = final_query.select_custom_calendar_details()

        orderby_fields = orderby_fields or []

        offset = offset * limit

        no_offset_limit = is_bulk_action or is_export or (len(orderby_fields) > 0)

        if no_offset_limit == False:
            final_query = final_query.limit_and_offset(limit, offset).query

        count_employees_sql = (
            base_query.clone_deep().count_distinct_employee_email_id().get_sql()
        )
        get_payouts_status_sql = final_query.get_sql()
        self.logger.info(
            f"Raw SQL for payouts query constructed {get_payouts_status_sql}"
        )

        with connection.cursor() as cursor:
            cursor.execute(count_employees_sql)
            count_res = cursor.fetchone()
            emp_count = count_res[0]
            cursor.execute(get_payouts_status_sql)
            columns = [col[0] for col in cursor.description]
            payout_details = cursor.fetchall()
        self.logger.info("Got payout details from SQL")
        columns_index = dict(zip(columns, range(len(columns))))
        grouped_employees = pydash.group_by(
            payout_details, lambda row: row[columns_index["employee_email_id"]]
        )
        payees = list(grouped_employees.keys())
        if not payees:
            return {"data": [], "count": emp_count}
        paid_amount_data = SettlementTotalService(
            self.client_id,
            self.ped,
            payee_emails=payees,
        ).get_payouts_view_data()
        self.logger.info("Got paid amount data")
        if not is_payout_value:
            for payee_email, transactions in paid_amount_data.items():
                if logged_in_user == payee_email:
                    continue
                for transaction in transactions:
                    transaction["amount"] = "-"

        plan_name_map = {}
        payee_plans = {}
        if payees:
            plan_details = PlanDetailsAllAccessor(
                client_id=self.client_id
            ).get_all_employees_plans(effective_date, payees)
            self.logger.info("Got plan details for payees")
            payee_plans = {}
            plan_ids = set()
            for plan in plan_details:
                plan_id = str(plan["plan_id"])
                plan_ids.add(plan_id)
                if plan["employee_email_id"] not in payee_plans:
                    payee_plans[plan["employee_email_id"]] = {
                        "main_plans": [],
                        "spiff_plans": [],
                    }
                if plan["plan_type"] == "MAIN":
                    payee_plans[plan["employee_email_id"]]["main_plans"].append(plan_id)
                else:
                    payee_plans[plan["employee_email_id"]]["spiff_plans"].append(
                        plan_id
                    )
            plan_names = CommissionPlanAccessor(
                client_id=self.client_id
            ).get_plan_names_for_ids(plan_ids)
            self.logger.info("Got plan names for plan ids")
            plan_name_map = {
                str(plan["plan_id"]): plan["plan_name"] for plan in plan_names
            }

        self.logger.info("Got payout/paid amount details")
        result = []
        quick_filters_count = {
            "unpaid": 0,
            "partially_paid": 0,
            "locked_payouts": 0,
            "unlocked_payouts": 0,
        }

        # If no approval status filter provided then fetch status for filtered payouts
        if not filters.get("approval_status") and is_approval_on:
            self.logger.info("Getting approval status for filtered payees.")
            email_entity_key_map = {}
            for payee in payees:
                email_entity_key_map[payee] = (
                    payee + ENTITY_KEY_DELIMETER + self.ped.strftime("%Y-%m-%d")
                )
            instance_records = ApprovalInstanceAccessor(
                self.client_id
            ).get_latest_instances_by_entity_keys(list(email_entity_key_map.values()))
            # fetch all active stages for the fetched instances
            active_stages = ApprovalInstanceStageAccessor(
                self.client_id
            ).get_all_stage_ids_list(
                filters={
                    "approval_wf_instance_id__in": [
                        instance_record["approval_wf_instance_id"]
                        for instance_record in instance_records
                    ],
                    "status": APPROVAL_WORKFLOW_STATUS.REQUESTED.value,
                },
                projection=[
                    "approval_wf_instance_id",
                    "stage_instance_id",
                    "stage_name",
                    "due_date",
                    "requested_time",
                ],
            )

            if not email_id_stage_status_map:
                email_id_approval_details_map = self.get_email_id_approval_details_map(
                    logged_in_user, instance_records, active_stages
                )

            active_stage_ids = [stage["stage_instance_id"] for stage in active_stages]

            # if any one of the sub requests are rejected, fetch that instance id
            partially_rejected_wf_ids = SubApprovalRequestsAccessor(
                self.client_id
            ).get_req_ids_by_stage_id_and_status(
                active_stage_ids,
                ApprovalSubRequestsStatus.Declined().name,
            )
            email_id_approval_status_map = {
                instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]: (
                    APPROVAL_WORKFLOW_STATUS.NEEDS_ATTENTION.value
                    if instance_record["approval_wf_instance_id"]
                    in partially_rejected_wf_ids
                    and instance_record["status"]
                    == APPROVAL_WORKFLOW_STATUS.REQUESTED.value
                    else instance_record["status"]
                )
                for instance_record in instance_records
            }

            email_id_entity_type_map = {
                instance_record["entity_key"].split(ENTITY_KEY_DELIMETER)[
                    0
                ]: instance_record["entity_type"]
                for instance_record in instance_records
            }
            self.logger.info(
                f"Entity type email map for period: {email_id_entity_type_map}"
            )

        email_in_req_comm_adj = set()
        if get_commission_adjustment_approvals_flag(self.client_id):
            comm_adj_status_records = list(
                CommissionAdjustmentStatusAccessor(
                    self.client_id
                ).get_requested_commission_adjustments_for_payee_in_period(
                    payee_email=payees, ped=self.ped
                )
            )
            for comm_adj_status_record in comm_adj_status_records:
                email_in_req_comm_adj.add(comm_adj_status_record["payee_email_id"])

        all_role_permission = RolePermissionsAccessor(
            self.client_id
        ).get_role_ids_and_display_names()
        self.role_permission_id_name_map = {
            str(rl["role_permission_id"]): rl["display_name"]
            for rl in all_role_permission
        }

        for email, employee_data in grouped_employees.items():
            if (
                has_custom_calendar
                and employee_data[0][columns_index["custom_calendar_name"]]
            ):
                payout_frequency = employee_data[0][
                    columns_index["custom_calendar_name"]
                ]
            else:
                payout_frequency = employee_data[0][columns_index["payout_frequency"]]
            payee_primary_plans = []
            payee_spiff_plans = []
            if email in payee_plans:
                payee_primary_plans = [
                    plan_name_map[plan_id]
                    for plan_id in payee_plans[email]["main_plans"]
                ]
                payee_spiff_plans = [
                    plan_name_map[plan_id]
                    for plan_id in payee_plans[email]["spiff_plans"]
                ]
            if employee_data[0][columns_index["payment_status"]] == "Unpaid":
                quick_filters_count["unpaid"] += 1
            if employee_data[0][columns_index["payment_status"]] == "Partially Paid":
                quick_filters_count["partially_paid"] += 1
            if (
                employee_data[0][columns_index["comm_calc_status"]] == "Frozen"
                and employee_data[0][columns_index["settlement_calc_status"]]
                == "Frozen"
            ):
                quick_filters_count["locked_payouts"] += 1
            else:
                quick_filters_count["unlocked_payouts"] += 1

            custom_field_data_for_export = resolve_custom_field_data_for_user_filters(
                employee_data[0][columns_index["custom_field_data"]],
                system_name_default_values_map=self.custom_fields_data[
                    "system_name_default_values_map"
                ],
                types_map=self.custom_fields_data["types_map"],
                dropdown_options_map=self.custom_fields_data["dropdown_options_map"],
                as_dicts=True,
            )

            # ------------ Applying view:payoutvalueothers permission -----------
            if email == logged_in_user or is_payout_value:
                payout_amount_data = {
                    "total_payout": employee_data[0][columns_index["total_payout"]],
                    "paid_amount": employee_data[0][columns_index["paid_amount"]],
                    "processed_amount": employee_data[0][
                        columns_index["processed_amount"]
                    ],
                    "ignored_amount": employee_data[0][columns_index["ignored_amount"]],
                    "pending_amount": employee_data[0][columns_index["pending_amount"]],
                    "commission": employee_data[0][columns_index["commission"]],
                    "payout_split_up": (
                        json.loads(employee_data[0][columns_index["payout_split_up"]])
                        if employee_data[0][columns_index["payout_split_up"]]
                        else {}
                    ),
                }

            else:
                payout_amount_data = {
                    "total_payout": "-",
                    "paid_amount": "-",
                    "processed_amount": "-",
                    "ignored_amount": "-",
                    "pending_amount": "-",
                    "commission": "-",
                    "payout_split_up": {},
                }
            if email == logged_in_user or is_view_payroll:
                payout_amount_data.update(
                    {
                        "base_pay": (
                            employee_data[0][columns_index["fixed_pay"]]
                            if employee_data[0][columns_index["fixed_pay"]]
                            else None
                        ),
                        "variable_pay_as_per_period": employee_data[0][
                            columns_index["variable_pay_as_per_period"]
                        ],
                    }
                )
            else:
                payout_amount_data.update(
                    {"base_pay": "-", "variable_pay_as_per_period": "-"}
                )

            if is_bulk_action == "LOCK_UNLOCK_BULK_ACTION":
                result.append(
                    {
                        "payee_email_id": email,
                        "comm_calc_status": employee_data[0][
                            columns_index["comm_calc_status"]
                        ],
                        "settlement_calc_status": employee_data[0][
                            columns_index["settlement_calc_status"]
                        ],
                        "is_comm_adj_requested": (
                            True if email in email_in_req_comm_adj else False
                        ),
                    }
                )

            elif is_bulk_action == "BULK_EMAIL_STATEMENTS":
                result.append(
                    {
                        "payee_email_id": email,
                        "full_name": employee_data[0][columns_index["full_name"]],
                        "period_start_date": employee_data[0][
                            columns_index["period_start_date"]
                        ].strftime("%Y-%m-%d"),
                        "total_payout": str(payout_amount_data["total_payout"]),
                        "payee_currency_symbol": (
                            self.currency_map[
                                employee_data[0][columns_index["pay_currency"]]
                            ]
                            if employee_data[0][columns_index["pay_currency"]]
                            else None
                        ),
                        "payout_frequency": employee_data[0][
                            columns_index["payout_frequency"]
                        ],
                    }
                )

            elif is_bulk_action == "BULK_REQUEST_APPROVALS":
                if (
                    employee_data[0][columns_index["comm_calc_status"]] == "Frozen"
                    and employee_data[0][columns_index["settlement_calc_status"]]
                    == "Frozen"
                ):
                    result.append(
                        {
                            "email_id": email,
                            "payout": str(payout_amount_data["total_payout"]),
                            "currency": employee_data[0][columns_index["pay_currency"]],
                        }
                    )
                    emp_count = len(result)

            else:
                payout_data = self.get_payout_data_for_payouts_page(
                    email_id_approval_status_map,
                    email_id_approval_details_map,
                    email_id_entity_type_map,
                    email,
                    columns_index,
                    paid_amount_data,
                    email_in_req_comm_adj,
                    employee_data,
                    payout_frequency,
                    payee_primary_plans,
                    payee_spiff_plans,
                    custom_field_data_for_export,
                )
                payout_data.update(payout_amount_data)
                result.append(payout_data)

        if is_bulk_action not in [
            "LOCK_UNLOCK_BULK_ACTION",
            "BULK_EMAIL_STATEMENTS",
            "BULK_REQUEST_APPROVALS",
        ]:
            result = self.apply_sort_and_format(result, limit, offset, orderby_fields)
            # result = self.reduce_result_columns(result, selected_columns)

        self.logger.info("END: Payout data for period-{} ".format(self.ped))
        return {"data": result, "count": emp_count}

    def reduce_result_columns(self, result, selected_columns):
        if not selected_columns:
            return result
        for payout in result:
            for key in list(payout.keys()):
                if key not in selected_columns:
                    del payout[key]
        return result

    def get_payout_data_for_payouts_page(
        self,
        email_id_approval_status_map,
        email_id_approval_details_map,
        email_id_entity_type_map,
        email,
        columns_index,
        paid_amount_data,
        email_in_req_comm_adj,
        employee_data,
        payout_frequency,
        payee_primary_plans,
        payee_spiff_plans,
        custom_field_data_for_export,
    ):
        email_id_stage_status_map = email_id_approval_details_map.get(
            "email_id_stage_status_map", {}
        )
        email_id_approvals_updated_by_map = email_id_approval_details_map.get(
            "email_id_approvals_updated_by_map", {}
        )
        email_id_rejected_details_map = email_id_approval_details_map.get(
            "email_id_rejected_details_map", {}
        )
        payout_data = {
            "period": employee_data[0][columns_index["period_start_date"]],
            "payee_name": employee_data[0][columns_index["full_name"]],
            "profile_picture": employee_data[0][columns_index["profile_picture"]],
            "payment_status": employee_data[0][columns_index["payment_status"]],
            "payee_currency": employee_data[0][columns_index["pay_currency"]],
            "approval_status": (
                email_id_approval_status_map[email]
                if email in email_id_approval_status_map
                else APPROVAL_WORKFLOW_STATUS.NOT_REQUESTED.value
            ),
            "approvals_updated_by": (
                email_id_approvals_updated_by_map[email]
                if email in email_id_approvals_updated_by_map
                else None
            ),
            "rejected_details": (
                email_id_rejected_details_map[email]
                if email in email_id_rejected_details_map
                else None
            ),
            "entity_type": (
                email_id_entity_type_map[email]
                if email in email_id_entity_type_map
                else APPROVAL_ENTITY_TYPES.PAYOUT.value
            ),
            "stage_name": (
                email_id_stage_status_map[email]["stage_name"]
                if email in email_id_stage_status_map
                else None
            ),
            "due_date": (
                email_id_stage_status_map[email]["due_date"]
                if email in email_id_stage_status_map
                else None
            ),
            "awaiting_response_from": (
                email_id_stage_status_map[email]["pending_approvers"]
                if email in email_id_stage_status_map
                else None
            ),
            "approved_requests_count": (
                email_id_stage_status_map[email]["approved_requests_count"]
                if email in email_id_stage_status_map
                else None
            ),
            "stage_requested_on": (
                email_id_stage_status_map[email]["stage_requested_on"]
                if email in email_id_stage_status_map
                else None
            ),
            "is_comm_adj_requested": True if email in email_in_req_comm_adj else False,
            "commission_percentage": employee_data[0][
                columns_index["commission_percentage"]
            ],
            "employee_id": employee_data[0][columns_index["employee_id"]],
            "primary_commission_plans": (
                ", ".join(payee_primary_plans) if payee_primary_plans else None
            ),
            "spiff_plans": ", ".join(payee_spiff_plans) if payee_spiff_plans else None,
            "designation": employee_data[0][columns_index["designation"]],
            "reporting_manager": employee_data[0][
                columns_index["reporting_manager_full_name"]
            ],
            "joining_date": (
                employee_data[0][columns_index["joining_date"]]
                if employee_data[0][columns_index["joining_date"]]
                else None
            ),
            "employment_country": (
                self.active_countries[
                    employee_data[0][columns_index["employment_country"]]
                ]
                if employee_data[0][columns_index["employment_country"]]
                in self.active_countries
                else None
            ),
            "payout_frequency": payout_frequency,
            "user_type": ", ".join(
                [
                    self.role_permission_id_name_map.get(role_id, "-")
                    for role_id in literal_eval(
                        employee_data[0][columns_index["user_role"]]
                    )
                ]
            ),
            "key": email,
            "payee_email_id": email,
            "payee_currency_symbol": (
                self.currency_map[employee_data[0][columns_index["pay_currency"]]]
                if employee_data[0][columns_index["pay_currency"]]
                else None
            ),
            "period_start_date": employee_data[0][columns_index["period_start_date"]],
            "period_end_date": self.ped,
            "comm_calc_status": employee_data[0][columns_index["comm_calc_status"]],
            "settlement_calc_status": employee_data[0][
                columns_index["settlement_calc_status"]
            ],
            "comm_freezed_at": (
                json.loads(employee_data[0][columns_index["comm_calc_details"]])[
                    "comm_freezed_at"
                ]
                if employee_data[0][columns_index["comm_calc_details"]]
                and json.loads(employee_data[0][columns_index["comm_calc_details"]])
                else None
            ),
            "comm_freezed_by": (
                json.loads(employee_data[0][columns_index["comm_calc_details"]])[
                    "comm_freezed_by"
                ]
                if employee_data[0][columns_index["comm_calc_details"]]
                and json.loads(employee_data[0][columns_index["comm_calc_details"]])
                else None
            ),
            "settlement_freezed_at": (
                json.loads(employee_data[0][columns_index["settlement_calc_details"]])[
                    "settlement_freezed_at"
                ]
                if employee_data[0][columns_index["settlement_calc_details"]]
                and json.loads(
                    employee_data[0][columns_index["settlement_calc_details"]]
                )
                else None
            ),
            "settlement_freezed_by": (
                json.loads(employee_data[0][columns_index["settlement_calc_details"]])[
                    "settlement_freezed_by"
                ]
                if employee_data[0][columns_index["settlement_calc_details"]]
                and json.loads(
                    employee_data[0][columns_index["settlement_calc_details"]]
                )
                else None
            ),
            "payout_transactions": (
                paid_amount_data[email] if email in paid_amount_data else []
            ),
            "user_source": employee_data[0][columns_index["user_source"]],
            "exit_date": employee_data[0][columns_index["exit_date"]],
            "created_date": employee_data[0][columns_index["created_date"]],
            "user_status": get_user_status(
                employee_data[0][columns_index["status"]],
                employee_data[0][columns_index["exit_date"]],
                employee_data[0][columns_index["deactivation_date"]],
            ),
            **custom_field_data_for_export,
        }

        return payout_data

    def apply_sort_and_format(
        self,
        payouts_data: list[dict],
        limit: int,
        offset: int,
        orderby_fields: list[SortInfoType],
    ):
        number_columns = [
            "total_payout",
            "paid_amount",
            "processed_amount",
            "ignored_amount",
            "pending_amount",
            "commission",
            "base_pay",
            "variable_pay_as_per_period",
            "commission_percentage",
        ]

        date_columns = [
            {"column": "period", "format": "%-d-%b-%Y"},
            {"column": "joining_date", "format": "%-d-%b-%Y"},
            {"column": "period_start_date", "format": "%Y-%m-%d"},
            {"column": "period_end_date", "format": "%Y-%m-%d"},
            {"column": "exit_date", "format": "%-d-%b-%Y"},
            {"column": "created_date", "format": "%-d-%b-%Y"},
        ]

        df = pd.DataFrame(payouts_data)

        if orderby_fields:
            df = self.sort_payout_data(
                df, orderby_fields, number_columns, limit, offset
            )

        for col in date_columns:
            if col["column"] in df.columns:
                df[col["column"]] = pd.to_datetime(
                    df[col["column"]], errors="coerce"
                ).dt.strftime(col["format"])

        for col in number_columns:
            if col in df.columns:
                if self.is_export:
                    df[col] = df[col].apply(
                        lambda x: round(x, 2) if x != "-" and x is not None else x
                    )
                else:
                    df[col] = df[col].apply(lambda x: str(x) if x is not None else x)

        clean_df = replace_nan_nat_for_df(df, empty_values=(pd.NaT, np.nan))

        return clean_df.to_dict("records")

    def sort_payout_data(
        self,
        payout_data: pd.DataFrame,
        orderby_fields: list[SortInfoType],
        number_columns: list[str],
        limit: int,
        offset: int,
    ):
        orderby_fields = remove_ignore_columns(orderby_fields, self.sort_ignore_columns)
        col_ids = [col["column"] for col in orderby_fields]

        # Repalcing "-" with None for number columns like total_payout, paid_amount, etc.
        to_replace_num_col = {}
        for col in number_columns:
            if col in col_ids:
                to_replace_num_col[col] = {"-": None}
        df = payout_data.replace(to_replace_num_col)

        custom_date_fields = []
        for col_id, col_type in self.custom_fields_data["types_map"].items():
            if col_type == "Date" and col_id in col_ids:
                custom_date_fields.append(col_id)

        # Change format of custom date fields to make it sortable.
        # Format stored in DB: %Y-%m-%d
        for col in custom_date_fields:
            df[col] = pd.to_datetime(df[col], errors="coerce")

        def get_pending_amount(df: pd.DataFrame, col: str):
            res = (df["payment_status"] != "Unpaid") * df[col].astype(str)
            # Replacing empty string values with None and converting the dtype to float.
            return res.replace({"": None}).astype(float)

        sorted_df = (
            SortUtility(df, orderby_fields)
            .sort_records(
                column_replacements=[
                    {
                        "column": "approval_status",
                        "callback": lambda df, col: df[col].replace(
                            {"not_requested": None, "aborted": "cancelled"}
                        ),
                    },
                    {"column": "pending_amount", "callback": get_pending_amount},
                ]
            )
            .apply_limit_offset(limit, offset)
            .as_dataframe()
        )

        for col in custom_date_fields:
            sorted_df[col] = pd.to_datetime(
                sorted_df[col], errors="coerce"
            ).dt.strftime("%Y-%m-%d")

        return sorted_df

    def format_approval_data_for_export(self, result):
        """
        Formats approval-related data for export by converting statuses, dates and names
        into export-friendly formats.

        Returns:
            dict: Modified result with formatted approval data
        """
        for payout in result["data"]:
            # Extract approval fields
            approval_status = payout["approval_status"]
            awaiting_response_from = payout["awaiting_response_from"]
            due_date = payout["due_date"]

            # Format and update approval data
            payout.update(
                {
                    # Convert approval status using export mapping
                    "approval_status": (
                        APPROVAL_WORKFLOW_STATUS_EXPORT.get(approval_status)
                        if approval_status
                        else None
                    ),
                    # Convert due date to integer if exists
                    "due_date": int(due_date) if due_date else None,
                    # Format approver names as comma-separated string
                    "awaiting_response_from": (
                        ", ".join(
                            f"{user.get('first_name')} {user.get('last_name')}"
                            for user in awaiting_response_from
                        )
                        if awaiting_response_from
                        else None
                    ),
                }
            )

            # Remove unused approval fields
            payout.pop("approval_requests_count", None)
            payout.pop("approvals_updated_by", None)
            payout.pop("rejected_details", None)

        return result

    def get_export_data_payouts(
        self, filters, is_approval_on=False, logged_email=None, columns=None
    ):
        default_export_cols = ["payee_name", "period", "payee_email_id"]
        columns = default_export_cols + columns if columns else default_export_cols

        result = self.get_payout_data(
            filters,
            is_export=True,
            is_approval_on=is_approval_on,
            logged_in_user=logged_email,
            selected_columns=columns,
        )
        if is_approval_on:
            result = self.format_approval_data_for_export(result)

        terms_to_localize = {
            "payout": PayoutExportTerms.PAYOUT.value,
            "commission_percent": PayoutExportTerms.COMM_PERCENT.value,
            "primary_comm_plan": PayoutExportTerms.PRIMARY_COMM_PLAN.value,
            "payout_frequency": PayoutExportTerms.PAYOUT_FREQUENCY.value,
            "commission": PayoutExportTerms.COMMISSION.value,
        }
        localized_terms = get_localized_words_service(terms_to_localize, self.client_id)
        export_df = pd.DataFrame(result["data"])
        for col in columns:
            if col not in export_df.columns:
                export_df[col] = None
        export_df = export_df[columns]
        export_df.rename(
            columns={
                "payee_name": "Name",
                "period": "Period",
                "payee_email_id": "Email ID",
                "payment_status": "Status",
                "payee_currency": "Currency",
                "total_payout": localized_terms["payout"],
                "approval_status": "Approval Status",
                "stage_requested_on": "Stage Requested On",
                "stage_name": "Approval Stage Name",
                "due_date": "Approval Days Remaining",
                "awaiting_response_from": "Approval Awaiting Response From",
                "paid_amount": "Paid",
                "processed_amount": "Processed",
                "ignored_amount": "Ignored",
                "pending_amount": "Pending",
                "commission_percentage": localized_terms["commission_percent"],
                "employee_id": "Employee ID",
                "primary_commission_plans": localized_terms["primary_comm_plan"],
                "spiff_plans": "SPIFF Plans",
                "designation": "Designation",
                "reporting_manager": "Reporting Manager",
                "joining_date": "Joining Date",
                "commission": localized_terms["commission"],
                "employment_country": "Employment Country",
                "payout_frequency": localized_terms["payout_frequency"],
                "user_type": "User Type",
                "base_pay": "Base Pay",
                "variable_pay_as_per_period": "Variable Pay",
                "user_source": "User Source",
                "exit_date": "Exit Date",
                "created_date": "Added Date",
                "user_status": "User Status",
                **self.custom_fields_data["system_name_display_name_map"],
            },
            inplace=True,
        )

        # Convert Approval Days Remaining to integer format, keeping None values as empty strings
        if "Approval Days Remaining" in export_df.columns:
            export_df["Approval Days Remaining"] = export_df[
                "Approval Days Remaining"
            ].apply(lambda x: str(int(x)) if pd.notnull(x) else "")

        # Remove approval columns if approvals is disabled
        if not is_approval_on:
            approval_columns = [
                "Approval Status",
                "Stage Requested On",
                "Approval Stage Name",
                "Approval Days Remaining",
                "Approval Awaiting Response From",
            ]
            export_df.drop(columns=approval_columns, errors="ignore", inplace=True)

        csv_data = export_df.to_csv(index=False, date_format="%d %b %Y")
        return csv_data
