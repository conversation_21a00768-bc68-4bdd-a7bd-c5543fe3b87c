from django.utils import timezone
from pypika import Criterion, Field, Table
from pypika import functions as fn
from sqlalchemy import func

from interstage_project.db.models import EsCharField
from spm.services.query_builder.mixins.base import BaseMixin
from spm.utils import UserStatus


class EmployeeMixin(BaseMixin):
    def __init__(self):
        super().__init__()
        self.employee = Table("employee")

    def get_employee_field(self, field, table_alias=None):
        table = self.employee
        if table_alias:
            table = table.as_(table_alias)
        return getattr(table, field)

    def select_field(self, field_name):
        field = Field(field_name) if field_name != "*" else field_name
        self.query = self.query.select(field)
        return self

    def select_basic_details_in_employee(self):
        self.query = self.query.select(
            self.employee.employee_email_id,
            self.employee.first_name,
            self.employee.last_name,
            self.employee.profile_picture,
        )
        self.select_employee_full_name()
        return self

    def select_exit_date(self):
        self.query = self.query.select(self.employee.exit_date)
        return self

    def select_all_employee(self):
        self.query = self.query.select(
            self.employee.temporal_id.as_("employee_tid"),
            self.employee.client_id,
            self.employee.employee_email_id,
            self.employee.user_role,
            self.employee.user_source,
            self.employee.profile_picture,
            self.employee.created_by,
            self.employee.created_date,
            self.employee.exit_date,
            self.employee.deactivation_date,
            self.employee.status,
            self.employee.first_name,
            self.employee.last_name,
            self.employee.last_commission_date,
        )
        return self

    def select_employees_details_payout(self):
        self.query = self.query.select(
            self.employee.temporal_id,
            self.employee.employee_email_id,
            self.employee.user_role,
            self.employee.exit_date,
            self.employee.user_source,
            self.employee.created_date,
            self.employee.deactivation_date,
            self.employee.status,
        )
        return self

    def select_employee_full_name(self):
        self.query = self.query.select(
            fn.Concat(
                self.employee.first_name,
                " ",
                self.employee.last_name,
                output_field=EsCharField(),
            ).as_("full_name")
        )
        return self

    def select_employee_profile_picture(self):
        self.query = self.query.select(
            self.employee.profile_picture,
        )
        return self

    def select_employee_full_name_with_lower_case(self):
        self.query = self.query.select(
            fn.Lower(
                fn.Concat(
                    Field("first_name"),
                    " ",
                    Field("last_name"),
                    output_field=EsCharField(),
                ).as_("full_name_lower")
            ),
            fn.Concat(
                self.employee.first_name,
                " ",
                self.employee.last_name,
                output_field=EsCharField(),
            ).as_("full_name"),
        ).distinct()
        return self

    def select_employee_email_id(self):
        self.query = self.query.select(self.employee.employee_email_id)
        return self

    def select_distinct_employee_email_id(self):
        self.query = self.query.select(self.employee.employee_email_id).distinct()
        return self

    def search_employee_full_name(self, text):
        self.query = self.query.where(
            (
                (
                    fn.Concat(
                        self.employee.first_name,
                        " ",
                        self.employee.last_name,
                        output_field=EsCharField(),
                    ).ilike(f"%{text}%")
                )
                | (self.employee.employee_email_id.ilike(f"{text}%"))
            )
        )
        return self

    def starts_with_name_or_email(self, text):
        self.query = self.query.where(
            (
                (
                    fn.Concat(
                        self.employee.first_name,
                        " ",
                        self.employee.last_name,
                        output_field=EsCharField(),
                    ).ilike(f"{text}%")
                )
                | (self.employee.last_name.ilike(f"{text}%"))
                | (self.employee.employee_email_id.ilike(f"{text}%"))
            )
        )
        return self

    def order_by_employee_full_name(self):
        self.query = self.query.orderby(Field("full_name"))
        return self

    def order_by_employee_email_id(self):
        self.query = self.query.orderby(Field("employee_email_id"))
        return self

    def order_by_employee_full_name_employee_email(self):
        """order the result by full_name and employee_email_id field in employee table"""
        self.query = self.query.orderby(
            fn.Lower(
                fn.Concat(
                    Field("first_name"),
                    " ",
                    Field("last_name"),
                    output_field=EsCharField(),
                )
            ),
            Field("employee_email_id"),
        )
        return self

    def groupby_by_email_and_name(self):
        self.query = self.query.groupby(
            self.employee.employee_email_id,
            self.employee.first_name,
            self.employee.last_name,
        )

    def order_by_employee_first_name(self):
        self.query = self.query.orderby(Field("first_name"))
        return self

    def order_by_employee_email(self):
        self.query = self.query.orderby(Field("employee_email_id"))
        return self

    def kd_ed_aware(self):
        cond = []
        cond.append(self.hierarchy.effective_end_date.isnull())
        cond.append(self.hierarchy.effective_end_date >= timezone.now())
        conditions = Criterion.any(cond)
        self.query = self.query.where(conditions)
        return self

    def kd_effective_date_aware(self, table_name):
        self.query = self.query.where(
            table_name.knowledge_end_date.isnull()
            & table_name.is_deleted.negate()
            & table_name.effective_start_date
            <= timezone.now()
        )
        cond = []
        cond.append(table_name.effective_end_date.isnull())
        cond.append(table_name.effective_end_date >= timezone.now())
        conditions = Criterion.any(cond)
        self.query = self.query.where(conditions)
        return self

    def exit_date_aware(self, effective_date=None):
        effective_date = timezone.now() if effective_date is None else effective_date
        self.query = self.query.where(
            (self.employee.exit_date >= effective_date)
            | self.employee.exit_date.isnull()
        )
        return self

    def where_employee_status_in(self, status, effective_date=None):
        effective_date = timezone.now() if effective_date is None else effective_date
        other_status = []
        exit_status = []
        for stat in status:
            if stat in [
                UserStatus.MARKED_FOR_EXIT.value,
                UserStatus.INACTIVE.value,
                UserStatus.PENDING_EXIT.value,
                UserStatus.MARKED_FOR_DEACTIVATION.value,
            ]:
                exit_status.append(stat)
            else:
                other_status.append(stat)
        conditions = None
        if len(exit_status) == 0 and len(other_status) > 0:
            conditions = (
                (self.employee.status.isin(other_status))
                & (self.employee.exit_date.isnull())
                & (self.employee.deactivation_date.isnull())
            )
        else:
            cond = []
            if len(exit_status) > 0:
                for stat in exit_status:
                    if stat == UserStatus.MARKED_FOR_EXIT.value:
                        cond.append(self.employee.exit_date > effective_date)
                    if stat == UserStatus.INACTIVE.value:
                        cond.append(self.employee.exit_date < effective_date)
                    if stat == UserStatus.MARKED_FOR_DEACTIVATION.value:
                        cond.append(
                            (self.employee.deactivation_date > effective_date)
                            & (self.employee.exit_date.isnull())
                        )
                    if stat == UserStatus.PENDING_EXIT.value:
                        cond.append(
                            (self.employee.deactivation_date < effective_date)
                            & (self.employee.exit_date.isnull())
                        )
            if len(other_status) > 0:
                cond.append(
                    self.employee.status.isin(other_status)
                    & self.employee.exit_date.isnull()
                    & self.employee.deactivation_date.isnull()
                )
            conditions = Criterion.any(cond)
        self.query = self.query.where(conditions)
        return self

    def where_user_source_in(self, sources):
        if sources and isinstance(sources, list):
            if "manual" in sources:
                self.query = self.query.where(
                    self.employee.user_source.isin(sources)
                    | self.employee.user_source.isnull()
                )
            else:
                self.query = self.query.where(self.employee.user_source.isin(sources))
        return self

    def where_employee_user_role_in_array(self, roles, negate=False):
        if roles and isinstance(roles, list):
            where_clause = self.employee.user_role.contains([roles[0]])
            for value in roles[1:]:
                where_clause |= self.employee.user_role.contains([value])
            if negate:
                where_clause = where_clause.negate()
            self.query = self.query.where(where_clause)
        return self

    def where_employee_exit_date_is(self, query_type, *dates):
        if query_type == "before":
            self.query = self.query.where(self.employee.exit_date < dates[0])
        elif query_type == "after":
            self.query = self.query.where(self.employee.exit_date > dates[0])
        elif query_type == "between":
            self.query = self.query.where(
                (self.employee.exit_date > dates[0])
                & (self.employee.exit_date < dates[1])
            )
        return self

    def where_employee_email_id_in(self, sub_query_or_emails):
        sub_query_or_emails = sub_query_or_emails or []
        self.query = self.query.where(
            self.employee.employee_email_id.isin(sub_query_or_emails)
        )
        return self

    def where_employee_email_id_not_in(self, sub_query_or_emails):
        self.query = self.query.where(
            self.employee.employee_email_id.notin(sub_query_or_emails)
        )
        return self

    def count_distinct_employee_email_id(self):
        self.query = self.query.select(
            fn.Count(self.employee.employee_email_id).distinct()
        )
        return self

    def count_employee_email_id(self, sub_query_or_emails):
        sub_query_or_emails = sub_query_or_emails or []
        self.query = self.query.select(
            fn.Count(self.employee.employee_email_id).distinct()
        ).where(self.employee.employee_email_id.isin(sub_query_or_emails))
        return self

    def apply_filters(self, **filters):
        self.logger.info(f"(EmployeeMixin) : Applying filters", extra=self.log_context)
        super().apply_filters(**filters)
        effective_date = (
            timezone.now()
            if filters.get("effective_date") is None
            or filters.get("is_payout_view", False)
            else filters["effective_date"]
        )
        if filters.get("search_term"):
            self.search_employee_full_name(filters.get("search_term"))
        if filters.get("status"):
            status = filters.get("status")
            if not isinstance(status, list):
                filter_type = status.get("type", "IN")
                status = status.get("value", [])
                if filter_type == "NOTIN":
                    status = [
                        stat.value for stat in UserStatus if stat.value not in status
                    ]
            self.where_employee_status_in(status, effective_date=effective_date)

        if filters.get("role"):
            if isinstance(filters.get("role"), list):
                self.where_employee_user_role_in_array(filters.get("role"))
            else:
                negate = (
                    True if filters.get("role").get("type", "IN") == "NOTIN" else False
                )
                self.where_employee_user_role_in_array(
                    filters["role"].get("value"), negate
                )
        if filters.get("user_source"):
            user_source_filter = filters.get("user_source")
            if isinstance(user_source_filter, list):
                self.where_user_source_in(user_source_filter)
            elif isinstance(user_source_filter, dict):
                self.filter_text_field(
                    getattr(self.employee, "user_source"),
                    user_source_filter.get("type"),
                    user_source_filter.get("value"),
                )
        if filters.get("exit_date"):
            self.filter_date_field(
                getattr(self.employee, "exit_date"),
                filters["exit_date"].get("type"),
                filters["exit_date"].get("date1"),
                filters["exit_date"].get("date2"),
                exclude_time=True,
            )
        if filters.get("email_id_in"):
            self.where_employee_email_id_in(filters.get("email_id_in"))
        if filters.get("email_id_not_in"):
            self.where_employee_email_id_not_in(filters.get("email_id_not_in"))
        return self


class EmployeePayrollDetailsMixin(BaseMixin):
    def __init__(self):
        super().__init__()
        self.employee_payroll_details = Table("employee_payroll_details")

    def get_employee_payroll_details_field(self, field, table_alias=None):
        table = self.employee_payroll_details
        if table_alias:
            table = table.as_(table_alias)
        return getattr(table, field)

    def select_all_payroll_details(self):
        self.query = self.query.select(
            self.employee_payroll_details.temporal_id.as_("payroll_tid"),
            self.employee_payroll_details.joining_date,
            self.employee_payroll_details.employee_id,
            self.employee_payroll_details.designation,
            self.employee_payroll_details.fixed_pay,
            self.employee_payroll_details.variable_pay,
            self.employee_payroll_details.pay_currency,
            self.employee_payroll_details.employment_country,
            self.employee_payroll_details.payout_frequency,
            self.employee_payroll_details.effective_start_date.as_(
                "payroll_effective_start_date"
            ),
            self.employee_payroll_details.effective_end_date.as_(
                "payroll_effective_end_date"
            ),
        )
        return self

    def select_payroll_details_payout(self):
        self.query = self.query.select(
            self.employee_payroll_details.joining_date,
            self.employee_payroll_details.employee_id,
            self.employee_payroll_details.designation,
            self.employee_payroll_details.fixed_pay,
            self.employee_payroll_details.variable_pay,
            self.employee_payroll_details.pay_currency,
            self.employee_payroll_details.employment_country,
            self.employee_payroll_details.payout_frequency,
        )
        return self

    def select_payroll_details_payout_frequency(self):
        self.query = self.query.select(
            self.employee_payroll_details.payout_frequency,
        )
        return self

    def select_employee_payroll_details_min_effective_start_date(
        self, alias="min_effective_start_date"
    ):
        self.query = self.query.select(
            fn.Min(self.employee_payroll_details.effective_start_date).as_(alias)
        )
        return self

    def where_payout_frequency_in(self, payout_frequencies):
        self.query = self.query.where(
            self.employee_payroll_details.payout_frequency.isin(payout_frequencies)
        )
        return self

    def where_employement_country_in(self, countries):
        self.query = self.query.where(
            self.employee_payroll_details.employment_country.isin(countries)
        )
        return self

    def where_pay_currency_in(self, currencies):
        self.query = self.query.where(
            self.employee_payroll_details.pay_currency.isin(currencies)
        )
        return self

    def where_joining_date_is(self, query_type, *dates):
        if query_type == "before":
            self.query = self.query.where(
                self.employee_payroll_details.joining_date < dates[0]
            )
        elif query_type == "after":
            self.query = self.query.where(
                self.employee_payroll_details.joining_date > dates[0]
            )
        elif query_type == "between":
            self.query = self.query.where(
                (self.employee_payroll_details.joining_date > dates[0])
                & (self.employee_payroll_details.joining_date < dates[1])
            )
        return self

    def apply_filters(self, **filters):
        self.logger.info(
            f"(EmployeePayrollDetailsMixin) : Applying filters", extra=self.log_context
        )
        super().apply_filters(**filters)
        if filters.get("payout_frequency"):
            if isinstance(filters.get("payout_frequency"), list):
                self.where_payout_frequency_in(filters.get("payout_frequency"))
            else:
                self.filter_text_field(
                    getattr(self.employee_payroll_details, "payout_frequency"),
                    filters["payout_frequency"].get("type"),
                    filters["payout_frequency"].get("value"),
                )
        if filters.get("employment_country"):
            if isinstance(filters.get("employment_country"), list):
                self.where_employement_country_in(filters.get("employment_country"))
            else:
                self.filter_text_field(
                    getattr(self.employee_payroll_details, "employment_country"),
                    filters["employment_country"].get("type"),
                    filters["employment_country"].get("value"),
                )
        if filters.get("pay_currency"):
            if isinstance(filters.get("pay_currency"), list):
                self.where_pay_currency_in(filters.get("pay_currency"))
            else:
                self.filter_text_field(
                    getattr(self.employee_payroll_details, "pay_currency"),
                    filters["pay_currency"].get("type"),
                    filters["pay_currency"].get("value"),
                )

        if filters.get("joining_date"):
            self.filter_date_field(
                getattr(self.employee_payroll_details, "joining_date"),
                filters["joining_date"].get("type"),
                filters["joining_date"].get("date1"),
                filters["joining_date"].get("date2"),
                exclude_time=True,
            )
        if filters.get("fixed_pay"):
            self.filter_numeric_field(
                getattr(self.employee_payroll_details, "fixed_pay"),
                filters["fixed_pay"].get("type"),
                filters["fixed_pay"].get("value1"),
                filters["fixed_pay"].get("value2"),
            )
        if filters.get("variable_pay"):
            self.filter_numeric_field(
                getattr(self.employee_payroll_details, "variable_pay"),
                filters["variable_pay"].get("type"),
                filters["variable_pay"].get("value"),
            )
        if filters.get("designation"):
            self.filter_text_field(
                getattr(self.employee_payroll_details, "designation"),
                filters["designation"].get("type"),
                filters["designation"].get("value"),
            )
        if filters.get("employee_id"):
            self.filter_text_field(
                getattr(self.employee_payroll_details, "employee_id"),
                filters["employee_id"].get("type"),
                filters["employee_id"].get("value"),
            )
        return self

    def group_by_employee_payroll_details_employee_email_id(self):
        self.query = self.query.groupby(self.employee_payroll_details.employee_email_id)
        return self


class HierarchyMixin(BaseMixin):
    def __init__(self):
        super().__init__()
        self.hierarchy = Table("hierarchy")

    def get_hierarchy_field(self, field, table_alias=None):
        table = self.hierarchy
        if table_alias:
            table = table.alias(table_alias)
        return getattr(table, field)

    def select_all_hierarchy(self):
        self.query = self.query.select(
            self.hierarchy.temporal_id.as_("hierarchy_tid"),
            self.hierarchy.reporting_manager_email_id,
            self.hierarchy.effective_start_date.as_("hierarchy_effective_start_date"),
            self.hierarchy.effective_end_date.as_("hierarchy_effective_end_date"),
        )
        return self

    def select_hierarchy_details_payout(self):
        self.query = self.query.select(self.hierarchy.reporting_manager_email_id)
        return self

    def select_distinct_reporting_manager_email_id(self):
        self.query = self.query.select(
            self.hierarchy.reporting_manager_email_id
        ).distinct()
        return self

    def select_reporting_manager_email_id(self):
        self.query = self.query.select(self.hierarchy.reporting_manager_email_id)
        return self

    def where_reporting_manager_in(self, manager_emails):
        self.query = self.query.where(
            self.hierarchy.reporting_manager_email_id.isin(manager_emails)
        )
        return self

    def where_effective_dates_from(self, year_dates):
        start_date, end_date = year_dates[0], year_dates[1]
        self.query = self.query.where(
            (self.hierarchy.effective_start_date <= end_date)
            | (
                (self.hierarchy.effective_end_date >= start_date)
                | (self.hierarchy.effective_end_date.isnull())
            )
        )
        return self

    def apply_filters(self, **filters):
        self.logger.info(f"(HierarchyMixin) : Applying filters", extra=self.log_context)
        super().apply_filters(**filters)
        if filters.get("reporting_manager"):
            if isinstance(filters.get("reporting_manager"), list):
                self.where_reporting_manager_in(filters.get("reporting_manager"))
            else:
                self.filter_text_field(
                    getattr(self.hierarchy, "reporting_manager_email_id"),
                    filters["reporting_manager"].get("type"),
                    filters["reporting_manager"].get("value"),
                )
        return self


class PlanDetailsMixin(BaseMixin):
    def __init__(self):
        super().__init__()
        self.plan_details = Table("plan_details")

    def get_plan_details_field(self, field, table_alias=None):
        table = self.plan_details
        if table_alias:
            table = table.as_(table_alias)
        return getattr(table, field)

    def select_all_plan_details(self):
        self.query = self.query.select(
            self.plan_details.temporal_id.as_("plan_detail_tid"),
            self.plan_details.plan_id,
            self.plan_details.plan_type,
            self.plan_details.effective_start_date.as_("plan_effective_start_date"),
            self.plan_details.effective_end_date.as_("plan_effective_end_date"),
        )
        return self

    def select_plan_details_payout(self):
        self.query = self.query.select(
            self.plan_details.plan_id, self.plan_details.plan_type
        )
        return self

    def apply_filters(self, **filters):
        self.logger.info(
            f"(PlanDetailsMixin) : Applying filters", extra=self.log_context
        )
        super().apply_filters(**filters)
        if filters.get("commission_plan"):
            filter_type = filters.get("commission_plan").get("type")
            filter_value = filters.get("commission_plan").get("value")
            if filter_type != "NOTIN":
                self.filter_text_field(
                    getattr(self.plan_details, "plan_id"),
                    filter_type,
                    filter_value,
                )
        return self


class EmployeeQuotaMixin(BaseMixin):
    def __init__(self):
        super().__init__()
        self.employee_quota = Table("employee_quota")

    def construct_quota_filters(
        self, quota_category=None, quota_schedule=None, quota_year=None
    ):
        conditions = []
        if quota_category:
            conditions.append(self.employee_quota.quota_category_name == quota_category)

        if quota_schedule:
            conditions.append(self.employee_quota.quota_schedule_type == quota_schedule)

        if quota_year:
            conditions.append(self.employee_quota.quota_year == quota_year)

        query_conditions = Criterion.all(conditions)
        self.query = self.query.where(query_conditions)
        return self

    def select_basic_quota_details(self):
        self.query = self.query.select(
            self.employee_quota.quota_schedule_type,
            self.employee_quota.quota_category_name,
            self.employee_quota.quota_year,
        )
        return self

    def select_non_effective_employee_quotas(self):
        self.query = (
            self.query.where(self.employee_quota.is_team_quota == False)
            .select(
                self.employee_quota.quota_category_name,
                self.employee_quota.quota_year,
                func.count().label("count"),
            )
            .groupby(
                self.employee_quota.quota_category_name, self.employee_quota.quota_year
            )
        )
        return self

    def apply_filters(self, **filters):
        super().apply_filters(**filters)
        return self
