import base64
import csv
import datetime
import io
import time as t1
import traceback
import uuid

from dateutil.parser import parse
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from django.utils.timezone import make_aware
from pydash import nest
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

import spm.services.commission_actions_service.payout_action_service as add_lock
import spm.services.settlement_actions_service.settlement_arrear_service as settlement_arrear
from commission_engine.accessors.client_accessor import (
    avoid_concurrent_register_payment,
    can_run_payout_snapshot_etl,
    can_run_settlement_snapshot_etl,
    can_run_sf_payout_snapshot,
    get_client,
    get_client_subscription_plan,
    is_notification_v2,
    is_read_settlement_from_snowflake,
    write_commission_to_snowflake,
)
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.accessors.settlement_accessor import SettlementAccessor
from commission_engine.accessors.settlement_lock_accessor import (
    SettlementLockAccessor,
    SettlementLockDetailsAccessor,
)
from commission_engine.models.settlement_models import (
    SettlementLock,
    SettlementLockDetail,
)
from commission_engine.services.payout_snapshot_service.inter_comm_snapshot_sync import (
    update_inter_comm_snapshot_locked_kd,
    update_inter_comm_snapshot_on_payout,
)
from commission_engine.services.payout_snapshot_service.optimized_payout_snapshot_sync import (
    update_payout_snapshot_locked_kd,
)
from commission_engine.services.payout_snapshot_service.payout_snapshot_sync_v3 import (
    update_payout_snapshot_on_payout,
)
from commission_engine.services.payout_status_changes_service import (
    add_payment_details_for_payout_status_changes,
)
from commission_engine.services.payout_status_service import (
    update_comm_payout_freeze_status,
    update_payout_details,
)
from commission_engine.services.settlement_snapshot_service.settlement_snapshot_sync import (
    update_settlement_snapshot_data_query,
)
from commission_engine.snowflake_accessors.settlement_accessor import (
    SettlementSnowflakeAccessor,
)
from commission_engine.utils import (
    convert_str_to_date,
    end_of_day,
    get_payout_details,
    last_day_of_month_from_month_year,
    make_aware_wrapper,
    start_of_day,
)
from commission_engine.utils.date_utils import get_start_date_by_freq_and_end_date
from commission_engine.utils.fx_utils import FXRate
from commission_engine.utils.general_data import Notification, PayoutStatusChangesTypes
from everstage_ddd.notifications import notify_payout_initiated_v2
from everstage_etl.tasks.utils.snapshot_utils import (
    can_run_snowflake_settlement_snapshot_etl,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import (
    LogWithContext,
    get_queue_name_respect_to_task_group,
)
from spm.accessors.notification_accessors import ClientNotificationAccessor
from spm.accessors.payout_accessor import PayoutAccessor, PayoutArrearsAccessor
from spm.accessors.payout_process_lock_accessors import PayoutProcessLockAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.models.payout_models import PayoutModel
from spm.services import audit_services
from spm.services.commission_actions_service.commission_email_services import (
    payout_paid_mails,
)
from spm.services.commission_actions_service.commission_slack_services import (
    notify_lock_status_to_admin,
    notify_paid_status_to_admin,
    notify_payment_initiated_to_payee,
)
from spm.services.commission_data_services import get_payee_payrolls_secondary_kd_aware


def update_settlement_lock_status(
    client_id,
    payee_ids,
    month,
    year,
    is_locked,
    audit,
    abort_approvals=False,
    both_comm_and_sett_report_etl=False,
):
    import everstage_etl.tasks.report_etl as report_etl

    time = timezone.now()
    data = {}
    logger = LogWithContext({})
    if payee_ids and month and year:
        event_type_code = EVENT["FREEZE_FREEZE-SETTLEMENT"]["code"]
        end_date = last_day_of_month_from_month_year(month, year)
        # settlement_details = get_all_settlement_payouts_per_month(client_id, month, request.data["year"])
        ###################### audit log #####################

        audit_data = {
            "payee_ids": payee_ids,
            "month": month,
            "year": year,
            "is_locked": is_locked,
        }
        updated_by = audit["updated_by"]
        updated_at = time
        ######################################################
        for payee in payee_ids:
            lock = SettlementLockAccessor(client_id).get_payee_lock_in_end_date(
                end_date, payee
            )
            event_key = payee + "#" + month + year
            summary = payee
            try:
                if is_locked:
                    if lock is None or not lock.is_locked:
                        add_settlement_lock_entry(
                            client_id,
                            payee,
                            end_date,
                            time,
                            audit,
                            lock,
                        )
                        audit_services.log(
                            client_id,
                            event_type_code,
                            event_key,
                            summary,
                            updated_by,
                            updated_at,
                            audit_data,
                        )
                else:
                    if lock and lock.is_locked:
                        # payout = PayoutAccessor(client_id).get_payout_for_payee_and_date(payee, end_date)
                        SettlementLockAccessor(client_id).mark_as_unlocked(
                            payee, end_date, time, audit
                        )
                        SettlementLockDetailsAccessor(
                            client_id
                        ).invalidate_lock_detail_with_lock_id(lock.lock_id, time)
                        event_type_code = EVENT["UNFREEZE_FREEZE-SETTLEMENT"]["code"]
                        audit_services.log(
                            client_id,
                            event_type_code,
                            event_key,
                            summary,
                            updated_by,
                            updated_at,
                            audit_data,
                        )
                    if abort_approvals:
                        # Abort approval instances if active
                        add_lock.abort_payout_instances(
                            client_id, end_date.strftime("%d-%m-%Y"), payee_ids, audit
                        )
                arrear_data = {
                    "period_end_date": make_aware_wrapper(end_of_day(end_date)),
                    "payee_id": payee,
                }
                settlement_arrear.add_update_payout_arrear(
                    client_id, arrear_data, time, audit
                )
            except:
                print(traceback.print_exc())
                return Response(
                    {"status", "FAILED"}, status=status.HTTP_400_BAD_REQUEST
                )
            data[payee] = {"status": "SUCCESS", "payee_id": payee}

        if not is_locked:
            # Trigger Report ETL
            subscription_plan = get_client_subscription_plan(client_id)
            report_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.REPORT.value
            )
            start_millisecond = int(round(t1.time() * 1000))
            report_sped = end_date
            report_ped = end_date if both_comm_and_sett_report_etl else None
            logger.info("TRIGGERING REPORT ETL")
            report_etl.report_object_etl_by_period_wrapper.si(
                client_id=client_id,
                ped=report_ped,
                sped=report_sped,
                payee_email_id=payee_ids,
                is_unfreeze_mode=True,
                is_custom_mode=False,
                log_context=logger.get_context(),
            ).set(queue=report_queue_name).apply_async()
            end_millisecond = int(round(t1.time() * 1000))
            logger.info(
                "update_lock_status report_object_etl_by_period_wrapper {}".format(
                    end_millisecond - start_millisecond
                )
            )

        notify_lock_status_to_admin(
            client_id, payee_ids, updated_by, is_locked, end_date
        )
        update_payout_details(client_id, end_date, payee_ids, audit=audit)
        return Response(data, status=status.HTTP_201_CREATED)
    else:
        return Response({"status", "FAILED"}, status=status.HTTP_400_BAD_REQUEST)


def update_settlement_lock_status_v2(
    client_id,
    payee_ids,
    date,
    is_locked,
    audit,
    abort_approvals=False,
    both_comm_and_sett_report_etl=False,
    e2e_sync_run_id: uuid.UUID | None = None,
):
    """optimised version of update_settlement_lock_status, Lock or unlock Settlement and payout_status in bulk mode for a list of payees"""
    import everstage_etl.tasks.report_etl as report_etl
    from everstage_etl.tasks.settlement_snapshot_sync_task import (
        settlement_snapshot_sync_task,
    )

    time = timezone.now()
    data = {}
    func_start_millisecond = int(round(t1.time() * 1000))
    settlement_snapshot_task_to_return = None
    logger = LogWithContext({})
    if payee_ids and date:
        event_type_code = EVENT["FREEZE_FREEZE-SETTLEMENT"]["code"]
        end_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        # settlement_details = get_all_settlement_payouts_per_month(client_id, month, request.data["year"])

        ###################### audit log #####################
        payout_status_objs = []
        updated_by = audit["updated_by"]
        ######################################################
        lock_list = SettlementLockAccessor(client_id).get_payees_lock_in_end_date(
            end_date, payee_ids
        )
        updated_payees = []
        if is_locked:
            lock_ids = []
            for lock in lock_list:
                if not lock or not lock.is_locked:
                    lock_ids.append(lock.lock_id)
                else:
                    payee_ids.remove(lock.payee_email_id)
            if len(payee_ids) > 0:
                start_millisecond = int(round(t1.time() * 1000))
                lock_settlement_for_payees(
                    client_id, payee_ids, lock_ids, end_date, time, audit
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_settlement_status lock_settlement_for_payees {}".format(
                        end_millisecond - start_millisecond
                    )
                )
                start_millisecond = int(round(t1.time() * 1000))
                payout_status_objs = update_comm_payout_freeze_status(
                    client_id, end_date, payee_ids, False, True, updated_by
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_settlement_status update_comm_payout_freeze_status {}".format(
                        end_millisecond - start_millisecond
                    )
                )
                subscription_plan = get_client_subscription_plan(client_id)
                snapshot_etl_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.SNAPSHOT.value
                )
                snapshot_etl_sync = can_run_payout_snapshot_etl(client_id)
                settlement_snapshot_etl = can_run_settlement_snapshot_etl(client_id)
                if settlement_snapshot_etl:
                    logger.info(
                        "Settlement Snapshot task for Lock as settlement snapshot flag is enabled."
                    )
                    update_settlement_snapshot_data_query.si(
                        client_id, {"payees": payee_ids, "ped": end_date}, time
                    ).set(queue=snapshot_etl_queue_name).apply_async()
                if snapshot_etl_sync:
                    update_payout_snapshot_locked_kd(
                        client_id, {"payees": payee_ids, "ped": end_date}, time
                    )
                if can_run_sf_payout_snapshot(client_id):
                    update_inter_comm_snapshot_locked_kd(
                        client_id, {"payees": payee_ids, "ped": end_date}, time
                    )
            updated_payees = payee_ids
        else:
            unlock_ids = []
            unlock_payees = []
            event_type_code = EVENT["UNFREEZE_FREEZE-SETTLEMENT"]["code"]
            for lock in lock_list:
                if lock and lock.is_locked:
                    unlock_ids.append(lock.lock_id)
                    unlock_payees.append(lock.payee_email_id)

            if len(unlock_payees) > 0:
                start_millisecond = int(round(t1.time() * 1000))
                unlocked_records = unlock_settlement_for_payees(
                    client_id, unlock_payees, unlock_ids, end_date, time, audit
                )
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_settlement_status unlock_settlement_for_payees {}".format(
                        end_millisecond - start_millisecond
                    )
                )

                subscription_plan = get_client_subscription_plan(client_id)
                report_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.REPORT.value
                )
                snapshot_etl_queue_name = get_queue_name_respect_to_task_group(
                    client_id, subscription_plan, TaskGroupEnum.SNAPSHOT.value
                )
                start_millisecond = int(round(t1.time() * 1000))
                report_sped = end_date
                report_ped = end_date if both_comm_and_sett_report_etl else None
                logger.info("TRIGGERING REPORT ETL - V2")
                if not write_commission_to_snowflake(client_id):
                    report_etl.report_object_etl_by_period_wrapper.si(
                        client_id=client_id,
                        ped=report_ped,
                        sped=report_sped,
                        payee_email_id=payee_ids,
                        is_unfreeze_mode=True,
                        is_custom_mode=False,
                        log_context=logger.get_context(),
                    ).set(queue=report_queue_name).apply_async()

                if can_run_snowflake_settlement_snapshot_etl(client_id):
                    logger.info(
                        "update_settlement_lock_status_v2: Returning settlement snapshot tasks"
                    )
                    settlement_snapshot_task_to_return = settlement_snapshot_sync_task(
                        client_id=client_id,
                        e2e_sync_run_id=e2e_sync_run_id or uuid.uuid4(),
                        curr_date=end_date,
                        secondary_kd=make_aware(datetime.datetime.now()),
                        run_unlock_sync=True,
                        unlocked_records=unlocked_records,
                    )
                else:
                    settlement_snapshot_sync_task.si(
                        client_id=client_id,
                        e2e_sync_run_id=uuid.uuid4(),
                        curr_date=end_date,
                        secondary_kd=make_aware(datetime.datetime.now()),
                        run_unlock_sync=True,
                        unlocked_records=unlocked_records,
                    ).set(queue=snapshot_etl_queue_name).apply_async()

                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_lock_status report_object_etl_by_period_wrapper {}".format(
                        end_millisecond - start_millisecond
                    )
                )
            if abort_approvals:
                start_millisecond = int(round(t1.time() * 1000))
                # Abort approval instances if active
                add_lock.abort_payout_instances(client_id, date, payee_ids, audit)
                end_millisecond = int(round(t1.time() * 1000))
                logger.info(
                    "update_settlement_status abort_payout_instances {}".format(
                        end_millisecond - start_millisecond
                    )
                )
            updated_payees = unlock_payees
            start_millisecond = int(round(t1.time() * 1000))
            payees_arrear_map = {}

            for payee in updated_payees:
                payee_arrear_param = {
                    "period_end_date": make_aware_wrapper(end_of_day(end_date)),
                    "payee_id": payee,
                }
                payees_arrear_map[payee] = payee_arrear_param
            settlement_arrear.add_update_payout_arrear_for_payees(
                client_id, updated_payees, payees_arrear_map, end_date, time, audit
            )

            end_millisecond = int(round(t1.time() * 1000))
            logger.info(
                "update_settlement_status add_update_payout_arrear {}".format(
                    end_millisecond - start_millisecond
                )
            )
            start_millisecond = int(round(t1.time() * 1000))
            payout_status_objs = update_payout_details(
                client_id, end_date, payee_ids, audit=audit
            )
            end_millisecond = int(round(t1.time() * 1000))
            logger.info(
                "update_settlement_status update_payout_details {}".format(
                    end_millisecond - start_millisecond
                )
            )

        for payee in updated_payees:
            data[payee] = {"status": "SUCCESS", "payee_id": payee}

        notify_lock_status_to_admin(client_id, payee_ids, updated_by, is_locked, date)
        start_millisecond = int(round(t1.time() * 1000))
        track_commission_lock_data(
            client_id,
            is_locked,
            event_type_code,
            updated_by,
            time,
            payout_status_objs,
            date,
        )
        end_millisecond = int(round(t1.time() * 1000))
        logger.info(
            "update_settlement_status track_commission_lock_data {}".format(
                end_millisecond - start_millisecond
            )
        )
        func_end_millisecond = int(round(t1.time() * 1000))
        logger.info(
            "update_settlement_status v2 {}".format(
                func_end_millisecond - func_start_millisecond
            )
        )
        return (
            Response(data, status=status.HTTP_201_CREATED),
            settlement_snapshot_task_to_return,
        )
    else:
        return (
            Response({"status", "FAILED"}, status=status.HTTP_400_BAD_REQUEST),
            settlement_snapshot_task_to_return,
        )


def lock_settlement_for_payees(client_id, payees, lock_ids, end_date, time, audit):
    """create settlement_lock and settlement_lock_details for a period for a list of payees"""
    settlement_acc = SettlementAccessor(client_id)
    if is_read_settlement_from_snowflake(client_id):
        settlement_acc = SettlementSnowflakeAccessor(client_id)
    payees_settlement_data = (
        settlement_acc.get_kd_settlement_data_for_end_date_for_payees(
            kd=time, ped=end_date, payee_emails=payees, as_dicts=True
        )
    )

    SettlementLockAccessor(client_id).invalidate_payees_lock(payees, end_date, time)
    SettlementLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_ids(
        lock_ids, time
    )

    payee_settlement_dict = {}
    for payee_settlement in payees_settlement_data:
        if payee_settlement["payee_email_id"] in payee_settlement_dict:
            payee_settlement_list = payee_settlement_dict[
                payee_settlement["payee_email_id"]
            ]
            payee_settlement_list.append(payee_settlement)
        else:
            payee_settlement_list = []
            payee_settlement_list.append(payee_settlement)
            payee_settlement_dict[payee_settlement["payee_email_id"]] = (
                payee_settlement_list
            )
    skipped_payees = set(payees) - set(payee_settlement_dict.keys())
    if skipped_payees:
        skipped_payee_payout_freq = get_payout_details(
            client_id=client_id, period_end_date=end_date, emails=list(skipped_payees)
        )
        lock_data_list = []
        for payee in skipped_payees:
            psd = get_start_date_by_freq_and_end_date(
                end_date, skipped_payee_payout_freq[payee]
            )
            lock_data_list.append(
                SettlementLock(
                    **{
                        "client_id": client_id,
                        "lock_id": uuid.uuid4(),
                        "additional_details": audit,
                        "knowledge_begin_date": time,
                        "period_start_date": psd,
                        "period_end_date": end_date,
                        "is_locked": True,
                        "locked_knowledge_date": time,
                        "plan_ids": None,
                        "payee_email_id": payee,
                    }
                )
            )
        SettlementLock.objects.bulk_create(lock_data_list)

    if payee_settlement_dict is not None:
        bulk_create_settlement_locks_for_payees(
            client_id, payee_settlement_dict, audit, time
        )


def unlock_settlement_for_payees(client_id, payees, lock_ids, end_date, time, audit):
    """unlock settlement_lock and settlement_lock_details for a period for a list of payees"""
    unlocked_obj = SettlementLockAccessor(client_id).mark_payees_as_unlocked(
        payees, end_date, time, audit
    )
    SettlementLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_ids(
        lock_ids, time
    )
    return unlocked_obj


def track_commission_lock_data(
    client_id,
    is_locked,
    event_type_code,
    updated_by,
    updated_at,
    payout_status_objs,
    date,
):
    """settlement lock, unlock bulk insertion into audit_trail"""

    audit_data_list = []
    for payout_status in payout_status_objs:
        audit_data = {}
        audit_data["calculated_payout"] = payout_status.total_payout
        audit_data["pending_amt"] = payout_status.pending_amount
        audit_data["payment_status"] = payout_status.payment_status
        payee = payout_status.payee_email_id
        if is_locked:
            audit_data["calculation_status"] = "Locked"
        else:
            audit_data["calculation_status"] = "Not Locked"

        event_key = payee + "#" + date
        summary = payee
        audit_data["event_key"] = event_key
        audit_data["summary"] = summary
        audit_data_list.append(audit_data)

    audit_services.bulk_log(
        client_id, event_type_code, updated_by, updated_at, audit_data_list
    )


def bulk_create_settlement_locks_for_payees(
    client_id, payee_settlement_dict, audit, time
):
    """creates new record in settlement_lock and settlement_lock_details for a list of payees"""

    lock_data_list = []
    settlement_details_obj = []
    for payee, payee_settlement in payee_settlement_dict.items():
        lock_id = uuid.uuid4()
        grouped_by_plan = nest(
            payee_settlement, "plan_id", "criteria_id", "settlement_rule_id"
        )
        settlement_plans = list(map(str, grouped_by_plan.keys()))
        settlement_lock_data = SettlementLock(
            **{
                "client_id": client_id,
                "lock_id": lock_id,
                "additional_details": audit,
                "knowledge_begin_date": time,
                "period_start_date": payee_settlement[0]["period_start_date"],
                "period_end_date": payee_settlement[0]["period_end_date"],
                "is_locked": True,
                "locked_knowledge_date": time,
                "plan_ids": settlement_plans,
                "payee_email_id": payee,
            }
        )

        lock_data_list.append(settlement_lock_data)
        for _plan_id, plan_data in grouped_by_plan.items():
            for _criteria_id, criteria_data in plan_data.items():
                for _rule_id, settlement_data in criteria_data.items():
                    settlement_detail = {
                        "client_id": client_id,
                        "additional_details": audit,
                        "knowledge_begin_date": time,
                        "payee_email_id": settlement_data[0]["payee_email_id"],
                        "plan_id": settlement_data[0]["plan_id"],
                        "criteria_id": settlement_data[0]["criteria_id"],
                        "lock_id": lock_id,
                        "settlement_rule_id": settlement_data[0]["settlement_rule_id"],
                    }
                    line_item_ids = []
                    settlement_amount = 0
                    for data in settlement_data:
                        settlement_amount = settlement_amount + data["amount"]
                        line_item_ids.append(data["line_item_id"])

                    settlement_detail["line_item_id"] = line_item_ids
                    settlement_detail["amount"] = settlement_amount

                    settlement_details_obj.append(
                        SettlementLockDetail(**settlement_detail)
                    )

    SettlementLock.objects.bulk_create(lock_data_list)
    SettlementLockDetail.objects.bulk_create(settlement_details_obj)


def add_settlement_lock_entry(client_id, payee, end_date, time, audit, lock):
    settlement_acc = SettlementAccessor(client_id)
    if is_read_settlement_from_snowflake(client_id):
        settlement_acc = SettlementSnowflakeAccessor(client_id)
    if lock:
        SettlementLockAccessor(client_id).invalidate_lock(payee, end_date, time)
        SettlementLockDetailsAccessor(client_id).invalidate_lock_detail_with_lock_id(
            lock.lock_id, time
        )
    lock_id = uuid.uuid4()
    all_settlement_data = settlement_acc.get_kd_settlement_data_for_end_date(
        kd=time, ped=end_date, payee_email=payee, as_dicts=True
    )
    if len(all_settlement_data) > 0:
        grouped_by_plan = nest(
            all_settlement_data, "plan_id", "criteria_id", "settlement_rule_id"
        )
        settlement_plans = list(map(str, grouped_by_plan.keys()))
        settlement_lock_data = SettlementLock(
            **{
                "client_id": client_id,
                "lock_id": lock_id,
                "additional_details": audit,
                "knowledge_begin_date": time,
                "period_start_date": all_settlement_data[0]["period_start_date"],
                "period_end_date": all_settlement_data[0]["period_end_date"],
                "is_locked": True,
                "locked_knowledge_date": time,
                "plan_ids": settlement_plans,
                "payee_email_id": payee,
            }
        )
        settlement_details_obj = []
        for _plan_id, plan_data in grouped_by_plan.items():
            for _criteria_id, criteria_data in plan_data.items():
                for _rule_id, settlement_data in criteria_data.items():
                    settlement_detail = {
                        "client_id": client_id,
                        "additional_details": audit,
                        "knowledge_begin_date": time,
                        "payee_email_id": settlement_data[0]["payee_email_id"],
                        "plan_id": settlement_data[0]["plan_id"],
                        "criteria_id": settlement_data[0]["criteria_id"],
                        "lock_id": lock_id,
                        "settlement_rule_id": settlement_data[0]["settlement_rule_id"],
                    }
                    line_item_ids = []
                    settlement_amount = 0
                    for data in settlement_data:
                        settlement_amount = settlement_amount + data["amount"]
                        line_item_ids.append(data["line_item_id"])

                    settlement_detail["line_item_id"] = line_item_ids
                    settlement_detail["amount"] = settlement_amount

                    settlement_details_obj.append(
                        SettlementLockDetail(**settlement_detail)
                    )

        SettlementLock.objects.bulk_create([settlement_lock_data])
        SettlementLockDetail.objects.bulk_create(settlement_details_obj)


def get_lock_and_payee_ids(locks, payee_ids) -> dict:
    """
    Get valid lock ids and payee ids from locks and payee ids
    """
    lock_ids = []
    payees_to_process = payee_ids.copy()
    for lock in locks:
        if not lock or not lock.is_locked:
            lock_ids.append(lock.lock_id)
        else:
            payees_to_process.remove(lock.payee_email_id)
    return {
        "lock_ids": lock_ids,
        "payee_ids": payees_to_process,
    }


@transaction.atomic
def update_paid_status(
    client_id, payee_ids, date, settlement_details, audit, logged_in_user=None
):
    time = timezone.now()
    data = {}

    payout_initiated_task = ClientNotificationAccessor(
        client_id
    ).get_notification_status(Notification.PAYOUT_INITIATED_NOTIFICATION)
    is_email_payout_initated_enabled = payout_initiated_task["email"]
    is_slack_payout_initated_enabled = payout_initiated_task["slack"]
    is_ms_teams_payout_initated_enabled = payout_initiated_task["ms_teams"]
    notification_v2 = is_notification_v2(client_id)

    # if payee_ids and date:
    modified_settlement_details_map = {}
    for _key, value in settlement_details.items():
        modified_settlement_details_map[value["payee_email_id"]] = value
    settlement_details = modified_settlement_details_map
    end_date = make_aware(end_of_day(parse(date, dayfirst=True)))

    settlement_lock = SettlementLockAccessor(client_id).get_payees_lock_in_end_date(
        end_date, payee_ids
    )
    commission_lock = CommissionLockAccessor(client_id).get_payees_lock_in_end_date(
        end_date, payee_ids
    )
    # Get valid lock ids and payee ids from locks and payee ids
    settlement_lock_details = get_lock_and_payee_ids(settlement_lock, payee_ids)
    commission_lock_details = get_lock_and_payee_ids(commission_lock, payee_ids)

    # Lock settlement for payees
    commission_lock_ids = commission_lock_details.get("lock_ids")
    commission_payee_ids = commission_lock_details.get("payee_ids")

    # Lock commission for payees
    settlement_payee_ids = settlement_lock_details.get("payee_ids")
    settlement_lock_ids = settlement_lock_details.get("lock_ids")
    try:
        if settlement_payee_ids and len(settlement_payee_ids) > 0:
            lock_settlement_for_payees(
                client_id=client_id,
                payees=settlement_payee_ids,
                lock_ids=settlement_lock_ids,
                end_date=end_date,
                time=time,
                audit=audit,
            )
        if commission_payee_ids and len(commission_payee_ids) > 0:
            add_lock.lock_commission_for_payees(
                client_id=client_id,
                payee_ids=commission_payee_ids,
                lock_ids=commission_lock_ids,
                ped=end_date,
                time=time,
                audit=audit,
            )
        for payee in payee_ids:
            add_settlement_payout_entry(
                client_id,
                payee,
                end_date,
                time,
                audit,
                settlement_details,
            )
            if not notification_v2:
                notify_payment_initiated_to_payee(
                    client_id,
                    payee,
                    settlement_details,
                    is_slack_payout_initated_enabled,
                    is_ms_teams_payout_initated_enabled,
                )
            data[payee] = {"success": True, "payee_id": payee}
    except Exception as e:
        raise SQLParseError() from e

    notify_paid_status_to_admin(
        client_id, payee_ids, audit["updated_by"], is_paid=True, date=end_date
    )
    update_payout_details(client_id, end_date, payee_ids, audit=audit)
    # Add payout status changes for payment register
    add_payment_details_for_payout_status_changes(
        client_id=client_id,
        ped=end_date,
        payees=payee_ids,
        event_type=PayoutStatusChangesTypes.PAYMENT_REGISTER.value,
    )
    snapshot_etl_sync = can_run_payout_snapshot_etl(client_id)
    if snapshot_etl_sync:
        update_payout_snapshot_on_payout(
            client_id, {"payees": payee_ids, "ped": end_date}, time
        )
        update_settlement_snapshot_data_query(
            client_id, {"payees": payee_ids, "ped": end_date}, time
        )
        if can_run_sf_payout_snapshot(client_id):
            update_inter_comm_snapshot_on_payout(
                client_id, {"payees": payee_ids, "ped": end_date}, time
            )
    # Sending statements with emails to respective users
    if logged_in_user and is_email_payout_initated_enabled and not notification_v2:
        payout_paid_mails(client_id, logged_in_user, settlement_details)

    if notification_v2 and logged_in_user:
        notify_payout_initiated_v2(
            client_id,
            logged_in_user=logged_in_user,
            payout_details=settlement_details,
        )

    if avoid_concurrent_register_payment(client_id):
        return data
    else:
        return Response(data, status=status.HTTP_201_CREATED)


def add_settlement_payout_entry(
    client_id, payee, end_date, time, audit, settlement_details
):
    payout_id = uuid.uuid4()
    client = get_client(client_id)
    base_currency = client.base_currency
    payout_amount = settlement_details[payee]["paid_amount"]
    payee_details = get_payee_payrolls_secondary_kd_aware(
        client_id, end_date, [payee], True
    )
    payee_currency = payee_details["payroll_data"][0]["pay_currency"]
    payout_adjustments = settlement_details[payee]["calculated_adjustments"]
    fx_rate = 1
    fx_rate_obj = FXRate(client_id)
    if base_currency != payee_currency:
        fx_rate = fx_rate_obj.get_fx_rate_kd(
            end_date, payee_currency, payee_details["sec_kd_data"][payee]
        )
        payout_amount = fx_rate_obj.change_to_base_currency(payout_amount, fx_rate)
    payout_adjustments["fx_rate"] = float(fx_rate)
    payout_data = PayoutModel(
        **{
            "client_id": client_id,
            "additional_details": audit,
            "knowledge_begin_date": time,
            "period_start_date": start_of_day(
                convert_str_to_date(settlement_details[payee]["period_start_date"])
            ),
            "period_end_date": end_of_day(
                convert_str_to_date(settlement_details[payee]["period_end_date"])
            ),
            "payee_id": payee,
            "payout_knowledge_date": time,
            "payout_amount": payout_amount,
            "is_paid": True,
            "payout_adjustments": payout_adjustments,
            "comment": settlement_details[payee]["comment"],
            "payout_id": payout_id,
            "transaction_date": time,
        }
    )
    payout_data.save()
    arrear_data = {
        "period_end_date": make_aware_wrapper(
            end_of_day(
                convert_str_to_date(settlement_details[payee]["period_end_date"])
            )
        ),
        "payee_id": payee,
    }
    settlement_arrear.add_update_payout_arrear(
        client_id, arrear_data, time, audit, False
    )


@transaction.atomic()
def invalidate_payout_id_as_unpaid(client_id, payout_id, audit, date):
    # pylint: disable=unused-argument
    time = timezone.now()
    try:
        qs = (
            PayoutAccessor(client_id)
            .client_kd_aware()
            .filter(payout_id=payout_id)
            .first()
        )
        PayoutAccessor(client_id).invalidate_payout_id(payout_id, time)
        payout_details = {
            "period_end_date": qs.period_end_date,
            "payee_id": qs.payee_id,
        }
        qs.is_paid = False
        qs.knowledge_begin_date = time
        qs.payout_knowledge_date = time
        qs.additional_details = audit
        PayoutAccessor(client_id).create_payout(qs)
        settlement_arrear.add_update_payout_arrear(
            client_id, payout_details, time, audit
        )

        notify_paid_status_to_admin(
            client_id, [qs.payee_id], audit["updated_by"], is_paid=False, date=date
        )
        update_payout_details(client_id, qs.period_end_date, [qs.payee_id], audit=audit)
        # Add payout status changes for payment invalidate
        add_payment_details_for_payout_status_changes(
            client_id=client_id,
            ped=qs.period_end_date,
            payees=[qs.payee_id],
            event_type=PayoutStatusChangesTypes.PAYMENT_INVALIDATE.value,
        )
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    except:
        return Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST)


@transaction.atomic
def invalidate_process_ignore_arrears(client_id, payee_email_id, arrear_month, audit):
    ped = make_aware_wrapper(
        end_of_day(datetime.datetime.strptime(arrear_month, "%Y-%m-%d"))
    )
    time = timezone.now()
    try:
        arrear_record = PayoutArrearsAccessor(
            client_id
        ).get_processed_arrears_for_payee_and_period(payee_id=payee_email_id, ped=ped)
        if arrear_record:
            arrear_record = arrear_record[0]
            PayoutArrearsAccessor(client_id).invalidate_arrear(
                payee_email_id, ped, time
            )
            arrear_record.pk = None
            arrear_record.pending_amount = arrear_record.ignored_amount
            arrear_record.ignored_amount = 0
            arrear_record.processed_at = time
            arrear_record.knowledge_begin_date = time
            arrear_record.additional_details = audit
            arrear_record.is_processed = False
            PayoutArrearsAccessor(client_id).persist_arrear(arrear_record)
            update_payout_details(client_id, ped, [payee_email_id], audit=audit)
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    except Exception as e:
        print(e)
        return Response({"status": "FAILED"}, status=status.HTTP_400_BAD_REQUEST)


def is_settlement_changed(client_id, end_date, payee_emails):
    settlement_acc = SettlementAccessor(client_id)
    if is_read_settlement_from_snowflake(client_id):
        settlement_acc = SettlementSnowflakeAccessor(client_id)
    period_end_date = make_aware(end_of_day(parse(end_date, dayfirst=True)))
    before_settlement_lock, after_settlement_lock = (
        settlement_acc.get_settlements_before_and_after_lock(
            period_end_date, payee_emails
        )
    )
    return add_lock.compare_payout_changes(
        before_settlement_lock, after_settlement_lock
    )


def acquire_payout_lock(cache_key, value=True, timeout=600):
    """Acquires a lock for payout processing by setting a cache key."""
    return cache.add(cache_key, value, timeout=timeout)


def release_payout_lock(cache_key):
    """Releases a lock for payout processing by deleting the cache key."""
    cache.delete(cache_key)


def generate_conflicting_payees_csv(conflicting_payees):
    output = io.StringIO()
    csv_writer = csv.writer(output)
    csv_writer.writerow(["Payee Email ID", "Error"])  # CSV Header

    for payee in conflicting_payees:
        csv_writer.writerow([payee, "Previous process in progress"])

    return base64.b64encode(output.getvalue().encode()).decode()


def acquire_lock_payout_process(client_id, payee_email_ids, period_end_date, event):
    """
    Acquire locks for payees and return a list of payee emails to process and conflicting payee emails.

    - First, check which payees already have locks.
    - Attempt to create locks for remaining payees.
    - Check again to see if any payees were not locked due to conflicts.
    """
    lock_accessor = PayoutProcessLockAccessor(client_id)

    # Get payees that already have an active lock
    payee_email_ids_locked = lock_accessor.payees_by_lock_status(
        payee_email_ids, period_end_date, event
    )

    payees_to_process = list(set(payee_email_ids) - set(payee_email_ids_locked))

    created_locks = lock_accessor.create_locks(
        payees_to_process, period_end_date, event
    )

    # Find payees that were not successfully locked (conflicts)
    successfully_locked = {lock.payee_email_id for lock in created_locks}
    conflicting_payees = list(set(payees_to_process) - successfully_locked)
    # Combine with originally locked payees (they were already locked before this attempt)
    conflicting_payees.extend(payee_email_ids_locked)

    return {
        "payee_ids_to_process": list(successfully_locked),
        "conflicting_payees": conflicting_payees,
    }
