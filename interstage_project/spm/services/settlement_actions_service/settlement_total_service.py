import collections

import pydash

import interstage_project.utils as iputils
import spm.services.commission_data_services as commission_data_services
from commission_engine.accessors.client_accessor import (
    get_base_currency_threshold,
    get_client,
    get_payee_currency_threshold,
)
from commission_engine.accessors.settlement_accessor import (
    CommissionSettlementMapAccessor,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.settlement_data_service import (
    get_commission_adjustments_payees,
    get_draws_payees,
    get_payee_list,
    get_settlement_records_payees,
)
from commission_engine.utils import (
    FXRate,
    date_utils,
    first_day_of_month,
    get_fiscal_year,
    get_period_draws,
    get_variable_pay_per_period,
    make_aware_wrapper,
)
from commission_engine.utils.general_data import static_frequencies
from spm.accessors.approval_workflow_accessors import ApprovalInstanceAccessor
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanCriteriaAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    HierarchyAccessor,
    PlanDetailsAllAccessor,
)
from spm.accessors.payout_accessor import PayoutAccessor, PayoutArrearsAccessor
from spm.constants.approval_workflow_constants import ENTITY_KEY_DELIMETER
from spm.services.custom_calendar_services import get_calendar_ids_and_psd_map
from spm.services.period_label_services import (
    get_period_label_ped_map,
    get_period_label_psd_ped_map,
)


class SettlementTotalService:
    def __init__(self, client_id, ped, payee_emails=None, logger=None):
        self.client_id = client_id
        self.ped = ped
        if payee_emails:
            self.payee_emails = payee_emails
        else:
            self.payee_emails = get_payee_list(self.client_id, self.ped)
        self.fx_rate_convertor = FXRate(self.client_id)
        self.plan_data = {}
        self.criteria_data = {}
        self.client_data = get_client(self.client_id)
        self.fiscal_start = self.client_data.fiscal_start_month
        self.payroll_dict = {}
        self.payee_dict = {}
        self.payee_psd_map = {}
        self.payee_ped_map = {}
        self.currency_symbol_map = {}
        # Mapping currency codes to locale ids.
        # For example:
        # {
        #     "USD": "en-US",
        #     "INR": "en-IN",
        #     "EUR": "fr-FR",
        #     ...
        # }
        self.currency_locale_map = {}
        self.payee_plans = {}
        self.payee_sec_kd_map = {}
        self.main_plan_details_dict = collections.defaultdict(list)
        self.spiff_plan_details_dict = collections.defaultdict(list)
        if not logger:
            log_context = {
                "client_id": client_id,
                "ped": ped,
                "payee_email": payee_emails,
            }
            self.logger = iputils.LogWithContext(log_context)
        else:
            self.logger = logger
        self.custom_calendar_details = {"psd_map": {}, "calendar_name_map": {}}
        self.custom_calendar_payees = []

    @staticmethod
    def group_by_payees(data, payee_field="payee_email_id"):
        grouped_data = dict()
        for record in data:
            if record[payee_field] not in grouped_data:
                grouped_data[record[payee_field]] = []
            grouped_data[record[payee_field]].append(record)
        return grouped_data

    def get_custom_calendar_details(self):
        self.custom_calendar_details = get_calendar_ids_and_psd_map(
            self.client_id, self.ped
        )

    def get_payee_details(self):
        payee_details = EmployeeAccessor(self.client_id).get_employees(
            self.payee_emails
        )
        self.payee_dict = (
            {x["employee_email_id"]: x for x in payee_details} if payee_details else {}
        )
        payee_payrolls = None
        if self.payee_emails:
            payee_payroll_sec_kd_data = (
                commission_data_services.get_payee_payrolls_secondary_kd_aware(
                    self.client_id, self.ped, self.payee_emails, True
                )
            )
            payee_payrolls = payee_payroll_sec_kd_data["payroll_data"]
            self.payee_sec_kd_map = payee_payroll_sec_kd_data["sec_kd_data"]
        self.payroll_dict = (
            {x["employee_email_id"]: x for x in payee_payrolls}
            if payee_payrolls
            else {}
        )

        payees_list = set(self.payroll_dict.keys())
        self.payee_emails = list(set(self.payee_emails).intersection(payees_list))

        for payee in self.payroll_dict:
            payee_frequency = self.payroll_dict[payee]["payout_frequency"]
            result = date_utils.get_period_start_and_end_date(
                self.ped,
                self.fiscal_start,
                payee_frequency,
                client_id=self.client_id,
            )
            if result["end_date"] != self.ped:
                self.payee_emails.remove(payee)
            else:
                if payee_frequency.lower() not in static_frequencies:
                    self.custom_calendar_payees.append(payee)
                self.payee_psd_map[payee] = result["start_date"]
                self.payee_ped_map[payee] = result["end_date"]

    def get_comm_period_wise_map(self, distinct_periods):
        return commission_data_services.get_payee_periods_secondary_kd_map(
            client_id=self.client_id,
            peds=list(distinct_periods),
            payee_emails=self.payee_emails,
            logger=self.logger,
        )

    @staticmethod
    def group_commission_by_criteria(commissions: list) -> dict:
        """groups commissions by plan_id and criteria_id"""

        criteria_level_commissions = dict()
        for commission in commissions:
            plan_id = str(commission.get("commission_plan_id"))
            criteria_id = str(commission.get("criteria_id"))
            amount = commission.get("amount")

            if not criteria_level_commissions.get(plan_id):
                criteria_level_commissions[plan_id] = {}

            if not criteria_level_commissions.get(plan_id, {}).get(criteria_id):
                criteria_level_commissions[plan_id][criteria_id] = 0

            criteria_level_commissions[plan_id][criteria_id] += amount

        return criteria_level_commissions

    @staticmethod
    def group_settlements_by_criteria(settlements: list) -> dict:
        """groups settlements by plan_id and criteria_id"""

        criteria_level_settlements = dict()
        for settlement in settlements:
            plan_id = str(settlement.get("plan_id"))
            criteria_id = str(settlement.get("criteria_id"))
            amount = settlement.get("amount")

            if criteria_level_settlements.get(plan_id) is None:
                criteria_level_settlements[plan_id] = {}

            if criteria_level_settlements.get(plan_id, {}).get(criteria_id) is None:
                criteria_level_settlements[plan_id][criteria_id] = 0

            criteria_level_settlements[plan_id][criteria_id] += amount

        return criteria_level_settlements

    def get_currency_symbol(self):
        self.currency_symbol_map = {
            country.currency_code: country.currency_symbol
            for country in list(CountriesAccessor(self.client_id).get_all_countries())
        }

    def get_currency_locale_map(self):
        """Get a map of all currency codes to its corresponding locale ids"""
        locales = CountriesAccessor(self.client_id).get_locale_ids_from_currency_code()
        self.currency_locale_map = {
            locale["currency_code"]: locale["locale_id"] for locale in locales
        }

    def get_plan_data(self):
        plan_records = PlanDetailsAllAccessor(self.client_id).get_all_employees_plans(
            self.ped, self.payee_emails
        )
        commission_plans = CommissionPlanAccessor(
            self.client_id
        ).get_all_commission_plans()
        plan_ids = []

        for plan in commission_plans:
            plan_ids.append(plan.plan_id)
            plan_details = {
                "plan_id": plan.plan_id,
                "plan_name": plan.plan_name,
                "plan_display_order": plan.plan_display_order,
                "plan_type": plan.plan_type,
            }
            self.plan_data[str(plan.plan_id)] = plan_details

        for x in plan_records:
            if x["employee_email_id"] not in self.payee_plans:
                self.payee_plans[x["employee_email_id"]] = set()
            self.payee_plans[x["employee_email_id"]].add(str(x["plan_id"]))
            plan_details = self.plan_data[str(x.get("plan_id"))]
            if plan_details["plan_type"] == "MAIN":
                self.main_plan_details_dict[x["employee_email_id"]].append(
                    plan_details["plan_name"]
                )
            else:
                self.spiff_plan_details_dict[x["employee_email_id"]].append(
                    plan_details["plan_name"]
                )

    def get_criteria_data(self):
        criteria_dict = PlanCriteriaAccessor(self.client_id).get_criteria_details(
            list(self.plan_data.keys())
        )
        self.criteria_data = {
            str(criteria.get("criteria_id")): criteria for criteria in criteria_dict
        }

    def get_deferred_and_earned_commissions_curr_period(
        self, commissions_curr_period, settlements_for_deals_in_curr_period, fx_rate
    ):
        """returns commissions earned in a period and settlements"""
        deferred_commissions = {}
        earned_commissions = {}
        curr_payout = {}
        total_deferred_for_a_period = 0
        total_earned_for_a_period = 0
        total_settlement_for_a_period = 0

        criteria_level_commissions = self.group_commission_by_criteria(
            commissions_curr_period
        )
        criteria_level_curr_settlements = self.group_settlements_by_criteria(
            settlements_for_deals_in_curr_period
        )

        for plan_id, commissions_by_plan in criteria_level_commissions.items():
            criteria_details_earned_commissions = {}
            criteria_details_deferred_commissions = {}
            criteria_details_payouts = {}
            total_settlement_in_plan = 0
            total_commission_in_plan = 0
            total_deferred_in_plan = 0
            for criteria_id, commission_amount in commissions_by_plan.items():
                criteria_record = self.criteria_data.get(criteria_id, {})
                settlement_amount = criteria_level_curr_settlements.get(
                    plan_id, {}
                ).get(criteria_id, 0)
                if fx_rate:
                    commission_amount = self.fx_rate_convertor.change_to_payee_currency(
                        commission_amount, fx_rate
                    )
                    settlement_amount = self.fx_rate_convertor.change_to_payee_currency(
                        settlement_amount, fx_rate
                    )

                commission_amount = float(commission_amount)
                settlement_amount = float(settlement_amount)
                deferred_amount = settlement_amount - commission_amount
                is_hidden_criteria = criteria_record.get("criteria_config", {}).get(
                    "criteria_is_hidden", False
                )

                criteria_details_deferred_commissions[criteria_id] = {
                    "criteria_id": criteria_id,
                    "criteria_name": criteria_record.get("criteria_name"),
                    "criteria_type": criteria_record.get("criteria_type"),
                    "criteria_description": criteria_record.get("criteria_description"),
                    "criteria_display_order": criteria_record.get(
                        "criteria_display_order"
                    ),
                    "datasheet_id": criteria_record.get("criteria_data", {}).get(
                        "datasheet_id"
                    ),
                    "databook_id": criteria_record.get("criteria_data", {}).get(
                        "databook_id"
                    ),
                    "amount": deferred_amount,
                    "is_hidden_criteria": is_hidden_criteria,
                    "sort_cols": pydash.get(
                        criteria_record, ["criteria_config", "sort_cols"]
                    )
                    or (
                        [
                            [
                                criteria_record["criteria_data"]["date_field"],
                                "asc",
                            ]
                        ]
                        if pydash.has(criteria_record, ["criteria_data", "date_field"])
                        else []
                    ),
                }

                criteria_details_earned_commissions[criteria_id] = {
                    "criteria_id": criteria_id,
                    "criteria_name": criteria_record.get("criteria_name"),
                    "criteria_type": criteria_record.get("criteria_type"),
                    "criteria_description": criteria_record.get("criteria_description"),
                    "criteria_display_order": criteria_record.get(
                        "criteria_display_order"
                    ),
                    "datasheet_id": criteria_record.get("criteria_data", {}).get(
                        "datasheet_id"
                    ),
                    "databook_id": criteria_record.get("criteria_data", {}).get(
                        "databook_id"
                    ),
                    "amount": commission_amount,
                    "is_hidden_criteria": is_hidden_criteria,
                    "sort_cols": pydash.get(
                        criteria_record, ["criteria_config", "sort_cols"]
                    )
                    or (
                        [
                            [
                                criteria_record["criteria_data"]["date_field"],
                                "asc",
                            ]
                        ]
                        if pydash.has(criteria_record, ["criteria_data", "date_field"])
                        else []
                    ),
                }

                criteria_details_payouts[criteria_id] = {
                    "criteria_id": criteria_id,
                    "criteria_name": criteria_record.get("criteria_name"),
                    "criteria_type": criteria_record.get("criteria_type"),
                    "criteria_description": criteria_record.get("criteria_description"),
                    "criteria_display_order": criteria_record.get(
                        "criteria_display_order"
                    ),
                    "datasheet_id": criteria_record.get("criteria_data", {}).get(
                        "datasheet_id"
                    ),
                    "databook_id": criteria_record.get("criteria_data", {}).get(
                        "databook_id"
                    ),
                    "amount": settlement_amount,
                    "is_hidden_criteria": is_hidden_criteria,
                    "sort_cols": pydash.get(
                        criteria_record, ["criteria_config", "sort_cols"]
                    )
                    or (
                        [
                            [
                                criteria_record["criteria_data"]["date_field"],
                                "asc",
                            ]
                        ]
                        if pydash.has(criteria_record, ["criteria_data", "date_field"])
                        else []
                    ),
                }

                total_commission_in_plan += commission_amount
                total_settlement_in_plan += settlement_amount
                total_deferred_in_plan += deferred_amount

            if plan_id in self.plan_data:
                deferred_commissions[plan_id] = {
                    "plan_id": plan_id,
                    "plan_name": self.plan_data.get(plan_id)["plan_name"],
                    "plan_display_order": self.plan_data.get(plan_id)[
                        "plan_display_order"
                    ],
                    "plan_type": self.plan_data.get(plan_id)["plan_type"],
                    "amount": total_deferred_in_plan,
                    "criteria_details": criteria_details_deferred_commissions,
                }

                earned_commissions[plan_id] = {
                    "plan_id": plan_id,
                    "plan_name": self.plan_data.get(plan_id)["plan_name"],
                    "plan_display_order": self.plan_data.get(plan_id)[
                        "plan_display_order"
                    ],
                    "plan_type": self.plan_data.get(plan_id)["plan_type"],
                    "amount": total_commission_in_plan,
                    "criteria_details": criteria_details_earned_commissions,
                }

                curr_payout[plan_id] = {
                    "plan_id": plan_id,
                    "plan_name": self.plan_data.get(plan_id)["plan_name"],
                    "plan_display_order": self.plan_data.get(plan_id)[
                        "plan_display_order"
                    ],
                    "plan_type": self.plan_data.get(plan_id)["plan_type"],
                    "amount": total_settlement_in_plan,
                    "criteria_details": criteria_details_payouts,
                }

            total_deferred_for_a_period += total_deferred_in_plan
            total_earned_for_a_period += total_commission_in_plan
            total_settlement_for_a_period += total_settlement_in_plan
        return {
            "deferred_commissions": deferred_commissions,
            "earned_commissions": earned_commissions,
            "curr_payout": curr_payout,
            "total_deferred_for_a_period": total_deferred_for_a_period,
            "total_earned_for_a_period": total_earned_for_a_period,
            "total_settled_for_a_period": total_settlement_for_a_period,
        }

    def get_previous_deferred_commissions(
        self,
        settlements_for_past_deals,
        payee_email,
        payee_currency=None,
        current_fx_rate=None,
    ):
        """returns the commissions earned but settled in a latter period"""
        distinct_periods = set()
        for record in settlements_for_past_deals:
            distinct_periods.add(record["comm_period_end_date"])
        sec_kd_data = self.get_comm_period_wise_map(distinct_periods)
        previous_deferred_commissions_by_period = {}
        total_previous_deferred = 0
        group_records_by_period = {}
        plan_ids = set()
        criteria_ids = set()
        period_psd_ped_map = {}
        has_period_label_feature = has_feature(self.client_id, "payout_period_label")
        label_map = {}

        for settlement in settlements_for_past_deals:
            plan_ids.add(str(settlement.get("plan_id")))
            criteria_ids.add(str(settlement.get("criteria_id")))
            fiscal_year = get_fiscal_year(
                self.fiscal_start, settlement.get("comm_period_start_date")
            )

            if payee_email in self.custom_calendar_payees:
                period = settlement.get("comm_period_end_date").strftime("%d-%b-%Y")
            else:
                period = date_utils.get_period_for_statement(
                    psd=settlement.get("comm_period_start_date"),
                    ped=settlement.get("comm_period_end_date"),
                    fiscal_start=self.fiscal_start,
                    fiscal_year=fiscal_year,
                )

            if period not in period_psd_ped_map:
                period_psd_ped_map[period] = {
                    "psd": settlement.get("comm_period_start_date"),
                    "ped": settlement.get("comm_period_end_date"),
                    "comm_ped": settlement.get("comm_period_end_date").strftime(
                        "%d/%m/%Y"
                    ),
                }
            if period not in group_records_by_period:
                group_records_by_period[period] = []

            group_records_by_period[period].append(settlement)

        if has_period_label_feature:
            period_tuples = list(
                set((x["psd"], x["ped"]) for x in period_psd_ped_map.values())
            )
            label_map = get_period_label_psd_ped_map(self.client_id, period_tuples)

        for period, settlements in group_records_by_period.items():
            previous_deferred_commissions_by_period[period] = {}
            total_deferred_in_period = 0
            criteria_level_past_settlements = self.group_settlements_by_criteria(
                settlements
            )
            plan_details = {}
            comm_period = period_psd_ped_map[period]["ped"].strftime("%Y-%m-%d")
            sec_kd_date = None
            if comm_period in sec_kd_data and payee_email in sec_kd_data[comm_period]:
                sec_kd_date = sec_kd_data[comm_period][payee_email]

            fx_rate = 1
            if payee_currency:
                fx_rate = self.fx_rate_convertor.get_fx_rate_kd(
                    period_psd_ped_map[period]["ped"],
                    payee_currency,
                    sec_kd_date,
                )
            for (
                plan_id,
                settlements_by_plan,
            ) in criteria_level_past_settlements.items():
                criteria_details = {}
                total_deferred_in_plan = 0
                for (
                    criteria_id,
                    settlement_amount,
                ) in settlements_by_plan.items():
                    settlement_amount = float(
                        self.fx_rate_convertor.change_to_payee_currency(
                            settlement_amount, fx_rate
                        )
                    )
                    if current_fx_rate:
                        settlement_amount = float(
                            self.fx_rate_convertor.change_to_base_currency(
                                settlement_amount, current_fx_rate
                            )
                        )
                    criteria_record = self.criteria_data.get(criteria_id, {})
                    is_hidden_criteria = criteria_record.get("criteria_config", {}).get(
                        "criteria_is_hidden", False
                    )
                    criteria_details[criteria_id] = {
                        "criteria_id": criteria_id,
                        "criteria_name": criteria_record.get("criteria_name"),
                        "criteria_type": criteria_record.get("criteria_type"),
                        "criteria_description": criteria_record.get(
                            "criteria_description"
                        ),
                        "criteria_display_order": criteria_record.get(
                            "criteria_display_order"
                        ),
                        "datasheet_id": criteria_record.get("criteria_data", {}).get(
                            "datasheet_id"
                        ),
                        "databook_id": criteria_record.get("criteria_data", {}).get(
                            "databook_id"
                        ),
                        "amount": settlement_amount,
                        "is_hidden_criteria": is_hidden_criteria,
                    }
                    total_deferred_in_plan += settlement_amount
                plan_details[plan_id] = {
                    "plan_id": plan_id,
                    "plan_name": self.plan_data.get(plan_id)["plan_name"],
                    "plan_display_order": self.plan_data.get(plan_id)[
                        "plan_display_order"
                    ],
                    "amount": total_deferred_in_plan,
                    "plan_type": self.plan_data.get(plan_id)["plan_type"],
                    "criteria_details": criteria_details,
                }
                total_deferred_in_period += total_deferred_in_plan
            previous_deferred_commissions_by_period[period] = {
                "period": (
                    label_map.get(
                        (
                            period_psd_ped_map[period]["psd"],
                            period_psd_ped_map[period]["ped"],
                        ),
                        period,
                    )
                    if has_period_label_feature
                    else period
                ),
                "comm_period": period_psd_ped_map[period]["comm_ped"],
                "amount": total_deferred_in_period,
                "plan_details": plan_details,
            }
            total_previous_deferred += total_deferred_in_period

        return {
            "previous_commission_deferred_details": previous_deferred_commissions_by_period,
            "total_previous_deferred": total_previous_deferred,
        }

    def get_commission_adjustments(self, commission_adjustments):
        """commission adjustments for a payee in a period"""

        commission_adjustment_details = []
        total_commission_adjustment = 0
        for adj in commission_adjustments:
            criteria_record = self.criteria_data.get(adj["criteria_id"], {})
            amount = float(adj["amount"])
            commission_adjustment_details.append(
                {
                    "plan_id": str(adj["plan_id"]) if adj["plan_id"] else None,
                    "plan_name": (
                        self.plan_data.get(adj["plan_id"])["plan_name"]
                        if adj["plan_id"]
                        else None
                    ),
                    "plan_display_order": (
                        self.plan_data.get(adj["plan_id"])["plan_display_order"]
                        if adj["plan_id"]
                        else None
                    ),
                    "criteria_id": (
                        str(adj["criteria_id"]) if adj["criteria_id"] else None
                    ),
                    "criteria_name": criteria_record.get("criteria_name"),
                    "criteria_description": criteria_record.get("criteria_description"),
                    "line_item_id": adj["line_item_id"],
                    "reason": adj["reason"],
                    "reason_category": adj["reason_category"],
                    "amount": amount,
                }
            )
            total_commission_adjustment += amount

        return {
            "total": total_commission_adjustment,
            "details": commission_adjustment_details,
        }

    def get_processed_arrears(
        self, processed_arrears, base_currency=False, current_month_fx_rate=1
    ):
        distinct_periods = set()
        for rec in processed_arrears:
            distinct_periods.add(rec["period_end_date"])
        sec_kd_data = self.get_comm_period_wise_map(distinct_periods)
        total = 0
        details = {}
        has_period_label_feature = has_feature(self.client_id, "payout_period_label")
        label_map = {}

        if has_period_label_feature:
            period_tuples = set()
            for rec in processed_arrears:
                period_tuples.add(
                    (
                        make_aware_wrapper(first_day_of_month(rec["period_end_date"])),
                        make_aware_wrapper(rec["period_end_date"]),
                    )
                )
            period_tuples = list(period_tuples)
            label_map = get_period_label_ped_map(self.client_id, period_tuples)

        for rec in processed_arrears:
            processed_amount = float(rec.get("processed_amount"))
            payee_id = rec.get("payee_id")

            if rec["arrear_adjustments"] and "fx_rate" in rec["arrear_adjustments"]:
                fx_rate = rec["arrear_adjustments"]["fx_rate"]
            else:
                comm_period = rec["period_end_date"].strftime("%Y-%m-%d")
                payee_currency = self.payroll_dict[rec["payee_id"]]["pay_currency"]
                sec_kd_date = None
                if comm_period in sec_kd_data and payee_id in sec_kd_data[comm_period]:
                    sec_kd_date = sec_kd_data[comm_period][payee_id]
                fx_rate = self.fx_rate_convertor.get_fx_rate_kd(
                    rec["period_end_date"],
                    payee_currency,
                    sec_kd_date,
                )
            processed_amount = processed_amount * float(fx_rate)
            if base_currency:
                processed_amount = self.fx_rate_convertor.change_to_base_currency(
                    processed_amount, current_month_fx_rate
                )
            processed_amount = float(processed_amount)
            total += processed_amount

            # Key is the period label
            period_label = None
            if rec.get("period_end_date"):
                date_index = rec["period_end_date"].strftime("%Y-%m-%d")
                period_label = label_map.get(date_index)
                if not period_label:
                    period_label = (
                        rec["period_end_date"].strftime("%d-%b-%Y")
                        if payee_id in self.custom_calendar_payees
                        else rec["period_end_date"].strftime("%b %Y")
                    )
            key = period_label
            if key not in details:
                details[key] = {"period": period_label, "amount": 0}

            details[key]["amount"] += processed_amount

        return {"total": total, "details": list(details.values())}

    @staticmethod
    def get_draw(
        period,
        total_payout,
        draw_record,
        draw_adj_records,
        base_currency=False,
        fx_rate_convertor=None,
        fx_rate=None,
    ):
        draw_amount_total = 0
        if draw_record:
            for draws in draw_record["draws"]:
                for each_draw in draws:
                    draw_amount = float(each_draw["draw_amount"])
                    if base_currency:
                        draw_amount = float(
                            fx_rate_convertor.change_to_base_currency(
                                draw_amount, fx_rate
                            )
                        )
                    if period.lower() == each_draw["draw_period"].lower():
                        if each_draw["draw_type_name"] in ["NRG", "RG"]:
                            if total_payout < draw_amount:
                                draw_amount_total = float(draw_amount) - float(
                                    total_payout
                                )
                            else:
                                draw_amount_total = 0
                        else:
                            draw_amount_total += float(draw_amount)
        draw_adj_total = 0
        for adj_rec in draw_adj_records:
            if period.lower() == adj_rec["period"].lower():
                adj_amount = float(adj_rec["amount"])
                if base_currency:
                    adj_amount = float(
                        fx_rate_convertor.change_to_base_currency(adj_amount, fx_rate)
                    )
                draw_adj_total += adj_amount

        total_draw_adjustments = draw_amount_total - draw_adj_total
        details = []
        if draw_amount_total or draw_adj_total:
            details.append(
                {"draw_availed": draw_amount_total, "draw_recovered": draw_adj_total}
            )
        return {
            "total": total_draw_adjustments,
            "details": details,
        }

    def get_commissions_data(self):
        commissions_data = commission_data_services.get_commission_records_payees(
            client_id=self.client_id, ped=self.ped, payee_psd_map=self.payee_psd_map
        )

        return {
            "commissions_curr_period": self.group_by_payees(
                commissions_data["curr_period"]
            ),
            "locked_kd_map": commissions_data["locked_kd_map"],
        }

    def get_settlements_data(self):
        settlement_data = get_settlement_records_payees(
            client_id=self.client_id, ped=self.ped, payee_psd_map=self.payee_psd_map
        )

        return {
            "settlement_curr_period": self.group_by_payees(
                settlement_data["curr_period"]
            ),
            "settlement_prev_period": self.group_by_payees(
                settlement_data["prev_period"]
            ),
            "locked_kd_map": settlement_data["locked_kd_map"],
        }

    def get_commission_adjustments_data(
        self, locked_kd_map, secondary_kd_map=None, base_currency=False
    ):
        commission_adjustments = get_commission_adjustments_payees(
            client_id=self.client_id,
            ped=self.ped,
            locked_kd_map=locked_kd_map,
            payees=self.payee_emails,
            base_currency=base_currency,
            secondary_kd_map=secondary_kd_map,
        )

        return self.group_by_payees(commission_adjustments, "payee_id")

    def get_processed_arrears_data(self):
        processed_arrears = PayoutArrearsAccessor(
            self.client_id
        ).get_processed_arrears_for_payout_period_payees(
            payout_ped=self.ped, payout_psd_map=self.payee_psd_map
        )
        return self.group_by_payees(processed_arrears, "payee_id")

    def get_draws_data(self, locked_kd_map):
        payee_year_map = {}
        for payee in self.payee_psd_map:
            payee_year_map[payee] = get_fiscal_year(
                self.fiscal_start, self.payee_psd_map[payee]
            )

        draws_data = get_draws_payees(self.client_id, payee_year_map, locked_kd_map)
        return {
            "draws": self.group_by_payees(draws_data["draws"], "employee_email_id"),
            "draws_adj": self.group_by_payees(
                draws_data["draw_adjustments"], "payee_id"
            ),
        }

    def get_total_paid_amount(self, base_currency=False) -> dict:
        paid_amount = {}
        for payee in self.payee_emails:
            paid_amount[payee] = {"paid": 0.0, "processed": 0.0, "ignored": 0.0}

        all_payouts = PayoutAccessor(
            self.client_id
        ).get_all_payout_transactions_for_payees_and_date(
            payee_ids=self.payee_emails, ped=self.ped
        )
        all_payouts_grouped = self.group_by_payees(all_payouts, "payee_id")

        processed_arrears = PayoutArrearsAccessor(
            self.client_id
        ).get_processed_arrears_for_payees_and_period(
            payee_ids=self.payee_emails, ped=self.ped
        )
        all_arrears_grouped = self.group_by_payees(processed_arrears, "payee_id")

        payout_transactions = {}
        distinct_periods = set()
        distinct_periods.add(self.ped)

        payee_ped_map = {}
        for payee in all_arrears_grouped:
            payee_ped_map[payee] = []
            for rec in all_arrears_grouped[payee]:
                distinct_periods.add(rec["period_end_date"])
        sec_kd_data = self.get_comm_period_wise_map(distinct_periods)
        for payee in all_payouts_grouped:
            payout_transactions[payee] = []
            for rec in all_payouts_grouped[payee]:
                if base_currency:
                    fx_rate = 1
                elif (
                    rec["payout_adjustments"] and "fx_rate" in rec["payout_adjustments"]
                ):
                    fx_rate = rec["payout_adjustments"]["fx_rate"]
                else:
                    comm_period = rec["period_end_date"].strftime("%Y-%m-%d")
                    payee_currency = self.payroll_dict[payee]["pay_currency"]
                    sec_kd_date = None
                    if comm_period in sec_kd_data and payee in sec_kd_data[comm_period]:
                        sec_kd_date = sec_kd_data[comm_period][payee]
                    fx_rate = self.fx_rate_convertor.get_fx_rate_kd(
                        rec["period_end_date"],
                        payee_currency,
                        sec_kd_date,
                    )
                payout_amount = float(rec["payout_amount"]) * float(fx_rate)
                if rec["is_paid"]:
                    paid_amount[payee]["paid"] += payout_amount
                payout_transactions[payee].append(
                    {
                        "payout_id": str(rec["payout_id"]),
                        "date": (
                            rec["payout_knowledge_date"].strftime("%Y-%m-%d")
                            if rec["payout_knowledge_date"]
                            else None
                        ),
                        "amount": str(payout_amount),
                        "comment": rec["comment"],
                        "type": "payout",
                        "is_paid": rec["is_paid"],
                        "paid_by": rec["additional_details"]["updated_by"],
                        "payee_currency_symbol": self.currency_symbol_map[
                            self.payroll_dict[payee]["pay_currency"]
                        ],
                        "payee_email_id": payee,
                    }
                )

        for payee in all_arrears_grouped:
            if payee not in payout_transactions:
                payout_transactions[payee] = []
            for rec in all_arrears_grouped[payee]:
                if base_currency:
                    fx_rate = 1
                elif (
                    rec["arrear_adjustments"] and "fx_rate" in rec["arrear_adjustments"]
                ):
                    fx_rate = rec["arrear_adjustments"]["fx_rate"]
                else:
                    comm_period = rec["period_end_date"].strftime("%Y-%m-%d")
                    payee_currency = self.payroll_dict[payee]["pay_currency"]
                    fx_rate = self.fx_rate_convertor.get_fx_rate_kd(
                        rec["period_end_date"],
                        payee_currency,
                        sec_kd_data[comm_period][payee],
                    )
                fx_rate = float(fx_rate)

                if rec["processed_amount"]:
                    amount = float(rec["processed_amount"]) * fx_rate
                    paid_amount[payee]["processed"] += float(amount)
                    comment = f"Processed on period {rec['payout_period_end_date'].strftime('%d-%m-%Y')} "
                    comment += f"by {rec['additional_details']['updated_by']}"
                else:
                    amount = float(rec["ignored_amount"]) * fx_rate
                    comment = f"Ignored by {rec['additional_details']['updated_by']}"
                    paid_amount[payee]["ignored"] += float(amount)
                payout_transactions[payee].append(
                    {
                        "date": (
                            rec["processed_at"].strftime("%Y-%m-%d")
                            if rec["processed_at"]
                            else None
                        ),
                        "amount": str(amount),
                        "arrear_month": rec["period_end_date"].strftime("%Y-%m-%d"),
                        "ignored_flag": True if rec["ignored_amount"] else False,
                        "comment": comment,
                        "type": "arrear",
                        "is_paid": True,
                        "paid_by": rec["additional_details"]["updated_by"],
                        "payee_currency_symbol": self.currency_symbol_map[
                            self.payroll_dict[payee]["pay_currency"]
                        ],
                        "payee_email_id": payee,
                    }
                )
        return {"paid_amount": paid_amount, "payout_transactions": payout_transactions}

    def get_data(
        self,
        statements_flag=False,
        base_currency=False,
    ):
        self.logger.info(
            "BEGIN: Get {} data".format("Statements" if statements_flag else "Payout")
        )
        self.get_custom_calendar_details()
        self.logger.info("Got custom calendar details")
        self.get_payee_details()
        self.logger.info("Got payee details")
        self.get_plan_data()
        self.logger.info("Got plan details")
        self.get_criteria_data()
        self.logger.info("Got criteria details")
        self.get_currency_symbol()
        self.logger.info("Got currency symbol details")
        self.get_currency_locale_map()
        self.logger.info("Got currency locale details")
        commissions_data = self.get_commissions_data()
        self.logger.info("Got commission data")
        settlements_data = self.get_settlements_data()
        self.logger.info("Got settlements data")
        commission_adjustments = self.get_commission_adjustments_data(
            commissions_data["locked_kd_map"], self.payee_sec_kd_map, base_currency
        )
        self.logger.info("Got commission adjustments data")
        draws_data = self.get_draws_data(commissions_data["locked_kd_map"])
        self.logger.info("Got draw and draw adjustments data")
        processed_arrears = self.get_processed_arrears_data()
        self.logger.info("Got processed arrears data")
        paid_amount_data = self.get_total_paid_amount(base_currency)
        self.logger.info("Got payout paid data")
        approval_status_map = self.get_approval_status_map()
        self.logger.info("Got approval status data")
        result = {}
        for payee in self.payee_emails:
            payee_name = (
                self.payee_dict[payee]["first_name"]
                + " "
                + self.payee_dict[payee]["last_name"]
                if payee in self.payee_dict
                else None
            )
            if self.payroll_dict[payee]["variable_pay"]:
                variable_pay_as_per_period = get_variable_pay_per_period(
                    self.client_id,
                    self.payroll_dict[payee]["variable_pay"],
                    self.payroll_dict[payee]["payout_frequency"],
                    self.ped,
                )
            else:
                variable_pay_as_per_period = 0
            variable_pay = (
                variable_pay_as_per_period if variable_pay_as_per_period else 0
            )
            fx_rate = None
            total_payout = 0
            payout_split_up = {
                "settlement": 0,
                "commission": 0,
                "commission_adj": 0,
                "draw_adj": 0,
                "processed_arrears_amount": 0,
            }
            payee_currency_code = self.payroll_dict[payee]["pay_currency"]
            payee_currency_symbol = self.currency_symbol_map[payee_currency_code]
            if base_currency:
                payee_currency_code = self.client_data.base_currency
                payee_currency_symbol = self.currency_symbol_map[payee_currency_code]

            # Get locale id based on payee currency code
            payee_locale = self.currency_locale_map.get(payee_currency_code, None)

            payee_details = {
                "earned_commission": 0,
                "earned_commission_details": {},
                "deferred_commission": 0,
                "deferred_commission_details": {},
                "previous_commission_deferred": 0,
                "previous_commission_deferred_details": {},
                "adjustments": 0,
                "adjustments_details": [],
                "draws": 0,
                "draws_details": [],
                "payout_arrears": 0,
                "payout_arrears_details": {},
                "total_payout": 0,
                "total_paid_amount": 0,
                "pending_amount": 0,
                "payment_status": None,
                "commission_lock_date": None,
                "settlement_lock_date": None,
                "settlement_calc_status": "Not Frozen",
                "comm_calc_status": "Not Frozen",
                "approval_status": (
                    approval_status_map[payee].get("status")
                    if approval_status_map.get(payee)
                    else None
                ),
                "approval_status_completion_time": (
                    approval_status_map[payee].get("completion_time")
                    if approval_status_map.get(payee)
                    else None
                ),
                "approval_status_requested_time": (
                    approval_status_map[payee].get("requested_time")
                    if approval_status_map.get(payee)
                    else None
                ),
                "payee_currency_symbol": payee_currency_symbol,
                "locale_id": payee_locale,
                "lastUpdatedTime": None,
                "commission_locked_by": None,
                "settlement_locked_by": None,
                "period_start_date": self.payee_psd_map[payee].strftime("%Y-%m-%d"),
                "period_end_date": self.ped.strftime("%Y-%m-%d"),
                "payee_email_id": payee,
                "payee_name": payee_name,
                "payee_currency": self.payroll_dict[payee]["pay_currency"],
                "variable_pay": str(variable_pay),
                "variable_pay_as_per_period": str(variable_pay_as_per_period),
            }

            if payee in commissions_data["locked_kd_map"]:
                commission_lock_entry = commissions_data["locked_kd_map"][payee]
                if commission_lock_entry:
                    commission_lock_date = commission_lock_entry[
                        "locked_knowledge_date"
                    ]
                    commission_locked_by = commission_lock_entry["additional_details"][
                        "updated_by"
                    ]
                    payee_details["commission_lock_date"] = (
                        commission_lock_date.strftime("%Y-%m-%d")
                    )
                    payee_details["commission_locked_by"] = commission_locked_by
                    payee_details["comm_calc_status"] = (
                        "Frozen" if commission_lock_date else "Not Frozen"
                    )
            # self.logger.info("Got commission lock details")

            if payee in settlements_data["locked_kd_map"]:
                settlement_lock_entry = settlements_data["locked_kd_map"][payee]
                if settlement_lock_entry:
                    settlement_lock_date = settlement_lock_entry[
                        "locked_knowledge_date"
                    ]
                    settlement_locked_by = settlement_lock_entry["additional_details"][
                        "updated_by"
                    ]
                    payee_details["settlement_lock_date"] = (
                        settlement_lock_date.strftime("%Y-%m-%d")
                    )
                    payee_details["settlement_locked_by"] = settlement_locked_by
                    payee_details["settlement_calc_status"] = (
                        "Frozen" if settlement_lock_date else "Not Frozen"
                    )
            # self.logger.info("Got settlement lock details")

            payee_currency = self.payroll_dict[payee]["pay_currency"]
            if self.client_data.base_currency != payee_currency:
                fx_rate = self.fx_rate_convertor.get_fx_rate_kd(
                    self.ped, payee_currency, self.payee_sec_kd_map[payee]
                )

            if payee in commissions_data["commissions_curr_period"]:
                settlement_records = (
                    settlements_data["settlement_curr_period"][payee]
                    if payee in settlements_data["settlement_curr_period"]
                    else []
                )
                if commissions_data["commissions_curr_period"][payee]:
                    payee_details["lastUpdatedTime"] = commissions_data[
                        "commissions_curr_period"
                    ][payee][0]["secondary_kd"]
                deferred_and_earned_commissions = (
                    self.get_deferred_and_earned_commissions_curr_period(
                        commissions_curr_period=commissions_data[
                            "commissions_curr_period"
                        ][payee],
                        settlements_for_deals_in_curr_period=settlement_records,
                        fx_rate=None if base_currency else fx_rate,
                    )
                )

                commission_amount = float(
                    deferred_and_earned_commissions.get("total_earned_for_a_period", 0)
                )
                deferred_amount = float(
                    deferred_and_earned_commissions.get(
                        "total_deferred_for_a_period", 0
                    )
                )
                settled_amount = float(
                    deferred_and_earned_commissions.get("total_settled_for_a_period", 0)
                )

                total_payout += commission_amount
                total_payout += deferred_amount

                if base_currency:
                    payout_split_up["commission"] = commission_amount
                    payout_split_up["settlement"] = settled_amount
                else:
                    payout_split_up["commission"] = float(
                        self.fx_rate_convertor.change_to_base_currency(
                            commission_amount, fx_rate
                        )
                    )
                    payout_split_up["settlement"] = float(
                        self.fx_rate_convertor.change_to_base_currency(
                            settled_amount, fx_rate
                        )
                    )

                payee_details["earned_commission"] = commission_amount
                payee_details["deferred_commission"] = deferred_amount
                payee_details["current_payout"] = settled_amount
                payee_details["earned_commission_details"] = (
                    deferred_and_earned_commissions.get("earned_commissions", {})
                )
                payee_details["deferred_commission_details"] = (
                    deferred_and_earned_commissions.get("deferred_commissions", {})
                )
                payee_details["current_payout_details"] = (
                    deferred_and_earned_commissions.get("curr_payout", {})
                )

            if payee in settlements_data["settlement_prev_period"]:
                currency = None
                if self.client_data.base_currency != payee_currency:
                    currency = payee_currency

                previous_deferred = self.get_previous_deferred_commissions(
                    settlements_data["settlement_prev_period"][payee],
                    payee,
                    currency,
                    fx_rate if base_currency else None,
                )
                prev_deferred_amount = float(
                    previous_deferred.get("total_previous_deferred", 0)
                )
                total_payout += prev_deferred_amount

                payee_details["previous_commission_deferred"] = prev_deferred_amount
                payee_details["previous_commission_deferred_details"] = (
                    previous_deferred.get("previous_commission_deferred_details", {})
                )

            if payee in commission_adjustments:
                adjustments = self.get_commission_adjustments(
                    commission_adjustments[payee]
                )
                adjustment_amount = float(adjustments.get("total", 0))
                total_payout += adjustment_amount
                payout_split_up["commission_adj"] = adjustment_amount
                payee_details["adjustments"] = adjustment_amount
                payee_details["adjustments_details"] = adjustments.get("details", [])

            if payee in processed_arrears:
                payout_arrears = self.get_processed_arrears(
                    processed_arrears[payee], base_currency, fx_rate
                )
                processed_amount = payout_arrears.get("total", 0)

                payout_split_up["processed_arrears_amount"] = processed_amount
                total_payout += processed_amount
                payee_details["payout_arrears"] = processed_amount
                payee_details["payout_arrears_details"] = payout_arrears.get(
                    "details", {}
                )

            if payee in draws_data["draws"]:
                adj_records = []
                if payee in draws_data["draws_adj"]:
                    adj_records = draws_data["draws_adj"][payee]
                draw_period = get_period_draws(
                    self.payee_psd_map[payee], self.ped, self.fiscal_start
                )
                draws = self.get_draw(
                    draw_period,
                    total_payout,
                    draws_data["draws"][payee][0],
                    adj_records,
                    base_currency,
                    self.fx_rate_convertor,
                    fx_rate,
                )
                draw_amount = float(draws["total"])
                total_payout += draw_amount
                payout_split_up["draw_adj"] = draw_amount
                payee_details["draws"] = draw_amount
                payee_details["draws_details"] = draws.get("details", [])

            total_paid_amount = 0.0
            paid_amount = 0.0
            processed_amount = 0.0
            ignored_amount = 0.0
            if payee in paid_amount_data["paid_amount"]:
                paid_amount = paid_amount_data["paid_amount"][payee]["paid"]
                processed_amount = paid_amount_data["paid_amount"][payee]["processed"]
                ignored_amount = paid_amount_data["paid_amount"][payee]["ignored"]
                total_paid_amount = paid_amount + processed_amount + ignored_amount

            if total_paid_amount != "-":
                pending_amount = float(total_payout) - float(total_paid_amount)
            else:
                pending_amount = total_payout

            threshold = get_payee_currency_threshold(client_id=self.client_id)
            same_currency_threshold = get_base_currency_threshold(
                client_id=self.client_id
            )
            total_payout_threshold = 0.005
            if (
                -total_payout_threshold < total_payout < total_payout_threshold
                and total_paid_amount == 0
            ):
                payment_status = "Zero Payout"
                total_payout = 0
                pending_amount = 0
            elif total_paid_amount:
                payment_status = "Partially Paid"
                difference = round(total_payout - total_paid_amount, 3)
                if (difference == 0) or (
                    (
                        self.client_data.base_currency != payee_currency
                        and -threshold <= difference <= threshold
                    )
                    or (
                        self.client_data.base_currency == payee_currency
                        and -same_currency_threshold
                        < difference
                        < same_currency_threshold
                    )
                ):
                    payment_status = "Paid"
                    pending_amount = 0
                    total_paid_amount = total_payout
                elif total_paid_amount >= total_payout:
                    payment_status = "Over Paid"
            else:
                payment_status = "Unpaid"

            payee_details["total_paid_amount"] = total_paid_amount
            payee_details["paid_amount"] = paid_amount
            payee_details["processed_amount"] = processed_amount
            payee_details["ignored_amount"] = ignored_amount
            payee_details["pending_amount"] = pending_amount
            payee_details["payment_status"] = payment_status
            payee_details["total_payout"] = total_payout
            payee_details["fx_rate"] = float(fx_rate) if fx_rate else 1
            payee_details["payout_split_up"] = payout_split_up
            payee_details["commission_percentage"] = str(
                round(float(total_payout) / float(variable_pay) * 100, 2)
                if variable_pay > 0
                else 0
            )

            if base_currency:
                base_variable_pay = self.fx_rate_convertor.change_to_base_currency(
                    variable_pay,
                    fx_rate,
                )
                payee_details["variable_pay"] = str(base_variable_pay)
                payee_details["variable_pay_as_per_period"] = str(base_variable_pay)
                payee_details["commission_percentage"] = str(
                    round(
                        float(total_payout) / float(base_variable_pay) * 100,
                        2,
                    )
                    if base_variable_pay > 0
                    else 0
                )

            if statements_flag:
                result[payee] = payee_details
            else:
                result[payee] = {
                    **payee_details,
                    "payout_transactions": (
                        paid_amount_data["payout_transactions"][payee]
                        if payee in paid_amount_data["payout_transactions"]
                        else []
                    ),
                }
        self.logger.info(
            "END: Get {} data".format("Statements" if statements_flag else "Payout")
        )
        return result

    def get_countries_and_reporting_manager_details(self):
        active_countries = {
            country.country_code: country.country_name
            for country in list(
                CountriesAccessor(self.client_id).get_all_active_countries()
            )
        }
        self.logger.info("Got active countries data")

        reporting_managers = HierarchyAccessor(self.client_id).get_employee_hierarchy(
            self.ped, self.payee_emails
        )
        self.logger.info("Got reporting managers data")

        reporting_managers_dict = collections.defaultdict(list)
        if reporting_managers:
            for x in reporting_managers:
                reporting_managers_dict[x["employee_email_id"]] = x[
                    "reporting_manager_email_id"
                ]
        return active_countries, reporting_managers_dict

    def get_payout_data(self):
        self.logger.info("BEGIN: Payout data for period-{} ".format(self.ped))
        (
            active_countries,
            reporting_managers_dict,
        ) = self.get_countries_and_reporting_manager_details()
        payout_data = self.get_data()
        self.logger.info("Got payout data")
        result = []
        for payee in self.payee_emails:
            payee_name = payout_data[payee]["payee_name"]
            variable_pay_as_per_period = payout_data[payee][
                "variable_pay_as_per_period"
            ]
            variable_pay = payout_data[payee]["variable_pay"]

            payout_frequency = (
                self.payroll_dict[payee]["payout_frequency"]
                if self.payroll_dict[payee]["payout_frequency"]
                else None
            )
            payout_frequency_id = payout_frequency
            if payee in self.custom_calendar_payees:
                payout_frequency = self.custom_calendar_details["calendar_name_map"][
                    payout_frequency
                ]
            result.append(
                {
                    "payee_name": payee_name,
                    "key": payee,
                    "period": self.payee_psd_map[payee].strftime("%-d-%b-%Y"),
                    "payee_email_id": payee,
                    "employee_id": (
                        self.payroll_dict[payee]["employee_id"]
                        if self.payroll_dict[payee]["employee_id"]
                        else None
                    ),
                    "designation": (
                        self.payroll_dict[payee]["designation"]
                        if self.payroll_dict[payee]["designation"]
                        else None
                    ),
                    "employment_country": (
                        active_countries[self.payroll_dict[payee]["employment_country"]]
                        if self.payroll_dict[payee]["employment_country"]
                        in active_countries
                        else None
                    ),
                    "primary_commission_plans": (
                        ", ".join(list(map(str, self.main_plan_details_dict[payee])))
                        if payee in self.main_plan_details_dict
                        else None
                    ),
                    "spiff_plans": (
                        ", ".join(list(map(str, self.spiff_plan_details_dict[payee])))
                        if payee in self.spiff_plan_details_dict
                        else None
                    ),
                    "payout_frequency": payout_frequency,
                    "payout_frequency_id": payout_frequency_id,
                    "variable_pay_as_per_period": str(variable_pay_as_per_period),
                    "variable_pay": str(variable_pay),
                    "joining_date": (
                        self.payroll_dict[payee]["joining_date"].strftime("%-d-%b-%Y")
                        if self.payroll_dict[payee]["joining_date"]
                        else None
                    ),
                    "reporting_manager": (
                        reporting_managers_dict[payee]
                        if payee in reporting_managers_dict
                        else None
                    ),
                    "user_type": (
                        self.payee_dict[payee]["user_role"]
                        if self.payee_dict[payee]["user_role"]
                        else None
                    ),
                    "base_pay": (
                        str(self.payroll_dict[payee]["fixed_pay"])
                        if self.payroll_dict[payee]["fixed_pay"]
                        else None
                    ),
                    "payment_status": payout_data[payee]["payment_status"],
                    "payee_currency": self.payroll_dict[payee]["pay_currency"],
                    "payee_currency_symbol": self.currency_symbol_map[
                        self.payroll_dict[payee]["pay_currency"]
                    ],
                    "period_start_date": self.payee_psd_map[payee].strftime("%Y-%m-%d"),
                    "period_end_date": self.ped.strftime("%Y-%m-%d"),
                    "total_payout": str(payout_data[payee]["total_payout"]),
                    "payout_split_up": payout_data[payee]["payout_split_up"],
                    "commission": str(payout_data[payee]["earned_commission"]),
                    "paid_amount": str(payout_data[payee]["paid_amount"]),
                    "processed_amount": str(payout_data[payee]["processed_amount"]),
                    "ignored_amount": str(payout_data[payee]["ignored_amount"]),
                    "pending_amount": str(payout_data[payee]["pending_amount"]),
                    "commission_percentage": str(
                        payout_data[payee]["commission_percentage"]
                    ),
                    "comm_calc_status": payout_data[payee]["comm_calc_status"],
                    "settlement_calc_status": payout_data[payee][
                        "settlement_calc_status"
                    ],
                    "comm_freezed_at": payout_data[payee]["commission_lock_date"],
                    "comm_freezed_by": payout_data[payee]["commission_locked_by"],
                    "settlement_freezed_at": payout_data[payee]["settlement_lock_date"],
                    "settlement_freezed_by": payout_data[payee]["settlement_locked_by"],
                    "payout_transactions": payout_data[payee]["payout_transactions"],
                    "fx_rate": payout_data[payee]["fx_rate"],
                }
            )
        self.logger.info("END: Payout data for period-{} ".format(self.ped))
        return result

    def get_statements_data(self, currency=None, export_details=False) -> dict:
        self.logger.info(
            "BEGIN: Statements data for payee - {} ".format(self.payee_emails[0])
        )
        base_currency = False
        if self.client_data.base_currency == currency:
            base_currency = True
        statements_data = self.get_data(
            statements_flag=True,
            base_currency=base_currency,
        )
        self.logger.info("Got statements data")
        settlement_rule_ids = {}
        if len(self.payee_emails) == 0:
            return {}
        if self.payee_emails[0] in self.payee_plans:
            plan_ids = self.payee_plans[self.payee_emails[0]]
            map_records = CommissionSettlementMapAccessor(
                self.client_id
            ).get_records_for_commission_plan_ids(list(plan_ids))
            settlement_rule_ids = {record.commission_plan_id for record in map_records}
        self.logger.info("Got has active settlement rules data")
        data = statements_data[self.payee_emails[0]]

        # check for has_settlement_rules_history_for_active_plans
        all_active_plan_ids_qs = PlanDetailsAllAccessor(
            self.client_id
        ).get_employee_all_plan_ids_qs(self.payee_emails[0])
        settlement_record_exists = CommissionSettlementMapAccessor(
            self.client_id
        ).check_if_any_record_exists_for_commission_plan_ids(all_active_plan_ids_qs)
        self.logger.info("Got has_settlement_rules_history_for_active_plans data")

        self.logger.info(
            "END: Statements data for payee - {} ".format(self.payee_emails[0])
        )
        return_data = {
            **data,
            "has_active_settlement_rules": True if len(settlement_rule_ids) else False,
            "has_settlement_rules_history_for_active_plans": bool(
                settlement_record_exists
            ),
        }

        payee = self.payee_emails[0]
        payout_frequency = (
            self.payroll_dict[payee]["payout_frequency"]
            if self.payroll_dict[payee]["payout_frequency"]
            else None
        )
        if payee in self.custom_calendar_payees:
            payout_frequency = self.custom_calendar_details["calendar_name_map"][
                payout_frequency
            ]
        if export_details:
            (
                active_countries,
                reporting_managers_dict,
            ) = self.get_countries_and_reporting_manager_details()
            return_data["designation"] = (
                self.payroll_dict[payee]["designation"]
                if self.payroll_dict[payee]["designation"]
                else None
            )
            return_data["payout_frequency"] = payout_frequency
            manager_name = None
            if payee in reporting_managers_dict:
                manager_name = EmployeeAccessor(self.client_id).get_employee(
                    reporting_managers_dict[payee], ["first_name", "last_name"]
                )
                manager_name = (
                    f"{manager_name['first_name']} {manager_name['last_name']}"
                )
            return_data["reporting_manager"] = manager_name
            return_data["employment_country"] = (
                active_countries[self.payroll_dict[payee]["employment_country"]]
                if self.payroll_dict[payee]["employment_country"] in active_countries
                else None
            )
            return_data["fixed_pay"] = (
                self.payroll_dict[payee]["fixed_pay"]
                if self.payroll_dict[payee]["fixed_pay"]
                else None
            )
            return_data["employee_id"] = (
                self.payroll_dict[payee]["employee_id"]
                if self.payroll_dict[payee]["employee_id"]
                else None
            )
        return return_data

    def get_payouts_view_data(self):
        self.logger.info("BEGIN: Get Payouts view data")
        self.get_currency_symbol()
        self.logger.info("Got currency symbol")
        self.get_custom_calendar_details()
        self.logger.info("Got custom calendar details")
        self.get_payee_details()
        self.logger.info("Got payee details")
        return self.get_total_paid_amount()["payout_transactions"]

    def get_approval_status_map(self):
        """
        Returns: {<payee_email_id> : <status>}

        <status> is None indicates no approval request raised for this payee_email_id yet.
        """
        self.logger.info("BEGIN: get_approval_status_map")
        email_entity_key_map = {}
        approval_status_map = {}

        for payee in self.payee_emails:
            entity_key = payee + ENTITY_KEY_DELIMETER + self.ped.strftime("%Y-%m-%d")
            email_entity_key_map[payee] = entity_key
            approval_status_map[payee] = None

        instance_records = ApprovalInstanceAccessor(
            self.client_id
        ).get_latest_instances_by_entity_keys(list(email_entity_key_map.values()))

        for record in instance_records:
            payee_email_id = record["entity_key"].split(ENTITY_KEY_DELIMETER)[0]
            approval_status_map.update(
                {
                    payee_email_id: {
                        "status": record["status"],
                        "completion_time": record["completion_time"],
                        "requested_time": record["requested_time"],
                    },
                }
            )

        self.logger.info("END: get_approval_status_map")

        return approval_status_map
