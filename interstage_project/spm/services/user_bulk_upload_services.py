"""
Validation Flow -> One Sync Request

Import Flow:
* Sync Request
    * Upload raw records (CSV) to s3.
    * Create a task with s3_url
    * return the task id

* Engine (Celery)
    * Read the CSV file from s3.
    * validate the records
    * Create user in Auth0 (or get if already created)
    * Insert user to DB
    * Send invite email
    * Upload report CSV file (success and errors) to s3.

"""

import copy
import json
import logging
import re
from collections import defaultdict
from datetime import datetime, timedelta
from io import String<PERSON>
from urllib.request import urlopen

import networkx as nx
import pydash
from django.db import transaction
from django.utils import timezone
from schema import SchemaError, Use

from async_tasks.config import AsyncTaskConfig
from async_tasks.service import AsyncTaskService
from commission_engine.accessors.client_accessor import get_client
from commission_engine.accessors.hris_integration_accessor import HrisConfigAccessor
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.utils import end_of_day, make_aware_wrapper
from commission_engine.utils.general_data import (
    IntegrationType,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
    static_frequencies,
)
from commission_engine.utils.s3_utils import S3Uploader
from interstage_project.auth_management_api import create_user_in_auth, update_user_name
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from ms_teams_everstage.services.tenant_services import (
    integrate_msteams_for_newly_added_users,
)
from spm.accessors.commission_plan_accessor import PlanPayeeAccessor
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    ForecastPlanDetailsAllAccessor,
    HierarchyAccessor,
    PlanDetailsAllAccessor,
)
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.accessors.custom_calendar_accessors import CustomPeriodsAccessor
from spm.accessors.custom_field_accessor import (
    CustomFieldDataAccessor,
    CustomFieldsAccessor,
)
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.accessors.forecast_plan_accessor import ForecastPlanPayeeAccessor
from spm.accessors.quota_acessors import EmployeeQuotaAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.bulk_uploader.base_bulk_uploader import (
    BaseBulkUploadEmailSender,
    BaseBulkUploader,
)
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.schema_validators.base_schema_validator import (
    ERROR_KEY_DELIMITER,
    BaseSchemaValidator,
)
from spm.serializers.config_serializers import (
    EmployeePayrollSerializer,
    EmployeeSerializer,
)
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.bulk_add_utils import (
    construct_employee_dict,
    construct_employee_payroll_dict,
    handle_payout_frequency_change_bulk,
    handle_payroll_esd_change_bulk,
    moidfy_bulk_custom_field_data,
    new_construct_custom_field_dict,
)
from spm.services.config_services.employee_invite_services import (
    send_email_password_invite_email,
    send_social_auth_invite_email,
)
from spm.services.config_services.slack_config_services import (
    add_default_slack_and_ms_teams_notification_for_users,
    integrate_slack_for_newly_added_users,
)
from spm.services.rbac_services import get_ui_permissions
from spm.utils import ConnectionType, ConnectionTypeText

log = logging.getLogger(__name__)

ACTION_TYPE_INSERT = "insert"
ACTION_TYPE_EDIT = "edit"
ACTION_TYPE_UPDATE = "update"
JOINING_DATE_KEY = "joining_date"


def check_for_hierarchy_cycle(raw_records):
    log.info("BEGIN: Check for Hierarchy cycle - %d records", len(raw_records))
    if len(raw_records) and "reporting_manager_email_id" in raw_records[0]:
        # constructing adj list
        manager_map = {}
        for rec in raw_records:
            manager_map[rec["employee_email_id"]] = rec["reporting_manager_email_id"]

        def dfs(node, path):
            if node in path:
                return path

            path.add(node)
            if node in manager_map:
                cyclic_path = dfs(manager_map[node], path)
                if cyclic_path is not None:
                    return cyclic_path

            path.remove(node)

        for email_id in manager_map.keys():
            cycle_detected = dfs(email_id, set())
            if cycle_detected is not None:
                log.info(
                    "END: Check for Hierarchy cycle - Cycle detected: %s",
                    cycle_detected,
                )
                return cycle_detected

    log.info("END: Check for Hierarchy cycle - No cycle detected")


class UserSchemaValidator(BaseSchemaValidator):
    """Schema Validator to validate records in User Bulk upload CSV file."""

    payroll_mandatory_fields = [
        "employment_country",
        "pay_currency",
        "payout_frequency",
        "joining_date",
        "effective_start_date",
        "payee_role",
    ]
    payroll_fields = [
        "employment_country",
        "pay_currency",
        "payout_frequency",
        "joining_date",
        "effective_start_date",
        "payee_role",
        "employee_id",
        "designation",
        "fixed_pay",
        "variable_pay",
    ]
    hierarchy_fields = [
        "manager_effective_start_date",
        "manager_effective_end_date",
        "reporting_manager_email_id",
    ]
    hierarchy_mandatory_fields = [
        "manager_effective_start_date",
        "reporting_manager_email_id",
    ]

    def validate_employee_email(self, email_id):
        email_id = email_id.lower().strip()
        pat = r"^[A-Za-z0-9._+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$"
        if not re.fullmatch(pat, email_id):
            raise SchemaError("Email is not valid.")
        if self.logged_email and email_id == self.logged_email and not self.is_own_data:
            raise SchemaError("You don't have permission to update this user.")
        if self.action_type.lower() == ACTION_TYPE_INSERT:
            if email_id in self.existing_emails:
                raise SchemaError("Email is already taken.")
            if not self.is_payroll_selected and self.is_hierarchy_selected:
                raise SchemaError("Joining date is required for hierarchy details.")
        else:
            if email_id not in self.existing_emails:
                raise SchemaError("Email does not exist.")
            if self.payee_emails and email_id not in self.payee_emails:
                raise SchemaError("You don't have permission to update this user.")
            if (
                email_id not in self.existing_payroll_emails
                and not self.is_payroll_not_selected_or_all_mandatory_given
            ):
                raise SchemaError("Mandatory payroll fields are required.")

            if (
                self.is_hierarchy_selected
                and email_id not in self.existing_payroll_emails
                and not self.joining_date_selected
            ):
                raise SchemaError("Joining date is required for hierarchy details.")
            if (
                email_id not in self.existing_hierarchy_emails
                and not self.is_hierarchy_not_selected_or_all_mandatory_given
            ):
                raise SchemaError("Mandatory hierrarchy fields are required.")

            if (
                not self.is_power_admin
                and email_id in self.existing_payroll_emails
                and self.joining_date_selected
            ):
                raise SchemaError("Joining date cannot be modified.")
        if email_id in self.seen_emails:
            raise SchemaError("Duplicate record found for Email id")

        self.seen_emails.add(email_id)
        return email_id

    @staticmethod
    def validate_name(val):
        val = val.strip()
        if not val:
            raise SchemaError("This field is required.")

        return val[0].upper() + val[1:]

    def standardize_user_role(self, value):
        if not value:
            raise SchemaError("This field is required.")
        if value in self.valid_user_roles_map:
            return True
        else:
            raise SchemaError(
                "User Role should be in {}".format(
                    ", ".join(self.valid_roles_display_names)
                )
            )

    def validate_country_code(self, val):
        val = val.strip()
        if val not in self.valid_country_codes:
            raise SchemaError("Invalid country code.")
        return True

    def validate_currency_code(self, val):
        val = val.strip()
        if val not in self.valid_currency_codes:
            raise SchemaError("Invalid currency code.")
        return True

    def validate_pay(self, val):
        if not val:
            return str(float(0.0))
        try:
            if ",," in val:
                raise SchemaError("Not a valid number.")
            if "." in val and "," in val:
                if val.rindex(",") > val.index("."):
                    raise SchemaError("Not a valid number.")
            val = val.replace(",", "")
            return str(round(float(val), 2))
        except ValueError as e:
            raise SchemaError("Not a valid number.") from e

    def validate_custom_effective_date(self, val):
        date_formats = [
            "%d-%b-%Y",
            "%d %b %Y",
            "%d/%b/%Y",
            "%d.%b.%Y",
            "%d-%m-%Y",
            "%d/%m/%Y",
            "%d %m %Y",
            "%d.%m.%Y",
            "%d-%B-%Y",
            "%d/%B/%Y",
            "%d %B %Y",
            "%d.%B.%Y",
        ]
        for date_format in date_formats:
            try:
                if self.custom_effective_date_selected:
                    if val:
                        value = val.strip()
                        date = datetime.strptime(value, date_format)
                        return date.strftime(self.date_format)
                    else:
                        raise SchemaError("Invalid date value.")
            except ValueError:
                pass

    def validate_manager_effective_dates(self, record: dict, joining_date: datetime):
        """
        Validate both given manager effective start date and end date. This also validates
        that the esd should not be lesser than exit date.
        """
        # Importing here to avoid cyclic import
        from spm.services.config_services.hierarchy_services import (
            HierarchyFactory,
            UpdateModes,
        )

        employee_email = record["employee_email_id"]

        last_rec = self.existing_latest_email_hierarchy_records_map.get(employee_email)
        prev_last_rec = self.existing_prev_to_latest_email_hierarchy_records_map.get(
            employee_email
        )

        # Here the effective start date potentially be None when overwrite is ON and only
        # effective end date has chosen.
        manager_email = record.get("reporting_manager_email_id")
        mgr_esd = record.get("manager_effective_start_date")
        mgr_eed = record.get("manager_effective_end_date")

        if isinstance(mgr_esd, str):
            mgr_esd = make_aware_wrapper(datetime.strptime(mgr_esd, self.date_format))

        if isinstance(mgr_eed, str):
            mgr_eed = end_of_day(
                make_aware_wrapper(datetime.strptime(mgr_eed, self.date_format))
            )

        if last_rec is None:
            update_mode = UpdateModes.CREATE
        elif self.action_type == ACTION_TYPE_EDIT:
            # If any of hierarchy fields are not given, overwrite the values with the existing
            # hierarchy values.
            manager_email = manager_email or last_rec.reporting_manager_email_id
            mgr_esd = mgr_esd or last_rec.effective_start_date
            # `mgr_eed` can become None in two ways:
            # 1. effective end date key is not even present in the record
            # 2. effective end date key is present in the record but value is None
            # We should overwrite with existing effective date ONLY when the
            # effective end date key is not present in the record.
            if "manager_effective_end_date" not in record:
                mgr_eed = last_rec.effective_end_date
            update_mode = UpdateModes.OVERWRITE
        else:
            manager_email = manager_email or last_rec.reporting_manager_email_id
            mgr_esd = mgr_esd or last_rec.effective_start_date
            if "manager_effective_end_date" not in record:
                mgr_eed = last_rec.effective_end_date
            update_mode = UpdateModes.UPDATE

        # By logic, mgr_esd and manager_email should not be None at this point.
        # Rasing error in worst case scenario.
        if mgr_esd is None or manager_email is None:
            raise SchemaError("Effective start or Manager email is empty.")

        updater = HierarchyFactory(
            self.client_id,
            employee_email=employee_email,
            manager_email=manager_email,
            effective_start_date=mgr_esd,
            effective_end_date=mgr_eed,
        ).get_updator(update_mode)

        is_valid_esd, date = updater.validate_esd(
            joining_date=joining_date, last_rec=last_rec, prev_last_rec=prev_last_rec
        )
        if not is_valid_esd:
            if date is not None:
                error_msg = (
                    f"manager_effective_start_date{ERROR_KEY_DELIMITER}Manager effective "
                    f"start date should be greater than {date.strftime(self.date_format)}."
                )
            else:
                error_msg = (
                    f"manager_effective_start_date{ERROR_KEY_DELIMITER}Invalid Manager "
                    "effective start date "
                )
            raise SchemaError(error_msg)

        if mgr_eed and mgr_eed < mgr_esd:
            error_msg = (
                f"manager_effective_end_date{ERROR_KEY_DELIMITER}Manager effective end date "
                "should not be lesser than effective start date."
            )
            raise SchemaError(error_msg)

        # Handling exit date
        manager_rec = self.existing_email_employee_records_map.get(manager_email)
        if (
            manager_rec
            and (exit_date := manager_rec["exit_date"])
            and (mgr_eed is None or mgr_eed > exit_date)
        ):
            error_msg = (
                f"manager_effective_start_date{ERROR_KEY_DELIMITER}Manager Effective end date "
                f"should be less than {exit_date.strftime(self.date_format)} "
                "as user is marked for exit post that date."
            )
            raise SchemaError(error_msg)

    def validate_start_dates(self, record):
        joining_date = record.get("joining_date", None)
        if (
            not joining_date
            and record["employee_email_id"]
            in self.existing_latest_email_payroll_records_map
        ):
            old_payroll = self.existing_latest_email_payroll_records_map[
                record["employee_email_id"]
            ]
            joining_date = old_payroll.get("joining_date")
        effective_start_date = record.get("effective_start_date", None)
        custom_field_effective_start_date = record.get(
            "custom_field_effective_start_date", None
        )

        if custom_field_effective_start_date and re.match(
            r"\b(\d{2}) (\w{3}) (\d{4})\b", custom_field_effective_start_date
        ):
            date_format = "%d %b %Y"
        else:
            date_format = "%d-%b-%Y"
        if effective_start_date and joining_date:
            joining_date_dt = (
                datetime.strptime(joining_date, self.date_format)
                if isinstance(joining_date, str)
                else datetime.strptime(
                    joining_date.strftime(self.date_format), self.date_format
                )
            )
            effective_start_date_dt = datetime.strptime(
                effective_start_date, self.date_format
            )
            # todo: check this
            if effective_start_date_dt < joining_date_dt:
                raise SchemaError(
                    f"effective_start_date{ERROR_KEY_DELIMITER}Effective start date should be greater than Joining Date"
                )

        if custom_field_effective_start_date:
            past_date_dt = datetime.strptime("01-JAN-2000", self.date_format)
            if joining_date:
                joining_date_dt = (
                    datetime.strptime(joining_date, self.date_format)
                    if isinstance(joining_date, str)
                    else datetime.strptime(
                        joining_date.strftime(self.date_format), self.date_format
                    )
                )
            custom_field_effective_start_date_dt = datetime.strptime(
                custom_field_effective_start_date, date_format
            )
            if joining_date and custom_field_effective_start_date_dt < joining_date_dt:
                raise SchemaError(
                    f"custom_field_effective_start_date{ERROR_KEY_DELIMITER}Custom Field Effective start date should be greater Joining Date "
                )
            if custom_field_effective_start_date_dt < past_date_dt:
                raise SchemaError(
                    f"custom_field_effective_start_date{ERROR_KEY_DELIMITER}Custom Field Effective start date should be greater than 1st Jan 2000"
                )

        if (
            self.action_type == ACTION_TYPE_UPDATE
            or self.action_type == ACTION_TYPE_EDIT
        ):
            if effective_start_date is not None:
                old_payroll = (
                    self.existing_latest_email_payroll_records_map.get(
                        record["employee_email_id"]
                    )
                    if self.action_type == ACTION_TYPE_UPDATE
                    else self.existing_prev_to_latest_email_payroll_records_map.get(
                        record["employee_email_id"]
                    )
                )
                if old_payroll:
                    new_effective_start_date = datetime.strptime(
                        effective_start_date, self.date_format
                    )
                    old_effective_start_date = old_payroll["effective_start_date"]
                    if (
                        not make_aware_wrapper(new_effective_start_date)
                        > old_effective_start_date
                    ):
                        ext_date = old_effective_start_date.strftime(self.date_format)
                        raise SchemaError(
                            f"effective_start_date{ERROR_KEY_DELIMITER}Effective start date should be greater than {ext_date}"
                        )

            if custom_field_effective_start_date is not None:
                old_custom_field_data = (
                    self.existing_latest_email_custom_field_records_map.get(
                        record["employee_email_id"]
                    )
                    if self.action_type == ACTION_TYPE_UPDATE
                    else self.existing_prev_to_latest_email_custom_field_records_map.get(
                        record["employee_email_id"]
                    )
                )
                if old_custom_field_data:
                    new_effective_start_date = datetime.strptime(
                        custom_field_effective_start_date, date_format
                    )
                    old_custom_field_effective_start_date = old_custom_field_data[
                        "effective_start_date"
                    ]
                    if (
                        not make_aware_wrapper(new_effective_start_date)
                        > old_custom_field_effective_start_date
                    ):
                        ext_date = old_custom_field_effective_start_date.strftime(
                            self.date_format
                        )
                        raise SchemaError(
                            f"custom_field_effective_start_date{ERROR_KEY_DELIMITER}Custom Field Effective start date should be greater than {ext_date}"
                        )

        # If any of the hierarchy fields are selected
        if self.is_hierarchy_selected:
            if not joining_date:
                raise SchemaError("Joining date is required for hierarchy details.")

            joining_date_dt = joining_date
            if isinstance(joining_date_dt, str):
                joining_date_dt = make_aware_wrapper(
                    datetime.strptime(joining_date, self.date_format)
                )

            self.validate_manager_effective_dates(record, joining_date_dt)

        payout_frequency = record.get("payout_frequency")

        if (
            payout_frequency
            and effective_start_date
            and payout_frequency in self.calendar_start_and_end_date_map
        ):
            if not isinstance(effective_start_date, datetime):
                effective_start_date_dt = make_aware_wrapper(
                    datetime.strptime(str(effective_start_date), self.date_format)
                )
            else:
                effective_start_date_dt = make_aware_wrapper(effective_start_date)
            calendar_start_date = self.calendar_start_and_end_date_map[
                payout_frequency
            ]["start_date"]

            calendar_end_date = self.calendar_start_and_end_date_map[payout_frequency][
                "end_date"
            ]
            if effective_start_date_dt < calendar_start_date:
                raise SchemaError(
                    f"effective_start_date{ERROR_KEY_DELIMITER}Effective start date should be greater than or equal to calendar start date {datetime.strftime(calendar_start_date, self.date_format)}"
                )
            if effective_start_date_dt > calendar_end_date:
                raise SchemaError(
                    f"effective_start_date{ERROR_KEY_DELIMITER}Effective start date should be less than or equal to calendar end date {datetime.strftime(calendar_end_date, self.date_format)}"
                )

    def validate_payout_frequency_change(self, record):
        """
        Validate whether the payout frequency could be changed considering commission
        locks, commission plans, and quota of an employee.
        """

        employee_email_id = record["employee_email_id"]
        new_payout_frequency = record.get("payout_frequency")

        eff_start_date = (
            record.get("effective_start_date")
            if record.get("effective_start_date")
            else self.existing_latest_email_payroll_records_map.get(
                employee_email_id, {}
            ).get("effective_start_date")
        )
        if (
            not eff_start_date
            or not new_payout_frequency
            or self.action_type not in (ACTION_TYPE_EDIT, ACTION_TYPE_UPDATE)
        ):
            return

        old_payout_freq = self.existing_latest_email_payroll_records_map.get(
            employee_email_id, {}
        ).get("payout_frequency")

        if old_payout_freq and (old_payout_freq != new_payout_frequency):
            commission_locks = self.commission_lock_map.get(employee_email_id, [])
            plans = self.plan_map.get(employee_email_id, [])
            forecast_plans = self.forecast_plan_map.get(employee_email_id, [])
            if isinstance(eff_start_date, str):
                new_eff_start_date = make_aware_wrapper(
                    datetime.strptime(
                        self.validate_date(eff_start_date), self.date_format
                    )
                )
            else:
                new_eff_start_date = eff_start_date

            error_type, message = handle_payout_frequency_change_bulk(
                client_id=self.client_id,
                new_effective_start_date=new_eff_start_date,
                new_payout_freq=new_payout_frequency,
                all_locks=commission_locks,
                all_plans=plans,
                all_forecast_plans=forecast_plans,
            )

            if error_type is not None:
                raise SchemaError(f"payout_frequency{ERROR_KEY_DELIMITER}{message}")

    def validate_payroll_effective_start_date(self, record):
        """
        Validate whether the effective start date of the payroll record is considering commission
        locks, commission plans
        """
        employee_email_id = record["employee_email_id"]
        is_effective_date_selected = (
            True
            if self.selected_fields and "effective_start_date" in self.selected_fields
            else False
        )
        eff_start_date = (
            record.get("effective_start_date")
            if record.get("effective_start_date")
            else None
        )
        prev_payroll = self.existing_prev_to_latest_email_payroll_records_map.get(
            employee_email_id, None
        )
        # Skip validation if payroll effective start date is not selected or action type is not overwrite
        if (
            not is_effective_date_selected
            or not eff_start_date
            or self.action_type not in (ACTION_TYPE_EDIT)
        ):
            return

        commission_locks = self.commission_lock_map.get(employee_email_id, [])
        commission_plans = self.all_plan_map.get(employee_email_id, [])
        forecast_plans = self.forecast_all_plan_map.get(employee_email_id, [])

        if isinstance(eff_start_date, str):
            new_eff_start_date = make_aware_wrapper(
                datetime.strptime(self.validate_date(eff_start_date), self.date_format)
            )
        else:
            new_eff_start_date = eff_start_date

        error_type, message = handle_payroll_esd_change_bulk(
            client_id=self.client_id,
            new_effective_start_date=new_eff_start_date,
            all_locks=commission_locks,
            all_plans=commission_plans,
            all_forecast_plans=forecast_plans,
            is_perv_payroll=True if prev_payroll else False,
        )

        if error_type is not None:
            raise SchemaError(f"effective_start_date{ERROR_KEY_DELIMITER}{message}")

    def validate_manager_email(self, record, params):
        if record.get("reporting_manager_email_id"):
            manager_email = record.get("reporting_manager_email_id").strip()
            pat = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z|0-9]{1,}\b"
            if not re.fullmatch(pat, manager_email):
                raise SchemaError("Manager email is not valid.")
            if self.payee_emails and manager_email not in self.payee_emails:
                raise SchemaError("You don't have permission to update this user.")
            if (manager_email in self.existing_emails) or (
                manager_email in self.input_email_role_map
                and self.input_email_rec_id_map.get(manager_email)
                not in params["error_record_ids"]
            ):
                return True
            raise SchemaError(
                "Manager email neither active nor accurate in input records."
            )
        return None

    def validate_joining_date(self, record):
        employee_email_id = record.get("employee_email_id", None)
        joining_date = record.get("joining_date", None)
        if employee_email_id and joining_date:
            minimum_effective_start_date = (
                self.payees_min_effective_start_date[employee_email_id]
                if employee_email_id in self.payees_min_effective_start_date
                else None
            )
            joining_date_ed = make_aware_wrapper(
                datetime.strptime(joining_date, self.date_format)
            )
            if (
                minimum_effective_start_date
                and minimum_effective_start_date < joining_date_ed
            ):
                raise SchemaError(
                    f"Joining should be less than {datetime.strftime(minimum_effective_start_date, self.date_format)}"
                )
            return True

    def validate_bulk_user_overwrite(self, record):
        if self.action_type == ACTION_TYPE_EDIT:
            employee_email_id = record.get("employee_email_id", None)
            if (
                self.is_payroll_selected
                and employee_email_id
                and employee_email_id
                not in self.existing_latest_email_payroll_records_map
            ):
                raise SchemaError(
                    "Cannot update Payroll as there is no effective dated information to overwrite"
                )
            elif (
                self.is_hierarchy_selected
                and employee_email_id
                and employee_email_id
                not in self.existing_latest_email_hierarchy_records_map
            ):
                raise SchemaError(
                    "Cannot update Reporting Manager as there is no effective dated information to overwrite."
                )

    def validate_user_source(self, record):
        """
        Validate whether the user source is hris or manual.
        if the user source is hris and if a hris configured field has an update
        then raise an error.
        """
        if (
            self.action_type == ACTION_TYPE_EDIT
            or self.action_type == ACTION_TYPE_UPDATE
        ) and self.selected_fields:
            user_source = self.existing_email_employee_records_map[
                record["employee_email_id"]
            ].get("user_source", "manual")
            if user_source == "hris":
                hris_configured_fields = HrisConfigAccessor(
                    self.client_id
                ).get_mapped_user_fields_list()
                for field in self.selected_fields:
                    if field == "employee_email_id":
                        continue
                    if (field in hris_configured_fields) or (
                        # If HRIS is configured for Reporting Hierarchy, check only for
                        # the existence of reporting manager email ID field in HRIS
                        # configured fields.
                        field in self.hierarchy_fields
                        and "reporting_manager_email_id" in hris_configured_fields
                    ):
                        raise SchemaError(
                            "Cannot update data as user is managed via integrations."
                        )

    @staticmethod
    def validate_payee_roll(val):
        # Default as No for Crystal Access
        if not val:
            return "No"
        val = val.strip()
        if val.lower() not in ["yes", "no"]:
            raise SchemaError("Crystal Access should be Yes or No")

        return "Yes" if val.lower() == "yes" else "No"

    def __init__(
        self,
        client_id,
        raw_records,
        selected_fields=None,
        action_type="insert",
        payee_emails=None,
        logged_email=None,
        is_own_data=False,
        is_power_admin=False,
    ):
        self.client_id = client_id
        self.client = get_client(client_id)
        self.action_type = action_type
        self.selected_fields = selected_fields
        self.payee_emails = payee_emails
        self.logged_email = logged_email
        self.is_own_data = is_own_data
        self.is_power_admin = is_power_admin
        # loading existing emails
        self.existing_emails = set()
        self.input_email_role_map = {}
        self.input_email_rec_id_map = {}
        self.calendar_start_and_end_date_map = {}
        self.payees_min_effective_start_date = {}

        employee_records = EmployeeAccessor(
            client_id
        ).get_all_employees_including_exit_payees()
        self.existing_email_employee_records_map = {
            record.__dict__["employee_email_id"]: record.__dict__
            for record in employee_records
        }

        payroll_records = EmployeePayrollAccessor(
            client_id
        ).get_latest_employee_payroll_list()
        self.existing_latest_email_payroll_records_map = {
            record["employee_email_id"]: record for record in payroll_records
        }

        hierarchy_records = HierarchyAccessor(
            client_id
        ).get_latest_employee_hierarchy_list()
        self.existing_latest_email_hierarchy_records_map = {
            record.employee_email_id: record for record in hierarchy_records
        }

        custom_field_data_records = CustomFieldDataAccessor(
            client_id
        ).latest_custom_field_data_with_email_list()
        self.existing_latest_email_custom_field_records_map = {
            record["email"]: record for record in custom_field_data_records
        }

        payroll_prev_to_latest_records = EmployeePayrollAccessor(
            client_id
        ).get_prev_to_latest_employee_payroll_list()
        self.existing_prev_to_latest_email_payroll_records_map = {
            record["employee_email_id"]: record
            for record in payroll_prev_to_latest_records
        }

        hierarchy_prev_to_latest_records = HierarchyAccessor(
            client_id
        ).get_prev_to_latest_employee_hierarchy_list()
        self.existing_prev_to_latest_email_hierarchy_records_map = {
            record.employee_email_id: record
            for record in hierarchy_prev_to_latest_records
        }

        custom_field_prev_to_latest_records = CustomFieldDataAccessor(
            client_id
        ).get_prev_to_latest_custom_field_data_list()

        calendar_start_and_end_dates = CustomPeriodsAccessor(
            client_id
        ).get_all_calendar_start_and_end_dates()

        if calendar_start_and_end_dates:
            self.calendar_start_and_end_date_map = {
                str(record["calendar_id"]): record
                for record in calendar_start_and_end_dates
            }

        self.existing_prev_to_latest_email_custom_field_records_map = {
            record["email"]: record for record in custom_field_prev_to_latest_records
        }

        self.existing_emails = [
            record.__dict__["employee_email_id"] for record in employee_records
        ]

        self.input_email_role_map = {
            rec["employee_email_id"]: rec.get("user_role") for rec in raw_records
        }
        self.input_email_rec_id_map = {
            rec["employee_email_id"]: rec.get("raw_record_id") for rec in raw_records
        }
        # loading payroll and hierrarchy emails
        self.existing_payroll_emails = EmployeePayrollAccessor(
            self.client_id
        ).get_all_employee_payroll_email()
        self.existing_hierarchy_emails = HierarchyAccessor(
            self.client_id
        ).get_all_employee_hierrarchy_email()

        # loading effective custom fields
        self.custom_effective_field_list = CustomFieldsAccessor(
            self.client_id
        ).fetch_all_effective_custom_fields()

        # ---------- Fetching values for validating payout frequency change -----------
        raw_record_emails = list(self.input_email_rec_id_map.keys())
        # commission_lock_map - Maps payee email id to a list of active commission locks
        commission_lock_records = CommissionLockAccessor(client_id).get_lock_by_ids(
            raw_record_emails
        )
        self.commission_lock_map = defaultdict(list)
        for lock in commission_lock_records:
            self.commission_lock_map[lock["payee_email_id"]].append(lock)

        # plan_map - Maps payee email id to a list of active main plans
        plan_records = PlanDetailsAllAccessor(client_id).get_all_employee_plans(
            raw_record_emails
        )
        self.plan_map = defaultdict(list)
        for plan in plan_records:
            self.plan_map[plan["employee_email_id"]].append(plan)

        # all_plan_records - Maps payee email id to a list of all commission plans
        all_plan_records = PlanPayeeAccessor(client_id).get_all_employee_plans(
            raw_record_emails
        )
        self.all_plan_map = defaultdict(list)
        for plan in all_plan_records:
            self.all_plan_map[plan["employee_email_id"]].append(plan)

        # forecast plan_map - Maps payee email id to a list of active main plans
        forecast_plan_records = ForecastPlanDetailsAllAccessor(
            client_id
        ).get_all_employee_plans(raw_record_emails)
        self.forecast_plan_map = defaultdict(list)
        for forecast_plan in forecast_plan_records:
            self.forecast_plan_map[forecast_plan["employee_email_id"]].append(
                forecast_plan
            )

        # forecast_all_plan_map - Maps payee email id to a list of all forecast plans
        forecast_all_plan_records = ForecastPlanPayeeAccessor(
            client_id
        ).get_all_employee_plans(raw_record_emails)
        self.forecast_all_plan_map = defaultdict(list)
        for forecast_plan in forecast_all_plan_records:
            self.forecast_all_plan_map[forecast_plan["employee_email_id"]].append(
                forecast_plan
            )

        # quota_map - Maps payee email id to a list of quotas
        quota_records = EmployeeQuotaAccessor(client_id).get_all_quota_given_email_ids(
            raw_record_emails, projection=["employee_email_id", "quota_year"]
        )
        self.quota_map = defaultdict(list)
        for quota in quota_records:
            self.quota_map[quota["employee_email_id"]].append(quota)
        # ---------- Fetched values for validating payout frequency change -----------

        # Get minimum effective start date from payroll, heirarchy and custom field data
        payees_min_start_date_records = EmployeePayrollAccessor(
            client_id
        ).get_payees_minimum_effective_start_date()

        self.payees_min_effective_start_date = {
            employee.employee_email_id: employee.minimium_start_date
            for employee in payees_min_start_date_records
        }

        self.is_hierarchy_selected = False
        self.is_payroll_selected = False
        self.is_payroll_not_selected_or_all_mandatory_given = True
        self.is_hierarchy_not_selected_or_all_mandatory_given = True
        self.joining_date_selected = True
        self.custom_effective_date_selected = False
        # check if all mandatory fields are provided
        if selected_fields:
            payroll_field_list = [
                field for field in selected_fields if field in self.payroll_fields
            ]  # filter payroll fields
            if payroll_field_list:
                self.is_payroll_selected = True
                self.is_payroll_not_selected_or_all_mandatory_given = all(
                    field in payroll_field_list
                    for field in self.payroll_mandatory_fields
                )
            hierarchy_field_list = [
                field for field in selected_fields if field in self.hierarchy_fields
            ]  # filter hierrachy fields
            if hierarchy_field_list:
                self.is_hierarchy_selected = True
                self.is_hierarchy_not_selected_or_all_mandatory_given = all(
                    field in hierarchy_field_list
                    for field in self.hierarchy_mandatory_fields
                )
            self.joining_date_selected = JOINING_DATE_KEY in selected_fields

            if self.custom_effective_field_list:
                for field in selected_fields:
                    if field in self.custom_effective_field_list:
                        self.custom_effective_date_selected = True
                        break

        self.seen_emails = set()
        self.valid_country_codes = set()
        self.valid_currency_codes = set()
        self.valid_user_roles_map = {}
        self.valid_roles_display_names = []

        user_roles = RolePermissionsAccessor(client_id).get_role_ids_and_display_names()
        for role in user_roles:
            self.valid_user_roles_map[str(role["role_permission_id"])] = role[
                "display_name"
            ]
            self.valid_roles_display_names.append(role["display_name"])

        countries = CountriesAccessor(client_id).get_all_active_countries()
        for country in countries:
            self.valid_country_codes.add(country.country_code)
            self.valid_currency_codes.add(country.currency_code)
        all_fields = {
            "employee_email_id": Use(self.validate_employee_email),
            "first_name": Use(self.validate_name),
            "last_name": Use(self.validate_name),
            "user_role": self.standardize_user_role,
            "employee_id": str,
            "joining_date": Use(self.validate_date),
            "effective_start_date": Use(self.validate_date),
            "designation": str,
            "employment_country": self.validate_country_code,
            "fixed_pay": Use(self.validate_pay),
            "variable_pay": Use(self.validate_pay),
            "pay_currency": self.validate_currency_code,
            "payee_role": Use(self.validate_payee_roll),
            "payout_frequency": Use(self.standardize_period),
            "manager_effective_start_date": Use(self.validate_date),
            "manager_effective_end_date": Use(
                self.check_mandatory(self.validate_date, is_mandatory=False)
            ),
            "custom_field_effective_start_date": Use(
                self.validate_custom_effective_date
            ),
        }

        # Custom fields validation
        active_custom_fields = CustomFieldsAccessor(
            self.client_id
        ).get_active_custom_fields_by_client()
        for field in active_custom_fields:
            # String/Text data type
            if field.field_type == "Text":
                all_fields[field.system_name] = Use(
                    self.check_mandatory(
                        self.validate_string,
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Number data type
            elif field.field_type == "Number":
                all_fields[field.system_name] = Use(
                    self.check_mandatory(
                        self.validate_number,
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Date data type
            elif field.field_type == "Date":
                all_fields[field.system_name] = Use(
                    self.check_mandatory(
                        self.validate_date,
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Checkbox data type
            elif field.field_type == "Checkbox":
                all_fields[field.system_name] = Use(
                    self.check_mandatory(
                        self.validate_bool_str,
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Dropdown data type
            elif field.field_type == "Dropdown":
                all_fields[field.system_name] = Use(
                    self.validate_drop_down(
                        options=[x["display_name"] for x in field.options],
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Email data type
            elif field.field_type == "Email":
                all_fields[field.system_name] = Use(
                    self.check_mandatory(
                        self.validate_email,
                        is_mandatory=field.is_mandatory,
                    )
                )
            # Raise error if data type is not valid
            else:
                raise SchemaError("Invalid custom field type")
        # pylint: disable=import-outside-toplevel; circular import
        from spm.services.custom_calendar_services import get_custom_calendar_map

        custom_calendar_keys = list(get_custom_calendar_map(self.client_id).keys())

        super(UserSchemaValidator, self).__init__(
            client_id=self.client_id,
            custom_calendar_list=custom_calendar_keys,
            all_fields=all_fields,
            selected_fields=selected_fields,
            cross_field_validators=[
                self.validate_start_dates,
                self.validate_payout_frequency_change,
                self.validate_payroll_effective_start_date,
                self.validate_joining_date,
                self.validate_bulk_user_overwrite,
                self.validate_user_source,
            ],
            cross_record_validators=[self.validate_manager_email],
        )


class UserBulkUploader(BaseBulkUploader):
    date_format = "%d-%b-%Y"
    employee_fields = [
        "employee_email_id",
        "first_name",
        "last_name",
        "user_role",
    ]

    payroll_fields = [
        "employee_id",
        "joining_date",
        "effective_start_date",
        "designation",
        "employment_country",
        "payee_role",
        "fixed_pay",
        "variable_pay",
        "pay_currency",
        "payout_frequency",
    ]

    def __init__(
        self,
        client_id,
        raw_records,
        selected_fields=None,
        action_type=ACTION_TYPE_INSERT,
        audit=None,
        update_progress_func=None,
        send_invite=False,
        logger=None,
        token_scopes=None,
        payee_emails=None,
        logged_email=None,
        is_own_data=False,
        is_power_admin=False,
    ):
        self.schema_validator = UserSchemaValidator(
            client_id,
            raw_records=raw_records,
            selected_fields=selected_fields,
            action_type=action_type,
            payee_emails=payee_emails,
            logged_email=logged_email,
            is_own_data=is_own_data,
            is_power_admin=is_power_admin,
        )
        self.selected_fields = selected_fields
        self.action_type = action_type
        self.audit = audit
        self.update_progress_func = update_progress_func
        self.send_invite = send_invite
        self.token_scopes = token_scopes
        self.input_emails = set(
            [str(record.get("employee_email_id", "")).lower() for record in raw_records]
        )
        self.inserted_emails = []
        # fetch all employee, payroll, hierrachy, custom_field records for input email and construct map
        employee_records = EmployeeAccessor(client_id).get_employees(
            list(self.input_emails), as_dicts=True
        )
        self.existing_latest_email_employee_records_map = {
            x["employee_email_id"]: x for x in employee_records
        }
        payroll_records = EmployeePayrollAccessor(
            client_id
        ).get_latest_employee_payroll_list(self.input_emails)
        self.existing_latest_email_payroll_records_map = {
            x["employee_email_id"]: x for x in payroll_records
        }

        hierarchy_records = HierarchyAccessor(
            client_id
        ).get_latest_employee_hierarchy_list(list(self.input_emails))
        self.existing_latest_email_hierarchy_records_map = {
            x.employee_email_id: x for x in hierarchy_records
        }
        hierarchy_prev_to_latest_records = HierarchyAccessor(
            client_id
        ).get_prev_to_latest_employee_hierarchy_list()
        self.existing_prev_to_latest_email_hierarchy_records_map = {
            record.employee_email_id: record
            for record in hierarchy_prev_to_latest_records
        }

        custom_field_data_records = CustomFieldDataAccessor(
            client_id
        ).latest_custom_field_data_with_email_list(self.input_emails)
        self.existing_latest_email_custom_field_records_map = {
            x["email"]: x for x in custom_field_data_records
        }

        self.custom_effective_field_list = CustomFieldsAccessor(
            client_id
        ).fetch_all_effective_custom_fields()

        self.existing_emails = set()
        all_existing_employees = EmployeeAccessor(
            client_id
        ).get_all_employees_including_exit_payees()
        self.existing_emails = [
            record.__dict__["employee_email_id"] for record in all_existing_employees
        ]

        self.only_joining_date_selected = False
        if selected_fields:
            payroll_field_set = set(selected_fields) & set(self.payroll_fields)
            if payroll_field_set == {JOINING_DATE_KEY} or payroll_field_set == {
                JOINING_DATE_KEY,
                "effective_start_date",
            }:
                self.only_joining_date_selected = True

        super(UserBulkUploader, self).__init__(client_id, raw_records, logger=logger)

    def reorder_raw_records(self):
        if all(
            (
                "reporting_manager_email_id" not in record
                or not record["reporting_manager_email_id"]
            )
            for record in self.raw_records
        ):
            self.logger.info("No hierarchy data is provided. Skipping reordering.")
            return

        self.logger.info(
            f"BEGIN: REORDER RECORDS. TOTAL COUNT: {len(self.raw_records)}"
        )

        # Create a directed graph to represent the hierarchy. If the reporting manager
        # information is provided, add it to the graph. Else, simply add the employee
        # email Id as a separate node.
        graph = nx.DiGraph()
        for record in self.raw_records:
            if record.get("reporting_manager_email_id"):
                graph.add_edge(
                    record["reporting_manager_email_id"], record["employee_email_id"]
                )
            else:
                graph.add_node(record["employee_email_id"])

        try:
            top_sort = nx.topological_sort(graph)
            top_sort = list(top_sort)
        except nx.NetworkXUnfeasible:
            self.logger.info("Cycle detected in given hierarchy. Skipping reordering.")
            return

        # Sort the raw records based on the topological sort ordering, such that the
        # most parent node comes first, and the most child node comes last.
        self.raw_records = sorted(
            self.raw_records,
            key=lambda x: top_sort.index(x["employee_email_id"]),
        )

        self.logger.info(f"END: REORDER RECORDS. TOTAL COUNT: {len(self.raw_records)}")

    def _get_records_for_cycle_detection(self):
        hier_records = HierarchyAccessor(self.client_id).get_all_employee_hierarchy(
            orderby_esd=True, as_dicts=True
        )
        emp_hier_map: dict[str, list[dict]] = defaultdict(list)
        for rec in hier_records:
            emp_hier_map[rec["employee_email_id"]].append(rec)  # type: ignore

        self.logger.info(f"Number of hierarchy records in DB: {len(hier_records)}")
        self.logger.info(f"Number of raw records: {len(self.raw_records)}")

        def _add_effective_dates(rec, esd, eed):
            return {**rec, "effective_start_date": esd, "effective_end_date": eed}

        # Loop through all the raw records and "mimic" the hierarchy records structure
        # after applying the given effective start and end dates. Then pass the mimicked
        # records to the cycle detection algorithm.
        for rec in self.raw_records:
            if rec.get("errors"):
                # If there are some errored records, skip the hierarchy cycle detection
                return []

            emp_records = emp_hier_map[rec["employee_email_id"]]
            manager_email = rec.get("reporting_manager_email_id")

            esd = rec.get("manager_effective_start_date") or None
            if isinstance(esd, str):
                esd = make_aware_wrapper(datetime.strptime(esd, self.date_format))

            eed = rec.get("manager_effective_end_date") or None
            if isinstance(eed, str):
                eed = make_aware_wrapper(datetime.strptime(eed, self.date_format))

            # Note that action type "update" and "insert" only changes the number of
            # records in emp_records.
            if self.action_type == ACTION_TYPE_UPDATE and esd:
                if (
                    emp_records
                    and (last_rec := emp_records[-1])
                    and last_rec["effective_end_date"] is None
                ):
                    last_rec["effective_end_date"] = end_of_day(esd - timedelta(days=1))

                emp_records.append(_add_effective_dates(rec, esd, eed))

            # In "edit" mode, the existing records are modified.
            elif self.action_type == ACTION_TYPE_EDIT and emp_records:
                last_rec = emp_records[-1]

                if (
                    esd
                    and len(emp_records) > 1
                    and (prev_last_rec := emp_records[-2])
                    and prev_last_rec["effective_end_date"]
                    and prev_last_rec["effective_end_date"] + timedelta(microseconds=1)
                    == last_rec["effective_start_date"]
                ):
                    prev_last_rec["effective_end_date"] = end_of_day(
                        esd - timedelta(days=1)
                    )

                last_rec["effective_start_date"] = (
                    esd or last_rec["effective_start_date"]
                )
                last_rec["effective_end_date"] = eed or last_rec["effective_end_date"]
                last_rec["reporting_manager_email_id"] = (
                    manager_email or last_rec["reporting_manager_email_id"]
                )

            elif self.action_type == ACTION_TYPE_INSERT and esd:
                emp_records.append(_add_effective_dates(rec, esd, eed))

        flatten_records = pydash.flatten(emp_hier_map.values())

        records = [
            {
                "employee_email_id": rec["employee_email_id"],
                "reporting_manager_email_id": rec["reporting_manager_email_id"],
                "effective_start_date": rec["effective_start_date"],
                "effective_end_date": rec.get("effective_end_date") or None,
            }
            for rec in flatten_records
            if rec.get("reporting_manager_email_id") and rec.get("effective_start_date")
        ]

        return records

    def detect_hierarchy_cycles(self):
        """
        Detect hierarchy cycles considering all the existing hierarchy entries and the
        given entries that are yet to be updated / inserted. If a cycle is detected,
        the corresponding records are marked as errors in the
        "reporting_manager_email_id" field.
        This cycle detection should be called only once during a validation cycle.
        """
        from spm.services.config_services.hierarchy_services import (
            get_hierarchy_cycles_given_records,
        )

        self.logger.info("BEGIN: DETECT HIERARCHY CYCLES")

        if not self.schema_validator.is_hierarchy_selected:  # type: ignore
            self.logger.info("Hierarchy not selected. Skipping cycle detection.")
            return

        records = self._get_records_for_cycle_detection()

        if records:
            self.logger.info(f"Number of records to check cycles: {len(records)}")
            cycles = get_hierarchy_cycles_given_records(records)
        else:
            self.logger.info("Found schema errors. Skipping cycle detection.")
            return

        cycle_emails = set(pydash.flatten(cycles))

        cycle_msg = "Hierarchy cycle detected."
        # Adding the cycle detection error to the records if its not present.
        for rec in self.raw_records:
            if rec["employee_email_id"] in cycle_emails:
                self.error_record_ids.append(rec["raw_record_id"])
                if "errors" in rec:
                    if rec["errors"].get("reporting_manager_email_id"):
                        rec["errors"]["reporting_manager_email_id"].append(cycle_msg)
                    else:
                        rec["errors"]["reporting_manager_email_id"] = [cycle_msg]
                else:
                    rec["errors"] = {"reporting_manager_email_id": [cycle_msg]}

        self.logger.info("END: DETECT HIERARCHY CYCLES")

    def _get_hier_details(self, record):
        curr_hier_details = {
            field: record[field]
            for field in [
                "reporting_manager_email_id",
                "manager_effective_start_date",
                "manager_effective_end_date",
            ]
            if field in record
        }

        existing_hier = {}
        last_rec = self.existing_latest_email_hierarchy_records_map.get(
            record["employee_email_id"]
        )
        if last_rec is not None:
            existing_hier = {
                "reporting_manager_email_id": last_rec.reporting_manager_email_id,
                "manager_effective_start_date": last_rec.effective_start_date,
                "manager_effective_end_date": last_rec.effective_end_date,
            }

        return {**existing_hier, **curr_hier_details}

    def preprocess_records(self):
        """Restructure and prune the erroneous records"""
        self.logger.info(
            f"BEGIN: PREPROCESS RECORDS. TOTAL COUNT: {len(self.raw_records)}"
        )
        for raw_rec in self.raw_records:
            if "errors" in raw_rec and raw_rec["errors"]:
                self.filtered_error_records.append(raw_rec)
                continue

            rec = dict()
            rec["raw_record_id"] = raw_rec["raw_record_id"]
            for field in self.employee_fields:
                if field in raw_rec:
                    rec[field] = raw_rec[field]

            # employee user role : UUID -> str
            if "user_role" in raw_rec:
                # converting user_role string to list of single role as a part of Multi User role support phase 1.
                rec["user_role"] = [str(raw_rec["user_role"])]

            # employee payroll details
            employee_payroll = dict()
            for field in self.payroll_fields:
                if field in raw_rec:
                    employee_payroll[field] = raw_rec[field]

            if employee_payroll:
                # handling payee_role: crystal_access
                if "payee_role" in employee_payroll:
                    employee_payroll["payee_role"] = (
                        "Revenue"
                        if employee_payroll["payee_role"].lower() == "yes"
                        else "Non-Revenue"
                    )
                rec["employee_payroll"] = employee_payroll

            # Hierarchy details populated here will be used in the `persist_records` stage
            # This will populate any missing hierarchy details in the current record.
            # For eg. if reporting_manager_email_id has not chosen in UI, but a latest
            # hierarchy entry is present in DB, then that latest entry's manager ID will be used
            # while saving the hierarchy entry.
            if self.schema_validator.is_hierarchy_selected:  # type: ignore
                hier_details = self._get_hier_details(raw_rec)
                rec = {**hier_details, **rec}

            # custom fields
            active_custom_fields = CustomFieldsAccessor(
                self.client_id
            ).get_active_custom_fields_by_client()
            custom_fields_data = dict()
            for active_field in active_custom_fields:
                if active_field.system_name in raw_rec:
                    custom_fields_data[active_field.system_name] = raw_rec[
                        active_field.system_name
                    ]

            if custom_fields_data:
                rec["custom_field"] = {
                    "data": custom_fields_data,
                    "custom_field_effective_start_date": (
                        raw_rec["custom_field_effective_start_date"]
                        if "custom_field_effective_start_date" in raw_rec
                        else None
                    ),
                    "custom_joining_date": (
                        raw_rec["joining_date"] if "joining_date" in raw_rec else None
                    ),
                }

            self.processed_records.append(rec)
        self.logger.info(
            f"END: PREPROCESS RECORDS. TOTAL COUNT: {len(self.raw_records)}"
        )

    def get_serializers_and_status(self, req):
        time = timezone.now()
        req_email = req["employee_email_id"]
        employee_data = req
        if (
            "first_name" in req or "last_name" in req
        ) and self.action_type != ACTION_TYPE_INSERT:
            old_data = self.existing_latest_email_employee_records_map.get(
                req_email, {}
            )
            employee_data = {**old_data, **req}
        employee_ser = construct_employee_dict(
            self.client_id,
            self.audit,
            EmployeeSerializer,
            employee_data,
            time,
        )

        payroll_req = req
        if "employee_payroll" in req and self.action_type != ACTION_TYPE_INSERT:
            old_payroll = self.existing_latest_email_payroll_records_map.get(
                req_email, {}
            )
            if old_payroll:
                payroll_req["employee_payroll"] = {
                    **old_payroll,
                    **payroll_req["employee_payroll"],
                }
        payroll_ser = construct_employee_payroll_dict(
            self.client_id, self.audit, EmployeePayrollSerializer, payroll_req, time
        )

        custom_field_data = req
        custom_effective_start_date = (
            custom_field_data["custom_field"]["custom_field_effective_start_date"]
            if "custom_field" in custom_field_data
            and "custom_field_effective_start_date" in custom_field_data["custom_field"]
            else None
        )
        custom_effective_start_date_dt = (
            datetime.strptime(
                re.sub(
                    r"\b(\d{2}) (\w{3}) (\d{4})\b",
                    r"\1-\2-\3",
                    custom_effective_start_date,
                ),
                "%d-%b-%Y",
            ).strftime("%d-%b-%Y")
            if custom_effective_start_date
            else None
        )
        custom_joining_date = (
            custom_field_data["custom_field"]["custom_joining_date"]
            if "custom_field" in custom_field_data
            and "custom_joining_date" in custom_field_data["custom_field"]
            else None
        )
        is_overwrite = True if self.action_type == ACTION_TYPE_EDIT else False
        if "custom_field" in req:
            if self.action_type != ACTION_TYPE_INSERT:
                old_custom_field_data = (
                    self.existing_latest_email_custom_field_records_map.get(
                        req_email, {}
                    )
                )
                if old_custom_field_data:
                    custom_field_data["custom_field"] = {
                        **old_custom_field_data["data"],
                        **custom_field_data["custom_field"],
                    }
        custom_field = new_construct_custom_field_dict(
            self.client_id,
            custom_field_data,
            custom_effective_start_date_dt,
            is_overwrite,
        )

        are_serializers_valid = (not employee_ser or employee_ser.is_valid()) and (
            not payroll_ser or payroll_ser.is_valid()
        )
        return (
            employee_ser,
            custom_field,
            payroll_ser,
            custom_effective_start_date_dt,
            custom_joining_date,
            are_serializers_valid,
        )

    def persist_hierarchy(self, record: dict, emp_details):
        # Importing here to avoid cyclic import
        from spm.services.config_services.hierarchy_services import (
            HierarchyFactory,
            UpdateModes,
        )

        employee_email = record["employee_email_id"]

        log.info("BEGIN: Persist Hierarchy for %s", employee_email)

        last_rec = self.existing_latest_email_hierarchy_records_map.get(employee_email)
        prev_last_rec = self.existing_prev_to_latest_email_hierarchy_records_map.get(
            employee_email
        )

        mgr_esd = record["manager_effective_start_date"]
        # Here the mgr_esd could be datetime object when overwrite is ON and only
        # effective end date has chosen.
        if isinstance(mgr_esd, str):
            mgr_esd = make_aware_wrapper(datetime.strptime(mgr_esd, self.date_format))

        mgr_eed = record.get("manager_effective_end_date")
        # Here the mgr_esd could be datetime object when overwrite is ON and only
        # manager / effective start date has chosen.
        if isinstance(mgr_eed, str):
            mgr_eed = make_aware_wrapper(
                end_of_day(datetime.strptime(mgr_eed, self.date_format))
            )

        if last_rec is None:
            update_mode = UpdateModes.CREATE
        elif self.action_type == ACTION_TYPE_EDIT:
            update_mode = UpdateModes.OVERWRITE
        else:
            update_mode = UpdateModes.UPDATE

        updater = HierarchyFactory(
            self.client_id,
            employee_email=employee_email,
            manager_email=record["reporting_manager_email_id"],
            effective_start_date=mgr_esd,
            effective_end_date=mgr_eed,
            audit=self.audit,
        ).get_updator(update_mode=update_mode)

        updater.save_hierarchy(last_rec=last_rec, prev_last_rec=prev_last_rec)
        updater.audit_log(emp_details=emp_details)
        updater.notify_hierarchy_change()
        updater.invalidate_dynamic_team_cache()

        log.info("END: Persist Hierarchy")

    @transaction.atomic
    def persist_records(self) -> int:
        from spm.services.config_services.employee_services import (
            modify_effective_start_date,
            persist_employee_custom_field_data,
        )

        time = timezone.now()
        total_count = len(self.processed_records)
        self.logger.info(f"PERSIST RECORDS STARTED. TOTAL COUNT: {total_count}")
        error_count = 0
        employee_slack_config = {}
        install_slack_ms_teams_for_emps = []
        installed_slack_for_employees = []
        installed_msteams_for_employees = []

        employee_ms_teams_config = {}
        # Integration config map - list of dicts with keys - client_id, employee_email_id, integration_type, config, knowledge_begin_date
        integration_config_map_list = []
        ##### Integerate slack and ms-teams for the users #####
        try:
            for req in self.processed_records:
                install_slack_ms_teams_for_emps.append(req["employee_email_id"])
            employee_slack_config = integrate_slack_for_newly_added_users(
                self.client_id, install_slack_ms_teams_for_emps
            )
            employee_ms_teams_config = integrate_msteams_for_newly_added_users(
                self.client_id, install_slack_ms_teams_for_emps
            )
        except Exception as e:
            self.logger.error(
                f"Exception - Error integrating slack/msteams for employees in bulk - {e}"
            )
        ######################################################

        for req in self.processed_records:
            req_email = req["employee_email_id"]
            if employee_slack_config and req_email in employee_slack_config.keys():
                emp_config = {}
                installed_slack_for_employees.append(req_email)
                for key, value in employee_slack_config[req_email].items():
                    emp_config[key] = value
                integration_config_map_list.append(
                    {
                        "integration_type": IntegrationType.SLACK.value,
                        "employee_email_id": req_email,
                        "client": self.client_id,
                        "knowledge_begin_date": time,
                        "config": emp_config,
                    }
                )
            if (
                employee_ms_teams_config
                and req_email in employee_ms_teams_config.keys()
            ):
                emp_config = {}
                installed_msteams_for_employees.append(req_email)
                for key, value in employee_ms_teams_config[req_email].items():
                    emp_config[key] = value
                integration_config_map_list.append(
                    {
                        "integration_type": IntegrationType.MS_TEAMS.value,
                        "employee_email_id": req_email,
                        "client": self.client_id,
                        "knowledge_begin_date": time,
                        "config": emp_config,
                    }
                )
            employee_details = self.existing_latest_email_employee_records_map.get(
                req_email, {}
            )
            (
                employee_ser,
                custom_field,
                payroll_ser,
                custom_effective_start_date,
                custom_joining_date,
                are_serializers_valid,
            ) = self.get_serializers_and_status(req)
            if are_serializers_valid:
                if employee_ser:
                    if self.action_type == ACTION_TYPE_INSERT:
                        create_user_in_auth(self.client_id, employee_ser.validated_data)
                        if (
                            req_email
                            not in self.existing_latest_email_employee_records_map
                        ):
                            EmployeeWriteAccessor(self.client_id).persist_employee(
                                employee_ser
                            )
                            if not employee_details:
                                emp = EmployeeAccessor(self.client_id).get_employee(
                                    req_email
                                )
                                employee_details = emp.__dict__
                    else:
                        update_user_name(
                            {
                                "employee_email_id": req_email,
                                "first_name": employee_ser.validated_data["first_name"],
                                "last_name": employee_ser.validated_data["last_name"],
                            }
                        )
                        EmployeeWriteAccessor(
                            self.client_id
                        ).update_employee_name_and_role(
                            email_id=req_email,
                            first_name=employee_ser.validated_data["first_name"],
                            last_name=employee_ser.validated_data["last_name"],
                            role=employee_ser.validated_data["user_role"],
                            knowledge_date=time,
                        )

                    # audit
                    if self.action_type == ACTION_TYPE_INSERT:
                        event_type_code = EVENT["CREATE_USER"]["code"]
                    elif self.action_type == ACTION_TYPE_UPDATE:
                        event_type_code = EVENT["UPDATE_USER"]["code"]
                    else:
                        event_type_code = EVENT["EDIT_USER"]["code"]

                    audit_services.log(
                        self.client_id,
                        event_type_code=event_type_code,
                        event_key=req_email,
                        summary=employee_ser.validated_data["first_name"]
                        + employee_ser.validated_data["last_name"],
                        updated_by=self.audit["updated_by"],
                        updated_at=time,
                        audit_data=copy.deepcopy(req),
                    )

                if payroll_ser:
                    payroll_acc = EmployeePayrollAccessor(self.client_id)
                    effective_end_date = make_aware_wrapper(
                        end_of_day(payroll_ser.validated_data["effective_start_date"])
                    ) - timedelta(days=1)
                    joining_date_ed = None
                    if self.only_joining_date_selected:
                        joining_date_ed = payroll_ser.validated_data["joining_date"]
                        payroll_acc.modify_only_joining_date(
                            req_email, joining_date_ed, time
                        )
                    else:
                        if (
                            self.selected_fields
                            and JOINING_DATE_KEY in self.selected_fields
                        ):
                            joining_date_ed = payroll_ser.validated_data["joining_date"]
                            payroll_acc.update_joining_date(
                                req_email, joining_date_ed, time
                            )

                        if self.action_type == ACTION_TYPE_EDIT:
                            if payroll_acc.get_latest_employee_payroll(req_email):
                                if (
                                    self.selected_fields
                                    and JOINING_DATE_KEY in self.selected_fields
                                ):
                                    payroll_acc.update_prev_to_latest_employee_payroll_end_date(
                                        req_email, effective_end_date
                                    )
                                else:
                                    payroll_acc.update_effective_end_date_for_prev_to_latest(
                                        req_email, time, effective_end_date
                                    )
                                ui_permission = get_ui_permissions(
                                    client_id=self.client_id,
                                    email_id=self.audit["updated_by"],
                                )
                                if (
                                    ui_permission
                                    and RbacPermissions.EDIT_PAYROLL.value
                                    in ui_permission
                                ):  # FOR FINADMIN
                                    payroll_acc.invalidate_latest(req_email, time)
                                    payroll_acc.persist_employee_payroll(payroll_ser)
                                else:  # Ops Admin
                                    EmployeePayrollAccessor(
                                        self.client_id
                                    ).edit_emp_payroll_without_pay(payroll_ser, time)
                        else:
                            payroll_acc.add_or_update_employee_payroll(
                                payroll_ser,
                                time,
                                effective_end_date,
                                joining_date_ed,
                            )

                        audit_data = {
                            **copy.deepcopy(req["employee_payroll"]),
                            **employee_details,
                        }
                        audit_services.log(
                            self.client_id,
                            event_type_code=EVENT["UPDATE_USER"]["code"],
                            event_key=req_email,
                            summary=audit_data["first_name"] + audit_data["last_name"],
                            updated_by=self.audit["updated_by"],
                            updated_at=time,
                            audit_data=audit_data,
                        )

                if (
                    (manager_email := req.get("reporting_manager_email_id"))
                    # if reporting manager already exists in system
                    or (manager_email in self.existing_emails)
                    # if reporting manager present in input and its inserted
                    or (
                        manager_email in self.input_emails
                        and manager_email in self.inserted_emails
                    )
                ):
                    try:
                        self.persist_hierarchy(req, employee_details)
                    except Exception:
                        log.exception("Error while persisting hierarchy", extra=req)
                        error_count += 1
                else:
                    log.info(
                        "Manager %s not found in system. Skipping create hierarchy",
                        manager_email,
                    )

                if custom_field:
                    if self.action_type == ACTION_TYPE_EDIT:
                        modify_effective_start_date(
                            self.client_id,
                            req_email,
                            custom_effective_start_date,
                            "custom",
                        )
                    custom_field = moidfy_bulk_custom_field_data(
                        self.client_id, custom_field
                    )
                    persist_employee_custom_field_data(
                        self.client_id,
                        req_email,
                        custom_field,
                        self.audit,
                        time,
                        custom_joining_date,
                    )
                    audit_data = {
                        **employee_details,
                        "Email": req_email,
                    }

                    audit_services.log(
                        self.client_id,
                        event_type_code=EVENT["UPDATE_CUSTOM-FIELD-DATA"]["code"],
                        event_key=req_email,
                        summary=audit_data["first_name"] + audit_data["last_name"],
                        updated_by=self.audit["updated_by"],
                        updated_at=time,
                        audit_data=audit_data,
                    )

                self.success_raw_ids.add(req["raw_record_id"])
                self.inserted_emails.append(req["employee_email_id"])
                if self.send_invite:
                    # send invitation email
                    admin_details = (
                        EmployeeAccessor(self.client_id)
                        .get_employee(self.audit["updated_by"])
                        .__dict__
                    )
                    if self.client.auth_connection_name != "email-password":
                        if self.client.connection_type == ConnectionType.GSUITE.value:
                            login_text = ConnectionTypeText.GSUITE.value
                        elif (
                            self.client.connection_type
                            == ConnectionType.SALESFORCE.value
                        ):
                            login_text = ConnectionTypeText.SALESFORCE.value
                        else:
                            login_text = ConnectionTypeText.OKTA.value

                        send_social_auth_invite_email(
                            client_id=self.client_id,
                            email=req_email,
                            employee_name=f"{employee_details['first_name']} {employee_details['last_name']}",
                            admin_name=f"{admin_details['first_name']} {admin_details['last_name']}",
                            login_text=login_text,
                        )
                    else:
                        send_email_password_invite_email(
                            client_id=self.client_id,
                            email=req_email,
                            employee_name=f"{employee_details['first_name']} {employee_details['last_name']}",
                            admin_name=f"{admin_details['first_name']} {admin_details['last_name']}",
                        )

            else:
                error_count += 1
                self.logger.error(
                    "Error occurred while adding user with email id {}".format(
                        req_email
                    )
                )

        # update progress in async task table
        if self.update_progress_func is not None:
            self.update_progress_func(
                {
                    "success_count": len(self.success_raw_ids),
                    "error_count": error_count,
                    "total_count": total_count,
                }
            )

        # perisist slack and ms-teams config for newly added users
        if installed_msteams_for_employees:
            IntegrationConfigAccessor().invalidate_msteams_config_for_email_ids(
                client_id=self.client_id,
                email_ids=installed_msteams_for_employees,
                end_time=time,
            )
        if installed_slack_for_employees:
            IntegrationConfigAccessor().invalidate_slack_config_for_email_ids(
                client_id=self.client_id,
                email_ids=installed_slack_for_employees,
                end_time=time,
            )
        IntegrationConfigAccessor().bulk_create_config_records(
            fields_map=integration_config_map_list
        )

        ######## Set slack and ms-teams notifcation #########
        try:
            add_default_slack_and_ms_teams_notification_for_users(
                client_id=self.client_id,
                newly_added_users=install_slack_ms_teams_for_emps,
                users_slack_map=employee_slack_config,
                users_ms_teams_map=employee_ms_teams_config,
            )
        except Exception as e:
            self.logger.error(
                f"Exception - Error handling default msteams/slack tasks for emps in bulk - {e}"
            )
        ######################################################

        # Refresh user groups

        # Importing here to avoid circular import
        from spm.tasks import invalidate_and_cache_all_user_groups

        subscription_plan = (self.client.client_features or {}).get(
            "subscription_plan", "BASIC"
        )
        misc_queue_name = get_queue_name_respect_to_task_group(
            self.client_id, subscription_plan, TaskGroupEnum.MISC.value
        )

        invalidate_and_cache_all_user_groups.si(self.client_id).set(
            queue=misc_queue_name
        ).apply_async()

        return len(self.success_raw_ids)


class UserBulkUploadEmailSender(BaseBulkUploadEmailSender):
    def __init__(
        self,
        client_id,
        file_name,
        email_id,
        selected_fields,
        processed_records,
        success_raw_ids,
        filtered_error_records,
        template_data,
    ):
        # remove errors and raw_record_id from data in file stored
        if "errors" in selected_fields:
            selected_fields.remove("errors")
        if "raw_record_id" in selected_fields:
            selected_fields.remove("raw_record_id")
        super(UserBulkUploadEmailSender, self).__init__(
            client_id,
            "user",
            file_name,
            selected_fields,
            email_id,
            "d-7b80bebb22a541ffa1444a39396b289e",
            processed_records,
            success_raw_ids,
            filtered_error_records,
            template_data,
        )


def initiate_bulk_import_users_task(
    client_id, request_data, audit, created_by, token_scopes, is_power_admin
):
    log.info("BEGIN: BULK IMPORT USER")
    raw_records_data = request_data["records"]
    file_name = request_data["file_name"]
    action_type = request_data["overwrite_value"]  # insert, update or edit
    notification_email = request_data["notification_email"]
    meta_data = request_data["meta_data"]
    json_buffer = StringIO()
    json.dump(raw_records_data, json_buffer)
    file = json_buffer.getvalue()
    file_path = f"{client_id}/user-bulk-upload/raw-records/{file_name}.json"
    s3_uploader = S3Uploader()
    is_upload_complete = s3_uploader.upload_file(file, file_path)

    if is_upload_complete:
        get_file_presigned_url(file_path)
        task_id = AsyncTaskService(
            client_id=client_id,
            created_by=created_by,
            task=AsyncTaskConfig.BULK_IMPORT_USERS,
        ).run_task(
            params={
                "client_id": client_id,
                "file_path": file_path,
                "file_name": file_name,
                "action_type": action_type,
                "notification_email": notification_email,
                "send_invite": meta_data.get("send_invite", False),
                "audit": audit,
                "token_scopes": token_scopes,
                "is_power_admin": is_power_admin,
            },
            force_run=True,
        )
        return task_id
    else:
        raise Exception("File Upload Failed")


def get_file_presigned_url(file_path):
    presigned_url = S3Uploader().generate_presigned_url(file_path)
    return presigned_url


def read_data_from_url(file_url):
    response = urlopen(file_url)
    return json.loads(response.read())


def bulk_import_users_service(
    client_id,
    file_path,
    file_name,
    action_type,
    token_scopes,
    notification_email=None,
    send_invite=False,
    audit=None,
    update_progress_func=None,
    logger=None,
    is_power_admin=False,
):
    file_url = get_file_presigned_url(file_path)
    data_records = read_data_from_url(file_url)
    client_name = get_client(client_id).name
    user_roles = RolePermissionsAccessor(client_id).get_role_ids_and_display_names()
    user_role_map = {
        str(role["role_permission_id"]): role["display_name"] for role in user_roles
    }
    if logger:
        logger.info(f"DATA RECORDS READ FROM S3. TOTAL COUNT: {len(data_records)}")
    bulk_uploader = UserBulkUploader(
        client_id=client_id,
        raw_records=data_records,
        token_scopes=token_scopes,
        selected_fields=data_records[0].keys(),
        action_type=action_type,
        audit=audit,
        update_progress_func=update_progress_func,
        send_invite=send_invite,
        logger=logger,
        is_power_admin=is_power_admin,
    )
    bulk_uploader.reorder_raw_records()
    bulk_uploader.validate_schema()
    bulk_uploader.detect_hierarchy_cycles()
    bulk_uploader.preprocess_records()
    if len(bulk_uploader.filtered_error_records) != 0:
        filtered_erros = bulk_uploader.filtered_error_records
        log.error("Failed Importing the records for users: %s", filtered_erros)
    serializer_error_emails = set()
    for rec in bulk_uploader.processed_records:
        are_serializers_valid = bulk_uploader.get_serializers_and_status(rec)[-1]
        if not are_serializers_valid:
            serializer_error_emails.add(rec["employee_email_id"])
    if serializer_error_emails:
        log.error(
            "Failed Serlialization of records during Importing for the users: %s",
            serializer_error_emails,
        )
        for rec in bulk_uploader.raw_records:
            if rec["employee_email_id"] in serializer_error_emails:
                if "errors" not in rec:
                    rec["errors"] = {}
                if not rec["errors"]:
                    rec["errors"]["_"] = ["Something went wrong, re-check the data"]
    success_count = bulk_uploader.persist_records()
    employee = EmployeeAccessor(client_id).get_employee(notification_email)
    notify_user_name = (
        employee.first_name + " " + employee.last_name if employee else "Everstage User"
    )
    template_data = {
        "name": notify_user_name,
        "count_success": len(bulk_uploader.success_raw_ids),
        "count_failure": len(bulk_uploader.filtered_error_records)
        + (len(bulk_uploader.processed_records) - len(bulk_uploader.success_raw_ids)),
        "client_name": client_name,
    }

    filtered_raw_records = [
        record
        for record in bulk_uploader.raw_records
        if record["raw_record_id"]
        in [record["raw_record_id"] for record in bulk_uploader.processed_records]
    ]

    # pylint: disable=import-outside-toplevel; circular import
    from spm.services.custom_calendar_services import get_custom_calendar_map

    custom_calendars = get_custom_calendar_map(client_id)
    for record in filtered_raw_records:
        if (
            "payout_frequency" in record
            and str(record["payout_frequency"]).lower() not in static_frequencies
            and str(record["payout_frequency"]) in custom_calendars
        ):
            record["payout_frequency"] = custom_calendars[record["payout_frequency"]]

    for record in filtered_raw_records:
        if "user_role" in record:
            record["user_role"] = user_role_map.get(record["user_role"])

    for record in bulk_uploader.filtered_error_records:
        if "user_role" in record:
            record["user_role"] = user_role_map.get(record["user_role"])

    if notification_email is not None:
        UserBulkUploadEmailSender(
            client_id=client_id,
            file_name=file_name,
            email_id=notification_email,
            selected_fields=list(data_records[0].keys()),
            processed_records=filtered_raw_records,
            success_raw_ids=bulk_uploader.success_raw_ids,
            filtered_error_records=bulk_uploader.filtered_error_records,
            template_data=template_data,
        ).send()
    else:
        log.error("The Email to Notify is None")

    event_properties = {}
    if action_type == "insert":
        event_name = SegmentEvents.IMPORT_NEW_USERS.value
        event_properties[SegmentProperties.COUNT_OF_USERS_IMPORTED.value] = (
            success_count
        )
    else:
        event_name = SegmentEvents.EDIT_EXISTING_USERS.value
        if action_type == "update":
            event_properties[SegmentProperties.OVERWRITE_VALUE.value] = False
        else:
            event_properties[SegmentProperties.OVERWRITE_VALUE.value] = True
        event_properties[SegmentProperties.COUNT_OF_USERS_UPDATED.value] = success_count

    analytics_data = {
        "user_id": audit["updated_by"],
        "event_name": event_name,
        "event_properties": event_properties,
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)
    return {"successCount": success_count}
