import copy
import io
import json
import logging
import traceback

from _datetime import timed<PERSON><PERSON>
from celery import shared_task
from django.utils import timezone
from pandas import DataFrame

import interstage_project.utils as iputils
import spm.services.commission_data_services as cds
from async_tasks.accessor import Async<PERSON><PERSON><PERSON>ccessor
from async_tasks.models import TaskStatus
from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_subscription_plan,
)
from commission_engine.accessors.skd_pkd_map_accessor import CustomObjectPkdMapAccessor
from commission_engine.utils import end_of_day, make_aware_wrapper
from commission_engine.utils.cache_utils import delete_hierarchy_cache
from commission_engine.utils.general_data import STATUS_CODE
from common.celery.celery_base_task import EverCeleryBaseTask
from common.everstage_supabase.services import get_supabase_client
from interstage_project.auth_management_api import create_user_in_auth
from interstage_project.celery import TaskGroupEnum
from interstage_project.threadlocal_log_context import set_threadlocal_context
from interstage_project.utils import (
    LogWithContext,
    get_queue_name_respect_to_task_group,
)
from spm.accessors.approval_workflow_accessors import ApprovalRequestsAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.custom_field_accessor import CustomFieldDataAccessor
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.constants.approval_workflow_constants import APPROVAL_WORKFLOW_STATUS
from spm.constants.commission_plan_constants import TEMP_PLAN_DOCS_RECORD_VALIDITY
from spm.constants.data_import_constants import TASKS
from spm.models.commission_plan_models import TempPlanDocs
from spm.serializers.config_serializers import (
    EmployeePayrollSerializer,
    EmployeeSerializer,
    HierarchySerializer,
)
from spm.serializers.custom_fields_serializer import CustomFieldDataSerializer
from spm.services import audit_services, user_group_service
from spm.services.approval_workflow_services import approval_instance_services
from spm.services.approval_workflow_services.approval_email_services import (
    send_task_action_email,
)
from spm.services.bulk_commission_adjustment_services import (
    bulk_import_adjustments_service,
)
from spm.services.config_services.bulk_add_utils import (
    construct_custom_field_dict,
    construct_employee_dict,
    construct_employee_payroll_dict,
    construct_hierarchy_dict,
    is_email_exists,
    modify_bulk_add_custom_field_data,
)
from spm.services.config_services.employee_invite_services import (
    send_email_password_invite_email,
    send_social_auth_invite_email,
)
from spm.services.config_services.remove_employee_service import (
    bulk_delete_users_service,
)
from spm.services.custom_object_services.custom_object_service import get_object_by_ids
from spm.services.data_import_services.data_import_services import (
    get_config_file,
    get_fields_to_update_in_async_table,
)
from spm.services.data_import_services.data_import_tasks import (
    ManageData,
    bulk_manage_data,
)
from spm.services.docusign_services.docusign_sync_services import (
    bulk_get_daily_changed_envelopes,
)
from spm.services.docusign_services.template_services import (
    bulk_send_envelopes,
    export_contract_status,
)
from spm.services.etl_report_automation_service import daily_etl_timings_generator
from spm.services.user_bulk_upload_services import bulk_import_users_service
from spm.utils import ConnectionType, ConnectionTypeText

module_logger = logging.getLogger(__name__)


@shared_task(base=EverCeleryBaseTask)
def process_bulk_add_task(client_id, audit, validate_resp_data, record, time):
    logger = iputils.LogWithContext({"client_id": client_id})
    set_threadlocal_context({"client_id": client_id})
    req_email = record["employee_email_id"]
    print(f"Executing user & task {req_email}")
    if (
        validate_resp_data["status"] == "Validation Passed"
        or validate_resp_data["status"] == "Validation Warning"
    ):
        # converting user_role string to list of single role as a part of Multi User role support phase 1.
        if "user_role" in record:
            record["user_role"] = [record["user_role"]]
        e_ser = construct_employee_dict(
            client_id, audit, EmployeeSerializer, record, time
        )
        p_ser = construct_employee_payroll_dict(
            client_id, audit, EmployeePayrollSerializer, record, time
        )
        h_ser = construct_hierarchy_dict(
            client_id, audit, HierarchySerializer, record, time
        )
        c_ser = construct_custom_field_dict(
            client_id,
            audit,
            CustomFieldDataSerializer,
            record,
            time,
        )
        if (
            (not e_ser or e_ser.is_valid())
            and (not p_ser or p_ser.is_valid())
            and (not h_ser or h_ser.is_valid())
            and (not c_ser or c_ser.is_valid())
        ):
            if e_ser and not is_email_exists(client_id, req_email):
                create_user_in_auth(client_id, e_ser.validated_data)
                EmployeeWriteAccessor(client_id).persist_employee(e_ser)

                ###################### audit log #####################
                event_type_code = EVENT["CREATE_USER"]["code"]
                event_key = req_email
                summary = record["first_name"] + record["last_name"]
                audit_data = copy.deepcopy(record)
                updated_by = audit["updated_by"]
                updated_at = time
                ######################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )

            if p_ser:
                effective_end_date = make_aware_wrapper(
                    end_of_day(p_ser.validated_data["effective_start_date"])
                ) - timedelta(days=1)
                EmployeePayrollAccessor(client_id).add_or_update_employee_payroll(
                    p_ser, time, effective_end_date
                )
                employee_details = (
                    EmployeeAccessor(client_id).get_employee(req_email).__dict__
                )
                ###################### audit log #####################
                event_type_code = EVENT["UPDATE_USER"]["code"]
                event_key = req_email
                audit_data = {
                    **copy.deepcopy(record["employee_payroll"]),
                    **employee_details,
                }
                summary = audit_data["first_name"] + audit_data["last_name"]
                updated_by = audit["updated_by"]
                updated_at = time
                #####################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )

            if h_ser:
                effective_end_date = make_aware_wrapper(
                    end_of_day(h_ser.validated_data["effective_start_date"])
                ) - timedelta(days=1)
                HierarchyAccessor(client_id).add_or_update_employee_hierarchy(
                    h_ser, time, effective_end_date
                )
                employee_details = (
                    EmployeeAccessor(client_id).get_employee(req_email).__dict__
                )
                ###################### audit log #####################
                event_type_code = EVENT["UPDATE_MANAGER"]["code"]
                event_key = req_email
                audit_data = {
                    **employee_details,
                    "reporting_manager_email_id": record["employee_hierarchy"][
                        "reporting_manager_email_id"
                    ],
                    "manager_effective_start_date": record["employee_hierarchy"][
                        "effective_start_date"
                    ],
                }
                summary = audit_data["first_name"] + audit_data["last_name"]
                updated_by = audit["updated_by"]
                updated_at = time
                ######################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )
            if c_ser:
                c_ser = modify_bulk_add_custom_field_data(c_ser, client_id)
                CustomFieldDataAccessor(client_id).add_or_update_custom_field_data(
                    c_ser
                )
                employee_details = (
                    EmployeeAccessor(client_id).get_employee(req_email).__dict__
                )
                ###################### audit log #####################
                event_type_code = EVENT["UPDATE_CUSTOM-FIELD-DATA"]["code"]
                event_key = req_email
                audit_data = {
                    **employee_details,
                    "Email": req_email,
                }
                summary = audit_data["first_name"] + audit_data["last_name"]
                updated_by = audit["updated_by"]
                updated_at = time
                ######################################################
                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )
            delete_hierarchy_cache(client_id)
        else:
            logger.error(
                "Error occurred while adding user with email id {}".format(req_email)
            )

    print(f"Executing user add task - Done {req_email}")
    return "Success"


@shared_task(base=EverCeleryBaseTask)
def generate_and_upload_criteria_details_csv_task(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    try:
        logger = LogWithContext({"client_id": client_id, "task_id": task_id, **params})
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)
        result = cds.generate_and_upload_criteria_details_csv(**params)
        async_task_accessor.set_done(task_id, result)
    except Exception as e:
        async_task_accessor.set_failed(task_id, str(e))


@shared_task(base=EverCeleryBaseTask)
def bulk_import_users(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    try:
        logger = LogWithContext({"client_id": client_id, "task_id": task_id})
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        def update_progress(progress: dict):
            async_task_accessor.set_result(task_id, result=progress)

        bulk_import_users_service(
            client_id=params["client_id"],
            file_path=params["file_path"],
            file_name=params["file_name"],
            action_type=params["action_type"],
            notification_email=params["notification_email"],
            send_invite=params["send_invite"],
            audit=params["audit"],
            update_progress_func=update_progress,
            logger=logger,
            token_scopes=params["token_scopes"],
            is_power_admin=params["is_power_admin"],
        )

    except Exception as e:
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(e))


@shared_task(base=EverCeleryBaseTask)
def bulk_delete_users(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    try:

        logger = LogWithContext({"client_id": client_id, "task_id": task_id})
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        def update_progress(progress: dict):
            async_task_accessor.update_task(
                task_id,
                fields={
                    "completed_at": timezone.now(),
                    "status": TaskStatus.DONE,
                    "result": progress,
                },
            )

        bulk_delete_users_service(
            client_id=params["client_id"],
            file_path=params["file_path"],
            logged_email=params["logged_email"],
            notification_email=params["notification_email"],
            audit=params["audit"],
            update_progress_func=update_progress,
            log=logger,
        )

    except Exception as e:
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(e))


@shared_task(base=EverCeleryBaseTask)
def bulk_import_adjustments(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    try:
        logger = LogWithContext({"client_id": client_id, "task_id": task_id})
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        def update_progress(progress: dict):
            async_task_accessor.set_result(task_id, result=progress)

        bulk_import_adjustments_service(
            client_id=params["client_id"],
            file_path=params["file_path"],
            file_name=params["file_name"],
            period_start_date=params["psd"],
            period_end_date=params["ped"],
            override_selected=params["override_selected"],
            selected_workflow=params["selected_workflow"],
            notification_email=params["notification_email"],
            audit=params["audit"],
            update_progress_func=update_progress,
            logger=logger,
            token_scopes=params["token_scopes"],
            is_power_admin=params["is_power_admin"],
        )

    except Exception as e:
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(e))


@shared_task(base=EverCeleryBaseTask)
def hris_approval_task(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    try:
        supabase = get_supabase_client()
    except Exception as exc:
        module_logger.exception("Cannot initialize supabase client: %s", str(exc))
    try:
        logger = LogWithContext({"client_id": client_id, "task_id": task_id})
        datasheet_id = params["datasheet_id"]
        databook_id = params["databook_id"]
        records_imported = params["records_imported"]
        action = params["action"]
        update_type = params["update_type"]
        import_all = params["import_all"]
        audit = params["audit"]
        realtime_task_id = params["realtime_task_id"]
        email_to_notify = params["email_to_notify"]
        created_by = params["created_by"]
        send_invite = params["send_invite"]
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        def update_progress_in_supabase(status: str, data: dict):
            try:
                if "completed_at" in data:
                    # date field should be string
                    data["completed_at"] = str(data["completed_at"])

                sup_data = {"data": {"task_id": task_id, **data}}
                supabase.update(sup_data).eq("task_id", realtime_task_id).execute()
            except Exception:  # pylint: disable=broad-except
                module_logger.exception(
                    "Cannot update status to %s in supabase table", status
                )

        def update_progress(
            status: str, result: dict = None, update_task_end_time: bool = False
        ):
            updated_fields = get_fields_to_update_in_async_table(
                status, result, update_task_end_time
            )
            async_task_accessor.update_task(task_id, updated_fields)
            update_progress_in_supabase(status, updated_fields)

        from commission_engine.services.hris_integration_services.hris_approval_service import (
            save_hris_approval_status,
            send_email_notification_for_hris_approval,
        )

        post_approval_status = save_hris_approval_status(
            client_id=client_id,
            datasheet_id=datasheet_id,
            databook_id=databook_id,
            records_imported=records_imported,
            action=action,
            update_type=update_type,
            import_all=import_all,
            audit=audit,
            created_by=created_by,
            send_invite=send_invite,
        )
        complete_status_message = {
            "processed_record_count": post_approval_status.get(
                "processed_records_count"
            ),
            "skipped_records_count": post_approval_status.get("skipped_records_count"),
        }
        update_progress(TaskStatus.DONE, complete_status_message, True)

        # send task complete email to the user
        try:
            if email_to_notify and action == "approved":
                module_logger.info(
                    "BEGIN: Sending email notification for hris approval: %s",
                    email_to_notify,
                )
                send_email_notification_for_hris_approval(
                    post_approval_status=post_approval_status,
                    email_to_notify=email_to_notify,
                    triggered_by=audit.get("updated_by"),
                )
                module_logger.info(
                    "END: Sending email notification for hris approval: %s",
                    email_to_notify,
                )
        except Exception as err:
            module_logger.exception(
                "Cannot send email notification for hris approval: %s", str(err)
            )

        try:
            # send invite to newly imported users
            if (
                update_type == "new_user"
                and action == "approved"
                and send_invite is True
            ):
                module_logger.info(
                    "BEGIN: Sending invite to newly imported users: %s",
                    email_to_notify,
                )
                admin_details = (
                    EmployeeAccessor(client_id)
                    .get_employee(audit["updated_by"])
                    .__dict__
                )
                client_data = get_client(client_id)
                for employee in post_approval_status.get(
                    "processed_records_with_user_fields", []
                ):
                    if client_data.auth_connection_name != "email-password":
                        if client_data.connection_type == ConnectionType.GSUITE.value:
                            login_text = ConnectionTypeText.GSUITE.value
                        elif (
                            client_data.connection_type
                            == ConnectionType.SALESFORCE.value
                        ):
                            login_text = ConnectionTypeText.SALESFORCE.value
                        else:
                            login_text = ConnectionTypeText.OKTA.value
                        send_social_auth_invite_email(
                            client_id=client_id,
                            email=employee["employee_email_id"],
                            employee_name=f"{employee['first_name']} {employee['last_name']}",
                            admin_name=f"{admin_details['first_name']} {admin_details['last_name']}",
                            login_text=login_text,
                        )
                    else:
                        send_email_password_invite_email(
                            client_id=client_id,
                            email=employee["employee_email_id"],
                            employee_name=f"{employee['first_name']} {employee['last_name']}",
                            admin_name=f"{admin_details['first_name']} {admin_details['last_name']}",
                        )
                module_logger.info(
                    "END: Sending invite to newly imported users: %s", email_to_notify
                )
        except Exception as err:
            module_logger.exception(
                "Cannot send invite to newly imported users: %s", str(err)
            )
            traceback.print_exc()
    except Exception as err:
        traceback.print_exc()
        updated_fields = get_fields_to_update_in_async_table(
            TaskStatus.FAILED, {"error": str(err)}, update_task_end_time=True
        )
        async_task_accessor.update_task(task_id, updated_fields)
        if "completed_at" in updated_fields:
            # date field should be string
            updated_fields["completed_at"] = str(updated_fields["completed_at"])
        supabase_data = {"data": {"task_id": task_id, **updated_fields}}

        try:
            supabase.update(supabase_data).eq(
                "task_id", params["realtime_task_id"]
            ).execute()
        except Exception:  # pylint: disable=broad-except
            module_logger.exception("Cannot update status to exited in supabase table")


@shared_task(base=EverCeleryBaseTask)
def bulk_manage_data_task(client_id, task_id, params):
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)

    # Even when the Initialization of supabase fails due to absence of env variable or down-time
    # The rest of the process continues except the real-time update
    try:
        supabase = get_supabase_client()
    except Exception as exc:
        module_logger.exception("Cannot initialize supabase client: %s", str(exc))

    try:
        logger = LogWithContext(
            {"client_id": client_id, "task_id": task_id, "params": params}
        )

        def update_progress_in_supabase(status: str, data: dict):
            try:
                if "completed_at" in data:
                    # date field should be string
                    data["completed_at"] = str(data["completed_at"])

                sup_data = {"data": {"task_id": task_id, **data}}
                supabase.update(sup_data).eq("task_id", params["realtime_id"]).execute()
            except Exception:  # pylint: disable=broad-except
                module_logger.exception(
                    "Cannot update status to %s in supabase table", status
                )

        def update_progress(
            status: str, result: dict = None, update_task_end_time: bool = False
        ):
            updated_fields = get_fields_to_update_in_async_table(
                status, result, update_task_end_time
            )
            async_task_accessor.update_task(task_id, updated_fields)
            update_progress_in_supabase(status, updated_fields)

        primary_kd = bulk_manage_data(
            task=params["task"],
            client_id=params["client_id"],
            csv_file_path=params["csv_file_path"],
            config_file_path=params["config_file_path"],
            audit=params["audit"],
            send_email=params["send_email"],
            email_to_notify=params["email_to_notify"],
            uploaded_file_name=params["uploaded_file_name"],
            update_progress_func=update_progress,
            logger=logger,
        )

        async_task = async_task_accessor.get_task_by_id(task_id)
        if async_task and async_task.status in (
            TaskStatus.DONE,
            TaskStatus.PARTIALLY_FAILED,
        ):
            # TODO
            # The current Primary KD updation logic does not consider cases where all records are ignored
            # This might result in cases where data is not actually stale but is reported as stale
            co_pkd_map_accessor = CustomObjectPkdMapAccessor(client_id)
            co_pkd_map_accessor.upsert_custom_object_primary_kd(
                params["custom_object_id"], primary_kd
            )

    except Exception as e:
        traceback.print_exc()
        module_logger.exception("Error in bulk_manage_data_task: %s", str(e))
        updated_fields = get_fields_to_update_in_async_table(
            TaskStatus.FAILED, {"error": str(e)}, update_task_end_time=True
        )
        async_task_accessor.update_task(task_id, updated_fields)
        if "completed_at" in updated_fields:
            # date field should be string
            updated_fields["completed_at"] = str(updated_fields["completed_at"])
        supabase_data = {"data": {"task_id": task_id, **updated_fields}}

        try:
            supabase.update(supabase_data).eq(
                "task_id", params["realtime_id"]
            ).execute()
        except Exception:  # pylint: disable=broad-except
            module_logger.exception("Cannot update status to exited in supabase table")


@shared_task(base=EverCeleryBaseTask)
def bulk_create_approval_instances(client_id, task_id, params):
    logger = LogWithContext({"client_id": client_id, "task_id": task_id})
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})

    # Even when the Initialization of supabase fails due to absence of env variable or down-time
    # The rest of the process continues except the real-time update
    is_auto_mode: bool = (
        True
        if "template_id_params_map" in params and params["template_id_params_map"]
        else False
    )
    try:
        supabase = get_supabase_client()
    except Exception as exc:
        module_logger.exception("Cannot initialize supabase client: %s", str(exc))

    try:
        logger.info("BEGIN: Bulk create approval instances task.")
        AsyncTaskAccessor(client_id).set_processing(task_id)
        try:
            supabase.update({"data": {"status": TaskStatus.PROCESSING}}).eq(
                "task_id", params["realtime_id"]
            ).execute()

        except Exception:  # pylint: disable=broad-except
            module_logger.exception(
                "Cannot update status to processing in supabase table"
            )

        if is_auto_mode:
            # create approval instances for each template id
            for template_id, instance_params in params[
                "template_id_params_map"
            ].items():
                approval_instance_services.create_approval_instance_and_stages(
                    client_id=params["client_id"],
                    template_id=template_id,
                    entity_type=params["entity_type"],
                    instance_params=instance_params,
                    created_by=params["created_by"],
                    audit_data=params["audit_data"],
                    template_data=params["template_data"],
                    logger=logger,
                    is_bulk_mode=True,
                )
        else:
            approval_instance_services.create_approval_instance_and_stages(
                client_id=params["client_id"],
                template_id=params["template_id"],
                entity_type=params["entity_type"],
                instance_params=params["instance_params"],
                created_by=params["created_by"],
                audit_data=params["audit_data"],
                template_data=params["template_data"],
                logger=logger,
                is_bulk_mode=True,
            )
        logger.info("END: Bulk create approval instances task")
        AsyncTaskAccessor(client_id).set_done(task_id, {"result": "success"})
        try:
            supabase.update({"data": {"status": TaskStatus.DONE}}).eq(
                "task_id", params["realtime_id"]
            ).execute()

        except Exception:  # pylint: disable=broad-except
            module_logger.exception("Cannot update status to done in supabase table")

    except Exception as e:
        traceback.print_exc()
        logger.error("Exception while bulk creating approval instances.")
        AsyncTaskAccessor(client_id).set_failed(task_id, str(e))
        try:
            supabase.update({"data": {"status": TaskStatus.FAILED}}).eq(
                "task_id", params["realtime_id"]
            ).execute()

        except Exception:  # pylint: disable=broad-except
            module_logger.exception("Cannot update status to exited in supabase table")

        # Send Email Report for failed
        send_task_action_email(
            client_id,
            params["created_by"],
            email_type="bulk_create_instance_failure",
            logger=logger,
            is_payout_action=True,
        )


@shared_task(base=EverCeleryBaseTask)
def bulk_approve_request(client_id, task_id, params):
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    logger = LogWithContext({"client_id": client_id, "task_id": task_id})
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    try:
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        approval_instance_services.approve_request(
            client_id=params["client_id"],
            all_request_ids=params["all_request_ids"],
            audit=params["audit"],
            logger=logger,
        )
        approved_requests = ApprovalRequestsAccessor(client_id).get_bulk_request_by_ids(
            request_ids=params["all_request_ids"],
            status=APPROVAL_WORKFLOW_STATUS.APPROVED.value,
            projection=["approval_request_id"],
        )
        approved_requests_ids = [
            str(ar["approval_request_id"]) for ar in approved_requests
        ]
        result = {
            "total_count": len(params["all_request_ids"]),
            "success_count": len(approved_requests_ids),
            "error_count": len(params["all_request_ids"]) - len(approved_requests_ids),
            "error_requests": list(
                set(params["all_request_ids"]) - set(approved_requests_ids)
            ),
        }

        async_task_accessor.set_result(task_id, result={"request_ids": result})
    except Exception as e:
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(e))
        send_task_action_email(
            client_id,
            params["created_by"],
            email_type="bulk_approve_failure",
            logger=logger,
        )


@shared_task(base=EverCeleryBaseTask)
def bulk_reject_request(client_id, task_id, params):
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    logger = LogWithContext({"client_id": client_id, "task_id": task_id})
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    try:
        params["logger"] = logger
        async_task_accessor.set_processing(task_id)

        approval_instance_services.reject_request(
            client_id=params["client_id"],
            request_data=params["request_data"],
            audit=params["audit"],
            logger=logger,
        )
        all_request_ids = params["request_data"]["request_id"]
        rejected_requests = ApprovalRequestsAccessor(client_id).get_bulk_request_by_ids(
            request_ids=all_request_ids,
            status=APPROVAL_WORKFLOW_STATUS.REJECTED.value,
            projection=["approval_request_id"],
        )
        rejected_requests_ids = [
            str(ar["approval_request_id"]) for ar in rejected_requests
        ]
        result = {
            "total_count": len(all_request_ids),
            "success_count": len(rejected_requests_ids),
            "error_count": len(all_request_ids) - len(rejected_requests_ids),
            "error_requests": list(set(all_request_ids) - set(rejected_requests_ids)),
        }

        async_task_accessor.set_result(task_id, result={"request_ids": result})
    except Exception as e:
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(e))
        send_task_action_email(
            client_id,
            params["created_by"],
            email_type="bulk_reject_failure",
            logger=logger,
        )


@shared_task(base=EverCeleryBaseTask)
def invalidate_and_cache_all_user_groups(client_id):
    set_threadlocal_context({"client_id": client_id})
    user_group_service.invalidate_and_cache_all_user_groups(client_id)


@shared_task(base=EverCeleryBaseTask)
def remove_rule_in_user_groups_by_fields(client_id, field):
    set_threadlocal_context({"client_id": client_id})
    return user_group_service.remove_rule_in_user_groups_by_fields(client_id, field)


@shared_task(base=EverCeleryBaseTask)
def user_group_members_sync(client_id):
    set_threadlocal_context({"client_id": client_id})
    return user_group_service.user_group_members_sync(client_id)


@shared_task(base=EverCeleryBaseTask)
def user_group_members_base_task(client_id):
    set_threadlocal_context({"client_id": client_id})
    subscription_plan = get_client_subscription_plan(client_id)
    user_group_members_queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.USERGROUPMEMBERS.value
    )
    user_group_members_sync.si(client_id).set(
        queue=user_group_members_queue_name
    ).apply_async()


@shared_task(base=EverCeleryBaseTask)
def docusign_documents_sync_base_task(client_id):
    set_threadlocal_context({"client_id": client_id})
    subscription_plan = get_client_subscription_plan(client_id)
    docusign_sync_queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DOCUSIGNDOCUMENTS.value
    )
    bulk_get_daily_changed_envelopes.si(client_id).set(
        queue=docusign_sync_queue_name
    ).apply_async()


@shared_task(base=EverCeleryBaseTask)
def bulk_send_envelopes_task(client_id, task_id, params):
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    logger = LogWithContext({"client_id": client_id, "task_id": task_id})
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})
    logger.info(f"BEGIN: Task for Bulk Send Envelopes with task_id : {task_id}")
    try:
        async_task_accessor.set_processing(task_id)

        response = bulk_send_envelopes(
            client_id=params["client_id"],
            audit=params["audit"],
            login_email_id=params["login_email_id"],
            template_id=params["template_id"],
            template_name=params["template_name"],
            raw_envelope_data=params["raw_envelope_data"],
            logger=logger,
        )

        if response["status"] == "SUCCESS":
            async_task_accessor.set_done(task_id, result=response["data"])
        else:
            async_task_accessor.set_failed(task_id, exception=response["data"])

        logger.info(
            f"END: Task completed for Bulk Send Envelopes with task_id : {task_id}"
        )
    except Exception as exc:
        logger.error(
            f"Task for Bulk Send Envelopes with task_id : {task_id} failed with error - {str(exc)}"
        )
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(exc))


@shared_task(base=EverCeleryBaseTask)
def update_daily_etl_timings_task(
    source_db_uri_env_name, dest_client_id, dest_custom_obj_id
):
    daily_etl_timings_generator(
        source_db_uri_env_name=source_db_uri_env_name,
        destination_client_id=dest_client_id,
        destination_custom_obj_id=dest_custom_obj_id,
    )


@shared_task(base=EverCeleryBaseTask)
def cleanup_temp_plan_docs_task():
    module_logger.info("BEGIN: cleanup_temp_plan_docs_task")
    knowledge_date = timezone.now()
    min_knowledge_begin_date = knowledge_date - timedelta(
        seconds=TEMP_PLAN_DOCS_RECORD_VALIDITY
    )
    qs = TempPlanDocs.objects.filter(
        knowledge_end_date__isnull=True,
        knowledge_begin_date__lt=min_knowledge_begin_date,
    )
    if not qs.exists():
        module_logger.info("No records to cleanup")
    module_logger.info("Total records to cleanup: %s", qs.count())
    qs2 = qs.filter(doc__isnull=False)
    for temp_plan_doc in qs2:
        if temp_plan_doc.doc:
            temp_plan_doc.doc.delete()
    module_logger.info("Deleted %s files", qs2.count())
    res = qs.update(knowledge_end_date=knowledge_date, is_deleted=True)
    module_logger.info("Invalidated %s records", res)
    module_logger.info("END: cleanup_temp_plan_docs_task")


@shared_task(base=EverCeleryBaseTask)
def contracts_status_export(client_id, task_id, params):
    async_task_accessor = AsyncTaskAccessor(client_id=client_id)
    logger = LogWithContext({"client_id": client_id, "task_id": task_id})
    set_threadlocal_context({"client_id": client_id, "task_id": task_id})

    logger.info(f"BEGIN: Task for Send Contracts status with task_id : {task_id}")
    try:
        async_task_accessor.set_processing(task_id)
        mail_response = export_contract_status(
            client_id,
            email_id=params["login_email_id"],
            to_email_id=params["to_email_id"],
            fiscal_year=params["fiscal_year"],
            is_archived=params["is_archived"],
            logger=logger,
        )
        if mail_response == STATUS_CODE.SUCCESS:
            async_task_accessor.set_done(task_id, result={"value": "SUCCESS"})
        else:
            async_task_accessor.set_failed(task_id, exception={"value": "FAILED"})

        async_task_accessor.set_completed_at(task_id, timezone.now())
        logger.info(
            f"END: Task completed for Send Contracts status with task_id : {task_id}"
        )
    except Exception as exc:
        logger.error(
            f"Task for Send Contracts Status with task_id : {task_id} failed with error - {str(exc)}"
        )
        traceback.print_exc()
        async_task_accessor.set_failed(task_id, str(exc))


@shared_task(base=EverCeleryBaseTask)
def upload_data_to_custom_object(
    client_id, report_df: DataFrame, custom_object_id: int, upload_type: str
) -> None:
    """
    Given a DataFrame & custom object id upload the dataframe to the custom object

    Args:
        client_id: int
        report_df: DataFrame
        custom_object_id: int
        upload_type: str
    """

    if report_df.empty:
        module_logger.warning("No data to upload")
        return
    module_logger.info(f"Uploading {len(report_df)} records to custom object")

    # Upload data to custom object
    csv_file_obj = io.BytesIO()
    report_df.to_csv(csv_file_obj)  # write to BytesIO buffer
    csv_file_obj.seek(0)
    custom_object = get_object_by_ids(
        client_id=client_id,
        custom_object_ids=custom_object_id,
        as_dicts=True,
    )
    custom_obj_config = get_config_file(
        client_id,
        custom_object_id,
        custom_object[0]["name"],
        custom_object[0]["csv_header_map"],
    )
    task_config = json.loads(str(custom_obj_config))

    manage_data = ManageData(
        task=upload_type,
        client_id=client_id,
        custom_object_id=custom_object_id,
        custom_object_name=custom_object[0]["name"],
        task_config=task_config,
        audit=None,
        uploaded_file_name="uploaded_file_name",
        csv_object=csv_file_obj,
        logger=module_logger,
    )

    if upload_type == TASKS.UPSERT.value:
        _, primary_kd = manage_data.upsert()
    elif upload_type == TASKS.DELETE.value:
        _, primary_kd = manage_data.delete()
    elif upload_type == TASKS.UPDATE.value:
        _, primary_kd = manage_data.update()
    elif upload_type == TASKS.CREATE.value:
        _, primary_kd = manage_data.create()

    co_pkd_map_accessor = CustomObjectPkdMapAccessor(client_id)
    co_pkd_map_accessor.upsert_custom_object_primary_kd(custom_object_id, primary_kd)
    return {
        "client_id": client_id,
        "custom_object_id": custom_object_id,
        "primary_kd": primary_kd,
    }
