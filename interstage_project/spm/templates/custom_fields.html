<!-- This file contains the html code for custom fields data -->
{% load custom_filters %}
<div class="flex justify-center items-center flex-col pt-2 px-1">
  {% if custom_fields %}
  <div
    class="w-11/12 bg-gray1 rounded-md grid grid-cols-3 p-4 text-small gap-x-20 gap-y-2"
  >
    {% for key,value in custom_fields.items %}
    <!--  -->
    {% if forloop.counter|divisibleby:3 %}
    <div class="flex flex-row gap-2 justify-end">
      <div class="w-5/12 break-words">{{key}}</div>
      <div class="w-5/12 break-words text-black1 font-semibold">
        {% with value|handle_empty_value as filtered_value %}
        <!--  -->
        {{ filtered_value}} {% endwith %}
      </div>
    </div>
    {% else %}
    <div class="flex flex-row gap-2">
      <div class="w-5/12 break-words">{{key}}</div>
      <div class="break-words text-black1 font-semibold">
        {% with value|handle_empty_value as filtered_value %}
        <!--  -->
        {{filtered_value}} {% endwith %}
      </div>
    </div>
    {% endif %}
    <!--  -->
    {% endfor %}
  </div>
  <div class="w-11/12 flex justify-between py-4">
    <div class="text-small text-gray2 text-center">
      Period: {{payee_details.period_start_date}} -
      {{payee_details.period_end_date}}
    </div>
    <div class="border-l border-gray-300 h-4"></div>
    <div class="text-small text-gray2 text-center">
      Last Calculated: {{payee_details.last_calculated}}
    </div>
    <div class="border-l border-gray-300 h-4"></div>
    <div class="text-small text-gray2 text-center">
      Downloaded: {{payee_details.downloaded}}
    </div>
  </div>
  {% endif %}
</div>
