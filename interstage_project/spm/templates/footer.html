<!-- This file would be read as a string in statement_export_service -->
<html>
  <head>
    <style>
      footer {
        font-size: 8px;
        font-family: "Inter !important";
        padding: 16px 28px 0px 28px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        -webkit-print-color-adjust: exact; /* This is needed to display background color in header */
      }
      .footer-logo {
        width: 80px; /* Adjust the width as needed */
        height: 14px;
      }
      .logo-overview {
        display: flex;
        align-items: center;
        gap: 6px;
      }
      .footer-text {
        font-weight: 400;
      }
      .zoom-text {
        font-weight: 500;
        color: #2e90fa;
      }
      .zoom {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-left: -40px;
      }
      .zoom-logo {
        width: 10px;
        height: 10px;
      }
      .page {
        color: #666C7A;
        font-weight: 500;
      }
      .powered{
        min-width: 100px;
      }
    </style>
  </head>
  <body>
    <footer>
      <div class="powered">
      {% if is_custom_pdf %}
        <div class="logo-overview">
          <div class="footer-text">Powered by</div>
          <img class="footer-logo" src="{{everstage_logo}}" />
        </div>
        {% endif %}
      </div>
      <div class="zoom">
        <img class="zoom-logo" src="{{perspective_icon}}" />
        <p class="zoom-text">Zoom in/out for better readability</p>
      </div>
      <div class="page">
        Page <span class="pageNumber"></span> of
        <span class="totalPages"></span>
      </div>
    </footer>
  </body>
</html>
