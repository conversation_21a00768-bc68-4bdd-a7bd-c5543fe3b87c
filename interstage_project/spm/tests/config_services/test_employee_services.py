# pylint: disable=protected-access

import datetime
import decimal
import json
import unittest
from datetime import timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import UUID

import arrow
import pydash
import pytest
from django.http import HttpRequest
from django.utils import timezone
from rest_framework.request import Request

from commission_engine.accessors.client_accessor import (
    get_client,
    toggle_client_notification,
)
from commission_engine.accessors.schedule_accessor import NotificationTaskAccessor
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
)
from commission_engine.utils import STATUS_CODE
from commission_engine.utils.date_utils import (
    first_day_of_month,
    last_day_of_month,
    start_of_day,
)
from interstage_project.utils import LogWithContext
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.models import CommissionPlan
from spm.models.commission_plan_models import PlanPayee
from spm.models.config_models.employee_models import (
    Employee,
    EmployeePayroll,
    Hierarchy,
    PlanDetails,
)
from spm.models.custom_field_models import CustomFieldData, CustomFields
from spm.serializers.config_serializers import (
    EmployeePayrollSerializer,
    EmployeeSerializer,
    HierarchySerializer,
    PlanDetailsSerializer,
)
from spm.services.config_services import (
    employee_services,
    employee_services_utils,
    hierarchy_services,
)
from spm.services.config_services.slack_config_services import get_employee_in_slack
from spm.services.custom_field_services import (
    resolve_custom_field_data_for_user_export,
    resolve_custom_field_data_for_user_filters,
)
from spm.services.exit_user_services import (
    _validate_payees_reporting_to_exiting_manager,
)
from spm.tests import models
from spm.tests.logger import LoggerUnit

cal_id_1 = UUID("b8b0423e-6e1d-47f6-b921-aa78c4240804")
cal_id_2 = UUID("79781140-5e36-48e2-9e31-8ab123704b1d")


def generate_employee_data_for_exit_users(i):
    return {
        "first_name": "User",
        "last_name": chr(i + 65),
        "user_source": None,
        "user_role": (
            ["9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"]
            if i <= 8
            else ["0490f3ce-49df-4189-b6d1-10726f6382b5"]
        ),
        "status": "Active" if i <= 3 else "Added",
        "employee_email_id": f"user{chr(i + 97)}@mail.com",
        "exit_date": (
            None
            if i < 6
            else (
                timezone.make_aware(datetime.datetime(2023, 2, 1))
                if i < 8
                else timezone.make_aware(datetime.datetime(2022, 5, 1))
            )
        ),
        "last_commission_date": (
            None
            if i < 6
            else (
                timezone.make_aware(datetime.datetime(2023, 2, 1))
                if i < 8
                else timezone.make_aware(datetime.datetime(2022, 5, 1))
            )
        ),
    }


def generate_employee_data(i):
    return {
        "first_name": "User",
        "last_name": chr(i + 65),
        "user_source": None,
        "user_role": (
            ["9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"]
            if i <= 8
            else ["0490f3ce-49df-4189-b6d1-10726f6382b5"]
        ),
        "status": "Active" if i <= 3 else "Added",
        "employee_email_id": f"user{chr(i + 97)}@mail.com",
        "created_date": timezone.now(),
        "exit_date": (
            None
            if i < 6
            else (
                timezone.make_aware(datetime.datetime(2023, 2, 1))
                if i < 8
                else timezone.make_aware(datetime.datetime(2022, 5, 1))
            )
        ),
    }


def generate_employee_data_for_managers(i):
    return {
        "first_name": "Boss",
        "last_name": f"User{chr(i + 65)}",
        "user_role": ["376f8e40-9506-4725-abb3-e1f6bee0406d"],
        "user_source": None,
        "status": None,
        "created_date": timezone.now(),
        "employee_email_id": f"bossuser{chr(i + 97)}@mail.com",
    }


def generate_employee_payroll_data(i):
    return {
        "designation": "Intern" if i <= 3 else "SDE",
        "employment_country": "IND" if i <= 5 else "USA",
        "payout_frequency": "Monthly" if i <= 7 else "Quarterly",
        "pay_currency": "INR" if i <= 3 else "USD",
        "variable_pay": 10000 * (i + 1),
        "fixed_pay": i + 1,
        "joining_date": (
            timezone.make_aware(datetime.datetime(2022, 5, 1))
            if i <= 7
            else timezone.make_aware(datetime.datetime(2022, 11, 1))
        ),
    }


def generate_hierarchy_data(i):
    return {
        "reporting_manager_email_id": (
            f"bossuser{chr(i + 97)}@mail.com" if i < 5 else "<EMAIL>"
        ),
        "manager_full_name": f"Boss User{chr(i + 65)}" if i < 5 else "User A",
    }


def generate_custom_field_data(i):
    return {
        "cf_1_dropdown_field": None if i < 2 else ("option1" if i < 8 else "option2"),
        "cf_1_date_field": (
            None
            if i < 2
            else (
                timezone.make_aware(datetime.datetime(2022, 5, 1))
                if i < 5
                else timezone.make_aware(datetime.datetime(2022, 11, 1))
            ).strftime("%Y-%m-%d")
        ),
        "cf_1_checkbox_field": True if i < 6 else False,
        "cf_1_text_field": "Hi {}".format(i),
        "cf_1_number_field": i,
    }


def generate_plan_details(i, effective_date=None):
    plan_details = []
    if i > 4:
        plan_details.append(
            {
                "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85670"),
                "plan_name": "Main Plan 1",
                "plan_type": "MAIN",
                "effective_start_date": timezone.make_aware(
                    datetime.datetime(2022, 1, 1)
                ),
                "effective_end_date": timezone.make_aware(
                    datetime.datetime(2022, 6, 30)
                ),
            }
        )
    if i > 7:
        plan_details.append(
            {
                "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85671"),
                "plan_name": "Main Plan 2",
                "plan_type": "MAIN",
                "effective_start_date": timezone.make_aware(
                    datetime.datetime(2022, 7, 1)
                ),
                "effective_end_date": timezone.make_aware(
                    last_day_of_month(
                        datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                    )
                ),
            }
        )
    if i > 6:
        plan_details.append(
            {
                "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85672"),
                "plan_name": "SPIFF Plan 1",
                "plan_type": "SPIFF",
                "effective_start_date": timezone.make_aware(
                    datetime.datetime(2022, 1, 1)
                ),
                "effective_end_date": timezone.make_aware(
                    last_day_of_month(
                        datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                    )
                ),
            }
        )
    if i > 8:
        plan_details.append(
            {
                "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85673"),
                "plan_name": "SPIFF Plan 2",
                "plan_type": "SPIFF",
                "effective_start_date": timezone.make_aware(
                    datetime.datetime(2022, 1, 1)
                ),
                "effective_end_date": timezone.make_aware(
                    last_day_of_month(
                        datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                    )
                ),
            }
        )
    if effective_date:
        plan_details = pydash.filter_(
            plan_details,
            lambda plan_detail: plan_detail["effective_start_date"] <= effective_date
            and plan_detail["effective_end_date"] >= effective_date,
        )
    return plan_details


def create_data_to_get_current_and_exit_users():
    client = get_client(1)
    knowledge_date = timezone.now()
    employees_data = [
        {
            "employee": generate_employee_data_for_exit_users(i),
            "employee_payroll_details": generate_employee_payroll_data(i),
            "plan_details": generate_plan_details(i),
        }
        for i in range(10)
    ]

    for employee_data in employees_data:
        Employee.objects.create(
            employee_email_id=employee_data["employee"]["employee_email_id"],
            first_name=employee_data["employee"]["first_name"],
            last_name=employee_data["employee"]["last_name"],
            user_role=employee_data["employee"]["user_role"],
            status=employee_data["employee"]["status"],
            created_date=knowledge_date,
            client=client,
            knowledge_begin_date=knowledge_date,
            exit_date=employee_data["employee"].get("exit_date"),
            last_commission_date=employee_data["employee"].get("last_commission_date"),
        )
        joining_date = (
            employee_data["employee_payroll_details"]["joining_date"]
            if employee_data.get("employee_payroll_details")
            else timezone.now()
        )
        if employee_data.get("employee_payroll_details"):
            EmployeePayroll.objects.create(
                employee_email_id=employee_data["employee"]["employee_email_id"],
                joining_date=joining_date,
                designation=employee_data["employee_payroll_details"]["designation"],
                employment_country=employee_data["employee_payroll_details"][
                    "employment_country"
                ],
                variable_pay=employee_data["employee_payroll_details"]["variable_pay"],
                fixed_pay=employee_data["employee_payroll_details"]["fixed_pay"],
                pay_currency=employee_data["employee_payroll_details"]["pay_currency"],
                payout_frequency=employee_data["employee_payroll_details"][
                    "payout_frequency"
                ],
                effective_start_date=joining_date,
                payee_role="Revenue",
                client=client,
                knowledge_begin_date=knowledge_date,
            )
        if employee_data.get("plan_details"):
            for plan_detail in employee_data["plan_details"]:
                PlanDetails.objects.create(
                    employee_email_id=employee_data["employee"]["employee_email_id"],
                    plan_id=plan_detail["plan_id"],
                    plan_type=plan_detail["plan_type"],
                    effective_start_date=plan_detail["effective_start_date"],
                    effective_end_date=plan_detail["effective_end_date"],
                    knowledge_begin_date=knowledge_date,
                    client=client,
                )


def create_data_for_get_filtered_users():
    client = get_client(1)
    knowledge_date = timezone.now()
    data_type_map = get_data_types()
    custom_fields = [
        {
            "display_name": "DropdownField",
            "system_name": "cf_1_dropdown_field",
            "field_type": "Dropdown",
            "is_effective_dated": False,
            "data_type": data_type_map["STRINGARRAY"],
            "options": [
                {
                    "system_name": "option1",
                    "display_name": "Option1",
                    "display_order": 1,
                },
                {
                    "system_name": "option2",
                    "display_name": "Option2",
                    "display_order": 2,
                },
            ],
        },
        {
            "display_name": "DateField",
            "system_name": "cf_1_date_field",
            "field_type": "Date",
            "is_effective_dated": False,
            "data_type": data_type_map["DATE"],
        },
        {
            "display_name": "CheckboxField",
            "system_name": "cf_1_checkbox_field",
            "field_type": "Checkbox",
            "is_effective_dated": False,
            "data_type": data_type_map["BOOLEAN"],
        },
        {
            "display_name": "TextField",
            "system_name": "cf_1_text_field",
            "field_type": "Text",
            "is_effective_dated": False,
            "data_type": data_type_map["STRING"],
        },
        {
            "display_name": "NumericField",
            "system_name": "cf_1_number_field",
            "field_type": "Number",
            "is_effective_dated": False,
            "data_type": data_type_map["INTEGER"],
        },
    ]
    for i, custom_field in enumerate(custom_fields):
        CustomFields.objects.create(
            display_name=custom_field["display_name"],
            system_name=custom_field["system_name"],
            field_type=custom_field["field_type"],
            options=custom_field.get("options", {}),
            display_order=i,
            data_type_id=custom_field["data_type"],
            knowledge_begin_date=knowledge_date,
            client=client,
        )
    commission_plans = [
        {
            "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85670"),
            "plan_name": "Main Plan 1",
            "plan_type": "MAIN",
            "plan_start_date": timezone.make_aware(datetime.datetime(2022, 1, 1)),
            "plan_end_date": timezone.make_aware(datetime.datetime(2022, 6, 30)),
            "created_by": "<EMAIL>",
            "created_on": knowledge_date,
        },
        {
            "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85671"),
            "plan_name": "Main Plan 2",
            "plan_type": "MAIN",
            "plan_start_date": timezone.make_aware(datetime.datetime(2022, 7, 1)),
            "plan_end_date": timezone.make_aware(
                last_day_of_month(
                    datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                )
            ),
            "created_by": "<EMAIL>",
            "created_on": knowledge_date,
        },
        {
            "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85672"),
            "plan_name": "SPIFF Plan 1",
            "plan_type": "SPIFF",
            "plan_start_date": timezone.make_aware(datetime.datetime(2022, 1, 1)),
            "plan_end_date": timezone.make_aware(
                last_day_of_month(
                    datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                )
            ),
            "created_by": "<EMAIL>",
            "created_on": knowledge_date,
        },
        {
            "plan_id": UUID("fd466fc2-2306-4735-90b6-755a5ad85673"),
            "plan_name": "SPIFF Plan 2",
            "plan_type": "SPIFF",
            "plan_start_date": timezone.make_aware(datetime.datetime(2022, 1, 1)),
            "plan_end_date": timezone.make_aware(
                last_day_of_month(
                    datetime.datetime(datetime.datetime.now().year + 1, 12, 31)
                )
            ),
            "created_by": "<EMAIL>",
            "created_on": knowledge_date,
        },
    ]
    for i, commission_plan in enumerate(commission_plans):
        CommissionPlan.objects.create(
            **commission_plan,
            is_draft=False,
            knowledge_begin_date=knowledge_date,
            client=client,
        )
    managers_data = [
        {
            "employee": generate_employee_data_for_managers(i),
        }
        for i in range(5)
    ]
    for manager_data in managers_data:
        Employee.objects.create(
            employee_email_id=manager_data["employee"]["employee_email_id"],
            first_name=manager_data["employee"]["first_name"],
            last_name=manager_data["employee"]["last_name"],
            user_role=manager_data["employee"]["user_role"],
            user_source=manager_data["employee"]["user_source"],
            status=manager_data["employee"]["status"],
            created_date=knowledge_date,
            client=client,
            knowledge_begin_date=knowledge_date,
        )
    employees_data = [
        {
            "employee": generate_employee_data(i),
            "employee_payroll_details": generate_employee_payroll_data(i),
            "hierarchy": generate_hierarchy_data(i),
            "custom_field_data": generate_custom_field_data(i),
            "plan_details": generate_plan_details(i),
        }
        for i in range(10)
    ]
    for employee_data in employees_data:
        Employee.objects.create(
            employee_email_id=employee_data["employee"]["employee_email_id"],
            first_name=employee_data["employee"]["first_name"],
            last_name=employee_data["employee"]["last_name"],
            user_role=employee_data["employee"]["user_role"],
            user_source=employee_data["employee"]["user_source"],
            status=employee_data["employee"]["status"],
            created_date=knowledge_date,
            client=client,
            knowledge_begin_date=knowledge_date,
            exit_date=employee_data["employee"].get("exit_date"),
        )
        joining_date = (
            employee_data["employee_payroll_details"]["joining_date"]
            if employee_data.get("employee_payroll_details")
            else timezone.now()
        )
        if employee_data.get("employee_payroll_details"):
            EmployeePayroll.objects.create(
                employee_email_id=employee_data["employee"]["employee_email_id"],
                joining_date=joining_date,
                designation=employee_data["employee_payroll_details"]["designation"],
                employment_country=employee_data["employee_payroll_details"][
                    "employment_country"
                ],
                variable_pay=employee_data["employee_payroll_details"]["variable_pay"],
                fixed_pay=employee_data["employee_payroll_details"]["fixed_pay"],
                pay_currency=employee_data["employee_payroll_details"]["pay_currency"],
                payout_frequency=employee_data["employee_payroll_details"][
                    "payout_frequency"
                ],
                effective_start_date=joining_date,
                payee_role="Revenue",
                client=client,
                knowledge_begin_date=knowledge_date,
            )
        if employee_data.get("hierarchy"):
            Hierarchy.objects.create(
                employee_email_id=employee_data["employee"]["employee_email_id"],
                reporting_manager_email_id=employee_data["hierarchy"][
                    "reporting_manager_email_id"
                ],
                effective_start_date=joining_date,
                client=client,
                knowledge_begin_date=knowledge_date,
            )
        if employee_data.get("custom_field_data"):
            CustomFieldData.objects.create(
                email=employee_data["employee"]["employee_email_id"],
                data=employee_data["custom_field_data"],
                knowledge_begin_date=knowledge_date,
                client=client,
                effective_start_date=joining_date,
            )
        if employee_data.get("plan_details"):
            for plan_detail in employee_data["plan_details"]:
                PlanDetails.objects.create(
                    employee_email_id=employee_data["employee"]["employee_email_id"],
                    plan_id=plan_detail["plan_id"],
                    plan_type=plan_detail["plan_type"],
                    effective_start_date=plan_detail["effective_start_date"],
                    effective_end_date=plan_detail["effective_end_date"],
                    knowledge_begin_date=knowledge_date,
                    client=client,
                )

        models.create_custom_calendar_table()
        models.create_custom_periods_table()


def generate_result_for_get_filtered_users(
    i, is_manager=False, is_export=False, effective_date=None, is_payouts=False
):
    effective_date = timezone.now() if effective_date is None else effective_date

    status_col_text = "status"
    if is_payouts:
        status_col_text = "user_status"
    employee_data = (
        generate_employee_data(i)
        if not is_manager
        else generate_employee_data_for_managers(i)
    )
    employee_payroll_data = (
        generate_employee_payroll_data(i) if not is_manager else None
    )
    hierarchy_data = generate_hierarchy_data(i) if not is_manager else None
    custom_field_data = (
        json.dumps(generate_custom_field_data(i)) if not is_manager else None
    )
    plan_details = (
        generate_plan_details(i, effective_date=effective_date)
        if not is_manager
        else None
    )
    main_plan_details = (
        pydash.filter_(plan_details, lambda item: item["plan_type"] == "MAIN")
        if plan_details
        else None
    )
    spiff_plan_details = (
        pydash.filter_(plan_details, lambda item: item["plan_type"] == "SPIFF")
        if plan_details
        else None
    )
    full_name = f"{employee_data['first_name']} {employee_data['last_name']}"
    variable_pay = (
        round(decimal.Decimal(employee_payroll_data["variable_pay"]), 2)
        if employee_payroll_data
        else None
    )
    base_pay = (
        round(decimal.Decimal(employee_payroll_data["fixed_pay"]), 2)
        if employee_payroll_data
        else None
    )
    status = employee_services.get_user_status(
        employee_data["status"],
        employee_data.get("exit_date"),
        employee_data.get("deactivation_date"),
        effective_date=effective_date,
    )
    role_name_map = {
        "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5": "Payee",
        "0490f3ce-49df-4189-b6d1-10726f6382b5": "Viewer",
        "376f8e40-9506-4725-abb3-e1f6bee0406d": "-",
    }

    payroll_details = {
        "designation": None,
        "employee_id": None,
        "employment_country": None,
        "fixed_pay": None,
        "joining_date": None,
        "pay_currency": None,
        "payout_frequency": None,
        "variable_pay": None,
    }
    if employee_payroll_data:
        payroll_details = {
            "designation": employee_payroll_data["designation"],
            "employee_email_id": employee_data["employee_email_id"],
            "employee_id": None,
            "employment_country": employee_payroll_data["employment_country"],
            "fixed_pay": base_pay,
            "joining_date": employee_payroll_data["joining_date"],
            "pay_currency": employee_payroll_data["pay_currency"],
            "payout_frequency": employee_payroll_data["payout_frequency"],
            "variable_pay": variable_pay,
        }

    hierarchy_details = {
        "reporting_manager_email_id": None,
        "reporting_manager_full_name": None,
    }
    if hierarchy_data:
        hierarchy_details = {
            "reporting_manager_email_id": hierarchy_data["reporting_manager_email_id"],
            "reporting_manager_full_name": hierarchy_data["manager_full_name"],
        }

    custom_fields_data = resolve_custom_field_data_for_user_filters(
        custom_field_data,
        system_name_default_values_map={
            "cf_1_dropdown_field": None,
            "cf_1_date_field": None,
            "cf_1_checkbox_field": None,
            "cf_1_text_field": None,
            "cf_1_number_field": None,
        },
        types_map={
            "cf_1_dropdown_field": "Dropdown",
            "cf_1_date_field": "Date",
            "cf_1_checkbox_field": "Checkbox",
            "cf_1_text_field": "Text",
            "cf_1_number_field": "Number",
        },
        dropdown_options_map={
            "cf_1_dropdown_field": {
                "option1": "Option1",
                "option2": "Option2",
            },
        },
        as_dicts=True,
    )

    role_names = ", ".join(
        [role_name_map.get(role_id, "-") for role_id in employee_data["user_role"]]
    )

    if is_export:
        return {
            "Name": full_name,
            "Email": employee_data["employee_email_id"],
            "Designation": (
                employee_payroll_data["designation"] if employee_payroll_data else None
            ),
            "Primary Commission Plan": ", ".join(
                pydash.map_(main_plan_details, "plan_name")
            ),
            "SPIFF Plans": ", ".join(pydash.map_(spiff_plan_details, "plan_name")),
            "Base Pay": base_pay,
            "Variable Pay": variable_pay,
            "Payout Frequency": (
                employee_payroll_data["payout_frequency"]
                if employee_payroll_data
                else None
            ),
            "Payout Currency": (
                employee_payroll_data["pay_currency"] if employee_payroll_data else None
            ),
            "Employee ID": None,
            "Employment Country": (
                employee_payroll_data["employment_country"]
                if employee_payroll_data
                else None
            ),
            "Reporting Manager": (
                hierarchy_data["reporting_manager_email_id"] if hierarchy_data else None
            ),
            "Reporting Manager Name": (
                hierarchy_data["manager_full_name"] if hierarchy_data else None
            ),
            "Joining Date": (
                employee_payroll_data["joining_date"] if employee_payroll_data else None
            ),
            "Exit Date": employee_data.get("exit_date"),
            "Status": status,
            "Role": ", ".join(
                [
                    role_name_map.get(role_id, "-")
                    for role_id in employee_data["user_role"]
                ]
            ),
            **resolve_custom_field_data_for_user_export(
                custom_field_data,
                display_name_default_values_map={
                    "DropdownField": None,
                    "DateField": None,
                    "CheckboxField": None,
                    "TextField": None,
                    "NumericField": None,
                },
                system_name_display_name_map={
                    "cf_1_dropdown_field": "DropdownField",
                    "cf_1_date_field": "DateField",
                    "cf_1_checkbox_field": "CheckboxField",
                    "cf_1_text_field": "TextField",
                    "cf_1_number_field": "NumericField",
                },
                types_map={
                    "cf_1_dropdown_field": "Dropdown",
                    "cf_1_date_field": "Date",
                    "cf_1_checkbox_field": "Checkbox",
                    "cf_1_text_field": "Text",
                    "cf_1_number_field": "Number",
                },
                dropdown_options_map={
                    "cf_1_dropdown_field": {
                        "option1": "Option1",
                        "option2": "Option2",
                    },
                },
            ),
        }
    data = {
        "client_id": 1,
        "created_by": None,
        "created_date": employee_data.get("created_date"),
        "exit_date": employee_data.get("exit_date"),
        "last_commission_date": employee_data.get("last_commission_date"),
        "deactivation_date": employee_data.get("deactivation_date"),
        "first_name": employee_data["first_name"],
        "last_name": employee_data["last_name"],
        "full_name": full_name,
        "profile_picture": None,
        status_col_text: status,
        "user_role": json.dumps(employee_data["user_role"]),
        "user_role_name": role_names,
        "user_source": employee_data["user_source"],
        "employee_email_id": employee_data["employee_email_id"],
        **hierarchy_details,
        **payroll_details,
        "employee_plan_details": (
            pydash.map_(
                main_plan_details,
                lambda item: pydash.pick(item, ["plan_id", "plan_name", "plan_type"]),
            )
            if main_plan_details
            else None
        ),
        "employee_spiff_plan_details": (
            pydash.map_(
                spiff_plan_details,
                lambda item: pydash.pick(item, ["plan_id", "plan_name", "plan_type"]),
            )
            if spiff_plan_details
            else None
        ),
        **custom_fields_data,
    }
    if not is_payouts:
        del data["created_date"]
    return data


@pytest.fixture
def test_data_for_map_payee_plan_details():
    data = {
        "plans": [
            {
                "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                "plan_name": "MAIN Plan 1",
                "plan_start_date": first_day_of_month(datetime.datetime(2022, 1, 1)),
                "plan_end_date": last_day_of_month(datetime.datetime(2022, 12, 31)),
                "plan_type": "MAIN",
            },
            {
                "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                "plan_name": "MAIN Plan 2",
                "plan_start_date": first_day_of_month(datetime.datetime(2022, 1, 1)),
                "plan_end_date": last_day_of_month(datetime.datetime(2022, 12, 31)),
                "plan_type": "MAIN",
            },
            {
                "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                "plan_name": "SPIFF Plan 1",
                "plan_start_date": first_day_of_month(datetime.datetime(2022, 1, 1)),
                "plan_end_date": last_day_of_month(datetime.datetime(2022, 12, 31)),
                "plan_type": "SPIFF",
            },
            {
                "plan_id": "23cd17bd-f719-4880-96cd-c681d55195be",
                "plan_name": "SPIFF Plan 2",
                "plan_start_date": first_day_of_month(datetime.datetime(2022, 1, 1)),
                "plan_end_date": last_day_of_month(datetime.datetime(2022, 12, 31)),
                "plan_type": "SPIFF",
            },
        ],
        "payees": [
            {
                "email": "<EMAIL>",
                "employee": {
                    "first_name": "User",
                    "last_name": "1",
                },
                "employee_payrolls": [
                    {
                        "freq": "Monthly",
                        "joining_date": first_day_of_month(
                            datetime.datetime(2021, 11, 1)
                        ),
                        "esd": first_day_of_month(datetime.datetime(2022, 1, 1)),
                        "eed": last_day_of_month(datetime.datetime(2022, 3, 31)),
                    },
                    {
                        "freq": "Monthly",
                        "joining_date": first_day_of_month(
                            datetime.datetime(2021, 11, 1)
                        ),
                        "esd": first_day_of_month(datetime.datetime(2022, 4, 1)),
                        "eed": last_day_of_month(datetime.datetime(2022, 6, 30)),
                    },
                    {
                        "freq": "Quarterly",
                        "joining_date": first_day_of_month(
                            datetime.datetime(2021, 11, 1)
                        ),
                        "esd": first_day_of_month(datetime.datetime(2022, 7, 1)),
                        "eed": None,
                    },
                ],
                "plan_payees": [
                    {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "esd": first_day_of_month(datetime.datetime(2022, 7, 1)),
                        "eed": last_day_of_month(datetime.datetime(2022, 12, 31)),
                        "plan_type": "MAIN",
                    },
                    {
                        "plan_id": "23cd17bd-f719-4880-96cd-c681d55195be",
                        "esd": first_day_of_month(datetime.datetime(2022, 7, 1)),
                        "eed": last_day_of_month(datetime.datetime(2022, 12, 31)),
                        "plan_type": "SPIFF",
                    },
                ],
            },
            {
                "email": "<EMAIL>",
                "employee": {
                    "first_name": "User",
                    "last_name": "2",
                },
                "employee_payrolls": [
                    {
                        "freq": "Monthly",
                        "joining_date": first_day_of_month(
                            datetime.datetime(2022, 1, 1)
                        ),
                        "esd": first_day_of_month(datetime.datetime(2022, 1, 1)),
                        "eed": None,
                    },
                ],
            },
        ],
    }
    for plan in data["plans"]:
        models.create_commission_plan_object(
            plan_id=plan["plan_id"],
            plan_name=plan["plan_name"],
            plan_start_date=plan["plan_start_date"],
            plan_end_date=plan["plan_end_date"],
            plan_type=plan["plan_type"],
        )
    for payee in data["payees"]:
        if payee.get("plan_payees", []):
            for plan_payee in payee["plan_payees"]:
                models.create_plan_payee(
                    payee["email"],
                    plan_payee["plan_id"],
                    effective_start_date=plan_payee["esd"],
                    effective_end_date=plan_payee["eed"],
                    plan_type=plan_payee["plan_type"],
                )
                models.create_plan_details(
                    payee["email"],
                    plan_payee["plan_id"],
                    effective_start_date=plan_payee["esd"],
                    effective_end_date=plan_payee["eed"],
                    plan_type=plan_payee["plan_type"],
                )
        if payee.get("employee_payrolls", []):
            for employee_payroll in payee["employee_payrolls"]:
                models.create_employee_payroll_object(
                    payee["email"],
                    "",
                    "IND",
                    "INR",
                    freq=employee_payroll["freq"],
                    joining_date=employee_payroll["joining_date"],
                    variable_pay=1000,
                    role="Revenue",
                    esd=employee_payroll["esd"],
                    eed=employee_payroll["eed"],
                )
        if payee.get("employee"):
            models.create_employee_objects(
                payee["email"],
                payee["employee"]["first_name"],
                payee["employee"]["last_name"],
                False,
            )


@pytest.mark.django_db
@pytest.mark.spm
class TestEmployeeServices:
    def test_get_response(self):
        models.create_employee_payroll_object(
            "<EMAIL>", "12345", "IND", "INR", "Monthly"
        )
        resp = employee_services.get_response(
            EmployeePayrollSerializer(
                employee_services.get_all_employee_payroll_details("1"),
                many=True,
            )
        )
        assert resp.status_code == 200

    def test_get_all_employees(self):
        models.create_employee_objects("<EMAIL>", "ashiq", "mohamed", True)
        all_employees = employee_services.get_all_employees("1")
        assert len(all_employees) > 0

    @pytest.mark.parametrize(
        "kwargs, expected_result",
        [  # case selected status options are active and exited.
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["ACTIVE", "EXITED"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
            # case not selected user status options
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                },
                [],
            ),
            # case selected status option is active.
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["ACTIVE"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
            # case selected status option is exited
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["EXITED"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
            # case selected status option is yet to join
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["YET_TO_JOIN"],
                },
                [],
            ),
            # case selected status options are yet to join and exited
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["YET_TO_JOIN", "EXITED"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
            # case selected status options are yet to join and active
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["YET_TO_JOIN", "ACTIVE"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
            # case selected status options are yet to join, active and exited
            (
                {
                    "component": "manage_users",
                    "is_to_fetch_exited_users": True,
                    "selected_options": ["YET_TO_JOIN", "ACTIVE", "EXITED"],
                },
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
        ],
    )
    def test_employee_details_based_on_permission_including_selected_options(
        self, kwargs, expected_result
    ):
        info = Mock()
        info.context.client_id = 7011
        info.context.user.username = "<EMAIL>"
        employee_services.get_ui_permissions = MagicMock(
            return_value=[
                "view:teams",
            ]
        )
        employee_services.get_data_permission = MagicMock(
            return_value={"type": "ALL_DATA"}
        )
        result = employee_services.employee_details_based_on_permission(info, **kwargs)
        sorted_result = [employee.employee_email_id for employee in result]
        assert sorted_result == expected_result

    @pytest.mark.parametrize("test_id", ["valid", "invalid", "error"])
    def test_persist_employee(self, test_id):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(
            {
                "first_name": "Delete",
                "last_name": "Automationxyz",
            }
        )
        if test_id == "invalid":
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "user_role": ["376f8e40-9506-4725-abb3-e1f6bee0406d"],
                }
            )
        elif test_id == "valid":
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "user_role": ["9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"],
                }
            )
        elif test_id == "error":
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                }
            )
        request.__setattr__("audit", LoggerUnit.logger())

        logger = LogWithContext(
            {
                "client_id": "1",
                "request_id": "1223",
                "request_name": "testing",
            }
        )
        request.__setattr__("logger", logger)
        employee_services.create_user_in_auth = MagicMock(return_value=None)
        employee_services.invalidate_and_cache_all_user_groups.apply_async = MagicMock(
            return_value=None
        )
        resp = employee_services.persist_employee("1", EmployeeSerializer, request)
        if test_id == "valid":
            assert resp.status_code == 201
        elif test_id == "invalid" or test_id == "error":
            assert resp.status_code == 400

    # ********* Employee Payroll details
    @pytest.mark.django_db
    @pytest.mark.parametrize("test_id", [1, 2, 3, 4, 5, 6])
    def test_persist_employee_payroll(self, test_id):
        models.create_custom_calendar_table()
        models.create_custom_periods_table()
        models.create_custom_calendar(
            name="Weekly",
            calendar_id=cal_id_1,
        )
        models.create_custom_periods(
            calendar_id=cal_id_1,
            period_start_date=start_of_day(datetime.datetime(2022, 7, 1)),
            period_end_date=start_of_day(datetime.datetime(2022, 7, 15)),
        )
        models.create_employee_objects("<EMAIL>", "ashiq", "mohamed", True)
        employee_services.get_ui_permissions = MagicMock(return_value=["edit:payroll"])

        http_request = HttpRequest()
        x = EmployeeAccessor("1").get_employee("<EMAIL>")
        request = Request(request=http_request)
        if test_id == 1:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": "Monthly",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "01-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        elif test_id == 2:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "10-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "10-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": "Monthly",
                            "effective_start_date": "05-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "20-Mar-2022",
                        },
                        {
                            "name": "employee_id",
                            "value": "1234",
                            "effective_start_date": "15-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        elif test_id == 3:
            models.create_custom_field(
                system_name="cf_1_test", display_name="Test", is_effective_dated=False
            )
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "10-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "15-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": "Monthly",
                            "effective_start_date": "05-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "20-Mar-2022",
                        },
                        {
                            "name": "employee_id",
                            "value": "1234",
                            "effective_start_date": "25-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
            request.client_id = "1"
        elif test_id == 4:
            models.create_custom_field(system_name="cf_1_test", display_name="Test")
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [],
                    "custom_field": [
                        {
                            "name": "cf_1_test",
                            "value": "Testing",
                            "effective_start_date": "25-Mar-2022",
                        }
                    ],
                }
                | x.__dict__
            )
            request.client_id = "1"
        elif test_id == 5:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": str(cal_id_1),
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "01-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        elif test_id == 6:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "01-Jul-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "01-Jul-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": str(cal_id_1),
                            "effective_start_date": "01-Jul-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "01-Jul-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        request.__setattr__("audit", LoggerUnit.logger())

        employee_services.invalidate_and_cache_all_user_groups = MagicMock()
        persist_employee_payroll_result = employee_services.modify_employee_payroll(
            "1",
            request.data["employee_email_id"],
            request.data["custom_field"],
            request.data["payroll"],
            request.audit,
        )
        if test_id in [1, 2, 3, 4, 6]:
            assert persist_employee_payroll_result["status"] == 201
        elif test_id in [5]:
            assert persist_employee_payroll_result["status"] == 400

    @pytest.mark.parametrize("test_id", [1, 2])
    def test_update_employee_payroll(self, test_id):
        employee_services.get_ui_permissions = MagicMock(
            return_value=[
                "edit:payroll",
            ]
        )
        models.create_employee_objects(
            "<EMAIL>", "ashiq", "mohamed", True
        )
        models.create_employee_objects(
            "<EMAIL>",
            "ashiq",
            "mohamed",
            True,
            role="376f8e40-9506-4725-abb3-e1f6bee0406d",
        )
        models.create_employee_payroll_object(
            "<EMAIL>", "12345", "IND", "INR", "Monthly"
        )
        http_request = HttpRequest()
        x = EmployeeAccessor("1").get_employee("<EMAIL>")
        request = Request(request=http_request)
        if test_id == 1:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "joining_date",
                            "value": "01-Mar-2022",
                            "effective_start_date": None,
                        },
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": "Monthly",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "01-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        else:
            request.data.update(
                {
                    "employee_email_id": "<EMAIL>",
                    "payroll": [
                        {
                            "name": "employment_country",
                            "value": "IND",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "pay_currency",
                            "value": "INR",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payout_frequency",
                            "value": "Monthly",
                            "effective_start_date": "01-Mar-2022",
                        },
                        {
                            "name": "payee_role",
                            "value": "Revenue",
                            "effective_start_date": "01-Mar-2022",
                        },
                    ],
                    "custom_field": [],
                }
                | x.__dict__
            )
        request.__setattr__("audit", LoggerUnit.logger())
        resp = employee_services.update_employee_payroll(
            "1",
            request.data["employee_email_id"],
            request.data["payroll"],
            request.audit,
            timezone.now(),
        )
        if test_id == 1 or test_id == 2:
            assert resp["status"] == STATUS_CODE.SUCCESS
        else:
            assert resp["status"] == STATUS_CODE.FAILED

    _PayoutMsgs = employee_services_utils.PayoutFreqMsgs

    @pytest.mark.django_db
    @pytest.mark.parametrize(
        "esd, modify_effective_start_date, expected",
        (
            (timezone.datetime(2023, 1, 1), False, _PayoutMsgs.PLAN),
            (timezone.datetime(2023, 3, 1), False, _PayoutMsgs.PLAN),
            (timezone.datetime(2023, 6, 1), False, _PayoutMsgs.COMMISSION_LOCK),
            (timezone.datetime(2022, 12, 1), False, _PayoutMsgs.PLAN),
            (timezone.datetime(2023, 9, 1), False, None),
            (timezone.datetime(2023, 4, 1), False, _PayoutMsgs.COMMISSION_LOCK),
            (timezone.datetime(2022, 12, 31), False, _PayoutMsgs.PLAN),
            (timezone.datetime(2022, 12, 31), True, None),
            (timezone.datetime(2023, 1, 1), True, None),
            (timezone.datetime(2023, 3, 31), True, _PayoutMsgs.PLAN),
            (timezone.datetime(2023, 1, 4), True, _PayoutMsgs.PLAN),
        ),
    )
    def test_handle_payout_frequency_change(
        self, esd, modify_effective_start_date, expected
    ):
        models.create_custom_calendar_table()
        models.create_custom_periods_table()
        models.create_custom_calendar(
            name="BiWeekly",
            calendar_id=cal_id_2,
        )
        models.create_custom_periods(
            calendar_id=cal_id_2,
            period_start_date=start_of_day(datetime.datetime(2023, 1, 1)),
            period_end_date=start_of_day(datetime.datetime(2023, 1, 15)),
        )

        models.create_custom_periods(
            calendar_id=cal_id_2,
            period_start_date=start_of_day(datetime.datetime(2023, 7, 1)),
            period_end_date=start_of_day(datetime.datetime(2023, 7, 15)),
        )

        models.create_custom_periods(
            calendar_id=cal_id_2,
            period_start_date=start_of_day(datetime.datetime(2024, 7, 1)),
            period_end_date=start_of_day(datetime.datetime(2024, 7, 15)),
        )

        models.create_plan_details(
            email_id="<EMAIL>",
            plan_id="3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
            effective_start_date=first_day_of_month(datetime.datetime(2023, 1, 1)),
            effective_end_date=last_day_of_month(datetime.datetime(2023, 3, 31)),
        )
        models.create_commission_lock_objects(
            email_id="<EMAIL>",
            period_start_date=first_day_of_month(datetime.datetime(2023, 4, 1)),
            period_end_date=last_day_of_month(datetime.datetime(2023, 6, 30)),
        )
        result, _ = employee_services_utils.handle_payout_frequency_change(
            client_id=1,
            employee_email_id="<EMAIL>",
            new_effective_start_date=esd,
            modify_effective_start_date=modify_effective_start_date,
        )
        assert result == expected

    @pytest.mark.parametrize(
        "fail_reason, new_payout_freq, expected",
        (
            (_PayoutMsgs.PLAN, "Quarterly", False),
            (_PayoutMsgs.PLAN, "Halfyearly", False),
            (_PayoutMsgs.PLAN, "Annual", False),
            (_PayoutMsgs.COMMISSION_LOCK, "Quarterly", False),
            (_PayoutMsgs.QUOTA, "Quarterly", False),
            # No change in payout freq, so handle payout freq shouldn't be called
            (None, "Monthly", True),
        ),
    )
    def test_update_employee_payroll_payout_freq_change(
        self, fail_reason, new_payout_freq, expected
    ):
        """Testes only the case when the payout frequency has changed in update payroll"""
        jan_first = timezone.datetime(2023, 1, 1, tzinfo=timezone.utc)
        models.create_employee_objects(
            "<EMAIL>",
            "Firstname",
            "Lastname",
            True,
            role="9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
        )
        models.create_employee_payroll_object(
            "<EMAIL>",
            "12345",
            "IND",
            "INR",
            "Monthly",
            joining_date=jan_first,
            esd=jan_first,
        )

        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(
            {
                "employee_email_id": "<EMAIL>",
                "payroll": [
                    {
                        "name": "joining_date",
                        "value": "01-Mar-2022",
                        "effective_start_date": "01-Mar-2022",
                    },
                    {
                        "name": "employment_country",
                        "value": "IND",
                        "effective_start_date": "01-Mar-2022",
                    },
                    {
                        "name": "pay_currency",
                        "value": "INR",
                        "effective_start_date": "01-Mar-2022",
                    },
                    {
                        "name": "payout_frequency",
                        "value": new_payout_freq,
                        "effective_start_date": "01-Mar-2022",
                    },
                    {
                        "name": "payee_role",
                        "value": "Revenue",
                        "effective_start_date": "01-Mar-2022",
                    },
                ],
                "custom_field": [],
            }
        )
        setattr(request, "audit", LoggerUnit.logger())

        # Note: Had to mock handle payout freq function from employee services not
        # the employee services utils.
        with patch.object(
            employee_services,
            "handle_payout_frequency_change",
        ) as mock_handle_fn:
            mock_handle_fn.return_value = fail_reason, ""

            employee_services.invalidate_and_cache_all_user_groups = MagicMock()
            resp = employee_services.update_employee_payroll(
                1,
                request.data["employee_email_id"],
                request.data["payroll"],
                request.audit,
                timezone.now(),
            )

        if expected:
            assert (
                resp["status"] == STATUS_CODE.SUCCESS
            ) and mock_handle_fn.call_count == 0
        else:
            assert (
                resp["status"] == STATUS_CODE.FAILED
            ) and mock_handle_fn.call_count == 1

    def test_get_all_employee_hierarchy(self):
        x = hierarchy_services.get_all_employee_hierarchy("1")
        print(x)

    # ********* Employee Plan details

    @patch("spm.tasks.invalidate_and_cache_all_user_groups.apply_async")
    @pytest.mark.parametrize(
        "payload, expected_status_code, expected_result",
        [
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "31-Jan-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Feb-2022",
                        "effective_end_date": "28-Feb-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "30-Jun-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2021",
                        "effective_end_date": "31-Dec-2023",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Effective start date should be greater than plan start 01-Jan-2022",
                        "Effective start date should be greater than oldest payroll 01-Jan-2022",
                        "Effective start date should be greater than joining date 01-Nov-2021",
                        "Effective end date should be less than plan end 31-Dec-2022",
                        "Payee has another plan MAIN Plan 2 from 01-Jul-2022 to 31-Dec-2022",
                        "Selected duration falls under multiple payout periods",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Payees added to the plan should be on the same payout frequency",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "User '<EMAIL>' is already part of the plan 'MAIN Plan 2'!",
                    ],
                },
            ),
        ],
    )
    def test_persist_employee_plan(
        self,
        _mock_invalidate_and_cache_all_user_groups,
        test_data_for_map_payee_plan_details,
        payload,
        expected_status_code,
        expected_result,
    ):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(payload)
        request.__setattr__("audit", LoggerUnit.logger())
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        resp = employee_services.persist_employee_plan(
            "1", PlanDetailsSerializer, request, timezone.now()
        )
        assert expected_status_code == resp.status_code
        if resp.status_code == 201:
            query = {
                "client_id": 1,
                "is_deleted": False,
                "knowledge_end_date__isnull": True,
                "employee_email_id": payload["employee_email_id"],
                "plan_id": payload["plan_details"]["plan_id"],
            }
            plan_payee = PlanPayee.objects.get(**query)
            assert plan_payee.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_payee.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
            plan_detail = PlanDetails.objects.get(**query)
            assert plan_detail.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_detail.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
        if expected_status_code == 400 and expected_result:
            expected_errors = set(expected_result["errors"])
            actual_errors = set(pydash.get(resp.data, "errors", []))
            assert expected_errors == actual_errors

    @patch("spm.tasks.invalidate_and_cache_all_user_groups.apply_async")
    @pytest.mark.parametrize(
        "payload, expected_status_code, expected_result",
        [
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "31-Jan-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Feb-2022",
                        "effective_end_date": "28-Feb-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "30-Jun-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2021",
                        "effective_end_date": "31-Dec-2023",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Effective start date should be greater than plan start 01-Jan-2022",
                        "Effective start date should be greater than oldest payroll 01-Jan-2022",
                        "Effective start date should be greater than joining date 01-Nov-2021",
                        "Effective end date should be less than plan end 31-Dec-2022",
                        "Selected duration falls under multiple payout periods",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Payees added to the plan should be on the same payout frequency",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
        ],
    )
    def test_edit_employee_plan(
        self,
        _mock_invalidate_and_cache_all_user_groups,
        test_data_for_map_payee_plan_details,
        payload,
        expected_status_code,
        expected_result,
    ):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(payload)
        request.__setattr__("audit", LoggerUnit.logger())
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        resp = employee_services.edit_employee_plan(
            "1", PlanDetailsSerializer, request, timezone.now()
        )
        assert expected_status_code == resp.status_code
        if resp.status_code == 201:
            query = {
                "client_id": 1,
                "is_deleted": False,
                "knowledge_end_date__isnull": True,
                "employee_email_id": payload["employee_email_id"],
                "plan_id": payload["plan_details"]["plan_id"],
            }
            plan_payee = PlanPayee.objects.get(**query)
            assert plan_payee.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_payee.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
            plan_detail = PlanDetails.objects.get(**query)
            assert plan_detail.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_detail.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
        if expected_status_code == 400 and expected_result:
            expected_errors = set(expected_result["errors"])
            actual_errors = set(pydash.get(resp.data, "errors", []))
            assert expected_errors == actual_errors

    @patch("spm.tasks.invalidate_and_cache_all_user_groups.apply_async")
    @pytest.mark.parametrize(
        "payload, expected_status_code, expected_result",
        [
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "31-Jan-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Feb-2022",
                        "effective_end_date": "28-Feb-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "30-Jun-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2021",
                        "effective_end_date": "31-Dec-2023",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Effective start date should be greater than plan start 01-Jan-2022",
                        "Effective start date should be greater than oldest payroll 01-Jan-2022",
                        "Effective start date should be greater than joining date 01-Nov-2021",
                        "Effective end date should be less than plan end 31-Dec-2022",
                        "Payee has another plan MAIN Plan 2 from 01-Jul-2022 to 31-Dec-2022",
                        "Selected duration falls under multiple payout periods",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "Payees added to the plan should be on the same payout frequency",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                400,
                {
                    "errors": [
                        "User '<EMAIL>' is already part of the plan 'MAIN Plan 2'!",
                    ],
                },
            ),
        ],
    )
    def test_update_employee_plan(
        self,
        _mock_invalidate_and_cache_all_user_groups,
        test_data_for_map_payee_plan_details,
        payload,
        expected_status_code,
        expected_result,
    ):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(payload)
        request.__setattr__("audit", LoggerUnit.logger())
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        resp = employee_services.update_employee_plan(
            "1", PlanDetailsSerializer, request, timezone.now()
        )
        assert expected_status_code == resp.status_code
        if resp.status_code == 201:
            query = {
                "client_id": 1,
                "is_deleted": False,
                "knowledge_end_date__isnull": True,
                "employee_email_id": payload["employee_email_id"],
                "plan_id": payload["plan_details"]["plan_id"],
            }
            plan_payee = PlanPayee.objects.get(**query)
            assert plan_payee.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_payee.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
            plan_detail = PlanDetails.objects.get(**query)
            assert plan_detail.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_detail.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
        if expected_status_code == 400 and expected_result:
            expected_errors = set(expected_result["errors"])
            actual_errors = set(pydash.get(resp.data, "errors", []))
            assert expected_errors == actual_errors

    @patch("spm.tasks.invalidate_and_cache_all_user_groups.apply_async")
    @pytest.mark.parametrize(
        "payload, expected_status_code, expected_result",
        [
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "31-Jan-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "31-Jan-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Feb-2022",
                        "effective_end_date": "28-Feb-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Feb-2022",
                        "effective_end_date": "28-Feb-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "30-Jun-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2022",
                        "effective_end_date": "30-Jun-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                201,
                None,
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2021",
                        "effective_end_date": "31-Dec-2023",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                400,
                {
                    "errors": [
                        "Effective start date should be greater than plan start 01-Jan-2022",
                        "Effective start date should be greater than oldest payroll 01-Jan-2022",
                        "Effective start date should be greater than joining date 01-Nov-2021",
                        "Effective end date should be less than plan end 31-Dec-2022",
                        "Payee has another plan MAIN Plan 2 from 01-Jul-2022 to 31-Dec-2022",
                        "Selected duration falls under multiple payout periods",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "3d39f08b-7a95-465a-b6c1-3dcc6a1a57cb",
                        "effective_start_date": "01-Jan-2021",
                        "effective_end_date": "31-Dec-2023",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                400,
                {
                    "errors": [
                        "Effective start date should be greater than plan start 01-Jan-2022",
                        "Effective start date should be greater than oldest payroll 01-Jan-2022",
                        "Effective start date should be greater than joining date 01-Nov-2021",
                        "Effective end date should be less than plan end 31-Dec-2022",
                        "Selected duration falls under multiple payout periods",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                400,
                {
                    "errors": [
                        "Payees added to the plan should be on the same payout frequency",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                400,
                {
                    "errors": [
                        "Payees added to the plan should be on the same payout frequency",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": False,
                },
                400,
                {
                    "errors": [
                        "User '<EMAIL>' is already part of the plan 'MAIN Plan 2'!",
                    ],
                },
            ),
            (
                {
                    "plan_details": {
                        "plan_id": "d37defce-618c-43cd-865d-86cf20343369",
                        "effective_start_date": "01-Jul-2022",
                        "effective_end_date": "31-Dec-2022",
                    },
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                    "is_edit": True,
                },
                201,
                None,
            ),
        ],
    )
    def test_modify_employee_plan(
        self,
        _mock_invalidate_and_cache_all_user_groups,
        test_data_for_map_payee_plan_details,
        payload,
        expected_status_code,
        expected_result,
    ):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(payload)
        request.__setattr__("audit", LoggerUnit.logger())
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        resp = employee_services.modify_employee_plan(
            "1", PlanDetailsSerializer, request
        )
        assert expected_status_code == resp.status_code
        if resp.status_code == 201:
            query = {
                "client_id": 1,
                "is_deleted": False,
                "knowledge_end_date__isnull": True,
                "employee_email_id": payload["employee_email_id"],
                "plan_id": payload["plan_details"]["plan_id"],
            }
            plan_payee = PlanPayee.objects.get(**query)
            assert plan_payee.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_payee.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
            plan_detail = PlanDetails.objects.get(**query)
            assert plan_detail.effective_start_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_start_date"].strftime("%d-%b-%Y")
            assert plan_detail.effective_end_date.strftime("%d-%b-%Y") == payload[
                "plan_details"
            ]["effective_end_date"].strftime("%d-%b-%Y")
        if expected_status_code == 400 and expected_result:
            expected_errors = set(expected_result["errors"])
            actual_errors = set(pydash.get(resp.data, "errors", []))
            assert expected_errors == actual_errors

    @patch("spm.tasks.invalidate_and_cache_all_user_groups.apply_async")
    @pytest.mark.parametrize(
        "payload, expected_errors",
        [
            (
                # Adding payee to a different SPIFF plan under one
                # effective payroll (Jan-Mar)
                {
                    "spiff_plans": [
                        {
                            "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                            "spiff_period": ["01-Jan-2022", "31-Jan-2022"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                None,
            ),
            (
                # Adding payee to a different SPIFF plan under one
                # effective payroll (Jan-Mar)
                {
                    "spiff_plans": [
                        {
                            "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                            "spiff_period": ["01-Feb-2022", "28-Feb-2022"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                None,
            ),
            (
                # Adding payee to a different SPIFF plan under two
                # effective payrolls (Jan-Mar & Apr-Jun) where both
                # payrolls have the same payout frequency
                {
                    "spiff_plans": [
                        {
                            "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                            "spiff_period": ["01-Jan-2022", "30-Jun-2022"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                None,
            ),
            (
                # Adding payee to a different SPIFF plan under three
                # effective payrolls (Jan-Mar, Apr-Jun & Jul-NULL) where
                # payrolls have different payout frequencies
                {
                    "spiff_plans": [
                        {
                            "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                            "spiff_period": ["01-Jan-2021", "31-Dec-2023"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "plans": {
                        "1086bb4c-598c-441e-8269-37c043a2c617": {
                            "errors": [
                                "Effective start date should be greater than plan start 01-Jan-2022",
                                "Effective start date should be greater than oldest payroll 01-Jan-2022",
                                "Effective start date should be greater than joining date 01-Nov-2021",
                                "Effective end date should be less than plan end 31-Dec-2022",
                                "Selected duration falls under multiple payout periods",
                            ]
                        }
                    }
                },
            ),
            (
                # Adding new payee to an existing SPIFF plan where the
                # existing payee has a different payout frequency compared
                # to the new payee
                {
                    "spiff_plans": [
                        {
                            "plan_id": "23cd17bd-f719-4880-96cd-c681d55195be",
                            "spiff_period": ["01-Jul-2022", "31-Dec-2022"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "plans": {
                        "23cd17bd-f719-4880-96cd-c681d55195be": {
                            "errors": [
                                "Payees added to the plan should be on the same payout frequency"
                            ]
                        }
                    }
                },
            ),
            (
                # Updating the existing plan payee but providing the same
                # effective start and end dates
                {
                    "spiff_plans": [
                        {
                            "plan_id": "23cd17bd-f719-4880-96cd-c681d55195be",
                            "spiff_period": ["01-Jul-2022", "31-Dec-2022"],
                        }
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                None,
            ),
            (
                # 1. Adding payee to a different SPIFF plan.
                # 2. Updating the existing plan payee by providing a new
                #    effective start and end dates.
                {
                    "spiff_plans": [
                        {
                            "plan_id": "1086bb4c-598c-441e-8269-37c043a2c617",
                            "spiff_period": ["01-Jan-2022", "31-Mar-2022"],
                        },
                        {
                            "plan_id": "23cd17bd-f719-4880-96cd-c681d55195be",
                            "spiff_period": ["01-Jan-2022", "31-Mar-2022"],
                        },
                    ],
                    "entry_point": "test",
                    "employee_email_id": "<EMAIL>",
                },
                None,
            ),
        ],
    )
    def test_spiff(
        self,
        _mock_invalidate_and_cache_all_user_groups,
        test_data_for_map_payee_plan_details,
        payload,
        expected_errors,
    ):
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.data.update(payload)
        request.__setattr__("audit", LoggerUnit.logger())
        request.__setattr__("client_id", "1")
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        resp = employee_services.persist_employee_spiff_plan(request)
        actual_errors_dict = (
            {plan_id: set(res["errors"]) for plan_id, res in resp["plans"].items()}
            if resp and "plans" in resp
            else None
        )
        if expected_errors:
            expected_errors_dict = {
                plan_id: set(res["errors"])
                for plan_id, res in expected_errors["plans"].items()
            }
            assert expected_errors_dict == actual_errors_dict
        else:
            assert actual_errors_dict is None, "Expected no errors but got some"
            plan_ids = pydash.map_(payload["spiff_plans"], "plan_id")
            query = {
                "client_id": 1,
                "is_deleted": False,
                "knowledge_end_date__isnull": True,
                "employee_email_id": payload["employee_email_id"],
                "plan_id__in": plan_ids,
            }
            expected_plan_payees_dict = {
                item["plan_id"]: item["spiff_period"] for item in payload["spiff_plans"]
            }
            plan_payees = list(PlanPayee.objects.filter(**query).values())
            plan_ids = pydash.map_(plan_payees, "plan_id")
            assert len(plan_ids) == len(
                set(plan_ids)
            ), "Duplicate plan_payees found! Possibly invalidation didn't work!"
            actual_plan_payees_dict = {
                str(plan_payee["plan_id"]): [
                    plan_payee["effective_start_date"].strftime("%d-%b-%Y"),
                    plan_payee["effective_end_date"].strftime("%d-%b-%Y"),
                ]
                for plan_payee in plan_payees
            }
            # Checking if the plan payee entries are updated
            assert expected_plan_payees_dict == actual_plan_payees_dict
            plan_details = list(PlanDetails.objects.filter(**query).values())
            plan_ids = pydash.map_(plan_details, "plan_id")
            assert len(plan_ids) == len(
                set(plan_ids)
            ), "Duplicate plan_details found! Possibly invalidation didn't work!"
            actual_plan_details_dict = {
                str(plan_payee["plan_id"]): [
                    plan_payee["effective_start_date"].strftime("%d-%b-%Y"),
                    plan_payee["effective_end_date"].strftime("%d-%b-%Y"),
                ]
                for plan_payee in plan_details
            }
            # Checking if the plan details entries are updated
            assert expected_plan_payees_dict == actual_plan_details_dict

    # ******* Validate Bulk add

    @pytest.mark.parametrize(
        "test_id",
        ["employee", "employee_payroll", "employee_hierarchy"],
    )
    def test_validate_bulk_add(self, test_id):
        http_request = HttpRequest()
        request = Request(request=http_request)
        client_id = 1
        audit = {
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
        }
        time = timezone.now()
        models.create_employee_objects(
            "<EMAIL>",
            "ashiq",
            "mohamed",
            True,
            role="376f8e40-9506-4725-abb3-e1f6bee0406d",
        )
        if test_id == "employee_hierarchy":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                    "employee_payroll": {
                        "client": client_id,
                        "additional_details": audit,
                        "employee_email_id": "<EMAIL>",
                        "knowledge_begin_date": time,
                        "effective_start_date": arrow.now().format("DD-MMM-YYYY"),
                        "joining_date": arrow.now().format("DD-MMM-YYYY"),
                        "variable_pay": 2000,
                        "designation": None,
                        "level": None,
                        "fixed_pay": None,
                        "employment_country": "IND",
                        "pay_currency": "INR",
                        "payout_frequency": "Monthly",
                        "payee_role": "Revenue",
                    },
                    "employee_hierarchy": {
                        "employee_email_id": "<EMAIL>",
                        "reporting_manager_email_id": "<EMAIL>",
                        "effective_start_date": (
                            arrow.now() + timedelta(days=1)
                        ).format("DD-MMM-YYYY"),
                        "client": client_id,
                        "additional_details": audit,
                        "knowledge_begin_date": time,
                    },
                },
            ]
        elif test_id == "employee":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                }
            ]
        elif test_id == "custom_field":
            models.create_custom_field(system_name="cf_1_region", display_name="Region")
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "knowledge_begin_date": time,
                    "custom_field": {"cf_1_test": "USA"},
                    "only_custom_field": True,
                }
            ]
        elif test_id == "employee_payroll":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                    "employee_payroll": {
                        "client": client_id,
                        "additional_details": audit,
                        "designation": None,
                        "level": None,
                        "fixed_pay": None,
                        "employment_country": "IND",
                        "pay_currency": "INR",
                        "payout_frequency": "Monthly",
                        "payee_role": "Revenue",
                        "employee_email_id": "<EMAIL>",
                        "knowledge_begin_date": time,
                        "effective_start_date": arrow.now().format("DD-MMM-YYYY"),
                        "joining_date": arrow.now().format("DD-MMM-YYYY"),
                        "variable_pay": 2000,
                    },
                }
            ]

        logger = LogWithContext({})
        request.__setattr__("logger", logger)

        response = employee_services.validate_bulk_add(
            client_id,
            EmployeeSerializer,
            EmployeePayrollSerializer,
            HierarchySerializer,
            request,
            audit,
        )
        assert response.status_code == 201

    @pytest.mark.parametrize(
        "user_role, client_id, expected_result",
        [
            ("<EMAIL>", 9000, False),
        ],
    )
    def test_check_user_has_permission(self, user_role, client_id, expected_result):
        assert (
            employee_services.check_user_has_permission(user_role, client_id)
            == expected_result
        )

    @pytest.mark.parametrize("test_id", ["valid", "invalid", "sqlparser"])
    def test_save_fresh_chat_id(self, test_id):
        models.create_employee_objects("<EMAIL>", "ashiq", "mohamed", True)
        http_request = HttpRequest()
        request = Request(request=http_request)
        if test_id == "valid":
            request.data.update(
                {
                    "email": "<EMAIL>",
                    "freshchat_id": "qwerty5",
                }
            )
        elif test_id == "invalid":
            request.data.update(
                {
                    "email": "<EMAIL>",
                    "freshchat_id": None,
                }
            )
        else:
            request.data.update(
                {
                    "email": "@crick.com",
                    "freshchat_id": None,
                }
            )
        request.__setattr__("audit", {})
        request.__setattr__("client_id", "1")
        response = employee_services.save_freshchat_id(request)
        if test_id == "valid":
            assert response.status_code == 201
        else:
            assert response.status_code == 400

    @pytest.mark.parametrize(
        "test_id",
        ["employee", "employee_payroll", "employee_hierarchy"],
    )
    def test_bulk_add(self, test_id):
        http_request = HttpRequest()
        request = Request(request=http_request)
        client_id = 1
        audit = {
            "server": "0.0.0.0",
            "updated_by": "<EMAIL>",
        }
        time = timezone.now()
        models.create_employee_objects(
            "<EMAIL>",
            "ashiq",
            "mohamed",
            True,
            role="376f8e40-9506-4725-abb3-e1f6bee0406d",
        )

        if test_id == "employee_hierarchy":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                    "employee_payroll": {
                        "client": client_id,
                        "additional_details": audit,
                        "employee_email_id": "<EMAIL>",
                        "knowledge_begin_date": time,
                        "effective_start_date": arrow.now().format("DD-MMM-YYYY"),
                        "joining_date": arrow.now().format("DD-MMM-YYYY"),
                        "variable_pay": 2000,
                        "designation": None,
                        "level": None,
                        "fixed_pay": None,
                        "employment_country": "IND",
                        "pay_currency": "INR",
                        "payout_frequency": "Monthly",
                        "payee_role": "Revenue",
                    },
                    "employee_hierarchy": {
                        "employee_email_id": "<EMAIL>",
                        "reporting_manager_email_id": "<EMAIL>",
                        "effective_start_date": (
                            arrow.now() + timedelta(days=1)
                        ).format("DD-MMM-YYYY"),
                        "client": client_id,
                        "additional_details": audit,
                        "knowledge_begin_date": time,
                    },
                },
            ]
        elif test_id == "employee":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                }
            ]
        elif test_id == "custom_field":
            models.create_custom_field(system_name="cf_1_region", display_name="Region")
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "knowledge_begin_date": time,
                    "custom_field": {"cf_1_test": "USA"},
                }
            ]
        elif test_id == "employee_payroll":
            request._full_data = [
                {
                    "id": 1,
                    "client": client_id,
                    "additional_details": audit,
                    "employee_email_id": "<EMAIL>",
                    "first_name": "a",
                    "last_name": "b",
                    "user_role": "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                    "knowledge_begin_date": time,
                    "created_date": time,
                    "send_notification": get_client(client_id).payee_notification,
                    "employee_payroll": {
                        "client": client_id,
                        "additional_details": audit,
                        "designation": None,
                        "level": None,
                        "fixed_pay": None,
                        "employment_country": "IND",
                        "pay_currency": "INR",
                        "payout_frequency": "Monthly",
                        "payee_role": "Revenue",
                        "employee_email_id": "<EMAIL>",
                        "knowledge_begin_date": time,
                        "effective_start_date": arrow.now().format("DD-MMM-YYYY"),
                        "joining_date": arrow.now().format("DD-MMM-YYYY"),
                        "variable_pay": 2000,
                    },
                }
            ]

        employee_services.create_user_in_auth = MagicMock()
        logger = LogWithContext({})
        request.__setattr__("logger", logger)
        request.__setattr__("audit", audit)
        response = employee_services.bulk_add(
            client_id,
            EmployeeSerializer,
            EmployeePayrollSerializer,
            HierarchySerializer,
            request,
            audit,
        )
        assert response.status_code == 201

    def test_exit_employee(self):
        models.create_employee_objects("<EMAIL>", "ashiq", "mohamed", True)
        http_request = HttpRequest()
        request = Request(request=http_request)
        request.__setattr__("audit", {"updated_by": "<EMAIL>"})
        employee_services.exit_employee(
            "1",
            "<EMAIL>",
            arrow.now().format("DD/MM/YYYY"),
            arrow.now().format("DD/MM/YYYY"),
            request.audit,
        )

    @pytest.mark.parametrize(
        "client_id, employee_email_id, effective_end_date, actual_result",
        [
            (
                1,
                "<EMAIL>",
                datetime.datetime(2030, 1, 31),
                {
                    "payees_reporting_to_exiting_manager": [
                        {"payee": "<EMAIL>", "reporting_end_date": None},
                    ]
                },
            )
        ],
    )
    def test_validate_exit_employee(
        self, client_id, employee_email_id, effective_end_date, actual_result
    ):
        models.create_employee_objects("<EMAIL>", "reportee", "1", False)
        models.create_employee_objects(
            "<EMAIL>",
            "reportee",
            "2",
            False,
            exit_date=timezone.make_aware(datetime.datetime(2022, 11, 26)),
        )
        models.create_employee_objects(
            "<EMAIL>",
            "chase",
            "1",
            False,
            exit_date=timezone.make_aware(datetime.datetime(2023, 11, 26)),
        )
        models.create_employee_objects("<EMAIL>", "manager", "1", False)
        models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
        models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
        models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")

        return_val = _validate_payees_reporting_to_exiting_manager(
            client_id, employee_email_id, effective_end_date
        )
        assert return_val == actual_result

    def test_get_employee_in_slack(self):
        try:
            get_employee_in_slack("ters", "rew")
        except Exception as e:
            assert str(e) == "Slack user_id ters not found in Everstage."

    def test_save_slack_details(self):
        models.create_employee_objects("<EMAIL>", "Venkat", "Mohamed", True)
        employee_services.save_slack_details(
            "1", "<EMAIL>", "ty", "yui", timezone.now()
        )

    @pytest.mark.parametrize(
        "filters, search_term, offset, limit, data_for_export, expected_res",
        [
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "",
                None,
                None,
                False,
                {
                    "data": (
                        [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                                is_manager=True,
                            )
                            for i in range(5)
                        ]
                        + [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                            )
                            for i in range(10)
                        ]
                    ),
                    "count": 17,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "",
                0,
                10,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                            is_manager=True,
                        )
                        for i in range(5)
                    ]
                    + [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(5)
                    ],
                    "count": 17,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "",
                1,
                10,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(5, 10)
                    ],
                    "count": 17,
                },
            ),
            ({}, "", 2, 10, False, {"data": [], "count": 17}),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "userb",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            1,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                            is_manager=True,
                        ),
                        generate_result_for_get_filtered_users(
                            1,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        ),
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "oss",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                            is_manager=True,
                        )
                        for i in range(5)
                    ],
                    "count": 5,
                },
            ),
            ({}, "abcd", None, None, False, {"data": [], "count": 0}),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Active"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(4)
                    ],
                    "count": 5,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Marked for exit"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6, 8)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Inactive"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8, 10)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Marked for exit", "Inactive"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6, 10)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Active", "Added"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6)
                    ],
                    "count": 7,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "status": ["Active", "Inactive"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in [*list(range(4)), *list(range(8, 10))]
                    ],
                    "count": 7,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "role": ["9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(9)
                    ],
                    "count": 9,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "role": [
                        "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                        "0490f3ce-49df-4189-b6d1-10726f6382b5",
                    ],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(10)
                    ],
                    "count": 10,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "exit_date": {
                        "type": "before",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8, 10)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "exit_date": {
                        "type": "after",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6, 8)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "exit_date": {
                        "type": "between",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                        "date2": timezone.make_aware(datetime.datetime(2022, 12, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {"data": [], "count": 0},
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8)
                    ],
                    "count": 8,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly", "Quarterly"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(10)
                    ],
                    "count": 10,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "employment_country": ["IND"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6)
                    ],
                    "count": 6,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "employment_country": ["USA", "CAN"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6, 10)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "pay_currency": ["INR"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(4)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "pay_currency": ["USD", "CAD"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(4, 10)
                    ],
                    "count": 6,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "joining_date": {
                        "type": "before",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8)
                    ],
                    "count": 8,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "joining_date": {
                        "type": "after",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8, 10)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "joining_date": {
                        "type": "between",
                        "date1": timezone.make_aware(datetime.datetime(2022, 10, 1)),
                        "date2": timezone.make_aware(datetime.datetime(2022, 11, 1)),
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(8, 10)
                    ],
                    "count": 2,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "reporting_manager": ["<EMAIL>"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            0,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                    ],
                    "count": 1,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "reporting_manager": ["<EMAIL>", "<EMAIL>"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in [0, *list(range(5, 10))]
                    ],
                    "count": 6,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {"cf_1_dropdown_field": ["option1"]},
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(2, 8)
                    ],
                    "count": 6,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {"cf_1_dropdown_field": ["option1", "option2"]},
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(2, 10)
                    ],
                    "count": 8,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {
                        "cf_1_date_field": {
                            "type": "before",
                            "date1": timezone.make_aware(
                                datetime.datetime(2022, 10, 1)
                            ),
                        }
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(2, 5)
                    ],
                    "count": 3,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {
                        "cf_1_date_field": {
                            "type": "after",
                            "date1": timezone.make_aware(
                                datetime.datetime(2022, 10, 1)
                            ),
                        }
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(5, 10)
                    ],
                    "count": 5,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {
                        "cf_1_date_field": {
                            "type": "between",
                            "date1": timezone.make_aware(
                                datetime.datetime(2022, 10, 1)
                            ),
                            "date2": timezone.make_aware(
                                datetime.datetime(2022, 12, 1)
                            ),
                        }
                    },
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(5, 10)
                    ],
                    "count": 5,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {"cf_1_checkbox_field": [True]},
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6)
                    ],
                    "count": 6,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "custom_field": {"cf_1_checkbox_field": [False]},
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(6, 10)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "mapping_status": "Mapped",
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(7, 10)
                    ],
                    "count": 3,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "mapping_status": "Unmapped",
                },
                "",
                None,
                None,
                False,
                {
                    "data": (
                        [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                                is_manager=True,
                            )
                            for i in range(5)
                        ]
                        + [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                            )
                            for i in range(7)
                        ]
                    ),
                    "count": 14,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(datetime.datetime.now().year + 2, 12, 1)
                    ),
                    "mapping_status": "Unmapped",
                },
                "",
                None,
                None,
                False,
                {
                    "data": (
                        [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(
                                        datetime.datetime.now().year + 2, 12, 1
                                    )
                                ),
                                is_manager=True,
                            )
                            for i in range(5)
                        ]
                        + [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(
                                        datetime.datetime.now().year + 2, 12, 1
                                    )
                                ),
                            )
                            for i in range(10)
                        ]
                    ),
                    "count": 17,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly"],
                    "pay_currency": ["USD"],
                },
                "",
                None,
                None,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(4, 8)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly"],
                    "pay_currency": ["USD"],
                },
                "",
                0,
                10,
                False,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in range(4, 8)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                },
                "",
                None,
                None,
                True,
                {
                    "data": (
                        [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                                is_manager=True,
                                is_export=True,
                            )
                            for i in range(5)
                        ]
                        + [
                            generate_result_for_get_filtered_users(
                                i,
                                effective_date=timezone.make_aware(
                                    datetime.datetime(2022, 11, 26)
                                ),
                                is_export=True,
                            )
                            for i in range(10)
                        ]
                    ),
                    "count": 17,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly"],
                    "pay_currency": ["USD"],
                },
                "",
                None,
                None,
                True,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                            is_export=True,
                        )
                        for i in range(4, 8)
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "payout_frequency": ["Monthly"],
                    "pay_currency": ["USD"],
                },
                "",
                0,
                10,
                True,
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                            is_export=True,
                        )
                        for i in range(4, 8)
                    ],
                    "count": 4,
                },
            ),
        ],
    )
    def test_get_filtered_users_raw(
        self, filters, search_term, offset, limit, data_for_export, expected_res
    ):
        create_data_for_get_filtered_users()
        effective_date = filters.get("effective_date")
        actual_res = employee_services.get_filtered_users_raw(
            1,
            filters,
            search_term=search_term,
            offset=offset,
            limit=limit,
            data_for_export=data_for_export,
            effective_date=effective_date,
        )
        filtered_actual_res = []
        for emp in actual_res["data"]:
            if "employee_email_id" in emp:
                if (
                    emp["employee_email_id"] != "<EMAIL>"
                    and emp["employee_email_id"] != "<EMAIL>"
                ):
                    filtered_actual_res.append(emp)
            elif "Email" in emp:
                if (
                    emp["Email"] != "<EMAIL>"
                    and emp["Email"] != "<EMAIL>"
                ):
                    filtered_actual_res.append(emp)
        actual_res["data"] = filtered_actual_res
        if not data_for_export:
            actual_res["data"] = pydash.map_(
                actual_res["data"],
                lambda item: pydash.omit(
                    item, ["created_date", "can_user_manage_admins"]
                ),
            )
        else:
            actual_res["data"] = pydash.map_(
                actual_res["data"],
                lambda item: pydash.omit(
                    item, ["Created Date", "can_user_manage_admins"]
                ),
            )
        assert expected_res == actual_res

    @pytest.mark.parametrize(
        "filters, orderby_fields, expected_res",
        [
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "employment_country": ["USA", "CAN"],
                },
                [{"column": "exit_date", "order": "asc"}],
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in [8, 9, 6, 7]
                    ],
                    "count": 4,
                },
            ),
            (
                {
                    "effective_date": timezone.make_aware(
                        datetime.datetime(2022, 11, 26)
                    ),
                    "employment_country": ["USA", "CAN"],
                },
                [
                    {"column": "user_role_name", "order": "desc"},
                    {"column": "cf_1_dropdown_field", "order": "desc"},
                ],
                {
                    "data": [
                        generate_result_for_get_filtered_users(
                            i,
                            effective_date=timezone.make_aware(
                                datetime.datetime(2022, 11, 26)
                            ),
                        )
                        for i in [9, 8, 6, 7]
                    ],
                    "count": 4,
                },
            ),
        ],
    )
    def test_get_filtered_users_raw_with_sort_cols(
        self, filters, orderby_fields, expected_res
    ):
        create_data_for_get_filtered_users()
        effective_date = filters.get("effective_date")
        actual_res = employee_services.get_filtered_users_raw(
            1,
            filters,
            effective_date=effective_date,
            orderby_fields=orderby_fields,
        )
        filtered_actual_res = []
        for emp in actual_res["data"]:
            if "employee_email_id" in emp:
                if (
                    emp["employee_email_id"] != "<EMAIL>"
                    and emp["employee_email_id"] != "<EMAIL>"
                ):
                    filtered_actual_res.append(emp)
            elif "Email" in emp:
                if (
                    emp["Email"] != "<EMAIL>"
                    and emp["Email"] != "<EMAIL>"
                ):
                    filtered_actual_res.append(emp)
        actual_res["data"] = filtered_actual_res
        actual_res["data"] = pydash.map_(
            actual_res["data"],
            lambda item: pydash.omit(item, ["created_date", "can_user_manage_admins"]),
        )

        assert expected_res == actual_res

    @pytest.mark.parametrize(
        "effective_date, expected_result",
        [
            (
                timezone.make_aware(datetime.datetime(2022, 12, 1)),
                {
                    "Unmapped": 14,
                    "Mapped": 3,
                    "Added": 2,
                    "Inactive": 2,
                },
            ),
            (
                timezone.make_aware(datetime.datetime(2023, 3, 1)),
                {
                    "Unmapped": 14,
                    "Mapped": 3,
                    "Added": 2,
                    "Inactive": 4,
                },
            ),
        ],
    )
    def test_get_all_filters_count(self, effective_date, expected_result):
        create_data_for_get_filtered_users()
        assert expected_result == employee_services.get_all_filters_count(
            1, effective_date=effective_date, logged_email="<EMAIL>"
        )

    @pytest.mark.parametrize(
        "client_id, email_id,effective_start_date, expected_result",
        [
            (
                "1",
                "<EMAIL>",
                timezone.make_aware(datetime.datetime(2021, 5, 15)),
                {"joining_date": timezone.make_aware(datetime.datetime(2021, 5, 15))},
            ),
            (
                "1",
                "<EMAIL>",
                timezone.make_aware(datetime.datetime(2020, 8, 15)),
                {"joining_date": timezone.make_aware(datetime.datetime(2020, 8, 15))},
            ),
            (
                "1",
                "<EMAIL>",
                timezone.make_aware(datetime.datetime(2021, 5, 15)),
                {"joining_date": None},
            ),
        ],
    )
    def test_validate_joining_date(
        self, client_id, email_id, effective_start_date, expected_result
    ):
        models.create_employee_objects(
            email_id,
            "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
            client_id,
            True,
            created_date=datetime.datetime(2020, 8, 12),
            status="Added",
        )
        if email_id != "<EMAIL>":
            models.create_employee_payroll_object(
                email_id,
                "1",
                "IND",
                "INR",
                "Monthly",
                joining_date=datetime.datetime(2020, 5, 15),
                esd=datetime.datetime(2021, 5, 15),
            )
            models.create_employee_hierarchy(
                email_id, "<EMAIL>", start_date=effective_start_date
            )
        result = employee_services.validate_joining_date(client_id, email_id)
        assert result.data == expected_result

    @pytest.mark.parametrize(
        "expected_length, expected_email_list",
        [
            (
                5,
                [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            ),
        ],
    )
    def test_get_valid_employees_for_sync(self, expected_length, expected_email_list):
        create_data_to_get_current_and_exit_users()
        curr_date = timezone.make_aware(datetime.datetime(2022, 12, 1))
        emp_payroll_data = employee_services.get_valid_employees_for_sync(
            client_id=1, curr_date=curr_date
        )
        actual_email_list = [emp.employee_email_id for emp in emp_payroll_data]
        assert len(actual_email_list) == expected_length
        assert actual_email_list == expected_email_list

    def test_get_all_employee_details_based_on_permission(self):
        employee_services.get_ui_permissions = MagicMock(
            return_value=[
                "manage:datasheetpermissions",
            ]
        )

        result = employee_services.get_all_employee_details_based_on_permission(
            client_id=3024,
            login_user_id="<EMAIL>",
            component="databooks",
            params={},
        )
        expected_result = [
            {
                "first_name": "everstage",
                "last_name": "admin",
                "employee_email_id": "<EMAIL>",
                "full_name": "everstage admin",
            },
            {
                "first_name": "Payee",
                "last_name": "1",
                "employee_email_id": "<EMAIL>",
                "full_name": "Payee 1",
            },
            {
                "first_name": "Payee",
                "last_name": "2",
                "employee_email_id": "<EMAIL>",
                "full_name": "Payee 2",
            },
            {
                "first_name": "Payee",
                "last_name": "3",
                "employee_email_id": "<EMAIL>",
                "full_name": "Payee 3",
            },
        ]
        assert result.model_dump() == expected_result

    def test_employee_details_based_on_permission(self):
        info = Mock()
        info.context.client_id = 3023
        info.context.user.username = "<EMAIL>"
        kwargs = {
            "user_status": "active",
            "as_of_date": "2022-01-01",
            "offset_value": 0,
            "limit_value": 10,
            "search_term": "test",
            "component": "manage_users",
            "user_role": "admin",
        }

        # Mock Employee objects
        employee1 = {"id": 1, "name": "Employee 1"}
        employee2 = {"id": 2, "name": "Employee 2"}
        employee_services.get_ui_permissions = MagicMock(
            return_value=[
                "view:teams",
            ]
        )
        employee_services.get_data_permission = MagicMock(
            return_value={"type": "ALL_DATA"}
        )
        # Mock the return value of get_all_employees_with_limit
        employee_services.EmployeeAccessor.get_all_employees_with_limit = MagicMock(
            return_value=[employee1, employee2]
        )

        # Call the function
        result = employee_services.employee_details_based_on_permission(info, **kwargs)

        # Assertions
        expected_result = [
            {"id": 1, "name": "Employee 1"},
            {"id": 2, "name": "Employee 2"},
        ]
        assert result == expected_result

    @patch("spm.services.config_services.employee_services.get_password_reset_link")
    @patch("spm.services.config_services.employee_services.send_email")
    @pytest.mark.parametrize(
        "filter_mode, expected",
        (
            (None, 46),
            (
                {
                    "value": True,
                    "constraints": {
                        "role": None,
                        "status": ["Invited"],
                        "payout_frequency": ["Monthly"],
                        "mapping_status": None,
                        "reporting_manager": None,
                        "employment_country": None,
                        "pay_currency": None,
                        "joining_date": None,
                        "exit_date": None,
                    },
                },
                5,
            ),
        ),
    )
    def test_send_auth_invite__selected_all(
        self, mock_send_email, mock_get_password_reset_link, filter_mode, expected
    ):
        client_id = 7010
        login_user_id = "<EMAIL>"
        mock_get_password_reset_link.return_value = "somelink"

        res = employee_services.send_auth_invite(
            client_id,
            email_ids=None,
            filter_mode=filter_mode,
            login_user_id=login_user_id,
            all_users_selected=True,
        )

        assert mock_send_email.si.call_count == expected
        assert (
            res["is_success"] is True
            and len(res["response"]["succeeded_emails"]) == expected
        )

    @patch("spm.services.config_services.employee_services.get_password_reset_link")
    @patch("spm.services.config_services.employee_services.send_email")
    @pytest.mark.parametrize(
        "auth_connection_name, connection_type, expected_data",
        (
            (
                "email-password",
                None,
                {
                    "name": "Ashley Jackson",
                    "admin_name": "everstage admin",
                    "reset_url": "somelink&type=invite",
                },
            ),
            (
                "everstage-social",
                "G-Suite",
                {
                    "name": "Ashley Jackson",
                    "admin_name": "everstage admin",
                    "Org_SSO": "Sign in with Google",
                    "app_url": None,
                },
            ),
            (
                "everstage-social",
                "Salesforce",
                {
                    "name": "Ashley Jackson",
                    "admin_name": "everstage admin",
                    "Org_SSO": "Sign in with Salesforce",
                    "app_url": None,
                },
            ),
            (
                "everstage-social",
                "Okta",
                {
                    "name": "Ashley Jackson",
                    "admin_name": "everstage admin",
                    "Org_SSO": "Sign in with Okta",
                    "app_url": None,
                },
            ),
        ),
    )
    def test_send_auth_invite__connection_type(
        self,
        mock_send_email,
        mock_get_password_reset_link,
        auth_connection_name,
        connection_type,
        expected_data,
    ):
        client_id = 7010
        client = get_client(client_id)
        client.auth_connection_name = auth_connection_name
        client.connection_type = connection_type
        client.save()

        email_ids = ["<EMAIL>", "<EMAIL>"]
        login_user_id = "<EMAIL>"
        mock_get_password_reset_link.return_value = "somelink"

        res = employee_services.send_auth_invite(
            client_id,
            email_ids=email_ids,
            login_user_id=login_user_id,
            filter_mode=None,
        )
        mesasge_obj = mock_send_email.si.call_args_list[0].kwargs["message"].get()
        template_data = mesasge_obj["personalizations"][0]["dynamic_template_data"]

        assert template_data == expected_data

        assert (
            res["is_success"] is True
            and res["response"]["succeeded_emails"] == email_ids[:1]
            and res["response"]["failed_emails"] == email_ids[1:]
        )

    @patch("spm.services.config_services.employee_services.get_password_reset_link")
    @patch("spm.services.config_services.employee_services.send_email")
    def test_send_auth_invite__client_notification(
        self, mock_send_email, mock_get_password_reset_link
    ):
        client_id = 7010
        login_user_id = "<EMAIL>"
        email_ids = ["<EMAIL>"]

        toggle_client_notification(client_id, True)
        mock_get_password_reset_link.return_value = "somelink"

        employee_services.send_auth_invite(client_id, email_ids, login_user_id)

        tasks = NotificationTaskAccessor(client_id).get_filtered_payees_by_task(
            "EMAIL_COMMISSION_NOTIFICATION", email_ids
        )
        assert len(tasks) == len(email_ids)

    # once materialized view is fixed,. this code will be uncommented
    # def test_get_all_filters_count(self):
    #
    #     # Mapped
    #     models.create_employee_objects(
    #         "<EMAIL>", "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", "1", True, created_date=datetime.datetime(2022, 8, 12), status="Added"
    #     )
    #     models.create_employee_payroll_object(
    #         "<EMAIL>", "1", "IND", "INR", "Monthly", joining_date=datetime.datetime(2022, 5, 15)
    #     )
    #     models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
    #     models.create_plan_details("<EMAIL>", "55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370")
    #
    #     # Exited
    #     models.create_employee_objects(
    #         "<EMAIL>",
    #         "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
    #         "2",
    #         True,
    #         created_date=datetime.datetime(2022, 8, 12),
    #         exit_date=timezone.now(),
    #         status="Inactive",
    #     )
    #     models.create_employee_payroll_object(
    #         "<EMAIL>", "2", "IND", "INR", "Monthly", joining_date=datetime.datetime(2022, 5, 15)
    #     )
    #     models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
    #
    #     # Unmapped
    #     models.create_employee_objects(
    #         "<EMAIL>", "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", "3", True, created_date=datetime.datetime(2022, 8, 12), status="Invited"
    #     )
    #     models.create_employee_payroll_object(
    #         "<EMAIL>", "3", "IND", "INR", "Monthly", joining_date=datetime.datetime(2022, 5, 15)
    #     )
    #     models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
    #
    #     models.create_employee_objects(
    #         "<EMAIL>", "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", "5", True, created_date=datetime.datetime(2022, 8, 12), status="Added"
    #     )
    #     models.create_employee_payroll_object(
    #         "<EMAIL>", "5", "IND", "INR", "Monthly", joining_date=datetime.datetime(2022, 5, 15)
    #     )
    #     models.create_plan_details("<EMAIL>", "55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370")
    #
    #     models.create_employee_objects(
    #         "<EMAIL>", "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", "6", True, created_date=datetime.datetime(2022, 8, 12), status="Added"
    #     )
    #     models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
    #     models.create_plan_details("<EMAIL>", "55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370")
    #
    #     # Marked For Exit
    #     models.create_employee_objects(
    #         "<EMAIL>",
    #         "9e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
    #         "4",
    #         True,
    #         created_date=datetime.datetime(2022, 8, 12),
    #         exit_date=datetime.datetime(2023, 1, 1),
    #         status="Added",
    #     )
    #     models.create_employee_payroll_object(
    #         "<EMAIL>", "4", "IND", "INR", "Monthly", joining_date=datetime.datetime(2022, 5, 15)
    #     )
    #     models.create_employee_hierarchy("<EMAIL>", "<EMAIL>")
    #     models.create_plan_details("<EMAIL>", "55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370")
    #     expected_result = {"Unmapped": 6, "Mapped": 2, "Added": 3, "Inactive": 1}
    #     assert expected_result == employee_services.get_all_filters_count(1)
    #     assert expected_result == employee_services.get_all_filters_count(1)
