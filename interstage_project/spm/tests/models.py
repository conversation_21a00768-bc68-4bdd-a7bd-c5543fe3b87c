import datetime
import uuid

import arrow
from django.db import connection
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.models import Commission, PayoutStatus
from commission_engine.models.commission_models import CommissionLock, CommLockDetail
from commission_engine.models.databook_models import DatasheetVariable
from commission_engine.models.etl_config_models import AccessTokenConfig
from commission_engine.models.report_models import CommissionReportEnrichment
from commission_engine.models.settlement_models import (
    CommissionSettlementMap,
    Settlement,
    SettlementKdMap,
    SettlementLock,
    SettlementLockDetail,
    SettlementRule,
)
from commission_engine.utils.date_utils import (
    first_day_of_month,
    last_day_of_month,
    make_aware_wrapper,
)
from spm.commission_adjustment_approvals.models import CommissionAdjustmentStatus
from spm.models import (
    DRS,
    Auth0AccessToken,
    Auth0Session,
    CommissionPlan,
    CustomCalendar,
    CustomCategory,
    CustomPeriods,
    DrawRecover,
    DRSUpdates,
    Employee,
    EmployeePayroll,
    PayoutArrear,
    PlanCriteria,
    PlanPayee,
    SessionClient,
    TokenSession,
)
from spm.models.approval_workflow_models import (
    ApprovalInstance,
    ApprovalInstanceStage,
    ApprovalRequests,
    SubApprovalRequests,
)
from spm.models.commission_plan_models import PlanDocs
from spm.models.config_models.client_config_models import ClientConfig
from spm.models.config_models.employee_models import Hierarchy, PlanDetails
from spm.models.config_models.integration_config_models import IntegrationConfig
from spm.models.custom_field_models import CustomFields
from spm.models.docusign_models import Docusign, TemplateDetails
from spm.models.manual_adjustment_models import CommissionAdj
from spm.models.notification_channel_connection_status_models import (
    NotificationChannelConnectionStatus,
)
from spm.models.period_info_models import PeriodInfo
from spm.models.quota_models import EmployeeQuota
from spm.models.rbac_models import RolePermissions
from spm.models.teams_models.team_models import Membership, TeamConfig
from spm.models.user_group_models import UserGroup


def create_plan_payee(
    email,
    plan_id,
    effective_start_date=first_day_of_month(timezone.now()),
    effective_end_date=last_day_of_month(timezone.now()),
    plan_type=None,
):
    PlanPayee.objects.create(
        employee_email_id=email,
        plan_id=plan_id,
        effective_start_date=effective_start_date,
        effective_end_date=effective_end_date,
        client_id=1,
        knowledge_begin_date=timezone.now(),
        plan_type=plan_type,
    )


def create_commission_object(
    email,
    amount,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    period_start_date=first_day_of_month(timezone.now()),
    period_end_date=last_day_of_month(timezone.now()),
    secondary_kd=None,
    line_item_id=None,
):
    secondary_kd = timezone.now() if secondary_kd is None else secondary_kd
    Commission.objects.create(
        period_start_date=period_start_date,
        client_id=1,
        knowledge_begin_date=timezone.now(),
        period_end_date=period_end_date,
        payee_email_id=email,
        commission_plan_id=plan_id,
        criteria_id=criteria,
        line_item_type="test",
        line_item_id=line_item_id,
        tier_id=None,
        amount=amount,
        primary_kd=None,
        secondary_kd=secondary_kd,
        secondary_snapshot_id=None,
        commission_snapshot_id=None,
        context_ids=None,
        knowledge_end_date=None,
    )


def create_commission_adjustment(
    email,
    amount,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
):
    CommissionAdj.objects.create(
        adjustment_type="Commission",
        period_start_date=first_day_of_month(timezone.now()),
        period_end_date=last_day_of_month(timezone.now()),
        payee_id=email,
        amount=amount,
        is_reverse=False,
        plan_id=plan_id,
        criteria_id=criteria_id,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_commission_adjustment_bulk(commission_adjs):
    """
    Requried Params: {"adjustment_id", "payee_email_id", "amount", "plan_id", "criteria_id", "reason_category_id", "reason_category", "period_start_date", "period_end_date" }
    """
    CommissionAdj.objects.bulk_create(
        [
            CommissionAdj(
                adjustment_id=commission_adj.get("adjustment_id", uuid.uuid4()),
                adjustment_type="Commission",
                period_start_date=first_day_of_month(timezone.now()),
                period_end_date=last_day_of_month(timezone.now()),
                payee_id=commission_adj.get("payee_email_id", "<EMAIL>"),
                amount=commission_adj.get("amount", 10),
                is_reverse=False,
                plan_id=commission_adj.get("plan_id"),
                criteria_id=commission_adj.get("criteria_id"),
                client_id=1,
                knowledge_begin_date=timezone.now(),
                reason_category_id=commission_adj.get("reason_category_id"),
                reason_category=commission_adj.get("reason_category"),
            )
            for commission_adj in commission_adjs
        ]
    )


def create_commission_adj_status_bulk(commission_adj_statuses):
    """
    Requried Params: {"adjustment_id", "payee_email_id", "amount", "plan_id", "criteria_id", "reason_category_id", "reason_category", "approval_status", "is_auto_approved", "is_approval_skipped" }
    """
    CommissionAdjustmentStatus.objects.bulk_create(
        [
            CommissionAdjustmentStatus(
                adjustment_id=comm_adj_status.get("adjustment_id", uuid.uuid4()),
                payee_email_id=comm_adj_status.get(
                    "payee_email_id", "<EMAIL>"
                ),
                amount=comm_adj_status.get("amount", 10),
                plan_id=comm_adj_status.get("plan_id"),
                criteria_id=comm_adj_status.get("criteria_id"),
                reason_category_id=comm_adj_status.get("reason_category_id"),
                reason_category=comm_adj_status.get("reason_category"),
                approval_status=comm_adj_status.get("approval_status", "approved"),
                period_start_date=first_day_of_month(timezone.now()),
                period_end_date=last_day_of_month(timezone.now()),
                client_id=1,
                is_auto_approved=comm_adj_status.get("is_auto_approved", False),
                is_approval_skipped=comm_adj_status.get("is_approval_skipped", False),
            )
            for comm_adj_status in commission_adj_statuses
        ]
    )


def create_commission_lock(email, plan_ids):
    CommissionLock.objects.create(
        period_start_date=first_day_of_month(timezone.now()),
        period_end_date=last_day_of_month(timezone.now()),
        payee_email_id=email,
        locked_knowledge_date=timezone.now(),
        is_locked=True,
        commission_plan_ids=plan_ids,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_commission_lock_detail(
    email,
    amount,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
):
    CommLockDetail.objects.create(
        payee_email_id=email,
        commission_plan_id=plan_id,
        criteria_id=criteria_id,
        line_item_type="Sheet1",
        amount=amount,
        lock_id="d729708f-40f4-4e5b-8b89-66d4e0cc56cf",
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_criteria(
    criteria_data=None,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_name="test",
    criteria_description=None,
    criteria_type="Simple",
    criteria_column=None,
    client_id=1,
    criteria_config=None,
    knowledge_begin_date=None,
    knowledge_end_date=None,
):
    criteria_data = {} if criteria_data is None else criteria_data
    criteria_column = [] if criteria_column is None else criteria_column
    criteria_config = {} if criteria_config is None else criteria_config
    knowledge_begin_date = (
        timezone.now() if knowledge_begin_date is None else knowledge_begin_date
    )
    PlanCriteria.objects.create(
        plan_id=plan_id,
        criteria_id=criteria_id,
        criteria_name=criteria_name,
        criteria_type=criteria_type,
        criteria_description=criteria_description,
        criteria_display_order=None,
        criteria_data=criteria_data,
        criteria_column=criteria_column,
        client_id=client_id,
        knowledge_begin_date=knowledge_begin_date,
        knowledge_end_date=knowledge_end_date,
        criteria_config=criteria_config,
    )


def create_settlement_object(
    email,
    amount,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    period_start_date=first_day_of_month(timezone.now()),
    period_end_date=last_day_of_month(timezone.now()),
    line_item_id=None,
):
    Settlement.objects.create(
        period_start_date=period_start_date,
        client_id=1,
        knowledge_begin_date=timezone.now(),
        period_end_date=period_end_date,
        comm_period_start_date=period_start_date,
        comm_period_end_date=period_end_date,
        commission_date=timezone.now(),
        settlement_date=timezone.now(),
        payee_email_id=email,
        plan_id=plan_id,
        criteria_id=criteria,
        line_item_id=line_item_id,
        commission_row_key=line_item_id,
        settlement_flag=True,
        amount=amount,
        knowledge_end_date=None,
    )


def create_settlement_criteria(
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
    settlement_rule_id="f4f78bfa-b974-4ab4-85fa-fab765140166",
):
    SettlementRule.objects.create(
        settlement_rule_id=settlement_rule_id,
        name="Settlement Rule",
        settlement_flag_expr={},
        amount_expr={},
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )
    CommissionSettlementMap.objects.create(
        commission_plan_id=plan_id,
        criteria_id=criteria_id,
        settlement_rule_id=settlement_rule_id,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_settlement_lock(email, plan_ids):
    SettlementLock.objects.create(
        period_start_date=first_day_of_month(timezone.now()),
        period_end_date=last_day_of_month(timezone.now()),
        payee_email_id=email,
        locked_knowledge_date=timezone.now(),
        is_locked=True,
        plan_ids=plan_ids,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_settlement_lock_detail(
    email,
    amount,
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
    settlement_rule_id="f4f78bfa-b974-4ab4-85fa-fab765140166",
):
    SettlementLockDetail.objects.create(
        payee_email_id=email,
        plan_id=plan_id,
        criteria_id=criteria_id,
        settlement_rule_id=settlement_rule_id,
        amount=amount,
        lock_id="d729708f-40f4-4e5b-8b89-66d4e0cc56cf",
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_settlement_kd_map(
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
    settlement_rule_id="f4f78bfa-b974-4ab4-85fa-fab765140166",
    email="<EMAIL>",
):
    SettlementKdMap.objects.create(
        payee_email_id=email,
        plan_id=plan_id,
        criteria_id=criteria_id,
        settlement_rule_id=settlement_rule_id,
        primary_kd=timezone.now(),
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_employee_objects(
    email,
    first_name,
    last_name,
    send_notification=False,
    status=None,
    exit_date=None,
    created_date=timezone.now(),
    time_zone=None,
    employee_config=None,
    role=["44ea88fe-c24d-49f1-b2c5-6478cc4e054a"],
    client_id=1,
    knowledge_begin_date=None,
):
    Employee.objects.create(
        employee_email_id=email,
        first_name=first_name,
        last_name=last_name,
        user_role=role,
        created_date=created_date,
        created_by="<EMAIL>",
        profile_picture=None,
        status=status,
        send_notification=send_notification,
        client_id=client_id,
        knowledge_begin_date=(
            knowledge_begin_date
            if knowledge_begin_date
            else timezone.now().replace(year=2021)
        ),
        time_zone=time_zone,
        employee_config=employee_config,
        exit_date=exit_date,
    )


def create_employee_payroll_object(
    email,
    emp_id,
    country,
    currency,
    freq="Monthly",
    joining_date=timezone.now(),
    variable_pay=123.09,
    role="Revenue",
    esd=None,
    eed=None,
    client_id=1,
):
    esd = timezone.now().replace(year=2021) if esd is None else esd
    EmployeePayroll.objects.create(
        employee_email_id=email,
        employee_id=emp_id,
        joining_date=joining_date,
        employment_country=country,
        fixed_pay=123.90,
        variable_pay=variable_pay,
        pay_currency=currency,
        payout_frequency=freq,
        effective_start_date=esd,
        effective_end_date=eed,
        client_id=client_id,
        knowledge_begin_date=timezone.now().replace(year=2021),
        payee_role=role,
    )


def create_employee_hierarchy(
    email, manager_email, end_date=None, start_date=None, client_id=1
):
    Hierarchy.objects.create(
        employee_email_id=email,
        client_id=client_id,
        knowledge_begin_date=timezone.now(),
        reporting_manager_email_id=manager_email,
        effective_start_date=start_date or first_day_of_month(timezone.now()),
        effective_end_date=end_date,
    )


def create_commission_plan_object(
    plan_end_date=last_day_of_month(timezone.now() + timezone.timedelta(days=30)),
    plan_name="My Test",
    plan_id="55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370",
    plan_start_date=first_day_of_month(timezone.now()),
    plan_type="MAIN",
    is_draft=True,
    client_id=1,
):
    CommissionPlan.objects.create(
        knowledge_begin_date=timezone.now(),
        plan_name=plan_name,
        plan_id=plan_id,
        plan_start_date=plan_start_date,
        plan_end_date=plan_end_date,
        created_on=timezone.now(),
        client_id=client_id,
        plan_display_order=None,
        plan_type=plan_type,
        is_draft=is_draft,
    )


def create_plan_supporting_doc(
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa", file_name="test.pdf", email=None
):
    PlanDocs.objects.create(
        plan_id=plan_id,
        file_name=file_name,
        doc=None,
        employee_email_id=email,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_docusign_template_details(**kwargs):
    TemplateDetails.objects.create(
        account_id=kwargs.get("account_id", "720550f8-d324-4aa3-8dc7-5c229ce90b08"),
        template_id=kwargs.get("template_id", "9a9ef957-262d-4afc-83b5-050b2dbd4391"),
        plan_id=kwargs.get("plan_id", "1f0206e4-192b-44fc-a6e5-e71d38b8f9aa"),
        client_id=1,
        knowledge_begin_date=timezone.now(),
        fiscal_year=kwargs.get("fiscal_year", 2023),
        primary_recipient_role=kwargs.get("primary_recipient_role", "Payee"),
    )


def create_commission_spiff_plan_object(
    plan_end_date=last_day_of_month(timezone.now() + timezone.timedelta(days=30)),
    plan_name="My Test",
    id="55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370",
):
    CommissionPlan.objects.create(
        knowledge_begin_date=timezone.now(),
        plan_name=plan_name,
        plan_id=id,
        plan_start_date=first_day_of_month(timezone.now()),
        plan_end_date=plan_end_date,
        created_on=timezone.now(),
        client_id=1,
        plan_display_order=None,
        plan_type="SPIFF",
    )


def create_commission_lock_object(
    email,
    plan_ids="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    commission_snapshot_id="4086af68-de57-43d0-892d-04c7b2b0a315",
    period_start_date=first_day_of_month(timezone.now()),
    period_end_date=last_day_of_month(timezone.now()),
):
    CommissionLock.objects.filter(
        client_id=1,
        payee_email_id=email,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
    ).update(is_deleted=True)
    CommissionLock.objects.create(
        period_start_date=period_start_date,
        period_end_date=period_end_date,
        payee_email_id=email,
        locked_knowledge_date=timezone.now(),
        is_locked=True,
        lock_id=commission_snapshot_id,
        commission_plan_ids=plan_ids,
        commission_snapshot_id=commission_snapshot_id,
        knowledge_begin_date=timezone.now(),
        client_id=1,
    )


def create_client_config(config_type, value, end_date=None):
    ClientConfig.objects.create(
        effective_start_date=timezone.now(),
        effective_end_date=end_date,
        is_ed_enabled=False,
        type=config_type,
        value=value,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_draw_recover(email, rec_bal, amount):
    DrawRecover.objects.create(
        adjustment_id="55d24a4d-5b46-46ce-9cd0-8dbb5c2dd370",
        adjustment_type="RecoverDraw",
        payee_id=email,
        fiscal_year=arrow.now().format("YYYY"),
        period=arrow.now().format("MMM"),
        recoverable_balance=rec_bal,
        amount=amount,
        comments=None,
        drs_id=None,
        posted_by=None,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_teams(team_id, team_name):
    TeamConfig.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id=team_id,
        team_name=team_name,
        team_created_by="<EMAIL>",
        team_created_time=timezone.now(),
        team_updated_by="<EMAIL>",
        team_updated_time=timezone.now(),
        team_type="team",
        pod_owner_name=None,
    )


def create_pod_teams(team_id, team_name, owner_name):
    TeamConfig.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id=team_id,
        team_name=team_name,
        team_created_by="<EMAIL>",
        team_created_time=timezone.now(),
        team_updated_by="<EMAIL>",
        team_updated_time=timezone.now(),
        team_type="pod",
        pod_owner_name=owner_name,
    )


def create_membership(email, teamid, end_date):
    Membership.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id=teamid,
        group_member_email_id=email,
        effective_start_date=timezone.now(),
        effective_end_date=end_date,
    )


def create_plan_details(
    email_id,
    plan_id,
    effective_start_date=first_day_of_month(timezone.now()),
    effective_end_date=last_day_of_month(timezone.now()),
    plan_type="MAIN",
):
    PlanDetails.objects.create(
        employee_email_id=email_id,
        plan_id=plan_id,
        effective_start_date=effective_start_date,
        effective_end_date=effective_end_date,
        client_id=1,
        knowledge_begin_date=timezone.now(),
        plan_type=plan_type,
    )


def create_payout_arrear(psd, ped, pending_amount):
    PayoutArrear.objects.create(
        client_id=1,
        period_start_date=psd,
        period_end_date=ped,
        payee_id="<EMAIL>",
        pending_amount=pending_amount,
        knowledge_begin_date=timezone.now(),
        processed_at=timezone.now(),
    )


def create_custom_field(**kwargs):
    CustomFields.objects.create(
        is_effective_dated=kwargs.get("is_effective_dated", False),
        display_name=kwargs.get("display_name"),
        system_name=kwargs.get("system_name"),
        field_type=kwargs.get("field_type", "Integer"),
        options=kwargs.get("options", {}),
        is_archived=kwargs.get("is_archived", False),
        display_order=kwargs.get("display_order", 1),
        is_mandatory=kwargs.get("is_mandatory", False),
        data_type_id=kwargs.get("data_type_id", 1),
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_employee_quota_objects(**kwargs):
    EmployeeQuota.objects.create(
        employee_email_id=kwargs.get("employee_email_id"),
        quota_category_name=kwargs.get("quota_category_name", "Primary"),
        quota_year=kwargs.get("quota_year"),
        quota_schedule_type=kwargs.get("quota_schedule_type"),
        schedule_quota=kwargs.get("schedule_quota", {}),
        payout_quota=kwargs.get("payout_quota", {}),
        additional_details=kwargs.get("additional_details", {}),
        is_team_quota=kwargs.get("is_team_quota", False),
        team_type=kwargs.get("team_type", None),
        client_id=1,
        knowledge_begin_date=timezone.now(),
        effective_start_date=make_aware(
            datetime.datetime(int(kwargs.get("quota_year")), 1, 1)
        ),
    )


def create_commission_lock_objects(
    email_id,
    period_start_date,
    period_end_date,
    knowledge_begin_date=timezone.now(),
    is_locked=True,
):
    CommissionLock.objects.create(
        client_id=1,
        knowledge_begin_date=knowledge_begin_date,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
        payee_email_id=email_id,
        is_locked=is_locked,
    )


def create_user_group(name, config):
    UserGroup.objects.create(
        user_group_name=name,
        user_group_config=config,
        created_by="<EMAIL>",
        created_at=timezone.now(),
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_report_enrichment(
    plan_id="1f0206e4-192b-44fc-a6e5-e71d38b8f9aa",
    criteria_id="e11d2560-84fc-4bbd-bdf6-056c636e46a3",
    databook_id="5f2b88aa-5d31-4ad3-b642-0f4f6a817535",
    datasheet_id="b61528ce-adca-4815-8f50-19d4defdb6ae",
):
    CommissionReportEnrichment.objects.create(
        commission_plan_id=plan_id,
        criteria_id=criteria_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        system_name="co_1_amount",
        report_system_name="co_1_amount_47347933_b8fe_4f38_8bc0_b6b63c3afd85",
        display_name="amount",
        data_type_id=1,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_integration_config_object(
    client_id, integration_type, config, employee_email_id
):
    IntegrationConfig.objects.create(
        integration_type=integration_type,
        config=config,
        client_id=client_id,
        knowledge_begin_date=timezone.now(),
        employee_email_id=employee_email_id,
    )


def create_docusign_object(**kwargs):
    Docusign.objects.create(
        client_id=kwargs.get("client_id", 1),
        knowledge_begin_date=timezone.now(),
        email_id=kwargs.get("email_id", "<EMAIL>"),
        login_email_id=kwargs.get("login_email_id", "<EMAIL>"),
        account_id=kwargs.get("account_id", "720550f8-d324-4aa3-8dc7-5c229ce90b08"),
        refresh_token=kwargs.get(
            "refresh_token",
            "***********************************************************************************************.AQoAAAABAAgABwAAmPeLc-zaSAgAABhchAYE20gCADizGK4Q56xIiEkp3HJp4aIVAAEAAAAYAAEAAAAFAAAADQAkAAAAMzNmMTYxZDYtNmY3Yy00ZmVjLWI0NzgtNzU5MzI5NGZiM2JhIgAkAAAAMzNmMTYxZDYtNmY3Yy00ZmVjLWI0NzgtNzU5MzI5NGZiM2JhMACAUExzWcnaSDcA-qGEzLtjCEGy6Gn09A6R1Q.GBbTOaZH80S-FrjczhUHrDK8bFpKYG3nLNkDd-AfEtU8jtjTK_yGU5TosTAHYNYRe-OwXPVMExFrk4g92dSr7hMk4Kp0soNoZ6KhCbwfWz60mq3AG2XuBkI6u2VVkTZoOFos7lCXZWeD99e_WeNQpbAQChpgFn8LJkXigDj_wjRK9N53pczBZL2NOvJRnqPcK_W1qDzDwIOQixVLD5QMCcT1yh4HGPNDxcG17uF9ELOR8bOyac2fY_mbMASrjTPkM9v48rH-2c-Tkr9jPRfh-z7v5tuW2aXcCdGau3d2J5rYL2OcjiuX7qRUgxSFgt-M8l_dAVpO2cJKh7CWSirrdg",
        ),
        refresh_token_expires_at=timezone.now() + datetime.timedelta(days=30),
        base_uri=kwargs.get("base_uri", "https://demo.docusign.net"),
    )


def create_payout_status_object(
    email,
    paid_amount=100,
    period_start_date=first_day_of_month(timezone.now()),
    period_end_date=last_day_of_month(timezone.now()),
    payment_status="Paid",
):
    PayoutStatus.objects.create(
        client_id=1,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
        payee_email_id=email,
        paid_amount=paid_amount,
        payment_status=payment_status,
        total_payout=200,
        processed_amount=100,
        ignored_amount=0,
        pending_amount=(200 - paid_amount),
        commission_percentage=0,
        commission=0,
        fx_rate=1,
        knowledge_begin_date=timezone.now(),
        payout_frequency="Monthly",
    )


def create_approval_wf_instance_bulk(data):
    approval_wf_instances = []
    for record in data:
        approval_wf_instances.append(
            ApprovalInstance(
                knowledge_begin_date=record.get("knowledge_begin_date"),
                knowledge_end_date=record.get("knowledge_end_date"),
                additional_details=record.get("additional_details", None),
                approval_wf_instance_id=record.get("approval_wf_instance_id"),
                template_id=record.get("template_id", None),
                entity_type=record.get("entity_type"),
                entity_key=record.get("entity_key"),
                status=record.get("status"),
                requested_time=record.get("requested_time"),
                completion_time=record.get("completion_time", None),
                notify_on_reject=record.get("notify_on_reject", []),
                notify_all_approvers=record.get("notify_all_approvers", False),
                instance_data=record.get("instance_data"),
                created_by=record.get("created_by"),
                is_system_action=record.get("is_system_action", False),
                client_id=record.get("client_id"),
            )
        )
    ApprovalInstance.objects.bulk_create(approval_wf_instances)


def create_approval_stage_instance_bulk(data):
    approval_stage_instances = []
    for record in data:
        approval_stage_instances.append(
            ApprovalInstanceStage(
                knowledge_begin_date=record.get("knowledge_begin_date"),
                knowledge_end_date=record.get("knowledge_end_date"),
                additional_details=record.get("additional_details", None),
                stage_instance_id=record.get("stage_instance_id"),
                approval_wf_instance_id=record.get("approval_wf_instance_id"),
                stage_template_id=record.get("stage_template_id", None),
                stage_name=record.get("stage_name"),
                approvers=record.get("approvers"),
                approval_strategy=record.get("approval_strategy"),
                due_date=record.get("due_date"),
                approval_trigger=record.get("approval_trigger"),
                cool_off_date=record.get("cool_off_date"),
                is_auto_approve=record.get("is_auto_approve", False),
                status=record.get("status"),
                initiated_time=record.get("initiated_time"),
                requested_time=record.get("requested_time"),
                completed_time=record.get("completed_time"),
                notes=record.get("notes"),
                is_system_action=record.get("is_system_action", False),
                stage_order=record.get("stage_order", False),
                stage_template_data=record.get("stage_template_data", {}),
                client_id=record.get("client_id"),
            )
        )
    ApprovalInstanceStage.objects.bulk_create(approval_stage_instances)


def create_approval_requests_bulk(data):
    approval_stage_instances = []
    for record in data:
        approval_stage_instances.append(
            ApprovalRequests(
                knowledge_begin_date=record.get("knowledge_begin_date"),
                knowledge_end_date=record.get("knowledge_end_date"),
                additional_details=record.get("additional_details", None),
                approval_request_id=record.get("approval_request_id"),
                approval_wf_instance_id=record.get("approval_wf_instance_id"),
                stage_instance_id=record.get("stage_instance_id"),
                entity_type=record.get("entity_type", None),
                entity_key=record.get("entity_key"),
                approver=record.get("approver"),
                status=record.get("status"),
                requested_time=record.get("requested_time"),
                completed_time=record.get("completed_time", None),
                is_system_action=record.get("is_system_action", False),
                client_id=record.get("client_id"),
            )
        )
    ApprovalRequests.objects.bulk_create(approval_stage_instances)


def create_sub_approval_requests_bulk(data):
    sub_approval_requests = []
    for record in data:
        sub_approval_requests.append(
            SubApprovalRequests(
                knowledge_begin_date=record.get("knowledge_begin_date"),
                knowledge_end_date=record.get("knowledge_end_date"),
                additional_details=record.get("additional_details", None),
                sub_request_id=record.get("sub_request_id"),
                request_id=record.get("request_id"),
                approval_wf_instance_id=record.get("approval_wf_instance_id"),
                stage_instance_id=record.get("stage_instance_id"),
                sub_entity_id=record.get("sub_entity_id"),
                sub_request_data=record.get("sub_request_data"),
                approver=record.get("approver"),
                status=record.get("status"),
                requested_time=record.get("requested_time"),
                completed_time=record.get("completed_time", None),
                is_system_action=record.get("is_system_action", False),
                client_id=record.get("client_id"),
                comments=record.get("comments", None),
                period_start_date=record.get("period_start_date", None),
                period_end_date=record.get("period_end_date", None),
            )
        )
    SubApprovalRequests.objects.bulk_create(sub_approval_requests)


# Function to create period label entry
def create_period_label_data(client_id):
    PeriodInfo.objects.create(
        period_start_date=make_aware_wrapper(
            first_day_of_month(datetime.datetime(2023, 11, 1))
        ),
        period_end_date=make_aware_wrapper(
            last_day_of_month(datetime.datetime(2023, 11, 30))
        ),
        period_label="November CF 2023",
        knowledge_begin_date=datetime.datetime(2022, 11, 1),
        client_id=client_id,
    )


def create_datasheet_variable(
    databook_id="bcb74a6a-78c2-4b6d-8b8d-6a45edf54e64",
    datasheet_id="4588769c-6abc-4c72-8a6e-039eab6d198d",
    system_name="co_1_amount",
    display_name="amount",
    data_type_id=1,
    field_order=0,
    meta_data=None,
):
    DatasheetVariable.objects.create(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        system_name=system_name,
        display_name=display_name,
        data_type_id=data_type_id,
        field_order=field_order,
        meta_data=meta_data,
        client_id=1,
        knowledge_begin_date=timezone.now(),
    )


def create_role_permissions(**kwargs):
    RolePermissions.objects.create(
        client_id=kwargs.get("client_id", 1),
        role_permission_id=kwargs.get(
            "role_permission_id", "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"
        ),
        knowledge_begin_date=timezone.now(),
        permissions={
            "crystal": {"permissions": ["manage:crystal"], "data_permission": None},
            "queries": {
                "permissions": [
                    "view:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "dashboard": {
                "permissions": [
                    "view:dashboard",
                ],
                "data_permission": None,
            },
            "databooks": {
                "permissions": [
                    "view:databook",
                ],
                "data_permission": None,
            },
            "everstage": {"permissions": ["view:everstage"], "data_permission": None},
            "manage_users": {
                "permissions": [
                    "manage:users",
                    "view:payroll",
                    "edit:payroll",
                ],
                "data_permission": {"type": "ALL_DATA"},
                "impersonated_user_roles": ["ALL"],
            },
            "quotas_draws": {
                "permissions": [
                    "view:quotas",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_feed": {
                "permissions": ["view:commissionfeed"],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                ],
                "data_permission": None,
            },
            "payouts_statements": {
                "permissions": [
                    "view:payouts",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        display_name=kwargs.get("display_name", "Custom Role"),
        description=kwargs.get("description", ""),
        created_at=timezone.now(),
    )


def create_custom_calendar_table():
    create_query = f"""create table if not exists custom_calendar
    (temporal_id bigserial,
    knowledge_begin_date timestamptz,
    knowledge_end_date timestamptz,
    is_deleted bool,
    calendar_id uuid,
    name varchar,
    additional_details jsonb,
    client_id int4,
    variable_pay_type varchar,
    calendar_type varchar
    )"""
    with connection.cursor() as cursor:
        cursor.execute(create_query)


def create_custom_periods_table():
    create_query = f"""create table if not exists custom_periods
    (temporal_id bigserial,
    knowledge_begin_date timestamptz,
    knowledge_end_date timestamptz,
    is_deleted bool,
    calendar_id uuid,
    period_start_date timestamptz,
    period_end_date timestamptz,
    additional_details jsonb,
    client_id int4
    )"""
    with connection.cursor() as cursor:
        cursor.execute(create_query)


def create_custom_calendar(
    name,
    calendar_id=uuid.uuid4(),
    knowledge_begin_date=timezone.now(),
    variable_pay_type="ANNUAL",
    calendar_type="CUSTOM",
):
    CustomCalendar.objects.create(
        name=name,
        calendar_id=calendar_id,
        knowledge_begin_date=knowledge_begin_date,
        knowledge_end_date=None,
        is_deleted=False,
        client_id=1,
        variable_pay_type=variable_pay_type,
        calendar_type=calendar_type,
    )


def create_custom_periods(
    calendar_id,
    period_start_date,
    period_end_date,
    knowledge_begin_date=timezone.now(),
    period_label=None,
):
    if not period_label:
        start_date_str = period_start_date.strftime("%b %d, %Y")
        end_date_str = period_end_date.strftime("%b %d, %Y")
        period_label = f"{start_date_str} - {end_date_str}"
    CustomPeriods.objects.create(
        calendar_id=calendar_id,
        knowledge_begin_date=knowledge_begin_date,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
        period_label=period_label,
        knowledge_end_date=None,
        is_deleted=False,
        client_id=1,
    )


def create_access_token_config(
    client_id,
    access_token_config_id,
    access_type,
    access_token_url,
    access_request_body,
    service_name,
    connection_name,
    knowledge_begin_date=timezone.now(),
    knowledge_end_date=None,
    payload_type="params",
    api_access_key=None,
    jwt_data=None,
    domain=None,
    connection_status="CONNECTED",
    created_on=timezone.now(),
):
    AccessTokenConfig.objects.create(
        client_id=client_id,
        knowledge_begin_date=knowledge_begin_date,
        knowledge_end_date=knowledge_end_date,
        access_token_config_id=access_token_config_id,
        access_type=access_type,
        payload_type=payload_type,
        api_access_key=api_access_key,
        access_token_url=access_token_url,
        access_request_body=access_request_body,
        jwt_data=jwt_data,
        service_name=service_name,
        domain=domain,
        connection_name=connection_name,
        connection_status=connection_status,
        created_on=created_on,
    )


def create_auth0_access_token(
    client_id,
    employee_email_id,
    session_id,
    token_id,
    creation_additional_details={},
    is_blacklisted=False,
    blacklisted_date=None,
    blacklisting_additional_details=None,
):
    Auth0AccessToken.objects.create(
        client_id=client_id,
        employee_email_id=employee_email_id,
        session_id=session_id,
        token_id=token_id,
        creation_additional_details=creation_additional_details,
        is_blacklisted=is_blacklisted,
        blacklisted_date=blacklisted_date,
        blacklisting_additional_details=blacklisting_additional_details,
    )


def create_auth0_session(
    session_id,
    creation_additional_details={},
    is_blacklisted=False,
    blacklisted_date=None,
    blacklisting_additional_details=None,
):
    Auth0Session.objects.create(
        session_id=session_id,
        creation_additional_details=creation_additional_details,
        is_blacklisted=is_blacklisted,
        blacklisted_date=blacklisted_date,
        blacklisting_additional_details=blacklisting_additional_details,
    )


def create_session_client(
    session_id,
    email_id,
    client_id,
    support_membership_id=None,
):
    SessionClient.objects.create(
        session_id=session_id,
        email_id=email_id,
        client_id=client_id,
        membership_id=support_membership_id,
    )


def create_token_session_mapping(token_id, session_id):
    TokenSession.objects.create(token_id=token_id, session_id=session_id)


def create_custom_category_bulk(cateogries, created_by, module_name, client_id):
    """
    Requried Params: {"custom_category_id", "custom_category_name", "is_default", "display_order", "is_active", "is_used" }
    """
    CustomCategory.objects.bulk_create(
        [
            CustomCategory(
                **category,
                created_at=timezone.now(),
                created_by=created_by,
                client_id=client_id,
                knowledge_end_date=None,
                knowledge_begin_date=timezone.now(),
                is_deleted=False,
                module_name=module_name,
            )
            for category in cateogries
        ]
    )


def create_query_bulk(queries):
    """
    Requried Params: {"drs_id", "logger", "assignee", "subject", "status", "category_id", "category", "sequence_number" }
    """
    DRS.objects.bulk_create(
        [
            DRS(
                **query,
                client_id=1,
                logged_time=timezone.now(),
                knowledge_begin_date=timezone.now(),
                knowledge_end_date=None,
                involved_users=[],
                is_deleted=False,
                additional_details="",
            )
            for query in queries
        ]
    )

    drs_updates = []

    for query in queries:
        drs_update = DRSUpdates(
            drs_id=query.get("drs_id"),
            additional_details=query.get("additional_details", ""),
            is_deleted=query.get("is_deleted", False),
            knowledge_begin_date=query.get("knowledge_begin_date", timezone.now()),
            knowledge_end_date=query.get("knowledge_begin_date", None),
            updated_by=query.get("logger"),
            updated_time=query.get("logged_time", timezone.now()),
            client_id=1,
            meta={
                "status": {
                    "new_value": query.get("status"),
                    "old_value": query.get("status"),
                },
                "assignee": {
                    "new_value": query.get("assignee"),
                    "old_value": query.get("assignee"),
                },
                "category": {
                    "new_value": query.get("category"),
                    "old_value": query.get("category"),
                },
                "category_id": {
                    "new_value": query.get("category_id"),
                    "old_value": query.get("category_id"),
                },
                "involved_users": {
                    "new_value": query.get("involved_users", []),
                    "old_value": query.get("involved_users", []),
                },
            },
            message="Test Message",
            message_markdown="<p>Test Message</p>",
        )
        drs_updates.append(drs_update)

    DRSUpdates.objects.bulk_create(drs_updates)


def create_notification_channel_connection_status(
    notification_channel, notification_channel_status, updated_by
):
    NotificationChannelConnectionStatus.objects.create(
        client_id=1,
        notification_channel=notification_channel,
        notification_channel_status=notification_channel_status,
        knowledge_begin_date=timezone.now(),
        updated_at=timezone.now(),
        updated_by=updated_by,
    )
