from typing import Dict
from unittest.mock import patch
from uuid import UUID

import pytest
from django.utils import timezone
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError

from commission_engine.tests.models import create_client
from everstage_admin_backend.tsar import create_test_support_membership
from interstage_project.session_utils import set_tsar_membership
from interstage_project.threadlocals import clear_threadlocals
from spm.models.config_models.employee_models import Employee
from spm.models.session_management_models import SessionClient
from spm.serializers.multi_client_serializers import SetClientInputSerializer
from spm.services.multi_client_services import get_clients, set_client
from spm.tests.models import (
    create_auth0_access_token,
    create_employee_objects,
    create_session_client,
)


@pytest.fixture(scope="function")
def data_setup():
    create_client(
        name="Blackrock",
        domain="blackrock.com",
        client_id=9871401,
    )
    create_client(
        name="Sonata",
        domain="sonatasoft.com",
        client_id=9871402,
    )
    create_client(
        name="Samsung",
        domain="samsung.com",
        client_id=9871403,
    )

    yield

    clear_threadlocals()


@pytest.mark.django_db
@pytest.mark.multi_client
class TestGetClients:
    def test_reg_user_exited(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - no client
        """
        ######## DATA SETUP ########
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Exited",
        )

        ######## TEST AND ASSERT AN EXCEPTION ########

        with pytest.raises(NotFound, match="Employee not Invited"):
            get_clients(
                session_id="a925ff73-c550-490e-a453-1266e54c2be8",
                login_email="<EMAIL>",
                email="<EMAIL>",
                is_impersonation=False,
                client_id=None,
                con_provider="google-oauth2",
                auth0_user_id="auth0|123456",
            )

    def test_reg_user_uniclient(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - single client
        """
        ######## DATA SETUP ########
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )

        ######## TEST ########

        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=None,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########

        assert data["clients"] == [
            {"client_id": 9871401, "client_name": "Blackrock", "src": None}
        ]
        assert data["selected_client_id"] == 9871401
        assert data["client_support_memberships"] == []
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    @pytest.mark.parametrize(
        "is_selected_client_mapping_present",
        [True, False],
    )
    def test_reg_user_multiclient_impersontated(
        self, is_selected_client_mapping_present, data_setup
    ):
        """
        Test get_clients for
        - regular customer user
        - multi client
        - impersonated
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )

        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        # auth0_access_token
        create_auth0_access_token(
            client_id=9871402,
            employee_email_id="<EMAIL>",
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            token_id="a925ff73-c550-490e-a453-1266e54c2be9",
        )

        # session client
        if is_selected_client_mapping_present:
            create_session_client(
                session_id="a925ff73-c550-490e-a453-1266e54c2be8",
                email_id="<EMAIL>",
                client_id=9871402,
            )

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=True,
            client_id=None,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871401, "client_name": "Blackrock", "src": None},
            {"client_id": 9871402, "client_name": "Sonata", "src": None},
        ]
        assert data["selected_client_id"] == 9871402
        assert data["client_support_memberships"] == []
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    def test_reg_user_multiclient_with_preffered_client(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - multi client
        - non-impersonated
        - preffered client set in employee config
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            employee_config={"preffered_client_id": 9871402},
            status="Active",
        )

        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            employee_config={"preffered_client_id": 9871402},
            status="Active",
        )

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=None,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871401, "client_name": "Blackrock", "src": None},
            {"client_id": 9871402, "client_name": "Sonata", "src": None},
        ]
        assert data["selected_client_id"] == 9871402
        assert data["client_support_memberships"] == []
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    def test_reg_user_multiclient_without_preffered_client(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - multi client
        - non-impersonated
        - without preffered client in employee config
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )

        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=None,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871401, "client_name": "Blackrock", "src": None},
            {"client_id": 9871402, "client_name": "Sonata", "src": None},
        ]
        assert data["selected_client_id"] == None
        assert data["client_support_memberships"] == []
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    def test_reg_user_session_client_mapping(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - session client mapping already exists
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )

        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=9871401,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871401, "client_name": "Blackrock", "src": None},
            {"client_id": 9871402, "client_name": "Sonata", "src": None},
        ]
        assert data["selected_client_id"] == 9871401
        assert data["client_support_memberships"] == []
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    def test_reg_user_session_client_membership_mapping(self, data_setup):
        """
        Test get_clients for
        - regular customer user
        - session client membership mapping exists
        - try to update that mapping; and face exception
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )

        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        # session client
        create_session_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871401,
            support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
        )

        ######## TEST ########
        with pytest.raises(
            PermissionDenied, match="SWITCHING_SUPPORT_MEMBERSHIP_NOT_ALLOWED"
        ):
            # try to update the session client mapping being a regular user while the current session is already mapped with a support membership
            get_clients(
                session_id="a925ff73-c550-490e-a453-1266e54c2be8",
                login_email="<EMAIL>",
                email="<EMAIL>",
                is_impersonation=False,
                client_id=9871401,
                con_provider="google-oauth2",
                auth0_user_id="auth0|123456",
            )

    def test_staff_user_fresh_login(self, data_setup):
        """
        Test get_clients for
        - verified staff user (google-oauth2)
        - having support memberships; but not yet chosen any
        - `session<>client<>membership` mapping doesn't exist
        """
        ######## DATA SETUP ########
        # employees
        # present as employee in client 2; but has memberships in client 1 and 3
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
            employee_config={"preffered_client_id": 9871402},
        )

        # memberships
        starts1 = timezone.now() - timezone.timedelta(days=1)
        ends1 = timezone.now() + timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
            client_id=9871401,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts1,
            ends_at=ends1,
            approver="<EMAIL>",
            status="APPROVED",
        )

        starts3 = timezone.now() - timezone.timedelta(days=3)
        ends3 = timezone.now() + timezone.timedelta(days=3)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"),
            client_id=9871403,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts3,
            ends_at=ends3,
            approver="<EMAIL>",
            status="APPROVED",
        )

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=None,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871402, "client_name": "Sonata", "src": None}
        ]
        assert data["selected_client_id"] == None
        assert data["client_support_memberships"][0]["client_id"] == 9871401
        assert data["client_support_memberships"][0]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd161"
        )
        assert data["client_support_memberships"][1]["client_id"] == 9871403
        assert data["client_support_memberships"][1]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd163"
        )
        assert data["selected_support_membership_id"] == None
        assert data["ui_action"] == None

    def test_support_user(self, data_setup):
        """
        Test get_clients for
        - support user
        - having support memberships; and chosen one
        - `session<>client<>membership` mapping exists
        - selected support membership is present in thread local
        """
        ######## DATA SETUP ########
        # employees
        # present as employee in client 2; but has memberships in client 1 and 3
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
            employee_config={"preffered_client_id": 9871402},
        )

        # memberships
        starts1 = timezone.now() - timezone.timedelta(days=1)
        ends1 = timezone.now() + timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
            client_id=9871401,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts1,
            ends_at=ends1,
            approver="<EMAIL>",
            status="APPROVED",
        )

        starts3 = timezone.now() - timezone.timedelta(days=3)
        ends3 = timezone.now() + timezone.timedelta(days=3)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"),
            client_id=9871403,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts3,
            ends_at=ends3,
            approver="<EMAIL>",
            status="APPROVED",
        )

        # session client
        create_session_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871403,
            support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"),
        )

        # add support membership to thread local
        set_tsar_membership(UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"))

        ######## TEST ########
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            client_id=9871403,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871402, "client_name": "Sonata", "src": None}
        ]
        assert data["selected_client_id"] == 9871403
        assert data["client_support_memberships"][0]["client_id"] == 9871401
        assert data["client_support_memberships"][0]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd161"
        )
        assert data["client_support_memberships"][1]["client_id"] == 9871403
        assert data["client_support_memberships"][1]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd163"
        )
        assert data["selected_support_membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd163"
        )
        assert data["ui_action"] == None

    @patch("spm.services.multi_client_services.logout_as_user")
    def test_support_user_impersonated(self, mock_logout_as_user, data_setup):
        """
        Test get_clients for
        - support user
        - having support memberships; and chosen one
        - `session<>client<>membership` mapping exists
        - selected support membership is present in thread local
        """
        ######## DATA SETUP ########
        # employees
        # present as employee in client 2; but has memberships in client 1 and 3
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
            employee_config={"preffered_client_id": 9871402},
        )

        # another payee to whom "ankur" (the support user in client 3) has impersonated into (from client 1)
        create_employee_objects(
            email="<EMAIL>",
            first_name="Suryavarathan",
            last_name="Muthalagesan",
            client_id=9871401,
            status="Active",
        )

        # memberships
        starts1 = timezone.now() - timezone.timedelta(days=1)
        ends1 = timezone.now() + timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
            client_id=9871401,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts1,
            ends_at=ends1,
            approver="<EMAIL>",
            status="APPROVED",
        )

        starts3 = timezone.now() - timezone.timedelta(days=3)
        ends3 = timezone.now() + timezone.timedelta(days=3)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"),
            client_id=9871403,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts3,
            ends_at=ends3,
            approver="<EMAIL>",
            status="APPROVED",
        )

        # session client
        create_session_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871403,
            support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"),
        )

        # add support membership to thread local
        set_tsar_membership(UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd163"))

        ######## TEST ########
        mock_logout_as_user.return_value = None
        data: Dict = get_clients(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=True,
            client_id=9871403,
            con_provider="google-oauth2",
            auth0_user_id="auth0|123456",
        )

        ######## ASSERT ########
        assert data["clients"] == [
            {"client_id": 9871402, "client_name": "Sonata", "src": None}
        ]
        assert data["selected_client_id"] == 9871403
        assert data["client_support_memberships"][0]["client_id"] == 9871401
        assert data["client_support_memberships"][0]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd161"
        )
        assert data["client_support_memberships"][1]["client_id"] == 9871403
        assert data["client_support_memberships"][1]["membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd163"
        )
        assert data["selected_support_membership_id"] == UUID(
            "71cd035f-c101-4e6f-ba12-7fdcaaadd163"
        )
        assert data["ui_action"] == "reload"


@pytest.mark.django_db
@pytest.mark.multi_client
class TestSetClientInputSerializer:
    def test_reg_user(self, data_setup):
        """
        Test set_client serializer for
        - regular user
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        ######## TEST ########
        data = {
            "login_email": "<EMAIL>",
            "con_provider": "google-oauth2",
            "selected_client_id": 9871402,
            "is_selection_default": True,
            "selected_support_membership_id": None,
        }

        ser = SetClientInputSerializer(data=data)
        ser.is_valid(raise_exception=True)
        validated_data: Dict = ser.data

        ######## ASSERT ########
        assert validated_data["login_email"] == "<EMAIL>"
        assert validated_data["con_provider"] == "google-oauth2"
        assert validated_data["selected_client_id"] == 9871402
        assert validated_data["is_selection_default"] == True
        assert validated_data["selected_support_membership_id"] == None

    def test_reg_user_invalid_client(self, data_setup):
        """
        Test set_client serializer for
        - regular user
        - invalid client
        - raise exception
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        ######## TEST ########
        # choose a client which is not accessible to the user
        data = {
            "login_email": "<EMAIL>",
            "con_provider": "google-oauth2",
            "selected_client_id": 9871403,
            "is_selection_default": True,
            "selected_support_membership_id": None,
        }

        with pytest.raises(ValidationError):
            ser = SetClientInputSerializer(data=data)
            ser.is_valid(raise_exception=True)

    def test_support_user(self, data_setup):
        """
        Test set_client serializer for
        - staff member choosing a support membership
        """
        # support memberships
        starts = timezone.now() - timezone.timedelta(days=1)
        ends = timezone.now() + timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
            client_id=9871401,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts,
            ends_at=ends,
            approver="<EMAIL>",
            status="APPROVED",
        )

        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
            client_id=9871402,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts,
            ends_at=ends,
            approver="<EMAIL>",
            status="APPROVED",
        )

        ######## TEST ########
        data = {
            "login_email": "<EMAIL>",
            "con_provider": "google-oauth2",
            "selected_client_id": 9871405,  # give wrong client_id; it should be ignored and selected_client_id should be set from the membership
            "is_selection_default": True,  # should be ignored; and set to False
            "selected_support_membership_id": UUID(
                "71cd035f-c101-4e6f-ba12-7fdcaaadd162"
            ),
        }

        ser = SetClientInputSerializer(data=data)
        ser.is_valid(raise_exception=True)
        validated_data: Dict = ser.data

        ######## ASSERT ########
        assert validated_data["login_email"] == "<EMAIL>"
        assert validated_data["con_provider"] == "google-oauth2"
        assert validated_data["selected_client_id"] == 9871402
        assert validated_data["is_selection_default"] == False
        assert (
            validated_data["selected_support_membership_id"]
            == "71cd035f-c101-4e6f-ba12-7fdcaaadd162"
        )

    def test_unverified_staff_member_trying_to_set_membership(self, data_setup):
        """
        Test set_client serializer for
        - staff member choosing a support membership
        - but the member is not verified (con_provider is not google-oauth2)
        """
        # support memberships
        starts = timezone.now() - timezone.timedelta(days=1)
        ends = timezone.now() + timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
            client_id=9871401,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts,
            ends_at=ends,
            approver="<EMAIL>",
            status="APPROVED",
        )

        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
            client_id=9871402,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts,
            ends_at=ends,
            approver="<EMAIL>",
            status="APPROVED",
        )

        ######## TEST ########
        data = {
            "login_email": "<EMAIL>",
            "con_provider": "Username-Password-Authentication",  # un-accepable con_provider for a support user
            "selected_client_id": 9871402,
            "is_selection_default": False,
            "selected_support_membership_id": UUID(
                "71cd035f-c101-4e6f-ba12-7fdcaaadd162"
            ),
        }

        with pytest.raises(ValidationError):
            ser = SetClientInputSerializer(data=data)
            ser.is_valid(raise_exception=True)

    def test_staff_member_trying_to_set_obsolete_membership(self, data_setup):
        """
        Test set_client serializer for
        - staff member choosing a support membership
        - but the membership is obsolete
        """
        # support memberships
        starts = timezone.now() - timezone.timedelta(days=2)
        ends = timezone.now() - timezone.timedelta(days=1)
        create_test_support_membership(
            created_by="<EMAIL>",
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
            client_id=9871402,
            members=[
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ],
            starts_at=starts,
            ends_at=ends,
            approver="<EMAIL>",
            status="APPROVED",
        )

        ######## TEST ########
        data = {
            "login_email": "<EMAIL>",
            "con_provider": "Username-Password-Authentication",  # un-accepable con_provider for a support user
            "selected_client_id": 9871402,
            "is_selection_default": False,
            "selected_support_membership_id": UUID(
                "71cd035f-c101-4e6f-ba12-7fdcaaadd162"
            ),
        }

        with pytest.raises(ValidationError):
            ser = SetClientInputSerializer(data=data)
            ser.is_valid(raise_exception=True)


@pytest.mark.django_db
@pytest.mark.multi_client
class TestSetClient:
    def test_reg_user(self, data_setup):
        """
        Test set_client for
        - regular user
        """
        ######## DATA SETUP ########
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        ######## TEST ########
        data: Dict = set_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            auth0_user_id="auth0|123456",
            selected_client_id=9871402,
            is_selection_default=True,
            selected_support_membership_id=None,
        )

        ######## ASSERT ########
        # 1. Check if the session client mapping is created
        assert SessionClient.objects.get(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871402,
            membership_id=None,
        )

        # 2. Check if employee config is updated
        employee = Employee.objects.filter(
            employee_email_id="<EMAIL>", client_id=9871402
        ).last()
        assert employee.employee_config["preffered_client_id"] == 9871402

        # 3. Check if the response is as expected
        assert data["ui_action"] == None

    def test_staff_user_tring_to_update_membership_session(self, data_setup):
        """
        Test set_client for
        - staff member trying to update the session client mapping
        - raise exception
        """
        # employees
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871401,
            status="Active",
        )
        create_employee_objects(
            email="<EMAIL>",
            first_name="Ankur",
            last_name="Gupta",
            client_id=9871402,
            status="Active",
        )

        # session client membership mapping
        create_session_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871401,
            support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd161"),
        )

        ######## TEST ########
        with pytest.raises(
            PermissionDenied, match="SWITCHING_SUPPORT_MEMBERSHIP_NOT_ALLOWED"
        ):
            set_client(
                session_id="a925ff73-c550-490e-a453-1266e54c2be8",
                login_email="<EMAIL>",
                email="<EMAIL>",
                is_impersonation=False,
                auth0_user_id="auth0|123456",
                selected_client_id=9871402,
                is_selection_default=True,
                selected_support_membership_id=None,
            )

    def test_support_user(self, data_setup):
        """
        Test set_client for
        - staff member choosing a support membership
        """
        ######## TEST ########
        data: Dict = set_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            auth0_user_id="auth0|123456",
            selected_client_id=9871402,
            is_selection_default=False,
            selected_support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )

        ######## ASSERT ########
        # 1. Check if the session client mapping is created
        assert SessionClient.objects.get(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871402,
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )

        # 2. Check response
        assert data["ui_action"] == None

    @patch("spm.services.multi_client_services.logout_as_user")
    def test_support_user_with_impersonation(self, logout_as_user, data_setup):
        """
        Test set_client for
        - staff member choosing a support membership
        - support user already imporsonated into a user from another client
        """
        # employee (impersonated user) in a different client
        create_employee_objects(
            email="<EMAIL>",
            first_name="Suryavarathan",
            last_name="Muthalagesan",
            client_id=9871401,
            status="Active",
        )

        ######## TEST ########
        logout_as_user.return_value = None
        data: Dict = set_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=True,
            auth0_user_id="auth0|123456",
            selected_client_id=9871402,
            is_selection_default=False,
            selected_support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )

        ######## ASSERT ########
        # 1. Check if the session client mapping is created
        assert SessionClient.objects.get(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871402,
            membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )

        # 2. Check response
        assert data["ui_action"] == "reload"

    def test_support_user_with_existing_session_mapping(self, data_setup):
        """
        Test set_client for
        - staff member choosing a support membership
        - but the session client mapping already exists
        - raise exception
        """
        ######## DATA SETUP ######## # session client membership mapping
        create_session_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            email_id="<EMAIL>",
            client_id=9871401,
            support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )

        ######## TEST ########
        with pytest.raises(
            PermissionDenied, match="SWITCHING_SUPPORT_MEMBERSHIP_NOT_ALLOWED"
        ):
            set_client(
                session_id="a925ff73-c550-490e-a453-1266e54c2be8",
                login_email="<EMAIL>",
                email="<EMAIL>",
                is_impersonation=False,
                auth0_user_id="auth0|123456",
                selected_client_id=9871402,
                is_selection_default=False,
                selected_support_membership_id=UUID(
                    "71cd035f-c101-4e6f-ba12-7fdcaaadd161"
                ),
            )

        assert set_client(
            session_id="a925ff73-c550-490e-a453-1266e54c2be8",
            login_email="<EMAIL>",
            email="<EMAIL>",
            is_impersonation=False,
            auth0_user_id="auth0|123456",
            selected_client_id=9871401,
            is_selection_default=False,
            selected_support_membership_id=UUID("71cd035f-c101-4e6f-ba12-7fdcaaadd162"),
        )
