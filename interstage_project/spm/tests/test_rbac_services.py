from datetime import datetime, timezone
from unittest.mock import call, patch
from uuid import uuid4

import pytest
from deepdiff.diff import DeepDiff

from commission_engine.utils.general_data import COMMISSION_TYPE
from interstage_project.utils import LogWithContext
from spm.accessors.accessor_factories.commission_plan_accessor_factory import (
    PlanSharedDetailsAccessorFactory,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.models.config_models.employee_models import Employee
from spm.models.rbac_models import RolePermissions
from spm.services.rbac_services import (
    clone_role,
    create_role,
    does_user_have_databook_manage_permission,
    edit_role,
    get_all_role_details,
    get_data_permission,
    get_data_permission_cache,
    get_role_details,
    get_ui_permission_cache,
    get_ui_permissions,
    get_user_permissions,
    get_valid_payee_emails,
    get_valid_payee_emails_paginated,
    handle_permissions_admin_ui,
    invalidate_all_shared_plans_for_emp_with_role,
    invalidate_role,
    is_email_in_individual_and_team_data,
    is_own_data_permission,
    is_payout_value_permission,
    is_view_payroll_permission,
    merge_commission_plan_scope,
    merge_data_permissions,
    merge_permission_objects,
    move_and_invalidate_role,
    remove_shared_plan_entries_on_role_change,
    role_permission_details,
)
from spm.tests.models import create_role_permissions


@pytest.fixture()
def create_data():
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="Dinesh",
        last_name="Kumar",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="Oliver",
        last_name="Queen",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="bruce",
        last_name="wayne",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="Clark",
        last_name="Kent",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="Barry",
        last_name="Allen",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    Employee.objects.create(
        client_id=1,
        knowledge_begin_date=datetime.now(),
        employee_email_id="<EMAIL>",
        first_name="diana",
        last_name="prince",
        user_role=["2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6"],
        created_date=datetime.now(),
        created_by="<EMAIL>",
    )
    create_role_permissions()
    RolePermissions.objects.create(
        client_id=1,
        role_permission_id="2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6",
        knowledge_begin_date=datetime.now(),
        permissions={
            "crystal": {"permissions": ["manage:crystal"], "data_permission": None},
            "databooks": {
                "permissions": [
                    "view:databook",
                    "manage:databook",
                ],
                "data_permission": None,
            },
            "queries": {
                "permissions": [
                    "view:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
        },
        display_name="Custom Role 2",
        description="",
        created_at=datetime.now(),
    )


@pytest.mark.django_db
class TestDataPermission:
    @patch(
        "spm.services.rbac_services.get_data_permission_cache_key",
        return_value="cache-key",
    )
    @patch("spm.services.rbac_services.cache.get", return_value={"type": "ALL_DATA"})
    @pytest.mark.parametrize(
        "client_id, email_id, component", [(1, "<EMAIL>", "queries")]
    )
    def test_get_data_permission_with_cache_hit(
        self,
        mock_get_cache_key,
        mock_cache_get,
        create_data,
        client_id,
        email_id,
        component,
    ):
        # Call the function
        data_permission = get_data_permission(client_id, email_id, component)

        # Check the return value
        assert data_permission == {"type": "ALL_DATA"}

    @pytest.mark.parametrize(
        "client_id, email_id, component", [(1, "<EMAIL>", "queries")]
    )
    def test_get_data_permission_with_cache_miss(
        self,
        create_data,
        client_id,
        email_id,
        component,
    ):
        # Call the function
        data_permission = get_data_permission(client_id, email_id, component)
        # Check the cache was set
        assert data_permission == {"type": "ALL_DATA"}

    @patch(
        "spm.services.rbac_services.get_ui_permission_cache_key",
        return_value="cache-key",
    )
    @patch(
        "spm.services.rbac_services.cache.get",
        return_value=["view:dashboard", "view:everstage"],
    )
    @pytest.mark.parametrize("client_id, email_id", [(1, "<EMAIL>")])
    def test_ui_permissions_with_cache_hit(
        self, mock_get_cache_key, mock_cache_get, create_data, client_id, email_id
    ):
        # Call the function
        ui_permissions = get_ui_permissions(client_id, email_id)

        expected_value = ["view:dashboard", "view:everstage"]
        assert DeepDiff(ui_permissions, expected_value, ignore_order=True) == {}

    @pytest.mark.parametrize("client_id, email_id", [(1, "<EMAIL>")])
    def test_ui_permissions_with_cache_miss(self, create_data, client_id, email_id):
        # Call the function
        ui_permissions = get_ui_permissions(client_id, email_id)
        expected_value = [
            "manage:crystal",
            "view:queries",
            "view:dashboard",
            "view:databook",
            "view:everstage",
            "manage:users",
            "view:payroll",
            "view:quotas",
            "view:commissionfeed",
            "view:commissionplan",
            "edit:commissionplan",
            "create:commissionplan",
            "view:payouts",
            "edit:payroll",
        ]
        assert DeepDiff(ui_permissions, expected_value, ignore_order=True) == {}

    @patch("spm.services.rbac_services.RolePermissionsAccessor")
    @pytest.mark.parametrize(
        "client_id,display_name,description,is_name_exists, expected_result",
        [
            (1, "Custom Role Test", "", False, 201),
            (1, "Custom Role Test", "", True, 400),
        ],
    )
    def test_create_role(
        self,
        mock_role_permissions_accessor,
        client_id,
        display_name,
        description,
        is_name_exists,
        expected_result,
    ):
        logger = LogWithContext({})
        audit = {"updated_by": "<EMAIL>"}
        mock_is_name_exist = mock_role_permissions_accessor.return_value.is_name_exist
        mock_is_name_exist.return_value = is_name_exists
        response = create_role(client_id, display_name, description, logger, audit)
        assert response.status_code == expected_result

    @pytest.mark.parametrize(
        "client_id,role_id,display_name,description, expected_result",
        [
            (
                1,
                "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                "Custom Role Test Changed",
                "Test Description",
                201,
            ),
            (
                1,
                None,
                "Custom Role Test Changed",
                "Test Description",
                400,
            ),
        ],
    )
    def test_edit_role(
        self,
        create_data,
        client_id,
        role_id,
        display_name,
        description,
        expected_result,
    ):
        permissions = {
            "crystal": {"permissions": ["manage:crystal"], "data_permission": None},
            "queries": {
                "permissions": [
                    "view:queries",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "dashboard": {
                "permissions": [
                    "view:dashboard",
                ],
                "data_permission": None,
            },
            "databooks": {
                "permissions": [
                    "view:databook",
                ],
                "data_permission": None,
            },
            "everstage": {
                "permissions": ["view:everstage"],
                "data_permission": None,
            },
            "manage_users": {
                "permissions": [
                    "manage:users",
                    "view:payroll",
                ],
                "data_permission": {"type": "ALL_DATA"},
                "impersonated_user_roles": ["ALL"],
            },
            "quotas_draws": {
                "permissions": [
                    "view:quotas",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_feed": {
                "permissions": ["view:commissionfeed"],
                "data_permission": {"type": "ALL_DATA"},
            },
            "commission_plans": {
                "permissions": [
                    "view:commissionplan",
                    "edit:commissionplan",
                    "create:commissionplan",
                ],
                "data_permission": None,
            },
            "payouts_statements": {
                "permissions": [
                    "view:payouts",
                ],
                "data_permission": {"type": "ALL_DATA"},
            },
        }
        logger = LogWithContext({})
        audit = {"updated_by": "<EMAIL>"}
        response = edit_role(
            client_id, role_id, permissions, display_name, description, logger, audit
        )
        assert response.status_code == expected_result
        if response.status_code == 201:
            assert response.data["display_name"] == "Custom Role Test Changed"
            assert response.data["description"] == "Test Description"

    @pytest.mark.parametrize(
        "client_id, role_id, expected_result",
        [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", 201), (1, None, 400)],
    )
    def test_clone_role(self, create_data, client_id, role_id, expected_result):
        logger = LogWithContext({})
        audit = {"updated_by": "<EMAIL>"}
        response = clone_role(client_id, role_id, logger, audit)
        assert response.status_code == expected_result
        if response.status_code == 201:
            assert response.data["display_name"] == "Copy of Custom Role"

    @pytest.mark.parametrize(
        "client_id, role_id, expected_result",
        [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", 201), (1, None, 400)],
    )
    def test_invalidate_role(self, create_data, client_id, role_id, expected_result):
        logger = LogWithContext({})
        audit = {"updated_by": "<EMAIL>"}
        response = invalidate_role(client_id, role_id, logger, audit)
        assert response.status_code == expected_result

    @pytest.mark.parametrize(
        "client_id, role_id,new_role_id, expected_result",
        [
            (
                1,
                "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5",
                "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6",
                201,
            ),
            (1, None, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e6", 400),
            (1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", None, 400),
        ],
    )
    def test_move_and_invalidate_role(
        self, create_data, client_id, role_id, new_role_id, expected_result
    ):
        logger = LogWithContext({})
        audit = {"updated_by": "<EMAIL>"}
        response = move_and_invalidate_role(
            client_id, role_id, logger, audit, new_role_id
        )
        assert response.status_code == expected_result

    def test_get_all_role_details(self):
        logger = LogWithContext({})
        response = get_all_role_details(1, logger)
        assert isinstance(response, list)

    @pytest.mark.parametrize(
        "client_id, role_id, expected_result",
        [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", 201), (1, None, 400)],
    )
    def test_get_role_details(self, create_data, client_id, role_id, expected_result):
        logger = LogWithContext({})
        response = get_role_details(client_id, role_id, logger)
        assert response.status_code == expected_result

    @pytest.mark.parametrize(
        "client_id, role_id, expected_result",
        [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", 201), (1, None, 400)],
    )
    def test_role_permission_details(
        self, create_data, client_id, role_id, expected_result
    ):
        logger = LogWithContext({})
        response = role_permission_details(client_id, role_id, logger)
        assert response.status_code == expected_result

    @patch("spm.services.rbac_services.PermissionsAccessor")
    def test_role_permission_details_without_permissions(
        self, mock_permission_accessor
    ):
        logger = LogWithContext({})
        mock_all_permissions = mock_permission_accessor.return_value.get_all_permissions
        mock_all_permissions.return_value = []
        response = role_permission_details(
            1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5", logger
        )
        assert response.status_code == 400

    @pytest.mark.parametrize(
        "client_id,email_id,expected_result", [(1, "<EMAIL>", True)]
    )
    def test_is_view_payroll_permission(
        self, create_data, client_id, email_id, expected_result
    ):
        is_view_payroll = is_view_payroll_permission(client_id, email_id)
        assert is_view_payroll == expected_result

    @pytest.mark.parametrize(
        "client_id,email_id, expected_result", [(1, "<EMAIL>", False)]
    )
    def test_is_own_data_permission(
        self, create_data, client_id, email_id, expected_result
    ):
        is_own_data = is_own_data_permission(client_id, email_id)
        assert is_own_data == expected_result

    @pytest.mark.parametrize(
        "client_id,email_id, expected_result", [(1, "<EMAIL>", False)]
    )
    def test_is_payout_value_permission(
        self, create_data, client_id, email_id, expected_result
    ):
        is_payout_value = is_payout_value_permission(client_id, email_id)
        assert is_payout_value == expected_result

    @pytest.mark.parametrize(
        "client_id,email_id, expected_result",
        [(1, "<EMAIL>", False), (1, "<EMAIL>", True)],
    )
    def test_does_user_have_databook_manage_permission(
        self, create_data, client_id, email_id, expected_result
    ):
        has_databook_manage_permission = does_user_have_databook_manage_permission(
            client_id, email_id
        )
        assert has_databook_manage_permission == expected_result

    @pytest.mark.parametrize("client_id,email_id", [(1, "<EMAIL>")])
    def test_get_user_permissions(self, create_data, client_id, email_id):
        user_permissions = get_user_permissions(client_id, email_id)
        expected_value = {
            "impersonated_user_roles": ["ALL"],
            "permissions": [
                "manage:crystal",
                "view:queries",
                "view:dashboard",
                "view:databook",
                "view:everstage",
                "manage:users",
                "view:payroll",
                "view:quotas",
                "view:commissionfeed",
                "view:commissionplan",
                "edit:commissionplan",
                "create:commissionplan",
                "view:payouts",
                "edit:payroll",
            ],
            "plans_scope": {},
        }
        assert DeepDiff(user_permissions, expected_value, ignore_order=True) == {}

    @patch(
        "spm.services.rbac_services.get_own_dynamic_flat_team_email_ids",
        return_value=["<EMAIL>", "<EMAIL>"],
    )
    @patch(
        "spm.services.user_group_service.UserGroupMemberService.get_user_group_members_emails",
        return_value=["<EMAIL>", "<EMAIL>"],
    )
    @pytest.mark.parametrize("client_id,email_id", [(1, "<EMAIL>")])
    def test_get_valid_payee_emails(
        self,
        mock_get_user_group_members_emails,
        mock_get_own_dynamic_flat_team_email_ids,
        create_data,
        client_id,
        email_id,
    ):
        data_permission = {
            "type": "INDIVIDUAL_AND_TEAM_DATA",
            "selected_user_groups": ["98046cb0-736c-417c-8007-eaf3e214da14"],
            "is_user_groups_selected": True,
            "is_reporting_team_selected": True,
        }
        valid_payee_emails = get_valid_payee_emails(
            client_id, email_id, data_permission
        )
        assert set(valid_payee_emails) == set(
            [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ]
        )

    @patch("spm.services.rbac_services.cache.get", return_value=None)
    @pytest.mark.parametrize(
        "client_id,role_id", [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5")]
    )
    def test_ui_permission_cache(self, mock_cache_get, create_data, client_id, role_id):
        permissions = get_ui_permission_cache(client_id, role_id, "cache-key")
        assert permissions == [
            "manage:crystal",
            "view:queries",
            "view:dashboard",
            "view:databook",
            "view:everstage",
            "manage:users",
            "view:payroll",
            "edit:payroll",
            "view:quotas",
            "view:commissionfeed",
            "view:commissionplan",
            "edit:commissionplan",
            "create:commissionplan",
            "view:payouts",
        ]

    @patch("spm.services.rbac_services.cache.get", return_value=None)
    @pytest.mark.parametrize(
        "client_id,role_id", [(1, "2e7d875b-6651-4c1d-b5c0-7d39eda7f7e5")]
    )
    def test_get_data_permission_cache(
        self, mock_cache_get, create_data, client_id, role_id
    ):
        component = "queries"
        permissions = get_data_permission_cache(
            client_id, role_id, component, "cache-key"
        )
        assert permissions == {"type": "ALL_DATA"}

    @patch(
        "spm.services.user_group_service.UserGroupMemberService.get_user_group_members_emails",
        return_value=[
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
    )
    @patch("spm.services.rbac_services.check_if_reportee_in_hierarchy")
    @pytest.mark.parametrize(
        "test_id, client_id, login_user_id, email_id",
        [
            (1, 1, "<EMAIL>", "<EMAIL>"),
            (2, 1, "<EMAIL>", "<EMAIL>"),
            (3, 1, "<EMAIL>", "<EMAIL>"),
            (4, 1, "<EMAIL>", "<EMAIL>"),
        ],
    )
    def test_is_email_in_individual_and_team_data(
        self,
        mock_check_if_reportee_in_hierarchy,
        mock_get_user_group_members_emails,
        create_data,
        test_id,
        client_id,
        login_user_id,
        email_id,
    ):
        data_permission = {
            "type": "INDIVIDUAL_AND_TEAM_DATA",
            "selected_user_groups": ["98046cb0-736c-417c-8007-eaf3e214da14"],
            "is_user_groups_selected": True,
            "is_reporting_team_selected": True,
        }
        if test_id == 1:
            mock_check_if_reportee_in_hierarchy.return_value = True
            result = is_email_in_individual_and_team_data(
                client_id, login_user_id, email_id, data_permission
            )
            assert result == True
        elif test_id == 2:
            mock_check_if_reportee_in_hierarchy.return_value = False
            result = is_email_in_individual_and_team_data(
                client_id, login_user_id, email_id, data_permission
            )
            assert result == True
        elif test_id == 3:
            mock_check_if_reportee_in_hierarchy.return_value = True
            result = is_email_in_individual_and_team_data(
                client_id, login_user_id, email_id, data_permission
            )
            assert result == True
        else:
            mock_check_if_reportee_in_hierarchy.return_value = False
            result = is_email_in_individual_and_team_data(
                client_id, login_user_id, email_id, data_permission
            )
            assert result == False

    @patch(
        "spm.services.rbac_services.get_paginated_user_group_members",
        return_value=[
            {
                "full_name": "Barry Allen",
                "employee_email_id": "<EMAIL>",
            },
            {
                "full_name": "Clark Kent",
                "employee_email_id": "<EMAIL>",
            },
            {
                "full_name": "diana prince",
                "employee_email_id": "<EMAIL>",
            },
        ],
    )
    @patch(
        "spm.services.rbac_services.get_reportees_in_hierarchy",
        return_value=[
            {
                "full_name": "Barry Allen",
                "employee_email_id": "<EMAIL>",
                "periods": [
                    {
                        "effective_start_date": datetime(year=2022, month=1, day=1),
                        "effective_end_date": datetime(year=2022, month=3, day=31),
                    },
                    {
                        "effective_start_date": datetime(year=2022, month=7, day=1),
                        "effective_end_date": None,
                    },
                ],
            },
            {
                "full_name": "bruce wayne",
                "employee_email_id": "<EMAIL>",
                "periods": [
                    {
                        "effective_start_date": datetime(year=2022, month=1, day=1),
                        "effective_end_date": datetime(year=2022, month=3, day=31),
                    },
                ],
            },
            {
                "full_name": "Oliver Kent",
                "employee_email_id": "<EMAIL>",
                "periods": [
                    {
                        "effective_start_date": datetime(year=2022, month=1, day=1),
                        "effective_end_date": None,
                    },
                ],
            },
        ],
    )
    @pytest.mark.parametrize(
        "test_id, client_id, email, data_permission",
        [
            (
                1,
                1,
                "<EMAIL>",
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": ["98046cb0-736c-417c-8007-eaf3e214da14"],
                    "is_user_groups_selected": True,
                    "is_reporting_team_selected": False,
                },
            ),
            (
                2,
                1,
                "<EMAIL>",
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": [],
                    "is_user_groups_selected": False,
                    "is_reporting_team_selected": True,
                },
            ),
            (
                3,
                1,
                "<EMAIL>",
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": ["98046cb0-736c-417c-8007-eaf3e214da14"],
                    "is_user_groups_selected": True,
                    "is_reporting_team_selected": True,
                },
            ),
            (
                4,
                1,
                "<EMAIL>",
                {"type": "ALL_DATA"},
            ),
            (
                5,
                1,
                "<EMAIL>",
                {"type": "ALL_DATA"},
            ),
        ],
    )
    def test_get_valid_payee_emails_paginated(
        self,
        mock_get_reportees_in_hierarchy,
        mock_get_paginated_user_group_members,
        create_data,
        test_id,
        client_id,
        email,
        data_permission,
    ):
        limit = 3
        if test_id == 1:
            result = get_valid_payee_emails_paginated(
                client_id,
                email,
                data_permission,
                limit,
                search_term=None,
                full_name_offset=None,
                email_offset=None,
            )
            assert result == [
                {
                    "full_name": "Barry Allen",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "Clark Kent",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "diana prince",
                    "employee_email_id": "<EMAIL>",
                },
            ]
        elif test_id == 2:
            result = get_valid_payee_emails_paginated(
                client_id,
                email,
                data_permission,
                limit,
                search_term=None,
                full_name_offset=None,
                email_offset=None,
            )
            assert result == [
                {
                    "full_name": "Barry Allen",
                    "employee_email_id": "<EMAIL>",
                    "periods": [
                        {
                            "effective_start_date": datetime(year=2022, month=1, day=1),
                            "effective_end_date": datetime(year=2022, month=3, day=31),
                        },
                        {
                            "effective_start_date": datetime(year=2022, month=7, day=1),
                            "effective_end_date": None,
                        },
                    ],
                },
                {
                    "full_name": "bruce wayne",
                    "employee_email_id": "<EMAIL>",
                    "periods": [
                        {
                            "effective_start_date": datetime(year=2022, month=1, day=1),
                            "effective_end_date": datetime(year=2022, month=3, day=31),
                        },
                    ],
                },
                {
                    "full_name": "Oliver Kent",
                    "employee_email_id": "<EMAIL>",
                    "periods": [
                        {
                            "effective_start_date": datetime(year=2022, month=1, day=1),
                            "effective_end_date": None,
                        },
                    ],
                },
            ]
        elif test_id == 3:
            result = get_valid_payee_emails_paginated(
                client_id,
                email,
                data_permission,
                limit,
                search_term=None,
                full_name_offset=None,
                email_offset=None,
            )
            assert result == [
                {
                    "full_name": "Barry Allen",
                    "employee_email_id": "<EMAIL>",
                    "periods": [
                        {
                            "effective_start_date": datetime(year=2022, month=1, day=1),
                            "effective_end_date": datetime(year=2022, month=3, day=31),
                        },
                        {
                            "effective_start_date": datetime(year=2022, month=7, day=1),
                            "effective_end_date": None,
                        },
                    ],
                },
                {
                    "full_name": "bruce wayne",
                    "employee_email_id": "<EMAIL>",
                    "periods": [
                        {
                            "effective_start_date": datetime(year=2022, month=1, day=1),
                            "effective_end_date": datetime(year=2022, month=3, day=31),
                        },
                    ],
                },
                {
                    "full_name": "Clark Kent",
                    "employee_email_id": "<EMAIL>",
                },
            ]
        elif test_id == 4:
            result = get_valid_payee_emails_paginated(
                client_id,
                email,
                data_permission,
                limit,
                search_term=None,
                full_name_offset=None,
                email_offset=None,
            )
            assert result == [
                {
                    "full_name": "Barry Allen",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "bruce wayne",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "Clark Kent",
                    "employee_email_id": "<EMAIL>",
                },
            ]
        else:
            full_name_offset = "bruce wayne"
            email_offset = "<EMAIL>"
            result = get_valid_payee_emails_paginated(
                client_id,
                email,
                data_permission,
                limit,
                full_name_offset=full_name_offset,
                email_offset=email_offset,
            )
            assert result == [
                {
                    "full_name": "Clark Kent",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "diana prince",
                    "employee_email_id": "<EMAIL>",
                },
                {
                    "full_name": "Dinesh Kumar",
                    "employee_email_id": "<EMAIL>",
                },
            ]

    @patch("spm.services.rbac_services.get_client_features")
    @pytest.mark.parametrize(
        "feature,get_client_features, expected_result",
        [
            (
                {"show_approval_feature": "true", "client_id": 9001},
                {"show_approval_feature": False},
                {"view:requestapprovals"},
            ),
        ],
    )
    def test_handle_permissions_admin_ui(
        self, mock_get_client_features, feature, get_client_features, expected_result
    ):
        mock_get_client_features.return_value = get_client_features
        handle_permissions_admin_ui(feature)
        role_permission = get_user_permissions(1, "<EMAIL>")
        assert expected_result.issubset(set(role_permission["permissions"]))

    @pytest.mark.parametrize(
        "merge_permission_list, expected_result",
        [
            (
                [
                    {"type": "INDIVIDUAL_DATA"},
                    {"type": "ALL_DATA"},
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [],
                        "is_user_groups_selected": False,
                        "is_reporting_team_selected": True,
                    },
                ],
                {"type": "ALL_DATA"},
            ),
            (
                [
                    {"type": "INDIVIDUAL_DATA"},
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [],
                        "is_user_groups_selected": False,
                        "is_reporting_team_selected": True,
                    },
                ],
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": [],
                    "is_user_groups_selected": False,
                    "is_reporting_team_selected": True,
                },
            ),
            (
                [
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [
                            "8c34f18e-eeea-463b-9697-17a27b3e3ccf"
                        ],
                        "is_user_groups_selected": True,
                        "is_reporting_team_selected": False,
                    },
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [
                            "4fa4f0ee-d782-481e-a7aa-26dc5dad2074"
                        ],
                        "is_user_groups_selected": True,
                        "is_reporting_team_selected": False,
                    },
                ],
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": [
                        "4fa4f0ee-d782-481e-a7aa-26dc5dad2074",
                        "8c34f18e-eeea-463b-9697-17a27b3e3ccf",
                    ],
                    "is_user_groups_selected": True,
                    "is_reporting_team_selected": False,
                },
            ),
            (
                [
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [
                            "8c34f18e-eeea-463b-9697-17a27b3e3ccf"
                        ],
                        "is_user_groups_selected": True,
                        "is_reporting_team_selected": False,
                    },
                    {
                        "type": "INDIVIDUAL_AND_TEAM_DATA",
                        "selected_user_groups": [],
                        "is_user_groups_selected": False,
                        "is_reporting_team_selected": True,
                    },
                ],
                {
                    "type": "INDIVIDUAL_AND_TEAM_DATA",
                    "selected_user_groups": ["8c34f18e-eeea-463b-9697-17a27b3e3ccf"],
                    "is_user_groups_selected": True,
                    "is_reporting_team_selected": True,
                },
            ),
        ],
    )
    def test_merge_data_permissions(self, merge_permission_list, expected_result):
        result = merge_data_permissions(merge_permission_list)
        assert DeepDiff(result, expected_result, ignore_order=True) == {}

    @pytest.mark.parametrize(
        "merge_permission_object_list, expected_result",
        [
            (
                [
                    {
                        "everstage": {
                            "permissions": ["view:everstage"],
                            "data_permission": None,
                        },
                    },
                    {
                        "quotas_draws": {
                            "permissions": ["view:quotas"],
                            "data_permission": {"type": "ALL_DATA"},
                        },
                    },
                    {
                        "payouts_statements": {
                            "permissions": ["view:payoutvalueothers"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                        }
                    },
                ],
                {
                    "payouts_statements": {
                        "permissions": ["view:payoutvalueothers"],
                        "data_permission": {"type": "INDIVIDUAL_DATA"},
                    },
                    "everstage": {
                        "permissions": ["view:everstage"],
                        "data_permission": {},
                    },
                    "quotas_draws": {
                        "permissions": ["view:quotas"],
                        "data_permission": {"type": "ALL_DATA"},
                    },
                },
            ),
            (
                [
                    {
                        "payouts_statements": {
                            "permissions": ["view:payoutvalueothers"],
                            "data_permission": {"type": "ALL_DATA"},
                        }
                    },
                    {
                        "payouts_statements": {
                            "permissions": ["view:payoutvalueothers"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                        }
                    },
                    {
                        "payouts_statements": {
                            "permissions": ["view:payoutvalueothers"],
                            "data_permission": {
                                "type": "INDIVIDUAL_AND_TEAM_DATA",
                                "selected_user_groups": [],
                                "is_user_groups_selected": False,
                                "is_reporting_team_selected": True,
                            },
                        }
                    },
                ],
                {
                    "payouts_statements": {
                        "permissions": ["view:payoutvalueothers"],
                        "data_permission": {"type": "ALL_DATA"},
                    },
                },
            ),
            (
                [
                    {
                        "manage_users": {
                            "permissions": ["manage:users", "allow:impersonation"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                            "impersonated_user_roles": [
                                "b570a0c3-7214-49c9-834e-e29af0c8752a"
                            ],
                        }
                    },
                    {
                        "manage_users": {
                            "permissions": ["manage:users", "allow:impersonation"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                            "impersonated_user_roles": [
                                "8c34f18e-eeea-463b-9697-17a27b3e3ccf"
                            ],
                        }
                    },
                ],
                {
                    "manage_users": {
                        "permissions": ["manage:users", "allow:impersonation"],
                        "data_permission": {"type": "INDIVIDUAL_DATA"},
                        "impersonated_user_roles": [
                            "8c34f18e-eeea-463b-9697-17a27b3e3ccf",
                            "b570a0c3-7214-49c9-834e-e29af0c8752a",
                        ],
                    }
                },
            ),
            (
                [
                    {
                        "manage_users": {
                            "permissions": ["manage:users", "allow:impersonation"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                            "impersonated_user_roles": [
                                "b570a0c3-7214-49c9-834e-e29af0c8752a",
                                "8c34f18e-eeea-463b-9697-17a27b3e3ccf",
                            ],
                        }
                    },
                    {
                        "manage_users": {
                            "permissions": ["manage:users", "allow:impersonation"],
                            "data_permission": {"type": "INDIVIDUAL_DATA"},
                            "impersonated_user_roles": ["ALL"],
                        }
                    },
                ],
                {
                    "manage_users": {
                        "permissions": ["manage:users", "allow:impersonation"],
                        "data_permission": {"type": "INDIVIDUAL_DATA"},
                        "impersonated_user_roles": ["ALL"],
                    }
                },
            ),
        ],
    )
    def test_merge_permission_objects(
        self, merge_permission_object_list, expected_result
    ):
        result = merge_permission_objects(merge_permission_object_list)
        assert DeepDiff(result, expected_result, ignore_order=True) == {}

    @pytest.mark.parametrize(
        "role_plan_scope_map,expected_result,description",
        [
            (
                {},
                {
                    "can_view": None,
                    "can_edit": None,
                    "can_delete": None,
                },
                "Empty case",
            ),
            (
                {
                    "role1": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    }
                },
                {
                    "can_view": "SHARED_PLANS",
                    "can_edit": "SHARED_PLANS",
                    "can_delete": "SHARED_PLANS",
                },
                "One role - should be returned unchanged",
            ),
            (
                {
                    "role1": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                    "role2": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                },
                {
                    "can_view": "SHARED_PLANS",
                    "can_edit": "SHARED_PLANS",
                    "can_delete": "SHARED_PLANS",
                },
                "Two identical permissions - should remain unchanged",
            ),
            (
                {
                    "role1": {
                        "can_view": None,
                        "can_edit": None,
                        "can_delete": None,
                    },
                    "role2": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                },
                {
                    "can_view": "SHARED_PLANS",
                    "can_edit": "SHARED_PLANS",
                    "can_delete": "SHARED_PLANS",
                },
                "Converting None to Shared 1",
            ),
            (
                {
                    "role1": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                    "role2": {
                        "can_view": None,
                        "can_edit": None,
                        "can_delete": None,
                    },
                },
                {
                    "can_view": "SHARED_PLANS",
                    "can_edit": "SHARED_PLANS",
                    "can_delete": "SHARED_PLANS",
                },
                "Converting None to Shared 2",
            ),
            (
                {
                    "role1": {
                        "can_view": "ALL_PLANS",
                        "can_edit": "ALL_PLANS",
                        "can_delete": "ALL_PLANS",
                    },
                    "role2": {
                        "can_view": None,
                        "can_edit": None,
                        "can_delete": None,
                    },
                },
                {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": "ALL_PLANS",
                },
                "Converting None to All 1",
            ),
            (
                {
                    "role1": {
                        "can_view": None,
                        "can_edit": None,
                        "can_delete": None,
                    },
                    "role2": {
                        "can_view": "ALL_PLANS",
                        "can_edit": "ALL_PLANS",
                        "can_delete": "ALL_PLANS",
                    },
                },
                {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": "ALL_PLANS",
                },
                "Converting None to All 2",
            ),
            (
                {
                    "role1": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                    "role2": {
                        "can_view": "ALL_PLANS",
                        "can_edit": "ALL_PLANS",
                        "can_delete": "ALL_PLANS",
                    },
                },
                {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": "ALL_PLANS",
                },
                "Converting Shared to All 1",
            ),
            (
                {
                    "role1": {
                        "can_view": "ALL_PLANS",
                        "can_edit": "ALL_PLANS",
                        "can_delete": "ALL_PLANS",
                    },
                    "role2": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": "SHARED_PLANS",
                    },
                },
                {
                    "can_view": "ALL_PLANS",
                    "can_edit": "ALL_PLANS",
                    "can_delete": "ALL_PLANS",
                },
                "Converting Shared to All 2",
            ),
            (
                {
                    "role1": {
                        "can_view": None,
                        "can_edit": None,
                        "can_delete": None,
                    },
                    "role2": {
                        "can_view": "ALL_PLANS",
                        "can_edit": "SHARED_PLANS",
                        "can_delete": None,
                    },
                    "role3": {
                        "can_view": "ALL_PLANS",
                        "can_edit": None,
                        "can_delete": None,
                    },
                    "role4": {
                        "can_view": "SHARED_PLANS",
                        "can_edit": None,
                        "can_delete": None,
                    },
                },
                {
                    "can_view": "ALL_PLANS",
                    "can_edit": "SHARED_PLANS",
                    "can_delete": None,
                },
                "More than two plans",
            ),
        ],
    )
    def test_merge_commission_plan_scope(
        self, role_plan_scope_map, expected_result, description
    ):
        result = merge_commission_plan_scope(
            role_plan_scope_map, role_plan_scope_map.keys()
        )
        assert (
            DeepDiff(result, expected_result, ignore_order=True) == {}
        ), f"Test case {description} failed"

    @patch.object(RolePermissionsAccessor, "get_commission_plan_permission_roles")
    @patch("spm.services.rbac_services.merge_commission_plan_scope")
    @patch("spm.services.rbac_services.remove_shared_plan_entries_on_role_change")
    def test_invalidate_all_shared_plans_for_emp_with_role(
        self,
        mock_remove_shared_plan_entries_on_role_change,
        mock_merge_commission_plan_scope,
        mock_get_commission_plan_permission_roles,
    ):
        prev_role_ids = ["id1", "id2"]
        curr_role_ids = ["id1"]
        employee_email_id = "<EMAIL>"

        mock_commission_plan_permission_roles = [
            {
                "role_permission_id": "id1",
                "permissions__commission_plans": {
                    "permissions": [
                        "view:commissionplan",
                        "edit:commissionplan",
                        "create:commissionplan",
                    ],
                    "plans_scope": {
                        "can_edit": "SHARED_PLANS",
                        "can_view": "SHARED_PLANS",
                        "can_delete": None,
                    },
                    "data_permission": None,
                },
            },
            {
                "role_permission_id": "id2",
                "permissions__commission_plans": {
                    "permissions": [
                        "view:commissionplan",
                        "edit:commissionplan",
                        "create:commissionplan",
                    ],
                    "plans_scope": {
                        "can_edit": "ALL_PLANS",
                        "can_view": "ALL_PLANS",
                        "can_delete": None,
                    },
                    "data_permission": None,
                },
            },
        ]
        mock_get_commission_plan_permission_roles.return_value = (
            mock_commission_plan_permission_roles
        )

        mock_merge_commission_plan_scope.side_effect = [
            {
                "can_edit": "ALL_PLANS",
                "can_view": "ALL_PLANS",
                "can_delete": None,
            },
            {
                "can_edit": "SHARED_PLANS",
                "can_view": "SHARED_PLANS",
                "can_delete": None,
            },
        ]

        invalidate_all_shared_plans_for_emp_with_role(
            123, prev_role_ids, curr_role_ids, employee_email_id
        )

        mock_get_commission_plan_permission_roles.assert_called_once()
        expected_role_plan_scope_map = {
            "id1": {
                "can_edit": "SHARED_PLANS",
                "can_view": "SHARED_PLANS",
                "can_delete": None,
            },
            "id2": {
                "can_edit": "ALL_PLANS",
                "can_view": "ALL_PLANS",
                "can_delete": None,
            },
        }
        mock_merge_commission_plan_scope.assert_has_calls(
            [
                call(expected_role_plan_scope_map, prev_role_ids),
                call(expected_role_plan_scope_map, curr_role_ids),
            ]
        )
        mock_remove_shared_plan_entries_on_role_change.assert_called_once_with(
            123,
            {
                "can_edit": "ALL_PLANS",
                "can_view": "ALL_PLANS",
                "can_delete": None,
            },
            {
                "can_edit": "SHARED_PLANS",
                "can_view": "SHARED_PLANS",
                "can_delete": None,
            },
            ["<EMAIL>"],
        )

    @pytest.mark.parametrize(
        "commission_type", [COMMISSION_TYPE.COMMISSION, COMMISSION_TYPE.FORECAST]
    )
    @patch("django.utils.timezone.now")
    def test_remove_shared_plan_entries_on_role_change_update_payee_role_flow(
        self, mock_timezone_now, commission_type
    ):
        # Updating the role of a single payee
        # remove_shared_plan_entries_on_role_change is called with employee_email_ids
        # In this flow role_ids will be None
        mock_kbd = datetime(2024, 7, 11, 6, 0, 0, tzinfo=timezone.utc)
        mock_ked = datetime(2024, 7, 11, 11, 0, 0, tzinfo=timezone.utc)
        mock_timezone_now.return_value = mock_ked
        mock_user = "<EMAIL>"
        client_id = 7008

        # Setup data
        PlanSharedDetailsAccessorFactory(
            client_id, commission_type
        ).get_accessor().create_objects(
            [
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": mock_user,
                },
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": mock_user,
                },
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": mock_user,
                },
            ]
        )

        remove_shared_plan_entries_on_role_change(
            client_id=client_id,
            prev_role_plan_scope={
                "can_view": "SHARED_PLANS",
                "can_edit": "SHARED_PLANS",
                "can_delete": None,
            },
            curr_role_plan_scope={
                "can_view": "SHARED_PLANS",
                "can_edit": None,
                "can_delete": None,
            },
            employee_email_ids=[mock_user],
        )

        invalidated_data = (
            PlanSharedDetailsAccessorFactory(client_id, commission_type)
            .get_accessor()
            .client_aware()
            .filter(target_id=mock_user)
            .values()
        )

        assert len(invalidated_data) == 3, "Mismatch in invalidated data length"
        assert all(
            data["knowledge_end_date"] == mock_ked for data in invalidated_data
        ), "Mismatch in knowledge end date"

        # Cleanup
        PlanSharedDetailsAccessorFactory(
            client_id, commission_type
        ).get_accessor().client_aware().filter(target_id=mock_user).delete()

    @pytest.mark.parametrize(
        "commission_type", [COMMISSION_TYPE.COMMISSION, COMMISSION_TYPE.FORECAST]
    )
    @patch("django.utils.timezone.now")
    @patch.object(EmployeeAccessor, "get_employee_email_ids_by_role")
    def test_remove_shared_plan_entries_on_role_change_edit_or_delete_role_flow(
        self, mock_get_employee_email_ids_by_role, mock_timezone_now, commission_type
    ):
        # Editing or deleting a role
        # Entries of all payees of that role must be invalidated
        # remove_shared_plan_entries_on_role_change is called with role_ids
        # In this employee_email_ids role_ids will be None
        mock_kbd = datetime(2024, 7, 11, 6, 0, 0, tzinfo=timezone.utc)
        mock_ked = datetime(2024, 7, 11, 11, 0, 0, tzinfo=timezone.utc)
        mock_timezone_now.return_value = mock_ked
        mock_get_employee_email_ids_by_role.return_value = [
            "<EMAIL>",
            "<EMAIL>",
        ]

        client_id = 7008

        # Setup data
        PlanSharedDetailsAccessorFactory(
            client_id, commission_type
        ).get_accessor().create_objects(
            [
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": "<EMAIL>",
                },
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": "<EMAIL>",
                },
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": "<EMAIL>",
                },
                {
                    "client_id": client_id,
                    "knowledge_begin_date": mock_kbd,
                    "knowledge_end_date": None,
                    "plan_id": uuid4(),
                    "permission_type": "can_edit",
                    "target_type": "user",
                    "target_id": "<EMAIL>",
                },
            ]
        )

        remove_shared_plan_entries_on_role_change(
            client_id=client_id,
            prev_role_plan_scope={
                "can_view": "SHARED_PLANS",
                "can_edit": "SHARED_PLANS",
                "can_delete": None,
            },
            curr_role_plan_scope={
                "can_view": "SHARED_PLANS",
                "can_edit": None,
                "can_delete": None,
            },
            role_ids=["mock-role-id"],
        )

        mock_get_employee_email_ids_by_role.assert_called_once_with(["mock-role-id"])

        invalidated_data = (
            PlanSharedDetailsAccessorFactory(client_id, commission_type)
            .get_accessor()
            .client_aware()
            .filter(target_id__in=["<EMAIL>", "<EMAIL>"])
            .values()
        )

        assert len(invalidated_data) == 4, "Mismatch in invalidated data length"
        assert all(
            data["knowledge_end_date"] == mock_ked for data in invalidated_data
        ), "Mismatch in knowledge end date"

        # Cleanup
        PlanSharedDetailsAccessorFactory(
            client_id, commission_type
        ).get_accessor().client_aware().filter(
            target_id__in=["<EMAIL>", "<EMAIL>"]
        ).delete()
