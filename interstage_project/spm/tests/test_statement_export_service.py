import json
import os
from decimal import Decimal
from unittest import mock

import pytest
from dateutil.parser import parse
from django.utils.timezone import make_aware
from pandas import Timestamp

from commission_engine.accessors.client_accessor import get_client, save_client
from commission_engine.utils import end_of_day, start_of_day
from spm.services import statements_export_service
from spm.services.datasheet_permission_services import (
    get_visible_columns_for_logged_in_user,
)
from spm.services.localization_services import get_statements_translated_message_service

# List of dictionaries to be used as payout and commisison details (criteria wise)
payout_details = [
    {
        "criteria": [
            {
                "criteria": "primary quota criteria",
                "amount": "29,021.92",
            }
        ],
        "plan_name": "Quarterly main - 2024",
        "payout_details": "29,021.92",
    }
]

# Dictionary to be used as criteria data (Line item level data for each criteria)
criteria_data = {
    "051739cf-3e57-4621-a184-af9d99a656d4": {
        "headers": [
            {
                "headerName": "deal_id",
                "field": "1",
                "maxWidth": 200,
                "actualHeader": "deal_id",
            },
            {
                "headerName": "tierName",
                "field": "2",
                "maxWidth": 200,
                "actualHeader": "tierName",
            },
            {
                "headerName": "Quota Erosion",
                "type": "numericColumn",
                "field": "3",
                "maxWidth": 100,
                "actualHeader": "quota_erosion",
            },
            {
                "headerName": "Commission (INR)",
                "type": "numericColumn",
                "field": "4",
                "maxWidth": 100,
                "actualHeader": "commission",
            },
        ],
        "rows": [
            {"1": "2", "2": "Tier 1", "3": "10933", "4": "₹ 10,938.00"},
            {"1": "2", "2": "Tier 0", "3": "5", "4": "₹ 5,469.00"},
        ],
    }
}


criteria = {
    "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
        "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
        "criteria_type": "CustomSimple",
        "criteria_name": "Criteria 1",
        "criteria_display_order": 1,
        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
        "amount": 3451.62,
        "is_hidden_criteria": False,
    },
    "22a29c5c-4c28-44a5-8769-dfebddd33c95": {
        "criteria_id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
        "criteria_type": "CustomQuota",
        "criteria_name": "Criteria 2",
        "criteria_display_order": 2,
        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
        "amount": 345162.0,
        "is_hidden_criteria": True,
    },
}

commission_adjustments_1 = {
    "adjustments": {
        "headers": [
            {"headerName": "Reason", "field": "1", "maxWidth": 200},
            {"headerName": "Reason Category", "field": "2", "maxWidth": 200},
            {"headerName": "Plan", "field": "3", "maxWidth": 200},
            {"headerName": "Criteria", "field": "4", "maxWidth": 200},
            {"headerName": "Line Item", "field": "5", "maxWidth": 200},
            {
                "headerName": "Commission ( USD )",
                "type": "numericColumn",
                "field": "6",
                "maxWidth": 100,
            },
        ],
        "rows": [
            {"1": "-", "2": "-", "3": "-", "4": "-", "5": "-", "6": "$100.00"},
            {
                "1": "-",
                "2": "CRM Issue",
                "3": "Plan Yearly",
                "4": "Yearly sales",
                "5": "12",
                "6": "$290.00",
            },
        ],
        "total": "390.00",
    }
}
commission_adjustments_2 = {
    "adjustments": {
        "headers": [
            {"headerName": "Reason", "field": "1", "maxWidth": 200},
            {"headerName": "Reason Category", "field": "2", "maxWidth": 200},
            {"headerName": "Plan", "field": "3", "maxWidth": 200},
            {"headerName": "Criteria", "field": "4", "maxWidth": 200},
            {"headerName": "Line Item", "field": "5", "maxWidth": 200},
            {
                "headerName": "Commission ( USD )",
                "type": "numericColumn",
                "field": "6",
                "maxWidth": 100,
            },
        ],
        "rows": [
            {
                "1": "-",
                "2": "Calculation Issue",
                "3": "Settlement",
                "4": "Settlement 1",
                "5": "1",
                "6": "$210.00",
            }
        ],
        "total": "210.00",
    }
}
draw_adjustments = {
    "adjustments": {
        "headers": [
            {"headerName": "Draw Availed", "field": "1", "maxWidth": 200},
            {"headerName": "Draw Recovered", "field": "2", "maxWidth": 200},
            {
                "headerName": "Commission ( USD )",
                "type": "numericColumn",
                "field": "3",
                "maxWidth": 100,
            },
        ],
        "rows": [{"1": "200.00", "2": "0.00", "3": "$200.00"}],
        "total": "200.00",
    }
}
table_headers = {
    "payout_summary": "Payout Summary",
    "payout_by_plan_and_criteria": "Payout by Plan and Criteria",
    "commission_by_plan_and_criteria": "Commission by Plan and Criteria",
    "total_payout": "Total Payout",
    "detailed_payout_insights": "Detailed Payout Insights by Plan and Criteria",
    "detailed_commission_insights": "Detailed Commission Insights by Plan and Criteria",
    "statement_for": "Statement for",
    "commission_summary": "Commission Summary",
    "quota_attainment": "Quota Attainment",
    "payout_from_deferred_period": "from previously deferred commission period",
    "commissions_by_plan_and_criteria": "Commissions",
    "earned_commission": "Earned Commissions",
    "deferred_commission": "Deferred Commissions",
    "payout": "Payout",
    "commission": "Commission",
    "commission_adjustments": "Payout from Commission Adjustments",
    "draw_adjustments": "Payout from Draw Adjustments",
}

table_headers_german = {
    "payout_summary": "Übersicht der Auszahlung",
    "payout_by_plan_and_criteria": "Auszahlung nach Plan und Kriterien",
    "commission_by_plan_and_criteria": "Commission by Plan and Criteria",
    "total_payout": "Gesamte Auszahlung",
    "detailed_payout_insights": "Auszahlung nach Art und Opportunity",
    "detailed_commission_insights": "Detailed Commission Insights by Plan and Criteria",
    "statement_for": "Provisionsabrechnung für Periode",
    "commission_summary": "Commission Summary",
    "quota_attainment": "Quota Attainment",
    "payout_from_deferred_period": "from previously deferred commission period",
    "commissions_by_plan_and_criteria": "Commissions",
    "earned_commission": "Earned Commissions",
    "deferred_commission": "Deferred Commissions",
    "payout": "Payout",
    "commission": "Commission",
    "commission_adjustments": "Payout from Commission Adjustments",
    "draw_adjustments": "Payout from Draw Adjustments",
}

ds_columns = ["co_1_deal_id", "tierName", "quotaErosion", "commission"]


def remove_key(d, key):
    if isinstance(d, dict):
        return {k: remove_key(v, key) for k, v in d.items() if k != key}
    elif isinstance(d, list):
        return [remove_key(item, key) for item in d]
    return d


data = {
    "8b76b324-af44-4ad4-9aa6-a71f3fe59a28": {
        "plan_id": "8b76b324-af44-4ad4-9aa6-a71f3fe59a28",
        "plan_name": "Stat1_Copy",
        "plan_type": "SPIFF",
        "plan_display_order": 2,
        "amount": 348613.62,
        "criteria_details": {
            "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
                "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                "criteria_type": "CustomSimple",
                "criteria_name": "Criteria 1",
                "criteria_display_order": 1,
                "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                "amount": 3451.62,
                "is_hidden_criteria": False,
            },
            "22a29c5c-4c28-44a5-8769-dfebddd33c95": {
                "criteria_id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
                "criteria_type": "CustomQuota",
                "criteria_name": "Criteria 2",
                "criteria_display_order": 2,
                "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                "amount": 345162.0,
                "is_hidden_criteria": True,
            },
        },
    },
}

deferred_data = {
    "July 2023": {
        "period": "July 2023",
        "comm_period": "31/07/2023",
        "amount": 4448.948,
        "plan_details": {
            "8b76b324-af44-4ad4-9aa6-a71f3fe59a28": {
                "plan_id": "8b76b324-af44-4ad4-9aa6-a71f3fe59a28",
                "plan_name": "Stat1_Copy",
                "plan_display_order": None,
                "plan_type": "SPIFF",
                "amount": 348613.62,
                "criteria_details": {
                    "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
                        "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                        "criteria_type": "CustomSimple",
                        "criteria_name": "Criteria 1",
                        "criteria_display_order": 1,
                        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                        "amount": 3451.62,
                    },
                    "22a29c5c-4c28-44a5-8769-dfebddd33c95": {
                        "criteria_id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
                        "criteria_type": "CustomQuota",
                        "criteria_name": "Criteria 2",
                        "criteria_display_order": 2,
                        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                        "amount": 345162.0,
                    },
                },
            },
        },
    },
}

plan_level_data = {
    "earned_commission_details": data,
    "deferred_commission_details": data,
    "current_payout_details": data,
    "previous_commission_deferred_details": deferred_data,
}

line_item_data = {
    "records": [
        {
            "co_3_close_date": Timestamp("2023-07-31 00:00:00"),
            "co_3_deal_amount": 13247.0,
            "co_3_deal_name": "deal 15",
            "co_3_id": "15",
            "co_3_invoice_date": Timestamp("2023-08-12 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "15",
            "commission": 264.94,
        },
        {
            "co_3_close_date": Timestamp("2023-07-27 00:00:00"),
            "co_3_deal_amount": 23849.0,
            "co_3_deal_name": "deal 12",
            "co_3_id": "12",
            "co_3_invoice_date": Timestamp("2023-07-31 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "12",
            "commission": 476.98,
        },
        {
            "co_3_close_date": Timestamp("2023-07-25 00:00:00"),
            "co_3_deal_amount": 41374.0,
            "co_3_deal_name": "deal 14",
            "co_3_id": "14",
            "co_3_invoice_date": Timestamp("2023-07-31 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "14",
            "commission": 827.48,
        },
        {
            "co_3_close_date": Timestamp("2023-07-20 00:00:00"),
            "co_3_deal_amount": 4290.0,
            "co_3_deal_name": "deal 21",
            "co_3_id": "21",
            "co_3_invoice_date": Timestamp("2023-08-13 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "21",
            "commission": 85.8,
        },
        {
            "co_3_close_date": Timestamp("2023-07-04 00:00:00"),
            "co_3_deal_amount": 4379.0,
            "co_3_deal_name": "stat 3",
            "co_3_id": "3",
            "co_3_invoice_date": Timestamp("2023-08-28 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "3",
            "commission": 87.58,
        },
        {
            "co_3_close_date": Timestamp("2023-07-02 00:00:00"),
            "co_3_deal_amount": 75349.0,
            "co_3_deal_name": "stat 4",
            "co_3_id": "5",
            "co_3_invoice_date": Timestamp("2023-07-13 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "5",
            "commission": 1506.98,
        },
        {
            "co_3_close_date": Timestamp("2023-07-01 00:00:00"),
            "co_3_deal_amount": 10093.0,
            "co_3_deal_name": "stat 1",
            "co_3_id": "1",
            "co_3_invoice_date": Timestamp("2023-07-12 00:00:00"),
            "co_3_payee": "<EMAIL>",
            "row_key": "1",
            "commission": 201.86,
        },
    ],
    "columns": [
        "co_3_id",
        "co_3_close_date",
        "co_3_deal_amount",
        "co_3_deal_name",
        "co_3_invoice_date",
        "co_3_payee",
        "commission",
    ],
    "is_line_item_level": True,
    "trace_enabled": False,
}

visible_cols = [
    "co_3_id",
    "co_3_close_date",
    "co_3_deal_amount",
    "co_3_deal_name",
    "co_3_invoice_date",
    "co_3_payee",
    "commission",
]

plan_criteria_for_user_logged = {
    "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
        "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
        "criteria_type": "CustomSimple",
        "criteria_name": "Criteria 1",
        "criteria_display_order": 1,
        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
        "amount": 3451.62,
        "is_hidden_criteria": False,
    },
    "22a29c5c-4c28-44a5-8769-dfebddd33c95": {
        "criteria_id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
        "criteria_type": "CustomQuota",
        "criteria_name": "Criteria 2",
        "criteria_display_order": 2,
        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
        "amount": 345162.0,
        "is_hidden_criteria": True,
    },
}

criteria_table_headers = [
    {"headerName": "id", "type": "text", "field": "1", "actualHeader": "co_3_id"},
    {
        "headerName": "close date",
        "type": "date",
        "field": "2",
        "actualHeader": "co_3_close_date",
    },
    {
        "headerName": "deal amount",
        "type": "number",
        "field": "3",
        "actualHeader": "co_3_deal_amount",
    },
    {
        "headerName": "Royalty (INR)",
        "type": "number",
        "field": "4",
        "actualHeader": "commission",
    },
]

criteria_table_records = [
    {
        "1": "34",
        "2": Timestamp("2023-07-18 00:00:00"),
        "3": "1,436.0",
        "4": "₹2,872.00",
    },
    {
        "1": "37",
        "2": Timestamp("2023-07-13 00:00:00"),
        "3": "5,437.0",
        "4": "₹10,874.00",
    },
    {
        "1": "39",
        "2": Timestamp("2023-07-11 00:00:00"),
        "3": "7,863.0",
        "4": "₹15,726.00",
    },
    {
        "1": "40",
        "2": Timestamp("2023-07-10 00:00:00"),
        "3": "4,256.0",
        "4": "₹8,512.00",
    },
    {
        "1": "31",
        "2": Timestamp("2023-07-05 00:00:00"),
        "3": "5,478.0",
        "4": "₹10,956.00",
    },
]

criteria_level_data = [
    {
        "criteria": [
            {
                "id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                "criteria": "Criteria 1",
                "amount": "3,451.62",
            },
            {
                "id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
                "criteria": "Criteria 2",
                "amount": "345,162.00",
            },
        ],
        "plan_name": "Stat1_Copy",
        "payout_details": "348,613.62",
    },
]

criteria_level_deferred_data = [
    {
        "comission_period": "July 2023",
        "period_key": "july2023",
        "deferred_plan": [
            {
                "criteria": [
                    {
                        "id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                        "criteria": "Criteria 1",
                        "amount": "3,451.62",
                    },
                    {
                        "id": "22a29c5c-4c28-44a5-8769-dfebddd33c95",
                        "criteria": "Criteria 2",
                        "amount": "345,162.00",
                    },
                ],
                "plan_name": "Stat1_Copy",
                "payout_details": "348,613.62",
            },
        ],
    }
]

data_to_format = {
    "criteria_details": {
        "criteria_id": "54d8572a-1ea4-4a44-ae92-87bbb69806a9",
        "criteria_name": "Criteria 1",
        "criteria_display_order": 1,
        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
        "amount": 48940.0,
        "is_hidden_criteria": True,
    },
    "criteria_data": {
        "records": [
            {
                "co_3_close_date": Timestamp("2023-07-18 00:00:00"),
                "co_3_deal_amount": 1436.87631649869,
                "co_3_id": "34",
                "row_key": "34",
                "commission": -2872.0,
            },
            {
                "co_3_close_date": Timestamp("2023-07-13 00:00:00"),
                "co_3_deal_amount": Decimal("121324.124132413"),
                "co_3_id": None,
                "row_key": "37",
                "commission": 10874.0,
            },
            {
                "co_3_close_date": Timestamp("2023-07-11 00:00:00"),
                "co_3_deal_amount": 7863.0,
                "co_3_id": "39",
                "row_key": "39",
                "commission": 0,
            },
            {
                "co_3_close_date": Timestamp("2023-07-10 00:00:00"),
                "co_3_deal_amount": 4256.0,
                "co_3_id": 0,
                "row_key": "40",
                "commission": 8512.0,
            },
            {
                "co_3_close_date": Timestamp("2023-07-05 00:00:00"),
                "co_3_deal_amount": 5478.0,
                "co_3_id": "31",
                "row_key": "31",
                "commission": 10956.0,
            },
        ],
        "columns": [
            "co_3_id",
            "co_3_close_date",
            "co_3_deal_amount",
            "commission",
        ],
        "is_line_item_level": True,
        "trace_enabled": False,
    },
}


@pytest.mark.django_db
@pytest.mark.spm
class TestStatementsExportPdf:
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf,
        "create_previous_deferred_plan_details_sheet",
    )  # Non settlement view previous deferred payout details table
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf,
        "create_deferred_plan_details_sheet",
    )  # Non settlement view deferred commission details table
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf, "create_plan_details_sheet"
    )  # Non settlement view payout summary table
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf,
        "create_current_period_payout_details_sheet",
    )  # Settlement view payout summary table
    @mock.patch.object(
        statements_export_service, "format_criteria_data"
    )  # Format criteria data for pdf
    @pytest.mark.parametrize(
        "client_id,payee_email,psd,ped,login_user_id,is_settlement_view,expected_result",
        [
            (  # statement with payout summary, current_period_payout_details
                3009,
                "<EMAIL>",
                "01-02-2023",
                "30-04-2023",
                "<EMAIL>",
                False,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": [
                            "Type",
                            "Commission ( EUR )",
                            "Payout (%)",
                        ],
                        "rows": [
                            {
                                "type": "Quarterly main - 2024",
                                "commission": "29,021.92",
                                "payout": "580.44%",
                            },
                            {
                                "type": "Total Payout",
                                "commission": "29,021.92",
                                "payout": "580.44%",
                            },
                        ],
                    },
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "commission_summary": {},
                    "quota_attainment": {},
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": [],
                    "earned_commission_details": [],
                    "payee_details": {
                        "name": "Arthur Curry",
                        "email": "<EMAIL>",
                        "designation": None,
                        "period_start_date": "01-Feb-2023",
                        "period_end_date": "30-Apr-2023",
                        "last_calculated": "05 Jun 2023, 03:02AM",
                        "downloaded": "13 Jul 2023, 04:14PM",
                        "status": "Not Frozen",
                        "settlement_status": False,
                        "payout_month": "Q1 (Feb 2023 - Apr 2023)",
                    },
                    "payroll_details": {
                        "pay_currency": "EUR",
                        "payout_frequency": "Quarterly",
                        "variable_pay": 5000.0,
                        "reporting_manager": None,
                        "employment_country": "United Kingdom",
                        "fixed_pay": "0.00",
                        "employee_id": None,
                        "total_payout": "29,021.92",
                        "currency_format": "€",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "custom_fields": {
                        "Reporting Manager": None,
                        "Country": "United Kingdom",
                        "Payout Currency": "EUR",
                        "On-Target Variable Pay": "€5000.0",
                    },
                    "table_headers": table_headers,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers,
                        }
                    ),
                    "adjustments": {},
                },
            ),
            (
                3009,  # statement with payout summary,commission summary, current_period_payout_details, deferred_commission_details, earned_commission_details
                "<EMAIL>",
                "01-02-2023",
                "28-02-2023",
                "<EMAIL>",
                True,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": [
                            "Type",
                            "Commission ( EUR )",
                            "Payout (%)",
                        ],
                        "rows": [
                            {
                                "type": "Payout from current period",
                                "commission": "417,146.47",
                                "payout": "50057.78%",
                            },
                            {
                                "type": "Total Payout",
                                "commission": "417,146.47",
                                "payout": "50057.78%",
                            },
                        ],
                    },
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "commission_summary": {
                        "headers": [
                            "Payout Summary",
                            "Commission ( EUR )",
                            "Payout (%)",
                        ],
                        "rows": [
                            {"type": "Earned Commissions", "commission": "417,146.47"},
                            {"type": "Deferred Commissions", "commission": "-0.00"},
                        ],
                        "table": "Commission Summary",
                    },
                    "quota_attainment": {},
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": payout_details,
                    "earned_commission_details": payout_details,
                    "payee_details": {
                        "name": "Bruce Wayne",
                        "email": "<EMAIL>",
                        "designation": None,
                        "period_start_date": "01-Feb-2023",
                        "period_end_date": "28-Feb-2023",
                        "last_calculated": "05 Jun 2023, 03:01AM",
                        "downloaded": "13 Jul 2023, 05:05PM",
                        "status": "Not Frozen",
                        "settlement_status": True,
                        "payout_month": "February 2023",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "payroll_details": {
                        "pay_currency": "EUR",
                        "payout_frequency": "Monthly",
                        "variable_pay": 833.33,
                        "reporting_manager": None,
                        "employment_country": "United Kingdom",
                        "fixed_pay": "0.00",
                        "employee_id": None,
                        "total_payout": "417,146.47",
                        "currency_format": "€",
                    },
                    "custom_fields": {
                        "Reporting Manager": None,
                        "Country": "United Kingdom",
                        "Payout Currency": "EUR",
                        "On-Target Variable Pay": "€833.33",
                    },
                    "table_headers": table_headers,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "previous_deferred_comm_pdf_criteria_details": (
                                criteria_data,
                                600,
                            ),
                            "deferred_commission_criteria_details": (
                                criteria_data,
                                600,
                            ),
                            "earned_commission_criteria_details": (criteria_data, 600),
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers,
                        }
                    ),
                    "adjustments": {},
                },
            ),
            (
                3009,  # statement with payout summary,quota attainment, current_period_payout_details
                "<EMAIL>",
                "01-05-2023",
                "31-07-2023",
                "<EMAIL>",
                False,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": ["Type", "Commission ( GBP )", "Payout (%)"],
                        "rows": [
                            {
                                "type": "Plan Quarterly",
                                "commission": "1,400.00",
                                "payout": "46.67%",
                            },
                            {
                                "type": "Total Payout",
                                "commission": "1,400.00",
                                "payout": "46.67%",
                            },
                        ],
                    },
                    "commission_summary": {},
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "quota_attainment": {
                        "table": "Quota Attainment",
                        "headers": [
                            "Quota",
                            "Manager/Individual",
                            "Quota Period",
                            "Attained",
                            "Quota Attainment (%)",
                        ],
                        "rows": [
                            {
                                "name": "Primary Quota",
                                "user_role": "As Individual",
                                "quota": "0.00",
                                "quota_period": "NA",
                                "attained": "0.00",
                                "quota_attainment": "0.00%",
                            }
                        ],
                    },
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": [],
                    "earned_commission_details": [],
                    "payee_details": {
                        "name": "quarterly payout",
                        "email": "<EMAIL>",
                        "designation": "SDE 1",
                        "period_start_date": "01-May-2023",
                        "period_end_date": "31-Jul-2023",
                        "last_calculated": "26 Jul 2023, 05:56PM",
                        "downloaded": "26 Jul 2023, 06:34PM",
                        "status": "Not Frozen",
                        "settlement_status": False,
                        "payout_month": "Q2 (May 2023 - Jul 2023)",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "payroll_details": {
                        "pay_currency": "GBP",
                        "payout_frequency": "Quarterly",
                        "variable_pay": 3000.0,
                        "reporting_manager": "Barry Allen",
                        "employment_country": "Netherlands",
                        "fixed_pay": "120,000.00",
                        "employee_id": None,
                        "total_payout": "1,400.00",
                        "currency_format": "£",
                    },
                    "custom_fields": {
                        "Reporting Manager": "Barry Allen",
                        "Country": "Netherlands",
                        "Payout Currency": "GBP",
                        "On-Target Variable Pay": "£3000.0",
                    },
                    "table_headers": table_headers,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers,
                        }
                    ),
                    "adjustments": {},
                },
            ),
            (
                3009,  # statement with payout summary,current_period_payout_details
                "<EMAIL>",
                "01-02-2023",
                "31-07-2023",
                "<EMAIL>",
                False,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": ["Type", "Commission ( USD )", "Payout (%)"],
                        "rows": [
                            {
                                "type": "Plan Halfyearly",
                                "commission": "40,000.00",
                                "payout": "400.00%",
                            },
                            {
                                "type": "Total Payout",
                                "commission": "40,000.00",
                                "payout": "400.00%",
                            },
                        ],
                    },
                    "commission_summary": {},
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "quota_attainment": {},
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": [],
                    "earned_commission_details": [],
                    "payee_details": {
                        "name": "halfyearly payout",
                        "email": "<EMAIL>",
                        "designation": "SDE 2",
                        "period_start_date": "01-Feb-2023",
                        "period_end_date": "31-Jul-2023",
                        "last_calculated": "26 Jul 2023, 05:57PM",
                        "downloaded": "26 Jul 2023, 06:39PM",
                        "status": "Not Frozen",
                        "settlement_status": False,
                        "payout_month": "H1 (Feb 2023 - Jul 2023)",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "payroll_details": {
                        "pay_currency": "USD",
                        "payout_frequency": "Halfyearly",
                        "variable_pay": 10000.0,
                        "reporting_manager": "Barry Allen",
                        "employment_country": "France",
                        "fixed_pay": "200,000.00",
                        "employee_id": None,
                        "total_payout": "40,000.00",
                        "currency_format": "$",
                    },
                    "custom_fields": {
                        "Reporting Manager": "Barry Allen",
                        "Country": "France",
                        "Payout Currency": "USD",
                        "On-Target Variable Pay": "$10000.0",
                    },
                    "table_headers": table_headers,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers,
                        }
                    ),
                    "adjustments": {},
                },
            ),
            (
                3009,  # statement with payout summary, current_period_payout_details, commission adjustments
                "<EMAIL>",
                "01-02-2023",
                "31-01-2024",
                "<EMAIL>",
                False,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": ["Type", "Commission ( USD )", "Payout (%)"],
                        "rows": [
                            {
                                "type": "Plan Yearly",
                                "commission": "7,200.00",
                                "payout": "360.00%",
                            },
                            {
                                "type": "Payout from Commission Adjustments",
                                "commission": "390.00",
                                "payout": "19.50%",
                            },
                            {
                                "type": "Total Payout",
                                "commission": "7,590.00",
                                "payout": "379.50%",
                            },
                        ],
                    },
                    "commission_summary": {},
                    "quota_attainment": {},
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": [],
                    "earned_commission_details": [],
                    "payee_details": {
                        "name": "yearly payout",
                        "email": "<EMAIL>",
                        "designation": "SDE I",
                        "period_start_date": "01-Feb-2023",
                        "period_end_date": "31-Jan-2024",
                        "last_calculated": "26 Jul 2023, 05:58PM",
                        "downloaded": "26 Jul 2023, 06:48PM",
                        "status": "Not Frozen",
                        "settlement_status": False,
                        "payout_month": "FY 2024",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "payroll_details": {
                        "pay_currency": "USD",
                        "payout_frequency": "Annual",
                        "variable_pay": 2000.0,
                        "reporting_manager": None,
                        "employment_country": "United Kingdom",
                        "fixed_pay": "10,000.00",
                        "employee_id": None,
                        "total_payout": "7,590.00",
                        "currency_format": "$",
                    },
                    "custom_fields": {
                        "Reporting Manager": None,
                        "Country": "United Kingdom",
                        "Payout Currency": "USD",
                        "On-Target Variable Pay": "$2000.0",
                    },
                    "table_headers": table_headers,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "commission_adjustments": commission_adjustments_1,
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers,
                        }
                    ),
                    "adjustments": {
                        "commission_adjustments": commission_adjustments_1,
                    },
                },
            ),
            (
                3009,  # statement with payout summary, commission summary, current_period_payout_details, earned commissions, deferred commissions, commission adjustments, draw adjustments
                "<EMAIL>",
                "01-07-2023",
                "31-07-2023",
                "<EMAIL>",
                True,
                {
                    "client_id": 3009,
                    "use_aggrid": False,
                    "payout_summary": {
                        "table": "Payout Summary",
                        "headers": ["Type", "Commission ( USD )", "Payout (%)"],
                        "rows": [
                            {
                                "type": "Payout from current period",
                                "commission": "0.00",
                                "payout": "0.00%",
                            },
                            {
                                "type": "Payout from Commission Adjustments",
                                "commission": "210.00",
                                "payout": "3.07%",
                            },
                            {
                                "type": "Payout from Draw Adjustments",
                                "commission": "200.00",
                                "payout": "2.92%",
                            },
                            {
                                "type": "Gesamte Auszahlung",
                                "commission": "410.00",
                                "payout": "5.99%",
                            },
                        ],
                    },
                    "commission_summary": {
                        "headers": [
                            "Payout Summary",
                            "Commission ( USD )",
                            "Payout (%)",
                        ],
                        "rows": [
                            {
                                "type": "Earned Commissions",
                                "commission": "27,399.33",
                            },
                            {
                                "type": "Deferred Commissions",
                                "commission": "-27,399.33",
                            },
                        ],
                        "table": "Commission Summary",
                    },
                    "quota_attainment": {},
                    "current_pd_payout_details": payout_details,
                    "previous_deferred_comm_pd_payout_details": [],
                    "deferred_commission_details": payout_details,
                    "earned_commission_details": payout_details,
                    "payee_details": {
                        "name": "settlement view",
                        "email": "<EMAIL>",
                        "designation": "SDE",
                        "period_start_date": "01-Jul-2023",
                        "period_end_date": "31-Jul-2023",
                        "last_calculated": "26 Jul 2023, 07:08PM",
                        "downloaded": "26 Jul 2023, 07:10PM",
                        "status": "Not Frozen",
                        "settlement_status": True,
                        "payout_month": "01.07.2023-31.07.2023",
                    },
                    "payout_status_details": {
                        "payout_completion_date": None,
                        "payout_request_date": None,
                        "payout_status": None,
                    },
                    "payroll_details": {
                        "pay_currency": "USD",
                        "payout_frequency": "Monthly",
                        "variable_pay": 6849.83,
                        "reporting_manager": "Stat Pdf",
                        "employment_country": "Albania",
                        "fixed_pay": "190,180.00",
                        "employee_id": None,
                        "total_payout": "410.00",
                        "currency_format": "$",
                    },
                    "custom_fields": {
                        "Reporting Manager": "Stat Pdf",
                        "Country": "Albania",
                        "Payout Currency": "USD",
                        "On-Target Variable Pay": "$6849.83",
                    },
                    "breakdown_summary": {},
                    "show_payout_table_breakdown": False,
                    "table_headers": table_headers_german,
                    "image_url": os.getenv("REACT_APP_EVERSTAGE_ASSETS_URL", ""),
                    "banner": "dark",
                    "json_data": json.dumps(
                        {
                            "commission_adjustments": commission_adjustments_2,
                            "draw_adjustments": draw_adjustments,
                            "previous_deferred_comm_pdf_criteria_details": (
                                criteria_data,
                                600,
                            ),
                            "deferred_commission_criteria_details": (
                                criteria_data,
                                600,
                            ),
                            "earned_commission_criteria_details": (criteria_data, 600),
                            "current_pd_criteria_details": (criteria_data, 600),
                            "headers": table_headers_german,
                        }
                    ),
                    "adjustments": {
                        "commission_adjustments": commission_adjustments_2,
                        "draw_adjustments": draw_adjustments,
                    },
                },
            ),
        ],
    )
    def test_export_statements_pdf(
        self,
        mock_format_criteria_data,
        mock_create_current_period_payout_details_sheet,
        mock_create_plan_details_sheet,
        mock_create_deferred_plan_details_sheet,
        mock_create_previous_deferred_plan_details_sheet,
        client_id,
        payee_email,
        psd,
        ped,
        login_user_id,
        is_settlement_view,
        expected_result,
    ):
        psd = make_aware(start_of_day(parse(psd, dayfirst=True)))
        ped = make_aware(end_of_day(parse(ped, dayfirst=True)))
        statements_data = statements_export_service.get_statements_data(
            client_id=client_id, ped=ped, payee_email=payee_email
        )
        if is_settlement_view:
            mock_create_current_period_payout_details_sheet.return_value = (
                payout_details
            )
            mock_create_plan_details_sheet.return_value = payout_details

            mock_create_deferred_plan_details_sheet.return_value = payout_details
            mock_create_previous_deferred_plan_details_sheet.return_value = []
            mock_format_criteria_data.return_value = criteria_data, 600
        else:
            mock_create_current_period_payout_details_sheet.return_value = []
            mock_create_plan_details_sheet.return_value = payout_details

            mock_create_deferred_plan_details_sheet.return_value = []
            mock_create_previous_deferred_plan_details_sheet.return_value = []
            mock_format_criteria_data.return_value = criteria_data, 600
        # German translation
        if payee_email == "<EMAIL>":
            client = get_client(client_id)
            client.client_features["preferred_language"] = "de"
            save_client(client)
        export_obj = statements_export_service.ExportStatementsPdf(
            client_id, payee_email, psd, ped, login_user_id, is_settlement_view
        )
        context_data = export_obj.export_statements_data(statements_data)
        # if the pdf is downloaded, then the downloaded time will be updated and the test will fail, so removing this line
        del context_data["payee_details"]["downloaded"]
        del expected_result["payee_details"]["downloaded"]

        json_data = json.loads(context_data["json_data"])
        context_data["json_data"] = json.dumps(json_data)

        context_data = remove_key(context_data, "downloaded")

        assert context_data == expected_result

    @mock.patch("spm.services.rbac_services.get_ui_permissions")  # get user permissions
    @pytest.mark.parametrize(
        "client_id, login_user_id,criteria_data, expected_result",
        [
            (
                3009,
                "<EMAIL>",  # payee has no permission to view hidden criteria
                criteria,
                {
                    "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
                        "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                        "criteria_type": "CustomSimple",
                        "criteria_name": "Criteria 1",
                        "criteria_display_order": 1,
                        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                        "amount": 3451.62,
                        "is_hidden_criteria": False,
                    },
                },
            ),
            (
                3009,
                "<EMAIL>",  # superadmin has permission to view hidden
                # criteria but for export case product team doesn't want to show hidden criteria to superadmin also
                criteria,
                {
                    "ddd476ef-7075-43de-8c9f-814ebb233cc0": {
                        "criteria_id": "ddd476ef-7075-43de-8c9f-814ebb233cc0",
                        "criteria_type": "CustomSimple",
                        "criteria_name": "Criteria 1",
                        "criteria_display_order": 1,
                        "datasheet_id": "00a7e118-bbaa-4617-9b74-380098e28a48",
                        "databook_id": "c64a0a5a-1fe9-47bc-b097-b235a852dd38",
                        "amount": 3451.62,
                        "is_hidden_criteria": False,
                    },
                },
            ),
            (
                3009,  # failure case
                "<EMAIL>",  # payee has no permission to view hidden criteria
                criteria,
                criteria,
            ),
        ],
    )
    def test_get_criteria_details_for_logged_in_user(
        self,
        mock_get_ui_permissions,
        client_id,
        login_user_id,
        criteria_data,
        expected_result,
    ):
        if login_user_id == "<EMAIL>":
            mock_get_ui_permissions.return_value = [
                "view:hiddencriteria",
            ]
        else:
            mock_get_ui_permissions.return_value = []
        criteria_data = (
            statements_export_service.get_criteria_details_for_logged_in_user(
                client_id, login_user_id, criteria_data
            )
        )
        if login_user_id == "<EMAIL>":
            assert criteria_data != expected_result
        else:
            assert criteria_data == expected_result

    @mock.patch(
        "spm.services.rbac_services.does_user_have_databook_manage_permission"
    )  # user have databook manage permission or not
    @mock.patch(
        "spm.services.commission_plan_services.get_datasheet_id_from_plan_and_criteria_id"
    )  # get datasheet id from plan and criteria id
    @mock.patch(
        "spm.services.datasheet_permission_services.get_all_hidden_columns_for_user"
    )  # get all hidden columns for user
    @pytest.mark.parametrize(
        "client_id, login_user_id,plan_id, criteria_id, columns,expected_result",
        [
            (
                3009,
                "<EMAIL>",  # payee has no permission to view hidden columns
                "f9c8c0be-4d37-4881-b877-03c5bc11bb4f",
                "be92aa7f-dedd-4162-a1b2-2a7d00bc71b1",
                ds_columns,
                ["co_1_deal_id", "tierName", "commission"],
            ),
            (
                3009,
                "<EMAIL>",  # superadmin has permission to view hidden columns
                "f9c8c0be-4d37-4881-b877-03c5bc11bb4f",
                "be92aa7f-dedd-4162-a1b2-2a7d00bc71b1",
                ds_columns,
                ds_columns,
            ),
            (
                3009,  # failure case
                "<EMAIL>",  # payee has no permission to view hidden columns
                "f9c8c0be-4d37-4881-b877-03c5bc11bb4f",
                "be92aa7f-dedd-4162-a1b2-2a7d00bc71b1",
                ds_columns,
                ds_columns,
            ),
        ],
    )
    def test_get_visible_columns_for_logged_in_user(
        self,
        mock_get_all_hidden_columns_for_user,
        mock_get_datasheet_id_from_plan_and_criteria_id,
        mock_does_user_have_databook_manage_permission,
        client_id,
        login_user_id,
        plan_id,
        criteria_id,
        columns,
        expected_result,
    ):
        if login_user_id == "<EMAIL>":
            mock_does_user_have_databook_manage_permission.return_value = True
        else:
            mock_does_user_have_databook_manage_permission.return_value = False
            mock_get_all_hidden_columns_for_user.return_value = {
                "00a7e118-bbaa-4617-9b74-380098e28a48": [
                    "quotaErosion",  # hidden column
                ]
            }
            mock_get_datasheet_id_from_plan_and_criteria_id.return_value = (
                "00a7e118-bbaa-4617-9b74-380098e28a48"
            )

        visible_columns = get_visible_columns_for_logged_in_user(
            client_id, login_user_id, plan_id, criteria_id, columns
        )

        if login_user_id == "<EMAIL>":
            assert visible_columns != expected_result
        else:
            assert visible_columns == expected_result

    @mock.patch.object(
        statements_export_service, "get_visible_columns_for_logged_in_user"
    )
    @mock.patch.object(statements_export_service, "get_payout_details_for_criteria")
    @mock.patch.object(
        statements_export_service, "get_criteria_details_for_logged_in_user"
    )
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf, "create_criteria_data_table"
    )
    @pytest.mark.parametrize(
        "data,expected_result", [(plan_level_data, criteria_level_data)]
    )
    def test_create_plan_details_sheet(
        self,
        mock_create_criteria_data_table,
        mock_get_criteria_details_for_logged_in_user,
        mock_get_payout_details_for_criteria,
        mock_get_visible_columns_for_logged_in_user,
        data,
        expected_result,
    ):
        mock_get_payout_details_for_criteria.return_value = line_item_data
        mock_get_criteria_details_for_logged_in_user.return_value = (
            plan_criteria_for_user_logged
        )
        mock_get_visible_columns_for_logged_in_user.return_value = visible_cols
        mock_create_criteria_data_table.return_value = (
            criteria_table_headers,
            criteria_table_records,
        )
        plan_details = statements_export_service.ExportStatementsPdf(
            3009,
            "<EMAIL>",
            "01-02-2023",
            "30-04-2023",
            "<EMAIL>",
            False,
        ).create_plan_details_sheet(data)
        assert plan_details == expected_result

    @mock.patch.object(
        statements_export_service, "get_visible_columns_for_logged_in_user"
    )
    @mock.patch.object(statements_export_service, "get_payout_details_for_criteria")
    @mock.patch.object(
        statements_export_service, "get_criteria_details_for_logged_in_user"
    )
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf, "create_criteria_data_table"
    )
    @pytest.mark.parametrize(
        "data,expected_result", [(plan_level_data, criteria_level_data)]
    )
    def test_create_deferred_plan_details_sheet(
        self,
        mock_create_criteria_data_table,
        mock_get_criteria_details_for_logged_in_user,
        mock_get_payout_details_for_criteria,
        mock_get_visible_columns_for_logged_in_user,
        data,
        expected_result,
    ):
        mock_get_payout_details_for_criteria.return_value = line_item_data
        mock_get_criteria_details_for_logged_in_user.return_value = (
            plan_criteria_for_user_logged
        )
        mock_get_visible_columns_for_logged_in_user.return_value = visible_cols
        mock_create_criteria_data_table.return_value = (
            criteria_table_headers,
            criteria_table_records,
        )
        plan_details = statements_export_service.ExportStatementsPdf(
            3009,
            "<EMAIL>",
            "01-02-2023",
            "30-04-2023",
            "<EMAIL>",
            False,
        ).create_deferred_plan_details_sheet(data)
        assert plan_details == expected_result

    @mock.patch.object(
        statements_export_service, "get_visible_columns_for_logged_in_user"
    )
    @mock.patch.object(statements_export_service, "get_payout_details_for_criteria")
    @mock.patch.object(
        statements_export_service, "get_criteria_details_for_logged_in_user"
    )
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf, "create_criteria_data_table"
    )
    @pytest.mark.parametrize(
        "data,expected_result", [(plan_level_data, criteria_level_data)]
    )
    def test_create_current_period_payout_details_sheet(
        self,
        mock_create_criteria_data_table,
        mock_get_criteria_details_for_logged_in_user,
        mock_get_payout_details_for_criteria,
        mock_get_visible_columns_for_logged_in_user,
        data,
        expected_result,
    ):
        mock_get_payout_details_for_criteria.return_value = line_item_data
        mock_get_criteria_details_for_logged_in_user.return_value = (
            plan_criteria_for_user_logged
        )
        mock_get_visible_columns_for_logged_in_user.return_value = visible_cols
        mock_create_criteria_data_table.return_value = (
            criteria_table_headers,
            criteria_table_records,
        )
        plan_details = statements_export_service.ExportStatementsPdf(
            3009,
            "<EMAIL>",
            "01-02-2023",
            "30-04-2023",
            "<EMAIL>",
            False,
        ).create_current_period_payout_details_sheet(data)
        assert plan_details == expected_result

    @mock.patch.object(
        statements_export_service, "get_visible_columns_for_logged_in_user"
    )
    @mock.patch.object(statements_export_service, "get_payout_details_for_criteria")
    @mock.patch.object(
        statements_export_service, "get_criteria_details_for_logged_in_user"
    )
    @mock.patch.object(
        statements_export_service.ExportStatementsPdf, "create_criteria_data_table"
    )
    @pytest.mark.parametrize(
        "data,expected_result", [(plan_level_data, criteria_level_deferred_data)]
    )
    def test_create_previous_deferred_plan_details_sheet(
        self,
        mock_create_criteria_data_table,
        mock_get_criteria_details_for_logged_in_user,
        mock_get_payout_details_for_criteria,
        mock_get_visible_columns_for_logged_in_user,
        data,
        expected_result,
    ):
        mock_get_payout_details_for_criteria.return_value = line_item_data
        mock_get_criteria_details_for_logged_in_user.return_value = (
            plan_criteria_for_user_logged
        )
        mock_get_visible_columns_for_logged_in_user.return_value = visible_cols
        mock_create_criteria_data_table.return_value = (
            criteria_table_headers,
            criteria_table_records,
        )
        plan_details = statements_export_service.ExportStatementsPdf(
            3009,
            "<EMAIL>",
            "01-02-2023",
            "30-04-2023",
            "<EMAIL>",
            False,
        ).create_previous_deferred_plan_details_sheet(data)
        assert plan_details == expected_result

    @pytest.mark.parametrize(
        "criteria_details,criteria_data,expected_result",
        [
            (
                data_to_format["criteria_details"],
                data_to_format["criteria_data"],
                (
                    [
                        {
                            "headerName": "id",
                            "type": "text",
                            "field": "1",
                            "actualHeader": "co_3_id",
                        },
                        {
                            "headerName": "close date",
                            "type": "date",
                            "field": "2",
                            "actualHeader": "co_3_close_date",
                        },
                        {
                            "headerName": "deal amount",
                            "type": "number",
                            "field": "3",
                            "actualHeader": "co_3_deal_amount",
                        },
                        {
                            "headerName": "Commission (INR)",
                            "type": "number",
                            "field": "4",
                            "actualHeader": "commission",
                        },
                    ],
                    [
                        {
                            "1": "34",
                            "2": Timestamp("2023-07-18 00:00:00"),
                            "3": "1,436.88",
                            "4": "-₹2,872.00",
                        },
                        {
                            "1": "-",
                            "2": Timestamp("2023-07-13 00:00:00"),
                            "3": "121,324.12",
                            "4": "₹10,874.00",
                        },
                        {
                            "1": "39",
                            "2": Timestamp("2023-07-11 00:00:00"),
                            "3": "7,863.0",
                            "4": "₹0.00",
                        },
                        {
                            "1": "0",
                            "2": Timestamp("2023-07-10 00:00:00"),
                            "3": "4,256.0",
                            "4": "₹8,512.00",
                        },
                        {
                            "1": "31",
                            "2": Timestamp("2023-07-05 00:00:00"),
                            "3": "5,478.0",
                            "4": "₹10,956.00",
                        },
                    ],
                ),
            )
        ],
    )
    def test_create_criteria_data_table(
        self,
        criteria_details,
        criteria_data,
        expected_result,
    ):
        export_pdf = statements_export_service.ExportStatementsPdf(
            3009,
            "<EMAIL>",
            "01-02-2023",
            "30-04-2023",
            "<EMAIL>",
            False,
        )
        export_pdf.datasheet_variable_map = {
            "00a7e118-bbaa-4617-9b74-380098e28a48": {
                "co_3_deal_name": {"display_name": "deal name", "data_type": 4},
                "co_3_id": {"display_name": "id", "data_type": 4},
                "co_3_close_date": {"display_name": "close date", "data_type": 2},
                "co_3_payee": {"display_name": "payee", "data_type": 12},
                "co_3_invoice_date": {"display_name": "invoice date", "data_type": 2},
                "co_3_deal_amount": {"display_name": "deal amount", "data_type": 1},
            }
        }
        export_pdf.payroll_details["pay_currency"] = "INR"
        export_pdf.payroll_details["currency_format"] = "₹"
        header, rows = export_pdf.create_criteria_data_table(
            criteria_details, criteria_data
        )
        assert (header, rows) == expected_result


class TestTranslationMessage:
    @pytest.mark.django_db
    @pytest.mark.spm
    @pytest.mark.parametrize(
        "message,client_id,language,expected_result",
        [
            ("PAYOUT_SUMMARY", 3009, "en", "Payout Summary"),
            (
                "PAYOUT_SUMMARY",
                3009,
                "de",
                "Übersicht der Auszahlung",
            ),  # valid german translation
            ("TOTAL_PAYOUT", 3009, "en", "Total Payout"),
            (
                "TOTAL_PAYOUT",
                3009,
                "de",
                "Gesamte Auszahlung",
            ),  # valid german translation
            ("PAYOUT_BY_PLAN_AND_CRITERIA", 3009, "en", "Payout by Plan and Criteria"),
            (
                "PAYOUT_BY_PLAN_AND_CRITERIA",
                3009,
                "de",
                "Auszahlung nach Plan und Kriterien",
            ),  # valid german translation
            (
                "COMMISSION_BY_PLAN_AND_CRITERIA",
                3009,
                "en",
                "Commission by Plan and Criteria",
            ),
            (
                "COMMISSION_BY_PLAN_AND_CRITERIA",
                3009,
                "de",
                "Commission by Plan and Criteria",
            ),  # German translation not available, so returning the english message
            (
                "DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA",
                3009,
                "en",
                "Detailed Payout Insights by Plan and Criteria",
            ),
            (
                "DETAILED_PAYOUT_INSIGHTS_BY_PLAN_AND_CRITERIA",
                3009,
                "de",
                "Auszahlung nach Art und Opportunity",
            ),  # valid german translation
            ("STATEMENT_FOR", 2, "en", "Statement for"),
            (
                "STATEMENT_FOR",
                2,
                "de",
                "Provisionsabrechnung für Periode",
            ),  # valid german translation
            (
                "DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA",
                3009,
                "en",
                "Detailed Commission Insights by Plan and Criteria",
            ),
            (
                "DETAILED_COMMISSION_INSIGHTS_BY_PLAN_AND_CRITERIA",
                3009,
                "de",
                "Detailed Commission Insights by Plan and Criteria",
            ),  # German translation not available, so returning the english message
        ],
    )
    def test_get_statements_translated_message_service(
        self, message, client_id, language, expected_result
    ):
        result = get_statements_translated_message_service(message, client_id, language)
        assert result == expected_result


@pytest.mark.parametrize(
    "criteria_details, expected_orderby_fields",
    [
        (
            {
                "criteria_name": "Comm Simple",
                "criteria_type": "CustomSimple",
                "sort_cols": [["co_3_date", "asc"]],
            },
            [{"column": "co_3_date", "order": "asc"}],
        ),
        (
            {
                "criteria_name": "Tier Sorting",
                "criteria_type": "CustomTier",
                "sort_cols": [["amount", "desc"]],
            },
            [
                {"column": "amount", "order": "desc"},
                {"column": "row_key", "order": "asc"},
                {"column": "tierName", "order": "asc"},
            ],
        ),
        (
            {
                "criteria_name": "Quota Sorting",
                "criteria_type": "Quota",
                "sort_cols": [],
            },
            [
                {"column": "row_key", "order": "asc"},
                {"column": "tierName", "order": "asc"},
            ],
        ),
        (
            {
                "criteria_name": "No Sorting",
                "criteria_type": "CustomSimple",
                "sort_cols": [],
            },
            [],
        ),
    ],
)
def test_get_orderby_fields(criteria_details, expected_orderby_fields):
    result = statements_export_service.get_orderby_fields(criteria_details)

    assert sorted(result, key=lambda x: (x["column"], x["order"])) == sorted(
        expected_orderby_fields, key=lambda x: (x["column"], x["order"])
    )
