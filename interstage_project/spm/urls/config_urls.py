from django.urls import path

from spm.views import draws_views
from spm.views.config_views import client_config_views, employee_views
from spm.views.team_views import modify_hierarchy_views, team_views

urlpatterns = [
    path(
        "employee",
        employee_views.EmployeeConfig.as_view(),
        name="employee_config_view",
    ),
    path(
        "deleteemployee",
        employee_views.EmployeeDelete.as_view(),
        name="employee_delete_view",
    ),
    path(
        "employee-v2",
        employee_views.EmployeeConfigV2.as_view(),
        name="employee_config_view_2",
    ),
    path(
        "mappayee",
        employee_views.EmployeePayroll.as_view(),
        name="map_employee_view",
    ),
    path(
        "mappayee/validate",
        employee_views.ValidatePayee.as_view(),
        name="validate_payee",
    ),
    path(
        "mappayee/historical-employee-record/edit",
        employee_views.EditHistoricalEmployeeRecord.as_view(),
        name="edit_historical_employee_records",
    ),
    path(
        "mappayee/historical-employee-record/split",
        employee_views.SplitHistoricalEmployeeRecord.as_view(),
        name="split_historical_employee_records",
    ),
    path(
        "mappayee/historical-employee-record/delete",
        employee_views.DeleteHistoricalEmployeeRecord.as_view(),
        name="delete_historical_employee_records",
    ),
    path(
        "mappayee/hierarchy",
        employee_views.Hierarchy.as_view(),
        name="map_hierarchy_view",
    ),
    path(
        "mappayee/hierarchy/historical-hierarchy/edit",
        employee_views.EditHistoricalHierarchy.as_view(),
        name="edit_historical_hierarchy",
    ),
    path(
        "mappayee/hierarchy/historical-hierarchy/split",
        employee_views.SplitHistoricalHierarchy.as_view(),
        name="split_historical_hierarchy",
    ),
    path(
        "mappayee/hierarchy/historical-hierarchy/delete",
        employee_views.DeleteHistoricalHierarchy.as_view(),
        name="delete_historical_hierarchy",
    ),
    path("teams", team_views.TeamConfigView.as_view(), name="teams_view"),
    path(
        "teams/teamnamevalidation",
        team_views.TeamNameValidation.as_view(),
        name="teams_validation_view",
    ),
    path(
        "teams/membership",
        team_views.MembershipConfigView.as_view(),
        name="members_add_view",
    ),
    path(
        "teams/pod",
        team_views.PODConfigView.as_view(),
        name="members_add_view",
    ),
    path(
        "hierarchyteams",
        team_views.DynamicTeamView.as_view(),
        name="members_view",
    ),
    path(
        "hierarchyteams/modify",
        modify_hierarchy_views.ModifyHierarchyView.as_view(),
        name="members_view",
    ),
    path("bulkadd_old", employee_views.BulkAddOld.as_view(), name="bulk_add_old"),
    path(
        "bulkvalidate_old",
        employee_views.ValidateBulkAddOld.as_view(),
        name="bulk_validate_old",
    ),
    path("bulkadd", employee_views.BulkAdd.as_view(), name="bulk_add"),
    path(
        "bulkvalidate",
        employee_views.ValidateBulkAdd.as_view(),
        name="bulk_validate",
    ),
    path("draws", draws_views.DrawsViewConfig.as_view(), name="draws_view"),
    path(
        "clientconfig",
        client_config_views.ClientConfig.as_view(),
        name="client_config_view",
    ),
    path(
        "freshchat",
        employee_views.SaveFreshchatId.as_view(),
        name="freshchat_view",
    ),
    path(
        "sendinvite",
        employee_views.SendAuthInvite.as_view(),
        name="send_auth_invite",
    ),
    path(
        "updateusersource",
        employee_views.UpdateUserSource.as_view(),
        name="update_user_source",
    ),
    path(
        "updateemployeenamerole",
        employee_views.UpdateEmployeeNameRole.as_view(),
        name="update_employee_name_role",
    ),
    path(
        "updateemployeepassword",
        employee_views.UpdateEmployeePassword.as_view(),
        name="update_employee_password",
    ),
    path(
        "checkupdaterole",
        employee_views.UpdateRoleCanViewerCheck.as_view(),
        name="check_update_role",
    ),
    path("savetags", client_config_views.Tags.as_view(), name="save_tags"),
    path("removetag", client_config_views.RemoveTag.as_view(), name="remove_tag"),
    path(
        "applyuserfilters",
        employee_views.ApplyUserFilters.as_view(),
        name="apply_user_filters",
    ),
    path("users/export", employee_views.UsersExport.as_view(), name="export_users"),
    path(
        "users/get_filtered_emails_list",
        employee_views.GetFilteredUsersEmails.as_view(),
        name="get_filtered_users_email",
    ),
    path(
        "mappayee/validateJoiningDate",
        employee_views.ValidatePayeeJoiningDate.as_view(),
        name="validate_payee_joining_date",
    ),
    path(
        "plan-v2/users/designations",
        employee_views.UsersDesignations.as_view(),
        name="users_designations",
    ),
    path(
        "mappayee/validate_and_remove_user",
        employee_views.ValidateAndRemoveUser.as_view(),
        name="validate_and_delete_user",
    ),
    path(
        "export/remove_user_validations",
        employee_views.ExportRemoveUserValidations.as_view(),
        name="export_remove_user_validations",
    ),
]
