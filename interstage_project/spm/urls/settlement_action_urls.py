from django.urls import path

from spm.views import payout_arrear_views, settlement_actions_views

urlpatterns = [
    path(
        "settlements/update_freeze_status",
        settlement_actions_views.LockStatusView.as_view(),
        name="freeze_status_view",
    ),
    path(
        "settlements/update_paid_status",
        settlement_actions_views.PayoutStatusView.as_view(),
        name="paid_status_view",
    ),
    path(
        "settlements/update_paid_status_v2",
        settlement_actions_views.PayoutStatusViewV2.as_view(),
        name="update_settlement_status_v2",
    ),
    path(
        "settlements/update_settlement_comm_freeze_status",
        settlement_actions_views.SettlmentCommissionLockStatusView.as_view(),
        name="update_settlement_comm_freeze_status",
    ),
    path(
        "settlements/comm_lock_with_pending_changes",
        settlement_actions_views.CommLockWithPendingChangesView.as_view(),
        name="comm_lock_with_pending_changes",
    ),
    path(
        "settlements/revert_payout_transaction",
        settlement_actions_views.RevertPayoutTransactionView.as_view(),
        name="revert_payout_transaction",
    ),
    path(
        "settlements/process_arrears",
        payout_arrear_views.ProcessPayoutArrears.as_view(),
        name="process_arrears",
    ),
    path(
        "settlements/ignore_arrears",
        payout_arrear_views.IgnorePayoutArrears.as_view(),
        name="ignore_arrears",
    ),
    path(
        "apply_payouts_filters",
        settlement_actions_views.PayoutFiltersView.as_view(),
        name="apply_payouts_filters",
    ),
    path(
        "export_payouts_data",
        settlement_actions_views.PayoutsExport.as_view(),
        name="apply_payouts_filters_and_export",
    ),
    path(
        "validate_update_settlement_comm_freeze_status",
        settlement_actions_views.ValidateUnlockPayoutView.as_view(),
        name="validate_update_settlement_comm_freeze_status",
    ),
    path(
        "export_changed_payout_details",
        settlement_actions_views.ExportChangedPayoutDetailsView.as_view(),
        name="export_changed_payout_details",
    ),
]
