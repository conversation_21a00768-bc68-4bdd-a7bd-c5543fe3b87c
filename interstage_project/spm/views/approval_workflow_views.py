import logging
import traceback
from typing import Any, Dict
from uuid import uuid4

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from async_tasks.accessor import AsyncTaskAccessor
from async_tasks.config import AsyncTaskConfig
from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.tasks.approval_workflows import auto_approve_daily_task
from commission_engine.utils.general_data import STATUS_CODE, RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import (
    add_log_context_view,
    get_queue_name_respect_to_task_group,
    handle_ever_exception,
)
from spm.commission_adjustment_approvals.services import approval_workflow_services
from spm.constants.approval_workflow_constants import APPROVAL_ENTITY_TYPES
from spm.serializers.approval_wokflow_serializers import (
    AcceptSubRequestSerializer,
    ApprovalConfigSerializer,
    ApproveRequestSerializer,
    DeclineSubRequestSerializer,
    DeleteInstanceSerializer,
    GetSubRequestsSerializer,
    ReEvaluateInstanceSerializer,
    RejectRequestSerializer,
    RevokeApprovalInstancePayload,
    SubApprovalDataCountSerializer,
    SubApprovalDataForApproverSerializer,
    SubApprovalDataForStageApproverSerializer,
    ValidateGetPlanCriteriaListSerializer,
)
from spm.services.approval_line_items import line_item_services
from spm.services.approval_workflow_services import (
    approval_instance_services,
    approval_workflow_template_services,
)
from spm.services.approval_workflow_services.approvals_settings_services import (
    get_approval_config,
    update_approval_config,
)
from spm.services.commission_plan_approvals.commission_plan_approvals_services import (
    get_approval_templates_by_entity_type,
    get_commission_plan_approvals_status_data,
    get_pending_approvals,
    get_pending_approvals_count,
)

logger = logging.getLogger(__name__)


class CreateApprovalTemplateView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    @add_log_context_view("CreateApprovalTemplateView")
    def post(self, request):
        try:
            request.data["created_by"] = request.user.username
            return approval_workflow_template_services.create_template(
                request.data, request.client_id, request.audit, request.logger
            )
        except Exception as e:
            request.logger.error(
                "Exception while creating approval template - {}".format(
                    request.data["template_name"]
                )
            )
            traceback.print_exc()
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdateApprovalTemplateView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    @add_log_context_view("UpdateApprovalTemplateView")
    def post(self, request):
        try:
            request.data["template_data"]["updated_by"] = request.user.username
            return approval_workflow_template_services.update_template(
                request.data["template_id"],
                request.data["template_data"],
                request.client_id,
                request.audit,
                request.logger,
            )
        except Exception as e:
            request.logger.error(
                "Exception while updating approval template - {}".format(
                    request.data["template_id"]
                )
            )
            traceback.print_exc()
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class DeleteApprovalTemplateView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    @add_log_context_view("DeleteApprovalTemplateView")
    def post(self, request):
        try:
            return approval_workflow_template_services.delete_template(
                request.data["template_id"],
                request.user.username,
                request.client_id,
                request.audit,
                request.logger,
            )
        except Exception as e:
            request.logger.error(
                "Exception while deleting approval template - {}".format(
                    request.data["template_id"]
                )
            )
            traceback.print_exc()
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class CloneApprovalTemplateView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    @add_log_context_view("CloneApprovalTemplateView")
    def post(self, request):
        try:
            return approval_workflow_template_services.clone_template(
                request.data["template_id"],
                request.user.username,
                request.client_id,
                request.audit,
                request.logger,
            )
        except Exception as e:
            request.logger.error(
                "Exception while cloning approval template - {}".format(
                    request.data["template_id"]
                )
            )
            traceback.print_exc()
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class CreateApprovalInstanceView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_REQUESTAPPROVALS.value), name="dispatch"
    )
    @add_log_context_view("CreateApprovalInstanceView")
    def post(self, request):
        try:
            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                AsyncTaskConfig.BULK_CREATE_APPROVAL_INSTANCES.name
            )
            is_auto_mode: bool = request.data.get(
                "is_auto_mode", False
            )  # Gathered from UI
            if async_active_tasks:
                return Response(
                    {
                        "status": "failed",
                        "message": "Another related task is already in progress, please try later!",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if not request.data["bulk_mode"]:
                if is_auto_mode:  # Handles automated approval flow, wont be used now
                    """
                    Here we iteratively handle all the templates based on which instances need to be created
                    with instance_params for each template

                    With 'auto_mode' we can have a mapping of
                    templates to payees, such that we can support multiple templates on instance creation

                    The current structure of 'instance_params' is as follows:

                    - instance_params (dict) - The parameters for creating the approval instances.
                        Eg:{
                                "date": "10/10/2010",
                                "instance_details": [
                                {
                                    "email_id": "<EMAIL>",
                                    "currency": "USD",
                                    "payout": "31508.000000"
                                },
                                {
                                    "email_id": "<EMAIL>",
                                    "currency": "INR",
                                    "payout": "31507.000000"
                                },
                                ],
                            }
                        Here, instance details consists of the payee details for which the approval instance is to be created.

                    With auto mode, we will create a mapping of template_id to instance_params, which will be derived based on expression provided
                    by the user[TODO, Currently mocking with magic_func].

                    template_id_params_map = {'template_1' : {instance_params for this template with its relevant payees},
                                            'template_2': {instance_params for this template with its relevant payees}
                                            }
                    """
                    template_id_params_map: Dict[str, Dict[str, Any]] = (
                        approval_instance_services.resolve_templates_for_payees(
                            request.data["instance_params"]
                        )
                    )
                    for template_id, instance_params in template_id_params_map.items():
                        res = approval_instance_services.create_approval_instance_and_stages(
                            request.client_id,
                            template_id,
                            request.data["entity_type"],
                            instance_params,
                            request.user.username,
                            request.data.get("template_data"),
                            request.audit,
                            is_bulk_mode=request.data["bulk_mode"],
                            logger=request.logger,
                        )
                        if res.data["status"] == "failed":
                            return res  # Check how to introduce transaction integrity, need to make transaction atomic at view level
                else:
                    return (
                        approval_instance_services.create_approval_instance_and_stages(
                            request.client_id,
                            request.data.get("template_id"),
                            request.data["entity_type"],
                            request.data["instance_params"],
                            request.user.username,
                            request.data.get("template_data"),
                            request.audit,
                            is_bulk_mode=request.data["bulk_mode"],
                            logger=request.logger,
                        )
                    )
            task_id = approval_instance_services.initiate_create_approval_instances(
                request.client_id,
                request.data.get("template_id"),
                request.data["entity_type"],
                request.data["instance_params"],
                created_by=request.user.username,
                template_data=request.data.get("template_data"),
                audit_data=request.audit,
                logger=request.logger,
                is_selected_all=request.data.get("is_selected_all", False),
                # search term and filters won't be in request data when is_selected_all
                # is false
                search_term=request.data.get("search_term", None),
                filters=request.data.get("filters", None),
                is_auto_mode=is_auto_mode,  # Defaults to false
            )
            if not task_id:
                return Response(
                    {
                        "status": "failed",
                        "message": "Bulk approval creation task submission failed.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {
                    "status": "success",
                    "message": "Bulk approval creation task submitted.",
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            request.logger.error("Exception while creating approval instance.")
            traceback.print_exc()
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ApproveApprovalRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @add_log_context_view("ApproveApprovalRequestView")
    def post(self, request):
        try:
            is_approve_all = request.data.get("select_all", False)
            if not is_approve_all:
                valid_request_ids = (
                    approval_instance_services.get_valid_request_ids_for_approver_email(
                        request.client_id,
                        request.user.username,
                        request.data["request_id"],
                    )
                )
                if not valid_request_ids:
                    return Response(
                        {
                            "status": "FAILED",
                            "message": "User is not allowed to approve the requests",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )
                request.data["request_id"] = list(map(str, valid_request_ids))
            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                [
                    AsyncTaskConfig.BULK_APPROVE_REQUEST.name,
                    AsyncTaskConfig.BULK_REJECT_REQUEST.name,
                ]
            )
            if (
                request.data.get("entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value)
                not in [
                    APPROVAL_ENTITY_TYPES.QUOTE.value,
                    APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
                ]
                and async_active_tasks
            ):
                return Response(
                    {
                        "status": "FAILED",
                        "message": "Another task running, please try again later",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            async_task = request.data.get("bulk_request", False)
            entity_type = request.data.get(
                "entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value
            )
            if async_task:
                task_id = approval_instance_services.initiate_bulk_approve_task(
                    request.client_id,
                    request.data,
                    request.audit,
                    created_by=request.user.username,
                    entity_type=entity_type,
                )
                return Response(
                    {
                        "status": "SUCCESS",
                        "task_id": task_id,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                approval_instance_services.approve_request(
                    request.client_id,
                    request.data["request_id"],
                    request.audit,
                    request.logger,
                )
                return Response(
                    {
                        "status": "SUCCESS",
                    },
                    status=status.HTTP_201_CREATED,
                )
        except Exception as e:
            traceback.print_exc()
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class RejectApprovalRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @add_log_context_view("RejectApprovalRequestView")
    def post(self, request):
        try:
            is_reject_all = request.data.get("select_all", False)
            if not is_reject_all:
                valid_request_ids = (
                    approval_instance_services.get_valid_request_ids_for_approver_email(
                        request.client_id,
                        request.user.username,
                        request.data["request_id"],
                    )
                )
                if not valid_request_ids:
                    return Response(
                        {
                            "status": "FAILED",
                            "message": "User is not allowed to reject the requests",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )
                request.data["request_id"] = list(map(str, valid_request_ids))
            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                [
                    AsyncTaskConfig.BULK_REJECT_REQUEST.name,
                    AsyncTaskConfig.BULK_APPROVE_REQUEST.name,
                ]
            )
            if (
                request.data.get("entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value)
                not in [
                    APPROVAL_ENTITY_TYPES.QUOTE.value,
                    APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
                ]
                and async_active_tasks
            ):
                return Response(
                    {
                        "status": "FAILED",
                        "message": "Another task running, please try again later",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            async_task = request.data.get("bulk_request", False)
            entity_type = request.data.get(
                "entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value
            )
            if async_task:
                task_id = approval_instance_services.initiate_bulk_reject_task(
                    request.client_id,
                    request.data,
                    request.audit,
                    created_by=request.user.username,
                    entity_type=entity_type,
                )
                return Response(
                    {
                        "status": "SUCCESS",
                        "task_id": task_id,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                approval_instance_services.reject_request(
                    request.client_id, request.data, request.audit, request.logger
                )
                return Response(
                    {
                        "status": "SUCCESS",
                    },
                    status=status.HTTP_201_CREATED,
                )
        except Exception as e:
            error_dict = {
                "trace_back": traceback.print_exc(),
                "request_id": request.data["request_id"],
            }
            request.logger.error(
                "Exception while rejecting approval request - {}".format(error_dict)
            )
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class WithdrawStageRequestsView(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_PAYOUTS.value,
                RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("WithdrawStageRequestsView")
    def post(self, request):
        try:

            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                [
                    AsyncTaskConfig.BULK_REJECT_REQUEST.name,
                    AsyncTaskConfig.BULK_APPROVE_REQUEST.name,
                ]
            )
            if (
                request.data.get("entity_type", APPROVAL_ENTITY_TYPES.PAYOUT.value)
                != APPROVAL_ENTITY_TYPES.QUOTE.value
                and async_active_tasks
            ):
                return Response(
                    {
                        "status": "FAILED",
                        "message": "Another Task is running, please try later.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            approval_instance_services.withdraw_stage_and_move_to_next(
                request.client_id, request.data, request.audit, request.logger
            )
            return Response(
                {
                    "status": "SUCCESS",
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            request.logger.error(
                "Exception while wothdrawing Stage ID: {}".format(
                    request.data["stage_id"]
                )
            )
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class AddApproverStageView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_REQUESTAPPROVALS.value), name="dispatch"
    )
    @add_log_context_view("AddApproverStageView")
    def post(self, request):
        try:
            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                [
                    AsyncTaskConfig.BULK_REJECT_REQUEST.name,
                    AsyncTaskConfig.BULK_APPROVE_REQUEST.name,
                ]
            )
            if async_active_tasks:
                return Response(
                    {
                        "status": "FAILED",
                        "message": "Another Task is running, please try later.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            approval_instance_services.add_approver_to_stage(
                request.client_id,
                request.data,
                request.audit,
                request.logger,
            )
            return Response(
                {
                    "status": "SUCCESS",
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            request.logger.error(
                "Exception while adding approver to Stage ID: {}".format(
                    request.data["stage_id"]
                )
            )
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ChangeStageDueStage(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_PAYOUTS.value,
                RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("ChangeStageDueStage")
    def post(self, request):
        try:
            async_active_tasks = AsyncTaskAccessor(
                client_id=request.client_id
            ).get_active_tasks_by_type(
                [
                    AsyncTaskConfig.BULK_REJECT_REQUEST.name,
                    AsyncTaskConfig.BULK_APPROVE_REQUEST.name,
                ]
            )
            if async_active_tasks:
                return Response(
                    {
                        "status": "FAILED",
                        "message": "Another Task is running, please try later.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            approval_instance_services.change_stage_due_date(
                request.client_id,
                request.data,
                request.user.username,
                request.audit,
                request.logger,
            )
            return Response(
                {
                    "status": "SUCCESS",
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            request.logger.error(
                "Exception while changing due date for Stage ID: {}".format(
                    request.data["stage_id"]
                )
            )
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SendReminderToStageApprovers(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_PAYOUTS.value,
                RbacPermissions.MANAGE_COMMISSIONADJUSTMENT.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("SendReminderToStageApprovers")
    def post(self, request):
        try:
            reminder_sent = approval_instance_services.send_reminder_to_stage_approvers(
                request.client_id, request.data, request.logger
            )
            if not reminder_sent:
                return Response(
                    {"status": "FAILED", "message": "No approvers to remind"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "status": "SUCCESS",
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            request.logger.error(
                "Exception while sending reminder for Stage ID: {}".format(
                    request.data["stage_id"]
                )
            )
            return Response(
                {
                    "status": "FAILED",
                    "message": "Something went wrong. Please try again.",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AutoApproveRequests(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_REQUESTAPPROVALS.value), name="dispatch"
    )
    @add_log_context_view("AutoApproveRequests")
    def post(self, request):
        try:
            auto_approve_daily_task(client_ids=request.data["client_ids"])
            return Response(
                {
                    "status": "SUCCESS",
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            traceback.print_exc()
            request.logger.error(
                "Exception while running auto approve daily task - for client_ids {}".format(
                    request.data["client_ids"]
                )
            )
            return Response(
                {"status": "FAILED", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetPlanCriteriaList(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("GetPlanCriteriaList")
    def post(self, request):
        client_id = request.client_id
        login_user_id = request.user.username

        # Passing request data to serializer for validation
        serializer = ValidateGetPlanCriteriaListSerializer(data=request.data)
        # If the serializer is not valid, then return the error response
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        is_valid_request = True
        if request.data.get("stage_id"):
            is_valid_request = line_item_services.get_timeline_data_permission(
                client_id,
                login_user_id,
                request.data["approver_email"],
            )
        if is_valid_request:
            plan_criteria_list = line_item_services.get_plan_criteria_list(
                client_id=client_id,
                login_user_id=login_user_id,
                logger=logger,
                **serializer.validated_data,
            )
            return Response(
                {
                    "status": "SUCCESS",
                    "plan_criteria_list": plan_criteria_list,
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "status": "FAILED",
                "message": "User is not allowed to approve the requests",
            },
            status=status.HTTP_403_FORBIDDEN,
        )


class AcceptSubRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("AcceptSubRequest")
    def post(self, request):
        client_id = request.client_id
        from spm.services.approval_line_items.line_item_services import (
            accept_sub_request,
            get_valid_request_ids_for_accept_req,
        )

        serializer = AcceptSubRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        logged_in_user = request.user.username
        req_ids = request.data["sub_request_ids"]
        audit_data = request.audit
        valid_request_ids = get_valid_request_ids_for_accept_req(
            client_id, logged_in_user, req_ids, audit_data
        )
        if not valid_request_ids:
            return Response(
                {
                    "status": "FAILED",
                    "message": "User is not allowed to approve the requests",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        valid_request_ids = list(map(str, valid_request_ids))
        return_status = accept_sub_request(
            client_id=client_id,
            sub_request_ids=valid_request_ids,
            logged_in_user=logged_in_user,
            audit_data=audit_data,
        )
        return Response(
            return_status,
            status=status.HTTP_200_OK,
        )


class DeclineSubRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("DeclineSubRequest")
    def post(self, request):
        client_id = request.client_id
        from spm.services.approval_line_items.line_item_services import (
            decline_sub_requests,
            get_valid_request_ids_for_approver_email,
        )

        serializer = DeclineSubRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        sub_req_ids = [
            sub_req["sub_request_id"] for sub_req in request.data["sub_request_details"]
        ]
        valid_request_ids = get_valid_request_ids_for_approver_email(
            request.client_id,
            request.user.username,
            sub_req_ids,
        )
        if not valid_request_ids:
            return Response(
                {
                    "status": "FAILED",
                    "message": "User is not allowed to approve the requests",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        valid_request_ids = list(map(str, valid_request_ids))
        sub_request_data = []
        for sub_req in request.data["sub_request_details"]:
            if sub_req["sub_request_id"] in valid_request_ids:
                sub_request_data.append(sub_req)
        return_status = decline_sub_requests(
            client_id=client_id,
            sub_request_details=sub_request_data,
            audit=request.audit,
        )
        return Response(
            return_status,
            status=status.HTTP_200_OK,
        )


class ApproveRequestOfSubRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("ApproveRequestOfSubRequest")
    def post(self, request):
        client_id = request.client_id
        from spm.services.approval_line_items.line_item_services import approve_request

        serializer = ApproveRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        valid_request_ids = (
            approval_instance_services.get_valid_request_ids_for_approver_email(
                request.client_id,
                request.user.username,
                request.data["request_ids"],
            )
        )
        if not valid_request_ids:
            return Response(
                {
                    "status": "FAILED",
                    "message": "User is not allowed to approve the requests",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        valid_request_ids = list(map(str, valid_request_ids))
        return_status = approve_request(
            client_id=client_id,
            request_ids=valid_request_ids,
            logged_in_user=request.user.username,
            audit=request.audit,
        )
        return Response(
            return_status,
            status=status.HTTP_200_OK,
        )


class RejectRequestOfSubRequestView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("RejectRequestOfSubRequest")
    def post(self, request):
        client_id = request.client_id
        from spm.services.approval_line_items.line_item_services import reject_request

        serializer = RejectRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        valid_request_ids = (
            approval_instance_services.get_valid_request_ids_for_approver_email(
                request.client_id,
                request.user.username,
                request.data["request_ids"],
            )
        )
        if not valid_request_ids:
            return Response(
                {
                    "status": "FAILED",
                    "message": "User is not allowed to reject the requests",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        valid_request_ids = list(map(str, valid_request_ids))
        return_status = reject_request(
            client_id=client_id,
            request_ids=valid_request_ids,
            rejection_reason=request.data["rejection_reason"],
            audit=request.audit,
        )
        return Response(
            return_status,
            status=status.HTTP_200_OK,
        )


class GetApprovalConfig(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("GetApprovalConfig")
    def get(self, request):
        client_id = request.client_id
        approval_config = get_approval_config(client_id=client_id, logger=logger)
        return Response(
            {
                "status": "SUCCESS",
                "data": approval_config,
            },
            status=status.HTTP_200_OK,
        )


class UpdateApprovalConfig(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_APPROVAL_WORKFLOWS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("UpdateApprovalConfig")
    def post(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username
        audit = request.audit
        serializer = ApprovalConfigSerializer(data=request.data["approval_config"])

        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        approval_config = request.data["approval_config"]
        approval_config_status = update_approval_config(
            client_id, approval_config, logged_in_user, audit, logger
        )
        return Response(
            {
                "status": approval_config_status,
                "message": "Approval config updated successfully",
            },
            status=status.HTTP_200_OK,
        )


class SubApprovalDataForApprover(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("SubApprovalDataForApprover")
    def post(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username

        serializer = SubApprovalDataForApproverSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        snapshot_data = line_item_services.get_sub_approval_data_for_approver(
            client_id=client_id,
            logged_in_user=logged_in_user,
            logger=logger,
            **serializer.validated_data,
        )
        return Response(
            {
                "status": "SUCCESS",
                "snapshot_data": snapshot_data,
            },
            status=status.HTTP_200_OK,
        )


class SubApprovalDataForStageApprover(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("SubApprovalDataForStageApprover")
    def post(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username

        serializer = SubApprovalDataForStageApproverSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        is_valid_request = line_item_services.get_timeline_data_permission(
            client_id,
            logged_in_user,
            request.data["approver_email"],
        )
        if is_valid_request:
            snapshot_data = line_item_services.get_sub_approval_data_for_stage_approver(
                client_id=client_id,
                logged_in_user=logged_in_user,
                logger=logger,
                **serializer.validated_data,
            )
            return Response(
                {
                    "status": "SUCCESS",
                    "snapshot_data": snapshot_data,
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "status": "FAILED",
                "message": "User is not allowed to view the request",
            },
            status=status.HTTP_403_FORBIDDEN,
        )


class SubApprovalDataCount(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    @add_log_context_view("SubApprovalDataForApprover")
    def post(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username
        serializer = SubApprovalDataCountSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        is_valid_request = True
        if request.data.get("stage_id"):
            is_valid_request = line_item_services.get_timeline_data_permission(
                client_id,
                logged_in_user,
                request.data["approver_email"],
            )
        if is_valid_request:
            sub_request_count = line_item_services.get_sub_approval_data_count(
                client_id=client_id,
                logged_in_user=logged_in_user,
                logger=logger,
                **serializer.validated_data,
            )
            return Response(
                {
                    "status": "SUCCESS",
                    "total_records": sub_request_count,
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "status": "FAILED",
                "message": "User is not allowed to view the request",
            },
            status=status.HTTP_403_FORBIDDEN,
        )


class PayeeCurrencyList(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value),
        name="dispatch",
    )
    @add_log_context_view("PayeeCurrencyList")
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        payee_currency_list = approval_workflow_services.get_payees_currency_list(
            client_id=client_id,
        )
        return Response(
            {
                "status": "SUCCESS",
                "data": payee_currency_list,
            },
            status=status.HTTP_200_OK,
        )


class DeleteInstances(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        serializer = DeleteInstanceSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        response = approval_instance_services.delete_approval_requests(
            client_id=client_id,
            logged_in_user=request.user.username,
            **serializer.validated_data,
        )
        return Response(response, status=status.HTTP_200_OK)


class ReEvaluateApprovals(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_REQUESTAPPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        serializer = ReEvaluateInstanceSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        is_sub_requests_modified = line_item_services.re_evaluate_approvals(
            client_id=client_id,
            logged_in_user=request.user.username,
            audit_data=request.audit,
            **serializer.validated_data,
        )
        if is_sub_requests_modified:
            return Response(
                {
                    "status": "SUCCESS",
                    "message": "Successfully requested approval for new changes",
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "status": "FAILED",
                "message": "There are no new changes. Cannot request approval again",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class GetSubRequests(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_APPROVALS.value), name="dispatch"
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username
        serializer = GetSubRequestsSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "message": "Input format invalid. Please try again later.",
                    "status": STATUS_CODE.FAILED.value,
                    "error": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        response = line_item_services.get_sub_request_ids_for_all_request_action(
            client_id=client_id,
            logged_in_user=logged_in_user,
            **serializer.validated_data,
        )
        return Response(
            {"sub_request_ids": response, "status": STATUS_CODE.SUCCESS.value},
            status=status.HTTP_200_OK,
        )


class RevokeApprovalInstanceView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @handle_ever_exception
    def post(self, request):

        async_active_tasks = AsyncTaskAccessor(
            client_id=request.client_id
        ).get_active_tasks_by_type(AsyncTaskConfig.BULK_CREATE_APPROVAL_INSTANCES.name)

        if async_active_tasks:
            return Response(
                {
                    "status": "failed",
                    "message": "Another related task is already in progress, please try later!",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        payload_data = {
            "client_id": request.client_id,
            "entity_type": request.data["entity_type"],
            "bulk_mode": request.data["bulk_mode"],
            "instance_params": request.data.get("instance_params"),
            "is_selected_all": request.data["is_selected_all"],
            "filters": request.data.get("filters"),
            "search_term": request.data.get("search_term"),
            "audit_data": {
                "updated_by": request.user.username,
                "logged_in_user": request.user.username,
                "date": request.data.get("date"),
            },
            "comments": request.data.get("comments"),
        }
        RevokeApprovalInstancePayload.model_validate(payload_data)

        if not payload_data["is_selected_all"]:
            instance_params = payload_data["instance_params"]
            approval_instance_services.revoke_instances(
                payload_data["client_id"],
                payload_data["entity_type"],
                instance_params,
                payload_data["comments"],
                {
                    "updated_by": request.user.username,
                    "logged_in_user": request.user.username,
                    "date": request.data.get("date"),
                },
            )
            return Response(
                {
                    "status": "success",
                    "message": "Approval instance revoked sucessfully.",
                },
                status=status.HTTP_200_OK,
            )
        subscription_plan = get_client_subscription_plan(payload_data["client_id"])
        misc_queue_name = get_queue_name_respect_to_task_group(
            payload_data["client_id"],
            subscription_plan,
            TaskGroupEnum.MISC.value,
        )

        approval_instance_services.bulk_revoke_approval_instances.si(
            **payload_data
        ).set(queue=misc_queue_name).apply_async()

        return Response(
            {
                "status": "success",
                "message": "Bulk approval revoking task submitted.",
            },
            status=status.HTTP_202_ACCEPTED,
        )


class GetApprovalStatusBanner(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @handle_ever_exception
    def get(self, request, entity_key):
        client_id = request.client_id
        logged_in_user = request.user.username

        response = get_commission_plan_approvals_status_data(
            client_id=client_id,
            plan_id=entity_key,
            logged_in_user=logged_in_user,
        )
        return Response(
            {"status": "SUCCESS", "data": response},
            status=status.HTTP_200_OK,
        )


class GetPendingApprovals(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username

        response = get_pending_approvals(
            client_id=client_id,
            logged_in_user=logged_in_user,
        )
        return Response(
            {"status": "SUCCESS", "data": response},
            status=status.HTTP_200_OK,
        )


class GetPendingApprovalsCount(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        logged_in_user = request.user.username

        response = get_pending_approvals_count(
            client_id=client_id,
            logged_in_user=logged_in_user,
        )
        return Response(
            {"status": "SUCCESS", "data": response},
            status=status.HTTP_200_OK,
        )


class GetApprovalTemplates(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @handle_ever_exception
    def get(self, request, entity_key):
        client_id = request.client_id
        logged_in_user = request.user.username
        response = get_approval_templates_by_entity_type(
            client_id=client_id,
            logged_in_user=logged_in_user,
            entity_type=APPROVAL_ENTITY_TYPES.COMMISSION_PLAN.value,
            entity_key=entity_key,
        )
        return Response(
            {"status": "SUCCESS", "data": response},
            status=status.HTTP_200_OK,
        )
