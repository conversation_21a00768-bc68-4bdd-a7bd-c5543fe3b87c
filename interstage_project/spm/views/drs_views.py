import logging

from django.http import JsonResponse
from django.utils.decorators import method_decorator
from pydantic import ValidationError
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import (
    RBAC,
    RESPONSE__PERMISSION_DENIED,
    RBACComponent,
    RbacPermissions,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view, handle_ever_exception
from spm.accessors.drs_accessor import DrsAccessor
from spm.serializers import Drs<PERSON>erializer, DrsUpdatesSerializer
from spm.services import drs_services
from spm.services.rbac_services import get_data_permission, get_valid_payee_emails

logger = logging.getLogger(__name__)


class DrsViewConfig(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.CREATE_QUERIES.value), name="dispatch"
    )
    @add_log_context_view("CreateQuery")
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        try:
            drs_data = drs_services.DRSParams(**request.data)
        except ValidationError as e:
            logger.exception("Error in DRS data validation: %s", e.errors())
            return JsonResponse(
                {"error": e.errors()}, status=status.HTTP_400_BAD_REQUEST
            )

        res = drs_services.persist_drs(client_id, drs_data, request.audit)
        if res["success"]:
            return Response(res["data"], status=status.HTTP_201_CREATED)
        else:
            return JsonResponse(
                {"error": res["error"]}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DrsUpdatesViewConfig(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.EDIT_QUERIES.value), name="dispatch"
    )
    @add_log_context_view("UpdateQuery")
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        drs_id = request.GET["drs_id"]
        # Directly construct the model without validation
        # TODO (@aravindm711es): Validate the request data in next iteration
        drs_update_data = drs_services.DRSParams.model_construct(
            _fields_set=set(request.data.keys()), **request.data
        )

        login_user_id = request.user.username
        data_permission = get_data_permission(
            client_id, login_user_id, RBACComponent.QUERIES.value
        )

        if not data_permission:
            return JsonResponse(
                {"message": "You don't have permission to access this data"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, login_user_id, data_permission
            )
            drs = DrsAccessor(client_id).get_drs_id_by_data_permission(
                drs_id, payee_emails
            )
            if not drs:
                return JsonResponse(
                    {"message": "You don't have permission to access this data"},
                    status=status.HTTP_403_FORBIDDEN,
                )

        res = drs_services.persist_drs_updates(
            client_id, drs_id, drs_update_data, request.audit
        )
        if res["success"]:
            return Response(res["data"], status=status.HTTP_201_CREATED)
        else:
            return JsonResponse(
                {"error": res["error"]}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetAssigneeUserList(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.CREATE_QUERIES.value), name="dispatch"
    )
    @add_log_context_view("GetAssigneeUserList")
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        employee_email_id = request.user.username

        response = drs_services.get_assignee_user_list(client_id, employee_email_id)
        return Response(response, status=status.HTTP_200_OK)


class GetQueryAdminList(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.CREATE_QUERIES.value), name="dispatch"
    )
    @add_log_context_view("GetQueryAdminList")
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        admin_list = drs_services.get_query_admins(client_id)
        email_name_map = drs_services.filter_support_user_and_get_email_name_map(
            client_id, admin_list
        )
        return Response(email_name_map, status=status.HTTP_200_OK)
