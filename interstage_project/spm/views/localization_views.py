import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_ever_exception
from spm.services.localization_services import (
    delete_domain_specific_attributes,
    get_domain_specific_attributes,
    get_theme_specific_attributes,
    update_domain_specific_attributes,
    update_theme_specific_attributes,
)

logger = logging.getLogger(__name__)


class LocalizationView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    def get(self, request):
        try:
            client_domain_specific_attributes = get_domain_specific_attributes(
                request.client_id
            )
            lng = request.query_params.get("lng")
            return Response(
                client_domain_specific_attributes[lng], status=status.HTTP_200_OK
            )
        except Exception:
            logger.exception("Error in get_client_domain_specific_attributes_service:")
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    def post(self, request):
        try:
            domain_specific_attributes = request.data
            # Converting the attributes to Uppercase
            for language, attributes in domain_specific_attributes.items():
                upper_case_attributes = {}
                for key in attributes.keys():
                    upper_case_attributes[key.upper()] = attributes[key]
                domain_specific_attributes[language] = upper_case_attributes
            message = update_domain_specific_attributes(
                request.client_id, domain_specific_attributes, remove_empty_keys=True
            )
            return Response(status=status.HTTP_200_OK, data={"message": message})
        except Exception:
            logger.exception(
                "Error in update_client_domain_specific_attributes_service:"
            )
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    )
    def delete(self, request):
        try:
            domain_specific_attributes_to_delete = request.data["keys"]
            delete_domain_specific_attributes(
                request.client_id, domain_specific_attributes_to_delete
            )
            return Response(status=status.HTTP_200_OK)
        except Exception:
            logger.exception(
                "Error in delete_client_domain_specific_attributes_service:"
            )
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomThemeView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @handle_ever_exception
    def get(self, request):
        client_theme_specific_attributes = get_theme_specific_attributes(
            request.client_id
        )
        return Response(client_theme_specific_attributes, status=status.HTTP_200_OK)

    # Uncomment this once the feature is ready and proper permission is added.
    # @method_decorator(
    #     requires_scope(RbacPermissions.MANAGE_CONFIG.value), name="dispatch"
    # )
    # @handle_ever_exception
    # def post(self, request):
    #     message = update_theme_specific_attributes(request.client_id, request.data)
    #     return Response(status=status.HTTP_200_OK, data={"message": message})
