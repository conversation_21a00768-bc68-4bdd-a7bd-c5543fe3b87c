import logging
import traceback
from datetime import datetime

from django.core.cache import cache
from django.db import transaction
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from ua_parser import user_agent_parser

from commission_engine.accessors.client_accessor import get_client
from commission_engine.services.payee_notification_service import (
    get_active_slack_tasks_for_payee,
)
from commission_engine.services.payout_status_changes_service import (
    add_payee_status_details_for_payout_status_changes,
)
from commission_engine.utils.cache_utils import (
    get_selected_client_in_cache_for_employee,
)
from commission_engine.utils.general_data import (
    RBAC,
    LoginMethods,
    PayoutStatusChangesTypes,
    RBACComponent,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
)
from interstage_project.auth_management_api import login_as_user, logout_as_user
from interstage_project.auth_utils import get_token_auth_header, requires_scope
from interstage_project.jwt_handler import jwt_hard_decode_token
from interstage_project.session_utils import restricted_usage___get_login_user
from interstage_project.utils import LogWithContext, add_log_context_view
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.employee_accessor_v2 import EmployeeWriteAccessor
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.audit_services import (
    login_as_audit,
    login_audit,
    logout_as_audit,
    logout_audit,
)
from spm.services.config_services.employee_services import get_user_status
from spm.services.rbac_services import (
    get_data_permission,
    get_payee_role_id,
    get_role_display_name,
    get_user_permissions,
    get_valid_payee_emails,
)
from spm.services.session_management_services import session_management_for_logout

logger = LogWithContext()


class LoginView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @transaction.atomic
    def post(self, request):
        """
        - Logs a record in audit trail when the user logs in to the application
        - Updates the segment user_properties for the logged in user
        - Triggers Segment Login event
        """
        client_id = request.client_id
        email = request.user.username
        is_login_as = request.data["is_login_as"]
        time = timezone.now()
        try:
            employee = EmployeeAccessor(client_id).get_employee(email)
            if not is_login_as and employee and employee.status == "Invited":
                EmployeeWriteAccessor(client_id).update_status(
                    email, "Active", timezone.now()
                )

                # Add payout status changes for the payee as status changed to active
                add_payee_status_details_for_payout_status_changes(
                    client_id=client_id,
                    payee_emails=[email],
                    event_type=PayoutStatusChangesTypes.ACTIVE_PAYEE.value,
                )

            # updating segment user_properties if changed
            client = get_client(client_id)
            has_reportees = HierarchyAccessor(client_id).reportees_exist_for_email(
                email, timezone.now()
            )
            payroll = EmployeePayrollAccessor(
                client_id
            ).get_valid_record_for_given_date(
                knowledge_date=time,
                effective_date=time,
                employee_email_id=email,
            )
            exit_date = employee.exit_date
            deactivation_date = employee.deactivation_date
            emp_status = get_user_status(
                emp_status="Active",
                exit_date=exit_date,
                deactivation_date=deactivation_date,
            )

            emp_config = getattr(employee, "employee_config", {})
            slack_connected = (
                True if emp_config and emp_config.get("slack_user_id") else False
            )

            # While updating a property of list type, it appends the values to the existing list,
            # so sending an empty array before updating

            user_data = {
                "user_id": email,
                "user_properties": {
                    SegmentProperties.SLACK_NOTIFICATION_FREQ.value: []
                },
            }
            # flushing existing list of message due to list update
            analyser = CoreAnalytics(analyser_type="segment")
            analyser.register_user(user_data)

            tasks = get_active_slack_tasks_for_payee(client_id, email)
            task_freq = [task.frequency for task in tasks]
            role_display_name = get_role_display_name(client_id, employee.user_role)

            # Parse metadata of incoming request for segment
            x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
            client_ip_addr = (
                x_forwarded_for.split(",")[0]
                if x_forwarded_for
                else request.META.get("REMOTE_ADDR")
            )

            user_agent = user_agent_parser.Parse(request.META["HTTP_USER_AGENT"])
            browser = (
                user_agent["user_agent"].get("family", "")
                + " "
                + user_agent["user_agent"].get("major", "")
                + "."
                + user_agent["user_agent"].get("minor", "")
                + "."
                + user_agent["user_agent"].get("patch", "")
            )
            device = (
                user_agent["device"].get("family", "")
                + " "
                + user_agent["os"].get("family", "")
            )

            analytics_data = {
                "user_id": email,
                "user_properties": {
                    SegmentProperties.IP_ADDRESS.value: client_ip_addr,
                    SegmentProperties.BROWSER.value: browser,
                    SegmentProperties.DEVICE.value: device,
                    SegmentProperties.NAME.value: f"{employee.first_name} {employee.last_name}",
                    SegmentProperties.EMAIL.value: email,
                    SegmentProperties.CLIENT_ID.value: client_id,
                    SegmentProperties.CLIENT.value: client.name,
                    SegmentProperties.IS_MANAGER.value: has_reportees,
                    SegmentProperties.ROLE.value: role_display_name,
                    SegmentProperties.EMPLOYEE_ID.value: getattr(
                        payroll, "employee_id", None
                    ),
                    SegmentProperties.CRYSTAL_ACCESS.value: (
                        "Yes"
                        if getattr(payroll, "payee_role", "") == "Revenue"
                        else "No"
                    ),
                    SegmentProperties.STATUS.value: emp_status,
                    SegmentProperties.EMPLOYMENT_COUNTRY.value: getattr(
                        payroll, "employment_country", None
                    ),
                    SegmentProperties.JOINING_DATE.value: (
                        str(getattr(payroll, "joining_date", None))
                        if getattr(payroll, "joining_date", None) is not None
                        else None
                    ),
                    SegmentProperties.EFFECTIVE_START_DATE.value: (
                        str(getattr(payroll, "effective_start_date", None))
                        if getattr(payroll, "effective_start_date", None) is not None
                        else None
                    ),
                    SegmentProperties.EXIT_DATE.value: (
                        str(getattr(payroll, "exit_date", None))
                        if getattr(payroll, "exit_date", None) is not None
                        else None
                    ),
                    SegmentProperties.EFFECTIVE_END_DATE.value: (
                        str(getattr(payroll, "effective_end_date", ""))
                        if getattr(payroll, "effective_end_date", None) is not None
                        else None
                    ),
                    SegmentProperties.DESIGNATION.value: getattr(
                        payroll, "designation", None
                    ),
                    SegmentProperties.LEVEL.value: getattr(payroll, "level", None),
                    SegmentProperties.PAY_CURRENCY.value: getattr(
                        payroll, "pay_currency", None
                    ),
                    SegmentProperties.PAYOUT_FREQUENCY.value: getattr(
                        payroll, "payout_frequency", None
                    ),
                    SegmentProperties.SLACK_CONNECTED.value: slack_connected,
                    SegmentProperties.SLACK_NOTIFICATION_FREQ.value: tuple(task_freq),
                },
            }
            analyser.register_user(analytics_data)
            # segment login event
            con_provider = None
            token = get_token_auth_header(request)
            if token:
                decoded = jwt_hard_decode_token(token)
                if decoded.get("con_provider"):
                    con_provider = decoded.get("con_provider")
                # Logging time taken to login
                if decoded.get("iat"):
                    iat = make_aware(datetime.utcfromtimestamp(decoded.get("iat")))
                    time_diff = (time - iat).total_seconds()
                    logger.update_context(
                        {"client_id": client_id, "login_time_taken": time_diff}
                    )
                    logger.info(f"Time Taken to Login: {time_diff} seconds")
            connection_type = None
            if con_provider == LoginMethods.GOOGLE.value:
                connection_type = "Google"
            elif con_provider == LoginMethods.AUTH0.value:
                connection_type = "E-mail"
            elif con_provider == LoginMethods.SALESFORCE.value:
                connection_type = "Salesforce"

            analytics_data = {
                "user_id": email,
                "event_name": SegmentEvents.LOGIN.value,
                "event_properties": {
                    SegmentProperties.LOGIN_METHOD.value: connection_type,
                    SegmentProperties.PLATFORM_USED.value: "Web",
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            return login_audit(client_id, email, is_login_as)
        except Exception as e:
            logging.error(e, traceback.print_exc())


class LoginAsView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.ALLOW_IMPERSONATION.value), name="dispatch"
    )
    @transaction.atomic
    def post(self, request):
        """
        - Logs a record in audit trail when the user logged in as another user to the application
        """
        client_id = request.client_id
        login_user_id = request.audit["updated_by"]

        if request.data.get("email"):
            token = get_token_auth_header(request)
            if not token:
                return Response(
                    {"error": "Invalid/Missing Token"}, status=status.HTTP_403_FORBIDDEN
                )

            decoded = jwt_hard_decode_token(token)
            all_employee_emails = EmployeeAccessor(
                client_id
            ).get_all_non_deactivated_users_email_list()
            email = request.data["email"]
            if email not in all_employee_emails:
                return Response(
                    {"error": "Invalid Email Id"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user_id = decoded.get("sub")
            data_permission = get_data_permission(
                client_id, login_user_id, RBACComponent.MANAGE_USERS.value
            )
            user_permissions = get_user_permissions(client_id, login_user_id)
            allowed_impersonated_roles = user_permissions.get(
                "impersonated_user_roles", []
            )
            impersonated_email_roles = get_payee_role_id(client_id, email)

            if not data_permission or data_permission["type"] != RBAC.ALL_DATA.value:
                payee_emails = get_valid_payee_emails(
                    client_id, login_user_id, data_permission
                )
                if email not in payee_emails:
                    return Response(
                        {"error": "You don't have access to this resource"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if (
                RBAC.ALLOW_ALL_IMPERSONATION.value not in allowed_impersonated_roles
                and not any(
                    roles in allowed_impersonated_roles
                    for roles in impersonated_email_roles
                )
            ) or email == login_user_id:
                return Response(
                    {"error": "You don't have access to this resource"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                logger.info(f"******Logging in as user - /login_as {email}")
                # If needed user_details need to be taken from Auth0
                # user_details = get_user_details_by_email(email)
                user_detail = {
                    "name": request.data.get("name"),
                    "email": request.data.get("email"),
                }
                login_as_user(user_id, user_detail)
                logger.info(f"******Logged in as user - /login_as {email}")
                return login_as_audit(
                    client_id, login_user_id, user_detail["email"], user_detail["name"]
                )

            except Exception as e:
                logout_as_user(user_id)
                logging.error(e, traceback.print_exc())
                return Response(e, status=status.HTTP_403_FORBIDDEN)

        return Response(
            {"error": "Missing Email Id"},
            status=status.HTTP_403_FORBIDDEN,
        )


class LogoutView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("Logout")
    def post(self, request):
        timestamp = timezone.now()
        logger = request.logger
        client_id = request.client_id
        email = request.user.username
        session_id = request.session_id
        token_id = request.token_id

        logger.info("BEGIN: Session Management for LOGOUT")
        login_email = restricted_usage___get_login_user().username  # type: ignore
        master_email = request.master_user.username
        impersonated_user = None if master_email == email else email
        session_management_for_logout(
            client_id=client_id,
            login_user=login_email,
            impersonated_user=impersonated_user,
            token_id=token_id,
            session_id=session_id,
            meta=request.META,
            audit=request.audit,
            timestamp=timestamp,
            logger=logger,
        )

        logger.info("END: Session Management for LOGOUT")

        # [TODO]########### T E M P O R A R Y   C O D E   B L O C K ##################
        # Delete email<>client mapping in cache; created to be used by Chrome Extension flow.
        # Modify chrome extension flow to get rid off this dependency.
        cache_key = get_selected_client_in_cache_for_employee(login_email)
        cache.delete(cache_key)
        ##############################################################################

        analytics_data = {
            "user_id": email,
            "event_name": SegmentEvents.LOGOUT.value,
            "event_properties": {
                SegmentProperties.PLATFORM_USED.value: "Web",
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return logout_audit(client_id, email)


class LogoutAsView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @transaction.atomic
    def post(self, request):
        """
        - Logs a record in audit trail when the user logged out as another user to the application
        """
        client_id = request.client_id
        token = request.auth
        # in case of supportuser's request, `master_email` will be common-support-email, so this should be used for any operation to prevent any foot-prints of staff-member's actual email
        master_email = request.master_user.username
        email = request.user.username
        if token:
            decoded = request.decoded_token
            user_id = decoded.get("sub")
            # in case of supportuser's request, `user_metadata.admin_email` will be staff-member's actual email, so this should NOT be used anywhere
            # TODO: Prevent `user_metadata` lookup
            user_metadata = decoded.get("https://everstage.com/user_metadata")
            if user_metadata is not None:
                try:
                    logger.info(f"******Logging out as user - /login_as {email}")
                    logout_as_user(user_id)
                    logger.info(f"******Logged out as user - /login_as {email}")
                    return logout_as_audit(
                        client_id,
                        master_email,
                        email,
                        user_metadata.get("name"),
                    )
                except Exception as e:
                    logging.error(e, traceback.print_exc())
                    return Response(e, status=status.HTTP_403_FORBIDDEN)
            else:
                return Response("FAILURE", status=status.HTTP_403_FORBIDDEN)
        else:
            return Response(
                {"error": "Invalid/Missing Token"}, status=status.HTTP_403_FORBIDDEN
            )
