import logging
import traceback
from datetime import datetime

import pydash
from django.db import transaction
from django.utils.decorators import method_decorator
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from async_tasks.config import AsyncTaskConfig

# change the scope
from async_tasks.service import AsyncTaskService
from commission_engine.accessors.client_accessor import get_client_features
from commission_engine.services import update_payee_email_service
from commission_engine.utils.date_utils import end_of_day, start_of_day
from commission_engine.utils.general_data import (
    RESPONSE__PERMISSION_DENIED,
    RBACComponent,
    RbacPermissions,
)
from interstage_project.auth_utils import (
    authorize_for_profile_lookup,
    authorize_for_profile_lookup_v2,
    requires_scope,
)
from interstage_project.utils import add_log_context_view
from spm.services import payee_profile_services
from spm.services.rbac_services import get_ui_permissions, is_own_data_permission

logger_ = logging.getLogger(__name__)


class PayeeProfileFreeze(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("PayeePayoutFreeze")
    def post(self, request):
        return payee_profile_services.freeze_payout(request)


class PayeeProfileUnLock(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("PayeePayoutUnlock")
    def post(self, request):
        return payee_profile_services.unlock_payout(request)


class PayeeProfileUpdate(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @add_log_context_view("PayeeProfileUpdateTimezone")
    def post(self, request):
        try:
            return payee_profile_services.update_payee_profile(request)
        except:
            return Response({"status": "failed"}, status=status.HTTP_201_CREATED)


class PayeeProfileTimezoneUpdate(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    def post(self, request):
        try:
            email_id = request.user.username
            time_zone = request.data.get("timezone", None)
            is_time_zone_changed = request.data["is_time_zone_changed"]
            client_id = request.client_id

            logger_.info("BEGIN: Updating payee profile timezone")
            res = payee_profile_services.update_payee_profile_timezone(
                email_id,
                client_id,
                time_zone,
                is_time_zone_changed,
                audit=request.audit,
            )
            logger_.info("END: Updating payee profile timezone")

            return Response(res, status=status.HTTP_201_CREATED)
        except Exception:
            logger_.exception("Error in updating payee profile timezone")
            return Response({"status": "failed"}, status=status.HTTP_400_BAD_REQUEST)


class UpdatePayeeProfilePicture(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @add_log_context_view("UpdatePayeeProfilePicture")
    def post(self, request):
        try:
            logger = request.logger
            login_user_id = request.audit.get("updated_by")
            client_id = request.client_id
            email = request.data["email"]
            is_authorised = False
            client_features = get_client_features(client_id)
            user_permissions = get_ui_permissions(client_id, login_user_id)
            profile_picture_permission = client_features.get(
                "profile_picture_permission", "ALL"
            )
            if login_user_id == email:
                if (profile_picture_permission in ["SELF", "ALL"]) or (
                    profile_picture_permission == "ADMIN"
                    and RbacPermissions.MANAGE_OWNDATA.value in user_permissions
                ):
                    is_authorised = True
            elif (
                profile_picture_permission in ["ADMIN", "ALL"]
                and RbacPermissions.MANAGE_USERS.value in user_permissions
            ):
                is_authorised = authorize_for_profile_lookup_v2(
                    client_id,
                    login_user_id,
                    email,
                    RBACComponent.MANAGE_USERS.value,
                )

            if is_authorised:
                cloudfront_url = request.data["url"]
                log_context = {
                    "client_id": client_id,
                    "email": email,
                    "login_user_id": login_user_id,
                }
                logger.update_context(log_context)
                logger.info(f"BEGIN: Update payee profile picture")
                data = payee_profile_services.update_payee_profile_picture_url(
                    client_id, email, cloudfront_url, logger
                )
                data = {
                    "status": "success",
                }
                logger.info(f"END: Update payee profile picture")
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"status": "failed", "message": "User not authorised"},
                    status=status.HTTP_403_FORBIDDEN,
                )
        except Exception as e:
            print("Exception", e)
            return Response({"status": "failed"}, status=status.HTTP_201_CREATED)


class GeneratePayeeProfilePicture(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @add_log_context_view("GeneratePayeeProfilePicture")
    def post(self, request):
        try:
            logger = request.logger
            login_user_id = request.audit.get("updated_by")
            client_id = request.client_id
            email = request.data["email"]
            prompt = request.data["prompt"]
            style = request.data.get("style", None)
            img_base_64 = request.data.get("img_base_64", None)
            log_context = {
                "client_id": client_id,
                "email": email,
                "prompt": prompt,
                "login_user_id": login_user_id,
            }
            logger.update_context(log_context)

            # Check for data permission of logged in user: manage users
            is_authorized = authorize_for_profile_lookup(
                client_id=client_id,
                login_user_id=login_user_id,
                email_id=email,
                component=RBACComponent.MANAGE_USERS.value,
            )
            if not is_authorized:
                logger.info(
                    f"User: {login_user_id} don't have permission to update user details of {email}"
                )
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

            # Check for the own data permission of logged in user
            has_owndata_perm = is_own_data_permission(client_id, login_user_id)
            if not has_owndata_perm and login_user_id == email:
                logger.info(
                    f"User: {login_user_id} don't have permission to change own profile picture"
                )
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

            logger.info(f"BEGIN: Generate payee profile picture")
            data = payee_profile_services.generate_payee_profile_picture_url(
                client_id,
                email,
                prompt,
                logger=logger,
                style=style,
                img_base_64=img_base_64,
            )
            data = {"status": "success", "data": data}
            logger.info(f"END: Generate payee profile picture")
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            print("Exception", e)
            return Response({"status": "failed"}, status=status.HTTP_201_CREATED)


class UploadPayeeProfilePicture(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @add_log_context_view("GeneratePayeeProfilePicture")
    def post(self, request):
        try:
            logger = request.logger
            login_user_id = request.audit.get("updated_by")
            client_id = request.client_id
            email = request.data["email"]
            image = request.data.get("image", None)
            log_context = {
                "client_id": client_id,
                "email": email,
                "image": image,
                "login_user_id": login_user_id,
            }
            logger.update_context(log_context)

            # Check for data permission of logged in user: manage users
            is_authorized = authorize_for_profile_lookup(
                client_id=client_id,
                login_user_id=login_user_id,
                email_id=email,
                component=RBACComponent.MANAGE_USERS.value,
            )
            if not is_authorized:
                logger.info(
                    f"User: {login_user_id} don't have permission to update user details of {email}"
                )
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

            # Check for the own data permission of logged in user
            has_owndata_perm = is_own_data_permission(client_id, login_user_id)
            if not has_owndata_perm and login_user_id == email:
                logger.info(
                    f"User: {login_user_id} don't have permission to change own profile picture"
                )
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

            data = payee_profile_services.upload_payee_profile_picture(
                client_id, email, image, logger
            )
            data = {"status": "success", "data": data}
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            print("Exception", e)
            return Response({"status": "failed"}, status=status.HTTP_201_CREATED)


class GenerateCriteriaData(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_PAYOUTS.value), name="dispatch"
    )
    @add_log_context_view("DownloadCriteriaData")
    def post(self, request):
        try:
            client_id = request.client_id
            payee_email = request.data["payee_email"]
            psd = request.data["psd"]
            ped = request.data["ped"]
            plan_id = request.data["plan_id"]
            criteria_id = request.data["criteria_id"]

            task_id = AsyncTaskService(
                client_id=client_id,
                created_by=request.user.username,
                task=AsyncTaskConfig.GENERATE_COMMISSION_DATA_CSV,
            ).run_task(
                params={
                    "client_id": client_id,
                    "psd": psd,
                    "ped": ped,
                    "payee_email": payee_email,
                    "plan_id": plan_id,
                    "criteria_id": criteria_id,
                },
                force_run=True,
            )
            return Response({"task_id": task_id})
        except Exception as e:
            return Response(
                {"status": "failed", "exception": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdatePayeeEmail(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ADMINUI.value), name="dispatch"
    )
    @add_log_context_view("UpdatePayeeEmail")
    def post(self, request):
        try:
            return update_payee_email_service.update_payee_email(request)
        except update_payee_email_service.EmailAlreadyExistsException:
            return Response({"status": "ERROR"}, status=status.HTTP_409_CONFLICT)
        except Exception:
            return Response(
                {"status": "FAILED"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class Individuals(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_COMMISSIONPLAN.value), name="dispatch"
    )
    @add_log_context_view("GetIndividuals")
    def get(self, request):
        client_id = request.client_id
        logger = request.logger
        try:
            plan_period = []
            if request.query_params.get("plan_start") and request.query_params.get(
                "plan_end"
            ):
                plan_period.append(
                    make_aware(
                        start_of_day(
                            datetime.strptime(
                                request.query_params.get("plan_start"), "%d-%b-%Y"
                            )
                        )
                    )
                )
                plan_period.append(
                    make_aware(
                        end_of_day(
                            datetime.strptime(
                                request.query_params.get("plan_end"), "%d-%b-%Y"
                            )
                        )
                    )
                )
            payout_frequency = request.query_params.get("payout_frequency")
            exclude_payees_in_plan = request.query_params.get("exclude_payees_in_plan")
            include_reportees = (
                pydash.chain(
                    request.query_params.get("include_reportees", "").split(",")
                )
                .compact()
                .uniq()
                .value()
            )
            exclude_reportees = (
                pydash.chain(
                    request.query_params.get("exclude_reportees", "").split(",")
                )
                .compact()
                .uniq()
                .value()
            )
            limit = request.query_params.get("limit_value")
            offset = request.query_params.get("offset_value")
            search_text = request.query_params.get("search_text")
            with_count = request.query_params.get("with_count")
            res = payee_profile_services.get_individuals(
                client_id,
                plan_period=plan_period,
                payout_frequency=payout_frequency,
                exclude_payees_in_plan=exclude_payees_in_plan,
                include_reportees=include_reportees,
                exclude_reportees=exclude_reportees,
                limit=limit,
                offset=offset,
                search_text=search_text,
                with_count=with_count,
                logger=logger,
            )
            return Response(res)
        except Exception:
            traceback.print_exc()
            return Response(
                {"status": "FAILED"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class Teams(APIView):
    @method_decorator(requires_scope(RbacPermissions.VIEW_TEAMS.value), name="dispatch")
    @add_log_context_view("GetTeams")
    def get(self, request):
        client_id = request.client_id
        logger = request.logger
        try:
            reportee_plan_period = []
            if request.query_params.get("plan_start") and request.query_params.get(
                "plan_end"
            ):
                reportee_plan_period.append(
                    make_aware(
                        start_of_day(
                            datetime.strptime(
                                request.query_params.get("plan_start"), "%d-%b-%Y"
                            )
                        )
                    )
                )
                reportee_plan_period.append(
                    make_aware(
                        end_of_day(
                            datetime.strptime(
                                request.query_params.get("plan_end"), "%d-%b-%Y"
                            )
                        )
                    )
                )
            reportee_payout_frequency = request.query_params.get("payout_frequency")
            exclude_payees_in_plan = request.query_params.get("exclude_payees_in_plan")
            include_reportees = (
                pydash.chain(
                    request.query_params.get("include_reportees", "").split(",")
                )
                .compact()
                .uniq()
                .value()
            )
            exclude_reportees = (
                pydash.chain(
                    request.query_params.get("exclude_reportees", "").split(",")
                )
                .compact()
                .uniq()
                .value()
            )
            limit = request.query_params.get("limit_value")
            offset = request.query_params.get("offset_value")
            search_text = request.query_params.get("search_text")
            with_count = request.query_params.get("with_count")
            res = payee_profile_services.get_teams(
                client_id,
                reportee_plan_period=reportee_plan_period,
                reportee_payout_frequency=reportee_payout_frequency,
                exclude_payees_in_plan=exclude_payees_in_plan,
                include_reportees=include_reportees,
                exclude_reportees=exclude_reportees,
                limit=limit,
                offset=offset,
                manager_search_text=search_text,
                with_count=with_count,
                logger=logger,
            )
            return Response(res)
        except Exception:
            traceback.print_exc()
            return Response(
                {"status": "FAILED"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ManagersForPlanV2(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_COMMISSIONPLAN.value,
                RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, request):
        client_id = request.client_id
        try:
            limit = int(request.query_params.get("limit_value"))
            offset = int(request.query_params.get("offset_value"))
            search_text = request.query_params.get("search_text")
            res = payee_profile_services.get_manager_for_plan_v2(
                client_id, limit=limit, offset=offset, search_text=search_text
            )
            return Response(res)
        except Exception:
            traceback.print_exc()
            return Response(
                {"status": "FAILED"},
                status=status.HTTP_400_BAD_REQUEST,
            )
