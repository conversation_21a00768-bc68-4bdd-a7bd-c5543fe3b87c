from django.db import transaction
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view
from spm.services import rbac_services


class CreateRole(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("CreateRole")
    def post(self, request):
        client_id = request.client_id
        display_name = request.data["display_name"]
        description = request.data["description"]
        logger = request.logger
        audit = request.audit
        return rbac_services.create_role(
            client_id, display_name, description, logger, audit
        )


class AllRoleDetails(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_USERS.value,
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_ROLES.value,
                RbacPermissions.VIEW_PAYOUTS.value,
                RbacPermissions.MANAGE_ADMINUI.value,
                RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @add_log_context_view("AllRoleDetails")
    def get(self, request):
        client_id = getattr(request, "client_id", None)
        if request.query_params.get("client_id"):
            client_id = request.query_params.get("client_id")

        if client_id is None:
            return Response(
                {"error": "client_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        logger = request.logger
        all_roles = rbac_services.get_all_role_details(client_id, logger)
        return Response(all_roles, status=status.HTTP_200_OK)


class RoleDetails(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("RoleDetails")
    def get(self, request):
        client_id = request.client_id
        role_permission_id = request.GET["role_permission_id"]
        logger = request.logger
        return rbac_services.get_role_details(client_id, role_permission_id, logger)


class RolePermissionDetails(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("RolePermissionDetails")
    def get(self, request):
        client_id = request.client_id
        role_permission_id = request.GET["role_permission_id"]
        logger = request.logger
        return rbac_services.role_permission_details(
            client_id, role_permission_id, logger
        )


class InvalidateRole(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("InvalidateRole")
    def post(self, request):
        client_id = request.client_id
        role_permission_id = request.data["role_permission_id"]
        logger = request.logger
        audit = request.audit
        return rbac_services.invalidate_role(
            client_id, role_permission_id, logger, audit
        )


class MoveAndInvalidateRole(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("MoveAndInvalidateRole")
    def post(self, request):
        client_id = request.client_id
        role_permission_id = request.data["role_permission_id"]
        logger = request.logger
        audit = request.audit
        new_role_permission_id = request.data["new_role_permission_id"]
        return rbac_services.move_and_invalidate_role(
            client_id, role_permission_id, logger, audit, new_role_permission_id
        )


class EditRole(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("EditRole")
    def patch(self, request):
        client_id = request.client_id
        role_permission_id = request.data["role_permission_id"]
        permissions = request.data["permissions"]
        display_name = request.data["display_name"]
        description = request.data["description"]
        logger = request.logger
        audit = request.audit
        return rbac_services.edit_role(
            client_id,
            role_permission_id,
            permissions,
            display_name,
            description,
            logger,
            audit,
        )


class CloneRole(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("CloneRole")
    def post(self, request):
        client_id = request.client_id
        role_permission_id = request.data["role_permission_id"]
        logger = request.logger
        audit = request.audit
        return rbac_services.clone_role(client_id, role_permission_id, logger, audit)


class UserPermissions(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("UserPermissions")
    def post(self, request):
        client_id = request.client_id
        email_id = request.user.username
        permissions = rbac_services.get_user_permissions(client_id, email_id)
        return Response(
            permissions,
            status=status.HTTP_201_CREATED,
        )


class DataPermissions(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_EVERSTAGE.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("DataPermissions")
    def post(self, request):
        client_id = request.client_id
        email_id = request.user.username
        component = request.data["component"]
        permissions = rbac_services.get_data_permission(client_id, email_id, component)
        return Response(
            permissions,
            status=status.HTTP_201_CREATED,
        )
