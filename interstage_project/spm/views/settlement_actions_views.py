import json
import logging
import traceback
import uuid

from dateutil.parser import parse
from django.db import transaction
from django.db.utils import IntegrityError
from django.http import FileResponse, HttpResponse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.timezone import make_aware
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import (
    avoid_concurrent_register_payment,
    can_avoid_locking_with_pending_changes,
    is_notification_v2,
)
from commission_engine.utils import end_of_day
from commission_engine.utils.general_data import (
    RBAC,
    RESPONSE__PERMISSION_DENIED,
    Notification,
    RBACComponent,
    RbacPermissions,
)
from everstage_ddd.notifications import notify_statements_lock_to_payees_v2
from everstage_etl.snowflake_report_etl.optimized_payout_snapshot_task import (
    run_snapshots_and_reports_on_unfreeze,
)
from interstage_project.auth_utils import authorize_for_profile_lookup, requires_scope
from interstage_project.utils import LogWithContext, add_log_context_view
from ms_teams_everstage.services.commission_services.notification_service import (
    post_commissions_frozen_for_payee_notification_on_msteams,
)
from slack_everstage.services.commission_services.notification_service import (
    post_commissions_frozen_for_payee_notification_on_slack,
)
from spm.accessors.notification_accessors import ClientNotificationAccessor
from spm.accessors.payout_accessor import PayoutAccessor
from spm.accessors.payout_process_lock_accessors import PayoutProcessLockAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.models.payout_process_lock_models import (
    PayeePeriodActionEvents,
    PayoutProcessMessageCodes,
)
from spm.services import audit_services
from spm.services.commission_actions_service.commission_email_services import (
    payout_locked_mails,
)
from spm.services.commission_actions_service.payout_action_service import (
    check_sync_running_status,
    export_changed_payout_details,
    get_payees_with_pending_changes,
    is_commission_changed,
    update_commission_secondary_kd,
    update_lock_status_v2,
)
from spm.services.payout_view_services import PayoutsViewData
from spm.services.rbac_services import (
    get_data_permission,
    get_valid_payee_emails,
    is_payout_value_permission,
)
from spm.services.settlement_actions_service.settlement_actions_service import (
    acquire_lock_payout_process,
    acquire_payout_lock,
    generate_conflicting_payees_csv,
    invalidate_payout_id_as_unpaid,
    invalidate_process_ignore_arrears,
    is_settlement_changed,
    release_payout_lock,
    update_paid_status,
    update_settlement_lock_status_v2,
)

module_logger = logging.getLogger(__name__)


class LockStatusView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        is_locked = request.data["is_locked"]
        abort_instance_check = request.data["abort_instance_check"]
        audit = request.audit
        logger = LogWithContext()

        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )

        logger.info("Feature Flag enabled executing update_lock_status_v2")
        return update_settlement_lock_status_v2(
            client_id,
            payee_ids,
            date,
            is_locked,
            audit,
            abort_approvals=abort_instance_check,
        )


class ValidateUnlockPayoutView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("ValidateUnlockPayoutView")
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, effective_date
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )

        payout_changed_payees = list(
            is_commission_changed(client_id, date, payee_ids).union(
                is_settlement_changed(client_id, date, payee_ids)
            )
        )
        return Response(
            {
                "payee_ids": payout_changed_payees,
                "payout_changed": bool(True) if payout_changed_payees else bool(False),
            },
            status=status.HTTP_200_OK,
        )


class ExportChangedPayoutDetailsView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    def post(self, request):
        payee_ids = request.data.get("payee_ids", [])
        client_id = request.client_id

        # Generate the Excel file
        excel_file = export_changed_payout_details(client_id, payee_ids)

        response = FileResponse(
            excel_file, as_attachment=True, filename="payout_changed_user_details.xlsx"
        )
        return response


class SettlmentCommissionLockStatusView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("UpdateSettlementCommissionLockStatus")
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        is_locked = request.data["is_locked"]
        abort_instance_check = request.data["abort_instance_check"]
        audit = request.audit
        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, effective_date
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )
        batch_size = 500
        output_data = {}
        logger = request.logger
        log_context = {"client_id": client_id}

        total_iterations = int(len(payee_ids) / batch_size)
        if (len(payee_ids) % batch_size) > 0:
            total_iterations += 1
        logger.info("Feature Flag enabled executing update_lock_status_v2")
        payout_snapshot_tasks = []
        settlement_snapshot_tasks = []
        # This id will be used only when Snapshot syncs run in Snowflake mode
        e2e_sync_run_id = uuid.uuid4()
        try:
            for i in range(0, total_iterations):
                start = batch_size * i
                end = start + batch_size
                batch_payee_id = payee_ids[start:end]
                commission_response, payout_snapshot_tasks_batch = (
                    update_lock_status_v2(
                        client_id,
                        batch_payee_id,
                        is_locked,
                        date,
                        audit,
                        request.logger,
                        abort_approvals=abort_instance_check,
                        both_comm_and_sett_report_etl=True,
                        e2e_sync_run_id=e2e_sync_run_id,
                    )
                )
                output_data.update(commission_response.data)
                settlement_response, settlement_snapshot_tasks_batch = (
                    update_settlement_lock_status_v2(
                        client_id,
                        batch_payee_id,
                        date,
                        is_locked,
                        audit,
                        both_comm_and_sett_report_etl=True,
                        e2e_sync_run_id=e2e_sync_run_id,
                    )
                )
                if not is_locked:
                    update_commission_secondary_kd(
                        client_id,
                        batch_payee_id,
                        date,
                        log_context,
                    )
                if payout_snapshot_tasks_batch is not None:
                    payout_snapshot_tasks.extend(payout_snapshot_tasks_batch)
                if settlement_snapshot_tasks_batch is not None:
                    settlement_snapshot_tasks.extend(settlement_snapshot_tasks_batch)
        except IntegrityError as e:
            logger.error(
                "Error while updating lock status for payee ids {} for period {} - {}".format(
                    payee_ids, date, e
                )
            )
            return Response(
                ["Lock status for some payees are already updated"],
                status=status.HTTP_409_CONFLICT,
            )

        # Trigger Payout and Settlement Snapshot Syncs, followed by Snowflake Report Sync
        if len(payout_snapshot_tasks) > 0 or len(settlement_snapshot_tasks) > 0:
            logger.info("Triggering Snowflake Snapshot + Report ETL on unfreeze")
            run_snapshots_and_reports_on_unfreeze(
                client_id,
                payout_snapshot_tasks,
                settlement_snapshot_tasks,
                e2e_sync_run_id,
            )

        # Send emails to respective payees if the locked. Exclude payee email ids with errors
        if is_locked and output_data:
            # Filter out failed response value by their status key
            filtered_emails = []
            for key, val in output_data.items():
                if key == "status" and val == "FAILED":
                    continue
                filtered_emails.append(key)
            ped = make_aware(end_of_day(parse(date, dayfirst=True)))

            statement_lock_task = ClientNotificationAccessor(
                client_id
            ).get_notification_status(Notification.STATEMENTS_LOCKED_NOTIFICATION)
            is_email_statement_lock_enabled = statement_lock_task["email"]
            is_slack_statement_lock_enabled = statement_lock_task["slack"]
            is_ms_teams_statement_lock_enabled = statement_lock_task["ms_teams"]

            if is_notification_v2(client_id):
                notify_statements_lock_to_payees_v2(
                    client_id,
                    payee_email_ids=filtered_emails,
                    ped=ped,
                    logged_in_user=logged_email,
                )
            else:
                if is_email_statement_lock_enabled:
                    payout_locked_mails(client_id, logged_email, filtered_emails, ped)
                ## Send MS-Teams/Slack Notification to payees
                if is_ms_teams_statement_lock_enabled:
                    for email in filtered_emails:
                        try:
                            logger.info(
                                f"BEGIN: post_commissions_frozen_for_payee_notification_on_msteams for {email} on MS-Teams"
                            )
                            post_commissions_frozen_for_payee_notification_on_msteams(
                                client_id, email, ped
                            )
                            logger.info(
                                f"END: post_commissions_frozen_for_payee_notification_on_msteams for {email} on MS-Teams"
                            )
                        except Exception as error:
                            logger.error(
                                f"EXCEPTION: STATEMENTS LOCKED MSTEAMS NOTIFICATION - {error}"
                            )
                            logger.error(
                                f"EXCEPTION TB: STATEMENTS LOCKED MSTEAMS NOTIFICATION traceback- {traceback.print_exc()}"
                            )
                else:
                    logger.info(
                        "SKIPPED: STATEMENTS LOCKED MSTEAMS NOTIFICATION NOT ENABLED"
                    )
                if is_slack_statement_lock_enabled:
                    for email in filtered_emails:
                        try:
                            logger.info(
                                f"BEGIN: post_commissions_frozen_for_payee_notification_on_slack for {email} on Slack"
                            )
                            post_commissions_frozen_for_payee_notification_on_slack(
                                client_id, email, ped
                            )
                            logger.info(
                                f"END: post_commissions_frozen_for_payee_notification_on_slack for {email} on Slack"
                            )
                        except Exception as error:
                            logger.error(
                                f"EXCEPTION: STATEMENTS LOCKED SLACKNOTIFICATION - {error}"
                            )
                            logger.error(
                                f"EXCEPTION TB: STATEMENTS LOCKED SLACK NOTIFICATION traceback- {traceback.print_exc()}"
                            )
                else:
                    logger.info(
                        "SKIPPED: STATEMENTS LOCKED SLACK NOTIFICATION NOT ENABLED"
                    )

        error = []

        if settlement_response.status_code != 201:
            error.append("Settlement Lock Failure")
        if commission_response.status_code != 201:
            error.append("Commission Lock Failure")
        if (
            commission_response.status_code == 201
            and settlement_response.status_code == 201
        ):
            return Response(output_data, status=status.HTTP_201_CREATED)
        else:
            return Response(error, status=status.HTTP_400_BAD_REQUEST)


class PayoutStatusView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.REGISTER_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        settlement_details = request.data["settlement_details"]
        audit = request.audit
        time = timezone.now()
        ##################### audit log #####################
        event_type_code = EVENT["INITIATE_PAYOUT"]["code"]
        event_key = "payout_{}".format(date)
        summary = "Register Payment"
        audit_data = {**request.data}
        updated_by = request.audit["updated_by"]
        updated_at = time
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        # Checking for data permission of logged in user: payout statements
        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, effective_date
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Checking for "view:payoutvalueothers" permission. If not payout value others permission
        # filter out other payee ids except that of logged in user.
        has_payoutvalue_perm = is_payout_value_permission(client_id, logged_email)
        if not has_payoutvalue_perm:
            if logged_email in payee_ids:
                payee_ids = [logged_email]
            else:
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

        cache_key = f"client_{client_id}_{event_type_code}"
        # checking if already an process is in progress for the client
        if not acquire_payout_lock(cache_key):
            return Response(
                "The previous payment registration is still being processed. Please wait a few minutes before trying again.",
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            response = update_paid_status(
                client_id,
                payee_ids,
                date,
                settlement_details,
                audit,
                logged_in_user=logged_email,
            )
            if isinstance(response, dict):
                return Response(response, status=status.HTTP_200_OK)
            return response
        except Exception:
            return Response(
                {"error": "Error while processing payouts"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        finally:
            release_payout_lock(cache_key)


class PayoutStatusViewV2(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.REGISTER_PAYOUTS.value), name="dispatch"
    )
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        settlement_details = request.data["settlement_details"]
        audit = request.audit
        time = timezone.now()
        ##################### audit log #####################
        event_type_code = EVENT["INITIATE_PAYOUT"]["code"]
        event_key = "payout_{}".format(date)
        summary = "Register Payment"
        audit_data = {**request.data}
        updated_by = request.audit["updated_by"]
        updated_at = time

        if can_avoid_locking_with_pending_changes(
            client_id
        ) and check_sync_running_status(client_id):
            return Response(
                {"message_code": "CANNOT_LOCK_ON_ONGOING_SYNC"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        (
            is_pending_changes,
            pending_changes_payees_set,
            pending_changes_file_content,
        ) = get_payees_with_pending_changes(client_id, payee_ids, date)

        total_payees = len(payee_ids)

        if can_avoid_locking_with_pending_changes(client_id) and is_pending_changes:
            if len(payee_ids) == 1:
                return Response(
                    {
                        "message_code": "PAYMENT_RESTRICTED_SINGLE_PAYEE_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            elif len(payee_ids) == len(pending_changes_payees_set):
                return Response(
                    {
                        "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                        "display_message": f"Payment(s) registered successfully for 0 payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                payee_ids = [
                    email
                    for email in payee_ids
                    if email not in pending_changes_payees_set
                ]
                audit_data["payee_ids"] = payee_ids

        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        # Checking for data permission of logged in user: payout statements
        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, effective_date
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Checking for "view:payoutvalueothers" permission. If not payout value others permission
        # filter out other payee ids except that of logged in user.
        has_payoutvalue_perm = is_payout_value_permission(client_id, logged_email)
        if not has_payoutvalue_perm:
            if logged_email in payee_ids:
                payee_ids = [logged_email]
            else:
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )
        if not payee_ids or not date:
            return Response(
                "Payee ids and date are required for this action",
                status=status.HTTP_400_BAD_REQUEST,
            )

        if avoid_concurrent_register_payment(client_id):
            try:
                # Acquire lock and returns a list of payee ids to process and a list of conflicting payee ids
                payout_process_lock_accessor = PayoutProcessLockAccessor(
                    client_id=client_id
                )

                processed_payee_ids = acquire_lock_payout_process(
                    client_id,
                    payee_ids,
                    effective_date,
                    PayeePeriodActionEvents.INITIATE_PAYOUT.value,
                )
                payee_ids_to_process = processed_payee_ids["payee_ids_to_process"]
                conflicting_payees = processed_payee_ids["conflicting_payees"]

                response_data = {
                    "message_code": PayoutProcessMessageCodes.PROCESSED_COMPLETELY.value,
                    "conflictingPayeesCsv": None,
                    "message": "Successfully processed payouts for selected payees.",
                }
                result = {}
                # Process payees without conflicts
                if payee_ids_to_process:
                    result = update_paid_status(
                        client_id,
                        payee_ids_to_process,
                        date,
                        settlement_details,
                        audit,
                        logged_in_user=logged_email,
                    )
                    response_data["message_code"] = (
                        PayoutProcessMessageCodes.PROCESSED_COMPLETELY.value
                    )
                    response_data["message"] = "Payment(s) registered successfully"

                if len(conflicting_payees) > 0:
                    response_data["conflictingPayeesCsv"] = (
                        generate_conflicting_payees_csv(conflicting_payees)
                    )
                    response_data["message"] = (
                        f"Payement(s) successfully registered for {len(result)} payees(s). However, registration(s) for {len(conflicting_payees)} payee(s) could not be completed as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
                        if conflicting_payees and result
                        else (
                            "Payment(s) could not be registered for the selected payee(s) as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
                            if conflicting_payees and not result
                            else "Payment(s) registered successfully"
                        )
                    )
                    response_data["message_code"] = (
                        PayoutProcessMessageCodes.PROCESSED_PARTIALLY.value
                        if conflicting_payees and result
                        else PayoutProcessMessageCodes.PROCESS_FAILED.value
                    )

                if (
                    can_avoid_locking_with_pending_changes(client_id)
                    and is_pending_changes
                ):
                    return Response(
                        {
                            "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                            "pending_changes_file_content": pending_changes_file_content,
                            "display_message": f"Payment(s) registered successfully for {total_payees - len(pending_changes_payees_set)} payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                return Response(response_data, status=status.HTTP_200_OK)

            except Exception as e:
                module_logger.exception("Error while processing payouts - {}".format(e))
                return Response(
                    {"error": "Error while processing payouts"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            finally:
                # Release the lock for all payees
                if (
                    payout_process_lock_accessor
                    and payee_ids_to_process
                    and effective_date
                ):
                    payout_process_lock_accessor.release_lock(
                        payee_ids_to_process,
                        effective_date,
                        PayeePeriodActionEvents.INITIATE_PAYOUT.value,
                    )
        else:
            cache_key = f"client_{client_id}_{event_type_code}"
            # checking if already an process is in progress for the client
            if not acquire_payout_lock(cache_key):
                return Response(
                    "The previous payment registration is still being processed. Please wait a few minutes before trying again.",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            try:
                successful_response = update_paid_status(
                    client_id,
                    payee_ids,
                    date,
                    settlement_details,
                    audit,
                    logged_in_user=logged_email,
                )

                if (
                    can_avoid_locking_with_pending_changes(client_id)
                    and is_pending_changes
                ):
                    return Response(
                        {
                            "message_code": "PAYMENT_PARTIALLY_RESTRICTED_MESSAGE",
                            "pending_changes_file_content": pending_changes_file_content,
                            "display_message": f"Payment(s) registered successfully for {total_payees - len(pending_changes_payees_set)} payee(s). However, payments for {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be registered (see downloaded CSV). Run commission sync for these payees or all payees in this payout period and retry payment registration ",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                return successful_response
            except Exception:
                return Response(
                    {"error": "Error while processing payouts"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            finally:
                release_payout_lock(cache_key)


class RevertPayoutTransactionView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.INVALIDATE_PAYOUTS.value), name="dispatch"
    )
    def post(self, request):
        client_id = request.client_id
        payout_id = request.data.get("payout_id")
        payout_type = request.data["type"]
        date = request.data["date"]
        audit = request.audit
        time = timezone.now()
        ##################### audit log #####################
        event_type_code = EVENT["INVALIDATE_PAYOUT"]["code"]
        event_key = payout_id
        summary = "Invalidate Payout"
        audit_data = {**request.data}
        updated_by = request.audit["updated_by"]
        updated_at = time
        date = request.data["date"]
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        if payout_type == "payout":
            payout = PayoutAccessor(client_id).get_payout_by_payout_id(payout_id)
            if not payout:
                return Response(
                    "Payout with id {} does not exist".format(payout_id),
                    status=status.HTTP_400_BAD_REQUEST,
                )
            payee_email_id = payout.payee_id
        else:
            payee_email_id = request.data.get("payee_email_id")

        # Checking for data permission of logged in user: payout statements
        logged_in_user = request.user.username
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        is_authorized = authorize_for_profile_lookup(
            client_id=client_id,
            login_user_id=logged_in_user,
            email_id=payee_email_id,
            component=RBACComponent.PAYOUTS_STATEMENTS.value,
            effective_date=effective_date,
        )
        if not is_authorized:
            return Response(
                RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
            )

        # Checking for "view:payoutvalueothers" permission. Note that if the user
        # don't have the permission, they shouldn't be able to revert payout.
        has_payoutvalue_perm = is_payout_value_permission(client_id, logged_in_user)
        if not has_payoutvalue_perm and logged_in_user != payee_email_id:
            return Response(
                RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
            )

        if payout_type == "payout":
            return invalidate_payout_id_as_unpaid(client_id, payout_id, audit, date)
        else:
            arrear_month = request.data.get("arrear_month")
            return invalidate_process_ignore_arrears(
                client_id, payee_email_id, arrear_month, audit
            )


class PayoutFiltersView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("payout_filter")
    def post(self, request):
        logger = request.logger
        client_id = request.client_id
        search_term = request.data.get("search_term", None)
        limit = request.data["pagination"]["limit_value"]
        offset = request.data["pagination"]["offset_value"]
        filters = request.data.get("filters", None)
        date = request.data["date"]
        ped = make_aware(end_of_day(parse(date, dayfirst=True)))
        is_bulk_action = request.data.get("is_bulk_action", False)
        orderby_fields = request.data.get("orderby_fields", [])
        selected_columns = request.data.get("selected_columns", [])
        logged_email = request.user.username
        try:
            data_permission = get_data_permission(
                client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
            )
            if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
                payee_emails = get_valid_payee_emails(
                    client_id, logged_email, data_permission, ped
                )
                filters = filters or {}
                if payee_emails:
                    filters["email_id_in"] = payee_emails
            result = PayoutsViewData(client_id, ped=ped, logger=logger).get_payout_data(
                filters,
                search_term=search_term,
                offset=offset,
                limit=limit,
                is_bulk_action=is_bulk_action,
                is_approval_on=request.data.get("is_approval_on"),
                logged_in_user=logged_email,
                orderby_fields=orderby_fields,
                selected_columns=selected_columns,
            )
            return Response(data=json.dumps(result), status=status.HTTP_200_OK)
        except Exception as exception:
            logger.error(
                "Error while applying filters in payouts for period- {} exception - {}".format(
                    ped, exception
                )
            )
            logger.error(
                "Apply payout filters traceback - {}".format(traceback.print_exc())
            )
            return Response("FAILED", status=status.HTTP_400_BAD_REQUEST)


class PayoutsExport(APIView):
    """
    Export payouts data as csv
    """

    @method_decorator(
        requires_scope(RbacPermissions.EXPORT_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("payoutsExport")
    def post(self, request):
        client_id = request.client_id
        filters = request.data.get("filters")
        columns = request.data.get("columns")
        date = request.data["date"]
        ped = make_aware(end_of_day(parse(date, dayfirst=True)))
        logged_email = request.user.username
        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, ped
            )
            filters = filters or {}
            if payee_emails:
                filters["email_id_in"] = payee_emails
        result = PayoutsViewData(client_id, ped=ped).get_export_data_payouts(
            filters=filters,
            is_approval_on=request.data.get("is_approval_on"),
            logged_email=logged_email,
            columns=columns,
        )
        response = HttpResponse(
            result,
            content_type="text/csv",
        )
        response["Content-Disposition"] = 'attachment; filename="payouts.csv"'
        return response


class CommLockWithPendingChangesView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value), name="dispatch"
    )
    @transaction.atomic
    @add_log_context_view("CommLockWithPendingChangesView")
    def post(self, request):
        client_id = request.client_id
        payee_ids = request.data["payee_ids"]
        date = request.data["date"]
        is_locked = request.data["is_locked"]
        abort_instance_check = request.data["abort_instance_check"]
        audit = request.audit
        logged_email = request.user.username
        total_payees = len(payee_ids)

        if check_sync_running_status(client_id):
            # If the sync is running, then we cannot lock the payees
            return Response(
                {"message_code": "CANNOT_LOCK_ON_ONGOING_SYNC"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        is_pending_changes = False
        pending_changes_file_content = None

        if is_locked:
            # On locking we check for pending changes and exclude them from the lock
            (
                is_pending_changes,
                pending_changes_payees_set,
                pending_changes_file_content,
            ) = get_payees_with_pending_changes(client_id, payee_ids, date)
            payee_ids = [
                email for email in payee_ids if email not in pending_changes_payees_set
            ]

        data_permission = get_data_permission(
            client_id, logged_email, RBACComponent.PAYOUTS_STATEMENTS.value
        )
        effective_date = make_aware(end_of_day(parse(date, dayfirst=True)))
        if data_permission and data_permission["type"] != RBAC.ALL_DATA.value:
            payee_emails = get_valid_payee_emails(
                client_id, logged_email, data_permission, effective_date
            )
            if not set(payee_ids).issubset(set(payee_emails)):
                return Response(
                    "You don't have access to this resource",
                    status=status.HTTP_400_BAD_REQUEST,
                )
        batch_size = 500
        output_data = {}
        logger = request.logger
        log_context = {"client_id": client_id}

        total_iterations = int(len(payee_ids) / batch_size)
        if (len(payee_ids) % batch_size) > 0:
            total_iterations += 1
        logger.info("Feature Flag enabled executing update_lock_status_v2")
        payout_snapshot_tasks = []
        settlement_snapshot_tasks = []
        # This id will be used only when Snapshot syncs run in Snowflake mode
        e2e_sync_run_id = uuid.uuid4()
        try:
            for i in range(0, total_iterations):
                start = batch_size * i
                end = start + batch_size
                batch_payee_id = payee_ids[start:end]
                commission_response, payout_snapshot_tasks_batch = (
                    update_lock_status_v2(
                        client_id,
                        batch_payee_id,
                        is_locked,
                        date,
                        audit,
                        request.logger,
                        abort_approvals=abort_instance_check,
                        both_comm_and_sett_report_etl=True,
                        e2e_sync_run_id=e2e_sync_run_id,
                    )
                )
                output_data.update(commission_response.data)
                settlement_response, settlement_snapshot_tasks_batch = (
                    update_settlement_lock_status_v2(
                        client_id,
                        batch_payee_id,
                        date,
                        is_locked,
                        audit,
                        both_comm_and_sett_report_etl=True,
                        e2e_sync_run_id=e2e_sync_run_id,
                    )
                )
                if not is_locked:
                    update_commission_secondary_kd(
                        client_id,
                        batch_payee_id,
                        date,
                        log_context,
                    )
                if payout_snapshot_tasks_batch is not None:
                    payout_snapshot_tasks.extend(payout_snapshot_tasks_batch)
                if settlement_snapshot_tasks_batch is not None:
                    settlement_snapshot_tasks.extend(settlement_snapshot_tasks_batch)
        except IntegrityError as e:
            logger.error(
                "Error while updating lock status for payee ids {} for period {} - {}".format(
                    payee_ids, date, e
                )
            )
            return Response(
                ["Lock status for some payees are already updated"],
                status=status.HTTP_409_CONFLICT,
            )

        # Trigger Payout and Settlement Snapshot Syncs, followed by Snowflake Report Sync
        if len(payout_snapshot_tasks) > 0 or len(settlement_snapshot_tasks) > 0:
            logger.info("Triggering Snowflake Snapshot + Report ETL on unfreeze")
            run_snapshots_and_reports_on_unfreeze(
                client_id,
                payout_snapshot_tasks,
                settlement_snapshot_tasks,
                e2e_sync_run_id,
            )

        # Send emails to respective payees if the locked. Exclude payee email ids with errors
        if is_locked and output_data:
            # Filter out failed response value by their status key
            filtered_emails = []
            for key, val in output_data.items():
                if key == "status" and val == "FAILED":
                    continue
                filtered_emails.append(key)
            ped = make_aware(end_of_day(parse(date, dayfirst=True)))

            statement_lock_task = ClientNotificationAccessor(
                client_id
            ).get_notification_status(Notification.STATEMENTS_LOCKED_NOTIFICATION)
            is_email_statement_lock_enabled = statement_lock_task["email"]
            is_slack_statement_lock_enabled = statement_lock_task["slack"]
            is_ms_teams_statement_lock_enabled = statement_lock_task["ms_teams"]

            if is_notification_v2(client_id):
                notify_statements_lock_to_payees_v2(
                    client_id,
                    payee_email_ids=filtered_emails,
                    ped=ped,
                    logged_in_user=logged_email,
                )
            else:
                if is_email_statement_lock_enabled:
                    payout_locked_mails(client_id, logged_email, filtered_emails, ped)
                ## Send MS-Teams/Slack Notification to payees
                if is_ms_teams_statement_lock_enabled:
                    for email in filtered_emails:
                        try:
                            logger.info(
                                f"BEGIN: post_commissions_frozen_for_payee_notification_on_msteams for {email} on MS-Teams"
                            )
                            post_commissions_frozen_for_payee_notification_on_msteams(
                                client_id, email, ped
                            )
                            logger.info(
                                f"END: post_commissions_frozen_for_payee_notification_on_msteams for {email} on MS-Teams"
                            )
                        except Exception as error:
                            logger.error(
                                f"EXCEPTION: STATEMENTS LOCKED MSTEAMS NOTIFICATION - {error}"
                            )
                            logger.error(
                                f"EXCEPTION TB: STATEMENTS LOCKED MSTEAMS NOTIFICATION traceback- {traceback.print_exc()}"
                            )
                else:
                    logger.info(
                        "SKIPPED: STATEMENTS LOCKED MSTEAMS NOTIFICATION NOT ENABLED"
                    )
                if is_slack_statement_lock_enabled:
                    for email in filtered_emails:
                        try:
                            logger.info(
                                f"BEGIN: post_commissions_frozen_for_payee_notification_on_slack for {email} on Slack"
                            )
                            post_commissions_frozen_for_payee_notification_on_slack(
                                client_id, email, ped
                            )
                            logger.info(
                                f"END: post_commissions_frozen_for_payee_notification_on_slack for {email} on Slack"
                            )
                        except Exception as error:
                            logger.error(
                                f"EXCEPTION: STATEMENTS LOCKED SLACKNOTIFICATION - {error}"
                            )
                            logger.error(
                                f"EXCEPTION TB: STATEMENTS LOCKED SLACK NOTIFICATION traceback- {traceback.print_exc()}"
                            )
                else:
                    logger.info(
                        "SKIPPED: STATEMENTS LOCKED SLACK NOTIFICATION NOT ENABLED"
                    )

        error = []

        if is_pending_changes:
            # We return the details of the payees with pending changes
            if len(pending_changes_payees_set) == 1:
                return Response(
                    {
                        "message_code": "LOCK_RESTRICTED_SINGLE_PAYEE_MESSAGE",
                        "pending_changes_file_content": pending_changes_file_content,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {
                    "message_code": "LOCK_PARTIALLY_COMPLETED_MESSAGE",
                    "display_message": f"Statement locking was successful for {total_payees - len(pending_changes_payees_set)} payee(s). However, {len(pending_changes_payees_set)} payee(s) with unsynced commission-impacting changes could not be locked (see downloaded CSV). Run commission sync for these payees or all payees in this payout period, then return to complete locking their statements. ",
                    "pending_changes_file_content": pending_changes_file_content,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if settlement_response.status_code != 201:
            error.append("Settlement Lock Failure")
        if commission_response.status_code != 201:
            error.append("Commission Lock Failure")
        if (
            commission_response.status_code == 201
            and settlement_response.status_code == 201
        ):
            return Response(output_data, status=status.HTTP_201_CREATED)
        else:
            return Response(error, status=status.HTTP_400_BAD_REQUEST)
