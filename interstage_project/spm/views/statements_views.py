import logging
import os
import traceback

from dateutil.parser import parse
from django.db import transaction
from django.http import FileResponse, HttpResponse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.utils.timezone import make_aware
from rest_framework import serializers, status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils import date_utils, end_of_day, start_of_day
from commission_engine.utils.general_data import (
    RESPONSE__PERMISSION_DENIED,
    RBACComponent,
    RbacPermissions,
    SegmentEvents,
    SegmentProperties,
    StatementsPdf,
)
from interstage_project.auth_utils import (
    authorize_for_profile_lookup,
    authorize_for_profile_lookup_v2,
    requires_scope,
)
from interstage_project.utils import add_log_context_view
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.services import (
    audit_services,
    bulk_download_statements_services,
    email_statements_service,
)
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.rbac_services import get_ui_permissions, is_payout_value_permission
from spm.services.settlement_actions_service.settlement_total_service import (
    SettlementTotalService,
)
from spm.services.statements_export_service import (
    ExportStatements,
    export_statements_as_pdf,
)
from spm.utils import delete_temp_file


class ExportAsPdfSerializer(serializers.Serializer):
    """
    Validates request payload for the export as pdf request
    """

    payee_email = serializers.EmailField()
    # Default is iso-8601 format (YYYY-MM-DD)
    psd = serializers.DateField()
    ped = serializers.DateField()
    is_settlement_view = serializers.BooleanField(default=False)  # type: ignore


class ExportStatementsPDF(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.EXPORT_STATEMENT.value,
                RbacPermissions.EXPORT_PAYOUTS.value,
            ]
        ),
        name="dispatch",
    )
    @add_log_context_view("StatementsExport")
    def post(self, request):
        try:
            logger = request.logger
            logger.info("BEGIN: Exporting statements as pdf")
            serializer = ExportAsPdfSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            client_id = request.client_id
            login_user_id = str(request.user)
            payee_email_id = serializer.validated_data["payee_email"]

            # Check for data permission of logged in user: payout statements
            is_authorized = authorize_for_profile_lookup_v2(
                client_id=client_id,
                login_user_id=login_user_id,
                email_id=payee_email_id,
                component=RBACComponent.PAYOUTS_STATEMENTS.value,
            )
            if not is_authorized:
                logger.info(
                    f"User: {login_user_id} don't have permission to email statements of { payee_email_id}"
                )
                return Response(
                    RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
                )

            pdf_bytes = export_statements_as_pdf(
                client_id=client_id,
                login_user_id=login_user_id,
                **serializer.validated_data,
            )
            audit_services.log(
                client_id=client_id,
                event_type_code=EVENT["EXPORT_STATEMENTS"]["code"],
                event_key=payee_email_id,
                summary="Pdf export successful for payee_email: {} for period: {}".format(
                    payee_email_id,
                    serializer.validated_data["ped"],
                ),
                updated_by=login_user_id,
                updated_at=timezone.now(),
                audit_data=request.data,
            )
            logger.info("END: Exporting statements as pdf")
            response = HttpResponse(content_type="application/pdf")
            response["Content-Disposition"] = "attachment;"
            response.write(pdf_bytes)
            return response
        except Exception as e:
            logger.error("Error while exporting statements as pdf")
            delete_temp_file(request.data.get("payee_email"))
            traceback.print_exc()
            return Response({"error": str(e)}, status=500)


class StatementsExport(APIView):
    """
    Export statements for a payee for a particular period
    """

    @staticmethod
    def audit(client_id, event_key, summary, updated_by, audit_data):
        event_type_code = EVENT["EXPORT_STATEMENTS"]["code"]

        updated_at = timezone.now()
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.EXPORT_STATEMENT.value,
                RbacPermissions.EXPORT_PAYOUTS.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @add_log_context_view("StatementsExport")
    def post(self, request):
        logger = request.logger
        try:
            client_id = request.client_id
            payee_email = request.data["payee_email"]
            login_user_id = str(request.user)
            is_payout_value = is_payout_value_permission(client_id, login_user_id)
            is_authorized = authorize_for_profile_lookup_v2(
                client_id,
                login_user_id,
                payee_email,
                RBACComponent.PAYOUTS_STATEMENTS.value,
            )

            if not is_payout_value and login_user_id != payee_email:
                logger.info(
                    f"User {login_user_id} don'th have permission to view payout value of others"
                )
                return Response(
                    {
                        "status": "You are not authorized to perform this action.",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            if is_authorized:
                psd = request.data["psd"]
                ped = request.data["ped"]
                is_settlement_view = request.data.get("is_settlement_view", False)
                csv_file_name = payee_email + "_" + ped
                psd = make_aware(start_of_day(parse(psd, dayfirst=True)))
                ped = make_aware(end_of_day(parse(ped, dayfirst=True)))

                excel_file = ExportStatements(
                    client_id, payee_email, psd, ped, login_user_id, is_settlement_view
                ).export_statements_data()

                logger.info(
                    "Export successful for payee: {} for period: {}".format(
                        payee_email, ped
                    )
                )

                self.audit(
                    client_id,
                    event_key=payee_email,
                    summary="Export successful for payee_email: {} for period: {}".format(
                        payee_email, ped.date()
                    ),
                    updated_by=request.audit["updated_by"],
                    audit_data=request.data,
                )
                # client_data = get_client(client_id)
                # fiscal_start = client_data.fiscal_start_month
                # fiscal_year = get_fiscal_year(fiscal_start, psd)
                # period = get_period_for_statement(psd, ped, fiscal_start, fiscal_year)
                analytics_data = {
                    "user_id": request.audit["updated_by"],
                    "event_name": SegmentEvents.EXPORT_STATEMENTS_CSV.value,
                    "event_properties": {
                        SegmentProperties.PERIOD.value: ped.strftime("%d-%b-%Y"),
                        SegmentProperties.PAYEE_NAME.value: payee_email,
                    },
                }
                analytics = CoreAnalytics(analyser_type="segment")
                analytics.send_analytics(analytics_data)

                return FileResponse(
                    excel_file, as_attachment=True, filename=csv_file_name
                )
            else:
                return Response(
                    {
                        "status": "You are not authorized to perform this action.",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )
        except Exception as exception:
            logger.error(
                "Error while exporting statements for payee: {} period- {} exception - {}".format(
                    request.data["payee_email"], request.data["ped"], exception
                )
            )
            logger.error(
                "export statement traceback - {}".format(traceback.print_exc())
            )
            return Response(
                {"status": "Error while exporting statements "},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EmailStatements(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value),
        name="dispatch",
    )
    def post(self, request):
        logger = logging.getLogger(__name__)

        client_id = request.client_id
        logged_in_user = str(request.user)

        payee_email_id = request.data["payee_email"]
        psd = request.data["psd"]
        ped = request.data["ped"]
        recipient_email_id = request.data["recipient_email"]
        is_settlement_view = request.data.get("is_settlement_view", False)
        psd = make_aware(start_of_day(parse(psd, dayfirst=True)))
        ped = make_aware(end_of_day(parse(ped, dayfirst=True)))

        # Check for data permission of logged in user: payout statements
        is_authorized = authorize_for_profile_lookup(
            client_id=client_id,
            login_user_id=logged_in_user,
            email_id=payee_email_id,
            component=RBACComponent.PAYOUTS_STATEMENTS.value,
            effective_date=ped,
        )
        if not is_authorized:
            logger.info(
                "User: %s don't have permission to email statements of %s",
                logged_in_user,
                payee_email_id,
            )
            return Response(
                RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN
            )

        try:
            response_value, is_success = email_statements_service.email_statements(
                client_id,
                logged_in_user,
                payee_email_id,
                psd,
                ped,
                is_settlement_view,
                recipient_email_id=recipient_email_id,
            )

            resp_status = (
                status.HTTP_200_OK if is_success else status.HTTP_400_BAD_REQUEST
            )
            return Response(response_value, status=resp_status)

        except Exception as exception:
            logger.error(
                "Error emailing statements: %s period- %s",
                payee_email_id,
                ped.strftime("%B %Y"),
            )
            logger.exception(exception)
            return Response(
                {"status": "Error in emailing statements"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class BulkEmailStatements(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value),
        name="dispatch",
    )
    def post(self, request):
        logger = logging.getLogger(__name__)

        client_id = request.client_id
        logged_in_user = str(request.user)

        search_term = request.data.get("search_term", "")
        filters = request.data.get("filters", {})
        is_selected_all = request.data["is_selected_all"]
        date = request.data["date"]
        supabase_task_id = request.data["task_id"]
        ped = make_aware(end_of_day(parse(date, dayfirst=True)))
        payee_email_ids = request.data.get("employee_email_ids", [])

        try:
            response_value, is_success = email_statements_service.bulk_email_statements(
                client_id,
                logged_in_user,
                supabase_task_id,
                search_term,
                filters,
                ped,
                is_selected_all,
                payee_email_ids,
            )  # type: ignore
            if is_success:
                return Response(response_value, status=status.HTTP_200_OK)
            else:
                return Response(response_value, status=status.HTTP_400_BAD_REQUEST)

        except Exception as exception:
            logger.error(
                "Error in bulk emailing statements: %s period- %s",
                payee_email_ids,
                ped.strftime("%B %Y"),
            )
            logger.exception(exception)
            return Response(
                {"status": "Error in bulk email statements"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class EmailStatementsTestStatus(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_PAYOUTS.value),
        name="dispatch",
    )
    def post(self, request):
        """
        View to get the status and employee email for a list of record IDs based on client ID.
        Payload format:
        {
            "client_id": 10140   # The client ID
        }
        Response format:
        [
            {
                "status": "SUCCESS",
                "emp_id": "<EMAIL>"
            },
            {
                "status": "SUCCESS",
                "emp_id": "<EMAIL>"
            }
        ]
        """
        # Extract the payload data
        client_id = request.data.get("client_id")

        try:
            # Call the service to fetch data
            result = email_statements_service.get_payee_email_and_status_by_client_id(
                client_id
            )

            return Response(result)

        except Exception as e:
            # Handle any errors during processing
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BulkS3UploadStatements(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.EXPORT_STATEMENT.value,
                RbacPermissions.EXPORT_PAYOUTS.value,
            ]
        ),
        name="dispatch",
    )
    def post(self, request):
        logger = logging.getLogger(__name__)
        client_id = request.client_id
        logged_in_user = str(request.user)
        date = request.data["ped"]
        limit = request.data.get("limit_value", None)
        offset = request.data.get("offset_value", None)
        export_type = request.data.get("export_type", "xlsx")
        payees = request.data.get("payees", [])
        bulk_key = request.data.get("bulk_key", timezone.now().strftime("%Y%m%d%H%M%S"))
        ped = make_aware(end_of_day(parse(date, dayfirst=True)))
        try:
            (
                response_value,
                is_success,
            ) = email_statements_service.upload_statements_to_s3_wrapper(
                client_id,
                ped,
                logged_in_user,
                limit,
                offset,
                export_type,
                payees,
                bulk_key=bulk_key,
            )
            if is_success:
                return Response(response_value, status=status.HTTP_200_OK)
            else:
                return Response(response_value, status=status.HTTP_400_BAD_REQUEST)

        except Exception as exception:
            logger.error(
                "Error in bulk uploading statements to S3 period- %s, exception - %s",
                ped.strftime("%B %Y"),
                exception,
            )
            logger.exception(exception)
            logger.error(
                "bulk upload statement traceback - {}".format(traceback.print_exc())
            )
            return Response(
                {"status": "Error in bulk uploading statements"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class BulkStatementsEmailDownload(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_PAYOUTS.value]),
        name="dispatch",
    )
    def post(self, request):
        logger = logging.getLogger(__name__)
        client_id = request.client_id
        logged_in_user = str(request.user)
        date = request.data["ped"]
        email_id = request.data["email_id"]
        export_type = request.data.get("export_type", "xlsx")
        payees = request.data.get("payees", [])
        bulk_key = request.data.get("bulk_key", timezone.now().strftime("%Y%m%d%H%M%S"))
        ped = make_aware(end_of_day(parse(date, dayfirst=True)))
        data_to_validate = {
            "logged_in_user": logged_in_user,
            "client_id": client_id,
            "ped": ped,
            "export_type": export_type,
            "payees": payees,
            "email_id": email_id,
        }

        try:
            bulk_download_statements_services.BulkDownloadStatementModel.model_validate(
                data_to_validate
            )
            option_status = bulk_download_statements_services.check_sync_running_status(
                client_id
            )
            if option_status:
                return Response(
                    {
                        "message": "Statements cannot be download while commission sync is in progress. Please try later."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            user_permissions = get_ui_permissions(client_id, logged_in_user)
            if RbacPermissions.EXPORT_PAYOUTS.value not in user_permissions:
                return Response(
                    {"message": "You are not authorized to perform this action."},
                    status=status.HTTP_403_FORBIDDEN,
                )
            task_id = bulk_download_statements_services.bulk_statements_zip_wrapper(
                client_id,
                ped,
                logged_in_user,
                export_type,
                payees,
                bulk_key=bulk_key,
                email_id=email_id,
            )

            return Response(
                {
                    "message": "Your request is being processed. You will receive an email shortly.",
                    "task_id": task_id,
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception:
            logger.exception("Error in creating bulk statement download task")
            return Response(
                {"message": "Error in bulk statement download. Please cntact support."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class StatementPayoutTransactionData(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_PAYOUTS.value,
                RbacPermissions.VIEW_STATEMENTS.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, request, employee_email_id, ped):
        logger = logging.getLogger(__name__)
        logger.info("Statements: BEGIN: Payouts Transaction Data")
        payee_email = employee_email_id
        logged_in_user = request.user.username
        ped_date = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
        client_id = request.client_id
        is_authorized = authorize_for_profile_lookup(
            client_id,
            logged_in_user,
            payee_email,
            RBACComponent.PAYOUTS_STATEMENTS.value,
        )
        if is_authorized:
            paid_amount_data = SettlementTotalService(
                client_id, ped_date, [payee_email]
            ).get_payouts_view_data()
            logger.info("Statements: END: Payouts Transaction Data")
            statement_data = (
                paid_amount_data[payee_email] if payee_email in paid_amount_data else []
            )

            return Response(
                {"data": statement_data},
                status=status.HTTP_200_OK,
            )
        return Response(RESPONSE__PERMISSION_DENIED, status=status.HTTP_403_FORBIDDEN)
