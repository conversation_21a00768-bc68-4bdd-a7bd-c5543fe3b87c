import json
import logging
import os
import traceback
import uuid
from typing import Any, List, TypedDict

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

from commission_engine.accessors.client_accessor import (
    get_client_features,
    get_client_fiscal_start_month,
    get_clients_by_ids,
)
from commission_engine.utils.general_data import (
    STATUS_CODE,
    DashboardType,
    RbacPermissions,
)
from spm.accessors.config_accessors.employee_accessor import (
    EmployeeAccessor,
    EmployeePayrollAccessor,
)
from spm.accessors.dashboard_accessor import (
    DashboardAccessor,
    DashboardUserGroupMapAccessor,
    DashboardUserMapAccessor,
)
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.serializers.dashboard_serializers import (
    DashboardSerializer,
    DashboardUserGroupMapBulkSerializer,
    DashboardUserMapBulkSerializer,
)
from spm.services import audit_services, user_group_service
from spm.services.rbac_services import get_ui_permission_by_role, get_user_permissions
from spm.utils import filter_employees_for_share, filter_users_with_dashbord_permissions
from superset.custom_exceptions.exceptions import SupersetExceptions
from superset.services.dashboard_api_services import SupersetApi
from superset.services.db_meta_data_services import get_dataset_id_for_datasheet

SUPERSET_HOST_URL = os.getenv("SUPERSET_HOST", "http://localhost:9000")

logger = logging.getLogger(__name__)


@transaction.atomic
def custom_delete_dashboard(request):
    """
    This method is used to delete dashboard
    Checks for dashboard type and deletes the dashboard accordingly
    If it is a superset dashboard, it deletes the dashboard from superset and then from DB,
    Else it considers it as a custom dashboard and deletes it from DB
    """

    if request.data.get("dashboard_type") == DashboardType.SUPERSET_DASHBOARD.value:
        result = DashboardAccessor(request.client_id).dashboard_type_id_aware(
            request.data.get("dashboard_id"),
            request.data.get("superset_dashboard_id"),
        )
        if not result:
            logger.info(
                "Dashboard not found for deletion: %s", request.data.get("dashboard_id")
            )
            return Response(
                {"status": "FAILURE", "message": "Dashboard not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        resp = delete_analytics_dashboard(request)
        if resp.status_code != status.HTTP_201_CREATED:
            logger.exception("Failed to delete dashboard in Everstage DB")
            return Response(
                {
                    "status": "FAILURE",
                    "message": "Failed to delete dashboard in Everstage DB",
                },
                status=resp.status_code,
            )
        response = SupersetApi().delete_dashboard_superset(
            request.data.get("superset_dashboard_id")
        )
        if response.status_code == status.HTTP_200_OK:
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_201_CREATED,
            )
        # Failed to delete dashboard in Superset: return error response with specific message
        logger.exception("Error while deleting dashboards in Superset")
        message = response.json().get("message", "Superset deletion failed")
        transaction.set_rollback(True)
        return Response(
            {"status": "FAILURE", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    else:
        return delete_analytics_dashboard(request)


def get_guest_token(dash_id, ev_token, client_id, email):
    """
    This method is used to get guest token for a dashboard from superset

    params:
        dash_id: The dash id of which the token is to be generated
        ev_token: ev_token of the user
    """

    if dash_id is None or ev_token is None:
        logger.exception("Dashbord Id or Token is Missing")
        return Response(
            {"Message": "Invalid Token/Id", "status": "FAILURE"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    try:
        fiscal_start_month = get_client_fiscal_start_month(client_id)
        payload = json.dumps(
            {
                "user": {
                    "username": "",
                    "first_name": "",
                    "last_name": "",
                },
                "resources": [
                    {
                        "type": "dashboard",
                        "id": dash_id,
                    }
                ],
                "rls": [],
                "client_id_from_ev_token": client_id,
                "email_id_from_ev_token": email,
                "fiscal_start_month": str(fiscal_start_month),
            }
        )
        response = SupersetApi().fetch_guest_token_superset(payload)
        token = response.json()["token"]
        return Response(
            {"token": token, "status": "SUCCESS"},
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.exception("Invalid Token/Id  - %s", str(e))
        return Response(
            {"Message": "Invalid Token/Id", "status": "FAILURE"},
            status=status.HTTP_400_BAD_REQUEST,
        )


def get_dashboard_details_for_datasheet_id(*, client_id, datasheet_id):
    dataset_id = get_dataset_id_for_datasheet(
        client_id=client_id, datasheet_id=datasheet_id
    )
    if dataset_id is None:
        return []
    dashboard_details = DashboardAccessor(
        client_id
    ).get_dashboard_details_from_dataset_id(dataset_id=dataset_id)

    return [
        {
            "name": dashboard["name"],
            "url": f"/dashboard?id={dashboard['dashboard_id']}&type=superset_dashboard",
        }
        for dashboard in dashboard_details
    ]


class DashboardData(TypedDict):
    shared_user_groups: List[str]
    shared_users: List[str]
    dashboard_type: DashboardType


class DashboardDataIn(DashboardData):
    created_by: str


class DashboardDataOutAdmin(TypedDict):
    shared_users: List[str]
    created_by: dict
    all_users: List[Any]
    count_all_users: int


class DashboardDataOutPayee(TypedDict):
    created_by: dict


def get_shared_user_groups(client_id, shared_user_groups, dashboard_type):
    """
    This method is used to get the users in the shared user groups for a dashboard
    who have the required permissions

    Args:
        client_id (int): client id
        shared_user_groups List(str): The list of user_groups
        dashboard_type (str): The type of the dashboard

    Returns:
        Set(str): The list of users who have access to the dashboard
    """
    total_users = set()
    try:
        logger.info("Getting shared user groups for client %s", client_id)
        for group_id in shared_user_groups:
            users = user_group_service.UserGroupMemberService(
                client_id
            ).get_user_group_members_emails(group_id)
            # Passing users in shared_user_groups to filter_users_with_dashbord_permissions
            # will return the users who have the permission to view the dashboard
            updated_users_from_usergroup = filter_users_with_dashbord_permissions(
                client_id,
                users,
                dashboard_type,
                user_group=True,
            )
            total_users.update(set(updated_users_from_usergroup))
        return total_users
    except Exception:
        logger.exception("Exception while getting shared user groups")
        return total_users


def get_shared_users(client_id, shared_users, dashboard_type):
    """
    This method is used to get the shared users for a dashboard who have the required
    permissions

    Args:
        client_id (int): client id
        shared_users List(str): The list of users who have shared access
        dashboard_type (str): The type of the dashboard

    Returns:
        List(str): The list of users who have access to the dashboard
    """
    updated_users = []
    try:
        logger.info("Getting shared users for client %s", client_id)
        # Passing shared_users to filter_users_with_dashbord_permissions will return the
        # shared_users who have the permission to view the dashboard
        updated_users = filter_users_with_dashbord_permissions(
            client_id,
            shared_users,
            dashboard_type,
        )
        return updated_users
    except Exception:
        logger.exception("Exception while getting shared users")
        return updated_users


def get_dashboard_by_id(client_id, dashboard_id, user_email):
    """
    This method is used to get the dashboard by id

    params:
        client_id: client id
        dashboard_id: The id of the dashboard
    """

    client_features = get_client_features(client_id=client_id)
    superset_admin_permissions = bool(client_features.get("show_superset_dashboard"))
    if superset_admin_permissions is False:
        logger.exception("Access is denied")
        return Response(
            {"status": "FAILURE"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    user_role = EmployeeAccessor(client_id).get_employee_role(user_email)
    ui_permissions = get_ui_permission_by_role(client_id, user_role)
    is_admin = RbacPermissions.MANAGE_ANALYTICS.value in ui_permissions
    try:
        dashboard_id = uuid.UUID(dashboard_id)
        dashboard_data = DashboardAccessor(client_id).get_dashboards_by_ids(
            [dashboard_id], dashboard_type=DashboardType.ALL_DASHBOARDS.value
        )

        if dashboard_data:
            users_dict = DashboardUserMapAccessor(
                client_id
            ).get_dashboard_user_email_id_map([dashboard_id])
            user_group_dict = DashboardUserGroupMapAccessor(
                client_id
            ).get_dashboard_user_group_id_map([dashboard_id])

            for record in dashboard_data:
                dashboard_id = record["dashboard_id"]
                shared_users = (
                    users_dict[dashboard_id] if dashboard_id in users_dict else []
                )
                shared_user_groups = (
                    user_group_dict[dashboard_id]
                    if dashboard_id in user_group_dict
                    else []
                )
                if is_admin:
                    record["shared_users"] = shared_users
                    record["shared_user_groups"] = shared_user_groups
                additional_fields = add_additional_fields(
                    client_id,
                    data={
                        "shared_users": shared_users,
                        "shared_user_groups": shared_user_groups,
                        "dashboard_type": record["dashboard_type"],
                        "created_by": record["created_by"],
                    },
                    is_admin=is_admin,
                )
                record.update(additional_fields)

            return Response(
                {
                    "data": dashboard_data.pop(),
                    "fiscal_offset": get_client_fiscal_start_month(client_id),
                    "superset_host_url": SUPERSET_HOST_URL,
                    "status": "SUCCESS",
                },
                status=status.HTTP_200_OK,
            )

        else:
            logger.exception("No data for the requested dashboard id")
            return Response(
                {
                    "data": "No dashboard present with this id",
                    "superset_host_url": SUPERSET_HOST_URL,
                    "status": "FAILURE",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        logger.exception("Dashboard id is not valid - %s", str(e))
        return Response(
            {"status": "FAILURE"},
            status=status.HTTP_400_BAD_REQUEST,
        )


def get_default_dashboards(client_id: str, permissions: dict):
    """
    This returns the default dashboards for the client

    Args:
        client_id: int(client id)
        permissions: The permissions of the user

    Returns:
        default_dashboards (list): list of default dashboards
    """
    logger.info("Getting default dashboards for the client %s", str(client_id))
    default_dashboards = []
    # Admin Default Dashboard : We add the default dashboards only if the user has
    # "show default payee dashboard" permission (i.e "view:payeedashboard" permission)
    if RbacPermissions.VIEW_ADMINDASHBOARD.value in permissions["permissions"]:
        default_dashboards.append(
            {
                "name": (get_clients_by_ids([client_id]).first()).name + " Metrics",
                "is_default": True,
                "created_by": "Everstage",
                "dashboard_type": "admin_dashboard",
            }
        )
    # Payee Default Dashboard
    if RbacPermissions.VIEW_PAYEEDASHBOARD.value in permissions["permissions"]:
        default_dashboards.append(
            {
                "name": "My Metrics",
                "is_default": True,
                "created_by": "Everstage",
                "dashboard_type": "payee_dashboard",
            }
        )
    return default_dashboards


def get_dashboard_ids(client_id, user):
    """
    This function returns the list of dashboard ids based on the user

    Args:
        client_id (int): client id
        user (str): user email id

    Returns:
        dashboard_ids (list): list of dashboard ids
    """
    logger.info("Fetching dashboard ids for the user - %s", user)
    dashboard_ids = set()
    user_groups = user_group_service.UserGroupMemberService(
        client_id
    ).get_user_groups_of_user(user)
    if user_groups:
        dashboard_ids.update(
            DashboardUserGroupMapAccessor(
                client_id
            ).get_dashboard_ids_by_user_group_ids(user_groups)
        )
    dashboard_ids.update(
        DashboardUserMapAccessor(client_id).get_dashboard_ids_by_user_email_id(user)
    )
    return dashboard_ids


def get_dashboards_by_permissions(client_id, dashboard_ids, user_dashboard_permissions):
    """
    This function returns the list of dashboards based on the permissions of the user

    Args:
        client_id (int): client id
        dashboard_ids (list): list of dashboard ids
        user_dashboard_permissions (dict): dictionary of user permissions

    Returns:
        list: list of dashboards based on the permissions of the user
    """
    logger.info("Fetching dashboards by permissions for client id - %s", str(client_id))
    dashboards_list = []
    if user_dashboard_permissions["superset_admin_permissions"] is True:
        dashboards_list = DashboardAccessor(client_id).get_all_dashboard_info()
    if user_dashboard_permissions["superset_permissions"] is True:
        dashboards_list = dashboards_list + DashboardAccessor(
            client_id
        ).get_dashboards_by_ids(dashboard_ids)

    return dashboards_list


def get_all_dashboards_without_additional_info(
    client_id: str,
    user: str,
    dashboard_type: DashboardType,
):
    """
    This method is used to get all the dashboards details required to populate
    the dashbaord dropdown in everheader

    params:
        client_id: The client id of the target datasheet
        user: The email id of the user
        dashboard_type: The type of the dashboard (Superset/Custom/All)

    Returns:
        list: list of dashboards details (default,custom and superset)
    """
    default_dashboards, dashboards_list = get_all_dashboards(
        client_id, user, dashboard_type
    )
    return default_dashboards + dashboards_list


def get_all_dashboards_with_additional_info(
    client_id: str,
    user: str,
    dashboard_type: DashboardType,
):
    """
    This method is used to get all the dashboards

    params:
        client_id: The client id of the target datasheet
        user: The email id of the user
        dashboard_type: The type of the dashboard (Superset/Custom/All)

    Returns:
        list: list of dashboards details (default,custom and superset) with
        additional fields (shared_users,shared_user_groups,created_by)
    """
    default_dashboards, dashboards_list = get_all_dashboards(
        client_id, user, dashboard_type
    )
    users_dict = DashboardUserMapAccessor(client_id).get_dashboard_user_email_id_map()
    user_group_dict = DashboardUserGroupMapAccessor(
        client_id
    ).get_dashboard_user_group_id_map()
    user_role = EmployeeAccessor(client_id).get_employee_role(user)
    ui_permissions = get_ui_permission_by_role(client_id, user_role)
    is_admin = RbacPermissions.MANAGE_ANALYTICS.value in ui_permissions
    for record in dashboards_list:
        dashboard_id = record["dashboard_id"]
        shared_users = users_dict[dashboard_id] if dashboard_id in users_dict else []
        shared_user_groups = (
            user_group_dict[dashboard_id] if dashboard_id in user_group_dict else []
        )
        if is_admin:
            record["shared_users"] = shared_users
            record["shared_user_groups"] = shared_user_groups
        additional_fields = add_additional_fields(
            client_id,
            data={
                "shared_users": shared_users,
                "shared_user_groups": shared_user_groups,
                "dashboard_type": record["dashboard_type"],
                "created_by": record["created_by"],
            },
            is_admin=is_admin,
        )

        record.update(additional_fields)
    all_dashboards = default_dashboards + dashboards_list
    return all_dashboards


def get_all_dashboards(
    client_id: str,
    user: str,
    dashboard_type: DashboardType,
):
    """
    * This method is used to get all the dashboards from the database
    * This method checks for respective admin permissions of the client and Rbac
    permissions of the user
    * if admin permissions are enabled and Rbac permissions are enabled - All dashboards
    of respective dashboard type are returned
    * if admin permissions are enabled and Rbac permissions are disabled - Only the
    shared dashboard is returned
    * if dashboard_type is all - All dashboards of respective dashboard type are
    returned with the required permissions

    params:
        client_id: The client id of the target datasheet
        user: The email id of the user
        dashboard_type: The type of the dashboard (Superset/Custom/All)
    """

    client_features = get_client_features(client_id=client_id)
    superset_enabled = bool(client_features.get("show_superset_dashboard"))

    permissions = get_user_permissions(client_id, user)
    default_dashboards = get_default_dashboards(client_id, permissions)
    # Return if the user has only default dashboard permissions
    if dashboard_type == DashboardType.DEFAULT_DASHBOARD.value:
        return default_dashboards, []

    superset_admin_permissions = False
    superset_permissions = False
    if superset_enabled:
        superset_admin_permissions = (
            RbacPermissions.MANAGE_ANALYTICS.value in permissions["permissions"]
        )
        superset_permissions = not superset_admin_permissions
    user_dashboard_permissions = {
        "superset_admin_permissions": superset_admin_permissions,
        "superset_permissions": superset_permissions,
    }
    dashboard_ids = set()
    dashboard_ids.update(get_dashboard_ids(client_id, user))

    dashboards_list = get_dashboards_by_permissions(
        client_id,
        dashboard_ids,
        user_dashboard_permissions,
    )

    return default_dashboards, dashboards_list


def add_additional_fields(
    client_id, data: DashboardDataIn, is_admin: bool
) -> DashboardDataOutAdmin | DashboardDataOutPayee:
    """
    This method is used to add additional fields like shared user details, created_by
    and so on to the dashboard data
    """
    total_users = set()
    total_users.update(
        get_shared_user_groups(
            client_id, data["shared_user_groups"], data["dashboard_type"]
        )
    )
    updated_users = get_shared_users(
        client_id, data["shared_users"], data["dashboard_type"]
    )
    total_users.update(updated_users)
    all_users = EmployeeAccessor(client_id).get_employees(total_users)
    created_by = EmployeeAccessor(client_id).get_employee(data["created_by"])

    designation = EmployeePayrollAccessor(client_id).get_employee_designation(
        data["created_by"]
    )
    if len(designation) == 0:
        designation = "Admin"
    else:
        designation = designation.first()["designation"]
    if is_admin:
        return {
            "shared_users": updated_users,
            "all_users": all_users,
            "count_all_users": len(total_users),
            "created_by": {
                "first_name": created_by.first_name if created_by else "Deactivated",
                "last_name": created_by.last_name if created_by else "User",
            },
        }
    else:
        return {
            "created_by": {
                "first_name": created_by.first_name if created_by else "Deactivated",
                "last_name": created_by.last_name if created_by else "User",
            },
        }


def clone_object(current_object, curr_time, request_audit):
    current_object.pk = None
    current_object._state.adding = True  # pylint: disable=protected-access
    current_object.knowledge_begin_date = curr_time
    current_object.additional_details = request_audit
    return current_object


def update_dashboard(
    client_id,
    audit_data,
    dashboard_data,
):
    """
    This method is used to update the dashboard in everstage db

    params:
        client_id -> str: client id
        audit_data -> dict: audit data
        dashboard_data -> dict: dashboard data
        superset_dataset_ids -> list: list of superset dataset ids

    returns:
        status: status of the dashboard updation
    """
    superset_dashboard_id = dashboard_data.get("superset_dashboard_id")
    dashboard_title = dashboard_data.get("name", "")
    time = timezone.now()
    dashboard = DashboardAccessor(client_id).get_dashboard_by_superset_id(
        superset_dashboard_id
    )
    if dashboard:
        dashboard = clone_object(dashboard, time, audit_data)
        dashboard.name = dashboard_title
        dashboard.last_modified = time
        dashboard.superset_dataset_ids = dashboard_data.get("superset_dataset_ids", [])
        DashboardAccessor(client_id).invalidate_dashboard(dashboard.dashboard_id, time)
        DashboardAccessor(client_id).persist_dashboard(dashboard)
        audit_services.log(
            client_id=client_id,
            event_type_code=EVENT["UPDATE_DASHBOARD"]["code"],
            event_key=dashboard.dashboard_id,
            summary="Superset Dashboard updated",
            updated_by=audit_data["updated_by"],
            updated_at=time,
            audit_data=audit_data,
        )
        logger.info("Dashboard is successfully updated")
        return STATUS_CODE.SUCCESS.value

    else:
        logger.error(
            "The specified superset_dashboard_id %s for updation does not exist",
            superset_dashboard_id,
        )
        raise SupersetExceptions(
            code=f"Dashboard with id {superset_dashboard_id} not found for updation",
        )


def is_dashboard_name_exists(client_id, dashboard_name):
    db_names = DashboardAccessor(client_id).get_all_dashboard_names()
    db_names = [name.lower() for name in db_names]
    if dashboard_name.lower() in db_names:
        return True
    return False


def rename_analytics_dashboard(request, rename=["name"]):
    time = timezone.now()
    client_id = request.client_id
    dashboard_id = request.data.get("dashboard_id")
    new_name = {"dashboard_id": dashboard_id}
    if "name" in rename:
        new_name["dashboard_name"] = request.data.get("name")
    if "last_modified" in rename:
        new_name["last_modified"] = request.data.get("last_modified")

    dashboard = DashboardAccessor(client_id).get_dashboard_by_id(dashboard_id)
    logger = request.logger
    logger.update_context(new_name)
    if dashboard:
        dashboard = clone_object(dashboard, time, request.audit)
        if "name" in rename:
            dashboard.name = new_name["dashboard_name"]
        if "last_modified" in rename:
            dashboard.last_modified = new_name["last_modified"]

        try:
            DashboardAccessor(client_id).invalidate_dashboard(dashboard_id, time)
            DashboardAccessor(client_id).persist_dashboard(dashboard)
            logger.info("Dashboard is successfully renamed")
            return Response(
                {
                    "status": "SUCCESS",
                    "dashboard_id": dashboard_id,
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logger.error(
                "Exception in renaming dashboard: {}".format(dashboard_id),
            )
            raise SQLParseError() from e
    else:
        raise ObjectDoesNotExist


def create_dashboard(client_id, audit_data, dashboard_data):
    """
    This method is used to create a single dashboard or in bulk in everstage db

    params:
        client_id -> str: client id
        audit_data -> dict: audit data
        dashboard_data -> List[dict]: dashboard data

    returns:
        status: status of the dashboard creation
    """
    dashboard_type = DashboardType.SUPERSET_DASHBOARD.value
    time = timezone.now()
    dashboard_object = DashboardAccessor(client_id)
    fields_to_add = {
        "dashboard_type": dashboard_type,
        "last_modified": time,
        "created_at": time,
        "knowledge_begin_date": time,
        "additional_details": audit_data,
        "client": client_id,
    }

    dashboard_ids = []
    for dash in dashboard_data:
        dash.update(fields_to_add)
        dashboard_ids.append(dash["dashboard_id"])

    dashboard_ser = DashboardSerializer(data=dashboard_data, many=True)
    if dashboard_ser.is_valid():
        dashboard_object.bulk_invalidate_dashboards_by_dashboard_ids(
            dashboard_ids, time
        )
        dashboard_object.persist_dashboard(dashboard_ser)

        for dashboard_id in dashboard_ids:
            audit_services.log(
                client_id=client_id,
                event_type_code=EVENT["CREATE_DASHBOARD"]["code"],
                event_key=dashboard_id,
                summary="Superset Dashboard created",
                updated_by=audit_data["updated_by"],
                updated_at=time,
                audit_data=audit_data,
            )
        logger.info(
            "New Dashboards with ids %s created successfully",
            dashboard_ids,
        )
    else:
        logger.error(
            "The dashboard_data for dashboard creation contains invalid data %s",
            dashboard_ser.errors,
        )
        raise SupersetExceptions(
            code=f"Dashboards contain invalid data for creation {dashboard_ser.errors}",
        )
    return STATUS_CODE.SUCCESS.value


def delete_dashboard(client_id, audit_data, dashboard_data):
    """
    This method is used to delete a single dashboard or in bulk in everstage db

    params:
        client_id -> str: client id
        audit_data -> dict: audit data
        dashboard_data -> List[dict]: dashboard data

    returns:
        status: status of the dashboard deletion
    """
    superset_dashboard_ids = dashboard_data.get("superset_dashboard_id")
    knowledge_date = timezone.now()
    dashboard_object = DashboardAccessor(client_id)
    dashboard_ids = dashboard_object.get_dashboard_ids_by_superset_ids(
        superset_dashboard_ids
    )
    if len(dashboard_ids) != len(superset_dashboard_ids):
        # if we find that dashboard is not existing in everstage it means it's deleted from everstage, and we will return the success reponse from superset
        # and allow to delete dashaboard in superset as well.
        logger.info(
            "Dashboards with id %s not found for deletion",
            superset_dashboard_ids,
        )
        return STATUS_CODE.SUCCESS.value
    dashboard_object.bulk_invalidate_dashboards_by_dashboard_ids(
        dashboard_ids, knowledge_date
    )
    DashboardUserMapAccessor(
        client_id
    ).bulk_invalidate_dashboard_user_map_by_dashboard_ids(dashboard_ids, knowledge_date)
    DashboardUserGroupMapAccessor(
        client_id
    ).bulk_invalidate_dashboard_user_group_map_by_dashboard_ids(
        dashboard_ids, knowledge_date
    )
    for dashboard_id in dashboard_ids:
        audit_services.log(
            client_id=client_id,
            event_type_code=EVENT["DELETE_DASHBOARD"]["code"],
            event_key=dashboard_id,
            summary="Superset Dashboard deleted",
            updated_by=audit_data["updated_by"],
            updated_at=knowledge_date,
            audit_data=audit_data,
        )
    logger.info(
        "Deleted dashboard with superset_dashboard_id %s successfully",
        superset_dashboard_ids,
    )
    return STATUS_CODE.SUCCESS.value


def delete_analytics_dashboard(request):
    client_id = request.client_id
    knowledge_date = timezone.now()
    dashboard_id = request.data.get("dashboard_id")
    logger = request.logger
    logger.update_context({"dashboard_id": dashboard_id})
    try:
        DashboardAccessor(client_id).invalidate_dashboard(dashboard_id, knowledge_date)
        DashboardUserMapAccessor(
            client_id
        ).invalidate_dashboard_user_map_by_dashboard_id(dashboard_id, knowledge_date)
        DashboardUserGroupMapAccessor(
            client_id
        ).invalidate_dashboard_user_group_map_by_dashboard_id(
            dashboard_id, knowledge_date
        )
        logger.info(
            "Deleted dashboard with dashboard_id {} successfully".format(dashboard_id)
        )
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    except Exception:
        logger.exception(
            "Exception in Deleting Dashboard with dashboard_id {}".format(dashboard_id)
        )
        raise


def create_analytics_dashboard(
    request, dashboard_type=DashboardType.SUPERSET_DASHBOARD.value
):
    time = timezone.now()
    request.data["knowledge_begin_date"] = time
    request.data["created_at"] = time
    request.data["dashboard_type"] = dashboard_type
    request.data["additional_details"] = request.audit
    client_id = request.client_id
    logger = request.logger
    logger.update_context({"dashboard_id": request.data["dashboard_id"]})
    dashboard_ser = DashboardSerializer(data=request.data)
    try:
        if dashboard_ser.is_valid():
            DashboardAccessor(client_id).invalidate_dashboard(
                request.data["dashboard_id"], time
            )
            DashboardAccessor(client_id).persist_dashboard(dashboard_ser)
            logger.info(
                "New Dashboard with name {} created successfully".format(
                    dashboard_ser.validated_data["name"].lower()
                )
            )
            return Response(
                {
                    "status": "SUCCESS",
                    "dashboardId": request.data["dashboard_id"],
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            error_dict = {
                "ser_errors": dashboard_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in creating dashboard with Id {}".format(
                    request.data["dashboard_id"]
                ),
                error_dict,
            )
            return Response(dashboard_ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(
            "Exception in creating dashboard with Id {}".format(
                request.data["dashboard_id"]
            )
        )
        raise SQLParseError() from e


# TODO : Move to superset module
def get_shareable_users_for_dashboard(client_id, dashboard_type):
    """
    This function returns the list of users who have the permission to view the
    dashboard for the given client_id and dashboard_type

    Args:
        client_id (int): client_id of the user
        dashboard_type (enum): type of the dashboard

    Returns:
        list: list of users who have the permission to view the dashboard
    """
    employees = EmployeeAccessor(client_id).get_all_active_employee_email_ids()
    employee_email_ids = [employee["employee_email_id"] for employee in employees]
    # Passing employee_email_ids to filter_users_with_dashbord_permissions will return
    # the employees who have the permission to view the dashboard
    filtered_employee_email_ids = filter_users_with_dashbord_permissions(
        client_id,
        employee_email_ids,
        dashboard_type,
    )
    return filtered_employee_email_ids


def share_dashboard(request):
    client_id = request.client_id
    dashboard_id = request.data["dashboard_id"]
    shared_users = request.data["shared_users"]
    shared_user_groups = request.data["shared_user_groups"]
    dashboard_type = request.data["dashboard_type"]
    time = timezone.now()
    logger = request.logger
    logger.update_context({"dashboard_id": dashboard_id})

    try:
        dashboard = DashboardAccessor(client_id).get_dashboard_by_id(dashboard_id)
        if dashboard:
            dashboard = clone_object(dashboard, time, request.audit)

            existing_users = DashboardUserMapAccessor(
                client_id
            ).get_shared_user_emails_for_dashboard(dashboard_id)
            existing_user_groups = DashboardUserGroupMapAccessor(
                client_id
            ).get_shared_user_group_ids_for_dashboard(dashboard_id)
            existing_user_groups = [str(group_id) for group_id in existing_user_groups]
            existing_users_with_permission = filter_employees_for_share(
                client_id, existing_users, dashboard_type
            )
            users_to_delete = set(existing_users_with_permission) - set(shared_users)
            user_groups_to_invalidate = set(existing_user_groups) - set(
                shared_user_groups
            )

            users_to_insert = set(shared_users) - set(existing_users)
            user_groups_to_insert = set(shared_user_groups) - set(existing_user_groups)

            if (
                len(users_to_insert)
                + len(user_groups_to_insert)
                + len(users_to_delete)
                + len(user_groups_to_invalidate)
            ) == 0:
                return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)

            shared_users_data = []
            shared_user_groups_data = []
            for shared_user in users_to_insert:
                shared_users_data.append(
                    {
                        "pk": None,
                        "knowledge_begin_date": time,
                        "client": client_id,
                        "additional_details": request.audit,
                        "employee_email_id": shared_user,
                        "dashboard_id": dashboard_id,
                    }
                )

            for user_group_id in user_groups_to_insert:
                shared_user_groups_data.append(
                    {
                        "pk": None,
                        "knowledge_begin_date": time,
                        "client": client_id,
                        "additional_details": request.audit,
                        "user_group_id": user_group_id,
                        "dashboard_id": dashboard_id,
                    }
                )

            is_db_user_ser_valid = True
            is_db_user_group_ser_valid = True
            dashboard_user_map_ser = None
            dashboard_user_group_map_ser = None
            error_dict = {}
            if shared_users_data:
                dashboard_user_map_ser = DashboardUserMapBulkSerializer(
                    data=shared_users_data, many=True
                )
                if not dashboard_user_map_ser.is_valid():
                    error_dict["dashboard_user_group_ser"] = (
                        dashboard_user_map_ser.errors
                    )
                    is_db_user_ser_valid = False
            if shared_user_groups_data:
                dashboard_user_group_map_ser = DashboardUserGroupMapBulkSerializer(
                    data=shared_user_groups_data, many=True
                )
                if not dashboard_user_group_map_ser.is_valid():
                    error_dict["dashboard_user_group_map_ser"] = (
                        dashboard_user_group_map_ser.errors
                    )
                    is_db_user_group_ser_valid = False

            if not (is_db_user_ser_valid and is_db_user_group_ser_valid):
                logger.error(
                    "Error sharing dashboard {}".format(dashboard_id),
                    error_dict,
                )
                return Response(
                    "Error sharing dashboard with users",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            ###################### audit log #####################
            event_type_code = EVENT["SHARE_DASHBOARD"]["code"]
            event_key = dashboard_id
            summary = dashboard.name
            audit_data = dashboard.__dict__
            updated_by = request.audit["updated_by"]
            updated_at = time
            ######################################################
            DashboardAccessor(client_id).invalidate_dashboard(dashboard_id, time)
            if users_to_delete:
                DashboardUserMapAccessor(client_id).invalidate_dashboard_user_map(
                    dashboard_id, users_to_delete, time
                )
            if user_groups_to_invalidate:
                DashboardUserGroupMapAccessor(
                    client_id
                ).invalidate_dashboard_user_group_map(
                    dashboard_id, user_groups_to_invalidate, time
                )
            if dashboard_user_map_ser:
                DashboardUserMapAccessor(client_id).persist_data(dashboard_user_map_ser)
            if dashboard_user_group_map_ser:
                DashboardUserGroupMapAccessor(client_id).persist_data(
                    dashboard_user_group_map_ser
                )
            DashboardAccessor(client_id).persist_dashboard(dashboard)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Dashboard : {} is successfully shared with users {}, groups {}".format(
                    dashboard_id, shared_users, shared_user_groups
                )
            )
            return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
        else:
            return Response({"status": "SUCCESS"}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error("Exception in sharing dashboard {}".format(dashboard_id))
        raise SQLParseError() from e
