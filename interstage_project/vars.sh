# The purpose of this file is to simplify starting the  python django shell
# At some point, we should unify this with eb.envars
# python manage.py shell_plus
#SANDBOX
export DB_SCHEMA=public
export DB_HOST=localhost
export DB_NAME=interstage-dev
# export DB_NAME=DBEVERSTAGEDEMO
export DB_USER=interdevuser
export DB_PASSWORD=localdev
export DB_PORT=5432

export ADMIN_DB_USER=interstage-admin
export ADMIN_DB_PASSWORD=adminuser

export DEBUG=1
export SECRET_KEY='@r*&z!0xi#*-t80h0n+w3%4rqr7i(*f6(5k#1vjz70dfp7h^vt'
export DJANGO_ALLOWED_HOSTS=".amazonaws.com localhost"
# To make things work inside the notebook
export DJANGO_ALLOW_ASYNC_UNSAFE=true

# Redis related variables
export CELERY_BROKER_URL=redis://localhost:6379
export CELERY_RESULT_BACKEND=redis://localhost:6379
export ELASTIC_CACHE_SERVER=redis://localhost:6379
export ELASTIC_CACHE_RO_SERVER=redis://localhost:6379

export ENV=LOCALDEV

export DOCUSIGN_CLIENT_ID=************************************
export DOCUSIGN_CLIENT_SECRET=75657500-86fc-405b-8011-31834f6c912c
export SLACK_SIGNING_SECRET=0da4a917ceeddc4a9e01a3cde1e4a044
export SLACK_CLIENT_ID=2526869108800.2555868161504
export SLACK_CLIENT_SECRET=85256ec6ce2773e707e65c8d98663dfb

export DOCUMENT360_READER_GROUP_ID_PAYEES=************************************
export DOCUMENT360_READER_GROUP_ID_ADMINS=************************************
export DOCUMENT360_BASE64ENCODED_CLIENT_ID_SECRET=************************************************************************************************************
export DJANGO_SETTINGS_MODULE=interstage_project.settings

export OPENAI_API_KEY=sk-none

export AWS_ACCESS_KEY_ID=********************
export AWS_SECRET_ACCESS_KEY=+3R344w5ri3/ZVcViDGAcr6qiycjo76mhhN+dyGz
export S3_PVT_ASSETS_BUCKET=everstage-pvt-assets-local
export S3_FLOWER=everstage-qa-flower-dump

export SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
export SNOWFLAKE_PASSWORD=Tes#Pwd3
export SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
export SNOWFLAKE_REGION=ap-south-1
export SNOWFLAKE_DATABASE=EVERSTAGE_LOCAL_INDIA
export SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
export SNOWFLAKE_SCHEMA=PUBLIC
export SNOWFLAKE_ROLE=ACCOUNTADMIN

export ANALYTICS_SNOWFLAKE_USER=EVERSTAGE_LOCAL_USER
export ANALYTICS_SNOWFLAKE_PASSWORD=Tes#Pwd3
export ANALYTICS_SNOWFLAKE_ACCOUNT=qt08442.ap-south-1.aws
export ANALYTICS_SNOWFLAKE_REGION=ap-south-1
export ANALYTICS_SNOWFLAKE_DATABASE=ANALYTICS_DEV_DB
export ANALYTICS_SNOWFLAKE_INTERNAL_DATABASE=ANALYTICS_DEV_INTERNAL_DB
export ANALYTICS_SNOWFLAKE_PROD_REPLICATION_DATABASE=ANALYTICS_DEV_INTERNAL_DB
export ANALYTICS_SNOWFLAKE_AGGREGATED_DATABASE=CUSTOMER_ANALYTICS_DB
export ANALYTICS_SNOWFLAKE_WAREHOUSE=EVERSTAGE_LOCAL
export ANALYTICS_SNOWFLAKE_SCHEMA=PUBLIC
export ANALYTICS_SNOWFLAKE_ROLE=ACCOUNTADMIN

# Snowflake US Creds

# export SNOWFLAKE_USER=everstage_dev_user
# export SNOWFLAKE_PASSWORD=Tes#Pwd3
# export SNOWFLAKE_ACCOUNT=oea92247
# export SNOWFLAKE_REGION=us-west-2
# export SNOWFLAKE_DATABASE=EVERSTAGE_LOCAL
# export SNOWFLAKE_WAREHOUSE=COMPUTE_WH
# export SNOWFLAKE_SCHEMA=PUBLIC
# export SNOWFLAKE_ROLE=ACCOUNTADMIN

export SEGMENT_WRITE_KEY=DUMMY_KEY

export EFS_BASE_PATH=/mnt/efs
export METRICS_LAMBDA_NAME=metrics_test

export DJANGO_REDIS_SCAN_ITERSIZE=100000

export CELERYD_MAX_TASKS_PER_CHILD=100
export WORKER_PROC_ALIVE_TIMEOUT=30

export TYPESENSE_API_KEY=vN7S2iILVmrjLTzLSxZDIKDYcPdWP9L8
export TYPESENSE_HOST=47plzx9tc6nf5a2ip-1.a1.typesense.net
export TYPESENSE_PORT=443
export TYPESENSE_PROTOCOL=https
export TYPESENSE_CONNECTION_TIMEOUT=5

export KAFKA_BROKER_URL=localhost:29092
export AWS_REGION=ap-south-1

export S3_REGION_NAME=us-west-1
export TOPIC_DEFAULT_PARTITION_NUMBER=3
export ECS_CONTAINER_METADATA_URI_V4="http://localhost:51678/mock-metadata"
export UDF_S3_LOCATION=s3://udf-bundle

export SIGMA_API_CLIENT_ID=f2240318a62508a2c91cb6bbb440490e002eab8240efd036def0c5194b93c195
export SIGMA_API_CLIENT_SECRET=86928c3e43e59dab1f046bf02bd360321ac5ab75bef0c907f331e16526f64553357aaac8e992506ec20f0c684f3c8813a577b5990a12d6b0cb5e48c4f0594779
export SIGMA_BASE_URL=https://aws-api.sigmacomputing.com
export SIGMA_TEMPLATE_WORKBOOK_ID=173d748b-f53f-43db-a84c-b25122685612
export SIGMA_SNOWFLAKE_USER_PASSWORD=abyC5vt5tKGLtN
export SIGMA_EMBED_CLIENT_ID=e84dc675ff48e91b42cecc696a0ef7b189744cc25151f48ed831ea40c1431e40
export SIGMA_EMBED_CLIENT_SECRET=b00410a719fba45027ab1bb3af9240ff89e1eec634769fa52f74e30b44660bfc3de6aecaf1caa6ea64628888e9206951053e79ba89544419058493724f926a5b

# Include npm registry token
source npm-vars.sh
