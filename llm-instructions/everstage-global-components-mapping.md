## Use the following map to identify the elements in the code and convert them:
   - **But<PERSON>**: `<EverButton/>` // `/interstage_project/frontend/src/v2/stories/form/EverButton.stories.jsx`
   - **ButtonGroup**: `<EverButtonGroup/>` // `/interstage_project/frontend/src/v2/stories/form/EverButtonGroup.stories.jsx`
   - **Input**: `<EverInput/>` // `/interstage_project/frontend/src/v2/stories/form/EverInput.stories.jsx`
   - **Select**: `<EverSelect/>` // `/interstage_project/frontend/src/v2/stories/form/EverSelect.stories.jsx`
   - **Checkbox**: `<EverCheckbox/>` // `/interstage_project/frontend/src/v2/stories/form/EverCheckbox.stories.jsx`
   - **Radio**: `<EverRadioGroup/>` // `/interstage_project/frontend/src/v2/stories/form/EverRadioGroup.stories.jsx`
   - **Tabs**: `<EverTabs/>` // `/interstage_project/frontend/src/v2/stories/navigation/EverTabs.stories.jsx`
   - **Modal**: `<EverModal/>` // `/interstage_project/frontend/src/v2/stories/surface/EverModal.stories.jsx`
   - **Table**: `<AgGridReact/>` // `/interstage_project/frontend/src/v2/dataDisplay/agGrid/AgGridReact.stories.jsx`
   - **Card**: `<EverCard/>` // `/interstage_project/frontend/src/v2/stories/surface/EverCard.stories.jsx`
   - **Tooltip**: `<EverTooltip/>` // `/interstage_project/frontend/src/v2/stories/surface/EverTooltip.stories.jsx`
   - **Texts**:
        - **Heading1**: `<EverTg.Heading1 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Heading1.stories.jsx`
        - **Heading2**: `<EverTg.Heading2 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Heading2.stories.jsx`
        - **Heading3**: `<EverTg.Heading3 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Heading3.stories.jsx`
        - **Heading4**: `<EverTg.Heading4 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Heading4.stories.jsx`
        - **SubHeading1**: `<EverTg.SubHeading1 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.SubHeading1.stories.jsx`
        - **SubHeading2**: `<EverTg.SubHeading2 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.SubHeading2.stories.jsx`
        - **SubHeading3**: `<EverTg.SubHeading3 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.SubHeading3.stories.jsx`
        - **SubHeading4**: `<EverTg.SubHeading4 />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.SubHeading4.stories.jsx`
        - **Text**: `<EverTg.Text />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Text.stories.jsx`
        - **Description**: `<EverTg.Description />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Description.stories.jsx`
        - **Caption**: `<EverTg.Caption />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Caption.stories.jsx`
        - **Caption.Medium**: `<EverTg.Medium />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.Medium.stories.jsx`
        - **Caption.Thick**: `<EverTg.CaptionThick />` // `/interstage_project/frontend/src/v2/stories/dataDisplay/everTg/EverTg.CaptionThick.stories.jsx`
        - **EverNavPortal**: `<EverNavPortal target={navPortalLocation}>` // `interstage_project/frontend/src/v2/components/EverNavPortal.js`, `interstage_project/frontend/src/v2/features/admin-settings/new-manual-adjustment/adjustment-summary/index.js`

## Components we use from antd. Use these components as is from antd. Whenever you need to create the following components, use the same props as antd version 4.x.x:
- `<Dropdown>` - use the same props as antd version 4.x.x. For usage check the `interstage_project/frontend/src/v2/features/users/employee-view/employee-summary/index.js` file.
- `<Menu>` - use the same props as antd version 4.x.x  For usage check the `interstage_project/frontend/src/v2/features/users/employee-view/employee-summary/index.js` file.