# Adding a new router:

* Make sure to export the main component as default and use lazy loading for the routes in `/interstage_project/frontend/src/v2/Router.js` file.
* Add a feature folder inside the `/interstage_project/frontend/src/v2/features` folder and add the component files there. Use the same name as the route for the folder name.
* Do not wrap the main imported component with `AuthRoute`. Don't do like this: `<Route element={<AuthRoute><Component /></AuthRoute>} />`.
* To make the breadcrumb work go to the `/interstage_project/frontend/src/v2/components/router/EverHeader.js` file and modify the `generatePageName` function for this route.