# :bulb: Updated Prompt: Convert Polymet Project into Everstage Feature
## :brain: Objective:
Transform the standalone `polymet-ai-agent` React project into a feature inside the Everstage frontend (located at `frontend/v2/features/`).
Ensure full alignment with:
- Everstage **design system**
- Existing **components**
- Project **structure and coding guidelines**
---
## :file_folder: Polymet Project Structure (`src/`):
- `assets/`: :white_check_mark: Ignore this.
- `components/`: Common components, mostly built using **Radix UI**.
- `hooks/`: Custom React hooks. :arrows_counterclockwise: Can be reused.
- `lib/`: Utility functions and helpers. :arrows_counterclockwise: Reuse or move to utilities folder if needed.
- `polymet/`: Core app – contains UI and layouts.
  - `components/`: Page-level reusable components.
  - `layout/`: Layout elements like Header, Sidebar.
  - `page/`: Route-level pages.
---
## :white_check_mark: Key Conversion Rules
### :arrows_counterclockwise: Component Replacement Strategy (:fire: Most Important):
1. :mag: Scan all components used from `polymet-ai-agent/src/components/`.
2. :repeat: For each component:
   - :white_check_mark: If there’s an equivalent in `frontend/v2/components/`, use it directly.
   - :question: If not sure about props or usage:
     - Refer to `stories/` folder in Everstage project for examples.
     - Use default props if examples are not available.
   - :x: If no match exists in Everstage, then:
     - :twisted_rightwards_arrows: Move the component into `frontend/v2/components/Custom/` (or suitable subfolder).
     - :sponge: Clean it up to follow Everstage design guidelines.
3. :no_entry_sign: Do **not** import or use components from `polymet-ai-agent/src/components/` directly.
   Always resolve them into Everstage equivalents or migrate properly.
---
### :building_construction: Flatten Folder Structure:
Convert everything inside `polymet/` into a new feature. For example:
```
frontend/v2/features/agent/
```
Merge and flatten:
- `layout/`, `components/`, and `page/` ➝ into single feature folder as components, views, and layout elements.
---
### :dna: Folder and File Handling Guidelines:
- :inbox_tray: Move hooks to `frontend/v2/hooks/` if reusable.
- :toolbox: Move utilities to `frontend/v2/utils/` or appropriate.
- :page_facing_up: Pages → Route views.
- :bricks: Reusable UI parts → `components/` inside feature.
- :card_index_dividers: Follow route-setup instructions from:
  `llm-instructions/how-to-add-new-route.md`
---
## :wrench: Code & Style Guidelines
### :link: Read These Before You Start:
- `.augment-guidelines.md`
- `llm-instructions/how-to-read-figma.md`
- `llm-instructions/everstage-global-components-mapping.md`
---
### :art: Use Only Everstage Global Components:
- Never use raw HTML tags like `<input>` or `<button>`.
- Use:
```js
import {
  EverButton,
  EverInput,
  EverSelect,
  EverAccordion,
  EverTooltip,
  // ... other components ...
} from “~/v2/components”;
```
- :white_check_mark: `EverSelect` already has a chevron, no need to add one.
- :x: Don’t add new styles or classnames like `w-[100px]`. Use Tailwind spacing like `w-24`.
---
### :nail_care: General Rules:
- Always group imports properly (libraries, components, utils, hooks, etc).
- Leave a line between import groups.
- Avoid creating new component libraries unless strictly necessary.
- Do not modify or overwrite existing global components.
- Follow the usage pattern shown in the stories.
---
## :repeat: Review & Iteration Checklist:
After initial conversion:
- :white_check_mark: Are all components resolved (replaced or moved)?
- :white_check_mark: Does the structure follow feature folder conventions?
- :white_check_mark: Is the routing properly set up?
- :white_check_mark: Does the feature work as expected visually and functionally?
- :white_check_mark: Are you reusing state and hooks properly?
If not, **iterate** and refine.
---
## :pushpin: Final Checklist
- [ ] Follow `.augment-guidelines.md`
- [ ] Replace or migrate components as per mapping rules.
- [ ] Flatten folders into `frontend/v2/features/<feature-name>`
- [ ] Setup routes.
- [ ] Match design system and code quality.
- [ ] Iterate until it works end-to-end.