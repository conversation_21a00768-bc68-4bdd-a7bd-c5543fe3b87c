# General Instructions for Reading Figma Files

## Objective
To accurately interpret the structure, layout, styling, and components within a Figma file to facilitate tasks like UI code generation, design token extraction, or design system analysis.

## 1. Access and Setup
- **Identify Target Scope**: Determine if you need to analyze the entire file, specific pages, or specific frames/components. This helps focus the analysis and manage API usage

## 2. Understand Figma Structure
- **File Hierarchy**: Recognize the hierarchy: File -> Pages -> Frames (or Components, Groups) -> Layers (individual elements like text, shapes, images)
- **Navigate Pages**: Process one page at a time unless instructed otherwise. Identify the relevant page(s) for the task
- **Identify Top-Level Frames**: Within a page, focus on the main artboards or frames that represent screens or distinct UI sections

## 3. Element Identification and Property Extraction
### Iterate Through Layers
- Traverse the layer tree within the target scope (frame or component)
- Determine the type of each layer (e.g., RECTANGLE, TEXT, VECTOR, INSTANCE, FRAME, GROUP)

### Extract Layout Properties
- **Position**: x, y coordinates relative to the parent
- **Size**: width, height
- **Constraints**: Horizontal and vertical constraints (e.g., LEFT, RIGHT, SCALE, TOP, BOTTOM) if not using Auto Layout
- **Auto Layout**: If a frame uses Auto Layout, extract:
  - layoutMode (HORIZONTAL or VERTICAL)
  - itemSpacing
  - paddingLeft, paddingRight, paddingTop, paddingBottom
  - primaryAxisAlignItems, counterAxisAlignItems
  - For items within Auto Layout: layoutAlign and layoutGrow

### Extract Styling Properties
- **Fills**: Background colors, gradients, images
  - Note color (RGBA), type (e.g., SOLID, GRADIENT_LINEAR), visible
- **Strokes**: Border colors, thickness, alignment
- **Effects**: Shadows, blurs
  - Note type (e.g., DROP_SHADOW), color, offset, radius, visible
- **Corner Radius**: cornerRadius or individual rectangleCornerRadii
- **Opacity**: opacity

### Extract Text Properties
- **Content**: characters
- **Font**: fontName (family, style)
- **Size**: fontSize
- **Weight**: Implicit in fontName.style
- **Line Height**: lineHeight (note units)
- **Letter Spacing**: letterSpacing
- **Alignment**: textAlignHorizontal, textAlignVertical
- **Decoration**: textDecoration
- **Case**: textCase
- Styles can be mentioned as:
    * textStyle: style_2JAKKB
    * fills: fill_7N0BRP
    * strokes: stroke_7N0BRP
    * cornerRadius: cornerRadius_2JAKKB
    * opacity: opacity_2JAKKB
- You should search the corresponding values in the figma file to understand the style.

### Identify Icons/Images
- Recognize VECTOR layers as potential icons
- Note layers with image fills
- Check export settings

## 4. Component and Instance Recognition
- **Identify Components**: Distinguish between main components (COMPONENT) and instances (INSTANCE)
- **Extract Main Component Info**: For instances, identify the mainComponent.id
- **Analyze Variants**: Identify variant properties for COMPONENT_SETs
- **Overrides**: Check for instance overrides

## 5. Style and Design System Mapping
- **Identify Applied Styles**: Check for shared styles.
- **Map to Code Components**: Map Figma elements to corresponding code components based on properties and conventions

## 6. Handling Prototyping and Interactions
- Identify prototypeInteractions
- Note triggers, actions, and destinations

## 7. Ambiguity and Refinement
- **Grouping vs. Frames**: Understand organizational vs. layout boundaries
- **Nested Structures**: Apply analysis recursively
- **Conflicting Information**: Prioritize specific rules
- **Seek Clarification**: Flag ambiguous design intent

## 8. Limitations
- **Plugins**: Custom data may not be accessible via standard API
- **Complex Interactions**: Rich animations may not be fully captured
- **Design Intent**: API provides data but not reasoning behind choices


## 9. Figma design token and Everstage global components mapping:
| Figma Token | Everstage Component |
|-------------|-------------------|
| Button (Type:Filled) | `<EverButton type="filled" />` |
| Button (Type:No fill/outline) | `<EverButton type="text" />` |
| Button (Type:Outline) | `<EverButton type="ghost" />` |
| Button (Type:Link) | `<EverButton type="link" />` |
| Button (Hierarchy:Primary) | `<EverButton />` |
| Button (Hierarchy:Success) | `<EverButton color="success" />` |
| Button (Hierarchy:Error) | `<EverButton color="error" />` |
| Button (Hierarchy:Info) | `<EverButton color="info" />` |
| Button (Hierarchy:Warning) | `<EverButton color="warning" />` |
| Button (Hierarchy:Neutral) | `<EverButton color="base" />` |
| Button (Size:Large) 48px | `<EverButton size="large" />` |
| Button (Size:Medium) 40px | `<EverButton size="medium" />` |
| Button (Size:Small) 32px | `<EverButton size="small" />` |
| Button (Icon Left) | `<EverButton prependIcon={<Icon />} />` |
| Button (Icon Right) | `<EverButton appendIcon={<Icon />} />` |
| Button Base | `<EverButtonGroup />` |
| Card | `<EverCard />` |
| Card fill="base-100" | `<EverCard className="bg-ever-base-100" />` |
| Card fill="primary-lite" | `<EverCard className="bg-ever-primary-lite" />` |
| Card fill="success-lite" | `<EverCard className="bg-ever-success-lite" />` |
| Card fill="error-lite" | `<EverCard className="bg-ever-error-lite" />` |
| Card fill="info-lite" | `<EverCard className="bg-ever-info-lite" />` |
| Card fill="warning-lite" | `<EverCard className="bg-ever-warning-lite" />` |
| Card stroke="base-400" | `<EverCard outlined={true} />` |
| Card stroke="primary-400" | `<EverCard outlined={true} className="border-ever-primary" />` |
| Card stroke="success-400" | `<EverCard outlined={true} className="border-ever-success" />` |
| Card stroke="error-400" | `<EverCard outlined={true} className="border-ever-error" />` |
| Frame | `<div />` |
| EverNavPortal | `<EverNavPortal />` know how to use from `~/v2/Router.js` |
| Text - Heading 1 - 20px Semi-bold (600) | `<EverTg.Heading1 />` |
| Text - Sub-Heading 1 - 20px Med (500) | `<EverTg.SubHeading1 />` |
| Text - Heading 2 - 18px Semi-Bold (600) | `<EverTg.Heading2 />` |
| Text - Sub-Heading 2 - 18px Med (500) | `<EverTg.SubHeading2 />` |
| Text - Heading 3 - 16px Semi-bold (600) | `<EverTg.Heading3 />` |
| Text - Sub-Heading 3 - 16px Med (500) | `<EverTg.SubHeading3 />` |
| Text - Heading 4 - 14px Semi-Bold (600) | `<EverTg.Heading4 />` |
| Text - Sub-Heading 4 - 14px Med (500) | `<EverTg.SubHeading4 />` |
| Text - Text - 14px Reg (400) | `<EverTg.Text />` |
| Text - Caption - 12px Reg (400) | `<EverTg.Caption />` |
| Text - Caption - 12px Med (500) | `<EverTg.Caption.Medium />` |
| Text - Caption - 12px Semi-Bold (600) | `<EverTg.Caption.Thick />` |
| **Any text with content-mid text color** | `<EverTg.Description />` |

# Everstage specific guidelines:
* Use Figma MCP to read the figma link.
* If the screenshot or figma link has a header title with large bold text, don't add it to the feature folder components. It would be added automatically by the router changes.
* Ignore the left navigation bar part of the screenshot or figma link.
* If the figma link or screenshot has buttons and inputs on top of the file, then use <EverNavbarPortal /> component to place the buttons and inputs.